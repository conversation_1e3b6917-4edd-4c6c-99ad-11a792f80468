# Smartlead Sync Curl Commands with Exact Metadata Structure

These curl commands will sync contacts to Smartlead with the exact metadata structure used by your EmailGenerationProcessor.

## Campaign IDs
- **Primary Campaign**: `2372187` (as updated in your changes)
- **Fallback Campaign**: `2317946` (from EmailGenerationProcessor)

## 1. Individual Contact Sync Commands

### Sync <PERSON><PERSON><PERSON> (Contact ID: 281419)
```bash
curl -X POST "http://localhost:3030/api/smartlead/contacts/190010/sync" \
  -H "Content-Type: application/json" \
  -d '{
    "campaignId": "2372187",
    "subject": "Exclusive Multifamily Investment Opportunities in Your Market",
    "html_body": "<p><PERSON> <PERSON><PERSON>,</p><p>I noticed your work with NY Major Construction and Development on large-scale projects. We have several exclusive multifamily investment opportunities that align with your construction expertise and could provide significant returns.</p><p>Would you be interested in discussing these opportunities?</p><p>Best regards,<br>Your Name</p>",
    "is_html": true,
    "custom_fields": {
      "email": "jshu<PERSON><EMAIL>",
      "job_title": "Assistant Project Manager",
      "last_name": "<PERSON>",
      "first_name": "<PERSON><PERSON><PERSON>",
      "company_name": "NY Major Construction and Development",
      "subject1": "Exclusive Multifamily Investment Opportunities in Your Market",
      "subject2": "Value-Add Multifamily Deals in Phoenix — 3 Matches",
      "subject3": "Phoenix Multifamily Cap Rates Hit 4.8% — Impact Analysis",
      "body1": "<p>Hi Shubham,</p><p>I noticed your work with NY Major Construction and Development on large-scale projects. We have several exclusive multifamily investment opportunities that align with your construction expertise and could provide significant returns.</p><p>Would you be interested in discussing these opportunities?</p><p>Best regards,<br>Your Name</p>",
      "body2": "<p>Hi Shubham,</p><p>Following up on our previous conversation about investment opportunities. We have 3 specific deals that match your criteria:</p><ul><li><strong>Phoenix Multifamily Complex</strong> - $25M deal, 8.5% IRR, Value-Add strategy</li><li><strong>Atlanta Mixed-Use Development</strong> - $45M deal, 12.2% IRR, Opportunistic strategy</li><li><strong>Miami Beach Luxury Condos</strong> - $18M deal, 15.8% IRR, Development strategy</li></ul><p>Would you like to explore any of these opportunities?</p>",
      "body3": "<p>Hi Shubham,</p><p>Market update: Phoenix multifamily cap rates have compressed to 4.8%, down from 5.2% last quarter. This represents a 7.7% increase in property values, creating excellent opportunities for construction professionals like yourself.</p><p>Recent transactions in your market include a $32M acquisition by Blackstone and a $28M development by Lennar. Our advisory team can help you capitalize on these market conditions.</p><p>Would you like to discuss how to leverage these trends?</p>",
      "email_generation_processor": "true",
      "generated_at": "2024-01-15T10:30:00Z",
      "processor_version": "1.1",
      "contact_id": "281419",
      "email_generation_version": "1.1",
      "update_count": "1"
    }
  }'
```

### Sync Deep Gujral (Contact ID: 280598)
```bash
curl -X POST "http://localhost:3030/api/smartlead/contacts/280598/sync" \
  -H "Content-Type: application/json" \
  -d '{
    "campaignId": "2372187",
    "subject": "Partner-Level Investment Opportunities for Trusteer Financial",
    "html_body": "<p>Hi Deep,</p><p>As a Partner at Trusteer Financial, you understand the value of strategic investments. We have several partner-level opportunities in the commercial real estate space that could complement your current portfolio.</p><p>Would you like to explore these exclusive deals?</p><p>Best regards,<br>Your Name</p>",
    "is_html": true,
    "custom_fields": {
      "email": "<EMAIL>",
      "job_title": "Partner",
      "last_name": "Gujral",
      "first_name": "Deep",
      "company_name": "Trusteer Financial",
      "subject1": "Partner-Level Investment Opportunities for Trusteer Financial",
      "subject2": "Manhattan Office Deals — 3 Partner-Level Matches",
      "subject3": "Manhattan Office Cap Rates Stabilize at 5.1% — Market Analysis",
      "body1": "<p>Hi Deep,</p><p>As a Partner at Trusteer Financial, you understand the value of strategic investments. We have several partner-level opportunities in the commercial real estate space that could complement your current portfolio.</p><p>Would you like to explore these exclusive deals?</p><p>Best regards,<br>Your Name</p>",
      "body2": "<p>Hi Deep,</p><p>Following up on our previous conversation about partner-level opportunities. We have 3 specific deals that align with Trusteer Financial''s investment criteria:</p><ul><li><strong>Manhattan Office Tower</strong> - $85M deal, 9.2% IRR, Core-Plus strategy</li><li><strong>San Francisco Tech Campus</strong> - $120M deal, 11.5% IRR, Value-Add strategy</li><li><strong>Chicago Mixed-Use Portfolio</strong> - $65M deal, 13.8% IRR, Opportunistic strategy</li></ul><p>Would you like to explore any of these opportunities?</p>",
      "body3": "<p>Hi Deep,</p><p>Market update: Manhattan office cap rates have stabilized at 5.1%, with Class A properties showing strong tenant demand. This represents a 12% increase in transaction volume compared to last quarter.</p><p>Recent transactions include a $95M acquisition by Brookfield and a $78M development by Related Companies. Our advisory team can help you navigate these market conditions.</p><p>Would you like to discuss strategic positioning?</p>",
      "email_generation_processor": "true",
      "generated_at": "2024-01-15T10:30:00Z",
      "processor_version": "1.1",
      "contact_id": "280598",
      "email_generation_version": "1.1",
      "update_count": "1"
    }
  }'
```

### Sync Gianluca Calabretta (Contact ID: 280876)
```bash
curl -X POST "http://localhost:3030/api/smartlead/contacts/280876/sync" \
  -H "Content-Type: application/json" \
  -d '{
    "campaignId": "2372187",
    "subject": "Customer Service Excellence in Real Estate Investment",
    "html_body": "<p>Hi Gianluca,</p><p>Your role in customer service sales at Doorland Group shows your commitment to client satisfaction. We have investment opportunities that could enhance your service offerings and provide additional value to your clients.</p><p>Would you be interested in learning more?</p><p>Best regards,<br>Your Name</p>",
    "is_html": true,
    "custom_fields": {
      "email": "<EMAIL>",
      "job_title": "Customer Service Sales Representative",
      "last_name": "Calabretta",
      "first_name": "Gianluca",
      "company_name": "Doorland Group",
      "subject1": "Customer Service Excellence in Real Estate Investment",
      "subject2": "Residential Development Opportunities — 3 Client Matches",
      "subject3": "Residential Development Cap Rates at 6.2% — Client Opportunities",
      "body1": "<p>Hi Gianluca,</p><p>Your role in customer service sales at Doorland Group shows your commitment to client satisfaction. We have investment opportunities that could enhance your service offerings and provide additional value to your clients.</p><p>Would you be interested in learning more?</p><p>Best regards,<br>Your Name</p>",
      "body2": "<p>Hi Gianluca,</p><p>Following up on our previous conversation about enhancing your service offerings. We have 3 specific opportunities that could benefit Doorland Group''s clients:</p><ul><li><strong>Residential Development Fund</strong> - $15M deal, 10.5% IRR, Development strategy</li><li><strong>Retail Portfolio Acquisition</strong> - $22M deal, 8.9% IRR, Core strategy</li><li><strong>Industrial Warehouse Investment</strong> - $18M deal, 12.3% IRR, Value-Add strategy</li></ul><p>Would you like to explore these opportunities?</p>",
      "body3": "<p>Hi Gianluca,</p><p>Market update: Residential development cap rates have compressed to 6.2%, with strong demand for single-family rentals. This represents a 15% increase in development activity compared to last quarter.</p><p>Recent transactions include a $25M acquisition by Invitation Homes and a $20M development by American Homes 4 Rent. Our advisory team can help you provide value to your clients.</p><p>Would you like to discuss client opportunities?</p>",
      "email_generation_processor": "true",
      "generated_at": "2024-01-15T10:30:00Z",
      "processor_version": "1.1",
      "contact_id": "280876",
      "email_generation_version": "1.1",
      "update_count": "1"
    }
  }'
```

## 2. Batch Sync Command

### Sync All 3 Contacts at Once
```bash
curl -X POST "http://localhost:3030/api/smartlead/contacts/batch-sync" \
  -H "Content-Type: application/json" \
  -d '{
    "contactIds": [281419, 280598, 280876],
    "campaignId": "2372187"
  }'
```

## 3. Campaign-Based Sync

### Sync All Contacts from Campaign
```bash
curl -X POST "http://localhost:3030/api/smartlead/campaigns/2372187/sync" \
  -H "Content-Type: application/json" \
  -d '{}'
```

## 4. Status Check Commands

### Get Contact Sync Status
```bash
curl -X GET "http://localhost:3030/api/smartlead/contacts/281419/sync"
```

### Get Campaign Leads
```bash
curl -X GET "http://localhost:3030/api/smartlead/campaigns/2372187/leads"
```

## 5. PSQL Commands to Create Dummy Data

### Create Dummy Threads and Messages
```sql
-- Create dummy threads
INSERT INTO threads (thread_id, workspace_id, subject, status, metadata, created_at, updated_at, to_email, contact_id)
VALUES 
  (
    gen_random_uuid(),
    'f8b45e3c-7186-4fdc-a696-c5971b1d2c7f',
    'DRIP CAMPAIGN FOR Shubham Jain',
    'active',
    '{"email_generation": true, "processor_type": "EmailGenerationProcessor", "created_by": "system"}',
    NOW(),
    NOW(),
    '<EMAIL>',
    281419
  ),
  (
    gen_random_uuid(),
    'f8b45e3c-7186-4fdc-a696-c5971b1d2c7f',
    'DRIP CAMPAIGN FOR Deep Gujral',
    'active',
    '{"email_generation": true, "processor_type": "EmailGenerationProcessor", "created_by": "system"}',
    NOW(),
    NOW(),
    '<EMAIL>',
    280598
  ),
  (
    gen_random_uuid(),
    'f8b45e3c-7186-4fdc-a696-c5971b1d2c7f',
    'DRIP CAMPAIGN FOR Gianluca Calabretta',
    'active',
    '{"email_generation": true, "processor_type": "EmailGenerationProcessor", "created_by": "system"}',
    NOW(),
    NOW(),
    '<EMAIL>',
    280876
  );

-- Create dummy messages with exact metadata structure
INSERT INTO messages (
  message_id, 
  thread_id, 
  from_email, 
  to_email, 
  subject, 
  body, 
  metadata, 
  direction, 
  smartlead_campaign_id, 
  role, 
  created_at
)
SELECT 
  gen_random_uuid(),
  t.thread_id,
  '<EMAIL>',
  t.to_email,
  t.subject,
  '',
  '{
    "variables": {
      "email": "' || t.to_email || '",
      "job_title": "' || CASE 
        WHEN t.contact_id = 281419 THEN 'Assistant Project Manager'
        WHEN t.contact_id = 280598 THEN 'Partner'
        WHEN t.contact_id = 280876 THEN 'Customer Service Sales Representative'
      END || '",
      "last_name": "' || CASE 
        WHEN t.contact_id = 281419 THEN 'Jain'
        WHEN t.contact_id = 280598 THEN 'Gujral'
        WHEN t.contact_id = 280876 THEN 'Calabretta'
      END || '",
      "first_name": "' || CASE 
        WHEN t.contact_id = 281419 THEN 'Shubham'
        WHEN t.contact_id = 280598 THEN 'Deep'
        WHEN t.contact_id = 280876 THEN 'Gianluca'
      END || '",
      "company_name": "' || CASE 
        WHEN t.contact_id = 281419 THEN 'NY Major Construction and Development'
        WHEN t.contact_id = 280598 THEN 'Trusteer Financial'
        WHEN t.contact_id = 280876 THEN 'Doorland Group'
      END || '",
      "subject1": "Exclusive Investment Opportunities",
      "subject2": "Specific Deal Matches",
      "subject3": "Market Update Analysis",
      "body1": "<p>Hi there,</p><p>We have exclusive investment opportunities that align with your expertise.</p>",
      "body2": "<p>Hi there,</p><p>Here are specific deals that match your criteria.</p>",
      "body3": "<p>Hi there,</p><p>Here is a market update relevant to your focus area.</p>"
    },
    "generated_at": "' || NOW() || '",
    "contact_id": ' || t.contact_id || ',
    "email_generation_version": "1.1",
    "update_count": 1
  }'::jsonb,
  'outbound',
  '2372187',
  'assistant',
  NOW()
FROM threads t
WHERE t.contact_id IN (281419, 280598, 280876);

-- Update contacts to mark them as having generated emails
UPDATE contacts 
SET 
  email_generated = true,
  email_generation_status = 'completed',
  email_generation_date = NOW(),
  updated_at = NOW()
WHERE contact_id IN (281419, 280598, 280876);
```

## Expected Response Format

```json
{
  "success": true,
  "lead_id": "12345",
  "contact_id": "281419",
  "email": "<EMAIL>",
  "campaign_id": "2372187",
  "message": "Successfully synced to Smartlead",
  "custom_fields": {
    "email": "<EMAIL>",
    "job_title": "Assistant Project Manager",
    "first_name": "Shubham",
    "last_name": "Jain",
    "company_name": "NY Major Construction and Development",
    "subject1": "Exclusive Multifamily Investment Opportunities in Your Market",
    "body1": "<p>Hi Shubham,</p><p>I noticed your work with NY Major Construction and Development...</p>",
    "email_generation_processor": "true",
    "generated_at": "2024-01-15T10:30:00Z",
    "processor_version": "1.1"
  }
}
```

## Notes

- **Exact Metadata Structure**: These commands use the exact metadata structure from your EmailGenerationProcessor
- **Campaign ID**: Updated to `2372187` as per your changes
- **Contact IDs**: Using real contact IDs from your database (281419, 280598, 280876)
- **Target Email**: All emails will be sent to `<EMAIL>`
- **Custom Fields**: Includes all variables, subjects, and bodies from the email generation process
- **HTML Content**: Properly formatted HTML content for Smartlead
- **Database Integration**: PSQL commands create the exact structure needed for sync
