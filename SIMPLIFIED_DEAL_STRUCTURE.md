# Simplified Deal Structure

## Overview

This document describes the new simplified deal table structure that replaces the wide `deal_data_pivoted` table with a more normalized, efficient design.

## Key Changes

### 1. New Table Structure

**Old Structure**: `deal_data_pivoted` (100+ columns, wide table)
**New Structure**: `deals` (~80 columns, normalized table)

### 2. Foreign Key Relationships

The new `deals` table includes a foreign key relationship to the `persons` table:

```sql
sponsor_id INTEGER REFERENCES public.persons(person_id)
```

This allows for proper normalization and relationship tracking between deals and sponsors.

### 3. Improved Data Types

- **Financial Values**: Changed from TEXT to DECIMAL(15,2) for better precision and calculations
- **Percentages**: Changed from TEXT to DECIMAL(5,4) for rates and ratios
- **Dates**: Proper DATE type for timeline fields
- **Integers**: Proper INTEGER type for counts and durations

### 4. Simplified Field Structure

The new structure focuses on the most important deal fields while maintaining flexibility:

#### Core Deal Information

- `deal_name` - Name or title of the deal
- `sponsor_id` - Foreign key to persons table
- `sponsor_name` - Text field for flexibility
- `geo_region` - Geographic region or market
- `deal_type` - Acquisition, Development, Refinance, etc.
- `deal_stage` - Underwriting, Due Diligence, Closing, etc.

#### Property Information

- `property_type` - Multifamily, Office, Retail, etc.
- `asset_type` - Core, Core Plus, Value Add, etc.
- `property_address`, `property_city`, `property_state`, `property_zip`
- `gross_sf`, `net_sf`, `lot_size`
- `num_residential_units`, `num_commercial_units`, `hotel_keys`

#### Financial Terms

- `loan_amount`, `loan_term_months`
- `interest_rate`, `interest_rate_type`
- `sofr_plus`, `wsj_prime`, `ust_5y`, `ust_7y`, `ust_10y`
- `recourse`, `attach_point`

#### Financial Metrics

- `noi_yr1` through `noi_yr5`
- `yield_on_cost_yr1`, `terminal_cap_rate`
- `ltv`, `debt_yield_yr1`
- `value_psf`, `debt_psf`

#### Returns

- `gp_equity_multiple`, `gp_irr`
- `lp_equity_multiple`, `lp_irr`
- `total_equity_multiple`, `total_irr`

#### Project Budget (Simplified)

- `total_development_cost`
- `acquisition_cost`, `construction_cost`
- `soft_costs`, `financing_costs`

#### Sources and Uses of Funds

- `senior_debt_funding`, `mezzanine_debt_funding`
- `gp_equity_funding`, `lp_equity_funding`, `preferred_equity_funding`
- `acquisition_funding`, `construction_funding`, `total_funding`

## Files Created

### 1. Database Schema

- `public/create_deal_table.sql` - SQL script to create the new deals table

### 2. Extraction Logic

- `src/lib/prompts/deal-extraction-simplified.ts` - Simplified extraction map and prompt

### 3. API Route

- `src/app/api/deals/upload-simplified/route.ts` - New upload endpoint for simplified structure

### 4. UI Components

- `src/components/dashboard/deals/SimplifiedDealUpload.tsx` - Upload component
- `src/app/dashboard/deals/simplified/page.tsx` - Page for simplified upload

## Usage

### 1. Create the New Table

Run the SQL script to create the new table:

```bash
psql -d your_database -f public/create_deal_table.sql
```

### 2. Access the Simplified Upload

Navigate to: `/dashboard/deals/simplified`

### 3. Upload Documents

The simplified upload supports:

- Multiple file uploads
- Excel file conversion to text
- Universal extraction with custom fields
- Comprehensive metadata tracking

## Benefits

### 1. Performance

- Reduced column count improves query performance
- Better indexing on key fields
- Proper data types for calculations

### 2. Data Integrity

- Foreign key relationships ensure data consistency
- Proper data types prevent invalid data
- Normalized structure reduces redundancy

### 3. Maintainability

- Cleaner, more focused schema
- Easier to understand and modify
- Better separation of concerns

### 4. Flexibility

- JSONB fields for custom data
- Maintains all existing functionality
- Backward compatible with existing workflows

## Migration Strategy

### Option 1: Parallel Operation

- Keep both tables running
- Use simplified structure for new uploads
- Gradually migrate existing data

### Option 2: Direct Migration

- Create new table
- Migrate existing data
- Switch to new structure

### Option 3: Hybrid Approach

- Use simplified structure for new deals
- Keep legacy structure for historical data
- Create views to unify data access

## API Endpoints

### New Simplified Upload

```
POST /api/deals/upload-simplified
```

**Request Format:**

```javascript
const formData = new FormData();
formData.append("file_0", file);
formData.append("file_type_0", "memorandum");
formData.append("file_count", "1");
formData.append("mode", "file");
```

**Response Format:**

```javascript
{
  "message": "Successfully processed 1 document using simplified structure with Gemini 2.0 Flash",
  "fileCount": 1,
  "outputFile": "document_simplified_gemini2.0_2024-01-01T12-00-00-000Z.json",
  "extractedData": { /* extracted data */ },
  "databaseSaved": true,
  "structure": "simplified"
}
```

## Comparison with Legacy Structure

| Aspect          | Legacy (deal_data_pivoted) | New (deals)                           |
| --------------- | -------------------------- | ------------------------------------- |
| Columns         | 100+                       | ~80                                   |
| Data Types      | Mostly TEXT                | Proper types (DECIMAL, INTEGER, DATE) |
| Relationships   | None                       | Foreign key to persons table          |
| Performance     | Slower queries             | Optimized with indexes                |
| Maintainability | Complex                    | Simplified and focused                |
| Flexibility     | High (wide table)          | High (JSONB fields)                   |

## Next Steps

1. **Test the new structure** with sample documents
2. **Validate data extraction** accuracy
3. **Performance testing** with larger datasets
4. **User feedback** and iteration
5. **Gradual migration** of existing data

## Support

For questions or issues with the simplified structure:

1. Check the console logs for detailed error messages
2. Review the output JSON files for extraction results
3. Verify database table creation and permissions
4. Test with different document types and formats
