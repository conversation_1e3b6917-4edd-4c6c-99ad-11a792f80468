# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
# Add directories or file patterns to ignore during indexing (e.g. foo/ or *.csv)
*.csv
*.log
cre_urls.csv
observer_urls.csv
bisnow_urls.csv
*.csv
# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
.cursor
scripts/news_chrome_profile
scripts/urlfetch_chrome_profile
scripts/venv/*
*.pyc
new_links.txt
data/*
anax-dash-env
processing-out
deal-processing-outputs

# Debug/logging directories
error-files/
prompt-logs/

file_storage
scraper_profiles/
*.dump
# *.sql
*.xlsx
anax_data.sql
full_schema.sql
.cursor/*
dist
uploads