import { Pool } from "pg";
import csvWriter from "csv-writer";
import fs from "fs";
import path from "path";

const { createObjectCsvWriter } = csvWriter;
const DB_USER = "anax_user";
const DB_PASSWORD = "anax_password";
const DB_HOST = "localhost";
const DB_PORT = "5433";
const DB_DATABASE = "anax";
const pool = new Pool({
  user: DB_USER,
  password: DB_PASSWORD,
  host: DB_HOST,
  port: parseInt(DB_PORT || "5432"),
  database: DB_DATABASE,
  // Improved connection resilience settings
  connectionTimeoutMillis: 10000, // 10 seconds (increased from 5)
  idleTimeoutMillis: 30000, // 30 seconds
  max: 15, // Maximum number of clients in the pool (reduced from 20)
  allowExitOnIdle: true, // Allow pool to exit when no connections
});

// CSV writer setup
// const csvWriterInstance = createObjectCsvWriter({
//   path: "job_title_summary_tier.csv",
//   header: [
//     { id: "alike_string", title: "Alike String" },
//     { id: "job_titles", title: "Job Titles (Array)" },
//     { id: "total_count", title: "Count" },
//     { id: "tier", title: "Tier" },
//   ],
// });

// // SQL query with proper fuzzy match coverage
// const query = `
//   WITH matched_titles AS (
//     SELECT
//       title,
//       CASE
//         -- Tier 1 - C-Suite / Founders
//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Credit Officer%', '%CCO%', '%Chief Credit Officer (CCO)%'
//         ]) THEN 'Chief Credit Officer'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Development Officer%', '%CDO%', '%Chief Development Officer (CDO)%'
//         ]) THEN 'Chief Development Officer'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Executive Officer%', '%CEO%', '%Chief Executive Officer (CEO)%'
//         ]) THEN 'Chief Executive Officer'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Financial Officer%', '%CFO%', '%Chief Financial Officer (CFO)%'
//         ]) THEN 'Chief Financial Officer'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Investment Officer%', '%CIO%', '%Chief Investment Officer (CIO)%'
//         ]) THEN 'Chief Investment Officer'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Lending Officer%', '%CLO%', '%Chief Lending Officer (CLO)%'
//         ]) THEN 'Chief Lending Officer'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Operating Officer%', '%COO%', '%Chief Operating Officer (COO)%'
//         ]) THEN 'Chief Operating Officer'

//         WHEN title ILIKE '%Chief Real Estate Officer%' THEN 'Chief Real Estate Officer'
//         WHEN title ILIKE '%Chief Risk Officer%' THEN 'Chief Risk Officer'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Strategy Officer%', '%CSO%', '%Chief Strategy Officer (CSO)%'
//         ]) THEN 'Chief Strategy Officer'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Founder%', '%Co-Founder%', '%CoFounder%'
//         ]) THEN 'Founder / Co-Founder'

//         WHEN title ILIKE '%Managing Partner Real Estate Investment%' THEN 'Managing Partner, Real Estate Investment'
//         WHEN title ILIKE '%Managing Partner Real Estate Finance%' THEN 'Managing Partner, Real Estate Finance'
//         WHEN title ILIKE '%Managing Partner Real Estate%' THEN 'Managing Partner, Real Estate'
//         WHEN title ILIKE '%Managing Partner%' THEN 'Managing Partner'

//         WHEN title ILIKE '%President%' THEN 'President'

//         -- Tier 2 - Senior Executives
//         WHEN title ILIKE '%Head of Acquisitions%' THEN 'Head Of Acquisitions'
//         WHEN title ILIKE '%Head of Capital Markets%' THEN 'Head Of Capital Markets'
//         WHEN title ILIKE '%Head of Investments%' THEN 'Head Of Investments'
//         WHEN title ILIKE '%Head of Loan Originations%' THEN 'Head Of Loan Originations'
//         WHEN title ILIKE '%Head of Private Markets%' THEN 'Head Of Private Markets'
//         WHEN title ILIKE '%Head of Real Estate Investments%' THEN 'Head Of Real Estate Investments'
//         WHEN title ILIKE '%Head of Real Estate%' THEN 'Head Of Real Estate'

//         WHEN title ILIKE '%Managing Director of Acquisitions%' THEN 'Managing Director, Acquisitions'
//         WHEN title ILIKE '%Managing Director Acquisitions%' THEN 'Managing Director, Acquisitions'
//         WHEN title ILIKE '%Managing Director of Capital Markets%' THEN 'Managing Director, Capital Markets'
//         WHEN title ILIKE '%Managing Director Capital Markets%' THEN 'Managing Director, Capital Markets'
//         WHEN title ILIKE '%Managing Director Real Estate Investments%' THEN 'Managing Director, Real Estate Investments'
//         WHEN title ILIKE '%Managing Director of Real Estate Investments%' THEN 'Managing Director, Real Estate Investments'
//         WHEN title ILIKE '%Managing Director%' THEN 'Managing Director'

//         WHEN title ILIKE '%Partner of Real Estate Finance%' THEN 'Partner, Real Estate Finance'
//         WHEN title ILIKE '%Partner Real Estate Finance%' THEN 'Partner, Real Estate Finance'
//         WHEN title ILIKE '%Partner of Real Estate%' THEN 'Partner, Real Estate'
//         WHEN title ILIKE '%Partner Real Estate%' THEN 'Partner, Real Estate'
//         WHEN title ILIKE '%Partner Real Estate Investment%' THEN 'Partner, Real Estate Investment'
//         WHEN title ILIKE '%Partner%' THEN 'Partner'

//         WHEN title ILIKE '%Principal - Real Estate%' THEN 'Principal, Real Estate'
//         WHEN title ILIKE '%Principal%' THEN 'Principal'

//         -- Tier 3 - Mid-Level Executives
//         WHEN title ILIKE '%Director of Acquisitions%' THEN 'Director, Acquisitions'
//         WHEN title ILIKE '%Director, Acquisitions%' THEN 'Director, Acquisitions'
//         WHEN title ILIKE '%Director of Capital Markets%' THEN 'Director, Capital Markets'
//         WHEN title ILIKE '%Director, Capital Markets%' THEN 'Director, Capital Markets'
//         WHEN title ILIKE '%Director of Investments%' THEN 'Director, Investments'
//         WHEN title ILIKE '%Director, Investments%' THEN 'Director, Investments'
//         WHEN title ILIKE '%Director of CRE Lending%' OR title ILIKE '%Director CRE Lending%' THEN 'Director, Cre Lending'
//         WHEN title ILIKE '%Director of Structured Finance%' THEN 'Director, Structured Finance'
//         WHEN title ILIKE '%Director, Structured Finance%' THEN 'Director, Structured Finance'
//         WHEN title ILIKE '%Director of Real Estate%' THEN 'Director, Real Estate'
//         WHEN title ILIKE '%Director of Structured Investments%' THEN 'Director, Structured Investments'
//         WHEN title ILIKE '%Director of Real Estate Strategy%' THEN 'Director, Real Estate Strategy'
//         WHEN title ILIKE '%Director, Real Estate Strategy%' THEN 'Director, Real Estate Strategy'
//         WHEN title ILIKE '%Director of Underwriting%' THEN 'Director, Underwriting'
//         WHEN title ILIKE '%Director, Underwriting%' THEN 'Director, Underwriting'
//         WHEN title ILIKE '%Director of Debt & Equity Placement%' THEN 'Director, Debt Equity Placement'
//         WHEN title ILIKE '%Director, Debt & Equity Placement%' THEN 'Director, Debt Equity Placement'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of Acquisitions%', '%VP of Acquisitions%', '%VP, Acquisitions%'
//         ]) THEN 'Vice President, Acquisitions'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of Capital Markets%', '%VP of Capital Markets%', '%VP, Capital Markets%', '%VP Capital Markets%'
//         ]) THEN 'Vice President, Capital Markets'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of CRE Lending%', '%VP of CRE Lending%', '%VP, CRE Lending%'
//         ]) THEN 'Vice President, Cre Lending'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of Investments%', '%VP of Investments%', '%VP, Investments%'
//         ]) THEN 'Vice President, Investments'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of Real Estate%', '%VP of Real Estate%', '%VP, Real Estate%'
//         ]) THEN 'Vice President, Real Estate'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of Structured Finance%', '%VP of Structured Finance%', '%VP, Structured Finance%'
//         ]) THEN 'Vice President, Structured Finance'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of Underwriting%', '%VP of Underwriting%', '%VP, Underwriting%'
//         ]) THEN 'Vice President, Underwriting'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of Debt & Equity Placement%', '%VP of Debt & Equity Placement%', '%VP, Debt & Equity Placement%'
//         ]) THEN 'Vice President, Debt Equity Placement'

//         -- Tier 4 - Investment / Portfolio Managers
//         WHEN title ILIKE '%CRE Origination Manager%' THEN 'Cre Origination Manager'
//         WHEN title ILIKE '%Investment Manager Real Estate%' THEN 'Investment Manager, Real Estate'
//         WHEN title ILIKE '%Investment Officer Real Estate%' THEN 'Investment Officer Real Estate'
//         WHEN title ILIKE '%Portfolio Manager - Real Estate%' OR title ILIKE '%Portfolio Manager, Real Estate%' THEN 'Portfolio Manager, Real Estate'
//         WHEN title ILIKE '%Real Assets Manager%' THEN 'Real Assets Manager'
//         WHEN title ILIKE '%Real Estate Portfolio Strategist%' THEN 'Real Estate Portfolio Strategist'

//         -- Tier 5 - Analysts & Associates
//         WHEN title ILIKE '%Acquisitions Associate%' THEN 'Acquisitions Associate'
//         WHEN title ILIKE '%Capital Markets Associate%' THEN 'Capital Markets Associate'
//         WHEN title ILIKE '%Capital Markets Analyst%' THEN 'Capital Markets Analyst'
//         WHEN title ILIKE '%Real Estate Investment Analyst%' THEN 'Real Estate Investment Analyst'
//         WHEN title ILIKE '%Real Estate Acquisitions Analyst%' THEN 'Real Estate Acquisitions Analyst'
//         WHEN title ILIKE '%Real Estate Private Equity Analyst%' THEN 'Real Estate Private Equity Analyst'
//         WHEN title ILIKE '%Investment Associate Real Estate%' THEN 'Investment Associate Real Estate'
//         WHEN title ILIKE '%CRE Origination Analyst%' THEN 'Cre Origination Analyst'
//         WHEN title ILIKE '%CRE Originations Associate%' THEN 'CRE Originations Associate'
//         WHEN title ILIKE '%CRE Originations%' OR title ILIKE '%Originations%' THEN 'Cre Originations'

//         -- Default fallback
//         ELSE 'Other / Unclassified'
//     END AS alike_string,
//     CASE
//         -- Tier 1 - C-Suite / Founders
//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Credit Officer%', '%CCO%', '%Chief Credit Officer (CCO)%'
//         ]) THEN 'tier1'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Development Officer%', '%CDO%', '%Chief Development Officer (CDO)%'
//         ]) THEN 'tier1'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Executive Officer%', '%CEO%', '%Chief Executive Officer (CEO)%'
//         ]) THEN 'tier1'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Financial Officer%', '%CFO%', '%Chief Financial Officer (CFO)%'
//         ]) THEN 'tier1'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Investment Officer%', '%CIO%', '%Chief Investment Officer (CIO)%'
//         ]) THEN 'tier1'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Lending Officer%', '%CLO%', '%Chief Lending Officer (CLO)%'
//         ]) THEN 'tier1'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Operating Officer%', '%COO%', '%Chief Operating Officer (COO)%'
//         ]) THEN 'tier1'

//         WHEN title ILIKE '%Chief Real Estate Officer%' THEN 'tier1'
//         WHEN title ILIKE '%Chief Risk Officer%' THEN 'tier1'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Chief Strategy Officer%', '%CSO%', '%Chief Strategy Officer (CSO)%'
//         ]) THEN 'tier1'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Founder%', '%Co-Founder%', '%CoFounder%'
//         ]) THEN 'tier1'

//         WHEN title ILIKE '%Managing Partner Real Estate Investment%' THEN 'tier1'
//         WHEN title ILIKE '%Managing Partner Real Estate Finance%' THEN 'tier1'
//         WHEN title ILIKE '%Managing Partner Real Estate%' THEN 'tier1'
//         WHEN title ILIKE '%Managing Partner%' THEN 'tier1'

//         WHEN title ILIKE '%President%' THEN 'tier1'

//         -- Tier 2 - Senior Executives
//         WHEN title ILIKE '%Head of Acquisitions%' THEN 'tier2'
//         WHEN title ILIKE '%Head of Capital Markets%' THEN 'tier2'
//         WHEN title ILIKE '%Head of Investments%' THEN 'tier2'
//         WHEN title ILIKE '%Head of Loan Originations%' THEN 'tier2'
//         WHEN title ILIKE '%Head of Private Markets%' THEN 'tier2'
//         WHEN title ILIKE '%Head of Real Estate Investments%' THEN 'tier2'
//         WHEN title ILIKE '%Head of Real Estate%' THEN 'tier2'

//         WHEN title ILIKE '%Managing Director of Acquisitions%' THEN 'tier2'
//         WHEN title ILIKE '%Managing Director Acquisitions%' THEN 'tier2'
//         WHEN title ILIKE '%Managing Director of Capital Markets%' THEN 'tier2'
//         WHEN title ILIKE '%Managing Director Capital Markets%' THEN 'tier2'
//         WHEN title ILIKE '%Managing Director Real Estate Investments%' THEN 'tier2'
//         WHEN title ILIKE '%Managing Director of Real Estate Investments%' THEN 'tier2'
//         WHEN title ILIKE '%Managing Director%' THEN 'tier2'

//         WHEN title ILIKE '%Partner of Real Estate Finance%' THEN 'tier2'
//         WHEN title ILIKE '%Partner Real Estate Finance%' THEN 'tier2'
//         WHEN title ILIKE '%Partner of Real Estate%' THEN 'tier2'
//         WHEN title ILIKE '%Partner Real Estate%' THEN 'tier2'
//         WHEN title ILIKE '%Partner Real Estate Investment%' THEN 'tier2'
//         WHEN title ILIKE '%Partner%' THEN 'tier2'

//         WHEN title ILIKE '%Principal - Real Estate%' THEN 'tier2'
//         WHEN title ILIKE '%Principal%' THEN 'tier2'

//         -- Tier 3 - Mid-Level Executives
//         WHEN title ILIKE '%Director of Acquisitions%' THEN 'tier3'
//         WHEN title ILIKE '%Director, Acquisitions%' THEN 'tier3'
//         WHEN title ILIKE '%Director of Capital Markets%' THEN 'tier3'
//         WHEN title ILIKE '%Director, Capital Markets%' THEN 'tier3'
//         WHEN title ILIKE '%Director of Investments%' THEN 'tier3'
//         WHEN title ILIKE '%Director, Investments%' THEN 'tier3'
//         WHEN title ILIKE '%Director of CRE Lending%' OR title ILIKE '%Director CRE Lending%' THEN 'tier3'
//         WHEN title ILIKE '%Director of Structured Finance%' THEN 'tier3'
//         WHEN title ILIKE '%Director, Structured Finance%' THEN 'tier3'
//         WHEN title ILIKE '%Director of Real Estate%' THEN 'tier3'
//         WHEN title ILIKE '%Director of Structured Investments%' THEN 'tier3'
//         WHEN title ILIKE '%Director of Real Estate Strategy%' THEN 'tier3'
//         WHEN title ILIKE '%Director, Real Estate Strategy%' THEN 'tier3'
//         WHEN title ILIKE '%Director of Underwriting%' THEN 'tier3'
//         WHEN title ILIKE '%Director, Underwriting%' THEN 'tier3'
//         WHEN title ILIKE '%Director of Debt & Equity Placement%' THEN 'tier3'
//         WHEN title ILIKE '%Director, Debt & Equity Placement%' THEN 'tier3'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of Acquisitions%', '%VP of Acquisitions%', '%VP, Acquisitions%'
//         ]) THEN 'tier3'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of Capital Markets%', '%VP of Capital Markets%', '%VP, Capital Markets%', '%VP Capital Markets%'
//         ]) THEN 'tier3'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of CRE Lending%', '%VP of CRE Lending%', '%VP, CRE Lending%'
//         ]) THEN 'tier3'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of Investments%', '%VP of Investments%', '%VP, Investments%'
//         ]) THEN 'tier3'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of Real Estate%', '%VP of Real Estate%', '%VP, Real Estate%'
//         ]) THEN 'tier3'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of Structured Finance%', '%VP of Structured Finance%', '%VP, Structured Finance%'
//         ]) THEN 'tier3'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of Underwriting%', '%VP of Underwriting%', '%VP, Underwriting%'
//         ]) THEN 'tier3'

//         WHEN title ILIKE ANY (ARRAY[
//             '%Vice President of Debt & Equity Placement%', '%VP of Debt & Equity Placement%', '%VP, Debt & Equity Placement%'
//         ]) THEN 'tier3'

//         -- Tier 4 - Investment / Portfolio Managers
//         WHEN title ILIKE '%CRE Origination Manager%' THEN 'tier4'
//         WHEN title ILIKE '%Investment Manager Real Estate%' THEN 'tier4'
//         WHEN title ILIKE '%Investment Officer Real Estate%' THEN 'tier4'
//         WHEN title ILIKE '%Portfolio Manager - Real Estate%' OR title ILIKE '%Portfolio Manager, Real Estate%' THEN 'tier4'
//         WHEN title ILIKE '%Real Assets Manager%' THEN 'tier4'
//         WHEN title ILIKE '%Real Estate Portfolio Strategist%' THEN 'tier4'

//         -- Tier 5 - Analysts & Associates
//         WHEN title ILIKE '%Acquisitions Associate%' THEN 'tier5'
//         WHEN title ILIKE '%Capital Markets Associate%' THEN 'tier5'
//         WHEN title ILIKE '%Capital Markets Analyst%' THEN 'tier5'
//         WHEN title ILIKE '%Real Estate Investment Analyst%' THEN 'tier5'
//         WHEN title ILIKE '%Real Estate Acquisitions Analyst%' THEN 'tier5'
//         WHEN title ILIKE '%Real Estate Private Equity Analyst%' THEN 'tier5'
//         WHEN title ILIKE '%Investment Associate Real Estate%' THEN 'tier5'
//         WHEN title ILIKE '%CRE Origination Analyst%' THEN 'tier5'
//         WHEN title ILIKE '%CRE Originations Associate%' THEN 'tier5'
//         WHEN title ILIKE '%CRE Originations%' OR title ILIKE '%Originations%' THEN 'tier5'

//         -- Default fallback
//         ELSE 'tier0'
//     END AS tier
//     FROM contacts
//   )
//   SELECT
//     alike_string,
//     tier,
//     ARRAY_AGG(DISTINCT title ORDER BY title) AS job_titles,
//     COUNT(*) AS total_count
//   FROM matched_titles
//   WHERE alike_string IS NOT NULL
//   GROUP BY alike_string, tier
//   ORDER BY total_count DESC;
// `;

// (async () => {
//   const client = await pool.connect();
//   try {
//     const res = await client.query(query);

//     const formatted = res.rows.map((row) => ({
//       alike_string: row.alike_string,
//       job_titles: `{${row.job_titles.join(", ")}}`,
//       total_count: row.total_count,
//       tier: row.tier,
//     }));

//     await csvWriterInstance.writeRecords(formatted);
//     console.log('✅ CSV successfully written to job_title_summary.csv');
//   } catch (err) {
//     console.error('❌ Error during execution:', err.stack);
//   } finally {
//     client.release();
//     await pool.end();
//   }
// })();

async function updateLinkedinUrls() {
  // Establish a connection to the database pool.
  const client = await pool.connect();

  try {
    // Begin a new transaction.
    await client.query("BEGIN");

    // Execute a single, powerful UPDATE statement to modify all relevant rows at once.
    // This is significantly more efficient than fetching, looping, and updating one by one.
    const res = await client.query(
      `UPDATE contacts
        SET linkedin_url=REPLACE(linkedin_url, 'http://', 'https://')
        WHERE linkedin_url LIKE 'http://%'`
    );

    // Commit the transaction to make the changes permanent.
    await client.query("COMMIT");

    console.log(
      `Transaction committed successfully. ${res.rowCount} rows were updated.`
    );
  } catch (err) {
    // If any error occurs, roll back the entire transaction.
    await client.query("ROLLBACK");
    console.error("Transaction rolled back due to error:", err);
  } finally {
    // Always release the client back to the pool.
    client.release();
    // Close the pool connection.
    await pool.end();
  }
}

// async function exportUniqueCompanyWebsitesToCSV() {
//   const client = await pool.connect();
//   try {
//     await client.query("BEGIN");

//     // 1. Total companies
//     const totalCompaniesRes = await client.query(
//       `SELECT COUNT(*) FROM companies`
//     );
//     const totalCompanies = parseInt(totalCompaniesRes.rows[0].count, 10);

//     // 2. Number of blanks
//     const blanksRes = await client.query(
//       `SELECT COUNT(*) FROM companies WHERE company_website IS NULL OR company_website = ''`
//     );
//     const numBlanks = parseInt(blanksRes.rows[0].count, 10);

//     // 3. All non-blank websites and names
//     const res = await client.query(
//       `SELECT company_website, company_name FROM companies WHERE company_website IS NOT NULL AND company_website <> ''`
//     );

//     // Map: domain -> Set of company names
//     const domainToNames = new Map();
//     for (const row of res.rows) {
//       const website = row.company_website.trim();
//       if (!website) continue;
//       if (!domainToNames.has(website)) {
//         domainToNames.set(website, new Set());
//       }
//       domainToNames.get(website).add(row.company_name || "");
//     }

//     // 4. Number of duplicate domains
//     let numDuplicates = 0;
//     for (const namesSet of domainToNames.values()) {
//       if (namesSet.size > 1) numDuplicates++;
//     }

//     // Prepare CSV rows
//     const csvRows = [["company_website", "company_names", "count"]];
//     for (const [domain, namesSet] of domainToNames.entries()) {
//       const namesArr = Array.from(namesSet);
//       csvRows.push([domain, namesArr.join("|"), namesArr.length]);
//     }
//     // Add total row
//     const allDomains = Array.from(domainToNames.keys()).join("|");
//     csvRows.push(["TOTAL", allDomains, ""]);
//     // Write CSV
//     const csvContent = csvRows.map((row) => row.join(",")).join("\n");
//     const outPath = path.join(process.cwd(), "company_websites.csv");
//     fs.writeFileSync(outPath, csvContent, "utf8");
//     await client.query("COMMIT");

//     // Print stats
//     console.log(`Total companies in DB: ${totalCompanies}`);
//     console.log(`Number of companies with blank website: ${numBlanks}`);
//     console.log(
//       `Number of unique company_website values: ${domainToNames.size}`
//     );
//     console.log(`Number of duplicate company_website values: ${numDuplicates}`);
//     console.log(
//       `Exported ${domainToNames.size} unique company_website values to company_websites.csv`
//     );
//   } catch (err) {
//     await client.query("ROLLBACK");
//     console.error("Transaction rolled back due to error:", err);
//   } finally {
//     client.release();
//     await pool.end();
//   }
// }

updateLinkedinUrls();

// exportUniqueCompanyWebsitesToCSV();
