const path = require('path');
const fs = require('fs');

// Define paths
const rootDir = __dirname;
const anaxDashDir = path.join(rootDir, 'dash');
const caseStudiesDir = path.join(rootDir, 'studies');
const prefectDir = path.join(rootDir, 'prefect');
const prefectEnvPath = path.join(prefectDir, 'prefect-env');
const logsDir = path.join(rootDir, 'logs');

// Ensure logs directory exists
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

module.exports = {
    apps: [
        // Anax Dashboard - Next.js App (Production)
        {
            name: 'dash',
            script: 'pnpm',
            args: 'prod-server',
            cwd: anaxDashDir,
            autorestart: true,
            watch: false,
            interpreter: '/bin/bash',
            max_memory_restart: '4G',
            error_file: path.join(logsDir, 'dash-error.log'),
            out_file: path.join(logsDir, 'dash-out.log'),
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            merge_logs: true,
            instances: 1,

            disable_logs: false,
            env: {
                NODE_ENV: 'production',
                // Suppress debug logs in productio
            }
        },

        // Anax Case Studies - Vite React App (Production)
        {
            name: 'studies',
            script: 'pnpm',
            args: 'prod-server',
            cwd: caseStudiesDir,
            autorestart: true,
            watch: false,
            interpreter: '/bin/bash',
            max_memory_restart: '1G',
            error_file: path.join(logsDir, 'studies-error.log'),
            out_file: path.join(logsDir, 'studies-out.log'),
            log_file: path.join(logsDir, 'studies-combined.log'),
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            merge_logs: true,
            instances: 1,

            disable_logs: false,
            env: {
                NODE_ENV: 'production',
                // Suppress debug logs in production
                LOG_LEVEL: 'info',
                NODE_LOG_LEVEL: 'info',
                // Disable verbose console logging
                DEBUG: '',
                // Suppress Vite debug output
                VITE_LOG_LEVEL: 'info'
            }
        },

        // Prefect Server
        {
            name: 'prefect-server',
            script: path.join(prefectEnvPath, 'bin', 'prefect'),
            args: 'server start',
            interpreter: path.join(prefectEnvPath, 'bin', 'python3'),
            cwd: prefectDir,
            env: {
                PATH: `${prefectEnvPath}/bin:${process.env.PATH}`,
                PYTHONPATH: prefectDir,
                PREFECT_API_URL: 'http://127.0.0.1:4200/api'
            },
            autorestart: true,
            watch: false,
            max_memory_restart: '1G',
            error_file: path.join(logsDir, 'prefect-server-error.log'),
            out_file: path.join(logsDir, 'prefect-server-out.log'),
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            merge_logs: true,
            instances: 1
        },

        // Prefect Worker - News Pipeline
        {
            name: 'prefect-worker',
            script: path.join(prefectEnvPath, 'bin', 'prefect'),
            args: 'worker start -p news-pipeline-pool',
            interpreter: path.join(prefectEnvPath, 'bin', 'python3'),
            cwd: prefectDir,
            env: {
                PATH: `${prefectEnvPath}/bin:${process.env.PATH}`,
                PYTHONPATH: prefectDir,
                PREFECT_API_URL: 'http://127.0.0.1:4200/api'
            },
            autorestart: true,
            watch: false,
            max_memory_restart: '1G',
            error_file: path.join(logsDir, 'prefect-worker-error.log'),
            out_file: path.join(logsDir, 'prefect-worker-out.log'),
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            merge_logs: true,
            instances: 1,
            dependency: ['prefect-server']
        },

        // News Pipeline
        {
            name: 'news-pipeline',
            script: path.join(prefectDir, 'flows', 'deployment.py'),
            interpreter: path.join(prefectEnvPath, 'bin', 'python3'),
            args: '--schedule daily',
            cwd: prefectDir,
            env: {
                PATH: `${prefectEnvPath}/bin:${process.env.PATH}`,
                PYTHONPATH: prefectDir,
                PREFECT_API_URL: 'http://127.0.0.1:4200/api',
                HEADLESS: 'true'
            },
            autorestart: false,
            watch: false,
            max_memory_restart: '1G',
            error_file: path.join(logsDir, 'news-pipeline-error.log'),
            out_file: path.join(logsDir, 'news-pipeline-out.log'),
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            merge_logs: true,
            instances: 1,
            dependency: ['prefect-server', 'prefect-worker']
        }
    ]
};