# V2 Filtering System Documentation

## Overview

The V2 Filtering System is an enhanced version of the existing filtering capabilities, designed to work with the new data structures from ContactEnrichmentProcessorV2 and CompanyOverviewProcessorV2. It provides comprehensive multi-table filtering with advanced analytics and AI-enhanced data.

## Features

### Core Capabilities
- **Multi-Table JOIN Queries**: Filter across multiple database tables simultaneously
- **Enhanced V2 Data Fields**: Access to new enriched data from AI processors
- **NOT Filters**: Exclude specific values with toggle-able NOT logic
- **Conditional Filtering**: Show/hide filter sections based on related data
- **Advanced Range Filtering**: Support for numeric ranges with real-time validation
- **Boolean Toggle Filters**: Easy on/off switches for presence-based filtering
- **Dynamic Filter Options**: Auto-populated filter options based on actual data

## Components

### ContactUnifiedFiltersV2 (`src/components/dashboard/people/ContactUnifiedFiltersV2.tsx`)

Enhanced contact filtering with comprehensive V2 enrichment data:

#### Core Contact Table Filters
- **Source**: Contact data sources with count display
- **Email Status**: Email verification status (Verified, Invalid, Unknown, Failed)
- **Job Tier**: Hierarchical job positions with counts
- **Contact Location**: Country, State, City location filtering
- **Processing Status**: Email verification, enrichment, generation, sending statuses

#### Contact Enrichment V2 Filters
- **Contact Type**: AI-detected contact categories
- **Relationship Owner**: Assigned relationship managers
- **Role in Decision Making**: Decision-making authority levels
- **Source of Introduction**: How contacts were introduced
- **Personal Information**: 
  - Executive summary presence
  - Career timeline availability
  - Additional contact methods (emails, phones)
- **Social Media Presence**: Twitter, Facebook, Instagram, YouTube accounts
- **Education Information**: College, high school, graduation years
- **Personal Details**: Achievements, hobbies, age ranges, addresses
- **Compliance**: Accredited investor status

#### Company Processor Flags
- **Website Scraping Status**: Related company processing status
- **Company Overview Status**: Company data extraction status
- **Company Overview V2 Status**: Enhanced company processing status

#### Investment Criteria Filters
- **Capital Position**: Investment focus areas
- **Deal Size Ranges**: Minimum and maximum deal sizes
- **Property Types**: Real estate property categories
- **Strategies**: Investment strategies
- **Geographic Filters**: Countries, regions, states, cities
- **Conditional Loan Fields**: Shown only for relevant capital positions
  - Loan types, terms, interest rates
  - LTV, LTC ratios
  - Origination and exit fees
  - DSCR requirements

#### Gmail Outreach Filters
- **Email Outreach Status**: Reached out vs not reached out

### CompanyUnifiedFiltersV2 (`src/components/dashboard/companies/CompanyUnifiedFiltersV2.tsx`)

Enhanced company filtering with comprehensive V2 overview data:

#### Core Companies Table Filters
- **Basic Information**: Address, city, state, country, website
- **Industry & Source**: Industry categories and data sources
- **Processing Status**: Website scraping, overview, overview V2 statuses

#### Company Overview V2 Filters
- **Core Company Information**:
  - Company type and business model
  - Founded year ranges
- **Investment & Strategy**:
  - Investment focus areas
  - Investment strategy mission and approach
- **Contact Information**:
  - Phone and email availability flags
  - Social media presence (Twitter, LinkedIn, etc.)
- **Location Information**:
  - Headquarters details
  - Office locations
- **Financial Metrics**:
  - Fund size and AUM ranges
  - Property, office, and employee counts
  - Revenue and financial ratios
- **Financial Information**:
  - Balance sheet strength
  - Funding sources
  - Credit ratings
  - Dry powder and deployment targets
- **Investment & Fund Information**:
  - Investment vehicle types
  - Fundraising status
  - Lender types and loan volumes
  - Portfolio health
- **Partnership & Leadership**:
  - Key partnerships
  - Executive teams
  - Equity and debt partners
- **Market Positioning**:
  - Market cycle positioning
  - Urban vs suburban preferences
  - ESG focus, PropTech adoption
  - Regulatory expertise
- **Corporate Structure**:
  - Parent companies and subsidiaries
  - Stock ticker symbols and exchanges
- **Business Information**:
  - Target customer profiles
  - Major competitors
  - Industry awards and recognitions
- **Transaction & Portfolio Data**:
  - Historical transaction metrics
  - Portfolio statistics
- **Relationship & Pipeline Data**:
  - Internal relationship managers
  - Pipeline status and news sentiment

#### Contact Processor Filters
- **Associated Contacts**: Filter companies by contact availability
- **Contact Processing Status**: Related contact processing statuses

#### Investment Criteria Filters
- Same comprehensive investment criteria as contacts

## API Endpoints

### Contact Filter Options V2
**Endpoint**: `GET /api/contacts/filter-options-v2`

**Parameters**:
- `type`: Filter option category
  - `enrichment_v2`: V2 enrichment field options
  - `company_processors`: Related company processing options

**Response Structure**:
```json
{
  "contactTypes": [{"value": "string", "label": "string", "count": "number"}],
  "relationshipOwners": [...],
  "rolesInDecisionMaking": [...],
  "sourcesOfIntroduction": [...],
  "educationColleges": [...],
  "educationHighSchools": [...],
  "ageRanges": [...]
}
```

### Company Filter Options V2
**Endpoint**: `GET /api/companies/filter-options-v2`

**Parameters**:
- `type`: Filter option category
  - `core`: Core company table options
  - `overview_v2`: V2 overview field options
  - `contact_processors`: Related contact processing options

**Response Structure**:
```json
{
  "companyTypes": [{"value": "string", "label": "string", "count": "number"}],
  "businessModels": [...],
  "investmentFocus": [...],
  "partnerships": [...],
  // ... extensive V2 fields
}
```

## Usage Examples

### Basic Implementation

```tsx
import ContactUnifiedFiltersV2 from '@/components/dashboard/people/ContactUnifiedFiltersV2';
import { ContactUnifiedFiltersV2 } from '@/types/unified-filters-v2';

function ContactsPage() {
  const [filters, setFilters] = useState<ContactUnifiedFiltersV2>({
    page: 1,
    limit: 25,
    sortBy: 'updated_at',
    sortOrder: 'desc'
  });

  const handleFiltersChange = (newFilters: ContactUnifiedFiltersV2) => {
    setFilters(newFilters);
    // Apply filters to data fetching logic
  };

  const handleClearFilters = () => {
    setFilters({
      page: 1,
      limit: 25,
      sortBy: 'updated_at',
      sortOrder: 'desc'
    });
  };

  return (
    <ContactUnifiedFiltersV2
      filters={filters}
      onFiltersChange={handleFiltersChange}
      onClearFilters={handleClearFilters}
      isLoading={loading}
    />
  );
}
```

### Advanced Filtering

```tsx
// Example: Filter contacts with specific V2 enrichment criteria
const advancedFilters: ContactUnifiedFiltersV2 = {
  // Core filters
  source: ['LinkedIn', 'Cold Outreach'],
  emailStatus: ['Verified'],
  
  // V2 Enrichment filters
  contactType: ['Decision Maker', 'Influencer'],
  accreditedInvestorStatus: true,
  hasExecutiveSummary: true,
  hasTwitter: true,
  
  // Investment criteria
  capitalPosition: ['Equity', 'Mezzanine'],
  propertyTypes: ['Multifamily', 'Office'],
  dealSizeMin: 10,
  dealSizeMax: 100,
  
  // NOT filters
  notSource: ['Spam', 'Invalid'],
  notContactType: ['Spam Contact'],
  
  page: 1,
  limit: 25
};
```

## Key Features

### 1. Enhanced Search
Global search works across all V2 enriched fields including:
- Names, emails, companies
- Executive summaries
- Career timelines
- Education information
- Partnership details
- Investment focus areas

### 2. Smart Filter Organization
Filters are organized into logical sections:
- **Core Table Data**: Primary database fields
- **V2 Enhanced Data**: AI-processed enrichments
- **Related Table Data**: Cross-table relationships
- **Processing Flags**: System processing statuses

### 3. Conditional Logic
- Loan-specific fields appear only when relevant capital positions are selected
- Related company data shows when companies are associated
- Dynamic options based on actual database content

### 4. NOT Filter Support
Toggle between inclusive and exclusive filtering:
- Eye icon (👁) = Include these values
- Eye-off icon (👁‍🗨) = Exclude these values
- Available for key categorical filters

### 5. Real-time Validation
- Range inputs validate numeric values
- Auto-completion for text fields
- Dynamic option loading based on selections

## Technical Architecture

### Type Definitions
Located in `src/types/unified-filters-v2.ts`:
- `ContactUnifiedFiltersV2`: Comprehensive contact filtering interface
- `CompanyUnifiedFiltersV2`: Comprehensive company filtering interface

### Component Structure
```
FilterComponent/
├── Filter Bar (top-level controls)
├── Filter Panel (right-side drawer)
│   ├── Global Search
│   ├── Section 1: Core Table
│   ├── Section 2: V2 Enhancements
│   ├── Section 3: Related Data
│   └── Section 4: Processing Flags
└── Footer (apply/reset actions)
```

### Data Flow
1. **Component Mount**: Fetch all filter options via API
2. **User Interaction**: Update pending filters (not applied yet)
3. **Apply Action**: Push pending filters to parent component
4. **Parent Integration**: Use filters for data queries

## Benefits

### 1. Enhanced User Experience
- **Comprehensive Filtering**: Access to all V2 enriched data
- **Intuitive Interface**: Logical grouping and visual indicators
- **Real-time Feedback**: Instant filter counts and validation

### 2. Improved Data Discovery
- **Multi-dimensional Analysis**: Filter across multiple data aspects
- **Relationship Insights**: Cross-table filtering capabilities
- **Quality Assessment**: Filter by data completeness and confidence

### 3. Advanced Analytics
- **Conditional Filtering**: Context-aware filter availability
- **Range Analysis**: Numeric range filtering for financial metrics
- **Boolean Logic**: Complex inclusion/exclusion patterns

### 4. Developer Friendly
- **Type Safety**: Full TypeScript support
- **Extensible**: Easy to add new filter types
- **Maintainable**: Clear separation of concerns

## Future Enhancements

### Planned Features
1. **Saved Filter Sets**: Save and recall common filter combinations
2. **Filter Sharing**: Share filter configurations between users
3. **Advanced Operators**: Support for complex logical operations
4. **Filter Analytics**: Track most-used filter combinations
5. **Export Capabilities**: Export filtered data with applied criteria

### Performance Optimizations
1. **Lazy Loading**: Load filter options on demand
2. **Caching**: Cache frequently-used filter options
3. **Debounced Updates**: Reduce API calls during rapid filtering
4. **Virtual Scrolling**: Handle large filter option lists

## Best Practices

### 1. Filter Strategy
- Start with broad filters and narrow down progressively
- Use NOT filters to exclude known irrelevant data
- Combine multiple filter types for precise targeting

### 2. Performance Considerations
- Apply most selective filters first
- Use range filters for large numeric datasets
- Consider pagination for large result sets

### 3. User Experience
- Provide clear feedback on active filters
- Show result counts when possible
- Offer quick reset options for complex filter states

This V2 filtering system represents a significant enhancement to the data discovery and analysis capabilities, providing users with powerful tools to navigate and analyze the enriched contact and company data effectively.
