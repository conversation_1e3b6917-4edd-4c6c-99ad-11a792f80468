# Processor Limits Configuration

This document explains how to configure dynamic limits for processor jobs in the Anax Dashboard.

## Overview

The processor scheduler now supports dynamic configuration of how many records each processor will process per run. This allows you to:

- Control resource usage and API rate limits
- Optimize processing performance
- Prevent overwhelming external services
- Scale processing based on your infrastructure

## Configuration Methods

### 1. Environment Variables (Recommended)

Set environment variables to configure processor limits:

```bash
# Email Validator
EMAIL_VALIDATOR_DEFAULT_LIMIT=100
EMAIL_VALIDATOR_MAX_LIMIT=1000
EMAIL_VALIDATOR_BATCH_SIZE=20

# Company Web Crawler
COMPANY_WEB_CRAWLER_DEFAULT_LIMIT=50
COMPANY_WEB_CRAWLER_MAX_LIMIT=500
COMPANY_WEB_CRAWLER_BATCH_SIZE=15

# Company Overview
COMPANY_OVERVIEW_DEFAULT_LIMIT=50
COMPANY_OVERVIEW_MAX_LIMIT=500
COMPANY_OVERVIEW_BATCH_SIZE=15

# Company Overview V2
COMPANY_OVERVIEW_V2_DEFAULT_LIMIT=50
COMPANY_OVERVIEW_V2_MAX_LIMIT=500
COMPANY_OVERVIEW_V2_BATCH_SIZE=15

# Company Investment Criteria
COMPANY_INVESTMENT_CRITERIA_DEFAULT_LIMIT=30
COMPANY_INVESTMENT_CRITERIA_MAX_LIMIT=300
COMPANY_INVESTMENT_CRITERIA_BATCH_SIZE=10

# Contact Enrichment
CONTACT_ENRICHMENT_DEFAULT_LIMIT=100
CONTACT_ENRICHMENT_MAX_LIMIT=1000
CONTACT_ENRICHMENT_BATCH_SIZE=20

# Contact Enrichment V2
CONTACT_ENRICHMENT_V2_DEFAULT_LIMIT=100
CONTACT_ENRICHMENT_V2_MAX_LIMIT=1000
CONTACT_ENRICHMENT_V2_BATCH_SIZE=20

# Contact Investment Criteria
CONTACT_INVESTMENT_CRITERIA_DEFAULT_LIMIT=50
CONTACT_INVESTMENT_CRITERIA_MAX_LIMIT=500
CONTACT_INVESTMENT_CRITERIA_BATCH_SIZE=15

# Email Generation
EMAIL_GENERATION_DEFAULT_LIMIT=100
EMAIL_GENERATION_MAX_LIMIT=1000
EMAIL_GENERATION_BATCH_SIZE=20

# News HTML Fetcher
NEWS_HTML_FETCHER_DEFAULT_LIMIT=50
NEWS_HTML_FETCHER_MAX_LIMIT=500
NEWS_HTML_FETCHER_BATCH_SIZE=15

# News Enrichment
NEWS_ENRICHMENT_DEFAULT_LIMIT=50
NEWS_ENRICHMENT_MAX_LIMIT=500
NEWS_ENRICHMENT_BATCH_SIZE=15
```

### 2. API Configuration

Use the API endpoint to get and update processor limits:

```bash
# Get current limits
GET /api/processor-limits

# Update limits for a processor
POST /api/processor-limits
{
  "processorName": "email_validator",
  "limit": 150,
  "maxLimit": 1200,
  "batchSize": 25
}
```

### 3. Manual Job Execution

When running jobs manually, you can override the default limits:

```typescript
// Execute with custom limit
await processorScheduler.executeManualJob('email_validation', {
  limit: 200,
  batchSize: 30
})

// Execute with default limits
await processorScheduler.executeManualJob('email_validation')
```

## Configuration Parameters

### Default Limit
- **Purpose**: Number of records to process in each scheduled run
- **Default**: Varies by processor (30-100 records)
- **Usage**: Controls the workload per job execution

### Max Limit
- **Purpose**: Maximum allowed limit for manual job execution
- **Default**: Varies by processor (300-1000 records)
- **Usage**: Prevents excessive resource usage in manual runs

### Batch Size
- **Purpose**: Number of records to process concurrently within a batch
- **Default**: Varies by processor (10-20 records)
- **Usage**: Controls concurrency and memory usage

## Default Values

| Processor | Default Limit | Max Limit | Batch Size |
|-----------|---------------|-----------|------------|
| Email Validator | 100 | 1000 | 20 |
| Company Web Crawler | 50 | 500 | 15 |
| Company Overview | 50 | 500 | 15 |
| Company Overview V2 | 50 | 500 | 15 |
| Company Investment Criteria | 30 | 300 | 10 |
| Contact Enrichment | 100 | 1000 | 20 |
| Contact Enrichment V2 | 100 | 1000 | 20 |
| Contact Investment Criteria | 50 | 500 | 15 |
| Email Generation | 100 | 1000 | 20 |
| News HTML Fetcher | 50 | 500 | 15 |
| News Enrichment | 50 | 500 | 15 |

## Best Practices

### 1. Start Conservative
- Begin with lower limits and increase gradually
- Monitor system performance and API usage
- Watch for rate limiting errors

### 2. Consider API Rate Limits
- Email validation: 1 request/second
- Company overview V2: 1.5 seconds between requests
- Contact enrichment V2: 1.8 seconds between requests
- Company investment criteria: 2 seconds between requests

### 3. Monitor Resource Usage
- Check CPU and memory usage during processing
- Monitor database connection pool usage
- Watch for timeout errors

### 4. Scale Based on Infrastructure
- Higher limits for dedicated servers
- Lower limits for shared hosting
- Adjust based on available memory and CPU

## Troubleshooting

### Common Issues

1. **Jobs timing out**
   - Reduce batch size
   - Increase timeout settings
   - Check for slow external API responses

2. **Rate limiting errors**
   - Reduce default limits
   - Increase minTime in bottleneck config
   - Check API provider rate limits

3. **Memory issues**
   - Reduce batch size
   - Lower concurrent job limits
   - Monitor memory usage during processing

### Validation

The system automatically validates limits:
- Limits must be greater than 0
- Default limit cannot exceed max limit
- Batch size must be reasonable for the processor type

## Migration from Previous Version

If you were previously using unlimited processing:

1. Set conservative default limits initially
2. Monitor job completion times
3. Gradually increase limits based on performance
4. Set appropriate max limits for manual runs

## API Reference

### GET /api/processor-limits
Returns current processor limit configuration.

**Response:**
```json
{
  "success": true,
  "data": {
    "email_validator": {
      "defaultLimit": 100,
      "maxLimit": 1000,
      "batchSize": 20
    },
    // ... other processors
  }
}
```

### POST /api/processor-limits
Updates processor limit configuration.

**Request:**
```json
{
  "processorName": "email_validator",
  "limit": 150,
  "maxLimit": 1200,
  "batchSize": 25
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "processorName": "email_validator",
    "limits": {
      "defaultLimit": 150,
      "maxLimit": 1200,
      "batchSize": 25
    },
    "message": "Limits updated successfully (Note: Changes require restart to take effect)"
  }
}
```
