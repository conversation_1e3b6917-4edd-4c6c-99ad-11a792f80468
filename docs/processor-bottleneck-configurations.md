# Processor-Specific Bottleneck Configurations

This document summarizes the processor-specific Bottleneck rate limiting configurations that have been implemented to optimize external API usage and prevent rate limit violations.

## Summary of Changes

All processors now use Bottleneck for multi-processing with rate limiting and concurrency control. Each processor has been configured with optimal settings based on their external API dependencies.

## Processor Configurations

### EmailValidatorProcessor (BulkEmailChecker API)

**External API**: BulkEmailChecker API  
**Rate Limits**: Conservative approach for email verification services

```typescript
{
  maxConcurrent: 1,                    // Single concurrent request to avoid rate limits
  minTime: 2000,                       // 2 seconds between requests
  retryAttempts: 2,                    // Fewer retries for external API
  retryDelayBase: 3000,               // 3 second base delay for retries
  retryDelayMax: 15000,               // Max 15 second retry delay
  defaultPriority: 5,                  // Normal priority
  enableJobMetrics: true               // Track performance for API monitoring
}
```

### CompanyOverviewProcessorV2 (Perplexity API)

**External API**: Perplexity Sonar API  
**Rate Limits**: Balanced for comprehensive company research

```typescript
{
  maxConcurrent: 2,                    // Conservative concurrency for Perplexity API
  minTime: 1500,                       // 1.5 seconds between requests
  retryAttempts: 3,                    // Standard retries for LLM API
  retryDelayBase: 2000,               // 2 second base delay for retries
  retryDelayMax: 30000,               // Max 30 second retry delay
  defaultPriority: 3,                  // Higher priority for company overview
  enableJobMetrics: true               // Track LLM API performance
}
```

### ContactEnrichmentProcessorV2 (Perplexity API)

**External API**: Perplexity Sonar Deep Research  
**Rate Limits**: Slightly more conservative for deep enrichment

```typescript
{
  maxConcurrent: 2,                    // Conservative concurrency for Perplexity API
  minTime: 1800,                       // 1.8 seconds between requests (more conservative)
  retryAttempts: 3,                    // Standard retries for LLM API
  retryDelayBase: 2500,               // 2.5 second base delay for retries
  retryDelayMax: 30000,               // Max 30 second retry delay
  defaultPriority: 4,                  // Normal priority for contact enrichment
  enableJobMetrics: true               // Track LLM API performance
}
```

### CompanyInvestmentCriteriaProcessor (Perplexity API)

**External API**: Perplexity API  
**Rate Limits**: Conservative for complex investment criteria extraction

```typescript
{
  maxConcurrent: 2,                    // Conservative concurrency for Perplexity API
  minTime: 2000,                       // 2 seconds between requests (more conservative for complex extraction)
  retryAttempts: 3,                    // Standard retries for LLM API
  retryDelayBase: 3000,               // 3 second base delay for retries
  retryDelayMax: 45000,               // Max 45 second retry delay (longer for complex tasks)
  defaultPriority: 6,                  // Lower priority for investment criteria
  enableJobMetrics: true               // Track LLM API performance
}
```

### ContactInvestmentCriteriaProcessor (Perplexity API)

**External API**: Perplexity API  
**Rate Limits**: Balanced for contact-specific criteria processing

```typescript
{
  maxConcurrent: 2,                    // Conservative concurrency for Perplexity API
  minTime: 1700,                       // 1.7 seconds between requests
  retryAttempts: 3,                    // Standard retries for LLM API
  retryDelayBase: 2500,               // 2.5 second base delay for retries
  retryDelayMax: 40000,               // Max 40 second retry delay
  defaultPriority: 5,                  // Normal priority for contact IC
  enableJobMetrics: true               // Track LLM API performance
}
```

## Architecture Updates

### BaseProcessor Enhancements

1. **Bottleneck Integration**: All processors now inherit Bottleneck rate limiting and concurrency control
2. **Processor-wise Configuration**: Each processor can override default settings in its constructor
3. **Job Metrics**: Optional performance tracking for external API calls
4. **Retry Logic**: Built-in exponential backoff with configurable limits
5. **Event Monitoring**: Comprehensive logging of job execution and failures

### ProcessorScheduler Updates

1. **Custom Configuration Support**: Manual jobs can override Bottleneck settings
2. **Documentation**: Clear comments explaining each processor's optimization
3. **Configuration Passing**: Proper propagation of Bottleneck configs to new instances

## Usage Examples

### Default Configuration (Recommended)

```typescript
// Uses optimized settings for BulkEmailChecker API
const emailProcessor = new EmailValidatorProcessor()
await emailProcessor.process()

// Uses optimized settings for Perplexity API
const companyProcessor = new CompanyOverviewProcessorV2()
await companyProcessor.process()
```

### Custom Configuration Override

```typescript
// Override for testing with more conservative settings
const testProcessor = new ContactEnrichmentProcessorV2({
  bottleneckConfig: {
    maxConcurrent: 1,       // Even more conservative
    minTime: 5000,          // 5 seconds between requests
    retryAttempts: 1,       // Minimal retries for fast feedback
    enableJobMetrics: true
  }
})
```

### Scheduler with Custom Settings

```typescript
// Manual job with custom Bottleneck configuration
const result = await processorScheduler.executeManualJob('company_overview_v2', {
  limit: 10,
  bottleneckConfig: {
    maxConcurrent: 1,
    minTime: 4000,
    enableJobMetrics: true
  }
})
```

## Environment Variables

Default configurations can be adjusted using environment variables:

```bash
# Default settings (will be overridden by processor-specific configs)
PROCESSOR_MAX_CONCURRENT=3
PROCESSOR_MIN_TIME=100
PROCESSOR_RETRY_ATTEMPTS=3
PROCESSOR_RETRY_DELAY_BASE=1000
PROCESSOR_RETRY_DELAY_MAX=30000
PROCESSOR_JOB_TIMEOUT=300000
PROCESSOR_ENABLE_METRICS=true
```

## Benefits

1. **Rate Limit Compliance**: Prevents API violations and blacklisting
2. **Optimal Performance**: Each processor tuned for its specific API
3. **Failure Resilience**: Exponential backoff retry logic
4. **Monitoring**: Built-in metrics for performance tracking
5. **Flexibility**: Easy to override settings for special use cases
6. **Scalability**: Concurrent processing with proper throttling

## Monitoring

Each processor now provides:

- **Job Metrics**: Execution time, success rate, error tracking
- **Bottleneck Status**: Current queue status, running jobs
- **Event Logging**: Detailed logs of API calls and retries
- **Performance Data**: Average response times, throughput metrics

This implementation ensures that all external API calls are properly rate-limited while maintaining optimal processing throughput.
