# Asynchronous Upload System

This document describes the new asynchronous upload system that allows users to upload CSV/XLSX files for background processing using existing investment criteria rules and conflict detection.

## Overview

The asynchronous upload system consists of:

1. **File Storage**: CSV/XLSX files are stored in the filesystem
2. **Database Tables**: Upload metadata and data are stored in dedicated tables
3. **Background Worker**: Processes uploaded data using existing rules
4. **Status Dashboard**: UI for monitoring upload progress
5. **API Endpoints**: For managing uploads and worker processes

## Architecture

```
User Upload → File Storage → Database Tables → Background Worker → Final Data Tables
     ↓              ↓              ↓              ↓              ↓
   UI/API     FileStorageService  upload_logs   UploadProcessor  companies/contacts
                                upload_data_log                  investment_criteria
```

## Database Schema

### upload_logs
Tracks upload metadata and processing status:
- `upload_id`: Primary key
- `file_name`, `file_path`: File information
- `status`: pending, processing, completed, failed
- `header_mappings`: CSV header to database field mappings
- `total_rows`, `processed_until`: Progress tracking
- `error_status`, `retry_count`: Error handling
- `companies_processed`, `contacts_processed`: Results tracking

### upload_data_log
Stores CSV data in generic format:
- `data_id`: Primary key
- `upload_log_id`: Foreign key to upload_logs
- `headers_map`: Maps key1, key2, etc. to actual field names
- `key1` to `key50`: Generic columns for CSV data
- `processed`: Row processing status

## Setup Instructions

### 1. Database Setup

Run the SQL schema:
```bash
psql -h your-host -U your-user -d your-database -f sql-files/async_upload_tables.sql
```

### 2. File Storage Setup

The system uses the filesystem for file storage. Ensure the upload directory exists:
```bash
mkdir -p data/uploads/investors
```

### 3. Environment Configuration

No additional environment variables are required. The system uses existing database connections.

## Usage Guide

### 1. User Upload Flow

**Frontend (Enhanced CSV Uploader):**
1. User selects CSV/XLSX file
2. File is parsed and preview is shown
3. User maps headers to database fields
4. User clicks "Upload" - file is stored for background processing
5. User receives upload_id and can monitor progress

**API Endpoint:**
```
POST /api/investors/upload-async
Content-Type: multipart/form-data

- file: CSV/XLSX file
- headerMappings: JSON string of header mappings
- source: Optional source description
- uploadedBy: Optional user identifier
- llmMetadata: Optional LLM processing metadata
```

### 2. Background Processing

**Automatic Processing:**
The background worker can be run continuously:
```bash
npx tsx scripts/process-upload-worker.ts --continuous --interval=30
```

**Manual Processing:**
Process specific upload:
```bash
npx tsx scripts/process-upload-worker.ts --upload-id=123
```

Process all pending:
```bash
npx tsx scripts/process-upload-worker.ts
```

**API Trigger:**
```bash
# Process specific upload
curl -X POST /api/workers/process-uploads \
  -H "Content-Type: application/json" \
  -d '{"upload_id": 123}'

# Process all pending
curl -X POST /api/workers/process-uploads \
  -H "Content-Type: application/json" \
  -d '{"trigger_all": true}'
```

### 3. Monitoring

**Status Dashboard:**
Visit `/dashboard/upload-status` to see:
- Upload statistics
- Progress tracking
- Pending uploads
- Processing results
- Manual trigger buttons

**API Status Check:**
```bash
# Get all uploads
curl /api/workers/process-uploads

# Get specific upload
curl "/api/investors/upload-async?upload_id=123"
```

## Testing the End-to-End Flow

### 1. Database Setup Test
```sql
-- Verify tables exist
SELECT COUNT(*) FROM upload_logs;
SELECT COUNT(*) FROM upload_data_log;

-- Check indexes
SELECT indexname FROM pg_indexes WHERE tablename IN ('upload_logs', 'upload_data_log');
```

### 2. Upload Test

**Prepare Test Data:**
Create a test CSV file (`test-investors.csv`):
```csv
Company Name,Website,Contact Name,Email,Investment Focus
Example Corp,https://example.com,John Doe,<EMAIL>,Real Estate
Test Inc,https://test.com,Jane Smith,<EMAIL>,Tech Ventures
```

**Upload via API:**
```bash
curl -X POST /api/investors/upload-async \
  -F "file=@test-investors.csv" \
  -F 'headerMappings={"Company Name":"company_name","Website":"company_website","Contact Name":"full_name","Email":"email","Investment Focus":"investment_focus"}' \
  -F "source=Test Upload" \
  -F "uploadedBy=test-user"
```

Expected response:
```json
{
  "success": true,
  "upload_id": 1,
  "message": "Upload stored successfully. 2 rows queued for processing.",
  "file_info": {
    "name": "test-investors.csv",
    "size": 157,
    "type": "csv"
  }
}
```

### 3. Processing Test

**Check Upload Status:**
```bash
curl "/api/investors/upload-async?upload_id=1"
```

**Process the Upload:**
```bash
curl -X POST /api/workers/process-uploads \
  -H "Content-Type: application/json" \
  -d '{"upload_id": 1}'
```

**Verify Results:**
```sql
-- Check upload status
SELECT upload_id, file_name, status, total_rows, processed_until, 
       companies_processed, contacts_processed, conflicts_detected 
FROM upload_logs WHERE upload_id = 1;

-- Check data processing
SELECT processed, error_status FROM upload_data_log WHERE upload_log_id = 1;

-- Verify data was inserted
SELECT company_name, company_website FROM companies 
WHERE source = 'Test Upload' ORDER BY created_at DESC LIMIT 5;

SELECT full_name, email FROM contacts 
WHERE source = 'Test Upload' ORDER BY created_at DESC LIMIT 5;
```

### 4. Error Handling Test

**Test Retry Mechanism:**
```sql
-- Simulate a failed upload
UPDATE upload_logs SET status = 'failed', error_message = 'Test error', retry_count = 1 
WHERE upload_id = 1;

-- Reset for retry
UPDATE upload_logs SET status = 'pending' WHERE upload_id = 1;
```

**Test Worker Script:**
```bash
# Check stats
npx tsx scripts/process-upload-worker.ts --stats

# Process with verbose logging
npx tsx scripts/process-upload-worker.ts --upload-id=1
```

### 5. UI Dashboard Test

1. Navigate to `/dashboard/upload-status`
2. Verify dashboard loads with correct data
3. Test auto-refresh functionality
4. Upload a new file and watch real-time progress
5. Test manual processing triggers

## Production Deployment

### 1. Cron Job Setup

Add to crontab for automatic processing:
```bash
# Process uploads every 2 minutes
*/2 * * * * cd /path/to/project && npx tsx scripts/process-upload-worker.ts >> logs/upload-worker.log 2>&1

# Cleanup old logs daily
0 2 * * * find /path/to/project/logs -name "upload-worker.log.*" -mtime +7 -delete
```

### 2. System Monitoring

**Log Monitoring:**
```bash
# Monitor worker logs
tail -f logs/upload-worker.log

# Monitor application logs for upload-related errors
tail -f logs/app.log | grep -i upload
```

**Database Monitoring:**
```sql
-- Monitor pending uploads
SELECT COUNT(*) as pending_count FROM upload_logs WHERE status = 'pending';

-- Monitor failed uploads
SELECT upload_id, file_name, error_message, retry_count 
FROM upload_logs WHERE status = 'failed' AND error_status = 'fatal';

-- Monitor processing performance
SELECT AVG(processing_duration_seconds) as avg_duration_seconds
FROM upload_overview 
WHERE processing_duration_seconds IS NOT NULL AND created_at > NOW() - INTERVAL '24 hours';
```

### 3. Performance Tuning

**Worker Configuration:**
- Adjust `BATCH_SIZE` in `UploadProcessor.ts` based on server capacity
- Use multiple worker instances for high-volume processing
- Monitor memory usage during large file processing

**Database Optimization:**
```sql
-- Monitor query performance
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
WHERE query LIKE '%upload_%' 
ORDER BY total_time DESC;

-- Optimize indexes if needed
ANALYZE upload_logs;
ANALYZE upload_data_log;
```

## Troubleshooting

### Common Issues

**1. Upload Fails Immediately**
- Check file permissions on upload directory
- Verify database connection
- Check file size limits

**2. Processing Stuck**
- Check worker script is running
- Look for database connection issues
- Verify upload_data_log has data

**3. Conflicts Not Detected**
- Verify header mappings are correct
- Check existing data in companies/contacts tables
- Review conflict detection logic in UploadProcessor

### Debug Commands

```bash
# Check worker status
npx tsx scripts/process-upload-worker.ts --stats

# Process single upload with detailed logging
DEBUG=* npx tsx scripts/process-upload-worker.ts --upload-id=123

# Reset failed upload for retry
curl -X POST /api/uploads/reset-for-retry -d '{"upload_id": 123}'
```

### Log Analysis

**Upload Processing Logs:**
```bash
# Find all upload processing attempts
grep "Processing upload:" logs/upload-worker.log

# Find errors
grep "Error processing" logs/upload-worker.log

# Find completion stats
grep "Completed processing upload" logs/upload-worker.log
```

## API Reference

### Upload Endpoints

**POST /api/investors/upload-async**
- Store file for background processing
- Returns upload_id for status tracking

**GET /api/investors/upload-async?upload_id=X**
- Get specific upload status

**GET /api/investors/upload-async**
- Get all uploads (paginated)

### Worker Endpoints

**POST /api/workers/process-uploads**
- Trigger background processing
- Body: `{"upload_id": X}` or `{"trigger_all": true}`

**GET /api/workers/process-uploads**
- Get worker status and pending uploads

### Admin Endpoints

**POST /api/uploads/reset-for-retry**
- Reset failed upload for retry (admin only)
- Body: `{"upload_id": X}`

## Migration from Synchronous System

### 1. Update Frontend

Replace synchronous upload calls:
```typescript
// Old synchronous approach
const response = await fetch('/api/investors/upload-with-conflicts', {
  method: 'POST',
  body: formData
});

// New asynchronous approach
const response = await fetch('/api/investors/upload-async', {
  method: 'POST',
  body: formData
});

// Then monitor progress
const statusResponse = await fetch(`/api/investors/upload-async?upload_id=${uploadId}`);
```

### 2. Background Processing

Set up worker automation:
```bash
# Start background worker
npx tsx scripts/process-upload-worker.ts --continuous
```

### 3. Monitoring Setup

1. Deploy upload status dashboard
2. Set up log monitoring
3. Configure alerts for failed uploads

## Future Enhancements

1. **Webhook Support**: Notify external systems when processing completes
2. **Priority Queue**: Allow high-priority uploads to jump the queue
3. **Parallel Processing**: Process multiple uploads simultaneously
4. **File Validation**: Pre-validate files before storing
5. **Progress Streaming**: Real-time progress updates via WebSocket
6. **Advanced Retry Logic**: Exponential backoff for retries
7. **Data Transformation Pipeline**: Configurable data transformation rules

## Security Considerations

1. **File Validation**: Validate file types and sizes
2. **Access Control**: Implement proper authentication for admin endpoints
3. **Data Sanitization**: Sanitize CSV data before processing
4. **File Storage**: Secure file storage with proper permissions
5. **Audit Logging**: Log all upload and processing activities

## Performance Metrics

Monitor these key metrics:
- Upload success rate
- Average processing time
- Queue length
- Error rates by type
- File processing throughput
- Database performance during processing 