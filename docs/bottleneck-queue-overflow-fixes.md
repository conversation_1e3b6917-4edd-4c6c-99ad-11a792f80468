# Bottleneck Queue Overflow Fixes

## Problem Description

The EmailValidator and other processors were experiencing "This job has been dropped by Bottleneck" errors due to queue overflow. The logs showed multiple consecutive failures:

```
[2025-08-21T15:19:10.356Z] [EmailValidator] [ERROR] Job EmailValidator-entity-285794-1755789546623-201 failed: Error: This job has been dropped by Bottleneck
[2025-08-21T15:19:10.357Z] [EmailValidator] [WARN] Job EmailValidator-entity-285794-1755789546623-201 failed on attempt 2, retrying in 6000ms: This job has been dropped by Bottleneck
```

## Root Cause Analysis

1. **Queue Overflow**: All entities (potentially hundreds) were being queued simultaneously with `Promise.allSettled(processingJobs)`
2. **Default highWater Too Low**: The default `highWater` setting of 50 was insufficient for batch processing
3. **Ineffective Queue Strategy**: The `LEAK` strategy was dropping jobs instead of managing them properly
4. **High Concurrency**: `maxConcurrent` values of 5-10 were creating too many simultaneous jobs

## Applied Fixes

### 1. BaseProcessor Default Configuration Changes

**File**: `src/lib/processors/BaseProcessor.ts`

```typescript
// Before
highWater: parseInt(process.env.PROCESSOR_HIGH_WATER || '50'),
strategy: Bottleneck.strategy.LEAK,
timeout: parseInt(process.env.PROCESSOR_JOB_TIMEOUT || '300000'), // 5 minutes

// After
highWater: parseInt(process.env.PROCESSOR_HIGH_WATER || '1000'), // Increased from 50 to 1000
strategy: Bottleneck.strategy.OVERFLOW,  // Changed from LEAK to OVERFLOW
timeout: parseInt(process.env.PROCESSOR_JOB_TIMEOUT || '180000'), // Reduced to 3 minutes
```

**Enhanced Bottleneck Initialization**:
```typescript
// Before
this.bottleneck = new Bottleneck({
  maxConcurrent: this.bottleneckConfig.maxConcurrent,
  minTime: this.bottleneckConfig.minTime,
  highWater: this.bottleneckConfig.highWater,
  strategy: this.bottleneckConfig.strategy
})

// After
this.bottleneck = new Bottleneck({
  maxConcurrent: this.bottleneckConfig.maxConcurrent,
  minTime: this.bottleneckConfig.minTime,
  highWater: this.bottleneckConfig.highWater,
  strategy: this.bottleneckConfig.strategy,
  timeout: this.bottleneckConfig.timeout,
  retryDelayBase: this.bottleneckConfig.retryDelayBase,
  retryDelayMax: this.bottleneckConfig.retryDelayMax
})
```

### 2. Processor-Specific Configuration Updates

#### EmailValidatorProcessor
```typescript
// Updated Configuration
maxConcurrent: 3,           // Reduced from 10
minTime: 1000,             // Reduced from 2000ms  
timeout: 120000,           // 2 minutes
highWater: 500,            // Increased from default
strategy: 'OVERFLOW'       // Changed from LEAK
```

#### CompanyOverviewProcessorV2
```typescript
// Updated Configuration
maxConcurrent: 3,           // Reduced from 5
minTime: 1500,             // Maintained
timeout: 240000,           // 4 minutes for LLM processing
highWater: 300,            // Increased from default
strategy: 'OVERFLOW'       // Changed from LEAK
```

#### ContactEnrichmentProcessorV2
```typescript
// Updated Configuration
maxConcurrent: 3,           // Reduced from 5
minTime: 1800,             // Maintained 
timeout: 240000,           // 4 minutes for LLM processing
highWater: 300,            // Increased from default
strategy: 'OVERFLOW'       // Changed from LEAK
```

#### CompanyInvestmentCriteriaProcessor
```typescript
// Updated Configuration
maxConcurrent: 3,           // Reduced from 5
minTime: 2000,             // Maintained
timeout: 300000,           // 5 minutes for complex processing
highWater: 200,            // Lower due to complexity
strategy: 'OVERFLOW'       // Changed from LEAK
```

#### ContactInvestmentCriteriaProcessor
```typescript
// Updated Configuration
maxConcurrent: 3,           // Reduced from 5
minTime: 1000,             // Maintained (user's change)
timeout: 300000,           // 5 minutes for complex processing
highWater: 200,            // Lower due to complexity
strategy: 'OVERFLOW'       // Changed from LEAK
```

## Key Improvements

### 1. Queue Management
- **Increased `highWater` limits** to handle batch processing without dropping jobs
- **Changed strategy to `OVERFLOW`** for better queue management than `LEAK`
- **Processor-specific queue sizes** based on complexity and processing time

### 2. Concurrency Control
- **Reduced `maxConcurrent` values** from 5-10 to 3 across all processors
- **Maintains rate limiting** while preventing queue saturation
- **Balanced throughput** with stability

### 3. Timeout Management
- **Reduced default timeout** from 5 minutes to 3 minutes
- **Processor-specific timeouts**: 2 minutes for email validation, 4-5 minutes for LLM tasks
- **Prevents job accumulation** from long-running tasks

### 4. Strategy Changes
- **OVERFLOW strategy**: Queues jobs and processes them in order
- **Better than LEAK**: Doesn't drop jobs arbitrarily
- **Handles batch processing** more gracefully

## Expected Results

1. **No more job drops**: Higher `highWater` limits prevent queue overflow
2. **Stable processing**: Lower concurrency reduces system stress
3. **Better error handling**: OVERFLOW strategy maintains job integrity
4. **Predictable performance**: Consistent processing rates with proper timeouts

## Monitoring

Watch for these improvements in the logs:
- ✅ No more "This job has been dropped by Bottleneck" errors
- ✅ Steady processing rates without overwhelm
- ✅ Jobs completing within timeout windows
- ✅ Successful retry handling without drops

## Environment Variables

You can still override these settings via environment variables:

```bash
PROCESSOR_MAX_CONCURRENT=3
PROCESSOR_MIN_TIME=100  
PROCESSOR_HIGH_WATER=1000
PROCESSOR_JOB_TIMEOUT=180000
PROCESSOR_RETRY_ATTEMPTS=3
```

## Next Steps

1. **Monitor processing logs** for the absence of drop errors
2. **Adjust concurrency** if needed based on actual API limits
3. **Fine-tune timeouts** based on actual processing times
4. **Consider implementing** batch processing at the application level for very large datasets
