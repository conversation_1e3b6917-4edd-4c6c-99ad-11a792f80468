# Unified Campaign Sequence Section

## Overview

The `UnifiedCampaignSequenceSection` component combines campaign sequence management with message variables in a single, streamlined interface. This component replaces the previous separate `CampaignSequenceSection` and `MessagesSection` components to provide a more cohesive user experience.

## Features

### 🚀 **Auto-Loading Messages**
- Automatically fetches all contact messages on component mount
- Extracts variables from existing message metadata
- No need for external message state management

### 📊 **Campaign Management**
- Dropdown selection of available campaigns
- Auto-loads campaign sequence templates and variables
- Variant selection for different sequence steps

### 🔧 **Variable Management**
- **Contact Variables**: Auto-populated from contact information (read-only)
- **Custom Variables**: Editable variables extracted from messages and sequences
- **Rich Text Support**: Toggle between plain text and rich HTML editing
- **Usage Tracking**: Shows which variables are actively used in templates

### 👁️ **Live Preview**
- Real-time preview with variable replacement
- Template view showing raw template with highlighted variables
- Preview view showing final rendered content

### 🔄 **Smartlead Integration**
- One-click sync to Smartlead with all variable values
- Proper contact update handling
- Error handling and user feedback

## Usage

```tsx
import UnifiedCampaignSequenceSection from './sections/UnifiedCampaignSequenceSection';

<UnifiedCampaignSequenceSection
  contactId={contactId}
  contact={contact}
  onContactUpdate={(updatedContact) => {
    // Handle contact updates after Smartlead sync
    setContact(updatedContact);
  }}
/>
```

## Props

| Prop | Type | Description |
|------|------|-------------|
| `contactId` | `string \| number` | The ID of the contact |
| `contact` | `Contact \| undefined` | Contact information object |
| `onContactUpdate` | `(contact: Contact) => void \| undefined` | Callback when contact is updated after sync |

## Component Architecture

### State Management
The component manages all state internally:
- Messages loading and filtering
- Campaign selection and sequence data
- Variable values and rich text modes
- UI state (tabs, loading states)

### API Integration
- `GET /api/campaigns` - Fetch available campaigns
- `GET /api/messages?contact_id={id}` - Fetch contact messages
- `GET /api/smartlead/campaigns/{id}/sequence` - Fetch campaign sequence
- `POST /api/messages?contact_id={id}` - Save new messages
- `POST /api/smartlead/contacts/{id}/sync` - Sync to Smartlead

### Variable Types

#### Contact Variables (Read-only)
These are automatically populated from the contact object:
- `first_name`
- `last_name` 
- `email`
- `company_name`
- `job_title`
- `phone_number`
- `company_website`
- `industry`

#### Custom Variables (Editable)
- Extracted from message metadata
- Extracted from campaign sequence templates
- Support both plain text and rich HTML content
- Can be added, edited, and removed by users

## Variable Extraction Logic

1. **From Messages**: Extracts variables from `message.metadata.variables`
2. **From Sequences**: Extracts `{{variable_name}}` patterns from subject and body templates
3. **Priority**: Message variables take precedence over sequence defaults
4. **Contact Priority**: Contact fields always override custom variables for standard fields

## Rich Text Support

Variables support rich text editing with the following features:
- HTML content editing with toolbar
- Toggle between plain text and rich text modes
- Automatic detection of HTML content
- Sanitization for Smartlead compatibility

## Sync Process

When syncing to Smartlead:
1. Combines all variables with contact information
2. Contact fields take highest priority
3. Sends variables as custom fields to Smartlead
4. Updates contact record with Smartlead lead ID and status
5. Provides user feedback on success/failure

## Error Handling

The component includes comprehensive error handling:
- Network request failures
- Invalid campaign selections
- Variable extraction errors
- Sync failures with detailed messages

## Benefits Over Previous Implementation

### Simplified Integration
- Single component instead of managing two separate sections
- No need for external message state management
- Automatic data loading and synchronization

### Better User Experience
- Unified interface for all campaign and variable management
- Clear visual indicators for variable usage
- Streamlined sync process

### Improved Data Consistency
- Single source of truth for variables
- Proper priority handling between different variable sources
- Better metadata management

## Migration from Legacy Components

Replace both `CampaignSequenceSection` and `MessagesSection` with:

```tsx
// Before
<CampaignSequenceSection {...sequenceProps} />
<MessagesSection {...messageProps} />

// After
<UnifiedCampaignSequenceSection 
  contactId={contactId}
  contact={contact}
  onContactUpdate={handleContactUpdate}
/>
```

## Future Enhancements

- **Batch Variable Import**: Import variables from CSV or other sources
- **Variable Templates**: Save and reuse common variable sets
- **Advanced Filtering**: Filter variables by usage, type, or source
- **Variable Validation**: Validate required variables before sync
- **History Tracking**: Track variable changes over time 