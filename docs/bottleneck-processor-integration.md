# Bottleneck Integration for BaseProcessor

The BaseProcessor has been enhanced with Bottleneck integration to provide advanced rate limiting, concurrency control, and multi-processing capabilities with processor-wise configuration.

## Features

- **Rate Limiting**: Control the minimum time between job starts
- **Concurrency Control**: Limit the number of concurrent jobs
- **Retry Logic**: Built-in exponential backoff retry mechanism
- **Job Metrics**: Optional job execution tracking and performance metrics
- **Queue Management**: Advanced queue strategies for handling overflow
- **Processor-wise Configuration**: Each processor can have its own Bottleneck settings

## Configuration

### Environment Variables

Set these environment variables to configure default behavior across all processors:

```bash
# Rate limiting and concurrency
PROCESSOR_MAX_CONCURRENT=3           # Maximum concurrent jobs (default: 3)
PROCESSOR_MIN_TIME=100               # Minimum time between job starts in ms (default: 100)
PROCESSOR_HIGH_WATER=50              # Queue high water mark (default: 50)

# Retry configuration
PROCESSOR_RETRY_ATTEMPTS=3           # Number of retry attempts (default: 3)
PROCESSOR_RETRY_DELAY_BASE=1000      # Base delay for exponential backoff in ms (default: 1000)
PROCESSOR_RETRY_DELAY_MAX=30000      # Maximum retry delay in ms (default: 30000)

# Timeouts and monitoring
PROCESSOR_JOB_TIMEOUT=300000         # Job timeout in ms (default: 5 minutes)
PROCESSOR_ENABLE_METRICS=true       # Enable job execution metrics (default: false)
```

### Processor-wise Configuration

Each processor can override the default configuration by providing a `bottleneckConfig` in the `ProcessorOptions`:

```typescript
import { BaseProcessor, BottleneckConfig } from '../lib/processors/BaseProcessor'
import { ProcessorOptions } from '../types/processing'

class MyCustomProcessor extends BaseProcessor {
  constructor(options: ProcessorOptions = {}) {
    // Processor-specific Bottleneck configuration
    const customBottleneckConfig: BottleneckConfig = {
      maxConcurrent: 5,           // Allow 5 concurrent jobs for this processor
      minTime: 200,               // 200ms minimum between job starts
      retryAttempts: 5,           // More aggressive retries
      retryDelayBase: 500,        // Faster initial retry
      defaultPriority: 3,         // Higher priority jobs (lower number = higher priority)
      enableJobMetrics: true      // Enable detailed metrics for this processor
    }

    super('MyCustomProcessor', {
      ...options,
      bottleneckConfig: customBottleneckConfig
    })
  }

  async getUnprocessedEntities(): Promise<EntityData[]> {
    // Your implementation here
    return []
  }

  async processEntity(entity: EntityData): Promise<{ success: boolean; error?: string }> {
    // Your processing logic here
    return { success: true }
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    // Your status update logic here
  }
}
```

## Usage Examples

### Basic Usage

```typescript
// Create processor with default Bottleneck configuration
const processor = new MyProcessor({
  limit: 100,
  batchSize: 50
})

// Process entities with automatic rate limiting and concurrency control
const result = await processor.process()
console.log(`Processed: ${result.processed}, Successful: ${result.successful}, Failed: ${result.failed}`)
```

### Advanced Configuration

```typescript
// Create processor with custom Bottleneck settings
const highVolumeProcessor = new MyProcessor({
  limit: 1000,
  bottleneckConfig: {
    maxConcurrent: 10,          // High concurrency for bulk processing
    minTime: 50,                // Faster job scheduling
    retryAttempts: 2,           // Fewer retries for faster processing
    enableJobMetrics: true      // Track performance
  }
})

const result = await highVolumeProcessor.process()

// Get detailed job metrics
const metrics = highVolumeProcessor.getJobMetrics()
console.log('Job Metrics:', metrics)

// Get current Bottleneck status
const status = highVolumeProcessor.getBottleneckStatus()
console.log('Bottleneck Status:', status)
```

### Using executeWithBottleneck for Custom Jobs

```typescript
class MyProcessor extends BaseProcessor {
  async customProcessing(): Promise<void> {
    const entities = await this.getUnprocessedEntities()
    
    // Process entities with custom job priorities
    const promises = entities.map((entity, index) => {
      const priority = entity.urgent ? 1 : 5  // Urgent entities get higher priority
      const jobId = `custom-${entity.id}-${Date.now()}`
      
      return this.executeWithBottleneck(
        async () => {
          // Your custom processing logic
          const result = await this.processEntity(entity)
          await this.updateEntityStatus(entity.id, result.success, result.error)
          return result
        },
        jobId,
        priority,
        true  // Enable retry
      )
    })
    
    await Promise.allSettled(promises)
  }
}
```

## Migration from Legacy Processing

### Before (Sequential Processing)

```typescript
// Old sequential batch processing
for (let i = 0; i < entities.length; i += batchSize) {
  const batch = entities.slice(i, i + batchSize)
  
  for (const entity of batch) {
    await this.processEntity(entity)
    await this.sleep(batchDelay)  // Manual rate limiting
  }
}
```

### After (Bottleneck Processing)

```typescript
// New concurrent processing with Bottleneck
const jobs = entities.map((entity, index) => 
  this.executeWithBottleneck(
    () => this.processEntity(entity),
    `entity-${entity.id}-${index}`,
    undefined,  // Use default priority
    true        // Enable retry
  )
)

await Promise.allSettled(jobs)
```

## Best Practices

### 1. Choose Appropriate Concurrency Levels

```typescript
// For I/O-heavy processors (API calls, database operations)
const ioHeavyConfig: BottleneckConfig = {
  maxConcurrent: 10,
  minTime: 100
}

// For CPU-intensive processors
const cpuIntensiveConfig: BottleneckConfig = {
  maxConcurrent: require('os').cpus().length,
  minTime: 500
}

// For rate-limited external APIs
const apiLimitedConfig: BottleneckConfig = {
  maxConcurrent: 2,
  minTime: 1000  // Respect API rate limits
}
```

### 2. Use Job Priorities

```typescript
// Priority levels: 1 (highest) to 10 (lowest)
const urgentJob = this.executeWithBottleneck(fn, jobId, 1)      // Highest priority
const normalJob = this.executeWithBottleneck(fn, jobId, 5)      // Normal priority
const lowPriorityJob = this.executeWithBottleneck(fn, jobId, 9) // Low priority
```

### 3. Monitor Performance

```typescript
if (this.bottleneckConfig.enableJobMetrics) {
  const metrics = this.getJobMetrics()
  const avgDuration = Object.values(metrics)
    .filter(m => m.duration)
    .reduce((sum, m) => sum + (m.duration || 0), 0) / Object.keys(metrics).length

  console.log(`Average job duration: ${avgDuration}ms`)
  
  // Clear metrics periodically to avoid memory issues
  if (Object.keys(metrics).length > 1000) {
    this.clearJobMetrics()
  }
}
```

### 4. Graceful Cleanup

```typescript
// Always cleanup when done
try {
  await processor.process()
} finally {
  await processor.cleanup()  // Stops Bottleneck and waits for jobs to complete
}
```

## Error Handling

The new system provides enhanced error handling:

```typescript
try {
  const result = await processor.process()
  console.log('Processing completed successfully')
} catch (error) {
  console.error('Processing failed:', error)
  
  // Check job metrics for detailed error information
  const metrics = processor.getJobMetrics()
  const failedJobs = Object.entries(metrics).filter(([_, metric]) => !metric.success)
  console.log(`Failed jobs: ${failedJobs.length}`)
}
```

## Performance Tuning

### Finding Optimal Settings

1. **Start Conservative**: Begin with low concurrency (2-3) and adjust based on results
2. **Monitor Resources**: Watch CPU, memory, and network usage
3. **Test Incrementally**: Increase concurrency gradually while monitoring performance
4. **Consider External Limits**: Respect API rate limits and database connection pools

### Example Tuning Process

```typescript
// Step 1: Baseline test
const baseline = new MyProcessor({ 
  bottleneckConfig: { maxConcurrent: 1, minTime: 0 } 
})

// Step 2: Increase concurrency
const concurrent = new MyProcessor({ 
  bottleneckConfig: { maxConcurrent: 5, minTime: 100 } 
})

// Step 3: Add rate limiting
const rateLimited = new MyProcessor({ 
  bottleneckConfig: { maxConcurrent: 5, minTime: 500 } 
})

// Compare results and choose the best configuration
```

## Troubleshooting

### Common Issues

1. **High Memory Usage**: Reduce `maxConcurrent` or enable `clearJobMetrics()`
2. **Rate Limit Errors**: Increase `minTime` or reduce `maxConcurrent`
3. **Slow Processing**: Check if `minTime` is too high or `maxConcurrent` too low
4. **Job Timeouts**: Increase `timeout` in `BottleneckConfig`

### Debug Information

```typescript
// Enable debug logging
process.env.LOG_LEVEL = 'debug'

// Check Bottleneck status
const status = processor.getBottleneckStatus()
console.log('Queue status:', status)

// Monitor job metrics in real-time
setInterval(() => {
  const metrics = processor.getJobMetrics()
  console.log(`Active jobs: ${Object.keys(metrics).length}`)
}, 5000)
```
