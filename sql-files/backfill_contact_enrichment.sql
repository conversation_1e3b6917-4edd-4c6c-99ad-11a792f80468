-- Backfill script for contact_enrichment table
-- This script migrates existing data from contact_extracted_data and contact_searched_data 
-- into the consolidated contact_enrichment table

-- First, let's see what we're working with
SELECT 
  'contact_extracted_data' as table_name,
  COUNT(*) as record_count
FROM contact_extracted_data
UNION ALL
SELECT 
  'contact_searched_data' as table_name,
  COUNT(*) as record_count
FROM contact_searched_data
UNION ALL
SELECT 
  'contact_enrichment' as table_name,
  COUNT(*) as record_count
FROM contact_enrichment;

-- Insert data from contact_extracted_data and contact_searched_data into contact_enrichment
-- Using a LEFT JOIN to combine both tables on contact_id
INSERT INTO contact_enrichment (
    contact_id,
    osint_profile,
    executive_summary,
    career_timeline,
    notable_activities,
    education,
    personal_tidbits,
    conversation_hooks,
    sources,
    company_type,
    capital_positions,
    confidence,
    reasoning,
    input_data,
    prompt_content,
    tokens_used,
    llm_model,
    llm_usage,
    status,
    error_message,
    processing_attempts,
    created_at,
    updated_at,
    completed_at
)
SELECT DISTINCT
    COALESCE(e.contact_id, s.contact_id) as contact_id,
    
    -- OSINT Profile Data (from contact_searched_data)
    s.profile as osint_profile,
    
    -- Extracted Profile Data (from contact_extracted_data)
    e.executive_summary,
    e.career_timeline,
    e.notable_activities,
    e.education,
    e.personal_tidbits,
    e.conversation_hooks,
    e.sources,
    
    -- Classification Data (from contact_extracted_data)
    e.company_type,
    CASE 
        WHEN e.capital_position IS NOT NULL THEN 
            jsonb_build_array(e.capital_position)
        ELSE NULL
    END as capital_positions,
    e.classification_confidence as confidence,
    e.classification_reasoning as reasoning,
    
    -- Processing Metadata (prefer contact_searched_data input_data, fallback to extracted)
    COALESCE(s.input_data, jsonb_build_object('contact_id', COALESCE(e.contact_id, s.contact_id))) as input_data,
    COALESCE(s.prompt_content, 'Migrated from legacy tables') as prompt_content,
    COALESCE(s.tokens_used, 0) as tokens_used,
    'legacy_migration' as llm_model,
    jsonb_build_object('migration_note', 'Migrated from contact_extracted_data and contact_searched_data') as llm_usage,
    
    -- Processing Status
    CASE 
        WHEN e.contact_id IS NOT NULL AND s.contact_id IS NOT NULL THEN 'completed'
        WHEN e.contact_id IS NOT NULL AND s.contact_id IS NULL THEN 'completed'
        WHEN e.contact_id IS NULL AND s.contact_id IS NOT NULL THEN 'completed'
        ELSE 'completed'
    END as status,
    
    NULL as error_message,
    1 as processing_attempts,
    
    -- Timestamps (use the earliest created_at)
    LEAST(
        COALESCE(e.created_at, s.created_at, NOW()),
        COALESCE(s.created_at, e.created_at, NOW())
    ) as created_at,
    
    GREATEST(
        COALESCE(e.updated_at, s.updated_at, NOW()),
        COALESCE(s.updated_at, e.updated_at, NOW())
    ) as updated_at,
    
    GREATEST(
        COALESCE(e.updated_at, s.updated_at, NOW()),
        COALESCE(s.updated_at, e.updated_at, NOW())
    ) as completed_at
    
FROM contact_extracted_data e
FULL OUTER JOIN contact_searched_data s ON e.contact_id = s.contact_id
WHERE NOT EXISTS (
    SELECT 1 FROM contact_enrichment ce 
    WHERE ce.contact_id = COALESCE(e.contact_id, s.contact_id)
);

-- Show the results after migration
SELECT 
    'After Migration' as status,
    COUNT(*) as total_records,
    COUNT(CASE WHEN osint_profile IS NOT NULL THEN 1 END) as with_osint_profile,
    COUNT(CASE WHEN executive_summary IS NOT NULL THEN 1 END) as with_executive_summary,
    COUNT(CASE WHEN career_timeline IS NOT NULL THEN 1 END) as with_career_timeline,
    COUNT(CASE WHEN company_type IS NOT NULL THEN 1 END) as with_company_type,
    COUNT(CASE WHEN capital_positions IS NOT NULL THEN 1 END) as with_capital_positions,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_status
FROM contact_enrichment;

-- Check for any contacts that might have been missed
SELECT 
    'Contacts with extracted=true but not in enrichment' as check_type,
    COUNT(*) as count
FROM contacts c
WHERE c.extracted = true
AND NOT EXISTS (
    SELECT 1 FROM contact_enrichment ce WHERE ce.contact_id = c.contact_id
);

-- Check for any contacts that might have been missed (searched=true)
SELECT 
    'Contacts with searched=true but not in enrichment' as check_type,
    COUNT(*) as count
FROM contacts c
WHERE c.searched = true
AND NOT EXISTS (
    SELECT 1 FROM contact_enrichment ce WHERE ce.contact_id = c.contact_id
);

-- Sample of migrated data
SELECT 
    contact_id,
    CASE WHEN osint_profile IS NOT NULL THEN 'YES' ELSE 'NO' END as has_osint,
    CASE WHEN executive_summary IS NOT NULL THEN 'YES' ELSE 'NO' END as has_summary,
    CASE WHEN career_timeline IS NOT NULL THEN 'YES' ELSE 'NO' END as has_timeline,
    company_type,
    capital_positions,
    confidence,
    status,
    created_at,
    completed_at
FROM contact_enrichment
ORDER BY contact_id
LIMIT 10; 