-- Comprehensive Deal Strategies Mapping Function
-- Creates a function to map strategies and applies it to all deals

-- Create a function to map individual strategies
CREATE OR REPLACE FUNCTION map_deal_strategy(strategy text) 
RETURNS text AS $$
BEGIN
    RETURN CASE strategy
        WHEN 'Acquisition' THEN NULL
        WHEN 'Bridge' THEN NULL
        WHEN 'Ground-Lease' THEN NULL
        WHEN 'Permanent' THEN NULL
        WHEN 'Recapitalization' THEN NULL
        WHEN 'Refinance' THEN NULL
        WHEN 'Construction' THEN 'Opportunistic'
        WHEN 'Development' THEN 'Opportunistic'
        WHEN 'Distressed' THEN 'Opportunistic'
        WHEN 'Ground Up' THEN 'Opportunistic'
        WHEN 'Rescue Capital' THEN 'Opportunistic'
        WHEN 'Special Situations' THEN 'Opportunistic'
        WHEN 'Redevelopment' THEN 'Value-Add'
        WHEN 'Repositioning' THEN 'Value-Add'
        WHEN 'Stabilization' THEN 'Value-Add'
        WHEN 'Stabilized' THEN 'Core'
        ELSE strategy -- Keep unchanged: Core, Core Plus, Opportunistic, Value-Add
    END;
END;
$$ LANGUAGE plpgsql;

-- Create a function to map an array of strategies
CREATE OR REPLACE FUNCTION map_deal_strategies_array(strategies text[]) 
RETURNS text[] AS $$
DECLARE
    mapped_strategies text[] := ARRAY[]::text[];
    strategy text;
    mapped_strategy text;
BEGIN
    -- If input is null or empty, return null
    IF strategies IS NULL OR array_length(strategies, 1) IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Map each strategy in the array
    FOREACH strategy IN ARRAY strategies
    LOOP
        mapped_strategy := map_deal_strategy(strategy);
        
        -- Only add non-null mapped strategies
        IF mapped_strategy IS NOT NULL THEN
            mapped_strategies := array_append(mapped_strategies, mapped_strategy);
        END IF;
    END LOOP;
    
    -- Remove duplicates and return
    IF array_length(mapped_strategies, 1) IS NULL THEN
        RETURN NULL;
    ELSE
        RETURN array_remove(array_remove(mapped_strategies, NULL), '');
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Apply the mapping to all deals
UPDATE investment_criteria 
SET strategies = map_deal_strategies_array(strategies)
WHERE entity_type = 'Deal';

-- Show the results
SELECT 
    criteria_id,
    strategies as original_strategies,
    map_deal_strategies_array(strategies) as mapped_strategies
FROM investment_criteria 
WHERE entity_type = 'Deal' 
AND strategies IS NOT NULL
LIMIT 20;

-- Clean up - drop the functions if no longer needed
-- DROP FUNCTION IF EXISTS map_deal_strategy(text);
-- DROP FUNCTION IF EXISTS map_deal_strategies_array(text[]); 