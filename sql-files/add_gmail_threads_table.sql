-- Create gmail_threads table for Gmail-specific threads
CREATE TABLE IF NOT EXISTS gmail_threads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider_thread_id TEXT NOT NULL,
    account_email TEXT NOT NULL,
    subject TEXT,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE (provider_thread_id, account_email)
); 