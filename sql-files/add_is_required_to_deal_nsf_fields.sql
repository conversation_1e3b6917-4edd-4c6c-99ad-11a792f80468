-- Migration: Add is_required column to deal_nsf_fields table
-- Date: 2025-01-18
-- Description: Adding is_required column to track whether a capital position is required for the deal

-- Add new is_required column
ALTER TABLE deal_nsf_fields
ADD COLUMN is_required BOOLEAN DEFAULT FALSE;

-- Add comment to the new column
COMMENT ON COLUMN deal_nsf_fields.is_required IS 'Indicates whether this capital position is required for the deal (based on ask_capital_position)';

-- Create index for better query performance
CREATE INDEX idx_deal_nsf_fields_is_required ON deal_nsf_fields(is_required);

-- Update existing records to set is_required based on capital_position
-- This is a one-time update for existing data
UPDATE deal_nsf_fields 
SET is_required = TRUE 
WHERE capital_position IS NOT NULL 
AND capital_position IN (
    SELECT unnest(ask_capital_position) 
    FROM dealsv2 
    WHERE dealsv2.deal_id = deal_nsf_fields.deal_id
);
