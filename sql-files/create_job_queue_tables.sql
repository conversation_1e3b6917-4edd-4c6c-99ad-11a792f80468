-- Job Queue Tables
-- These tables handle job persistence for BullMQ jobs

-- Job<PERSON> table - stores job information and status
CREATE TABLE IF NOT EXISTS public.jobs (
    job_id VARCHAR(255) PRIMARY KEY,  -- BullMQ job ID
    queue_name VARCHAR(100) NOT NULL DEFAULT 'default',
    job_name VARCHAR(100) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'waiting', -- waiting, active, completed, failed, delayed, paused
    priority INTEGER DEFAULT 0,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    delay_until TIMESTAMP WITH TIME ZONE,
    
    -- Job data and results
    data JSONB,           -- Input data for the job
    result JSONB,         -- Result data from completed job
    error_message TEXT,   -- Error message if failed
    stack_trace TEXT,     -- Stack trace if failed
    
    -- Progress tracking
    progress INTEGER DEFAULT 0, -- 0-100
    progress_data JSONB,  -- Custom progress data
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    failed_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    created_by VARCHAR(255),
    processor_id VARCHAR(255), -- Which worker processed this job
    processing_duration INTEGER, -- Duration in milliseconds
    metadata JSONB,
    
    -- Indexes
    CONSTRAINT valid_status CHECK (status IN ('waiting', 'active', 'completed', 'failed', 'delayed', 'paused', 'cancelled'))
);

-- Job logs table - stores job execution logs
CREATE TABLE IF NOT EXISTS public.job_logs (
    log_id SERIAL PRIMARY KEY,
    job_id VARCHAR(255) NOT NULL REFERENCES public.jobs(job_id) ON DELETE CASCADE,
    level VARCHAR(20) NOT NULL DEFAULT 'info', -- debug, info, warn, error
    message TEXT NOT NULL,
    data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_log_level CHECK (level IN ('debug', 'info', 'warn', 'error'))
);

-- Job files table - links jobs to files they process/create
CREATE TABLE IF NOT EXISTS public.job_files (
    job_file_id SERIAL PRIMARY KEY,
    job_id VARCHAR(255) NOT NULL REFERENCES public.jobs(job_id) ON DELETE CASCADE,
    file_id UUID, -- References files table if available
    file_name VARCHAR(500),
    file_type VARCHAR(100),
    file_size BIGINT,
    relationship_type VARCHAR(100), -- input, output, attachment, etc.
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_jobs_status ON public.jobs(status);
CREATE INDEX IF NOT EXISTS idx_jobs_queue_name ON public.jobs(queue_name);
CREATE INDEX IF NOT EXISTS idx_jobs_job_name ON public.jobs(job_name);
CREATE INDEX IF NOT EXISTS idx_jobs_created_at ON public.jobs(created_at);
CREATE INDEX IF NOT EXISTS idx_jobs_created_by ON public.jobs(created_by);
CREATE INDEX IF NOT EXISTS idx_jobs_status_created_at ON public.jobs(status, created_at);

CREATE INDEX IF NOT EXISTS idx_job_logs_job_id ON public.job_logs(job_id);
CREATE INDEX IF NOT EXISTS idx_job_logs_level ON public.job_logs(level);
CREATE INDEX IF NOT EXISTS idx_job_logs_created_at ON public.job_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_job_files_job_id ON public.job_files(job_id);
CREATE INDEX IF NOT EXISTS idx_job_files_file_id ON public.job_files(file_id);
CREATE INDEX IF NOT EXISTS idx_job_files_relationship_type ON public.job_files(relationship_type);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_job_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
DROP TRIGGER IF EXISTS trigger_update_job_updated_at ON public.jobs;
CREATE TRIGGER trigger_update_job_updated_at
    BEFORE UPDATE ON public.jobs
    FOR EACH ROW
    EXECUTE FUNCTION update_job_updated_at();

-- Views for easier querying
CREATE OR REPLACE VIEW public.job_queue_status AS
SELECT 
    queue_name,
    status,
    COUNT(*) as job_count,
    MIN(created_at) as oldest_job,
    MAX(created_at) as newest_job
FROM public.jobs 
GROUP BY queue_name, status
ORDER BY queue_name, status;

CREATE OR REPLACE VIEW public.recent_jobs AS
SELECT 
    job_id,
    queue_name,
    job_name,
    status,
    progress,
    created_at,
    started_at,
    completed_at,
    processing_duration,
    created_by,
    error_message
FROM public.jobs 
ORDER BY created_at DESC
LIMIT 100;

-- Sample data cleanup function (optional)
CREATE OR REPLACE FUNCTION cleanup_old_jobs(days_old INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.jobs 
    WHERE created_at < NOW() - INTERVAL '1 day' * days_old
    AND status IN ('completed', 'failed');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Comments
COMMENT ON TABLE public.jobs IS 'Stores job queue information and status for BullMQ jobs';
COMMENT ON TABLE public.job_logs IS 'Stores execution logs for jobs';
COMMENT ON TABLE public.job_files IS 'Links jobs to files they process or create';
COMMENT ON FUNCTION cleanup_old_jobs IS 'Removes old completed/failed jobs to keep the table clean'; 