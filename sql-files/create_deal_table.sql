-- Create simplified deal table with core fields and JSONB for additional data
-- This focuses on the essential fields for deal matching and analysis

-- Create the deals table
CREATE TABLE public.deals (
    deal_id SERIAL PRIMARY KEY,
    
    -- Core Fields (All Capital Types)
    deal_name TEXT,
 
    sponsor_name TEXT, -- Keep as text for flexibility
    contact_id INTEGER REFERENCES public.contacts(contact_id), -- Nullable foreign key to contacts table
    
    -- Location
    region TEXT, -- Geographic region (Northeast, Southeast, Southwest, Midwest, West Coast, etc.)
    state TEXT, -- Property state (full name)
    city TEXT, -- Property city
    
    -- Deal Size
    deal_size_min DECIMAL(15,2), -- Minimum deal size in dollars
    deal_size_max DECIMAL(15,2), -- Maximum deal size in dollars
    deal_size_range TEXT, -- Deal size range as text (e.g., "$10M-$50M")
    
    -- Property Type
    property_type TEXT, -- Primary property type (Multifamily, Office, Retail, Industrial, Hotel, etc.)
    
    -- Capital Type
    capital_type TEXT, -- Equity, Debt, or Hybrid
    
    -- Match Type
    match_type TEXT, -- Lead-to-Deal or Deal-to-Lead
    
    -- Equity-Specific Fields
    equity_structure TEXT, -- Common Equity, Co-GP, GP, JV, LP, Preferred Equity
    hold_period_months INTEGER, -- Hold period in months
    attachment_point DECIMAL(5,4), -- Attachment point as decimal
    expected_irr DECIMAL(5,4), -- Expected IRR as decimal
    expected_em DECIMAL(5,4), -- Expected Equity Multiple as decimal
    
    -- Debt-Specific Fields
    loan_type TEXT, -- Acquisition, Bridge, Construction, Permanent, Refinance, etc.
    term_months INTEGER, -- Term in months
    ltc DECIMAL(5,4), -- Loan-to-Cost as decimal
    ltv DECIMAL(5,4), -- Loan-to-Value as decimal
    
    -- Status and Processing
    status TEXT DEFAULT 'active',
    deal_stage TEXT, -- Underwriting, Due Diligence, Closing, etc.
    priority TEXT, -- High, Medium, Low
    
    -- Document Processing Metadata
    document_type TEXT,
    extraction_confidence TEXT,
    processing_notes TEXT,
    extraction_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processor_version TEXT,
    llm_model_used TEXT,
    llm_provider TEXT,
    extraction_method TEXT,
    document_source TEXT,
    document_filename TEXT,
    document_size_bytes INTEGER,
    processing_duration_ms INTEGER,
    
    -- Additional Data (All other fields go here)
    extra_fields JSONB, -- All other fields from extraction maps
    
    -- Review and Quality
    review_status TEXT DEFAULT 'pending',
    reviewed_by TEXT,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    review_notes TEXT,
    data_quality_issues JSONB,
    missing_critical_fields JSONB,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance on core fields
CREATE INDEX idx_deals_contact_id ON public.deals(contact_id);
CREATE INDEX idx_deals_region ON public.deals(region);
CREATE INDEX idx_deals_state ON public.deals(state);
CREATE INDEX idx_deals_city ON public.deals(city);
CREATE INDEX idx_deals_property_type ON public.deals(property_type);
CREATE INDEX idx_deals_capital_type ON public.deals(capital_type);
CREATE INDEX idx_deals_match_type ON public.deals(match_type);
CREATE INDEX idx_deals_equity_structure ON public.deals(equity_structure);
CREATE INDEX idx_deals_loan_type ON public.deals(loan_type);
CREATE INDEX idx_deals_status ON public.deals(status);
CREATE INDEX idx_deals_deal_stage ON public.deals(deal_stage);
CREATE INDEX idx_deals_extraction_timestamp ON public.deals(extraction_timestamp);
CREATE INDEX idx_deals_review_status ON public.deals(review_status);
CREATE INDEX idx_deals_extra_fields ON public.deals USING GIN(extra_fields);

-- Create composite indexes for common queries
CREATE INDEX idx_deals_location ON public.deals(region, state, city);
CREATE INDEX idx_deals_deal_size ON public.deals(deal_size_min, deal_size_max);
CREATE INDEX idx_deals_equity_metrics ON public.deals(expected_irr, expected_em);
CREATE INDEX idx_deals_debt_metrics ON public.deals(ltv, ltc);

-- Create trigger to update the updated_at timestamp
CREATE TRIGGER update_deals_updated_at 
    BEFORE UPDATE ON public.deals 
    FOR EACH ROW 
    EXECUTE FUNCTION public.update_modified_column();

-- Add comments for documentation
COMMENT ON TABLE public.deals IS 'Simplified deals table with core fields for deal matching and analysis';
COMMENT ON COLUMN public.deals.deal_id IS 'Primary key for the deal';
COMMENT ON COLUMN public.deals.contact_id IS 'Nullable foreign key to contacts table for the primary contact';
COMMENT ON COLUMN public.deals.sponsor_name IS 'Sponsor name as text (for flexibility when sponsor_id is not available)';

-- Core fields comments
COMMENT ON COLUMN public.deals.region IS 'Geographic region of the deal';
COMMENT ON COLUMN public.deals.state IS 'Property state (full name)';
COMMENT ON COLUMN public.deals.city IS 'Property city';
COMMENT ON COLUMN public.deals.deal_size_min IS 'Minimum deal size in dollars';
COMMENT ON COLUMN public.deals.deal_size_max IS 'Maximum deal size in dollars';
COMMENT ON COLUMN public.deals.deal_size_range IS 'Deal size range as text (e.g., "$10M-$50M")';
COMMENT ON COLUMN public.deals.property_type IS 'Primary property type';
COMMENT ON COLUMN public.deals.capital_type IS 'Capital type: Equity, Debt, or Hybrid';
COMMENT ON COLUMN public.deals.match_type IS 'Match type: Lead-to-Deal or Deal-to-Lead';

-- Equity-specific comments
COMMENT ON COLUMN public.deals.equity_structure IS 'Equity structure: Common Equity, Co-GP, GP, JV, LP, Preferred Equity';
COMMENT ON COLUMN public.deals.hold_period_months IS 'Hold period in months';
COMMENT ON COLUMN public.deals.attachment_point IS 'Attachment point as decimal';
COMMENT ON COLUMN public.deals.expected_irr IS 'Expected IRR as decimal';
COMMENT ON COLUMN public.deals.expected_em IS 'Expected Equity Multiple as decimal';

-- Debt-specific comments
COMMENT ON COLUMN public.deals.loan_type IS 'Loan type: Acquisition, Bridge, Construction, Permanent, Refinance, etc.';
COMMENT ON COLUMN public.deals.term_months IS 'Term in months';
COMMENT ON COLUMN public.deals.ltc IS 'Loan-to-Cost as decimal';
COMMENT ON COLUMN public.deals.ltv IS 'Loan-to-Value as decimal';

-- Additional data comments
COMMENT ON COLUMN public.deals.extra_fields IS 'JSONB object containing all additional fields not in the core schema';
COMMENT ON COLUMN public.deals.status IS 'Deal status: active, inactive, closed, etc.';
COMMENT ON COLUMN public.deals.deal_stage IS 'Current stage: Underwriting, Due Diligence, Closing, etc.';
COMMENT ON COLUMN public.deals.review_status IS 'Review status: pending, reviewed, approved, rejected';

-- Table to store files associated with each deal
CREATE TABLE IF NOT EXISTS public.deal_files (
    id SERIAL PRIMARY KEY,
    deal_id INTEGER REFERENCES public.deals(deal_id),
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
); 