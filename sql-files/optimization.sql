-- Enable pg_trgm if not already enabled
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Add GIN indexes for fuzzy matching on ARRAY columns (text[])
CREATE INDEX IF NOT EXISTS deals_property_types_gin_trgm_idx ON deals USING gin (property_types gin_trgm_ops);
CREATE INDEX IF NOT EXISTS deals_region_gin_trgm_idx ON deals USING gin (region gin_trgm_ops);
CREATE INDEX IF NOT EXISTS deals_state_gin_trgm_idx ON deals USING gin (state gin_trgm_ops);
CREATE INDEX IF NOT EXISTS deals_city_gin_trgm_idx ON deals USING gin (city gin_trgm_ops);
CREATE INDEX IF NOT EXISTS deals_loan_type_gin_trgm_idx ON deals USING gin (loan_type gin_trgm_ops);
CREATE INDEX IF NOT EXISTS deals_capital_position_gin_trgm_idx ON deals USING gin (capital_position gin_trgm_ops);

CREATE INDEX IF NOT EXISTS investment_criteria_property_types_gin_trgm_idx ON investment_criteria USING gin (property_types gin_trgm_ops);
CREATE INDEX IF NOT EXISTS investment_criteria_region_gin_trgm_idx ON investment_criteria USING gin (region gin_trgm_ops);
CREATE INDEX IF NOT EXISTS investment_criteria_state_gin_trgm_idx ON investment_criteria USING gin (state gin_trgm_ops);
CREATE INDEX IF NOT EXISTS investment_criteria_city_gin_trgm_idx ON investment_criteria USING gin (city gin_trgm_ops);
CREATE INDEX IF NOT EXISTS investment_criteria_loan_type_gin_trgm_idx ON investment_criteria USING gin (loan_type gin_trgm_ops);
CREATE INDEX IF NOT EXISTS investment_criteria_capital_position_gin_trgm_idx ON investment_criteria USING gin (capital_position gin_trgm_ops);

-- Add GIN index for fuzzy matching on text columns if needed
CREATE INDEX IF NOT EXISTS deals_deal_name_gin_trgm_idx ON deals USING gin (deal_name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS deals_sponsor_name_gin_trgm_idx ON deals USING gin (sponsor_name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS investment_criteria_entity_type_gin_trgm_idx ON investment_criteria USING gin (entity_type gin_trgm_ops);



ALTER TABLE news ADD COLUMN IF NOT EXISTS bad_url BOOLEAN DEFAULT FALSE;