-- Update investment_criteria table to allow 'dealv2' and 'deal_v2' as entity types
-- This migration adds support for V2 deals in the investment criteria system

-- Drop the existing constraint
ALTER TABLE public.investment_criteria DROP CONSTRAINT IF EXISTS investment_criteria_entity_type_check;

-- Add the new constraint with additional entity types
ALTER TABLE public.investment_criteria ADD CONSTRAINT investment_criteria_entity_type_check 
CHECK (entity_type IN ('Deal', 'Contact', 'Company', 'Company Overview', 'Contact Overview', 'dealv2', 'deal_v2'));

-- Add comment to document the change
COMMENT ON CONSTRAINT investment_criteria_entity_type_check ON public.investment_criteria IS 
'Updated constraint to support V2 deals with entity_type values: dealv2, deal_v2';
