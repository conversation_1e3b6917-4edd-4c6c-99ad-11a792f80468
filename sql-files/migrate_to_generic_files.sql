-- Migration script to move from existing deal_files to new generic file system
-- This script handles the transition to the new flexible polymorphic relationship structure

-- Step 1: Create the new tables (if they don't exist)
-- Run the generic_files_schema.sql first to create the new tables

-- Step 2: Migrate existing deal_files data to the new system
-- This assumes you have existing deal_files table with columns like:
-- deal_file_id, deal_id, file_name, file_path, file_type, uploaded_at, etc.

-- First, let's check if the old tables exist and create a backup
DO $$
BEGIN
    -- Create backup of existing deal_files if it exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'deal_files') THEN
        CREATE TABLE IF NOT EXISTS deal_files_backup AS SELECT * FROM deal_files;
        RAISE NOTICE 'Created backup of deal_files table';
    END IF;
    
    -- Create backup of existing file_relationships if it exists (old structure)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'file_relationships') THEN
        CREATE TABLE IF NOT EXISTS file_relationships_backup AS SELECT * FROM file_relationships;
        RAISE NOTICE 'Created backup of file_relationships table';
    END IF;
END $$;

-- Step 3: Migrate deal_files to the new files table
-- This is a template - adjust column names based on your actual deal_files table structure

INSERT INTO public.files (
    file_id,
    file_name,
    original_name,
    title,
    description,
    content_hash,
    content_hash_algorithm,
    mime_type,
    file_size_bytes,
    file_extension,
    uploaded_by,
    uploaded_at,
    upload_source,
    is_public,
    access_level,
    tags,
    metadata,
    custom_fields,
    created_at,
    updated_at
)
SELECT 
    -- Generate new UUID for file_id
    uuid_generate_v4() as file_id,
    
    -- Map existing columns to new structure
    COALESCE(df.file_name, 'migrated_file_' || df.deal_file_id) as file_name,
    COALESCE(df.file_name, 'migrated_file_' || df.deal_file_id) as original_name,
    df.file_name as title, -- Use file_name as title if no separate title field
    NULL as description, -- Add description if you have it
    
    -- Generate content hash from file path or use existing hash
    -- If you have file content, you should hash it properly
    -- For now, we'll create a hash from the file path and deal_file_id
    encode(sha256(COALESCE(df.file_path, '') || df.deal_file_id::text), 'hex') as content_hash,
    'sha256' as content_hash_algorithm,
    
    -- Map file type to MIME type
    CASE 
        WHEN df.file_type ILIKE '%.pdf' THEN 'application/pdf'
        WHEN df.file_type ILIKE '%.doc%' THEN 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        WHEN df.file_type ILIKE '%.xls%' THEN 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        WHEN df.file_type ILIKE '%.ppt%' THEN 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        WHEN df.file_type ILIKE '%.txt' THEN 'text/plain'
        WHEN df.file_type ILIKE '%.csv' THEN 'text/csv'
        WHEN df.file_type ILIKE '%.jpg%' OR df.file_type ILIKE '%.jpeg%' THEN 'image/jpeg'
        WHEN df.file_type ILIKE '%.png' THEN 'image/png'
        ELSE 'application/octet-stream'
    END as mime_type,
    
    -- File size (set to 0 if not available)
    COALESCE(df.file_size, 0) as file_size_bytes,
    
    -- Extract file extension
    CASE 
        WHEN df.file_type LIKE '%.%' THEN lower(split_part(df.file_type, '.', -1))
        ELSE NULL
    END as file_extension,
    
    -- Upload metadata
    df.uploaded_by,
    COALESCE(df.uploaded_at, CURRENT_TIMESTAMP) as uploaded_at,
    'migration' as upload_source,
    
    -- Access settings
    false as is_public,
    'private' as access_level,
    
    -- Tags and metadata
    ARRAY['migrated', 'deal_files'] as tags,
    jsonb_build_object(
        'original_deal_file_id', df.deal_file_id,
        'original_file_path', df.file_path,
        'original_file_type', df.file_type,
        'migration_date', CURRENT_TIMESTAMP
    ) as metadata,
    
    -- Custom fields (add any additional fields you had)
    jsonb_build_object(
        'deal_id', df.deal_id,
        'original_table', 'deal_files'
    ) as custom_fields,
    
    -- Timestamps
    COALESCE(df.uploaded_at, CURRENT_TIMESTAMP) as created_at,
    CURRENT_TIMESTAMP as updated_at

FROM deal_files df
WHERE NOT EXISTS (
    -- Avoid duplicates by checking if we already migrated this file
    SELECT 1 FROM public.files f 
    WHERE f.metadata->>'original_deal_file_id' = df.deal_file_id::text
);

-- Step 4: Create relationships for the migrated files
INSERT INTO public.file_relationships (
    relationship_id,
    file_id,
    target_table_name,
    target_column_name,
    target_row_id,
    relationship_type,
    relationship_title,
    relationship_notes,
    display_order,
    is_primary,
    created_at,
    updated_at
)
SELECT 
    uuid_generate_v4() as relationship_id,
    f.file_id,
    'deals' as target_table_name,
    'deal_id' as target_column_name,
    f.custom_fields->>'deal_id' as target_row_id,
    'attachment' as relationship_type,
    'Migrated from deal_files' as relationship_title,
    'Automatically migrated from legacy deal_files table' as relationship_notes,
    0 as display_order,
    false as is_primary,
    f.created_at,
    f.updated_at
FROM public.files f
WHERE f.custom_fields->>'original_table' = 'deal_files'
AND NOT EXISTS (
    -- Avoid duplicate relationships
    SELECT 1 FROM public.file_relationships fr 
    WHERE fr.file_id = f.file_id 
    AND fr.target_table_name = 'deals'
    AND fr.target_column_name = 'deal_id'
    AND fr.target_row_id = f.custom_fields->>'deal_id'
);

-- Step 5: Migrate any existing file_relationships (if you had them)
-- This handles the case where you already had a file_relationships table with the old structure

INSERT INTO public.file_relationships (
    relationship_id,
    file_id,
    target_table_name,
    target_column_name,
    target_row_id,
    relationship_type,
    relationship_title,
    relationship_notes,
    display_order,
    is_primary,
    created_at,
    updated_at
)
SELECT 
    uuid_generate_v4() as relationship_id,
    fr.file_id,
    fr.entity_type || 's' as target_table_name, -- Add 's' to make plural table names
    fr.entity_type || '_id' as target_column_name, -- Assume column name follows pattern
    fr.entity_id::text as target_row_id,
    COALESCE(fr.relationship_type, 'attachment') as relationship_type,
    fr.relationship_title,
    fr.relationship_notes,
    COALESCE(fr.display_order, 0) as display_order,
    COALESCE(fr.is_primary, false) as is_primary,
    COALESCE(fr.created_at, CURRENT_TIMESTAMP) as created_at,
    COALESCE(fr.updated_at, CURRENT_TIMESTAMP) as updated_at
FROM file_relationships_backup fr
WHERE EXISTS (
    -- Only create relationships for files that exist in the new system
    SELECT 1 FROM public.files f WHERE f.file_id = fr.file_id
)
AND NOT EXISTS (
    -- Avoid duplicate relationships
    SELECT 1 FROM public.file_relationships new_fr 
    WHERE new_fr.file_id = fr.file_id 
    AND new_fr.target_table_name = fr.entity_type || 's'
    AND new_fr.target_column_name = fr.entity_type || '_id'
    AND new_fr.target_row_id = fr.entity_id::text
);

-- Step 6: Create indexes for better performance (if not already created by schema)
CREATE INDEX IF NOT EXISTS idx_files_migration_metadata ON public.files USING GIN(metadata);
CREATE INDEX IF NOT EXISTS idx_files_migration_custom_fields ON public.files USING GIN(custom_fields);

-- Step 7: Verify migration
DO $$
DECLARE
    old_count INTEGER;
    new_count INTEGER;
    relationship_count INTEGER;
BEGIN
    -- Count records in old table
    SELECT COUNT(*) INTO old_count FROM deal_files;
    
    -- Count records in new table
    SELECT COUNT(*) INTO new_count FROM public.files WHERE custom_fields->>'original_table' = 'deal_files';
    
    -- Count relationships
    SELECT COUNT(*) INTO relationship_count FROM public.file_relationships 
    WHERE relationship_notes LIKE '%Migrated from legacy%';
    
    RAISE NOTICE 'Migration Summary:';
    RAISE NOTICE 'Original deal_files records: %', old_count;
    RAISE NOTICE 'Migrated files: %', new_count;
    RAISE NOTICE 'Created relationships: %', relationship_count;
    
    IF old_count = new_count THEN
        RAISE NOTICE 'Migration completed successfully!';
    ELSE
        RAISE WARNING 'Migration may have issues - record counts do not match';
    END IF;
END $$;

-- Step 8: Optional - Create a view for backward compatibility
-- This allows existing code to still work with the old table structure

CREATE OR REPLACE VIEW deal_files_compatibility AS
SELECT 
    f.file_id as deal_file_id,
    (f.custom_fields->>'deal_id')::integer as deal_id,
    f.file_name,
    f.original_name as file_path, -- Map original_name to file_path for compatibility
    f.file_extension as file_type,
    f.file_size_bytes as file_size,
    f.uploaded_by,
    f.uploaded_at,
    f.created_at,
    f.updated_at
FROM public.files f
WHERE f.custom_fields->>'original_table' = 'deal_files';

-- Step 9: Optional - Create a function to help with the transition
CREATE OR REPLACE FUNCTION get_deal_files(deal_id_param INTEGER)
RETURNS TABLE (
    deal_file_id UUID,
    deal_id INTEGER,
    file_name TEXT,
    file_path TEXT,
    file_type TEXT,
    file_size BIGINT,
    uploaded_by TEXT,
    uploaded_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        f.file_id,
        (f.custom_fields->>'deal_id')::integer,
        f.file_name,
        f.original_name,
        f.file_extension,
        f.file_size_bytes,
        f.uploaded_by,
        f.uploaded_at
    FROM public.files f
    WHERE f.custom_fields->>'deal_id' = deal_id_param::text
    ORDER BY f.uploaded_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Step 10: Cleanup (run this after verifying migration is successful)
-- Uncomment these lines after you've verified everything works correctly

/*
-- Drop the compatibility view if no longer needed
-- DROP VIEW IF EXISTS deal_files_compatibility;

-- Drop the compatibility function if no longer needed  
-- DROP FUNCTION IF EXISTS get_deal_files(INTEGER);

-- Drop the old tables (BE CAREFUL - make sure you have backups!)
-- DROP TABLE IF EXISTS deal_files;
-- DROP TABLE IF EXISTS file_relationships_backup;
-- DROP TABLE IF EXISTS deal_files_backup;
*/

-- Migration complete!
-- The new system is now ready to use with the flexible polymorphic relationship structure
-- All logic is handled by the FileManager utility class 