-- Create investment_criteria table for storing investment preferences and criteria
-- This table can be linked to deals, contacts, or companies

-- Idempotent migration for investment_criteria table
-- 1. Create the table if it does not exist
CREATE TABLE IF NOT EXISTS public.investment_criteria (
    criteria_id SERIAL PRIMARY KEY
);

-- 2. Add columns if they do not exist
DO $$
BEGIN
    -- entity_type
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='entity_type') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN entity_type TEXT NOT NULL CHECK (entity_type IN ('Deal', 'Contact', 'Company'));
    END IF;
    -- entity_id
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='entity_id') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN entity_id TEXT NOT NULL;
    END IF;
    -- target_return
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='target_return') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN target_return DECIMAL(5,4);
    END IF;
    -- property_types
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='property_types') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN property_types TEXT[];
    END IF;
    -- property_sub_categories
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='property_sub_categories') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN property_sub_categories TEXT[];
    END IF;
    -- strategies
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='strategies') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN strategies TEXT[];
    END IF;
    -- asset_classes
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='asset_classes') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN asset_classes TEXT[];
    END IF;
    -- minimum_deal_size
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='minimum_deal_size') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN minimum_deal_size DECIMAL(15,2);
    END IF;
    -- maximum_deal_size
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='maximum_deal_size') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN maximum_deal_size DECIMAL(15,2);
    END IF;
    -- min_hold_period
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='min_hold_period') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN min_hold_period INTEGER;
    END IF;
    -- max_hold_period
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='max_hold_period') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN max_hold_period INTEGER;
    END IF;
    -- financial_products
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='financial_products') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN financial_products TEXT[];
    END IF;
    -- historical_irr
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='historical_irr') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN historical_irr DECIMAL(5,4);
    END IF;
    -- historical_em
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='historical_em') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN historical_em DECIMAL(5,4);
    END IF;
    -- country
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='country') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN country TEXT[];
    END IF;
    -- region
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='region') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN region TEXT[];
    END IF;
    -- state
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='state') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN state TEXT[];
    END IF;
    -- city
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='city') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN city TEXT[];
    END IF;
    -- loan_program
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='loan_program') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN loan_program TEXT[];
    END IF;
    -- loan_type
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='loan_type') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN loan_type TEXT[];
    END IF;
    -- capital_type
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='capital_type') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN capital_type TEXT[];
    END IF;
    -- capital_position
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='capital_position') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN capital_position TEXT[];
    END IF;
    -- capital_source
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='capital_source') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN capital_source TEXT;
    END IF;
    -- structured_loan_tranche
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='structured_loan_tranche') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN structured_loan_tranche TEXT[];
    END IF;
    -- min_loan_term
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='min_loan_term') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN min_loan_term INTEGER;
    END IF;
    -- max_loan_term
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='max_loan_term') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN max_loan_term INTEGER;
    END IF;
    -- loan_interest_rate
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='loan_interest_rate') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN loan_interest_rate DECIMAL(5,4);
    END IF;
    -- loan_interest_rate_sofr
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='loan_interest_rate_sofr') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN loan_interest_rate_sofr DECIMAL(5,4);
    END IF;
    -- loan_interest_rate_wsj
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='loan_interest_rate_wsj') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN loan_interest_rate_wsj DECIMAL(5,4);
    END IF;
    -- loan_interest_rate_prime
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='loan_interest_rate_prime') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN loan_interest_rate_prime DECIMAL(5,4);
    END IF;
    -- loan_ltv
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='loan_ltv') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN loan_ltv DECIMAL(5,4);
    END IF;
    -- loan_ltc
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='loan_ltc') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN loan_ltc DECIMAL(5,4);
    END IF;
    -- loan_origination_fee
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='loan_origination_fee') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN loan_origination_fee DECIMAL(5,4);
    END IF;
    -- loan_exit_fee
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='loan_exit_fee') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN loan_exit_fee DECIMAL(5,4);
    END IF;
    -- min_loan_dscr
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='min_loan_dscr') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN min_loan_dscr DECIMAL(5,4);
    END IF;
    -- max_loan_dscr
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='max_loan_dscr') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN max_loan_dscr DECIMAL(5,4);
    END IF;
    -- recourse_loan
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='recourse_loan') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN recourse_loan TEXT;
    END IF;
    -- extra_fields
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='extra_fields') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN extra_fields JSONB;
    END IF;
    -- created_at
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='created_at') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
    END IF;
    -- updated_at
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='updated_at') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
    END IF;
    -- created_by
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='created_by') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN created_by TEXT;
    END IF;
    -- updated_by
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='updated_by') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN updated_by TEXT;
    END IF;
    -- is_active
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='is_active') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN is_active BOOLEAN DEFAULT true;
    END IF;
    -- notes
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='notes') THEN
        ALTER TABLE public.investment_criteria ADD COLUMN notes TEXT;
    END IF;
END $$;

-- 3. Add constraints (if not already present)
-- (You may need to add logic to check for constraints if you want to make this fully idempotent)

-- 4. Add indexes (if not already present)
-- (You may need to add logic to check for indexes if you want to make this fully idempotent)

-- 5. Add comments (optional, for documentation)
-- (You may need to add logic to check for comments if you want to make this fully idempotent)

-- Create indexes for better performance
CREATE INDEX idx_investment_criteria_entity ON public.investment_criteria(entity_type, entity_id);
CREATE INDEX idx_investment_criteria_property_types ON public.investment_criteria USING GIN(property_types);
CREATE INDEX idx_investment_criteria_strategies ON public.investment_criteria USING GIN(strategies);
CREATE INDEX idx_investment_criteria_asset_classes ON public.investment_criteria USING GIN(asset_classes);
CREATE INDEX idx_investment_criteria_country ON public.investment_criteria USING GIN(country);
CREATE INDEX idx_investment_criteria_region ON public.investment_criteria USING GIN(region);
CREATE INDEX idx_investment_criteria_state ON public.investment_criteria USING GIN(state);
CREATE INDEX idx_investment_criteria_city ON public.investment_criteria USING GIN(city);
CREATE INDEX idx_investment_criteria_loan_type ON public.investment_criteria USING GIN(loan_type);
CREATE INDEX idx_investment_criteria_capital_type ON public.investment_criteria USING GIN(capital_type);
CREATE INDEX idx_investment_criteria_deal_size ON public.investment_criteria(minimum_deal_size, maximum_deal_size);
CREATE INDEX idx_investment_criteria_hold_period ON public.investment_criteria(min_hold_period, max_hold_period);
CREATE INDEX idx_investment_criteria_loan_term ON public.investment_criteria(min_loan_term, max_loan_term);
CREATE INDEX idx_investment_criteria_target_return ON public.investment_criteria(target_return);
CREATE INDEX idx_investment_criteria_historical_irr ON public.investment_criteria(historical_irr);
CREATE INDEX idx_investment_criteria_historical_em ON public.investment_criteria(historical_em);
CREATE INDEX idx_investment_criteria_extra_fields ON public.investment_criteria USING GIN(extra_fields);

-- Create trigger to update the updated_at timestamp
CREATE TRIGGER update_investment_criteria_updated_at 
    BEFORE UPDATE ON public.investment_criteria 
    FOR EACH ROW 
    EXECUTE FUNCTION public.update_modified_column();

-- Add comments for documentation
COMMENT ON TABLE public.investment_criteria IS 'Investment criteria and preferences for deals, contacts, and companies';
COMMENT ON COLUMN public.investment_criteria.criteria_id IS 'Primary key for the investment criteria record';
COMMENT ON COLUMN public.investment_criteria.entity_type IS 'Type of entity this criteria belongs to: Deal, Contact, or Company';
COMMENT ON COLUMN public.investment_criteria.entity_id IS 'ID of the entity (deal_id, contact_id, or company_id)';
COMMENT ON COLUMN public.investment_criteria.target_return IS 'Target return as decimal (e.g., 0.15 for 15%)';
COMMENT ON COLUMN public.investment_criteria.property_types IS 'Array of preferred property types';
COMMENT ON COLUMN public.investment_criteria.property_sub_categories IS 'Array of preferred property subcategories';
COMMENT ON COLUMN public.investment_criteria.strategies IS 'Array of preferred investment strategies';
COMMENT ON COLUMN public.investment_criteria.asset_classes IS 'Array of preferred asset classes';
COMMENT ON COLUMN public.investment_criteria.minimum_deal_size IS 'Minimum deal size in dollars';
COMMENT ON COLUMN public.investment_criteria.maximum_deal_size IS 'Maximum deal size in dollars';
COMMENT ON COLUMN public.investment_criteria.min_hold_period IS 'Minimum hold period in months';
COMMENT ON COLUMN public.investment_criteria.max_hold_period IS 'Maximum hold period in months';
COMMENT ON COLUMN public.investment_criteria.financial_products IS 'Array of preferred financial products';
COMMENT ON COLUMN public.investment_criteria.historical_irr IS 'Historical IRR as decimal';
COMMENT ON COLUMN public.investment_criteria.historical_em IS 'Historical Equity Multiple as decimal';
COMMENT ON COLUMN public.investment_criteria.country IS 'Array of preferred countries';
COMMENT ON COLUMN public.investment_criteria.region IS 'Array of preferred regions';
COMMENT ON COLUMN public.investment_criteria.state IS 'Array of preferred states';
COMMENT ON COLUMN public.investment_criteria.city IS 'Array of preferred cities';
COMMENT ON COLUMN public.investment_criteria.loan_program IS 'Array of preferred loan programs';
COMMENT ON COLUMN public.investment_criteria.loan_type IS 'Array of preferred loan types';
COMMENT ON COLUMN public.investment_criteria.capital_type IS 'Array of preferred capital types';
COMMENT ON COLUMN public.investment_criteria.capital_position IS 'Array of preferred capital positions (e.g., Senior Debt, Preferred Equity)';
COMMENT ON COLUMN public.investment_criteria.capital_source IS 'Primary capital source preference';
COMMENT ON COLUMN public.investment_criteria.structured_loan_tranche IS 'Array of preferred structured loan tranches';
COMMENT ON COLUMN public.investment_criteria.min_loan_term IS 'Minimum loan term in months';
COMMENT ON COLUMN public.investment_criteria.max_loan_term IS 'Maximum loan term in months';
COMMENT ON COLUMN public.investment_criteria.loan_interest_rate IS 'Preferred loan interest rate as decimal';
COMMENT ON COLUMN public.investment_criteria.loan_interest_rate_sofr IS 'SOFR-based interest rate as decimal';
COMMENT ON COLUMN public.investment_criteria.loan_interest_rate_wsj IS 'WSJ-based interest rate as decimal';
COMMENT ON COLUMN public.investment_criteria.loan_interest_rate_prime IS 'Prime-based interest rate as decimal';
COMMENT ON COLUMN public.investment_criteria.loan_ltv IS 'Preferred Loan-to-Value ratio as decimal';
COMMENT ON COLUMN public.investment_criteria.loan_ltc IS 'Preferred Loan-to-Cost ratio as decimal';
COMMENT ON COLUMN public.investment_criteria.loan_origination_fee IS 'Preferred loan origination fee as decimal';
COMMENT ON COLUMN public.investment_criteria.loan_exit_fee IS 'Preferred loan exit fee as decimal';
COMMENT ON COLUMN public.investment_criteria.min_loan_dscr IS 'Minimum preferred Debt Service Coverage Ratio';
COMMENT ON COLUMN public.investment_criteria.max_loan_dscr IS 'Maximum preferred Debt Service Coverage Ratio';
COMMENT ON COLUMN public.investment_criteria.recourse_loan IS 'Recourse loan preference';
COMMENT ON COLUMN public.investment_criteria.extra_fields IS 'JSONB object containing additional fields not in the standard schema';
COMMENT ON COLUMN public.investment_criteria.is_active IS 'Whether this criteria record is active';
COMMENT ON COLUMN public.investment_criteria.notes IS 'Additional notes about the investment criteria';

-- Ensure criteria_id is SERIAL PRIMARY KEY and sequence is synced
DO $$
BEGIN
  -- If criteria_id does not exist, add it as SERIAL PRIMARY KEY
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='criteria_id'
  ) THEN
    ALTER TABLE public.investment_criteria ADD COLUMN criteria_id SERIAL PRIMARY KEY;
  END IF;
  -- If criteria_id exists but is not SERIAL, alter it
  IF EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name='investment_criteria' AND column_name='criteria_id'
  ) THEN
    -- Try to set default to nextval if not already
    BEGIN
      ALTER TABLE public.investment_criteria ALTER COLUMN criteria_id SET DEFAULT nextval('investment_criteria_criteria_id_seq');
    EXCEPTION WHEN undefined_table THEN
      -- Create the sequence if it doesn't exist
      CREATE SEQUENCE IF NOT EXISTS investment_criteria_criteria_id_seq;
      ALTER TABLE public.investment_criteria ALTER COLUMN criteria_id SET DEFAULT nextval('investment_criteria_criteria_id_seq');
    END;
  END IF;
END $$;

-- Sync the sequence to the max value
SELECT setval('investment_criteria_criteria_id_seq', (SELECT COALESCE(MAX(criteria_id), 1) FROM public.investment_criteria)); 