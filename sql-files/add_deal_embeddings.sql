-- Add vector embeddings to deals table for efficient similarity search
-- This enables fast duplicate detection using HNSW indexing

-- Add vector column for deal embeddings (using 768 dimensions for Gemini text-embedding-004)
ALTER TABLE public.deals 
ADD COLUMN IF NOT EXISTS deal_embedding vector(768);

-- Add metadata columns for embedding tracking
ALTER TABLE public.deals 
ADD COLUMN IF NOT EXISTS embedding_model TEXT DEFAULT 'text-embedding-004',
ADD COLUMN IF NOT EXISTS embedding_created_at TIMESTAMP DEFAULT NOW();

-- Create HNSW index for fast vector similarity search
-- Using cosine distance (good for text embeddings)
CREATE INDEX IF NOT EXISTS deals_embedding_hnsw_idx 
ON public.deals 
USING hnsw (deal_embedding vector_cosine_ops)
WITH (m = 24, ef_construction = 200);

-- Create additional index for exact search when needed
CREATE INDEX IF NOT EXISTS deals_embedding_ivfflat_idx 
ON public.deals 
USING ivfflat (deal_embedding vector_cosine_ops)
WITH (lists = 100);

-- Add partial index for active deals only (excludes deleted/replaced deals)
CREATE INDEX IF NOT EXISTS deals_embedding_active_hnsw_idx 
ON public.deals 
USING hnsw (deal_embedding vector_cosine_ops)
WITH (m = 24, ef_construction = 200)
WHERE status NOT IN ('deleted', 'Replaced');

-- Function to find similar deals using vector similarity
CREATE OR REPLACE FUNCTION find_similar_deals(
    query_embedding vector(768),
    similarity_threshold float8 DEFAULT 0.85,
    max_results integer DEFAULT 10
)
RETURNS TABLE (
    deal_id integer,
    deal_name text,
    sponsor_name text,
    city text,
    state text,
    region text,
    property_type text,
    created_at timestamp,
    similarity_score float8
) 
LANGUAGE sql
STABLE
AS $$
    SELECT 
        d.deal_id,
        d.deal_name,
        d.sponsor_name,
        d.city,
        d.state,
        d.region,
        d.property_type,
        d.created_at,
        1 - (d.deal_embedding <=> query_embedding) as similarity_score
    FROM public.deals d
    WHERE d.deal_embedding IS NOT NULL
    AND d.status NOT IN ('deleted', 'Replaced')
    AND d.deal_name IS NOT NULL 
    AND d.deal_name != ''
    AND (1 - (d.deal_embedding <=> query_embedding)) >= similarity_threshold
    ORDER BY d.deal_embedding <=> query_embedding
    LIMIT max_results;
$$;

-- Add comment explaining the approach
COMMENT ON COLUMN public.deals.deal_embedding IS 'Vector embedding of deal information for similarity search using Gemini text-embedding-004. Generated from deal_name, sponsor_name, property_type, and location data.';
COMMENT ON FUNCTION find_similar_deals IS 'Find deals similar to the query embedding using cosine similarity with Gemini embeddings. Returns deals with similarity >= threshold, ordered by similarity score.';

-- Show current table structure
\d public.deals; 