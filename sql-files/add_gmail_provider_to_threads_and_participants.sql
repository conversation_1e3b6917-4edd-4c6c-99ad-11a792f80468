-- Add provider_thread_id to threads
-- AL<PERSON><PERSON> TABLE threads ADD COLUMN IF NOT EXISTS provider_thread_id TEXT;
-- <PERSON><PERSON><PERSON> TABLE threads ADD COLUMN IF NOT EXISTS provider TEXT;
-- -- Add unique constraint for provider_thread_id + provider
-- DO $$
-- BEGIN
--     IF NOT EXISTS (
--         SELECT 1 FROM information_schema.table_constraints 
--         WHERE constraint_name = 'threads_provider_thread_id_provider_unique' AND table_name = 'threads'
--     ) THEN
--         ALTER TABLE threads ADD CONSTRAINT threads_provider_thread_id_provider_unique UNIQUE (provider_thread_id, provider);
--     END IF;
-- END$$;
-- Optionally, update thread_participants to reference threads by id (if schema uses thread_id as FK and expects UUID, you may need to adjust this in code and schema)

-- Add provider and account_email to threads
ALTER TABLE threads
ADD COLUMN IF NOT EXISTS provider TEXT DEFAULT 'gmail',
ADD COLUMN IF NOT EXISTS account_email TEXT;

-- Add provider to thread_participants
ALTER TABLE thread_participants
ADD COLUMN IF NOT EXISTS provider TEXT DEFAULT 'gmail';

-- Create email_messages table
CREATE TABLE IF NOT EXISTS email_messages (
  id SERIAL PRIMARY KEY,
  thread_id UUID REFERENCES threads(thread_id),
  gmail_message_id TEXT,
  sender TEXT,
  recipients TEXT[],
  sent_at TIMESTAMPTZ,
  subject TEXT,
  body TEXT,
  raw TEXT,
  provider TEXT DEFAULT 'gmail',
  account_email TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
); 