-- Company Schema Migration Script
-- This script migrates the companies table to the new schema
-- Focuses only on the companies table - no new tables needed

-- Phase 1: Add new columns to companies table
-- ===========================================

-- Basic Information
ALTER TABLE companies ADD COLUMN IF NOT EXISTS company_type VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS business_model TEXT;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS investment_focus TEXT[]; -- Array of strings
ALTER TABLE companies ADD COLUMN IF NOT EXISTS investment_strategy_mission TEXT;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS investment_strategy_approach TEXT;

-- Contact Information
ALTER TABLE companies ADD COLUMN IF NOT EXISTS secondary_phone VARCHAR(100);
ALTER TABLE companies ADD COLUMN IF NOT EXISTS main_email VARCHAR(255);
ALTER TABLE companies ADD COLUMN IF NOT EXISTS secondary_email VARCHAR(255);
ALTER TABLE companies ADD COLUMN IF NOT EXISTS twitter VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS facebook VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS instagram VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS youtube VARCHAR;

-- Address Information
ALTER TABLE companies ADD COLUMN IF NOT EXISTS additional_address TEXT;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS additional_city VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS additional_state VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS additional_zipcode VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS additional_country VARCHAR;

-- Financial Information
ALTER TABLE companies ADD COLUMN IF NOT EXISTS fund_size INTEGER; -- USD amount
ALTER TABLE companies ADD COLUMN IF NOT EXISTS aum INTEGER; -- USD amount
ALTER TABLE companies ADD COLUMN IF NOT EXISTS number_of_properties INTEGER;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS number_of_offices INTEGER;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS office_locations TEXT[]; -- Array of strings
ALTER TABLE companies ADD COLUMN IF NOT EXISTS number_of_employees INTEGER;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS partnerships TEXT[]; -- Array of strings
ALTER TABLE companies ADD COLUMN IF NOT EXISTS balance_sheet_strength TEXT;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS funding_sources TEXT[]; -- Array of strings
ALTER TABLE companies ADD COLUMN IF NOT EXISTS recent_capital_raises TEXT;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS typical_debt_to_equity_ratio FLOAT;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS development_fee_structure TEXT;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS key_equity_partners TEXT[]; -- Array of strings
ALTER TABLE companies ADD COLUMN IF NOT EXISTS key_debt_partners TEXT[]; -- Array of strings

-- Strategy & Focus
ALTER TABLE companies ADD COLUMN IF NOT EXISTS market_cycle_positioning VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS urban_vs_suburban_preference VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS sustainability_esg_focus BOOLEAN;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS technology_proptech_adoption BOOLEAN;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS adaptive_reuse_experience BOOLEAN;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS regulatory_zoning_expertise BOOLEAN;

-- Investment Vehicle Information
ALTER TABLE companies ADD COLUMN IF NOT EXISTS investment_vehicle_type VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS active_fund_name_series VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS fund_size_active_fund INTEGER; -- USD amount
ALTER TABLE companies ADD COLUMN IF NOT EXISTS fundraising_status VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS lender_type VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS annual_loan_volume INTEGER; -- USD amount
ALTER TABLE companies ADD COLUMN IF NOT EXISTS lending_origin VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS portfolio_health TEXT;

-- Leadership & Governance
ALTER TABLE companies ADD COLUMN IF NOT EXISTS board_of_directors TEXT[]; -- Array of strings
ALTER TABLE companies ADD COLUMN IF NOT EXISTS key_executives TEXT[]; -- Array of strings
ALTER TABLE companies ADD COLUMN IF NOT EXISTS founder_background TEXT;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS company_history TEXT;

-- Public Company Information
ALTER TABLE companies ADD COLUMN IF NOT EXISTS stock_ticker_symbol VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS stock_exchange VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS market_capitalization INTEGER; -- USD amount
ALTER TABLE companies ADD COLUMN IF NOT EXISTS annual_revenue INTEGER; -- USD amount
ALTER TABLE companies ADD COLUMN IF NOT EXISTS net_income INTEGER; -- USD amount
ALTER TABLE companies ADD COLUMN IF NOT EXISTS ebitda INTEGER; -- USD amount
ALTER TABLE companies ADD COLUMN IF NOT EXISTS profit_margin FLOAT;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS credit_rating VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS quarterly_earnings_link VARCHAR;

-- Business Information
ALTER TABLE companies ADD COLUMN IF NOT EXISTS products_services_description TEXT;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS target_customer_profile TEXT;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS major_competitors TEXT[]; -- Array of strings
ALTER TABLE companies ADD COLUMN IF NOT EXISTS market_share_percentage FLOAT;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS unique_selling_proposition TEXT;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS industry_awards_recognitions TEXT[]; -- Array of strings

-- Corporate Structure
ALTER TABLE companies ADD COLUMN IF NOT EXISTS corporate_structure VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS parent_company VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS subsidiaries TEXT[]; -- Array of strings

-- Data Quality & Tracking
ALTER TABLE companies ADD COLUMN IF NOT EXISTS recent_news_sentiment VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS data_source VARCHAR;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS last_updated_timestamp TIMESTAMP WITH TIME ZONE;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS data_confidence_score FLOAT;

-- Relationship Management
ALTER TABLE companies ADD COLUMN IF NOT EXISTS dry_powder NUMERIC; -- Available capital
ALTER TABLE companies ADD COLUMN IF NOT EXISTS annual_deployment_target NUMERIC; -- Deployment target
ALTER TABLE companies ADD COLUMN IF NOT EXISTS transactions_completed_last_12m INTEGER; -- Recent transactions
ALTER TABLE companies ADD COLUMN IF NOT EXISTS internal_relationship_manager TEXT; -- Relationship manager
ALTER TABLE companies ADD COLUMN IF NOT EXISTS last_contact_date DATE; -- Last contact
ALTER TABLE companies ADD COLUMN IF NOT EXISTS pipeline_status VARCHAR; -- Pipeline status
ALTER TABLE companies ADD COLUMN IF NOT EXISTS role_in_previous_deal VARCHAR; -- Previous role
ALTER TABLE companies ADD COLUMN IF NOT EXISTS total_transaction_volume_ytd NUMERIC; -- YTD volume
ALTER TABLE companies ADD COLUMN IF NOT EXISTS deal_count_ytd INTEGER; -- YTD deal count
ALTER TABLE companies ADD COLUMN IF NOT EXISTS average_deal_size NUMERIC; -- Average deal size
ALTER TABLE companies ADD COLUMN IF NOT EXISTS portfolio_size_sqft INTEGER; -- Portfolio size
ALTER TABLE companies ADD COLUMN IF NOT EXISTS portfolio_asset_count INTEGER; -- Asset count

-- Add missing processing columns that you want to keep
ALTER TABLE companies ADD COLUMN IF NOT EXISTS website_scraping_error_count INTEGER DEFAULT 0;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS company_overview_error_count INTEGER DEFAULT 0;

-- LLM Processing
ALTER TABLE companies ADD COLUMN IF NOT EXISTS llm_response TEXT;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS llm_token_usage JSONB;
ALTER TABLE companies ADD COLUMN IF NOT EXISTS llm_used TEXT;


-- Phase 3: Rename existing columns
-- ================================

-- Rename columns to match new schema
ALTER TABLE companies RENAME COLUMN company_phone TO main_phone;
ALTER TABLE companies RENAME COLUMN company_website TO website;
ALTER TABLE companies RENAME COLUMN company_linkedin TO linkedin;
ALTER TABLE companies RENAME COLUMN company_address TO headquarters_address;
ALTER TABLE companies RENAME COLUMN company_city TO headquarters_city;
ALTER TABLE companies RENAME COLUMN company_state TO headquarters_state;
ALTER TABLE companies RENAME COLUMN company_zip TO headquarters_zipcode;
ALTER TABLE companies RENAME COLUMN company_country TO headquarters_country;
ALTER TABLE companies RENAME COLUMN industry TO company_industry;

-- Phase 4: Remove unnecessary columns
-- ===================================

-- Remove processing/system columns that are not needed
ALTER TABLE companies DROP COLUMN IF EXISTS extra_attrs;
ALTER TABLE companies DROP COLUMN IF EXISTS processed;
ALTER TABLE companies DROP COLUMN IF EXISTS extracted;
ALTER TABLE companies DROP COLUMN IF EXISTS overview;
ALTER TABLE companies DROP COLUMN IF EXISTS last_processed_stage;
ALTER TABLE companies DROP COLUMN IF EXISTS last_processed_at;
ALTER TABLE companies DROP COLUMN IF EXISTS processing_error_count;
ALTER TABLE companies DROP COLUMN IF EXISTS processing_attempts;
ALTER TABLE companies DROP COLUMN IF EXISTS web_scraping_error;
ALTER TABLE companies DROP COLUMN IF EXISTS web_scraping_date;

-- Phase 5: Add constraints and indexes
-- ====================================

-- Add indexes for commonly queried columns
CREATE INDEX IF NOT EXISTS idx_companies_company_type ON companies(company_type);
CREATE INDEX IF NOT EXISTS idx_companies_company_industry ON companies(company_industry);
CREATE INDEX IF NOT EXISTS idx_companies_fund_size ON companies(fund_size);
CREATE INDEX IF NOT EXISTS idx_companies_aum ON companies(aum);
CREATE INDEX IF NOT EXISTS idx_companies_founded_year ON companies(founded_year);
CREATE INDEX IF NOT EXISTS idx_companies_headquarters_state ON companies(headquarters_state);
CREATE INDEX IF NOT EXISTS idx_companies_headquarters_country ON companies(headquarters_country);
CREATE INDEX IF NOT EXISTS idx_companies_pipeline_status ON companies(pipeline_status);
CREATE INDEX IF NOT EXISTS idx_companies_last_contact_date ON companies(last_contact_date);
CREATE INDEX IF NOT EXISTS idx_companies_data_confidence_score ON companies(data_confidence_score);

-- Add indexes for array columns
CREATE INDEX IF NOT EXISTS idx_companies_investment_focus_gin ON companies USING GIN(investment_focus);
CREATE INDEX IF NOT EXISTS idx_companies_partnerships_gin ON companies USING GIN(partnerships);
CREATE INDEX IF NOT EXISTS idx_companies_office_locations_gin ON companies USING GIN(office_locations);
CREATE INDEX IF NOT EXISTS idx_companies_funding_sources_gin ON companies USING GIN(funding_sources);
CREATE INDEX IF NOT EXISTS idx_companies_key_equity_partners_gin ON companies USING GIN(key_equity_partners);
CREATE INDEX IF NOT EXISTS idx_companies_key_debt_partners_gin ON companies USING GIN(key_debt_partners);
CREATE INDEX IF NOT EXISTS idx_companies_board_of_directors_gin ON companies USING GIN(board_of_directors);
CREATE INDEX IF NOT EXISTS idx_companies_key_executives_gin ON companies USING GIN(key_executives);
CREATE INDEX IF NOT EXISTS idx_companies_major_competitors_gin ON companies USING GIN(major_competitors);
CREATE INDEX IF NOT EXISTS idx_companies_industry_awards_recognitions_gin ON companies USING GIN(industry_awards_recognitions);
CREATE INDEX IF NOT EXISTS idx_companies_subsidiaries_gin ON companies USING GIN(subsidiaries);

-- Add indexes for processing columns you want to keep
CREATE INDEX IF NOT EXISTS idx_companies_website_scraping_status ON companies(website_scraping_status);
CREATE INDEX IF NOT EXISTS idx_companies_company_overview_status ON companies(company_overview_status);
CREATE INDEX IF NOT EXISTS idx_companies_conflict_status ON companies(conflict_status);

-- Phase 6: Update triggers for updated_at
-- =======================================

-- Create or replace function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_companies_updated_at ON companies;
CREATE TRIGGER update_companies_updated_at
    BEFORE UPDATE ON companies
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Phase 7: Add comments for documentation
-- ========================================

COMMENT ON TABLE companies IS 'Main companies table with comprehensive company information';

-- Add comments to key columns
COMMENT ON COLUMN companies.company_id IS 'Primary key for company records';
COMMENT ON COLUMN companies.company_name IS 'Company name (max 255 chars) with proper capitalization';
COMMENT ON COLUMN companies.fund_size IS 'Fund size in USD (integer)';
COMMENT ON COLUMN companies.aum IS 'Assets under management in USD (integer)';
COMMENT ON COLUMN companies.dry_powder IS 'Available capital for new investments';
COMMENT ON COLUMN companies.pipeline_status IS 'Current stage in business development pipeline';
COMMENT ON COLUMN companies.data_confidence_score IS 'Reliability score for data points (1-5)';
COMMENT ON COLUMN companies.website_scraping_status IS 'Status of website scraping process';
COMMENT ON COLUMN companies.website_scraping_date IS 'Date when website was last scraped';
COMMENT ON COLUMN companies.website_scraping_error IS 'Error message from website scraping';
COMMENT ON COLUMN companies.website_scraping_error_count IS 'Number of website scraping errors';
COMMENT ON COLUMN companies.company_overview_status IS 'Status of company overview generation';
COMMENT ON COLUMN companies.company_overview_date IS 'Date when company overview was last generated';
COMMENT ON COLUMN companies.company_overview_error IS 'Error message from company overview generation';
COMMENT ON COLUMN companies.company_overview_error_count IS 'Number of company overview errors';
COMMENT ON COLUMN companies.conflicts IS 'Conflict detection data';
COMMENT ON COLUMN companies.conflict_status IS 'Current conflict status';
COMMENT ON COLUMN companies.conflict_created_at IS 'When conflict was first detected';
COMMENT ON COLUMN companies.conflict_resolved_at IS 'When conflict was resolved';
COMMENT ON COLUMN companies.conflict_source IS 'Source of conflict detection';
