-- Add capital_position as an array to companies_dummy table
ALTER TABLE companies 
ADD COLUMN capital_position TEXT[] DEFAULT NULL;

-- Create index on capital_position for better query performance
CREATE INDEX companies_capital_position_idx 
ON companies USING GIN (capital_position);

-- Migrate capital_position data from contacts to companies_dummy
-- This will collect all unique capital positions for each company
UPDATE companies_dummy 
SET capital_position = subquery.capital_positions
FROM (
    SELECT 
        c.company_id,
        ARRAY_AGG(DISTINCT ct.capital_position) as capital_positions
    FROM companies_dummy c
    INNER JOIN contacts ct ON c.company_id = ct.company_id
    WHERE ct.capital_position IS NOT NULL 
      AND ct.capital_position != ''
      AND ct.capital_position != 'Unknown'
    GROUP BY c.company_id
) AS subquery
WHERE companies_dummy.company_id = subquery.company_id;

-- Also update the main companies table to keep it in sync
ALTER TABLE companies 
ADD COLUMN capital_position TEXT[] DEFAULT NULL;

CREATE INDEX companies_capital_position_idx 
ON companies USING GIN (capital_position);

-- Migrate data to main companies table as well
UPDATE companies 
SET capital_position = subquery.capital_positions
FROM (
    SELECT 
        c.company_id,
        ARRAY_AGG(DISTINCT ct.capital_position) as capital_positions
    FROM companies c
    INNER JOIN contacts ct ON c.company_id = ct.company_id
    WHERE ct.capital_position IS NOT NULL 
      AND ct.capital_position != ''
      AND ct.capital_position != 'Unknown'
    GROUP BY c.company_id
) AS subquery
WHERE companies.company_id = subquery.company_id;

-- Show migration results
SELECT 
    'companies_dummy' as table_name,
    COUNT(*) as total_companies,
    COUNT(capital_position) as companies_with_capital_position,
    COUNT(DISTINCT UNNEST(capital_position)) as unique_capital_positions
FROM companies_dummy
UNION ALL
SELECT 
    'companies' as table_name,
    COUNT(*) as total_companies,
    COUNT(capital_position) as companies_with_capital_position,
    COUNT(DISTINCT UNNEST(capital_position)) as unique_capital_positions
FROM companies;

-- Show sample of capital positions
SELECT 
    company_name,
    capital_position
FROM companies_dummy 
WHERE capital_position IS NOT NULL 
LIMIT 10; 