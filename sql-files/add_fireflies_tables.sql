-- Fireflies Accounts Table
CREATE TABLE IF NOT EXISTS fireflies_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    api_key TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Fireflies Transcripts Table
CREATE TABLE IF NOT EXISTS fireflies_transcripts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider_transcript_id TEXT NOT NULL,
    account_id UUID NOT NULL REFERENCES fireflies_accounts(id) ON DELETE CASCADE,
    title TEXT,
    meeting_date TIMESTAMP,
    duration INTEGER, -- in seconds
    participants TEXT[],
    transcript_text TEXT,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE (provider_transcript_id, account_id)
);

-- Add last_sync_at to fireflies_accounts for tracking sync status
ALTER TABLE fireflies_accounts ADD COLUMN IF NOT EXISTS last_sync_at TIMESTAMP;

-- Add status column to fireflies_accounts
ALTER TABLE fireflies_accounts ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active';

-- Add error_count and last_error to fireflies_accounts for monitoring
ALTER TABLE fireflies_accounts ADD COLUMN IF NOT EXISTS error_count INTEGER DEFAULT 0;
ALTER TABLE fireflies_accounts ADD COLUMN IF NOT EXISTS last_error TEXT; 