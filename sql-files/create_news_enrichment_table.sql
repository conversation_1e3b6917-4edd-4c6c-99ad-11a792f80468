-- Create news_enrichment table based on CSV schema
-- This table will contain denormalized enrichment data for news articles
-- If an article has multiple deals, multiple records will be created

CREATE TABLE IF NOT EXISTS news_enrichment (
    id SERIAL PRIMARY KEY,
    
    -- Meta/Source Data
    news_id INTEGER NOT NULL REFERENCES deal_news(id),
    source_name VARCHAR(255),
    source_url TEXT,
    data_source_link TEXT,
    publication_date DATE,
    scrape_date TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    scraping_source_type VARCHAR(50),
    
    -- Core Article Content
    headline TEXT,
    summary TEXT,
    body_text TEXT,
    news_topic VARCHAR(100),
    market_trend_tags JSONB,
    
    -- Entity Information (Lists stored as JSONB)
    buyer_name J<PERSON><PERSON><PERSON>,
    seller_name JSON<PERSON>,
    lender_name JSON<PERSON>,
    broker_name J<PERSON>N<PERSON>,
    company_name J<PERSON>N<PERSON>,
    equity_partner JSONB,
    developer_name <PERSON><PERSON><PERSON><PERSON>,
    tenant_name <PERSON><PERSON><PERSON><PERSON>,
    report_author <PERSON><PERSON><PERSON><PERSON>,
    linked_entities JSONB,
    
    -- Property & Project Details
    property_type VARCHAR(100),
    sub_property_type VARCHAR(100),
    strategies JSONB,
    project_name JSON<PERSON>,
    address JSONB,
    location_city JSONB,
    location_state JSONB,
    location_neighborhood JSONB,
    zip_code JSONB,
    square_footage BIGINT,
    unit_count INTEGER,
    construction_type VARCHAR(100),
    project_timeline VARCHAR(255),
    job_creation INTEGER,
    subsidy_info TEXT,
    
    -- Transaction Details
    deal_type JSONB,
    deal_size DECIMAL(15,2),
    deal_status VARCHAR(50),
    cap_rate DECIMAL(5,4),
    price_per_sf DECIMAL(10,2),
    loan_type JSONB,
    equity_type JSONB,
    
    -- Capital & Fund Information
    financing_type JSONB,
    capital_stack_notes JSONB,
    capital_markets_topic VARCHAR(100),
    interest_rate_impact JSONB,
    fund_loan_type JSONB,
    fund_equity_type JSONB,
    fund_name JSONB,
    fund_type JSONB,
    fund_size DECIMAL(15,2),
    fund_strategy JSONB,
    fundraising_status VARCHAR(50),
    capital_raised DECIMAL(15,2),
    target_irr DECIMAL(5,4),
    syndication_info JSONB,
    
    -- Distress & Financial Instruments
    distress_flag BOOLEAN,
    loan_performance VARCHAR(50),
    delinquency_rate DECIMAL(5,4),
    default_notices INTEGER,
    restructuring_event TEXT,
    recovery_estimate DECIMAL(5,4),
    cmbs_data JSONB,
    rating_agency_action JSONB,
    
    -- Market-Level Metrics
    market_name JSONB,
    submarket_name JSONB,
    time_period JSONB,
    vacancy_rate DECIMAL(5,4),
    availability_rate DECIMAL(5,4),
    rental_rate DECIMAL(10,2),
    absorption_rate BIGINT,
    transaction_volume DECIMAL(15,2),
    construction_pipeline BIGINT,
    deliveries BIGINT,
    occupancy_change DECIMAL(5,4),
    
    -- Market-Level Trends & Commentary
    rental_trend JSONB,
    cap_rate_avg DECIMAL(5,4),
    cap_rate_trend JSONB,
    new_supply_trend JSONB,
    demand_trend JSONB,
    sales_volume_change DECIMAL(5,4),
    leasing_activity JSONB,
    sentiment_summary TEXT,
    forecast_summary TEXT,
    macroeconomic_commentary TEXT,
    remote_work_impact TEXT,
    distress_indicator BOOLEAN,
    
    -- LLM / System Fields
    llm_tags JSONB,
    quotes_llm_tags JSONB,
    source_confidence DECIMAL(3,2),
    extraction_notes TEXT,
    
    -- System fields
    deal_record_id INTEGER, -- Links to deal_news_deals.id if this is a specific deal
    is_deal_specific BOOLEAN DEFAULT false, -- True if this record represents a specific deal
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    UNIQUE(news_id, deal_record_id) -- Prevent duplicate records for same news/deal combination
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_news_enrichment_news_id ON news_enrichment(news_id);
CREATE INDEX IF NOT EXISTS idx_news_enrichment_deal_record_id ON news_enrichment(deal_record_id);
CREATE INDEX IF NOT EXISTS idx_news_enrichment_publication_date ON news_enrichment(publication_date);
CREATE INDEX IF NOT EXISTS idx_news_enrichment_property_type ON news_enrichment(property_type);
CREATE INDEX IF NOT EXISTS idx_news_enrichment_deal_type ON news_enrichment USING GIN   (deal_type);
CREATE INDEX IF NOT EXISTS idx_news_enrichment_market_name ON news_enrichment USING GIN(market_name);
CREATE INDEX IF NOT EXISTS idx_news_enrichment_strategies ON news_enrichment USING GIN(strategies);
CREATE INDEX IF NOT EXISTS idx_news_enrichment_llm_tags ON news_enrichment USING GIN(llm_tags);

-- Create update trigger
CREATE OR REPLACE FUNCTION update_news_enrichment_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER news_enrichment_updated_at_trigger
    BEFORE UPDATE ON news_enrichment
    FOR EACH ROW
    EXECUTE FUNCTION update_news_enrichment_updated_at();

COMMENT ON TABLE news_enrichment IS 'Denormalized news enrichment data based on CSV schema. Multiple records per article if multiple deals exist.'; 