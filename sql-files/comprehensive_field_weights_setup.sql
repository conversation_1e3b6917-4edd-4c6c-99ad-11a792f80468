-- COMPREHENSIVE FIELD WEIGHTS SETUP
-- Includes ALL investment_criteria columns for deal matching
-- Prioritizes existing fields to sum to 100%, sets others to 0 for future expansion

-- 1. Clear existing weights
DELETE FROM field_weights;

-- 2. Insert comprehensive field weights
INSERT INTO field_weights (field_name, weight, description) VALUES 

-- ===== BINARY GATE (EXCLUSION FILTER) =====
('capital_position', 1.0, 'Binary gate - must match or deal excluded completely'),

-- ===== TIER 1: HIGH PRIORITY FIELDS (Currently Used) - Sum to 100% =====
('location', 0.35, 'Geographic matching (region/state/city hierarchy) - 35%'),
('deal_size', 0.30, 'Deal size overlap with complex fuzzy logic - 30%'),
('property_types', 0.15, 'Property type array matching - 15%'),
('strategies', 0.10, 'Investment strategies array matching - 10%'),
('financial_products', 0.05, 'Financial products array matching - 5%'),
('loan_to_value', 0.03, 'LTV range overlap matching - 3%'),
('interest_rate', 0.02, 'Interest rate comparison matching - 2%'),

-- ===== TIER 2: MEDIUM PRIORITY FIELDS (Ready to Enable) - Currently 0% =====
('property_sub_categories', 0.0, 'Property subcategory array matching - Ready to enable'),
('target_return', 0.0, 'Target return/IRR range matching - Ready to enable'),  
('historical_irr', 0.0, 'Historical IRR range matching - Ready to enable'),
('historical_em', 0.0, 'Historical equity multiple range matching - Ready to enable'),
('hold_period', 0.0, 'Hold period range matching (min/max) - Ready to enable'),
('loan_term', 0.0, 'Loan term range matching (min/max months) - Ready to enable'),
('loan_to_cost', 0.0, 'LTC range overlap matching - Ready to enable'),
('loan_dscr', 0.0, 'DSCR range overlap matching - Ready to enable'),

-- ===== TIER 3: LOAN & FINANCING FIELDS (Ready to Enable) - Currently 0% =====
('loan_type', 0.0, 'Loan type array matching - Ready to enable'),
('loan_type_normalized', 0.0, 'Normalized loan type array matching - Ready to enable'),
('loan_program', 0.0, 'Loan program array matching - Ready to enable'),
('structured_loan_tranche', 0.0, 'Loan tranche array matching - Ready to enable'),
('recourse_loan', 0.0, 'Recourse loan array matching - Ready to enable'),
('interest_rate_sofr', 0.0, 'SOFR-based interest rate matching - Ready to enable'),
('interest_rate_wsj', 0.0, 'WSJ Prime-based interest rate matching - Ready to enable'),
('interest_rate_prime', 0.0, 'Prime-based interest rate matching - Ready to enable'),
('interest_rate_libor', 0.0, 'LIBOR-based interest rate matching - Ready to enable'),
('interest_rate_5yt', 0.0, '5-year Treasury-based rate matching - Ready to enable'),
('interest_rate_10yt', 0.0, '10-year Treasury-based rate matching - Ready to enable'),

-- ===== TIER 4: FEE & COST FIELDS (Ready to Enable) - Currently 0% =====
('loan_origination_fee', 0.0, 'Origination fee range matching - Ready to enable'),
('loan_exit_fee', 0.0, 'Exit fee range matching - Ready to enable'),
('closing_time_weeks', 0.0, 'Closing time requirement matching - Ready to enable'),

-- ===== TIER 5: GEOGRAPHIC GRANULARITY (Ready to Enable) - Currently 0% =====
('country', 0.0, 'Country array matching - Ready to enable'),
('region', 0.0, 'Region array matching (separate from location) - Ready to enable'), 
('state', 0.0, 'State array matching (separate from location) - Ready to enable'),
('city', 0.0, 'City array matching (separate from location) - Ready to enable'),
('location_focus', 0.0, 'Location focus array matching - Ready to enable'),

-- ===== TIER 6: METADATA & ENTITY FIELDS (Ready to Enable) - Currently 0% =====
('capital_source', 0.0, 'Capital source text matching - Ready to enable'),
('entity_name', 0.0, 'Entity name text matching - Ready to enable'),
('notes', 0.0, 'Notes text similarity matching - Ready to enable');

-- 3. Verification queries
SELECT '=== FIELD WEIGHTS SETUP COMPLETE ===' as status;

-- 4. Show current active fields (weight > 0)
SELECT 
  '=== TIER 1: ACTIVE FIELDS (Weight > 0) ===' as tier;

SELECT 
  field_name,
  ROUND(weight * 100, 1) || '%' as percentage,
  description
FROM field_weights 
WHERE weight > 0 AND field_name != 'capital_position'
ORDER BY weight DESC;

-- 5. Show binary gate
SELECT 
  '=== BINARY GATE (Exclusion Filter) ===' as tier;
  
SELECT 
  field_name,
  'Binary Gate' as type,
  description
FROM field_weights 
WHERE field_name = 'capital_position';

-- 6. Show ready-to-enable fields
SELECT 
  '=== TIER 2-6: READY TO ENABLE (Weight = 0) ===' as tier;

SELECT 
  field_name,
  '0%' as current_percentage,
  description
FROM field_weights 
WHERE weight = 0
ORDER BY field_name;

-- 7. Normalization check
SELECT 
  '=== NORMALIZATION CHECK ===' as check_type;

SELECT 
  'Active Fields Total:' as info,
  ROUND(SUM(weight) * 100, 1) || '%' as total_percentage,
  COUNT(*) as field_count,
  CASE 
    WHEN ABS(SUM(weight) - 1.0) < 0.001 THEN 'PERFECT ✓'
    ELSE 'ERROR - DOES NOT SUM TO 100%'
  END as status
FROM field_weights 
WHERE weight > 0 AND field_name != 'capital_position';

-- 8. Summary
SELECT 
  '=== SUMMARY ===' as summary;

SELECT 
  COUNT(CASE WHEN weight > 0 AND field_name != 'capital_position' THEN 1 END) as active_fields,
  COUNT(CASE WHEN weight = 0 THEN 1 END) as ready_to_enable,
  COUNT(CASE WHEN field_name = 'capital_position' THEN 1 END) as binary_gates,
  COUNT(*) as total_fields
FROM field_weights;

-- 9. Instructions for enabling new fields
SELECT 
  '=== TO ENABLE NEW FIELDS ===' as instructions;
  
SELECT 
  'To enable a new field:' as step_1,
  '1. Reduce existing field weights proportionally' as step_2,
  '2. Assign weight to new field (ensure total = 100%)' as step_3,
  '3. Update matching logic in matching-utils.ts' as step_4,
  '4. Test the new matching behavior' as step_5; 