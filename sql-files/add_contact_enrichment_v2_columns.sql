-- Contact Enrichment V2 Schema Migration
-- Adding new columns based on revised contact schema for enhanced enrichment

-- Add new contact information columns
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS company_name TEXT;
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS additional_email TEXT;

-- Add social media columns
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS twitter TEXT;
<PERSON>TER TABLE contacts ADD COLUMN IF NOT EXISTS facebook TEXT;
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS instagram TEXT;
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS youtube TEXT;

-- Add personal information columns
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS executive_summary TEXT;
<PERSON>TER TABLE contacts ADD COLUMN IF NOT EXISTS career_timeline JSONB DEFAULT '[]'::jsonb;

-- Add education columns
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS education_college TEXT;
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS education_college_year_graduated TEXT;
<PERSON><PERSON><PERSON> TABLE contacts ADD COLUMN IF NOT EXISTS education_high_school TEXT;
<PERSON><PERSON><PERSON> TABLE contacts ADD COLUMN IF NOT EXISTS education_high_school_year_graduated TEXT;

-- Add personal details columns
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS honorable_achievements JSONB DEFAULT '[]'::jsonb;
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS hobbies JSONB DEFAULT '[]'::jsonb;
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS age TEXT;

-- Add location details (zipcode is already contact_zip_code, address is contact_address)
-- These already exist but with different names

-- Add contact metadata columns
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS contact_type TEXT;
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS relationship_owner TEXT;
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS role_in_decision_making TEXT;

-- Add interaction tracking columns
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS last_contact_date TIMESTAMP WITH TIME ZONE;
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS source_of_introduction TEXT;

-- Add compliance columns
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS accredited_investor_status BOOLEAN DEFAULT FALSE;
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS kyc_status TEXT DEFAULT 'pending';

-- Add v2 enrichment tracking columns
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS contact_enrichment_v2_status VARCHAR(50) DEFAULT 'pending';
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS contact_enrichment_v2_date TIMESTAMP WITH TIME ZONE;
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS contact_enrichment_v2_error TEXT;

-- Add indexes for new searchable columns
CREATE INDEX IF NOT EXISTS idx_contacts_company_name ON contacts(company_name);
CREATE INDEX IF NOT EXISTS idx_contacts_contact_type ON contacts(contact_type);
CREATE INDEX IF NOT EXISTS idx_contacts_relationship_owner ON contacts(relationship_owner);
CREATE INDEX IF NOT EXISTS idx_contacts_role_in_decision_making ON contacts(role_in_decision_making);
CREATE INDEX IF NOT EXISTS idx_contacts_enrichment_v2_status ON contacts(contact_enrichment_v2_status);
CREATE INDEX IF NOT EXISTS idx_contacts_accredited_investor ON contacts(accredited_investor_status);
CREATE INDEX IF NOT EXISTS idx_contacts_kyc_status ON contacts(kyc_status);

-- Add comments to document the new columns
COMMENT ON COLUMN contacts.company_name IS 'Company name associated with the contact';
COMMENT ON COLUMN contacts.additional_email IS 'Secondary email address for the contact';
COMMENT ON COLUMN contacts.twitter IS 'Twitter profile URL';
COMMENT ON COLUMN contacts.facebook IS 'Facebook profile URL';
COMMENT ON COLUMN contacts.instagram IS 'Instagram profile URL';
COMMENT ON COLUMN contacts.youtube IS 'YouTube channel URL';
COMMENT ON COLUMN contacts.executive_summary IS 'Executive summary of the contact profile';
COMMENT ON COLUMN contacts.career_timeline IS 'JSON array of career milestones and timeline';
COMMENT ON COLUMN contacts.education_college IS 'College or university attended';
COMMENT ON COLUMN contacts.education_college_year_graduated IS 'Year graduated from college';
COMMENT ON COLUMN contacts.education_high_school IS 'High school attended';
COMMENT ON COLUMN contacts.education_high_school_year_graduated IS 'Year graduated from high school';
COMMENT ON COLUMN contacts.honorable_achievements IS 'JSON array of notable achievements and honors';
COMMENT ON COLUMN contacts.hobbies IS 'JSON array of hobbies and interests';
COMMENT ON COLUMN contacts.age IS 'Age of the contact (stored as text for flexibility)';
COMMENT ON COLUMN contacts.contact_type IS 'Type/category of contact (e.g., Decision Maker, Influencer)';
COMMENT ON COLUMN contacts.relationship_owner IS 'Person who owns the relationship with this contact';
COMMENT ON COLUMN contacts.role_in_decision_making IS 'Role in decision making process (Decision Maker, Influencer, Analyst, Gatekeeper)';
COMMENT ON COLUMN contacts.last_contact_date IS 'Date of last contact interaction';
COMMENT ON COLUMN contacts.source_of_introduction IS 'How the contact was initially introduced or discovered';
COMMENT ON COLUMN contacts.accredited_investor_status IS 'Whether the contact is an accredited investor';
COMMENT ON COLUMN contacts.kyc_status IS 'Know Your Customer verification status';
COMMENT ON COLUMN contacts.contact_enrichment_v2_status IS 'Status of V2 contact enrichment processing';
COMMENT ON COLUMN contacts.contact_enrichment_v2_date IS 'Date when V2 contact enrichment was completed';
COMMENT ON COLUMN contacts.contact_enrichment_v2_error IS 'Error message if V2 enrichment failed';
