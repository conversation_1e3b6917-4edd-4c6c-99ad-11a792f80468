-- Update news_enrichment table to ensure correct data types
-- Based on CSV schema requirements

-- Ensure all JSONB fields are properly typed
ALTER TABLE news_enrichment 
  ALTER COLUMN deal_type TYPE JSONB USING deal_type::jsonb,
  ALTER COLUMN news_topic TYPE JSONB USING news_topic::jsonb,
  ALTER COLUMN property_type TYPE JSONB USING property_type::jsonb,
  ALTER COLUMN sub_property_type TYPE JSONB USING sub_property_type::jsonb,
  ALTER COLUMN construction_type TYPE JSONB USING construction_type::jsonb,
  ALTER COLUMN capital_markets_topic TYPE TEXT USING capital_markets_topic::text;

-- Update GIN indexes for JSONB fields
DROP INDEX IF EXISTS idx_news_enrichment_deal_type;
CREATE INDEX IF NOT EXISTS idx_news_enrichment_deal_type ON news_enrichment USING GIN (deal_type);

-- Add any missing indexes for new JSONB fields
CREATE INDEX IF NOT EXISTS idx_news_enrichment_loan_type ON news_enrichment USING GIN (loan_type);
CREATE INDEX IF NOT EXISTS idx_news_enrichment_equity_type ON news_enrichment USING GIN (equity_type);
CREATE INDEX IF NOT EXISTS idx_news_enrichment_fund_type ON news_enrichment USING GIN (fund_type);
CREATE INDEX IF NOT EXISTS idx_news_enrichment_rental_trend ON news_enrichment USING GIN (rental_trend);

-- Ensure the table is ready for the new comprehensive schema
COMMENT ON TABLE news_enrichment IS 'Comprehensive news enrichment data based on CSV schema. Multiple records per article if multiple deals exist. Updated with JSONB arrays for multi-value fields.';

-- Show the final schema
SELECT 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'news_enrichment' 
ORDER BY ordinal_position; 


Create Table news as 
Select * from deal_news where 1=1;