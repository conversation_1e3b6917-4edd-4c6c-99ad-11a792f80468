-- Create consolidated contact_enrichment table
-- This table combines data from contact_searched_data, contact_extracted_data, and processing metadata

CREATE TABLE IF NOT EXISTS contact_enrichment (
    id SERIAL PRIMARY KEY,
    contact_id BIGINT NOT NULL REFERENCES contacts(contact_id),
    
    -- OSINT Profile Data (from contact_searched_data)
    osint_profile TEXT,
    
    -- Extracted Profile Data (from contact_extracted_data)
    executive_summary TEXT,
    career_timeline JSONB,
    notable_activities JSONB,
    education JSONB,
    personal_tidbits JSONB,
    conversation_hooks JSONB,
    sources JSONB,
    
    -- Classification Data
    company_type TEXT,
    capital_positions JSONB,
    confidence DOUBLE PRECISION DEFAULT 0.0,
    reasoning TEXT,
    
    -- Processing Metadata
    input_data JSONB,
    prompt_content TEXT,
    tokens_used INTEGER,
    llm_model VARCHAR(100),
    llm_usage JSONB,
    
    -- Processing Status
    status VARCHAR(20) DEFAULT 'pending', -- pending, running, completed, failed
    error_message TEXT,
    processing_attempts INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
    
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_contact_enrichment_contact_id ON contact_enrichment(contact_id);
CREATE INDEX IF NOT EXISTS idx_contact_enrichment_status ON contact_enrichment(status);
CREATE INDEX IF NOT EXISTS idx_contact_enrichment_company_type ON contact_enrichment(company_type);
CREATE INDEX IF NOT EXISTS idx_contact_enrichment_created_at ON contact_enrichment(created_at);
CREATE INDEX IF NOT EXISTS idx_contact_enrichment_completed_at ON contact_enrichment(completed_at);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_contact_enrichment_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_contact_enrichment_updated_at
    BEFORE UPDATE ON contact_enrichment
    FOR EACH ROW
    EXECUTE FUNCTION update_contact_enrichment_updated_at();

-- Add comments for documentation
COMMENT ON TABLE contact_enrichment IS 'Consolidated table for storing all contact enrichment data including OSINT research, extracted profiles, and classification results';
COMMENT ON COLUMN contact_enrichment.contact_id IS 'Foreign key to contacts table';
COMMENT ON COLUMN contact_enrichment.osint_profile IS 'Comprehensive OSINT research findings with source URLs';
COMMENT ON COLUMN contact_enrichment.executive_summary IS 'Professional bio summary based on research findings';
COMMENT ON COLUMN contact_enrichment.career_timeline IS 'Array of career progression entries';
COMMENT ON COLUMN contact_enrichment.notable_activities IS 'Array of deals, publications, speaking engagements, etc.';
COMMENT ON COLUMN contact_enrichment.education IS 'Educational background and credentials';
COMMENT ON COLUMN contact_enrichment.personal_tidbits IS 'Personal information like hobbies, philanthropy, etc.';
COMMENT ON COLUMN contact_enrichment.conversation_hooks IS 'Personalized conversation starters for outreach';
COMMENT ON COLUMN contact_enrichment.sources IS 'Complete list of source URLs referenced';
COMMENT ON COLUMN contact_enrichment.company_type IS 'Normalized company type';
COMMENT ON COLUMN contact_enrichment.capital_position IS 'Capital position classification';
COMMENT ON COLUMN contact_enrichment.confidence IS 'Confidence level for classification (0.0-1.0)';
COMMENT ON COLUMN contact_enrichment.reasoning IS 'Explanation of classification decision';
COMMENT ON COLUMN contact_enrichment.enrichment_type IS 'Type of enrichment performed';
COMMENT ON COLUMN contact_enrichment.input_data IS 'Input data used for enrichment';
COMMENT ON COLUMN contact_enrichment.prompt_content IS 'Prompt content used for LLM call';
COMMENT ON COLUMN contact_enrichment.tokens_used IS 'Number of tokens used in LLM call';
COMMENT ON COLUMN contact_enrichment.llm_model IS 'LLM model used for enrichment';
COMMENT ON COLUMN contact_enrichment.llm_usage IS 'Detailed LLM usage statistics';
COMMENT ON COLUMN contact_enrichment.status IS 'Current processing status';
COMMENT ON COLUMN contact_enrichment.error_message IS 'Error message if processing failed';
COMMENT ON COLUMN contact_enrichment.processing_attempts IS 'Number of processing attempts';
COMMENT ON COLUMN contact_enrichment.completed_at IS 'Timestamp when enrichment was completed'; 