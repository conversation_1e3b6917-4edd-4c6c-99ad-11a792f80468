-- Migration script to add missing columns to deal_data_pivoted table
-- These columns are referenced in the extraction map but don't exist in the database

-- Add missing project budget source columns
ALTER TABLE public.deal_data_pivoted 
ADD COLUMN IF NOT EXISTS "project_budget_sources__total_total_development" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources__total_" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources__total_nsf" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources_debt_senior_total_development" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources_debt_senior_" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources_debt_senior_nsf" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources_debt_mezzanine_total_development" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources_debt_mezzanine_" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources_debt_mezzanine_nsf" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources_equity_general_partner_gp__total_development" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources_equity_general_partner_gp_" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources_equity_general_partner_gp_nsf" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources_equity_limited_partner_lp__total_development" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources_equity_limited_partner_lp_" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources_equity_limited_partner_lp_nsf" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources_equity_preferred_equity__total_development" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources_equity_preferred_equity_" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_sources_equity_preferred_equity_nsf" TEXT;

-- Add missing project budget uses columns
ALTER TABLE public.deal_data_pivoted 
ADD COLUMN IF NOT EXISTS "project_budget_uses_acquisition_costs_gsf" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_uses_acquisition_costs_nsf" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_uses_acquisition_costs_" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_uses_acquisition_total_development" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_uses_hard_costs_total_development" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_uses_soft_costs_total_development" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_uses_financing_costs_total_development" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_uses_total_total_development" TEXT;

-- Add missing project budget profit summary columns
ALTER TABLE public.deal_data_pivoted 
ADD COLUMN IF NOT EXISTS "project_budget_profit_summary_condo_equity_returns_general_partner_gp__equity_multiple" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_profit_summary_condo_equity_returns_general_partner_gp__irr" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_profit_summary_condo_equity_returns_limited_partner_lp__equity_mutliple" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_profit_summary_condo_equity_returns_limited_partner_lp__irr" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_profit_summary_condo_equity_returns_total_equity_multiple" TEXT,
ADD COLUMN IF NOT EXISTS "project_budget_profit_summary_condo_equity_returns_total_irr" TEXT;

-- Add missing rental pro forma columns
ALTER TABLE public.deal_data_pivoted 
ADD COLUMN IF NOT EXISTS "rental_pro_forma_operational__noi__year_1" TEXT,
ADD COLUMN IF NOT EXISTS "rental_pro_forma_sales_assumptions__yield_on_cost_year_1_" TEXT,
ADD COLUMN IF NOT EXISTS "rental_pro_forma_sales_assumptions__terminal_cap_rate_" TEXT,
ADD COLUMN IF NOT EXISTS "rental_pro_forma_sales_assumptions__ltv" TEXT,
ADD COLUMN IF NOT EXISTS "rental_pro_forma_sales_assumptions__debt_yield_year_1_" TEXT,
ADD COLUMN IF NOT EXISTS "rental_pro_forma_sales_assumptions__value_psf" TEXT,
ADD COLUMN IF NOT EXISTS "rental_pro_forma_sales_assumptions__debt_psf" TEXT,
ADD COLUMN IF NOT EXISTS "rental_pro_forma_sales_assumptions__sale_year_including__2_year_construction_" TEXT,
ADD COLUMN IF NOT EXISTS "rental_pro_forma_operational_financial_detail__debt_yield_last_dollar__year_1_" TEXT,
ADD COLUMN IF NOT EXISTS "rental_pro_forma_operational_financial_detail_debt_service_coverage_ratio_dscr__year_1" TEXT,
ADD COLUMN IF NOT EXISTS "rental_pro_forma_operational_return_analysis_net_cash_flow_to_equity_cash_on_cash__year_1" TEXT,
ADD COLUMN IF NOT EXISTS "rental_pro_forma_operational_equity_return_gp_equity_multiple_" TEXT,
ADD COLUMN IF NOT EXISTS "rental_pro_forma_operational_equity_return_gp_irr" TEXT;

-- Add comments for documentation
COMMENT ON COLUMN public.deal_data_pivoted."project_budget_sources__total_total_development" IS 'Total development amount from project budget sources';
COMMENT ON COLUMN public.deal_data_pivoted."project_budget_sources__total_" IS 'Total amount from project budget sources';
COMMENT ON COLUMN public.deal_data_pivoted."project_budget_sources__total_nsf" IS 'Total per NSF from project budget sources';

-- Log the changes
DO $$
BEGIN
    RAISE NOTICE 'Added missing columns to deal_data_pivoted table';
END $$; 