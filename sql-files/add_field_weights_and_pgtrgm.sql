-- Enable pg_trgm extension for fuzzy string matching
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Create field_weights table for configurable matching weights
CREATE TABLE IF NOT EXISTS field_weights (
    id SERIAL PRIMARY KEY,
    field_name TEXT UNIQUE NOT NULL,
    weight NUMERIC(4,3) NOT NULL DEFAULT 0.1,
    description TEXT
);

-- Insert default weights for common fields (customize as needed)
INSERT INTO field_weights (field_name, weight, description) VALUES
    ('property_types', 0.3, 'Property type match'),
    ('region', 0.2, 'Region match'),
    ('deal_size', 0.3, 'Deal size overlap'),
    ('location', 0.2, 'Location match'),
    ('irr', 0.2, 'Expected IRR'),
    ('em', 0.1, 'Equity Multiple'),
    ('hold_period', 0.1, 'Hold Period'),
    ('equity_structure', 0.1, 'Equity Structure'),
    ('loan_type', 0.1, 'Loan Type'),
    ('ltc', 0.1, 'Loan-to-Cost'),
    ('ltv', 0.1, 'Loan-to-Value'),
    ('term', 0.1, 'Term (Months)')
ON CONFLICT (field_name) DO NOTHING; 