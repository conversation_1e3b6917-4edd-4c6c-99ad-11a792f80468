-- =====================================================
-- Asynchronous Upload System Database Schema
-- =====================================================

-- Upload Logs Table - Tracks upload metadata and processing status
CREATE TABLE upload_logs (
    upload_id SERIAL PRIMARY KEY,
    file_name VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000) NOT NULL, -- Path to stored file in filesystem
    file_size BIGINT NOT NULL, -- File size in bytes
    file_type VARCHAR(50) NOT NULL, -- csv, xlsx, etc
    original_headers TEXT[] NOT NULL, -- Original CSV headers
    total_rows INTEGER NOT NULL, -- Total number of data rows (excluding header)
    
    -- Header Mapping Information
    header_mappings JSONB NOT NULL, -- Final header mappings {csv_header: db_field}
    mapped_columns TEXT[], -- Database columns found in AI mapping
    header_mapping_status VARCHAR(50) DEFAULT 'completed', -- completed, failed
    
    -- Processing Status
    status VARCHAR(50) DEFAULT 'pending', -- pending, processing, completed, failed, cancelled
    processed_until INTEGER DEFAULT 0, -- Last successfully processed row number
    is_completed BOOLEAN DEFAULT FALSE,
    
    -- Error Tracking
    error_status VARCHAR(50) DEFAULT 'none', -- none, recoverable, fatal
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    last_retry_at TIMESTAMP,
    
    -- AI/LLM Information
    llm_used VARCHAR(100), -- openai-gpt4, claude, etc
    llm_prompt TEXT, -- Prompt used for header mapping
    llm_input TEXT, -- Input sent to LLM
    llm_output TEXT, -- Raw LLM response
    tokens_used INTEGER DEFAULT 0,
    
    -- Processing Results
    companies_processed INTEGER DEFAULT 0,
    contacts_processed INTEGER DEFAULT 0,
    investment_criteria_processed INTEGER DEFAULT 0,
    conflicts_detected INTEGER DEFAULT 0,
    
    -- Metadata
    source VARCHAR(200), -- Import source description
    uploaded_by VARCHAR(100), -- User who uploaded
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_started_at TIMESTAMP,
    processing_completed_at TIMESTAMP,
    
    -- Additional metadata
    extra_attrs JSONB DEFAULT '{}' -- For future extensibility
);

-- Indexes for upload_logs
CREATE INDEX idx_upload_logs_status ON upload_logs(status);
CREATE INDEX idx_upload_logs_created_at ON upload_logs(created_at);
CREATE INDEX idx_upload_logs_file_name ON upload_logs(file_name);
CREATE INDEX idx_upload_logs_processing_status ON upload_logs(status, is_completed);

-- Upload Data Log Table - Generic table to store CSV data
CREATE TABLE upload_data_log (
    data_id SERIAL PRIMARY KEY,
    upload_log_id INTEGER REFERENCES upload_logs(upload_id) ON DELETE CASCADE,
    row_number INTEGER NOT NULL, -- Row number in original CSV (1-based)
    
    -- Header mapping reference
    headers_map JSONB NOT NULL, -- {key1: "first_name", key2: "last_name", ...}
    
    -- Processing status for this row
    processed BOOLEAN DEFAULT FALSE,
    error_status VARCHAR(50) DEFAULT 'none', -- none, error, retry
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    
    -- Generic data columns (50 columns to handle any CSV structure)
    key1 TEXT,
    key2 TEXT,
    key3 TEXT,
    key4 TEXT,
    key5 TEXT,
    key6 TEXT,
    key7 TEXT,
    key8 TEXT,
    key9 TEXT,
    key10 TEXT,
    key11 TEXT,
    key12 TEXT,
    key13 TEXT,
    key14 TEXT,
    key15 TEXT,
    key16 TEXT,
    key17 TEXT,
    key18 TEXT,
    key19 TEXT,
    key20 TEXT,
    key21 TEXT,
    key22 TEXT,
    key23 TEXT,
    key24 TEXT,
    key25 TEXT,
    key26 TEXT,
    key27 TEXT,
    key28 TEXT,
    key29 TEXT,
    key30 TEXT,
    key31 TEXT,
    key32 TEXT,
    key33 TEXT,
    key34 TEXT,
    key35 TEXT,
    key36 TEXT,
    key37 TEXT,
    key38 TEXT,
    key39 TEXT,
    key40 TEXT,
    key41 TEXT,
    key42 TEXT,
    key43 TEXT,
    key44 TEXT,
    key45 TEXT,
    key46 TEXT,
    key47 TEXT,
    key48 TEXT,
    key49 TEXT,
    key50 TEXT,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure row numbers are unique per upload (each upload can have multiple rows)
    CONSTRAINT unique_upload_row UNIQUE (upload_log_id, row_number)
);

-- Indexes for upload_data_log
CREATE INDEX idx_upload_data_log_upload_id ON upload_data_log(upload_log_id);
CREATE INDEX idx_upload_data_log_processed ON upload_data_log(processed);
CREATE INDEX idx_upload_data_log_row_number ON upload_data_log(upload_log_id, row_number);
CREATE INDEX idx_upload_data_log_error_status ON upload_data_log(error_status);



-- Function to update upload_logs.updated_at automatically
CREATE OR REPLACE FUNCTION update_upload_logs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for auto-updating updated_at
CREATE TRIGGER trigger_upload_logs_updated_at
    BEFORE UPDATE ON upload_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_upload_logs_updated_at();

-- Function to update upload_data_log.updated_at automatically
CREATE OR REPLACE FUNCTION update_upload_data_log_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for auto-updating updated_at
CREATE TRIGGER trigger_upload_data_log_updated_at
    BEFORE UPDATE ON upload_data_log
    FOR EACH ROW
    EXECUTE FUNCTION update_upload_data_log_updated_at();

-- Helper Views for common queries

-- View for upload overview
CREATE VIEW upload_overview AS
SELECT 
    ul.upload_id,
    ul.file_name,
    ul.status,
    ul.total_rows,
    ul.processed_until,
    ul.is_completed,
    ul.companies_processed,
    ul.contacts_processed,
    ul.conflicts_detected,
    ul.error_status,
    ul.retry_count,
    ul.created_at,
    ul.processing_started_at,
    ul.processing_completed_at,
    -- Calculate progress percentage
    CASE 
        WHEN ul.total_rows > 0 THEN ROUND((ul.processed_until::DECIMAL / ul.total_rows) * 100, 2)
        ELSE 0
    END as progress_percentage,
    -- Calculate processing duration
    CASE 
        WHEN ul.processing_completed_at IS NOT NULL AND ul.processing_started_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (ul.processing_completed_at - ul.processing_started_at))
        ELSE NULL
    END as processing_duration_seconds
FROM upload_logs ul;

-- View for active processing jobs
CREATE VIEW active_processing_jobs AS
SELECT 
    ul.upload_id,
    ul.file_name,
    ul.status as upload_status,
    ul.processing_started_at,
    ul.processed_until,
    ul.total_rows,
    ul.retry_count,
    CASE 
        WHEN ul.total_rows > 0 THEN ROUND((ul.processed_until::DECIMAL / ul.total_rows) * 100, 2)
        ELSE 0
    END as progress_percentage
FROM upload_logs ul
WHERE ul.status IN ('pending', 'processing');

-- Comments for documentation
COMMENT ON TABLE upload_logs IS 'Tracks metadata and status for asynchronous file uploads';
COMMENT ON TABLE upload_data_log IS 'Stores actual CSV data in generic format for background processing';

COMMENT ON COLUMN upload_logs.header_mappings IS 'JSON object mapping CSV headers to database fields';
COMMENT ON COLUMN upload_logs.processed_until IS 'Last successfully processed row number (0-based)';
COMMENT ON COLUMN upload_data_log.headers_map IS 'Maps key1,key2,etc to actual field names for this upload';
COMMENT ON COLUMN upload_data_log.row_number IS 'Original row number in CSV file (1-based, excluding header)'; 