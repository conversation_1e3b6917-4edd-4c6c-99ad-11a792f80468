-- Migration: Add deal-contacts relationship table for multiple contacts per deal

-- Create deal_contacts junction table
CREATE TABLE IF NOT EXISTS public.deal_contacts (
    id SERIAL PRIMARY KEY,
    deal_id INTEGER NOT NULL REFERENCES public.deals(deal_id) ON DELETE CASCADE,
    contact_id INTEGER NOT NULL REFERENCES public.contacts(contact_id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(deal_id, contact_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_deal_contacts_deal_id ON public.deal_contacts(deal_id);
CREATE INDEX IF NOT EXISTS idx_deal_contacts_contact_id ON public.deal_contacts(contact_id);

-- Add comments for documentation
COMMENT ON TABLE public.deal_contacts IS 'Junction table linking deals to multiple contacts';
COMMENT ON COLUMN public.deal_contacts.deal_id IS 'Reference to the deal';
COMMENT ON COLUMN public.deal_contacts.contact_id IS 'Reference to the contact';

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_deal_contacts_updated_at 
    BEFORE UPDATE ON public.deal_contacts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column(); 