-- Gmail Accounts Table
CREATE TABLE IF NOT EXISTS gmail_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT NOT NULL UNIQUE,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Gmail Threads Table
CREATE TABLE IF NOT EXISTS gmail_threads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider_thread_id TEXT NOT NULL,
    account_id UUID NOT NULL REFERENCES gmail_accounts(id) ON DELETE CASCADE,
    subject TEXT,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE (provider_thread_id, account_id)
);

-- Gmail Messages Table
CREATE TABLE IF NOT EXISTS gmail_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    thread_id UUID NOT NULL REFERENCES gmail_threads(id) ON DELETE CASCADE,
    gmail_message_id TEXT NOT NULL,
    sender TEXT,
    recipients TEXT[],
    sent_at TIMESTAMP,
    subject TEXT,
    body TEXT,
    raw JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE (gmail_message_id)
); 

-- Add last_history_id to gmail_accounts
ALTER TABLE gmail_accounts ADD COLUMN IF NOT EXISTS last_history_id TEXT;

-- Add metadata column to gmail_messages for storing all message metadata
ALTER TABLE gmail_messages ADD COLUMN IF NOT EXISTS metadata JSONB;

-- Add account_id column to gmail_messages
ALTER TABLE gmail_messages ADD COLUMN IF NOT EXISTS account_id UUID REFERENCES gmail_accounts(id);
-- Optionally, backfill account_id for existing messages (if needed)
-- UPDATE gmail_messages m SET account_id = t.account_id FROM gmail_threads t WHERE m.thread_id = t.id AND m.account_id IS NULL;

-- (No schema change needed for sender/recipients, but clarify in comments)
-- sender: TEXT (email only)
-- recipients: TEXT[] (emails only) 