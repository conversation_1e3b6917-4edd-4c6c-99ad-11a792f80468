-- Generic File Management System - Core Schema
-- This system handles any type of files with content hash deduplication
-- No file system storage - only metadata and relationships

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create the generic files table
CREATE TABLE IF NOT EXISTS public.files (
    file_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- File metadata
    file_name TEXT NOT NULL,
    original_name TEXT NOT NULL, -- Original filename as uploaded
    title TEXT, -- User-provided title for the file
    description TEXT, -- Optional description
    
    -- Content identification
    content_hash VARCHAR(64) NOT NULL UNIQUE, -- SHA-256 hash of file content for deduplication
    content_hash_algorithm VARCHAR(20) DEFAULT 'sha256',
    
    -- File properties
    mime_type TEXT NOT NULL,
    file_size_bytes BIGINT NOT NULL,
    file_extension TEXT, -- Extracted from original filename
    
    -- Upload metadata
    uploaded_by TEXT, -- User who uploaded the file
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    upload_source TEXT, -- web, api, email, etc.
    
    -- Security and access
    is_public BOOLEAN DEFAULT false,
    access_level TEXT DEFAULT 'private', -- private, team, public
    
    -- Additional metadata
    tags TEXT[], -- Array of tags for categorization
    metadata JSONB, -- Flexible metadata storage
    custom_fields JSONB, -- User-defined custom fields
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_files_content_hash ON public.files(content_hash);
CREATE INDEX IF NOT EXISTS idx_files_file_name ON public.files(file_name);
CREATE INDEX IF NOT EXISTS idx_files_original_name ON public.files(original_name);
CREATE INDEX IF NOT EXISTS idx_files_mime_type ON public.files(mime_type);
CREATE INDEX IF NOT EXISTS idx_files_uploaded_at ON public.files(uploaded_at);
CREATE INDEX IF NOT EXISTS idx_files_uploaded_by ON public.files(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_files_access_level ON public.files(access_level);
CREATE INDEX IF NOT EXISTS idx_files_tags ON public.files USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_files_metadata ON public.files USING GIN(metadata);
CREATE INDEX IF NOT EXISTS idx_files_custom_fields ON public.files USING GIN(custom_fields);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_files_uploaded_at_desc ON public.files(uploaded_at DESC);
CREATE INDEX IF NOT EXISTS idx_files_type_size ON public.files(mime_type, file_size_bytes);

-- Create trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_files_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_files_updated_at 
    BEFORE UPDATE ON public.files 
    FOR EACH ROW 
    EXECUTE FUNCTION update_files_updated_at();

-- Create table for flexible file relationships
-- This allows files to be linked to any table with any column structure
CREATE TABLE IF NOT EXISTS public.file_relationships (
    relationship_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- File reference
    file_id UUID NOT NULL REFERENCES public.files(file_id) ON DELETE CASCADE,
    
    -- Flexible entity reference
    target_table_name TEXT NOT NULL, -- Name of the target table (e.g., 'deals', 'contacts', 'companies')
    target_column_name TEXT NOT NULL, -- Name of the ID column in target table (e.g., 'deal_id', 'contact_id', 'id')
    target_row_id TEXT NOT NULL, -- The actual row ID value (as text for flexibility)
    
    -- Relationship metadata
    relationship_type TEXT DEFAULT 'attachment', -- attachment, primary, secondary, etc.
    relationship_title TEXT, -- Custom title for this relationship
    relationship_notes TEXT,
    
    -- Ordering and display
    display_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT false, -- Primary file for this entity
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure unique relationships per file per target row
    UNIQUE(file_id, target_table_name, target_column_name, target_row_id)
);

-- Create indexes for file relationships
CREATE INDEX IF NOT EXISTS idx_file_relationships_file_id ON public.file_relationships(file_id);
CREATE INDEX IF NOT EXISTS idx_file_relationships_target ON public.file_relationships(target_table_name, target_column_name, target_row_id);
CREATE INDEX IF NOT EXISTS idx_file_relationships_type ON public.file_relationships(relationship_type);
CREATE INDEX IF NOT EXISTS idx_file_relationships_primary ON public.file_relationships(is_primary);

-- Create trigger to update the updated_at timestamp for relationships
CREATE TRIGGER update_file_relationships_updated_at 
    BEFORE UPDATE ON public.file_relationships 
    FOR EACH ROW 
    EXECUTE FUNCTION update_files_updated_at();

-- Add comments for documentation
COMMENT ON TABLE public.files IS 'Generic file management table with content hash deduplication';
COMMENT ON COLUMN public.files.file_id IS 'UUID primary key for security';
COMMENT ON COLUMN public.files.content_hash IS 'SHA-256 hash of file content for deduplication';
COMMENT ON COLUMN public.files.title IS 'User-provided title for the file';
COMMENT ON COLUMN public.files.original_name IS 'Original filename as uploaded';
COMMENT ON COLUMN public.files.access_level IS 'Access level: private, team, public';

COMMENT ON TABLE public.file_relationships IS 'Flexible relationships between files and any table/column';
COMMENT ON COLUMN public.file_relationships.relationship_id IS 'UUID primary key for security';
COMMENT ON COLUMN public.file_relationships.target_table_name IS 'Name of the target table (e.g., deals, contacts)';
COMMENT ON COLUMN public.file_relationships.target_column_name IS 'Name of the ID column in target table (e.g., deal_id, contact_id)';
COMMENT ON COLUMN public.file_relationships.target_row_id IS 'The actual row ID value as text';
COMMENT ON COLUMN public.file_relationships.relationship_type IS 'Type of relationship: attachment, primary, secondary, etc.'; 