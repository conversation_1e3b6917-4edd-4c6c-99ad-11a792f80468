-- Convert contacts capital_position from TEXT to TEXT[] array
-- Based on data analysis: values are separated by "/" 

-- STEP 1: Contacts table migration
-- Add temporary array column to contacts table
ALTER TABLE contacts 
ADD COLUMN capital_position_array TEXT[] DEFAULT NULL;

-- Migrate existing data to array format (split by "/")
UPDATE contacts 
SET capital_position_array = CASE 
    WHEN capital_position IS NOT NULL 
         AND capital_position != '' 
         AND capital_position != 'Unknown'
    THEN string_to_array(trim(capital_position), '/')
    ELSE NULL
END
WHERE capital_position IS NOT NULL;

-- Drop the old index first
DROP INDEX IF EXISTS idx_contacts_capital_position;

-- Drop the old column and rename the new one
ALTER TABLE contacts 
DROP COLUMN capital_position;

ALTER TABLE contacts 
RENAME COLUMN capital_position_array TO capital_position;

-- Create GIN index for array operations (better for array queries)
CREATE INDEX idx_contacts_capital_position_gin 
ON contacts USING GIN (capital_position);

-- STEP 2: Contacts_dummy table migration  
-- Add temporary array column to contacts_dummy table
ALTER TABLE contacts_dummy 
ADD COLUMN capital_position_array TEXT[] DEFAULT NULL;

-- Migrate existing data to array format (split by "/")
UPDATE contacts_dummy 
SET capital_position_array = CASE 
    WHEN capital_position IS NOT NULL 
         AND capital_position != '' 
         AND capital_position != 'Unknown'
    THEN string_to_array(trim(capital_position), '/')
    ELSE NULL
END
WHERE capital_position IS NOT NULL;

-- Drop the old index first
DROP INDEX IF EXISTS contacts_dummy_capital_position_idx;

-- Drop the old column and rename the new one
ALTER TABLE contacts_dummy 
DROP COLUMN capital_position;

ALTER TABLE contacts_dummy 
RENAME COLUMN capital_position_array TO capital_position;

-- Create GIN index for array operations
CREATE INDEX contacts_dummy_capital_position_gin_idx 
ON contacts_dummy USING GIN (capital_position);

-- STEP 3: Verify the migration worked
-- Show sample data after migration
SELECT 
    contact_id,
    capital_position,
    array_length(capital_position, 1) as position_count
FROM contacts_dummy 
WHERE capital_position IS NOT NULL 
LIMIT 10;

-- Show statistics
SELECT 
    'contacts' as table_name,
    COUNT(*) as total_records,
    COUNT(capital_position) as records_with_positions,
    AVG(array_length(capital_position, 1)) as avg_positions_per_contact
FROM contacts
UNION ALL
SELECT 
    'contacts_dummy' as table_name,
    COUNT(*) as total_records,
    COUNT(capital_position) as records_with_positions,
    AVG(array_length(capital_position, 1)) as avg_positions_per_contact
FROM contacts_dummy; 