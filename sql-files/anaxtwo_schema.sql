CREATE TABLE public.deal_data_pivoted (
    deal_id TEXT,
    sponsor TEXT,
    geo_region TEXT,
    loan_term TEXT,
    sofr_plus TEXT,
    recourse TEXT,
    attach_point TEXT,
    diagrams_loc TEXT,
    diagrams_city TEXT,
    diagrams_state TEXT,
    diagrams_prop_type TEXT,
    diagrams_asset_type TEXT,
    diagrams_loan_type TEXT,
    proj_budget_gross_sf TEXT,
    proj_budget_lot_size TEXT,
    proj_budget_num_res_units TEXT,
    proj_budget_net_res_sf TEXT,
    proj_budget_net_com_sf TEXT,
    proj_budget_net_total_sf TEXT,
    proj_budget_src_total_dev TEXT,
    proj_budget_src_total TEXT,
    proj_budget_src_total_nsf TEXT,
    proj_budget_src_debt_senior_total_dev TEXT,
    proj_budget_src_debt_senior TEXT,
    proj_budget_src_debt_senior_nsf TEXT,
    proj_budget_src_debt_mezz_total_dev TEXT,
    proj_budget_src_debt_mezz TEXT,
    proj_budget_src_debt_mezz_nsf TEXT,
    proj_budget_src_eq_gp_total_dev TEXT,
    proj_budget_src_eq_gp TEXT,
    proj_budget_src_eq_gp_nsf TEXT,
    proj_budget_src_eq_lp_total_dev TEXT,
    proj_budget_src_eq_lp TEXT,
    proj_budget_src_eq_lp_nsf TEXT,
    proj_budget_src_eq_pref_total_dev TEXT,
    proj_budget_src_eq_pref TEXT,
    proj_budget_src_eq_pref_nsf TEXT,
    proj_budget_src_debt_senior_loan_total_acq TEXT,
    proj_budget_src_eq_gp_total_acq TEXT,
    proj_budget_src_eq_lp_total_acq TEXT,
    proj_budget_src_eq_pref_total_acq TEXT,
    proj_budget_uses_acq_costs_gsf TEXT,
    proj_budget_uses_acq_costs_nsf TEXT,
    proj_budget_uses_acq_costs TEXT,
    proj_budget_uses_acq_total_dev TEXT,
    proj_budget_uses_acq_total_acq TEXT,
    proj_budget_uses_hardcosts_gsf TEXT,
    proj_budget_uses_hardcosts_total_dev TEXT,
    proj_budget_uses_hardcosts_total_acq TEXT,
    proj_budget_uses_softcosts_gsf TEXT,
    proj_budget_uses_softcosts_nsf TEXT,
    proj_budget_uses_softcosts TEXT,
    proj_budget_uses_softcosts_total_dev TEXT,
    proj_budget_uses_softcosts_total_acq TEXT,
    proj_budget_uses_fincosts_gsf TEXT,
    proj_budget_uses_fincosts_nsf TEXT,
    proj_budget_uses_fincosts TEXT,
    proj_budget_uses_fincosts_total_dev TEXT,
    proj_budget_uses_fincosts_total_acq TEXT,
    proj_budget_uses_total_gsf TEXT,
    proj_budget_uses_total_nsf TEXT,
    proj_budget_uses_total TEXT,
    proj_budget_uses_total_dev TEXT,
    proj_budget_uses_total_acq TEXT,
    proj_budget_profit_gp_eq_mult TEXT,
    proj_budget_profit_gp_irr TEXT,
    proj_budget_profit_lp_eq_mult TEXT,
    proj_budget_profit_lp_irr TEXT,
    proj_budget_profit_total_eq_mult TEXT,
    proj_budget_profit_total_irr TEXT,
    unit_type TEXT,
    unit_type_avg_sf TEXT,
    unit_type_avg_rent_psf TEXT,
    unit_type_avg_rent TEXT,
    noi_yr1 TEXT,
    noi_yr2 TEXT,
    noi_yr3 TEXT,
    noi_yr4 TEXT,
    noi_yr5 TEXT,
    noi_yr6 TEXT,
    yield_on_cost_yr1 TEXT,
    terminal_cap_rate TEXT,
    ltv TEXT,
    debt_yield_yr1 TEXT,
    value_psf TEXT,
    debt_psf TEXT,
    sale_year_inc_constr TEXT,
    debt_yield_last_dollar_yr1 TEXT,
    debt_yield_last_dollar_yr2 TEXT,
    debt_yield_last_dollar_yr3 TEXT,
    debt_yield_last_dollar_yr4 TEXT,
    debt_yield_last_dollar_yr5 TEXT,
    debt_yield_last_dollar_yr6 TEXT,
    debt_yield_last_dollar_yr7 TEXT,
    dscr_yr1 TEXT,
    dscr_yr2 TEXT,
    dscr_yr3 TEXT,
    dscr_yr4 TEXT,
    dscr_yr5 TEXT,
    dscr_yr6 TEXT,
    dscr_yr7 TEXT,
    coc_yr1 TEXT,
    coc_yr2 TEXT,
    coc_yr3 TEXT,
    coc_yr4 TEXT,
    coc_yr5 TEXT,
    coc_yr6 TEXT,
    coc_yr7 TEXT,
    gp_eq_mult TEXT,
    gp_irr TEXT,
    lp_eq_mult TEXT,
    lp_irr TEXT,
    total_eq_mult TEXT,
    senior_debt_loan_amt TEXT,
    senior_debt_int_rate TEXT,
    senior_debt_loan_term TEXT,
    senior_debt_amort TEXT,
    senior_debt_io TEXT,
    senior_debt_bal_maturity TEXT,
    market_res_rate_psf TEXT,
    aff_market_res_psf TEXT,
    comm_facility_rent_psf TEXT,
    parking_rent_psf TEXT,
    -- New extra fields for enhanced metadata and processing
    document_type TEXT,
    extraction_confidence TEXT,
    processing_notes TEXT,
    missing_critical_fields TEXT,
    data_quality_issues TEXT,
    extraction_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processor_version TEXT,
    llm_model_used TEXT,
    llm_provider TEXT,
    extraction_method TEXT,
    document_source TEXT,
    document_filename TEXT,
    document_size_bytes INTEGER,
    processing_duration_ms INTEGER,
    confidence_scores JSONB,
    field_extraction_metadata JSONB,
    validation_results JSONB,
    quality_metrics JSONB,
    custom_fields JSONB,
    tags TEXT[],
    status TEXT DEFAULT 'processed',
    review_status TEXT DEFAULT 'pending',
    reviewed_by TEXT,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    review_notes TEXT,
    extra_fields JSONB
);
