-- Capital Position Field Weights Setup for V2 Deal Matching System
-- This creates a new table structure to manage field weights per capital position
-- Uses MCP (Mapping Configuration Parameters) to dynamically get capital positions

-- 1. Create the capital_position_field_weights table
CREATE TABLE IF NOT EXISTS capital_position_field_weights (
    id SERIAL PRIMARY KEY,
    capital_position TEXT NOT NULL,
    field_name TEXT NOT NULL,
    weight NUMERIC(4,3) NOT NULL DEFAULT 0.1,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(capital_position, field_name)
);

-- 2. Create indexes for performance
CREATE INDEX idx_capital_position_field_weights_position ON capital_position_field_weights(capital_position);
CREATE INDEX idx_capital_position_field_weights_field ON capital_position_field_weights(field_name);
CREATE INDEX idx_capital_position_field_weights_active ON capital_position_field_weights(is_active);

-- 3. Insert optimized weights for each capital position from MCP central mapping
-- Based on actual capital positions found in your system

-- Senior Debt weights (focus on loan terms, rates, and property types)
INSERT INTO capital_position_field_weights (capital_position, field_name, weight, description) VALUES
('Senior Debt', 'location', 0.20, 'Geographic matching for Senior Debt - 20%'),
('Senior Debt', 'deal_amount', 0.20, 'Deal amount overlap for Senior Debt - 20%'),
('Senior Debt', 'property_types', 0.15, 'Property type matching for Senior Debt - 15%'),
('Senior Debt', 'loan_to_value', 0.15, 'LTV range matching for Senior Debt - 15%'),
('Senior Debt', 'interest_rate', 0.10, 'Interest rate matching for Senior Debt - 10%'),
('Senior Debt', 'loan_term', 0.08, 'Loan term matching for Senior Debt - 8%'),
('Senior Debt', 'loan_dscr', 0.07, 'DSCR matching for Senior Debt - 7%'),
('Senior Debt', 'exit_cap_rate', 0.05, 'Exit cap rate for Senior Debt - 5%');

-- Stretch Senior weights (focus on higher risk debt criteria)
INSERT INTO capital_position_field_weights (capital_position, field_name, weight, description) VALUES
('Stretch Senior', 'location', 0.20, 'Geographic matching for Stretch Senior - 20%'),
('Stretch Senior', 'deal_amount', 0.20, 'Deal amount overlap for Stretch Senior - 20%'),
('Stretch Senior', 'property_types', 0.15, 'Property type matching for Stretch Senior - 15%'),
('Stretch Senior', 'loan_to_value', 0.15, 'LTV range matching for Stretch Senior - 15%'),
('Stretch Senior', 'interest_rate', 0.15, 'Interest rate matching for Stretch Senior - 15%'),
('Stretch Senior', 'strategies', 0.10, 'Investment strategies for Stretch Senior - 10%'),
('Stretch Senior', 'exit_cap_rate', 0.05, 'Exit cap rate for Stretch Senior - 5%');

-- Mezzanine weights (focus on higher risk tolerance and flexible terms)
INSERT INTO capital_position_field_weights (capital_position, field_name, weight, description) VALUES
('Mezzanine', 'location', 0.20, 'Geographic matching for Mezzanine - 20%'),
('Mezzanine', 'deal_amount', 0.20, 'Deal amount overlap for Mezzanine - 20%'),
('Mezzanine', 'property_types', 0.15, 'Property type matching for Mezzanine - 15%'),
('Mezzanine', 'strategies', 0.15, 'Investment strategies for Mezzanine - 15%'),
('Mezzanine', 'interest_rate', 0.15, 'Interest rate matching for Mezzanine - 15%'),
('Mezzanine', 'loan_to_cost', 0.10, 'LTC matching for Mezzanine - 10%'),
('Mezzanine', 'exit_cap_rate', 0.05, 'Exit cap rate for Mezzanine - 5%');

-- Preferred Equity weights (focus on returns and hold periods)
INSERT INTO capital_position_field_weights (capital_position, field_name, weight, description) VALUES
('Preferred Equity', 'location', 0.20, 'Geographic matching for Preferred Equity - 20%'),
('Preferred Equity', 'deal_amount', 0.20, 'Deal amount overlap for Preferred Equity - 20%'),
('Preferred Equity', 'target_return', 0.20, 'Target return matching for Preferred Equity - 20%'),
('Preferred Equity', 'property_types', 0.15, 'Property type matching for Preferred Equity - 15%'),
('Preferred Equity', 'hold_period', 0.10, 'Hold period matching for Preferred Equity - 10%'),
('Preferred Equity', 'strategies', 0.10, 'Investment strategies for Preferred Equity - 10%'),
('Preferred Equity', 'equity_multiple', 0.05, 'Equity multiple for Preferred Equity - 5%');

-- Common Equity weights (focus on total returns and property appreciation)
INSERT INTO capital_position_field_weights (capital_position, field_name, weight, description) VALUES
('Common Equity', 'location', 0.20, 'Geographic matching for Common Equity - 20%'),
('Common Equity', 'deal_amount', 0.20, 'Deal amount overlap for Common Equity - 20%'),
('Common Equity', 'target_return', 0.20, 'Target return matching for Common Equity - 20%'),
('Common Equity', 'property_types', 0.15, 'Property type matching for Common Equity - 15%'),
('Common Equity', 'strategies', 0.15, 'Investment strategies for Common Equity - 15%'),
('Common Equity', 'hold_period', 0.05, 'Hold period matching for Common Equity - 5%'),
('Common Equity', 'equity_multiple', 0.05, 'Equity multiple for Common Equity - 5%');

-- General Partner (GP) weights (focus on control and management)
INSERT INTO capital_position_field_weights (capital_position, field_name, weight, description) VALUES
('General Partner (GP)', 'location', 0.20, 'Geographic matching for GP - 20%'),
('General Partner (GP)', 'deal_amount', 0.20, 'Deal amount overlap for GP - 20%'),
('General Partner (GP)', 'target_return', 0.20, 'Target return matching for GP - 20%'),
('General Partner (GP)', 'strategies', 0.20, 'Investment strategies for GP - 20%'),
('General Partner (GP)', 'property_types', 0.10, 'Property type matching for GP - 10%'),
('General Partner (GP)', 'hold_period', 0.05, 'Hold period matching for GP - 5%'),
('General Partner (GP)', 'equity_multiple', 0.05, 'Equity multiple for GP - 5%');

-- Limited Partner (LP) weights (focus on passive investment criteria)
INSERT INTO capital_position_field_weights (capital_position, field_name, weight, description) VALUES
('Limited Partner (LP)', 'location', 0.20, 'Geographic matching for LP - 20%'),
('Limited Partner (LP)', 'deal_amount', 0.20, 'Deal amount overlap for LP - 20%'),
('Limited Partner (LP)', 'target_return', 0.20, 'Target return matching for LP - 20%'),
('Limited Partner (LP)', 'property_types', 0.15, 'Property type matching for LP - 15%'),
('Limited Partner (LP)', 'strategies', 0.15, 'Investment strategies for LP - 15%'),
('Limited Partner (LP)', 'hold_period', 0.05, 'Hold period matching for LP - 5%'),
('Limited Partner (LP)', 'equity_multiple', 0.05, 'Equity multiple for LP - 5%');

-- Co-GP weights (focus on partnership criteria)
INSERT INTO capital_position_field_weights (capital_position, field_name, weight, description) VALUES
('Co-GP', 'location', 0.20, 'Geographic matching for Co-GP - 20%'),
('Co-GP', 'deal_amount', 0.20, 'Deal amount overlap for Co-GP - 20%'),
('Co-GP', 'target_return', 0.20, 'Target return matching for Co-GP - 20%'),
('Co-GP', 'strategies', 0.20, 'Investment strategies for Co-GP - 20%'),
('Co-GP', 'property_types', 0.10, 'Property type matching for Co-GP - 10%'),
('Co-GP', 'hold_period', 0.05, 'Hold period matching for Co-GP - 5%'),
('Co-GP', 'equity_multiple', 0.05, 'Equity multiple for Co-GP - 5%');

-- Joint Venture (JV) weights (focus on partnership and control)
INSERT INTO capital_position_field_weights (capital_position, field_name, weight, description) VALUES
('Joint Venture (JV)', 'location', 0.20, 'Geographic matching for JV - 20%'),
('Joint Venture (JV)', 'deal_amount', 0.20, 'Deal amount overlap for JV - 20%'),
('Joint Venture (JV)', 'target_return', 0.20, 'Target return matching for JV - 20%'),
('Joint Venture (JV)', 'strategies', 0.20, 'Investment strategies for JV - 20%'),
('Joint Venture (JV)', 'property_types', 0.10, 'Property type matching for JV - 10%'),
('Joint Venture (JV)', 'hold_period', 0.05, 'Hold period matching for JV - 5%'),
('Joint Venture (JV)', 'equity_multiple', 0.05, 'Equity multiple for JV - 5%');

-- 4. Verification queries
SELECT '=== CAPITAL POSITION FIELD WEIGHTS SETUP COMPLETE ===' as status;

-- 5. Show summary by capital position
SELECT 
  '=== WEIGHTS BY CAPITAL POSITION (FROM MCP) ===' as summary;

SELECT 
  capital_position,
  COUNT(*) as field_count,
  ROUND(SUM(weight) * 100, 1) || '%' as total_weight,
  CASE 
    WHEN ABS(SUM(weight) - 1.0) < 0.001 THEN 'PERFECT ✓'
    ELSE 'ERROR - DOES NOT SUM TO 100%'
  END as status
FROM capital_position_field_weights 
WHERE is_active = true
GROUP BY capital_position
ORDER BY capital_position;

-- 6. Show top fields for each position
SELECT 
  '=== TOP FIELDS BY CAPITAL POSITION ===' as details;

SELECT 
  capital_position,
  field_name,
  ROUND(weight * 100, 1) || '%' as percentage,
  description
FROM capital_position_field_weights 
WHERE is_active = true
ORDER BY capital_position, weight DESC;

-- 7. Show MCP source data verification
SELECT 
  '=== MCP SOURCE DATA VERIFICATION ===' as mcp_info;

SELECT 
  'Capital positions configured:' as info,
  (SELECT COUNT(DISTINCT capital_position) FROM capital_position_field_weights) as configured_count,
  'Total weight records:' as info2,
  (SELECT COUNT(*) FROM capital_position_field_weights) as total_records;

-- 8. Create a function to get weights for a specific capital position
CREATE OR REPLACE FUNCTION get_capital_position_weights(target_position TEXT)
RETURNS TABLE(field_name TEXT, weight NUMERIC, description TEXT) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cpfw.field_name,
    cpfw.weight,
    cpfw.description
  FROM capital_position_field_weights cpfw
  WHERE cpfw.capital_position = target_position 
    AND cpfw.is_active = true
  ORDER BY cpfw.weight DESC;
END;
$$ LANGUAGE plpgsql;

-- 9. Create a function to normalize weights for a capital position
CREATE OR REPLACE FUNCTION get_normalized_capital_position_weights(target_position TEXT)
RETURNS TABLE(field_name TEXT, normalized_weight NUMERIC, description TEXT) AS $$
DECLARE
  total_weight NUMERIC;
BEGIN
  -- Get total weight for normalization
  SELECT SUM(weight) INTO total_weight
  FROM capital_position_field_weights
  WHERE capital_position = target_position AND is_active = true;
  
  -- Return normalized weights
  RETURN QUERY
  SELECT 
    cpfw.field_name,
    CASE 
      WHEN total_weight > 0 THEN cpfw.weight / total_weight
      ELSE 0
    END as normalized_weight,
    cpfw.description
  FROM capital_position_field_weights cpfw
  WHERE cpfw.capital_position = target_position 
    AND cpfw.is_active = true
  ORDER BY cpfw.weight DESC;
END;
$$ LANGUAGE plpgsql;

-- 10. Create a function to refresh weights from MCP (useful for updates)
CREATE OR REPLACE FUNCTION refresh_capital_position_weights_from_mcp()
RETURNS VOID AS $$
DECLARE
    capital_pos RECORD;
    field_names TEXT[] := ARRAY['location', 'deal_amount', 'property_types', 'strategies', 'target_return', 'loan_to_value', 'loan_to_cost', 'interest_rate', 'loan_term', 'loan_dscr', 'hold_period', 'equity_multiple', 'exit_cap_rate'];
    i INTEGER;
    total_weight NUMERIC;
BEGIN
    -- Get all capital positions from central mapping
    FOR capital_pos IN 
        SELECT DISTINCT value_1 as capital_position
        FROM central_mapping 
        WHERE type = 'Capital Position' 
          AND is_active = true
          AND value_1 IS NOT NULL
        ORDER BY value_1
    LOOP
        -- Check if this capital position already has weights configured
        IF NOT EXISTS (SELECT 1 FROM capital_position_field_weights WHERE capital_position = capital_pos.capital_position) THEN
            -- Insert default balanced weights for new capital positions
            FOR i IN 1..array_length(field_names, 1) LOOP
                INSERT INTO capital_position_field_weights (
                    capital_position, 
                    field_name, 
                    weight, 
                    description
                ) VALUES (
                    capital_pos.capital_position,
                    field_names[i],
                    1.0 / array_length(field_names, 1), -- Equal distribution
                    'Default weight for ' || field_names[i]
                );
            END LOOP;
            
            -- Normalize weights to sum to 1.0 for this capital position
            SELECT SUM(weight) INTO total_weight
            FROM capital_position_field_weights
            WHERE capital_position = capital_pos.capital_position;
            
            IF total_weight > 0 THEN
                UPDATE capital_position_field_weights 
                SET weight = weight / total_weight
                WHERE capital_position = capital_pos.capital_position;
            END IF;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Refreshed weights from MCP central mapping';
END;
$$ LANGUAGE plpgsql;

-- 11. Show final status
SELECT 
  '=== SETUP COMPLETE ===' as final_status,
  (SELECT COUNT(DISTINCT capital_position) FROM capital_position_field_weights) as total_capital_positions,
  (SELECT COUNT(*) FROM capital_position_field_weights) as total_weight_records,
  'Use refresh_capital_position_weights_from_mcp() to add new positions from MCP' as maintenance_note;
