-- Migration to add file processing metadata
-- This adds columns to track file processing status

-- Add processing status columns to files table if they don't exist
DO $$ 
BEGIN
    -- Add processing_status column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'files' 
        AND column_name = 'processing_status'
    ) THEN
        ALTER TABLE public.files ADD COLUMN processing_status VARCHAR(50) DEFAULT 'not_processed';
    END IF;

    -- Add processing_job_id column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'files' 
        AND column_name = 'processing_job_id'
    ) THEN
        ALTER TABLE public.files ADD COLUMN processing_job_id VARCHAR(255);
    END IF;

    -- Add processing_timestamp column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'files' 
        AND column_name = 'processing_timestamp'
    ) THEN
        ALTER TABLE public.files ADD COLUMN processing_timestamp TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add processing_completed_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'files' 
        AND column_name = 'processing_completed_at'
    ) THEN
        ALTER TABLE public.files ADD COLUMN processing_completed_at TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add processing_error column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'files' 
        AND column_name = 'processing_error'
    ) THEN
        ALTER TABLE public.files ADD COLUMN processing_error TEXT;
    END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_files_processing_status ON public.files(processing_status);
CREATE INDEX IF NOT EXISTS idx_files_processing_job_id ON public.files(processing_job_id);
CREATE INDEX IF NOT EXISTS idx_files_processing_timestamp ON public.files(processing_timestamp);

-- Add comments for documentation
COMMENT ON COLUMN public.files.processing_status IS 'Status of file processing: not_processed, processing, completed, failed';
COMMENT ON COLUMN public.files.processing_job_id IS 'ID of the job that processed this file';
COMMENT ON COLUMN public.files.processing_timestamp IS 'When processing started';
COMMENT ON COLUMN public.files.processing_completed_at IS 'When processing completed';
COMMENT ON COLUMN public.files.processing_error IS 'Error message if processing failed'; 