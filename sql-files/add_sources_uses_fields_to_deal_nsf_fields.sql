-- Migration: Add Sources/Uses fields to deal_nsf_fields table
-- Date: 2025-01-18
-- Description: Adding new source_type and use_type columns for the Sources/Uses structure
-- Keeping existing deal_type and nsf_context fields for backward compatibility

-- Add new source_type column
ALTER TABLE deal_nsf_fields 
ADD COLUMN source_type TEXT,
ADD CONSTRAINT check_source_type 
CHECK (source_type IN ('Senior Debt', 'Mezzanine', 'General Partner (GP)', 'Limited Partner (LP)', 'Preferred Equity') OR source_type IS NULL);

-- Add new use_type column  
ALTER TABLE deal_nsf_fields 
ADD COLUMN use_type TEXT,
ADD CONSTRAINT check_use_type 
CHECK (use_type IN ('Acquisition', 'Hard Cost', 'Soft Cost', 'Financing Cost') OR use_type IS NULL);

-- Add comments to the new columns
COMMENT ON COLUMN deal_nsf_fields.source_type IS 'NEW: Sources dropdown - Senior De<PERSON>, Mezzanine, General Partner (GP), Limited Partner (LP), Preferred Equity';
COMMENT ON COLUMN deal_nsf_fields.use_type IS 'NEW: Uses dropdown - Acquisition, Hard Cost, Soft Cost, Financing Cost';

-- Create indexes for better query performance
CREATE INDEX idx_deal_nsf_fields_source_type ON deal_nsf_fields(source_type);
CREATE INDEX idx_deal_nsf_fields_use_type ON deal_nsf_fields(use_type);

-- Add a constraint to ensure either source_type or use_type is set (but not both)
ALTER TABLE deal_nsf_fields 
ADD CONSTRAINT check_sources_uses_exclusive 
CHECK (
  (source_type IS NOT NULL AND use_type IS NULL) OR 
  (source_type IS NULL AND use_type IS NOT NULL) OR
  (source_type IS NULL AND use_type IS NULL) -- Allow both null for backward compatibility
);
