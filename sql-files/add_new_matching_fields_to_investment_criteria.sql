-- Add new matching fields to investment_criteria table for V2 deal matching system
-- This allows contacts to specify preferences for the new fields available in dealsv2

-- 1. Add new columns to investment_criteria table
ALTER TABLE investment_criteria 
ADD COLUMN IF NOT EXISTS deal_stages TEXT[],
ADD COLUMN IF NOT EXISTS property_size_min NUMERIC,
ADD COLUMN IF NOT EXISTS property_size_max NUMERIC,
ADD COLUMN IF NOT EXISTS exit_cap_rate_min NUMERIC,
ADD COLUMN IF NOT EXISTS exit_cap_rate_max NUMERIC,
ADD COLUMN IF NOT EXISTS equity_multiple_min NUMERIC,
ADD COLUMN IF NOT EXISTS equity_multiple_max NUMERIC;

-- 2. Add comments for the new columns
COMMENT ON COLUMN investment_criteria.deal_stages IS 'Preferred deal stages (e.g., ["Under Contract", "Due Diligence", "Closing"])';
COMMENT ON COLUMN investment_criteria.property_size_min IS 'Minimum property size preference in thousands of square feet';
COMMENT ON COLUMN investment_criteria.property_size_max IS 'Maximum property size preference in thousands of square feet';
COMMENT ON COLUMN investment_criteria.exit_cap_rate_min IS 'Minimum exit cap rate preference (as decimal)';
COMMENT ON COLUMN investment_criteria.exit_cap_rate_max IS 'Maximum exit cap rate preference (as decimal)';
COMMENT ON COLUMN investment_criteria.equity_multiple_min IS 'Minimum equity multiple preference';
COMMENT ON COLUMN investment_criteria.equity_multiple_max IS 'Maximum equity multiple preference';

-- 3. Create indexes for the new columns for better query performance
CREATE INDEX IF NOT EXISTS idx_investment_criteria_deal_stages ON investment_criteria USING GIN (deal_stages);
CREATE INDEX IF NOT EXISTS idx_investment_criteria_property_size ON investment_criteria (property_size_min, property_size_max);
CREATE INDEX IF NOT EXISTS idx_investment_criteria_exit_cap_rate ON investment_criteria (exit_cap_rate_min, exit_cap_rate_max);
CREATE INDEX IF NOT EXISTS idx_investment_criteria_equity_multiple ON investment_criteria (equity_multiple_min, equity_multiple_max);

-- 4. Update existing investment criteria with sample data for testing
-- Note: This is optional and can be customized based on your data

-- Example: Update some existing criteria with deal stage preferences
UPDATE investment_criteria 
SET deal_stages = ARRAY['Under Contract', 'Due Diligence', 'Closing']
WHERE capital_position && ARRAY['Common Equity', 'Preferred Equity'] 
  AND deal_stages IS NULL
LIMIT 10;

-- Example: Update some existing criteria with property size preferences
UPDATE investment_criteria 
SET property_size_min = 50, -- 50K SF
    property_size_max = 500  -- 500K SF
WHERE capital_position && ARRAY['Senior Debt', 'Mezzanine'] 
  AND property_size_min IS NULL
LIMIT 10;

-- Example: Update some existing criteria with exit cap rate preferences
UPDATE investment_criteria 
SET exit_cap_rate_min = 0.05, -- 5%
    exit_cap_rate_max = 0.08  -- 8%
WHERE capital_position && ARRAY['Common Equity', 'Preferred Equity'] 
  AND exit_cap_rate_min IS NULL
LIMIT 10;

-- Example: Update some existing criteria with equity multiple preferences
UPDATE investment_criteria 
SET equity_multiple_min = 1.5, -- 1.5x
    equity_multiple_max = 3.0  -- 3.0x
WHERE capital_position && ARRAY['Common Equity', 'General Partner (GP)'] 
  AND equity_multiple_min IS NULL
LIMIT 10;

-- 5. Create a function to get all matching fields for a contact
CREATE OR REPLACE FUNCTION get_contact_matching_fields(contact_id_param INTEGER)
RETURNS TABLE(
  criteria_id INTEGER,
  capital_position TEXT[],
  minimum_deal_size NUMERIC,
  maximum_deal_size NUMERIC,
  property_types TEXT[],
  strategies TEXT[],
  target_return NUMERIC,
  region TEXT[],
  state TEXT[],
  city TEXT[],
  loan_to_value_min NUMERIC,
  loan_to_value_max NUMERIC,
  loan_to_cost_min NUMERIC,
  loan_to_cost_max NUMERIC,
  min_loan_term NUMERIC,
  max_loan_term NUMERIC,
  min_loan_dscr NUMERIC,
  max_loan_dscr NUMERIC,
  min_hold_period NUMERIC,
  max_hold_period NUMERIC,
  interest_rate NUMERIC,
  deal_stages TEXT[],
  property_size_min NUMERIC,
  property_size_max NUMERIC,
  exit_cap_rate_min NUMERIC,
  exit_cap_rate_max NUMERIC,
  equity_multiple_min NUMERIC,
  equity_multiple_max NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ic.criteria_id,
    ic.capital_position,
    ic.minimum_deal_size,
    ic.maximum_deal_size,
    ic.property_types,
    ic.strategies,
    ic.target_return,
    ic.region,
    ic.state,
    ic.city,
    ic.loan_to_value_min,
    ic.loan_to_value_max,
    ic.loan_to_cost_min,
    ic.loan_to_cost_max,
    ic.min_loan_term,
    ic.max_loan_term,
    ic.min_loan_dscr,
    ic.max_loan_dscr,
    ic.min_hold_period,
    ic.max_hold_period,
    ic.interest_rate,
    ic.deal_stages,
    ic.property_size_min,
    ic.property_size_max,
    ic.exit_cap_rate_min,
    ic.exit_cap_rate_max,
    ic.equity_multiple_min,
    ic.equity_multiple_max
  FROM investment_criteria ic
  WHERE ic.entity_id = contact_id_param::TEXT 
    AND ic.entity_type = 'Contact'
    AND ic.is_active = true;
END;
$$ LANGUAGE plpgsql;

-- 6. Create a function to get all matching fields for a company
CREATE OR REPLACE FUNCTION get_company_matching_fields(company_id_param INTEGER)
RETURNS TABLE(
  criteria_id INTEGER,
  capital_position TEXT[],
  minimum_deal_size NUMERIC,
  maximum_deal_size NUMERIC,
  property_types TEXT[],
  strategies TEXT[],
  target_return NUMERIC,
  region TEXT[],
  state TEXT[],
  city TEXT[],
  loan_to_value_min NUMERIC,
  loan_to_value_max NUMERIC,
  loan_to_cost_min NUMERIC,
  loan_to_cost_max NUMERIC,
  min_loan_term NUMERIC,
  max_loan_term NUMERIC,
  min_loan_dscr NUMERIC,
  max_loan_dscr NUMERIC,
  min_hold_period NUMERIC,
  max_hold_period NUMERIC,
  interest_rate NUMERIC,
  deal_stages TEXT[],
  property_size_min NUMERIC,
  property_size_max NUMERIC,
  exit_cap_rate_min NUMERIC,
  exit_cap_rate_max NUMERIC,
  equity_multiple_min NUMERIC,
  equity_multiple_max NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ic.criteria_id,
    ic.capital_position,
    ic.minimum_deal_size,
    ic.maximum_deal_size,
    ic.property_types,
    ic.strategies,
    ic.target_return,
    ic.region,
    ic.state,
    ic.city,
    ic.loan_to_value_min,
    ic.loan_to_value_max,
    ic.loan_to_cost_min,
    ic.loan_to_cost_max,
    ic.min_loan_term,
    ic.max_loan_term,
    ic.min_loan_dscr,
    ic.max_loan_dscr,
    ic.min_hold_period,
    ic.max_hold_period,
    ic.interest_rate,
    ic.deal_stages,
    ic.property_size_min,
    ic.property_size_max,
    ic.exit_cap_rate_min,
    ic.exit_cap_rate_max,
    ic.equity_multiple_min,
    ic.equity_multiple_max
  FROM investment_criteria ic
  WHERE ic.entity_id = company_id_param::TEXT 
    AND ic.entity_type LIKE 'Company%'
    AND ic.is_active = true;
END;
$$ LANGUAGE plpgsql;

-- 7. Verification queries
SELECT '=== NEW MATCHING FIELDS ADDED TO INVESTMENT_CRITERIA ===' as status;

-- Show the new columns
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'investment_criteria' 
  AND column_name IN ('deal_stages', 'property_size_min', 'property_size_max', 
                      'exit_cap_rate_min', 'exit_cap_rate_max', 
                      'equity_multiple_min', 'equity_multiple_max')
ORDER BY column_name;

-- Show sample data
SELECT 
  '=== SAMPLE DATA FOR NEW FIELDS ===' as summary;

SELECT 
  criteria_id,
  capital_position,
  deal_stages,
  property_size_min,
  property_size_max,
  exit_cap_rate_min,
  exit_cap_rate_max,
  equity_multiple_min,
  equity_multiple_max
FROM investment_criteria 
WHERE deal_stages IS NOT NULL 
   OR property_size_min IS NOT NULL 
   OR exit_cap_rate_min IS NOT NULL 
   OR equity_multiple_min IS NOT NULL
LIMIT 5;
