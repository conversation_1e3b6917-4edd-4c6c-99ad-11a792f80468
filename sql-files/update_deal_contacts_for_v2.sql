-- Update deal_contacts table to support V2 deals
-- Add new columns for V2 support

-- Add deal_version column to distinguish between V1 and V2 deals
ALTER TABLE deal_contacts ADD COLUMN IF NOT EXISTS deal_version VARCHAR(10) DEFAULT 'v1';

-- Add deal_v2_id column for V2 deal references
ALTER TABLE deal_contacts ADD COLUMN IF NOT EXISTS deal_v2_id INTEGER;

-- Create index on the new column for performance
CREATE INDEX IF NOT EXISTS IDX_deal_contacts_deal_v2_id ON deal_contacts (deal_v2_id);

-- Add foreign key constraint for V2 deals
ALTER TABLE deal_contacts 
ADD CONSTRAINT IF NOT EXISTS deal_contacts_deal_v2_id_fkey 
FOREIGN KEY (deal_v2_id) REFERENCES dealsv2(deal_id) ON DELETE CASCADE;

-- Update existing records to have deal_version = 'v1'
UPDATE deal_contacts SET deal_version = 'v1' WHERE deal_version IS NULL;
