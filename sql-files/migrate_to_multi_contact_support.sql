-- Migration: Add multi-contact support for deals
-- This script creates the deal_contacts junction table and migrates existing data

-- Step 1: Create deal_contacts junction table
CREATE TABLE IF NOT EXISTS public.deal_contacts (
    id SERIAL PRIMARY KEY,
    deal_id INTEGER NOT NULL REFERENCES public.deals(deal_id) ON DELETE CASCADE,
    contact_id INTEGER NOT NULL REFERENCES public.contacts(contact_id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(deal_id, contact_id)
);

-- Step 2: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_deal_contacts_deal_id ON public.deal_contacts(deal_id);
CREATE INDEX IF NOT EXISTS idx_deal_contacts_contact_id ON public.deal_contacts(contact_id);

-- Step 3: Add comments for documentation
COMMENT ON TABLE public.deal_contacts IS 'Junction table linking deals to multiple contacts';
COMMENT ON COLUMN public.deal_contacts.deal_id IS 'Reference to the deal';
COMMENT ON COLUMN public.deal_contacts.contact_id IS 'Reference to the contact';

-- Step 4: Create trigger function for updated_at (if it doesn't exist)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Step 5: Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_deal_contacts_updated_at ON public.deal_contacts;
CREATE TRIGGER update_deal_contacts_updated_at 
    BEFORE UPDATE ON public.deal_contacts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Step 6: Backfill existing contact_id data from deals table
-- This migrates all existing single contact relationships to the new junction table
INSERT INTO public.deal_contacts (deal_id, contact_id, created_at, updated_at)
SELECT 
    deal_id,
    contact_id,
    created_at,
    updated_at
FROM public.deals 
WHERE contact_id IS NOT NULL
ON CONFLICT (deal_id, contact_id) DO NOTHING;

-- Step 7: Log the migration results
DO $$
DECLARE
    migrated_count INTEGER;
    total_deals INTEGER;
    deals_with_contacts INTEGER;
BEGIN
    -- Count total deals
    SELECT COUNT(*) INTO total_deals FROM public.deals;
    
    -- Count deals with contacts
    SELECT COUNT(*) INTO deals_with_contacts FROM public.deals WHERE contact_id IS NOT NULL;
    
    -- Count migrated relationships
    SELECT COUNT(*) INTO migrated_count FROM public.deal_contacts;
    
    RAISE NOTICE 'Migration completed:';
    RAISE NOTICE '  - Total deals: %', total_deals;
    RAISE NOTICE '  - Deals with contacts: %', deals_with_contacts;
    RAISE NOTICE '  - Migrated relationships: %', migrated_count;
    
    IF migrated_count = deals_with_contacts THEN
        RAISE NOTICE '  ✅ Migration successful - all contact relationships migrated';
    ELSE
        RAISE NOTICE '  ⚠️  Migration warning - some relationships may not have been migrated';
    END IF;
END $$; 