-- Central Mapping Table Schema
-- This table stores hierarchical mapping data for various types of classifications

CREATE TABLE IF NOT EXISTS public.central_mapping (
    id SERIAL PRIMARY KEY,
    type TEXT NOT NULL,           -- e.g., Property Type, U.S_Regions, capital_position, etc.
    level_1 TEXT NOT NULL,        -- First level header (e.g., Property Type)
    value_1 TEXT NOT NULL,        -- First level value (e.g., Multi-Family)
    level_2 TEXT DEFAULT NULL,    -- Second level header (e.g., Subproperty Type)
    value_2 TEXT DEFAULT NULL,    -- Second level value (e.g., Affordable Housing)
    level_3 TEXT DEFAULT NULL,    -- Third level header (e.g., Level 3)
    value_3 TEXT DEFAULT NULL,    -- Third level value (e.g., Specific subcategory)
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_central_mapping_type ON public.central_mapping(type);
CREATE INDEX IF NOT EXISTS idx_central_mapping_level_1 ON public.central_mapping(level_1);
CREATE INDEX IF NOT EXISTS idx_central_mapping_value_1 ON public.central_mapping(value_1);
CREATE INDEX IF NOT EXISTS idx_central_mapping_level_2 ON public.central_mapping(level_2);
CREATE INDEX IF NOT EXISTS idx_central_mapping_value_2 ON public.central_mapping(value_2);
CREATE INDEX IF NOT EXISTS idx_central_mapping_level_3 ON public.central_mapping(level_3);
CREATE INDEX IF NOT EXISTS idx_central_mapping_value_3 ON public.central_mapping(value_3);
CREATE INDEX IF NOT EXISTS idx_central_mapping_active ON public.central_mapping(is_active);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_central_mapping_type_level1 ON public.central_mapping(type, level_1);
CREATE INDEX IF NOT EXISTS idx_central_mapping_type_active ON public.central_mapping(type, is_active);

-- Add trigger to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_central_mapping_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_central_mapping_updated_at
    BEFORE UPDATE ON public.central_mapping
    FOR EACH ROW
    EXECUTE FUNCTION update_central_mapping_updated_at();

-- Add comments for documentation
COMMENT ON TABLE public.central_mapping IS 'Central mapping table for hierarchical classification data';
COMMENT ON COLUMN public.central_mapping.type IS 'The category type (e.g., Property Type, U.S_Regions, capital_position)';
COMMENT ON COLUMN public.central_mapping.level_1 IS 'First level header/classification';
COMMENT ON COLUMN public.central_mapping.value_1 IS 'First level value';
COMMENT ON COLUMN public.central_mapping.level_2 IS 'Second level header/classification (optional)';
COMMENT ON COLUMN public.central_mapping.value_2 IS 'Second level value (optional)';
COMMENT ON COLUMN public.central_mapping.level_3 IS 'Third level header/classification (optional)';
COMMENT ON COLUMN public.central_mapping.value_3 IS 'Third level value (optional)';
COMMENT ON COLUMN public.central_mapping.is_active IS 'Whether this mapping entry is active';

-- Sample data insertion (can be removed in production)
-- INSERT INTO public.central_mapping (type, level_1, value_1, level_2, value_2) VALUES
-- ('Property Type', 'Property Type', 'Multi-Family', 'Property Subproperty Type', 'Affordable Housing'),
-- ('Property Type', 'Property Type', 'Multi-Family', 'Property Subproperty Type', 'Rental'),
-- ('U.S_Regions', 'U.S_Regions', 'Appalachia', 'U.S_Regions_States', 'Alabama'),
-- ('U.S_Regions', 'U.S_Regions', 'Appalachia', 'U.S_Regions_States', 'Georgia'); 