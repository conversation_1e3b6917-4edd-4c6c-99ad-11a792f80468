-- Field Weights Setup for Enhanced Matching System
-- Updated to match actual matching algorithm and sum to exactly 100%

-- 1. Clear existing weights and set up fresh
DELETE FROM field_weights;

-- 2. Insert field weights that match the actual matching algorithm
-- These sum to exactly 1.0 (100%) excluding capital_position
INSERT INTO field_weights (field_name, weight, description) VALUES 
  -- Binary gate (excluded from normalization)
  ('capital_position', 1.0, 'Binary gate - must match or deal excluded completely'),
  
  -- Primary scoring fields (sum to exactly 1.0 = 100%)
  ('location', 0.35, 'Geographic matching (region/state/city hierarchy) - 35%'),
  ('deal_size', 0.30, 'Deal size overlap with complex fuzzy logic - 30%'),
  ('property_types', 0.15, 'Property type array matching - 15%'),
  ('strategies', 0.10, 'Investment strategies array matching - 10%'),
  ('financial_products', 0.05, 'Financial products array matching - 5%'),
  ('loan_to_value', 0.03, 'LTV range overlap matching - 3%'),
  ('interest_rate', 0.02, 'Interest rate comparison matching - 2%');

-- 3. Verify the setup
SELECT 'Updated field weights (matches actual matching algorithm):' as info;

SELECT 
  field_name,
  ROUND(weight * 100, 1) || '%' as percentage,
  description
FROM field_weights 
ORDER BY 
  CASE WHEN field_name = 'capital_position' THEN 0 ELSE weight END DESC;

-- 4. Verify normalization (should sum to exactly 100% excluding capital_position)
SELECT 
  'Normalization check:' as info,
  ROUND(SUM(weight) * 100, 1) || '%' as total_percentage,
  COUNT(*) as scoring_field_count,
  CASE 
    WHEN ABS(SUM(weight) - 1.0) < 0.001 THEN 'PERFECT ✓'
    ELSE 'ERROR - DOES NOT SUM TO 100%'
  END as status
FROM field_weights 
WHERE field_name != 'capital_position';

-- 5. Show capital position (binary gate)
SELECT 
  'Capital position (binary gate):' as info,
  ROUND(weight * 100, 1) || '%' as percentage,
  'Not counted in normalization' as note
FROM field_weights 
WHERE field_name = 'capital_position';

-- 6. Final summary
SELECT 
  'SUMMARY - Field Weights Distribution:' as info;

SELECT 
  field_name as field,
  ROUND(weight * 100, 1) as percentage,
  CASE 
    WHEN field_name = 'capital_position' THEN 'Binary Gate (Exclusion)'
    WHEN weight >= 0.20 THEN 'High Priority'
    WHEN weight >= 0.10 THEN 'Medium Priority' 
    WHEN weight >= 0.05 THEN 'Standard Priority'
    ELSE 'Low Priority'
  END as priority_level,
  description
FROM field_weights 
ORDER BY 
  CASE WHEN field_name = 'capital_position' THEN -1 ELSE weight END DESC; 