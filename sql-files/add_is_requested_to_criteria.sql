-- Add is_requested column to investment_criteria table
ALTER TABLE investment_criteria ADD COLUMN IF NOT EXISTS is_requested BOOLEAN DEFAULT false;

-- Add comment to the column
COMMENT ON COLUMN investment_criteria.is_requested IS 'Indicates if this criteria was specifically requested in the deal documents';

-- Create index for better performance on is_requested queries
CREATE INDEX IF NOT EXISTS idx_investment_criteria_is_requested ON investment_criteria(is_requested);

-- Create composite index for entity queries with is_requested
CREATE INDEX IF NOT EXISTS idx_investment_criteria_entity_is_requested ON investment_criteria(entity_type, entity_id, is_requested); 