-- Update Deal Strategies Mapping
-- Maps strategies for entity_type = 'Deal' based on the provided mapping

-- First, let's see what strategies currently exist for deals
SELECT DISTINCT unnest(strategies) as strategy
FROM investment_criteria 
WHERE entity_type = 'Deal' 
AND strategies IS NOT NULL
ORDER BY strategy;

-- Update strategies based on the mapping
-- Strategy mapping:
-- Acquisition -> null
-- Bridge -> null  
-- Construction -> Opportunistic
-- Core -> Core (no change)
-- Core Plus -> Core Plus (no change)
-- Development -> Opportunistic
-- Distressed -> Opportunistic
-- Ground-Lease -> null
-- Ground Up -> Opportunistic
-- Opportunistic -> Opportunistic (no change)
-- Permanent -> null
-- Recapitalization -> null
-- Redevelopment -> Value-Add
-- Refinance -> null
-- Repositioning -> Value-Add
-- Rescue Capital -> Opportunistic
-- Special Situations -> Opportunistic
-- Stabilization -> Value-Add
-- Stabilized -> Core
-- Value-Add -> Value-Add (no change)

UPDATE investment_criteria 
SET strategies = CASE 
    -- When strategies array contains only values that should be null
    WHEN strategies = ARRAY['Acquisition'] THEN NULL
    WHEN strategies = ARRAY['Bridge'] THEN NULL
    WHEN strategies = ARRAY['Ground-Lease'] THEN NULL
    WHEN strategies = ARRAY['Permanent'] THEN NULL
    WHEN strategies = ARRAY['Recapitalization'] THEN NULL
    WHEN strategies = ARRAY['Refinance'] THEN NULL
    
    -- When strategies array contains multiple values, map each one
    WHEN strategies @> ARRAY['Acquisition'] THEN 
        array_remove(strategies, 'Acquisition')
    WHEN strategies @> ARRAY['Bridge'] THEN 
        array_remove(strategies, 'Bridge')
    WHEN strategies @> ARRAY['Ground-Lease'] THEN 
        array_remove(strategies, 'Ground-Lease')
    WHEN strategies @> ARRAY['Permanent'] THEN 
        array_remove(strategies, 'Permanent')
    WHEN strategies @> ARRAY['Recapitalization'] THEN 
        array_remove(strategies, 'Recapitalization')
    WHEN strategies @> ARRAY['Refinance'] THEN 
        array_remove(strategies, 'Refinance')
    
    -- Map individual strategies to new values
    WHEN strategies = ARRAY['Construction'] THEN ARRAY['Opportunistic']
    WHEN strategies = ARRAY['Development'] THEN ARRAY['Opportunistic']
    WHEN strategies = ARRAY['Distressed'] THEN ARRAY['Opportunistic']
    WHEN strategies = ARRAY['Ground Up'] THEN ARRAY['Opportunistic']
    WHEN strategies = ARRAY['Rescue Capital'] THEN ARRAY['Opportunistic']
    WHEN strategies = ARRAY['Special Situations'] THEN ARRAY['Opportunistic']
    WHEN strategies = ARRAY['Redevelopment'] THEN ARRAY['Value-Add']
    WHEN strategies = ARRAY['Repositioning'] THEN ARRAY['Value-Add']
    WHEN strategies = ARRAY['Stabilization'] THEN ARRAY['Value-Add']
    WHEN strategies = ARRAY['Stabilized'] THEN ARRAY['Core']
    
    -- Handle arrays with multiple values that need mapping
    WHEN strategies @> ARRAY['Construction'] THEN 
        array_replace(strategies, 'Construction', 'Opportunistic')
    WHEN strategies @> ARRAY['Development'] THEN 
        array_replace(strategies, 'Development', 'Opportunistic')
    WHEN strategies @> ARRAY['Distressed'] THEN 
        array_replace(strategies, 'Distressed', 'Opportunistic')
    WHEN strategies @> ARRAY['Ground Up'] THEN 
        array_replace(strategies, 'Ground Up', 'Opportunistic')
    WHEN strategies @> ARRAY['Rescue Capital'] THEN 
        array_replace(strategies, 'Rescue Capital', 'Opportunistic')
    WHEN strategies @> ARRAY['Special Situations'] THEN 
        array_replace(strategies, 'Special Situations', 'Opportunistic')
    WHEN strategies @> ARRAY['Redevelopment'] THEN 
        array_replace(strategies, 'Redevelopment', 'Value-Add')
    WHEN strategies @> ARRAY['Repositioning'] THEN 
        array_replace(strategies, 'Repositioning', 'Value-Add')
    WHEN strategies @> ARRAY['Stabilization'] THEN 
        array_replace(strategies, 'Stabilization', 'Value-Add')
    WHEN strategies @> ARRAY['Stabilized'] THEN 
        array_replace(strategies, 'Stabilized', 'Core')
    
    ELSE strategies
END
WHERE entity_type = 'Deal' 
AND strategies IS NOT NULL;

-- Remove any empty arrays that might result from the mapping
UPDATE investment_criteria 
SET strategies = NULL
WHERE entity_type = 'Deal' 
AND (strategies = ARRAY[]::text[] OR array_length(strategies, 1) = 0);

-- Verify the results
SELECT criteria_id, strategies
FROM investment_criteria 
WHERE entity_type = 'Deal' 
AND strategies IS NOT NULL
LIMIT 20; 