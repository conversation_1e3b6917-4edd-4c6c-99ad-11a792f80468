-- Insert sample strategy options into central_mapping table
-- This provides the initial data for the strategy field in DealDetailV2

INSERT INTO central_mapping (mapping_category, mapping_value, mapping_description, is_active, created_at, updated_at) 
VALUES 
  ('strategy', 'Core', 'Conservative investment strategy focused on stable, income-producing properties with minimal risk', true, NOW(), NOW()),
  ('strategy', 'Core Plus', 'Moderate risk strategy targeting properties with some value-add potential while maintaining stability', true, NOW(), NOW()),
  ('strategy', 'Value-Add', 'Active management strategy to improve property performance through renovations and operational improvements', true, NOW(), NOW()),
  ('strategy', 'Opportunistic', 'High-risk, high-reward strategy involving significant property transformations or market timing', true, NOW(), NOW()),
  ('strategy', 'Distressed', 'Specialized strategy targeting properties with financial or physical distress for turnaround opportunities', true, NOW(), NOW()),
  ('strategy', 'Development', 'Ground-up development strategy for new construction projects', true, NOW(), NOW()),
  ('strategy', 'Land Banking', 'Long-term strategy of acquiring and holding land for future development', true, NOW(), NOW()),
  ('strategy', 'Joint Venture', 'Partnership-based strategy combining resources and expertise', true, NOW(), NOW())
ON CONFLICT (mapping_category, mapping_value) 
DO UPDATE SET 
  mapping_description = EXCLUDED.mapping_description,
  is_active = EXCLUDED.is_active,
  updated_at = NOW();

-- Verify the insert
SELECT 
  mapping_category, 
  mapping_value, 
  mapping_description, 
  is_active 
FROM central_mapping 
WHERE mapping_category = 'strategy' 
ORDER BY mapping_value;
