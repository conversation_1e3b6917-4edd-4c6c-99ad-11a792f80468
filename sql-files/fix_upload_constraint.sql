-- Fix upload_data_log constraint to allow multiple rows per upload
-- Drop the incorrect constraint that only allows one row per upload_log_id
ALTER TABLE upload_data_log DROP CONSTRAINT IF EXISTS unique_upload_row;

-- Add the correct constraint that allows multiple rows per upload but ensures row numbers are unique within each upload
ALTER TABLE upload_data_log ADD CONSTRAINT unique_upload_row UNIQUE (upload_log_id, row_number);

-- Clear any existing test data that might be causing conflicts
-- DELETE FROM upload_data_log WHERE upload_log_id = 1;
-- DELETE FROM upload_logs WHERE upload_id = 1;

-- Verify the constraint was updated correctly
SELECT 
    conname as constraint_name,
    pg_get_constraintdef(c.oid) as constraint_definition
FROM pg_constraint c
JOIN pg_class t ON c.conrelid = t.oid
WHERE t.relname = 'upload_data_log' 
AND contype = 'u'; 

TRUNCATE TABLE upload_data_log RESTART IDENTITY CASCADE;
TRUNCATE TABLE upload_logs RESTART IDENTITY CASCADE;