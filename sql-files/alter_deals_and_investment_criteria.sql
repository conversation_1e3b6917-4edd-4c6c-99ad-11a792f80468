-- Migration: Add capital_position to investment_criteria and remove overlapping fields from deals

-- 1. Add capital_position to investment_criteria
ALTER TABLE public.investment_criteria
ADD COLUMN IF NOT EXISTS capital_position TEXT[];

-- 2. Remove fields from deals that are common with investment_criteria
ALTER TABLE public.deals
DROP COLUMN IF EXISTS region,
DROP COLUMN IF EXISTS state,
DROP COLUMN IF EXISTS city,
DROP COLUMN IF EXISTS deal_size_min,
DROP COLUMN IF EXISTS deal_size_max,
DROP COLUMN IF EXISTS deal_size_range,
DROP COLUMN IF EXISTS property_type,
DROP COLUMN IF EXISTS capital_type,
DROP COLUMN IF EXISTS loan_type,
DROP COLUMN IF EXISTS term_months,
DROP COLUMN IF EXISTS ltc,
DROP COLUMN IF EXISTS ltv,
DROP COLUMN IF EXISTS hold_period_months,
DROP COLUMN IF EXISTS expected_irr,
DROP COLUMN IF EXISTS expected_em,
DROP COLUMN IF EXISTS equity_structure,
DROP COLUMN IF EXISTS attachment_point;

-- 3. Add new columns to deals for enhanced property and projection details
ALTER TABLE public.deals
ADD COLUMN IF NOT EXISTS zip_code TEXT,
ADD COLUMN IF NOT EXISTS neighborhood TEXT,
ADD COLUMN IF NOT EXISTS property_description TEXT,
ADD COLUMN IF NOT EXISTS lot_area DECIMAL(15, 2),
ADD COLUMN IF NOT EXISTS floor_area_ratio DECIMAL(10, 4),
ADD COLUMN IF NOT EXISTS zoning_square_footage DECIMAL(15, 2),
ADD COLUMN IF NOT EXISTS yield_on_cost DECIMAL(5, 4),
ADD COLUMN IF NOT EXISTS projected_gp_equity_multiple DECIMAL(5, 4),
ADD COLUMN IF NOT EXISTS projected_gp_irr DECIMAL(5, 4),
ADD COLUMN IF NOT EXISTS projected_lp_equity_multiple DECIMAL(5, 4),
ADD COLUMN IF NOT EXISTS projected_lp_irr DECIMAL(5, 4),
ADD COLUMN IF NOT EXISTS projected_total_equity_multiple DECIMAL(5, 4),
ADD COLUMN IF NOT EXISTS projected_total_irr DECIMAL(5, 4);

-- 4. Add column to store raw LLM output from deal processor
ALTER TABLE public.deals
ADD COLUMN IF NOT EXISTS raw_llm_output TEXT;

-- 5. Change document fields to arrays to support multiple files per deal
ALTER TABLE public.deals
  ALTER COLUMN document_type TYPE TEXT[] USING CASE WHEN document_type IS NULL THEN NULL ELSE ARRAY[document_type] END,
  ALTER COLUMN document_source TYPE TEXT[] USING CASE WHEN document_source IS NULL THEN NULL ELSE ARRAY[document_source] END,
  ALTER COLUMN document_filename TYPE TEXT[] USING CASE WHEN document_filename IS NULL THEN NULL ELSE ARRAY[document_filename] END,
  ALTER COLUMN extraction_method TYPE TEXT[] USING CASE WHEN extraction_method IS NULL THEN NULL ELSE ARRAY[extraction_method] END;
-- End of array migration

-- Note: Make sure to backup your data before running this migration. 