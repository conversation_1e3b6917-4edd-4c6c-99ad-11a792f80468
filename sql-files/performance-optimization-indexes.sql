-- Performance Optimization Indexes for Contacts Unified Filters API
-- Run these indexes to improve query performance

-- 1. Investment Criteria Capital Position Index (GIN for array operations)
CREATE INDEX IF NOT EXISTS idx_investment_criteria_capital_position_gin 
ON investment_criteria USING gin (capital_position);

-- 2. Contact Enrichment Capital Positions Index (GIN for JSONB operations)
CREATE INDEX IF NOT EXISTS idx_contact_enrichment_capital_positions_gin 
ON contact_enrichment USING gin (capital_positions);

-- 3. Contacts Source Index (for source filtering)
CREATE INDEX IF NOT EXISTS idx_contacts_source 
ON contacts USING btree (source);

-- 4. Contacts Email Status Index (for email status filtering)
CREATE INDEX IF NOT EXISTS idx_contacts_email_status 
ON contacts USING btree (email_status);

-- 5. Investment Criteria Property Types Index (GIN for array operations)
CREATE INDEX IF NOT EXISTS idx_investment_criteria_property_types_gin 
ON investment_criteria USING gin (property_types);

-- 6. Investment Criteria Strategies Index (GIN for array operations)
CREATE INDEX IF NOT EXISTS idx_investment_criteria_strategies_gin 
ON investment_criteria USING gin (strategies);

-- 7. Contact Enrichment Company Type Index
CREATE INDEX IF NOT EXISTS idx_contact_enrichment_company_type 
ON contact_enrichment USING btree (company_type);

-- 8. Composite Index for Investment Criteria Entity Lookups
CREATE INDEX IF NOT EXISTS idx_investment_criteria_entity_contact 
ON investment_criteria USING btree (entity_type, entity_id) 
WHERE entity_type = 'Contact';

-- 9. Index for Gmail Messages Recipients (already exists but verify)
-- CREATE INDEX IF NOT EXISTS idx_gmail_messages_recipients 
-- ON gmail_messages USING gin (recipients);

-- 10. Index for Contacts Email Lookups
CREATE INDEX IF NOT EXISTS idx_contacts_email_lookup 
ON contacts USING btree (email, personal_email) 
WHERE email IS NOT NULL;

-- Verify indexes were created
SELECT 
    indexname, 
    tablename, 
    indexdef 
FROM pg_indexes 
WHERE indexname LIKE 'idx_%' 
AND tablename IN ('contacts', 'investment_criteria', 'contact_enrichment', 'gmail_messages')
ORDER BY tablename, indexname; 