-- Migration to enhance extra_fields functionality
-- This adds better indexing and querying capabilities for extra_fields

-- Add additional indexes for commonly queried extra fields
CREATE INDEX IF NOT EXISTS idx_deals_extra_fields_financial_metrics 
ON public.deals USING GIN ((extra_fields->'financial_metrics'));

CREATE INDEX IF NOT EXISTS idx_deals_extra_fields_property_details 
ON public.deals USING GIN ((extra_fields->'property_details'));

CREATE INDEX IF NOT EXISTS idx_deals_extra_fields_market_data 
ON public.deals USING GIN ((extra_fields->'market_data'));

CREATE INDEX IF NOT EXISTS idx_deals_extra_fields_timeline 
ON public.deals USING GIN ((extra_fields->'timeline'));

CREATE INDEX IF NOT EXISTS idx_deals_extra_fields_legal_entities 
ON public.deals USING GIN ((extra_fields->'legal_entities'));

CREATE INDEX IF NOT EXISTS idx_deals_extra_fields_tenant_info 
ON public.deals USING GIN ((extra_fields->'tenant_info'));

CREATE INDEX IF NOT EXISTS idx_deals_extra_fields_risk_factors 
ON public.deals USING GIN ((extra_fields->'risk_factors'));

-- Create a function to extract specific values from extra_fields
CREATE OR REPLACE FUNCTION get_extra_field_value(
    extra_fields JSONB,
    category TEXT,
    field_name TEXT
) RETURNS TEXT AS $$
BEGIN
    RETURN extra_fields->category->>field_name;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create a function to check if extra_fields contains a specific category
CREATE OR REPLACE FUNCTION has_extra_field_category(
    extra_fields JSONB,
    category TEXT
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN extra_fields ? category;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create a function to get all field names from a specific category
CREATE OR REPLACE FUNCTION get_extra_field_category_keys(
    extra_fields JSONB,
    category TEXT
) RETURNS TEXT[] AS $$
BEGIN
    IF extra_fields ? category THEN
        RETURN ARRAY(
            SELECT jsonb_object_keys(extra_fields->category)
        );
    ELSE
        RETURN ARRAY[]::TEXT[];
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create a view for easier querying of extra fields
CREATE OR REPLACE VIEW deals_with_extra_fields AS
SELECT 
    d.*,
    extra_fields->'financial_metrics' as financial_metrics,
    extra_fields->'property_details' as property_details,
    extra_fields->'market_data' as market_data,
    extra_fields->'timeline' as timeline,
    extra_fields->'legal_entities' as legal_entities,
    extra_fields->'tenant_info' as tenant_info,
    extra_fields->'risk_factors' as risk_factors,
    extra_fields->'contact_info' as contact_info,
    extra_fields->'documents' as documents,
    extra_fields->'calculations' as calculations,
    extra_fields->'notes' as notes
FROM public.deals d;

-- Add comments for the new functions
COMMENT ON FUNCTION get_extra_field_value(JSONB, TEXT, TEXT) IS 'Extract a specific field value from a category in extra_fields';
COMMENT ON FUNCTION has_extra_field_category(JSONB, TEXT) IS 'Check if extra_fields contains a specific category';
COMMENT ON FUNCTION get_extra_field_category_keys(JSONB, TEXT) IS 'Get all field names from a specific category in extra_fields';
COMMENT ON VIEW deals_with_extra_fields IS 'View for easier querying of deals with structured extra_fields access';

-- Example queries that can now be performed efficiently:

-- 1. Find deals with specific financial metrics
-- SELECT * FROM deals WHERE extra_fields->'financial_metrics'->>'cap_rate'::numeric > 0.07;

-- 2. Find deals with specific property amenities
-- SELECT * FROM deals WHERE extra_fields->'property_details'->>'amenities' ? 'Pool';

-- 3. Find deals with specific risk factors
-- SELECT * FROM deals WHERE extra_fields->'risk_factors'->>'market_risks' ? 'Economic downturn';

-- 4. Find deals with construction timeline information
-- SELECT * FROM deals WHERE extra_fields->'timeline'->>'construction_start' IS NOT NULL;

-- 5. Find deals with specific tenant information
-- SELECT * FROM deals WHERE extra_fields->'tenant_info'->>'occupancy_rate'::numeric > 0.9;

-- 6. Use the view for easier querying
-- SELECT deal_name, financial_metrics->>'cap_rate' as cap_rate, property_details->>'amenities' as amenities 
-- FROM deals_with_extra_fields 
-- WHERE financial_metrics->>'cap_rate'::numeric > 0.07; 