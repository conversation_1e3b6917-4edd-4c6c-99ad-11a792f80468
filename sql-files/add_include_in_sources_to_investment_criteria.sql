-- Migration: Add include_in_sources boolean column to investment_criteria table

-- Add include_in_sources column with default value true
ALTER TABLE public.investment_criteria
ADD COLUMN IF NOT EXISTS include_in_sources BOOLEAN DEFAULT TRUE;

-- Add comment for documentation
COMMENT ON COLUMN public.investment_criteria.include_in_sources IS 'Flag to determine if this criteria should be included in sources calculation';

-- Create index for performance on boolean column
CREATE INDEX IF NOT EXISTS idx_investment_criteria_include_in_sources 
ON public.investment_criteria(include_in_sources);

-- Update existing records to have include_in_sources = true by default
UPDATE public.investment_criteria 
SET include_in_sources = TRUE 
WHERE include_in_sources IS NULL; 