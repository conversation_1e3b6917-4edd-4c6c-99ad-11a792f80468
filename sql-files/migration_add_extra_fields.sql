-- Migration script to add extra fields to deal_data_pivoted table
-- Run this script to update existing databases

-- Add new columns for enhanced metadata and processing
ALTER TABLE public.deal_data_pivoted 
ADD COLUMN IF NOT EXISTS document_type TEXT,
ADD COLUMN IF NOT EXISTS extraction_confidence TEXT,
ADD COLUMN IF NOT EXISTS processing_notes TEXT,
ADD COLUMN IF NOT EXISTS missing_critical_fields TEXT,
ADD COLUMN IF NOT EXISTS data_quality_issues TEXT,
ADD COLUMN IF NOT EXISTS extraction_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS processor_version TEXT,
ADD COLUMN IF NOT EXISTS llm_model_used TEXT,
ADD COLUMN IF NOT EXISTS llm_provider TEXT,
ADD COLUMN IF NOT EXISTS extraction_method TEXT,
ADD COLUMN IF NOT EXISTS document_source TEXT,
ADD COLUMN IF NOT EXISTS document_filename TEXT,
ADD COLUMN IF NOT EXISTS document_size_bytes INTEGER,
ADD COLUMN IF NOT EXISTS processing_duration_ms INTEGER,
ADD COLUMN IF NOT EXISTS confidence_scores JSONB,
ADD COLUMN IF NOT EXISTS field_extraction_metadata JSONB,
ADD COLUMN IF NOT EXISTS validation_results JSONB,
ADD COLUMN IF NOT EXISTS quality_metrics JSONB,
ADD COLUMN IF NOT EXISTS custom_fields JSONB,
ADD COLUMN IF NOT EXISTS tags TEXT[],
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'processed',
ADD COLUMN IF NOT EXISTS review_status TEXT DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS reviewed_by TEXT,
ADD COLUMN IF NOT EXISTS reviewed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS review_notes TEXT,
ADD COLUMN IF NOT EXISTS extra_fields JSONB;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_deal_data_document_type ON public.deal_data_pivoted(document_type);
CREATE INDEX IF NOT EXISTS idx_deal_data_extraction_timestamp ON public.deal_data_pivoted(extraction_timestamp);
CREATE INDEX IF NOT EXISTS idx_deal_data_status ON public.deal_data_pivoted(status);
CREATE INDEX IF NOT EXISTS idx_deal_data_review_status ON public.deal_data_pivoted(review_status);
CREATE INDEX IF NOT EXISTS idx_deal_data_tags ON public.deal_data_pivoted USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_deal_data_custom_fields ON public.deal_data_pivoted USING GIN(custom_fields);

-- Add comments for documentation
COMMENT ON COLUMN public.deal_data_pivoted.document_type IS 'Type of document processed (IM, OM, Term Sheet, etc.)';
COMMENT ON COLUMN public.deal_data_pivoted.extraction_confidence IS 'Overall confidence level of the extraction';
COMMENT ON COLUMN public.deal_data_pivoted.processing_notes IS 'Important notes about the extraction process';
COMMENT ON COLUMN public.deal_data_pivoted.missing_critical_fields IS 'JSON array of important missing fields';
COMMENT ON COLUMN public.deal_data_pivoted.data_quality_issues IS 'JSON array of data quality issues found';
COMMENT ON COLUMN public.deal_data_pivoted.extraction_timestamp IS 'When the extraction was performed';
COMMENT ON COLUMN public.deal_data_pivoted.processor_version IS 'Version of the processor used';
COMMENT ON COLUMN public.deal_data_pivoted.llm_model_used IS 'LLM model used for extraction';
COMMENT ON COLUMN public.deal_data_pivoted.llm_provider IS 'LLM provider (perplexity, gemini, etc.)';
COMMENT ON COLUMN public.deal_data_pivoted.extraction_method IS 'Method used for extraction (universal, legacy)';
COMMENT ON COLUMN public.deal_data_pivoted.document_source IS 'Source type of the document';
COMMENT ON COLUMN public.deal_data_pivoted.document_filename IS 'Original filename of the document';
COMMENT ON COLUMN public.deal_data_pivoted.document_size_bytes IS 'Size of the document in bytes';
COMMENT ON COLUMN public.deal_data_pivoted.processing_duration_ms IS 'Processing time in milliseconds';
COMMENT ON COLUMN public.deal_data_pivoted.confidence_scores IS 'JSON object with confidence scores for different aspects';
COMMENT ON COLUMN public.deal_data_pivoted.field_extraction_metadata IS 'Metadata about field extraction process';
COMMENT ON COLUMN public.deal_data_pivoted.validation_results IS 'Results of data validation checks';
COMMENT ON COLUMN public.deal_data_pivoted.quality_metrics IS 'Quality metrics for the extracted data';
COMMENT ON COLUMN public.deal_data_pivoted.custom_fields IS 'JSON object containing additional fields not in the standard mapping';
COMMENT ON COLUMN public.deal_data_pivoted.tags IS 'Array of tags for categorizing the deal';
COMMENT ON COLUMN public.deal_data_pivoted.status IS 'Processing status of the record';
COMMENT ON COLUMN public.deal_data_pivoted.review_status IS 'Review status of the extracted data';
COMMENT ON COLUMN public.deal_data_pivoted.reviewed_by IS 'User who reviewed the data';
COMMENT ON COLUMN public.deal_data_pivoted.reviewed_at IS 'When the review was performed';
COMMENT ON COLUMN public.deal_data_pivoted.review_notes IS 'Notes from the review process';
COMMENT ON COLUMN public.deal_data_pivoted.extra_fields IS 'JSON object containing additional fields not in the standard mapping';

-- Update existing records to have default values
UPDATE public.deal_data_pivoted 
SET 
  extraction_timestamp = CURRENT_TIMESTAMP,
  processor_version = '1.0',
  extraction_method = 'legacy',
  status = 'processed',
  review_status = 'pending'
WHERE extraction_timestamp IS NULL; 