{"info": {"_postman_id": "c1465e5a-d933-4a12-bb47-71fbee6b0763", "name": "Anax Dashboard API - Complete Collection", "description": "Comprehensive API collection for Anax Dashboard with all discovered endpoints from codebase analysis", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json", "_exporter_id": "41354347"}, "item": [{"name": "Companies", "description": "Company management APIs for CRUD operations, search, filtering, and data scraping. Includes unified filtering system, overview data, source tracking, and automated web scraping capabilities for company data enrichment.", "item": [{"name": "Get All Companies", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/companies?page=1&limit=20&sort=updated_at&direction=desc", "host": ["{{baseUrl}}"], "path": ["api", "companies"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "sort", "value": "updated_at"}, {"key": "direction", "value": "desc"}, {"key": "search", "value": "", "disabled": true}, {"key": "capitalTypes", "value": "", "disabled": true}, {"key": "loanTypes", "value": "", "disabled": true}, {"key": "propertyTypes", "value": "", "disabled": true}, {"key": "dealSize_min", "value": "", "disabled": true}, {"key": "dealSize_max", "value": "", "disabled": true}]}, "description": "Get list of all companies with filtering, sorting, and pagination"}}, {"name": "Get Company by ID", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/companies/{{companyId}}", "description": "Get a specific company by ID"}}, {"name": "Update Company", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Company Name\",\n  \"industry\": \"Real Estate Investment\",\n  \"website\": \"https://example.com\"\n}"}, "url": "{{baseUrl}}/api/companies/{{companyId}}", "description": "Update company details"}}, {"name": "Search Companies", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/companies/search?q={{searchQuery}}", "host": ["{{baseUrl}}"], "path": ["api", "companies", "search"], "query": [{"key": "q", "value": "{{searchQuery}}"}, {"key": "name", "value": "", "disabled": true}]}, "description": "Search companies by name or query"}}, {"name": "Get Company Unified Filters", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/companies/unified-filters?page=1&limit=25&sortBy=updated_at&sortOrder=desc", "host": ["{{baseUrl}}"], "path": ["api", "companies", "unified-filters"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "25"}, {"key": "sortBy", "value": "updated_at"}, {"key": "sortOrder", "value": "desc"}]}, "description": "Get companies with unified filtering system"}}, {"name": "Get Company Overview Filters", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/companies/overview-filters", "description": "Get filter options for company overview"}}, {"name": "Get Company Sources", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/companies/sources", "description": "Get list of company data sources"}}, {"name": "Scrape Company Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"companyId\": {{companyId}},\n  \"url\": \"https://example.com\"\n}"}, "url": "{{baseUrl}}/api/companies/scrape", "description": "Trigger company data scraping"}}]}, {"name": "Contacts", "description": "Contact management system with advanced filtering, processing status tracking, email verification, and batch operations. Supports contact extraction, enrichment, source attribution, and integration with email campaigns and CRM systems.", "item": [{"name": "Get All Contacts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/contacts?page=1&limit=10&sort=updated_at&direction=desc", "host": ["{{baseUrl}}"], "path": ["api", "contacts"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sort", "value": "updated_at"}, {"key": "direction", "value": "desc"}, {"key": "search", "value": "", "disabled": true}, {"key": "source", "value": "", "disabled": true}, {"key": "category", "value": "", "disabled": true}, {"key": "extracted_only", "value": "false", "disabled": true}, {"key": "email_generated", "value": "false", "disabled": true}, {"key": "email_verification_status", "value": "", "disabled": true}, {"key": "has_processing_error", "value": "false", "disabled": true}]}, "description": "Get list of all contacts with filtering and pagination"}}, {"name": "Create Contact", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"title\": \"Chief Investment Officer\",\n  \"company_name\": \"Example Corp\",\n  \"industry\": \"Real Estate Investment\",\n  \"contact_city\": \"New York\",\n  \"contact_state\": \"NY\",\n  \"contact_country\": \"United States\",\n  \"linkedin_url\": \"https://linkedin.com/in/johndoe\"\n}"}, "url": "{{baseUrl}}/api/contacts", "description": "Create a new contact"}}, {"name": "Get Contact by ID", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/contacts/{{contactId}}", "description": "Get a specific contact by ID"}}, {"name": "Update Contact", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"title\": \"Senior Investment Officer\"\n}"}, "url": "{{baseUrl}}/api/contacts/{{contactId}}", "description": "Update contact details"}}, {"name": "Search Contacts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/contacts/search?q={{searchQuery}}", "host": ["{{baseUrl}}"], "path": ["api", "contacts", "search"], "query": [{"key": "q", "value": "{{searchQuery}}"}]}, "description": "Search contacts by query"}}, {"name": "Search Contact by Email", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/contacts/search-by-email?email={{contactEmail}}", "host": ["{{baseUrl}}"], "path": ["api", "contacts", "search-by-email"], "query": [{"key": "email", "value": "{{contactEmail}}"}]}, "description": "Search contact by email address"}}, {"name": "Get Contact Overview", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/contacts/overview", "description": "Get contact overview data"}}, {"name": "Get Contact Overview by ID", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/contacts/overview/{{contactId}}", "description": "Get contact overview for specific contact"}}, {"name": "Get Contact Extracted Data", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/contacts/{{contactId}}/extracted-data", "description": "Get extracted data for a contact"}}, {"name": "Get Contact Sources", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/contacts/sources", "description": "Get list of contact data sources"}}, {"name": "Get Contact Sources Count", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/contacts/sources/count", "description": "Get count of contacts by source"}}]}, {"name": "Deals", "description": "Deal management APIs for real estate transactions including search, filtering, file uploads, and document processing. Features unified filtering system, deal categorization, file associations, and automated deal extraction from various sources.", "item": [{"name": "Get All Deals", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/deals?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "deals"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get all deals with pagination"}}, {"name": "Get Deal by ID", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/deals/{{dealId}}", "description": "Get a specific deal by ID"}}, {"name": "Search Deals", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/deals/search?q={{searchQuery}}", "host": ["{{baseUrl}}"], "path": ["api", "deals", "search"], "query": [{"key": "q", "value": "{{searchQuery}}"}]}, "description": "Search deals by query"}}, {"name": "Get Deal Filters", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/deals/filters", "description": "Get available deal filters"}}, {"name": "Get Deals with Unified Filters", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/deals/unified-filters?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "deals", "unified-filters"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get deals with unified filtering system"}}, {"name": "Upload Deal File", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": ""}, {"key": "dealName", "value": "Sample Deal", "type": "text"}, {"key": "description", "value": "Deal description", "type": "text"}]}, "url": "{{baseUrl}}/api/deals/upload", "description": "Upload a deal file for processing"}}, {"name": "Upload Deal File (Simplified)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": ""}]}, "url": "{{baseUrl}}/api/deals/upload-simplified", "description": "Simplified deal file upload"}}, {"name": "Get Deal Files", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/deals/{{dealId}}/files", "description": "Get files associated with a deal"}}]}, {"name": "Deal News", "description": "News processing and deal extraction system that monitors real estate news sources (TheRealDeal, Bisnow, PincusCo), processes articles with AI, extracts deal information, and enriches data with location analysis. Automates deal discovery from news content.", "item": [{"name": "Get Deal News", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/deal-news?source=all&page=1&onlyFetched=false&onlyExtracted=false", "host": ["{{baseUrl}}"], "path": ["api", "deal-news"], "query": [{"key": "source", "value": "all"}, {"key": "page", "value": "1"}, {"key": "onlyFetched", "value": "false"}, {"key": "onlyExtracted", "value": "false"}, {"key": "titleSearch", "value": "", "disabled": true}, {"key": "orderBy", "value": "updated_at", "disabled": true}]}, "description": "Get deal news with filtering options"}}, {"name": "Process News Articles", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"testMode\": false,\n  \"limit\": 3,\n  \"action\": \"start\"\n}"}, "url": "{{baseUrl}}/api/deal-news/process", "description": "Process raw news articles"}}, {"name": "Process TheRealDeal Articles", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"testMode\": false,\n  \"limit\": 3,\n  \"action\": \"start\"\n}"}, "url": "{{baseUrl}}/api/deal-news/process/therealdeal", "description": "Process TheRealDeal articles"}}, {"name": "Process Bisnow Articles", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"testMode\": false,\n  \"limit\": 3,\n  \"action\": \"start\"\n}"}, "url": "{{baseUrl}}/api/deal-news/process/bisnow", "description": "Process Bisnow articles"}}, {"name": "Process PincusCo Articles", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"testMode\": false,\n  \"limit\": 3,\n  \"action\": \"start\"\n}"}, "url": "{{baseUrl}}/api/deal-news/process/pincus", "description": "Process PincusCo articles"}}, {"name": "Extract Deals from News", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"testMode\": false\n}"}, "url": "{{baseUrl}}/api/deal-news/extract-deals", "description": "Extract deals from processed news"}}, {"name": "Process News Locations", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"testMode\": false,\n  \"limit\": 3\n}"}, "url": "{{baseUrl}}/api/deal-news/location", "description": "Process location data from news"}}, {"name": "Get Extracted Deals", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/deal-news/extracted-deals?page=1&pageSize=10&sort=created_at", "host": ["{{baseUrl}}"], "path": ["api", "deal-news", "extracted-deals"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "sort", "value": "created_at"}, {"key": "search", "value": "", "disabled": true}, {"key": "source", "value": "", "disabled": true}]}, "description": "Get extracted deals from news processing"}}]}, {"name": "Files", "description": "Comprehensive file management system with upload, download, metadata management, access control, and entity relationships. Supports file associations with deals, contacts, and companies, including relationship tracking and bulk operations.", "item": [{"name": "Get Files", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/files?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "files"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "file_name", "value": "", "disabled": true}, {"key": "mime_type", "value": "", "disabled": true}, {"key": "uploaded_by", "value": "", "disabled": true}]}, "description": "Search and list files"}}, {"name": "Upload File", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": ""}, {"key": "access_level", "value": "private", "type": "text"}, {"key": "uploaded_by", "value": "<EMAIL>", "type": "text"}]}, "url": "{{baseUrl}}/api/files", "description": "Upload a new file"}}, {"name": "Get File by ID", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/files/{{fileId}}", "description": "Get file details and relationships"}}, {"name": "Update File", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"access_level\": \"public\",\n  \"metadata\": {\"description\": \"Updated file\"}\n}"}, "url": "{{baseUrl}}/api/files/{{fileId}}", "description": "Update file metadata"}}, {"name": "Delete File", "request": {"method": "DELETE", "header": [], "url": "{{baseUrl}}/api/files/{{fileId}}", "description": "Delete a file"}}, {"name": "Download File", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/files/{{fileId}}/download", "description": "Download a file"}}, {"name": "Get File Relationships", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/files/relationships?file_id={{fileId}}", "host": ["{{baseUrl}}"], "path": ["api", "files", "relationships"], "query": [{"key": "file_id", "value": "{{fileId}}"}]}, "description": "Get file relationships"}}, {"name": "Create File Relationship", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"file_id\": \"{{fileId}}\",\n  \"target_table\": \"deals\",\n  \"target_id\": {{dealId}},\n  \"relationship_type\": \"attachment\"\n}"}, "url": "{{baseUrl}}/api/files/relationships", "description": "Create a file relationship"}}, {"name": "Get Entity Files", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/files/entity/{{entityType}}/{{entityId}}", "description": "Get files for a specific entity"}}]}, {"name": "Processing", "description": "Background job processing system that manages data enrichment pipelines including email validation, contact enrichment, company processing, and automated workflows. Features job scheduling, status monitoring, analytics, and manual execution controls.", "item": [{"name": "Get Processing Jobs", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/processing/trigger?action=jobs", "host": ["{{baseUrl}}"], "path": ["api", "processing", "trigger"], "query": [{"key": "action", "value": "jobs"}]}, "description": "Get all processing jobs"}}, {"name": "Get Running Jobs", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/processing/trigger?action=running_jobs", "host": ["{{baseUrl}}"], "path": ["api", "processing", "trigger"], "query": [{"key": "action", "value": "running_jobs"}]}, "description": "Get currently running jobs"}}, {"name": "Get Job Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/processing/trigger?action=job_status&jobId={{jobId}}", "host": ["{{baseUrl}}"], "path": ["api", "processing", "trigger"], "query": [{"key": "action", "value": "job_status"}, {"key": "jobId", "value": "{{jobId}}"}]}, "description": "Get specific job status"}}, {"name": "Execute Manual Job", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"execute_manual\",\n  \"stage\": \"email_validation\",\n  \"options\": {\n    \"limit\": 50,\n    \"singleId\": null,\n    \"filters\": {\n      \"source\": \"all\"\n    }\n  }\n}"}, "url": "{{baseUrl}}/api/processing/trigger", "description": "Execute a manual processing job"}}, {"name": "Toggle Scheduled Job", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"toggle_scheduled_job\",\n  \"options\": {\n    \"jobId\": \"email_validation_job\",\n    \"enabled\": true\n  }\n}"}, "url": "{{baseUrl}}/api/processing/trigger", "description": "Toggle a scheduled job on/off"}}, {"name": "Get Processing Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/processing/stats?source=all", "host": ["{{baseUrl}}"], "path": ["api", "processing", "stats"], "query": [{"key": "source", "value": "all"}]}, "description": "Get processing pipeline statistics"}}, {"name": "Get Processing Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/processing/analytics?period=daily&stage=all&realtime=false", "host": ["{{baseUrl}}"], "path": ["api", "processing", "analytics"], "query": [{"key": "period", "value": "daily"}, {"key": "stage", "value": "all"}, {"key": "realtime", "value": "false"}]}, "description": "Get processing analytics data"}}, {"name": "Get Processing Jobs List", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/processing/jobs?status=pending&limit=50&offset=0", "host": ["{{baseUrl}}"], "path": ["api", "processing", "jobs"], "query": [{"key": "status", "value": "pending"}, {"key": "limit", "value": "50"}, {"key": "offset", "value": "0"}]}, "description": "Get processing jobs with filtering"}}, {"name": "Get Processing Entities", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/processing/entities?type=contact&limit=100", "host": ["{{baseUrl}}"], "path": ["api", "processing", "entities"], "query": [{"key": "type", "value": "contact"}, {"key": "limit", "value": "100"}]}, "description": "Get entities for processing"}}]}, {"name": "Dashboard", "description": "Analytics and reporting APIs that provide dashboard statistics, timeline data, pipeline metrics, and performance insights. Supports real-time data visualization, filtering by date ranges and sources, and comprehensive business intelligence.", "item": [{"name": "Get Dashboard Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/dashboard/stats?source=all&dateRange=7d", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "stats"], "query": [{"key": "source", "value": "all"}, {"key": "date<PERSON><PERSON><PERSON>", "value": "7d"}]}, "description": "Get dashboard statistics"}}, {"name": "Get Dashboard Timeline", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/dashboard/timeline?dateRange=7d&source=all&entityType=all&stage=all", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "timeline"], "query": [{"key": "date<PERSON><PERSON><PERSON>", "value": "7d"}, {"key": "source", "value": "all"}, {"key": "entityType", "value": "all"}, {"key": "stage", "value": "all"}]}, "description": "Get dashboard timeline data"}}, {"name": "Get Simple Pipeline Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/dashboard/simple-pipeline?dateRange=7d&source=all", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "simple-pipeline"], "query": [{"key": "date<PERSON><PERSON><PERSON>", "value": "7d"}, {"key": "source", "value": "all"}]}, "description": "Get simple pipeline dashboard data"}}, {"name": "Get Pipeline Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/dashboard/pipeline?dateRange=7d&source=all", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "pipeline"], "query": [{"key": "date<PERSON><PERSON><PERSON>", "value": "7d"}, {"key": "source", "value": "all"}]}, "description": "Get detailed pipeline data"}}]}, {"name": "Investment Criteria", "description": "Investment criteria management system that handles investor preferences, deal size requirements, property type mappings, capital position filters, and matching algorithms. Enables sophisticated deal-to-investor matching based on investment profiles.", "item": [{"name": "Get Investment Criteria", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/investment-criteria?entityType=all&dealSizeMin=0&dealSizeMax=1000", "host": ["{{baseUrl}}"], "path": ["api", "investment-criteria"], "query": [{"key": "entityType", "value": "all"}, {"key": "dealSizeMin", "value": "0"}, {"key": "dealSizeMax", "value": "1000"}, {"key": "searchTerm", "value": "", "disabled": true}, {"key": "capitalTypes", "value": "", "disabled": true}, {"key": "propertyTypes", "value": "", "disabled": true}]}, "description": "Get investment criteria with filtering"}}, {"name": "Get Investment Criteria Mappings", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/investment-criteria/mappings", "description": "Get investment criteria mappings"}}, {"name": "Get Investment Criteria Filters", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/investment-criteria/filters", "description": "Get available investment criteria filters"}}, {"name": "Get Investment Criteria Options", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/investment-criteria/options", "description": "Get investment criteria options"}}]}, {"name": "Smartlead", "description": "Complete integration with Smartlead email automation platform including campaign management, lead synchronization, message tracking, webhook processing, and analytics. Automates email outreach workflows and manages prospect engagement.", "item": [{"name": "config", "item": [{"name": "Get Smartlead Configuration", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/smartlead/config", "description": "Get Smartlead configuration status"}}]}, {"name": "campaigns", "item": [{"name": "Get All Campaigns", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/smartlead/campaigns", "description": "List all available campaigns"}}, {"name": "Get Campaign by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/smartlead/campaigns/{{campaignId}}?api_key={{smartleadApiKey}}", "host": ["{{baseUrl}}"], "path": ["api", "smartlead", "campaigns", "{{campaignId}}"], "query": [{"key": "api_key", "value": "{{smartleadApiKey}}"}]}, "description": "Get details of a specific campaign"}}, {"name": "Get Campaign Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/smartlead/campaigns/{{campaignId}}/stats", "host": ["{{baseUrl}}"], "path": ["api", "smartlead", "campaigns", "{{campaignId}}", "stats"]}, "description": "Get comprehensive stats for a campaign"}}, {"name": "Add Lead to Campaign", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"lead_list\": [\n    {\n      \"email\": \"<EMAIL>\",\n      \"first_name\": \"<PERSON>\",\n      \"last_name\": \"<PERSON><PERSON>\",\n      \"custom_fields\": {\n        \"subject\": \"Real Estate Opportunity\",\n        \"html_body\": \"Dear <PERSON>,\\n\\nI wanted to reach out regarding...\"\n      }\n    }\n  ]\n}"}, "url": "{{baseUrl}}/api/smartlead/campaigns/{{campaignId}}/leads", "description": "Add leads to a specific campaign"}}, {"name": "Get Campaign Leads", "request": {"method": "GET", "header": [], "url": {"raw": "{{smartleadBaseUrl}}campaigns/{{campaignId}}/leads?api_key={{smartleadApiKey}}", "host": ["{{smartleadBaseUrl}}"], "path": ["campaigns", "{{campaignId}}", "leads"], "query": [{"key": "api_key", "value": "{{smartleadApiKey}}"}]}, "description": "Get all leads for a campaign (Direct Smartlead API)"}}, {"name": "Get Message History", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/smartlead/campaigns/{{campaignId}}/leads/{{leadId}}/message-history", "description": "Retrieve message history for a lead in a campaign"}}]}, {"name": "contacts", "item": [{"name": "Sync Contact to Smartlead", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"campaign_id\": \"{{campaignId}}\"\n}"}, "url": "{{baseUrl}}/api/smartlead/contacts/{{contactId}}/sync", "description": "Sync a contact to Smartlead"}}, {"name": "Batch Sync Contacts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contactIds\": [123, 456, 789],\n  \"campaignId\": \"{{campaignId}}\"\n}"}, "url": "{{baseUrl}}/api/smartlead/contacts/batch-sync", "description": "Sync multiple contacts to Smartlead"}}, {"name": "Get Contact's Lead ID", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/smartlead/contacts/{{contactId}}/lead-id", "description": "Get the Smartlead lead ID for a contact"}}, {"name": "Get Contact Messages", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/smartlead/contacts/{{contactId}}/messages?campaign_id={{campaignId}}", "host": ["{{baseUrl}}"], "path": ["api", "smartlead", "contacts", "{{contactId}}", "messages"], "query": [{"key": "campaign_id", "value": "{{campaignId}}"}]}, "description": "Get campaign messages for a contact"}}, {"name": "Create Contact Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"campaign_id\": \"{{campaignId}}\",\n  \"sequence_number\": 1,\n  \"subject\": \"Investment Opportunity\",\n  \"body\": \"Dear {{first_name}},\\n\\nI'm reaching out regarding...\"\n}"}, "url": "{{baseUrl}}/api/smartlead/contacts/{{contactId}}/messages", "description": "Create a new message for a contact"}}, {"name": "Update Contact Message", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"campaign_id\": \"{{campaignId}}\",\n  \"sequence_number\": 1,\n  \"subject\": \"Updated: Investment Opportunity\",\n  \"body\": \"Dear {{first_name}},\\n\\nI'm reaching out regarding...\"\n}"}, "url": "{{baseUrl}}/api/smartlead/contacts/{{contactId}}/messages/{{messageId}}", "description": "Update an existing message"}}, {"name": "Get Contact Threads", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/smartlead/contacts/{{contactId}}/threads", "description": "Get email threads for a contact"}}]}, {"name": "stats", "item": [{"name": "Get Smartlead Stats", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/smartlead/stats", "description": "Get Smartlead statistics"}}]}, {"name": "webhook", "item": [{"name": "Process Smartlead Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"event\": \"lead_replied\",\n  \"lead_id\": \"SL12345\",\n  \"campaign_id\": \"{{campaignId}}\"\n}"}, "url": "{{baseUrl}}/api/smartlead/webhook", "description": "Handle Smartlead webhook events"}}]}]}, {"name": "Database", "description": "Direct database access APIs for exploring table structures, querying data, and performing database operations. Provides administrative tools for schema inspection, data exploration, and system maintenance with pagination support.", "item": [{"name": "Get All Tables", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/db/tables", "description": "Get list of all database tables"}}, {"name": "Get Table Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/db/data?table={{tableName}}&page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "db", "data"], "query": [{"key": "table", "value": "{{tableName}}"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get data from a specific table"}}]}, {"name": "Data Quality", "description": "Data quality monitoring and reporting system that tracks data integrity, identifies issues, analyzes trends, and provides metrics for data health assessment. Ensures data accuracy and completeness across all systems.", "item": [{"name": "Get Data Quality Status", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/data-quality/status", "description": "Get data quality metrics and status"}}, {"name": "Get Data Quality Issues", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/data-quality/issues", "description": "Get identified data quality issues"}}, {"name": "Get Data Quality Trends", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/data-quality/trends", "description": "Get data quality trends over time"}}, {"name": "Get Data Quality Metrics", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/data-quality/metrics", "description": "Get detailed data quality metrics"}}, {"name": "Get Data Quality Reports", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/data-quality/reports", "description": "Get data quality reports"}}]}, {"name": "Mapping Tables", "description": "Centralized mapping and normalization system that manages data standardization, taxonomy hierarchies, and value mappings across different data sources. Essential for data consistency and automated categorization.", "item": [{"name": "Get All Mapping Tables", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/mapping-tables?type=&active=true", "host": ["{{baseUrl}}"], "path": ["api", "mapping-tables"], "query": [{"key": "type", "value": ""}, {"key": "active", "value": "true"}]}, "description": "Get mapping table data with filtering"}}, {"name": "Get Mapping Table by Name", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/mapping-tables/{{tableName}}", "description": "Get data from specific mapping table"}}, {"name": "Get Mapping Types", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/mapping-tables/types", "description": "Get all mapping types with counts"}}, {"name": "Get Mapping Type Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/mapping-tables/types?type={{mappingType}}", "host": ["{{baseUrl}}"], "path": ["api", "mapping-tables", "types"], "query": [{"key": "type", "value": "{{mappingType}}"}]}, "description": "Get hierarchical data for specific mapping type"}}]}, {"name": "Jobs & Queue", "description": "Job queue management system using BullMQ for background processing, task scheduling, and workflow orchestration. Monitors job status, provides statistics, and manages processing queues for system operations.", "item": [{"name": "Get Recent Jobs", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/jobs?limit=10&offset=0&include_stats=true", "host": ["{{baseUrl}}"], "path": ["api", "jobs"], "query": [{"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}, {"key": "include_stats", "value": "true"}, {"key": "created_by", "value": "", "disabled": true}]}, "description": "Get recent jobs with optional stats"}}, {"name": "Get Job by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/jobs?job_id={{jobId}}", "host": ["{{baseUrl}}"], "path": ["api", "jobs"], "query": [{"key": "job_id", "value": "{{jobId}}"}]}, "description": "Get specific job status"}}, {"name": "Get Queue Stats", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"get_stats\"\n}"}, "url": "{{baseUrl}}/api/jobs", "description": "Get job queue statistics"}}, {"name": "Get Job Queue Status", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/jobs/status", "description": "Check job queue system status"}}]}, {"name": "Messages & Threads", "description": "Email communication management system that tracks message history, conversation threads, contact interactions, and email campaign responses. Provides comprehensive communication audit trail and engagement analytics.", "item": [{"name": "Get All Messages", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/messages?contact_id={{contactId}}", "host": ["{{baseUrl}}"], "path": ["api", "messages"], "query": [{"key": "contact_id", "value": "{{contactId}}"}]}, "description": "Get messages for a contact"}}, {"name": "Get Message by ID", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/messages/{{messageId}}", "description": "Get a specific message by ID"}}, {"name": "Get All Threads", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/threads?page=1&limit=20&sort=updated_at&direction=desc", "host": ["{{baseUrl}}"], "path": ["api", "threads"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "sort", "value": "updated_at"}, {"key": "direction", "value": "desc"}, {"key": "search", "value": "", "disabled": true}, {"key": "contact_id", "value": "", "disabled": true}, {"key": "contact_email", "value": "", "disabled": true}, {"key": "status", "value": "", "disabled": true}]}, "description": "Get conversation threads with filtering"}}, {"name": "Get Thread by ID", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/threads/{{threadId}}", "description": "Get a specific thread by ID"}}]}, {"name": "Campaigns", "description": "Email campaign management system for creating, executing, and tracking marketing campaigns. Features template management, recipient targeting, performance metrics, A/B testing, and automated follow-up sequences.", "item": [{"name": "Get All Email Campaigns", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/campaigns", "description": "Get all email campaigns"}}, {"name": "Create Email Campaign", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"selectedSource\": \"all\",\n  \"subject\": \"Investment Opportunity\",\n  \"body\": \"Dear {{first_name}},\\n\\nI am writing to...\",\n  \"scheduledTime\": null\n}"}, "url": "{{baseUrl}}/api/email-campaign", "description": "Create and send an email campaign"}}, {"name": "Get Campaign Metrics", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/campaigns/metrics", "description": "Get campaign performance metrics"}}, {"name": "Test Campaign", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"testEmail\": \"<EMAIL>\",\n  \"subject\": \"Test Email\",\n  \"body\": \"This is a test email.\"\n}"}, "url": "{{baseUrl}}/api/campaigns/test", "description": "Send a test campaign email"}}]}, {"name": "Scraping", "description": "Advanced web scraping orchestration system that automates data collection from various sources including news sites (Bisnow, TheRealDeal, GlobeSt, PincusCo), company websites, and industry publications. Features ScraperManager with browser automation, login capabilities, and comprehensive scraping workflows.", "item": [{"name": "Get Scraping API Info", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/scraping", "description": "Get scraping API information and supported endpoints"}}, {"name": "Get Scraping Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/scraping?action=status", "host": ["{{baseUrl}}"], "path": ["api", "scraping"], "query": [{"key": "action", "value": "status"}]}, "description": "Get status of active scraping operations"}}, {"name": "Start Legacy Scraping Operation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"scraping\",\n  \"siteName\": \"bisnow\",\n  \"maxPages\": 30,\n  \"tryLogin\": true\n}"}, "url": "{{baseUrl}}/api/scraping", "description": "Start a legacy scraping operation using ScrapingProcessor"}}, {"name": "Start News Fetching Operation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"fetching\",\n  \"maxPages\": 30,\n  \"tryLogin\": true\n}"}, "url": "{{baseUrl}}/api/scraping", "description": "Start a news fetching operation using NewsFetchingProcessor"}}, {"name": "Start Single Site Scraper (Manager)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"manager\",\n  \"siteName\": \"{{scrapingSite}}\",\n  \"tryLogin\": true,\n  \"maxPages\": 30\n}"}, "url": "{{baseUrl}}/api/scraping", "description": "Start ScraperManager for a specific site (bisnow, therealdeal, globest, pincus)"}}, {"name": "Start All Sites <PERSON>er (Manager)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"manager\",\n  \"tryLogin\": true,\n  \"maxPages\": 30\n}"}, "url": "{{baseUrl}}/api/scraping", "description": "Start ScraperManager for all supported sites (bisnow, therealdeal, globest, pincus)"}}, {"name": "Start Bisnow Scraper", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"manager\",\n  \"siteName\": \"bisnow\",\n  \"tryLogin\": true,\n  \"maxPages\": 30\n}"}, "url": "{{baseUrl}}/api/scraping", "description": "Start ScraperManager specifically for Bisnow.com"}}, {"name": "Start The<PERSON><PERSON><PERSON><PERSON>er", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"manager\",\n  \"siteName\": \"therealdeal\",\n  \"tryLogin\": true,\n  \"maxPages\": 30\n}"}, "url": "{{baseUrl}}/api/scraping", "description": "Start ScraperManager specifically for TheRealDeal.com"}}, {"name": "Start GlobeSt Scraper", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"manager\",\n  \"siteName\": \"globest\",\n  \"tryLogin\": true,\n  \"maxPages\": 30\n}"}, "url": "{{baseUrl}}/api/scraping", "description": "Start ScraperManager specifically for GlobeSt.com"}}, {"name": "<PERSON> <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"manager\",\n  \"siteName\": \"pincus\",\n  \"tryLogin\": true,\n  \"maxPages\": 30\n}"}, "url": "{{baseUrl}}/api/scraping", "description": "Start ScraperManager specifically for PincusCo.com"}}, {"name": "Stop Scraping Operation", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/scraping?id={{scrapingId}}", "host": ["{{baseUrl}}"], "path": ["api", "scraping"], "query": [{"key": "id", "value": "{{scrapingId}}"}]}, "description": "Stop an active scraping operation by operation ID"}}]}, {"name": "Data Cleaning", "description": "Data normalization and cleaning system that standardizes investment criteria fields, property types, and other categorical data. Maintains clean value mappings and ensures data consistency across all sources.", "item": [{"name": "Get Investment Criteria Fields", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/data-cleaning", "description": "Get investment criteria fields for data cleaning"}}, {"name": "Get Clean Values", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/data-cleaning/clean-values", "description": "Get all clean values for data normalization"}}, {"name": "Add Clean Value", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"field\": \"investment_criteria_property_type\",\n  \"value\": \"Office\"\n}"}, "url": "{{baseUrl}}/api/data-cleaning/clean-values", "description": "Add a new clean value for data normalization"}}]}, {"name": "Matching", "description": "Intelligent matching system that connects deals with interested investors based on investment criteria, deal characteristics, and preferences. Uses algorithms to identify high-potential matches and optimize investor outreach targeting.", "item": [{"name": "Get Matching Data", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/matching", "description": "Get data matching results"}}, {"name": "Find Matching Deals for Contact", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/matching/deals-for-contact/{{contactId}}", "description": "Find deals matching a contact's criteria"}}, {"name": "Match Contacts to Deal", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/matching/contacts-for-deal/{{dealId}}", "description": "Find contacts matching a deal's criteria"}}, {"name": "Get Matching Companies", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/matching/companies", "description": "Get company matching results"}}]}, {"name": "Central Mappings", "description": "Core mapping system that manages relationships between capital positions, loan types, property types, and other fundamental business taxonomies. Provides centralized reference data for system-wide consistency.", "item": [{"name": "Get Capital Position Loan Types", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/central-mappings/capital-position-loan-types", "description": "Get capital position to loan type mappings"}}, {"name": "Get Property Types", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/central-mappings/property-types", "description": "Get property type mappings"}}]}, {"name": "People", "description": "People/person data management system that handles individual contact records, professional profiles, and personal information. Supports search, filtering, and data enrichment for comprehensive person database management.", "item": [{"name": "Get People Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/people?page=1&limit=50&search=", "host": ["{{baseUrl}}"], "path": ["api", "people"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "search", "value": ""}]}, "description": "Get people/persons data with pagination"}}]}, {"name": "List44", "description": "Specialized contact database integration that manages List44 data source with contact categorization, search capabilities, and data synchronization. Provides access to curated contact lists and industry-specific databases.", "item": [{"name": "Get List44 Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/list44?page=1&limit=50&search=&contactType=all", "host": ["{{baseUrl}}"], "path": ["api", "list44"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "search", "value": ""}, {"key": "contactType", "value": "all"}]}, "description": "Get List44 contact data"}}]}, {"name": "Process Upload Data", "description": "Data processing pipeline for uploaded files that normalizes, validates, and enriches contact and company data. Handles CSV imports, data transformation, field mapping, and automated data cleansing operations.", "item": [{"name": "Process Uploaded Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"Capital Position\": \"General Partner (GP)\",\n  \"Email\": \"<EMAIL>\",\n  \"First Name\": \"<PERSON>\",\n  \"Last Name\": \"Doe\",\n  \"Company\": \"Example Company\",\n  \"Job Title\": \"Investment Director\",\n  \"Industry\": \"Real Estate Investment\",\n  \"(Investment Criteria) Deal Size\": \"$5M - $50M\",\n  \"(Investment Criteria) Property Type\": \"Multi-Family/Office\"\n}"}, "url": "{{baseUrl}}/api/process-upload-data", "description": "Process and normalize uploaded contact/company data"}}]}, {"name": "News Enrichment", "description": "AI-powered news enrichment system that enhances news articles with location data, entity extraction, and contextual information. Improves deal extraction accuracy and provides additional metadata for better analysis.", "item": [{"name": "Enrich News Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"newsId\": {{newsId}},\n  \"enhanceLocation\": true,\n  \"extractEntities\": true\n}"}, "url": "{{baseUrl}}/api/news-enrichment", "description": "Enrich news articles with additional data"}}]}, {"name": "Conflicts", "description": "Deal conflict detection and resolution system that identifies potential duplicate deals, data inconsistencies, and conflicts requiring manual review. Helps maintain data quality and prevents duplicate processing.", "item": [{"name": "Get Deal Conflicts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/conflicts?status=pending&page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "conflicts"], "query": [{"key": "status", "value": "pending"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get deal conflicts for resolution"}}]}, {"name": "Team", "description": "Team management system that handles user accounts, roles, permissions, and team member information. Supports user authentication, access control, and team collaboration features.", "item": [{"name": "Get Team Members", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/team", "description": "Get team member information"}}]}, {"name": "Webhooks", "description": "Webhook processing system that handles incoming notifications from external services, API callbacks, and real-time data synchronization. Manages integration events and automated data updates.", "item": [{"name": "Process Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"source\": \"external_system\",\n  \"event_type\": \"data_update\",\n  \"payload\": {}\n}"}, "url": "{{baseUrl}}/api/webhooks", "description": "Process incoming webhook data"}}]}, {"name": "Workers", "description": "Background worker management system that monitors service status, worker health, and background process execution. Provides system administration tools for managing distributed processing workers.", "item": [{"name": "Get Worker Status", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/workers", "description": "Get background worker status"}}]}, {"name": "LiveKit Token", "description": "LiveKit integration for real-time communication features including video calls, audio conferencing, and live collaboration. Generates secure access tokens for real-time communication sessions.", "item": [{"name": "Get LiveKit Token", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/livekit-token?room={{roomName}}&identity={{userIdentity}}", "host": ["{{baseUrl}}"], "path": ["api", "livekit-token"], "query": [{"key": "room", "value": "{{roomName}}"}, {"key": "identity", "value": "{{userIdentity}}"}]}, "description": "Get LiveKit access token for real-time communication"}}]}, {"name": "Projections", "description": "Data analytics and forecasting system that provides business projections, trend analysis, and predictive insights based on historical data and market intelligence for strategic decision making.", "item": [{"name": "Get Projections Data", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/projections", "description": "Get data projections and forecasts"}}]}, {"name": "BullBoard (Inactive)", "description": "BullMQ queue monitoring dashboard interface (currently disabled). When active, provides visual queue management, job monitoring, and debugging tools for background processing systems.", "item": [{"name": "BullBoard UI", "request": {"method": "GET", "header": [], "url": "{{baseUrl}}/api/bullboard", "description": "Access BullMQ dashboard (if enabled)"}}]}, {"name": "Test AI Processor (Inactive)", "description": "AI processing testing endpoint (currently disabled). When active, provides testing capabilities for AI models, data extraction algorithms, and machine learning processing pipelines.", "item": [{"name": "Test AI Processing", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"testData\": \"Sample data for AI processing\"\n}"}, "url": "{{baseUrl}}/api/test-ai-processor", "description": "Test AI processing capabilities"}}]}], "variable": [{"key": "baseUrl", "value": "{{SERVER_URL}}", "type": "string", "description": "Base URL for the API - use environment specific value"}, {"key": "SERVER_URL", "value": "https://anax.cloud", "type": "string", "description": "Production server URL"}, {"key": "LOCAL_URL", "value": "http://localhost:3030", "type": "string", "description": "Local development URL"}, {"key": "smartleadApiKey", "value": "{{SMARTLEAD_API_KEY}}", "type": "string", "description": "Smartlead API key from environment"}, {"key": "SMARTLEAD_API_KEY", "value": "9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr", "type": "string", "description": "Smartlead API key"}, {"key": "smartleadBaseUrl", "value": "https://server.smartlead.ai/api/v1/", "type": "string", "description": "Smartlead API base URL"}, {"key": "OPENAI_API_KEY", "value": "********************************************************************************************************************************************************************", "type": "string", "description": "OpenAI API key for AI processing"}, {"key": "PERPLEXITY_API_KEY", "value": "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", "type": "string", "description": "Perplexity API key"}, {"key": "campaignId", "value": "1897921", "type": "string", "description": "De<PERSON><PERSON> Smartlead campaign ID"}, {"key": "contactId", "value": "123", "type": "string", "description": "Sample contact ID for testing"}, {"key": "companyId", "value": "456", "type": "string", "description": "Sample company ID for testing"}, {"key": "dealId", "value": "789", "type": "string", "description": "Sample deal ID for testing"}, {"key": "fileId", "value": "file-123", "type": "string", "description": "Sample file ID for testing"}, {"key": "threadId", "value": "thread-123", "type": "string", "description": "Sample thread ID for testing"}, {"key": "messageId", "value": "msg-123", "type": "string", "description": "Sample message ID for testing"}, {"key": "jobId", "value": "job-123", "type": "string", "description": "Sample job ID for testing"}, {"key": "leadId", "value": "SL12345", "type": "string", "description": "<PERSON><PERSON> Smartlead lead ID"}, {"key": "tableName", "value": "contacts", "type": "string", "description": "Sample table name for database operations"}, {"key": "searchQuery", "value": "investment", "type": "string", "description": "Sample search query"}, {"key": "contactEmail", "value": "<EMAIL>", "type": "string", "description": "<PERSON><PERSON> contact email"}, {"key": "mappingType", "value": "property_type", "type": "string", "description": "Sample mapping type"}, {"key": "entityType", "value": "contact", "type": "string", "description": "Sample entity type"}, {"key": "entityId", "value": "123", "type": "string", "description": "Sample entity ID"}, {"key": "roomName", "value": "meeting-room-1", "type": "string", "description": "Sample LiveKit room name"}, {"key": "userIdentity", "value": "user123", "type": "string", "description": "Sample user identity for LiveKit"}, {"key": "scrapingId", "value": "scrape-123", "type": "string", "description": "Sample scraping operation ID"}, {"key": "scrapingSite", "value": "bisnow", "type": "string", "description": "Target site for scraping (bisnow, therealdeal, globest, pincus)"}, {"key": "newsId", "value": "news-123", "type": "string", "description": "Sample news article ID"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set base URL based on environment", "const env = pm.environment.get('ENVIRONMENT') || 'production';", "", "if (env === 'local') {", "    pm.globals.set('SERVER_URL', pm.variables.get('LOCAL_URL'));", "} else {", "    pm.globals.set('SERVER_URL', pm.variables.get('SERVER_URL'));", "}", "", "console.log('Using environment:', env);", "console.log('Base URL set to:', pm.globals.get('SERVER_URL'));"]}}]}