# Company Schema Comparison Analysis

## Overview
This document compares the current database schema with the new proposed company schema to identify required changes.

## Current Database Structure

### Main Companies Table (`companies`)
**Current Columns:**
- `company_id` (integer, primary key)
- `company_name` (text)
- `company_linkedin` (text)
- `company_address` (text)
- `company_city` (text)
- `company_state` (text)
- `company_zip` (text)
- `company_website` (text)
- `industry` (text)
- `company_phone` (text)
- `founded_year` (integer)
- `company_country` (text)
- `extra_attrs` (jsonb)
- `created_at` (timestamp)
- `updated_at` (timestamp)
- `canonical_handle` (text)
- `processed` (boolean)
- `extracted` (boolean)
- `overview` (jsonb)
- `source` (text)
- `processing_state` (varchar)
- `website_scraping_status` (varchar)
- `website_scraping_date` (timestamp)
- `website_scraping_error` (text)
- `company_overview_status` (varchar)
- `company_overview_date` (timestamp)
- `company_overview_error` (text)
- `last_processed_stage` (varchar)
- `last_processed_at` (timestamp)
- `processing_error_count` (integer)
- `processing_attempts` (json)
- `web_scraping_error` (text)
- `web_scraping_date` (timestamp)
- `conflicts` (jsonb)
- `conflict_status` (text)
- `conflict_created_at` (timestamp)
- `conflict_resolved_at` (timestamp)
- `conflict_source` (text)
- `capital_position` (array)
- `summary` (text)

### Company Extracted Data Table (`company_extracted_data`)
**Current Columns:**
- `id` (bigint, primary key)
- `company_id` (bigint, foreign key)
- `companyname` (varchar)
- `companytype` (varchar)
- `businessmodel` (text)
- `fundsize` (varchar)
- `aum` (varchar)
- `numberofproperties` (integer)
- `headquarters` (varchar)
- `numberofoffices` (integer)
- `foundedyear` (integer)
- `numberofemployees` (varchar)
- `investmentfocus` (jsonb)
- `geographicfocus` (jsonb)
- `recentdeals` (jsonb)
- `mission` (text)
- `approach` (text)
- `targetreturn` (varchar)
- `propertytypes` (jsonb)
- `strategies` (jsonb)
- `assetclasses` (jsonb)
- `valuecreation` (jsonb)
- `targetmarkets` (jsonb)
- `dealsize` (varchar)
- `minimumdealsize` (varchar)
- `maximumdealsize` (varchar)
- `holdperiod` (varchar)
- `riskprofile` (varchar)
- `capitalsources` (jsonb)
- `financialproducts` (jsonb)
- `totaltransactions` (varchar)
- `totalsquarefeet` (varchar)
- `totalunits` (varchar)
- `historicalreturns` (varchar)
- `portfoliovalue` (varchar)
- `partnerships` (jsonb)
- `website` (varchar)
- `linkedin_url` (varchar)
- `mainphone` (varchar)
- `mainemail` (varchar)
- `socialmedia` (jsonb)
- `created_at` (timestamp)
- `updated_at` (timestamp)
- `index_company_id_idx` (integer)
- `investment_criteria_property_types` (jsonb)
- `investment_criteria_property_subcategories` (jsonb)
- `investment_criteria_asset_types` (jsonb)
- `investment_criteria_loan_types` (jsonb)
- `companyindustry` (varchar)
- `capitalposition` (varchar)
- `officelocations` (jsonb)

## New Proposed Schema Analysis

### Columns to ADD to Main Companies Table

#### Basic Information
- `company_type` (string) - Company Type Mapping
- `company_industry` (string) - Industry (rename from current `industry`)
- `business_model` (text) - Description of business model
- `investment_focus` (array<string>) - List of investment focus areas
- `investment_strategy_mission` (text) - Investment strategy mission
- `investment_strategy_approach` (text) - Investment strategy approach

#### Contact Information
- `main_phone` (string) - Main phone (max 100 chars)
- `secondary_phone` (string) - Secondary phone (max 100 chars)
- `main_email` (string) - Main email (max 255 chars)
- `secondary_email` (string) - Secondary email (max 255 chars)
- `twitter` (string) - Twitter URL
- `facebook` (string) - Facebook URL
- `instagram` (string) - Instagram URL
- `youtube` (string) - YouTube URL

#### Address Information
- `headquarters_address` (string) - Address
- `headquarters_city` (string) - City
- `headquarters_state` (string) - States
- `headquarters_zipcode` (string) - Zip Code
- `headquarters_country` (string) - Country
- `additional_address` (string) - Additional address
- `additional_city` (string) - Additional city
- `additional_state` (string) - Additional state
- `additional_zipcode` (string) - Additional zip code
- `additional_country` (string) - Additional country

#### Financial Information
- `fund_size` (integer USD) - Fund size
- `aum` (integer USD) - Assets under management
- `number_of_properties` (integer) - Number of properties
- `number_of_offices` (integer) - Number of offices
- `office_locations` (array<string>) - List of office locations
- `number_of_employees` (integer) - Employee count
- `partnerships` (array<string>) - List of partnerships
- `balance_sheet_strength` (string) - Financial health description
- `funding_sources` (array<string>) - Funding sources
- `recent_capital_raises` (text) - Recent capital raises
- `typical_debt_to_equity_ratio` (float) - Debt-to-equity ratio
- `development_fee_structure` (text) - Fee structure
- `key_equity_partners` (array<string>) - Key equity partners
- `key_debt_partners` (array<string>) - Key debt partners

#### Strategy & Focus
- `market_cycle_positioning` (string) - Market cycle strategy
- `urban_vs_suburban_preference` (string) - Location preference
- `sustainability_esg_focus` (boolean) - ESG focus
- `technology_proptech_adoption` (boolean) - Tech adoption
- `adaptive_reuse_experience` (boolean) - Adaptive reuse experience
- `regulatory_zoning_expertise` (boolean) - Regulatory expertise

#### Investment Vehicle Information
- `investment_vehicle_type` (enum) - Legal structure
- `active_fund_name_series` (string) - Active fund name/series
- `fund_size_active_fund` (integer USD) - Active fund size
- `fundraising_status` (string) - Fundraising status
- `lender_type` (enum) - Lender type
- `annual_loan_volume` (integer USD) - Annual loan volume
- `lending_origin` (string) - Lending origin
- `portfolio_health` (text) - Portfolio health

#### Leadership & Governance
- `board_of_directors` (array<string>) - Board members
- `key_executives` (array<string>) - Key executives
- `founder_background` (text) - Founder background
- `company_history` (text) - Company history

#### Public Company Information
- `stock_ticker_symbol` (string) - Stock ticker
- `stock_exchange` (enum) - Stock exchange
- `market_capitalization` (integer USD) - Market cap
- `annual_revenue` (integer USD) - Annual revenue
- `net_income` (integer USD) - Net income
- `ebitda` (integer USD) - EBITDA
- `profit_margin` (float) - Profit margin
- `credit_rating` (enum) - Credit rating
- `quarterly_earnings_link` (string) - Earnings link

#### Business Information
- `products_services_description` (text) - Products/services
- `target_customer_profile` (text) - Target customers
- `major_competitors` (array<string>) - Competitors
- `market_share_percentage` (float) - Market share
- `unique_selling_proposition` (text) - USP
- `industry_awards_recognitions` (array<string>) - Awards

#### Corporate Structure
- `corporate_structure` (enum) - Legal structure
- `parent_company` (string) - Parent company
- `subsidiaries` (array<string>) - Subsidiaries

#### Data Quality & Tracking
- `recent_news_sentiment` (enum) - News sentiment
- `data_source` (string) - Data source
- `last_updated_timestamp` (datetime) - Last updated
- `data_confidence_score` (float) - Confidence score

#### Relationship Management
- `dry_powder` (numeric) - Available capital
- `annual_deployment_target` (numeric) - Deployment target
- `transactions_completed_last_12m` (integer) - Recent transactions
- `internal_relationship_manager` (text) - Relationship manager
- `last_contact_date` (date) - Last contact
- `pipeline_status` (enum) - Pipeline status
- `role_in_previous_deal` (enum) - Previous role
- `total_transaction_volume_ytd` (numeric) - YTD volume
- `deal_count_ytd` (integer) - YTD deal count
- `average_deal_size` (numeric) - Average deal size
- `portfolio_size_sqft` (integer) - Portfolio size
- `portfolio_asset_count` (integer) - Asset count

### Columns to MODIFY in Main Companies Table

#### Rename Existing Columns
- `company_phone` → `main_phone` (and add `secondary_phone`)
- `company_website` → `website`
- `company_linkedin` → `linkedin`
- `company_address` → `headquarters_address`
- `company_city` → `headquarters_city`
- `company_state` → `headquarters_state`
- `company_zip` → `headquarters_zipcode`
- `company_country` → `headquarters_country`
- `industry` → `company_industry`

#### Data Type Changes
- `fund_size` should be integer USD instead of varchar
- `aum` should be integer USD instead of varchar
- `number_of_employees` should be integer instead of varchar

### Columns to DELETE from Main Companies Table

#### Processing/System Columns (Keep in separate table)
- `extra_attrs` (move to processing table)
- `processed` (move to processing table)
- `extracted` (move to processing table)
- `overview` (move to processing table)
- `processing_state` (move to processing table)
- `website_scraping_status` (move to processing table)
- `website_scraping_date` (move to processing table)
- `website_scraping_error` (move to processing table)
- `company_overview_status` (move to processing table)
- `company_overview_date` (move to processing table)
- `company_overview_error` (move to processing table)
- `last_processed_stage` (move to processing table)
- `last_processed_at` (move to processing table)
- `processing_error_count` (move to processing table)
- `processing_attempts` (move to processing table)
- `web_scraping_error` (move to processing table)
- `web_scraping_date` (move to processing table)

#### Conflict Management (Keep in separate table)
- `conflicts` (move to conflicts table)
- `conflict_status` (move to conflicts table)
- `conflict_created_at` (move to conflicts table)
- `conflict_resolved_at` (move to conflicts table)
- `conflict_source` (move to conflicts table)

### Company Extracted Data Table Changes

#### Columns to MIGRATE to Main Table
- `companytype` → `company_type`
- `businessmodel` → `business_model`
- `fundsize` → `fund_size` (with data type change)
- `aum` → `aum` (with data type change)
- `numberofproperties` → `number_of_properties`
- `numberofoffices` → `number_of_offices`
- `foundedyear` → `founded_year`
- `numberofemployees` → `number_of_employees` (with data type change)
- `investmentfocus` → `investment_focus`
- `mission` → `investment_strategy_mission`
- `approach` → `investment_strategy_approach`
- `partnerships` → `partnerships`
- `website` → `website`
- `linkedin_url` → `linkedin`
- `mainphone` → `main_phone`
- `mainemail` → `main_email`
- `socialmedia` → (split into individual social media columns)
- `companyindustry` → `company_industry`
- `capitalposition` → `capital_position`
- `officelocations` → `office_locations`

#### Columns to KEEP in Extracted Data Table
- `geographicfocus` (keep as extracted data)
- `recentdeals` (keep as extracted data)
- `targetreturn` (keep as extracted data)
- `propertytypes` (keep as extracted data)
- `strategies` (keep as extracted data)
- `assetclasses` (keep as extracted data)
- `valuecreation` (keep as extracted data)
- `targetmarkets` (keep as extracted data)
- `dealsize` (keep as extracted data)
- `minimumdealsize` (keep as extracted data)
- `maximumdealsize` (keep as extracted data)
- `holdperiod` (keep as extracted data)
- `riskprofile` (keep as extracted data)
- `capitalsources` (keep as extracted data)
- `financialproducts` (keep as extracted data)
- `totaltransactions` (keep as extracted data)
- `totalsquarefeet` (keep as extracted data)
- `totalunits` (keep as extracted data)
- `historicalreturns` (keep as extracted data)
- `portfoliovalue` (keep as extracted data)
- Investment criteria columns (keep as extracted data)

## Recommended Migration Strategy

### Phase 1: Schema Preparation
1. Create new columns in the main `companies` table
2. Add appropriate constraints and indexes
3. Create new tables for processing state and conflicts

### Phase 2: Data Migration
1. Migrate data from `company_extracted_data` to main table
2. Transform data types where needed
3. Split social media data into individual columns

### Phase 3: Cleanup
1. Remove old columns from main table
2. Update foreign key relationships
3. Update application code to use new schema

### Phase 4: Validation
1. Verify data integrity
2. Test application functionality
3. Update documentation

## SQL Migration Scripts Needed

1. **Add new columns to companies table**
2. **Create processing state table**
3. **Create conflicts table**
4. **Migrate data from company_extracted_data**
5. **Update foreign key relationships**
6. **Remove old columns**
7. **Add constraints and indexes**

## Impact Assessment

### Application Code Changes Required
- Update all API endpoints that read/write company data
- Update frontend components that display company information
- Update data processing scripts
- Update search and filtering logic

### Data Migration Considerations
- Preserve existing data during migration
- Handle data type conversions carefully
- Maintain referential integrity
- Plan for rollback strategy

### Performance Considerations
- Index new columns appropriately
- Consider partitioning for large datasets
- Optimize queries for new schema
- Monitor performance after migration
