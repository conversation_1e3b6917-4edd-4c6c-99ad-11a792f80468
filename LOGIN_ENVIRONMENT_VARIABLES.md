# Login Environment Variables Configuration

This document outlines the environment variables required for automated login functionality across different news sites in the Anax Dashboard application.

## Overview

The application supports automated login for several news sites to access premium content and bypass login walls. The login system is integrated into both the web scrapers and the NewsHTMLFetcher processor.

## Required Environment Variables

Add these variables to your `.env` file or your deployment environment:

### Bisnow
Required for accessing Bisnow content:
```bash
# Bisnow login credentials
BISNOW_EMAIL=<EMAIL>
BISNOW_PASSWORD=your_bisnow_password
```

### The Real Deal
Required for accessing The Real Deal content:
```bash
# The Real Deal login credentials
THEREALDEAL_EMAIL=<EMAIL>
THEREALDEAL_PASSWORD=your_therealdeal_password
```

### GlobeSt
Required for accessing GlobeSt content:
```bash
# GlobeSt login credentials
GLOBEST_EMAIL=<EMAIL>
GLOBEST_PASSWORD=your_globest_password
```

### PincusCo
Required for accessing PincusCo content:
```bash
# PincusCo login credentials
PINCUSCO_EMAIL=<EMAIL>
PINCUSCO_PASSWORD=your_pincusco_password
```

## Complete Environment Configuration

Here's a complete example of all login-related environment variables:

```bash
# ================================
# NEWS SITE LOGIN CREDENTIALS
# ================================

# Bisnow
BISNOW_EMAIL=<EMAIL>
BISNOW_PASSWORD=your_bisnow_password

# The Real Deal
THEREALDEAL_EMAIL=<EMAIL>
THEREALDEAL_PASSWORD=your_therealdeal_password

# GlobeSt
GLOBEST_EMAIL=<EMAIL>
GLOBEST_PASSWORD=your_globest_password

# PincusCo
PINCUSCO_EMAIL=<EMAIL>
PINCUSCO_PASSWORD=your_pincusco_password

# ================================
# BROWSER SETTINGS (Optional)
# ================================

# Set to 'false' for debugging with visible browser
HEADLESS=true

# Log level for debugging (debug, info, warn, error)
LOG_LEVEL=info
```

## How Login Works

### Automatic Detection
The system automatically detects which site a URL belongs to and attempts to log in using the appropriate credentials:

1. **URL Detection**: When processing a news URL, the system detects which site it belongs to based on the domain.
2. **Login Check**: Before fetching content, the system checks if login is required and if the user is already logged in.
3. **Automatic Login**: If login is required and credentials are available, the system automatically logs in.
4. **Content Fetching**: After successful login, the system fetches the protected content.

### Supported Sites
The login system currently supports:
- **Bisnow** (`bisnow.com`)
- **The Real Deal** (`therealdeal.com`)
- **GlobeSt** (`globest.com`)
- **PincusCo** (`pincusco.com`)

### Login Wall Detection
The system automatically detects login walls and paywalls using common indicators:
- "Please log in"
- "Sign in to continue"
- "Subscription required"
- "Premium content"
- And many more...

## Security Best Practices

### Environment Variables
- Never commit credentials to version control
- Use environment-specific `.env` files (`.env.local`, `.env.production`)
- Store production credentials in secure environment variable management systems

### Account Management
- Use dedicated accounts for scraping when possible
- Avoid using personal accounts
- Consider using business/developer accounts where available
- Regularly rotate credentials

### Rate Limiting
- The system includes built-in rate limiting to avoid being blocked
- Login attempts are limited to prevent account lockout
- Browser sessions are managed to maintain login state

## Troubleshooting

### Common Issues

#### Login Failures
```bash
# Check if credentials are properly set
echo $BISNOW_EMAIL
echo $BISNOW_PASSWORD

# For PincusCo
echo $PINCUSCO_EMAIL
echo $PINCUSCO_PASSWORD
```

#### Account Lockouts
- Wait 15-30 minutes before retrying
- Check if the account is locked by logging in manually
- Consider using different credentials

#### Site Changes
- Login selectors may change when sites are updated
- Check the `LoginUtility.ts` file for site-specific configurations
- Update selectors in the configuration if needed

### Debug Mode
Enable debug mode to see detailed login process:
```bash
# Enable debug logging
LOG_LEVEL=debug

# Run with visible browser (useful for debugging)
HEADLESS=false
```

### Testing Login
You can test login functionality using the processor:
```bash
# Test NewsHTMLFetcher with a specific news ID
npx tsx scripts/test-news-fetcher.ts --id 123

# Test with multiple IDs
npx tsx scripts/test-news-fetcher.ts --ids 123,456,789
```

## Adding New Sites

To add support for a new news site:

1. **Add Environment Variables**:
   ```bash
   NEWSITE_EMAIL=<EMAIL>
   NEWSITE_PASSWORD=password
   ```

2. **Update LoginUtility Configuration**:
   Edit `src/lib/scrapers/LoginUtility.ts` and add the new site configuration in the `initializeDefaultConfigs()` method.

3. **Add Site-Specific Login Method**:
   If needed, add a custom login method in the `LoginUtility` class.

4. **Test the Integration**:
   Test with a sample URL from the new site to ensure login works correctly.

## Monitoring and Maintenance

### Regular Checks
- Monitor login success rates in application logs
- Check for account lockouts or suspensions
- Verify that content is being fetched successfully

### Updates
- Keep site configurations updated when sites change their login process
- Update selectors and URLs as needed
- Test login functionality after any site updates

### Backup Credentials
- Maintain backup accounts for critical sites
- Document account details and recovery methods
- Set up alerts for failed login attempts

## Related Files

- `src/lib/scrapers/LoginUtility.ts` - Main login utility class
- `src/lib/processors/NewsHTMLFetcherProcessor.ts` - News HTML fetcher with login integration
- `src/lib/scrapers/ScraperManager.ts` - Scraper manager with login configurations
- `src/lib/scrapers/BisnowScraper.ts` - Bisnow-specific scraper
- `src/lib/scrapers/TheRealDealScraper.ts` - The Real Deal scraper
- `src/lib/scrapers/GlobestScraper.ts` - GlobeSt scraper
- `src/lib/scrapers/PincusScraper.ts` - PincusCo scraper 