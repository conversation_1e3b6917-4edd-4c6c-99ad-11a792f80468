#!/bin/bash

# Setup script for configuring cron job to run the scraper every 4 hours
# Usage: ./setup-cron.sh

echo "🔧 Setting up Anax Scraper Cron Job..."

# Get the current directory (where the project is located)
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "📁 Project directory: $PROJECT_DIR"

# Create the cron job command
CRON_COMMAND="0 */4 * * * cd $PROJECT_DIR && npx tsx scripts/cron-scraper.ts >> $PROJECT_DIR/logs/scraper-cron.log 2>&1"

echo "⏰ Cron command: $CRON_COMMAND"

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_DIR/logs"

# Check if cron job already exists
if crontab -l 2>/dev/null | grep -q "cron-scraper.ts"; then
    echo "⚠️  Cron job already exists. Removing old one..."
    # Remove existing cron job
    crontab -l 2>/dev/null | grep -v "cron-scraper.ts" | crontab -
fi

# Add the new cron job
echo "➕ Adding new cron job..."
(crontab -l 2>/dev/null; echo "$CRON_COMMAND") | crontab -

# Verify the cron job was added
echo "✅ Cron job added successfully!"
echo ""
echo "📋 Current cron jobs:"
crontab -l | grep "cron-scraper"

echo ""
echo "🎯 The scraper will now run every 4 hours at:"
echo "   - 12:00 AM (midnight)"
echo "   - 4:00 AM"
echo "   - 8:00 AM"
echo "   - 12:00 PM (noon)"
echo "   - 4:00 PM"
echo "   - 8:00 PM"

echo ""
echo "📄 Logs will be saved to: $PROJECT_DIR/logs/scraper-cron.log"
echo ""
echo "🚀 To manually run the scraper now, use:"
echo "   npx tsx scripts/cron-scraper.ts"
echo ""
echo "🗑️  To remove the cron job, run:"
echo "   crontab -l | grep -v 'cron-scraper.ts' | crontab -"

echo ""
echo "✨ Setup complete!" 