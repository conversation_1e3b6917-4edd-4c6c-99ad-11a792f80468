# Unused APIs and Components Analysis

## Executive Summary

This analysis identifies unused APIs and components in the Anax Dashboard application to help with code cleanup and maintenance.

### Key Findings:
- **Total API Endpoints**: 215
- **Total Components**: 239
- **Used API Endpoints**: 85 (with fetch calls)
- **Unused API Endpoints**: 130 (estimated)
- **Used Components**: 876 (import statements found)
- **Unused Components**: ~50 (estimated)

## 1. Most Frequently Used API Endpoints

The following API endpoints are most frequently used in the frontend:

- `/api/contacts/` (35 calls)
- `/api/mapping-tables/` (18 calls)
- `/api/deals/` (17 calls)
- `/api/smartlead/` (14 calls)
- `/api/processing/` (14 calls)
- `/api/companies/` (14 calls)
- `/api/dashboard/configuration/` (12 calls)
- `/api/investment-criteria/` (11 calls)
- `/api/v2/deals/${dealId}/` (8 calls)
- `/api/smartlead/contacts/[contactId]/sync/` (8 calls)
- `/api/smartlead/campaigns/${campaignId}/` (8 calls)
- `/api/deals/${dealId}/` (8 calls)
- `/api/investors/` (7 calls)
- `/api/deal-news/` (7 calls)
- `/api/messages/` (6 calls)

## 2. Unused API Endpoints

The following API endpoints appear to be unused (no fetch calls found in the frontend):

### Campaigns
- `/api/campaigns/metrics`
- `/api/campaigns/test`

### Central Mappings
- `/api/central-mappings/capital-position`
- `/api/central-mappings/capital-position-loan-types`
- `/api/central-mappings/property-type`
- `/api/central-mappings/property-types`
- `/api/central-mappings/strategy`
- `/api/central-mappings/subproperty-type`

### Companies
- `/api/companies/filter-options`
- `/api/companies/filter-options-v2`
- `/api/companies/filters`
- `/api/companies/filters/options`
- `/api/companies/overview-filters`
- `/api/companies/sources`
- `/api/companies/unified-filters`
- `/api/companies/unified-filters-v2`

### Contacts
- `/api/contacts/[contactId]/attach-investment-criteria`
- `/api/contacts/[contactId]/extracted-data`
- `/api/contacts/[contactId]/transcripts`
- `/api/contacts/filter-options`
- `/api/contacts/filter-options-v2`
- `/api/contacts/overview`
- `/api/contacts/overview/[contactId]`
- `/api/contacts/search`
- `/api/contacts/sources/count`
- `/api/contacts/unified-filters`
- `/api/contacts/unified-filters-v2`

### Dashboard Configuration
- `/api/dashboard/configuration/capital-position-weights`
- `/api/dashboard/configuration/field-weights`
- `/api/dashboard/configuration/fireflies`
- `/api/dashboard/configuration/fireflies/transcripts`
- `/api/dashboard/configuration/gmail`
- `/api/dashboard/configuration/gmail/messages`
- `/api/dashboard/configuration/gmail/threads`
- `/api/dashboard/configuration/gmail/threads-by-email`

### Data Quality
- `/api/data-quality/deals`
- `/api/data-quality/status`
- `/api/data-quality/contact-investment-criteria`

### Deal News
- `/api/deal-news/[id]`
- `/api/deal-news/stats`
- `/api/deal-news/extracted-deals/[id]`

### Deals
- `/api/deals/[id]/quality`
- `/api/deals/conflicts`
- `/api/deals/conflicts/resolve`
- `/api/deals/filters`
- `/api/deals/job-conflicts`
- `/api/deals/onboard`
- `/api/deals/requirements`
- `/api/deals/resolve-conflicts`
- `/api/deals/search`
- `/api/deals/test`
- `/api/deals/unified-filters`
- `/api/deals/upload-simple`

### Files
- `/api/files/[fileId]`
- `/api/files/[fileId]/download`
- `/api/files/entity/[type]/[id]`
- `/api/files/relationships`
- `/api/files/relationships/[relationshipId]`

### Industries
- `/api/industries/search`

### Investment Criteria
- `/api/investment-criteria/central-filter-options`
- `/api/investment-criteria/filter-options`
- `/api/investment-criteria/filters`

### Jobs
- `/api/jobs/recent`
- `/api/jobs/status`

### List44
-> `/api/list44`
-> `/api/list44/[uuid]`

### LiveKit
- `/api/livekit-token`

### Mapping Tables
- `/api/mapping-tables/bulk`
- `/api/mapping-tables/capital-position`
- `/api/mapping-tables/hierarchical`
- `/api/mapping-tables/upload`

### Matching
- `/api/matching/contacts-for-deal/[dealId]`
- `/api/matching/deals-for-contact/[contactId]`
- `/api/matching/news-for-company/[companyId]`
- `/api/matching/news-for-contact/[contactId]`
- `/api/matching/verify`

### Matching V2
- `/api/matching-v2/capital-position-weights`
- `/api/matching-v2/capital-position-weights/[capitalPosition]`
- `/api/matching-v2/contacts-for-deal/[dealId]`
- `/api/matching-v2/deals-for-contact/[contactId]`

### Messages
- `/api/messages/[messageId]`

### People
- `/api/people`

### Processing
- `/api/processing/analytics`
- `/api/processing/stats`
- `/api/processing/timeline`

### Projections
- `/api/projections/assumptions`
- `/api/projections/assumptions/initialize`
- `/api/projections/assumptions/update`

### Scraping
- `/api/scraping`

### Smartlead
- `/api/smartlead/clients`
- `/api/smartlead/email-accounts/reconnect`
- `/api/smartlead/stats`
- `/api/smartlead/webhook`

### Team
- `/api/team/threads`
- `/api/team/threads/[threadId]/messages`

### Threads
- `/api/threads`
- `/api/threads/[threadId]`

### V2 Deals
- `/api/v2/deals`
- `/api/v2/deals/[id]`
- `/api/v2/deals/[id]/jobs`
- `/api/v2/deals/[id]/nsf-fields`
- `/api/v2/deals/[id]/run-requirement-extraction`
- `/api/v2/deals/quality`
- `/api/v2/deals/search`
- `/api/v2/deals/upload`

### Webhooks
- `/api/webhooks/brevo`

### Workers
- `/api/workers/process-uploads`

## 3. Unused Components

The following components appear to be unused (no imports found):

### UI Components
- `ui/toaster`
- `ui/grafana-time-picker`
- `ui/mermaid`
- `ui/date-time-picker`
- `ui/toast`
- `ui/dropdown-menu`
- `ui/form`

### Conflict Components
- `conflicts/ConflictResolutionModal`

### Dashboard Components
- `dashboard/pitch/PitchView`
- `dashboard/pitch/CREPitchPresentation`
- `dashboard/mapping/CSVUploader`
- `dashboard/OldContacts`
- `dashboard/DataCleaning`
- `dashboard/parker/ParkerView`

### Data Quality Components
- `dashboard/data-quality/NullabilityTable`
- `dashboard/data-quality/CompanyOverviewMetrics`
- `dashboard/data-quality/DataQualityChart`
- `dashboard/data-quality/DataQualityEntity`
- `dashboard/data-quality/ContactEnrichmentMetrics`

### Deal Components
- `dashboard/deals/OverviewTab`

## 4. Recommendations

### High Priority Cleanup
1. **Remove unused API endpoints** that are clearly not needed
2. **Remove unused UI components** that are not imported anywhere
3. **Remove unused dashboard components** that are not part of any active features

### Medium Priority Cleanup
1. **Review API endpoints** that might be used by external systems
2. **Review components** that might be dynamically imported
3. **Check for webhook endpoints** that might be called by external services

### Low Priority Cleanup
1. **Keep API endpoints** that might be used for future features
2. **Keep components** that might be used for testing or development

## 5. Methodology

This analysis was performed by:
1. Scanning all API route files in `src/app/api/`
2. Searching for fetch calls in components and pages
3. Cross-referencing API endpoints with actual usage
4. Scanning all component files in `src/components/`
5. Searching for import statements and JSX usage
6. Cross-referencing components with actual usage

## 6. Limitations

- This analysis only considers direct fetch calls and import statements
- It doesn't account for dynamic imports or runtime usage
- It doesn't consider external API consumers
- It doesn't account for webhook endpoints that might be called by external services
- Some components might be used in ways not detected by this analysis

## 7. Next Steps

1. **Manual Review**: Manually verify each unused item before deletion
2. **Testing**: Ensure removal doesn't break any functionality
3. **Documentation**: Update documentation to reflect removed items
4. **Monitoring**: Monitor for any issues after cleanup
5. **Regular Maintenance**: Schedule regular cleanup reviews

## 8. Conclusion

This analysis reveals significant opportunities for code cleanup in the Anax Dashboard application:

### Key Insights:
- **60% of API endpoints** (130 out of 215) appear to be unused
- **21% of components** (approximately 50 out of 239) appear to be unused
- The most actively used APIs are related to contacts, deals, companies, and smartlead integration
- Many unused endpoints are related to configuration, data quality, and experimental features

### Impact of Cleanup:
- **Reduced bundle size** by removing unused components
- **Improved maintainability** by removing dead code
- **Better performance** by reducing API surface area
- **Clearer codebase** with less confusion about what's actually used

### Risk Assessment:
- **Low Risk**: Removing clearly unused UI components
- **Medium Risk**: Removing API endpoints (need to verify no external consumers)
- **High Risk**: Removing webhook endpoints or endpoints that might be used by external systems

This analysis provides a roadmap for systematic code cleanup while maintaining the application's functionality and stability.
