"use client";

import React, { useState, useCallback, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { X, Upload, FileText, AlertCircle } from "lucide-react";

interface FileUploadProps {
  onFilesSelected: (files: File[]) => void;
  value?: File[]; // Add value prop for controlled component behavior
  acceptedFileTypes?: string[];
  maxFiles?: number;
  maxFileSize?: number; // in bytes
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  description?: string;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onFilesSelected,
  value, // Accept value prop
  acceptedFileTypes = [".pdf", ".csv", ".xlsx", ".xls", ".doc", ".docx"],
  maxFiles = 10,
  maxFileSize = 1024 * 1024 * 1024, // 1GB default (effectively no limit)
  className = "",
  disabled = false,
  placeholder = "Drop your files here",
  description = "Support for PDF, CSV, XLSX, and other document formats. Drag and drop or click to browse.",
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>(value || []); // Initialize with value
  const [error, setError] = useState<string | null>(null);

  // Sync internal state with value prop when it changes
  useEffect(() => {
    if (value !== undefined) {
      setSelectedFiles(value);
    }
  }, [value]);

  const validateFiles = (
    files: File[]
  ): { valid: File[]; errors: string[] } => {
    const valid: File[] = [];
    const errors: string[] = [];

    files.forEach((file) => {
      // Check file size
      if (file.size > maxFileSize) {
        errors.push(
          `${file.name} is too large (${(file.size / 1024 / 1024).toFixed(
            1
          )}MB). Max size is ${(maxFileSize / 1024 / 1024).toFixed(1)}MB.`
        );
        return;
      }

      // Check file type
      const isValidType = acceptedFileTypes.some((type) => {
        if (type.startsWith(".")) {
          return file.name.toLowerCase().endsWith(type.toLowerCase());
        }
        return file.type === type;
      });

      if (!isValidType) {
        errors.push(`${file.name} is not a supported file type.`);
        return;
      }

      valid.push(file);
    });

    return { valid, errors };
  };

  const processFiles = (files: File[]) => {
    setError(null);

    // Check if adding these files would exceed maxFiles
    if (selectedFiles.length + files.length > maxFiles) {
      setError(
        `Maximum ${maxFiles} files allowed. You can add ${
          maxFiles - selectedFiles.length
        } more files.`
      );
      return;
    }

    const { valid, errors } = validateFiles(files);

    if (errors.length > 0) {
      setError(errors.join(" "));
    }

    if (valid.length > 0) {
      const newFiles = [...selectedFiles, ...valid];
      setSelectedFiles(newFiles);
      onFilesSelected(newFiles);
    }
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      if (disabled) return;

      const files = Array.from(e.dataTransfer.files);
      processFiles(files);
    },
    [disabled]
  );

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return;

    const files = e.target.files ? Array.from(e.target.files) : [];
    processFiles(files);

    // Reset input value to allow selecting the same file again
    e.target.value = "";
  };

  const removeFile = (index: number) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(newFiles);
    onFilesSelected(newFiles);
    setError(null);
  };

  const clearAllFiles = () => {
    setSelectedFiles([]);
    onFilesSelected([]);
    setError(null);
  };

  const getFileIcon = (fileName: string) => {
    const ext = fileName.toLowerCase().split(".").pop();
    switch (ext) {
      case "pdf":
        return "📄";
      case "csv":
        return "📊";
      case "xlsx":
      case "xls":
        return "📈";
      case "doc":
      case "docx":
        return "📝";
      default:
        return "📎";
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Drag and Drop Area */}
      {selectedFiles.length === 0 && (
        <div
          className={`border-2 border-dashed rounded-xl p-12 text-center transition-all duration-200 ${
            dragActive
              ? "border-blue-500 bg-blue-50/50 scale-[1.02]"
              : "border-gray-300 hover:border-blue-400 hover:bg-gray-50/50"
          } ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <div className="flex flex-col items-center">
            <div className="p-4 bg-blue-100 rounded-full mb-4">
              <Upload className="h-12 w-12 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {placeholder}
            </h3>
            <p className="text-gray-500 mb-6 max-w-sm">{description}</p>
            <input
              type="file"
              multiple
              accept={acceptedFileTypes.join(",")}
              onChange={handleFileChange}
              className="hidden"
              id="file-upload-input"
              disabled={disabled}
            />
            <Button
              size="lg"
              className="bg-blue-600 hover:bg-blue-700"
              disabled={disabled}
              onClick={() =>
                document.getElementById("file-upload-input")?.click()
              }
            >
              <Upload className="h-5 w-5 mr-2" />
              Browse Files
            </Button>
          </div>
        </div>
      )}

      {/* Selected Files Display */}
      {selectedFiles.length > 0 && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">
                Selected Files ({selectedFiles.length})
              </h3>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearAllFiles}
                  disabled={disabled}
                >
                  Clear All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    document.getElementById("file-upload-input")?.click()
                  }
                  disabled={disabled}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Add More
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              {selectedFiles.map((file, index) => (
                <div
                  key={`${file.name}-${index}`}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{getFileIcon(file.name)}</span>
                    <div>
                      <p className="font-medium text-gray-900">{file.name}</p>
                      <p className="text-sm text-gray-500">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(index)}
                    disabled={disabled}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>

            {/* Hidden input for adding more files */}
            <input
              type="file"
              multiple
              accept={acceptedFileTypes.join(",")}
              onChange={handleFileChange}
              className="hidden"
              id="file-upload-input"
              disabled={disabled}
            />
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
          <AlertCircle className="h-5 w-5 text-red-500" />
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* File Type Info */}
      <div className="text-sm text-gray-500">
        <p>Accepted file types: {acceptedFileTypes.join(", ")}</p>
        <p>
          Maximum {maxFiles} files, {maxFileSize / 1024 / 1024}MB each
        </p>
      </div>
    </div>
  );
};
