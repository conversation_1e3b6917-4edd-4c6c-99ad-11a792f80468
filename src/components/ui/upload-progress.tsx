"use client";

import { Progress } from "@/components/ui/progress";
import { CheckCircle, XCircle, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface UploadProgressProps {
  phase: 'idle' | 'uploading' | 'processing' | 'completed' | 'error' | 'waiting_for_trigger' | 'uploaded_ready_for_processing';
  currentRow: number;
  totalRows: number;
  percentage: number;
  statusMessage: string;
  className?: string;
}

export function UploadProgress({
  phase,
  currentRow,
  totalRows,
  percentage,
  statusMessage,
  className
}: UploadProgressProps) {
  const getPhaseIcon = () => {
    switch (phase) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'uploading':
      case 'processing':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'waiting_for_trigger':
      case 'uploaded_ready_for_processing':
        return <CheckCircle className="h-5 w-5 text-orange-500" />;
      default:
        return null;
    }
  };

  const getPhaseColor = () => {
    switch (phase) {
      case 'completed':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      case 'uploading':
        return 'bg-blue-500';
      case 'processing':
        return 'bg-purple-500';
      case 'waiting_for_trigger':
      case 'uploaded_ready_for_processing':
        return 'bg-orange-500';
      default:
        return 'bg-gray-300';
    }
  };

  const getStatusColor = () => {
    switch (phase) {
      case 'completed':
        return 'text-green-700';
      case 'error':
        return 'text-red-700';
      case 'uploading':
        return 'text-blue-700';
      case 'processing':
        return 'text-purple-700';
      case 'waiting_for_trigger':
      case 'uploaded_ready_for_processing':
        return 'text-orange-700';
      default:
        return 'text-gray-700';
    }
  };

  if (phase === 'idle') {
    return null;
  }

  return (
    <div className={cn("w-full space-y-3 p-4 bg-white border border-gray-200 rounded-lg shadow-sm", className)}>
      {/* Header with icon and phase */}
      <div className="flex items-center gap-3">
        {getPhaseIcon()}
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-gray-900 capitalize">{phase}</h3>
            <span className="text-sm font-medium text-gray-600">{percentage}%</span>
          </div>
        </div>
      </div>

      {/* Progress bar */}
      <div className="space-y-2">
        <Progress value={percentage} className="h-2" />
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span className={getStatusColor()}>{statusMessage}</span>
          {totalRows > 0 && (
            <span>
              {currentRow} / {totalRows} rows
            </span>
          )}
        </div>
      </div>

      {/* Additional details for processing phase */}
      {phase === 'processing' && totalRows > 0 && (
        <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
          <div className="flex justify-between">
            <span>Rows processed: {currentRow}</span>
            <span>Remaining: {totalRows - currentRow}</span>
          </div>
          {currentRow > 0 && (
            <div className="mt-1">
              Estimated time remaining: {Math.round(((totalRows - currentRow) * 200) / 1000)}s
            </div>
          )}
        </div>
      )}

      {/* Completion summary */}
      {phase === 'completed' && (
        <div className="text-sm bg-green-50 text-green-700 p-2 rounded">
          ✅ Upload completed successfully!
        </div>
      )}

      {/* Error message */}
      {phase === 'error' && (
        <div className="text-sm bg-red-50 text-red-700 p-2 rounded">
          ❌ Upload failed. Please try again.
        </div>
      )}
    </div>
  );
} 