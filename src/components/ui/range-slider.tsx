"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";

interface RangeSliderProps {
  min: number;
  max: number;
  step?: number;
  value: [number, number];
  onValueChange: (value: [number, number]) => void;
  formatValue?: (value: number) => string;
  className?: string;
  showInput?: boolean;
  disabled?: boolean;
  unit?: string;
}

const RangeSlider = React.forwardRef<HTMLDivElement, RangeSliderProps>(
  ({ min, max, step = 1, value, onValueChange, formatValue = (value) => value.toString(), className, showInput = true, disabled = false, unit = "" }, ref) => {
    const [localValue, setLocalValue] = React.useState<[number, number]>(value);
    const [minInput, setMinInput] = React.useState<string>(formatValue(value[0]));
    const [maxInput, setMaxInput] = React.useState<string>(formatValue(value[1]));

    // Update local state when prop value changes
    React.useEffect(() => {
      setLocalValue(value);
      setMinInput(formatValue(value[0]));
      setMaxInput(formatValue(value[1]));
    }, [value, formatValue]);

    const handleSliderChange = (newValue: number[]) => {
      const typedValue = newValue as [number, number];
      setLocalValue(typedValue);
      onValueChange(typedValue);
    };

    const handleMinInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setMinInput(e.target.value);
    };

    const handleMaxInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setMaxInput(e.target.value);
    };

    const handleMinInputBlur = () => {
      let newMin = parseFloat(minInput.replace(/[^\d.-]/g, ""));

      if (isNaN(newMin)) {
        newMin = value[0];
      } else {
        // Constrain to min and current max
        newMin = Math.max(min, Math.min(newMin, value[1]));
      }

      const newValue: [number, number] = [newMin, value[1]];
      setLocalValue(newValue);
      setMinInput(formatValue(newMin));
      onValueChange(newValue);
    };

    const handleMaxInputBlur = () => {
      let newMax = parseFloat(maxInput.replace(/[^\d.-]/g, ""));

      if (isNaN(newMax)) {
        newMax = value[1];
      } else {
        // Constrain to current min and max
        newMax = Math.min(max, Math.max(newMax, value[0]));
      }

      const newValue: [number, number] = [value[0], newMax];
      setLocalValue(newValue);
      setMaxInput(formatValue(newMax));
      onValueChange(newValue);
    };

    const [minVal, maxVal] = localValue;
    const minPercent = ((minVal - min) / (max - min)) * 100;
    const maxPercent = ((maxVal - min) / (max - min)) * 100;

    const handleMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const val = Math.min(Number(e.target.value), maxVal - step);
      onValueChange([val, maxVal]);
    };

    const handleMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const val = Math.max(Number(e.target.value), minVal + step);
      onValueChange([minVal, val]);
    };

    return (
      <div ref={ref} className={cn("relative flex items-center", className)}>
        <div className="relative w-full h-2 bg-gray-200 rounded-lg">
          <div
            className="absolute h-2 bg-blue-600 rounded-lg"
            style={{
              left: `${minPercent}%`,
              width: `${maxPercent - minPercent}%`,
            }}
          />
          <input
            type="range"
            min={min}
            max={max}
            step={step}
            value={minVal}
            onChange={handleMinChange}
            disabled={disabled}
            className={cn(
              "absolute w-full h-2 bg-transparent appearance-none cursor-pointer",
              "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",
              "[&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:w-4",
              "[&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-blue-600",
              "[&::-webkit-slider-thumb]:cursor-pointer [&::-webkit-slider-thumb]:shadow-md",
              "[&::-moz-range-thumb]:h-4 [&::-moz-range-thumb]:w-4 [&::-moz-range-thumb]:rounded-full",
              "[&::-moz-range-thumb]:bg-blue-600 [&::-moz-range-thumb]:cursor-pointer",
              "[&::-moz-range-thumb]:border-none [&::-moz-range-track]:bg-transparent",
              disabled && "opacity-50 cursor-not-allowed"
            )}
          />
          <input
            type="range"
            min={min}
            max={max}
            step={step}
            value={maxVal}
            onChange={handleMaxChange}
            disabled={disabled}
            className={cn(
              "absolute w-full h-2 bg-transparent appearance-none cursor-pointer",
              "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",
              "[&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:w-4",
              "[&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-blue-600",
              "[&::-webkit-slider-thumb]:cursor-pointer [&::-webkit-slider-thumb]:shadow-md",
              "[&::-moz-range-thumb]:h-4 [&::-moz-range-thumb]:w-4 [&::-moz-range-thumb]:rounded-full",
              "[&::-moz-range-thumb]:bg-blue-600 [&::-moz-range-thumb]:cursor-pointer",
              "[&::-moz-range-thumb]:border-none [&::-moz-range-track]:bg-transparent",
              disabled && "opacity-50 cursor-not-allowed"
            )}
          />
        </div>

        {showInput && (
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Input
                type="text"
                value={minInput}
                onChange={handleMinInputChange}
                onBlur={handleMinInputBlur}
                className="w-24 h-8 text-sm"
                disabled={disabled}
              />
              {unit && (
                <span className="ml-1 text-sm text-muted-foreground">{unit}</span>
              )}
            </div>
            <div className="mx-2 text-muted-foreground">—</div>
            <div className="flex items-center">
              <Input
                type="text"
                value={maxInput}
                onChange={handleMaxInputChange}
                onBlur={handleMaxInputBlur}
                className="w-24 h-8 text-sm"
                disabled={disabled}
              />
              {unit && (
                <span className="ml-1 text-sm text-muted-foreground">{unit}</span>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }
);
RangeSlider.displayName = "RangeSlider";

export { RangeSlider };
