"use client";

import * as React from "react";
import { X, Check, ChevronsUpDown } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

export type Option = {
  label: string;
  value: string;
  count?: number;
};

interface MultiSelectProps {
  options: Option[];
  selected: string[];
  onChange: (selected: string[]) => void;
  placeholder?: string;
  emptyMessage?: string;
  className?: string;
  badgeClassName?: string;
  disabled?: boolean;
  maxDisplayItems?: number;
}

export function MultiSelect({
  options,
  selected,
  onChange,
  placeholder = "Select options...",
  emptyMessage = "No options found.",
  className,
  badgeClassName,
  disabled = false,
  maxDisplayItems = 3,
}: MultiSelectProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");

  // Handle removing a selected item
  const handleUnselect = React.useCallback((item: string) => {
    onChange(selected.filter((i) => i !== item));
  }, [selected, onChange]);

  // Handle selecting/deselecting an item
  const handleSelect = React.useCallback((optionValue: string) => {
    console.log('🎯 MultiSelect handleSelect:', optionValue); // Debug log
    const isSelected = selected.includes(optionValue);
    
    if (isSelected) {
      onChange(selected.filter((item) => item !== optionValue));
    } else {
      onChange([...selected, optionValue]);
    }
    setSearchQuery("");
  }, [selected, onChange]);

  // Filter options based on search query
  const filteredOptions = React.useMemo(() => {
    if (searchQuery === "") return options;

    return options.filter((option) =>
      option.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [options, searchQuery]);

  // Get display labels for selected values
  const selectedLabels = React.useMemo(() => {
    return selected.map((value) => {
      const option = options.find((opt) => opt.value === value);
      return option?.label || value;
    });
  }, [selected, options]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          role="combobox"
          aria-expanded={open}
          onClick={() => setOpen(!open)}
          className={cn(
            "w-full justify-between text-left font-normal min-h-[40px] px-3 py-2 border border-gray-300 bg-white hover:bg-gray-50",
            !selected.length && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <div className="flex-1 min-w-0 overflow-hidden">
            {selected.length === 0 ? (
              <span className="text-muted-foreground">{placeholder}</span>
            ) : (
              <div className="flex gap-1 flex-wrap items-center max-h-[80px] overflow-y-auto">
                {selectedLabels.map((label, index) => (
                  <Badge
                    key={index}
                    className={cn("px-2 py-1 text-xs bg-gray-100 text-gray-800 flex-shrink-0", badgeClassName)}
                  >
                    {label}
                    <button
                      type="button"
                      className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          e.stopPropagation();
                          handleUnselect(selected[index]);
                        }
                      }}
                      onMouseDown={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleUnselect(selected[index]);
                      }}
                    >
                      <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>
          <ChevronsUpDown className="h-4 w-4 opacity-50 shrink-0 ml-2" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-[var(--radix-popover-trigger-width)] min-w-[200px] p-0 z-50"
        align="start"
        side="bottom"
        sideOffset={4}
        style={{ zIndex: 9999 }}
      >
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="Search options..."
            value={searchQuery}
            onValueChange={setSearchQuery}
            className="h-10 border-0"
          />
          <CommandList className="max-h-[300px] overflow-auto">
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup>
              {filteredOptions.map((option) => {
                const isSelected = selected.includes(option.value);
                return (
                  <CommandItem
                    key={option.value}
                    value={option.value}
                    onSelect={(value) => {
                      handleSelect(value);
                    }}
                    className="relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled]:pointer-events-auto data-[disabled]:opacity-100"
                  >
                    <div className="flex items-center gap-2 flex-1">
                      <div className="h-4 w-4 flex items-center justify-center">
                        {isSelected ? (
                          <Check className="h-4 w-4 text-primary" />
                        ) : (
                          <div className="h-4 w-4 rounded-sm border border-primary/30" />
                        )}
                      </div>
                      <span className={cn(
                        "flex-1 text-sm",
                        isSelected ? "font-medium" : "font-normal"
                      )}>
                        {option.label}
                      </span>
                      {option.count !== undefined && (
                        <Badge className="ml-2 text-xs border border-gray-300 bg-white">
                          {option.count}
                        </Badge>
                      )}
                    </div>
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </CommandList>
          {selected.length > 0 && (
            <div className="border-t border-border p-2">
              <Button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onChange([]);
                  setSearchQuery("");
                }}
                className="w-full text-xs h-8 hover:bg-gray-100 bg-transparent border-0"
              >
                Clear all ({selected.length})
              </Button>
            </div>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
}
