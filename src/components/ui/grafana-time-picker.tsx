"use client"

import * as React from "react"
import { CalendarIcon, Clock, RefreshCw } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import { format, subDays, subHours, subMinutes } from "date-fns"

interface TimeRange {
  from: Date
  to: Date
  label: string
}

interface GrafanaTimePickerProps {
  value: TimeRange
  onChange: (range: TimeRange) => void
  className?: string
}

const RELATIVE_TIME_RANGES = [
  { label: "Last 5 minutes", value: () => ({ from: subMinutes(new Date(), 5), to: new Date() }) },
  { label: "Last 15 minutes", value: () => ({ from: subMinutes(new Date(), 15), to: new Date() }) },
  { label: "Last 30 minutes", value: () => ({ from: subMinutes(new Date(), 30), to: new Date() }) },
  { label: "Last 1 hour", value: () => ({ from: subHours(new Date(), 1), to: new Date() }) },
  { label: "Last 3 hours", value: () => ({ from: subHours(new Date(), 3), to: new Date() }) },
  { label: "Last 6 hours", value: () => ({ from: subHours(new Date(), 6), to: new Date() }) },
  { label: "Last 12 hours", value: () => ({ from: subHours(new Date(), 12), to: new Date() }) },
  { label: "Last 24 hours", value: () => ({ from: subDays(new Date(), 1), to: new Date() }) },
  { label: "Last 2 days", value: () => ({ from: subDays(new Date(), 2), to: new Date() }) },
  { label: "Last 7 days", value: () => ({ from: subDays(new Date(), 7), to: new Date() }) },
  { label: "Last 30 days", value: () => ({ from: subDays(new Date(), 30), to: new Date() }) },
  { label: "Last 90 days", value: () => ({ from: subDays(new Date(), 90), to: new Date() }) },
  { label: "Last 6 months", value: () => ({ from: subDays(new Date(), 180), to: new Date() }) },
  { label: "Last year", value: () => ({ from: subDays(new Date(), 365), to: new Date() }) },
]

export function GrafanaTimePicker({ value, onChange, className }: GrafanaTimePickerProps) {
  const [open, setOpen] = React.useState(false)
  const [fromInput, setFromInput] = React.useState("")
  const [toInput, setToInput] = React.useState("")
  const [recentRanges, setRecentRanges] = React.useState<TimeRange[]>([])

  React.useEffect(() => {
    setFromInput(formatDateTime(value.from))
    setToInput(formatDateTime(value.to))
  }, [value])

  const formatDateTime = (date: Date) => {
    return format(date, "yyyy-MM-dd HH:mm:ss")
  }

  const handleRelativeTimeSelect = (range: any) => {
    const { from, to } = range.value()
    const newRange = { from, to, label: range.label }
    onChange(newRange)
    
    // Add to recent ranges
    setRecentRanges(prev => {
      const filtered = prev.filter(r => r.label !== range.label)
      return [newRange, ...filtered].slice(0, 4)
    })
    
    setOpen(false)
  }

  const handleAbsoluteTimeApply = () => {
    try {
      const from = new Date(fromInput)
      const to = new Date(toInput)
      
      if (isNaN(from.getTime()) || isNaN(to.getTime())) {
        return
      }
      
      const newRange = {
        from,
        to,
        label: `${format(from, "MMM dd, HH:mm")} to ${format(to, "MMM dd, HH:mm")}`
      }
      
      onChange(newRange)
      setOpen(false)
    } catch (error) {
      // Handle error
    }
  }

  const handleRefresh = () => {
    if (value.label.startsWith("Last")) {
      // For relative ranges, refresh by reapplying the same range
      const relativeRange = RELATIVE_TIME_RANGES.find(r => r.label === value.label)
      if (relativeRange) {
        handleRelativeTimeSelect(relativeRange)
      }
    }
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-8 px-3 text-xs font-normal border-gray-300 hover:border-gray-400 bg-white"
          >
            <Clock className="h-3 w-3 mr-1" />
            {value.label}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-96 p-0" align="start">
          <div className="bg-white border border-gray-200 rounded-md shadow-lg">
            <div className="p-4 border-b border-gray-200">
              <div className="grid grid-cols-2 gap-4">
                {/* Absolute Time Range */}
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-gray-900">Absolute time range</h4>
                  <div className="space-y-2">
                    <div>
                      <Label htmlFor="from" className="text-xs text-gray-600">From</Label>
                      <Input
                        id="from"
                        value={fromInput}
                        onChange={(e) => setFromInput(e.target.value)}
                        placeholder="YYYY-MM-DD HH:mm:ss"
                        className="h-8 text-xs"
                      />
                    </div>
                    <div>
                      <Label htmlFor="to" className="text-xs text-gray-600">To</Label>
                      <Input
                        id="to"
                        value={toInput}
                        onChange={(e) => setToInput(e.target.value)}
                        placeholder="YYYY-MM-DD HH:mm:ss"
                        className="h-8 text-xs"
                      />
                    </div>
                    <Button
                      onClick={handleAbsoluteTimeApply}
                      className="w-full h-8 text-xs bg-blue-600 hover:bg-blue-700"
                    >
                      Apply time range
                    </Button>
                  </div>
                  
                  {recentRanges.length > 0 && (
                    <div className="mt-4">
                      <h5 className="text-xs font-medium text-gray-600 mb-2">Recently used absolute ranges</h5>
                      <div className="space-y-1">
                        {recentRanges.map((range, index) => (
                          <button
                            key={index}
                            onClick={() => {
                              onChange(range)
                              setOpen(false)
                            }}
                            className="w-full text-left text-xs p-1 hover:bg-gray-100 rounded text-gray-700"
                          >
                            {formatDateTime(range.from)} to {formatDateTime(range.to)}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Relative Time Ranges */}
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-gray-900">Relative time ranges</h4>
                  <div className="space-y-1 max-h-80 overflow-y-auto">
                    {RELATIVE_TIME_RANGES.map((range, index) => (
                      <button
                        key={index}
                        onClick={() => handleRelativeTimeSelect(range)}
                        className="w-full text-left text-xs p-2 hover:bg-gray-100 rounded text-gray-700"
                      >
                        {range.label}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="p-3 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between text-xs text-gray-600">
                <span>Browser Time: United States, EST</span>
                <div className="flex items-center gap-2">
                  <span>UTC-05:00</span>
                  <Button variant="ghost" size="sm" className="h-6 text-xs">
                    Change time zone
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
      
      <Button
        variant="outline"
        size="sm"
        onClick={handleRefresh}
        className="h-8 px-2 border-gray-300 hover:border-gray-400"
      >
        <RefreshCw className="h-3 w-3" />
      </Button>
    </div>
  )
} 