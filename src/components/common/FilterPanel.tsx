import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MultiSelect, Option } from "@/components/ui/multi-select";
import { RangeSlider } from "@/components/ui/range-slider";
import {
  FilterState,
  formatCurrency,
  formatNumber,
  formatRange,
} from "@/lib/utils";
import { Filter, X, ChevronDown, ChevronUp } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

// Define filter categories and their options
export const CAPITAL_TYPES: Option[] = [
  { label: "Senior Debt", value: "Senior Debt" },
  { label: "Mezzanine", value: "Mezzanine" },
  { label: "Preferred Equity", value: "Preferred Equity" },
  { label: "Common Equity", value: "Common Equity" },
  { label: "Joint Venture (JV)", value: "Joint Venture (JV)" },
  { label: "Limited Partner (LP)", value: "Limited Partner (LP)" },
  { label: "General Partner (GP)", value: "General Partner (GP)" },
  { label: "Co-GP", value: "Co-GP" },
  { label: "Developer", value: "Developer" },
  { label: "Owner", value: "Owner" },
  { label: "Borrower", value: "Borrower" },
];

export const LOAN_TYPES: Option[] = [
  { label: "Bridge", value: "Bridge" },
  { label: "Long Term", value: "Long Term" },
  { label: "Short Term", value: "Short Term" },
  { label: "Construction", value: "Construction" },
  { label: "Permanent", value: "Permanent" },
  { label: "HUD/FHA", value: "HUD/FHA" },
  { label: "Mezzanine Financing", value: "Mezzanine Financing" },
  { label: "Interim Financing", value: "Interim Financing" },
];

export const PROPERTY_TYPES: Option[] = [
  { label: "Multi-Family", value: "Multi-Family" },
  { label: "Office", value: "Office" },
  { label: "Retail", value: "Retail" },
  { label: "Industrial", value: "Industrial" },
  { label: "Hospitality", value: "Hospitality" },
  { label: "Mixed-Use", value: "Mixed-Use" },
  { label: "Healthcare", value: "Healthcare" },
  { label: "Land", value: "Land" },
  { label: "Special Purpose", value: "Special Purpose" },
  {
    label: "Single-Family Residence (SFR)",
    value: "Single-Family Residence (SFR)",
  },
];

export const ASSET_TYPES: Option[] = [
  { label: "Core", value: "Core" },
  { label: "Core-Plus", value: "Core-Plus" },
  { label: "Value-Add", value: "Value-Add" },
  { label: "Opportunistic", value: "Opportunistic" },
  { label: "Distressed", value: "Distressed" },
  { label: "Ground-up Development", value: "Ground-up Development" },
  { label: "Pre-Development", value: "Pre-Development" },
];

export const REGIONS: Option[] = [
  { label: "Northeast", value: "Northeast" },
  { label: "Mid-Atlantic", value: "Mid-Atlantic" },
  { label: "Southeast", value: "Southeast" },
  { label: "Midwest", value: "Midwest" },
  { label: "Southwest", value: "Southwest" },
  { label: "West", value: "West" },
  { label: "Northwest", value: "Northwest" },
  { label: "National", value: "National" },
  { label: "International", value: "International" },
];

// Common US states as options
export const STATES: Option[] = [
  { label: "Alabama", value: "AL" },
  { label: "Alaska", value: "AK" },
  { label: "Arizona", value: "AZ" },
  { label: "Arkansas", value: "AR" },
  { label: "California", value: "CA" },
  { label: "Colorado", value: "CO" },
  { label: "Connecticut", value: "CT" },
  { label: "Delaware", value: "DE" },
  { label: "Florida", value: "FL" },
  { label: "Georgia", value: "GA" },
  { label: "Hawaii", value: "HI" },
  { label: "Idaho", value: "ID" },
  { label: "Illinois", value: "IL" },
  { label: "Indiana", value: "IN" },
  { label: "Iowa", value: "IA" },
  { label: "Kansas", value: "KS" },
  { label: "Kentucky", value: "KY" },
  { label: "Louisiana", value: "LA" },
  { label: "Maine", value: "ME" },
  { label: "Maryland", value: "MD" },
  { label: "Massachusetts", value: "MA" },
  { label: "Michigan", value: "MI" },
  { label: "Minnesota", value: "MN" },
  { label: "Mississippi", value: "MS" },
  { label: "Missouri", value: "MO" },
  { label: "Montana", value: "MT" },
  { label: "Nebraska", value: "NE" },
  { label: "Nevada", value: "NV" },
  { label: "New Hampshire", value: "NH" },
  { label: "New Jersey", value: "NJ" },
  { label: "New Mexico", value: "NM" },
  { label: "New York", value: "NY" },
  { label: "North Carolina", value: "NC" },
  { label: "North Dakota", value: "ND" },
  { label: "Ohio", value: "OH" },
  { label: "Oklahoma", value: "OK" },
  { label: "Oregon", value: "OR" },
  { label: "Pennsylvania", value: "PA" },
  { label: "Rhode Island", value: "RI" },
  { label: "South Carolina", value: "SC" },
  { label: "South Dakota", value: "SD" },
  { label: "Tennessee", value: "TN" },
  { label: "Texas", value: "TX" },
  { label: "Utah", value: "UT" },
  { label: "Vermont", value: "VT" },
  { label: "Virginia", value: "VA" },
  { label: "Washington", value: "WA" },
  { label: "West Virginia", value: "WV" },
  { label: "Wisconsin", value: "WI" },
  { label: "Wyoming", value: "WY" },
];

// Define major cities as options
export const CITIES: Option[] = [
  { label: "New York", value: "New York" },
  { label: "Los Angeles", value: "Los Angeles" },
  { label: "Chicago", value: "Chicago" },
  { label: "Houston", value: "Houston" },
  { label: "Phoenix", value: "Phoenix" },
  { label: "Philadelphia", value: "Philadelphia" },
  { label: "San Antonio", value: "San Antonio" },
  { label: "San Diego", value: "San Diego" },
  { label: "Dallas", value: "Dallas" },
  { label: "San Francisco", value: "San Francisco" },
  { label: "Austin", value: "Austin" },
  { label: "Seattle", value: "Seattle" },
  { label: "Denver", value: "Denver" },
  { label: "Boston", value: "Boston" },
  { label: "Miami", value: "Miami" },
  { label: "Atlanta", value: "Atlanta" },
  { label: "Washington DC", value: "Washington DC" },
  { label: "Nashville", value: "Nashville" },
  { label: "Portland", value: "Portland" },
  { label: "Las Vegas", value: "Las Vegas" },
];

// Define countries as options
export const COUNTRIES: Option[] = [
  { label: "United States", value: "United States" },
  { label: "Canada", value: "Canada" },
  { label: "Mexico", value: "Mexico" },
  { label: "United Kingdom", value: "United Kingdom" },
  { label: "Germany", value: "Germany" },
  { label: "France", value: "France" },
  { label: "Spain", value: "Spain" },
  { label: "Italy", value: "Italy" },
  { label: "Australia", value: "Australia" },
  { label: "Japan", value: "Japan" },
  { label: "China", value: "China" },
  { label: "India", value: "India" },
  { label: "Brazil", value: "Brazil" },
];

// Interface for filter panel props
interface FilterPanelProps {
  filters: FilterState;
  onFilterChange: (filters: FilterState) => void;
  onClearFilters: () => void;
  customOptions?: {
    capitalTypes?: Option[];
    loanTypes?: Option[];
    propertyTypes?: Option[];
    assetTypes?: Option[];
    regions?: Option[];
    states?: Option[];
    cities?: Option[];
    countries?: Option[];
  };
  // Hierarchical mappings for advanced filtering
  hierarchicalMappings?: {
    [type: string]: {
      flat: string[];
      hierarchical: {
        [parent: string]: {
          value: string;
          children: {
            [childType: string]: string[];
          };
        };
      };
    };
  };
  ranges?: {
    dealSize?: { min: number; max: number };
    ltv?: { min: number; max: number };
    ltc?: { min: number; max: number };
  };
  showCategories?: {
    capitalType?: boolean;
    loanType?: boolean;
    dealSize?: boolean;
    ltv?: boolean;
    ltc?: boolean;
    propertyType?: boolean;
    assetType?: boolean;
    location?: boolean;
  };
}

export default function FilterPanel({
  filters,
  onFilterChange,
  onClearFilters,
  customOptions = {},
  hierarchicalMappings,
  ranges = {},
  showCategories = {
    capitalType: true,
    loanType: true,
    dealSize: true,
    ltv: true,
    ltc: true,
    propertyType: true,
    assetType: true,
    location: true,
  },
}: FilterPanelProps) {
  const [activeTab, setActiveTab] = useState("investment");
  const [isOpen, setIsOpen] = useState(true);

  // Hierarchical filtering functions
  const getAvailableLoanTypes = (selectedCapitalTypes?: string[]) => {
    const capitalTypesToUse = selectedCapitalTypes || filters.capitalTypes;

    if (
      !hierarchicalMappings?.["Capital Position"]?.hierarchical ||
      !capitalTypesToUse?.length
    ) {
      return customOptions.loanTypes || LOAN_TYPES;
    }

    const availableLoanTypes = new Set<string>();

    capitalTypesToUse.forEach((capitalType: string) => {
      const hierarchicalData =
        hierarchicalMappings["Capital Position"]?.hierarchical?.[capitalType];
      if (hierarchicalData?.children?.["Loan Type"]) {
        hierarchicalData.children["Loan Type"].forEach((loanType: string) => {
          availableLoanTypes.add(loanType);
        });
      }
    });

    return Array.from(availableLoanTypes)
      .sort()
      .map((type) => ({ value: type, label: type }));
  };

  const getAvailableStates = (selectedRegions?: string[]) => {
    const regionsToUse = selectedRegions || filters.regions;

    if (
      !hierarchicalMappings?.["U.S Regions"]?.hierarchical ||
      !regionsToUse?.length
    ) {
      return customOptions.states || STATES;
    }

    const availableStates = new Set<string>();

    regionsToUse.forEach((region: string) => {
      const hierarchicalData =
        hierarchicalMappings["U.S Regions"]?.hierarchical?.[region];
      if (hierarchicalData?.children?.["U.S Regions States"]) {
        hierarchicalData.children["U.S Regions States"].forEach(
          (state: string) => {
            availableStates.add(state);
          }
        );
      }
    });

    return Array.from(availableStates)
      .sort()
      .map((state) => ({ value: state, label: state }));
  };

  // Use hierarchical parent categories if available, otherwise fallback to custom options or defaults
  const getCapitalTypeOptions = () => {
    if (hierarchicalMappings?.["Capital Position"]?.hierarchical) {
      return Object.keys(
        hierarchicalMappings["Capital Position"].hierarchical
      ).map((type) => ({ value: type, label: type }));
    }
    return customOptions.capitalTypes || CAPITAL_TYPES;
  };

  const getPropertyTypeOptions = () => {
    if (hierarchicalMappings?.["Property Type"]?.hierarchical) {
      return Object.keys(
        hierarchicalMappings["Property Type"].hierarchical
      ).map((type) => ({ value: type, label: type }));
    }
    return customOptions.propertyTypes || PROPERTY_TYPES;
  };

  const getRegionOptions = () => {
    if (hierarchicalMappings?.["U.S Regions"]?.hierarchical) {
      return Object.keys(hierarchicalMappings["U.S Regions"].hierarchical).map(
        (region) => ({ value: region, label: region })
      );
    }
    return customOptions.regions || REGIONS;
  };

  // Merge default options with custom options and hierarchical data
  const options = {
    capitalTypes: getCapitalTypeOptions(),
    loanTypes: getAvailableLoanTypes(),
    propertyTypes: getPropertyTypeOptions(),
    assetTypes: customOptions.assetTypes || ASSET_TYPES,
    regions: getRegionOptions(),
    states: getAvailableStates(),
    cities: customOptions.cities || CITIES,
    countries: customOptions.countries || COUNTRIES,
  };

  // Helper to update filter state
  const updateFilter = (key: string, value: any) => {
    onFilterChange({
      ...filters,
      [key]: value,
    });
  };

  // Count active filters
  const getActiveFilterCount = (): number => {
    let count = 0;

    if (filters.capitalTypes && filters.capitalTypes.length > 0) count++;
    if (filters.loanTypes && filters.loanTypes.length > 0) count++;
    if (filters.propertyTypes && filters.propertyTypes.length > 0) count++;
    if (filters.assetTypes && filters.assetTypes.length > 0) count++;
    if (filters.regions && filters.regions.length > 0) count++;
    if (filters.states && filters.states.length > 0) count++;
    if (filters.cities && filters.cities.length > 0) count++;
    if (filters.countries && filters.countries.length > 0) count++;

    // Check range filters
    if (
      filters.dealSize &&
      (filters.dealSize.min > 0 || filters.dealSize.max < 1000)
    )
      count++;
    if (filters.ltv && (filters.ltv.min > 0 || filters.ltv.max < 100)) count++;
    if (filters.ltc && (filters.ltc.min > 0 || filters.ltc.max < 100)) count++;

    return count;
  };

  // Show loan type filter only if debt-related capital types are selected
  const showLoanTypeFilter = () => {
    if (!showCategories.loanType) return false;

    const debtTypes = ["Senior Debt", "Mezzanine"];
    return (
      filters.capitalTypes &&
      filters.capitalTypes.some((type: string) => debtTypes.includes(type))
    );
  };

  // Replace the hardcoded ranges with the dynamic ranges from props
  const dealSizeMin = ranges.dealSize?.min ?? 0;
  const dealSizeMax = ranges.dealSize?.max ?? 1000;
  const ltvMin = ranges.ltv?.min ?? 0;
  const ltvMax = ranges.ltv?.max ?? 100;
  const ltcMin = ranges.ltc?.min ?? 0;
  const ltcMax = ranges.ltc?.max ?? 100;

  return (
    <Card className="border-gray-100 shadow-sm overflow-hidden sticky top-4">
      <Collapsible open={isOpen}>
        <CardHeader className="py-3 px-4 bg-white border-b border-gray-100">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base font-medium flex items-center text-gray-800">
              <Filter className="h-4 w-4 mr-2 text-primary/70" />
              Filters
              {getActiveFilterCount() > 0 && (
                <Badge className="ml-2 bg-primary/90 hover:bg-primary text-white">
                  {getActiveFilterCount()}
                </Badge>
              )}
            </CardTitle>
            <div className="flex items-center gap-2">
              {getActiveFilterCount() > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClearFilters}
                  className="h-8 px-2 text-xs text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                >
                  Clear All
                </Button>
              )}
              <CollapsibleTrigger>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 hover:bg-gray-100"
                >
                  {isOpen ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </CollapsibleTrigger>
            </div>
          </div>
        </CardHeader>

        <CollapsibleContent>
          <CardContent className="px-4 py-4">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid grid-cols-3 mb-5 bg-gray-50 p-1 rounded-lg">
                <TabsTrigger
                  value="investment"
                  className="text-sm py-1.5 data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm"
                >
                  Investment
                </TabsTrigger>
                <TabsTrigger
                  value="property"
                  className="text-sm py-1.5 data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm"
                >
                  Property
                </TabsTrigger>
                <TabsTrigger
                  value="location"
                  className="text-sm py-1.5 data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm"
                >
                  Location
                </TabsTrigger>
              </TabsList>

              <TabsContent value="investment" className="space-y-4">
                {showCategories.capitalType && (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Capital Type</h3>
                    <MultiSelect
                      options={options.capitalTypes}
                      selected={filters.capitalTypes || []}
                      onChange={(selected) => {
                        // Clear loan types when capital types change (hierarchical filtering)
                        const availableLoanTypes = getAvailableLoanTypes(
                          selected
                        ).map((opt) => opt.value);
                        const filteredLoanTypes =
                          filters.loanTypes?.filter((loanType: string) =>
                            availableLoanTypes.includes(loanType)
                          ) || [];

                        onFilterChange({
                          ...filters,
                          capitalTypes: selected,
                          loanTypes: filteredLoanTypes,
                        });
                      }}
                      placeholder="Select capital types..."
                      maxDisplayItems={2}
                    />
                  </div>
                )}

                {showLoanTypeFilter() && (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Loan Type</h3>
                    <MultiSelect
                      options={options.loanTypes}
                      selected={
                        filters.loanTypes?.filter((loanType: string) =>
                          options.loanTypes.some(
                            (opt) => opt.value === loanType
                          )
                        ) || []
                      }
                      onChange={(selected) =>
                        updateFilter("loanTypes", selected)
                      }
                      placeholder="Select loan types..."
                      maxDisplayItems={2}
                    />
                  </div>
                )}

                {showCategories.dealSize && (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Deal Size ($M)</h3>
                    <RangeSlider
                      min={dealSizeMin}
                      max={dealSizeMax}
                      step={5}
                      value={[
                        filters.dealSize?.min || dealSizeMin,
                        filters.dealSize?.max || dealSizeMax,
                      ]}
                      onValueChange={([min, max]) =>
                        updateFilter("dealSize", { min, max })
                      }
                      formatValue={(value) => `$${value}M`}
                    />
                  </div>
                )}

                {showCategories.ltv && (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Loan to Value (%)</h3>
                    <RangeSlider
                      min={ltvMin}
                      max={ltvMax}
                      step={5}
                      value={[
                        filters.ltv?.min || ltvMin,
                        filters.ltv?.max || ltvMax,
                      ]}
                      onValueChange={([min, max]) =>
                        updateFilter("ltv", { min, max })
                      }
                      formatValue={(value) => `${value}%`}
                      unit="%"
                    />
                  </div>
                )}

                {showCategories.ltc && (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Loan to Cost (%)</h3>
                    <RangeSlider
                      min={ltcMin}
                      max={ltcMax}
                      step={5}
                      value={[
                        filters.ltc?.min || ltcMin,
                        filters.ltc?.max || ltcMax,
                      ]}
                      onValueChange={([min, max]) =>
                        updateFilter("ltc", { min, max })
                      }
                      formatValue={(value) => `${value}%`}
                      unit="%"
                    />
                  </div>
                )}
              </TabsContent>

              <TabsContent value="property" className="space-y-4">
                {showCategories.propertyType && (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Property Type</h3>
                    <MultiSelect
                      options={options.propertyTypes}
                      selected={filters.propertyTypes || []}
                      onChange={(selected) =>
                        updateFilter("propertyTypes", selected)
                      }
                      placeholder="Select property types..."
                      maxDisplayItems={2}
                    />
                  </div>
                )}

                {showCategories.assetType && (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Asset Type</h3>
                    <MultiSelect
                      options={options.assetTypes}
                      selected={filters.assetTypes || []}
                      onChange={(selected) =>
                        updateFilter("assetTypes", selected)
                      }
                      placeholder="Select asset types..."
                      maxDisplayItems={2}
                    />
                  </div>
                )}
              </TabsContent>

              <TabsContent value="location" className="space-y-4">
                {showCategories.location && (
                  <>
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Country</h3>
                      <MultiSelect
                        options={options.countries}
                        selected={filters.countries || []}
                        onChange={(selected) =>
                          updateFilter("countries", selected)
                        }
                        placeholder="Select countries..."
                        maxDisplayItems={2}
                      />
                    </div>

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Region</h3>
                      <MultiSelect
                        options={options.regions}
                        selected={filters.regions || []}
                        onChange={(selected) => {
                          // Clear states when regions change (hierarchical filtering)
                          const availableStates = getAvailableStates(
                            selected
                          ).map((opt) => opt.value);
                          const filteredStates =
                            filters.states?.filter((state: string) =>
                              availableStates.includes(state)
                            ) || [];

                          onFilterChange({
                            ...filters,
                            regions: selected,
                            states: filteredStates,
                          });
                        }}
                        placeholder="Select regions..."
                        maxDisplayItems={2}
                      />
                    </div>

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">State</h3>
                      <MultiSelect
                        options={options.states}
                        selected={
                          filters.states?.filter((state: string) =>
                            options.states.some((opt) => opt.value === state)
                          ) || []
                        }
                        onChange={(selected) =>
                          updateFilter("states", selected)
                        }
                        placeholder="Select states..."
                        maxDisplayItems={2}
                      />
                    </div>

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">City</h3>
                      <MultiSelect
                        options={options.cities}
                        selected={filters.cities || []}
                        onChange={(selected) =>
                          updateFilter("cities", selected)
                        }
                        placeholder="Select cities..."
                        maxDisplayItems={2}
                      />
                    </div>
                  </>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}
