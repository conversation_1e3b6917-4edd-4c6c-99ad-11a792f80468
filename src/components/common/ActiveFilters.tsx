import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { X } from 'lucide-react'
import { FilterState, formatRange } from '@/lib/utils'
import { CAPITAL_TYPES, LOAN_TYPES, PROPERTY_TYPES, ASSET_TYPES, REGIONS, STATES, CITIES, COUNTRIES } from './FilterPanel'

interface ActiveFiltersProps {
  filters: FilterState
  onRemoveFilter: (key: string, value?: string | number) => void
  onClearFilters: () => void
  className?: string
}

export default function ActiveFilters({ 
  filters, 
  onRemoveFilter, 
  onClearFilters,
  className = '' 
}: ActiveFiltersProps) {
  // Check if there are any active filters
  const hasActiveFilters = Object.keys(filters).some(key => {
    if (Array.isArray(filters[key]) && filters[key].length > 0) return true
    if (key === 'dealSize' && (filters[key]?.min > 0 || filters[key]?.max < 1000)) return true
    if (key === 'ltv' && (filters[key]?.min > 0 || filters[key]?.max < 100)) return true
    if (key === 'ltc' && (filters[key]?.min > 0 || filters[key]?.max < 100)) return true
    return false
  })

  if (!hasActiveFilters) return null

  // Helper to get label for a filter value
  const getFilterLabel = (key: string, value: string): string => {
    switch (key) {
      case 'capitalTypes':
        return CAPITAL_TYPES.find(option => option.value === value)?.label || value
      case 'loanTypes':
        return LOAN_TYPES.find(option => option.value === value)?.label || value
      case 'propertyTypes':
        return PROPERTY_TYPES.find(option => option.value === value)?.label || value
      case 'assetTypes':
        return ASSET_TYPES.find(option => option.value === value)?.label || value
      case 'regions':
        return REGIONS.find(option => option.value === value)?.label || value
      case 'states':
        return STATES.find(option => option.value === value)?.label || value
      case 'cities':
        return CITIES.find(option => option.value === value)?.label || value
      case 'countries':
        return COUNTRIES.find(option => option.value === value)?.label || value
      default:
        return value
    }
  }

  // Helper to get category name for a filter key
  const getFilterCategory = (key: string): string => {
    switch (key) {
      case 'capitalTypes': return 'Capital Type'
      case 'loanTypes': return 'Loan Type'
      case 'propertyTypes': return 'Property Type'
      case 'assetTypes': return 'Asset Type'
      case 'regions': return 'Region'
      case 'states': return 'State'
      case 'cities': return 'City'
      case 'countries': return 'Country'
      case 'dealSize': return 'Deal Size'
      case 'ltv': return 'LTV'
      case 'ltc': return 'LTC'
      default: return key
    }
  }

  // Helper to get badge color for a filter category
  const getBadgeClass = (key: string): string => {
    switch (key) {
      case 'capitalTypes': return 'bg-blue-50 text-blue-800 hover:bg-blue-100'
      case 'loanTypes': return 'bg-indigo-50 text-indigo-800 hover:bg-indigo-100'
      case 'propertyTypes': return 'bg-green-50 text-green-800 hover:bg-green-100'
      case 'assetTypes': return 'bg-teal-50 text-teal-800 hover:bg-teal-100'
      case 'regions': return 'bg-amber-50 text-amber-800 hover:bg-amber-100'
      case 'states': return 'bg-orange-50 text-orange-800 hover:bg-orange-100'
      case 'cities': return 'bg-rose-50 text-rose-800 hover:bg-rose-100'
      case 'countries': return 'bg-purple-50 text-purple-800 hover:bg-purple-100'
      case 'dealSize': return 'bg-emerald-50 text-emerald-800 hover:bg-emerald-100'
      case 'ltv': return 'bg-cyan-50 text-cyan-800 hover:bg-cyan-100'
      case 'ltc': return 'bg-sky-50 text-sky-800 hover:bg-sky-100'
      default: return 'bg-gray-50 text-gray-800 hover:bg-gray-100'
    }
  }

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {Object.entries(filters).map(([key, value]) => {
        // Handle array filters (multi-select)
        if (Array.isArray(value) && value.length > 0) {
          return value.map((item, index) => (
            <Badge 
              key={`${key}-${index}`} 
              variant="secondary" 
              className={`flex items-center gap-1 ${getBadgeClass(key)}`}
            >
              <span>{getFilterCategory(key)}: {getFilterLabel(key, item)}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onRemoveFilter(key, item)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))
        }
        
        // Handle range filters
        if (key === 'dealSize' && value && (value.min > 0 || value.max < 1000)) {
          return (
            <Badge 
              key={key}
              variant="secondary" 
              className={`flex items-center gap-1 ${getBadgeClass(key)}`}
            >
              <span>Deal Size: ${value.min}M - ${value.max}M</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onRemoveFilter(key)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )
        }
        
        if (key === 'ltv' && value && (value.min > 0 || value.max < 100)) {
          return (
            <Badge 
              key={key}
              variant="secondary" 
              className={`flex items-center gap-1 ${getBadgeClass(key)}`}
            >
              <span>LTV: {value.min}% - {value.max}%</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onRemoveFilter(key)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )
        }
        
        if (key === 'ltc' && value && (value.min > 0 || value.max < 100)) {
          return (
            <Badge 
              key={key}
              variant="secondary" 
              className={`flex items-center gap-1 ${getBadgeClass(key)}`}
            >
              <span>LTC: {value.min}% - {value.max}%</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onRemoveFilter(key)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )
        }
        
        return null
      })}
      
      {hasActiveFilters && (
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={onClearFilters}
          className="text-xs h-7 px-2 text-gray-600"
        >
          Clear All
        </Button>
      )}
    </div>
  )
} 