"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { AlertTriangle, Calendar, MapPin, Building2 } from "lucide-react";
import { PotentialDealDuplicate } from "@/lib/utils/dealConflictDetector";

interface DealConflictModalProps {
  isOpen: boolean;
  onClose: () => void;
  duplicates: PotentialDealDuplicate[];
  newDealData: any;
  onResolve: (action: "replace" | "create_new", targetDealId?: number) => void;
}

export function DealConflictModal({
  isOpen,
  onClose,
  duplicates,
  newDealData,
  onResolve,
}: Deal<PERSON>onflictModalProps) {
  const [selectedAction, setSelectedAction] = useState<
    "replace" | "create_new" | null
  >(null);
  const [selectedDealToReplace, setSelectedDealToReplace] = useState<
    number | null
  >(null);

  const handleResolve = () => {
    if (!selectedAction) return;

    if (selectedAction === "replace") {
      const targetId =
        duplicates.length === 1 ? duplicates[0].deal_id : selectedDealToReplace;
      if (targetId) {
        onResolve("replace", targetId);
      }
    } else if (selectedAction === "create_new") {
      onResolve("create_new");
    }
  };

  const formatValue = (value: any): string => {
    if (value === null || value === undefined) return "Not specified";
    if (typeof value === "string" && value.trim() === "")
      return "Not specified";
    return String(value);
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString();
  };

  const getLocationString = (deal: any): string => {
    const parts = [deal.city, deal.state, deal.region].filter(Boolean);
    return parts.length > 0 ? parts.join(", ") : "Not specified";
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            Potential Duplicate Deal Detected
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              We found {duplicates.length} existing deal
              {duplicates.length > 1 ? "s" : ""} that might be the same as the
              one you're uploading. Please review the comparison below and
              choose how to proceed.
            </AlertDescription>
          </Alert>

          {/* New Deal Summary */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">New Deal Being Uploaded</h3>
            <div className="border rounded-lg p-4 bg-blue-50">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-blue-600" />
                    <span className="font-medium">Deal Name:</span>
                    <span>{formatValue(newDealData.deal_name)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Sponsor:</span>
                    <span>{formatValue(newDealData.sponsor_name)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-blue-600" />
                    <span className="font-medium">Location:</span>
                    <span>{getLocationString(newDealData)}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div>
                    <span className="font-medium">Property Type:</span>
                    <span className="ml-2">
                      {formatValue(newDealData.property_type)}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Capital Type:</span>
                    <span className="ml-2">
                      {formatValue(newDealData.capital_type)}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Deal Size:</span>
                    <span className="ml-2">
                      {newDealData.deal_size_min && newDealData.deal_size_max
                        ? `$${formatValue(
                            newDealData.deal_size_min
                          )} - $${formatValue(newDealData.deal_size_max)}`
                        : formatValue(
                            newDealData.deal_size_min ||
                              newDealData.deal_size_max
                          )}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Existing Deals */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">
              Existing Deal{duplicates.length > 1 ? "s" : ""} Found
            </h3>
            <div className="space-y-4">
              {duplicates.map((duplicate) => (
                <div
                  key={duplicate.deal_id}
                  className="border rounded-lg p-4 bg-yellow-50"
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="bg-yellow-100">
                        {Math.round(duplicate.similarity_score * 100)}% Match
                      </Badge>
                      <span className="text-sm text-gray-600">
                        {duplicate.match_reason}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <Calendar className="h-4 w-4" />
                      Created: {formatDate(duplicate.created_at)}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-yellow-600" />
                        <span className="font-medium">Deal Name:</span>
                        <span>{formatValue(duplicate.deal_name)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Sponsor:</span>
                        <span>{formatValue(duplicate.sponsor_name)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-yellow-600" />
                        <span className="font-medium">Location:</span>
                        <span>{getLocationString(duplicate)}</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div>
                        <span className="font-medium">Property Type:</span>
                        <span className="ml-2">
                          {formatValue(duplicate.property_type)}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium">Deal ID:</span>
                        <span className="ml-2">#{duplicate.deal_id}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Action Selection */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Choose Action</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <input
                  type="radio"
                  id="replace"
                  name="action"
                  value="replace"
                  checked={selectedAction === "replace"}
                  onChange={() => setSelectedAction("replace")}
                  className="mt-1"
                />
                <div className="space-y-1">
                  <label
                    htmlFor="replace"
                    className="font-medium cursor-pointer"
                  >
                    Keep New & Delete Old
                  </label>
                  <p className="text-sm text-gray-600">
                    Replace the existing deal with the new information. The old
                    deal will be marked as replaced.
                  </p>
                  {selectedAction === "replace" && duplicates.length > 1 && (
                    <div className="mt-2 ml-4 space-y-2">
                      <p className="text-sm font-medium">
                        Select which deal to replace:
                      </p>
                      {duplicates.map((duplicate) => (
                        <div
                          key={duplicate.deal_id}
                          className="flex items-center space-x-2"
                        >
                          <input
                            type="radio"
                            id={`replace-${duplicate.deal_id}`}
                            name="replaceDeal"
                            value={duplicate.deal_id}
                            checked={
                              selectedDealToReplace === duplicate.deal_id
                            }
                            onChange={() =>
                              setSelectedDealToReplace(duplicate.deal_id)
                            }
                          />
                          <label
                            htmlFor={`replace-${duplicate.deal_id}`}
                            className="text-sm cursor-pointer"
                          >
                            Deal #{duplicate.deal_id}:{" "}
                            {formatValue(duplicate.deal_name)}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                  {selectedAction === "replace" && duplicates.length === 1 && (
                    <div className="mt-2 ml-4">
                      <p className="text-sm text-gray-600">
                        Deal #{duplicates[0].deal_id} will be replaced with the
                        new information.
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <input
                  type="radio"
                  id="create_new"
                  name="action"
                  value="create_new"
                  checked={selectedAction === "create_new"}
                  onChange={() => setSelectedAction("create_new")}
                  className="mt-1"
                />
                <div className="space-y-1">
                  <label
                    htmlFor="create_new"
                    className="font-medium cursor-pointer"
                  >
                    Keep Both Deals
                  </label>
                  <p className="text-sm text-gray-600">
                    Create the new deal anyway. Both the existing and new deals
                    will be kept in the system.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex items-center justify-between">
          <Button variant="outline" onClick={onClose}>
            Cancel Upload
          </Button>
          <Button
            onClick={handleResolve}
            disabled={
              !selectedAction ||
              (selectedAction === "replace" &&
                duplicates.length > 1 &&
                !selectedDealToReplace)
            }
          >
            {selectedAction === "replace"
              ? "Keep New & Delete Old"
              : "Keep Both Deals"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
