"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { AlertTriangle, Calendar, MapPin, Building2, FileText } from "lucide-react";
import { PotentialDealDuplicate } from "@/lib/utils/dealConflictDetector";

interface JobConflictModalProps {
  isOpen: boolean;
  onClose: () => void;
  duplicates: PotentialDealDuplicate[];
  newDealData: any;
  jobId: string;
  onResolve: (action: "replace" | "create_new", targetDealId?: number) => void;
  isResolving: boolean;
}

export function JobConflictModal({
  isO<PERSON>,
  onClose,
  duplicates,
  newDealData,
  jobId,
  onResolve,
  isResolving,
}: JobConflictModalProps) {
  const [selectedDealId, setSelectedDealId] = useState<number | null>(null);

  const formatDate = (dateString: string) => {
    if (!dateString) return "Unknown";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (value: number | string | null): string => {
    if (value === null || value === undefined) return "N/A";
    const numValue = typeof value === "string" ? parseFloat(value) : value;
    if (isNaN(numValue)) return "N/A";
    if (numValue >= 1000) {
      return `$${(numValue / 1000).toFixed(1)}B`;
    }
    return `$${numValue.toFixed(1)}M`;
  };

  const handleReplace = () => {
    if (selectedDealId) {
      onResolve("replace", selectedDealId);
    }
  };

  const handleCreateNew = () => {
    onResolve("create_new");
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            Potential Duplicate Deal Detected
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              We found {duplicates.length} existing deal
              {duplicates.length > 1 ? "s" : ""} that might be the same as the
              one you're uploading. Please review the comparison below and
              choose how to proceed.
            </AlertDescription>
          </Alert>

          {/* Job Information */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <FileText className="h-4 w-4 text-blue-600" />
              <span className="font-semibold text-blue-900">Job Information</span>
            </div>
            <div className="text-sm text-blue-800">
              <p><strong>Job ID:</strong> {jobId}</p>
              <p><strong>Status:</strong> Processing completed with conflicts detected</p>
            </div>
          </div>

          {/* New Deal Summary */}
          <div className="border rounded-lg p-4 bg-green-50">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <span className="text-green-700">New Deal Data</span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Deal Name:</span>
                <p className="text-gray-900">{newDealData.deal_name || "Not specified"}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Sponsor:</span>
                <p className="text-gray-900">{newDealData.sponsor_name || "Not specified"}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Location:</span>
                <p className="text-gray-900">
                  {newDealData.neighborhood && (
                    <span className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {newDealData.neighborhood}
                    </span>
                  )}
                  {newDealData.zip_code && (
                    <span className="ml-2 text-gray-600">({newDealData.zip_code})</span>
                  )}
                </p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Property Type:</span>
                <p className="text-gray-900">{newDealData.property_type || "Not specified"}</p>
              </div>
              {newDealData.minimum_deal_size && (
                <div>
                  <span className="font-medium text-gray-700">Deal Size:</span>
                  <p className="text-gray-900">
                    {newDealData.maximum_deal_size
                      ? `${formatCurrency(newDealData.minimum_deal_size)} - ${formatCurrency(newDealData.maximum_deal_size)}`
                      : formatCurrency(newDealData.minimum_deal_size)}
                  </p>
                </div>
              )}
            </div>
          </div>

          <Separator />

          <div className="space-y-3">
            <h3 className="text-lg font-semibold">
              Existing Deal{duplicates.length > 1 ? "s" : ""} Found
            </h3>
            <div className="space-y-4">
              {duplicates.map((duplicate) => (
                <div
                  key={duplicate.deal_id}
                  className={`border rounded-lg p-4 ${
                    selectedDealId === duplicate.deal_id
                      ? "bg-blue-50 border-blue-300"
                      : "bg-yellow-50 border-yellow-200"
                  }`}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <input
                        type="radio"
                        name="selectedDeal"
                        value={duplicate.deal_id}
                        checked={selectedDealId === duplicate.deal_id}
                        onChange={(e) => setSelectedDealId(Number(e.target.value))}
                        className="text-blue-600"
                      />
                      <Badge variant="outline" className="bg-yellow-100">
                        {Math.round(duplicate.similarity_score * 100)}% Match
                      </Badge>
                      <span className="text-sm text-gray-600">
                        {duplicate.match_reason}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <Calendar className="h-4 w-4" />
                      Created: {formatDate(duplicate.created_at)}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">Deal Name:</span>
                      <p className="text-gray-900">{duplicate.deal_name || "Not specified"}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Sponsor:</span>
                      <p className="text-gray-900">{duplicate.sponsor_name || "Not specified"}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Location:</span>
                      <p className="text-gray-900">
                        {duplicate.neighborhood && (
                          <span className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {duplicate.neighborhood}
                          </span>
                        )}
                        {duplicate.zip_code && (
                          <span className="ml-2 text-gray-600">({duplicate.zip_code})</span>
                        )}
                        {duplicate.city && (
                          <span className="ml-2 text-gray-600">{duplicate.city}</span>
                        )}
                        {duplicate.state && (
                          <span className="ml-2 text-gray-600">{duplicate.state}</span>
                        )}
                      </p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Property Type:</span>
                      <p className="text-gray-900">{duplicate.property_type || "Not specified"}</p>
                    </div>
                  </div>

                  {duplicate.property_description && (
                    <div className="mt-3">
                      <span className="font-medium text-gray-700">Description:</span>
                      <p className="text-gray-900 text-sm mt-1">
                        {duplicate.property_description}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          <Separator />

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">How would you like to proceed?</h3>
            
            <div className="space-y-3">
              <div className="border rounded-lg p-4 bg-blue-50">
                <h4 className="font-semibold text-blue-900 mb-2">Option 1: Replace Existing Deal</h4>
                <p className="text-sm text-blue-800 mb-3">
                  Update the selected existing deal with the new data from your upload. 
                  This will preserve the deal ID but update all the information.
                </p>
                <Button
                  onClick={handleReplace}
                  disabled={!selectedDealId || isResolving}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isResolving ? "Processing..." : "Replace Selected Deal"}
                </Button>
              </div>

              <div className="border rounded-lg p-4 bg-green-50">
                <h4 className="font-semibold text-green-900 mb-2">Option 2: Create New Deal</h4>
                <p className="text-sm text-green-800 mb-3">
                  Create a new deal record despite the potential duplicates. 
                  This will create a new deal ID and keep all existing deals unchanged.
                </p>
                <Button
                  onClick={handleCreateNew}
                  disabled={isResolving}
                  variant="outline"
                  className="border-green-600 text-green-700 hover:bg-green-50"
                >
                  {isResolving ? "Processing..." : "Create New Deal"}
                </Button>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isResolving}
          >
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 