'use client'

import React from 'react'
import { format } from 'date-fns'

interface DataPoint {
  hour?: string
  date?: string
  count: number
}

interface DataQualityChartProps {
  data: DataPoint[]
  type: 'hourly' | 'daily'
  title: string
}

export function DataQualityChart({ data, type, title }: DataQualityChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        No data available
      </div>
    )
  }

  // Calculate max value for scaling
  const maxValue = Math.max(...data.map(d => d.count))
  const chartHeight = 200

  // Format the data for display
  const formattedData = data.slice(0, 20).reverse().map((item, index) => {
    let label = ''
    let fullLabel = ''
    
    if (type === 'daily' && item.date) {
      const date = new Date(item.date)
      label = format(date, 'MM/dd')
      fullLabel = format(date, 'MMM dd, yyyy')
    } else if (type === 'hourly' && item.hour) {
      const date = new Date(item.hour)
      label = format(date, 'HH:mm')
      fullLabel = format(date, 'MMM dd, HH:mm')
    }
    
    return {
      ...item,
      label,
      fullLabel,
      height: maxValue > 0 ? (item.count / maxValue) * chartHeight : 0
    }
  })

  return (
    <div className="w-full">
      <div className="mb-4">
        <h4 className="text-sm font-medium">{title}</h4>
        <p className="text-xs text-muted-foreground">
          {type === 'daily' ? 'Daily' : 'Hourly'} processing volume
        </p>
      </div>
      
      <div className="relative h-64 flex items-end justify-between bg-muted/20 rounded-lg p-4">
        {formattedData.map((item, index) => (
          <div
            key={index}
            className="group relative flex flex-col items-center"
            style={{ width: `${100 / formattedData.length}%` }}
          >
            {/* Tooltip */}
            <div className="absolute bottom-full mb-2 hidden group-hover:block bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap z-10">
              <div>{item.fullLabel}</div>
              <div className="font-medium">{item.count} records</div>
            </div>
            
            {/* Bar */}
            <div
              className="w-full max-w-8 bg-primary hover:bg-primary/80 rounded-t transition-colors"
              style={{ 
                height: `${item.height}px`,
                minHeight: item.count > 0 ? '2px' : '0px'
              }}
            />
            
            {/* Label */}
            <div className="mt-2 text-xs text-muted-foreground text-center transform -rotate-45 origin-top">
              {item.label}
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-4 flex justify-between text-xs text-muted-foreground">
        <span>0</span>
        <span>{maxValue} records</span>
      </div>
    </div>
  )
} 