import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { AlertTriangle, CheckCircle, TrendingUp, BarChart3, Database, Activity, Gauge, Sparkles, Target, DollarSign, FileText } from 'lucide-react'
import { DataQualityChart } from './DataQualityChart'

interface DealsDataQualityMetricsProps {
  data: any
  loading: boolean
  timeRange?: {
    startDate: string
    endDate: string
  }
}

export function DealsDataQualityMetrics({ data, loading, timeRange }: DealsDataQualityMetricsProps) {
  if (loading) {
    return <LoadingSkeleton />
  }

  if (!data) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">No data available</p>
        </CardContent>
      </Card>
    )
  }

  const { overview, debt, equity } = data

  // Debug: Log the data used in the detailed cards
  console.log('Overview Data Card data', overview);
  console.log('Debt Data Card data', debt);
  console.log('Equity Data Card data', equity);

  // Calculate overall data quality score
  const calculateDataQualityScore = (metrics: any) => {
    if (!metrics?.nullabilityMetrics) return 0
    const fields = Object.values(metrics.nullabilityMetrics)
    const totalScore = fields.reduce((sum: number, field: any) => {
      return sum + (100 - field.nullPercentage)
    }, 0)
    return Math.round(totalScore / fields.length)
  }

  const overviewQualityScore = calculateDataQualityScore(overview)
  const debtQualityScore = calculateDataQualityScore(debt)
  const equityQualityScore = calculateDataQualityScore(equity)
  const overallScore = Math.round((overviewQualityScore + debtQualityScore + equityQualityScore) / 3)

  const getQualityColor = (score: number) => {
    if (score >= 90) return 'text-emerald-600 bg-emerald-50 border-emerald-200'
    if (score >= 80) return 'text-blue-600 bg-blue-50 border-blue-200'
    if (score >= 70) return 'text-amber-600 bg-amber-50 border-amber-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  const getQualityIcon = (score: number) => {
    if (score >= 90) return <Sparkles className="h-5 w-5 text-emerald-600" />
    if (score >= 80) return <Target className="h-5 w-5 text-blue-600" />
    if (score >= 70) return <Gauge className="h-5 w-5 text-amber-600" />
    return <AlertTriangle className="h-5 w-5 text-red-600" />
  }

  const getQualityLabel = (score: number) => {
    if (score >= 90) return 'Exceptional'
    if (score >= 80) return 'Excellent'
    if (score >= 70) return 'Good'
    if (score >= 60) return 'Fair'
    return 'Needs Attention'
  }

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 border border-slate-200 p-8">
        <div className="relative z-10">
          <div className="flex items-start justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-slate-900 mb-2">Deals Data Quality Overview</h1>
              <p className="text-slate-600">Monitor and track deals and investment criteria data quality metrics</p>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-white/60 backdrop-blur-sm rounded-full border border-white/20">
              <Activity className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-slate-700">Live Monitoring</span>
            </div>
          </div>
          <div className="overflow-x-auto min-w-0">
            <div className="grid gap-6 md:grid-cols-4 min-w-[700px] sm:min-w-0">
              {/* Overall Quality Score */}
              <Card className={`border-2 transition-all duration-300 hover:shadow-lg min-w-0 overflow-visible ${getQualityColor(overallScore)}`}>
                <CardContent className="p-6 min-w-0 overflow-visible">
                  <div className="flex items-center justify-between mb-4 min-w-0 overflow-visible">
                    <div className="min-w-0 overflow-visible">
                      <p className="text-sm font-medium text-slate-600 mb-1 truncate" title="Overall Quality Score">Overall Quality Score</p>
                      <div className="flex items-center gap-2 min-w-0 overflow-visible">
                        {getQualityIcon(overallScore)}
                        <span className="text-3xl font-bold truncate" title={`${overallScore}%`}>{overallScore}%</span>
                      </div>
                    </div>
                  </div>
                  <Progress value={overallScore} className="mb-3" />
                  <p className="text-sm font-semibold truncate" title={getQualityLabel(overallScore)}>
                    {getQualityLabel(overallScore)}
                  </p>
                </CardContent>
              </Card>
              {/* Processing Throughput */}
              <Card className="border border-slate-200 bg-white/70 backdrop-blur-sm transition-all duration-300 hover:shadow-lg hover:bg-white/90 min-w-0 overflow-visible">
                <CardContent className="p-6 min-w-0 overflow-visible">
                  <div className="flex items-center justify-between mb-4 min-w-0 overflow-visible">
                    <div className="min-w-0 overflow-visible">
                      <p className="text-sm font-medium text-slate-600 mb-1 truncate" title="Processing Throughput">Processing Throughput</p>
                      <div className="flex items-center gap-2 min-w-0 overflow-visible">
                        <TrendingUp className="h-5 w-5 text-green-600" />
                        <span className="text-3xl font-bold text-slate-900 truncate" title={`${((overview?.recentActivity?.last24Hours || 0) + (debt?.recentActivity?.last24Hours || 0) + (equity?.recentActivity?.last24Hours || 0)).toLocaleString()}`}>{((overview?.recentActivity?.last24Hours || 0) + (debt?.recentActivity?.last24Hours || 0) + (equity?.recentActivity?.last24Hours || 0)).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                  <p className="text-sm font-semibold text-slate-700 truncate" title="Last 24 Hours">Last 24 Hours</p>
                  <div className="space-y-1 mt-2 min-w-0 overflow-visible">
                    <div className="flex justify-between text-xs min-w-0 overflow-visible">
                      <span className="text-slate-600 truncate" title="Overview:">Overview:</span>
                      <span className="font-semibold text-slate-900 truncate" title={`${(overview?.recentActivity?.last24Hours || 0).toLocaleString()}`}>{(overview?.recentActivity?.last24Hours || 0).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-xs min-w-0 overflow-visible">
                      <span className="text-slate-600 truncate" title="Debt:">Debt:</span>
                      <span className="font-semibold text-slate-900 truncate" title={`${(debt?.recentActivity?.last24Hours || 0).toLocaleString()}`}>{(debt?.recentActivity?.last24Hours || 0).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-xs min-w-0 overflow-visible">
                      <span className="text-slate-600 truncate" title="Equity:">Equity:</span>
                      <span className="font-semibold text-slate-900 truncate" title={`${(equity?.recentActivity?.last24Hours || 0).toLocaleString()}`}>{(equity?.recentActivity?.last24Hours || 0).toLocaleString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
              {/* Data Quality Score */}
              <Card className="border border-slate-200 bg-white/70 backdrop-blur-sm transition-all duration-300 hover:shadow-lg hover:bg-white/90 min-w-0 overflow-visible">
                <CardContent className="p-6 min-w-0 overflow-visible">
                  <div className="flex items-center justify-between mb-4 min-w-0 overflow-visible">
                    <div className="min-w-0 overflow-visible">
                      <p className="text-sm font-medium text-slate-600 mb-1 truncate" title="Data Quality Score">Data Quality Score</p>
                      <div className="flex items-center gap-2 min-w-0 overflow-visible">
                        <BarChart3 className="h-5 w-5 text-purple-600" />
                        <span className="text-3xl font-bold text-slate-900 truncate" title={`${Math.round((overviewQualityScore + debtQualityScore + equityQualityScore) / 3)}%`}>{Math.round((overviewQualityScore + debtQualityScore + equityQualityScore) / 3)}%</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2 min-w-0 overflow-visible">
                    <div className="flex justify-between text-sm min-w-0 overflow-visible">
                      <span className="text-slate-600 truncate" title="Overview:">Overview:</span>
                      <span className="font-semibold text-slate-900 truncate" title={`${overviewQualityScore}%`}>{overviewQualityScore}%</span>
                    </div>
                    <div className="flex justify-between text-sm min-w-0 overflow-visible">
                      <span className="text-slate-600 truncate" title="Debt:">Debt:</span>
                      <span className="font-semibold text-slate-900 truncate" title={`${debtQualityScore}%`}>{debtQualityScore}%</span>
                    </div>
                    <div className="flex justify-between text-sm min-w-0 overflow-visible">
                      <span className="text-slate-600 truncate" title="Equity:">Equity:</span>
                      <span className="font-semibold text-slate-900 truncate" title={`${equityQualityScore}%`}>{equityQualityScore}%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card className="border border-slate-200 bg-white/70 backdrop-blur-sm transition-all duration-300 hover:shadow-lg hover:bg-white/90 min-w-0 overflow-visible">
                <CardContent className="p-6 min-w-0 overflow-visible">
                  <div className="flex items-center justify-between mb-4 min-w-0 overflow-visible">
                    <div className="min-w-0 overflow-visible">
                      <p className="text-sm font-medium text-slate-600 mb-1 truncate" title="Total Records">Total Records</p>
                      <div className="flex items-center gap-2 min-w-0 overflow-visible">
                        <Database className="h-5 w-5 text-blue-600" />
                        <span className="text-3xl font-bold text-slate-900 truncate" title={`${((overview?.totalRecords || 0) + (debt?.totalRecords || 0) + (equity?.totalRecords || 0)).toLocaleString()}`}>{((overview?.totalRecords || 0) + (debt?.totalRecords || 0) + (equity?.totalRecords || 0)).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2 min-w-0 overflow-visible">
                    <div className="flex justify-between text-sm min-w-0 overflow-visible">
                      <span className="text-slate-600 truncate" title="Overview:">Overview:</span>
                      <span className="font-semibold text-slate-900 truncate" title={`${(overview?.totalRecords || 0).toLocaleString()}`}>{(overview?.totalRecords || 0).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm min-w-0 overflow-visible">
                      <span className="text-slate-600 truncate" title="Debt:">Debt:</span>
                      <span className="font-semibold text-slate-900 truncate" title={`${(debt?.totalRecords || 0).toLocaleString()}`}>{(debt?.totalRecords || 0).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm min-w-0 overflow-visible">
                      <span className="text-slate-600 truncate" title="Equity:">Equity:</span>
                      <span className="font-semibold text-slate-900 truncate" title={`${(equity?.totalRecords || 0).toLocaleString()}`}>{(equity?.totalRecords || 0).toLocaleString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div> {/* <-- Close .relative.z-10 */}
        {/* Background decoration */}
        <div className="absolute top-0 right-0 -mt-4 -mr-4 h-32 w-32 rounded-full bg-gradient-to-br from-indigo-200 to-blue-300 opacity-20"></div>
        <div className="absolute bottom-0 left-0 -mb-4 -ml-4 h-24 w-24 rounded-full bg-gradient-to-br from-slate-200 to-slate-300 opacity-20"></div>
      </div>
      {/* Detailed Metrics */}
      <div className="grid gap-8 lg:grid-cols-2">
        {/* Overview Details */}
        <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-all duration-300 bg-gradient-to-br from-white to-slate-50">
          <CardHeader className="border-b border-slate-100 bg-white/80 rounded-t-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-blue-100 border border-blue-200">
                  <DollarSign className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-lg font-semibold text-slate-900">Overview Data</CardTitle>
                  <CardDescription className="text-slate-600">
                    {(overview?.totalRecords || 0).toLocaleString()} overview records
                  </CardDescription>
                </div>
              </div>
              <Badge 
                variant="outline" 
                className={`px-3 py-1 font-semibold border-2 ${getQualityColor(overviewQualityScore)}`}
              >
                {overviewQualityScore}%
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            {overview?.nullabilityMetrics && (
              <div className="space-y-4">
                <h4 className="font-medium text-slate-800 flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-blue-600" />
                  Field Completeness Analysis
                </h4>
                <div className="max-h-[400px] overflow-y-auto pr-2 space-y-3">
                  {Object.entries(overview.nullabilityMetrics).map(([fieldName, metrics]: [string, any]) => {
                    const completionRate = 100 - metrics.nullPercentage
                    const getFieldQualityColor = (rate: number) => {
                      if (rate >= 90) return 'text-emerald-700 bg-emerald-50 border-emerald-200'
                      if (rate >= 70) return 'text-blue-700 bg-blue-50 border-blue-200'
                      if (rate >= 50) return 'text-amber-700 bg-amber-50 border-amber-200'
                      return 'text-red-700 bg-red-50 border-red-200'
                    }
                    return (
                      <div key={fieldName} className={`p-4 rounded-lg border-2 transition-all duration-200 hover:shadow-sm ${getFieldQualityColor(completionRate)} max-w-full`}> 
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm font-semibold capitalize truncate max-w-xs" title={fieldName.replace(/_/g, ' ')}>
                            {fieldName.replace(/_/g, ' ')}
                          </span>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-bold">{completionRate.toFixed(1)}%</span>
                            {completionRate >= 90 && <CheckCircle className="h-4 w-4 text-emerald-600" />}
                            {completionRate < 50 && <AlertTriangle className="h-4 w-4 text-red-600" />}
                          </div>
                        </div>
                        <Progress value={completionRate} className="mb-2" />
                        <div className="flex justify-between text-xs text-slate-600">
                          <span className="truncate max-w-xs" title={`${metrics.totalRecords - metrics.nullCount} complete`}>
                            {metrics.totalRecords - metrics.nullCount} complete
                          </span>
                          <span className="truncate max-w-xs" title={`${metrics.nullCount} missing`}>
                            {metrics.nullCount} missing
                          </span>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        {/* Debt Details */}
        <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-all duration-300 bg-gradient-to-br from-white to-slate-50">
          <CardHeader className="border-b border-slate-100 bg-white/80 rounded-t-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-indigo-100 border border-indigo-200">
                  <FileText className="h-5 w-5 text-indigo-600" />
                </div>
                <div>
                  <CardTitle className="text-lg font-semibold text-slate-900">Debt Data</CardTitle>
                  <CardDescription className="text-slate-600">
                    {(debt?.totalRecords || 0).toLocaleString()} debt records
                    <span className="block text-xs text-blue-700 mt-1 font-medium">
                      (Only debt linked to deals are shown. <b>entity_type = 'Deal'</b>)
                    </span>
                  </CardDescription>
                </div>
              </div>
              <Badge 
                variant="outline" 
                className={`px-3 py-1 font-semibold border-2 ${getQualityColor(debtQualityScore)}`}
              >
                {debtQualityScore}%
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            {debt?.nullabilityMetrics && (
              <div className="space-y-4">
                <h4 className="font-medium text-slate-800 flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-indigo-600" />
                  Field Completeness Analysis
                </h4>
                <div className="max-h-[400px] overflow-y-auto pr-2 space-y-3">
                  {Object.entries(debt.nullabilityMetrics).map(([fieldName, metrics]: [string, any]) => {
                    const completionRate = 100 - metrics.nullPercentage
                    const getFieldQualityColor = (rate: number) => {
                      if (rate >= 90) return 'text-emerald-700 bg-emerald-50 border-emerald-200'
                      if (rate >= 70) return 'text-indigo-700 bg-indigo-50 border-indigo-200'
                      if (rate >= 50) return 'text-amber-700 bg-amber-50 border-amber-200'
                      return 'text-red-700 bg-red-50 border-red-200'
                    }
                    return (
                      <div key={fieldName} className={`p-4 rounded-lg border-2 transition-all duration-200 hover:shadow-sm ${getFieldQualityColor(completionRate)} max-w-full`}>
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm font-semibold capitalize truncate max-w-xs" title={fieldName.replace(/_/g, ' ')}>
                            {fieldName.replace(/_/g, ' ')}
                          </span>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-bold">{completionRate.toFixed(1)}%</span>
                            {completionRate >= 90 && <CheckCircle className="h-4 w-4 text-emerald-600" />}
                            {completionRate < 50 && <AlertTriangle className="h-4 w-4 text-red-600" />}
                          </div>
                        </div>
                        <Progress value={completionRate} className="mb-2" />
                        <div className="flex justify-between text-xs text-slate-600">
                          <span className="truncate max-w-xs" title={`${metrics.totalRecords - metrics.nullCount} complete`}>
                            {metrics.totalRecords - metrics.nullCount} complete
                          </span>
                          <span className="truncate max-w-xs" title={`${metrics.nullCount} missing`}>
                            {metrics.nullCount} missing
                          </span>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        {/* Equity Details */}
        <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-all duration-300 bg-gradient-to-br from-white to-slate-50">
          <CardHeader className="border-b border-slate-100 bg-white/80 rounded-t-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-green-100 border border-green-200">
                  <Sparkles className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <CardTitle className="text-lg font-semibold text-slate-900">Equity Data</CardTitle>
                  <CardDescription className="text-slate-600">
                    {(equity?.totalRecords || 0).toLocaleString()} equity records
                    <span className="block text-xs text-green-700 mt-1 font-medium">
                      (Only equity linked to deals are shown. <b>entity_type = 'Deal'</b>)
                    </span>
                  </CardDescription>
                </div>
              </div>
              <Badge 
                variant="outline" 
                className={`px-3 py-1 font-semibold border-2 ${getQualityColor(equityQualityScore)}`}
              >
                {equityQualityScore}%
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            {equity?.nullabilityMetrics && (
              <div className="space-y-4">
                <h4 className="font-medium text-slate-800 flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-green-600" />
                  Field Completeness Analysis
                </h4>
                <div className="max-h-[400px] overflow-y-auto pr-2 space-y-3">
                  {Object.entries(equity.nullabilityMetrics).map(([fieldName, metrics]: [string, any]) => {
                    const completionRate = 100 - metrics.nullPercentage
                    const getFieldQualityColor = (rate: number) => {
                      if (rate >= 90) return 'text-emerald-700 bg-emerald-50 border-emerald-200'
                      if (rate >= 70) return 'text-green-700 bg-green-50 border-green-200'
                      if (rate >= 50) return 'text-amber-700 bg-amber-50 border-amber-200'
                      return 'text-red-700 bg-red-50 border-red-200'
                    }
                    return (
                      <div key={fieldName} className={`p-4 rounded-lg border-2 transition-all duration-200 hover:shadow-sm ${getFieldQualityColor(completionRate)} max-w-full`}>
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm font-semibold capitalize truncate max-w-xs" title={fieldName.replace(/_/g, ' ')}>
                            {fieldName.replace(/_/g, ' ')}
                          </span>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-bold">{completionRate.toFixed(1)}%</span>
                            {completionRate >= 90 && <CheckCircle className="h-4 w-4 text-emerald-600" />}
                            {completionRate < 50 && <AlertTriangle className="h-4 w-4 text-red-600" />}
                          </div>
                        </div>
                        <Progress value={completionRate} className="mb-2" />
                        <div className="flex justify-between text-xs text-slate-600">
                          <span className="truncate max-w-xs" title={`${metrics.totalRecords - metrics.nullCount} complete`}>
                            {metrics.totalRecords - metrics.nullCount} complete
                          </span>
                          <span className="truncate max-w-xs" title={`${metrics.nullCount} missing`}>
                            {metrics.nullCount} missing
                          </span>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      {/* Timeline/Throughput Chart (optional) */}
      {overview?.throughputMetrics && (
        <Card className="border border-slate-200 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-slate-900">Processing Timeline</CardTitle>
            <CardDescription>Deal processing activity over time</CardDescription>
          </CardHeader>
          <CardContent>
            <DataQualityChart 
              data={overview.throughputMetrics.daily} 
              type="daily"
              title="Deals Processing Activity"
            />
          </CardContent>
        </Card>
      )}
    </div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="space-y-8">
      <Skeleton className="h-32 w-full rounded-xl" />
      <div className="grid gap-8 lg:grid-cols-2">
        <Skeleton className="h-64 w-full rounded-xl" />
        <Skeleton className="h-64 w-full rounded-xl" />
      </div>
    </div>
  )
}