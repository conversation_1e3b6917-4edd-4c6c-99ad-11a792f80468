'use client'

import React, { useState } from 'react'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Activity, 
  BarChart3, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  AlertTriangle,
  Shield,
  Gauge,
  Target
} from 'lucide-react'
import { DataQualityChart } from './DataQualityChart'
import { NullabilityTable } from './NullabilityTable'

interface ContactEnrichmentMetricsProps {
  data: any
  loading: boolean
  timeRange?: {
    startDate: string
    endDate: string
  }
  startTimestamp?: number
  endTimestamp?: number
}

export function ContactEnrichmentMetrics({ 
  data, 
  loading, 
  timeRange,
  startTimestamp,
  endTimestamp 
}: ContactEnrichmentMetricsProps) {
  const [timelineView, setTimelineView] = useState<'hourly' | 'daily'>('daily')

  if (loading) {
    return <LoadingSkeleton />
  }

  if (!data) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border border-gray-200">
        <p className="text-gray-500">No data available</p>
      </div>
    )
  }

  const { contactEnrichment, mappingValidation } = data

  // Calculate metrics
  const calculateDataQualityScore = (metrics: any) => {
    if (!metrics?.nullabilityMetrics) return 0
    
    const fields = Object.values(metrics.nullabilityMetrics)
    const totalScore = fields.reduce((sum: number, field: any) => {
      return sum + (100 - field.nullPercentage)
    }, 0)
    
    return Math.round(totalScore / fields.length)
  }

  const dataQualityScore = calculateDataQualityScore(contactEnrichment)
  const mappingScore = Math.round(mappingValidation?.contactEnrichment?.overallValidation?.overall_mapping_validation_percentage || 0)

  return (
    <div className="space-y-6">
      {/* Key Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricPanel
          title="Data Quality Score"
          value={`${dataQualityScore}%`}
          icon={<Gauge className="h-4 w-4" />}
          color="blue"
          progress={dataQualityScore}
        />
        <MetricPanel
          title="Mapping Validation"
          value={`${mappingScore}%`}
          icon={<Shield className="h-4 w-4" />}
          color="green"
          progress={mappingScore}
        />
        <MetricPanel
          title="Processing Status"
          value={`${contactEnrichment?.statusMetrics?.completed || 0}`}
          icon={<CheckCircle className="h-4 w-4" />}
          color="purple"
          subtitle="Completed"
        />
        <MetricPanel
          title="24h Throughput"
          value={`${contactEnrichment?.recentActivity?.last24Hours || 0}`}
          icon={<TrendingUp className="h-4 w-4" />}
          color="orange"
          subtitle="Records processed"
        />
      </div>

      {/* Charts Row */}

      {/* Detailed Analysis Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Data Completeness */}
        <GrafanaPanel 
          title="Data Completeness"
          subtitle="Field-level completeness analysis"
          icon={<Target className="h-4 w-4" />}
        >
          <div className="space-y-4">
            <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-blue-900">Overall Completeness</span>
                <span className="text-lg font-bold text-blue-900">{dataQualityScore}%</span>
              </div>
              <Progress value={dataQualityScore} className="mt-2" />
            </div>
            
            {contactEnrichment?.nullabilityMetrics && (
              <div className="max-h-64 overflow-y-auto">
                <NullabilityTable 
                  metrics={contactEnrichment.nullabilityMetrics}
                  title=""
                />
              </div>
            )}
          </div>
        </GrafanaPanel>

        {/* Mapping Validation */}
        <GrafanaPanel 
          title="Mapping Validation"
          subtitle="Central mapping table validation"
          icon={<Shield className="h-4 w-4" />}
        >
          <div className="space-y-4">
            <div className="bg-green-50 p-3 rounded-lg border border-green-200">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-green-900">Validation Rate</span>
                <span className="text-lg font-bold text-green-900">{mappingScore}%</span>
              </div>
              <Progress value={mappingScore} className="mt-2" />
            </div>
            
            {mappingValidation?.contactEnrichment?.overallValidation && (
              <div className="space-y-3">
                <div className="bg-white p-3 rounded-lg border border-gray-200">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-gray-600">Company Type Mapping</span>
                    <span className="text-sm font-semibold text-gray-900">
                      {mappingValidation.contactEnrichment.overallValidation.valid_company_types || 0} / {mappingValidation.contactEnrichment.overallValidation.records_with_company_type || 0}
                    </span>
                  </div>
                  <Progress 
                    value={mappingValidation.contactEnrichment.overallValidation.company_type_validation_percentage || 0} 
                    className="h-2"
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    {mappingValidation.contactEnrichment.overallValidation.company_type_validation_percentage || 0}% valid mappings
                  </div>
                </div>
                
                <div className="bg-white p-3 rounded-lg border border-gray-200">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-gray-600">Capital Position Mapping</span>
                    <span className="text-sm font-semibold text-gray-900">
                      {mappingValidation.contactEnrichment.overallValidation.records_with_capital_positions || 0} / {mappingValidation.contactEnrichment.overallValidation.total_records || 0}
                    </span>
                  </div>
                  <Progress 
                    value={mappingValidation.contactEnrichment.overallValidation.records_with_capital_positions ? 
                      ((mappingValidation.contactEnrichment.overallValidation.records_with_capital_positions / (mappingValidation.contactEnrichment.overallValidation.total_records || 1)) * 100) : 0} 
                    className="h-2"
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    {mappingValidation.contactEnrichment.overallValidation.records_with_capital_positions ? 
                      Math.round((mappingValidation.contactEnrichment.overallValidation.records_with_capital_positions / (mappingValidation.contactEnrichment.overallValidation.total_records || 1)) * 100) : 0}% with capital positions
                  </div>
                </div>
              </div>
            )}
          </div>
        </GrafanaPanel>
      </div>

      {/* Timeline View Toggle */}
      <div className="flex items-center gap-2 mb-4">
        <Button 
          variant={timelineView === 'hourly' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setTimelineView('hourly')}
          className="h-8 px-3 text-xs"
        >
          <Clock className="h-3 w-3 mr-1" />
          Hourly
        </Button>
        <Button 
          variant={timelineView === 'daily' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setTimelineView('daily')}
          className="h-8 px-3 text-xs"
        >
          <BarChart3 className="h-3 w-3 mr-1" />
          Daily
        </Button>
      </div>

      {/* Additional Timeline Chart */}
      {contactEnrichment?.throughputMetrics && (
        <GrafanaPanel 
          title={`Processing Timeline (${timelineView === 'hourly' ? 'Hourly' : 'Daily'})`}
          subtitle={`${timelineView === 'hourly' ? 'Last 48 Hours' : 'Last 30 Days'} enrichment processing throughput`}
          icon={<TrendingUp className="h-4 w-4" />}
        >
          <div className="h-64">
            <DataQualityChart 
              data={timelineView === 'hourly' 
                ? contactEnrichment.throughputMetrics.hourly 
                : contactEnrichment.throughputMetrics.daily
              }
              type={timelineView}
              title={`Contact Enrichment Processing (${timelineView === 'hourly' ? 'Last 48 Hours' : 'Last 30 Days'})`}
            />
          </div>
        </GrafanaPanel>
      )}
    </div>
  )
}

// Helper Components
interface MetricPanelProps {
  title: string
  value: string
  icon: React.ReactNode
  color: 'blue' | 'green' | 'purple' | 'orange'
  progress?: number
  subtitle?: string
}

function MetricPanel({ title, value, icon, color, progress, subtitle }: MetricPanelProps) {
  const colorClasses = {
    blue: 'bg-blue-50 border-blue-200 text-blue-700',
    green: 'bg-green-50 border-green-200 text-green-700',
    purple: 'bg-purple-50 border-purple-200 text-purple-700',
    orange: 'bg-orange-50 border-orange-200 text-orange-700'
  }

  return (
    <div className={`p-4 rounded-lg border-2 ${colorClasses[color]}`}>
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium">{title}</span>
        {icon}
      </div>
      <div className="text-2xl font-bold mb-1">{value}</div>
      {subtitle && <div className="text-xs opacity-75">{subtitle}</div>}
      {progress !== undefined && (
        <Progress value={progress} className="mt-2 h-1" />
      )}
    </div>
  )
}

interface GrafanaPanelProps {
  title: string
  subtitle?: string
  icon?: React.ReactNode
  children: React.ReactNode
}

function GrafanaPanel({ title, subtitle, icon, children }: GrafanaPanelProps) {
  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className="px-4 py-3 border-b border-gray-200">
        <div className="flex items-center gap-2">
          {icon}
          <div>
            <h3 className="text-sm font-medium text-gray-900">{title}</h3>
            {subtitle && <p className="text-xs text-gray-600">{subtitle}</p>}
          </div>
        </div>
      </div>
      <div className="p-4">
        {children}
      </div>
    </div>
  )
}

function getStatusColor(status: string) {
  switch (status) {
    case 'completed': return 'bg-green-500'
    case 'processing': return 'bg-blue-500'
    case 'failed': return 'bg-red-500'
    default: return 'bg-gray-500'
  }
}

function LoadingSkeleton() {
  return (
    <div className="space-y-6">
      {/* Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="p-4 bg-white rounded-lg border border-gray-200">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </div>
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-1 w-full" />
            </div>
          </div>
        ))}
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {[...Array(2)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg border border-gray-200">
            <div className="px-4 py-3 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-4 w-32" />
              </div>
            </div>
            <div className="p-4">
              <Skeleton className="h-64 w-full" />
            </div>
          </div>
        ))}
      </div>

      {/* Additional panels */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {[...Array(2)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg border border-gray-200">
            <div className="px-4 py-3 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-4 w-32" />
              </div>
            </div>
            <div className="p-4">
              <Skeleton className="h-48 w-full" />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
} 