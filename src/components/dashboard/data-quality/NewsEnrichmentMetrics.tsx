'use client'

import React, { useState } from 'react'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Activity, 
  BarChart3, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  AlertTriangle,
  Shield,
  Gauge,
  Target,
  Database,
  Users,
  FileText,
  Building,
  User,
  DollarSign,
  Newspaper
} from 'lucide-react'
import { DataQualityChart } from './DataQualityChart'
import { NullabilityTable } from './NullabilityTable'

interface NewsEnrichmentMetricsProps {
  data: any
  loading: boolean
  timeRange?: {
    startDate: string
    endDate: string
  }
  startTimestamp?: number
  endTimestamp?: number
}

export function NewsEnrichmentMetrics({ 
  data, 
  loading, 
  timeRange,
  startTimestamp,
  endTimestamp 
}: NewsEnrichmentMetricsProps) {
  const [timelineView, setTimelineView] = useState<'hourly' | 'daily'>('daily')

  if (loading) {
    return <LoadingSkeleton />
  }

  if (!data) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border border-gray-200">
        <p className="text-gray-500">No data available</p>
      </div>
    )
  }

  const { newsData, dealsData, companiesData, personsData, mappingValidation } = data

  // Calculate overall data quality score
  const calculateDataQualityScore = (metrics: any) => {
    if (!metrics?.nullabilityMetrics) return 0
    
    const fields = Object.values(metrics.nullabilityMetrics)
    const totalScore = fields.reduce((sum: number, field: any) => {
      return sum + (100 - field.nullPercentage)
    }, 0)
    
    return Math.round(totalScore / fields.length)
  }

  const newsQualityScore = calculateDataQualityScore(newsData)
  const dealsQualityScore = calculateDataQualityScore(dealsData)
  const companiesQualityScore = calculateDataQualityScore(companiesData)
  const personsQualityScore = calculateDataQualityScore(personsData)
  const overallScore = Math.round((newsQualityScore + dealsQualityScore + companiesQualityScore + personsQualityScore) / 4)
  const mappingScore = Math.round(mappingValidation?.overallValidation?.overall_mapping_validation_percentage || 0)

  const totalRecords = (newsData?.totalRecords || 0) + (dealsData?.totalRecords || 0) + (companiesData?.totalRecords || 0) + (personsData?.totalRecords || 0)
  const throughput24h = newsData?.recentActivity?.last24Hours || 0

  return (
    <div className="space-y-6">
      {/* Key Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricPanel
          title="Data Quality Score"
          value={`${overallScore}%`}
          icon={<Gauge className="h-4 w-4" />}
          color="blue"
          progress={overallScore}
        />
        <MetricPanel
          title="Total Records"
          value={totalRecords.toLocaleString()}
          icon={<Database className="h-4 w-4" />}
          color="green"
          subtitle="Across all sources"
        />
        <MetricPanel
          title="Processing Status"
          value={`${totalRecords}`}
          icon={<CheckCircle className="h-4 w-4" />}
          color="purple"
          subtitle="Completed"
        />
        <MetricPanel
          title="24h Throughput"
          value={`${throughput24h}`}
          icon={<TrendingUp className="h-4 w-4" />}
          color="orange"
          subtitle="Articles processed"
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Processing Timeline */}
        <GrafanaPanel 
          title="Processing Timeline"
          subtitle="Daily news enrichment throughput"
          icon={<BarChart3 className="h-4 w-4" />}
        >
          <div className="h-64">
            {newsData?.throughputMetrics?.daily ? (
              <DataQualityChart 
                data={newsData.throughputMetrics.daily}
                type="daily"
                title="Daily Processing Volume"
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-2">
                  <BarChart3 className="h-12 w-12 text-gray-300 mx-auto" />
                  <p className="text-gray-500 font-medium">No throughput data available</p>
                  <p className="text-sm text-gray-400">Data will appear as processing occurs</p>
                </div>
              </div>
            )}
          </div>
        </GrafanaPanel>

        {/* Status Distribution */}
        <GrafanaPanel 
          title="Status Distribution"
          subtitle="Processing status breakdown"
          icon={<Activity className="h-4 w-4" />}
        >
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-green-500" />
                <span className="text-sm text-gray-700">Completed</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">{totalRecords}</span>
                <span className="text-xs text-gray-500">(100.0%)</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-red-500" />
                <span className="text-sm text-gray-700">Failed</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">0</span>
                <span className="text-xs text-gray-500">(0.0%)</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-blue-500" />
                <span className="text-sm text-gray-700">Processing</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">0</span>
                <span className="text-xs text-gray-500">(0.0%)</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-gray-500" />
                <span className="text-sm text-gray-700">Pending</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">0</span>
                <span className="text-xs text-gray-500">(0.0%)</span>
              </div>
            </div>
          </div>
        </GrafanaPanel>
      </div>

      {/* Detailed Analysis Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Data Completeness */}
        <GrafanaPanel 
          title="Data Completeness"
          subtitle="Field-level completeness analysis"
          icon={<Target className="h-4 w-4" />}
        >
          <div className="space-y-4">
            <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-blue-900">Overall Completeness</span>
                <span className="text-lg font-bold text-blue-900">{overallScore}%</span>
              </div>
              <Progress value={overallScore} className="mt-2" />
            </div>
            
            {newsData?.nullabilityMetrics && (
              <div className="max-h-64 overflow-y-auto">
                <NullabilityTable 
                  metrics={newsData.nullabilityMetrics}
                  title=""
                />
              </div>
            )}
          </div>
        </GrafanaPanel>

        {/* Source Distribution */}
        <GrafanaPanel 
          title="Source Distribution"
          subtitle="News enrichment source breakdown"
          icon={<Newspaper className="h-4 w-4" />}
        >
          <div className="space-y-4">
            <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-purple-900">Source Quality</span>
                <span className="text-lg font-bold text-purple-900">{newsQualityScore}%</span>
              </div>
              <Progress value={newsQualityScore} className="mt-2" />
            </div>
            
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-white p-3 rounded-lg border border-gray-200">
                <div className="text-xs text-gray-600">News Articles</div>
                <div className="text-lg font-semibold text-gray-900">
                  {(newsData?.totalRecords || 0).toLocaleString()}
                </div>
                <div className="text-xs text-gray-500">Enriched articles</div>
              </div>
              <div className="bg-white p-3 rounded-lg border border-gray-200">
                <div className="text-xs text-gray-600">Extracted Deals</div>
                <div className="text-lg font-semibold text-gray-900">
                  {(dealsData?.totalRecords || 0).toLocaleString()}
                </div>
                <div className="text-xs text-gray-500">Deal records</div>
              </div>
              <div className="bg-white p-3 rounded-lg border border-gray-200">
                <div className="text-xs text-gray-600">Companies</div>
                <div className="text-lg font-semibold text-gray-900">
                  {(companiesData?.totalRecords || 0).toLocaleString()}
                </div>
                <div className="text-xs text-gray-500">Company mentions</div>
              </div>
              <div className="bg-white p-3 rounded-lg border border-gray-200">
                <div className="text-xs text-gray-600">People</div>
                <div className="text-lg font-semibold text-gray-900">
                  {(personsData?.totalRecords || 0).toLocaleString()}
                </div>
                <div className="text-xs text-gray-500">Person mentions</div>
              </div>
            </div>
          </div>
        </GrafanaPanel>
      </div>

      {/* Mapping Validation Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Deal Types Mapping */}
        <GrafanaPanel 
          title="Deal Types Mapping Validation"
          subtitle="Deal types mapping validation from central mapping"
          icon={<Shield className="h-4 w-4" />}
        >
          <div className="space-y-4">
            <div className="bg-green-50 p-3 rounded-lg border border-green-200">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-green-900">Overall Validation Rate</span>
                <span className="text-lg font-bold text-green-900">{mappingScore}%</span>
              </div>
              <Progress value={mappingScore} className="mt-2" />
            </div>
            
            {mappingValidation?.overallValidation && (
              <div className="space-y-3">
                <div className="bg-white p-3 rounded-lg border border-gray-200">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-gray-600">Deal Types Mapping</span>
                    <span className="text-sm font-semibold text-gray-900">
                      {mappingValidation.overallValidation.records_with_deal_types || 0} / {mappingValidation.overallValidation.total_records || 0}
                    </span>
                  </div>
                  <Progress 
                    value={mappingValidation.overallValidation.records_with_deal_types ? 
                      ((mappingValidation.overallValidation.records_with_deal_types / (mappingValidation.overallValidation.total_records || 1)) * 100) : 0} 
                    className="h-2"
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    {mappingValidation.overallValidation.records_with_deal_types ? 
                      Math.round((mappingValidation.overallValidation.records_with_deal_types / (mappingValidation.overallValidation.total_records || 1)) * 100) : 0}% with deal types
                  </div>
                </div>
                
                <div className="bg-white p-3 rounded-lg border border-gray-200">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-gray-600">Capital Positions Mapping</span>
                    <span className="text-sm font-semibold text-gray-900">
                      {mappingValidation.overallValidation.records_with_capital_positions || 0} / {mappingValidation.overallValidation.total_records || 0}
                    </span>
                  </div>
                  <Progress 
                    value={mappingValidation.overallValidation.records_with_capital_positions ? 
                      ((mappingValidation.overallValidation.records_with_capital_positions / (mappingValidation.overallValidation.total_records || 1)) * 100) : 0} 
                    className="h-2"
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    {mappingValidation.overallValidation.records_with_capital_positions ? 
                      Math.round((mappingValidation.overallValidation.records_with_capital_positions / (mappingValidation.overallValidation.total_records || 1)) * 100) : 0}% with capital positions
                  </div>
                </div>
              </div>
            )}
          </div>
        </GrafanaPanel>

        {/* Property Types and Strategies Mapping */}
        <GrafanaPanel 
          title="Property Types & Strategies Mapping"
          subtitle="Property types and strategies mapping validation"
          icon={<Target className="h-4 w-4" />}
        >
          <div className="space-y-4">
            <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-blue-900">Mapping Quality</span>
                <span className="text-lg font-bold text-blue-900">{mappingScore}%</span>
              </div>
              <Progress value={mappingScore} className="mt-2" />
            </div>
            
            {mappingValidation?.overallValidation && (
              <div className="space-y-3">
                <div className="bg-white p-3 rounded-lg border border-gray-200">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-gray-600">Property Types Mapping</span>
                    <span className="text-sm font-semibold text-gray-900">
                      {mappingValidation.overallValidation.records_with_property_types || 0} / {mappingValidation.overallValidation.total_records || 0}
                    </span>
                  </div>
                  <Progress 
                    value={mappingValidation.overallValidation.records_with_property_types ? 
                      ((mappingValidation.overallValidation.records_with_property_types / (mappingValidation.overallValidation.total_records || 1)) * 100) : 0} 
                    className="h-2"
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    {mappingValidation.overallValidation.records_with_property_types ? 
                      Math.round((mappingValidation.overallValidation.records_with_property_types / (mappingValidation.overallValidation.total_records || 1)) * 100) : 0}% with property types
                  </div>
                </div>
                
                <div className="bg-white p-3 rounded-lg border border-gray-200">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-gray-600">Strategies Mapping</span>
                    <span className="text-sm font-semibold text-gray-900">
                      {mappingValidation.overallValidation.records_with_strategies || 0} / {mappingValidation.overallValidation.total_records || 0}
                    </span>
                  </div>
                  <Progress 
                    value={mappingValidation.overallValidation.records_with_strategies ? 
                      ((mappingValidation.overallValidation.records_with_strategies / (mappingValidation.overallValidation.total_records || 1)) * 100) : 0} 
                    className="h-2"
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    {mappingValidation.overallValidation.records_with_strategies ? 
                      Math.round((mappingValidation.overallValidation.records_with_strategies / (mappingValidation.overallValidation.total_records || 1)) * 100) : 0}% with strategies
                  </div>
                </div>
              </div>
            )}
          </div>
        </GrafanaPanel>
      </div>
    </div>
  )
}

// Helper Components
interface MetricPanelProps {
  title: string
  value: string
  icon: React.ReactNode
  color: 'blue' | 'green' | 'purple' | 'orange'
  progress?: number
  subtitle?: string
}

function MetricPanel({ title, value, icon, color, progress, subtitle }: MetricPanelProps) {
  const colorClasses = {
    blue: 'bg-blue-50 border-blue-200 text-blue-700',
    green: 'bg-green-50 border-green-200 text-green-700',
    purple: 'bg-purple-50 border-purple-200 text-purple-700',
    orange: 'bg-orange-50 border-orange-200 text-orange-700'
  }

  return (
    <div className={`p-4 rounded-lg border-2 ${colorClasses[color]}`}>
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium">{title}</span>
        {icon}
      </div>
      <div className="text-2xl font-bold mb-1">{value}</div>
      {subtitle && <div className="text-xs opacity-75">{subtitle}</div>}
      {progress !== undefined && (
        <Progress value={progress} className="mt-2 h-1" />
      )}
    </div>
  )
}

interface GrafanaPanelProps {
  title: string
  subtitle?: string
  icon?: React.ReactNode
  children: React.ReactNode
}

function GrafanaPanel({ title, subtitle, icon, children }: GrafanaPanelProps) {
  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className="px-4 py-3 border-b border-gray-200">
        <div className="flex items-center gap-2">
          {icon}
          <div>
            <h3 className="text-sm font-medium text-gray-900">{title}</h3>
            {subtitle && <p className="text-xs text-gray-600">{subtitle}</p>}
          </div>
        </div>
      </div>
      <div className="p-4">
        {children}
      </div>
    </div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="space-y-6">
      {/* Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="p-4 bg-white rounded-lg border border-gray-200">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </div>
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-1 w-full" />
            </div>
          </div>
        ))}
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {[...Array(2)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg border border-gray-200">
            <div className="px-4 py-3 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-4 w-32" />
              </div>
            </div>
            <div className="p-4">
              <Skeleton className="h-64 w-full" />
            </div>
          </div>
        ))}
      </div>

      {/* Additional panels */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {[...Array(2)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg border border-gray-200">
            <div className="px-4 py-3 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-4 w-32" />
              </div>
            </div>
            <div className="p-4">
              <Skeleton className="h-48 w-full" />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
} 