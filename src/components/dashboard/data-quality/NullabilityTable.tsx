'use client'

import React from 'react'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { AlertTriangle, CheckCircle } from 'lucide-react'

interface NullabilityMetric {
  nullCount: number
  nullPercentage: number
  totalRecords: number
}

interface NullabilityTableProps {
  metrics: Record<string, NullabilityMetric>
  title: string
}

export function NullabilityTable({ metrics, title }: NullabilityTableProps) {
  if (!metrics || Object.keys(metrics).length === 0) {
    return (
      <div className="text-sm text-muted-foreground">
        No nullability data available
      </div>
    )
  }

  // Sort fields by null percentage (worst first)
  const sortedFields = Object.entries(metrics)
    .sort(([, a], [, b]) => b.nullPercentage - a.nullPercentage)
    .slice(0, 10) // Show top 10 fields

  const getQualityColor = (percentage: number) => {
    if (percentage <= 10) return 'text-green-600'
    if (percentage <= 30) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getQualityBadge = (percentage: number) => {
    if (percentage <= 10) return { variant: 'default' as const, text: 'Excellent' }
    if (percentage <= 30) return { variant: 'secondary' as const, text: 'Good' }
    if (percentage <= 60) return { variant: 'outline' as const, text: 'Fair' }
    return { variant: 'destructive' as const, text: 'Poor' }
  }

  // Format field names for display
  const formatFieldName = (fieldName: string) => {
    return fieldName
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')
      .trim()
  }

  return (
    <div className="space-y-3">
      <h5 className="text-sm font-medium">{title}</h5>
      
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {sortedFields.map(([fieldName, metric]) => {
          const completeness = 100 - metric.nullPercentage
          const qualityBadge = getQualityBadge(metric.nullPercentage)
          
          return (
            <div key={fieldName} className="space-y-1 p-2 bg-muted/30 rounded-sm">
              {/* Field name and percentage */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">
                    {formatFieldName(fieldName)}
                  </span>
                  {metric.nullPercentage <= 10 ? (
                    <CheckCircle className="h-3 w-3 text-green-600" />
                  ) : metric.nullPercentage >= 60 ? (
                    <AlertTriangle className="h-3 w-3 text-red-600" />
                  ) : null}
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className={`text-xs font-medium ${getQualityColor(metric.nullPercentage)}`}>
                    {completeness.toFixed(1)}%
                  </span>
                  <Badge 
                    variant={qualityBadge.variant}
                    className="text-xs px-1 py-0"
                  >
                    {qualityBadge.text}
                  </Badge>
                </div>
              </div>
              
              {/* Progress bar */}
              <Progress 
                value={completeness} 
                className="h-1.5"
              />
              
              {/* Details */}
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>
                  {metric.totalRecords - metric.nullCount} of {metric.totalRecords} filled
                </span>
                <span>
                  {metric.nullCount} null values
                </span>
              </div>
            </div>
          )
        })}
      </div>
      
      {Object.keys(metrics).length > 10 && (
        <div className="text-xs text-muted-foreground">
          Showing top 10 fields. {Object.keys(metrics).length - 10} more fields available.
        </div>
      )}
    </div>
  )
} 