'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { DatePickerWithRange } from '@/components/ui/date-range-picker'
import { Button } from '@/components/ui/button'
import { RefreshCw, TrendingUp, CheckCircle, Database, Clock } from 'lucide-react'
import { DateRange } from 'react-day-picker'
import { subDays, format } from 'date-fns'
import { cn } from '@/lib/utils'

interface DataQualityResponse {
  success: boolean
  data: any
  timeRange: {
    startDate: string
    endDate: string
  }
}

interface EntityConfig {
  id: string
  name: string
  icon: React.ComponentType<any>
  description: string
  color: {
    primary: string
    secondary: string
    accent: string
  }
  apiEndpoint: string
  metricsComponent: React.ComponentType<any>
}

interface DataQualityEntityProps {
  config: EntityConfig
}

export default function DataQualityEntity({ config }: DataQualityEntityProps) {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: subDays(new Date(), 7),
    to: new Date()
  })
  const [data, setData] = useState<DataQualityResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null)

  const fetchData = async (startDate?: string, endDate?: string) => {
    const fromDate = startDate || dateRange?.from?.toISOString()
    const toDate = endDate || dateRange?.to?.toISOString()
    
    if (!fromDate || !toDate) return

    setLoading(true)
    try {
      const response = await fetch(`${config.apiEndpoint}?startDate=${fromDate}&endDate=${toDate}`)
      const result = await response.json()

      console.log(`${config.name} data received:`, result)
      setData(result)
      setLastRefresh(new Date())
    } catch (error) {
      console.error(`Error fetching ${config.name.toLowerCase()} data:`, error)
    } finally {
      setLoading(false)
    }
  }

  const handleTimeRangeChange = (startDate: string, endDate: string) => {
    const from = new Date(startDate)
    const to = new Date(endDate)
    setDateRange({ from, to })
    fetchData(startDate, endDate)
  }

  useEffect(() => {
    fetchData()
}, [dateRange, config.apiEndpoint])

  // Get metrics based on the entity type
  const getMetrics = () => {
    if (!data?.data) return { totalRecords: 0, recentActivity: {} }
    
    // Handle different data structures for each entity type
    switch (config.id) {
      case 'company':
        const companyData = data.data.companyOverview
        return {
          totalRecords: companyData?.totalRecords || 0,
          recentActivity: companyData?.recentActivity || {}
        }
      case 'contact':
        const contactData = data.data.contactEnrichment
        return {
          totalRecords: contactData?.totalRecords || 0,
          recentActivity: contactData?.recentActivity || {}
        }
      case 'news':
        const newsData = data.data.newsData
        return {
          totalRecords: newsData?.totalRecords || 0,
          recentActivity: newsData?.recentActivity || {}
        }
      default:
        return { totalRecords: 0, recentActivity: {} }
    }
  }

  const metrics = getMetrics()
  const IconComponent = config.icon
  const MetricsComponent = config.metricsComponent

  return (
    <div className="space-y-6">
      {/* Header - Hide for contact entity as it has its own Grafana-style controls */}
      {config.id !== 'contact' && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <DatePickerWithRange 
              date={dateRange} 
              onDateChange={setDateRange}
              className="w-[300px]"
            />
            <Button onClick={() => fetchData()} disabled={loading} size="sm">
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>
      )}
      {/* Detailed Metrics */}
      <MetricsComponent 
        data={data?.data} 
        loading={loading}
        timeRange={data?.timeRange}
        onTimeRangeChange={config.id === 'contact' ? handleTimeRangeChange : undefined}
      />
    </div>
  )
} 