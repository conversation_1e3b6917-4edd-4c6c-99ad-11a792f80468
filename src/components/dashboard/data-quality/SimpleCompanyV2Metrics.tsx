'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader, Card<PERSON><PERSON>le } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { 
  Building2, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  BarChart3,
  Database,
  ChevronDown,
  ChevronRight,
  TrendingUp,
  DollarSign,
  Activity,
  Shield
} from 'lucide-react'

interface ColumnGroup {
  name: string
  description: string
  columns: Array<{
    column_name: string
    populated_count: number
    populated_percentage: number
  }>
}

interface SimpleV2Metrics {
  totalRows: number
  completedV2Rows: number
  pendingV2Rows: number
  failedV2Rows: number
  overallPercentage: number
  columnGroups: ColumnGroup[]
  hourlyProcessingStats?: Array<{
    hour: string
    processed_count: number
    completed_count: number
    failed_count: number
  }>
  mappingValidation?: {
    total_records: number
    company_type_validation: {
      valid_count: number
      invalid_count: number
      invalid_values: string[]
    }
    capital_position_validation: {
      valid_count: number
      invalid_count: number
      invalid_values: string[]
    }
    investment_focus_validation: {
      valid_count: number
      invalid_count: number
      invalid_values: string[]
    }
    overall_mapping_validation_percentage: number
  }
}

interface SimpleCompanyV2MetricsProps {
  data?: SimpleV2Metrics
}

export function SimpleCompanyV2Metrics({ data: propData }: SimpleCompanyV2MetricsProps) {
  const [data, setData] = useState<SimpleV2Metrics | null>(propData || null)
  const [loading, setLoading] = useState(!propData)
  const [error, setError] = useState<string | null>(null)
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set())

  useEffect(() => {
    if (!propData) {
      fetchData()
    }
  }, [propData])

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch('/api/data-quality/company-overview-v2')
      const result = await response.json()
      
      if (result.success) {
        setData(result.data)
      } else {
        setError(result.error || 'Failed to fetch data')
      }
    } catch (error) {
      console.error('Error fetching V2 metrics:', error)
      setError('Failed to fetch data')
    } finally {
      setLoading(false)
    }
  }

  const toggleGroup = (groupName: string) => {
    const newExpanded = new Set(expandedGroups)
    if (newExpanded.has(groupName)) {
      newExpanded.delete(groupName)
    } else {
      newExpanded.add(groupName)
    }
    setExpandedGroups(newExpanded)
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-96 w-full" />
      </div>
    )
  }

  if (error || !data) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            {error || 'No data available'}
          </div>
        </CardContent>
      </Card>
    )
  }

  // Calculate derived values with safe defaults
  const completionRate = data.totalRows > 0 ? Math.round((data.completedV2Rows / data.totalRows) * 100) : 0
  const overallPercentage = data.overallPercentage || 0

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Database className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Companies</p>
                <p className="text-2xl font-bold text-gray-900">{(data.totalRows || 0).toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">V2 Completed</p>
                <p className="text-2xl font-bold text-green-600">{(data.completedV2Rows || 0).toLocaleString()}</p>
                <p className="text-xs text-gray-500">{completionRate}% complete</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">{(data.pendingV2Rows || 0).toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Failed</p>
                <p className="text-2xl font-bold text-red-600">{(data.failedV2Rows || 0).toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>



      {/* Hourly Processing Stats */}
      {data.hourlyProcessingStats && data.hourlyProcessingStats.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-blue-600" />
              <span>Hourly Processing Activity (Last 24 Hours)</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Processing Timeline Chart */}
              <div className="h-64 flex items-end space-x-2 overflow-x-auto p-4 bg-gray-50 rounded-lg">
                {data.hourlyProcessingStats.slice(0, 24).reverse().map((stat, index) => {
                  const maxCount = Math.max(...data.hourlyProcessingStats!.map(s => s.processed_count), 1)
                  const processedHeight = (stat.processed_count / maxCount) * 200
                  const completedHeight = (stat.completed_count / maxCount) * 200
                  const failedHeight = (stat.failed_count / maxCount) * 200
                  const hour = new Date(stat.hour).getHours()
                  
                  return (
                    <div key={index} className="flex flex-col items-center min-w-[40px]">
                      <div className="relative h-52 w-full flex flex-col justify-end">
                        {/* Background bar for total processed */}
                        <div 
                          className="absolute bottom-0 w-full bg-gray-300 rounded-t transition-all duration-300"
                          style={{ height: `${Math.max(processedHeight, 4)}px` }}
                        />
                        {/* Completed bar */}
                        <div 
                          className="absolute bottom-0 w-full bg-green-500 rounded-t transition-all duration-300"
                          style={{ height: `${Math.max(completedHeight, 2)}px` }}
                        />
                        {/* Failed bar */}
                        <div 
                          className="absolute bottom-0 w-full bg-red-500 transition-all duration-300"
                          style={{ 
                            bottom: `${completedHeight}px`,
                            height: `${Math.max(failedHeight, 2)}px` 
                          }}
                        />
                      </div>
                      <div className="text-xs text-gray-600 mt-2 font-medium">{hour}h</div>
                      <div className="text-xs text-gray-500 mt-1">{stat.processed_count}</div>
                    </div>
                  )
                })}
              </div>
              
              {/* Legend */}
              <div className="flex justify-center space-x-6 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-green-500 rounded"></div>
                  <span className="font-medium">Completed</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-red-500 rounded"></div>
                  <span className="font-medium">Failed</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-gray-300 rounded"></div>
                  <span className="font-medium">Total Processed</span>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-xs text-blue-600">
                  <strong>Processing Timeline:</strong> Shows company V2 processing activity over the last 24 hours. 
                  Green bars represent completed processing, red bars represent failed attempts, and gray bars show total processed.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Column Groups */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            <span>V2 Field Completeness by Category (Completed V2 Rows Only)</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.columnGroups.map((group) => {
              const groupAvgPercentage = group.columns.length > 0 
                ? Math.round(group.columns.reduce((sum, col) => sum + col.populated_percentage, 0) / group.columns.length)
                : 0
              
              const isExpanded = expandedGroups.has(group.name)
              
              return (
                <Collapsible key={group.name} open={isExpanded} onOpenChange={() => toggleGroup(group.name)}>
                  <CollapsibleTrigger asChild>
                    <div className="flex items-center justify-between p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                      <div className="flex items-center space-x-3">
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4 text-gray-500" />
                        ) : (
                          <ChevronRight className="h-4 w-4 text-gray-500" />
                        )}
                        <div>
                          <h3 className="font-medium text-gray-900">{group.name}</h3>
                          <p className="text-sm text-gray-600">{group.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="text-right">
                          <p className="text-sm font-medium">{group.columns.length} columns</p>
                          <p className="text-xs text-gray-600">Avg: {groupAvgPercentage}%</p>
                        </div>
                        <Badge 
                          variant="outline" 
                          className={`${
                            groupAvgPercentage >= 80 ? 'border-green-500 text-green-700' :
                            groupAvgPercentage >= 50 ? 'border-yellow-500 text-yellow-700' :
                            'border-red-500 text-red-700'
                          }`}
                        >
                          {groupAvgPercentage}%
                        </Badge>
                      </div>
                    </div>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent>
                    <div className="mt-3 ml-7 space-y-3">
                      {group.columns
                        .sort((a, b) => b.populated_percentage - a.populated_percentage)
                        .map((col) => (
                          <div key={col.column_name} className="space-y-2 p-3 border rounded-lg bg-gray-50">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium capitalize">
                                {col.column_name.replace(/_/g, ' ')}
                              </span>
                              <div className="flex items-center space-x-2">
                                <span className="text-xs text-gray-600">
                                  {(col.populated_count || 0).toLocaleString()} / {(data.completedV2Rows || 0).toLocaleString()}
                                </span>
                                <Badge 
                                  variant="outline" 
                                  className={`text-xs ${
                                    col.populated_percentage >= 80 ? 'border-green-500 text-green-700' :
                                    col.populated_percentage >= 50 ? 'border-yellow-500 text-yellow-700' :
                                    'border-red-500 text-red-700'
                                  }`}
                                >
                                  {col.populated_percentage}%
                                </Badge>
                              </div>
                            </div>
                            <Progress 
                              value={col.populated_percentage} 
                              className={`h-2 ${
                                col.populated_percentage >= 80 ? 'bg-green-100' :
                                col.populated_percentage >= 50 ? 'bg-yellow-100' :
                                'bg-red-100'
                              }`}
                            />
                          </div>
                        ))}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
