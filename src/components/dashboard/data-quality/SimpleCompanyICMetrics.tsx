'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { 
  Building2, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  BarChart3,
  Database,
  ChevronDown,
  ChevronRight,
  DollarSign,
  TrendingUp,
  MapPin,
  Home,
  Activity,
  Shield
} from 'lucide-react'

interface ColumnGroup {
  name: string
  description: string
  table: 'central' | 'debt' | 'equity'
  totalRecords: number
  columns: Array<{
    column_name: string
    populated_count: number
    populated_percentage: number
  }>
}

interface SimpleCompanyICMetrics {
  totalRows: number
  completedICRows: number
  pendingICRows: number
  failedICRows: number
  overallPercentage: number
  tableTotals: {
    central: number
    debt: number
    equity: number
  }
  columnGroups: ColumnGroup[]
  hourlyProcessingStats?: Array<{
    hour: string
    processed_count: number
    completed_count: number
    failed_count: number
  }>
  mappingValidation?: {
    total_records: number
    property_types_validation: {
      valid_count: number
      invalid_count: number
      invalid_values: string[]
    }
    strategies_validation: {
      valid_count: number
      invalid_count: number
      invalid_values: string[]
    }
    capital_position_validation: {
      valid_count: number
      invalid_count: number
      invalid_values: string[]
    }
    overall_mapping_validation_percentage: number
  }
}

interface SimpleCompanyICMetricsProps {
  data?: SimpleCompanyICMetrics
}

export function SimpleCompanyICMetrics({ data: propData }: SimpleCompanyICMetricsProps) {
  const [data, setData] = useState<SimpleCompanyICMetrics | null>(propData || null)
  const [loading, setLoading] = useState(!propData)
  const [error, setError] = useState<string | null>(null)
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set())

  useEffect(() => {
    // Always fetch fresh data to ensure we have the latest structure
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch('/api/data-quality/company-investment-criteria')
      const result = await response.json()
      
      if (result.success) {
        setData(result.data)
      } else {
        setError(result.error || 'Failed to fetch data')
      }
    } catch (error) {
      console.error('Error fetching Company IC metrics:', error)
      setError('Failed to fetch data')
    } finally {
      setLoading(false)
    }
  }

  const toggleGroup = (groupName: string) => {
    const newExpanded = new Set(expandedGroups)
    if (newExpanded.has(groupName)) {
      newExpanded.delete(groupName)
    } else {
      newExpanded.add(groupName)
    }
    setExpandedGroups(newExpanded)
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-96 w-full" />
      </div>
    )
  }

  if (error || !data) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>{error || 'No data available'}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Safely handle undefined values with defaults
  const totalRows = data.totalRows || 0
  const completedICRows = data.completedICRows || 0
  const pendingICRows = data.pendingICRows || 0
  const failedICRows = data.failedICRows || 0
  const completionRate = totalRows > 0 ? Math.round((completedICRows / totalRows) * 100) : 0
  const overallPercentage = data.overallPercentage || 0

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Database className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Companies (Overview Complete)</p>
                <p className="text-2xl font-bold text-gray-900">{totalRows.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">IC Extracted</p>
                <p className="text-2xl font-bold text-green-600">{completedICRows.toLocaleString()}</p>
                <p className="text-xs text-gray-500">{completionRate}% complete</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Pending IC</p>
                <p className="text-2xl font-bold text-yellow-600">{pendingICRows.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Failed IC</p>
                <p className="text-2xl font-bold text-red-600">{failedICRows.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5 text-blue-600" />
            <span>Investment Criteria Extraction Progress</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">IC Extraction Completion</span>
                <span className="text-sm text-gray-600">{completionRate}%</span>
              </div>
              <Progress value={completionRate} className="h-3" />
            </div>
            
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <Badge variant="default" className="bg-green-100 text-green-800">
                  IC Complete: {data.completedICRows}
                </Badge>
              </div>
              <div className="text-center">
                <Badge variant="default" className="bg-yellow-100 text-yellow-800">
                  IC Pending: {data.pendingICRows}
                </Badge>
              </div>
              <div className="text-center">
                <Badge variant="default" className="bg-red-100 text-red-800">
                  IC Failed: {data.failedICRows}
                </Badge>
              </div>
            </div>
            
            {/* Overall Data Quality Score */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-purple-700">Overall Data Quality Score</span>
                <span className="text-sm font-bold text-purple-600">{overallPercentage}%</span>
              </div>
              <Progress 
                value={overallPercentage} 
                className={`h-3 ${
                  overallPercentage >= 80 ? 'bg-green-100' :
                  overallPercentage >= 60 ? 'bg-blue-100' :
                  overallPercentage >= 40 ? 'bg-yellow-100' :
                  'bg-red-100'
                }`}
              />
              <p className="text-xs text-gray-600 mt-1">Field completeness across all investment criteria</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Table Totals Section */}
      {data.tableTotals && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-purple-600" />
              Investment Criteria Table Records
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div>
                  <p className="text-sm font-medium text-blue-800">Central Table</p>
                  <p className="text-lg font-bold text-blue-900">{(data.tableTotals?.central || 0).toLocaleString()}</p>
                  <p className="text-xs text-blue-600">Core investment criteria</p>
                </div>
                <Home className="h-8 w-8 text-blue-600" />
              </div>
              
              <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                <div>
                  <p className="text-sm font-medium text-green-800">Debt Table</p>
                  <p className="text-lg font-bold text-green-900">{(data.tableTotals?.debt || 0).toLocaleString()}</p>
                  <p className="text-xs text-green-600">Debt-specific criteria</p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
              
              <div className="flex items-center justify-between p-4 bg-purple-50 rounded-lg border border-purple-200">
                <div>
                  <p className="text-sm font-medium text-purple-800">Equity Table</p>
                  <p className="text-lg font-bold text-purple-900">{(data.tableTotals?.equity || 0).toLocaleString()}</p>
                  <p className="text-xs text-purple-600">Equity-specific criteria</p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-600" />
              </div>
            </div>
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-xs text-gray-600">
                <strong>Note:</strong> Each column group uses its corresponding table total for percentage calculations. 
                Central table contains all companies with IC data, while debt/equity tables contain subset records for companies with those specific criteria types.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Column Groups */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            <span>IC Field Completeness by Category (IC Extracted Companies Only)</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.columnGroups.map((group) => {
              const groupAvgPercentage = group.columns.length > 0 
                ? Math.round(group.columns.reduce((sum, col) => sum + col.populated_percentage, 0) / group.columns.length)
                : 0
              
              const isExpanded = expandedGroups.has(group.name)
              
              // Icon mapping for different group types
              const getGroupIcon = (groupName: string) => {
                if (groupName.toLowerCase().includes('deal') || groupName.toLowerCase().includes('scope')) return DollarSign
                if (groupName.toLowerCase().includes('geography') || groupName.toLowerCase().includes('location')) return MapPin
                if (groupName.toLowerCase().includes('asset') || groupName.toLowerCase().includes('property')) return Home
                if (groupName.toLowerCase().includes('debt')) return BarChart3
                if (groupName.toLowerCase().includes('equity')) return TrendingUp
                return Building2
              }
              
              const GroupIcon = getGroupIcon(group.name)
              
              return (
                <Collapsible key={group.name} open={isExpanded} onOpenChange={() => toggleGroup(group.name)}>
                  <CollapsibleTrigger asChild>
                    <div className="flex items-center justify-between p-4 border rounded-lg cursor-pointer hover:bg-blue-50 transition-colors">
                      <div className="flex items-center space-x-3">
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4 text-gray-500" />
                        ) : (
                          <ChevronRight className="h-4 w-4 text-gray-500" />
                        )}
                        <GroupIcon className="h-5 w-5 text-blue-600" />
                        <div>
                          <h3 className="font-medium text-gray-900 flex items-center gap-2">
                            {group.name}
                            <Badge variant="outline" className={`text-xs ${
                              group.table === 'central' ? 'border-blue-500 text-blue-700 bg-blue-50' :
                              group.table === 'debt' ? 'border-green-500 text-green-700 bg-green-50' :
                              'border-purple-500 text-purple-700 bg-purple-50'
                            }`}>
                              {group.table}
                            </Badge>
                          </h3>
                          <p className="text-sm text-gray-600">{group.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="text-right">
                          <p className="text-sm font-medium">{group.columns.length} fields</p>
                          <p className="text-xs text-gray-600">Avg: {groupAvgPercentage}%</p>
                          <p className="text-xs text-gray-500">Total: {group.totalRecords || completedICRows || 0} records</p>
                        </div>
                        <Badge 
                          variant="outline" 
                          className={`${
                            groupAvgPercentage >= 80 ? 'border-green-500 text-green-700' :
                            groupAvgPercentage >= 50 ? 'border-yellow-500 text-yellow-700' :
                            'border-red-500 text-red-700'
                          }`}
                        >
                          {groupAvgPercentage}%
                        </Badge>
                      </div>
                    </div>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent>
                    <div className="mt-3 ml-7 space-y-3">
                      {group.columns
                        .sort((a, b) => b.populated_percentage - a.populated_percentage)
                        .map((col) => (
                          <div key={col.column_name} className="space-y-2 p-3 border rounded-lg bg-blue-50">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium capitalize">
                                {col.column_name.replace(/_/g, ' ')}
                              </span>
                              <div className="flex items-center space-x-2">
                                <span className="text-xs text-gray-600">
                                  {(col.populated_count || 0).toLocaleString()} / {(group.totalRecords || completedICRows || 0).toLocaleString()}
                                </span>
                                <Badge 
                                  variant="outline" 
                                  className={`text-xs ${
                                    col.populated_percentage >= 80 ? 'border-green-500 text-green-700' :
                                    col.populated_percentage >= 50 ? 'border-yellow-500 text-yellow-700' :
                                    'border-red-500 text-red-700'
                                  }`}
                                >
                                  {col.populated_percentage}%
                                </Badge>
                              </div>
                            </div>
                            <Progress 
                              value={col.populated_percentage} 
                              className={`h-2 ${
                                col.populated_percentage >= 80 ? 'bg-green-100' :
                                col.populated_percentage >= 50 ? 'bg-yellow-100' :
                                'bg-red-100'
                              }`}
                            />
                          </div>
                        ))}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Hourly Processing Activity */}
      {data.hourlyProcessingStats && data.hourlyProcessingStats.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-purple-600" />
              <span>Hourly Processing Activity (Last 24 Hours)</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Processing Timeline Chart */}
              <div className="h-64 flex items-end space-x-2 overflow-x-auto p-4 bg-gray-50 rounded-lg">
                {data.hourlyProcessingStats.slice(0, 24).reverse().map((stat, index) => {
                  const maxCount = Math.max(...data.hourlyProcessingStats!.map(s => s.processed_count), 1)
                  const processedHeight = (stat.processed_count / maxCount) * 200
                  const completedHeight = (stat.completed_count / maxCount) * 200
                  const failedHeight = (stat.failed_count / maxCount) * 200
                  const hour = new Date(stat.hour).getHours()
                  
                  return (
                    <div key={index} className="flex flex-col items-center min-w-[40px]">
                      <div className="relative h-52 w-full flex flex-col justify-end">
                        {/* Background bar for total processed */}
                        <div 
                          className="absolute bottom-0 w-full bg-gray-300 rounded-t transition-all duration-300"
                          style={{ height: `${Math.max(processedHeight, 4)}px` }}
                        />
                        {/* Completed bar */}
                        <div 
                          className="absolute bottom-0 w-full bg-green-500 rounded-t transition-all duration-300"
                          style={{ height: `${Math.max(completedHeight, 2)}px` }}
                        />
                        {/* Failed bar */}
                        <div 
                          className="absolute bottom-0 w-full bg-red-500 transition-all duration-300"
                          style={{ 
                            bottom: `${completedHeight}px`,
                            height: `${Math.max(failedHeight, 2)}px` 
                          }}
                        />
                      </div>
                      <div className="text-xs text-gray-600 mt-2 font-medium">{hour}h</div>
                      <div className="text-xs text-gray-500 mt-1">{stat.processed_count}</div>
                    </div>
                  )
                })}
              </div>
              
              {/* Legend */}
              <div className="flex justify-center space-x-6 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-green-500 rounded"></div>
                  <span className="font-medium">Completed</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-red-500 rounded"></div>
                  <span className="font-medium">Failed</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-gray-300 rounded"></div>
                  <span className="font-medium">Total Processed</span>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-purple-50 rounded-lg">
                <p className="text-xs text-purple-600">
                  <strong>Processing Timeline:</strong> Shows IC processing activity over the last 24 hours. 
                  Green bars represent completed processing, red bars represent failed attempts, and gray bars show total processed.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Mapping Validation Results */}
      {data.mappingValidation && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-green-600" />
              <span>Mapping Validation Results</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Overall Validation Score */}
              <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                <div className="flex justify-between items-center mb-3">
                  <span className="text-sm font-medium text-blue-900">Overall Mapping Validation</span>
                  <span className="text-xl font-bold text-blue-900">{(data.mappingValidation?.overall_mapping_validation_percentage || 0).toFixed(1)}%</span>
                </div>
                <Progress 
                  value={data.mappingValidation?.overall_mapping_validation_percentage || 0} 
                  className="mt-3 h-2"
                />
                <p className="text-xs text-blue-700 mt-2">
                  {data.mappingValidation?.total_records || 0} total IC records analyzed for mapping compliance
                </p>
              </div>

              {/* Individual Field Validations */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {/* Property Types */}
                <div className="bg-white p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                  <div className="text-xs font-medium text-gray-600 uppercase tracking-wide">Property Types</div>
                  <div className="text-lg font-bold text-gray-900 mt-1">
                    {(data.mappingValidation?.property_types_validation?.valid_count || 0)} / {(data.mappingValidation?.property_types_validation?.valid_count || 0) + (data.mappingValidation?.property_types_validation?.invalid_count || 0)}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {(data.mappingValidation?.property_types_validation?.valid_count || 0) + (data.mappingValidation?.property_types_validation?.invalid_count || 0) > 0 
                      ? Math.round(((data.mappingValidation?.property_types_validation?.valid_count || 0) / ((data.mappingValidation?.property_types_validation?.valid_count || 0) + (data.mappingValidation?.property_types_validation?.invalid_count || 0))) * 100)
                      : 0}% valid
                  </div>
                </div>

                {/* Strategies */}
                <div className="bg-white p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                  <div className="text-xs font-medium text-gray-600 uppercase tracking-wide">Strategies</div>
                  <div className="text-lg font-bold text-gray-900 mt-1">
                    {(data.mappingValidation?.strategies_validation?.valid_count || 0)} / {(data.mappingValidation?.strategies_validation?.valid_count || 0) + (data.mappingValidation?.strategies_validation?.invalid_count || 0)}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {(data.mappingValidation?.strategies_validation?.valid_count || 0) + (data.mappingValidation?.strategies_validation?.invalid_count || 0) > 0 
                      ? Math.round(((data.mappingValidation?.strategies_validation?.valid_count || 0) / ((data.mappingValidation?.strategies_validation?.valid_count || 0) + (data.mappingValidation?.strategies_validation?.invalid_count || 0))) * 100)
                      : 0}% valid
                  </div>
                </div>

                {/* Capital Position */}
                <div className="bg-white p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                  <div className="text-xs font-medium text-gray-600 uppercase tracking-wide">Capital Position</div>
                  <div className="text-lg font-bold text-gray-900 mt-1">
                    {(data.mappingValidation?.capital_position_validation?.valid_count || 0)} / {(data.mappingValidation?.capital_position_validation?.valid_count || 0) + (data.mappingValidation?.capital_position_validation?.invalid_count || 0)}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {(data.mappingValidation?.capital_position_validation?.valid_count || 0) + (data.mappingValidation?.capital_position_validation?.invalid_count || 0) > 0 
                      ? Math.round(((data.mappingValidation?.capital_position_validation?.valid_count || 0) / ((data.mappingValidation?.capital_position_validation?.valid_count || 0) + (data.mappingValidation?.capital_position_validation?.invalid_count || 0))) * 100)
                      : 0}% valid
                  </div>
                </div>
              </div>

              <div className="mt-4 p-3 bg-green-50 rounded-lg">
                <p className="text-xs text-green-600">
                  <strong>Mapping Validation:</strong> Checks if extracted categorical values match approved values in the central mapping table. 
                  High validation scores indicate consistent, standardized data extraction.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
