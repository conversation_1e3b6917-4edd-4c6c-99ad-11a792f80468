import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Database, 
  DollarSign, 
  TrendingUp, 
  Target, 
  BarChart3,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';

interface V2MatchingQualityMetricsProps {
  data?: any;
}

interface FieldMetric {
  field_name: string;
  display_name: string;
  populated_count: number;
  total_count: number;
  quality_percentage: number;
}

interface TableMetrics {
  total_records: number;
  fields: FieldMetric[];
  overall_quality: number;
}

interface V2MatchingData {
  central_investment_criteria: TableMetrics;
  debt_investment_criteria: TableMetrics;
  equity_investment_criteria: TableMetrics;
  nsf_fields: TableMetrics;
  capital_positions: TableMetrics;
  deals_v2: TableMetrics;
  summary: {
    total_records: number;
    overall_quality: number;
    table_breakdown: Array<{
      table_name: string;
      total_records: number;
      overall_quality: number;
    }>;
  };
}

export function V2MatchingQualityMetrics({ data: propData }: V2MatchingQualityMetricsProps) {
  const [data, setData] = useState<V2MatchingData | null>(propData || null);
  const [loading, setLoading] = useState(!propData);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('summary');

  useEffect(() => {
    if (!propData) {
      fetchData();
    }
  }, [propData]);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch('/api/data-quality/matching-v2');
      const result = await response.json();
      
      if (result.success) {
        setData(result.data);
      } else {
        setError(result.error || 'Failed to fetch data');
      }
    } catch (error) {
      console.error('Error fetching V2 matching quality metrics:', error);
      setError('Failed to fetch data');
    } finally {
      setLoading(false);
    }
  };

  const getQualityColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600 bg-green-100 border-green-300';
    if (percentage >= 60) return 'text-yellow-600 bg-yellow-100 border-yellow-300';
    if (percentage >= 40) return 'text-orange-600 bg-orange-100 border-orange-300';
    return 'text-red-600 bg-red-100 border-red-300';
  };

  const getQualityIcon = (percentage: number) => {
    if (percentage >= 80) return <CheckCircle className="h-4 w-4" />;
    if (percentage >= 60) return <Info className="h-4 w-4" />;
    return <AlertCircle className="h-4 w-4" />;
  };

  const renderFieldMetrics = (metrics: TableMetrics, title: string) => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{title}</h3>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className={getQualityColor(metrics.overall_quality)}>
            {metrics.overall_quality}% Quality
          </Badge>
          <Badge variant="secondary">
            {metrics.total_records} Records
          </Badge>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {metrics.fields.map((field) => (
          <Card key={field.field_name} className="p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">
                {field.display_name}
              </span>
              <div className="flex items-center gap-1">
                {getQualityIcon(field.quality_percentage)}
                <Badge 
                  variant="outline" 
                  className={`text-xs ${getQualityColor(field.quality_percentage)}`}
                >
                  {field.quality_percentage}%
                </Badge>
              </div>
            </div>
            <Progress value={field.quality_percentage} className="h-2" />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>{field.populated_count} populated</span>
              <span>{field.total_count} total</span>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (error || !data || !data.summary) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            {error || 'No data available'}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            V2 Matching System Data Quality Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{data.summary?.total_records || 0}</div>
              <div className="text-sm text-gray-600">Total Records</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{data.summary?.overall_quality || 0}%</div>
              <div className="text-sm text-gray-600">Overall Quality</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{data.summary?.table_breakdown?.length || 0}</div>
              <div className="text-sm text-gray-600">Data Tables</div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {data.summary?.table_breakdown?.map((table) => (
              <Card key={table.table_name} className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">{table.table_name}</span>
                  <Badge variant="outline" className={getQualityColor(table.overall_quality)}>
                    {table.overall_quality}%
                  </Badge>
                </div>
                <Progress value={table.overall_quality} className="h-2" />
                <div className="text-xs text-gray-500 mt-1">
                  {table.total_records} records
                </div>
              </Card>
            )) || (
              <div className="col-span-full text-center text-gray-500 py-8">
                No table breakdown data available
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="summary" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Summary
          </TabsTrigger>
          <TabsTrigger value="central" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Central
          </TabsTrigger>
          <TabsTrigger value="debt" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Debt
          </TabsTrigger>
          <TabsTrigger value="equity" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Equity
          </TabsTrigger>
          <TabsTrigger value="nsf" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            NSF & Weights
          </TabsTrigger>
          <TabsTrigger value="deals" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            DealsV2
          </TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.summary?.table_breakdown?.map((table) => (
                  <div key={table.table_name} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        table.overall_quality >= 80 ? 'bg-green-500' :
                        table.overall_quality >= 60 ? 'bg-yellow-500' :
                        table.overall_quality >= 40 ? 'bg-orange-500' : 'bg-red-500'
                      }`} />
                      <span className="font-medium">{table.table_name}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-sm text-gray-600">{table.total_records} records</span>
                      <Badge variant="outline" className={getQualityColor(table.overall_quality)}>
                        {table.overall_quality}% Quality
                      </Badge>
                    </div>
                  </div>
                )) || (
                  <div className="text-center text-gray-500 py-8">
                    No table breakdown data available
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="central">
          <Card>
            <CardHeader>
              <CardTitle>Central Investment Criteria</CardTitle>
            </CardHeader>
            <CardContent>
              {renderFieldMetrics(data.central_investment_criteria, 'Central Investment Criteria Fields')}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="debt">
          <Card>
            <CardHeader>
              <CardTitle>Debt Investment Criteria</CardTitle>
            </CardHeader>
            <CardContent>
              {renderFieldMetrics(data.debt_investment_criteria, 'Debt Investment Criteria Fields')}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="equity">
          <Card>
            <CardHeader>
              <CardTitle>Equity Investment Criteria</CardTitle>
            </CardHeader>
            <CardContent>
              {renderFieldMetrics(data.equity_investment_criteria, 'Equity Investment Criteria Fields')}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="nsf" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>NSF Fields</CardTitle>
            </CardHeader>
            <CardContent>
              {renderFieldMetrics(data.nsf_fields, 'NSF Fields')}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Capital Position Weights</CardTitle>
            </CardHeader>
            <CardContent>
              {renderFieldMetrics(data.capital_positions, 'Capital Position Weight Fields')}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="deals">
          <Card>
            <CardHeader>
              <CardTitle>DealsV2</CardTitle>
            </CardHeader>
            <CardContent>
              {renderFieldMetrics(data.deals_v2, 'DealsV2 Fields')}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
