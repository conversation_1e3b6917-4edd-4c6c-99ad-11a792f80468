"use client";

import React from "react";
import NewsMonitor from "./NewsMonitor";

interface NewsProps {
  isActive?: boolean;
  title?: string;
  description?: string;
}

const News: React.FC<NewsProps> = ({
  isActive = true,
  title = "News & Deal Intelligence",
  description = "Monitor real estate news sources and extract deal information"
}) => {
  return (
    <div className="w-full">
      {/* Optional header - can be hidden if used within another layout */}
      <div className="mb-4">
        <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
        <p className="text-sm text-gray-600 mt-1">{description}</p>
      </div>

      {/* Main News Monitor Component */}
      <NewsMonitor isActive={isActive} />
    </div>
  );
};

export default News; 