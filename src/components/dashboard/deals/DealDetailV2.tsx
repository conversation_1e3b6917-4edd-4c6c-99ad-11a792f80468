"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useR<PERSON>er, useSearchParams } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  DollarSign,
  MapPin,
  Building2,
  User,
  Calendar,
  TrendingUp,
  Percent,
  FileText,
  Edit,
  Users,
  Database,
  Target,
  Clock,
  Home,
  Ruler,
  CreditCard,
  PieChart,
  Play,
  Save,
  Trash2,
  Plus,
  BarChart3,
  Square,
  Building,
  X,
  AlertTriangle,
  Upload,
  Zap,
  AlertCircle,
} from "lucide-react";
import { DealV2, PropertyV2, DealNsfFieldV2 } from "./shared/types-v2";
import DealFiles from "./DealFiles";
import { Contact } from "../people/shared/types";
import ContactCard from "../people/list-components/ContactCard";
import DataQualityBanner from "./DataQualityBanner";
import { calculateOverallQuality } from "@/lib/utils/nsfQualityCalculator";
import { calculateComprehensiveQuality } from "@/lib/utils/comprehensiveQualityCalculator";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { MultiSelect } from "@/components/ui/multi-select";
import { ContactMultiSelect } from "@/components/ui/contact-multi-select";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronDown } from "lucide-react";
import { ConflictIndicator } from "./ConflictIndicator";
import SourcesUsesTab from "./SourcesUsesTab";
import DebtTab from "./DebtTab";
import EquityTab from "./EquityTab";
import OverviewTab from "./OverviewTab";
import ContactTab from "./ContactTab";
import DealMatchingTab from "./DealMatchingTab";
import JobsTab from "./JobsTab";
import CapitalPositionSelector from "./CapitalPositionSelector";
import ConflictsDisplay from "./ConflictsDisplay";

interface DealDetailV2Props {
  dealId: string;
}

const DealDetailV2: React.FC<DealDetailV2Props> = ({ dealId }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const returnUrl = searchParams.get('from');
  const [deal, setDeal] = useState<DealV2 | null>(null);
  const [contact, setContact] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [contactLoading, setContactLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [activeTab, setActiveTab] = useState(() => {
    const tabFromUrl = searchParams.get('tab');
    const validTabs = ['overview', 'debt', 'equity', 'nsf', 'deal-matching', 'contact', 'jobs'];
    return tabFromUrl && validTabs.includes(tabFromUrl) ? tabFromUrl : "overview";
  });
  
  const [isRunningRequirementExtraction, setIsRunningRequirementExtraction] = useState(false);
  
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [editedDeal, setEditedDeal] = useState<DealV2 | null>(null);
  const [jobs, setJobs] = useState<any[]>([]);
  const [jobsLoading, setJobsLoading] = useState(false);
  const [jobsRefreshInterval, setJobsRefreshInterval] = useState<NodeJS.Timeout | null>(null);
  const [matchingContacts, setMatchingContacts] = useState<any[]>([]);
  const [matchingContactsLoading, setMatchingContactsLoading] = useState(false);
  const [matchingContactsError, setMatchingContactsError] = useState<string | null>(null);
  const [matchingContactsFiltering, setMatchingContactsFiltering] = useState<any>(null);
  const [showAllMatches, setShowAllMatches] = useState(false);
  const [isCrmMode, setIsCrmMode] = useState(true);
  const [matchingContactsPagination, setMatchingContactsPagination] = useState<any>(null);
  const [expandedContactId, setExpandedContactId] = React.useState<number | null>(null);
  const [dealContacts, setDealContacts] = useState<any[]>([]);
  const [dealContactsLoading, setDealContactsLoading] = useState(false);
  const [selectedContacts, setSelectedContacts] = useState<any[]>([]);
  const [isAddingContacts, setIsAddingContacts] = useState(false);
  const [showConflictModal, setShowConflictModal] = useState(false);
  const [selectedConflict, setSelectedConflict] = useState<any>(null);
  const [isNsfSyncing, setIsNsfSyncing] = useState(false);
  const [centralMappings, setCentralMappings] = useState<any>(null);
  const [isLoadingMappings, setIsLoadingMappings] = useState(true);

  // Centralized NSF Calculation Service
  // Calculate total NSF from property or NSF fields
  const getTotalNsf = useCallback((nsfFields: DealNsfFieldV2[]) => {
    // Get total NSF from property first (this is the primary source)
    const propertyTotalNsf = deal?.property?.totalNsfNetSquareFoot;
    if (propertyTotalNsf && propertyTotalNsf > 0) {
      return propertyTotalNsf;
    }
    
    // Fallback: calculate from NSF fields if property doesn't have it
    if (!nsfFields || nsfFields.length === 0) return 0;
    
    // Sum up all NSF measurements from fields (legacy fallback)
    return nsfFields.reduce((total, nsf) => {
      const nsfValue = parseFloat((nsf as any).totalNsfNetSquareFoot?.toString() || '0') || 0;
      return total + nsfValue;
    }, 0);
  }, [deal?.property?.totalNsfNetSquareFoot]);

      // Check if NSF fields need syncing - simplified to avoid infinite loops
    const checkNsfSync = useCallback(() => {
      if (!deal || !deal.nsfFields) return;
      
      const totalNsf = getTotalNsf(deal.nsfFields);
      
      // Check if any NSF field has a different total NSF value from property
      const needsSync = deal.property && deal.property.totalNsfNetSquareFoot !== totalNsf;
      
      if (needsSync && editedDeal) {
        // Update property with the new total NSF value
        setEditedDeal(prev => prev ? {
          ...prev,
          property: prev.property ? {
            ...prev.property,
            totalNsfNetSquareFoot: totalNsf
          } : prev.property
        } : null);
      }
    }, [deal?.nsfFields, deal?.property, editedDeal, getTotalNsf]);

  // Calculate total amount by context (sources/uses)
  const calculateAmountByContext = (nsfFields: any[], context: string): number => {
    if (!nsfFields || nsfFields.length === 0) return 0;
    
    return nsfFields
      .filter(nsf => nsf.nsfContext === context)
      .reduce((total, nsf) => {
        const amount = parseFloat(nsf.amount?.toString() || '0') || 0;
        return total + amount;
      }, 0);
  };

  // Synchronize NSF data across all fields
  const synchronizeNsfData = useCallback(() => {
    if (!editedDeal || !editedDeal.nsfFields) return;
    
    // Get total NSF from property
    const totalNsf = deal?.property?.totalNsfNetSquareFoot || 0;
    
    // Note: NSF measurements are now stored in the property object, not in individual NSF fields
    // This function is no longer needed as NSF measurements are centralized in property
  }, [editedDeal]);

  // Remove the problematic useEffect that was causing infinite loops
  // useEffect(() => {
  //   synchronizeNsfData();
  // }, [deal?.nsfFields, synchronizeNsfData]);

  // Fetch central mappings
  useEffect(() => {
    const fetchCentralMappings = async () => {
      try {
        const response = await fetch('/api/central-mappings');
        if (response.ok) {
          const data = await response.json();
          setCentralMappings(data.data);
        }
      } catch (error) {
        console.error('Error fetching central mappings:', error);
      } finally {
        setIsLoadingMappings(false);
      }
    };

    fetchCentralMappings();
  }, []);



  // Helper function to check if a field has conflicts
  const hasConflict = (fieldName: string): boolean => {
    if (!currentDeal?.conflicts) return false;
    
    // Check for both camelCase and snake_case versions of the field name
    const snakeCaseField = fieldName.replace(/([A-Z])/g, '_$1').toLowerCase();
    const conflictData = currentDeal.conflicts[fieldName] || currentDeal.conflicts[snakeCaseField];
    
    // Only return true if there are alternatives (actual conflicts to resolve)
    return conflictData !== undefined && 
           conflictData.alternatives && 
           conflictData.alternatives.length > 0;
  };

  // Helper function to get conflict data for a field
  const getConflictData = (fieldName: string) => {
    if (!currentDeal?.conflicts) return null;
    
    // Check for both camelCase and snake_case versions of the field name
    const snakeCaseField = fieldName.replace(/([A-Z])/g, '_$1').toLowerCase();
    return currentDeal.conflicts[fieldName] || currentDeal.conflicts[snakeCaseField];
  };

  // Helper function to check if an NSF field has conflicts
  const hasNsfConflict = (context: string, type: string, field: string): boolean => {
    if (!currentDeal?.conflicts?.nsf_fields) return false;
    
    const contextConflicts = currentDeal.conflicts.nsf_fields[context];
    if (!contextConflicts) return false;
    
    const typeConflicts = contextConflicts[type];
    if (!typeConflicts) return false;
    
    const fieldConflicts = typeConflicts[field];
    if (!fieldConflicts) return false;
    
    return fieldConflicts.alternatives && fieldConflicts.alternatives.length > 0;
  };

  // Helper function to get NSF conflict data
  const getNsfConflictData = (context: string, type: string, field: string) => {
    if (!currentDeal?.conflicts?.nsf_fields) return null;
    
    const contextConflicts = currentDeal.conflicts.nsf_fields[context];
    if (!contextConflicts) return null;
    
    const typeConflicts = contextConflicts[type];
    if (!typeConflicts) return null;
    
    return typeConflicts[field] || null;
  };

  // Helper function to get all conflicts with alternatives
  const getAllConflictsWithAlternatives = () => {
    const conflicts: Array<{
      conflictType: 'field' | 'nsf';
      fieldName?: string;
      context?: string;
      nsfType?: string;
      field?: string;
      conflictData: any;
    }> = [];
    
    if (!currentDeal?.conflicts) return conflicts;
    
    // Handle regular field conflicts
    Object.entries(currentDeal.conflicts).forEach(([fieldName, conflictData]) => {
      if (fieldName !== 'nsf_fields' && conflictData.alternatives && conflictData.alternatives.length > 0) {
        conflicts.push({
          conflictType: 'field',
          fieldName,
          conflictData
        });
      }
    });
    
    // Handle NSF field conflicts
    if (currentDeal.conflicts.nsf_fields) {
      Object.entries(currentDeal.conflicts.nsf_fields).forEach(([context, contextConflicts]) => {
        if (typeof contextConflicts === 'object' && contextConflicts !== null) {
          Object.entries(contextConflicts).forEach(([nsfType, typeConflicts]) => {
            if (typeof typeConflicts === 'object' && typeConflicts !== null) {
              Object.entries(typeConflicts).forEach(([field, fieldConflicts]) => {
                if (fieldConflicts && typeof fieldConflicts === 'object' && 
                    'alternatives' in fieldConflicts && 
                    fieldConflicts.alternatives && 
                    fieldConflicts.alternatives.length > 0) {
                  conflicts.push({
                    conflictType: 'nsf',
                    context,
                    nsfType,
                    field,
                    conflictData: fieldConflicts
                  });
                }
              });
            }
          });
        }
      });
    }
    
    return conflicts;
  };



  // Helper functions for calculating NSF quality
  const calculateNsfTypeQuality = (nsfFields: any[], dealType: string): number => {
    const typeFields = nsfFields.filter(nsf => nsf.dealType === dealType);
    if (typeFields.length === 0) return 0;
    
    let totalFields = 0;
    let completedFields = 0;
    
    typeFields.forEach(nsf => {
      // Count all possible fields for each NSF record
      const fields = [
        'amount', 'amountPerGsf', 'amountPerNsf', 'amountPerZfa', 'percentageOfTotal'
      ];
      
      totalFields += fields.length;
      fields.forEach(field => {
        if (nsf[field] !== null && nsf[field] !== undefined && nsf[field] !== '') {
          completedFields++;
        }
      });
    });
    
    return totalFields > 0 ? Math.round((completedFields / totalFields) * 100) : 0;
  };

  const calculateNsfTypeCompletedFields = (nsfFields: any[], dealType: string): number => {
    const typeFields = nsfFields.filter(nsf => nsf.dealType === dealType);
    if (typeFields.length === 0) return 0;
    
    let completedFields = 0;
    typeFields.forEach(nsf => {
      const fields = [
        'amount', 'amountPerGsf', 'amountPerNsf', 'amountPerZfa', 'percentageOfTotal'
      ];
      
      fields.forEach(field => {
        if (nsf[field] !== null && nsf[field] !== undefined && nsf[field] !== '') {
          completedFields++;
        }
      });
    });
    
    return completedFields;
  };

  const calculateNsfTypeTotalFields = (nsfFields: any[], dealType: string): number => {
    const typeFields = nsfFields.filter(nsf => nsf.dealType === dealType);
    if (typeFields.length === 0) return 0;
    
    const fields = [
      'amount', 'amountPerGsf', 'amountPerNsf', 'amountPerZfa', 'percentageOfTotal'
    ];
    
    return typeFields.length * fields.length;
  };

  const calculateNsfTypeMissingFields = (nsfFields: any[], dealType: string): string[] => {
    const typeFields = nsfFields.filter(nsf => nsf.dealType === dealType);
    if (typeFields.length === 0) return [];
    
    const missingFields: string[] = [];
    typeFields.forEach((nsf, index) => {
      const fields = [
        { key: 'amount', label: 'Amount' },
        { key: 'amountPerGsf', label: 'Amount per GSF' },
        { key: 'amountPerNsf', label: 'Amount per NSF' },
        { key: 'amountPerZfa', label: 'Amount per ZFA' },
        { key: 'percentageOfTotal', label: 'Percentage' }
      ];
      
      fields.forEach(field => {
        if (nsf[field.key] === null || nsf[field.key] === undefined || nsf[field.key] === '') {
          missingFields.push(`${dealType.toUpperCase()} ${index + 1} - ${field.label}`);
        }
      });
    });
    
    return missingFields;
  };

  // Format NSF quality data for the DataQualityBanner
  const formatNsfQualityForBanner = (nsfFields: any[]) => {
    const nsfTypes = ['debt', 'equity', 'acquisition', 'hard_cost', 'soft_cost', 'finance', 'refinance', 'development', 'repositioning', 'other'];
    const result: any = {};
    
    nsfTypes.forEach(type => {
      const typeFields = nsfFields.filter(nsf => nsf.dealType === type);
      if (typeFields.length === 0) return;
      
      let totalFields = 0;
      let completedFields = 0;
      const records: any[] = [];
      
      typeFields.forEach(nsf => {
        const fields = [
          'amount', 'amountPerGsf', 'amountPerNsf', 'amountPerZfa', 'percentageOfTotal'
        ];
        
        const fieldStatus: any = {};
        fields.forEach(field => {
          const hasValue = nsf[field] !== null && nsf[field] !== undefined && nsf[field] !== '';
          fieldStatus[field] = { hasValue, value: nsf[field] };
          totalFields++;
          if (hasValue) completedFields++;
        });
        
        records.push({
          dealType: nsf.dealType,
          nsfContext: nsf.nsfContext,
          fields: fieldStatus
        });
      });
      
      const qualityScore = totalFields > 0 ? Math.round((completedFields / totalFields) * 100) : 0;
      
      result[type] = {
        qualityScore,
        completedFields,
        totalFields,
        records
      };
    });
    
    return result;
  };

  // Calculate askAmount as list of individual required source amounts (read-only, auto-calculated)
  const calculateAskAmount = (nsfFields: any[]): number[] => {
    if (!nsfFields || nsfFields.length === 0) return [];
    
    const requiredSourceAmounts = nsfFields
      .filter(nsf => nsf.nsfContext === 'sources' && nsf.isRequired && nsf.amount && nsf.amount > 0)
      .map(nsf => nsf.amount || 0);
    
    console.log('calculateAskAmount - NSF fields:', nsfFields);
    console.log('calculateAskAmount - Required source fields with amounts:', nsfFields.filter(nsf => nsf.nsfContext === 'sources' && nsf.isRequired && nsf.amount && nsf.amount > 0));
    console.log('calculateAskAmount - Required source amounts (list):', requiredSourceAmounts);
    
    return requiredSourceAmounts;
  };

  // Get all available capital positions from NSF source fields (for reference only)
  const suggestAskCapitalPosition = (nsfFields: any[]): string[] => {
    if (!nsfFields || nsfFields.length === 0) return [];
    
    const availablePositions = nsfFields
      .filter(nsf => nsf.nsfContext === 'sources' && nsf.amount && nsf.amount > 0)
      .map(nsf => {
        // Use sourceType for capital position
        return nsf.sourceType;
      })
      .filter((pos): pos is string => pos !== null && pos !== '');
    
    console.log('suggestAskCapitalPosition - Available positions:', availablePositions);
    return availablePositions;
  };

  // Fetch contacts for this V2 deal
  const fetchDealContacts = async () => {
    if (!dealId) return;
    setDealContactsLoading(true);
    try {
      const res = await fetch(`/api/v2/deals/${dealId}/contacts`);
      if (!res.ok) throw new Error("Failed to fetch V2 deal contacts");
      const data = await res.json();
      setDealContacts(data.contacts || []);
    } catch (err) {
      console.error("Error loading V2 deal contacts:", err);
    } finally {
      setDealContactsLoading(false);
    }
  };

  // Add contacts to V2 deal
  const addContactsToDeal = async () => {
    if (!dealId || selectedContacts.length === 0) return;
    
    setIsAddingContacts(true);
    try {
      const contactIds = selectedContacts.map(contact => contact.contact_id);
      const res = await fetch(`/api/v2/deals/${dealId}/contacts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ contactIds }),
      });
      
      if (!res.ok) throw new Error("Failed to add contacts to V2 deal");
      
      const data = await res.json();
      toast.success(`Added ${data.totalAdded} contact(s) to V2 deal`);
      
      // Refresh deal contacts
      await fetchDealContacts();
      
      // Clear selected contacts
      setSelectedContacts([]);
    } catch (err) {
      console.error("Error adding contacts to V2 deal:", err);
      toast.error("Failed to add contacts to V2 deal");
    } finally {
      setIsAddingContacts(false);
    }
  };

  // Remove contact from V2 deal
  const removeContactFromDeal = async (contactId: number) => {
    if (!dealId) return;
    
    try {
      const res = await fetch(`/api/v2/deals/${dealId}/contacts?contactId=${contactId}`, {
        method: 'DELETE',
      });
      
      if (!res.ok) throw new Error("Failed to remove contact from V2 deal");
      
      toast.success("Contact removed from V2 deal");
      
      // Refresh deal contacts
      await fetchDealContacts();
    } catch (err) {
      console.error("Error removing contact from V2 deal:", err);
      toast.error("Failed to remove contact from V2 deal");
    }
  };

  // Fetch jobs for this deal
  const fetchJobs = async () => {
    try {
      setJobsLoading(true);
      const response = await fetch(`/api/v2/deals/${dealId}/jobs`);
      if (response.ok) {
        const data = await response.json();
        setJobs(data.jobs || []);
      }
    } catch (error) {
      console.error("Error fetching jobs:", error);
    } finally {
      setJobsLoading(false);
    }
  };

  // Handle running requirement extraction
  const handleRunRequirementExtraction = async () => {
    setIsRunningRequirementExtraction(true);
    
    try {
      const response = await fetch(`/api/v2/deals/${dealId}/run-requirement-extraction`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          dealId: parseInt(dealId),
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to run requirement extraction");
      }

      toast.success("Requirement extraction started successfully");
      
      // Refresh jobs after starting extraction
      if (jobsRefreshInterval) {
        clearInterval(jobsRefreshInterval);
      }
      fetchJobs();
      
    } catch (error) {
      console.error("Error running requirement extraction:", error);
      toast.error(error instanceof Error ? error.message : "Failed to run requirement extraction");
    } finally {
      setIsRunningRequirementExtraction(false);
    }
  };

  // Utility functions
  const formatCurrency = (amount: number | string | null | undefined | number[], isPerSquareFoot: boolean = false): string => {
    if (amount === null || amount === undefined) return "N/A";
    if (Array.isArray(amount)) return "N/A"; // Handle arrays
    const num = typeof amount === "string" ? parseFloat(amount) : amount;
    if (isNaN(num)) return "N/A";
    
    // For per-square-foot amounts, don't multiply by 1,000,000
    const displayAmount = isPerSquareFoot ? num : num * 1000000;
    
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(displayAmount);
  };

  // New function to format raw currency values with M, B, etc.
  const formatRawCurrency = (amount: number | string | null | undefined | number[]): string => {
    if (amount === null || amount === undefined) return "N/A";
    if (Array.isArray(amount)) return "N/A"; // Handle arrays
    const num = typeof amount === "string" ? parseFloat(amount) : amount;
    if (isNaN(num)) return "N/A";
    
    if (num >= 1000000000) {
      return `$${(num / 1000000000).toFixed(1)}B`;
    } else if (num >= 1000000) {
      return `$${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `$${(num / 1000).toFixed(1)}K`;
    } else {
      return `$${num.toLocaleString()}`;
    }
  };

  // Function to format raw values without M/B/K conversion (for NSF per-square-foot amounts)
  const formatRawValue = (amount: number | string | null | undefined | number[]): string => {
    if (amount === null || amount === undefined) return "N/A";
    if (Array.isArray(amount)) return "N/A"; // Handle arrays
    const num = typeof amount === "string" ? parseFloat(amount) : amount;
    if (isNaN(num)) return "N/A";
    return `$${num.toLocaleString()}`;
  };

  const formatPercentage = (value: number | string | null | number[]): string => {
    if (value === null || value === undefined) return "N/A";
    if (Array.isArray(value)) return "N/A"; // Handle arrays
    const num = typeof value === "string" ? parseFloat(value) : value;
    if (isNaN(num)) return "N/A";
    
    // Values are stored as decimals (0.67 for 67%), so just multiply by 100 for display
    // Use toFixed(2) to prevent scientific notation and show exactly 2 decimal places
    const percentage = (num * 100);
    return `${percentage.toFixed(2)}%`;
  };

  const formatNumber = (value: number | string | null | number[], unit?: string): string => {
    if (value === null || value === undefined) return "N/A";
    if (Array.isArray(value)) return "N/A"; // Handle arrays
    const num = typeof value === "string" ? parseFloat(value) : value;
    if (isNaN(num)) return "N/A";
    return `${num.toLocaleString()}${unit ? ` ${unit}` : ""}`;
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    const url = new URL(window.location.href);
    url.searchParams.set('tab', value);
    router.push(url.pathname + url.search);
  };

  // Fetch deal data using v2 API
  useEffect(() => {
    async function fetchDeal() {
      try {
        const response = await fetch(`/api/v2/deals/${dealId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch deal");
        }
        const dealData = await response.json();
        
        // Calculate askAmount from source NSF amounts (auto-calculated, read-only)
        const askAmount = calculateAskAmount(dealData.deal.nsfFields);
        
        // Only use existing askCapitalPosition, don't auto-suggest
        const suggestedAskCapitalPosition = dealData.deal.askCapitalPosition || [];
        
        const dealWithAskAmount = {
          ...dealData.deal,
          askAmount,
          askCapitalPosition: suggestedAskCapitalPosition
        };
        
        console.log('Fetched deal with auto-calculated askAmount:', dealWithAskAmount);
        console.log('Suggested askCapitalPosition:', suggestedAskCapitalPosition);
        
        // Don't auto-mark NSF fields as required - let user control this via askCapitalPosition
        
        setDeal(dealWithAskAmount);
        setEditedDeal(dealWithAskAmount);
      } catch (error) {
        console.error("Error fetching deal:", error);
        setError("Failed to load deal");
      } finally {
        setLoading(false);
      }
    }

    if (dealId) {
      fetchDeal();
      fetchJobs();
      fetchDealContacts();
    }
  }, [dealId]);

  // Comprehensive refresh function for when NSF fields are added/removed
  const refreshDealData = useCallback(async () => {
    if (!dealId) return;
    
    try {
      console.log('Refreshing deal data after NSF field changes...');
      const response = await fetch(`/api/v2/deals/${dealId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch updated deal");
      }
      const dealData = await response.json();
      const freshDeal = dealData.deal;
      
      // Calculate askAmount from source NSF amounts
      const askAmount = calculateAskAmount(freshDeal.nsfFields);
      
      const dealWithAskAmount = {
        ...freshDeal,
        askAmount,
        askCapitalPosition: freshDeal.askCapitalPosition || []
      };
      
      console.log('Refreshed deal data:', dealWithAskAmount);
      console.log('Updated investment criteria debt:', dealWithAskAmount.investmentCriteriaDebt);
      console.log('Updated investment criteria equity:', dealWithAskAmount.investmentCriteriaEquity);
      
      // Update the base deal data
      setDeal(dealWithAskAmount);
      
      // Merge with existing edits to preserve unsaved changes
      if (editedDeal) {
        setEditedDeal(prev => {
          if (!prev) return dealWithAskAmount;
          
          // Create a merged version that preserves user edits
          const mergedDeal = { ...dealWithAskAmount };
          
          // Preserve all user edits from editedDeal
          Object.keys(prev).forEach(key => {
            if (key === 'nsfFields') {
              // For NSF fields, merge the fresh data with any local edits
              const freshNsfFields = dealWithAskAmount.nsfFields || [];
              const editedNsfFields = prev.nsfFields || [];
              
              // Create a map of edited fields by ID
              const editedFieldsMap = new Map();
              editedNsfFields.forEach(field => {
                if (field.id > 0) { // Only track edits to existing fields
                  editedFieldsMap.set(field.id, field);
                }
              });
              
              // Merge fresh data with edits
              const mergedNsfFields = freshNsfFields.map(freshField => {
                const editedField = editedFieldsMap.get(freshField.id);
                if (editedField) {
                  // Return the edited version, preserving user changes
                  return { ...freshField, ...editedField };
                }
                return freshField;
              });
              
              mergedDeal.nsfFields = mergedNsfFields;
            } else if (key === 'investmentCriteriaDebt') {
              // For debt criteria, merge fresh data with local edits
              const freshDebtCriteria = dealWithAskAmount.investmentCriteriaDebt || [];
              const editedDebtCriteria = prev.investmentCriteriaDebt || [];
              
              // Create a map of edited debt criteria by ID
              const editedDebtMap = new Map();
              editedDebtCriteria.forEach(criteria => {
                if (criteria.investmentCriteriaDebtId > 0) {
                  editedDebtMap.set(criteria.investmentCriteriaDebtId, criteria);
                }
              });
              
              // Merge fresh debt data with edits
              const mergedDebtCriteria = freshDebtCriteria.map(freshCriteria => {
                const editedCriteria = editedDebtMap.get(freshCriteria.investmentCriteriaDebtId);
                if (editedCriteria) {
                  // Return the edited version, preserving user changes
                  return { ...freshCriteria, ...editedCriteria };
                }
                return freshCriteria;
              });
              
              mergedDeal.investmentCriteriaDebt = mergedDebtCriteria;
            } else if (key === 'investmentCriteriaEquity') {
              // For equity criteria, merge fresh data with local edits
              const freshEquityCriteria = dealWithAskAmount.investmentCriteriaEquity || [];
              const editedEquityCriteria = prev.investmentCriteriaEquity || [];
              
              // Create a map of edited equity criteria by ID
              const editedEquityMap = new Map();
              editedEquityCriteria.forEach(criteria => {
                if (criteria.investmentCriteriaEquityId > 0) {
                  editedEquityMap.set(criteria.investmentCriteriaEquityId, criteria);
                }
              });
              
              // Merge fresh equity data with edits
              const mergedEquityCriteria = freshEquityCriteria.map(freshCriteria => {
                const editedCriteria = editedEquityMap.get(freshCriteria.investmentCriteriaEquityId);
                if (editedCriteria) {
                  // Return the edited version, preserving user changes
                  return { ...freshCriteria, ...editedCriteria };
                }
                return freshCriteria;
              });
              
              mergedDeal.investmentCriteriaEquity = mergedEquityCriteria;
            } else if (key !== 'id' && key !== 'dealId' && key !== 'createdAt' && key !== 'updatedAt') {
              // Preserve other user edits (excluding system fields)
              mergedDeal[key] = prev[key];
            }
          });
          
          return mergedDeal;
        });
      } else {
        // If no edits exist, just set the fresh data
        setEditedDeal(dealWithAskAmount);
      }
      
      toast.success('Deal data refreshed successfully');
    } catch (error) {
      console.error("Error refreshing deal data:", error);
      toast.error("Failed to refresh deal data");
    }
  }, [dealId, calculateAskAmount, editedDeal]);

  // Save changes
  const handleSave = async () => {
    if (!editedDeal || !deal) return;
    
    setIsSaving(true);
    try {
      // Before saving, automatically update isRequired for all NSF sources based on askCapitalPosition
      const updatedNsfFields = editedDeal.nsfFields?.map(nsf => {
        if (nsf.nsfContext === 'sources') {
          // Determine the capital position to check - use sourceType for sources
          let capitalPosition = nsf.sourceType;
          
          // Mark as required if this capital position is in askCapitalPosition
          const isRequired = capitalPosition ? editedDeal.askCapitalPosition?.includes(capitalPosition) || false : false;
          
          console.log(`NSF Source ${nsf.id}: capitalPosition=${capitalPosition}, isRequired=${isRequired}`);
          
          return {
            ...nsf,
            isRequired
          };
        }
        return nsf;
      }) || [];

      // Prepare the comprehensive update payload
      const updatePayload = {
        ...editedDeal,
        // Include investment criteria updates
        investmentCriteriaDebt: editedDeal.investmentCriteriaDebt || [],
        investmentCriteriaEquity: editedDeal.investmentCriteriaEquity || [],
        // Include updated NSF fields with correct isRequired values
        nsfFields: updatedNsfFields,
        // Include investment criteria capital positions
        investmentCriteria: editedDeal.investmentCriteriaDebt?.map(debt => ({
          criteria_id: debt.investmentCriteriaId,
          capitalPosition: debt.capitalPosition
        })).concat(editedDeal.investmentCriteriaEquity?.map(equity => ({
          criteria_id: equity.investmentCriteriaId,
          capitalPosition: equity.capitalPosition
        })) || []) || []
      };

      console.log('Saving deal with payload:', updatePayload);
      console.log('Investment Criteria Debt:', editedDeal.investmentCriteriaDebt);
      console.log('Investment Criteria Equity:', editedDeal.investmentCriteriaEquity);

      const response = await fetch(`/api/v2/deals/${dealId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatePayload),
      });

      if (!response.ok) {
        throw new Error(`Failed to update deal: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        // Refresh the deal data by fetching again
        const refreshResponse = await fetch(`/api/v2/deals/${dealId}`);
        if (refreshResponse.ok) {
          const refreshData = await refreshResponse.json();
          setDeal(refreshData.deal);
          setEditedDeal(refreshData.deal);
        }
        setIsEditing(false);
        toast.success('Deal updated successfully');
      } else {
        throw new Error(result.message || 'Failed to update deal');
      }
    } catch (error) {
      console.error('Error saving deal:', error);
      toast.error(`Failed to save deal: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSaving(false);
    }
  };

  // Cancel editing
  const handleCancel = () => {
    setEditedDeal(deal);
    setIsEditing(false);
  };

  // Handle field changes
  const handleFieldChange = (field: string, value: any) => {
    if (!editedDeal) return;
    
    // If totalNsfNetSquareFoot is being updated from Overview tab, propagate to all NSF fields
    if (field === 'totalNsfNetSquareFoot') {
      setEditedDeal(prev => {
        if (!prev) return null;
        
        const updatedNsfFields = prev.nsfFields?.map(nsf => {
          // Recalculate per-NSF amounts and percentages using property values
          const amountPerNsf = nsf.amount && value > 0 ? nsf.amount / value : 0;
          const amountPerGsf = nsf.amount && prev.property?.gsfGrossSquareFoot && prev.property.gsfGrossSquareFoot > 0 ? nsf.amount / prev.property.gsfGrossSquareFoot : 0;
          const amountPerZfa = nsf.amount && prev.property?.zfaZoningFloorArea && prev.property.zfaZoningFloorArea > 0 ? nsf.amount / prev.property.zfaZoningFloorArea : 0;
          
          return {
            ...nsf,
            amountPerNsf,
            amountPerGsf,
            amountPerZfa
          };
        }) || [];
        
        // Recalculate askAmount since NSF fields have changed
        const newAskAmount = calculateAskAmount(updatedNsfFields);
        
        return {
          ...prev,
          [field]: value,
          nsfFields: updatedNsfFields,
          askAmount: newAskAmount
        };
      });
    } else {
      setEditedDeal(prev => prev ? { ...prev, [field]: value } : null);
    }
  };

  // Handle property field changes
  const handlePropertyFieldChange = (field: string, value: any) => {
    if (!editedDeal) return;
    setEditedDeal(prev => {
      if (!prev) return null;
      return {
        ...prev,
        property: prev.property 
          ? { ...prev.property, [field]: value } as PropertyV2 
          : { [field]: value } as PropertyV2
      };
    });
  };

  // Handle NSF field changes with synchronization
  const handleNsfFieldChange = (nsfId: number, field: keyof DealNsfFieldV2, value: any) => {
    if (!editedDeal) return;
    
    setEditedDeal(prev => {
      if (!prev) return null;
      
      // Update the specific NSF field
      const updatedNsfFields = prev.nsfFields?.map(nsf => 
        nsf.id === nsfId ? { ...nsf, [field]: value } : nsf
      ) || [];
      
      // If updating totalNsfNetSquareFoot, synchronize across all NSF fields
      if (field === 'totalNsfNetSquareFoot') {
        const synchronizedNsfFields = updatedNsfFields.map(nsf => ({
          ...nsf
        }));
        
        return {
          ...prev,
          nsfFields: synchronizedNsfFields,
          totalNsfNetSquareFoot: value
        };
      }
      
      // If updating amount, recalculate percentages and per-NSF values
      if (field === 'amount') {
        // Calculate totals separately for sources and uses
        const sourceFields = updatedNsfFields.filter(nsf => nsf.nsfContext === 'sources');
        const useFields = updatedNsfFields.filter(nsf => nsf.nsfContext === 'uses_total');
        
        // Calculate total sources (excluding Common Equity if LP/GP present)
        const hasLP = sourceFields.some(nsf => 
          nsf.sourceType === 'Limited Partner (LP)' || 
          (nsf as any).capitalPosition === 'Limited Partner (LP)'
        );
        const hasGP = sourceFields.some(nsf => 
          nsf.sourceType === 'General Partner (GP)' || 
          (nsf as any).capitalPosition === 'General Partner (GP)'
        );
        
        const totalSources = sourceFields.reduce((sum, nsf) => {
          const amount = parseFloat(nsf.amount?.toString() || '0') || 0;
          const isCommonEquity = nsf.sourceType === 'Common Equity' || (nsf as any).capitalPosition === 'Common Equity';
          
          // If LP/GP are present, exclude Common Equity from total
          if ((hasLP || hasGP) && isCommonEquity) {
            return sum; // Don't add Common Equity to total
          }
          
          return sum + amount;
        }, 0);
        
        // Calculate total uses
        const totalUses = useFields.reduce((sum, nsf) => {
          const amount = parseFloat(nsf.amount?.toString() || '0') || 0;
          return sum + amount;
        }, 0);
        
        console.log('🔄 NSF Amount Updated - Total Sources:', totalSources, 'Total Uses:', totalUses);
        console.log('🔄 NSF Fields before percentage calculation:', updatedNsfFields.map(nsf => ({ 
          id: nsf.id, 
          context: nsf.nsfContext, 
          type: nsf.sourceType || nsf.useType, 
          amount: nsf.amount 
        })));
        
        const updatedNsfFieldsWithPercentages = updatedNsfFields.map(nsf => {
          const amount = parseFloat(nsf.amount?.toString() || '0') || 0;
          let percentage = 0;
          
          if (nsf.nsfContext === 'sources') {
            // Calculate source percentage based on total sources (excluding Common Equity if LP/GP present)
            const isCommonEquity = nsf.sourceType === 'Common Equity' || (nsf as any).capitalPosition === 'Common Equity';
            
            if ((hasLP || hasGP) && isCommonEquity) {
              percentage = 0; // Common Equity is excluded from percentage calculation when LP/GP present
            } else {
              percentage = totalSources > 0 ? amount / totalSources : 0;
            }
          } else if (nsf.nsfContext === 'uses_total') {
            // Calculate use percentage based on total uses
            percentage = totalUses > 0 ? amount / totalUses : 0;
          }
          
          const amountPerNsf = editedDeal.property?.totalNsfNetSquareFoot && editedDeal.property.totalNsfNetSquareFoot > 0 ? amount / editedDeal.property.totalNsfNetSquareFoot : 0;
          const amountPerGsf = editedDeal.property?.gsfGrossSquareFoot && editedDeal.property.gsfGrossSquareFoot > 0 ? amount / editedDeal.property.gsfGrossSquareFoot : 0;
          const amountPerZfa = editedDeal.property?.zfaZoningFloorArea && editedDeal.property.zfaZoningFloorArea > 0 ? amount / editedDeal.property.zfaZoningFloorArea : 0;
          
          console.log(`📊 NSF Field ${nsf.id} (${nsf.nsfContext} - ${nsf.sourceType || nsf.useType}): amount=${nsf.amount}, calculated percentage=${percentage}, totalSources=${totalSources}, totalUses=${totalUses}`);
          
          return {
            ...nsf,
            percentageOfTotal: percentage,
            amountPerNsf,
            amountPerGsf,
            amountPerZfa
          };
        });
        
        console.log('🔄 Final NSF Fields with percentages:', updatedNsfFieldsWithPercentages.map(nsf => ({ 
          id: nsf.id, 
          context: nsf.nsfContext, 
          type: nsf.sourceType || nsf.useType, 
          amount: nsf.amount, 
          percentageOfTotal: nsf.percentageOfTotal 
        })));
        
        // Auto-update askAmount based on source amounts
        const sourceAmounts = updatedNsfFieldsWithPercentages
          .filter(nsf => nsf.nsfContext === 'sources' && nsf.isRequired)
          .map(nsf => nsf.amount || 0);
        
        return {
          ...prev,
          nsfFields: updatedNsfFieldsWithPercentages,
          askAmount: sourceAmounts
        };
      }
      
      return {
        ...prev,
        nsfFields: updatedNsfFields
      };
    });
  };

  // Wrapper function for SourcesUsesTab compatibility
  const handleNsfFieldChangeLegacy = (id: string, field: string, value: any) => {
    handleNsfFieldChange(parseInt(id), field as keyof DealNsfFieldV2, value);
  };

  // Handle debt criteria changes
  const handleDebtCriteriaChange = (criteriaId: number, field: string, value: any) => {
    console.log('handleDebtCriteriaChange called:', { criteriaId, field, value, editedDeal: !!editedDeal });
    if (!editedDeal) return;
    setEditedDeal(prev => {
      if (!prev) return null;
      console.log('Updating debt criteria:', { prev: prev.investmentCriteriaDebt, criteriaId, field, value });
      return {
        ...prev,
        investmentCriteriaDebt: prev.investmentCriteriaDebt?.map((debt) => 
          debt.investmentCriteriaDebtId === criteriaId ? { ...debt, [field]: value } : debt
        ) || []
      };
    });
  };

  // Handle equity criteria changes
  const handleEquityCriteriaChange = (criteriaId: number, field: string, value: any) => {
    console.log('handleEquityCriteriaChange called:', { criteriaId, field, value, editedDeal: !!editedDeal });
    if (!editedDeal) return;
    setEditedDeal(prev => {
      if (!prev) return null;
      console.log('Updating equity criteria:', { prev: prev.investmentCriteriaEquity, criteriaId, field, value });
      return {
        ...prev,
        investmentCriteriaEquity: prev.investmentCriteriaEquity?.map((equity) => 
          equity.investmentCriteriaEquityId === criteriaId ? { ...equity, [field]: value } : equity
        ) || []
      };
    });
  };

  // Update isRequired for NSF fields based on askCapitalPosition
  const updateIsRequiredForNsfFields = (askCapitalPosition: string[]) => {
    if (!editedDeal) return;
    
    console.log('updateIsRequiredForNsfFields called with:', askCapitalPosition);
    console.log('Current editedDeal.nsfFields:', editedDeal.nsfFields);
    
    const updatedNsfFields = editedDeal.nsfFields?.map(nsf => {
      // Determine the capital position to check - use sourceType for sources, useType for uses
      let capitalPosition = nsf.nsfContext === 'sources' ? nsf.sourceType : nsf.useType;
      
      const isRequired = capitalPosition ? askCapitalPosition.includes(capitalPosition) : false;
      
      console.log(`NSF ${nsf.id}: capitalPosition=${capitalPosition}, isRequired=${isRequired}`);
      
      // Mark as required/not required but keep the amounts intact
      return { ...nsf, isRequired };
    }) || [];
    
    console.log('Updated NSF fields:', updatedNsfFields);
    
    // Auto-calculate askAmount from updated NSF fields
    const newAskAmount = calculateAskAmount(updatedNsfFields);
    console.log('New askAmount calculated:', newAskAmount);
    
    setEditedDeal(prev => {
      if (!prev) return null;
      return {
        ...prev,
        nsfFields: updatedNsfFields,
        askAmount: newAskAmount
      };
    });
  };

  // Handle askCapitalPosition changes with automatic NSF field updates
  const handleAskCapitalPositionChange = (newPositions: string[]) => {
    if (!editedDeal) return;
    
    console.log('handleAskCapitalPositionChange called with:', newPositions);
    console.log('Current editedDeal:', editedDeal);
    
    setEditedDeal(prev => {
      if (!prev) return null;
      
      // Update askCapitalPosition
      const updatedDeal = {
        ...prev,
        askCapitalPosition: newPositions
      };
      
      console.log('Updated deal with new askCapitalPosition:', updatedDeal);
      
      // Update NSF fields to mark them as required if they match the new positions
      const updatedNsfFields = updatedDeal.nsfFields?.map(nsf => {
        // Determine the capital position to check - use sourceType for sources, useType for uses
        let capitalPosition = nsf.nsfContext === 'sources' ? nsf.sourceType : nsf.useType;
        
        const isRequired = capitalPosition ? newPositions.includes(capitalPosition) : false;
        
        console.log(`NSF ${nsf.id}: capitalPosition=${capitalPosition}, isRequired=${isRequired}`);
        
        // Mark as required/not required but keep the amounts intact
        return { ...nsf, isRequired };
      }) || [];
      
      console.log('Updated NSF fields:', updatedNsfFields);
      
      // Auto-calculate askAmount from updated NSF fields
      const sourceAmounts = calculateAskAmount(updatedNsfFields);
      
      console.log('Required source amounts for askAmount:', sourceAmounts);
      
      return {
        ...updatedDeal,
        nsfFields: updatedNsfFields,
        askAmount: sourceAmounts
      };
    });
  };

  // Add new debt criteria
  const addNewDebtCriteria = () => {
    if (!editedDeal) return;
    const newDebtCriteria = {
      investmentCriteriaDebtId: 0,
      investmentCriteriaId: 0,
      capitalPosition: '', // Required field for investment criteria
      notes: '',
      closingTime: 0,
      futureFacilities: '',
      eligibleBorrower: '',
      occupancyRequirements: '',
      lienPosition: '',
      minLoanDscr: 0,
      maxLoanDscr: 0,
      recourseLoan: '',
      loanMinDebtYield: '',
      prepayment: '',
      yieldMaintenance: '',
      applicationDeposit: 0,
      goodFaithDeposit: 0,
      loanOriginationMaxFee: 0,
      loanOriginationMinFee: 0,
      loanExitMinFee: 0,
      loanExitMaxFee: 0,
      loanInterestRate: 0,
      loanInterestRateBasedOffSofr: 0,
      loanInterestRateBasedOffWsj: 0,
      loanInterestRateBasedOffPrime: 0,
      loanInterestRateBasedOff3yt: 0,
      loanInterestRateBasedOff5yt: 0,
      loanInterestRateBasedOff10yt: 0,
      loanInterestRateBasedOff30yt: 0,
      rateLock: '',
      rateType: '',
      loanToValueMax: 0,
      loanToValueMin: 0,
      loanToCostMin: 0,
      loanToCostMax: 0,
      debtProgramOverview: '',
      loanType: '',
      structuredLoanTranche: '',
      loanProgram: '',
      minLoanTerm: 0,
      maxLoanTerm: 0,
      amortization: '',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setEditedDeal(prev => {
      if (!prev) return null;
      return {
        ...prev,
        investmentCriteriaDebt: [...(prev.investmentCriteriaDebt || []), newDebtCriteria]
      };
    });
  };

  // Add new equity criteria
  const addNewEquityCriteria = () => {
    if (!editedDeal) return;
    const newEquityCriteria = {
      investmentCriteriaEquityId: 0,
      investmentCriteriaId: 0,
      capitalPosition: '', // Required field for investment criteria
      targetReturn: 0,
      minimumInternalRateOfReturn: 0,
      minimumYieldOnCost: 0,
      minimumEquityMultiple: 0,
      targetCashOnCashMin: 0,
      minHoldPeriodYears: 0,
      maxHoldPeriodYears: 0,
      ownershipRequirement: '',
      attachmentPoint: 0,
      maxLeverageTolerance: 0,
      typicalClosingTimelineDays: 0,
      proofOfFundsRequirement: false,
      notes: '',
      equityProgramOverview: '',
      occupancyRequirements: '',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setEditedDeal(prev => {
      if (!prev) return null;
      return {
        ...prev,
        investmentCriteriaEquity: [...(prev.investmentCriteriaEquity || []), newEquityCriteria]
      };
    });
  };

  // Remove debt criteria
  const removeDebtCriteria = (index: number) => {
    if (!editedDeal) return;
    setEditedDeal(prev => {
      if (!prev) return null;
      return {
        ...prev,
        investmentCriteriaDebt: prev.investmentCriteriaDebt?.filter((_, i) => i !== index) || []
      };
    });
  };

  // Remove equity criteria
  const removeEquityCriteria = (index: number) => {
    if (!editedDeal) return;
    setEditedDeal(prev => {
      if (!prev) return null;
      return {
        ...prev,
        investmentCriteriaEquity: prev.investmentCriteriaEquity?.filter((_, i) => i !== index) || []
      };
    });
  };

  // Show loading state while fetching data
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Loading Deal...
            </h2>
            <p className="text-gray-600">
              Please wait while we fetch the deal information.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if there's an error or no deal found after loading
  if (error || !deal) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Deal Not Found
            </h2>
            <p className="text-gray-600 mb-6">
              {error || "The requested deal could not be found."}
            </p>
            <Button onClick={() => router.push("/dashboard/deals")}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Deals
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const currentDeal = isEditing ? editedDeal : deal;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {currentDeal?.dealName || "Loading..."}
              </h1>
              {currentDeal?.property?.owner?.ownerName && (
                <p className="text-gray-600">Owned by {currentDeal.property.owner.ownerName}</p>
              )}
              <div className="mt-2 flex items-center gap-2">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  V2 Enhanced View
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const url = new URL(window.location.href);
                    url.pathname = url.pathname.replace('/deals/v2/', '/deals/');
                    router.push(url.toString());
                  }}
                  className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 text-xs"
                >
                  Switch to V1
                </Button>
              </div>
            </div>
          </div>
          
          {/* Edit Controls */}
          <div className="flex items-center gap-2">
            {/* Run Requirement Extraction Button */}
            <Button
              onClick={handleRunRequirementExtraction}
              disabled={isRunningRequirementExtraction}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {isRunningRequirementExtraction ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Running...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Run Requirement Extraction
                </>
              )}
            </Button>
            
            {isEditing ? (
              <>
                <Button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isSaving ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Changes
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isSaving}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              </>
            ) : (
              <Button
                onClick={() => {
                  setIsEditing(true);
                  setEditedDeal(deal); // Initialize editedDeal with current deal data
                }}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Deal
              </Button>
            )}
          </div>
        </div>

        {/* Data Quality Banner - V2 Structure */}
        {deal && (
          <DataQualityBanner
            dataQualityMetrics={calculateComprehensiveQuality(deal)}
            dealName={deal.dealName || "Unnamed Deal"}
            nsfQuality={deal.nsfFields ? formatNsfQualityForBanner(deal.nsfFields) : undefined}
          />
        )}



        {/* Tabs */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Deal Details (V2)</h2>
          </div>
          <Tabs
            value={activeTab}
            onValueChange={handleTabChange}
            className="space-y-6"
          >
            <TabsList className="grid w-full mb-8 md:grid-cols-6 grid-cols-2">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="debt">Debt</TabsTrigger>
              <TabsTrigger value="equity">Equity</TabsTrigger>
              <TabsTrigger value="nsf">Sources/Uses</TabsTrigger>
              <TabsTrigger value="deal-matching">Deal Matching</TabsTrigger>
              <TabsTrigger value="contact">Contact</TabsTrigger>
              <TabsTrigger value="jobs">Jobs</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview">
              <OverviewTab
                deal={deal}
                currentDeal={currentDeal}
                dealId={dealId}
                isEditing={isEditing}
                isNsfSyncing={isNsfSyncing}
                onFieldChange={handleFieldChange}
                onPropertyFieldChange={handlePropertyFieldChange}
                updateIsRequiredForNsfFields={updateIsRequiredForNsfFields}
                getAllConflictsWithAlternatives={getAllConflictsWithAlternatives}
                hasConflict={hasConflict}
                getConflictData={getConflictData}
                formatNumber={formatNumber}
                formatRawCurrency={formatRawCurrency}
                formatPercentage={formatPercentage}
                centralMappings={centralMappings}
                isLoadingMappings={isLoadingMappings}
              />
            </TabsContent>

            {/* Debt Tab */}
            <TabsContent value="debt">
              <DebtTab
                deal={currentDeal}
                currentDeal={currentDeal}
                isEditing={isEditing}
                onDebtCriteriaChange={handleDebtCriteriaChange}
                onNsfFieldChange={handleNsfFieldChange}
                centralMappings={centralMappings}
                isLoadingMappings={isLoadingMappings}
              />
            </TabsContent>

            {/* Equity Tab */}
            <TabsContent value="equity">
              <EquityTab
                deal={currentDeal}
                currentDeal={currentDeal}
                isEditing={isEditing}
                onEquityCriteriaChange={handleEquityCriteriaChange}
                onNsfFieldChange={handleNsfFieldChange}
                centralMappings={centralMappings}
                isLoadingMappings={isLoadingMappings}
              />
            </TabsContent>

            {/* Sources/Uses Tab */}
            <TabsContent value="nsf">
              <SourcesUsesTab
                nsfFields={currentDeal?.nsfFields || []}
                isEditing={isEditing}
                isNsfSyncing={isNsfSyncing}
                onNsfFieldChange={handleNsfFieldChangeLegacy}
                hasConflict={hasConflict}
                getConflictData={getConflictData}
                hasNsfConflict={hasNsfConflict}
                getNsfConflictData={getNsfConflictData}
                formatNumber={formatNumber}
                formatRawCurrency={formatRawCurrency}
                formatPercentage={formatPercentage}
                getTotalNsf={getTotalNsf}
                calculateAmountByContext={calculateAmountByContext}
                dealId={parseInt(dealId)}
                property={currentDeal?.property}
                centralMappings={centralMappings}
                isLoadingMappings={isLoadingMappings}
                onPropertyFieldChange={handlePropertyFieldChange}
                onRefreshData={async () => {
                  // Use the comprehensive refresh function that preserves user edits
                  await refreshDealData();
                }}
              />
            </TabsContent>

            {/* Deal Matching Tab */}
            <TabsContent value="deal-matching">
              <DealMatchingTab dealId={dealId} deal={deal} />
            </TabsContent>

            {/* Contact Tab */}
            <TabsContent value="contact">
              <ContactTab
                selectedContacts={selectedContacts}
                setSelectedContacts={setSelectedContacts}
                dealId={dealId}
                fetchDealContacts={fetchDealContacts}
                addContactsToDeal={addContactsToDeal}
                removeContactFromDeal={removeContactFromDeal}
                isAddingContacts={isAddingContacts}
                matchingContacts={dealContacts}
                matchingContactsLoading={dealContactsLoading}
                matchingContactsError={null}
                matchingContactsFiltering={null}
                showAllMatches={false}
                isCrmMode={false}
                matchingContactsPagination={null}
                expandedContactId={expandedContactId}
                setExpandedContactId={setExpandedContactId}
                setMatchingContactsFiltering={() => {}}
                setShowAllMatches={() => {}}
                setIsCrmMode={() => {}}
                setMatchingContactsPagination={() => {}}
              />
            </TabsContent>

            {/* Jobs Tab */}
            <TabsContent value="jobs">
              <JobsTab jobs={jobs} jobsLoading={jobsLoading} />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default DealDetailV2;