import { useEffect, useState } from "react";

interface DealConflict {
  conflict_id: number;
  deal_id: number;
  conflict_data: any;
  status: string;
  created_at: string;
}

export default function DealConflictsList() {
  const [conflicts, setConflicts] = useState<DealConflict[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchConflicts() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch("/api/deals/conflicts");
        const data = await res.json();
        if (data.success) {
          setConflicts(data.conflicts);
        } else {
          setError(data.error || "Failed to fetch conflicts");
        }
      } catch (err) {
        setError("Failed to fetch conflicts");
      } finally {
        setLoading(false);
      }
    }
    fetchConflicts();
  }, []);

  if (loading) return <div>Loading conflicts...</div>;
  if (error) return <div className="text-red-600">{error}</div>;
  if (conflicts.length === 0) return <div>No unresolved conflicts found.</div>;

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold mb-2">Unresolved Deal Conflicts</h2>
      {conflicts.map((conflict) => (
        <div
          key={conflict.conflict_id}
          className="border rounded p-4 bg-white shadow-sm"
        >
          <div className="font-semibold">
            Conflict #{conflict.conflict_id} (Deal #{conflict.deal_id})
          </div>
          <div className="text-xs text-gray-500 mb-2">
            Created: {new Date(conflict.created_at).toLocaleString()}
          </div>
          <pre className="bg-gray-50 p-2 rounded text-xs overflow-x-auto mb-2">
            {JSON.stringify(conflict.conflict_data, null, 2)}
          </pre>
          <button className="px-3 py-1 bg-blue-600 text-white rounded" disabled>
            Resolve (coming soon)
          </button>
        </div>
      ))}
    </div>
  );
}
