"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { File, Download, Eye, Calendar, User, Hash, Upload, Play, CheckCircle, AlertCircle, RefreshCw, Plus, Trash2 } from "lucide-react";
import { toast } from "sonner";
import { FileUpload } from "@/components/ui/file-upload";

interface DealFile {
  file_id: string;
  file_name: string;
  original_name: string;
  title?: string;
  mime_type: string;
  file_size_bytes: number;
  relationship_type?: string;
  relationship_title?: string;
  is_primary?: boolean;
  display_order?: number;
  uploaded_at: string;
  processing_status?: string;
  processing_job_id?: string;
  processing_timestamp?: string;
  file_type?: string;
}

interface DealFilesProps {
  dealId: string;
  isV2Deal?: boolean;
}

export default function DealFiles({ dealId, isV2Deal = false }: DealFilesProps) {
  const [files, setFiles] = useState<DealFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [processing, setProcessing] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [newFiles, setNewFiles] = useState<File[]>([]);
  const [showUploadForm, setShowUploadForm] = useState(false);

  const fetchDealFiles = useCallback(async () => {
    try {
      setLoading(true);
      const apiEndpoint = isV2Deal ? `/api/v2/deals/${dealId}/files` : `/api/deals/${dealId}/files`;
      const response = await fetch(apiEndpoint);
      if (response.ok) {
        const data = await response.json();
        setFiles(data.files || []);
      } else {
        setError("Failed to load files");
      }
    } catch (err) {
      setError("Error loading files");
    } finally {
      setLoading(false);
    }
  }, [dealId, isV2Deal]);

  useEffect(() => {
    fetchDealFiles();
  }, [fetchDealFiles]);



  const handleFileUpload = async () => {
    if (newFiles.length === 0) return;

    setUploading(true);
    try {
      // Upload files one by one since the API expects a single file
      for (const file of newFiles) {
        const formData = new FormData();
        formData.append("file", file);
        formData.append("target_table_name", "deals");
        formData.append("target_column_name", "deal_id");
        formData.append("target_row_id", dealId);
        formData.append("relationship_type", "attachment");

        const apiEndpoint = isV2Deal ? `/api/v2/deals/${dealId}/files` : `/api/deals/${dealId}/files`;
        const response = await fetch(apiEndpoint, {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to upload file");
        }
      }

      toast.success("Files uploaded successfully");
      setNewFiles([]);
      setShowUploadForm(false);
      fetchDealFiles(); // Refresh the file list
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Error uploading files");
    } finally {
      setUploading(false);
    }
  };

  const handleProcessFiles = async () => {
    if (selectedFiles.length === 0) {
      toast.error("Please select files to process");
      return;
    }

    setProcessing(true);
    try {
      const apiEndpoint = isV2Deal ? `/api/v2/deals/${dealId}/files/process` : `/api/deals/${dealId}/files/process`;
      const response = await fetch(apiEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          selectedFiles: selectedFiles,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        const selectedFileNames = files
          .filter(file => selectedFiles.includes(file.file_id))
          .map(file => file.original_name)
          .join(", ");
        toast.success(`Processing started for ${selectedFiles.length} files: ${selectedFileNames}`);
        setSelectedFiles([]);
        fetchDealFiles(); // Refresh to show updated status
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to start processing");
      }
    } catch (error) {
      toast.error("Error starting processing");
    } finally {
      setProcessing(false);
    }
  };

  const handleSelectAll = () => {
    const processableFiles = files.filter(file => !isLLMOutputFile(file));
    if (selectedFiles.length === processableFiles.length) {
      setSelectedFiles([]);
    } else {
      setSelectedFiles(processableFiles.map(file => file.file_id));
    }
  };

  const handleFileSelect = (fileId: string) => {
    setSelectedFiles(prev => 
      prev.includes(fileId) 
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
    );
  };

  const handleDeleteFile = async (file: DealFile) => {
    if (!confirm(`Are you sure you want to remove "${file.original_name}" from this deal?`)) {
      return;
    }

    try {
      // Delete the file relationship using the new endpoint
              const apiEndpoint = isV2Deal ? `/api/v2/deals/${dealId}/files/${file.file_id}` : `/api/deals/${dealId}/files/${file.file_id}`;
        const deleteResponse = await fetch(apiEndpoint, {
          method: "DELETE",
        });

      if (!deleteResponse.ok) {
        const errorData = await deleteResponse.json();
        throw new Error(errorData.message || "Failed to delete file relationship");
      }

      toast.success(`"${file.original_name}" removed from deal`);
      
      // Remove the file from local state immediately
      setFiles(prevFiles => prevFiles.filter(f => f.file_id !== file.file_id));
      
      // Ask if user wants to rerun processing for remaining files
    
     
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Error deleting file");
    }
  };

  const downloadFile = async (fileId: string, originalName: string) => {
    try {
      const response = await fetch(`/api/files/${fileId}/download`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = originalName;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        console.error("Failed to download file");
      }
    } catch (error) {
      console.error("Error downloading file:", error);
    }
  };

  const viewLLMOutput = async (fileId: string, originalName: string) => {
    try {
      const response = await fetch(`/api/files/${fileId}/download`);
      if (response.ok) {
        const text = await response.text();
        try {
          const jsonData = JSON.parse(text);
          const formattedJson = JSON.stringify(jsonData, null, 2);
          
          // Create a new window/tab with the formatted JSON
          const newWindow = window.open('', '_blank');
          if (newWindow) {
            newWindow.document.write(`
              <html>
                <head>
                  <title>LLM Output - ${originalName}</title>
                  <style>
                    body { font-family: monospace; padding: 20px; background: #f5f5f5; }
                    pre { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                    h1 { color: #333; }
                  </style>
                </head>
                <body>
                  <h1>LLM Output: ${originalName}</h1>
                  <pre>${formattedJson}</pre>
                </body>
              </html>
            `);
            newWindow.document.close();
          }
        } catch (parseError) {
          console.error("Error parsing JSON:", parseError);
          // If it's not JSON, show as plain text
          const newWindow = window.open('', '_blank');
          if (newWindow) {
            newWindow.document.write(`
              <html>
                <head>
                  <title>File Content - ${originalName}</title>
                  <style>
                    body { font-family: monospace; padding: 20px; background: #f5f5f5; }
                    pre { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                    h1 { color: #333; }
                  </style>
                </head>
                <body>
                  <h1>File Content: ${originalName}</h1>
                  <pre>${text}</pre>
                </body>
              </html>
            `);
            newWindow.document.close();
          }
        }
      } else {
        console.error("Failed to download file");
      }
    } catch (error) {
      console.error("Error viewing file:", error);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileIcon = (mimeType: string) => {
    if (mimeType.includes("pdf")) return "📄";
    if (mimeType.includes("csv")) return "📊";
    if (mimeType.includes("spreadsheet") || mimeType.includes("excel")) return "📈";
    if (mimeType.includes("word") || mimeType.includes("document")) return "📝";
    return "📎";
  };

  const isLLMOutputFile = (file: DealFile) => {
    return file.original_name.includes("llm_output") || 
           file.original_name.includes("extraction_result") ||
           file.original_name.endsWith(".json");
  };

  const getProcessingStatusBadge = (file: DealFile) => {
    if (!file.processing_status) return null;
    
    switch (file.processing_status) {
      case "completed":
        return <Badge variant="default" className="text-xs bg-green-100 text-green-800">Completed</Badge>;
      case "processing":
        return <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800">Processing</Badge>;
      case "failed":
        return <Badge variant="destructive" className="text-xs">Failed</Badge>;
      default:
        return <Badge variant="outline" className="text-xs">Not Processed</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <File className="h-5 w-5" />
            Deal Files
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <File className="h-5 w-5" />
            Deal Files
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-red-600">
            <p>{error}</p>
            <Button onClick={fetchDealFiles} className="mt-2">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const processableFiles = files.filter(file => !isLLMOutputFile(file));

  return (
    <div className="space-y-6">
      {/* File Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Add Files
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!showUploadForm ? (
            <Button 
              onClick={() => setShowUploadForm(true)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add New Files
            </Button>
          ) : (
            <div className="space-y-4">
              <FileUpload
                onFilesSelected={setNewFiles}
                value={newFiles}
                acceptedFileTypes={[".pdf", ".csv", ".xlsx", ".xls", ".doc", ".docx"]}
                maxFiles={10}
                placeholder="Drop files here or click to browse"
                description="Upload files to process with the deal extraction prompt"
              />
              <div className="flex gap-2">
                <Button 
                  onClick={handleFileUpload}
                  disabled={newFiles.length === 0 || uploading}
                  className="flex items-center gap-2"
                >
                  {uploading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4" />
                      Upload Files
                    </>
                  )}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setShowUploadForm(false);
                    setNewFiles([]);
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* File Processing Section */}
      {processableFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              Process Files
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={selectedFiles.length === processableFiles.length && processableFiles.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                  <span className="text-sm font-medium">
                    Select All ({processableFiles.length} files)
                  </span>
                </div>
                <Button
                  onClick={handleProcessFiles}
                  disabled={selectedFiles.length === 0 || processing}
                  className="flex items-center gap-2"
                >
                  {processing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4" />
                      Process Selected ({selectedFiles.length})
                    </>
                  )}
                </Button>
              </div>
              
              {/* Selected Files Display */}
              {selectedFiles.length > 0 && (
                <div className="border rounded-lg p-4 bg-gray-50">
                  <div className="flex items-center gap-2 mb-3">
                    <File className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-gray-700">
                      Files to Process ({selectedFiles.length})
                    </span>
                  </div>
                  <div className="space-y-2">
                    {files
                      .filter(file => selectedFiles.includes(file.file_id))
                      .map((file, index) => (
                        <div key={`${file.file_id}-selected-${index}`} className="flex items-center gap-2 text-sm">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span className="text-gray-600">{file.original_name}</span>
                          <span className="text-gray-400">({formatFileSize(file.file_size_bytes)})</span>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Files List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <File className="h-5 w-5" />
              Deal Files ({files.length})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchDealFiles}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {files.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <File className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No files associated with this deal</p>
            </div>
          ) : (
            <div className="space-y-4">
              {files
                .sort((a, b) => {
                  // Sort by primary first, then by display order, then by upload date
                  if (a.is_primary && !b.is_primary) return -1;
                  if (!a.is_primary && b.is_primary) return 1;
                  if (a.display_order !== b.display_order) {
                    return (a.display_order || 0) - (b.display_order || 0);
                  }
                  return (
                    new Date(b.uploaded_at).getTime() -
                    new Date(a.uploaded_at).getTime()
                  );
                })
                .map((file, index) => (
                  <div
                    key={`${file.file_id}-${index}`}
                    className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        {!isLLMOutputFile(file) && (
                          <Checkbox
                            checked={selectedFiles.includes(file.file_id)}
                            onCheckedChange={() => handleFileSelect(file.file_id)}
                          />
                        )}
                        <div className="text-2xl">
                          {isLLMOutputFile(file) ? "🤖" : getFileIcon(file.mime_type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-gray-900 truncate">
                              {file.title || file.original_name}
                            </h4>
                            {file.is_primary && (
                              <Badge variant="default" className="text-xs">
                                Primary
                              </Badge>
                            )}
                            {isLLMOutputFile(file) && (
                              <Badge variant="secondary" className="text-xs">
                                LLM Output
                              </Badge>
                            )}
                            {file.relationship_type && (
                              <Badge variant="secondary" className="text-xs">
                                {file.relationship_type}
                              </Badge>
                            )}
                            {getProcessingStatusBadge(file)}
                          </div>
                          <p className="text-sm text-gray-600 mb-2">
                            {file.original_name}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span className="flex items-center gap-1">
                              <Hash className="h-3 w-3" />
                              {formatFileSize(file.file_size_bytes)}
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {formatDate(file.uploaded_at)}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {isLLMOutputFile(file) && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => viewLLMOutput(file.file_id, file.original_name)}
                            className="flex items-center gap-1"
                          >
                            <Eye className="h-3 w-3" />
                            View
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            downloadFile(file.file_id, file.original_name)
                          }
                          className="flex items-center gap-1"
                        >
                          <Download className="h-3 w-3" />
                          Download
                        </Button>
                        {!isLLMOutputFile(file) && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteFile(file)}
                            className="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-3 w-3" />
                            Remove
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          )}
        </CardContent>
      </Card>


    </div>
  );
}
