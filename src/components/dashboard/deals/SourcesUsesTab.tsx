import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Square, Plus, Trash2 } from "lucide-react";
import { DealNsfFieldV2 } from "./shared/types-v2";
import { ConflictIndicator } from "./ConflictIndicator";
import { toast } from "sonner";

interface SourcesUsesTabProps {
  nsfFields: DealNsfFieldV2[];
  isEditing: boolean;
  isNsfSyncing: boolean;
  onNsfFieldChange: (id: string, field: string, value: any) => void;
  hasConflict: (fieldName: string) => boolean;
  getConflictData: (fieldName: string) => any;
  hasNsfConflict: (context: string, type: string, field: string) => boolean;
  getNsfConflictData: (context: string, type: string, field: string) => any;
  formatNumber: (value: number | null, unit?: string) => string;
  formatRawCurrency: (value: number | null) => string;
  formatPercentage: (value: number | null) => string;
  getTotalNsf: (fields: DealNsfFieldV2[]) => number;
  calculateAmountByContext: (fields: DealNsfFieldV2[], context: string) => number;
  dealId?: number;
  property?: {
    gsfGrossSquareFoot?: number;
    zfaZoningFloorArea?: number;
    totalNsfNetSquareFoot?: number;
  };
  centralMappings?: any;
  isLoadingMappings?: boolean;
  onPropertyFieldChange?: (field: string, value: any) => void;
  onRefreshData?: () => Promise<void>; // New prop for refreshing data
}

const SourcesUsesTab: React.FC<SourcesUsesTabProps> = ({
  nsfFields,
  isEditing,
  isNsfSyncing,
  onNsfFieldChange,
  hasConflict,
  getConflictData,
  hasNsfConflict,
  getNsfConflictData,
  formatNumber,
  formatRawCurrency,
  formatPercentage,
  getTotalNsf,
  calculateAmountByContext,
  dealId,
  property,
  centralMappings,
  isLoadingMappings,
  onPropertyFieldChange,
  onRefreshData,
}) => {
  const [isAddingSource, setIsAddingSource] = useState(false);
  const [isAddingUse, setIsAddingUse] = useState(false);
  const [isDeleting, setIsDeleting] = useState<number | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [percentageInputs, setPercentageInputs] = useState<{ [key: string]: string }>({});
  const [newSourceData, setNewSourceData] = useState<{
    sourceType: string;
    amount: number;
    percentage: string;
  }>({
    sourceType: '',
    amount: 0,
    percentage: ''
  });
  
  const [newUseData, setNewUseData] = useState<{
    useType: string;
    amount: number;
    percentage: string;
  }>({
    useType: '',
    amount: 0,
    percentage: ''
  });



  // Separate sources and uses
  const sources = nsfFields.filter(field => field.sourceType || field.nsfContext === 'sources');
  const uses = nsfFields.filter(field => field.useType || field.nsfContext === 'uses_total');

    // Add new source
  const handleAddSource = () => {
    setNewSourceData({
      sourceType: '',
      amount: 0,
      percentage: ''
    });
    setIsAddingSource(true);
  };

  // Save new source
  const handleSaveNewSource = async (sourceData: {
    sourceType: string;
    amount: number;
    percentage: string;
  }) => {
    if (!dealId) return;
    
    try {
      const newSource: Partial<DealNsfFieldV2> = {
        id: -1, // Temporary ID for new items
        dealId,
        nsfContext: 'sources',
        dealType: 'sources', // Automatically set deal type based on context
        sourceType: sourceData.sourceType,
        amount: sourceData.amount || 0,
        amountPerGsf: 0,
        amountPerNsf: 0,
        amountPerZfa: 0,
        percentageOfTotal: sourceData.percentage ? parseFloat(sourceData.percentage) / 100 : 0,
        isRequired: false,
        additionalInfo: {}
      };

      const response = await fetch(`/api/v2/deals/${dealId}/nsf-fields`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newSource)
      });

      if (response.ok) {
        toast.success('Source added successfully');
        // Reset form and refresh data
        setIsAddingSource(false);
        setNewSourceData({ sourceType: '', amount: 0, percentage: '' });
        if (onRefreshData) {
          setIsRefreshing(true);
          try {
            await onRefreshData();
          } finally {
            setIsRefreshing(false);
          }
        }
      } else {
        const errorData = await response.json();
        console.error('Failed to add source:', errorData.error);
        toast.error(errorData.error || 'Failed to add source');
      }
    } catch (error) {
      console.error('Error adding source:', error);
    }
  };

  // Cancel adding source
  const handleCancelAddSource = () => {
    setIsAddingSource(false);
  };

  // Save new use
  const handleSaveNewUse = async (useData: {
    useType: string;
    amount: number;
    percentage: string;
  }) => {
    if (!dealId) return;
    
    try {
      const newUse: Partial<DealNsfFieldV2> = {
        id: -1, // Temporary ID for new items
        dealId,
        nsfContext: 'uses_total',
        dealType: 'uses', // Automatically set deal type based on context
        useType: useData.useType,
        amount: useData.amount || 0,
        amountPerGsf: 0,
        amountPerNsf: 0,
        amountPerZfa: 0,
        percentageOfTotal: useData.percentage ? parseFloat(useData.percentage) / 100 : 0,
        isRequired: false,
        additionalInfo: {}
      };

      const response = await fetch(`/api/v2/deals/${dealId}/nsf-fields`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newUse)
      });

      if (response.ok) {
        toast.success('Use added successfully');
        // Reset form and refresh data
        setIsAddingUse(false);
        setNewUseData({ useType: '', amount: 0, percentage: '' });
        if (onRefreshData) {
          setIsRefreshing(true);
          try {
            await onRefreshData();
          } finally {
            setIsRefreshing(false);
          }
        }
      } else {
        const errorData = await response.json();
        console.error('Failed to add use:', errorData.error);
        toast.error(errorData.error || 'Failed to add use');
      }
    } catch (error) {
      console.error('Error adding use:', error);
    } finally {
      setIsAddingUse(false);
    }
  };

  // Cancel adding use
  const handleCancelAddUse = () => {
    setIsAddingUse(false);
  };

  // Add new use
  const handleAddUse = () => {
    setNewUseData({
      useType: '',
      amount: 0,
      percentage: ''
    });
    setIsAddingUse(true);
  };

  // Delete NSF field
  const handleDelete = async (nsfId: number) => {
    if (!dealId) return;
    
    setIsDeleting(nsfId);
    try {
      const response = await fetch(`/api/v2/deals/${dealId}/nsf-fields?nsfFieldId=${nsfId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success('NSF field and related investment criteria deleted successfully');
        // Refresh data without page reload
        if (onRefreshData) {
          setIsRefreshing(true);
          try {
            await onRefreshData();
          } finally {
            setIsRefreshing(false);
          }
        }
      } else {
        console.error('Failed to delete NSF field');
        toast.error('Failed to delete NSF field');
      }
    } catch (error) {
      console.error('Error deleting NSF field:', error);
    } finally {
      setIsDeleting(null);
    }
  };

  // Handle percentage input changes
  const handlePercentageChange = (nsfId: string, value: string) => {
    setPercentageInputs(prev => ({ ...prev, [nsfId]: value }));
  };

  // Handle percentage input blur (save to actual field)
  const handlePercentageBlur = (nsfId: string) => {
    const inputValue = percentageInputs[nsfId];
    if (inputValue !== undefined) {
      const numericValue = parseFloat(inputValue) / 100; // Convert to decimal
      onNsfFieldChange(nsfId, 'percentageOfTotal', numericValue);
      setPercentageInputs(prev => {
        const newState = { ...prev };
        delete newState[nsfId];
        return newState;
      });
    }
  };

  // Initialize percentage inputs when component mounts or nsfFields change
  useEffect(() => {
    const initialInputs: { [key: string]: string } = {};
    nsfFields.forEach(nsf => {
      if (nsf.percentageOfTotal !== null && nsf.percentageOfTotal !== undefined) {
        initialInputs[nsf.id.toString()] = (nsf.percentageOfTotal * 100).toFixed(2);
      }
    });
    setPercentageInputs(initialInputs);
  }, [nsfFields]);

  const renderNsfField = (nsf: DealNsfFieldV2, index: number) => (
    <div key={nsf.id} className="border rounded-lg p-4 bg-gray-50">
      <div className="flex items-center justify-between mb-4">
        <div className="flex-1">
          {isEditing ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {/* Show Source Type for sources, Use Type for uses */}
              {nsf.nsfContext === 'sources' ? (
                <div>
                  <Label className="text-sm font-medium text-gray-600">Source Type</Label>
                  {isEditing ? (
                    <Select
                      value={nsf.sourceType || ""}
                      onValueChange={(value) => onNsfFieldChange(nsf.id.toString(), 'sourceType', value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select source type" />
                      </SelectTrigger>
                      <SelectContent>
                        {isLoadingMappings ? (
                          <SelectItem value="" disabled>Loading...</SelectItem>
                        ) : (
                          centralMappings?.capitalPositions?.map((position: any) => (
                            <SelectItem key={position.value} value={position.value}>
                              {position.label}
                            </SelectItem>
                          )) || []
                        )}
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{nsf.sourceType || "N/A"}</p>
                  )}
                </div>
              ) : (
                <div>
                  <Label className="text-sm font-medium text-gray-600">Use Type</Label>
                  {isEditing ? (
                    <Input
                      type="text"
                      value={nsf.useType || ""}
                      onChange={(e) => onNsfFieldChange(nsf.id.toString(), 'useType', e.target.value)}
                      className="mt-1"
                      placeholder="e.g., Hard Cost, Soft Cost"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{nsf.useType || "N/A"}</p>
                  )}
                </div>
              )}

              <div className="flex items-center space-x-2">
                <div className="text-sm text-gray-500 italic">
                  Required status is controlled by Ask Capital Position in Overview tab
                </div>
              </div>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between">
                                  <div>
                    <h4 className="font-medium text-gray-900 text-lg capitalize">
                      {nsf.nsfContext === 'sources' ? (nsf.sourceType || 'Source Entry') : (nsf.useType || 'Use Entry')}
                    </h4>
                    {nsf.nsfContext === 'sources' && nsf.sourceType && (
                      <p className="text-sm text-gray-600">Capital Position: {nsf.sourceType}</p>
                    )}
                    {nsf.nsfContext === 'uses_total' && nsf.useType && (
                      <p className="text-sm text-gray-600">Use Type: {nsf.useType}</p>
                    )}
                  </div>
                <div className="flex items-center space-x-2">
                  {nsf.isRequired && (
                    <Badge variant="destructive" className="text-xs">
                      Required
                    </Badge>
                  )}
                  {(nsf.nsfContext === 'sources' && nsf.sourceType) || (nsf.nsfContext === 'uses_total' && nsf.useType) ? (
                    <Badge variant="outline" className="text-xs capitalize">
                      {nsf.nsfContext === 'sources' ? 'Capital Position' : 'Use Type'}
                    </Badge>
                  ) : null}
                </div>
              </div>
            </>
          )}
        </div>
        
        {/* Delete Button */}
        {isEditing && (
          <Button
            variant="destructive"
            size="sm"
            onClick={() => handleDelete(nsf.id)}
            disabled={isDeleting === nsf.id}
            className="ml-4"
          >
            {isDeleting === nsf.id ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
          </Button>
        )}
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        
        
        <div>
          <Label className="text-sm font-medium text-gray-600">Amount</Label>
          {isEditing ? (
            <Input
              type="number"
              value={nsf.amount || ""}
              onChange={(e) => onNsfFieldChange(nsf.id.toString(), 'amount', parseFloat(e.target.value) || 0)}
              className="mt-1"
              placeholder="0"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amount)}</p>
          )}
          {/* NSF Field Amount Conflicts */}
          {(() => {
            const context = nsf.sourceType ? 'sources' : 'uses';
            const type = nsf.sourceType || nsf.useType;
            if (context && type && hasNsfConflict(context, type, 'amount')) {
              const conflictData = getNsfConflictData(context, type, 'amount');
              return conflictData ? (
                <ConflictIndicator
                  fieldName={`${type} Amount`}
                  conflictData={conflictData}
                  currentValue={nsf.amount}
                  onValueChange={(newValue) => onNsfFieldChange(nsf.id.toString(), 'amount', newValue)}
                  className="mt-2"
                />
              ) : null;
            }
            return null;
          })()}
        </div>
        
        {/* Second Row - Per GSF, Per ZFA, Per NSF */}
        <div>
          <Label className="text-sm font-medium text-gray-600">Per GSF</Label>
          {isEditing ? (
            <Input
              type="number"
              value={nsf.amountPerGsf || ""}
              onChange={(e) => onNsfFieldChange(nsf.id.toString(), 'amountPerGsf', parseFloat(e.target.value) || 0)}
              className="mt-1"
              placeholder="0"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amountPerGsf)}</p>
          )}
        </div>
        
        <div>
          <Label className="text-sm font-medium text-gray-600">Per ZFA</Label>
          {isEditing ? (
            <Input
              type="number"
              value={nsf.amountPerZfa || ""}
              onChange={(e) => onNsfFieldChange(nsf.id.toString(), 'amountPerZfa', parseFloat(e.target.value) || 0)}
              className="mt-1"
              placeholder="0"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amountPerZfa)}</p>
          )}
        </div>
        
        <div>
          <Label className="text-sm font-medium text-gray-600">Per NSF</Label>
          {isEditing ? (
            <Input
              type="number"
              value={nsf.amountPerNsf || ""}
              onChange={(e) => onNsfFieldChange(nsf.id.toString(), 'amountPerNsf', parseFloat(e.target.value) || 0)}
              className="mt-1"
              placeholder="0"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amountPerNsf)}</p>
          )}
        </div>
        
        {/* Third Row - Percentage and Measurements */}
        <div>
          <Label className="text-sm font-medium text-gray-600">Percent</Label>
          {isEditing ? (
            <Input
              type="number"
              step="0.01"
              min="0"
              max="100"
              value={percentageInputs[nsf.id] ?? (nsf.percentageOfTotal ? (nsf.percentageOfTotal * 100).toFixed(2) : "")}
              onChange={(e) => {
                setPercentageInputs(prev => ({
                  ...prev,
                  [nsf.id]: e.target.value
                }));
              }}
              onBlur={(e) => {
                const value = parseFloat(e.target.value) || 0;
                // Convert percentage (0-100) back to decimal (0-1) for backend
                onNsfFieldChange(nsf.id.toString(), 'percentageOfTotal', value / 100);
                // Clear the local input state after saving
                setPercentageInputs(prev => {
                  const newState = { ...prev };
                  delete newState[nsf.id];
                  return newState;
                });
              }}
              className="mt-1"
              placeholder="0.00"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">
              {nsf.percentageOfTotal ? `${(nsf.percentageOfTotal * 100).toFixed(2)}%` : "0.00%"}
            </p>
          )}
        </div>
        
        <div>
          <Label className="text-sm font-medium text-gray-600">GSF (Gross Square Foot)</Label>
          {isEditing ? (
            <Input
              type="number"
              value={property?.gsfGrossSquareFoot || ""}
              onChange={(e) => {
                const value = parseFloat(e.target.value) || 0;
                // Update property via parent component
                if (onPropertyFieldChange) {
                  onPropertyFieldChange('gsfGrossSquareFoot', value);
                }
              }}
              className="mt-1"
              placeholder="0"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">
              {formatNumber(property?.gsfGrossSquareFoot || null)} 
              <span className="text-xs text-gray-500 ml-2">(from property)</span>
            </p>
          )}
        </div>
        
        <div>
          <Label className="text-sm font-medium text-gray-600">ZFA (Zoning Floor Area)</Label>
          {isEditing ? (
            <Input
              type="number"
              value={property?.zfaZoningFloorArea || ""}
              onChange={(e) => {
                const value = parseFloat(e.target.value) || 0;
                // Update property via parent component
                if (onPropertyFieldChange) {
                  onPropertyFieldChange('zfaZoningFloorArea', value);
                }
              }}
              className="mt-1"
              placeholder="0"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">
              {formatNumber(property?.zfaZoningFloorArea || null)} 
              <span className="text-xs text-gray-500 ml-2">(from property)</span>
            </p>
          )}
        </div>
        
        <div>
          <Label className="text-sm font-medium text-gray-600">Total NSF</Label>
          {isEditing ? (
            <Input
              type="number"
              value={property?.totalNsfNetSquareFoot || ""}
              onChange={(e) => {
                const value = parseFloat(e.target.value) || 0;
                // Update property via parent component
                if (onPropertyFieldChange) {
                  onPropertyFieldChange('totalNsfNetSquareFoot', value);
                }
              }}
              className="mt-1"
              placeholder="0"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">
              {formatNumber(property?.totalNsfNetSquareFoot || null)} 
              <span className="text-xs text-gray-500 ml-2">(from property)</span>
            </p>
          )}
        </div>
      </div>
      
      {nsf.additionalInfo && Object.keys(nsf.additionalInfo).length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <Label className="text-sm font-medium text-gray-600">Additional Info</Label>
          {isEditing ? (
            <Textarea
              value={JSON.stringify(nsf.additionalInfo, null, 2)}
              onChange={(e) => {
                try {
                  const parsed = JSON.parse(e.target.value);
                  onNsfFieldChange(nsf.id.toString(), 'additionalInfo', parsed);
                } catch (error) {
                  // Handle invalid JSON
                }
              }}
              className="mt-1"
              rows={3}
              placeholder="Enter JSON data..."
            />
          ) : (
            <div className="space-y-2">
              {/* Display conflict resolution info */}
              {nsf.additionalInfo.conflict_resolution && (
                <div className="bg-blue-50 border border-blue-200 rounded p-3">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">Conflict Resolution Data</h4>
                  {Object.entries(nsf.additionalInfo.conflict_resolution).map(([fieldName, conflictData]: [string, any]) => (
                    <div key={fieldName} className="mb-3 p-2 bg-white rounded border">
                      <div className="text-xs font-medium text-blue-700 mb-1">{fieldName}</div>
                      <div className="text-xs space-y-1">
                        <div><span className="font-medium">Source:</span> {conflictData.source_file}</div>
                        <div><span className="font-medium">Confidence:</span> {conflictData.confidence}</div>
                        {conflictData.alternatives && conflictData.alternatives.length > 0 && (
                          <div>
                            <span className="font-medium">Alternatives:</span>
                            <ul className="ml-2 mt-1">
                              {conflictData.alternatives.map((alt: any, idx: number) => (
                                <li key={idx} className="text-xs">
                                  {alt.value} ({alt.file}) - {alt.context}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        {conflictData.resolution_notes && (
                          <div><span className="font-medium">Notes:</span> {conflictData.resolution_notes}</div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {/* Display other additional info */}
              {Object.entries(nsf.additionalInfo).filter(([key]) => key !== 'conflict_resolution').length > 0 && (
                <div className="text-xs text-gray-600 bg-gray-100 p-2 rounded">
                  <pre>{JSON.stringify(
                    Object.fromEntries(
                      Object.entries(nsf.additionalInfo).filter(([key]) => key !== 'conflict_resolution')
                    ), 
                    null, 
                    2
                  )}</pre>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Square className="h-5 w-5" />
          Sources/Uses ({nsfFields.length})
          {isNsfSyncing && (
            <div className="flex items-center gap-1 text-xs text-blue-600">
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
              Syncing...
            </div>
          )}
          {isRefreshing && (
            <div className="flex items-center gap-1 text-xs text-green-600">
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-green-600"></div>
              Refreshing...
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* NSF Summary Section */}
        {nsfFields && nsfFields.length > 0 && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-3">NSF Data Summary</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-blue-700 font-medium">Total NSF:</span>
                <div className="text-lg font-semibold text-blue-900">
                  {formatNumber(getTotalNsf(nsfFields), 'sq ft')}
                </div>
              </div>
              <div>
                <span className="text-blue-700 font-medium">Sources Amount:</span>
                <div className="text-lg font-semibold text-blue-900">
                  {formatRawCurrency(calculateAmountByContext(nsfFields, 'sources'))}
                </div>
              </div>
              <div>
                <span className="text-blue-700 font-medium">Uses Amount:</span>
                <div className="text-lg font-semibold text-blue-900">
                  {formatRawCurrency(calculateAmountByContext(nsfFields, 'uses_total'))}
                </div>
              </div>
              <div>
                <span className="text-blue-700 font-medium">Fields Count:</span>
                <div className="text-lg font-semibold text-blue-900">
                  {nsfFields.length}
                </div>
              </div>
            </div>
          </div>
        )}

        {nsfFields && nsfFields.length > 0 ? (
          <div className="space-y-6">
            {/* Sources Section */}
            <div>
              <div className="flex items-center justify-between mb-4 border-b border-gray-200 pb-2">
                <h3 className="text-lg font-semibold text-gray-900">
                  Sources
                </h3>
                {isEditing && (
                  <Button
                    onClick={handleAddSource}
                    disabled={isAddingSource}
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    {isAddingSource ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <Plus className="h-4 w-4" />
                    )}
                    Add Source
                  </Button>
                )}
              </div>
              {/* Empty card for adding new source */}
              {isAddingSource && (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 bg-gray-50 mb-4">
                  <div className="text-center mb-4">
                    <h4 className="text-lg font-medium text-gray-900">Add New Source</h4>
                    <p className="text-sm text-gray-500">Fill in the details below to create a new source</p>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Source Type</Label>
                      <Select
                        value={newSourceData.sourceType || ""}
                        onValueChange={(value) => setNewSourceData(prev => ({ ...prev, sourceType: value }))}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select source type" />
                        </SelectTrigger>
                        <SelectContent>
                          {isLoadingMappings ? (
                            <SelectItem value="" disabled>Loading...</SelectItem>
                          ) : (
                            centralMappings?.capitalPositions?.map((position: any) => (
                              <SelectItem key={position.value} value={position.value}>
                                {position.label}
                              </SelectItem>
                            )) || []
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Amount</Label>
                      <Input
                        type="number"
                        value={newSourceData.amount || ""}
                        onChange={(e) => setNewSourceData(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                        className="mt-1"
                        placeholder="0"
                      />
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Percentage</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        max="100"
                        value={newSourceData.percentage || ""}
                        onChange={(e) => setNewSourceData(prev => ({ ...prev, percentage: e.target.value }))}
                        className="mt-1"
                        placeholder="0.00"
                      />
                    </div>
                    
                    <div className="flex items-end space-x-2">
                      <Button
                        onClick={() => handleSaveNewSource(newSourceData)}
                        disabled={!newSourceData.sourceType || !newSourceData.amount}
                        size="sm"
                        className="flex-1"
                      >
                        Save
                      </Button>
                      <Button
                        variant="outline"
                        onClick={handleCancelAddSource}
                        size="sm"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </div>
              )}
              
              {sources.length > 0 ? (
                <div className="space-y-4">
                  {sources.map((nsf, index) => renderNsfField(nsf, index))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No sources defined yet. Click "Add Source" to create one.
                </div>
              )}
            </div>

            {/* Uses Section */}
            <div>
              <div className="flex items-center justify-between mb-4 border-b border-gray-200 pb-2">
                <h3 className="text-lg font-semibold text-gray-900">
                  Uses
                </h3>
                {isEditing && (
                  <Button
                    onClick={handleAddUse}
                    disabled={isAddingUse}
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    {isAddingUse ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <Plus className="h-4 w-4" />
                    )}
                    Add Use
                  </Button>
                )}
              </div>
              {/* Empty card for adding new use */}
              {isAddingUse && (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 bg-gray-50 mb-4">
                  <div className="text-center mb-4">
                    <h4 className="text-lg font-medium text-gray-900">Add New Use</h4>
                    <p className="text-sm text-gray-500">Fill in the details below to create a new use</p>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Use Type</Label>
                      <Input
                        type="text"
                        value={newUseData.useType || ""}
                        onChange={(e) => setNewUseData(prev => ({ ...prev, useType: e.target.value }))}
                        className="mt-1"
                        placeholder="e.g., Hard Cost, Soft Cost"
                      />
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Amount</Label>
                      <Input
                        type="number"
                        value={newUseData.amount || ""}
                        onChange={(e) => setNewUseData(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                        className="mt-1"
                        placeholder="0"
                      />
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Percentage</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        max="100"
                        value={newUseData.percentage || ""}
                        onChange={(e) => setNewUseData(prev => ({ ...prev, percentage: e.target.value }))}
                        className="mt-1"
                        placeholder="0.00"
                      />
                    </div>
                    
                    <div className="flex items-end space-x-2">
                      <Button
                        onClick={() => handleSaveNewUse(newUseData)}
                        disabled={!newUseData.useType || !newUseData.amount}
                        size="sm"
                        className="flex-1"
                      >
                        Save
                      </Button>
                      <Button
                        variant="outline"
                        onClick={handleCancelAddUse}
                        size="sm"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </div>
              )}
              
              {uses.length > 0 ? (
                <div className="space-y-4">
                  {uses.map((nsf, index) => renderNsfField(nsf, index))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No uses defined yet. Click "Add Use" to create one.
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <Square className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No NSF fields found for this deal.</p>
            <p className="text-sm text-gray-400 mt-1">
              NSF fields will appear here after processing deal documents.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SourcesUsesTab;
