"use client";

import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import {
  AlertTriangle,
  CheckCircle,
  Database,
  ChevronDown,
  ChevronUp,
  Info,
  Target,
  TrendingUp,
  Building,
  DollarSign,
  Users,
  FileText,
  Calendar,
  MapPin,
} from "lucide-react";

interface DataQualityMetrics {
  overview: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
  debt: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
    criteriaCount: number;
    individualCriteria?: Array<{
      criteria_id: number;
      capital_position: string[];
      qualityScore: number;
      completedFields: number;
      totalFields: number;
      missingFields: string[];
    }>;
  };
  equity: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
    criteriaCount: number;
    individualCriteria?: Array<{
      criteria_id: number;
      capital_position: string[];
      qualityScore: number;
      completedFields: number;
      totalFields: number;
      missingFields: string[];
    }>;
  };
  nsf: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
    sourcesCount: number;
    usesCount: number;
  };
  property: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
  financial: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
  units: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
  campaign: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
}

interface DataQualityBannerProps {
  dataQualityMetrics: DataQualityMetrics;
  dealName: string;
  nsfQuality?: any; // Add NSF quality data
}

const DataQualityBanner: React.FC<DataQualityBannerProps> = ({
  dataQualityMetrics,
  dealName,
  nsfQuality,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const { overview, debt, equity, nsf, property, financial, units, campaign } = dataQualityMetrics;
  
  // Calculate overall quality score across all sections
  const allSections = [overview, debt, equity, nsf, property, financial, units, campaign];
  const overallScore = Math.round(
    allSections.reduce((sum, section) => sum + section.qualityScore, 0) / allSections.length
  );
  
  // Get quality color and icon
  const getQualityColor = (score: number) => {
    if (score >= 90) return "text-emerald-600 bg-emerald-50 border-emerald-200";
    if (score >= 80) return "text-blue-600 bg-blue-50 border-blue-200";
    if (score >= 70) return "text-amber-600 bg-amber-50 border-amber-200";
    return "text-red-600 bg-red-50 border-red-200";
  };

  const getQualityIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="h-4 w-4" />;
    if (score >= 80) return <Target className="h-4 w-4" />;
    if (score >= 70) return <TrendingUp className="h-4 w-4" />;
    return <AlertTriangle className="h-4 w-4" />;
  };

  const getQualityMessage = (score: number) => {
    if (score >= 90) return "Excellent data quality";
    if (score >= 80) return "Good data quality";
    if (score >= 70) return "Fair data quality";
    return "Poor data quality - needs attention";
  };

  // Format field names for display
  const formatFieldName = (fieldName: string): string => {
    return fieldName
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Check if there are any missing fields across all sections
  const hasMissingFields = allSections.some(section => section.missingFields.length > 0);

  // Always show the banner to provide data quality feedback

  return (
    <Card className={`mb-6 border-l-4 ${getQualityColor(overallScore).split(' ')[1]} border-l-current`}>
      <CardContent className="p-4">
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              {getQualityIcon(overallScore)}
              <div>
                <h3 className="font-medium text-gray-900">
                  Overall Data Quality: {overallScore}%
                </h3>
                <p className="text-sm text-gray-600">
                  {getQualityMessage(overallScore)}
                </p>
                <p className="text-xs text-gray-400">
                  V2 Processor - Comprehensive Coverage
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="outline" className={getQualityColor(overallScore)}>
                {overallScore}% Complete
              </Badge>
              
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </CollapsibleTrigger>
            </div>
          </div>

          {/* Quality Breakdown Cards - All Sections */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
            <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-900 text-sm">Overview</span>
                </div>
                <Badge variant="outline" className="text-blue-700 bg-blue-100 border-blue-300 text-xs">
                  {overview.qualityScore}%
                </Badge>
              </div>
              <div className="text-xs text-blue-700">
                {overview.completedFields}/{overview.totalFields} fields
              </div>
            </div>

            <div className="bg-green-50 rounded-lg p-3 border border-green-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-green-900 text-sm">Debt</span>
                </div>
                <Badge variant="outline" className="text-green-700 bg-green-100 border-green-300 text-xs">
                  {debt.qualityScore}%
                </Badge>
              </div>
              <div className="text-xs text-green-700">
                {debt.completedFields}/{debt.totalFields} fields
                {debt.criteriaCount > 0 && (
                  <span className="ml-1">({debt.criteriaCount} criteria)</span>
                )}
              </div>
            </div>

            <div className="bg-purple-50 rounded-lg p-3 border border-purple-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-purple-600" />
                  <span className="font-medium text-purple-900 text-sm">Equity</span>
                </div>
                <Badge variant="outline" className="text-purple-700 bg-purple-100 border-purple-300 text-xs">
                  {equity.qualityScore}%
                </Badge>
              </div>
              <div className="text-xs text-purple-700">
                {equity.completedFields}/{equity.totalFields} fields
                {equity.criteriaCount > 0 && (
                  <span className="ml-1">({equity.criteriaCount} criteria)</span>
                )}
              </div>
            </div>

            <div className="bg-orange-50 rounded-lg p-3 border border-orange-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-orange-600" />
                  <span className="font-medium text-orange-900 text-sm">NSF</span>
                </div>
                <Badge variant="outline" className="text-orange-700 bg-orange-100 border-orange-300 text-xs">
                  {nsf.qualityScore}%
                </Badge>
              </div>
              <div className="text-xs text-orange-700">
                {nsf.completedFields}/{nsf.totalFields} fields
                <div className="text-xs">
                  {nsf.sourcesCount} sources, {nsf.usesCount} uses
                </div>
              </div>
            </div>

            <div className="bg-indigo-50 rounded-lg p-3 border border-indigo-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-indigo-600" />
                  <span className="font-medium text-indigo-900 text-sm">Property</span>
                </div>
                <Badge variant="outline" className="text-indigo-700 bg-indigo-100 border-indigo-300 text-xs">
                  {property.qualityScore}%
                </Badge>
              </div>
              <div className="text-xs text-indigo-700">
                {property.completedFields}/{property.totalFields} fields
              </div>
            </div>

            <div className="bg-teal-50 rounded-lg p-3 border border-teal-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-teal-600" />
                  <span className="font-medium text-teal-900 text-sm">Financial</span>
                </div>
                <Badge variant="outline" className="text-teal-700 bg-teal-100 border-teal-300 text-xs">
                  {financial.qualityScore}%
                </Badge>
              </div>
              <div className="text-xs text-teal-700">
                {financial.completedFields}/{financial.totalFields} fields
              </div>
            </div>

            <div className="bg-pink-50 rounded-lg p-3 border border-pink-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-pink-600" />
                  <span className="font-medium text-pink-900 text-sm">Units</span>
                </div>
                <Badge variant="outline" className="text-pink-700 bg-pink-100 border-pink-300 text-xs">
                  {units.qualityScore}%
                </Badge>
              </div>
              <div className="text-xs text-pink-700">
                {units.completedFields}/{units.totalFields} fields
              </div>
            </div>

            <div className="bg-cyan-50 rounded-lg p-3 border border-cyan-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-cyan-600" />
                  <span className="font-medium text-cyan-900 text-sm">Campaign</span>
                </div>
                <Badge variant="outline" className="text-cyan-700 bg-cyan-100 border-cyan-300 text-xs">
                  {campaign.qualityScore}%
                </Badge>
              </div>
              <div className="text-xs text-cyan-700">
                {campaign.completedFields}/{campaign.totalFields} fields
              </div>
            </div>
          </div>

          <CollapsibleContent className="mt-4 space-y-4">
            {/* NSF Quality Breakdown */}
            {nsfQuality && Object.keys(nsfQuality).length > 0 && (
              <div className="mb-4">
                <h4 className="font-medium text-sm text-gray-700 mb-3">NSF Fields Status</h4>
                <div className="space-y-3">
                  {Object.entries(nsfQuality).map(([type, data]: [string, any]) => (
                    <div key={type} className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Database className="h-4 w-4 text-gray-600" />
                          <span className="font-medium text-gray-900 capitalize">{type.replace('_', ' ')}</span>
                        </div>
                        <Badge variant="outline" className={`text-xs ${getQualityColor(data.qualityScore)}`}>
                          {data.qualityScore}%
                        </Badge>
                      </div>
                      
                      {/* Show NSF records for this type */}
                      {data.records && data.records.length > 0 && (
                        <div className="space-y-2">
                          {data.records.map((record: any, index: number) => (
                            <div key={index} className="bg-white rounded p-2 border border-gray-100">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium text-gray-700">
                                  {record.dealType} - {record.nsfContext}
                                </span>
                                <Badge variant="outline" className="text-xs">
                                  Record {index + 1}
                                </Badge>
                              </div>
                              
                              {/* Show field status */}
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                                {Object.entries(record.fields || {}).map(([fieldName, fieldData]: [string, any]) => (
                                  <div key={fieldName} className={`flex items-center gap-1 ${
                                    fieldData.hasValue ? 'text-green-700' : 'text-red-600'
                                  }`}>
                                    {fieldData.hasValue ? (
                                      <CheckCircle className="h-3 w-3" />
                                    ) : (
                                      <AlertTriangle className="h-3 w-3" />
                                    )}
                                    <span className="truncate">{formatFieldName(fieldName)}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                      
                      <div className="text-xs text-gray-600 mt-2">
                        {data.completedFields}/{data.totalFields} fields complete
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Detailed Field Breakdown for Each Section */}
            {overview.missingFields.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  Overview ({overview.qualityScore}% complete)
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {overview.missingFields.map((field) => (
                    <div key={field} className="flex items-center gap-2 text-sm">
                      <AlertTriangle className="h-3 w-3 text-amber-500" />
                      <span className="text-gray-700">{formatFieldName(field)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {debt.missingFields.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Debt ({debt.qualityScore}% complete)
                  {debt.criteriaCount > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {debt.criteriaCount} criteria
                    </Badge>
                  )}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-4">
                  {debt.missingFields.map((field) => (
                    <div key={field} className="flex items-center gap-2 text-sm">
                      <AlertTriangle className="h-3 w-3 text-amber-500" />
                      <span className="text-gray-700">{formatFieldName(field)}</span>
                    </div>
                  ))}
                </div>
                
                {/* Individual Debt Criteria Breakdown */}
                {debt.individualCriteria && debt.individualCriteria.length > 0 && (
                  <div className="mt-4 space-y-3">
                    <h5 className="font-medium text-gray-800 text-sm">Individual Criteria Breakdown:</h5>
                    {debt.individualCriteria.map((criteria, index) => (
                      <div key={criteria.criteria_id} className="bg-white rounded-lg p-3 border border-gray-200">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium text-gray-700">
                              Criteria #{index + 1}
                            </span>
                            <Badge variant="outline" className="text-xs">
                              {criteria.capital_position?.join(', ') || 'Unknown Position'}
                            </Badge>
                          </div>
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${
                              criteria.qualityScore >= 80 ? 'text-green-700 bg-green-50 border-green-200' :
                              criteria.qualityScore >= 60 ? 'text-yellow-700 bg-yellow-50 border-yellow-200' :
                              'text-red-700 bg-red-50 border-red-200'
                            }`}
                          >
                            {criteria.qualityScore}%
                          </Badge>
                        </div>
                        {criteria.missingFields.length > 0 && (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-1">
                            {criteria.missingFields.map((field) => (
                              <div key={field} className="flex items-center gap-1 text-xs">
                                <AlertTriangle className="h-2 w-2 text-amber-500" />
                                <span className="text-gray-600">{formatFieldName(field)}</span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {equity.missingFields.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Equity ({equity.qualityScore}% complete)
                  {equity.criteriaCount > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {equity.criteriaCount} criteria
                    </Badge>
                  )}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {equity.missingFields.map((field) => (
                    <div key={field} className="flex items-center gap-2 text-sm">
                      <AlertTriangle className="h-3 w-3 text-amber-500" />
                      <span className="text-gray-700">{formatFieldName(field)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {nsf.missingFields.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  NSF Fields ({nsf.qualityScore}% complete)
                  <Badge variant="secondary" className="text-xs">
                    {nsf.sourcesCount} sources, {nsf.usesCount} uses
                  </Badge>
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {nsf.missingFields.map((field) => (
                    <div key={field} className="flex items-center gap-2 text-sm">
                      <AlertTriangle className="h-3 w-3 text-amber-500" />
                      <span className="text-gray-700">{formatFieldName(field)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {property.missingFields.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Property ({property.qualityScore}% complete)
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {property.missingFields.map((field) => (
                    <div key={field} className="flex items-center gap-2 text-sm">
                      <AlertTriangle className="h-3 w-3 text-amber-500" />
                      <span className="text-gray-700">{formatFieldName(field)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {financial.missingFields.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Financial ({financial.qualityScore}% complete)
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {financial.missingFields.map((field) => (
                    <div key={field} className="flex items-center gap-2 text-sm">
                      <AlertTriangle className="h-3 w-3 text-amber-500" />
                      <span className="text-gray-700">{formatFieldName(field)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {units.missingFields.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Units ({units.qualityScore}% complete)
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {units.missingFields.map((field) => (
                    <div key={field} className="flex items-center gap-2 text-sm">
                      <AlertTriangle className="h-3 w-3 text-amber-500" />
                      <span className="text-gray-700">{formatFieldName(field)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {campaign.missingFields.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Campaign ({campaign.qualityScore}% complete)
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {campaign.missingFields.map((field) => (
                    <div key={field} className="flex items-center gap-2 text-sm">
                      <AlertTriangle className="h-3 w-3 text-amber-500" />
                      <span className="text-gray-700">{formatFieldName(field)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Quality Score Breakdown */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                <Info className="h-4 w-4" />
                Quality Breakdown
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {allSections.map((section, index) => {
                  const sectionNames = ['Overview', 'Debt', 'Equity', 'NSF', 'Property', 'Financial', 'Units', 'Campaign'];
                  const sectionColors = [
                    'bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500',
                    'bg-indigo-500', 'bg-teal-500', 'bg-pink-500', 'bg-cyan-500'
                  ];
                  
                  return (
                    <div key={index}>
                      <div className="flex justify-between text-sm mb-1">
                        <span>{sectionNames[index]}</span>
                        <span className="font-medium">{section.qualityScore}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${sectionColors[index]}`}
                          style={{ width: `${section.qualityScore}%` }}
                        />
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {section.completedFields} of {section.totalFields} fields
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
};

export default DataQualityBanner; 