"use client";

import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import {
  AlertTriangle,
  CheckCircle,
  Database,
  ChevronDown,
  ChevronUp,
  Info,
  Target,
  TrendingUp,
} from "lucide-react";

interface DataQualityMetricsV1 {
  overview: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
  debt: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
    criteriaCount: number;
    individualCriteria?: Array<{
      criteria_id: number;
      capital_position: string[];
      qualityScore: number;
      completedFields: number;
      totalFields: number;
      missingFields: string[];
    }>;
  };
  equity: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
    criteriaCount: number;
    individualCriteria?: Array<{
      criteria_id: number;
      capital_position: string[];
      qualityScore: number;
      completedFields: number;
      totalFields: number;
      missingFields: string[];
    }>;
  };
}

interface DataQualityBannerV1Props {
  dataQualityMetrics: DataQualityMetricsV1;
  dealName: string;
}

const DataQualityBannerV1: React.FC<DataQualityBannerV1Props> = ({
  dataQualityMetrics,
  dealName,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const { overview, debt, equity } = dataQualityMetrics;
  
  // Calculate overall quality score
  const overallScore = Math.round((Number(overview.qualityScore) + Number(debt.qualityScore) + Number(equity.qualityScore)) / 3);
  
  // Get quality color and icon
  const getQualityColor = (score: number) => {
    if (score >= 90) return "text-emerald-600 bg-emerald-50 border-emerald-200";
    if (score >= 80) return "text-blue-600 bg-blue-50 border-blue-200";
    if (score >= 70) return "text-amber-600 bg-amber-50 border-amber-200";
    return "text-red-600 bg-red-50 border-red-200";
  };

  const getQualityIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="h-4 w-4" />;
    if (score >= 80) return <Target className="h-4 w-4" />;
    if (score >= 70) return <TrendingUp className="h-4 w-4" />;
    return <AlertTriangle className="h-4 w-4" />;
  };

  const getQualityMessage = (score: number) => {
    if (score >= 90) return "Excellent data quality";
    if (score >= 80) return "Good data quality";
    if (score >= 70) return "Fair data quality";
    return "Poor data quality - needs attention";
  };

  // Format field names for display
  const formatFieldName = (fieldName: string): string => {
    return fieldName
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Check if there are any missing fields
  const hasMissingFields = overview.missingFields.length > 0 || debt.missingFields.length > 0 || equity.missingFields.length > 0;

  return (
    <Card className={`mb-6 border-l-4 ${getQualityColor(overallScore).split(' ')[1]} border-l-current`}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            {getQualityIcon(overallScore)}
            <div>
              <h3 className="font-medium text-gray-900">
                Overall Data Quality: {overallScore}%
              </h3>
              <p className="text-sm text-gray-600">
                {getQualityMessage(overallScore)}
              </p>
              <p className="text-xs text-gray-400">
                V1 Processor
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className={getQualityColor(overallScore)}>
              {overallScore}% Complete
            </Badge>
            
            <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-4 space-y-4">
                {/* Overview Fields Section */}
                {overview.missingFields.length > 0 && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      Overview ({overview.qualityScore}% complete)
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {overview.missingFields.map((field) => (
                        <div key={field} className="flex items-center gap-2 text-sm">
                          <AlertTriangle className="h-3 w-3 text-amber-500" />
                          <span className="text-gray-700">{formatFieldName(field)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Debt Fields Section */}
                {debt.missingFields.length > 0 && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      Debt ({debt.qualityScore}% complete)
                      {debt.criteriaCount > 0 && (
                        <Badge variant="secondary" className="text-xs">
                          {debt.criteriaCount} criteria
                        </Badge>
                      )}
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-4">
                      {debt.missingFields.map((field) => (
                        <div key={field} className="flex items-center gap-2 text-sm">
                          <AlertTriangle className="h-3 w-3 text-amber-500" />
                          <span className="text-gray-700">{formatFieldName(field)}</span>
                        </div>
                      ))}
                    </div>
                    
                    {/* Individual Debt Criteria Breakdown */}
                    {debt.individualCriteria && debt.individualCriteria.length > 0 && (
                      <div className="mt-4 space-y-3">
                        <h5 className="font-medium text-gray-800 text-sm">Individual Criteria Breakdown:</h5>
                        {debt.individualCriteria.map((criteria, index) => (
                          <div key={criteria.criteria_id} className="bg-white rounded-lg p-3 border border-gray-200">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <span className="text-sm font-medium text-gray-700">
                                  Criteria #{index + 1}
                                </span>
                                <Badge variant="outline" className="text-xs">
                                  {criteria.capital_position?.join(', ') || 'Unknown Position'}
                                </Badge>
                              </div>
                              <Badge 
                                variant="outline" 
                                className={`text-xs ${
                                  criteria.qualityScore >= 80 ? 'text-green-700 bg-green-50 border-green-200' :
                                  criteria.qualityScore >= 60 ? 'text-yellow-700 bg-yellow-50 border-yellow-200' :
                                  'text-red-700 bg-red-50 border-red-200'
                                }`}
                              >
                                {criteria.qualityScore}%
                              </Badge>
                            </div>
                            {criteria.missingFields.length > 0 && (
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-1">
                                {criteria.missingFields.map((field) => (
                                  <div key={field} className="flex items-center gap-1 text-xs">
                                    <AlertTriangle className="h-2 w-2 text-amber-500" />
                                    <span className="text-gray-600">{formatFieldName(field)}</span>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Equity Fields Section */}
                {equity.missingFields.length > 0 && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Equity ({equity.qualityScore}% complete)
                      {equity.criteriaCount > 0 && (
                        <Badge variant="secondary" className="text-xs">
                          {equity.criteriaCount} criteria
                        </Badge>
                      )}
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {equity.missingFields.map((field) => (
                        <div key={field} className="flex items-center gap-2 text-sm">
                          <AlertTriangle className="h-3 w-3 text-amber-500" />
                          <span className="text-gray-700">{formatFieldName(field)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Quality Score Breakdown */}
                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                    <Info className="h-4 w-4" />
                    Quality Breakdown
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Overview</span>
                        <span className="font-medium">{overview.qualityScore}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${overview.qualityScore >= 90 ? 'bg-emerald-500' : overview.qualityScore >= 80 ? 'bg-blue-500' : overview.qualityScore >= 70 ? 'bg-amber-500' : 'bg-red-500'}`}
                          style={{ width: `${overview.qualityScore}%` }}
                        />
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {overview.completedFields} of {overview.totalFields} fields completed
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Debt</span>
                        <span className="font-medium">{debt.qualityScore}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${debt.qualityScore >= 90 ? 'bg-emerald-500' : debt.qualityScore >= 80 ? 'bg-blue-500' : debt.qualityScore >= 70 ? 'bg-amber-500' : 'bg-red-500'}`}
                          style={{ width: `${debt.qualityScore}%` }}
                        />
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {debt.completedFields} of {debt.totalFields} fields completed
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Equity</span>
                        <span className="font-medium">{equity.qualityScore}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${equity.qualityScore >= 90 ? 'bg-emerald-500' : equity.qualityScore >= 80 ? 'bg-blue-500' : equity.qualityScore >= 70 ? 'bg-amber-500' : 'bg-red-500'}`}
                          style={{ width: `${equity.qualityScore}%` }}
                        />
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {equity.completedFields} of {equity.totalFields} fields completed
                      </div>
                    </div>
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        </div>

        {/* Quality Breakdown Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Database className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-900">Overview</span>
              </div>
              <Badge variant="outline" className="text-blue-700 bg-blue-100 border-blue-300">
                {overview.qualityScore}%
              </Badge>
            </div>
            <div className="text-sm text-blue-700">
              {overview.completedFields}/{overview.totalFields} fields completed
            </div>
          </div>

          <div className="bg-green-50 rounded-lg p-3 border border-green-200">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-green-600" />
                <span className="font-medium text-green-900">Debt</span>
              </div>
              <Badge variant="outline" className="text-green-700 bg-green-100 border-green-300">
                {debt.qualityScore}%
              </Badge>
            </div>
            <div className="text-sm text-green-700">
              {debt.completedFields}/{debt.totalFields} fields completed
            </div>
          </div>

          <div className="bg-purple-50 rounded-lg p-3 border border-purple-200">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-purple-600" />
                <span className="font-medium text-purple-900">Equity</span>
              </div>
              <Badge variant="outline" className="text-purple-700 bg-purple-100 border-purple-300">
                {equity.qualityScore}%
              </Badge>
            </div>
            <div className="text-sm text-purple-700">
              {equity.completedFields}/{equity.totalFields} fields completed
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DataQualityBannerV1; 