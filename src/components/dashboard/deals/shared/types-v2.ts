export interface DealV2 {
  dealId: number;
  dealName: string;
  summary: string;
  dealType: string;
  askCapitalPosition: string[];
  askAmount: number[];
  capitalRaiseTimeline: string;
  dateReceived: Date;
  dealStage: string;
  dateClosed: Date;
  dateUnderContract: Date;
  strategy: string;
  holdPeriod: number;
  dealStatus: string;
  
  // Financial metrics
  totalInternalRateOfReturnIrr: number;
  totalEquityMultiple: number;
  
  // Unit counts
  numAffordableHousing1bedroomUnits: number;
  numAffordableHousing2bedroomUnits: number;
  numAffordableHousing3bedroomUnits: number;
  numAffordableHousingStudiosUnits: number;
  numMarketRate1bedroomUnits: number;
  numMarketRate2bedroomUnits: number;
  numMarketRate3bedroomUnits: number;
  numMarketRateStudiosUnits: number;
  
  // Rental rates
  affordableHousingRent1bedroomUnit: number;
  affordableHousingRent2bedroomUnit: number;
  affordableHousingRent3bedroomUnit: number;
  affordableHousingRentStudioUnit: number;
  marketRateRent1bedroomUnit: number;
  marketRateRent2bedroomUnit: number;
  marketRateRent3bedroomUnit: number;
  
  // Sale prices
  affordableHousingSale1bedroomUnit: number;
  affordableHousingSale2bedroomUnit: number;
  affordableHousingSale3bedroomUnit: number;
  affordableHousingSaleStudioUnit: number;
  marketRateSale1bedroomUnit: number;
  marketRateSale2bedroomUnit: number;
  marketRateSale3bedroomUnit: number;
  marketRateSaleStudioUnit: number;
  
  // Community facility
  communityFacilityRent: number;
  communityFacilityRentAdditional: number;
  communityFacilitySalePrice: number;
  communityFacilitySalePriceAdditional: number;
  
  // Cost metrics
  costPerTotalNsfNetSquareFoot: number;
  costPerZoningFloorArea: number;
  
  // Financial metrics
  purchasePrice: number;        // Acquisition cost / Purchase price
  totalProjectCost: number;     // Total project cost (acquisition + hard costs + soft costs + financing)
  
  // Campaign data
  dealCampaignDate: Date;
  dealCampaignEmailsAdditionalInfoRequestedReceived: number;
  dealCampaignEmailsBounceBackReceived: number;
  dealCampaignEmailsOutOfOfficeResponseReceived: number;
  dealCampaignEmailsResponseReceived: number;
  dealCampaignEmailsSentOut: number;
  dealCampaignEmailsSoftQuotesReceived: number;
  dealCampaignEmailsTermSheetsReceived: number;
  dealCampaignStatus: string;
  dealCampaignLeadScore: number;
  dealCampaignPredictedScenario: string;
  
  // NSF measurements
  residentialNsfNetSquareFoot: number;
  retailNsfNetSquareFoot: number;
  occupancyRate: number;
  communityFacilityNsfNetSquareFoot: number;
  officeNsfNetSquareFoot: number;
  // Core NSF measurements moved to properties table
  // totalNsfNetSquareFoot: number;
  // gsfGrossSquareFoot: number;
  // zfaZoningFloorArea: number;
  
  // Property details
  numApartmentUnits: number;
  closingTime: number;
  hotelKeys: number;
  parkingSf: number;
  parkingSpots: number;
  totalNumAffordableHousingUnits: number;
  totalNumMarketRateUnits: number;
  
  // Debt information
  lienPosition: string;
  structuredLienTranche: string;
  loanAmount: number;
  interestRate: number;
  loanInterestRateBasedOffSofr: number;
  loanInterestRateBasedOffWsj: number;
  loanInterestRateBasedOffPrime: number;
  loanInterestRateBasedOff3yt: number;
  loanInterestRateBasedOff5yt: number;
  loanInterestRateBasedOff10yt: number;
  loanInterestRateBasedOff30yt: number;
  loanTerm: number;
  loanToCostLtc: number;
  loanToValueLtv: number;
  loanType: string;
  dscr: number;
  exitCapRate: number;
  recourse: string;
  
  // Senior debt costs
  seniorDebtCostGsfGrossSquareFoot: number;
  seniorDebtCostHotelKeys: number;
  seniorDebtCostPerApartmentUnits: number;
  seniorDebtNsfNetSquareFoot: number;
  seniorDebtZoningFloorArea: number;
  
  // Takeout loan
  takeoutLoanPerNsf: number;
  takeoutLoanAmortization: number;
  takeoutLoanAmount: number;
  takeoutLoanDebtYield: number;
  takeoutLoanDscr: number;
  takeoutLoanInterestRate: number;
  takeoutLoanInterestRateBasedOffSofr: number;
  takeoutLoanInterestRateBasedOffWsj: number;
  takeoutLoanInterestRateBasedOffPrime: number;
  takeoutLoanInterestRateBasedOff3yt: number;
  takeoutLoanInterestRateBasedOff5yt: number;
  takeoutLoanInterestRateBasedOff10yt: number;
  takeoutLoanInterestRateBasedOff30yt: number;
  takeoutLoanIoPeriod: number;
  takeoutLoanLoanToCostLtc: number;
  takeoutLoanLoanToValueLtv: number;
  takeoutLoanPosition: string;
  takeoutLoanTerm: number;
  takeoutLoanType: string;
  
  // Metadata
  missingCriticalFields: any;
  priority: string;
  processingDurationMs: number;
  processingNotes: string;
  processorVersion: string;
  reviewNotes: string;
  reviewStatus: string;
  reviewedAt: Date;
  reviewedBy: string;
  isDistressed: boolean;
  isInternalOnly: boolean;
  llmModelUsed: string;
  llmProvider: string;
  documentFilename: string[];
  documentSizeBytes: number;
  documentSource: string[];
  documentType: string[];
  extraFields: any;
  extractionConfidence: string;
  extractionMethod: string[];
  extractionTimestamp: Date;
  dataQualityIssues: any;
  
  // Conflicts field for storing conflict information from multiple file processing
  conflicts?: {
    [fieldName: string]: {
      chosen_value: any;
      chosen_file: string;
      reason: string;
      alternatives: Array<{
        value: any;
        file: string;
        context: string;
      }>;
    };
  };
  
  // Relationships
  propertyId: number;
  property?: PropertyV2;
  nsfFields?: DealNsfFieldV2[];
  nsf_fields?: DealNsfFieldV2[]; // API returns this as nsf_fields
  investmentCriteriaDebt?: InvestmentCriteriaDebtV2[];
  investmentCriteriaEquity?: InvestmentCriteriaEquityV2[];
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  
  // Data quality metrics
  data_quality_metrics?: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
  };
}

export interface PropertyV2 {
  propertyId: number;
  ownerId: number;
  address: string;
  city: string;
  state: string;
  zipcode: string;
  region: string;
  country: string;
  market: string;
  submarket: string;
  neighborhood: string;
  propertyType: string;
  subpropertyType: string;
  buildingSqft: number;
  landAcres: number;
  lotArea: number;
  yearBuilt: number;
  yearRenovated: number;
  latitude: number;
  longitude: number;
  propertyStatus: string;
  numberOfUnits: number;
  appraisalValue: number;
  appraisalValueDate: Date;
  gsfGrossSquareFoot: number;
  zfaZoningFloorArea: number;
  totalNsfNetSquareFoot: number;
  far: number;
  historicalOccupancyTrend: any;
  environmentalRiskScore: number;
  propertyDescription: string;
  floorAreaRatio: number;
  zoningSquareFootage: number;
  
  // Relationships
  owner?: OwnerV2;
  deals?: DealV2[];
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

export interface OwnerV2 {
  ownerId: number;
  ownerName: string;
  ownerType: string;
  ownerDescription: string;
  ownerWebsite: string;
  ownerLinkedin: string;
  ownerPhone: string;
  ownerEmail: string;
  ownerAddress: string;
  ownerCity: string;
  ownerState: string;
  ownerZipcode: string;
  ownerCountry: string;
  ownerRegion: string;
  ownerMarket: string;
  ownerSubmarket: string;
  ownerNeighborhood: string;
  ownerLatitude: number;
  ownerLongitude: number;
  ownerStatus: string;
  ownerNotes: string;
  ownerExtraFields: any;
  
  // Relationships
  properties?: PropertyV2[];
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

export interface DealNsfFieldV2 {
  id: number;
  dealId: number;
  dealType: string;
  nsfContext: string;
  // capitalPosition removed - use sourceType for sources, useType for uses
  
  // NEW FIELDS - Sources/Uses structure
  sourceType?: string;
  useType?: string;
  isRequired?: boolean;
  
  // Core NSF measurements - Moved to properties table
  // gsfGrossSquareFoot: number;
  // zfaZoningFloorArea: number;
  // totalNsfNetSquareFoot: number;
  amount: number;
  amountPerGsf: number;
  amountPerNsf: number;
  amountPerZfa: number;
  percentageOfTotal: number;
  additionalInfo: any;
  
  // Relationships
  deal?: DealV2;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

export interface InvestmentCriteriaDebtV2 {
  investmentCriteriaDebtId: number;
  investmentCriteriaId: number;
  capitalPosition?: string; // Link to specific capital position from central mapping
  notes?: string;
  closingTime?: number;
  futureFacilities?: string;
  eligibleBorrower?: string;
  occupancyRequirements?: string;
  lienPosition?: string;
  minLoanDscr?: number;
  maxLoanDscr?: number;
  recourseLoan?: string;
  loanMinDebtYield?: string;
  prepayment?: string;
  yieldMaintenance?: string;
  applicationDeposit?: number;
  goodFaithDeposit?: number;
  loanOriginationMaxFee?: number;
  loanOriginationMinFee?: number;
  loanExitMinFee?: number;
  loanExitMaxFee?: number;
  loanInterestRate?: number;
  loanInterestRateBasedOffSofr?: number;
  loanInterestRateBasedOffWsj?: number;
  loanInterestRateBasedOffPrime?: number;
  loanInterestRateBasedOff3yt?: number;
  loanInterestRateBasedOff5yt?: number;
  loanInterestRateBasedOff10yt?: number;
  loanInterestRateBasedOff30yt?: number;
  rateLock?: string;
  rateType?: string;
  loanToValueMax?: number;
  loanToValueMin?: number;
  loanToCostMin?: number;
  loanToCostMax?: number;
  debtProgramOverview?: string;
  loanType?: string;
  structuredLoanTranche?: string;
  loanProgram?: string;
  minLoanTerm?: number;
  maxLoanTerm?: number;
  amortization?: string;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

export interface InvestmentCriteriaEquityV2 {
  investmentCriteriaEquityId: number;
  investmentCriteriaId: number;
  capitalPosition?: string; // Link to specific capital position from central mapping
  
  // Equity Return Requirements
  targetReturn?: number;
  minimumInternalRateOfReturn?: number;
  minimumYieldOnCost?: number;
  minimumEquityMultiple?: number;
  targetCashOnCashMin?: number;
  
  // Equity Structure & Terms
  minHoldPeriodYears?: number;
  maxHoldPeriodYears?: number;
  ownershipRequirement?: string;
  attachmentPoint?: number;
  maxLeverageTolerance?: number;
  typicalClosingTimelineDays?: number;
  
  // Equity Program Details
  proofOfFundsRequirement?: boolean;
  notes?: string;
  equityProgramOverview?: string;
  occupancyRequirements?: string;
  
  // NEW FIELDS - Moved from DealsV2
  yieldOnCost?: number; // Moved from deal.yieldOnCost
  targetReturnIrrOnEquity?: number; // Moved from deal.commonEquityInternalRateOfReturnIrr
  equityMultiple?: number; // Moved from deal.commonEquityEquityMultiple
  
  // EQUITY POSITION SPECIFIC FIELDS - Automatically determined by capital position
  // These fields are automatically populated based on the capital position type
  // e.g., if capitalPosition is "GP Equity", these become GP-specific metrics
  positionSpecificIrr?: number; // IRR specific to this equity position (GP, LP, etc.)
  positionSpecificEquityMultiple?: number; // Equity multiple specific to this position
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

export interface DealV2Response {
  success: boolean;
  deal: DealV2;
  message: string;
}

export interface DealV2UpdateRequest {
  dealId: number;
  updates: Partial<DealV2>;
}

export interface DealV2UpdateResponse {
  success: boolean;
  deal: DealV2;
  message: string;
} 