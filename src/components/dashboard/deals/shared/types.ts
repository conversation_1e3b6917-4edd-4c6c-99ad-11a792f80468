export interface InvestmentCriteria {
  criteria_id: number;
  entity_type: string;
  entity_id: string;
  target_return: number | null;
  property_types: string[] | null;
  property_sub_categories: string[] | null;
  strategies: string[] | null;
  asset_classes: string[] | null;
  minimum_deal_size: string | number | null;
  maximum_deal_size: string | number | null;
  min_hold_period: number | null;
  max_hold_period: number | null;
  financial_products: string[] | null;
  historical_irr: number | null;
  historical_em: number | null;
  country: string[] | null;
  region: string[] | null;
  state: string[] | null;
  city: string[] | null;
  loan_program: string[] | null;
  loan_type: string[] | null;
  capital_type: string | null;
  capital_position: string[] | null;
  capital_source: string | null;
  structured_loan_tranche: string[] | null;
  min_loan_term: number | null;
  max_loan_term: number | null;
  interest_rate: number | string | null;
  loan_interest_rate: number | null;
  loan_interest_rate_sofr: number | string | null;
  loan_interest_rate_wsj: number | string | null;
  loan_interest_rate_prime: number | string | null;
  loan_ltv: number | null;
  loan_ltc: number | null;
  loan_to_value_min: number | string | null;
  loan_to_value_max: number | string | null;
  loan_to_cost_min: number | string | null;
  loan_to_cost_max: number | string | null;
  loan_origination_fee: number | null;
  loan_origination_fee_min: number | string | null;
  loan_origination_fee_max: number | string | null;
  loan_exit_fee: number | null;
  loan_exit_fee_min: number | string | null;
  loan_exit_fee_max: number | string | null;
  min_loan_dscr: number | null;
  max_loan_dscr: number | null;
  recourse_loan: string | null;
  extra_fields: Record<string, any> | null;
  created_at: string | null;
  updated_at: string | null;
  created_by: string | null;
  updated_by: string | null;
  is_active: boolean;
  is_requested: boolean;
  include_in_sources: boolean; // New field to control inclusion in sources calculation
  notes: string | null;
  // Quality metrics
  quality?: number;
  completedFields?: number;
  totalFields?: number;
  missingFields?: string[];
}

export interface Deal {
  deal_id: number;
  deal_name: string | null;
  sponsor_name: string | null;
  contact_id: number | null;
  match_type: string | null;

  // Status
  status: string | null;
  deal_stage: string | null;
  priority: string | null;
  deal_date: string | null;

  // Property Details
  zip_code: string | null;
  neighborhood: string | null;
  address: string | null; // New field for address
  property_description: string | null;
  lot_area: string | number | null;
  floor_area_ratio: string | number | null;
  zoning_square_footage: string | number | null;

  // Financial Projections
  yield_on_cost: number | null;
  projected_gp_equity_multiple: number | null;
  projected_gp_irr: number | null;
  projected_lp_equity_multiple: number | null;
  projected_lp_irr: number | null;
  projected_total_equity_multiple: number | null;
  projected_total_irr: number | null;

  // Metadata (now arrays)
  document_type: string[] | null;
  extraction_confidence: string | null;
  processing_notes: string | null;
  extraction_timestamp: string | null;
  processor_version: string | null;
  llm_model_used: string | null;
  llm_provider: string | null;
  extraction_method: string[] | null;
  document_source: string[] | null;
  document_filename: string[] | null;
  document_size_bytes: number | null;
  processing_duration_ms: number | null;

  // Review
  review_status: string | null;
  reviewed_by: string | null;
  reviewed_at: string | null;
  review_notes: string | null;
  data_quality_issues: Record<string, any> | null;
  missing_critical_fields: Record<string, any> | null;

  // Timestamps
  created_at: string | null;
  updated_at: string | null;

  // Deal flags
  is_distressed: boolean | null; // New field for distressed deal tag
  is_internal_only: boolean | null; // New field for internal/CRM sync flag

  // Extra fields
  extra_fields: Record<string, any> | null;

  // Related data (joined)
  contact?: {
    contact_id: number;
    first_name: string | null;
    last_name: string | null;
    email: string | null;
    company_name: string | null;
    job_title: string | null;
    phone_number: string | null;
    person_linkedin: string | null;
  } | null;

  // Investment criteria
  investment_criteria?: InvestmentCriteria[];

  // Data quality metrics
  deal_quality?: number;
  overall_quality?: number;
  data_quality_metrics?: {
    deal: {
      qualityScore: number;
      completedFields: number;
      totalFields: number;
      missingFields: string[];
      fieldDetails: Record<string, { hasValue: boolean; value: any }>;
    };
    investment_criteria: {
      qualityScore: number;
      completedFields: number;
      totalFields: number;
      missingFields: string[];
      fieldDetails: Record<string, { hasValue: boolean; value: any }>;
      criteriaCount: number;
    };
  };
}

export interface DealFilters {
  search?: string;
  property_type?: string;
  capital_type?: string;
  match_type?: string;
  status?: string;
  deal_stage?: string;
  priority?: string;
  review_status?: string;
  state?: string;
  city?: string;
  country?: string;
  region?: string;
}

export interface DealsResponse {
  deals: Deal[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface DealFiltersResponse {
  property_types: string[];
  capital_types: string[];
  match_types: string[];
  statuses: string[];
  deal_stages: string[];
  priorities: string[];
  review_statuses: string[];
  states: string[];
  cities: string[];
  countries: string[];
  regions: string[];
}
