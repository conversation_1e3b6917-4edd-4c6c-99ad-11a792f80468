"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON>Content, CardH<PERSON>er, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import { ArrowLeft, Plus, X, User, Building2, Mail, Phone, Globe, MapPin } from "lucide-react"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"

interface ContactFormData {
  first_name: string;
  last_name: string;
  email: string;
  personal_email: string;
  title: string;
  linkedin_url: string;
  phone_number: string;
  phone_number_secondary: string;
  company_name: string;
  company_website: string;
  industry: string;
  company_address: string;
  company_city: string;
  company_state: string;
  company_country: string;
  company_zip: string;
  contact_address: string;
  contact_city: string;
  contact_state: string;
  contact_country: string;
  contact_zip_code: string;
  region: string;
  capital_type: string;
  notes: string;
}

interface ContactCreationModalProps {
  onContactCreated: (contact: any) => void;
  trigger?: React.ReactNode;
  initialData?: Partial<ContactFormData>;
  onOpenChange?: (open: boolean) => void;
  open?: boolean;
}

const ContactCreationModal: React.FC<ContactCreationModalProps> = ({ 
  onContactCreated, 
  trigger,
  initialData = {},
  onOpenChange,
  open
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  
  // Update form data when initialData changes
  useEffect(() => {
    if (initialData && Object.keys(initialData).length > 0) {
      console.log('ContactCreationModal: Setting initial data:', initialData);
      setFormData(prev => ({
        ...prev,
        ...initialData
      }));
    }
  }, [initialData]);
  
  const [formData, setFormData] = useState<ContactFormData>({
    first_name: '',
    last_name: '',
    email: '',
    personal_email: '',
    title: '',
    linkedin_url: '',
    phone_number: '',
    phone_number_secondary: '',
    company_name: '',
    company_website: '',
    industry: '',
    company_address: '',
    company_city: '',
    company_state: '',
    company_country: '',
    company_zip: '',
    contact_address: '',
    contact_city: '',
    contact_state: '',
    contact_country: '',
    contact_zip_code: '',
    region: '',
    capital_type: '',
    notes: '',
    ...initialData
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear validation error when user starts typing
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.first_name.trim()) {
      errors.first_name = 'First name is required';
    }
    if (!formData.last_name.trim()) {
      errors.last_name = 'Last name is required';
    }
    if (!formData.company_name.trim()) {
      errors.company_name = 'Company name is required';
    }
    if (!formData.email.trim() && !formData.personal_email.trim()) {
      errors.email = 'At least one email address is required';
    }
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }
    if (formData.personal_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.personal_email)) {
      errors.personal_email = 'Please enter a valid personal email address';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/contacts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success('Contact created successfully!');
        
        // Call the callback with the new contact
        onContactCreated(result);
        
        // Reset form and close modal
        setFormData({
          first_name: '',
          last_name: '',
          email: '',
          personal_email: '',
          title: '',
          linkedin_url: '',
          phone_number: '',
          phone_number_secondary: '',
          company_name: '',
          company_website: '',
          industry: '',
          company_address: '',
          company_city: '',
          company_state: '',
          company_country: '',
          company_zip: '',
          contact_address: '',
          contact_city: '',
          contact_state: '',
          contact_country: '',
          contact_zip_code: '',
          region: '',
          capital_type: '',
          notes: ''
        });
        setIsOpen(false);
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create contact');
      }
    } catch (error) {
      console.error('Error creating contact:', error);
      toast.error('Failed to create contact');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      first_name: '',
      last_name: '',
      email: '',
      personal_email: '',
      title: '',
      linkedin_url: '',
      phone_number: '',
      phone_number_secondary: '',
      company_name: '',
      company_website: '',
      industry: '',
      company_address: '',
      company_city: '',
      company_state: '',
      company_country: '',
      company_zip: '',
      contact_address: '',
      contact_city: '',
      contact_state: '',
      contact_country: '',
      contact_zip_code: '',
      region: '',
      capital_type: '',
      notes: ''
    });
    setValidationErrors({});
    setIsOpen(false);
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    onOpenChange?.(open);
  };

  // Use controlled state if open prop is provided
  const isControlled = open !== undefined;
  const dialogOpen = isControlled ? open : isOpen;

  return (
    <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Create New Contact
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Create New Contact
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <User className="h-5 w-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="first_name" className="text-sm font-medium text-slate-700">
                    First Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="first_name"
                    name="first_name"
                    value={formData.first_name}
                    onChange={handleChange}
                    placeholder="John"
                    className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                    required
                  />
                  {validationErrors.first_name && (
                    <p className="text-red-500 text-sm mt-1">{validationErrors.first_name}</p>
                  )}
                </div>
                
                <div>
                  <Label htmlFor="last_name" className="text-sm font-medium text-slate-700">
                    Last Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="last_name"
                    name="last_name"
                    value={formData.last_name}
                    onChange={handleChange}
                    placeholder="Doe"
                    className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                    required
                  />
                  {validationErrors.last_name && (
                    <p className="text-red-500 text-sm mt-1">{validationErrors.last_name}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email" className="text-sm font-medium text-slate-700">
                    Work Email
                  </Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="<EMAIL>"
                    className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                  />
                  {validationErrors.email && (
                    <p className="text-red-500 text-sm mt-1">{validationErrors.email}</p>
                  )}
                </div>
                
                <div>
                  <Label htmlFor="personal_email" className="text-sm font-medium text-slate-700">
                    Personal Email
                  </Label>
                  <Input
                    id="personal_email"
                    name="personal_email"
                    type="email"
                    value={formData.personal_email}
                    onChange={handleChange}
                    placeholder="<EMAIL>"
                    className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                  />
                  {validationErrors.personal_email && (
                    <p className="text-red-500 text-sm mt-1">{validationErrors.personal_email}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title" className="text-sm font-medium text-slate-700">
                    Job Title
                  </Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    placeholder="Managing Director"
                    className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                  />
                </div>
                
                <div>
                  <Label htmlFor="phone_number" className="text-sm font-medium text-slate-700">
                    Phone Number
                  </Label>
                  <Input
                    id="phone_number"
                    name="phone_number"
                    value={formData.phone_number}
                    onChange={handleChange}
                    placeholder="+****************"
                    className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone_number_secondary" className="text-sm font-medium text-slate-700">
                    Secondary Phone
                  </Label>
                  <Input
                    id="phone_number_secondary"
                    name="phone_number_secondary"
                    value={formData.phone_number_secondary}
                    onChange={handleChange}
                    placeholder="+****************"
                    className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                  />
                </div>
                
                <div>
                  <Label htmlFor="linkedin_url" className="text-sm font-medium text-slate-700">
                    LinkedIn URL
                  </Label>
                  <Input
                    id="linkedin_url"
                    name="linkedin_url"
                    value={formData.linkedin_url}
                    onChange={handleChange}
                    placeholder="https://linkedin.com/in/johndoe"
                    className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Company Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Building2 className="h-5 w-5" />
                Company Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="company_name" className="text-sm font-medium text-slate-700">
                  Company Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="company_name"
                  name="company_name"
                  value={formData.company_name}
                  onChange={handleChange}
                  placeholder="Acme Corporation"
                  className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                  required
                />
                {validationErrors.company_name && (
                  <p className="text-red-500 text-sm mt-1">{validationErrors.company_name}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="company_website" className="text-sm font-medium text-slate-700">
                    Website
                  </Label>
                  <Input
                    id="company_website"
                    name="company_website"
                    value={formData.company_website}
                    onChange={handleChange}
                    placeholder="https://example.com"
                    className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                  />
                </div>
                
                <div>
                  <Label htmlFor="industry" className="text-sm font-medium text-slate-700">
                    Industry
                  </Label>
                  <Input
                    id="industry"
                    name="industry"
                    value={formData.industry}
                    onChange={handleChange}
                    placeholder="Real Estate Investment"
                    className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="company_address" className="text-sm font-medium text-slate-700">
                  Company Address
                </Label>
                <Input
                  id="company_address"
                  name="company_address"
                  value={formData.company_address}
                  onChange={handleChange}
                  placeholder="123 Business St, Suite 100"
                  className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="company_city" className="text-sm font-medium text-slate-700">
                    City
                  </Label>
                  <Input
                    id="company_city"
                    name="company_city"
                    value={formData.company_city}
                    onChange={handleChange}
                    placeholder="New York"
                    className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                  />
                </div>
                
                <div>
                  <Label htmlFor="company_state" className="text-sm font-medium text-slate-700">
                    State
                  </Label>
                  <Input
                    id="company_state"
                    name="company_state"
                    value={formData.company_state}
                    onChange={handleChange}
                    placeholder="NY"
                    className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="company_zip" className="text-sm font-medium text-slate-700">
                    Zip Code
                  </Label>
                  <Input
                    id="company_zip"
                    name="company_zip"
                    value={formData.company_zip}
                    onChange={handleChange}
                    placeholder="10001"
                    className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                  />
                </div>
                
                <div>
                  <Label htmlFor="company_country" className="text-sm font-medium text-slate-700">
                    Country
                  </Label>
                  <Input
                    id="company_country"
                    name="company_country"
                    value={formData.company_country}
                    onChange={handleChange}
                    placeholder="USA"
                    className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <MapPin className="h-5 w-5" />
                Additional Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="contact_address" className="text-sm font-medium text-slate-700">
                    Contact Address
                  </Label>
                  <Input
                    id="contact_address"
                    name="contact_address"
                    value={formData.contact_address}
                    onChange={handleChange}
                    placeholder="123 Main Street"
                    className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="contact_city" className="text-sm font-medium text-slate-700">
                      Contact City
                    </Label>
                    <Input
                      id="contact_city"
                      name="contact_city"
                      value={formData.contact_city}
                      onChange={handleChange}
                      placeholder="New York"
                      className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="contact_zip_code" className="text-sm font-medium text-slate-700">
                      Contact Zip Code
                    </Label>
                    <Input
                      id="contact_zip_code"
                      name="contact_zip_code"
                      value={formData.contact_zip_code}
                      onChange={handleChange}
                      placeholder="10001"
                      className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                    />
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="capital_type" className="text-sm font-medium text-slate-700">
                    Capital Type
                  </Label>
                  <Select value={formData.capital_type} onValueChange={(value) => handleSelectChange('capital_type', value)}>
                    <SelectTrigger className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300">
                      <SelectValue placeholder="Select capital type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="debt">Debt</SelectItem>
                      <SelectItem value="equity">Equity</SelectItem>
                      <SelectItem value="both">Both</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <Label htmlFor="notes" className="text-sm font-medium text-slate-700">
                  Notes
                </Label>
                <Textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  placeholder="Additional notes about this contact..."
                  className="min-h-[100px] rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                />
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex items-center justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex items-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4" />
                  Create Contact
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ContactCreationModal; 