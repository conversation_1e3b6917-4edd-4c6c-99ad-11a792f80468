"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { InvestmentCriteriaFilters } from "@/types/investment-criteria";
import { ReactMultiSelect } from "@/components/ui/react-multi-select";
import {
  Search,
  SlidersHorizontal,
  X,
  Filter,
  Target,
  DollarSign,
  MapPin,
  Calculator,
  Building,
  BarChart3,
  Building2,
  Activity,
  Clock,
  Calendar,
  TrendingUp,
  Banknote,
  Percent,
  Timer,
  LineChart,
  PieChart,
  User,
  Factory,
  Sparkles,
  ArrowUpDown,
  ChevronUp,
  ChevronDown,
  Settings,
  Globe,
  Home,
  FileText,
  Users,
  Briefcase,
  CheckCircle,
  AlertCircle,
  Star,
} from "lucide-react";

interface MappingsData {
  [key: string]: {
    parents?: string[];
    children?: string[];
    flat?: string[];
    hierarchical?: {
      [parent: string]: string[];
    };
  };
}

interface DealUnifiedFilters extends InvestmentCriteriaFilters {
  // Deal specific fields
  dealName?: string;
  sponsorName?: string;
  matchType?: string[];
  status?: string[];
  dealStage?: string[];
  priority?: string[];
  reviewStatus?: string[];
  extractionConfidence?: string[];

  // Deal document fields
  documentType?: string[];
  documentSource?: string[];
  extractionMethod?: string[];
  llmProvider?: string[];

  // Deal financial fields
  yieldOnCostMin?: number;
  yieldOnCostMax?: number;
  projectedGpIrrMin?: number;
  projectedGpIrrMax?: number;
  projectedLpIrrMin?: number;
  projectedLpIrrMax?: number;
  projectedGpEmMin?: number;
  projectedGpEmMax?: number;
  projectedLpEmMin?: number;
  projectedLpEmMax?: number;
  projectedTotalIrrMin?: number;
  projectedTotalIrrMax?: number;
  projectedTotalEmMin?: number;
  projectedTotalEmMax?: number;

  // Deal property fields
  zipCode?: string;
  neighborhood?: string;
  lotAreaMin?: number;
  lotAreaMax?: number;
  floorAreaRatioMin?: number;
  floorAreaRatioMax?: number;
  zoningSqFtMin?: number;
  zoningSqFtMax?: number;

  // Deal metadata
  processingDurationMin?: number;
  processingDurationMax?: number;
  documentSizeBytesMin?: number;
  documentSizeBytesMax?: number;

  // Date ranges
  extractionTimestampFrom?: string;
  extractionTimestampTo?: string;
  reviewedAtFrom?: string;
  reviewedAtTo?: string;
}

interface DealFiltersProps {
  filters: DealUnifiedFilters;
  mappings?: MappingsData;
  onFiltersChange: (filters: DealUnifiedFilters) => void;
  onClearFilters: () => void;
  isLoading?: boolean;
}

// Map filter group names to icons
const getGroupIcon = (groupName: string) => {
  switch (groupName) {
    case "Investment Focus":
      return Target;
    case "Deal Economics":
      return DollarSign;
    case "Geographic Focus":
      return MapPin;
    case "Loan Structure":
      return Calculator;
    case "Deal Profile":
      return Building;
    case "Deal Financials":
      return BarChart3;
    case "Deal Properties":
      return Building2;
    case "Deal Processing":
      return Activity;
    case "Other":
      return Settings;
    default:
      return Filter;
  }
};

export default function DealFiltersComponent({
  filters,
  mappings,
  onFiltersChange,
  onClearFilters,
  isLoading = false,
}: DealFiltersProps) {
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<DealUnifiedFilters>(filters);
  const [localRangeInputs, setLocalRangeInputs] = useState<{
    [key: string]: string;
  }>({});
  const [localTextInputs, setLocalTextInputs] = useState<{
    [key: string]: string;
  }>({});
  
  // Debounced search functionality
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>("");
  const [debouncedDealName, setDebouncedDealName] = useState<string>("");
  const [debouncedSponsorName, setDebouncedSponsorName] = useState<string>("");

  // Initialize debounced values from filters only once on mount
  useEffect(() => {
    setDebouncedSearchTerm(filters.searchTerm || "");
    setDebouncedDealName(filters.dealName || "");
    setDebouncedSponsorName(filters.sponsorName || "");
  }, []); // Only run once on mount

  // Sync local filters with prop changes (prevent unnecessary resets)
  useEffect(() => {
    console.log("=== FILTERS PROP CHANGED ===");
    console.log("New filters prop:", filters);
    console.log("Current local filters:", localFilters);
    console.log("============================");
    setLocalFilters(filters);
  }, [filters]);

  // Update parent when local filters change
  const updateFilters = useCallback((newFilters: Partial<DealUnifiedFilters>) => {
    console.log("=== updateFilters CALLED ===");
    console.log("Local filters:", localFilters);
    console.log("New filters:", newFilters);
    console.log("Call stack:", new Error().stack);
    console.log("=============================");
    
    // Don't reset page for pagination-related changes
    const isPaginationChange = Object.keys(newFilters).every(key => 
      ['page', 'limit', 'sortBy', 'sortOrder'].includes(key)
    );
    
    const updatedFilters = { 
      ...localFilters, 
      ...newFilters, 
      page: isPaginationChange ? localFilters.page : 1 
    };
    console.log(localFilters,';hh',newFilters)
    setLocalFilters(updatedFilters);
    onFiltersChange(updatedFilters);
  }, [localFilters, onFiltersChange]);

  // Debounced search effect
  useEffect(() => {
    const timer = setTimeout(() => {
      // Only trigger if the search term actually changed and is meaningful
      const newSearchTerm = debouncedSearchTerm || undefined;
      if (newSearchTerm !== localFilters.searchTerm) {
        console.log("=== DEBOUNCED SEARCH TRIGGERED ===");
        console.log("debouncedSearchTerm:", debouncedSearchTerm);
        console.log("localFilters.searchTerm:", localFilters.searchTerm);
        console.log("================================");
        const updatedFilters = { ...localFilters, searchTerm: newSearchTerm, page: 1 };
        setLocalFilters(updatedFilters);
        onFiltersChange(updatedFilters);
      }
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [debouncedSearchTerm, localFilters.searchTerm]);

  // Debounced deal name effect
  useEffect(() => {
    const timer = setTimeout(() => {
      // Only trigger if the deal name actually changed and is meaningful
      const newDealName = debouncedDealName || undefined;
      if (newDealName !== localFilters.dealName) {
        console.log("=== DEBOUNCED DEAL NAME TRIGGERED ===");
        console.log("debouncedDealName:", debouncedDealName);
        console.log("localFilters.dealName:", localFilters.dealName);
        console.log("=====================================");
        const updatedFilters = { ...localFilters, dealName: newDealName, page: 1 };
        setLocalFilters(updatedFilters);
        onFiltersChange(updatedFilters);
      }
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [debouncedDealName, localFilters.dealName]);

  // Debounced sponsor name effect
  useEffect(() => {
    const timer = setTimeout(() => {
      // Only trigger if the sponsor name actually changed and is meaningful
      const newSponsorName = debouncedSponsorName || undefined;
      if (newSponsorName !== localFilters.sponsorName) {
        console.log("=== DEBOUNCED SPONSOR NAME TRIGGERED ===");
        console.log("debouncedSponsorName:", debouncedSponsorName);
        console.log("localFilters.sponsorName:", localFilters.sponsorName);
        console.log("=======================================");
        const updatedFilters = { ...localFilters, sponsorName: newSponsorName, page: 1 };
        setLocalFilters(updatedFilters);
        onFiltersChange(updatedFilters);
      }
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [debouncedSponsorName, localFilters.sponsorName]);

  // Handle range input changes with local state (no immediate search)
  const updateLocalRangeInput = (key: string, value: string) => {
    setLocalRangeInputs((prev) => ({ ...prev, [key]: value }));
  };

  // Apply range filter when user finishes input (onBlur)
  const applyRangeFilter = (key: string, value: string) => {
    console.log("=== APPLY RANGE FILTER ===");
    console.log("Key:", key);
    console.log("Value:", value);
    console.log("Call stack:", new Error().stack);
    console.log("========================");
    
    const numericValue = value.trim() === "" ? undefined : Number(value);
    updateFilters({ [key]: numericValue } as Partial<DealUnifiedFilters>);

    // Clear local input state
    setLocalRangeInputs((prev) => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  };

  // Handle text input changes with local state (no immediate search)
  const updateLocalTextInput = (key: string, value: string) => {
    setLocalTextInputs((prev) => ({ ...prev, [key]: value }));
  };

  // Apply text filter when user finishes input (onBlur)
  const applyTextFilter = (key: string, value: string) => {
    console.log("=== APPLY TEXT FILTER ===");
    console.log("Key:", key);
    console.log("Value:", value);
    console.log("Call stack:", new Error().stack);
    console.log("=======================");
    
    const textValue = value.trim() === "" ? undefined : value.trim();
    updateFilters({ [key]: textValue } as Partial<DealUnifiedFilters>);

    // Clear local input state
    setLocalTextInputs((prev) => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  };

  // Handle debounced search input
  const handleSearchInputChange = (value: string) => {
    setDebouncedSearchTerm(value);
  };

  // Handle debounced deal name input
  const handleDealNameInputChange = (value: string) => {
    setDebouncedDealName(value);
  };

  // Handle debounced sponsor name input
  const handleSponsorNameInputChange = (value: string) => {
    setDebouncedSponsorName(value);
  };

  // Get current value for range input (local state or filter value)
  const getRangeInputValue = (key: string, filterValue?: number) => {
    return localRangeInputs[key] !== undefined
      ? localRangeInputs[key]
      : filterValue || "";
  };

  // Get current value for text input (local state or filter value)
  const getTextInputValue = (key: string, filterValue?: string) => {
    return localTextInputs[key] !== undefined
      ? localTextInputs[key]
      : filterValue || "";
  };

  // Helper function to get parent values (for main dropdowns)
  const getParentValues = (type: string) => {
    return mappings?.[type]?.parents || [];
  };

  // Helper function to get all child values (for initial child dropdown)
  const getAllChildValues = (type: string) => {
    return mappings?.[type]?.children || [];
  };

  // Helper function to get filtered child values based on selected parents
  const getFilteredChildValues = (type: string, selectedParents: string[]) => {
    if (!selectedParents?.length) {
      return getAllChildValues(type);
    }

    const filteredChildren = new Set<string>();
    selectedParents.forEach((parent) => {
      const children = mappings?.[type]?.hierarchical?.[parent] || [];
      children.forEach((child) => filteredChildren.add(child));
    });

    return Array.from(filteredChildren).sort();
  };

  // Count active filters including deal-specific filters
  const getActiveFilterCount = () => {
    let count = 0;

    // Investment criteria filters (base filters)
    if (localFilters.searchTerm) count++;
    if (localFilters.entityType) count++;
    if (localFilters.entityId) count++;
    if (localFilters.entityName) count++;
    if (localFilters.criteriaId) count++;
    if (localFilters.dealSizeMin || localFilters.dealSizeMax) count++;
    if (localFilters.targetReturnMin || localFilters.targetReturnMax) count++;
    if (localFilters.historicalIrrMin || localFilters.historicalIrrMax) count++;
    if (localFilters.historicalEmMin || localFilters.historicalEmMax) count++;
    if (localFilters.minHoldPeriod) count++;
    if (localFilters.maxHoldPeriod) count++;
    if (localFilters.capitalPosition?.length) count++;
    if (localFilters.loanTypes?.length) count++;
    if (localFilters.loanProgram?.length) count++;
    if (localFilters.structuredLoanTranche?.length) count++;
    if (localFilters.recourseLoan?.length) count++;
    if (localFilters.minLoanTerm) count++;
    if (localFilters.maxLoanTerm) count++;
    if (localFilters.interestRateMin || localFilters.interestRateMax) count++;
    if (localFilters.loanToValueMin || localFilters.loanToValueMax) count++;
    if (localFilters.loanToCostMin || localFilters.loanToCostMax) count++;
    if (localFilters.minLoanDscr) count++;
    if (localFilters.maxLoanDscr) count++;
    if (localFilters.propertyTypes?.length) count++;
    if (localFilters.propertySubcategories?.length) count++;
    if (localFilters.strategies?.length) count++;
    if (localFilters.financialProducts?.length) count++;
    if (localFilters.countries?.length) count++;
    if (localFilters.regions?.length) count++;
    if (localFilters.states?.length) count++;
    if (localFilters.cities?.length) count++;
    if (localFilters.closingTimeWeeks) count++;
    if (localFilters.isActive !== undefined) count++;

    // Deal-specific filters
    if (localFilters.dealName) count++;
    if (localFilters.sponsorName) count++;
    if (localFilters.matchType?.length) count++;
    if (localFilters.status?.length) count++;
    if (localFilters.dealStage?.length) count++;
    if (localFilters.priority?.length) count++;
    if (localFilters.reviewStatus?.length) count++;
    if (localFilters.extractionConfidence?.length) count++;
    if (localFilters.documentType?.length) count++;
    if (localFilters.documentSource?.length) count++;
    if (localFilters.extractionMethod?.length) count++;
    if (localFilters.llmProvider?.length) count++;
    if (localFilters.zipCode) count++;
    if (localFilters.neighborhood) count++;

    // Deal financial range filters
    if (localFilters.yieldOnCostMin || localFilters.yieldOnCostMax) count++;
    if (localFilters.projectedGpIrrMin || localFilters.projectedGpIrrMax)
      count++;
    if (localFilters.projectedLpIrrMin || localFilters.projectedLpIrrMax)
      count++;
    if (localFilters.projectedGpEmMin || localFilters.projectedGpEmMax) count++;
    if (localFilters.projectedLpEmMin || localFilters.projectedLpEmMax) count++;
    if (localFilters.projectedTotalIrrMin || localFilters.projectedTotalIrrMax)
      count++;
    if (localFilters.projectedTotalEmMin || localFilters.projectedTotalEmMax)
      count++;

    // Deal property range filters
    if (localFilters.lotAreaMin || localFilters.lotAreaMax) count++;
    if (localFilters.floorAreaRatioMin || localFilters.floorAreaRatioMax)
      count++;
    if (localFilters.zoningSqFtMin || localFilters.zoningSqFtMax) count++;

    // Deal metadata range filters
    if (
      localFilters.processingDurationMin ||
      localFilters.processingDurationMax
    )
      count++;
    if (localFilters.documentSizeBytesMin || localFilters.documentSizeBytesMax)
      count++;

    // Date range filters
    if (
      localFilters.extractionTimestampFrom ||
      localFilters.extractionTimestampTo
    )
      count++;
    if (localFilters.reviewedAtFrom || localFilters.reviewedAtTo) count++;

    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  // Enhanced sort options including deal-specific fields
  const UNIFIED_SORT_OPTIONS = [
    { value: "updated_at", label: "Last Updated (Newest First)", icon: Clock },
    { value: "created_at", label: "Created Date", icon: Calendar },
    { value: "deal_name", label: "Deal Name", icon: Building },
    { value: "sponsor_name", label: "Sponsor Name", icon: User },

    // Investment criteria sorts
    {
      value: "target_return",
      label: "Target Return (Highest First)",
      icon: Target,
    },
    {
      value: "historical_irr",
      label: "Historical IRR (Highest First)",
      icon: TrendingUp,
    },
    {
      value: "historical_em",
      label: "Historical EM (Highest First)",
      icon: Calculator,
    },
    {
      value: "minimum_deal_size",
      label: "Min Deal Size (Largest First)",
      icon: DollarSign,
    },
    {
      value: "maximum_deal_size",
      label: "Max Deal Size (Largest First)",
      icon: Banknote,
    },
    {
      value: "interest_rate",
      label: "Interest Rate (Highest First)",
      icon: Percent,
    },
    {
      value: "min_loan_term",
      label: "Min Loan Term (Longest First)",
      icon: Timer,
    },
    {
      value: "max_loan_term",
      label: "Max Loan Term (Longest First)",
      icon: Clock,
    },
    {
      value: "loan_to_value_max",
      label: "Max LTV (Highest First)",
      icon: TrendingUp,
    },
    {
      value: "loan_to_cost_max",
      label: "Max LTC (Highest First)",
      icon: LineChart,
    },

    // Deal-specific sorts
    {
      value: "yield_on_cost",
      label: "Yield on Cost (Highest First)",
      icon: DollarSign,
    },
    {
      value: "projected_total_irr",
      label: "Projected Total IRR (Highest First)",
      icon: TrendingUp,
    },
    {
      value: "projected_total_equity_multiple",
      label: "Projected Total EM (Highest First)",
      icon: Calculator,
    },
    {
      value: "projected_gp_irr",
      label: "Projected GP IRR (Highest First)",
      icon: BarChart3,
    },
    {
      value: "projected_lp_irr",
      label: "Projected LP IRR (Highest First)",
      icon: LineChart,
    },
    { value: "lot_area", label: "Lot Area (Largest First)", icon: Building2 },
    {
      value: "processing_duration_ms",
      label: "Processing Duration (Fastest First)",
      icon: Activity,
    },
    {
      value: "extraction_timestamp",
      label: "Extraction Date (Newest First)",
      icon: Calendar,
    },
  ];

  return (
    <>
      {/* Unified Filter Bar */}
      <div className="w-full bg-white border border-gray-200 rounded-xl shadow-sm mb-6">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            {/* Unified Filter Button */}
            <Button
              onClick={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
              className={`flex items-center gap-3 px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 ${
                isFilterPanelOpen
                  ? "bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white"
                  : "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
              }`}
            >
              <SlidersHorizontal className="h-5 w-5" />
              <span className="font-medium">Filters</span>
              {activeFilterCount > 0 && (
                <Badge className="bg-white/20 text-white border border-white/20 ml-1">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>

            {/* Quick Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search deals, sponsors, or criteria..."
                value={debouncedSearchTerm || localFilters.searchTerm || ""}
                onChange={(e) => handleSearchInputChange(e.target.value)}
                className="pl-10 border-gray-200 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
              />
            </div>
          </div>

          {/* Sort Controls */}
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                Sort by:
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Select
                value={localFilters.sortBy || "updated_at"}
                onValueChange={(value) => updateFilters({ sortBy: value })}
              >
                <SelectTrigger className="w-auto min-w-[250px] border-gray-200 bg-white shadow-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="max-h-[400px]">
                  {UNIFIED_SORT_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <option.icon className="h-4 w-4" />
                        {option.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Sort Order Toggle */}
              <Button
                variant="outline"
                size="icon"
                onClick={() =>
                  updateFilters({
                    sortOrder:
                      localFilters.sortOrder === "asc" ? "desc" : "asc",
                  })
                }
                className="h-10 w-10"
              >
                {localFilters.sortOrder === "asc" ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Comprehensive Right Side Filter Panel */}
      <div
        className={`fixed top-0 right-0 h-full w-[600px] bg-white border-l border-gray-200 shadow-xl transform transition-transform duration-300 ease-in-out z-50 ${
          isFilterPanelOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Enhanced Panel Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 via-indigo-50 to-blue-50">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="p-3 rounded-xl bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-lg">
                  <Building className="h-6 w-6" />
                </div>
                {activeFilterCount > 0 && (
                  <div className="absolute -top-2 -right-2 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center font-bold">
                    {activeFilterCount > 9 ? "9+" : activeFilterCount}
                  </div>
                )}
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                  Filters
                  <Badge className="bg-purple-100 text-purple-700 border border-purple-200 ml-2">
                    Investment Criteria + Deal Data
                  </Badge>
                </h2>
                <p className="text-sm text-gray-600 mt-1 flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Filter deals by investment criteria and deal-specific data
                </p>
              </div>
            </div>

            <Button
              onClick={() => setIsFilterPanelOpen(false)}
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Filter Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <div className="space-y-6">
              {/* Investment Focus Group */}
              <Card className="border-0 shadow-sm bg-gradient-to-r from-blue-50 to-indigo-50">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3 text-lg">
                    <Target className="h-5 w-5 text-blue-600" />
                    Investment Focus
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Capital Position */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      Capital Position
                    </Label>
                    <ReactMultiSelect
                      options={getParentValues("Capital Position").map(
                        (type: string) => ({ value: type, label: type })
                      )}
                      selected={localFilters.capitalPosition || []}
                      onChange={(selected: string[]) =>
                        updateFilters({ capitalPosition: selected })
                      }
                      placeholder="Select capital positions..."
                      disabled={isLoading}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Deal Profile Group */}
              <Card className="border-0 shadow-sm bg-gradient-to-r from-purple-50 to-pink-50">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3 text-lg">
                    <Building2 className="h-5 w-5 text-purple-600" />
                    Deal Profile
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Deal Name */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      Deal Name
                    </Label>
                    <Input
                      placeholder="Search deal name..."
                      value={debouncedDealName}
                      onChange={(e) => handleDealNameInputChange(e.target.value)}
                      className="w-full bg-white border-gray-200"
                      disabled={isLoading}
                    />
                  </div>

                  {/* Sponsor Name */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      Sponsor Name
                    </Label>
                    <Input
                      placeholder="Search sponsor name..."
                      value={debouncedSponsorName}
                      onChange={(e) => handleSponsorNameInputChange(e.target.value)}
                      className="w-full bg-white border-gray-200"
                      disabled={isLoading}
                    />
                  </div>

                  {/* Status */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      Status
                    </Label>
                    <ReactMultiSelect
                      options={getParentValues("Status").map(
                        (status: string) => ({ value: status, label: status })
                      )}
                      selected={localFilters.status || []}
                      onChange={(selected: string[]) =>
                        updateFilters({ status: selected })
                      }
                      placeholder="Select statuses..."
                      disabled={isLoading}
                    />
                  </div>

                  {/* Deal Stage */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      Deal Stage
                    </Label>
                    <ReactMultiSelect
                      options={getParentValues("Deal Stage").map(
                        (stage: string) => ({ value: stage, label: stage })
                      )}
                      selected={localFilters.dealStage || []}
                      onChange={(selected: string[]) =>
                        updateFilters({ dealStage: selected })
                      }
                      placeholder="Select deal stages..."
                      disabled={isLoading}
                    />
                  </div>

                  {/* Priority */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      Priority
                    </Label>
                    <ReactMultiSelect
                      options={getParentValues("Priority").map(
                        (priority: string) => ({
                          value: priority,
                          label: priority,
                        })
                      )}
                      selected={localFilters.priority || []}
                      onChange={(selected: string[]) =>
                        updateFilters({ priority: selected })
                      }
                      placeholder="Select priorities..."
                      disabled={isLoading}
                    />
                  </div>

                  {/* LLM Provider */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      LLM Provider
                    </Label>
                    <ReactMultiSelect
                      options={[
                        { value: "openai", label: "OpenAI" },
                        { value: "anthropic", label: "Anthropic" },
                        { value: "google", label: "Google" },
                        { value: "azure", label: "Azure OpenAI" },
                        { value: "ollama", label: "Ollama" },
                      ]}
                      selected={localFilters.llmProvider || []}
                      onChange={(selected: string[]) =>
                        updateFilters({ llmProvider: selected })
                      }
                      placeholder="Select LLM providers..."
                      disabled={isLoading}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Deal Economics Group */}
              <Card className="border-0 shadow-sm bg-gradient-to-r from-green-50 to-emerald-50">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3 text-lg">
                    <DollarSign className="h-5 w-5 text-green-600" />
                    Deal Economics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Deal Size Range */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      Deal Size Range ($M)
                    </Label>
                    <div className="grid grid-cols-2 gap-4">
                      <Input
                        type="number"
                        placeholder="Min ($M)"
                        value={getRangeInputValue(
                          "dealSizeMin",
                          localFilters.dealSizeMin
                        )}
                        onChange={(e) =>
                          updateLocalRangeInput("dealSizeMin", e.target.value)
                        }
                        onBlur={(e) =>
                          applyRangeFilter("dealSizeMin", e.target.value)
                        }
                        onKeyDown={(e) =>
                          e.key === "Enter" &&
                          applyRangeFilter("dealSizeMin", e.currentTarget.value)
                        }
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                      <Input
                        type="number"
                        placeholder="Max ($M)"
                        value={getRangeInputValue(
                          "dealSizeMax",
                          localFilters.dealSizeMax
                        )}
                        onChange={(e) =>
                          updateLocalRangeInput("dealSizeMax", e.target.value)
                        }
                        onBlur={(e) =>
                          applyRangeFilter("dealSizeMax", e.target.value)
                        }
                        onKeyDown={(e) =>
                          e.key === "Enter" &&
                          applyRangeFilter("dealSizeMax", e.currentTarget.value)
                        }
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  {/* Yield on Cost Range */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      Yield on Cost Range (%)
                    </Label>
                    <div className="grid grid-cols-2 gap-4">
                      <Input
                        type="number"
                        placeholder="Min (%)"
                        value={getRangeInputValue(
                          "yieldOnCostMin",
                          localFilters.yieldOnCostMin
                        )}
                        onChange={(e) =>
                          updateLocalRangeInput(
                            "yieldOnCostMin",
                            e.target.value
                          )
                        }
                        onBlur={(e) =>
                          applyRangeFilter("yieldOnCostMin", e.target.value)
                        }
                        onKeyDown={(e) =>
                          e.key === "Enter" &&
                          applyRangeFilter(
                            "yieldOnCostMin",
                            e.currentTarget.value
                          )
                        }
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                      <Input
                        type="number"
                        placeholder="Max (%)"
                        value={getRangeInputValue(
                          "yieldOnCostMax",
                          localFilters.yieldOnCostMax
                        )}
                        onChange={(e) =>
                          updateLocalRangeInput(
                            "yieldOnCostMax",
                            e.target.value
                          )
                        }
                        onBlur={(e) =>
                          applyRangeFilter("yieldOnCostMax", e.target.value)
                        }
                        onKeyDown={(e) =>
                          e.key === "Enter" &&
                          applyRangeFilter(
                            "yieldOnCostMax",
                            e.currentTarget.value
                          )
                        }
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  {/* Projected Total IRR Range */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      Projected Total IRR Range (%)
                    </Label>
                    <div className="grid grid-cols-2 gap-4">
                      <Input
                        type="number"
                        placeholder="Min (%)"
                        value={getRangeInputValue(
                          "projectedTotalIrrMin",
                          localFilters.projectedTotalIrrMin
                        )}
                        onChange={(e) =>
                          updateLocalRangeInput(
                            "projectedTotalIrrMin",
                            e.target.value
                          )
                        }
                        onBlur={(e) =>
                          applyRangeFilter(
                            "projectedTotalIrrMin",
                            e.target.value
                          )
                        }
                        onKeyDown={(e) =>
                          e.key === "Enter" &&
                          applyRangeFilter(
                            "projectedTotalIrrMin",
                            e.currentTarget.value
                          )
                        }
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                      <Input
                        type="number"
                        placeholder="Max (%)"
                        value={getRangeInputValue(
                          "projectedTotalIrrMax",
                          localFilters.projectedTotalIrrMax
                        )}
                        onChange={(e) =>
                          updateLocalRangeInput(
                            "projectedTotalIrrMax",
                            e.target.value
                          )
                        }
                        onBlur={(e) =>
                          applyRangeFilter(
                            "projectedTotalIrrMax",
                            e.target.value
                          )
                        }
                        onKeyDown={(e) =>
                          e.key === "Enter" &&
                          applyRangeFilter(
                            "projectedTotalIrrMax",
                            e.currentTarget.value
                          )
                        }
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Geographic Focus Group */}
              <Card className="border-0 shadow-sm bg-gradient-to-r from-emerald-50 to-teal-50">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3 text-lg">
                    <Globe className="h-5 w-5 text-emerald-600" />
                    Geographic Focus
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Regions */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      U.S. Regions
                    </Label>
                    <ReactMultiSelect
                      options={getParentValues("U.S Regions").map(
                        (region: string) => ({ value: region, label: region })
                      )}
                      selected={localFilters.regions || []}
                      onChange={(selected: string[]) =>
                        updateFilters({
                          regions: selected,
                          states:
                            localFilters.states?.filter((state) =>
                              getFilteredChildValues(
                                "U.S Regions",
                                selected
                              ).includes(state)
                            ) || [],
                        })
                      }
                      placeholder="Select U.S. regions..."
                      disabled={isLoading}
                    />
                  </div>

                  {/* States */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      States
                    </Label>
                    <ReactMultiSelect
                      options={getFilteredChildValues(
                        "U.S Regions",
                        localFilters.regions || []
                      ).map((state) => ({ value: state, label: state }))}
                      selected={
                        localFilters.states?.filter((state) =>
                          getFilteredChildValues(
                            "U.S Regions",
                            localFilters.regions || []
                          ).includes(state)
                        ) || []
                      }
                      onChange={(selected: string[]) =>
                        updateFilters({ states: selected })
                      }
                      placeholder="Select states..."
                      disabled={isLoading}
                    />
                  </div>

                  {/* Zip Code */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      Zip Code
                    </Label>
                    <Input
                      placeholder="Enter zip code..."
                      value={localFilters.zipCode || ""}
                      onChange={(e) =>
                        updateFilters({ zipCode: e.target.value || undefined })
                      }
                      className="w-full bg-white border-gray-200"
                      disabled={isLoading}
                    />
                  </div>

                  {/* Neighborhood */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      Neighborhood
                    </Label>
                    <Input
                      placeholder="Enter neighborhood..."
                      value={localFilters.neighborhood || ""}
                      onChange={(e) =>
                        updateFilters({
                          neighborhood: e.target.value || undefined,
                        })
                      }
                      className="w-full bg-white border-gray-200"
                      disabled={isLoading}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Enhanced Footer */}
          <div className="border-t border-gray-200 p-6 bg-gradient-to-r from-gray-50 to-purple-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                {activeFilterCount > 0 ? (
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4 text-purple-600" />
                    <span className="font-medium text-gray-700">
                      {activeFilterCount} Filter
                      {activeFilterCount !== 1 ? "s" : ""} Active
                    </span>
                    <Badge className="bg-purple-100 text-purple-700 border border-purple-200">
                      Investment + Deal Data
                    </Badge>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4 text-gray-400" />
                    <span>
                      No filters applied - showing all unified deal data
                    </span>
                  </div>
                )}
              </div>

              <div className="flex items-center gap-3">
                <Button
                  onClick={onClearFilters}
                  variant="outline"
                  size="sm"
                  className="text-gray-600 hover:text-gray-800"
                >
                  Clear All
                </Button>
                <Button
                  onClick={() => setIsFilterPanelOpen(false)}
                  className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white"
                >
                  Apply Filters
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
