"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Progress } from "@/components/ui/progress";
import {
  RefreshCw,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface Upload {
  job_id: string;
  status: string;
  progress: number;
  created_at: string;
  completed_at?: string;
  error_message?: string;
  created_by?: string;
  metadata?: any;
  files: Array<{
    file_name: string;
    file_type: string;
    file_size: number;
    is_primary: boolean;
  }>;
}

interface RecentUploadsProps {
  limit?: number;
  createdBy?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "completed":
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case "failed":
      return <XCircle className="h-4 w-4 text-red-500" />;
    case "active":
      return <AlertCircle className="h-4 w-4 text-blue-500" />;
    default:
      return <Clock className="h-4 w-4 text-gray-500" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case "completed":
      return "bg-green-100 text-green-800";
    case "failed":
      return "bg-red-100 text-red-800";
    case "active":
      return "bg-blue-100 text-blue-800";
    case "waiting":
      return "bg-yellow-100 text-yellow-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export default function RecentUploads({
  limit = 10,
  createdBy,
  autoRefresh = true,
  refreshInterval = 5000,
}: RecentUploadsProps) {
  const [uploads, setUploads] = useState<Upload[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<any>(null);

  const fetchUploads = async () => {
    try {
      const params = new URLSearchParams();
      params.append("limit", limit.toString());
      if (createdBy) params.append("created_by", createdBy);

      const response = await fetch(`/api/jobs/recent?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch uploads");
      }

      const data = await response.json();
      if (data.success) {
        setUploads(data.uploads);
        setStats(data.stats);
        setError(null);
      } else {
        setError(data.error || "Unknown error");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUploads();

    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(fetchUploads, refreshInterval);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [limit, createdBy, autoRefresh, refreshInterval]);

  const handleRefresh = () => {
    setLoading(true);
    fetchUploads();
  };

  if (loading && uploads.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Uploads</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <RefreshCw className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading uploads...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Recent Uploads</CardTitle>
          <div className="flex items-center gap-2">
            {stats && (
              <div className="flex gap-2 text-sm">
                <Badge variant="outline">
                  Active: {stats.database?.active || 0}
                </Badge>
                <Badge variant="outline">
                  Completed: {stats.database?.completed || 0}
                </Badge>
                <Badge variant="outline">
                  Failed: {stats.database?.failed || 0}
                </Badge>
              </div>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
            >
              <RefreshCw
                className={`h-4 w-4 ${loading ? "animate-spin" : ""}`}
              />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        {uploads.length === 0 ? (
          <div className="text-center py-8 text-gray-500">No uploads found</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Status</TableHead>
                <TableHead>Files</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Completed</TableHead>
                <TableHead>Creator</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {uploads.map((upload) => (
                <TableRow key={upload.job_id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(upload.status)}
                      <Badge className={getStatusColor(upload.status)}>
                        {upload.status}
                      </Badge>
                    </div>
                    {upload.error_message && (
                      <div className="mt-1 text-xs text-red-600">
                        {upload.error_message}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {upload.files?.map((file, idx) => (
                        <div key={idx} className="flex items-center gap-2">
                          <span className="text-sm font-medium">
                            {file.file_name}
                          </span>
                          {file.is_primary && (
                            <Badge variant="secondary" className="text-xs">
                              Primary
                            </Badge>
                          )}
                          <span className="text-xs text-gray-500">
                            ({formatFileSize(file.file_size)})
                          </span>
                        </div>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Progress value={upload.progress} className="w-20" />
                      <div className="text-xs text-gray-600">
                        {upload.progress}%
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatDistanceToNow(new Date(upload.created_at), {
                        addSuffix: true,
                      })}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {upload.completed_at ? (
                        formatDistanceToNow(new Date(upload.completed_at), {
                          addSuffix: true,
                        })
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {upload.created_by || (
                        <span className="text-gray-400">Unknown</span>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}
