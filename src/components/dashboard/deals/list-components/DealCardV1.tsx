"use client";

import React, { useState } from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DollarSign,
  MapPin,
  Building2,
  User,
  Calendar,
  ArrowRight,
  TrendingUp,
  Percent,
  Trash2,
  CheckCircle,
  AlertTriangle,
  Database,
  Edit,
} from "lucide-react";
import { Deal } from "../shared/types";
import { useRouter } from "next/navigation";
import { formatCurrency } from "@/lib/utils";

interface DealCardV1Props {
  deal: Deal;
  onDelete?: (dealId: string) => void;
  onEdit?: (deal: Deal) => void;
}

const DealCardV1: React.FC<DealCardV1Props> = ({ deal, onDelete, onEdit }) => {
  const router = useRouter();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click navigation

    if (!onDelete) {
      console.warn("No onDelete handler provided to DealCardV1");
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/deals/${deal.deal_id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        const result = await response.json();
        console.log("Delete response:", result);
        onDelete(deal.deal_id.toString());
        setIsDeleteDialogOpen(false);
      } else {
        const error = await response.json();
        console.error("Failed to delete deal:", error);
        alert(`Failed to delete deal: ${error.error || "Unknown error"}`);
      }
    } catch (error) {
      console.error("Error deleting deal:", error);
      alert("An error occurred while deleting the deal");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCardClick = () => {
    router.push(`/dashboard/deals/${deal.deal_id}`);
  };

  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEdit) {
      onEdit(deal);
    }
  };

  // Calculate quality score
  const calculateQualityScore = () => {
    const fields = [
      deal.deal_name,
      deal.sponsor_name,
      deal.property_address,
      deal.property_city,
      deal.property_state,
      deal.property_zip_code,
      deal.property_type,
      deal.deal_stage,
      deal.deal_status,
      deal.strategy,
      deal.hold_period,
      deal.yield_on_cost,
      deal.projected_gp_irr,
      deal.projected_lp_irr,
      deal.projected_gp_em,
      deal.projected_lp_em,
      deal.projected_total_irr,
      deal.projected_total_em,
    ];

    const completedFields = fields.filter(field => field !== null && field !== undefined && field !== '').length;
    const totalFields = fields.length;
    
    return Math.round((completedFields / totalFields) * 100);
  };

  const qualityScore = calculateQualityScore();

  const getQualityColor = (score: number) => {
    if (score >= 90) return "text-emerald-600 bg-emerald-50 border-emerald-200";
    if (score >= 80) return "text-blue-600 bg-blue-50 border-blue-200";
    if (score >= 70) return "text-amber-600 bg-amber-50 border-amber-200";
    return "text-red-600 bg-red-50 border-red-200";
  };

  const getQualityIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="h-4 w-4" />;
    if (score >= 80) return <Database className="h-4 w-4" />;
    if (score >= 70) return <TrendingUp className="h-4 w-4" />;
    return <AlertTriangle className="h-4 w-4" />;
  };

  return (
    <>
      <Card 
        className="cursor-pointer hover:shadow-md transition-shadow duration-200 border-l-4 border-l-orange-400"
        onClick={handleCardClick}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="font-semibold text-gray-900 truncate">
                  {deal.deal_name || "Unnamed Deal"}
                </h3>
                <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700 border-orange-200">
                  V1
                </Badge>
              </div>
              
              <div className="flex items-center gap-4 text-sm text-gray-600">
                {deal.sponsor_name && (
                  <div className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    <span>{deal.sponsor_name}</span>
                  </div>
                )}
                {deal.property_city && deal.property_state && (
                  <div className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    <span>{deal.property_city}, {deal.property_state}</span>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Badge variant="outline" className={getQualityColor(qualityScore)}>
                {qualityScore}%
              </Badge>
              {getQualityIcon(qualityScore)}
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="text-gray-500 mb-1">Strategy</div>
              <div className="font-medium">{deal.strategy || "N/A"}</div>
            </div>
            
            <div>
              <div className="text-gray-500 mb-1">Stage</div>
              <div className="font-medium">{deal.deal_stage || "N/A"}</div>
            </div>
            
            <div>
              <div className="text-gray-500 mb-1">Hold Period</div>
              <div className="font-medium">
                {deal.hold_period ? `${deal.hold_period} years` : "N/A"}
              </div>
            </div>
            
            <div>
              <div className="text-gray-500 mb-1">Total IRR</div>
              <div className="font-medium">
                {deal.projected_total_irr ? `${deal.projected_total_irr}%` : "N/A"}
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="h-3 w-3" />
              <span>
                {deal.created_at 
                  ? new Date(deal.created_at).toLocaleDateString()
                  : "No date"
                }
              </span>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleEditClick}
                className="h-8 w-8 p-0"
              >
                <Edit className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsDeleteDialogOpen(true);
                }}
                className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCardClick}
                className="h-8 w-8 p-0"
              >
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Deal</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{deal.deal_name || 'this deal'}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default DealCardV1; 