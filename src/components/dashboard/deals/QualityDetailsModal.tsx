"use client";

import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  AlertTriangle,
  CheckCircle,
  Database,
  Target,
  TrendingUp,
  Info,
} from "lucide-react";

interface QualityDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  deal: any;
}

const QualityDetailsModal: React.FC<QualityDetailsModalProps> = ({
  isOpen,
  onClose,
  deal,
}) => {
  if (!deal) return null;

  const dealQuality = deal.deal_quality || deal.data_quality_metrics?.deal?.qualityScore || 0;
  const criteriaQuality = deal.data_quality_metrics?.investment_criteria?.qualityScore || 0;
  const overallQuality = deal.overall_quality || Math.round((dealQuality + criteriaQuality) / 2);

  const getQualityColor = (score: number) => {
    if (score >= 90) return "text-emerald-600 bg-emerald-50 border-emerald-200";
    if (score >= 80) return "text-blue-600 bg-blue-50 border-blue-200";
    if (score >= 70) return "text-amber-600 bg-amber-50 border-amber-200";
    return "text-red-600 bg-red-50 border-red-200";
  };

  const getQualityIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="h-4 w-4" />;
    if (score >= 80) return <Target className="h-4 w-4" />;
    if (score >= 70) return <TrendingUp className="h-4 w-4" />;
    return <AlertTriangle className="h-4 w-4" />;
  };

  const formatFieldName = (fieldName: string): string => {
    return fieldName
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Data Quality Details: {deal.deal_name || "Unnamed Deal"}
          </DialogTitle>
          <DialogDescription>
            Comprehensive breakdown of data completeness and missing fields
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Overall Quality Score */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Overall Quality Score</h3>
              <Badge variant="outline" className={getQualityColor(overallQuality)}>
                {getQualityIcon(overallQuality)}
                <span className="ml-2">{overallQuality}%</span>
              </Badge>
            </div>
            <Progress value={overallQuality} className="h-3" />
          </div>

          {/* Deal Information Quality */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Database className="h-5 w-5" />
              Deal Information Quality
            </h3>
            
            <div className="flex items-center justify-between">
              <span>Completeness Score</span>
              <Badge variant="outline" className={getQualityColor(dealQuality)}>
                {dealQuality}%
              </Badge>
            </div>
            
            <Progress value={dealQuality} className="h-3" />
            
            {deal.data_quality_metrics?.deal?.missingFields?.length > 0 && (
              <div className="bg-amber-50 rounded-lg p-4">
                <h4 className="font-medium text-amber-900 mb-2 flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Missing Fields ({deal.data_quality_metrics.deal.missingFields.length})
                </h4>
                <div className="grid grid-cols-2 gap-2">
                  {deal.data_quality_metrics.deal.missingFields.map((field: string) => (
                    <div key={field} className="flex items-center gap-2 text-sm">
                      <AlertTriangle className="h-3 w-3 text-amber-500" />
                      <span className="text-amber-800">{formatFieldName(field)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Investment Criteria Quality */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Target className="h-5 w-5" />
              Investment Criteria Quality
            </h3>
            
            <div className="flex items-center justify-between">
              <span>Overall Criteria Completeness</span>
              <Badge variant="outline" className={getQualityColor(criteriaQuality)}>
                {criteriaQuality}%
              </Badge>
            </div>
            
            <Progress value={criteriaQuality} className="h-3" />

            {/* Individual Criteria Breakdown */}
            {deal.investment_criteria && deal.investment_criteria.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium">Individual Criteria Breakdown</h4>
                {deal.investment_criteria.map((criteria: any, index: number) => (
                  <div key={criteria.criteria_id || index} className="border rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">Criteria #{index + 1}</span>
                      <Badge variant="outline" className={getQualityColor(criteria.quality || 0)}>
                        {criteria.quality || 0}%
                      </Badge>
                    </div>
                    
                    <Progress value={criteria.quality || 0} className="h-2 mb-2" />
                    
                    {criteria.missingFields && criteria.missingFields.length > 0 && (
                      <div className="text-xs text-gray-600">
                        <span className="font-medium">Missing: </span>
                        {criteria.missingFields.slice(0, 3).map(formatFieldName).join(', ')}
                        {criteria.missingFields.length > 3 && ` +${criteria.missingFields.length - 3} more`}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Missing Fields Across All Criteria */}
            {deal.data_quality_metrics?.investment_criteria?.missingFields?.length > 0 && (
              <div className="bg-amber-50 rounded-lg p-4">
                <h4 className="font-medium text-amber-900 mb-2 flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Missing Fields Across All Criteria ({deal.data_quality_metrics.investment_criteria.missingFields.length})
                </h4>
                <div className="grid grid-cols-2 gap-2">
                  {deal.data_quality_metrics.investment_criteria.missingFields.map((field: string) => (
                    <div key={field} className="flex items-center gap-2 text-sm">
                      <AlertTriangle className="h-3 w-3 text-amber-500" />
                      <span className="text-amber-800">{formatFieldName(field)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Quality Recommendations */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
              <Info className="h-4 w-4" />
              Quality Recommendations
            </h4>
            <div className="space-y-2 text-sm text-blue-800">
              {overallQuality < 70 && (
                <p>• This deal has significant data gaps. Consider requesting additional information from the sponsor.</p>
              )}
              {dealQuality < 80 && (
                <p>• Deal information is incomplete. Focus on filling core deal fields like deal name, sponsor, and financial projections.</p>
              )}
              {criteriaQuality < 80 && (
                <p>• Investment criteria needs attention. Ensure all relevant criteria fields are populated for better matching.</p>
              )}
              {overallQuality >= 90 && (
                <p>• Excellent data quality! This deal is well-documented and ready for analysis.</p>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default QualityDetailsModal; 