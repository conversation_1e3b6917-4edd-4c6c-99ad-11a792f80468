"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PlusCircle, Filter } from "lucide-react";
import { DealV2 } from "./shared/types-v2";
import { Deal } from "./shared/types";
import DealCard from "./list-components/DealCard";
import DealCardV1 from "./list-components/DealCardV1";
import Pagination from "@/components/dashboard/deals/list-components/Pagination";
import { motion, AnimatePresence } from "framer-motion";
import { useRouter, useSearchParams } from "next/navigation";
import DealFiltersComponent from "./DealsFilters";
import { InvestmentCriteriaFilters } from "@/types/investment-criteria";

// Define the deal filters interface extending investment criteria
interface DealUnifiedFilters extends InvestmentCriteriaFilters {
  // Deal specific fields
  dealName?: string;
  sponsorName?: string;
  matchType?: string[];
  status?: string[];
  dealStage?: string[];
  priority?: string[];
  reviewStatus?: string[];
  extractionConfidence?: string[];

  // Deal document fields
  documentType?: string[];
  documentSource?: string[];
  extractionMethod?: string[];
  llmProvider?: string[];

  // Deal financial fields
  yieldOnCostMin?: number;
  yieldOnCostMax?: number;
  projectedGpIrrMin?: number;
  projectedGpIrrMax?: number;
  projectedLpIrrMin?: number;
  projectedLpIrrMax?: number;
  projectedGpEmMin?: number;
  projectedGpEmMax?: number;
  projectedLpEmMin?: number;
  projectedLpEmMax?: number;
  projectedTotalIrrMin?: number;
  projectedTotalIrrMax?: number;
  projectedTotalEmMin?: number;
  projectedTotalEmMax?: number;

  // Deal property fields
  zipCode?: string;
  neighborhood?: string;
  lotAreaMin?: number;
  lotAreaMax?: number;
  floorAreaRatioMin?: number;
  floorAreaRatioMax?: number;
  zoningSqFtMin?: number;
  zoningSqFtMax?: number;

  // Deal metadata
  processingDurationMin?: number;
  processingDurationMax?: number;
  documentSizeBytesMin?: number;
  documentSizeBytesMax?: number;

  // Date ranges
  extractionTimestampFrom?: string;
  extractionTimestampTo?: string;
  reviewedAtFrom?: string;
  reviewedAtTo?: string;

  // Pagination and sorting
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export default function DealsView() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get initial pagination and tab state from URL parameters
  const initialPageV2 = Math.max(1, parseInt(searchParams?.get('pageV2') || '1'));
  const initialPageV1 = Math.max(1, parseInt(searchParams?.get('pageV1') || '1'));
  const initialPageSize = Math.max(1, parseInt(searchParams?.get('limit') || '25'));
  const initialTab = (searchParams?.get('tab') as 'v2' | 'v1' | 'all') || 'v2';
  
  // Separate state for V1 and V2 deals
  const [dealsV2, setDealsV2] = useState<DealV2[]>([]);
  const [dealsV1, setDealsV1] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);
  
  // Separate pagination for V1 and V2 - initialized from URL
  const [totalDealsV2, setTotalDealsV2] = useState(0);
  const [totalDealsV1, setTotalDealsV1] = useState(0);
  const [totalPagesV2, setTotalPagesV2] = useState(0);
  const [totalPagesV1, setTotalPagesV1] = useState(0);
  
  // Local state for pagination and tab - initialized from URL
  const [currentPageV2, setCurrentPageV2] = useState(initialPageV2);
  const [currentPageV1, setCurrentPageV1] = useState(initialPageV1);
  const [pageSize, setPageSize] = useState(initialPageSize);
  const [activeTab, setActiveTab] = useState<'v2' | 'v1' | 'all'>(initialTab);
  
  // Track which tabs have been loaded
  const [loadedTabs, setLoadedTabs] = useState<Set<'v2' | 'v1' | 'all'>>(new Set());
  
  const [mappings, setMappings] = useState<any>({});

  // Initialize filters
  const [filters, setFilters] = useState<DealUnifiedFilters>({
    page: 1,
    limit: pageSize,
    sortBy: 'updatedAt',
    sortOrder: 'desc',
  });

  // Load mappings for unified filters
  useEffect(() => {
    async function loadMappings() {
      try {
        const response = await fetch("/api/investment-criteria/filters");
        if (response.ok) {
          const data = await response.json();
          setMappings(data.mappings || {});
        }
      } catch (error) {
        console.error("Failed to load mappings:", error);
      }
    }

    loadMappings();
  }, []);

  // Fetch just the counts for all tabs (lightweight)
  const fetchCounts = async () => {
    try {
      // Fetch V2 count
      const v2Params = new URLSearchParams();
      if (filters.searchTerm) {
        v2Params.append('search', filters.searchTerm);
      }
      if (filters.dealStage?.length) {
        v2Params.append('dealStage', filters.dealStage[0]);
      }
      if (filters.status?.length) {
        v2Params.append('dealStatus', filters.status[0]);
      }
      
      const v2Response = await fetch(`/api/v2/deals?${v2Params.toString()}&page=1&pageSize=1`);
      if (v2Response.ok) {
        const v2Data = await v2Response.json();
        setTotalDealsV2(v2Data.total || 0);
        setTotalPagesV2(v2Data.totalPages || 0);
      }

      // Fetch V1 count
      const v1Params = new URLSearchParams();
      if (filters.searchTerm) {
        v1Params.append('search', filters.searchTerm);
      }
      
      const v1Response = await fetch(`/api/deals?${v1Params.toString()}&page=1&pageSize=1`);
      if (v1Response.ok) {
        const v1Data = await v1Response.json();
        setTotalDealsV1(v1Data.total || 0);
        setTotalPagesV1(Math.ceil((v1Data.total || 0) / pageSize));
      }
    } catch (error) {
      console.error("Error fetching counts:", error);
    }
  };

  // Load initial counts when component mounts
  useEffect(() => {
    fetchCounts();
  }, []);

  // Load initial tab data when component mounts
  useEffect(() => {
    if (initialTab && !loadedTabs.has(initialTab)) {
      handleTabChange(initialTab);
    }
  }, []);

  // Fetch V2 deals function with proper filtering
  const fetchDealsV2 = async (page: number = 1, search: string = '') => {
    try {
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('pageSize', pageSize.toString());
      params.append('sortBy', filters.sortBy || 'updatedAt');
      params.append('sortOrder', filters.sortOrder || 'DESC');
      
      if (search) {
        params.append('search', search);
      }

      // Add other filters that V2 API supports
      if (filters.dealStage?.length) {
        params.append('dealStage', filters.dealStage[0]);
      }
      if (filters.status?.length) {
        params.append('dealStatus', filters.status[0]);
      }

      const response = await fetch(`/api/v2/deals?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        console.log("V2 API Response:", {
          dealsReceived: data.deals?.length || 0,
          total: data.total,
          totalPages: data.totalPages,
        });
        setDealsV2(data.deals || []);
        setTotalDealsV2(data.total || 0);
        setTotalPagesV2(data.totalPages || 0);
      } else {
        console.error("Failed to fetch V2 deals:", await response.text());
        setDealsV2([]);
        setTotalDealsV2(0);
        setTotalPagesV2(0);
      }
    } catch (error) {
      console.error("Error fetching V2 deals:", error);
      setDealsV2([]);
      setTotalDealsV2(0);
      setTotalPagesV2(0);
    }
  };

  // Fetch V1 deals function with proper filtering
  const fetchDealsV1 = async (page: number = 1, search: string = '') => {
    try {
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('pageSize', pageSize.toString());
      params.append('sortBy', filters.sortBy || 'deal_id');
      params.append('sortOrder', filters.sortOrder?.toUpperCase() || 'DESC');
      
      if (search) {
        params.append('search', search);
      }

      const response = await fetch(`/api/deals?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        console.log("V1 API Response:", {
          dealsReceived: data.deals?.length || 0,
          total: data.total,
        });
        setDealsV1(data.deals || []);
        setTotalDealsV1(data.total || 0);
        setTotalPagesV1(Math.ceil((data.total || 0) / pageSize));
      } else {
        console.error("Failed to fetch V1 deals:", await response.text());
        setDealsV1([]);
        setTotalDealsV1(0);
        setTotalPagesV1(0);
      }
    } catch (error) {
      console.error("Error fetching V1 deals:", error);
      setDealsV1([]);
      setTotalDealsV1(0);
      setTotalPagesV1(0);
    }
  };

  // Combined fetch function based on active tab
  const fetchDeals = async () => {
    setLoading(true);
    try {
      if (activeTab === 'v2' || activeTab === 'all') {
        await fetchDealsV2(currentPageV2, filters.searchTerm || '');
      }
      if (activeTab === 'v1' || activeTab === 'all') {
        await fetchDealsV1(currentPageV1, filters.searchTerm || '');
      }
    } finally {
      setLoading(false);
    }
  };

  // Load deals when filters, active tab, or search changes
  useEffect(() => {
    if (loadedTabs.has(activeTab)) {
      fetchDeals();
    }
  }, [activeTab, filters.searchTerm, pageSize, filters.sortBy, filters.sortOrder]);

  // Helper function to update URL and local state
  const updateUrlAndState = useCallback((updates: {
    pageV2?: number | null;
    pageV1?: number | null;
    limit?: number;
    tab?: 'v2' | 'v1' | 'all';
  }) => {
    const url = new URL(window.location.href);
    
    // Update local state and URL
    if (updates.pageV2 !== undefined) {
      if (updates.pageV2 !== null) {
        setCurrentPageV2(updates.pageV2);
        url.searchParams.set('pageV2', updates.pageV2.toString());
      } else {
        url.searchParams.delete('pageV2');
      }
    }
    if (updates.pageV1 !== undefined) {
      if (updates.pageV1 !== null) {
        setCurrentPageV1(updates.pageV1);
        url.searchParams.set('pageV1', updates.pageV1.toString());
      } else {
        url.searchParams.delete('pageV1');
      }
    }
    if (updates.limit !== undefined) {
      setPageSize(updates.limit);
      url.searchParams.set('limit', updates.limit.toString());
    }
    if (updates.tab !== undefined) {
      setActiveTab(updates.tab);
      url.searchParams.set('tab', updates.tab);
    }
    
    // Update URL
    router.push(url.pathname + url.search, { scroll: false });
  }, [router]);

  // Handle filter changes
  const handleFiltersChange = (newFilters: DealUnifiedFilters) => {
    console.log("=== handleFiltersChange CALLED ===");
    console.log("Previous filters:", filters);
    console.log("New filters:", newFilters);
    
    setFilters(newFilters);
    
    // Reset to first page when filters change
    updateUrlAndState({ pageV2: 1, pageV1: 1 });
    
    // Update counts when filters change
    fetchCounts();
    
    // Refresh data for all loaded tabs
    loadedTabs.forEach(tab => {
      if (tab === 'v2' || tab === 'all') {
        fetchDealsV2(1, newFilters.searchTerm || '');
      }
      if (tab === 'v1' || tab === 'all') {
        fetchDealsV1(1, newFilters.searchTerm || '');
      }
    });
  };

  // Handle clear filters
  const handleClearFilters = () => {
    const clearedFilters: DealUnifiedFilters = {
      page: 1,
      limit: pageSize,
      sortBy: 'updatedAt',
      sortOrder: 'desc',
    };
    setFilters(clearedFilters);
    updateUrlAndState({ pageV2: 1, pageV1: 1 });
    
    // Update counts when filters are cleared
    fetchCounts();
    
    // Refresh data for all loaded tabs
    loadedTabs.forEach(tab => {
      if (tab === 'v2' || tab === 'all') {
        fetchDealsV2(1, '');
      }
      if (tab === 'v1' || tab === 'all') {
        fetchDealsV1(1, '');
      }
    });
  };

  // Handle page changes
  const handlePageChangeV2 = (page: number) => {
    updateUrlAndState({ pageV2: page });
    fetchDealsV2(page, filters.searchTerm || '');
  };

  const handlePageChangeV1 = (page: number) => {
    updateUrlAndState({ pageV1: page });
    fetchDealsV1(page, filters.searchTerm || '');
  };

  // Handle page size changes
  const handlePageSizeChange = (newPageSize: number) => {
    updateUrlAndState({ limit: newPageSize, pageV2: 1, pageV1: 1 });
  };

  // Handle tab changes with lazy loading
  const handleTabChange = async (tab: 'v2' | 'v1' | 'all') => {
    // Clear other tab page parameters when switching tabs
    const urlUpdates: any = { tab };
    
    if (tab === 'v2') {
      urlUpdates.pageV2 = currentPageV2;
      urlUpdates.pageV1 = null; // Remove V1 page param
    } else if (tab === 'v1') {
      urlUpdates.pageV1 = currentPageV1;
      urlUpdates.pageV2 = null; // Remove V2 page param
    } else if (tab === 'all') {
      urlUpdates.pageV2 = currentPageV2;
      urlUpdates.pageV1 = currentPageV1;
    }
    
    updateUrlAndState(urlUpdates);
    
    // If this tab hasn't been loaded yet, load it
    if (!loadedTabs.has(tab)) {
      setLoading(true);
      try {
        if (tab === 'v2' || tab === 'all') {
          await fetchDealsV2(currentPageV2, filters.searchTerm || '');
        }
        if (tab === 'v1' || tab === 'all') {
          await fetchDealsV1(currentPageV1, filters.searchTerm || '');
        }
        setLoadedTabs(prev => new Set(prev).add(tab));
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle V2 deal deletion
  const handleDeleteDealV2 = (deletedDealId: string) => {
    setDealsV2((prevDeals) =>
      prevDeals.filter((deal) => String(deal.dealId) !== String(deletedDealId))
    );
    fetchDealsV2(currentPageV2, filters.searchTerm || '');
  };

  // Handle V1 deal deletion
  const handleDeleteDealV1 = (deletedDealId: string) => {
    setDealsV1((prevDeals) =>
      prevDeals.filter((deal) => String(deal.deal_id) !== String(deletedDealId))
    );
    fetchDealsV1(currentPageV1, filters.searchTerm || '');
  };

  // Get current deals and pagination based on active tab
  const getCurrentDeals = () => {
    switch (activeTab) {
      case 'v2':
        return dealsV2;
      case 'v1':
        return dealsV1;
      case 'all':
        return [...dealsV2, ...dealsV1];
      default:
        return [];
    }
  };

  const getCurrentTotal = () => {
    switch (activeTab) {
      case 'v2':
        return totalDealsV2;
      case 'v1':
        return totalDealsV1;
      case 'all':
        return totalDealsV2 + totalDealsV1;
      default:
        return 0;
    }
  };

  const getCurrentTotalPages = () => {
    switch (activeTab) {
      case 'v2':
        return totalPagesV2;
      case 'v1':
        return totalPagesV1;
      case 'all':
        return Math.max(totalPagesV2, totalPagesV1);
      default:
        return 0;
    }
  };

  const getCurrentPage = () => {
    switch (activeTab) {
      case 'v2':
        return currentPageV2;
      case 'v1':
        return currentPageV1;
      case 'all':
        return Math.max(currentPageV2, currentPageV1);
      default:
        return 1;
    }
  };

  const handlePageChange = (page: number) => {
    switch (activeTab) {
      case 'v2':
        handlePageChangeV2(page);
        break;
      case 'v1':
        handlePageChangeV1(page);
        break;
      case 'all':
        // For 'all' tab, change both pages
        updateUrlAndState({ pageV2: page, pageV1: page });
        fetchDealsV2(page, filters.searchTerm || '');
        fetchDealsV1(page, filters.searchTerm || '');
        break;
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <main className="w-full px-6 py-6">
        <header className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Deals</h1>
            <p className="text-gray-600">
              Manage and track your real estate deals
            </p>
            <div className="mt-2 flex items-center gap-2">
              <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                V2 Enhanced
              </Badge>
              <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                V1 Legacy
              </Badge>
              <span className="text-xs text-gray-500">
                Unified view with separate pagination
              </span>
            </div>
          </div>
          <Button
            onClick={() => router.push("/dashboard/deals/upload")}
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white"
          >
            <PlusCircle className="h-4 w-4" />
            Add Deal
          </Button>
        </header>

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => handleTabChange('v2')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'v2'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                V2 Deals ({totalDealsV2})
              </button>
              <button
                onClick={() => handleTabChange('v1')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'v1'
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                V1 Deals ({totalDealsV1})
              </button>
              <button
                onClick={() => handleTabChange('all')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'all'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                All Deals ({totalDealsV2 + totalDealsV1})
              </button>
            </nav>
          </div>
        </div>

        {/* Search Bar - Removed duplicate, using existing search in DealFiltersComponent */}
        
        {/* Deal Filters Component */}
        <DealFiltersComponent
          filters={filters}
          mappings={mappings}
          onFiltersChange={handleFiltersChange}
          onClearFilters={handleClearFilters}
          isLoading={loading}
        />

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        )}

        {/* Deals Grid */}
        {!loading && (
          <>
            <div className="mb-4 flex justify-between items-center">
              <p className="text-sm text-gray-600">
                {getCurrentTotal() === 0
                  ? "No deals found"
                  : `Showing ${getCurrentDeals().length} deals (${totalDealsV2} V2, ${totalDealsV1} V1)`
                }
              </p>
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-500">
                  Active filters: {Object.keys(filters).filter(key => 
                    filters[key as keyof DealUnifiedFilters] && 
                    !['page', 'limit', 'sortBy', 'sortOrder'].includes(key)
                  ).length}
                </span>
              </div>
            </div>

            <AnimatePresence>
              <div className="grid gap-4">
                {/* V2 Deals */}
                {activeTab === 'v2' || activeTab === 'all' ? (
                  dealsV2.map((deal, index) => (
                    <motion.div
                      key={`v2-${deal.dealId}`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.2, delay: index * 0.05 }}
                    >
                      <DealCard 
                        deal={deal} 
                        onDelete={handleDeleteDealV2}
                        onEdit={(deal) => router.push(`/dashboard/deals/v2/${deal.dealId}`)}
                      />
                    </motion.div>
                  ))
                ) : null}
                
                {/* V1 Deals */}
                {activeTab === 'v1' || activeTab === 'all' ? (
                  dealsV1.map((deal, index) => (
                    <motion.div
                      key={`v1-${deal.deal_id}`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.2, delay: (dealsV2.length + index) * 0.05 }}
                    >
                      <DealCardV1 
                        deal={deal} 
                        onDelete={handleDeleteDealV1}
                        onEdit={(deal) => router.push(`/dashboard/deals/${deal.deal_id}`)}
                      />
                    </motion.div>
                  ))
                ) : null}
              </div>
            </AnimatePresence>

            {/* Pagination */}
            {getCurrentTotalPages() > 1 && (
              <div className="mt-8">
                <Pagination
                  currentPage={activeTab === 'v2' ? currentPageV2 : activeTab === 'v1' ? currentPageV1 : Math.max(currentPageV2, currentPageV1)}
                  totalPages={getCurrentTotalPages()}
                  onPageChange={handlePageChange}
                  pageSize={pageSize}
                  onPageSizeChange={handlePageSizeChange}
                  totalItems={getCurrentTotal()}
                />
              </div>
            )}

            {/* Empty State */}
            {getCurrentDeals().length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <PlusCircle className="h-12 w-12 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No deals found
                </h3>
                <p className="text-gray-600 max-w-md mx-auto">
                  {filters.searchTerm 
                    ? `No deals match "${filters.searchTerm}". Try adjusting your search or filters.`
                    : "Try adjusting your filters or search criteria to find more deals, or upload your first deal to get started."
                  }
                </p>
                <Button
                  onClick={() => router.push("/dashboard/deals/upload")}
                  className="mt-4"
                >
                  Upload Deal
                </Button>
              </div>
            )}
          </>
        )}
      </main>
    </div>
  );
}