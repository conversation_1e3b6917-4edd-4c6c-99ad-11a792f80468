import React from "react";
import { Badge } from "@/components/ui/badge";

interface ConflictsDisplayProps {
  getAllConflictsWithAlternatives: () => any[];
}

const ConflictsDisplay: React.FC<ConflictsDisplayProps> = ({
  getAllConflictsWithAlternatives,
}) => {
  const allConflicts = getAllConflictsWithAlternatives();
  
  if (allConflicts.length === 0) {
    return null;
  }

  return (
    <div>
      <h4 className="font-medium text-sm text-gray-700 mb-2">Conflicts with Alternatives ({allConflicts.length})</h4>
      <div className="space-y-2">
        {allConflicts.map((conflict, index) => (
          <div key={index} className="bg-orange-50 border border-orange-200 rounded p-3">
            <div className="flex items-center justify-between mb-1">
              <span className="font-medium text-orange-700 text-sm">
                {conflict.conflictType === 'nsf' 
                  ? `NSF ${conflict.context} - ${conflict.nsfType} - ${conflict.field}`
                  : conflict.fieldName
                }
              </span>
              <Badge variant="outline" className="text-xs text-orange-600">
                {conflict.conflictData.chosen_file}
              </Badge>
            </div>
            <div className="text-xs text-gray-700 mb-1">
              <strong>Chosen:</strong> {JSON.stringify(conflict.conflictData.chosen_value)}
            </div>
            <div className="text-xs text-orange-600 mb-1">
              <strong>Reason:</strong> {conflict.conflictData.reason}
            </div>
            <div className="text-xs text-gray-600">
              <strong>Alternatives:</strong> {conflict.conflictData.alternatives.length} found
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ConflictsDisplay;
