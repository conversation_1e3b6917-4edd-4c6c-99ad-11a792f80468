import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, CheckCircle, XCircle, AlertCircle, Database, Target, BarChart3 } from 'lucide-react';
import { useDealQuality } from '@/hooks/useDealQuality';

interface DealQualityDetailsProps {
  dealId: number;
  dealName?: string;
}

const DealQualityDetails: React.FC<DealQualityDetailsProps> = ({ dealId, dealName }) => {
  const { qualityData, loading, error } = useDealQuality(dealId);
  const [expandedSections, setExpandedSections] = useState<{
    deal: boolean;
    criteria: boolean;
    [key: number]: boolean; // Individual criteria IDs
  }>({
    deal: false,
    criteria: false
  });

  const toggleSection = (section: string | number) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const getQualityColor = (score: number) => {
    if (score >= 90) return 'bg-emerald-100 text-emerald-800 border-emerald-200';
    if (score >= 80) return 'bg-blue-100 text-blue-800 border-blue-200';
    if (score >= 70) return 'bg-amber-100 text-amber-800 border-amber-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  const getQualityIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="h-4 w-4 text-emerald-600" />;
    if (score >= 80) return <CheckCircle className="h-4 w-4 text-blue-600" />;
    if (score >= 70) return <AlertCircle className="h-4 w-4 text-amber-600" />;
    return <XCircle className="h-4 w-4 text-red-600" />;
  };

  const formatValue = (value: any): string => {
    if (value === null || value === undefined || value === '') return 'Not provided';
    if (Array.isArray(value)) return value.length > 0 ? value.join(', ') : 'Not provided';
    if (typeof value === 'number') {
      // Format percentages
      if (value <= 1) return `${(value * 100).toFixed(1)}%`;
      return value.toLocaleString();
    }
    return String(value);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading quality data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center text-red-600">
            <XCircle className="h-5 w-5 mr-2" />
            <span>Error loading quality data: {error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!qualityData) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">No quality data available</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Overall Quality Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Overall Data Quality
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <span className="text-lg font-medium">Overall Completion</span>
            <Badge variant="outline" className={getQualityColor(qualityData.overall_quality)}>
              {qualityData.overall_quality}%
            </Badge>
          </div>
          <Progress value={qualityData.overall_quality} className="h-3 mb-4" />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-900">Deal Information</span>
                </div>
                <Badge variant="outline" className="text-blue-700 bg-blue-100 border-blue-300">
                  {qualityData.deal.qualityScore}%
                </Badge>
              </div>
              <div className="text-sm text-blue-700">
                {qualityData.deal.completedFields}/{qualityData.deal.totalFields} fields completed
              </div>
            </div>

            <div className="bg-green-50 rounded-lg p-3 border border-green-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-green-900">Investment Criteria</span>
                </div>
                <Badge variant="outline" className="text-green-700 bg-green-100 border-green-300">
                  {qualityData.investment_criteria.qualityScore}%
                </Badge>
              </div>
              <div className="text-sm text-green-700">
                {qualityData.investment_criteria.completedFields}/{qualityData.investment_criteria.totalFields} fields completed
                {qualityData.investment_criteria.criteriaCount > 0 && (
                  <span className="ml-1">({qualityData.investment_criteria.criteriaCount} criteria)</span>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Deal Information Details */}
      <Card>
        <Collapsible open={expandedSections.deal} onOpenChange={() => toggleSection('deal')}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Deal Information Details
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={getQualityColor(qualityData.deal.qualityScore)}>
                    {qualityData.deal.qualityScore}%
                  </Badge>
                  {expandedSections.deal ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="space-y-3">
                {qualityData.deal.fieldDetails.map((field) => (
                  <div key={field.field} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {field.hasValue ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      <div>
                        <div className="font-medium">{field.displayName}</div>
                        <div className="text-sm text-gray-600">
                          {field.hasValue ? formatValue(field.value) : 'Not provided'}
                        </div>
                      </div>
                    </div>
                    <Badge variant={field.hasValue ? "default" : "secondary"}>
                      {field.hasValue ? 'Complete' : 'Missing'}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Investment Criteria Details */}
      <Card>
        <Collapsible open={expandedSections.criteria} onOpenChange={() => toggleSection('criteria')}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Investment Criteria Details
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={getQualityColor(qualityData.investment_criteria.qualityScore)}>
                    {qualityData.investment_criteria.qualityScore}%
                  </Badge>
                  {expandedSections.criteria ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="space-y-4">
                {qualityData.investment_criteria.individualCriteria.map((criteria) => (
                  <Card key={criteria.criteria_id} className="border-l-4 border-l-blue-500">
                    <Collapsible open={expandedSections[criteria.criteria_id]} onOpenChange={() => toggleSection(criteria.criteria_id)}>
                      <CollapsibleTrigger asChild>
                        <CardHeader className="cursor-pointer hover:bg-gray-50">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              {getQualityIcon(criteria.qualityScore)}
                              <div>
                                <div className="font-medium">
                                  Criteria #{criteria.criteria_id}
                                  {criteria.capital_position && criteria.capital_position.length > 0 && (
                                    <span className="ml-2 text-sm text-gray-600">
                                      ({criteria.capital_position.join(', ')})
                                    </span>
                                  )}
                                </div>
                                <div className="text-sm text-gray-600">
                                  {criteria.completedFields}/{criteria.totalFields} fields completed
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className={getQualityColor(criteria.qualityScore)}>
                                {criteria.qualityScore}%
                              </Badge>
                              {expandedSections[criteria.criteria_id] ? (
                                <ChevronDown className="h-4 w-4" />
                              ) : (
                                <ChevronRight className="h-4 w-4" />
                              )}
                            </div>
                          </div>
                        </CardHeader>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <CardContent>
                          <div className="space-y-3">
                            {criteria.fieldDetails.map((field) => (
                              <div key={field.field} className="flex items-center justify-between p-2 border rounded">
                                <div className="flex items-center gap-2">
                                  {field.hasValue ? (
                                    <CheckCircle className="h-3 w-3 text-green-600" />
                                  ) : (
                                    <XCircle className="h-3 w-3 text-red-600" />
                                  )}
                                  <span className="text-sm font-medium">{field.displayName}</span>
                                </div>
                                <div className="text-sm text-gray-600">
                                  {field.hasValue ? formatValue(field.value) : 'Not provided'}
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </CollapsibleContent>
                    </Collapsible>
                  </Card>
                ))}
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  );
};

export default DealQualityDetails; 