"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { 
  Save, 
  X, 
  Plus, 
  Trash2, 
  DollarSign, 
  MapPin, 
  Building2, 
  Target,
  TrendingUp,
  Percent,
  Calendar,
  FileText
} from "lucide-react";
import { Deal, InvestmentCriteria } from "./shared/types";

interface DealEditFormProps {
  deal: Deal;
  onSave: (updatedDeal: Deal) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const DealEditForm: React.FC<DealEditFormProps> = ({ 
  deal, 
  onSave, 
  onCancel, 
  isLoading = false 
}) => {
  const [editedDeal, setEditedDeal] = useState<Deal>({ ...deal });
  const [editedCriteria, setEditedCriteria] = useState<InvestmentCriteria[]>(
    deal.investment_criteria || []
  );
  const [deletedCriteriaIds, setDeletedCriteriaIds] = useState<number[]>([]);

  // Helper function to format date for HTML date input
  const formatDateForInput = (dateString: string | null): string => {
    if (!dateString || dateString === null) return "";
    // If it's already in YYYY-MM-DD format, return as is
    if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) return dateString;
    // If it's a full timestamp, extract just the date part
    if (dateString.includes('T')) return dateString.split('T')[0];
    // Otherwise, try to parse as date and format
    try {
      return new Date(dateString).toISOString().split('T')[0];
    } catch {
      return "";
    }
  };

  // Helper function to handle array field changes
  const handleArrayFieldChange = (
    field: keyof InvestmentCriteria,
    value: string,
    index: number,
    criteriaIndex: number
  ) => {
    const updatedCriteria = [...editedCriteria];
    const fieldValue = updatedCriteria[criteriaIndex][field];
    const currentArray = Array.isArray(fieldValue) ? [...fieldValue] : [];
    
    if (index >= currentArray.length) {
      // Add new item
      currentArray.push(value);
    } else {
      // Update existing item
      currentArray[index] = value;
    }
    
    updatedCriteria[criteriaIndex] = {
      ...updatedCriteria[criteriaIndex],
      [field]: currentArray
    };
    
    setEditedCriteria(updatedCriteria);
  };
console.log(editedDeal,'hh')
  // Helper function to remove array item
  const removeArrayItem = (
    field: keyof InvestmentCriteria,
    index: number,
    criteriaIndex: number
  ) => {
    const updatedCriteria = [...editedCriteria];
    const fieldValue = updatedCriteria[criteriaIndex][field];
    const currentArray = Array.isArray(fieldValue) ? [...fieldValue] : [];
    currentArray.splice(index, 1);
    
    updatedCriteria[criteriaIndex] = {
      ...updatedCriteria[criteriaIndex],
      [field]: currentArray
    };
    
    setEditedCriteria(updatedCriteria);
  };

  // Helper function to add new array item
  const addArrayItem = (
    field: keyof InvestmentCriteria,
    criteriaIndex: number
  ) => {
    const updatedCriteria = [...editedCriteria];
    const fieldValue = updatedCriteria[criteriaIndex][field];
    const currentArray = Array.isArray(fieldValue) ? [...fieldValue] : [];
    currentArray.push("");
    
    updatedCriteria[criteriaIndex] = {
      ...updatedCriteria[criteriaIndex],
      [field]: currentArray
    };
    
    setEditedCriteria(updatedCriteria);
  };

  // Add new investment criteria
  const addInvestmentCriteria = () => {
    const newCriteria: InvestmentCriteria = {
      criteria_id: 0, // Use 0 to indicate new criteria
      entity_type: "Deal",
      entity_id: deal.deal_id.toString(),
      target_return: null,
      property_types: [],
      property_sub_categories: [],
      strategies: [],
      asset_classes: [],
      minimum_deal_size: null,
      maximum_deal_size: null,
      min_hold_period: null,
      max_hold_period: null,
      financial_products: [],
      historical_irr: null,
      historical_em: null,
      country: [],
      region: [],
      state: [],
      city: [],
      loan_program: [],
      loan_type: [],
      capital_type: null,
      capital_position: [],
      capital_source: null,
      structured_loan_tranche: [],
      min_loan_term: null,
      max_loan_term: null,
      interest_rate: null,
      loan_interest_rate: null,
      loan_interest_rate_sofr: null,
      loan_interest_rate_wsj: null,
      loan_interest_rate_prime: null,
      loan_ltv: null,
      loan_ltc: null,
      loan_to_value_min: null,
      loan_to_value_max: null,
      loan_to_cost_min: null,
      loan_to_cost_max: null,
      loan_origination_fee: null,
      loan_origination_fee_min: null,
      loan_origination_fee_max: null,
      loan_exit_fee: null,
      loan_exit_fee_min: null,
      loan_exit_fee_max: null,
      min_loan_dscr: null,
      max_loan_dscr: null,
      recourse_loan: null,
      extra_fields: null,
      created_at: null,
      updated_at: null,
      created_by: null,
      updated_by: null,
      is_active: true,
      is_requested: false,
      notes: null
    };
    
    setEditedCriteria([...editedCriteria, newCriteria]);
  };

  // Reset form state
  const resetFormState = () => {
    setEditedCriteria(deal.investment_criteria || []);
    setDeletedCriteriaIds([]);
  };

  // Remove investment criteria
  const removeInvestmentCriteria = (index: number) => {
    const criteriaToRemove = editedCriteria[index];
    
    console.log('Removing criteria:', criteriaToRemove);
    
    // If this criteria has a real criteria_id (not a temporary one), add it to deleted list
    if (criteriaToRemove.criteria_id && criteriaToRemove.criteria_id > 1000) { // Assuming temporary IDs are < 1000
      console.log('Adding to deleted criteria IDs:', criteriaToRemove.criteria_id);
      setDeletedCriteriaIds(prev => [...prev, criteriaToRemove.criteria_id]);
    } else {
      console.log('Criteria has temporary ID, not adding to deleted list');
    }
    
    // Remove from edited criteria
    setEditedCriteria(editedCriteria.filter((_, i) => i !== index));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Check if there are any new criteria (criteria_id = 0)
      const hasNewCriteria = editedCriteria.some(criteria => criteria.criteria_id === 0);
      
      // Update the deal with edited criteria and deleted criteria IDs
      const updatedDeal = {
        ...editedDeal,
        investment_criteria: editedCriteria,
        deleted_criteria_ids: deletedCriteriaIds,
        has_new_criteria: hasNewCriteria
      };
      
      console.log('Submitting deal with:', {
        editedCriteria: editedCriteria.length,
        deletedCriteriaIds: deletedCriteriaIds,
        updatedDeal
      });
      
      onSave(updatedDeal);
    } catch (error) {
      console.error("Error saving deal:", error);
      toast.error("Failed to save deal");
    }
  };

  // Render array field input
  const renderArrayField = (
    field: keyof InvestmentCriteria,
    label: string,
    criteriaIndex: number,
    placeholder?: string
  ) => {
    const fieldValue = editedCriteria[criteriaIndex][field];
    const values = Array.isArray(fieldValue) ? fieldValue : [];
    
    return (
      <div className="space-y-2">
        <Label>{label}</Label>
        {values.map((value, index) => (
          <div key={index} className="flex gap-2">
            <Input
              value={value}
              onChange={(e) => handleArrayFieldChange(field, e.target.value, index, criteriaIndex)}
              placeholder={placeholder}
            />
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => removeArrayItem(field, index, criteriaIndex)}
              className="px-2"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        ))}
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => addArrayItem(field, criteriaIndex)}
          className="w-full"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add {label}
        </Button>
      </div>
    );
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Edit Deal</h2>
        <div className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              resetFormState();
              onCancel();
            }}
            disabled={isLoading}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="deal-info" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="deal-info">Deal Information</TabsTrigger>
          <TabsTrigger value="financial">Financial Data</TabsTrigger>
          <TabsTrigger value="criteria">Investment Criteria</TabsTrigger>
        </TabsList>

        {/* Deal Information Tab */}
        <TabsContent value="deal-info" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="deal_name">Deal Name</Label>
                  <Input
                    id="deal_name"
                    value={editedDeal.deal_name || ""}
                    onChange={(e) => setEditedDeal({ ...editedDeal, deal_name: e.target.value })}
                    placeholder="Enter deal name"
                  />
                </div>
                <div>
                  <Label htmlFor="sponsor_name">Sponsor Name</Label>
                  <Input
                    id="sponsor_name"
                    value={editedDeal.sponsor_name || ""}
                    onChange={(e) => setEditedDeal({ ...editedDeal, sponsor_name: e.target.value })}
                    placeholder="Enter sponsor name"
                  />
                </div>
                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={editedDeal.status || ""}
                    onValueChange={(value) => setEditedDeal({ ...editedDeal, status: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="closed">Closed</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="deal_stage">Deal Stage</Label>
                  <Select
                    value={editedDeal.deal_stage || ""}
                    onValueChange={(value) => setEditedDeal({ ...editedDeal, deal_stage: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select deal stage" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pre-development">Pre-Development</SelectItem>
                      <SelectItem value="development">Development</SelectItem>
                      <SelectItem value="construction">Construction</SelectItem>
                      <SelectItem value="stabilized">Stabilized</SelectItem>
                      <SelectItem value="value-add">Value-Add</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="priority">Priority</Label>
                  <Select
                    value={editedDeal.priority || ""}
                    onValueChange={(value) => setEditedDeal({ ...editedDeal, priority: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="match_type">Match Type</Label>
                  <Select
                    value={editedDeal.match_type || ""}
                    onValueChange={(value) => setEditedDeal({ ...editedDeal, match_type: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select match type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="exact">Exact</SelectItem>
                      <SelectItem value="partial">Partial</SelectItem>
                      <SelectItem value="potential">Potential</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="deal_date">Deal Date</Label>
                  <Input
                    id="deal_date"
                    type="date"
                    value={formatDateForInput(editedDeal.deal_date)}
                    onChange={(e) => setEditedDeal({ ...editedDeal, deal_date: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label>Deal Flags</Label>
                  <div className="space-y-2 mt-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="is_distressed"
                        checked={editedDeal.is_distressed || false}
                        onCheckedChange={(checked) => 
                          setEditedDeal({ ...editedDeal, is_distressed: checked as boolean })
                        }
                      />
                      <Label htmlFor="is_distressed" className="text-sm font-normal">
                        Distressed Deal
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="is_internal_only"
                        checked={editedDeal.is_internal_only || false}
                        onCheckedChange={(checked) => 
                          setEditedDeal({ ...editedDeal, is_internal_only: checked as boolean })
                        }
                      />
                      <Label htmlFor="is_internal_only" className="text-sm font-normal">
                        Internal Only (Do Not Sync with CRM)
                      </Label>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="property_description">Property Description</Label>
                <Textarea
                  id="property_description"
                  value={editedDeal.property_description || ""}
                  onChange={(e) => setEditedDeal({ ...editedDeal, property_description: e.target.value })}
                  placeholder="Enter property description"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Location Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    value={editedDeal.address || ""}
                    onChange={(e) => setEditedDeal({ ...editedDeal, address: e.target.value })}
                    placeholder="Enter property address"
                  />
                </div>
                <div>
                  <Label htmlFor="zip_code">Zip Code</Label>
                  <Input
                    id="zip_code"
                    value={editedDeal.zip_code || ""}
                    onChange={(e) => setEditedDeal({ ...editedDeal, zip_code: e.target.value })}
                    placeholder="Enter zip code"
                  />
                </div>
                <div>
                  <Label htmlFor="neighborhood">Neighborhood</Label>
                  <Input
                    id="neighborhood"
                    value={editedDeal.neighborhood || ""}
                    onChange={(e) => setEditedDeal({ ...editedDeal, neighborhood: e.target.value })}
                    placeholder="Enter neighborhood"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Property Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="lot_area">Lot Area (sq ft)</Label>
                  <Input
                    id="lot_area"
                    type="number"
                    value={editedDeal.lot_area || ""}
                    onChange={(e) => setEditedDeal({ ...editedDeal, lot_area: e.target.value })}
                    placeholder="Enter lot area"
                  />
                </div>
                <div>
                  <Label htmlFor="floor_area_ratio">Floor Area Ratio</Label>
                  <Input
                    id="floor_area_ratio"
                    type="number"
                    step="0.01"
                    value={editedDeal.floor_area_ratio || ""}
                    onChange={(e) => setEditedDeal({ ...editedDeal, floor_area_ratio: e.target.value })}
                    placeholder="Enter FAR"
                  />
                </div>
                <div>
                  <Label htmlFor="zoning_square_footage">Zoning Square Footage</Label>
                  <Input
                    id="zoning_square_footage"
                    type="number"
                    value={editedDeal.zoning_square_footage || ""}
                    onChange={(e) => setEditedDeal({ ...editedDeal, zoning_square_footage: e.target.value })}
                    placeholder="Enter zoning sq ft"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Financial Data Tab */}
        <TabsContent value="financial" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Financial Projections
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="yield_on_cost">Yield on Cost (%)</Label>
                  <Input
                    id="yield_on_cost"
                    type="number"
                    step="0.01"
                    value={editedDeal.yield_on_cost || ""}
                    onChange={(e) => setEditedDeal({ ...editedDeal, yield_on_cost: parseFloat(e.target.value) || null })}
                    placeholder="Enter yield on cost"
                  />
                </div>
                <div>
                  <Label htmlFor="projected_total_irr">Projected Total IRR (%)</Label>
                  <Input
                    id="projected_total_irr"
                    type="number"
                    step="0.01"
                    value={editedDeal.projected_total_irr || ""}
                    onChange={(e) => setEditedDeal({ ...editedDeal, projected_total_irr: parseFloat(e.target.value) || null })}
                    placeholder="Enter projected total IRR"
                  />
                </div>
                <div>
                  <Label htmlFor="projected_gp_irr">Projected GP IRR (%)</Label>
                  <Input
                    id="projected_gp_irr"
                    type="number"
                    step="0.01"
                    value={editedDeal.projected_gp_irr || ""}
                    onChange={(e) => setEditedDeal({ ...editedDeal, projected_gp_irr: parseFloat(e.target.value) || null })}
                    placeholder="Enter projected GP IRR"
                  />
                </div>
                <div>
                  <Label htmlFor="projected_lp_irr">Projected LP IRR (%)</Label>
                  <Input
                    id="projected_lp_irr"
                    type="number"
                    step="0.01"
                    value={editedDeal.projected_lp_irr || ""}
                    onChange={(e) => setEditedDeal({ ...editedDeal, projected_lp_irr: parseFloat(e.target.value) || null })}
                    placeholder="Enter projected LP IRR"
                  />
                </div>
                <div>
                  <Label htmlFor="projected_gp_equity_multiple">Projected GP Equity Multiple</Label>
                  <Input
                    id="projected_gp_equity_multiple"
                    type="number"
                    step="0.01"
                    value={editedDeal.projected_gp_equity_multiple || ""}
                    onChange={(e) => setEditedDeal({ ...editedDeal, projected_gp_equity_multiple: parseFloat(e.target.value) || null })}
                    placeholder="Enter projected GP equity multiple"
                  />
                </div>
                <div>
                  <Label htmlFor="projected_lp_equity_multiple">Projected LP Equity Multiple</Label>
                  <Input
                    id="projected_lp_equity_multiple"
                    type="number"
                    step="0.01"
                    value={editedDeal.projected_lp_equity_multiple || ""}
                    onChange={(e) => setEditedDeal({ ...editedDeal, projected_lp_equity_multiple: parseFloat(e.target.value) || null })}
                    placeholder="Enter projected LP equity multiple"
                  />
                </div>
                <div>
                  <Label htmlFor="projected_total_equity_multiple">Projected Total Equity Multiple</Label>
                  <Input
                    id="projected_total_equity_multiple"
                    type="number"
                    step="0.01"
                    value={editedDeal.projected_total_equity_multiple || ""}
                    onChange={(e) => setEditedDeal({ ...editedDeal, projected_total_equity_multiple: parseFloat(e.target.value) || null })}
                    placeholder="Enter projected total equity multiple"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Investment Criteria Tab */}
        <TabsContent value="criteria" className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Investment Criteria</h3>
            <Button
              type="button"
              variant="outline"
              onClick={addInvestmentCriteria}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Criteria
            </Button>
          </div>

          {deletedCriteriaIds.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-center gap-2 text-red-700">
                <Trash2 className="h-4 w-4" />
                <span className="font-medium">
                  {deletedCriteriaIds.length} criteria marked for deletion
                </span>
              </div>
              <p className="text-sm text-red-600 mt-1">
                These criteria will be permanently deleted when you save the deal.
              </p>
            </div>
          )}

          {editedCriteria.map((criteria, criteriaIndex) => (
            <Card key={criteriaIndex}>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Investment Criteria {criteriaIndex + 1}
                  </CardTitle>
                  {editedCriteria.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeInvestmentCriteria(criteriaIndex)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Remove
                    </Button>
                  )}

                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Basic Criteria */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor={`target_return_${criteriaIndex}`}>Target Return (%)</Label>
                    <Input
                      id={`target_return_${criteriaIndex}`}
                      type="number"
                      step="0.01"
                      value={criteria.target_return || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          target_return: parseFloat(e.target.value) || null
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter target return"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`historical_irr_${criteriaIndex}`}>Historical IRR (%)</Label>
                    <Input
                      id={`historical_irr_${criteriaIndex}`}
                      type="number"
                      step="0.01"
                      value={criteria.historical_irr || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          historical_irr: parseFloat(e.target.value) || null
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter historical IRR"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`historical_em_${criteriaIndex}`}>Historical EM</Label>
                    <Input
                      id={`historical_em_${criteriaIndex}`}
                      type="number"
                      step="0.01"
                      value={criteria.historical_em || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          historical_em: parseFloat(e.target.value) || null
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter historical EM"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`minimum_deal_size_${criteriaIndex}`}>Minimum Deal Size</Label>
                    <Input
                      id={`minimum_deal_size_${criteriaIndex}`}
                      type="number"
                      value={criteria.minimum_deal_size || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          minimum_deal_size: e.target.value
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter minimum deal size"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`maximum_deal_size_${criteriaIndex}`}>Maximum Deal Size</Label>
                    <Input
                      id={`maximum_deal_size_${criteriaIndex}`}
                      type="number"
                      value={criteria.maximum_deal_size || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          maximum_deal_size: e.target.value
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter maximum deal size"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`min_hold_period_${criteriaIndex}`}>Min Hold Period (months)</Label>
                    <Input
                      id={`min_hold_period_${criteriaIndex}`}
                      type="number"
                      value={criteria.min_hold_period || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          min_hold_period: parseInt(e.target.value) || null
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter min hold period"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`max_hold_period_${criteriaIndex}`}>Max Hold Period (months)</Label>
                    <Input
                      id={`max_hold_period_${criteriaIndex}`}
                      type="number"
                      value={criteria.max_hold_period || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          max_hold_period: parseInt(e.target.value) || null
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter max hold period"
                    />
                  </div>
                </div>

                {/* Array Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {renderArrayField("property_types", "Property Types", criteriaIndex, "e.g., Multifamily")}
                  {renderArrayField("property_sub_categories", "Property Sub Categories", criteriaIndex, "e.g., Class A")}
                  {renderArrayField("strategies", "Strategies", criteriaIndex, "e.g., Value-Add")}
                  {renderArrayField("financial_products", "Financial Products", criteriaIndex, "e.g., Bridge Loan")}
                  {renderArrayField("capital_position", "Capital Position", criteriaIndex, "e.g., Equity")}
                  {renderArrayField("capital_source", "Capital Source", criteriaIndex, "e.g., Institutional")}
                  {renderArrayField("loan_type", "Loan Types", criteriaIndex, "e.g., Senior Debt")}
                  {renderArrayField("loan_program", "Loan Program", criteriaIndex, "e.g., Fannie Mae")}
                  {renderArrayField("country", "Countries", criteriaIndex, "e.g., United States")}
                  {renderArrayField("region", "Regions", criteriaIndex, "e.g., West Coast")}
                  {renderArrayField("state", "States", criteriaIndex, "e.g., California")}
                  {renderArrayField("city", "Cities", criteriaIndex, "e.g., Los Angeles")}
                </div>

                {/* Loan Terms */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor={`min_loan_term_${criteriaIndex}`}>Min Loan Term (months)</Label>
                    <Input
                      id={`min_loan_term_${criteriaIndex}`}
                      type="number"
                      value={criteria.min_loan_term || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          min_loan_term: parseInt(e.target.value) || null
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter min loan term"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`max_loan_term_${criteriaIndex}`}>Max Loan Term (months)</Label>
                    <Input
                      id={`max_loan_term_${criteriaIndex}`}
                      type="number"
                      value={criteria.max_loan_term || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          max_loan_term: parseInt(e.target.value) || null
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter max loan term"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`interest_rate_${criteriaIndex}`}>Interest Rate (%)</Label>
                    <Input
                      id={`interest_rate_${criteriaIndex}`}
                      type="number"
                      step="0.01"
                      value={criteria.interest_rate || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          interest_rate: parseFloat(e.target.value) || null
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter interest rate"
                    />
                  </div>
                </div>

                {/* LTV/LTC */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <Label htmlFor={`loan_to_value_min_${criteriaIndex}`}>Min LTV (%)</Label>
                    <Input
                      id={`loan_to_value_min_${criteriaIndex}`}
                      type="number"
                      step="0.01"
                      value={criteria.loan_to_value_min || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          loan_to_value_min: parseFloat(e.target.value) || null
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter min LTV"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`loan_to_value_max_${criteriaIndex}`}>Max LTV (%)</Label>
                    <Input
                      id={`loan_to_value_max_${criteriaIndex}`}
                      type="number"
                      step="0.01"
                      value={criteria.loan_to_value_max || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          loan_to_value_max: parseFloat(e.target.value) || null
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter max LTV"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`loan_to_cost_min_${criteriaIndex}`}>Min LTC (%)</Label>
                    <Input
                      id={`loan_to_cost_min_${criteriaIndex}`}
                      type="number"
                      step="0.01"
                      value={criteria.loan_to_cost_min || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          loan_to_cost_min: parseFloat(e.target.value) || null
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter min LTC"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`loan_to_cost_max_${criteriaIndex}`}>Max LTC (%)</Label>
                    <Input
                      id={`loan_to_cost_max_${criteriaIndex}`}
                      type="number"
                      step="0.01"
                      value={criteria.loan_to_cost_max || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          loan_to_cost_max: parseFloat(e.target.value) || null
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter max LTC"
                    />
                  </div>
                </div>

                {/* DSCR */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor={`min_loan_dscr_${criteriaIndex}`}>Min DSCR</Label>
                    <Input
                      id={`min_loan_dscr_${criteriaIndex}`}
                      type="number"
                      step="0.01"
                      value={criteria.min_loan_dscr || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          min_loan_dscr: parseFloat(e.target.value) || null
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter min DSCR"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`max_loan_dscr_${criteriaIndex}`}>Max DSCR</Label>
                    <Input
                      id={`max_loan_dscr_${criteriaIndex}`}
                      type="number"
                      step="0.01"
                      value={criteria.max_loan_dscr || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          max_loan_dscr: parseFloat(e.target.value) || null
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter max DSCR"
                    />
                  </div>
                </div>

                {/* Additional Loan Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor={`structured_loan_tranche_${criteriaIndex}`}>Structured Loan Tranche</Label>
                    <Input
                      id={`structured_loan_tranche_${criteriaIndex}`}
                      value={criteria.structured_loan_tranche || ""}
                      onChange={(e) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          structured_loan_tranche: e.target.value
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                      placeholder="Enter structured loan tranche"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`recourse_loan_${criteriaIndex}`}>Recourse Loan</Label>
                    <Select
                      value={criteria.recourse_loan || ""}
                      onValueChange={(value) => {
                        const updatedCriteria = [...editedCriteria];
                        updatedCriteria[criteriaIndex] = {
                          ...updatedCriteria[criteriaIndex],
                          recourse_loan: value
                        };
                        setEditedCriteria(updatedCriteria);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select recourse option" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="recourse">Recourse</SelectItem>
                        <SelectItem value="non-recourse">Non-Recourse</SelectItem>
                        <SelectItem value="partial-recourse">Partial Recourse</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Notes */}
                <div>
                  <Label htmlFor={`notes_${criteriaIndex}`}>Notes</Label>
                  <Textarea
                    id={`notes_${criteriaIndex}`}
                    value={criteria.notes || ""}
                    onChange={(e) => {
                      const updatedCriteria = [...editedCriteria];
                      updatedCriteria[criteriaIndex] = {
                        ...updatedCriteria[criteriaIndex],
                        notes: e.target.value
                      };
                      setEditedCriteria(updatedCriteria);
                    }}
                    placeholder="Enter additional notes"
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </form>
  );
};

export default DealEditForm; 