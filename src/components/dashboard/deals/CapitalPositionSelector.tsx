import React, { useMemo } from "react";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { DealV2 } from "./shared/types-v2";

interface CapitalPositionSelectorProps {
  currentDeal: DealV2 | null;
  isEditing: boolean;
  onFieldChange: (field: string, value: any) => void;
  updateIsRequiredForNsfFields: (positions: string[]) => void;
  capitalPositionOptions?: any[];
  loading?: boolean;
}

const CapitalPositionSelector: React.FC<CapitalPositionSelectorProps> = ({
  currentDeal,
  isEditing,
  onFieldChange,
  updateIsRequiredForNsfFields,
  capitalPositionOptions = [],
  loading = false,
}) => {
  // Get all unique capital positions from NSF fields or use provided options
  // Prioritize provided options, then NSF fields (SOURCES ONLY)
  const allOptions = useMemo(() => {
    return capitalPositionOptions.map(option => {
      return option.sourceType || option.capitalPosition || 'Unknown'
    })
  }, [capitalPositionOptions])
  
  const selectedOptions = useMemo(() => {
    return currentDeal?.nsfFields?.filter(nsf => nsf.nsfContext === 'sources' && nsf.isRequired)
    .map(nsf => nsf.sourceType || 'Unknown')
  }, [currentDeal?.nsfFields])

  if (!isEditing) {
    return (
      <div className="space-y-1">
        {selectedOptions&& selectedOptions.length > 0 ? (
          selectedOptions.map((position, index) => (
            <div key={index} className="flex items-center justify-between">
              <span className="text-sm text-gray-900">{position}</span>
              <Badge variant="outline" className="text-xs">
                Selected
              </Badge>
            </div>
          ))
        ) : (
          <p className="text-sm text-gray-500">No capital positions selected</p>
        )}
        <p className="text-xs text-gray-500 mt-2">
          Only selected positions above will be marked as required in NSF sources
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex flex-wrap gap-2">
        {allOptions.map((position) => {
          const isSelected = (currentDeal?.askCapitalPosition || []).includes(position);
          return (
            <div
              key={position}
              className={`flex items-center gap-2 px-3 py-2 rounded-md border cursor-pointer transition-colors ${
                isSelected
                  ? 'bg-blue-100 border-blue-300 text-blue-800'
                  : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
              }`}
              onClick={() => {
                const currentPositions = selectedOptions || [];
                const updatedPositions = isSelected
                  ? currentPositions.filter(pos => pos !== position)
                  : [...currentPositions, position];
                onFieldChange('askCapitalPosition', updatedPositions);
                // Update isRequired for NSF fields based on new ask capital position
                updateIsRequiredForNsfFields(updatedPositions.filter((pos): pos is string => pos !== undefined));
              }}
            >
              <span className="text-sm font-medium">{position}</span>
              {isSelected && (
                <Badge variant="secondary" className="text-xs">
                  Selected
                </Badge>
              )}
            </div>
          );
        })}
      </div>
              {allOptions.length === 0 && (
          <div className="text-sm text-gray-500">
            <p>No capital positions available from NSF source fields</p>
            <p className="text-xs mt-1">Debug: NSF Source Fields count: {currentDeal?.nsfFields?.filter(nsf => nsf.nsfContext === 'sources').length || 0}</p>
            {currentDeal?.nsfFields?.filter(nsf => nsf.nsfContext === 'sources').map((nsf, index) => (
              <p key={index} className="text-xs text-gray-400">
                Source {index}: sourceType={nsf.sourceType}
              </p>
            ))}
          </div>
        )}
        
        {/* Show current NSF sources and their required status */}
        {currentDeal?.nsfFields && currentDeal.nsfFields.filter(nsf => nsf.nsfContext === 'sources').length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Current NSF Sources Status:</h4>
            <div className="space-y-1">
              {currentDeal.nsfFields
                .filter(nsf => nsf.nsfContext === 'sources')
                .map((nsf, index) => {
                  const capitalPosition = nsf.sourceType || 'Unknown';
                  const isSelected = currentDeal.askCapitalPosition?.includes(capitalPosition);
                  return (
                    <div key={index} className="flex items-center justify-between text-xs">
                      <span className="text-gray-600">{capitalPosition}: {nsf.amount ? `$${(nsf.amount / 1000000).toFixed(1)}M` : 'No amount'}</span>
                      <Badge variant={isSelected ? "default" : "secondary"} className="text-xs">
                        {isSelected ? 'Required' : 'Not Required'}
                      </Badge>
                    </div>
                  );
                })}
            </div>
            {/* Debug info */}
            <div className="mt-2 pt-2 border-t border-gray-100">
              <p className="text-xs text-gray-400">
                Debug: askCapitalPosition = [{currentDeal.askCapitalPosition?.join(', ') || 'none'}]
              </p>
              <p className="text-xs text-gray-400">
                Debug: askAmount = [{currentDeal.askAmount?.join(', ') || 'none'}]
              </p>
            </div>
          </div>
        )}
    </div>
  );
};

export default CapitalPositionSelector;
