import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CreditCard } from "lucide-react";
import { DealV2, DealNsfFieldV2, InvestmentCriteriaDebtV2 } from "./shared/types-v2";
import { formatNumber, formatRawCurrency, formatPercentage } from "@/lib/utils/formatters";

interface DebtTabProps {
  deal: DealV2;
  currentDeal: DealV2 | null;
  isEditing: boolean;
  onDebtCriteriaChange?: (criteriaId: number, field: keyof InvestmentCriteriaDebtV2, value: any) => void;
  onNsfFieldChange?: (nsfId: number, field: keyof DealNsfFieldV2, value: any) => void;
}

const DebtTab: React.FC<DebtTabProps> = ({
  deal,
  currentDeal,
  isEditing,
  onDebtCriteriaChange,
  onNsfFieldChange,
}) => {
  const [percentageInputs, setPercentageInputs] = useState<{ [key: string]: string }>({});

  // Debug logging
  console.log('DebtTab render:', { 
    isEditing, 
    isEditingType: typeof isEditing,
    deal: deal?.dealName, 
    investmentCriteriaDebt: deal?.investmentCriteriaDebt,
    currentDeal: currentDeal?.dealName,
    currentDealInvestmentCriteria: currentDeal?.investmentCriteriaDebt
  });

  // Initialize percentage inputs when component mounts or nsfFields change
  useEffect(() => {
    const nsfFields = deal?.nsfFields || [];
    const initialInputs: { [key: string]: string } = {};
    nsfFields.forEach(nsf => {
      if (nsf.percentageOfTotal !== undefined && nsf.percentageOfTotal !== null) {
        initialInputs[nsf.id] = (nsf.percentageOfTotal * 100).toFixed(2);
      }
    });
    setPercentageInputs(initialInputs);
  }, [deal?.nsfFields]);
  
  // Group debt criteria by capital position
  const debtCriteriaByPosition = new Map<string, any[]>();
  
  if (deal.investmentCriteriaDebt) {
    deal.investmentCriteriaDebt.forEach(criteria => {
      // Handle both array and string formats for capital position
      const capitalPositions = Array.isArray(criteria.capitalPosition) 
        ? criteria.capitalPosition 
        : criteria.capitalPosition ? [criteria.capitalPosition] : [];
      
      capitalPositions.forEach(position => {
        if (!debtCriteriaByPosition.has(position)) {
          debtCriteriaByPosition.set(position, []);
        }
        debtCriteriaByPosition.get(position)!.push(criteria);
      });
    });
  }
  
  const renderCapitalPositionBreakdown = (capitalPosition: string, criteriaList: any[]) => {
    return (
      <div key={capitalPosition} className="border rounded-lg p-4 bg-gray-50 mb-4">
        <div className="flex justify-between items-center mb-3">
          <h4 className="font-medium text-gray-900 text-lg">{capitalPosition} Breakdown</h4>
          <Badge variant="outline" className="text-xs">
            {criteriaList.length} Investment Criteria Records
          </Badge>
        </div>
        
        {/* Show debt investment criteria for this capital position */}
        {criteriaList.map((criteria, criteriaIndex) => {
          // Get associated NSF data for this capital position - match sourceType against investment criteria capitalPosition
          const nsfFields = deal.nsfFields || (deal as any).nsf_fields || [];
          const associatedNsfData = nsfFields.filter(nsf => {
            // Match sourceType (e.g., "Senior Debt", "Mezzanine") against investment criteria capitalPosition
            const matchesSourceType = 
              nsf.sourceType === capitalPosition || 
              (nsf as any).source_type === capitalPosition;
            
            // If dealType is missing, infer from sourceType
            // Senior Debt, Mezzanine = debt type
            // GP Equity, LP Equity = equity type
            const inferredDealType = nsf.dealType || (nsf as any).deal_type || 
              (nsf.sourceType && ['Senior Debt', 'Mezzanine'].includes(nsf.sourceType) ? 'debt' : 
               nsf.sourceType && ['GP Equity', 'LP Equity'].includes(nsf.sourceType) ? 'equity' : null);
            
            const matchesDealType = inferredDealType === 'debt';
            
            return matchesSourceType && matchesDealType;
          }) || [];
          
          return (
            <div key={`criteria-${criteriaIndex}`} className="ml-4 p-3 bg-white rounded border mb-3">
              <h6 className="font-medium text-sm text-gray-700 mb-2">Investment Criteria - {criteria.loanType || 'Debt'}</h6>
              
              {/* NSF Data - Show on top */}
              {associatedNsfData.length > 0 && (
                <div className="mb-4">
                  <h6 className="font-medium text-sm text-gray-700 mb-3">Sources</h6>
                  {associatedNsfData.map((nsf, nsfIndex) => (
                    <div key={`nsf-${nsfIndex}`} className="border rounded-lg p-4 bg-gray-50 mb-3">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">

                        
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Amount</Label>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={nsf.amount || ''}
                              onChange={(e) => onNsfFieldChange?.(nsf.id, 'amount', parseFloat(e.target.value) || 0)}
                              className="mt-1"
                              placeholder="0"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amount)}</p>
                          )}
                        </div>
                        
                        {/* Second Row - Per GSF, Per ZFA, Per NSF */}
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Per GSF</Label>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={nsf.amountPerGsf || ""}
                              onChange={(e) => onNsfFieldChange?.(nsf.id, 'amountPerGsf', parseFloat(e.target.value) || 0)}
                              className="mt-1"
                              placeholder="0"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amountPerGsf)}</p>
                          )}
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Per ZFA</Label>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={nsf.amountPerZfa || ""}
                              onChange={(e) => onNsfFieldChange?.(nsf.id, 'amountPerZfa', parseFloat(e.target.value) || 0)}
                              className="mt-1"
                              placeholder="0"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amountPerZfa)}</p>
                          )}
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Per NSF</Label>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={nsf.amountPerNsf || ""}
                              onChange={(e) => onNsfFieldChange?.(nsf.id, 'amountPerNsf', parseFloat(e.target.value) || 0)}
                              className="mt-1"
                              placeholder="0"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amountPerNsf)}</p>
                          )}
                        </div>
                        
                        {/* Third Row - Percentage and Measurements */}
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Percent</Label>
                          {isEditing ? (
                            <Input
                              type="number"
                              step="0.01"
                              min="0"
                              max="100"
                              value={percentageInputs[nsf.id] ?? (nsf.percentageOfTotal ? (nsf.percentageOfTotal * 100).toFixed(2) : "")}
                              onChange={(e) => {
                                setPercentageInputs(prev => ({
                                  ...prev,
                                  [nsf.id]: e.target.value
                                }));
                              }}
                              onBlur={(e) => {
                                const value = parseFloat(e.target.value) || 0;
                                // Convert percentage (0-100) back to decimal (0-1) for backend
                                onNsfFieldChange?.(nsf.id, 'percentageOfTotal', value / 100);
                                // Clear the local input state after saving
                                setPercentageInputs(prev => {
                                  const newState = { ...prev };
                                  delete newState[nsf.id];
                                  return newState;
                                });
                              }}
                              className="mt-1"
                              placeholder="0.00"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {nsf.percentageOfTotal ? `${(nsf.percentageOfTotal * 100).toFixed(2)}%` : "0.00%"}
                            </p>
                          )}
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium text-gray-600">GSF (Gross Square Foot)</Label>
                          <p className="text-sm text-gray-900 mt-1">
                            {formatNumber(deal?.property?.gsfGrossSquareFoot || null, 'sq ft')} 
                            <span className="text-xs text-gray-500 ml-2">(from property)</span>
                          </p>
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium text-gray-600">ZFA (Zoning Floor Area)</Label>
                          <p className="text-sm text-gray-900 mt-1">
                            {formatNumber(deal?.property?.zfaZoningFloorArea || null, 'sq ft')} 
                            <span className="text-xs text-gray-500 ml-2">(from property)</span>
                          </p>
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Total NSF</Label>
                          <p className="text-sm text-gray-900 mt-1">
                            {formatNumber(deal?.property?.totalNsfNetSquareFoot || null, 'sq ft')} 
                            <span className="text-xs text-gray-500 ml-2">(from property)</span>
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {/* Investment Criteria Fields - Reorganized Layout */}
              <div className="pt-4 border-t border-gray-200">
                <h6 className="font-medium text-sm text-gray-700 mb-3">Investment Criteria</h6>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  {/* Row 1: Lien Position, Loan Type, LTC, LTV */}
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Lien Position</Label>
                    {isEditing ? (
                      <Select
                        value={criteria.lienPosition || ''}
                        onValueChange={(value) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'lienPosition', value)}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select lien position" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="First">First</SelectItem>
                          <SelectItem value="Second">Second</SelectItem>
                          <SelectItem value="Third">Third</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.lienPosition || 'N/A'}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Loan Type</Label>
                    {isEditing ? (
                      <Input
                        value={criteria.loanType || ''}
                        onChange={(e) => {
                          console.log('Loan Type onChange:', { 
                            value: e.target.value, 
                            criteriaId: criteria.investmentCriteriaDebtId,
                            onDebtCriteriaChange: !!onDebtCriteriaChange 
                          });
                          onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'loanType', e.target.value);
                        }}
                        className="mt-1"
                        placeholder="e.g., Construction Loan"
                      />
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.loanType || 'N/A'}</p>
                    )}
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">LTC Range</Label>
                    {isEditing ? (
                      <div className="flex gap-2 mt-1">
                        <Input
                          type="number"
                          step="0.01"
                          value={criteria.loanToCostMin || ''}
                          onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'loanToCostMin', parseFloat(e.target.value) || 0)}
                          placeholder="Min %"
                          className="flex-1"
                        />
                        <Input
                          type="number"
                          step="0.01"
                          value={criteria.loanToCostMax || ''}
                          onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'loanToCostMax', parseFloat(e.target.value) || 0)}
                          placeholder="Max %"
                          className="flex-1"
                        />
                      </div>
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.loanToCostMin && criteria.loanToCostMax ? `${criteria.loanToCostMin}%-${criteria.loanToCostMax}%` : 'N/A'}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">LTV Range</Label>
                    {isEditing ? (
                      <div className="flex gap-2 mt-1">
                        <Input
                          type="number"
                          step="0.01"
                          value={criteria.loanToValueMin || ''}
                          onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'loanToValueMin', parseFloat(e.target.value) || 0)}
                          placeholder="Min %"
                          className="flex-1"
                        />
                        <Input
                          type="number"
                          step="0.01"
                          value={criteria.loanToValueMax || ''}
                          onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'loanToValueMax', parseFloat(e.target.value) || 0)}
                          placeholder="Max %"
                          className="flex-1"
                        />
                      </div>
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.loanToValueMin && criteria.loanToValueMax ? `${criteria.loanToValueMin}%-${criteria.loanToValueMax}%` : 'N/A'}</p>
                    )}
                  </div>
                  
                  {/* Row 2: Loan Term, Interest Rate, Rate Type, Rate Lock */}
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Loan Term</Label>
                    {isEditing ? (
                      <div className="flex gap-2 mt-1">
                        <Input
                          type="number"
                          value={criteria.minLoanTerm || ''}
                          onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'minLoanTerm', parseInt(e.target.value) || 0)}
                          placeholder="Min"
                          className="flex-1"
                        />
                        <Input
                          type="number"
                          value={criteria.maxLoanTerm || ''}
                          onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'maxLoanTerm', parseInt(e.target.value) || 0)}
                          placeholder="Max"
                          className="flex-1"
                        />
                      </div>
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.minLoanTerm ? `${criteria.minLoanTerm}-${criteria.maxLoanTerm || 'N/A'} years` : 'N/A'}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Interest Rate</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.loanInterestRate || ''}
                        onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'loanInterestRate', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.loanInterestRate ? `${criteria.loanInterestRate}%` : 'N/A'}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Rate Type</Label>
                    {isEditing ? (
                      <Select
                        value={criteria.rateType || ''}
                        onValueChange={(value) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'rateType', value)}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select rate type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Fixed">Fixed</SelectItem>
                          <SelectItem value="Variable">Variable</SelectItem>
                          <SelectItem value="Hybrid">Hybrid</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.rateType || 'N/A'}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Rate Lock</Label>
                    {isEditing ? (
                      <Input
                        value={criteria.rateLock || ''}
                        onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'rateLock', e.target.value)}
                        className="mt-1"
                        placeholder="e.g., 30 days"
                      />
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.rateLock || 'N/A'}</p>
                    )}
                  </div>
                  
                  {/* Row 3: SOFR, WSJ, Prime, 3Y Treasury */}
                  <div>
                    <Label className="text-sm font-medium text-gray-600">SOFR Based Rate</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.loanInterestRateBasedOffSofr || ''}
                        onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'loanInterestRateBasedOffSofr', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.loanInterestRateBasedOffSofr ? `${criteria.loanInterestRateBasedOffSofr}%` : 'N/A'}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">WSJ Based Rate</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.loanInterestRateBasedOffWsj || ''}
                        onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'loanInterestRateBasedOffWsj', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.loanInterestRateBasedOffWsj ? `${criteria.loanInterestRateBasedOffWsj}%` : 'N/A'}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Prime Based Rate</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.loanInterestRateBasedOffPrime || ''}
                        onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'loanInterestRateBasedOffPrime', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.loanInterestRateBasedOffPrime ? `${criteria.loanInterestRateBasedOffPrime}%` : 'N/A'}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">3Y Treasury Based Rate</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.loanInterestRateBasedOff3yt || ''}
                        onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'loanInterestRateBasedOff3yt', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.loanInterestRateBasedOff3yt ? `${criteria.loanInterestRateBasedOff3yt}%` : 'N/A'}</p>
                    )}
                  </div>
                  
                  {/* Row 4: 5Y, 10Y, 30Y Treasury, DSCR */}
                  <div>
                    <Label className="text-sm font-medium text-gray-600">5Y Treasury Based Rate</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.loanInterestRateBasedOff5yt || ''}
                        onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'loanInterestRateBasedOff5yt', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.loanInterestRateBasedOff5yt ? `${criteria.loanInterestRateBasedOff5yt}%` : 'N/A'}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">10Y Treasury Based Rate</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.loanInterestRateBasedOff10yt || ''}
                        onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'loanInterestRateBasedOff10yt', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.loanInterestRateBasedOff10yt ? `${criteria.loanInterestRateBasedOff10yt}%` : 'N/A'}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">30Y Treasury Based Rate</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.loanInterestRateBasedOff30yt || ''}
                        onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'loanInterestRateBasedOff30yt', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.loanInterestRateBasedOff30yt ? `${criteria.loanInterestRateBasedOff30yt}%` : 'N/A'}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">DSCR Range</Label>
                    {isEditing ? (
                      <div className="flex gap-2 mt-1">
                        <Input
                          type="number"
                          step="0.01"
                          value={criteria.minLoanDscr || ''}
                          onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'minLoanDscr', parseFloat(e.target.value) || 0)}
                          placeholder="Min"
                          className="flex-1"
                        />
                        <Input
                          type="number"
                          step="0.01"
                          value={criteria.maxLoanDscr || ''}
                          onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'maxLoanDscr', parseFloat(e.target.value) || 0)}
                          placeholder="Max"
                          className="flex-1"
                        />
                      </div>
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.minLoanDscr && criteria.maxLoanDscr ? `${criteria.minLoanDscr}-${criteria.maxLoanDscr}` : 'N/A'}</p>
                    )}
                  </div>
                  
                  {/* Row 5: Debt Yield, Recourse, Amortization, Notes */}
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Debt Yield</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={criteria.loanMinDebtYield || ''}
                        onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'loanMinDebtYield', parseFloat(e.target.value) || 0)}
                        className="mt-1"
                        placeholder="0"
                      />
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.loanMinDebtYield || 'N/A'}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Recourse</Label>
                    {isEditing ? (
                      <Select
                        value={criteria.recourseLoan || ''}
                        onValueChange={(value) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'recourseLoan', value)}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select recourse type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Recourse">Recourse</SelectItem>
                          <SelectItem value="Non-Recourse">Non-Recourse</SelectItem>
                          <SelectItem value="Limited Recourse">Limited Recourse</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.recourseLoan || 'N/A'}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Amortization</Label>
                    {isEditing ? (
                      <Input
                        value={criteria.amortization || ''}
                        onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'amortization', e.target.value)}
                        className="mt-1"
                        placeholder="e.g., 30-year, Interest-only"
                      />
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.amortization || 'N/A'}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Notes</Label>
                    {isEditing ? (
                      <Textarea
                        value={criteria.notes || ''}
                        onChange={(e) => onDebtCriteriaChange?.(criteria.investmentCriteriaDebtId, 'notes', e.target.value)}
                        className="mt-1"
                        placeholder="Additional notes"
                      />
                    ) : (
                      <p className="text-gray-900 mt-1">{criteria.notes || 'N/A'}</p>
                    )}
                  </div>
                  

                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Debt Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Capital Position Breakdown */}
          {(() => {
            return Array.from(debtCriteriaByPosition.keys()).length > 0 && (
              <div>
                <h4 className="font-medium text-sm text-gray-700 mb-3">Capital Position Breakdown</h4>
                {Array.from(debtCriteriaByPosition.entries()).map(([capitalPosition, criteriaList]) => (
                  renderCapitalPositionBreakdown(capitalPosition, criteriaList)
                ))}
              </div>
            );
          })()}

          {/* Legacy NSF Fields Display - Removed since NSF is now integrated into Capital Position Breakdown */}
        </CardContent>
      </Card>
    </div>
  );
};

export default DebtTab;

