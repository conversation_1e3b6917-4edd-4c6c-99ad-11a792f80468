"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  ArrowLeft,
  DollarSign,
  MapPin,
  Building2,
  User,
  Calendar,
  TrendingUp,
  Percent,
  FileText,
  Edit,
  Users,
  Database,
  Target,
  Clock,
  Home,
  Ruler,
  CreditCard,
  <PERSON><PERSON><PERSON>,
  Play,
  Save,
  Trash2,
  Plus,
  BarChart3,
} from "lucide-react";
import { Deal, InvestmentCriteria } from "./shared/types";
import DealFiles from "./DealFiles";
import { Contact } from "../people/shared/types";
import ContactCard from "../people/list-components/ContactCard";
import DataQualityBanner from "./DataQualityBanner";
import DealEditForm from "./DealEditForm";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { MultiSelect } from "@/components/ui/multi-select";
import { ContactMultiSelect } from "@/components/ui/contact-multi-select";

interface DealDetailNewProps {
  dealId: string;
}

const DealDetailNew: React.FC<DealDetailNewProps> = ({ dealId }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get the return URL from query parameters
  const returnUrl = searchParams.get('from');
  const [deal, setDeal] = useState<Deal | null>(null);
  const [contact, setContact] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [contactLoading, setContactLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Initialize activeTab from URL or default to "overview"
  const [activeTab, setActiveTab] = useState(() => {
    const tabFromUrl = searchParams.get('tab');
    const validTabs = ['overview', 'debt', 'equity', 'deal-matching', 'contact', 'investment-criteria', 'jobs'];
    return tabFromUrl && validTabs.includes(tabFromUrl) ? tabFromUrl : "overview";
  });
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isRunningRequirementExtraction, setIsRunningRequirementExtraction] = useState(false);
  const [jobs, setJobs] = useState<any[]>([]);
  const [jobsLoading, setJobsLoading] = useState(false);
  const [jobsRefreshInterval, setJobsRefreshInterval] = useState<NodeJS.Timeout | null>(null);
  const [matchingContacts, setMatchingContacts] = useState<any[]>([]);
  const [matchingContactsLoading, setMatchingContactsLoading] = useState(false);
  const [matchingContactsError, setMatchingContactsError] = useState<string | null>(null);
  const [matchingContactsFiltering, setMatchingContactsFiltering] = useState<any>(null);
  const [showAllMatches, setShowAllMatches] = useState(false);
  const [isCrmMode, setIsCrmMode] = useState(true);
  const [matchingContactsPagination, setMatchingContactsPagination] = useState<any>(null);
  const [expandedContactId, setExpandedContactId] = React.useState<number | null>(null);
  const [editedDeal, setEditedDeal] = useState<Deal | null>(null);
  const [editedCriteria, setEditedCriteria] = useState<InvestmentCriteria[]>([]);
  const [deletedCriteriaIds, setDeletedCriteriaIds] = useState<number[]>([]);
  const [individualLocationEditing, setIndividualLocationEditing] = useState(false);
  const [individualPropertyEditing, setIndividualPropertyEditing] = useState(false);
  const [accessLevel, setAccessLevel] = useState<'admin' | 'restricted' | null>(null);
  const [criteriaToDelete, setCriteriaToDelete] = useState<{ id: number; index: number } | null>(null);
  const [isAddingCriteria, setIsAddingCriteria] = useState(false);
  const [newCriteria, setNewCriteria] = useState<InvestmentCriteria | null>(null);
  const [applyToAll, setApplyToAll] = useState(false);
  
  // Contact management state
  const [dealContacts, setDealContacts] = useState<any[]>([]);
  const [dealContactsLoading, setDealContactsLoading] = useState(false);
  const [selectedContacts, setSelectedContacts] = useState<any[]>([]);
  const [isAddingContacts, setIsAddingContacts] = useState(false);


  // Utility functions (moved up to be available for memoized variables)
  const formatCurrency = (amount: number | string | null | undefined): string => {
    if (amount === null || amount === undefined) return "N/A";
    const num = typeof amount === "string" ? parseFloat(amount) : amount;
    if (isNaN(num)) return "N/A";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(num * 1000000); // Convert millions to actual amount
  };

  // Memoized capital position options
  const capitalPositionOptions = React.useMemo(() => {
    // Use the actual criteria data instead of calling functions
    const currentCriteria = isEditing ? editedCriteria : (deal?.investment_criteria || []);
    const allPositions = new Set<string>();
    
    currentCriteria.forEach(criteria => {
      if (criteria.capital_position && Array.isArray(criteria.capital_position)) {
        criteria.capital_position.forEach(position => {
          if (position && position.trim()) {
            allPositions.add(position.trim());
          }
        });
      }
    });
    
    const positions = Array.from(allPositions).sort();
    console.log('🔍 MultiSelect options:', positions);
    
    return positions.map((position) => {
      // Find deal size for this position
      const criteria = currentCriteria.find(c => 
        c.capital_position && Array.isArray(c.capital_position) && 
        c.capital_position.includes(position)
      );
      
      const dealSize = criteria ? {
        min: criteria.minimum_deal_size,
        max: criteria.maximum_deal_size
      } : null;
      
      const rangeText = dealSize ? 
        ` (${formatCurrency(dealSize.min || 0)} - ${formatCurrency(dealSize.max || 0)})` : '';
      
      const option = {
        value: position,
        label: `${position}${rangeText}`
      };
      console.log('🔍 Option:', option);
      return option;
    });
  }, [isEditing, editedCriteria, deal?.investment_criteria]);

  // Memoized selected capital positions
  const selectedCapitalPositions = React.useMemo(() => {
    // First try to get from deal's extra_fields
    const currentDeal = isEditing ? editedDeal : deal;
    let selectedPositions: string[] = [];
    
    if (currentDeal?.extra_fields?.capital_position && Array.isArray(currentDeal.extra_fields.capital_position)) {
      selectedPositions = currentDeal.extra_fields.capital_position;
    } else {
      // Fall back to criteria that has is_requested: true
      const currentCriteria = isEditing ? editedCriteria : (deal?.investment_criteria || []);
      const requestedCriteria = currentCriteria.find(criteria => criteria.is_requested);
      if (requestedCriteria?.capital_position && Array.isArray(requestedCriteria.capital_position)) {
        selectedPositions = requestedCriteria.capital_position;
      }
    }
    
    console.log('🔍 MultiSelect selected:', selectedPositions);
    return selectedPositions;
  }, [isEditing, editedDeal, deal, editedCriteria]);

  // Memoized handler for capital positions change
  const handleCapitalPositionsChange = React.useCallback((selectedPositions: string[]) => {
    console.log('Capital positions changed to:', selectedPositions);
    
    // Update the deal's extra_fields with capital positions
    setEditedDeal(prev => prev ? {
      ...prev,
      extra_fields: {
        ...prev.extra_fields,
        capital_position: selectedPositions
      }
    } : null);
    
    // Update is_requested: mark matching criteria as true, others as false
    const currentCriteria = isEditing ? editedCriteria : (deal?.investment_criteria || []);
    if (currentCriteria.length > 0) {
      const updatedCriteria = currentCriteria.map(criteria => {
        const hasMatchingPosition = criteria.capital_position && 
          Array.isArray(criteria.capital_position) && 
          criteria.capital_position.some(pos => 
            pos && selectedPositions.includes(pos.trim())
          );
        
        console.log('Criteria:', criteria.criteria_id, 'Capital positions:', criteria.capital_position, 'Has matching position:', hasMatchingPosition);
        
        return {
          ...criteria,
          is_requested: hasMatchingPosition || false
        };
      });
      
      console.log('Updated criteria with is_requested:', updatedCriteria.map(c => ({ id: c.criteria_id, is_requested: c.is_requested })));
      setEditedCriteria(updatedCriteria);
    }
  }, [isEditing, editedCriteria, deal?.investment_criteria]);

  // Handle tab changes and update URL
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);
    
    // Update URL with the new tab
    const url = new URL(window.location.href);
    url.searchParams.set('tab', newTab);
    router.replace(url.pathname + url.search, { scroll: false });
  };

  // Helper functions to navigate to specific tabs
  const navigateToTab = (tab: string) => {
    handleTabChange(tab);
  };

  // Get current tab URL for sharing
  const getCurrentTabUrl = () => {
    const url = new URL(window.location.href);
    url.searchParams.set('tab', activeTab);
    return url.toString();
  };

  // Copy current tab URL to clipboard
  const copyTabUrl = async () => {
    try {
      await navigator.clipboard.writeText(getCurrentTabUrl());
      toast.success('Tab URL copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy URL');
    }
  };

  // Sync activeTab with URL search params
  useEffect(() => {
    const tabFromUrl = searchParams.get('tab');
    const validTabs = ['overview', 'debt', 'equity', 'deal-matching', 'contact', 'investment-criteria', 'jobs'];
    if (tabFromUrl && validTabs.includes(tabFromUrl) && tabFromUrl !== activeTab) {
      setActiveTab(tabFromUrl);
    }
  }, [searchParams, activeTab]);

  // Update page title with current tab
  useEffect(() => {
    if (deal?.deal_name) {
      const tabNames: Record<string, string> = {
        'overview': 'Overview',
        'debt': 'Debt',
        'equity': 'Equity',
        'deal-matching': 'Deal Matching',
        'contact': 'Contact',
        'investment-criteria': 'Investment Criteria',
        'jobs': 'Jobs'
      };
      const tabName = tabNames[activeTab] || 'Overview';
      document.title = `${deal.deal_name} - ${tabName} | Anax Dashboard`;
    }
  }, [activeTab, deal?.deal_name]);

  // Keyboard shortcuts for tab navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle shortcuts when not in an input field
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      // Ctrl/Cmd + number keys for tab navigation
      if ((event.ctrlKey || event.metaKey) && !event.shiftKey && !event.altKey) {
        const tabMap: Record<string, string> = {
          '1': 'overview',
          '2': 'debt',
          '3': 'equity',
          '4': 'deal-matching',
          '5': 'contact',
          '6': 'investment-criteria',
          '7': 'jobs'
        };

        const key = event.key;
        if (tabMap[key]) {
          event.preventDefault();
          handleTabChange(tabMap[key]);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Get access level from localStorage
  useEffect(() => {
    const savedAccess = localStorage.getItem('dashboardAccess');
    if (savedAccess === 'admin' || savedAccess === 'restricted') {
      setAccessLevel(savedAccess);
    }
  }, []);

  // Handle saving deal changes
  const handleSaveDeal = async (updatedDeal: Deal) => {
    setIsSaving(true);
    try {
      const response = await fetch(`/api/deals/${dealId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updatedDeal),
      });

      if (!response.ok) {
        throw new Error("Failed to update deal");
      }

      const savedDeal = await response.json();
      setDeal(savedDeal);
      setIsEditing(false);
      setActiveTab("overview");
      toast.success("Deal updated successfully");
    } catch (error) {
      console.error("Error updating deal:", error);
      toast.error("Failed to update deal");
    } finally {
      setIsSaving(false);
    }
  };

  // Handle saving inline edits
  const handleSaveInlineEdits = async () => {
    if (!editedDeal) return;
    
    setIsSaving(true);
    try {
      // Get the selected capital positions from the MultiSelect
      const selectedPositions = editedDeal?.extra_fields?.capital_position || [];

      // Update is_requested based on the selected capital positions
      const updatedCriteria = editedCriteria.map(criteria => {
        const hasMatchingPosition = criteria.capital_position && 
          Array.isArray(criteria.capital_position) && 
          criteria.capital_position.some(pos => 
            pos && selectedPositions.includes(pos.trim())
          );
        
        return {
          ...criteria,
          is_requested: hasMatchingPosition || false
        };
      });

      // Prepare the updated deal with edited criteria and capital positions in extra_fields
      const updatedDeal = {
        ...editedDeal,
        extra_fields: {
          ...editedDeal?.extra_fields,
          capital_position: selectedPositions
        },
        investment_criteria: updatedCriteria
      };

      // Debug: Log what we're sending
      console.log('=== SENDING DATA TO API ===');
      console.log('selectedPositions:', selectedPositions);
      console.log('updatedCriteria:', JSON.stringify(updatedCriteria, null, 2));
      console.log('updatedDeal:', JSON.stringify(updatedDeal, null, 2));
      console.log('=== END SENDING DATA ===');

      const response = await fetch(`/api/deals/${dealId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updatedDeal),
      });

      if (!response.ok) {
        throw new Error("Failed to update deal");
      }

      const savedDeal = await response.json();
      setDeal(savedDeal);
      setIsEditing(false);
      setEditedDeal(null);
      setEditedCriteria([]);
      toast.success("Deal updated successfully");
    } catch (error) {
      console.error("Error updating deal:", error);
      toast.error("Failed to update deal");
    } finally {
      setIsSaving(false);
    }
  };

  // Handle running requirement extraction
  const handleRunRequirementExtraction = async () => {
    setIsRunningRequirementExtraction(true);
    try {
      const response = await fetch(`/api/deals/${dealId}/run-requirement-extraction`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to run requirement extraction");
      }

      toast.success(result.message || "Requirement extraction completed successfully");
      
      // Refresh deal data to show updated criteria
      const dealResponse = await fetch(`/api/deals/${dealId}`);
      if (dealResponse.ok) {
        const updatedDeal = await dealResponse.json();
        setDeal(updatedDeal);
      }

      // Refresh jobs list
      fetchJobs();
    } catch (error) {
      console.error("Error running requirement extraction:", error);
      toast.error(error instanceof Error ? error.message : "Failed to run requirement extraction");
    } finally {
      setIsRunningRequirementExtraction(false);
    }
  };

  // Fetch jobs for this deal
  const fetchJobs = async () => {
    setJobsLoading(true);
    try {
      const response = await fetch(`/api/deals/${dealId}/jobs`);
      if (response.ok) {
        const jobsData = await response.json();
        setJobs(jobsData.jobs || []);
        
        // Check if there are any processing jobs and set up auto-refresh
        const hasProcessingJobs = jobsData.jobs?.some((job: any) => 
          job.status === 'pending' || job.status === 'processing' || job.status === 'active'
        );
        
        if (hasProcessingJobs) {
          // Set up auto-refresh every 5 seconds
          if (jobsRefreshInterval) {
            clearInterval(jobsRefreshInterval);
          }
          const interval = setInterval(fetchJobs, 5000);
          setJobsRefreshInterval(interval);
        } else {
          // Clear interval if no processing jobs
          if (jobsRefreshInterval) {
            clearInterval(jobsRefreshInterval);
            setJobsRefreshInterval(null);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching jobs:", error);
    } finally {
      setJobsLoading(false);
    }
  };

  // Fetch matching contacts for this deal
  const fetchMatchingContacts = async (useShowAll?: boolean) => {
    if (!dealId) return;
    setMatchingContactsLoading(true);
    setMatchingContactsError(null);
    try {
      // Use the passed parameter if provided, otherwise use the state
      const shouldShowAll = useShowAll !== undefined ? useShowAll : showAllMatches;
      const showAllParam = shouldShowAll ? '&show_all=true' : '';
      const crmModeParam = isCrmMode ? '&crm_mode=true' : '';
      const url = `/api/matching/contacts-for-deal/${dealId}?page=1&limit=50${showAllParam}${crmModeParam}`;
      
      const res = await fetch(url);
      if (!res.ok) throw new Error("Failed to fetch matching contacts");
      const data = await res.json();
      setMatchingContacts(data.matches || []);
      setMatchingContactsPagination(data.pagination || null);
      setMatchingContactsFiltering(data.filtering || null);
    } catch (err) {
      console.error('Error fetching matching contacts:', err);
      setMatchingContactsError("Error loading matching contacts");
    } finally {
      setMatchingContactsLoading(false);
    }
  };

  const handleToggleShowAllMatches = () => {
    const newShowAllMatches = !showAllMatches;
    setShowAllMatches(newShowAllMatches);
    // Refetch with new filter setting
    setTimeout(() => {
      fetchMatchingContacts(newShowAllMatches);
    }, 100);
  };

  // Fetch contacts for this deal
  const fetchDealContacts = async () => {
    if (!dealId) return;
    setDealContactsLoading(true);
    try {
      const res = await fetch(`/api/deals/${dealId}/contacts`);
      if (!res.ok) throw new Error("Failed to fetch deal contacts");
      const data = await res.json();
      setDealContacts(data.contacts || []);
    } catch (err) {
      console.error("Error loading deal contacts:", err);
    } finally {
      setDealContactsLoading(false);
    }
  };

  // Add contacts to deal
  const addContactsToDeal = async () => {
    if (!dealId || selectedContacts.length === 0) return;
    
    setIsAddingContacts(true);
    try {
      const contactIds = selectedContacts.map(contact => contact.contact_id);
      const res = await fetch(`/api/deals/${dealId}/contacts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ contactIds }),
      });
      
      if (!res.ok) throw new Error("Failed to add contacts to deal");
      
      const data = await res.json();
      toast.success(`Added ${data.totalAdded} contact(s) to deal`);
      
      // Refresh deal contacts
      await fetchDealContacts();
      
      // Clear selected contacts
      setSelectedContacts([]);
    } catch (err) {
      console.error("Error adding contacts to deal:", err);
      toast.error("Failed to add contacts to deal");
    } finally {
      setIsAddingContacts(false);
    }
  };

  // Remove contact from deal
  const removeContactFromDeal = async (contactId: number) => {
    if (!dealId) return;
    
    try {
      const res = await fetch(`/api/deals/${dealId}/contacts?contactId=${contactId}`, {
        method: 'DELETE',
      });
      
      if (!res.ok) throw new Error("Failed to remove contact from deal");
      
      toast.success("Contact removed from deal");
      
      // Refresh deal contacts
      await fetchDealContacts();
    } catch (err) {
      console.error("Error removing contact from deal:", err);
      toast.error("Failed to remove contact from deal");
    }
  };

  useEffect(() => {
    async function fetchDeal() {
      try {
        const response = await fetch(`/api/deals/${dealId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch deal");
        }
        const dealData = await response.json();
        setDeal(dealData);
      } catch (error) {
        console.error("Error fetching deal:", error);
        setError("Failed to load deal");
      } finally {
        setLoading(false);
      }
    }

    async function fetchContact() {
      if (!deal?.contact_id) return;
      setContactLoading(true);
      try {
        const response = await fetch(`/api/contacts/${deal.contact_id}`);
        if (response.ok) {
          const contactData = await response.json();
          setContact(contactData);
        }
      } catch (error) {
        console.error("Error fetching contact:", error);
      } finally {
        setContactLoading(false);
      }
    }

    if (dealId) {
      fetchDeal();
      fetchJobs();
    }
  }, [dealId]);

  // Fetch matching contacts when deal matching tab is active
  useEffect(() => {
    if (activeTab === "deal-matching" && dealId) {
      fetchMatchingContacts();
    }
  }, [activeTab, dealId, isCrmMode]);

  // Fetch deal contacts when contact tab is active
  useEffect(() => {
    if (activeTab === "contact" && dealId) {
      fetchDealContacts();
    }
  }, [activeTab, dealId]);

  const formatPercentage = (value: number | string | null): string => {
    if (value === null || value === undefined) return "N/A";
    const num = typeof value === "string" ? parseFloat(value) : value;
    if (isNaN(num)) return "N/A";
    return `${(num * 100).toFixed(1)}%`;
  };

  const formatNumber = (value: number | string | null, unit?: string): string => {
    if (value === null || value === undefined) return "N/A";
    const num = typeof value === "string" ? parseFloat(value) : value;
    if (isNaN(num)) return "N/A";
    return `${num.toLocaleString()}${unit ? ` ${unit}` : ""}`;
  };

  const getStatusColor = (status: string | null): string => {
    if (!status) return "bg-gray-100 text-gray-800 border-gray-200";
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-50 text-green-700 border-green-200";
      case "pending":
        return "bg-yellow-50 text-yellow-700 border-yellow-200";
      case "completed":
        return "bg-blue-50 text-blue-700 border-blue-200";
      case "cancelled":
        return "bg-red-50 text-red-700 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const formatArrayField = (field: string[] | null): string => {
    if (!field || field.length === 0) return "N/A";
    return field.join(", ");
  };

  const getLocation = (): string => {
    if (!deal) return "N/A";
    const parts = [
      deal.neighborhood,
      deal.zip_code,
    ].filter(Boolean);
    return parts.length > 0 ? parts.join(", ") : "N/A";
  };

  // Separate investment criteria into debt and equity
  const debtCriteria = deal?.investment_criteria?.filter(criteria => 
    criteria.capital_position?.some(pos => 
      pos.includes("Debt") || pos.includes("Senior") || pos.includes("Mezzanine")
    )
  ) || [];

  const equityCriteria = deal?.investment_criteria?.filter(criteria => 
    criteria.capital_position?.some(pos => 
      pos.includes("Equity") || pos.includes("Partner") || pos.includes("GP") || pos.includes("LP")
    )
  ) || [];

  // Helper functions to get current data (edited or original)
  const getCurrentDeal = () => isEditing ? editedDeal : deal;
  const getCurrentCriteria = () => isEditing ? editedCriteria : (deal?.investment_criteria || []);

  // Get all unique capital positions from all criteria
  const getAllCapitalPositions = () => {
    const allPositions = new Set<string>();
    getCurrentCriteria().forEach(criteria => {
      if (criteria.capital_position && Array.isArray(criteria.capital_position)) {
        criteria.capital_position.forEach(position => {
          if (position && position.trim()) {
            allPositions.add(position.trim());
          }
        });
      }
    });
    const positions = Array.from(allPositions).sort();
    console.log('Available capital positions:', positions);
    return positions;
  };

  // Get deal size range for a specific capital position
  const getDealSizeForPosition = (position: string) => {
    const criteria = getCurrentCriteria().find(c => 
      c.capital_position && Array.isArray(c.capital_position) && 
      c.capital_position.includes(position)
    );
    
    if (criteria) {
      return {
        min: criteria.minimum_deal_size,
        max: criteria.maximum_deal_size,
        criteria: criteria
      };
    }
    return null;
  };

  // Get total amount from all selected capital positions
  const getTotalAmountFromSelectedPositions = () => {
    // Get selected positions from deal's extra_fields or from requested criteria
    const currentDeal = getCurrentDeal();
    let selectedPositions: string[] = [];
    
    if (currentDeal?.extra_fields?.capital_position && Array.isArray(currentDeal.extra_fields.capital_position)) {
      selectedPositions = currentDeal.extra_fields.capital_position;
    } else {
      // Fall back to criteria that has is_requested: true
      const requestedCriteria = getCurrentCriteria().find(criteria => criteria.is_requested);
      if (requestedCriteria?.capital_position && Array.isArray(requestedCriteria.capital_position)) {
        selectedPositions = requestedCriteria.capital_position;
      }
    }

    // Calculate total from all selected positions
    let totalAmount = 0;
    const positionDetails: { position: string; amount: number }[] = [];

    selectedPositions.forEach(position => {
      const dealSize = getDealSizeForPosition(position);
      if (dealSize?.max) {
        const amount = Number(dealSize.max) || 0;
        totalAmount += amount;
        positionDetails.push({ position, amount });
      }
    });

    return {
      totalAmount,
      positionDetails,
      selectedPositions
    };
  };

  // Update field across all criteria when "Apply to All" is checked
  const updateAllCriteria = (field: keyof InvestmentCriteria, value: any, currentIndex: number) => {
    if (!applyToAll) {
      // Update only the current criteria
      const updatedCriteria = [...getCurrentCriteria()];
      updatedCriteria[currentIndex] = {
        ...updatedCriteria[currentIndex],
        [field]: value
      };
      setEditedCriteria(updatedCriteria);
    } else {
      // Update all criteria in real-time
      const updatedCriteria = getCurrentCriteria().map((criteria, index) => ({
        ...criteria,
        [field]: value
      }));
      setEditedCriteria(updatedCriteria);
    }
  };

  // Real-time update function for input fields
  const updateAllCriteriaRealTime = (field: keyof InvestmentCriteria, value: any, currentIndex: number) => {
    if (!applyToAll) {
      // Update only the current criteria
      const updatedCriteria = [...getCurrentCriteria()];
      updatedCriteria[currentIndex] = {
        ...updatedCriteria[currentIndex],
        [field]: value
      };
      setEditedCriteria(updatedCriteria);
    } else {
      // Update all criteria in real-time as user types
      const updatedCriteria = getCurrentCriteria().map((criteria, index) => ({
        ...criteria,
        [field]: value
      }));
      setEditedCriteria(updatedCriteria);
    }
  };

  // Get synchronized value for input fields when "Apply to All" is checked
  const getSynchronizedValue = (field: keyof InvestmentCriteria, currentIndex: number) => {
    if (!applyToAll) {
      // Return individual criteria value
      return getCurrentCriteria()[currentIndex]?.[field];
    } else {
      // Return the value from the first criteria (they should all be the same)
      return getCurrentCriteria()[0]?.[field];
    }
  };

  // Get synchronized array value for input fields when "Apply to All" is checked
  const getSynchronizedArrayValue = (field: keyof InvestmentCriteria, currentIndex: number): string[] => {
    if (!applyToAll) {
      // Return individual criteria value
      const value = getCurrentCriteria()[currentIndex]?.[field];
      return Array.isArray(value) ? value : [];
    } else {
      // Return the value from the first criteria (they should all be the same)
      const value = getCurrentCriteria()[0]?.[field];
      return Array.isArray(value) ? value : [];
    }
  };

  // Get synchronized numeric value for input fields when "Apply to All" is checked
  const getSynchronizedNumericValue = (field: keyof InvestmentCriteria, currentIndex: number): number | null => {
    if (!applyToAll) {
      // Return individual criteria value
      const value = getCurrentCriteria()[currentIndex]?.[field];
      
      // Handle different data types
      if (typeof value === 'number') {
        return value;
      } else if (typeof value === 'string') {
        const parsed = parseFloat(value);
        return isNaN(parsed) ? null : parsed;
      } else if (value === null || value === undefined) {
        return null;
      }
      return null;
    } else {
      // Return the value from the first criteria (they should all be the same)
      const value = getCurrentCriteria()[0]?.[field];
      
      // Handle different data types
      if (typeof value === 'number') {
        return value;
      } else if (typeof value === 'string') {
        const parsed = parseFloat(value);
        return isNaN(parsed) ? null : parsed;
      } else if (value === null || value === undefined) {
        return null;
      }
      return null;
    }
  };

  // Handle investment criteria deletion
  const handleDeleteCriteria = async () => {
    if (!criteriaToDelete) return;

    try {
      // Remove from local state immediately
      const updatedCriteria = getCurrentCriteria().filter((_, i) => i !== criteriaToDelete.index);
      
      if (isEditing) {
        setEditedCriteria(updatedCriteria);
      } else {
        // Update the deal state directly
        setDeal(prev => prev ? {
          ...prev,
          investment_criteria: updatedCriteria
        } : null);
      }

      // Send delete request to backend
      if (criteriaToDelete.id) {
        console.log('Deleting criteria ID:', criteriaToDelete.id, 'Type:', typeof criteriaToDelete.id);
        const response = await fetch(`/api/investment-criteria/${criteriaToDelete.id}`, {
          method: 'DELETE',
        });

        console.log('Delete response status:', response.status);
        
        if (!response.ok) {
          const errorData = await response.json();
          console.error('Delete response error:', errorData);
          throw new Error(errorData.error || 'Failed to delete investment criteria');
        }
        
        console.log('Delete successful');
      } else {
        console.log('No criteria_id found');
      }

      toast.success('Investment criteria deleted successfully');
    } catch (error) {
      toast.error('Error deleting investment criteria');
      // Revert local state if backend call failed
      if (!isEditing) {
        setDeal(prev => prev ? {
          ...prev,
          investment_criteria: getCurrentCriteria()
        } : null);
      }
    } finally {
      setCriteriaToDelete(null);
    }
  };

  // Get overview data from deal and investment criteria with capital stack hierarchy
  // This is used for the overall deal summary and includes ALL criteria
  const getOverviewData = () => {
    const currentCriteria = getCurrentCriteria();
    
    // Step 1: Determine the Common Equity and its Sources
    let commonEquity = 0;
    let gpContribution = 0;
    let lpContribution = 0;
    
    // Check for GP and LP values first (Rule of Precedence)
    const gpCriteria = currentCriteria.filter(c => 
      c.capital_position?.some((pos: string) => pos.includes("GP") || pos.includes("General Partner"))
    );
    const lpCriteria = currentCriteria.filter(c => 
      c.capital_position?.some((pos: string) => pos.includes("LP") || pos.includes("Limited Partner"))
    );
    
    if (gpCriteria.length > 0 || lpCriteria.length > 0) {
      // Use GP and LP values to calculate Common Equity
      gpContribution = gpCriteria.reduce((sum, c) => sum + (Number(c.maximum_deal_size) || 0), 0);
      lpContribution = lpCriteria.reduce((sum, c) => sum + (Number(c.maximum_deal_size) || 0), 0);
      commonEquity = gpContribution + lpContribution;
    } else {
      // Check for Common Equity value directly
      const commonEquityCriteria = currentCriteria.filter(c => 
        c.capital_position?.some((pos: string) => pos.includes("Common Equity"))
      );
      
      if (commonEquityCriteria.length > 0) {
        commonEquity = commonEquityCriteria.reduce((sum, c) => sum + (Number(c.maximum_deal_size) || 0), 0);
        // Derive GP and LP using standard split
        gpContribution = commonEquity * 0.10;
        lpContribution = commonEquity * 0.90;
      }
    }
    
    // Step 2: Calculate Total Debt & Total Equity
    const seniorDebtCriteria = currentCriteria.filter(c => 
      c.capital_position?.some((pos: string) => pos.includes("Senior Debt"))
    );
    const mezzanineCriteria = currentCriteria.filter(c => 
      c.capital_position?.some((pos: string) => pos.includes("Mezzanine"))
    );
    const preferredEquityCriteria = currentCriteria.filter(c => 
      c.capital_position?.some((pos: string) => pos.includes("Preferred Equity"))
    );
    
    const totalSeniorDebt = seniorDebtCriteria.reduce((sum, c) => sum + (Number(c.maximum_deal_size) || 0), 0);
    const totalMezzanine = mezzanineCriteria.reduce((sum, c) => sum + (Number(c.maximum_deal_size) || 0), 0);
    const totalPreferredEquity = preferredEquityCriteria.reduce((sum, c) => sum + (Number(c.maximum_deal_size) || 0), 0);
    
    const totalDebt = totalSeniorDebt + totalMezzanine;
    const totalEquity = commonEquity + totalPreferredEquity;
    
    // Step 3: Calculate Total Project Cost
    const totalSources = totalDebt + totalEquity;
    
    // Note: Joint Venture and Co-GP are excluded from financial calculations
    // as they are informational labels, not financial components
    
    return {
      totalDebt,
      totalEquity,
      totalSources,
      totalSeniorDebt,
      totalMezzanine,
      totalCommonEquity: commonEquity,
      totalPreferredEquity,
      gpContribution,
      lpContribution,
      seniorDebtCriteria,
      mezzanineCriteria,
      commonEquityCriteria: currentCriteria.filter(c => 
        c.capital_position?.some((pos: string) => pos.includes("Common Equity"))
      ),
      preferredEquityCriteria,
      gpCriteria,
      lpCriteria
    };
  };

  // Get sources data - only includes criteria with include_in_sources = true
  // This is used for the Sources section display
  const getSourcesData = () => {
    const currentCriteria = getCurrentCriteria();
    
    // Filter criteria that have include_in_sources flag set to true
    const includedCriteria = currentCriteria.filter(c => c.include_in_sources ?? true);
    
    // Calculate sources using the same capital stack hierarchy rules
    let commonEquity = 0;
    let gpContribution = 0;
    let lpContribution = 0;
    
    // Check for GP and LP values first (Rule of Precedence)
    const gpCriteria = includedCriteria.filter(c => 
      c.capital_position?.some((pos: string) => pos.includes("GP") || pos.includes("General Partner"))
    );
    const lpCriteria = includedCriteria.filter(c => 
      c.capital_position?.some((pos: string) => pos.includes("LP") || pos.includes("Limited Partner"))
    );
    
    if (gpCriteria.length > 0 || lpCriteria.length > 0) {
      // Use GP and LP values to calculate Common Equity
      gpContribution = gpCriteria.reduce((sum, c) => sum + (Number(c.maximum_deal_size) || 0), 0);
      lpContribution = lpCriteria.reduce((sum, c) => sum + (Number(c.maximum_deal_size) || 0), 0);
      commonEquity = gpContribution + lpContribution;
    } else {
      // Check for Common Equity value directly
      const commonEquityCriteria = includedCriteria.filter(c => 
        c.capital_position?.some((pos: string) => pos.includes("Common Equity"))
      );
      
      if (commonEquityCriteria.length > 0) {
        commonEquity = commonEquityCriteria.reduce((sum, c) => sum + (Number(c.maximum_deal_size) || 0), 0);
        // Derive GP and LP using standard split
        gpContribution = commonEquity * 0.10;
        lpContribution = commonEquity * 0.90;
      }
    }
    
    // Calculate debt and equity components
    const seniorDebtCriteria = includedCriteria.filter(c => 
      c.capital_position?.some((pos: string) => pos.includes("Senior Debt"))
    );
    const mezzanineCriteria = includedCriteria.filter(c => 
      c.capital_position?.some((pos: string) => pos.includes("Mezzanine"))
    );
    const preferredEquityCriteria = includedCriteria.filter(c => 
      c.capital_position?.some((pos: string) => pos.includes("Preferred Equity"))
    );
    
    const totalSeniorDebt = seniorDebtCriteria.reduce((sum, c) => sum + (Number(c.maximum_deal_size) || 0), 0);
    const totalMezzanine = mezzanineCriteria.reduce((sum, c) => sum + (Number(c.maximum_deal_size) || 0), 0);
    const totalPreferredEquity = preferredEquityCriteria.reduce((sum, c) => sum + (Number(c.maximum_deal_size) || 0), 0);
    
    const totalDebt = totalSeniorDebt + totalMezzanine;
    const totalEquity = commonEquity + totalPreferredEquity;
    const totalSources = totalDebt + totalEquity;
    
    return {
      totalSources,
      totalSeniorDebt,
      totalMezzanine,
      totalCommonEquity: commonEquity,
      totalPreferredEquity,
      gpContribution,
      lpContribution,
      gpCriteria,
      lpCriteria
    };
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !deal) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Deal Not Found
            </h2>
            <p className="text-gray-600 mb-6">
              {error || "The requested deal could not be found."}
            </p>
            <Button onClick={() => router.push("/dashboard/deals")}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Deals
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const overviewData = getOverviewData();

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
            
                  // Go back if possible (Next.js router.back() is equivalent to history.back())
                  router.back();
                
              }}
          
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {deal?.deal_name || "Loading..."}
              </h1>
              {deal?.sponsor_name && (
                <p className="text-gray-600">Sponsored by {deal.sponsor_name}</p>
              )}
              {/* Current Tab Indicator */}
              <div className="mt-2">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {activeTab === 'overview' && 'Overview'}
                  {activeTab === 'debt' && 'Debt'}
                  {activeTab === 'equity' && 'Equity'}
                  {activeTab === 'deal-matching' && 'Deal Matching'}
                  {activeTab === 'contact' && 'Contact'}
                  {activeTab === 'investment-criteria' && 'Investment Criteria'}
                  {activeTab === 'jobs' && 'Jobs'}
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {/* V2 Toggle Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const url = new URL(window.location.href);
                url.pathname = url.pathname.replace('/deals/', '/deals/v2/');
                router.push(url.toString());
              }}
              className="flex items-center gap-2 bg-green-50 text-green-700 border-green-200 hover:bg-green-100"
            >
              <BarChart3 className="h-4 w-4" />
              Switch to V2
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (isEditing) {
                  // Cancel editing
                  setIsEditing(false);
                  setEditedDeal(null);
                  setEditedCriteria([]);
                } else {
                  // Start editing
                  setIsEditing(true);
                  setEditedDeal({
                    ...deal,
                    address: deal.address || "",
                    is_distressed: deal.is_distressed || false,
                    is_internal_only: deal.is_internal_only || false,
                  });
                  
                  // Initialize criteria with proper is_requested values
                  const initialCriteria = deal?.investment_criteria || [];
                  
                  // If no criteria has is_requested: true, set the first one as requested
                  const hasRequestedCriteria = initialCriteria.some(criteria => criteria.is_requested);
                  const initializedCriteria = hasRequestedCriteria 
                    ? initialCriteria 
                    : initialCriteria.map((criteria, index) => ({
                        ...criteria,
                        is_requested: index === 0 // Set first criteria as requested if none are requested
                      }));
                  
                  setEditedCriteria(initializedCriteria);
                }
              }}
              className="flex items-center gap-2"
            >
              <Edit className="h-4 w-4" />
              {isEditing ? "Cancel Edit" : "Edit Deal"}
            </Button>
            {isEditing && (
              <Button
                variant="default"
                size="sm"
                onClick={handleSaveInlineEdits}
                disabled={isSaving}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
              >
                <Save className="h-4 w-4" />
                {isSaving ? "Saving..." : "Save Changes"}
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRunRequirementExtraction}
              disabled={isRunningRequirementExtraction}
              className="flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              {isRunningRequirementExtraction ? "Running..." : "Run Requirement Extraction"}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={copyTabUrl}
              className="flex items-center gap-2"
              title="Copy current tab URL"
            >
              <FileText className="h-4 w-4" />
              Copy URL
            </Button>
          </div>
        </div>

        {/* Data Quality Banner */}
        {deal.data_quality_metrics && (
          <DataQualityBanner
            dataQualityMetrics={deal.data_quality_metrics}
            dealName={deal.deal_name || "Unnamed Deal"}
          />
        )}

        {/* Tabs */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Deal Details</h2>
            <div className="text-xs text-gray-500">
              Use Ctrl/Cmd + 1-7 for quick navigation
            </div>
          </div>
          <Tabs
            value={activeTab}
            onValueChange={handleTabChange}
            className="space-y-6"
          >
            <TabsList className="grid w-full mb-8 md:grid-cols-6 grid-cols-2">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="debt">Debt</TabsTrigger>
              <TabsTrigger value="equity">Equity</TabsTrigger>
              <TabsTrigger value="deal-matching">Deal Matching</TabsTrigger>
              <TabsTrigger value="contact">Contact</TabsTrigger>
              <TabsTrigger value="investment-criteria">Investment Criteria</TabsTrigger>
              <TabsTrigger value="jobs">Jobs</TabsTrigger>
            </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Deal Information */}
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      Deal Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Deal Name
                        </label>
                        {isEditing ? (
                          <Input
                            value={getCurrentDeal()?.deal_name || ""}
                            onChange={(e) => setEditedDeal({ ...editedDeal!, deal_name: e.target.value })}
                            className="mt-1"
                          />
                        ) : (
                          <p className="text-sm text-gray-900 mt-1">
                            {getCurrentDeal()?.deal_name || "N/A"}
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Ask - Capital Positions
                        </label>
                        {isEditing ? (
                          <div className="mt-1">
                                                        <MultiSelect
                              disabled={false}
                              options={capitalPositionOptions}
                              selected={selectedCapitalPositions}
                              onChange={handleCapitalPositionsChange}
                              placeholder="Select capital positions..."
                              className="w-full"
                            />
                            {getAllCapitalPositions().length === 0 && (
                              <p className="text-xs text-gray-500 mt-1">
                                No capital positions available. Add criteria first.
                              </p>
                            )}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-900 mt-1">
                            {(() => {
                              // First try to get from deal's extra_fields
                              const deal = getCurrentDeal();
                              if (deal?.extra_fields?.capital_position) {
                                return formatArrayField(deal.extra_fields.capital_position);
                              }
                              
                              // Fall back to criteria that has is_requested: true
                              const requestedCriteria = getCurrentCriteria().find(criteria => criteria.is_requested);
                              if (requestedCriteria?.capital_position) {
                                return formatArrayField(requestedCriteria.capital_position);
                              }
                              return "N/A";
                            })()}
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Ask - Amount
                        </label>
                        {isEditing ? (
                          <div className="mt-1">
                            {(() => {
                              const { totalAmount, positionDetails, selectedPositions } = getTotalAmountFromSelectedPositions();
                              
                              return (
                                <>
                                  <Input
                                    type="number"
                                    value={totalAmount || overviewData.totalSources || ""}
                                    onChange={(e) => {
                                      const newAmount = parseFloat(e.target.value) || 0;
                                      // For now, distribute the amount equally among selected positions
                                      // This is a simplified approach - you might want more sophisticated distribution logic
                                      if (selectedPositions.length > 0) {
                                        const amountPerPosition = newAmount / selectedPositions.length;
                                        const updatedCriteria = [...getCurrentCriteria()];
                                        
                                        selectedPositions.forEach(position => {
                                          const criteriaIndex = updatedCriteria.findIndex(c => 
                                            c.capital_position && Array.isArray(c.capital_position) && 
                                            c.capital_position.includes(position)
                                          );
                                          if (criteriaIndex !== -1) {
                                            updatedCriteria[criteriaIndex] = {
                                              ...updatedCriteria[criteriaIndex],
                                              maximum_deal_size: amountPerPosition
                                            };
                                          }
                                        });
                                        
                                        setEditedCriteria(updatedCriteria);
                                      }
                                    }}
                                    placeholder="Enter ask amount"
                                    className="w-full"
                                  />
                                  {selectedPositions.length > 0 ? (
                                    <p className="text-xs text-gray-500 mt-1">
                                      Based on {selectedPositions.join(', ')}: {formatCurrency(totalAmount)}
                                      {positionDetails.length > 0 && (
                                        <span className="block mt-1">
                                          {positionDetails.map(detail => 
                                            `${detail.position}: ${formatCurrency(detail.amount)}`
                                          ).join(', ')}
                                        </span>
                                      )}
                                    </p>
                                  ) : (
                                    <p className="text-xs text-gray-500 mt-1">
                                      Select capital positions to see deal size range
                                    </p>
                                  )}
                                </>
                              );
                            })()}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-900 mt-1">
                            {(() => {
                              const { totalAmount, selectedPositions } = getTotalAmountFromSelectedPositions();
                              
                              if (selectedPositions.length > 0) {
                                return formatCurrency(totalAmount);
                              } else {
                                return formatCurrency(overviewData.totalSources);
                              }
                            })()}
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Deal Date
                        </label>
                        {isEditing ? (
                          <Input
                            type="date"
                            value={getCurrentDeal()?.deal_date ? new Date(getCurrentDeal()!.deal_date!).toISOString().split('T')[0] : ""}
                            onChange={(e) => setEditedDeal({ ...editedDeal!, deal_date: e.target.value })}
                            className="mt-1"
                          />
                        ) : (
                          <p className="text-sm text-gray-900 mt-1">
                            {getCurrentDeal()?.deal_date 
                              ? new Date(getCurrentDeal()!.deal_date!).toLocaleDateString()
                              : getCurrentDeal()?.created_at 
                                ? new Date(getCurrentDeal()!.created_at!).toLocaleDateString()
                                : "N/A"
                            }
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Deal Flags
                        </label>
                        {isEditing ? (
                          <div className="mt-1 space-y-2">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="is_distressed"
                                checked={getCurrentDeal()?.is_distressed || false}
                                onCheckedChange={(checked) => 
                                  setEditedDeal({ ...editedDeal!, is_distressed: checked as boolean })
                                }
                              />
                              <label htmlFor="is_distressed" className="text-sm text-gray-700">
                                Distressed Deal
                              </label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="is_internal_only"
                                checked={getCurrentDeal()?.is_internal_only || false}
                                onCheckedChange={(checked) => 
                                  setEditedDeal({ ...editedDeal!, is_internal_only: checked as boolean })
                                }
                              />
                              <label htmlFor="is_internal_only" className="text-sm text-gray-700">
                                Internal Only (Do Not Sync with CRM)
                              </label>
                            </div>
                          </div>
                        ) : (
                          <div className="mt-1 space-y-1">
                            {getCurrentDeal()?.is_distressed && (
                              <Badge variant="destructive" className="text-xs">
                                Distressed Deal
                              </Badge>
                            )}
                            {getCurrentDeal()?.is_internal_only && (
                              <Badge variant="secondary" className="text-xs">
                                Internal Only
                              </Badge>
                            )}
                            {!getCurrentDeal()?.is_distressed && !getCurrentDeal()?.is_internal_only && (
                              <span className="text-sm text-gray-500">No special flags</span>
                            )}
                          </div>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Timeline
                        </label>
                        {isEditing ? (
                          <Select
                            value={getCurrentDeal()?.deal_stage || ""}
                            onValueChange={(value) => setEditedDeal({ ...editedDeal!, deal_stage: value })}
                          >
                            <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Select deal stage" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="pre-development">Pre-Development</SelectItem>
                              <SelectItem value="development">Development</SelectItem>
                              <SelectItem value="construction">Construction</SelectItem>
                              <SelectItem value="stabilized">Stabilized</SelectItem>
                              <SelectItem value="value-add">Value-Add</SelectItem>
                            </SelectContent>
                          </Select>
                        ) : (
                          <p className="text-sm text-gray-900 mt-1">
                            {getCurrentDeal()?.deal_stage || "N/A"}
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Strategy
                        </label>
                        <p className="text-sm text-gray-900 mt-1">
                          {getCurrentCriteria().length > 0 
                            ? formatArrayField(getCurrentCriteria()[0]?.strategies)
                            : "N/A"
                          }
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Property Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      Property Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Project Name
                        </label>
                        {isEditing ? (
                          <Input
                            value={getCurrentDeal()?.deal_name || ""}
                            onChange={(e) => setEditedDeal({ ...editedDeal!, deal_name: e.target.value })}
                            className="mt-1"
                          />
                        ) : (
                          <p className="text-sm text-gray-900 mt-1">
                            {getCurrentDeal()?.deal_name || "N/A"}
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Address
                        </label>
                        {isEditing ? (
                          <Input
                            value={getCurrentDeal()?.address || ""}
                            onChange={(e) => setEditedDeal({ ...editedDeal!, address: e.target.value })}
                            className="mt-1"
                            placeholder="Enter property address"
                          />
                        ) : (
                          <p className="text-sm text-gray-900 mt-1">
                            {getCurrentDeal()?.address || "N/A"}
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          State
                        </label>
                        {isEditing ? (
                          <div className="mt-1">
                            <div className="flex items-center gap-2 mb-2">
                              <label className="text-xs text-gray-600">
                                <input
                                  type="checkbox"
                                  checked={!individualLocationEditing}
                                  onChange={(e) => setIndividualLocationEditing(!e.target.checked)}
                                  className="mr-1"
                                />
                                Edit all criteria together
                              </label>
                            </div>
                            {(() => {
                              const allStates = getCurrentCriteria().map(c => c.state).flat().filter(Boolean);
                              const uniqueStates = [...new Set(allStates)];
                              
                              if (uniqueStates.length === 1 || !individualLocationEditing) {
                                // All criteria have the same state or bulk editing - show one input
                                return (
                                  <Input
                                    value={uniqueStates[0] || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = getCurrentCriteria().map(criteria => ({
                                        ...criteria,
                                        state: [e.target.value]
                                      }));
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Enter state"
                                  />
                                );
                              } else {
                                // Different states and individual editing - show individual inputs with criteria numbers
                                return getCurrentCriteria().map((criteria, index) => (
                                  <div key={index} className="mb-2">
                                    <div className="text-xs text-gray-500 mb-1">Criteria {index + 1}</div>
                                    <Input
                                      value={getSynchronizedArrayValue('state', index).join(", ") || ""}
                                      onChange={(e) => {
                                        updateAllCriteriaRealTime('state', e.target.value.split(", ").filter(s => s.trim()), index);
                                      }}
                                      placeholder="Enter states (comma separated)"
                                    />
                                  </div>
                                ));
                              }
                            })()}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-900 mt-1">
                            {getCurrentCriteria().length > 0 
                              ? formatArrayField(getCurrentCriteria()[0]?.state)
                              : "N/A"
                            }
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          City
                        </label>
                        {isEditing ? (
                          <div className="mt-1">
                            {(() => {
                              const allCities = getCurrentCriteria().map(c => c.city).flat().filter(Boolean);
                              const uniqueCities = [...new Set(allCities)];
                              
                              if (uniqueCities.length === 1 || !individualLocationEditing) {
                                // All criteria have the same city or bulk editing - show one input
                                return (
                                  <Input
                                    value={uniqueCities[0] || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = getCurrentCriteria().map(criteria => ({
                                        ...criteria,
                                        city: [e.target.value]
                                      }));
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Enter city"
                                  />
                                );
                              } else {
                                // Different cities and individual editing - show individual inputs with criteria numbers
                                return getCurrentCriteria().map((criteria, index) => (
                                  <div key={index} className="mb-2">
                                    <div className="text-xs text-gray-500 mb-1">Criteria {index + 1}</div>
                                    <Input
                                      value={getSynchronizedArrayValue('city', index).join(", ") || ""}
                                      onChange={(e) => {
                                        updateAllCriteriaRealTime('city', e.target.value.split(", ").filter(s => s.trim()), index);
                                      }}
                                      placeholder="Enter cities (comma separated)"
                                    />
                                  </div>
                                ));
                              }
                            })()}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-900 mt-1">
                            {getCurrentCriteria().length > 0 
                              ? formatArrayField(getCurrentCriteria()[0]?.city)
                              : "N/A"
                            }
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Zip Code
                        </label>
                        {isEditing ? (
                          <Input
                            value={getCurrentDeal()?.zip_code || ""}
                            onChange={(e) => setEditedDeal({ ...editedDeal!, zip_code: e.target.value })}
                            className="mt-1"
                            placeholder="Enter zip code"
                          />
                        ) : (
                          <p className="text-sm text-gray-900 mt-1">
                            {getCurrentDeal()?.zip_code || "N/A"}
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Neighborhood
                        </label>
                        {isEditing ? (
                          <Input
                            value={getCurrentDeal()?.neighborhood || ""}
                            onChange={(e) => setEditedDeal({ ...editedDeal!, neighborhood: e.target.value })}
                            className="mt-1"
                            placeholder="Enter neighborhood"
                          />
                        ) : (
                          <p className="text-sm text-gray-900 mt-1">
                            {getCurrentDeal()?.neighborhood || "N/A"}
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Country
                        </label>
                        {isEditing ? (
                          <div className="mt-1">
                            {(() => {
                              const allCountries = getCurrentCriteria().map(c => c.country).flat().filter(Boolean);
                              const uniqueCountries = [...new Set(allCountries)];
                              
                              if (uniqueCountries.length === 1 || !individualLocationEditing) {
                                // All criteria have the same country or bulk editing - show one input
                                return (
                                  <Input
                                    value={uniqueCountries[0] || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = getCurrentCriteria().map(criteria => ({
                                        ...criteria,
                                        country: [e.target.value]
                                      }));
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Enter country"
                                  />
                                );
                              } else {
                                // Different countries and individual editing - show individual inputs with criteria numbers
                                return getCurrentCriteria().map((criteria, index) => (
                                  <div key={index} className="mb-2">
                                    <div className="text-xs text-gray-500 mb-1">Criteria {index + 1}</div>
                                    <Input
                                      value={getSynchronizedArrayValue('country', index).join(", ") || ""}
                                      onChange={(e) => {
                                        updateAllCriteriaRealTime('country', e.target.value.split(", ").filter(s => s.trim()), index);
                                      }}
                                      placeholder="Enter countries (comma separated)"
                                    />
                                  </div>
                                ));
                              }
                            })()}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-900 mt-1">
                            {getCurrentCriteria().length > 0 
                              ? formatArrayField(getCurrentCriteria()[0]?.country)
                              : "N/A"
                            }
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Region
                        </label>
                        {isEditing ? (
                          <div className="mt-1">
                            {(() => {
                              const allRegions = getCurrentCriteria().map(c => c.region).flat().filter(Boolean);
                              const uniqueRegions = [...new Set(allRegions)];
                              
                              if (uniqueRegions.length === 1 || !individualLocationEditing) {
                                // All criteria have the same region or bulk editing - show one input
                                return (
                                  <Input
                                    value={uniqueRegions[0] || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = getCurrentCriteria().map(criteria => ({
                                        ...criteria,
                                        region: [e.target.value]
                                      }));
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Enter region"
                                  />
                                );
                              } else {
                                // Different regions and individual editing - show individual inputs with criteria numbers
                                return getCurrentCriteria().map((criteria, index) => (
                                  <div key={index} className="mb-2">
                                    <div className="text-xs text-gray-500 mb-1">Criteria {index + 1}</div>
                                    <Input
                                      value={getSynchronizedArrayValue('region', index).join(", ") || ""}
                                      onChange={(e) => {
                                        updateAllCriteriaRealTime('region', e.target.value.split(", ").filter(s => s.trim()), index);
                                      }}
                                      placeholder="Enter regions (comma separated)"
                                    />
                                  </div>
                                ));
                              }
                            })()}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-900 mt-1">
                            {getCurrentCriteria().length > 0 
                              ? formatArrayField(getCurrentCriteria()[0]?.region)
                              : "N/A"
                            }
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Property Description */}
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Description
                      </label>
                      {isEditing ? (
                        <Input
                          value={getCurrentDeal()?.property_description || ""}
                          onChange={(e) => setEditedDeal({ ...editedDeal!, property_description: e.target.value })}
                          className="mt-1"
                          placeholder="Enter property description"
                        />
                      ) : (
                        <p className="text-sm text-gray-900 mt-1">
                          {getCurrentDeal()?.property_description || "N/A"}
                        </p>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Property Type
                        </label>
                        {isEditing ? (
                          <div className="mt-1">
                            <div className="flex items-center gap-2 mb-2">
                              <label className="text-xs text-gray-600">
                                <input
                                  type="checkbox"
                                  checked={!individualPropertyEditing}
                                  onChange={(e) => setIndividualPropertyEditing(!e.target.checked)}
                                  className="mr-1"
                                />
                                Edit all criteria together
                              </label>
                            </div>
                            {(() => {
                              const allPropertyTypes = getCurrentCriteria().map(c => c.property_types).flat().filter(Boolean);
                              const uniquePropertyTypes = [...new Set(allPropertyTypes)];
                              
                              if (uniquePropertyTypes.length === 1 || !individualPropertyEditing) {
                                // All criteria have the same property types or bulk editing - show one input
                                return (
                                  <Input
                                    value={uniquePropertyTypes[0] || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = getCurrentCriteria().map(criteria => ({
                                        ...criteria,
                                        property_types: [e.target.value]
                                      }));
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Enter property types"
                                  />
                                );
                              } else {
                                // Different property types and individual editing - show individual inputs with criteria numbers
                                return getCurrentCriteria().map((criteria, index) => (
                                  <div key={index} className="mb-2">
                                    <div className="text-xs text-gray-500 mb-1">Criteria {index + 1}</div>
                                    <Input
                                      value={criteria.property_types?.join(", ") || ""}
                                      onChange={(e) => {
                                        const updatedCriteria = [...getCurrentCriteria()];
                                        updatedCriteria[index] = {
                                          ...updatedCriteria[index],
                                          property_types: e.target.value.split(", ").filter(s => s.trim())
                                        };
                                        setEditedCriteria(updatedCriteria);
                                      }}
                                      placeholder="Enter property types (comma separated)"
                                    />
                                  </div>
                                ));
                              }
                            })()}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-900 mt-1">
                            {getCurrentCriteria().length > 0 
                              ? formatArrayField(getCurrentCriteria()[0]?.property_types)
                              : "N/A"
                            }
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Subproperty Type
                        </label>
                        {isEditing ? (
                          <div className="mt-1">
                            <div className="flex items-center gap-2 mb-2">
                              <label className="text-xs text-gray-600">
                                <input
                                  type="checkbox"
                                  checked={!individualPropertyEditing}
                                  onChange={(e) => setIndividualPropertyEditing(!e.target.checked)}
                                  className="mr-1"
                                />
                                Edit all criteria together
                              </label>
                            </div>
                            {(() => {
                              const allSubPropertyTypes = getCurrentCriteria().map(c => c.property_sub_categories).flat().filter(Boolean);
                              const uniqueSubPropertyTypes = [...new Set(allSubPropertyTypes)];
                              
                              if (uniqueSubPropertyTypes.length === 1 || !individualPropertyEditing) {
                                // All criteria have the same sub property types or bulk editing - show one input
                                return (
                                  <Input
                                    value={uniqueSubPropertyTypes[0] || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = getCurrentCriteria().map(criteria => ({
                                        ...criteria,
                                        property_sub_categories: [e.target.value]
                                      }));
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Enter subproperty types"
                                  />
                                );
                              } else {
                                // Different sub property types and individual editing - show individual inputs with criteria numbers
                                return getCurrentCriteria().map((criteria, index) => (
                                  <div key={index} className="mb-2">
                                    <div className="text-xs text-gray-500 mb-1">Criteria {index + 1}</div>
                                    <Input
                                      value={criteria.property_sub_categories?.join(", ") || ""}
                                      onChange={(e) => {
                                        const updatedCriteria = [...getCurrentCriteria()];
                                        updatedCriteria[index] = {
                                          ...updatedCriteria[index],
                                          property_sub_categories: e.target.value.split(", ").filter(s => s.trim())
                                        };
                                        setEditedCriteria(updatedCriteria);
                                      }}
                                      placeholder="Enter subproperty types (comma separated)"
                                    />
                                  </div>
                                ));
                              }
                            })()}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-900 mt-1">
                            {getCurrentCriteria().length > 0 
                              ? formatArrayField(getCurrentCriteria()[0]?.property_sub_categories)
                              : "N/A"
                            }
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Sources */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <DollarSign className="h-5 w-5" />
                      Sources
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Total
                        </label>
                        <p className="text-sm text-gray-900 mt-1">
                          {formatCurrency(getSourcesData().totalSources)}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Senior Debt
                        </label>
                        <p className="text-sm text-gray-900 mt-1">
                          {formatCurrency(getSourcesData().totalSeniorDebt)}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Mezzanine
                        </label>
                        <p className="text-sm text-gray-900 mt-1">
                          {formatCurrency(getSourcesData().totalMezzanine)}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          General Partner (GP)
                        </label>
                        <p className="text-sm text-gray-900 mt-1">
                          {formatCurrency(getSourcesData().gpContribution)}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Limited Partner (LP)
                        </label>
                        <p className="text-sm text-gray-900 mt-1">
                          {formatCurrency(getSourcesData().lpContribution)}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Preferred Equity
                        </label>
                        <p className="text-sm text-gray-900 mt-1">
                          {formatCurrency(getSourcesData().totalPreferredEquity)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>


              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Quick Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle>Capital Stack Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Total Project Cost */}
                    <div className="border-b border-gray-200 pb-3">
                      <label className="text-sm font-medium text-gray-600">
                        Total Project Cost
                      </label>
                      <p className="text-xl font-bold text-gray-900">
                        {formatCurrency(overviewData.totalSources)}
                      </p>
                    </div>
                    
                    {/* Debt Section */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-600">Total Debt</span>
                        <span className="text-lg font-semibold text-blue-600">
                          {formatCurrency(overviewData.totalDebt)}
                        </span>
                      </div>
                      <div className="ml-4 space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-500">Senior Debt</span>
                          <span className="text-blue-600">{formatCurrency(overviewData.totalSeniorDebt)}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-500">Mezzanine Debt</span>
                          <span className="text-blue-600">{formatCurrency(overviewData.totalMezzanine)}</span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Equity Section */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-600">Total Equity</span>
                        <span className="text-lg font-semibold text-green-600">
                          {formatCurrency(overviewData.totalEquity)}
                        </span>
                      </div>
                      <div className="ml-4 space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-500">Common Equity</span>
                          <span className="text-green-600">{formatCurrency(overviewData.totalCommonEquity)}</span>
                        </div>
                        {overviewData.gpContribution > 0 && (
                          <div className="ml-4 flex items-center justify-between text-xs">
                            <span className="text-gray-400">GP Contribution</span>
                            <span className="text-green-500">{formatCurrency(overviewData.gpContribution)}</span>
                          </div>
                        )}
                        {overviewData.lpContribution > 0 && (
                          <div className="ml-4 flex items-center justify-between text-xs">
                            <span className="text-gray-400">LP Contribution</span>
                            <span className="text-green-500">{formatCurrency(overviewData.lpContribution)}</span>
                          </div>
                        )}
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-500">Preferred Equity</span>
                          <span className="text-green-600">{formatCurrency(overviewData.totalPreferredEquity)}</span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Note about structural roles */}
                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex items-start gap-2">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                        <div className="text-xs text-yellow-800">
                          <strong>Note:</strong> Joint Venture and Co-GP are informational labels and are not included in financial calculations.
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Files Section */}
            <div className="mt-8">
              <DealFiles dealId={dealId} />
            </div>
          </TabsContent>

          {/* Debt Tab */}
          <TabsContent value="debt">
            <div className="space-y-6">
              {debtCriteria.length > 0 ? (
                debtCriteria.map((criteria, index) => (
                  <Card key={criteria.criteria_id || index}>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <CreditCard className="h-5 w-5" />
                        Financing Criteria - {formatArrayField(criteria.capital_position)}
                        {criteria.is_requested && (
                          <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
                            Requested
                          </Badge>
                        )}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Capital Position
                          </label>
                          {isEditing ? (
                            <Input
                              value={criteria.capital_position?.join(", ") || ""}
                              onChange={(e) => {
                                const updatedCriteria = [...getCurrentCriteria()];
                                const criteriaIndex = getCurrentCriteria().findIndex(c => c.criteria_id === criteria.criteria_id);
                                if (criteriaIndex !== -1) {
                                  updatedCriteria[criteriaIndex] = {
                                    ...updatedCriteria[criteriaIndex],
                                    capital_position: e.target.value.split(", ").filter(s => s.trim())
                                  };
                                  setEditedCriteria(updatedCriteria);
                                }
                              }}
                              className="mt-1"
                              placeholder="Enter capital positions (comma separated)"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {formatArrayField(criteria.capital_position)}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Loan Amount
                          </label>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={criteria.maximum_deal_size || ""}
                              onChange={(e) => {
                                const updatedCriteria = [...getCurrentCriteria()];
                                const criteriaIndex = getCurrentCriteria().findIndex(c => c.criteria_id === criteria.criteria_id);
                                if (criteriaIndex !== -1) {
                                  updatedCriteria[criteriaIndex] = {
                                    ...updatedCriteria[criteriaIndex],
                                    maximum_deal_size: parseFloat(e.target.value) || null
                                  };
                                  setEditedCriteria(updatedCriteria);
                                }
                              }}
                              className="mt-1"
                              placeholder="Enter loan amount"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {formatCurrency(criteria.maximum_deal_size)}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Loan Type
                          </label>
                          {isEditing ? (
                            <Input
                              value={criteria.loan_type?.join(", ") || ""}
                              onChange={(e) => {
                                const updatedCriteria = [...getCurrentCriteria()];
                                const criteriaIndex = getCurrentCriteria().findIndex(c => c.criteria_id === criteria.criteria_id);
                                if (criteriaIndex !== -1) {
                                  updatedCriteria[criteriaIndex] = {
                                    ...updatedCriteria[criteriaIndex],
                                    loan_type: e.target.value.split(", ").filter(s => s.trim())
                                  };
                                  setEditedCriteria(updatedCriteria);
                                }
                              }}
                              className="mt-1"
                              placeholder="Enter loan types (comma separated)"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {formatArrayField(criteria.loan_type)}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            LTC
                          </label>
                          {isEditing ? (
                            <Input
                              type="number"
                              step="0.01"
                              value={criteria.loan_to_cost_max || ""}
                              onChange={(e) => {
                                const updatedCriteria = [...getCurrentCriteria()];
                                const criteriaIndex = getCurrentCriteria().findIndex(c => c.criteria_id === criteria.criteria_id);
                                if (criteriaIndex !== -1) {
                                  updatedCriteria[criteriaIndex] = {
                                    ...updatedCriteria[criteriaIndex],
                                    loan_to_cost_max: parseFloat(e.target.value) || null
                                  };
                                  setEditedCriteria(updatedCriteria);
                                }
                              }}
                              className="mt-1"
                              placeholder="Enter LTC percentage"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {formatPercentage(criteria.loan_to_cost_max)}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            LTV
                          </label>
                          {isEditing ? (
                            <Input
                              type="number"
                              step="0.01"
                              value={criteria.loan_to_value_max || ""}
                              onChange={(e) => {
                                const updatedCriteria = [...getCurrentCriteria()];
                                const criteriaIndex = getCurrentCriteria().findIndex(c => c.criteria_id === criteria.criteria_id);
                                if (criteriaIndex !== -1) {
                                  updatedCriteria[criteriaIndex] = {
                                    ...updatedCriteria[criteriaIndex],
                                    loan_to_value_max: parseFloat(e.target.value) || null
                                  };
                                  setEditedCriteria(updatedCriteria);
                                }
                              }}
                              className="mt-1"
                              placeholder="Enter LTV percentage"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {formatPercentage(criteria.loan_to_value_max)}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Interest Rate
                          </label>
                          {isEditing ? (
                            <Input
                              type="number"
                              step="0.01"
                              value={criteria.interest_rate || ""}
                              onChange={(e) => {
                                const updatedCriteria = [...getCurrentCriteria()];
                                const criteriaIndex = getCurrentCriteria().findIndex(c => c.criteria_id === criteria.criteria_id);
                                if (criteriaIndex !== -1) {
                                  updatedCriteria[criteriaIndex] = {
                                    ...updatedCriteria[criteriaIndex],
                                    interest_rate: parseFloat(e.target.value) || null
                                  };
                                  setEditedCriteria(updatedCriteria);
                                }
                              }}
                              className="mt-1"
                              placeholder="Enter interest rate percentage"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {formatPercentage(criteria.interest_rate)}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Structured Loan Tranche
                          </label>
                          <p className="text-sm text-gray-900 mt-1">
                            {formatArrayField(criteria.structured_loan_tranche)}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Recourse
                          </label>
                          <p className="text-sm text-gray-900 mt-1">
                            {formatArrayField(criteria.recourse_loan)}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            DSCR
                          </label>
                          <p className="text-sm text-gray-900 mt-1">
                            {formatNumber(criteria.max_loan_dscr)}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Closing Time
                          </label>
                          <p className="text-sm text-gray-900 mt-1">
                            {formatNumber(criteria.closing_time_weeks, "weeks")}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Origination Fee
                          </label>
                          <p className="text-sm text-gray-900 mt-1">
                            {criteria.loan_origination_fee_min && criteria.loan_origination_fee_max
                              ? `${formatPercentage(criteria.loan_origination_fee_min)} - ${formatPercentage(criteria.loan_origination_fee_max)}`
                              : formatPercentage(criteria.loan_origination_fee_min || criteria.loan_origination_fee_max)
                            }
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Exit Fee
                          </label>
                          <p className="text-sm text-gray-900 mt-1">
                            {criteria.loan_exit_fee_min && criteria.loan_exit_fee_max
                              ? `${formatPercentage(criteria.loan_exit_fee_min)} - ${formatPercentage(criteria.loan_exit_fee_max)}`
                              : formatPercentage(criteria.loan_exit_fee_min || criteria.loan_exit_fee_max)
                            }
                          </p>
                        </div>
                      </div>
                      {criteria.notes && (
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Notes
                          </label>
                          <p className="text-sm text-gray-900 mt-1">
                            {criteria.notes}
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card>
                  <CardContent className="text-center py-8">
                    <p className="text-gray-500">No debt criteria found for this deal.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* Equity Tab */}
          <TabsContent value="equity">
            <div className="space-y-6">
              {equityCriteria.length > 0 ? (
                equityCriteria.map((criteria, index) => (
                  <Card key={criteria.criteria_id || index}>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <PieChart className="h-5 w-5" />
                        Investment Criteria - {formatArrayField(criteria.capital_position)}
                        {criteria.is_requested && (
                          <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
                            Requested
                          </Badge>
                        )}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Capital Position
                          </label>
                          {isEditing ? (
                            <Input
                              value={criteria.capital_position?.join(", ") || ""}
                              onChange={(e) => {
                                const updatedCriteria = [...getCurrentCriteria()];
                                const criteriaIndex = getCurrentCriteria().findIndex(c => c.criteria_id === criteria.criteria_id);
                                if (criteriaIndex !== -1) {
                                  updatedCriteria[criteriaIndex] = {
                                    ...updatedCriteria[criteriaIndex],
                                    capital_position: e.target.value.split(", ").filter(s => s.trim())
                                  };
                                  setEditedCriteria(updatedCriteria);
                                }
                              }}
                              className="mt-1"
                              placeholder="Enter capital positions (comma separated)"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {formatArrayField(criteria.capital_position)}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Investment Amount
                          </label>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={criteria.maximum_deal_size || ""}
                              onChange={(e) => {
                                const updatedCriteria = [...getCurrentCriteria()];
                                const criteriaIndex = getCurrentCriteria().findIndex(c => c.criteria_id === criteria.criteria_id);
                                if (criteriaIndex !== -1) {
                                  updatedCriteria[criteriaIndex] = {
                                    ...updatedCriteria[criteriaIndex],
                                    maximum_deal_size: parseFloat(e.target.value) || null
                                  };
                                  setEditedCriteria(updatedCriteria);
                                }
                              }}
                              className="mt-1"
                              placeholder="Enter investment amount"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {formatCurrency(criteria.maximum_deal_size)}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Attachment Point
                          </label>
                          {isEditing ? (
                            <Input
                              type="number"
                              step="0.01"
                              value={criteria.loan_to_value_max || ""}
                              onChange={(e) => {
                                const updatedCriteria = [...getCurrentCriteria()];
                                const criteriaIndex = getCurrentCriteria().findIndex(c => c.criteria_id === criteria.criteria_id);
                                if (criteriaIndex !== -1) {
                                  updatedCriteria[criteriaIndex] = {
                                    ...updatedCriteria[criteriaIndex],
                                    loan_to_value_max: parseFloat(e.target.value) || null
                                  };
                                  setEditedCriteria(updatedCriteria);
                                }
                              }}
                              className="mt-1"
                              placeholder="Enter attachment point percentage"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {formatPercentage(criteria.loan_to_value_max)}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Internal Rate of Return (IRR)
                          </label>
                          {isEditing ? (
                            <Input
                              type="number"
                              step="0.01"
                              value={criteria.target_return || ""}
                              onChange={(e) => {
                                const updatedCriteria = [...getCurrentCriteria()];
                                const criteriaIndex = getCurrentCriteria().findIndex(c => c.criteria_id === criteria.criteria_id);
                                if (criteriaIndex !== -1) {
                                  updatedCriteria[criteriaIndex] = {
                                    ...updatedCriteria[criteriaIndex],
                                    target_return: parseFloat(e.target.value) || null
                                  };
                                  setEditedCriteria(updatedCriteria);
                                }
                              }}
                              className="mt-1"
                              placeholder="Enter IRR percentage"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {formatPercentage(criteria.target_return)}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Equity Multiple (EM)
                          </label>
                          {isEditing ? (
                            <Input
                              type="number"
                              step="0.01"
                              value={criteria.historical_em || ""}
                              onChange={(e) => {
                                const updatedCriteria = [...getCurrentCriteria()];
                                const criteriaIndex = getCurrentCriteria().findIndex(c => c.criteria_id === criteria.criteria_id);
                                if (criteriaIndex !== -1) {
                                  updatedCriteria[criteriaIndex] = {
                                    ...updatedCriteria[criteriaIndex],
                                    historical_em: parseFloat(e.target.value) || null
                                  };
                                  setEditedCriteria(updatedCriteria);
                                }
                              }}
                              className="mt-1"
                              placeholder="Enter equity multiple"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {formatNumber(criteria.historical_em)}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Hold Period
                          </label>
                          {isEditing ? (
                            <Input
                              type="number"
                              value={criteria.max_hold_period || ""}
                              onChange={(e) => {
                                const updatedCriteria = [...getCurrentCriteria()];
                                const criteriaIndex = getCurrentCriteria().findIndex(c => c.criteria_id === criteria.criteria_id);
                                if (criteriaIndex !== -1) {
                                  updatedCriteria[criteriaIndex] = {
                                    ...updatedCriteria[criteriaIndex],
                                    max_hold_period: parseFloat(e.target.value) || null
                                  };
                                  setEditedCriteria(updatedCriteria);
                                }
                              }}
                              className="mt-1"
                              placeholder="Enter hold period in months"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {formatNumber(criteria.max_hold_period, "months")}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Historical IRR
                          </label>
                          {isEditing ? (
                            <Input
                              type="number"
                              step="0.01"
                              value={criteria.historical_irr || ""}
                              onChange={(e) => {
                                const updatedCriteria = [...getCurrentCriteria()];
                                const criteriaIndex = getCurrentCriteria().findIndex(c => c.criteria_id === criteria.criteria_id);
                                if (criteriaIndex !== -1) {
                                  updatedCriteria[criteriaIndex] = {
                                    ...updatedCriteria[criteriaIndex],
                                    historical_irr: parseFloat(e.target.value) || null
                                  };
                                  setEditedCriteria(updatedCriteria);
                                }
                              }}
                              className="mt-1"
                              placeholder="Enter historical IRR percentage"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {formatPercentage(criteria.historical_irr)}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Historical EM
                          </label>
                          {isEditing ? (
                            <Input
                              type="number"
                              step="0.01"
                              value={criteria.historical_em || ""}
                              onChange={(e) => {
                                const updatedCriteria = [...getCurrentCriteria()];
                                const criteriaIndex = getCurrentCriteria().findIndex(c => c.criteria_id === criteria.criteria_id);
                                if (criteriaIndex !== -1) {
                                  updatedCriteria[criteriaIndex] = {
                                    ...updatedCriteria[criteriaIndex],
                                    historical_em: parseFloat(e.target.value) || null
                                  };
                                  setEditedCriteria(updatedCriteria);
                                }
                              }}
                              className="mt-1"
                              placeholder="Enter historical equity multiple"
                            />
                          ) : (
                            <p className="text-sm text-gray-900 mt-1">
                              {formatNumber(criteria.historical_em)}
                            </p>
                          )}
                        </div>
                      </div>
                      {criteria.notes && (
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Notes
                          </label>
                          <p className="text-sm text-gray-900 mt-1">
                            {criteria.notes}
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card>
                  <CardContent className="text-center py-8">
                    <p className="text-gray-500">No equity criteria found for this deal.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* Deal Matching Tab */}
          <TabsContent value="deal-matching">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Matching Contacts
                  {matchingContacts.length > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {matchingContacts.length} matches found
                    </Badge>
                  )}
                  {matchingContacts.some((match: any) => match.fallback_used) && (
                    <Badge variant="outline" className="ml-2 bg-orange-50 text-orange-700 border-orange-200">
                      Company fallback used
                    </Badge>
                  )}
                </CardTitle>
                {/* Show field weights info if available */}
                {matchingContacts.length > 0 && matchingContacts[0]?.scoring_method === "normalized_weights" && (
                  <div className="text-sm text-gray-600 mt-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Using normalized field weights (always sum to 1.0)
                    </div>
                    <div className="text-xs mt-1">
                      All contacts must have matching capital position to be included
                    </div>
                  </div>
                )}

                {/* Filtering Toggle and Info */}
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Checkbox
                        id="show-all-matches"
                        checked={showAllMatches}
                        onCheckedChange={handleToggleShowAllMatches}
                      />
                      <label htmlFor="show-all-matches" className="text-sm font-medium text-gray-700">
                        Show all matches (including &lt; 50% score)
                      </label>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Checkbox
                        id="crm-mode-contacts"
                        checked={isCrmMode}
                        onCheckedChange={(checked: boolean | "indeterminate") => setIsCrmMode(checked === true)}
                      />
                      <label htmlFor="crm-mode-contacts" className="text-sm font-medium text-gray-700">
                        CRM Mode
                      </label>
                    </div>
                  </div>
                  
                  <div className="flex flex-col items-end gap-1">
                    {matchingContactsFiltering && !showAllMatches && (
                      <div className="text-sm text-gray-600">
                        Showing only matches ≥ 50% score
                        {matchingContactsFiltering.filtered_out_count > 0 && (
                          <span className="ml-2 text-orange-600">
                            ({matchingContactsFiltering.filtered_out_count} low-quality matches hidden)
                          </span>
                        )}
                      </div>
                    )}
                    
                    {matchingContactsFiltering && showAllMatches && (
                      <div className="text-sm text-gray-600">
                        Showing all matches (including low-quality)
                      </div>
                    )}
                    
                    {isCrmMode && (
                      <div className="text-xs text-gray-600 bg-blue-50 px-2 py-1 rounded">
                        Only showing contacts with requested investment criteria
                      </div>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {matchingContactsLoading ? (
                  <div className="text-gray-500">Loading matching contacts...</div>
                ) : matchingContactsError ? (
                  <div className="text-red-600">{matchingContactsError}</div>
                ) : matchingContacts.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <div className="text-gray-500">No matching contacts found for this deal.</div>
                    <div className="text-sm text-gray-400 mt-2">
                      Contacts must have matching capital position and other criteria
                    </div>
                  </div>
                ) : (
                  <>
                    {/* Summary Stats */}
                    <div className="mb-6 grid grid-cols-1 sm:grid-cols-4 gap-4">
                      <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                        <div className="text-sm font-medium text-blue-900">Total Matches</div>
                        <div className="text-2xl font-bold text-blue-700">{matchingContacts.length}</div>
                      </div>
                      <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                        <div className="text-sm font-medium text-green-900">Avg Score</div>
                        <div className="text-2xl font-bold text-green-700">
                          {Math.round(matchingContacts.reduce((sum, m) => sum + (m.score || 0), 0) / matchingContacts.length)}
                        </div>
                      </div>
                      <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
                        <div className="text-sm font-medium text-purple-900">High Quality</div>
                        <div className="text-2xl font-bold text-purple-700">
                          {matchingContacts.filter(m => (m.score || 0) >= 70).length}
                        </div>
                      </div>
                      <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
                        <div className="text-sm font-medium text-orange-900">Capital Match</div>
                        <div className="text-2xl font-bold text-orange-700">100%</div>
                        <div className="text-xs text-orange-600">Required</div>
                      </div>
                    </div>

                    {/* Contact Cards */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 min-w-0">
                      {matchingContacts.map((match) => {
                        const contact: Contact = {
                          contact_id: Number(match.contact_id),
                          first_name: match.first_name || "",
                          last_name: match.last_name || "",
                          full_name: match.full_name || `${match.first_name || ''} ${match.last_name || ''}`,
                          email: match.email,
                          company_id: match.company_id,
                          title: match.title,
                          phone_number: match.phone_number,
                          linkedin_url: match.linkedin_url,
                          contact_city: Array.isArray(match.contact_city) ? match.contact_city[0] : match.contact_city,
                          contact_state: Array.isArray(match.contact_state) ? match.contact_state[0] : match.contact_state,
                          company_name: match.company_name,
                        };
                        const isExpanded = expandedContactId === contact.contact_id;
                        const scoreColor = (match.score || 0) >= 80 ? 'bg-green-600' : 
                                          (match.score || 0) >= 60 ? 'bg-blue-600' :
                                          (match.score || 0) >= 40 ? 'bg-yellow-600' : 'bg-orange-600';
                        
                        return (
                          <Card key={contact.contact_id} className="relative group min-w-0 overflow-visible hover:shadow-md transition-shadow">
                            <CardContent className="flex flex-col items-stretch">
                              <ContactCard
                                contact={contact}
                                onSelectContact={() => router.push(`/dashboard/people/${contact.contact_id}`)}
                                isSelected={false}
                                onToggleSelection={() => {}}
                              />
                              
                              {/* Enhanced Score Badge */}
                              {match.score != null && (
                                <div className="absolute top-2 right-2 z-10">
                                  <div className={`${scoreColor} text-white text-xs font-semibold px-2 py-1 rounded shadow group-hover:scale-110 transition-transform`}>
                                    {match.score}%
                                  </div>
                                  <div className="bg-green-600 text-white text-xs px-2 py-0.5 rounded-b shadow">
                                    ✓ Capital
                                  </div>
                                  {/* New: Show criteria count and best score */}
                                  {match.matching_criteria_count > 1 && (
                                    <div className="mt-1 flex flex-col gap-1">
                                      <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                        {match.matching_criteria_count} criteria
                                      </Badge>
                                      {match.best_score > match.score && (
                                        <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                                          Best: {match.best_score}%
                                        </Badge>
                                      )}
                                    </div>
                                  )}
                                  {/* Show fallback indicator */}
                                  {match.fallback_used && (
                                    <div className="mt-1">
                                      <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700 border-orange-200">
                                        Company Criteria
                                      </Badge>
                                    </div>
                                  )}
                                </div>
                              )}

                              {/* Quick Match Indicators - Show All */}
                              <div className="mt-2 flex flex-wrap gap-1 max-h-20 overflow-y-auto">
                                {match.breakdown?.filter((b: any) => b.score > 0).map((b: any, idx: number) => (
                                  <Badge key={idx} variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                    {b.field.replace(/_/g, ' ')}: {b.score}%
                                  </Badge>
                                ))}
                                {(!match.breakdown || match.breakdown.filter((b: any) => b.score > 0).length === 0) && (
                                  <Badge variant="outline" className="text-xs text-gray-500">
                                    Capital position match only
                                  </Badge>
                                )}
                              </div>

                              <Button
                                variant="outline"
                                size="sm"
                                className="mt-2 self-end"
                                onClick={() => setExpandedContactId(isExpanded ? null : contact.contact_id)}
                              >
                                {isExpanded ? "Hide Details" : "Show Details"}
                              </Button>

                              {isExpanded && (
                                <div className="mt-3 p-3 border rounded bg-gray-50 space-y-3">
                                  {/* Capital Position Match */}
                                  <div className="bg-green-100 border border-green-300 rounded-lg p-2">
                                    <div className="flex items-center gap-2">
                                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                      <span className="font-semibold text-green-800 text-sm">Capital Position Match</span>
                                      <Badge className="bg-green-600 text-white">Required ✓</Badge>
                                    </div>
                                    <div className="text-xs text-green-700 mt-1">
                                      This contact meets the deal's capital position requirements
                                    </div>
                                  </div>

                                  {/* Multiple Criteria Summary */}
                                  {match.matching_criteria_count > 1 && (
                                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-3">
                                      <div className="flex items-center justify-between mb-2">
                                        <span className="font-semibold text-sm text-blue-900">Multiple Criteria Match</span>
                                        <Badge className="bg-blue-600 text-white text-xs">
                                          {match.matching_criteria_count} criteria
                                        </Badge>
                                      </div>
                                      <div className="text-xs text-blue-700">
                                        This contact matches multiple investment criteria for this deal
                                      </div>
                                    </div>
                                  )}

                                  {/* Detailed Breakdown */}
                                  {match.breakdown && match.breakdown.length > 0 && (
                                    <div className="space-y-2">
                                      <h4 className="font-semibold text-sm text-gray-900">Match Breakdown</h4>
                                      {match.breakdown.map((b: any, idx: number) => (
                                        <div key={idx} className="flex justify-between items-center text-xs">
                                          <span className="text-gray-600">{b.field.replace(/_/g, ' ')}</span>
                                          <Badge variant="outline" className="text-xs">
                                            {b.score}%
                                          </Badge>
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              )}
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Investment Criteria Tab */}
          <TabsContent value="investment-criteria">
            <div className="space-y-6">
              {/* Add Criteria Button - Only show when editing */}
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-4">
                  <h3 className="text-lg font-semibold text-gray-900">Investment Criteria</h3>
                  {isEditing && (
                    <div className="flex items-center gap-2">
                      <Checkbox
                        id="apply-to-all"
                        checked={applyToAll}
                        onCheckedChange={(checked) => setApplyToAll(checked as boolean)}
                      />
                      <label htmlFor="apply-to-all" className="text-sm font-medium text-gray-700">
                        Apply to All Criteria
                      </label>
                    </div>
                  )}
                </div>
                {isEditing && (
                  <Button
                    onClick={() => {
                      setIsAddingCriteria(true);
                      setNewCriteria({
                        criteria_id: 0,
                        entity_type: 'Deal',
                        entity_id: dealId,
                        target_return: null,
                        property_types: null,
                        property_sub_categories: null,
                        strategies: null,
                        asset_classes: null,
                        minimum_deal_size: null,
                        maximum_deal_size: null,
                        min_hold_period: null,
                        max_hold_period: null,
                        financial_products: null,
                        historical_irr: null,
                        historical_em: null,
                        country: null,
                        region: null,
                        state: null,
                        city: null,
                        loan_program: null,
                        loan_type: null,
                        capital_type: null,
                        capital_position: null,
                        capital_source: null,
                        structured_loan_tranche: null,
                        min_loan_term: null,
                        max_loan_term: null,
                        interest_rate: null,
                        loan_interest_rate: null,
                        loan_interest_rate_sofr: null,
                        loan_interest_rate_wsj: null,
                        loan_interest_rate_prime: null,
                        loan_ltv: null,
                        loan_ltc: null,
                        loan_to_value_min: null,
                        loan_to_value_max: null,
                        loan_to_cost_min: null,
                        loan_to_cost_max: null,
                        loan_origination_fee: null,
                        loan_origination_fee_min: null,
                        loan_origination_fee_max: null,
                        loan_exit_fee: null,
                        loan_exit_fee_min: null,
                        loan_exit_fee_max: null,
                        min_loan_dscr: null,
                        max_loan_dscr: null,
                        recourse_loan: null,
                        extra_fields: null,
                        created_at: null,
                        updated_at: null,
                        created_by: null,
                        updated_by: null,
                        is_active: true,
                        is_requested: false,
                        include_in_sources: true, // Default to true for new criteria
                        notes: null
                      });
                    }}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Add Criteria
                  </Button>
                )}
              </div>
              
              {deal?.investment_criteria && deal.investment_criteria.length > 0 ? (
                deal.investment_criteria.map((criteria, index) => (
                  <Card key={criteria.criteria_id || index}>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Target className="h-5 w-5" />
                          Investment Criteria #{index + 1}
                        </div>
                        <div className="flex items-center gap-2">
                          {criteria.is_requested && (
                            <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
                              Requested
                            </Badge>
                          )}
                          {criteria.quality !== undefined && (
                            <Badge 
                              variant="outline" 
                              className={`ml-2 ${
                                criteria.quality >= 80 ? 'border-green-200 text-green-700 bg-green-50' :
                                criteria.quality >= 60 ? 'border-yellow-200 text-yellow-700 bg-yellow-50' :
                                'border-red-200 text-red-700 bg-red-50'
                              }`}
                            >
                              {criteria.quality}% Complete
                            </Badge>
                          )}
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => setCriteriaToDelete({ id: criteria.criteria_id, index })}
                            className="flex items-center gap-2 ml-2"
                          >
                            <Trash2 className="h-4 w-4" />
                            Delete
                          </Button>
                        </div>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {/* Capital Positions - PROMINENT FIRST SECTION */}
                      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200 mb-6">
                        <h4 className="font-bold text-blue-900 mb-3 flex items-center gap-2">
                          <Target className="h-5 w-5" />
                          Capital Positions
                        </h4>
                        {isEditing ? (
                          <Input
                            value={getCurrentCriteria()[index]?.capital_position?.join(", ") || ""}
                            onChange={(e) => {
                              const updatedCriteria = [...getCurrentCriteria()];
                              updatedCriteria[index] = {
                                ...updatedCriteria[index],
                                capital_position: e.target.value.split(", ").filter(s => s.trim())
                              };
                              setEditedCriteria(updatedCriteria);
                            }}
                            placeholder="Enter capital positions (comma separated)"
                            className="w-full"
                          />
                        ) : (
                          <div className="flex flex-wrap gap-2">
                            {criteria.capital_position?.map((position: string, posIndex: number) => (
                              <span
                                key={posIndex}
                                className="inline-flex items-center px-3 py-2 rounded-lg text-sm font-semibold bg-white text-blue-900 border border-blue-300 shadow-sm"
                              >
                                {position}
                              </span>
                            )) || (
                              <span className="text-blue-700 text-sm font-medium">No capital positions specified</span>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Include in Sources Checkbox */}
                      <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            id={`include-in-sources-${index}`}
                            checked={getCurrentCriteria()[index]?.include_in_sources ?? true}
                            onCheckedChange={(checked) => {
                              const updatedCriteria = [...getCurrentCriteria()];
                              updatedCriteria[index] = {
                                ...updatedCriteria[index],
                                include_in_sources: checked as boolean
                              };
                              setEditedCriteria(updatedCriteria);
                            }}
                            disabled={!isEditing}
                          />
                          <label 
                            htmlFor={`include-in-sources-${index}`} 
                            className="text-sm font-medium text-gray-700"
                          >
                            Include in Sources Calculation
                          </label>
                        </div>
                        <p className="text-xs text-gray-500 mt-1 ml-6">
                          When checked, this criteria will be included in the Sources section calculations
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {/* Property & Strategy */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">
                            Property & Strategy
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="font-medium">
                                Property Types:
                              </span>
                              {isEditing ? (
                                <Input
                                  value={getSynchronizedArrayValue('property_types', index).join(", ") || ""}
                                  onChange={(e) => {
                                    updateAllCriteriaRealTime('property_types', e.target.value.split(", ").filter(s => s.trim()), index);
                                  }}
                                  placeholder="Enter property types (comma separated)"
                                  className="mt-1"
                                />
                              ) : (
                                <p>{formatArrayField(criteria.property_types)}</p>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">Strategies:</span>
                              {isEditing ? (
                                <Input
                                  value={getSynchronizedArrayValue('strategies', index).join(", ") || ""}
                                  onChange={(e) => {
                                    updateAllCriteriaRealTime('strategies', e.target.value.split(", ").filter(s => s.trim()), index);
                                  }}
                                  placeholder="Enter strategies (comma separated)"
                                  className="mt-1"
                                />
                              ) : (
                                <p>{formatArrayField(criteria.strategies)}</p>
                              )}
                            </div>

                          </div>
                        </div>

                        {/* Financial */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">
                            Financial
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="font-medium">Deal Size:</span>
                              {isEditing ? (
                                <div className="flex gap-2 mt-1">
                                  <Input
                                    type="number"
                                    value={getCurrentCriteria()[index]?.minimum_deal_size || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = [...getCurrentCriteria()];
                                      updatedCriteria[index] = {
                                        ...updatedCriteria[index],
                                        minimum_deal_size: parseFloat(e.target.value) || null
                                      };
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Min (M)"
                                    className="flex-1"
                                  />
                                  <Input
                                    type="number"
                                    value={getCurrentCriteria()[index]?.maximum_deal_size || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = [...getCurrentCriteria()];
                                      updatedCriteria[index] = {
                                        ...updatedCriteria[index],
                                        maximum_deal_size: parseFloat(e.target.value) || null
                                      };
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Max (M)"
                                    className="flex-1"
                                  />
                                </div>
                              ) : (
                                <p>
                                  {criteria.minimum_deal_size &&
                                  criteria.maximum_deal_size
                                    ? `${formatCurrency(
                                        criteria.minimum_deal_size
                                      )} - ${formatCurrency(
                                        criteria.maximum_deal_size
                                      )}`
                                    : formatCurrency(
                                        criteria.minimum_deal_size ||
                                          criteria.maximum_deal_size
                                      )}
                                </p>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">
                                Target Return:
                              </span>
                              {isEditing ? (
                                <Input
                                  type="number"
                                  step="0.01"
                                  value={getCurrentCriteria()[index]?.target_return || ""}
                                  onChange={(e) => {
                                    const updatedCriteria = [...getCurrentCriteria()];
                                    updatedCriteria[index] = {
                                      ...updatedCriteria[index],
                                      target_return: parseFloat(e.target.value) || null
                                    };
                                    setEditedCriteria(updatedCriteria);
                                  }}
                                  placeholder="Enter IRR (decimal)"
                                  className="mt-1"
                                />
                              ) : (
                                <p>{formatPercentage(criteria.target_return)}</p>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">
                                Financial Products:
                              </span>
                              {isEditing ? (
                                <Input
                                  value={getCurrentCriteria()[index]?.financial_products?.join(", ") || ""}
                                  onChange={(e) => {
                                    const split = e.target.value.split(",").map(s => s.trim()).filter(s => s.length > 0);
                                    const updatedCriteria = [...getCurrentCriteria()];
                                    updatedCriteria[index] = {
                                      ...updatedCriteria[index],
                                      financial_products: split
                                    };
                                    setEditedCriteria(updatedCriteria);
                                  }}
                                  placeholder="Enter financial products (comma separated)"
                                  className="mt-1"
                                />
                              ) : (
                                <p>
                                  {formatArrayField(criteria.financial_products)}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Geographic */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">
                            Geographic
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="font-medium">Country:</span>
                              {isEditing ? (
                                <Input
                                  value={getSynchronizedArrayValue('country', index).join(", ") || ""}
                                  onChange={(e) => {
                                    updateAllCriteriaRealTime('country', e.target.value.split(", ").filter(s => s.trim()), index);
                                  }}
                                  placeholder="Enter countries (comma separated)"
                                  className="mt-1"
                                />
                              ) : (
                                <p>{formatArrayField(criteria.country)}</p>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">Region:</span>
                              {isEditing ? (
                                <Input
                                  value={getSynchronizedArrayValue('region', index).join(", ") || ""}
                                  onChange={(e) => {
                                    updateAllCriteriaRealTime('region', e.target.value.split(", ").filter(s => s.trim()), index);
                                  }}
                                  placeholder="Enter regions (comma separated)"
                                  className="mt-1"
                                />
                              ) : (
                                <p>{formatArrayField(criteria.region)}</p>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">State:</span>
                              {isEditing ? (
                                <Input
                                  value={getSynchronizedArrayValue('state', index).join(", ") || ""}
                                  onChange={(e) => {
                                    updateAllCriteriaRealTime('state', e.target.value.split(", ").filter(s => s.trim()), index);
                                  }}
                                  placeholder="Enter states (comma separated)"
                                  className="mt-1"
                                />
                              ) : (
                                <p>{formatArrayField(criteria.state)}</p>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">City:</span>
                              {isEditing ? (
                                <Input
                                  value={getSynchronizedArrayValue('city', index).join(", ") || ""}
                                  onChange={(e) => {
                                    updateAllCriteriaRealTime('city', e.target.value.split(", ").filter(s => s.trim()), index);
                                  }}
                                  placeholder="Enter cities (comma separated)"
                                  className="mt-1"
                                />
                              ) : (
                                <p>{formatArrayField(criteria.city)}</p>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Loan Details */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">
                            Loan Details
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="font-medium">Loan Types:</span>
                              {isEditing ? (
                                <Input
                                  value={getCurrentCriteria()[index]?.loan_type?.join(", ") || ""}
                                  onChange={(e) => {
                                    const updatedCriteria = [...getCurrentCriteria()];
                                    updatedCriteria[index] = {
                                      ...updatedCriteria[index],
                                      loan_type: e.target.value.split(", ").filter(s => s.trim())
                                    };
                                    setEditedCriteria(updatedCriteria);
                                  }}
                                  placeholder="Enter loan types (comma separated)"
                                  className="mt-1"
                                />
                              ) : (
                                <p>{formatArrayField(criteria.loan_type)}</p>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">LTV:</span>
                              {isEditing ? (
                                <div className="flex gap-2 mt-1">
                                  <Input
                                    type="number"
                                    step="0.01"
                                    value={getCurrentCriteria()[index]?.loan_to_value_min || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = [...getCurrentCriteria()];
                                      updatedCriteria[index] = {
                                        ...updatedCriteria[index],
                                        loan_to_value_min: parseFloat(e.target.value) || null
                                      };
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Min %"
                                    className="flex-1"
                                  />
                                  <Input
                                    type="number"
                                    step="0.01"
                                    value={getCurrentCriteria()[index]?.loan_to_value_max || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = [...getCurrentCriteria()];
                                      updatedCriteria[index] = {
                                        ...updatedCriteria[index],
                                        loan_to_value_max: parseFloat(e.target.value) || null
                                      };
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Max %"
                                    className="flex-1"
                                  />
                                </div>
                              ) : (
                                <p>
                                  {criteria.loan_to_value_min !== null &&
                                  criteria.loan_to_value_min !== undefined &&
                                  criteria.loan_to_value_max !== null &&
                                  criteria.loan_to_value_max !== undefined
                                    ? `${formatPercentage(
                                        criteria.loan_to_value_min
                                      )} - ${formatPercentage(
                                        criteria.loan_to_value_max
                                      )}`
                                    : criteria.loan_to_value_min || criteria.loan_to_value_max
                                      ? formatPercentage(criteria.loan_to_value_min || criteria.loan_to_value_max)
                                      : "N/A"}
                                </p>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">LTC:</span>
                              {isEditing ? (
                                <div className="flex gap-2 mt-1">
                                  <Input
                                    type="number"
                                    step="0.01"
                                    value={getCurrentCriteria()[index]?.loan_to_cost_min || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = [...getCurrentCriteria()];
                                      updatedCriteria[index] = {
                                        ...updatedCriteria[index],
                                        loan_to_cost_min: parseFloat(e.target.value) || null
                                      };
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Min %"
                                    className="flex-1"
                                  />
                                  <Input
                                    type="number"
                                    step="0.01"
                                    value={getCurrentCriteria()[index]?.loan_to_cost_max || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = [...getCurrentCriteria()];
                                      updatedCriteria[index] = {
                                        ...updatedCriteria[index],
                                        loan_to_cost_max: parseFloat(e.target.value) || null
                                      };
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Max %"
                                    className="flex-1"
                                  />
                                </div>
                              ) : (
                                <p>
                                  {criteria.loan_to_cost_min !== null &&
                                  criteria.loan_to_cost_min !== undefined &&
                                  criteria.loan_to_cost_max !== null &&
                                  criteria.loan_to_cost_max !== undefined
                                    ? `${formatPercentage(
                                        criteria.loan_to_cost_min
                                    )} - ${formatPercentage(
                                        criteria.loan_to_cost_max
                                    )}`
                                  : criteria.loan_to_cost_min || criteria.loan_to_cost_max
                                    ? formatPercentage(criteria.loan_to_cost_min || criteria.loan_to_cost_max)
                                    : "N/A"}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Terms */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">
                            Terms
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="font-medium">Hold Period:</span>
                              {isEditing ? (
                                <div className="flex gap-2 mt-1">
                                  <Input
                                    type="number"
                                    value={getCurrentCriteria()[index]?.min_hold_period || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = [...getCurrentCriteria()];
                                      updatedCriteria[index] = {
                                        ...updatedCriteria[index],
                                        min_hold_period: parseFloat(e.target.value) || null
                                      };
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Min months"
                                    className="flex-1"
                                  />
                                  <Input
                                    type="number"
                                    value={getCurrentCriteria()[index]?.max_hold_period || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = [...getCurrentCriteria()];
                                      updatedCriteria[index] = {
                                        ...updatedCriteria[index],
                                        max_hold_period: parseFloat(e.target.value) || null
                                      };
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Max months"
                                    className="flex-1"
                                  />
                                </div>
                              ) : (
                                <p>
                                  {criteria.min_hold_period &&
                                  criteria.max_hold_period
                                    ? `${criteria.min_hold_period} - ${criteria.max_hold_period} months`
                                    : `${
                                        criteria.min_hold_period ||
                                        criteria.max_hold_period ||
                                        "N/A"
                                      } months`}
                                </p>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">Loan Term:</span>
                              {isEditing ? (
                                <div className="flex gap-2 mt-1">
                                  <Input
                                    type="number"
                                    value={getCurrentCriteria()[index]?.min_loan_term || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = [...getCurrentCriteria()];
                                      updatedCriteria[index] = {
                                        ...updatedCriteria[index],
                                        min_loan_term: parseFloat(e.target.value) || null
                                      };
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Min months"
                                    className="flex-1"
                                  />
                                  <Input
                                    type="number"
                                    value={getCurrentCriteria()[index]?.max_loan_term || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = [...getCurrentCriteria()];
                                      updatedCriteria[index] = {
                                        ...updatedCriteria[index],
                                        max_loan_term: parseFloat(e.target.value) || null
                                      };
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Max months"
                                    className="flex-1"
                                  />
                                </div>
                              ) : (
                                <p>
                                  {criteria.min_loan_term &&
                                  criteria.max_loan_term
                                    ? `${criteria.min_loan_term} - ${criteria.max_loan_term} months`
                                    : `${
                                        criteria.min_loan_term ||
                                        criteria.max_loan_term ||
                                        "N/A"
                                      } months`}
                                </p>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">
                                Interest Rate:
                              </span>
                              {isEditing ? (
                                <div className="space-y-2 mt-1">
                                  <Input
                                    type="number"
                                    step="0.001"
                                    min="0"
                                    max="1"
                                    value={getCurrentCriteria()[index]?.interest_rate_sofr || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = [...getCurrentCriteria()];
                                      const value = e.target.value === "" ? null : parseFloat(e.target.value);
                                      updatedCriteria[index] = {
                                        ...updatedCriteria[index],
                                        interest_rate_sofr: value
                                      };
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="SOFR rate (decimal, e.g., 0.05 for 5%)"
                                  />
                                  <Input
                                    type="number"
                                    step="0.001"
                                    min="0"
                                    max="1"
                                    value={getCurrentCriteria()[index]?.interest_rate_prime || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = [...getCurrentCriteria()];
                                      const value = e.target.value === "" ? null : parseFloat(e.target.value);
                                      updatedCriteria[index] = {
                                        ...updatedCriteria[index],
                                        interest_rate_prime: value
                                      };
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Prime rate (decimal, e.g., 0.05 for 5%)"
                                  />
                                  <Input
                                    type="number"
                                    step="0.001"
                                    min="0"
                                    max="1"
                                    value={getCurrentCriteria()[index]?.interest_rate_wsj || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = [...getCurrentCriteria()];
                                      const value = e.target.value === "" ? null : parseFloat(e.target.value);
                                      updatedCriteria[index] = {
                                        ...updatedCriteria[index],
                                        interest_rate_wsj: value
                                      };
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="WSJ rate (decimal, e.g., 0.05 for 5%)"
                                  />
                                  <Input
                                    type="number"
                                    step="0.001"
                                    min="0"
                                    max="1"
                                    value={getCurrentCriteria()[index]?.interest_rate || ""}
                                    onChange={(e) => {
                                      const updatedCriteria = [...getCurrentCriteria()];
                                      const value = e.target.value === "" ? null : parseFloat(e.target.value);
                                      updatedCriteria[index] = {
                                        ...updatedCriteria[index],
                                        interest_rate: value
                                      };
                                      setEditedCriteria(updatedCriteria);
                                    }}
                                    placeholder="Base rate (decimal, e.g., 0.05 for 5%)"
                                  />
                                </div>
                              ) : (
                                <div className="space-y-1">
                                  {criteria.interest_rate_sofr && (
                                    <p className="text-sm">
                                      <span className="font-medium text-emerald-600">SOFR:</span> {formatPercentage(criteria.interest_rate_sofr)}
                                    </p>
                                  )}
                                  {criteria.interest_rate_prime && (
                                    <p className="text-sm">
                                      <span className="font-medium text-blue-600">Prime:</span> {formatPercentage(criteria.interest_rate_prime)}
                                    </p>
                                  )}
                                  {criteria.interest_rate_wsj && (
                                    <p className="text-sm">
                                      <span className="font-medium text-purple-600">WSJ:</span> {formatPercentage(criteria.interest_rate_wsj)}
                                    </p>
                                  )}
                                  {criteria.interest_rate && (
                                    <p>
                                      <span className="font-medium">Base:</span> {formatPercentage(criteria.interest_rate)}
                                    </p>
                                  )}
                                  {!criteria.interest_rate_sofr && !criteria.interest_rate_prime && !criteria.interest_rate_wsj && !criteria.interest_rate && (
                                    <p>N/A</p>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Additional Terms & Fees */}
                        {(criteria.closing_time_weeks ||
                          criteria.loan_origination_fee_min ||
                          criteria.loan_origination_fee_max ||
                          criteria.loan_exit_fee_min ||
                          criteria.loan_exit_fee_max ||
                          criteria.min_loan_dscr ||
                          criteria.max_loan_dscr ||
                          criteria.loan_type_normalized) && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-2">
                              Additional Terms
                            </h4>
                            <div className="space-y-2 text-sm">
                              {criteria.closing_time_weeks && (
                                <div>
                                  <span className="font-medium">
                                    Closing Time:
                                  </span>
                                  {isEditing ? (
                                    <Input
                                      type="number"
                                      value={getCurrentCriteria()[index]?.closing_time_weeks || ""}
                                      onChange={(e) => {
                                        const updatedCriteria = [...getCurrentCriteria()];
                                        updatedCriteria[index] = {
                                          ...updatedCriteria[index],
                                          closing_time_weeks: parseFloat(e.target.value) || null
                                        };
                                        setEditedCriteria(updatedCriteria);
                                      }}
                                      placeholder="Weeks"
                                      className="mt-1"
                                    />
                                  ) : (
                                    <p>
                                      {formatNumber(
                                        criteria.closing_time_weeks
                                      )}{" "}
                                      weeks
                                    </p>
                                  )}
                                </div>
                              )}
                              {(criteria.loan_origination_fee_min || criteria.loan_origination_fee_max) && (
                                <div>
                                  <span className="font-medium">
                                    Origination Fee:
                                  </span>
                                  {isEditing ? (
                                    <div className="flex gap-2 mt-1">
                                      <Input
                                        type="number"
                                        step="0.01"
                                        value={getCurrentCriteria()[index]?.loan_origination_fee_min || ""}
                                        onChange={(e) => {
                                          const updatedCriteria = [...getCurrentCriteria()];
                                          updatedCriteria[index] = {
                                            ...updatedCriteria[index],
                                            loan_origination_fee_min: parseFloat(e.target.value) || null
                                          };
                                          setEditedCriteria(updatedCriteria);
                                        }}
                                        placeholder="Min %"
                                        className="flex-1"
                                      />
                                      <Input
                                        type="number"
                                        step="0.01"
                                        value={getCurrentCriteria()[index]?.loan_origination_fee_max || ""}
                                        onChange={(e) => {
                                          const updatedCriteria = [...getCurrentCriteria()];
                                          updatedCriteria[index] = {
                                            ...updatedCriteria[index],
                                            loan_origination_fee_max: parseFloat(e.target.value) || null
                                          };
                                          setEditedCriteria(updatedCriteria);
                                        }}
                                        placeholder="Max %"
                                        className="flex-1"
                                      />
                                    </div>
                                  ) : (
                                    <p>
                                      {criteria.loan_origination_fee_min !== null &&
                                      criteria.loan_origination_fee_min !== undefined &&
                                      criteria.loan_origination_fee_max !== null &&
                                      criteria.loan_origination_fee_max !== undefined
                                        ? `${formatPercentage(
                                            criteria.loan_origination_fee_min
                                          )} - ${formatPercentage(
                                            criteria.loan_origination_fee_max
                                          )}`
                                        : formatPercentage(
                                            criteria.loan_origination_fee_min || 
                                            criteria.loan_origination_fee_max
                                          )}
                                    </p>
                                  )}
                                </div>
                              )}
                              {(criteria.loan_exit_fee_min || criteria.loan_exit_fee_max) && (
                                <div>
                                  <span className="font-medium">Exit Fee:</span>
                                  {isEditing ? (
                                    <div className="flex gap-2 mt-1">
                                      <Input
                                        type="number"
                                        step="0.01"
                                        value={getCurrentCriteria()[index]?.loan_exit_fee_min || ""}
                                        onChange={(e) => {
                                          const updatedCriteria = [...getCurrentCriteria()];
                                          updatedCriteria[index] = {
                                            ...updatedCriteria[index],
                                            loan_exit_fee_min: parseFloat(e.target.value) || null
                                          };
                                          setEditedCriteria(updatedCriteria);
                                        }}
                                        placeholder="Min %"
                                        className="flex-1"
                                      />
                                      <Input
                                        type="number"
                                        step="0.01"
                                        value={getCurrentCriteria()[index]?.loan_exit_fee_max || ""}
                                        onChange={(e) => {
                                          const updatedCriteria = [...getCurrentCriteria()];
                                          updatedCriteria[index] = {
                                            ...updatedCriteria[index],
                                            loan_exit_fee_max: parseFloat(e.target.value) || null
                                          };
                                          setEditedCriteria(updatedCriteria);
                                        }}
                                        placeholder="Max %"
                                        className="flex-1"
                                      />
                                    </div>
                                  ) : (
                                    <p>
                                      {criteria.loan_exit_fee_min !== null &&
                                      criteria.loan_exit_fee_min !== undefined &&
                                      criteria.loan_exit_fee_max !== null &&
                                      criteria.loan_exit_fee_max !== undefined
                                        ? `${formatPercentage(
                                            criteria.loan_exit_fee_min
                                          )} - ${formatPercentage(
                                            criteria.loan_exit_fee_max
                                          )}`
                                        : formatPercentage(
                                            criteria.loan_exit_fee_min || 
                                            criteria.loan_exit_fee_max
                                          )}
                                    </p>
                                  )}
                                </div>
                              )}
                              {(criteria.min_loan_dscr || criteria.max_loan_dscr) && (
                                <div>
                                  <span className="font-medium">DSCR:</span>
                                  {isEditing ? (
                                    <div className="flex gap-2 mt-1">
                                      <Input
                                        type="number"
                                        step="0.01"
                                        value={getCurrentCriteria()[index]?.min_loan_dscr || ""}
                                        onChange={(e) => {
                                          const updatedCriteria = [...getCurrentCriteria()];
                                          updatedCriteria[index] = {
                                            ...updatedCriteria[index],
                                            min_loan_dscr: parseFloat(e.target.value) || null
                                          };
                                          setEditedCriteria(updatedCriteria);
                                        }}
                                        placeholder="Min"
                                        className="flex-1"
                                      />
                                      <Input
                                        type="number"
                                        step="0.01"
                                        value={getCurrentCriteria()[index]?.max_loan_dscr || ""}
                                        onChange={(e) => {
                                          const updatedCriteria = [...getCurrentCriteria()];
                                          updatedCriteria[index] = {
                                            ...updatedCriteria[index],
                                            max_loan_dscr: parseFloat(e.target.value) || null
                                          };
                                          setEditedCriteria(updatedCriteria);
                                        }}
                                        placeholder="Max"
                                        className="flex-1"
                                      />
                                    </div>
                                  ) : (
                                    <p>
                                      {criteria.min_loan_dscr !== null &&
                                      criteria.min_loan_dscr !== undefined &&
                                      criteria.max_loan_dscr !== null &&
                                      criteria.max_loan_dscr !== undefined
                                        ? `${criteria.min_loan_dscr} - ${criteria.max_loan_dscr}`
                                        : criteria.min_loan_dscr || criteria.max_loan_dscr}
                                    </p>
                                  )}
                                </div>
                              )}
                              {criteria.loan_type_normalized && (
                                <div>
                                  <span className="font-medium">Normalized Loan Types:</span>
                                  {isEditing ? (
                                    <Input
                                      value={getCurrentCriteria()[index]?.loan_type_normalized?.join(", ") || ""}
                                      onChange={(e) => {
                                        const updatedCriteria = [...getCurrentCriteria()];
                                        updatedCriteria[index] = {
                                          ...updatedCriteria[index],
                                          loan_type_normalized: e.target.value.split(", ").filter(s => s.trim())
                                        };
                                        setEditedCriteria(updatedCriteria);
                                      }}
                                      placeholder="Enter normalized loan types (comma separated)"
                                      className="mt-1"
                                    />
                                  ) : (
                                    <p>{formatArrayField(criteria.loan_type_normalized)}</p>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Notes */}
                      {criteria.notes && (
                        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                          <h4 className="font-semibold text-gray-900 mb-2">Notes</h4>
                          {isEditing ? (
                            <Input
                              value={getCurrentCriteria()[index]?.notes || ""}
                              onChange={(e) => {
                                const updatedCriteria = [...getCurrentCriteria()];
                                updatedCriteria[index] = {
                                  ...updatedCriteria[index],
                                  notes: e.target.value
                                };
                                setEditedCriteria(updatedCriteria);
                              }}
                              placeholder="Enter notes"
                            />
                          ) : (
                            <p className="text-sm text-gray-700">{criteria.notes}</p>
                          )}
                        </div>
                      )}

                      {/* Extra Fields */}
                      {criteria.extra_fields && Object.keys(criteria.extra_fields).length > 0 && (
                        <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                          <h4 className="font-semibold text-yellow-900 mb-2">Extra Fields</h4>
                          <pre className="text-xs text-yellow-800 overflow-x-auto">
                            {JSON.stringify(criteria.extra_fields, null, 2)}
                          </pre>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card>
                  <CardContent className="text-center py-8">
                    <Target className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p className="text-gray-500">No investment criteria found for this deal.</p>
                    <p className="text-sm text-gray-400 mt-2">
                      Investment criteria will appear here when available.
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Add New Criteria Form */}
              {isAddingCriteria && newCriteria && (
                <Card className="border-2 border-blue-200 bg-blue-50">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Plus className="h-5 w-5 text-blue-600" />
                        Add New Investment Criteria
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setIsAddingCriteria(false);
                            setNewCriteria(null);
                          }}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={async () => {
                            try {
                              // Add the new criteria to the deal
                              const updatedCriteria = [...getCurrentCriteria(), newCriteria];
                              
                              if (isEditing) {
                                setEditedCriteria(updatedCriteria);
                              } else {
                                setDeal(prev => prev ? {
                                  ...prev,
                                  investment_criteria: updatedCriteria
                                } : null);
                              }

                              // Save to backend with new criteria flag
                              const response = await fetch(`/api/deals/${dealId}`, {
                                method: 'PUT',
                                headers: {
                                  'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                  ...getCurrentDeal(),
                                  investment_criteria: updatedCriteria,
                                  has_new_criteria: true // Flag to indicate new criteria was added
                                }),
                              });

                              if (!response.ok) {
                                throw new Error('Failed to save new criteria');
                              }

                              // Refetch the deal data to get updated criteria with proper IDs
                              const refetchResponse = await fetch(`/api/deals/${dealId}`);
                              if (refetchResponse.ok) {
                                const updatedDealData = await refetchResponse.json();
                                setDeal(updatedDealData);
                                
                                // If we're in editing mode, also update the edited criteria
                                if (isEditing) {
                                  setEditedCriteria(updatedDealData.investment_criteria || []);
                                }
                              }

                              toast.success('New investment criteria added successfully');
                              setIsAddingCriteria(false);
                              setNewCriteria(null);
                            } catch (error) {
                              console.error('Error adding new criteria:', error);
                              toast.error('Error adding new criteria');
                            }
                          }}
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          Save Criteria
                        </Button>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {/* Capital Positions - PROMINENT FIRST SECTION */}
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200 mb-6">
                      <h4 className="font-bold text-blue-900 mb-3 flex items-center gap-2">
                        <Target className="h-5 w-5" />
                        Capital Positions
                      </h4>
                      <Input
                        value={newCriteria.capital_position?.join(", ") || ""}
                        onChange={(e) => {
                          setNewCriteria({
                            ...newCriteria,
                            capital_position: e.target.value.split(", ").filter(s => s.trim())
                          });
                        }}
                        placeholder="Enter capital positions (comma separated)"
                        className="w-full"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {/* Property & Strategy */}
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">
                          Property & Strategy
                        </h4>
                        <div className="space-y-2 text-sm">
                          <div>
                            <span className="font-medium">Property Types:</span>
                            <Input
                              value={newCriteria.property_types?.join(", ") || ""}
                              onChange={(e) => {
                                setNewCriteria({
                                  ...newCriteria,
                                  property_types: e.target.value.split(", ").filter(s => s.trim())
                                });
                              }}
                              placeholder="Enter property types (comma separated)"
                              className="mt-1"
                            />
                          </div>
                          <div>
                            <span className="font-medium">Strategies:</span>
                            <Input
                              value={newCriteria.strategies?.join(", ") || ""}
                              onChange={(e) => {
                                setNewCriteria({
                                  ...newCriteria,
                                  strategies: e.target.value.split(", ").filter(s => s.trim())
                                });
                              }}
                              placeholder="Enter strategies (comma separated)"
                              className="mt-1"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Financial Criteria */}
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">
                          Financial Criteria
                        </h4>
                        <div className="space-y-2 text-sm">
                          <div>
                            <span className="font-medium">Target Return (%):</span>
                            <Input
                              type="number"
                              value={newCriteria.target_return || ""}
                              onChange={(e) => {
                                setNewCriteria({
                                  ...newCriteria,
                                  target_return: e.target.value ? parseFloat(e.target.value) : null
                                });
                              }}
                              placeholder="Enter target return"
                              className="mt-1"
                            />
                          </div>
                          <div>
                            <span className="font-medium">Deal Size Range:</span>
                            <div className="flex gap-2 mt-1">
                              <Input
                                type="number"
                                value={newCriteria.minimum_deal_size || ""}
                                onChange={(e) => {
                                  setNewCriteria({
                                    ...newCriteria,
                                    minimum_deal_size: e.target.value ? parseFloat(e.target.value) : null
                                  });
                                }}
                                placeholder="Min"
                                className="flex-1"
                              />
                              <Input
                                type="number"
                                value={newCriteria.maximum_deal_size || ""}
                                onChange={(e) => {
                                  setNewCriteria({
                                    ...newCriteria,
                                    maximum_deal_size: e.target.value ? parseFloat(e.target.value) : null
                                  });
                                }}
                                placeholder="Max"
                                className="flex-1"
                              />
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Location */}
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">
                          Location
                        </h4>
                        <div className="space-y-2 text-sm">
                          <div>
                            <span className="font-medium">States:</span>
                            <Input
                              value={newCriteria.state?.join(", ") || ""}
                              onChange={(e) => {
                                setNewCriteria({
                                  ...newCriteria,
                                  state: e.target.value.split(", ").filter(s => s.trim())
                                });
                              }}
                              placeholder="Enter states (comma separated)"
                              className="mt-1"
                            />
                          </div>
                          <div>
                            <span className="font-medium">Cities:</span>
                            <Input
                              value={newCriteria.city?.join(", ") || ""}
                              onChange={(e) => {
                                setNewCriteria({
                                  ...newCriteria,
                                  city: e.target.value.split(", ").filter(s => s.trim())
                                });
                              }}
                              placeholder="Enter cities (comma separated)"
                              className="mt-1"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* Contact Tab */}
          <TabsContent value="contact">
            <div className="space-y-6">
              {/* Add Contact Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Add Contacts
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-600">
                      Search and Add Contacts
                    </label>
                    <ContactMultiSelect
                      selectedContacts={selectedContacts}
                      onContactsChange={setSelectedContacts}
                      placeholder="Search for contacts by email..."
                      className="w-full"
                      mode="deal-attachment"
                      dealId={dealId}
                      onContactAddedToDeal={async (newContact) => {
                        // Refresh the deal contacts list
                        fetchDealContacts();
                      }}
                    />
                    <p className="text-sm text-gray-500">
                      Search for contacts by email to add them to this deal. You can select multiple contacts. 
                      New contacts created through the "Create New Contact" button will be automatically added to this deal.
                    </p>
                  </div>
                  
                  <div className="flex items-center gap-2 pt-2">
                    <div className="flex-1 border-t border-gray-200"></div>
                    <span className="text-sm text-gray-500 px-2">or</span>
                    <div className="flex-1 border-t border-gray-200"></div>
                  </div>
                  
                  <div className="flex items-center justify-center">
                    {/* ContactCreationModal is now handled inside ContactMultiSelect */}
                  </div>
                  
                  {selectedContacts.length > 0 && (
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={addContactsToDeal}
                        disabled={isAddingContacts}
                        className="flex items-center gap-2"
                      >
                        {isAddingContacts ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        ) : (
                          <Plus className="h-4 w-4" />
                        )}
                        Add {selectedContacts.length} Contact{selectedContacts.length !== 1 ? 's' : ''} to Deal
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Current Contacts Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Current Contacts ({dealContacts.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {dealContactsLoading ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                      <p className="text-gray-500 mt-2">Loading contacts...</p>
                    </div>
                  ) : dealContacts.length > 0 ? (
                    <div className="space-y-4">
                      {dealContacts.map((contact) => (
                        <div key={contact.contact_id} className="border rounded-lg p-4 bg-gray-50 hover:bg-gray-100 transition-colors">
                          <div className="flex items-center justify-between">
                            <div 
                              className="flex items-center gap-3 flex-1 cursor-pointer"
                              onClick={() => router.push(`/dashboard/people/${contact.contact_id}`)}
                            >
                              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <User className="h-5 w-5 text-blue-600" />
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">
                                  {contact.first_name} {contact.last_name}
                                </div>
                                <div className="text-sm text-gray-600">
                                  {contact.email || contact.personal_email}
                                </div>
                                {contact.title && (
                                  <div className="text-xs text-gray-500">
                                    {contact.title}
                                  </div>
                                )}
                                {contact.company_name && (
                                  <div className="text-xs text-gray-500">
                                    {contact.company_name}
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-xs text-gray-500">
                                Added {new Date(contact.added_at).toLocaleDateString()}
                              </span>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  removeContactFromDeal(contact.contact_id);
                                }}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">No contacts added to this deal yet.</p>
                      <p className="text-sm text-gray-400 mt-1">
                        Use the search above to add contacts to this deal.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Legacy Contact Display (if primary contact exists) */}
              {deal?.contact_id && contact && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Primary Contact
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {contactLoading ? (
                      <div className="text-center py-4">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"></div>
                        <p className="text-gray-500 mt-2">Loading contact...</p>
                      </div>
                    ) : (
                      <div 
                        className="cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-2"
                        onClick={() => router.push(`/dashboard/people/${contact.contact_id}`)}
                      >
                        <ContactCard contact={contact} />
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* Jobs Tab */}
          <TabsContent value="jobs">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Processing Jobs
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={fetchJobs}
                      disabled={jobsLoading}
                      className="flex items-center gap-2"
                    >
                      <div className={`h-4 w-4 ${jobsLoading ? 'animate-spin' : ''}`}>
                        {jobsLoading ? (
                          <div className="rounded-full border-2 border-gray-300 border-t-blue-600"></div>
                        ) : (
                          <div className="rounded-full border-2 border-gray-300"></div>
                        )}
                      </div>
                      Refresh
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {jobsLoading ? (
                  <div className="animate-pulse space-y-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  </div>
                ) : jobs.length > 0 ? (
                  <div className="space-y-4">
                    {jobs.map((job) => (
                      <div key={job.job_id} className="border rounded-lg p-4 bg-gray-50">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Badge 
                              variant={job.status === 'completed' ? 'default' : 
                                      job.status === 'failed' ? 'destructive' : 
                                      job.status === 'processing' ? 'secondary' : 'outline'}
                              className={job.status === 'completed' ? 'bg-green-100 text-green-800 border-green-200' :
                                       job.status === 'failed' ? 'bg-red-100 text-red-800 border-red-200' :
                                       job.status === 'processing' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                                       'bg-gray-100 text-gray-800 border-gray-200'}
                            >
                              {job.status}
                            </Badge>
                            <span className="text-sm font-medium text-gray-600">
                              {job.job_type === 'requirement_extraction' ? 'Requirement Extraction' : job.job_type}
                            </span>
                          </div>
                          <span className="text-xs text-gray-500">
                            {new Date(job.created_at).toLocaleString()}
                          </span>
                        </div>
                        
                        {job.metadata && (
                          <div className="text-sm text-gray-600 mb-2">
                            {job.metadata.description || job.metadata.file_count ? 
                              `${job.metadata.description || 'Processing'} - ${job.metadata.file_count || 0} files` : 
                              'Processing job'
                            }
                          </div>
                        )}

                        {job.status === 'processing' && (
                          <div className="flex items-center gap-2 text-sm text-blue-600">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                            Processing...
                          </div>
                        )}

                        {job.status === 'completed' && job.result && (
                          <div className="text-sm text-green-600">
                            ✓ {job.result.message || 'Job completed successfully'}
                          </div>
                        )}

                        {job.status === 'failed' && job.error_message && (
                          <div className="text-sm text-red-600">
                            ✗ {job.error_message}
                          </div>
                        )}

                        {job.completed_at && (
                          <div className="text-xs text-gray-500 mt-2">
                            Completed: {new Date(job.completed_at).toLocaleString()}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No processing jobs found for this deal.</p>
                    <p className="text-sm mt-2">
                      Jobs will appear here when you run requirement extraction or other processing tasks.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        </div>
      </div>

      {/* Delete Investment Criteria Confirmation Dialog */}
      <AlertDialog open={Boolean(criteriaToDelete)} onOpenChange={() => setCriteriaToDelete(null)}>
        <AlertDialogContent className="max-w-md shadow-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-400 flex items-center">
              <Trash2 className="h-5 w-5 mr-2" />
              Delete Investment Criteria
            </AlertDialogTitle>
            <AlertDialogDescription className="text-gray-600 dark:text-gray-300">
              Are you sure you want to delete Investment Criteria #{criteriaToDelete ? criteriaToDelete.index + 1 : ''}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteCriteria} className="bg-red-600 hover:bg-red-700 text-white">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default DealDetailNew;