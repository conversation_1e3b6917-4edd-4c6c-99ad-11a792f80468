import React, { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { ContactMultiSelect } from "@/components/ui/contact-multi-select";
import { Contact } from "../people/shared/types";
import ContactCard from "../people/list-components/ContactCard";

interface ContactTabProps {
  selectedContacts: Contact[];
  setSelectedContacts: (contacts: Contact[]) => void;
  dealId: string;
  fetchDealContacts: () => void;
  addContactsToDeal: () => void;
  removeContactFromDeal: (contactId: number) => void;
  isAddingContacts: boolean;
  matchingContacts: any[];
  matchingContactsLoading: boolean;
  matchingContactsError: string | null;
  matchingContactsFiltering: any;
  showAllMatches: boolean;
  isCrmMode: boolean;
  matchingContactsPagination: any;
  expandedContactId: number | null;
  setExpandedContactId: (id: number | null) => void;
  setMatchingContactsFiltering: (filtering: any) => void;
  setShowAllMatches: (show: boolean) => void;
  setIsCrmMode: (mode: boolean) => void;
  setMatchingContactsPagination: (pagination: any) => void;
}

const ContactTab: React.FC<ContactTabProps> = ({
  selectedContacts,
  setSelectedContacts,
  dealId,
  fetchDealContacts,
  addContactsToDeal,
  removeContactFromDeal,
  isAddingContacts,
  matchingContacts,
  matchingContactsLoading,
  matchingContactsError,
  matchingContactsFiltering,
  showAllMatches,
  isCrmMode,
  matchingContactsPagination,
  expandedContactId,
  setExpandedContactId,
  setMatchingContactsFiltering,
  setShowAllMatches,
  setIsCrmMode,
  setMatchingContactsPagination,
}) => {
  


  return (
    <div className="space-y-6">
      {/* Add Contact Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add Contacts
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-600">
              Search and Add Contacts
            </label>
            <ContactMultiSelect
              selectedContacts={selectedContacts}
              onContactsChange={setSelectedContacts}
              placeholder="Search for contacts by email..."
              className="w-full"
              mode="deal-attachment"
              dealId={dealId}
              dealVersion="v2"
              onContactAddedToDeal={async (newContact) => {
                // Refresh the deal contacts list
                fetchDealContacts();
              }}
            />
            <p className="text-sm text-gray-500">
              Search for contacts by email to add them to this deal. You can select multiple contacts. 
              New contacts created through the "Create New Contact" button will be automatically added to this deal.
            </p>
          </div>
          
          <div className="flex items-center gap-2 pt-2">
            <div className="flex-1 border-t border-gray-200"></div>
            <span className="text-sm text-gray-500 px-2">or</span>
            <div className="flex-1 border-t border-gray-200"></div>
          </div>
        
          <div className="flex items-center justify-center">
            {/* ContactCreationModal is now handled inside ContactMultiSelect */}
          </div>
          
          {selectedContacts.length > 0 && (
            <div className="flex items-center gap-2">
              <Button
                onClick={addContactsToDeal}
                disabled={isAddingContacts}
                className="flex items-center gap-2"
              >
                {isAddingContacts ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Plus className="h-4 w-4" />
                )}
                Add {selectedContacts.length} Contact{selectedContacts.length !== 1 ? 's' : ''} to Deal
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Deal Contacts Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Deal Contacts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {matchingContactsLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </div>
            ) : matchingContactsError ? (
              <div className="text-center py-8">
                <p className="text-red-600">{matchingContactsError}</p>
              </div>
            ) : matchingContacts.length > 0 ? (
              <div className="grid gap-4">
                {matchingContacts.map((contact) => (
                  <div key={contact.contact_id} className="border rounded-lg p-4 bg-white">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">
                          {contact.first_name} {contact.last_name}
                        </h4>
                        <p className="text-sm text-gray-600">{contact.company_name}</p>
                        <p className="text-sm text-gray-500">{contact.email}</p>
                        {contact.title && <p className="text-sm text-gray-500">{contact.title}</p>}
                      </div>
                      <div className="flex items-center gap-3">
                        <p className="text-xs text-gray-400">Added: {new Date(contact.added_at).toLocaleDateString()}</p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (confirm(`Are you sure you want to remove ${contact.first_name} ${contact.last_name} from this deal?`)) {
                              removeContactFromDeal(contact.contact_id);
                            }
                          }}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          Remove
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">No contacts found for this deal.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Contact Matching Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Contact Matching
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <Button
                variant={isCrmMode ? "default" : "outline"}
                onClick={() => setIsCrmMode(true)}
                size="sm"
              >
                CRM Mode
              </Button>
              <Button
                variant={!isCrmMode ? "default" : "outline"}
                onClick={() => setIsCrmMode(false)}
                size="sm"
              >
                Deal Matching Mode
              </Button>
            </div>

            {matchingContactsFiltering && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">Active Filters</h4>
                <div className="text-sm text-blue-800">
                  <p>Investment Criteria: {matchingContactsFiltering.investmentCriteria}</p>
                  <p>Property Type: {matchingContactsFiltering.propertyType}</p>
                  <p>Deal Size: {matchingContactsFiltering.dealSize}</p>
                </div>
              </div>
            )}

            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                onClick={() => setShowAllMatches(!showAllMatches)}
                size="sm"
              >
                {showAllMatches ? "Show Top Matches" : "Show All Matches"}
              </Button>

              {matchingContactsPagination && (
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={matchingContactsPagination.currentPage === 1}
                    onClick={() => setMatchingContactsPagination({
                      ...matchingContactsPagination,
                      currentPage: matchingContactsPagination.currentPage - 1
                    })}
                  >
                    Previous
                  </Button>
                  <span className="text-sm text-gray-600">
                    Page {matchingContactsPagination.currentPage} of {matchingContactsPagination.totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={matchingContactsPagination.currentPage === matchingContactsPagination.totalPages}
                    onClick={() => setMatchingContactsPagination({
                      ...matchingContactsPagination,
                      currentPage: matchingContactsPagination.currentPage + 1
                    })}
                  >
                    Next
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ContactTab;
