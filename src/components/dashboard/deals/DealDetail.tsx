"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  ArrowLeft,
  DollarSign,
  MapPin,
  Building2,
  User,
  Calendar,
  TrendingUp,
  Percent,
  FileText,
  Edit,
  Users,
  Database,
  Target,
  Clock,
  Home,
  Ruler,
} from "lucide-react";
import { Deal, InvestmentCriteria } from "./shared/types";
import DealFiles from "./DealFiles";
import { Contact } from "../people/shared/types";
import ContactCard from "../people/list-components/ContactCard";
import DataQualityBannerV1 from "./DataQualityBannerV1";
import DealEditForm from "./DealEditForm";
import { toast } from "sonner";
import { Play } from "lucide-react";

interface DealDetailProps {
  dealId: string;
}

const DealDetail: React.FC<DealDetailProps> = ({ dealId }) => {
  const router = useRouter();
  const [deal, setDeal] = useState<Deal | null>(null);
  const [contact, setContact] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [contactLoading, setContactLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [showLLMOutput, setShowLLMOutput] = useState(false);
  const [llmOutputData, setLLMOutputData] = useState<any>(null);
  const [llmOutputLoading, setLLMOutputLoading] = useState(false);
  const [matchingContacts, setMatchingContacts] = useState<any[]>([]);
  const [matchingContactsLoading, setMatchingContactsLoading] = useState(false);
  const [matchingContactsError, setMatchingContactsError] = useState<string | null>(null);
  const [isCrmMode, setIsCrmMode] = useState(true);
  const [expandedContactId, setExpandedContactId] = React.useState<number | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isRunningRequirementExtraction, setIsRunningRequirementExtraction] = useState(false);
  const [jobs, setJobs] = useState<any[]>([]);
  const [jobsLoading, setJobsLoading] = useState(false);
  const [jobsRefreshInterval, setJobsRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // Handle saving deal changes
  const handleSaveDeal = async (updatedDeal: Deal & { deleted_criteria_ids?: number[] }) => {
    setIsSaving(true);
    try {
      const response = await fetch(`/api/deals/${dealId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updatedDeal),
      });

      if (!response.ok) {
        throw new Error("Failed to update deal");
      }

      const savedDeal = await response.json();
      setDeal(savedDeal);
      setIsEditing(false);
      setActiveTab("overview");
      toast.success("Deal updated successfully");
    } catch (error) {
      console.error("Error updating deal:", error);
      toast.error("Failed to update deal");
    } finally {
      setIsSaving(false);
    }
  };

  // Handle running requirement extraction
  const handleRunRequirementExtraction = async () => {
    setIsRunningRequirementExtraction(true);
    try {
      const response = await fetch(`/api/deals/${dealId}/run-requirement-extraction`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to run requirement extraction");
      }

      toast.success(result.message || "Requirement extraction completed successfully");
      
      // Refresh deal data to show updated criteria
      const dealResponse = await fetch(`/api/deals/${dealId}`);
      if (dealResponse.ok) {
        const updatedDeal = await dealResponse.json();
        setDeal(updatedDeal);
      }

      // Refresh jobs list
      fetchJobs();
    } catch (error) {
      console.error("Error running requirement extraction:", error);
      toast.error(error instanceof Error ? error.message : "Failed to run requirement extraction");
    } finally {
      setIsRunningRequirementExtraction(false);
    }
  };

  // Fetch jobs for this deal
  const fetchJobs = async () => {
    setJobsLoading(true);
    try {
      const response = await fetch(`/api/deals/${dealId}/jobs`);
      if (response.ok) {
        const jobsData = await response.json();
        setJobs(jobsData.jobs || []);
        
        // Check if there are any processing jobs and set up auto-refresh
        const hasProcessingJobs = jobsData.jobs?.some((job: any) => 
          job.status === 'pending' || job.status === 'processing' || job.status === 'active'
        );
        
        if (hasProcessingJobs) {
          // Clear existing interval
          if (jobsRefreshInterval) {
            clearInterval(jobsRefreshInterval);
          }
          
          // Set up new interval to refresh every 5 seconds
          const interval = setInterval(fetchJobs, 5000);
          setJobsRefreshInterval(interval);
        } else {
          // Clear interval if no processing jobs
          if (jobsRefreshInterval) {
            clearInterval(jobsRefreshInterval);
            setJobsRefreshInterval(null);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching jobs:", error);
    } finally {
      setJobsLoading(false);
    }
  };

  useEffect(() => {
    async function fetchDeal() {
      try {
        setLoading(true);
        const response = await fetch(`/api/deals/${dealId}`);
        if (response.ok) {
          const data = await response.json();
          setDeal(data);
        } else {
          setError("Failed to load deal");
        }
      } catch (err) {
        setError("Error loading deal");
      } finally {
        setLoading(false);
      }
    }

    if (dealId) {
      fetchDeal();
      fetchJobs();
    }
    
    // Cleanup interval on unmount
    return () => {
      if (jobsRefreshInterval) {
        clearInterval(jobsRefreshInterval);
      }
    };
  }, [dealId]);

  // Fetch contact data when contact tab is active and deal has contact_id
  useEffect(() => {
    async function fetchContact() {
      if (activeTab === "contact" && deal?.contact_id && !contact) {
        try {
          setContactLoading(true);
          const response = await fetch(`/api/contacts/${deal.contact_id}`);
          if (response.ok) {
            const contactData = await response.json();
            setContact(contactData);
          } else {
            console.error("Failed to load contact");
          }
        } catch (err) {
          console.error("Error loading contact:", err);
        } finally {
          setContactLoading(false);
        }
      }
    }

    fetchContact();
  }, [activeTab, deal?.contact_id, contact]);

  // Fetch matching contacts for this deal
  useEffect(() => {
    async function fetchMatchingContacts() {
      if (!dealId) return;
      setMatchingContactsLoading(true);
      setMatchingContactsError(null);
      try {
        const crmModeParam = isCrmMode ? '&crm_mode=true' : '';
        const res = await fetch(`/api/matching/contacts-for-deal/${dealId}?${crmModeParam}`);
        if (!res.ok) throw new Error("Failed to fetch matching contacts");
        const data = await res.json();
        setMatchingContacts(data.matches || []);
      } catch (err) {
        setMatchingContactsError("Error loading matching contacts");
      } finally {
        setMatchingContactsLoading(false);
      }
    }
    fetchMatchingContacts();
  }, [dealId, isCrmMode]);

  // Function to fetch and display LLM output
  const handleShowLLMOutput = async () => {
    if (!deal?.deal_id) return;

    try {
      setLLMOutputLoading(true);

      // First, get the files associated with this deal
      const filesResponse = await fetch(`/api/deals/${deal.deal_id}/files`);
      if (!filesResponse.ok) {
        throw new Error("Failed to fetch deal files");
      }

      const filesData = await filesResponse.json();

      // Find the LLM output file
      const llmOutputFile = filesData.files?.find(
        (file: any) =>
          file.relationship_type === "llm_output" ||
          file.original_name?.includes("llm_output") ||
          file.title?.includes("LLM Output")
      );

      if (!llmOutputFile) {
        throw new Error("No LLM output file found for this deal");
      }

      // Fetch the file content
      const fileResponse = await fetch(`/api/files/${llmOutputFile.file_id}`);
      if (!fileResponse.ok) {
        throw new Error("Failed to fetch LLM output file");
      }

      const fileData = await fileResponse.json();

      // For now, we'll show the metadata. In a production system,
      // you'd want to fetch the actual file content from storage
      setLLMOutputData({
        file: llmOutputFile,
        metadata: fileData.file,
        content: "File content would be loaded from storage in production",
      });

      setShowLLMOutput(true);
    } catch (error) {
      console.error("Error fetching LLM output:", error);
      alert(
        "Failed to load LLM output: " +
          (error instanceof Error ? error.message : "Unknown error")
      );
    } finally {
      setLLMOutputLoading(false);
    }
  };

  const formatCurrency = (amount: number | string | null): string => {
    if (amount === null || amount === undefined) return "N/A";
    const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
    if (isNaN(numAmount)) return "N/A";
    if (numAmount >= 1000000) {
      return `$${(numAmount / 1000000).toFixed(1)}M`;
    }
    if (numAmount >= 1000) {
      return `$${(numAmount / 1000).toFixed(1)}K`;
    }
    return `$${numAmount.toLocaleString()}`;
  };

  const formatPercentage = (value: number | string | null): string => {
    if (value === null || value === undefined) return "N/A";
    const numValue = typeof value === "string" ? parseFloat(value) : value;
    if (isNaN(numValue)) return "N/A";
    return `${(numValue * 100).toFixed(1)}%`;
  };

  const formatNumber = (
    value: number | string | null,
    unit?: string
  ): string => {
    if (value === null || value === undefined) return "N/A";
    const numValue = typeof value === "string" ? parseFloat(value) : value;
    if (isNaN(numValue)) return "N/A";
    return `${numValue.toLocaleString()}${unit ? ` ${unit}` : ""}`;
  };

  const getStatusColor = (status: string | null): string => {
    switch (status?.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "closed":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "inactive":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const formatArrayField = (field: string[] | null): string => {
    if (!field || field.length === 0) return "N/A";
    return field.join(", ");
  };

  // Get primary investment criteria (first one or most relevant)
  const primaryCriteria = deal?.investment_criteria?.[0];

  // Get location from investment criteria
  const getLocation = (): string => {
    if (!primaryCriteria) return "N/A";
    const parts = [
      primaryCriteria.city?.[0],
      primaryCriteria.state?.[0],
      primaryCriteria.country?.[0],
    ].filter(Boolean);
    return parts.length > 0 ? parts.join(", ") : "N/A";
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !deal) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Deal Not Found
            </h2>
            <p className="text-gray-600 mb-6">
              {error || "The requested deal could not be found."}
            </p>
            <Button onClick={() => router.push("/dashboard/deals")}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Deals
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => router.push("/dashboard/deals")}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Deals
          </Button>

          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {deal.deal_name || "Unnamed Deal"}
              </h1>
              <div className="flex items-center gap-4 text-gray-600">
                {deal.sponsor_name && (
                  <span className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    {deal.sponsor_name}
                  </span>
                )}
                <span className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  {getLocation()}
                </span>
                {deal.zip_code && (
                  <span className="flex items-center gap-1">
                    <Home className="h-4 w-4" />
                    {deal.zip_code}
                  </span>
                )}
                <span className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Created{" "}
                  {deal.created_at
                    ? new Date(deal.created_at).toLocaleDateString()
                    : "N/A"}
                </span>
              </div>
            </div>

            <div className="flex gap-2">
              {!isEditing && (
                <>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsEditing(true);
                      setActiveTab("edit");
                    }}
                    className="flex items-center"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Deal
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleRunRequirementExtraction}
                    disabled={isRunningRequirementExtraction}
                    className="flex items-center"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    {isRunningRequirementExtraction ? "Running..." : "Run Requirement Extraction"}
                  </Button>
                </>
              )}
              {deal.status && (
                <Badge
                  variant="outline"
                  className={getStatusColor(deal.status)}
                >
                  {deal.status}
                </Badge>
              )}
              {deal.deal_stage && (
                <Badge
                  variant="outline"
                  className="bg-blue-50 text-blue-700 border-blue-200"
                >
                  {deal.deal_stage}
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* Data Quality Banner */}
        {deal.data_quality_metrics && (
          <DataQualityBannerV1
            dataQualityMetrics={{
              overview: deal.data_quality_metrics.deal,
              debt: deal.data_quality_metrics.investment_criteria,
              equity: deal.data_quality_metrics.investment_criteria
            }}
            dealName={deal.deal_name || "Unnamed Deal"}
          />
        )}

        {/* Tabs */}
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <TabsList className={`grid w-full mb-8 ${isEditing ? 'md:grid-cols-7' : 'md:grid-cols-6'} grid-cols-2`}>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="deal-matching">Deal Matching</TabsTrigger>
            <TabsTrigger value="investment-criteria">
              Investment Criteria
            </TabsTrigger>
            <TabsTrigger value="contact">Contact</TabsTrigger>
            <TabsTrigger value="jobs">Jobs</TabsTrigger>
            {isEditing && (
              <TabsTrigger value="edit">Edit Deal</TabsTrigger>
            )}
          </TabsList>

          {/* Edit Form Tab */}
          {isEditing && (
            <TabsContent value="edit" className="mt-6">
              <DealEditForm
                deal={deal}
                onSave={handleSaveDeal}
                onCancel={() => {
                  setIsEditing(false);
                  setActiveTab("overview");
                }}
                isLoading={isSaving}
              />
            </TabsContent>
          )}

          {/* Overview Tab */}
          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Deal Details */}
              <div className="lg:col-span-2 space-y-6">
                {/* Property Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      Property Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {deal.property_description && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Description
                        </label>
                        <p className="text-sm text-gray-900 mt-1">
                          {deal.property_description}
                        </p>
                      </div>
                    )}
                    <div className="grid grid-cols-2 gap-4">
                      {deal.lot_area && (
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Lot Area
                          </label>
                          <p className="text-lg font-semibold">
                            {formatNumber(deal.lot_area, "sq ft")}
                          </p>
                        </div>
                      )}
                      {deal.floor_area_ratio && (
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Floor Area Ratio
                          </label>
                          <p className="text-lg font-semibold">
                            {formatNumber(deal.floor_area_ratio)}
                          </p>
                        </div>
                      )}
                      {deal.zoning_square_footage && (
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Zoning Square Footage
                          </label>
                          <p className="text-lg font-semibold">
                            {formatNumber(deal.zoning_square_footage, "sq ft")}
                          </p>
                        </div>
                      )}
                      {deal.neighborhood && (
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Neighborhood
                          </label>
                          <p className="text-lg font-semibold">
                            {deal.neighborhood}
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Financial Projections */}
                {(deal.yield_on_cost ||
                  deal.projected_gp_equity_multiple ||
                  deal.projected_gp_irr ||
                  deal.projected_lp_equity_multiple ||
                  deal.projected_lp_irr ||
                  deal.projected_total_equity_multiple ||
                  deal.projected_total_irr) && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <TrendingUp className="h-5 w-5" />
                        Financial Projections
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-3 gap-4 text-center">
                        {deal.yield_on_cost && (
                          <div className="text-center">
                            <div className="text-sm text-gray-600 mb-1">
                              Yield on Cost
                            </div>
                            <div className="text-2xl font-bold text-green-600">
                              {formatPercentage(deal.yield_on_cost)}
                            </div>
                          </div>
                        )}
                        {deal.projected_gp_irr && (
                          <div className="text-center">
                            <div className="text-sm text-gray-600 mb-1">
                              GP IRR
                            </div>
                            <div className="text-2xl font-bold text-blue-600">
                              {formatPercentage(deal.projected_gp_irr)}
                            </div>
                          </div>
                        )}
                        {deal.projected_lp_irr && (
                          <div className="text-center">
                            <div className="text-sm text-gray-600 mb-1">
                              LP IRR
                            </div>
                            <div className="text-2xl font-bold text-purple-600">
                              {formatPercentage(deal.projected_lp_irr)}
                            </div>
                          </div>
                        )}
                        {deal.projected_gp_equity_multiple && (
                          <div className="text-center">
                            <div className="text-sm text-gray-600 mb-1">
                              GP Equity Multiple
                            </div>
                            <div className="text-2xl font-bold text-orange-600">
                              {formatNumber(deal.projected_gp_equity_multiple)}x
                            </div>
                          </div>
                        )}
                        {deal.projected_lp_equity_multiple && (
                          <div className="text-center">
                            <div className="text-sm text-gray-600 mb-1">
                              LP Equity Multiple
                            </div>
                            <div className="text-2xl font-bold text-red-600">
                              {formatNumber(deal.projected_lp_equity_multiple)}x
                            </div>
                          </div>
                        )}
                        {deal.projected_total_irr && (
                          <div className="text-center">
                            <div className="text-sm text-gray-600 mb-1">
                              Total IRR
                            </div>
                            <div className="text-2xl font-bold text-indigo-600">
                              {formatPercentage(deal.projected_total_irr)}
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Processing Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        Processing Information
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleShowLLMOutput}
                        disabled={llmOutputLoading}
                        className="text-xs"
                      >
                        {llmOutputLoading
                          ? "Loading..."
                          : "View Raw LLM Output"}
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Document Types
                        </label>
                        <p>{formatArrayField(deal.document_type)}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Extraction Confidence
                        </label>
                        <p>{deal.extraction_confidence || "N/A"}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          LLM Model
                        </label>
                        <p>{deal.llm_model_used || "N/A"}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Extraction Methods
                        </label>
                        <p>{formatArrayField(deal.extraction_method)}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Document Sources
                        </label>
                        <p>{formatArrayField(deal.document_source)}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Processing Duration
                        </label>
                        <p>
                          {deal.processing_duration_ms
                            ? `${deal.processing_duration_ms}ms`
                            : "N/A"}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Extraction Date
                        </label>
                        <p>
                          {deal.extraction_timestamp
                            ? new Date(
                                deal.extraction_timestamp
                              ).toLocaleDateString()
                            : "N/A"}
                        </p>
                      </div>
                    </div>
                    {deal.document_filename &&
                      deal.document_filename.length > 0 && (
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Document Files
                          </label>
                          <ul className="mt-1 space-y-1">
                            {deal.document_filename.map((filename, index) => (
                              <li
                                key={index}
                                className="text-sm text-gray-900 flex items-center gap-2"
                              >
                                <FileText className="h-3 w-3" />
                                {filename}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    {deal.processing_notes && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Processing Notes
                        </label>
                        <p className="text-sm text-gray-900 mt-1">
                          {deal.processing_notes}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Data Quality Issues */}
                {deal.data_quality_issues &&
                  Object.keys(deal.data_quality_issues).length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-yellow-700">
                          Data Quality Issues
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {Object.entries(deal.data_quality_issues).map(
                            ([issue, value]) => (
                              <li
                                key={issue}
                                className="text-sm text-yellow-800 bg-yellow-50 p-2 rounded"
                              >
                                {issue.replace(/_/g, " ")}
                              </li>
                            )
                          )}
                        </ul>
                      </CardContent>
                    </Card>
                  )}

                {/* Extra Fields */}
                {deal.extra_fields &&
                  Object.keys(deal.extra_fields).length > 0 && (
                    <Card className="mt-6">
                      <CardHeader>
                        <CardTitle>Additional Data</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="overflow-x-auto">
                          <table className="min-w-full text-xs border">
                            <tbody>
                              {Object.entries(deal.extra_fields).map(
                                ([key, value]) => (
                                  <tr key={key} className="border-b">
                                    <td className="font-mono px-2 py-1 border-r bg-gray-100">
                                      {key}
                                    </td>
                                    <td className="px-2 py-1">
                                      {typeof value === "object"
                                        ? JSON.stringify(value)
                                        : String(value)}
                                    </td>
                                  </tr>
                                )
                              )}
                            </tbody>
                          </table>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                {/* Deal Files Section */}
                <DealFiles dealId={dealId} />
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Status Card */}
                <Card>
                  <CardHeader>
                    <CardTitle>Status</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Current Status
                      </label>
                      <div className="mt-1">
                        <Badge
                          variant="outline"
                          className={getStatusColor(deal.status)}
                        >
                          {deal.status || "Unknown"}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Deal Stage
                      </label>
                      <p>{deal.deal_stage || "N/A"}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Priority
                      </label>
                      <p>{deal.priority || "N/A"}</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Quick Summary */}
                {primaryCriteria && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Target className="h-5 w-5" />
                        Investment Summary
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {/* Capital Positions - PROMINENT FIRST */}
                      <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                        <label className="text-sm font-semibold text-blue-700">
                          Capital Positions
                        </label>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {primaryCriteria.capital_position?.map((position: string, index: number) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                            >
                              {position}
                            </span>
                          )) || (
                            <span className="text-gray-500 text-sm">No capital positions specified</span>
                          )}
                        </div>
                      </div>
                      
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Property Types
                        </label>
                        <p>
                          {formatArrayField(primaryCriteria.property_types)}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Strategies
                        </label>
                        <p>{formatArrayField(primaryCriteria.strategies)}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Financial Products
                        </label>
                        <p>
                          {formatArrayField(primaryCriteria.financial_products)}
                        </p>
                      </div>
                      {(primaryCriteria.minimum_deal_size ||
                        primaryCriteria.maximum_deal_size) && (
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Deal Size
                          </label>
                          <p>
                            {primaryCriteria.minimum_deal_size &&
                            primaryCriteria.maximum_deal_size
                              ? `${formatCurrency(
                                  primaryCriteria.minimum_deal_size
                                )} - ${formatCurrency(
                                  primaryCriteria.maximum_deal_size
                                )}`
                              : formatCurrency(
                                  primaryCriteria.minimum_deal_size ||
                                    primaryCriteria.maximum_deal_size
                                )}
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </TabsContent>

          {/* Deal Matching Tab */}
          <TabsContent value="deal-matching">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Matching Contacts
                  {matchingContacts.length > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {matchingContacts.length} matches found
                    </Badge>
                  )}
                  {matchingContacts.some((match: any) => match.fallback_used) && (
                    <Badge variant="outline" className="ml-2 bg-orange-50 text-orange-700 border-orange-200">
                      Company fallback used
                    </Badge>
                  )}
                </CardTitle>
                <div className="flex items-center gap-4 mt-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="crm-mode-contacts"
                      checked={isCrmMode}
                      onCheckedChange={(checked: boolean | "indeterminate") => setIsCrmMode(checked === true)}
                    />
                    <label htmlFor="crm-mode-contacts" className="text-sm font-medium text-gray-700">
                      CRM Mode
                    </label>
                  </div>
                  {isCrmMode && (
                    <div className="text-xs text-gray-600 bg-blue-50 px-2 py-1 rounded">
                      Only showing contacts with requested investment criteria
                    </div>
                  )}
                </div>
                {/* Show field weights info if available */}
                {matchingContacts.length > 0 && matchingContacts[0]?.scoring_method === "normalized_weights" && (
                  <div className="text-sm text-gray-600 mt-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Using normalized field weights (always sum to 1.0)
                    </div>
                    <div className="text-xs mt-1">
                      All contacts must have matching capital position to be included
                    </div>
                  </div>
                )}
              </CardHeader>
              <CardContent>
                {matchingContactsLoading ? (
                  <div className="text-gray-500">Loading matching contacts...</div>
                ) : matchingContactsError ? (
                  <div className="text-red-600">{matchingContactsError}</div>
                ) : matchingContacts.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <div className="text-gray-500">No matching contacts found for this deal.</div>
                    <div className="text-sm text-gray-400 mt-2">
                      Contacts must have matching capital position and other criteria
                    </div>
                  </div>
                ) : (
                  <>
                    {/* Summary Stats */}
                    <div className="mb-6 grid grid-cols-1 sm:grid-cols-4 gap-4">
                      <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                        <div className="text-sm font-medium text-blue-900">Total Matches</div>
                        <div className="text-2xl font-bold text-blue-700">{matchingContacts.length}</div>
                      </div>
                      <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                        <div className="text-sm font-medium text-green-900">Avg Score</div>
                        <div className="text-2xl font-bold text-green-700">
                          {Math.round(matchingContacts.reduce((sum, m) => sum + (m.score || 0), 0) / matchingContacts.length)}
                        </div>
                      </div>
                      <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
                        <div className="text-sm font-medium text-purple-900">High Quality</div>
                        <div className="text-2xl font-bold text-purple-700">
                          {matchingContacts.filter(m => (m.score || 0) >= 70).length}
                        </div>
                      </div>
                      <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
                        <div className="text-sm font-medium text-orange-900">Capital Match</div>
                        <div className="text-2xl font-bold text-orange-700">100%</div>
                        <div className="text-xs text-orange-600">Required</div>
                      </div>
                    </div>

                    {/* Contact Cards */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 min-w-0">
                      {matchingContacts.map((match) => {
                        const contact: Contact = {
                          contact_id: Number(match.contact_id),
                          first_name: match.first_name || "",
                          last_name: match.last_name || "",
                          full_name: match.full_name || `${match.first_name || ''} ${match.last_name || ''}`,
                          email: match.email,
                          company_id: match.company_id,
                          title: match.title,
                          phone_number: match.phone_number,
                          linkedin_url: match.linkedin_url,
                          contact_city: Array.isArray(match.contact_city) ? match.contact_city[0] : match.contact_city,
                          contact_state: Array.isArray(match.contact_state) ? match.contact_state[0] : match.contact_state,
                          company_name: match.company_name,
                        };
                        const isExpanded = expandedContactId === contact.contact_id;
                        const scoreColor = (match.score || 0) >= 80 ? 'bg-green-600' : 
                                          (match.score || 0) >= 60 ? 'bg-blue-600' :
                                          (match.score || 0) >= 40 ? 'bg-yellow-600' : 'bg-orange-600';
                        
                        return (
                          <Card key={contact.contact_id} className="relative group min-w-0 overflow-visible hover:shadow-md transition-shadow">
                            <CardContent className="flex flex-col items-stretch">
                              <ContactCard
                                contact={contact}
                                onSelectContact={() => router.push(`/dashboard/people/${contact.contact_id}`)}
                                isSelected={false}
                                onToggleSelection={() => {}}
                              />
                              
                              {/* Enhanced Score Badge */}
                              {match.score != null && (
                                <div className="absolute top-2 right-2 z-10">
                                  <div className={`${scoreColor} text-white text-xs font-semibold px-2 py-1 rounded shadow group-hover:scale-110 transition-transform`}>
                                    {match.score}%
                                  </div>
                                  <div className="bg-green-600 text-white text-xs px-2 py-0.5 rounded-b shadow">
                                    ✓ Capital
                                  </div>
                                  {/* New: Show criteria count and best score */}
                                  {match.matching_criteria_count > 1 && (
                                    <div className="mt-1 flex flex-col gap-1">
                                      <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                        {match.matching_criteria_count} criteria
                                      </Badge>
                                      {match.best_score > match.score && (
                                        <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                                          Best: {match.best_score}%
                                        </Badge>
                                      )}
                                    </div>
                                  )}
                                  {/* Show fallback indicator */}
                                  {match.fallback_used && (
                                    <div className="mt-1">
                                      <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700 border-orange-200">
                                        Company Criteria
                                      </Badge>
                                    </div>
                                  )}
                                </div>
                              )}

                              {/* Quick Match Indicators - Show All */}
                              <div className="mt-2 flex flex-wrap gap-1 max-h-20 overflow-y-auto">
                                {match.breakdown?.filter((b: any) => b.score > 0).map((b: any, idx: number) => (
                                  <Badge key={idx} variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                    {b.field.replace(/_/g, ' ')}: {b.score}%
                                  </Badge>
                                ))}
                                {(!match.breakdown || match.breakdown.filter((b: any) => b.score > 0).length === 0) && (
                                  <Badge variant="outline" className="text-xs text-gray-500">
                                    Capital position match only
                                  </Badge>
                                )}
                              </div>

                              <Button
                                variant="outline"
                                size="sm"
                                className="mt-2 self-end"
                                onClick={() => setExpandedContactId(isExpanded ? null : contact.contact_id)}
                              >
                                {isExpanded ? "Hide Details" : "Show Details"}
                              </Button>

                              {isExpanded && (
                                <div className="mt-3 p-3 border rounded bg-gray-50 space-y-3">
                                  {/* Capital Position Match */}
                                  <div className="bg-green-100 border border-green-300 rounded-lg p-2">
                                    <div className="flex items-center gap-2">
                                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                      <span className="font-semibold text-green-800 text-sm">Capital Position Match</span>
                                      <Badge className="bg-green-600 text-white">Required ✓</Badge>
                                    </div>
                                    <div className="text-xs text-green-700 mt-1">
                                      This contact meets the deal's capital position requirements
                                    </div>
                                  </div>

                                  {/* Multiple Criteria Summary */}
                                  {match.matching_criteria_count > 1 && (
                                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-3">
                                      <div className="flex items-center justify-between mb-2">
                                        <span className="font-semibold text-sm text-blue-900">Multiple Criteria Match</span>
                                        <Badge className="bg-blue-600 text-white text-xs">
                                          {match.matching_criteria_count} criteria
                                        </Badge>
                                      </div>
                                      <div className="grid grid-cols-2 gap-2 text-xs">
                                        <div>
                                          <span className="text-blue-700 font-medium">Average Score:</span>
                                          <span className="ml-1 font-semibold">{match.score}%</span>
                                        </div>
                                        <div>
                                          <span className="text-blue-700 font-medium">Best Match:</span>
                                          <span className="ml-1 font-semibold">{match.best_score}%</span>
                                        </div>
                                      </div>
                                    </div>
                                  )}

                                  {/* Individual Criteria Breakdowns */}
                                  {match.all_criteria_matches && match.all_criteria_matches.length > 0 && (
                                    <div className="space-y-2">
                                      <div className="flex items-center justify-between">
                                        <span className="font-semibold text-sm">Criteria Breakdown</span>
                                        <Badge variant="outline" className="text-xs">
                                          {match.all_criteria_matches.length} matches
                                        </Badge>
                                      </div>
                                      
                                      {match.all_criteria_matches.map((criteria: any, criteriaIdx: number) => (
                                        <div key={criteria.criteria_id || criteriaIdx} className="border rounded-lg p-3 bg-white">
                                          <div className="flex items-center justify-between mb-2">
                                            <span className="font-medium text-sm text-gray-700">
                                              Criteria #{criteriaIdx + 1}
                                            </span>
                                            <div className="flex items-center gap-2">
                                              <span className="text-sm font-semibold">{criteria.score}%</span>
                                              {criteria.score === match.best_score && (
                                                <Badge className="bg-green-600 text-white text-xs">Best</Badge>
                                              )}
                                            </div>
                                          </div>
                                          
                                          {/* Field-by-field breakdown for this criteria - Show All Fields */}
                                          <div className="grid grid-cols-1 gap-1 max-h-40 overflow-y-auto">
                                            {criteria.breakdown?.map((b: any, idx: number) => (
                                              <div key={idx} className="flex items-center justify-between py-1 px-2 bg-gray-50 rounded text-xs">
                                                <span className="capitalize font-medium">{b.field.replace(/_/g, ' ')}</span>
                                                <div className="flex items-center gap-2">
                                                  <span className={`font-semibold ${b.score >= 50 ? 'text-green-600' : b.score > 0 ? 'text-blue-600' : 'text-gray-500'}`}>
                                                    {b.score}%
                                                  </span>
                                                  {b.weight && (
                                                    <span className="text-gray-400">({(b.weight * 100).toFixed(0)}%)</span>
                                                  )}
                                                </div>
                                              </div>
                                            ))}
                                          </div>
                                          
                                          {/* Criteria-specific reasons - Show All */}
                                          {criteria.reasons && criteria.reasons.length > 0 && (
                                            <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded max-h-24 overflow-y-auto">
                                              <div className="text-xs text-blue-700">
                                                {criteria.reasons.join(" • ")}
                                              </div>
                                            </div>
                                          )}
                                        </div>
                                      ))}
                                    </div>
                                  )}

                                  {/* Fallback: Original Scoring Breakdown (for backward compatibility) */}
                                  {(!match.all_criteria_matches || match.all_criteria_matches.length === 0) && (
                                    <div className="space-y-2">
                                      <div className="flex items-center justify-between">
                                        <span className="font-semibold text-sm">Scoring Breakdown</span>
                                        <Badge variant="outline" className="text-xs">
                                          Method: {match.scoring_method || 'weighted'}
                                        </Badge>
                                      </div>
                                      
                                      {match.breakdown?.map((b: any, idx: number) => (
                                        <div key={idx} className="bg-white border rounded p-2">
                                          <div className="flex items-center justify-between mb-1">
                                            <span className="font-medium text-sm capitalize">{b.field.replace('_', ' ')}</span>
                                            <div className="flex items-center gap-2">
                                              <span className="text-sm font-semibold">{b.score}%</span>
                                              {b.weight && (
                                                <Badge variant="outline" className="text-xs">
                                                  Weight: {(b.weight * 100).toFixed(1)}%
                                                </Badge>
                                              )}
                                            </div>
                                          </div>
                                          <div className="w-full bg-gray-200 rounded-full h-2">
                                            <div
                                              className={`h-2 rounded-full ${
                                                b.score >= 80 ? 'bg-green-500' :
                                                b.score >= 60 ? 'bg-blue-500' :
                                                b.score >= 40 ? 'bg-yellow-500' :
                                                b.score > 0 ? 'bg-orange-500' : 'bg-gray-300'
                                              }`}
                                              style={{ width: `${b.score}%` }}
                                            ></div>
                                          </div>
                                          <div className="text-xs text-gray-600 mt-1">{b.reason}</div>
                                        </div>
                                      ))}
                                    </div>
                                  )}

                                  {/* All Reasons Summary */}
                                  {match.all_reasons && match.all_reasons.length > 0 && (
                                    <div className="bg-blue-50 border border-blue-200 rounded p-2">
                                      <div className="font-semibold text-sm text-blue-800 mb-1">All Key Matches:</div>
                                      <ul className="text-xs text-blue-700 space-y-0.5">
                                        {match.all_reasons.map((reason: string, idx: number) => (
                                          <li key={idx} className="flex items-center gap-1">
                                            <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                                            {reason}
                                          </li>
                                        ))}
                                      </ul>
                                    </div>
                                  )}

                                  {/* Weight Information */}
                                  {match.weightSum && (
                                    <div className="text-xs text-gray-500 pt-2 border-t">
                                      Primary criteria weight: {(match.weightSum * 100).toFixed(1)}%
                                      {match.weightSum < 0.99 && " (some criteria not applicable)"}
                                    </div>
                                  )}
                                </div>
                              )}
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Investment Criteria Tab */}
          <TabsContent value="investment-criteria">
            <div className="space-y-6">
              {deal.investment_criteria &&
              deal.investment_criteria.length > 0 ? (
                deal.investment_criteria.map((criteria, index) => (
                  <Card key={criteria.criteria_id || index}>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Target className="h-5 w-5" />
                          Investment Criteria #{index + 1}
                        </div>
                        {criteria.quality !== undefined && (
                          <Badge 
                            variant="outline" 
                            className={`ml-2 ${
                              criteria.quality >= 80 ? 'border-green-200 text-green-700 bg-green-50' :
                              criteria.quality >= 60 ? 'border-yellow-200 text-yellow-700 bg-yellow-50' :
                              'border-red-200 text-red-700 bg-red-50'
                            }`}
                          >
                            {criteria.quality}% Complete
                          </Badge>
                        )}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {/* Capital Positions - PROMINENT FIRST SECTION */}
                      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200 mb-6">
                        <h4 className="font-bold text-blue-900 mb-3 flex items-center gap-2">
                          <Target className="h-5 w-5" />
                          Capital Positions
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {criteria.capital_position?.map((position: string, index: number) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-3 py-2 rounded-lg text-sm font-semibold bg-white text-blue-900 border border-blue-300 shadow-sm"
                            >
                              {position}
                            </span>
                          )) || (
                            <span className="text-blue-700 text-sm font-medium">No capital positions specified</span>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {/* Property & Strategy */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">
                            Property & Strategy
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="font-medium">
                                Property Types:
                              </span>
                              <p>{formatArrayField(criteria.property_types)}</p>
                            </div>
                            <div>
                              <span className="font-medium">Strategies:</span>
                              <p>{formatArrayField(criteria.strategies)}</p>
                            </div>
                            <div>
                              <span className="font-medium">
                                Asset Classes:
                              </span>
                              <p>{formatArrayField(criteria.asset_classes)}</p>
                            </div>
                          </div>
                        </div>

                        {/* Financial */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">
                            Financial
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="font-medium">Deal Size:</span>
                              <p>
                                {criteria.minimum_deal_size &&
                                criteria.maximum_deal_size
                                  ? `${formatCurrency(
                                      criteria.minimum_deal_size
                                    )} - ${formatCurrency(
                                      criteria.maximum_deal_size
                                    )}`
                                  : formatCurrency(
                                      criteria.minimum_deal_size ||
                                        criteria.maximum_deal_size
                                    )}
                              </p>
                            </div>
                            <div>
                              <span className="font-medium">
                                Target Return:
                              </span>
                              <p>{formatPercentage(criteria.target_return)}</p>
                            </div>
                            <div>
                              <span className="font-medium">
                                Financial Products:
                              </span>
                              <p>
                                {formatArrayField(criteria.financial_products)}
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Geographic */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">
                            Geographic
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="font-medium">Country:</span>
                              <p>{formatArrayField(criteria.country)}</p>
                            </div>
                            <div>
                              <span className="font-medium">Region:</span>
                              <p>{formatArrayField(criteria.region)}</p>
                            </div>
                            <div>
                              <span className="font-medium">State:</span>
                              <p>{formatArrayField(criteria.state)}</p>
                            </div>
                            <div>
                              <span className="font-medium">City:</span>
                              <p>{formatArrayField(criteria.city)}</p>
                            </div>
                          </div>
                        </div>

                        {/* Loan Details */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">
                            Loan Details
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="font-medium">Loan Types:</span>
                              <p>{formatArrayField(criteria.loan_type)}</p>
                            </div>
                            <div>
                              <span className="font-medium">LTV:</span>
                              <p>
                                {/* Show range if both min and max are available as direct database columns */}
                                {criteria.loan_to_value_min !== null &&
                                criteria.loan_to_value_min !== undefined &&
                                criteria.loan_to_value_max !== null &&
                                criteria.loan_to_value_max !== undefined
                                  ? `${formatPercentage(
                                      criteria.loan_to_value_min
                                    )} - ${formatPercentage(
                                      criteria.loan_to_value_max
                                    )}`
                                  : /* Show single value from individual columns or legacy column */
                                  criteria.loan_to_value_min || criteria.loan_to_value_max
                                    ? formatPercentage(criteria.loan_to_value_min || criteria.loan_to_value_max)
                                    : formatPercentage(criteria.loan_ltv)}
                              </p>
                            </div>
                            <div>
                              <span className="font-medium">LTC:</span>
                              <p>
                                {/* Show range if both min and max are available as direct database columns */}
                                {criteria.loan_to_cost_min !== null &&
                                criteria.loan_to_cost_min !== undefined &&
                                criteria.loan_to_cost_max !== null &&
                                criteria.loan_to_cost_max !== undefined
                                  ? `${formatPercentage(
                                      criteria.loan_to_cost_min
                                    )} - ${formatPercentage(
                                      criteria.loan_to_cost_max
                                    )}`
                                  : /* Show single value from legacy column */
                                  criteria.loan_to_cost_min || criteria.loan_to_cost_max
                                    ? formatPercentage(criteria.loan_to_cost_min || criteria.loan_to_cost_max)
                                    : formatPercentage(criteria.loan_ltc)}
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Terms */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">
                            Terms
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="font-medium">Hold Period:</span>
                              <p>
                                {criteria.min_hold_period &&
                                criteria.max_hold_period
                                  ? `${criteria.min_hold_period} - ${criteria.max_hold_period} months`
                                  : `${
                                      criteria.min_hold_period ||
                                      criteria.max_hold_period ||
                                      "N/A"
                                    } months`}
                              </p>
                            </div>
                            <div>
                              <span className="font-medium">Loan Term:</span>
                              <p>
                                {criteria.min_loan_term &&
                                criteria.max_loan_term
                                  ? `${criteria.min_loan_term} - ${criteria.max_loan_term} months`
                                  : `${
                                      criteria.min_loan_term ||
                                      criteria.max_loan_term ||
                                      "N/A"
                                    } months`}
                              </p>
                            </div>
                            <div>
                              <span className="font-medium">
                                Interest Rate:
                              </span>
                              <div className="space-y-1">
                                {criteria.loan_interest_rate_sofr && (
                                  <p className="text-sm">
                                    <span className="font-medium text-emerald-600">SOFR:</span> {formatPercentage(criteria.loan_interest_rate_sofr)}
                                  </p>
                                )}
                                {criteria.loan_interest_rate_prime && (
                                  <p className="text-sm">
                                    <span className="font-medium text-blue-600">Prime:</span> {formatPercentage(criteria.loan_interest_rate_prime)}
                                  </p>
                                )}
                                {criteria.loan_interest_rate_wsj && (
                                  <p className="text-sm">
                                    <span className="font-medium text-purple-600">WSJ:</span> {formatPercentage(criteria.loan_interest_rate_wsj)}
                                  </p>
                                )}
                                {criteria.loan_interest_rate && (
                                  <p>
                                    <span className="font-medium">Base:</span> {formatPercentage(criteria.loan_interest_rate)}
                                  </p>
                                )}
                                {!criteria.loan_interest_rate_sofr && !criteria.loan_interest_rate_prime && !criteria.loan_interest_rate_wsj && !criteria.loan_interest_rate && (
                                  <p>N/A</p>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Additional Terms & Fees - Only show if there are meaningful values */}
                        {(criteria.extra_fields?.closing_time_weeks ||
                          criteria.loan_origination_fee ||
                          criteria.loan_exit_fee ||
                          criteria.min_loan_dscr ||
                          criteria.max_loan_dscr ||
                          criteria.extra_fields?.loan_type_normalized) && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-2">
                              Additional Terms
                            </h4>
                            <div className="space-y-2 text-sm">
                              {criteria.extra_fields?.closing_time_weeks && (
                                <div>
                                  <span className="font-medium">
                                    Closing Time:
                                  </span>
                                  <p>
                                    {formatNumber(
                                      criteria.extra_fields.closing_time_weeks
                                    )}{" "}
                                    weeks
                                  </p>
                                </div>
                              )}
                              {(criteria.loan_origination_fee_min || criteria.loan_origination_fee_max || criteria.loan_origination_fee) && (
                                <div>
                                  <span className="font-medium">
                                    Origination Fee:
                                  </span>
                                  <p>
                                    {/* Show range if both min and max are available */}
                                    {criteria.loan_origination_fee_min !== null &&
                                    criteria.loan_origination_fee_min !== undefined &&
                                    criteria.loan_origination_fee_max !== null &&
                                    criteria.loan_origination_fee_max !== undefined
                                      ? `${formatPercentage(
                                          criteria.loan_origination_fee_min
                                        )} - ${formatPercentage(
                                          criteria.loan_origination_fee_max
                                        )}`
                                      : /* Show single value */
                                      formatPercentage(
                                        criteria.loan_origination_fee_min || 
                                        criteria.loan_origination_fee_max ||
                                        criteria.loan_origination_fee
                                      )}
                                  </p>
                                </div>
                              )}
                              {(criteria.loan_exit_fee_min || criteria.loan_exit_fee_max || criteria.loan_exit_fee) && (
                                <div>
                                  <span className="font-medium">Exit Fee:</span>
                                  <p>
                                    {/* Show range if both min and max are available */}
                                    {criteria.loan_exit_fee_min !== null &&
                                    criteria.loan_exit_fee_min !== undefined &&
                                    criteria.loan_exit_fee_max !== null &&
                                    criteria.loan_exit_fee_max !== undefined
                                      ? `${formatPercentage(
                                          criteria.loan_exit_fee_min
                                        )} - ${formatPercentage(
                                          criteria.loan_exit_fee_max
                                        )}`
                                      : /* Show single value */
                                      formatPercentage(
                                        criteria.loan_exit_fee_min || 
                                        criteria.loan_exit_fee_max ||
                                        criteria.loan_exit_fee
                                      )}
                                  </p>
                                </div>
                              )}
                              {(criteria.min_loan_dscr ||
                                criteria.max_loan_dscr) && (
                                <div>
                                  <span className="font-medium">DSCR:</span>
                                  <p>
                                    {criteria.min_loan_dscr &&
                                    criteria.max_loan_dscr
                                      ? `${formatNumber(
                                          criteria.min_loan_dscr
                                        )} - ${formatNumber(
                                          criteria.max_loan_dscr
                                        )}`
                                      : formatNumber(
                                          criteria.min_loan_dscr ||
                                            criteria.max_loan_dscr
                                        )}
                                  </p>
                                </div>
                              )}
                              {criteria.extra_fields?.loan_type_normalized && (
                                <div>
                                  <span className="font-medium">
                                    Loan Type:
                                  </span>
                                  <p>
                                    {formatArrayField(
                                      criteria.extra_fields.loan_type_normalized
                                    )}
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                      
                      {/* Quality Details */}
                      {criteria.quality !== undefined && criteria.missingFields && criteria.missingFields.length > 0 && (
                        <div className="mt-4 pt-4 border-t border-gray-100">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-600">Missing Fields:</span>
                            <span className="text-xs text-gray-500">{criteria.completedFields}/{criteria.totalFields} completed</span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {criteria.missingFields.slice(0, 5).map((field: string, fieldIndex: number) => (
                              <Badge key={fieldIndex} variant="secondary" className="text-xs">
                                {field.replace(/_/g, ' ')}
                              </Badge>
                            ))}
                            {criteria.missingFields.length > 5 && (
                              <Badge variant="secondary" className="text-xs">
                                +{criteria.missingFields.length - 5} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card>
                  <CardContent className="text-center py-8">
                    <p className="text-gray-500">
                      No investment criteria available for this deal.
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* Contact Tab */}
          <TabsContent value="contact">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                {deal.contact_id ? (
                  contactLoading ? (
                    <div className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                    </div>
                  ) : contact ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Name
                          </label>
                          <p>
                            {[contact.first_name, contact.last_name]
                              .filter(Boolean)
                              .join(" ") || "N/A"}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Email
                          </label>
                          <p>{contact.email || "N/A"}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Company
                          </label>
                          <p>{contact.company_name || "N/A"}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Job Title
                          </label>
                          <p>{contact.title || "N/A"}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Phone
                          </label>
                          <p>{contact.phone_number || "N/A"}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            LinkedIn
                          </label>
                          <p>
                            {contact.person_linkedin ? (
                              <a
                                href={contact.person_linkedin}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:underline"
                              >
                                View Profile
                              </a>
                            ) : (
                              "N/A"
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <p className="text-gray-500">
                      Failed to load contact information.
                    </p>
                  )
                ) : (
                  <p className="text-gray-500">
                    No contact associated with this deal.
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Jobs Tab */}
          <TabsContent value="jobs">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Processing Jobs
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    {jobsRefreshInterval && (
                      <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-700 border-blue-200">
                        Auto-refreshing
                      </Badge>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={fetchJobs}
                      disabled={jobsLoading}
                      className="flex items-center gap-2"
                    >
                      <div className={`h-4 w-4 ${jobsLoading ? 'animate-spin' : ''}`}>
                        {jobsLoading ? (
                          <div className="rounded-full border-2 border-gray-300 border-t-blue-600"></div>
                        ) : (
                          <div className="rounded-full border-2 border-gray-300"></div>
                        )}
                      </div>
                      Refresh
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {jobsLoading ? (
                  <div className="animate-pulse space-y-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  </div>
                ) : jobs.length > 0 ? (
                  <div className="space-y-4">
                    {jobs.map((job) => (
                      <div key={job.job_id} className="border rounded-lg p-4 bg-gray-50">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Badge 
                              variant={job.status === 'completed' ? 'default' : 
                                      job.status === 'failed' ? 'destructive' : 
                                      job.status === 'processing' ? 'secondary' : 'outline'}
                              className={job.status === 'completed' ? 'bg-green-100 text-green-800 border-green-200' :
                                       job.status === 'failed' ? 'bg-red-100 text-red-800 border-red-200' :
                                       job.status === 'processing' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                                       'bg-gray-100 text-gray-800 border-gray-200'}
                            >
                              {job.status}
                            </Badge>
                            <span className="text-sm font-medium text-gray-600">
                              {job.job_type === 'requirement_extraction' ? 'Requirement Extraction' : job.job_type}
                            </span>
                          </div>
                          <span className="text-xs text-gray-500">
                            {new Date(job.created_at).toLocaleString()}
                          </span>
                        </div>
                        
                        {job.metadata && (
                          <div className="text-sm text-gray-600 mb-2">
                            {job.metadata.description || job.metadata.file_count ? 
                              `${job.metadata.description || 'Processing'} - ${job.metadata.file_count || 0} files` : 
                              'Processing job'
                            }
                          </div>
                        )}

                        {job.status === 'processing' && (
                          <div className="flex items-center gap-2 text-sm text-blue-600">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                            Processing...
                          </div>
                        )}

                        {job.status === 'completed' && job.result && (
                          <div className="text-sm text-green-600">
                            ✓ {job.result.message || 'Job completed successfully'}
                          </div>
                        )}

                        {job.status === 'failed' && job.error_message && (
                          <div className="text-sm text-red-600">
                            ✗ {job.error_message}
                          </div>
                        )}

                        {job.completed_at && (
                          <div className="text-xs text-gray-500 mt-2">
                            Completed: {new Date(job.completed_at).toLocaleString()}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No processing jobs found for this deal.</p>
                    <p className="text-sm mt-2">
                      Jobs will appear here when you run requirement extraction or other processing tasks.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* LLM Output Dialog */}
        <Dialog open={showLLMOutput} onOpenChange={setShowLLMOutput}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Raw LLM Output</DialogTitle>
              <DialogDescription>
                Raw Gemini LLM response and processing details for Deal{" "}
                {deal?.deal_id}
              </DialogDescription>
            </DialogHeader>

            {llmOutputData && (
              <div className="space-y-4">
                {/* File Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">File Information</CardTitle>
                  </CardHeader>
                  <CardContent className="text-sm">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <strong>File Name:</strong>{" "}
                        {llmOutputData.file.original_name}
                      </div>
                      <div>
                        <strong>Upload Date:</strong>{" "}
                        {new Date(
                          llmOutputData.file.uploaded_at
                        ).toLocaleString()}
                      </div>
                      <div>
                        <strong>File Size:</strong>{" "}
                        {llmOutputData.file.file_size_bytes} bytes
                      </div>
                      <div>
                        <strong>Type:</strong>{" "}
                        {llmOutputData.file.relationship_type}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Metadata */}
                {llmOutputData.metadata?.metadata && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">
                        Processing Metadata
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <pre className="text-xs bg-gray-50 p-3 rounded overflow-x-auto">
                        {JSON.stringify(
                          llmOutputData.metadata.metadata,
                          null,
                          2
                        )}
                      </pre>
                    </CardContent>
                  </Card>
                )}

                {/* Note about file content */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">File Content</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-blue-50 border border-blue-200 rounded p-3">
                      <p className="text-sm text-blue-700">
                        <strong>Note:</strong> The actual JSON file content
                        containing the raw LLM output is stored in the file
                        system. This dialog shows the file metadata. To view the
                        complete raw LLM output, download the file directly.
                      </p>
                      <div className="mt-2">
                        <Button
                          size="sm"
                          onClick={() => {
                            // In a production system, this would download the actual file
                            alert("File download would be implemented here");
                          }}
                        >
                          Download JSON File
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default DealDetail;
