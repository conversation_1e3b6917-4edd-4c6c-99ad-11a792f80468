import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { CheckCircle, AlertCircle, XCircle, TrendingUp } from 'lucide-react';

interface DealQualityIndicatorProps {
  dealQuality?: number;
  criteriaQuality?: number;
  overallQuality?: number;
  size?: 'sm' | 'md' | 'lg';
  showTooltip?: boolean;
  className?: string;
}

const DealQualityIndicator: React.FC<DealQualityIndicatorProps> = ({
  dealQuality = 0,
  criteriaQuality = 0,
  overallQuality,
  size = 'md',
  showTooltip = true,
  className = ''
}) => {
  const quality = overallQuality || Math.round((dealQuality + criteriaQuality) / 2);

  const getQualityColor = (score: number) => {
    if (score >= 90) return 'bg-emerald-100 text-emerald-800 border-emerald-200';
    if (score >= 80) return 'bg-blue-100 text-blue-800 border-blue-200';
    if (score >= 70) return 'bg-amber-100 text-amber-800 border-amber-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  const getQualityIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="h-3 w-3" />;
    if (score >= 80) return <TrendingUp className="h-3 w-3" />;
    if (score >= 70) return <AlertCircle className="h-3 w-3" />;
    return <XCircle className="h-3 w-3" />;
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'text-xs px-2 py-1';
      case 'lg':
        return 'text-sm px-3 py-1.5';
      default:
        return 'text-sm px-2.5 py-1';
    }
  };

  const badge = (
    <Badge 
      variant="outline" 
      className={`${getQualityColor(quality)} ${getSizeClasses()} ${className}`}
    >
      {getQualityIcon(quality)}
      <span className="ml-1">{quality}%</span>
    </Badge>
  );

  if (!showTooltip) {
    return badge;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {badge}
        </TooltipTrigger>
        <TooltipContent>
          <div className="space-y-1">
            <div className="font-medium">Data Quality: {quality}%</div>
            <div className="text-xs text-gray-500">
              Deal: {dealQuality}% • Criteria: {criteriaQuality}%
            </div>
            {quality < 70 && (
              <div className="text-xs text-amber-600">
                ⚠️ Needs attention
              </div>
            )}
            {quality >= 90 && (
              <div className="text-xs text-emerald-600">
                ✅ Excellent quality
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default DealQualityIndicator; 