import React, { useState } from 'react';
import { AlertTriangle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

interface ConflictAlternative {
  value: any;
  file: string;
  context: string;
}

interface ConflictData {
  chosen_value: any;
  chosen_file: string;
  reason: string;
  alternatives: ConflictAlternative[];
}

interface ConflictIndicatorProps {
  fieldName: string;
  conflictData: ConflictData;
  currentValue: any;
  onValueChange?: (newValue: any) => void;
  className?: string;
}

export const ConflictIndicator: React.FC<ConflictIndicatorProps> = ({
  fieldName,
  conflictData,
  currentValue,
  onValueChange,
  className = ""
}) => {
  const formatValue = (value: any): string => {
    if (typeof value === 'number') {
      return value.toLocaleString();
    }
    if (typeof value === 'string') {
      return value;
    }
    return JSON.stringify(value);
  };

  const handleAlternativeSelect = (alternative: ConflictAlternative) => {
    if (onValueChange) {
      onValueChange(alternative.value);
    }
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="flex items-center gap-1">
        <AlertTriangle className="h-4 w-4 text-orange-500" />
        <Badge variant="outline" className="text-xs text-orange-600 border-orange-300">
          Conflict Detected
        </Badge>
      </div>
      
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="ghost" size="sm" className="h-6 px-2">
            <span className="text-xs">View Alternatives</span>
          </Button>
        </PopoverTrigger>
        
        <PopoverContent className="w-96 p-0" align="start">
          <Card className="border-orange-200 bg-orange-50 border-0 shadow-none">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-orange-800">
                Conflict Resolution for {fieldName}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {/* Chosen Value */}
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-orange-700">Chosen Value:</span>
                  <Badge variant="secondary" className="text-xs">
                    {conflictData.chosen_file}
                  </Badge>
                </div>
                <div className="text-sm font-semibold text-green-700">
                  {formatValue(conflictData.chosen_value)}
                </div>
                <p className="text-xs text-orange-600">
                  {conflictData.reason}
                </p>
              </div>

              {/* Alternatives */}
              <div className="space-y-2">
                <span className="text-xs font-medium text-orange-700">Alternative Values:</span>
                {conflictData.alternatives.map((alternative, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-white rounded border border-orange-200">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">
                          {formatValue(alternative.value)}
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {alternative.file}
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-600">
                        {alternative.context}
                      </p>
                    </div>
                    {onValueChange && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleAlternativeSelect(alternative)}
                        className="ml-2 h-6 px-2 text-xs"
                      >
                        Use This
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </PopoverContent>
      </Popover>
    </div>
  );
};
