"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { PlusCircle, RefreshCw, Play, Database, CheckSquare, Settings, Zap, Sparkles, Settings2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import CompanyCard from "./filters/list-components/CompanyCard"
import Pagination from "../people/list-components/Pagination";
import PaginationSizeSelector from "../people/list-components/PaginationSizeSelector";
import CompanyUnifiedFiltersV2Component from "./CompanyUnifiedFiltersV2";
import { Company } from "./shared/types";
import { useRouter, useSearchParams } from "next/navigation";
import type { CompanyUnifiedFiltersV2 as CompanyUnifiedFiltersV2Type } from "../../../types/unified-filters-v2";

type CompanyProcessingStage = 'website_scraping' | 'company_overview_v2' | 'company_investment_criteria';

interface ProcessingJob {
  stage: CompanyProcessingStage;
  isExecuting: boolean;
}

interface UnifiedCompanyData extends Company {
  // Investment criteria data
  criteria_id?: number
  entity_type?: string
  entity_name?: string
  capital_position?: string[]
  loan_types?: string[]
  minimum_deal_size?: number
  maximum_deal_size?: number
  target_return?: number
  historical_irr?: number
  historical_em?: number
  property_types?: string[]
  strategies?: string[]
  regions?: string[]
  states?: string[]
  
  // Additional company extracted data not in shared types
  totaltransactions?: string
  totalsquarefeet?: string
  totalunits?: string
  historicalreturns?: string
  portfoliovalue?: string
  companyindustry?: string
  capitalposition?: string
  
  // Investment strategy fields
  investment_strategy?: string[]
  investmentstrategy?: string[]
}

export default function CompaniesView() {
  const [companies, setCompanies] = useState<UnifiedCompanyData[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCompanies, setTotalCompanies] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [mappings, setMappings] = useState<any>(null);
  
  // V2 filters state (V2 only - no legacy support)
  const [filters, setFilters] = useState<CompanyUnifiedFiltersV2Type>({
    page: 1,
    limit: 25,
    sortBy: 'updated_at',
    sortOrder: 'desc'
  });

  // Processing state
  const [selectedCompanies, setSelectedCompanies] = useState<Set<number>>(new Set());
  const [selectAll, setSelectAll] = useState(false);
  const [processingJobs, setProcessingJobs] = useState<ProcessingJob[]>([
    { stage: 'website_scraping', isExecuting: false },
    { stage: 'company_overview_v2', isExecuting: false },
    { stage: 'company_investment_criteria', isExecuting: false }
  ]);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();

  // Initialize V2 filters from URL parameters on component mount
  useEffect(() => {
    if (!searchParams) return;
    
    const urlFilters: CompanyUnifiedFiltersV2Type = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '25'),
      sortBy: searchParams.get('sortBy') || 'updated_at',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      searchTerm: searchParams.get('searchTerm') || undefined,
      
      // Company basic filters
      companyAddress: searchParams.get('companyAddress')?.split(',').filter(Boolean),
      companyCity: searchParams.get('companyCity')?.split(',').filter(Boolean),
      companyState: searchParams.get('companyState')?.split(',').filter(Boolean),
      companyCountry: searchParams.get('companyCountry')?.split(',').filter(Boolean),
      industry: searchParams.get('industry')?.split(',').filter(Boolean),
      source: searchParams.get('source')?.split(',').filter(Boolean),
      
      // Company V2 Overview filters
      companyType: searchParams.get('companyType')?.split(',').filter(Boolean),
      foundedYearMin: searchParams.get('foundedYearMin') ? parseInt(searchParams.get('foundedYearMin')!) : undefined,
      foundedYearMax: searchParams.get('foundedYearMax') ? parseInt(searchParams.get('foundedYearMax')!) : undefined,
      investmentFocus: searchParams.get('investmentFocus')?.split(',').filter(Boolean),
      
      // Financial metrics
      fundSizeMin: searchParams.get('fundSizeMin') ? parseFloat(searchParams.get('fundSizeMin')!) : undefined,
      fundSizeMax: searchParams.get('fundSizeMax') ? parseFloat(searchParams.get('fundSizeMax')!) : undefined,
      aumMin: searchParams.get('aumMin') ? parseFloat(searchParams.get('aumMin')!) : undefined,
      aumMax: searchParams.get('aumMax') ? parseFloat(searchParams.get('aumMax')!) : undefined,
      numberOfPropertiesMin: searchParams.get('numberOfPropertiesMin') ? parseInt(searchParams.get('numberOfPropertiesMin')!) : undefined,
      numberOfPropertiesMax: searchParams.get('numberOfPropertiesMax') ? parseInt(searchParams.get('numberOfPropertiesMax')!) : undefined,
      numberOfEmployeesMin: searchParams.get('numberOfEmployeesMin') ? parseInt(searchParams.get('numberOfEmployeesMin')!) : undefined,
      numberOfEmployeesMax: searchParams.get('numberOfEmployeesMax') ? parseInt(searchParams.get('numberOfEmployeesMax')!) : undefined,
      
      // Processing status filters
      websiteScrapingStatus: searchParams.get('websiteScrapingStatus')?.split(',').filter(Boolean),
      companyOverviewStatus: searchParams.get('companyOverviewStatus')?.split(',').filter(Boolean),
      overviewV2Status: searchParams.get('overviewV2Status')?.split(',').filter(Boolean),
      investmentCriteriaStatus: searchParams.get('investmentCriteriaStatus')?.split(',').filter(Boolean),
      
      // Investment criteria filters
      capitalPosition: searchParams.get('capitalPosition')?.split(',').filter(Boolean),
      propertyTypes: searchParams.get('propertyTypes')?.split(',').filter(Boolean),
      strategies: searchParams.get('strategies')?.split(',').filter(Boolean),
      regions: searchParams.get('regions')?.split(',').filter(Boolean),
      states: searchParams.get('states')?.split(',').filter(Boolean),
      cities: searchParams.get('cities')?.split(',').filter(Boolean),
      countries: searchParams.get('countries')?.split(',').filter(Boolean),
      dealSizeMin: searchParams.get('dealSizeMin') ? parseFloat(searchParams.get('dealSizeMin')!) : undefined,
      dealSizeMax: searchParams.get('dealSizeMax') ? parseFloat(searchParams.get('dealSizeMax')!) : undefined,
      
      // NOT filters
      notSource: searchParams.get('notSource')?.split(',').filter(Boolean),
      notIndustry: searchParams.get('notIndustry')?.split(',').filter(Boolean),
      notCompanyType: searchParams.get('notCompanyType')?.split(',').filter(Boolean),
      notWebsiteScrapingStatus: searchParams.get('notWebsiteScrapingStatus')?.split(',').filter(Boolean),
      notCompanyOverviewStatus: searchParams.get('notCompanyOverviewStatus')?.split(',').filter(Boolean),
      notOverviewV2Status: searchParams.get('notOverviewV2Status')?.split(',').filter(Boolean),
      notInvestmentCriteriaStatus: searchParams.get('notInvestmentCriteriaStatus')?.split(',').filter(Boolean),
    };
    
    console.log('CompaniesView: Initializing V2 filters from URL:', urlFilters);
    setFilters(urlFilters);
    // Fetch companies immediately with the V2 filters
    fetchCompanies(urlFilters);
  }, [searchParams]);

  // Load companies with V2 filters
  const fetchCompanies = async (currentFilters: CompanyUnifiedFiltersV2Type) => {
    console.log('CompaniesView: Starting to fetch companies with filters:', currentFilters);
    setLoading(true);
    try {
      // Build query string with all filters
      const params = new URLSearchParams();
      
      // Add all filter parameters
      Object.entries(currentFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value) && value.length > 0) {
            params.append(key, value.join(','));
          } else if (typeof value === 'boolean') {
            params.append(key, value.toString());
          } else if (typeof value === 'number' || typeof value === 'string') {
            params.append(key, value.toString());
          }
        }
      });

      const apiUrl = `/api/companies/unified-filters-v2?${params.toString()}`;
      console.log('CompaniesView: Making API call to:', apiUrl);
      
      const response = await fetch(apiUrl);
      if (response.ok) {
        const data = await response.json();
        console.log('CompaniesView: API response received:', { 
          companyCount: data.data?.length || 0, 
          total: data.pagination?.total || 0 
        });
        setCompanies(data.data || []);
        setTotalCompanies(data.pagination?.total || 0);
        setTotalPages(data.pagination?.totalPages || 0);
      } else {
        console.error("Failed to fetch companies:", await response.text());
        setCompanies([]);
        setTotalCompanies(0);
        setTotalPages(0);
      }
    } catch (error) {
      console.error("Error fetching companies:", error);
      setCompanies([]);
      setTotalCompanies(0);
      setTotalPages(0);
    } finally {
      setLoading(false);
    }
  };

  // Load mappings from investment criteria mappings API
  useEffect(() => {
    async function fetchMappings() {
      try {
        const response = await fetch('/api/investment-criteria/mappings');
        if (response.ok) {
          const data = await response.json();
          setMappings(data || {});
        } else {
          console.error("Failed to fetch mappings:", await response.text());
        }
      } catch (error) {
        console.error("Error fetching mappings:", error);
      }
    }

    fetchMappings();
  }, []);

  // Initialize V2 filters from URL parameters
  useEffect(() => {
    if (!searchParams) return;
    
    const urlFilters: CompanyUnifiedFiltersV2Type = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '25'),
      sortBy: searchParams.get('sortBy') || 'updated_at',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      searchTerm: searchParams.get('searchTerm') || undefined,
      
      // V2 Company data fields
      companyType: searchParams.get('companyType')?.split(',').filter(Boolean),
      industry: searchParams.get('industry')?.split(',').filter(Boolean),
      companyAddress: searchParams.get('companyAddress')?.split(',').filter(Boolean),
      companyCity: searchParams.get('companyCity')?.split(',').filter(Boolean),
      companyState: searchParams.get('companyState')?.split(',').filter(Boolean),
      
      // V2 Company Overview extracted metrics
      investmentFocus: searchParams.get('investmentFocus')?.split(',').filter(Boolean),
      officeLocations: searchParams.get('officeLocations')?.split(',').filter(Boolean),
      partnerships: searchParams.get('partnerships')?.split(',').filter(Boolean),
      closingTimeMin: searchParams.get('closingTimeMin') ? parseInt(searchParams.get('closingTimeMin')!) : undefined,
      closingTimeMax: searchParams.get('closingTimeMax') ? parseInt(searchParams.get('closingTimeMax')!) : undefined,
      
      // V2 Company Financial Range Fields
      fundSizeMin: searchParams.get('fundSizeMin') ? parseFloat(searchParams.get('fundSizeMin')!) : undefined,
      fundSizeMax: searchParams.get('fundSizeMax') ? parseFloat(searchParams.get('fundSizeMax')!) : undefined,
      aumMin: searchParams.get('aumMin') ? parseFloat(searchParams.get('aumMin')!) : undefined,
      aumMax: searchParams.get('aumMax') ? parseFloat(searchParams.get('aumMax')!) : undefined,
      foundedYearMin: searchParams.get('foundedYearMin') ? parseInt(searchParams.get('foundedYearMin')!) : undefined,
      foundedYearMax: searchParams.get('foundedYearMax') ? parseInt(searchParams.get('foundedYearMax')!) : undefined,
      numberOfPropertiesMin: searchParams.get('numberOfPropertiesMin') ? parseInt(searchParams.get('numberOfPropertiesMin')!) : undefined,
      numberOfPropertiesMax: searchParams.get('numberOfPropertiesMax') ? parseInt(searchParams.get('numberOfPropertiesMax')!) : undefined,
      numberOfOfficesMin: searchParams.get('numberOfOfficesMin') ? parseInt(searchParams.get('numberOfOfficesMin')!) : undefined,
      numberOfOfficesMax: searchParams.get('numberOfOfficesMax') ? parseInt(searchParams.get('numberOfOfficesMax')!) : undefined,
      numberOfEmployeesMin: searchParams.get('numberOfEmployeesMin') ? parseInt(searchParams.get('numberOfEmployeesMin')!) : undefined,
      numberOfEmployeesMax: searchParams.get('numberOfEmployeesMax') ? parseInt(searchParams.get('numberOfEmployeesMax')!) : undefined,

      // V2 Investment criteria filters
      capitalPosition: searchParams.get('capitalPosition')?.split(',').filter(Boolean),
      propertyTypes: searchParams.get('propertyTypes')?.split(',').filter(Boolean),
      propertySubcategories: searchParams.get('propertySubcategories')?.split(',').filter(Boolean),
      strategies: searchParams.get('strategies')?.split(',').filter(Boolean),
      loanTypes: searchParams.get('loanTypes')?.split(',').filter(Boolean),
      loanProgram: searchParams.get('loanProgram')?.split(',').filter(Boolean),
      structuredLoanTranche: searchParams.get('structuredLoanTranche')?.split(',').filter(Boolean),
      recourseLoan: searchParams.get('recourseLoan')?.split(',').filter(Boolean),
      
      // V2 Deal size and return filters
      dealSizeMin: searchParams.get('dealSizeMin') ? parseFloat(searchParams.get('dealSizeMin')!) : undefined,
      dealSizeMax: searchParams.get('dealSizeMax') ? parseFloat(searchParams.get('dealSizeMax')!) : undefined,
      targetReturnMin: searchParams.get('targetReturnMin') ? parseFloat(searchParams.get('targetReturnMin')!) : undefined,
      targetReturnMax: searchParams.get('targetReturnMax') ? parseFloat(searchParams.get('targetReturnMax')!) : undefined,
      
      // V2 Geographic filters
      regions: searchParams.get('regions')?.split(',').filter(Boolean),
      states: searchParams.get('states')?.split(',').filter(Boolean),
      cities: searchParams.get('cities')?.split(',').filter(Boolean),
      companyCountry: searchParams.get('companyCountry')?.split(',').filter(Boolean),
      
      // V2 Processing status filters
      websiteScrapingStatus: searchParams.get('websiteScrapingStatus')?.split(',').filter(Boolean),
      companyOverviewStatus: searchParams.get('companyOverviewStatus')?.split(',').filter(Boolean),
      overviewV2Status: searchParams.get('overviewV2Status')?.split(',').filter(Boolean),
      investmentCriteriaStatus: searchParams.get('investmentCriteriaStatus')?.split(',').filter(Boolean),
      source: searchParams.get('source')?.split(',').filter(Boolean),
      
      // V2 NOT filters
      notCompanyType: searchParams.get('notCompanyType')?.split(',').filter(Boolean),
      notIndustry: searchParams.get('notIndustry')?.split(',').filter(Boolean),
      notCapitalPosition: searchParams.get('notCapitalPosition')?.split(',').filter(Boolean),
      notPropertyTypes: searchParams.get('notPropertyTypes')?.split(',').filter(Boolean),
      notStrategies: searchParams.get('notStrategies')?.split(',').filter(Boolean),
      notInvestmentFocus: searchParams.get('notInvestmentFocus')?.split(',').filter(Boolean),
      notPartnerships: searchParams.get('notPartnerships')?.split(',').filter(Boolean),
      notSource: searchParams.get('notSource')?.split(',').filter(Boolean),
      notWebsiteScrapingStatus: searchParams.get('notWebsiteScrapingStatus')?.split(',').filter(Boolean),
      notCompanyOverviewStatus: searchParams.get('notCompanyOverviewStatus')?.split(',').filter(Boolean),
      notOverviewV2Status: searchParams.get('notOverviewV2Status')?.split(',').filter(Boolean),
      notInvestmentCriteriaStatus: searchParams.get('notInvestmentCriteriaStatus')?.split(',').filter(Boolean),
    };
    
    console.log('CompaniesView: Initializing V2 filters from URL:', urlFilters);
    console.log('CompaniesView: Search params:', Object.fromEntries(searchParams.entries()));
    
    setFilters(urlFilters);
    
    // Fetch companies immediately with V2 filters
    fetchCompanies(urlFilters);
  }, [searchParams]);



  // Handle V2 filter changes
  const handleFiltersChange = (newFilters: CompanyUnifiedFiltersV2Type) => {
    console.log('CompaniesView: handleFiltersChange called with V2 filters:', newFilters);
    setFilters(newFilters);
    
    // Fetch companies with new V2 filters immediately
    fetchCompanies(newFilters);
    
    // Update URL with new V2 filters
    const params = new URLSearchParams();
    
    // Add all V2 filter parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','));
        } else if (typeof value === 'boolean') {
          params.set(key, value.toString());
        } else if (typeof value === 'number' || typeof value === 'string') {
          params.set(key, value.toString());
        }
      }
    });
    
    // Preserve tab parameter and add filter parameters
    params.set('tab', 'companies');
    
    // Update URL without triggering a page reload
    const newUrl = `/dashboard/entity?${params.toString()}`;
    console.log('CompaniesView: Updating URL to (V2):', newUrl);
    router.replace(newUrl, { scroll: false });
  };



  // Handle clear filters (V2 only)
  const handleClearFilters = () => {
    const clearedFilters: CompanyUnifiedFiltersV2Type = {
      page: 1,
      limit: 25,
      sortBy: 'updated_at',
      sortOrder: 'desc'
    };
    setFilters(clearedFilters);
    
    // Fetch companies with cleared V2 filters immediately
    fetchCompanies(clearedFilters);
    
    // Update URL to remove all filter parameters but keep tab
    router.replace('/dashboard/entity?tab=companies', { scroll: false });
  };

  // Handle pagination (V2)
  const handlePageChange = (page: number) => {
    const newFilters = { ...filters, page };
    setFilters(newFilters);
    
    // Fetch companies with new page immediately using V2
    fetchCompanies(newFilters);
    
    // Update URL with new page
    const params = new URLSearchParams();
    
    // Add all V2 filter parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','));
        } else if (typeof value === 'boolean') {
          params.set(key, value.toString());
        } else if (typeof value === 'number' || typeof value === 'string') {
          params.set(key, value.toString());
        }
      }
    });
    
    // Preserve tab parameter and add filter parameters
    params.set('tab', 'companies');
    
    // Update URL without triggering a page reload
    const newUrl = `/dashboard/entity?${params.toString()}`;
    console.log('CompaniesView: Updating URL to (V2):', newUrl);
    router.replace(newUrl, { scroll: false });
  };

  // Handle pagination size change (V2)
  const handlePaginationSizeChange = (newLimit: number) => {
    const newFilters = { ...filters, limit: newLimit, page: 1 }; // Reset to page 1 when changing size
    setFilters(newFilters);
    
    // Fetch companies with new limit immediately using V2
    fetchCompanies(newFilters);
    
    // Update URL with new limit
    const params = new URLSearchParams();
    
    // Add all V2 filter parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','));
        } else if (typeof value === 'boolean') {
          params.set(key, value.toString());
        } else if (typeof value === 'number' || typeof value === 'string') {
          params.set(key, value.toString());
        }
      }
    });
    
    // Preserve tab parameter and add filter parameters
    params.set('tab', 'companies');
    
    // Update URL without triggering a page reload
    const newUrl = `/dashboard/entity?${params.toString()}`;
    console.log('CompaniesView: Updating URL to (V2):', newUrl);
    router.replace(newUrl, { scroll: false });
  };

  // Handle company selection
  const handleSelectCompany = (companyId: number) => {
    setSelectedCompanies(prev => {
      const newSet = new Set(prev);
      if (newSet.has(companyId)) {
        newSet.delete(companyId);
      } else {
        newSet.add(companyId);
      }
      return newSet;
    });
  };

  const handleToggleSelection = (companyId: number, event: React.MouseEvent | React.ChangeEvent) => {
    event.stopPropagation();
    handleSelectCompany(companyId);
  };

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedCompanies(new Set());
    } else {
      const allCompanyIds = new Set(companies.map(company => company.company_id));
      setSelectedCompanies(allCompanyIds);
    }
    setSelectAll(!selectAll);
  };

  // Processing job execution
  const executeProcessingJob = async (stage: CompanyProcessingStage) => {
    if (selectedCompanies.size === 0) return;

    const companyIds = Array.from(selectedCompanies);
    
    // Update processing state
    setProcessingJobs(prev => 
      prev.map(job => 
        job.stage === stage 
          ? { ...job, isExecuting: true }
          : job
      )
    );

    try {
      const jobOptions: any = {
        multiIds: companyIds,
        filters: buildFilters()
      }

      const response = await fetch('/api/processing/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'execute_manual',
          stage,
          entityType: 'company',
          options: jobOptions
        })
      });

      const data = await response.json();
      if (data.success) {
        toast({
          title: "Processing Started",
          description: `Successfully triggered ${getStageConfig(stage).title} for ${companyIds.length} company${companyIds.length !== 1 ? 'ies' : 'y'}`,
        });
        
        // Refresh companies after a short delay to show updated status
        setTimeout(async () => {
          const fetchCompanies = async () => {
            setLoading(true);
            try {
              const params = new URLSearchParams();
              Object.entries(filters).forEach(([key, value]) => {
                if (value !== undefined && value !== null && value !== '') {
                  if (Array.isArray(value) && value.length > 0) {
                    params.append(key, value.join(','));
                  } else if (typeof value === 'boolean') {
                    params.append(key, value.toString());
                  } else if (typeof value === 'number' || typeof value === 'string') {
                    params.append(key, value.toString());
                  }
                }
              });

              const response = await fetch(`/api/companies/unified-filters-v2?${params.toString()}`);
              if (response.ok) {
                const data = await response.json();
                setCompanies(data.data || []);
                setTotalCompanies(data.pagination?.total || 0);
                setTotalPages(data.pagination?.totalPages || 0);
              }
            } catch (error) {
              console.error("Error refreshing companies:", error);
            } finally {
              setLoading(false);
            }
          };

          await fetchCompanies();
        }, 2000);
      } else {
        toast({
          title: "Processing Failed",
          description: data.error || 'Failed to start processing job',
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error executing processing job:', error);
      toast({
        title: "Processing Error",
        description: 'An error occurred while starting the processing job',
        variant: "destructive",
      });
    } finally {
      // Reset processing state after a delay
      setTimeout(() => {
        setProcessingJobs(prev => 
          prev.map(job => 
            job.stage === stage 
              ? { ...job, isExecuting: false }
              : job
          )
        );
      }, 3000);
    }
  };

  const buildFilters = () => {
    const filterParams: any = {};
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          filterParams[key] = value;
        } else if (typeof value === 'boolean' || typeof value === 'number' || typeof value === 'string') {
          filterParams[key] = value;
        }
      }
    });
    return filterParams;
  };

  const getStageConfig = (stage: CompanyProcessingStage) => {
    const configs = {
      website_scraping: {
        title: 'Website Scraping',
        icon: Database,
        color: 'bg-blue-500',
        description: 'Scrape company websites for data'
      },
      company_overview_v2: {
        title: 'Company Overview V2',
        icon: Zap,
        color: 'bg-green-500',
        description: 'Extract structured company overview (V2)'
      },
      company_investment_criteria: {
        title: 'Investment Criteria',
        icon: CheckSquare,
        color: 'bg-orange-500',
        description: 'Extract investment criteria and preferences'
      }
    };
    return configs[stage];
  };

  const getCompanyProcessingStatus = (company: UnifiedCompanyData, stage: CompanyProcessingStage) => {
    switch (stage) {
      case 'website_scraping':
        return (company as any).website_scraping_status;
      case 'company_overview_v2':
        return (company as any).overview_v2_status;
      case 'company_investment_criteria':
        return (company as any).investment_criteria_status;
      default:
        return null;
    }
  };

  const getSelectedCompaniesStatusSummary = (stage: CompanyProcessingStage) => {
    if (selectedCompanies.size === 0) return null;

    const statusCounts: { [key: string]: number } = {};
    let total = 0;

    companies.forEach(company => {
      if (selectedCompanies.has(company.company_id)) {
        const status = getCompanyProcessingStatus(company, stage);
        if (status) {
          statusCounts[status] = (statusCounts[status] || 0) + 1;
        }
        total++;
      }
    });

    return { statusCounts, total };
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="w-full px-6 py-6">
        <header className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Companies</h1>
            <p className="text-sm text-gray-500 mt-1">
              Manage and explore your company database
            </p>
          </div>
          <Button
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white"
            onClick={() => router.push("/dashboard/companies/add")}
          >
            <PlusCircle className="h-4 w-4" />
            Add Company
          </Button>
        </header>



        {/* V2 Company Filters Only */}
        <CompanyUnifiedFiltersV2Component
          filters={filters}
          mappings={mappings}
          onFiltersChange={handleFiltersChange}
          onClearFilters={handleClearFilters}
          isLoading={loading}
        />

        {/* Company Selection and Processing */}
        {companies.length > 0 && (
          <Card className="mb-6 border-0 shadow-sm bg-gradient-to-r from-blue-50 via-purple-50 to-indigo-50">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-lg">
                <CheckSquare className="h-5 w-5 text-blue-600" />
                Company Processing
                <Badge variant="secondary" className="bg-blue-100 text-blue-700 border border-blue-200">
                  Batch Operations
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-4">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-700">
                      Select all {companies.length} companies
                    </span>
                  </label>
                  {selectedCompanies.size > 0 && (
                    <Badge className="bg-blue-100 text-blue-700 border border-blue-200">
                      {selectedCompanies.size} selected
                    </Badge>
                  )}
                </div>
              </div>

              {selectedCompanies.size > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {processingJobs.map((job) => {
                    const config = getStageConfig(job.stage);
                    const statusSummary = getSelectedCompaniesStatusSummary(job.stage);
                    
                    return (
                      <Card key={job.stage} className="border border-gray-200">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-2">
                              <config.icon className="h-4 w-4 text-gray-600" />
                              <span className="font-medium text-gray-900">{config.title}</span>
                            </div>
                            {statusSummary && (
                              <div className="flex items-center gap-1">
                                {Object.entries(statusSummary.statusCounts).map(([status, count]) => (
                                  <Badge key={status} variant="outline" className="text-xs">
                                    {status}: {count}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                          
                          <p className="text-sm text-gray-600 mb-3">
                            {config.description}
                          </p>
                          
                          <Button
                            onClick={() => executeProcessingJob(job.stage)}
                            disabled={job.isExecuting}
                            className="w-full"
                            size="sm"
                          >
                            {job.isExecuting ? (
                              <>
                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                Processing...
                              </>
                            ) : (
                              <>
                                <Play className="h-4 w-4 mr-2" />
                                Process {selectedCompanies.size} Company{selectedCompanies.size !== 1 ? 'ies' : 'y'}
                              </>
                            )}
                          </Button>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Results Summary */}
        <div className="mb-4 flex items-center justify-between">
          <div className="text-sm text-gray-600 flex items-center">
            {loading ? (
              <span>Loading companies...</span>
            ) : (
              <span>
                Showing {companies.length} of {totalCompanies.toLocaleString()} companies
                {filters.searchTerm && ` matching "${filters.searchTerm}"`}
              </span>
            )}
          </div>
          <div className="flex items-center gap-4">
            <PaginationSizeSelector
              currentSize={filters.limit || 25}
              onSizeChange={handlePaginationSizeChange}
            />
            {totalPages > 1 && (
              <Pagination
                currentPage={filters.page || 1}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            )}
          </div>
        </div>

        {/* Companies Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
          {loading ? (
            // Loading skeletons
            Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            ))
          ) : companies.length === 0 ? (
            // No results
            <div className="col-span-full text-center py-12">
              <img 
                src="/api/placeholder/200/200" 
                alt="No companies found" 
                className="mx-auto mb-4 opacity-50"
              />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No companies found</h3>
              <p className="text-gray-500 mb-4">
                Try adjusting your filters or search terms to find companies.
              </p>
              <Button
                onClick={handleClearFilters}
                variant="outline"
                className="mx-auto"
              >
                Clear all filters
              </Button>
            </div>
          ) : (
            // Company cards
            companies.map((company) => (
              <CompanyCard 
                key={company.company_id} 
                company={company}
                isSelected={selectedCompanies.has(company.company_id)}
                onToggleSelection={handleToggleSelection}
              />
            ))
          )}
        </div>


      </main>
    </div>
  );
}
