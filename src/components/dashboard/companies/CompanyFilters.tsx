'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  X, Filter, RotateCcw, Search, Building, User,
  DollarSign, Percent, MapPin, TrendingUp, Building2, Globe, Settings, Sparkles,
  SlidersHorizontal, ChevronRight, Calendar, Clock, Target, PieChart, BarChart3,
  Briefcase, CreditCard, LineChart, Activity, Banknote, ArrowUpDown, ArrowUp, ArrowDown,
  Calculator, Timer, TrendingDown, AlertCircle, Users, Home, Factory, Crown
} from 'lucide-react'
import { CompanyFilters, CompanyFilterOptions, COMPANY_SORT_OPTIONS } from '@/types/company-filters'
import CompanyFilterPanel from './filters/CompanyFilterPanel'

interface CompanyFiltersProps {
  filters: CompanyFilters
  filterOptions: CompanyFilterOptions
  onFiltersChange: (filters: CompanyFilters) => void
  onClearFilters: () => void
  isLoading?: boolean
}

// Icon mapping for sort options
const iconMap: { [key: string]: any } = {
  Clock,
  Calendar,
  Building,
  DollarSign,
  Banknote,
  Building2,
  Users,
  AlertCircle,
  Target,
  TrendingUp,
  Calculator
}

export default function CompanyFiltersComponent({
  filters,
  filterOptions,
  onFiltersChange,
  onClearFilters,
  isLoading = false
}: CompanyFiltersProps) {
  const [localFilters, setLocalFilters] = useState<CompanyFilters>(filters)
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false)

  // Update local filters when props change
  useEffect(() => {
    setLocalFilters(filters)
  }, [filters])

  // Debounced update for search term
  useEffect(() => {
    const timer = setTimeout(() => {
      if (localFilters.searchTerm !== filters.searchTerm) {
        onFiltersChange(localFilters)
      }
    }, 300)

    return () => clearTimeout(timer)
  }, [localFilters.searchTerm])

  // Update filters for non-search changes immediately
  const updateFilters = (updates: Partial<CompanyFilters>) => {
    const newFilters = { ...localFilters, ...updates }
    setLocalFilters(newFilters)
    
    // Don't auto-update search term (it's debounced above)
    if (!updates.hasOwnProperty('searchTerm')) {
      onFiltersChange(newFilters)
    }
  }

  // Currency formatting helper
  const formatCurrencyFromMillions = (millions: number): string => {
    if (millions >= 1000) {
      return `$${(millions / 1000).toFixed(1)}B`
    } else if (millions >= 1000) {
      return `$${(millions / 1000).toFixed(1)}M`
    } else {
      return `$${millions}K`
    }
  }

  // Count active filters - comprehensive coverage of all filter fields
  const getActiveFilterCount = () => {
    let count = 0
    
    // Basic filters
    if (localFilters.searchTerm) count++
    if (localFilters.companyId) count++
    if (localFilters.companyName) count++
    
    // Company Profile filters
    if (localFilters.companyType?.length) count++
    if (localFilters.businessModel) count++
    if (localFilters.companyIndustry?.length) count++
    if (localFilters.capitalPosition?.length) count++
    if (localFilters.fundSizeMin || localFilters.fundSizeMax) count++
    if (localFilters.aumMin || localFilters.aumMax) count++
    if (localFilters.numberOfPropertiesMin || localFilters.numberOfPropertiesMax) count++
    if (localFilters.headquarters?.length) count++
    if (localFilters.numberOfOfficesMin || localFilters.numberOfOfficesMax) count++
    if (localFilters.foundedYearMin || localFilters.foundedYearMax) count++
    if (localFilters.numberOfEmployeesMin || localFilters.numberOfEmployeesMax) count++
    
    // Basic company fields
    if (localFilters.industry?.length) count++
    if (localFilters.companyState?.length) count++
    if (localFilters.companyCity?.length) count++
    if (localFilters.companyCountry?.length) count++
    if (localFilters.processed !== undefined) count++
    if (localFilters.extracted !== undefined) count++
    
    // Legacy overview structure
    if (localFilters.primaryIndustry?.length) count++
    if (localFilters.coreCapitalPosition?.length) count++
    if (localFilters.investmentStrategy?.length) count++
    if (localFilters.holdHorizon?.length) count++
    
    // Geographic and property filters
    if (localFilters.geographicFocus?.length) count++
    if (localFilters.propertyTypes?.length) count++
    if (localFilters.assetTypes?.length) count++
    if (localFilters.dealStructure?.length) count++
    if (localFilters.investmentFocus?.length) count++
    
    // Capital commitments
    if (localFilters.debtRangeMin || localFilters.debtRangeMax) count++
    if (localFilters.equityRangeMin || localFilters.equityRangeMax) count++
    
    // Processing status
    if (localFilters.processingState?.length) count++
    if (localFilters.websiteScrapingStatus?.length) count++
    if (localFilters.companyOverviewStatus?.length) count++
    
    // Investment criteria relationships
    if (localFilters.hasInvestmentCriteria !== undefined) count++
    if (localFilters.targetReturnMin || localFilters.targetReturnMax) count++
    if (localFilters.dealSizeMin || localFilters.dealSizeMax) count++
    if (localFilters.minHoldPeriodMin || localFilters.minHoldPeriodMax) count++
    if (localFilters.maxHoldPeriodMin || localFilters.maxHoldPeriodMax) count++
    if (localFilters.criteriaPropertyTypes?.length) count++
    if (localFilters.criteriaStrategies?.length) count++
    if (localFilters.criteriaRegions?.length) count++
    if (localFilters.criteriaStates?.length) count++
    if (localFilters.criteriaCities?.length) count++
    
    return count
  }

  const activeFilterCount = getActiveFilterCount()

  // Show active filters as badges
  const renderActiveFilters = () => {
    const activeFilters: Array<{ label: string; onRemove: () => void }> = []

    // Add active filter badges
    if (localFilters.companyType?.length) {
      activeFilters.push({
        label: `Company Type: ${localFilters.companyType.join(', ')}`,
        onRemove: () => updateFilters({ companyType: undefined })
      })
    }

    if (localFilters.businessModel) {
      activeFilters.push({
        label: `Business Model: ${localFilters.businessModel}`,
        onRemove: () => updateFilters({ businessModel: undefined })
      })
    }

    if (localFilters.fundSizeMin || localFilters.fundSizeMax) {
      const min = localFilters.fundSizeMin ? formatCurrencyFromMillions(localFilters.fundSizeMin) : ''
      const max = localFilters.fundSizeMax ? formatCurrencyFromMillions(localFilters.fundSizeMax) : ''
      const rangeLabel = min && max ? `${min} - ${max}` : min ? `≥ ${min}` : `≤ ${max}`
      activeFilters.push({
        label: `Fund Size: ${rangeLabel}`,
        onRemove: () => updateFilters({ fundSizeMin: undefined, fundSizeMax: undefined })
      })
    }

    if (localFilters.aumMin || localFilters.aumMax) {
      const min = localFilters.aumMin ? formatCurrencyFromMillions(localFilters.aumMin) : ''
      const max = localFilters.aumMax ? formatCurrencyFromMillions(localFilters.aumMax) : ''
      const rangeLabel = min && max ? `${min} - ${max}` : min ? `≥ ${min}` : `≤ ${max}`
      activeFilters.push({
        label: `AUM: ${rangeLabel}`,
        onRemove: () => updateFilters({ aumMin: undefined, aumMax: undefined })
      })
    }

    if (localFilters.headquarters?.length) {
      activeFilters.push({
        label: `Headquarters: ${localFilters.headquarters.join(', ')}`,
        onRemove: () => updateFilters({ headquarters: undefined })
      })
    }

    if (localFilters.propertyTypes?.length) {
      activeFilters.push({
        label: `Property Types: ${localFilters.propertyTypes.join(', ')}`,
        onRemove: () => updateFilters({ propertyTypes: undefined })
      })
    }

    if (localFilters.hasInvestmentCriteria !== undefined) {
      activeFilters.push({
        label: `Has Investment Criteria: ${localFilters.hasInvestmentCriteria ? 'Yes' : 'No'}`,
        onRemove: () => updateFilters({ hasInvestmentCriteria: undefined })
      })
    }

    // Add more active filter badges as needed...

    return activeFilters.slice(0, 5) // Limit to 5 visible badges
  }

  const activeFilterBadges = renderActiveFilters()

  return (
    <>
      {/* Modern Compact Filter Bar */}
      <div className="w-full bg-white border border-gray-200 rounded-xl shadow-sm mb-6">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            {/* Filter Button */}
            <Button
              onClick={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
              className={`flex items-center gap-3 px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 ${
                isFilterPanelOpen 
                  ? 'bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white' 
                  : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white'
              }`}
            >
              <SlidersHorizontal className="h-5 w-5" />
              <span className="font-medium">Advanced Filters</span>
              {activeFilterCount > 0 && (
                <Badge className="bg-white/20 text-white border border-white/20 ml-1">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>

            {/* Quick Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search companies, names, or IDs..."
                value={localFilters.searchTerm || ''}
                onChange={(e) => updateFilters({ searchTerm: e.target.value || undefined })}
                className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
              />
            </div>
          </div>

          <div className="flex items-center gap-4">
            {/* Clear Filters */}
            {activeFilterCount > 0 && (
              <Button
                onClick={onClearFilters}
                variant="outline"
                className="flex items-center gap-2 px-4 py-2 border-gray-200 text-gray-600 hover:bg-gray-50 hover:text-gray-800 transition-colors"
              >
                <RotateCcw className="h-4 w-4" />
                Clear All
              </Button>
            )}

            {/* Sort Controls */}
            <div className="flex items-center gap-2">
              <Select 
                value={localFilters.sortBy || 'updated_at'} 
                onValueChange={(value) => updateFilters({ sortBy: value })}
              >
                <SelectTrigger className="w-auto min-w-[200px] border-gray-200 bg-white shadow-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="max-h-[400px]">
                  <div className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100">
                    Default
                  </div>
                  <SelectItem value="updated_at">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Last Updated (Newest First)
                    </div>
                  </SelectItem>
                  <SelectItem value="created_at">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Created Date (Newest First)
                    </div>
                  </SelectItem>
                  <SelectItem value="company_name">
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      Company Name (A-Z)
                    </div>
                  </SelectItem>
                  
                  <div className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100 mt-2">
                    Financial Metrics
                  </div>
                  <SelectItem value="fund_size_parsed">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Fund Size (Largest First)
                    </div>
                  </SelectItem>
                  <SelectItem value="aum_parsed">
                    <div className="flex items-center gap-2">
                      <Banknote className="h-4 w-4" />
                      AUM (Largest First)
                    </div>
                  </SelectItem>
                  <SelectItem value="founded_year">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Founded Year (Newest First)
                    </div>
                  </SelectItem>
                  
                  <div className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100 mt-2">
                    Size & Scale
                  </div>
                  <SelectItem value="number_of_properties">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4" />
                      Number of Properties (Most First)
                    </div>
                  </SelectItem>
                  <SelectItem value="number_of_offices">
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      Number of Offices (Most First)
                    </div>
                  </SelectItem>
                  <SelectItem value="employees_max">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Number of Employees (Most First)
                    </div>
                  </SelectItem>
                  
                  <div className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100 mt-2">
                    Investment Criteria
                  </div>
                  <SelectItem value="investment_criteria_count">
                    <div className="flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      Investment Criteria Count (Most First)
                    </div>
                  </SelectItem>
                  <SelectItem value="criteria_target_return_max">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Target Return (Highest First)
                    </div>
                  </SelectItem>
                  <SelectItem value="criteria_deal_size_max">
                    <div className="flex items-center gap-2">
                      <Calculator className="h-4 w-4" />
                      Deal Size (Largest First)
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>

              {/* Sort Order */}
              <Button
                onClick={() => updateFilters({ 
                  sortOrder: localFilters.sortOrder === 'asc' ? 'desc' : 'asc' 
                })}
                variant="outline"
                size="sm"
                className="border-gray-200 p-2"
              >
                {localFilters.sortOrder === 'asc' ? (
                  <ArrowUp className="h-4 w-4" />
                ) : (
                  <ArrowDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Active Filter Badges */}
        {activeFilterBadges.length > 0 && (
          <div className="px-4 pb-4">
            <div className="flex items-center gap-2 flex-wrap">
              <span className="text-sm text-gray-500 font-medium">Active filters:</span>
              {activeFilterBadges.map((filter, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="flex items-center gap-1 px-3 py-1 bg-blue-50 text-blue-700 border border-blue-200 hover:bg-blue-100 transition-colors"
                >
                  <span className="text-xs">{filter.label}</span>
                  <button
                    onClick={filter.onRemove}
                    className="ml-1 hover:bg-blue-200 rounded-full p-0.5 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
              {activeFilterCount > 5 && (
                <Badge variant="outline" className="px-3 py-1 text-gray-500 border-gray-300">
                  +{activeFilterCount - 5} more
                </Badge>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Advanced Filter Panel */}
      <CompanyFilterPanel
        isOpen={isFilterPanelOpen}
        onClose={() => setIsFilterPanelOpen(false)}
        filters={localFilters}
        filterOptions={filterOptions}
        onFiltersChange={updateFilters}
        onClearFilters={onClearFilters}
        isLoading={isLoading}
      />
    </>
  )
} 