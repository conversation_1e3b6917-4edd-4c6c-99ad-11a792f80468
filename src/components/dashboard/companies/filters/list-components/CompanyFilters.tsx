'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { CheckIcon, ChevronDown, Filter, X } from 'lucide-react'
import { Source } from '../shared/types'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'

interface CompanyFiltersProps {
  sources: Source[]
  selectedSources: string[]
  searchTerm: string
  onSearchChange: (value: string) => void
  onSourceSelect: (source: string) => void
  onSourceRemove: (source: string) => void
  onClearFilters: () => void
  propertyTypes: string[]
  selectedPropertyTypes: string[]
  onPropertyTypeSelect: (type: string) => void
  onPropertyTypeRemove: (type: string) => void
  companyTypes: string[]
  selectedCompanyTypes: string[]
  onCompanyTypeSelect: (type: string) => void
  onCompanyTypeRemove: (type: string) => void
  investmentFocus: string[]
  selectedInvestmentFocus: string[]
  onInvestmentFocusSelect: (focus: string) => void
  onInvestmentFocusRemove: (focus: string) => void
  geographicFocus: string[]
  selectedGeographicFocus: string[]
  onGeographicFocusSelect: (region: string) => void
  onGeographicFocusRemove: (region: string) => void
  hasOverviewData: boolean
  onHasOverviewDataChange: (value: boolean) => void
}

export default function CompanyFilters({
  sources,
  selectedSources,
  searchTerm,
  onSearchChange,
  onSourceSelect,
  onSourceRemove,
  onClearFilters,
  propertyTypes,
  selectedPropertyTypes,
  onPropertyTypeSelect,
  onPropertyTypeRemove,
  companyTypes,
  selectedCompanyTypes,
  onCompanyTypeSelect,
  onCompanyTypeRemove,
  investmentFocus,
  selectedInvestmentFocus,
  onInvestmentFocusSelect,
  onInvestmentFocusRemove,
  geographicFocus,
  selectedGeographicFocus,
  onGeographicFocusSelect,
  onGeographicFocusRemove,
  hasOverviewData,
  onHasOverviewDataChange
}: CompanyFiltersProps) {
  const [filterOpen, setFilterOpen] = useState(false)
  const [activeFilterTab, setActiveFilterTab] = useState('source')
  
  const hasActiveFilters = selectedSources.length > 0 || 
    selectedPropertyTypes.length > 0 || 
    selectedCompanyTypes.length > 0 || 
    selectedInvestmentFocus.length > 0 || 
    selectedGeographicFocus.length > 0 ||
    hasOverviewData

  return (
    <div className="space-y-4 mb-6">
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Input
            placeholder="Search companies..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>

        <Popover open={filterOpen} onOpenChange={setFilterOpen}>
          <PopoverTrigger asChild>
            <Button 
              variant={hasActiveFilters ? "default" : "outline"} 
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              <span>Filter</span>
              {hasActiveFilters && (
                <Badge className="ml-1 bg-white text-primary">
                  {selectedSources.length + 
                   selectedPropertyTypes.length + 
                   selectedCompanyTypes.length + 
                   selectedInvestmentFocus.length + 
                   selectedGeographicFocus.length +
                   (hasOverviewData ? 1 : 0)
                  }
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] p-0" align="end">
            <Tabs 
              defaultValue="source" 
              value={activeFilterTab} 
              onValueChange={setActiveFilterTab}
              className="w-full"
            >
              <div className="border-b px-3">
                <TabsList className="grid grid-cols-5 h-auto py-2">
                  <TabsTrigger value="source" className="text-xs py-1 px-2">Source</TabsTrigger>
                  <TabsTrigger value="company" className="text-xs py-1 px-2">Company</TabsTrigger>
                  <TabsTrigger value="property" className="text-xs py-1 px-2">Property</TabsTrigger>
                  <TabsTrigger value="investment" className="text-xs py-1 px-2">Investment</TabsTrigger>
                  <TabsTrigger value="region" className="text-xs py-1 px-2">Region</TabsTrigger>
                </TabsList>
              </div>
              
              <TabsContent value="source" className="p-0 m-0">
                <Command>
                  <CommandInput placeholder="Search sources..." />
                  <CommandList>
                    <CommandEmpty>No sources found.</CommandEmpty>
                    <CommandGroup>
                      {sources.map((source) => (
                        <CommandItem
                          key={source.source}
                          onSelect={() => onSourceSelect(source.source)}
                          className="flex items-center justify-between"
                        >
                          <div className="flex items-center">
                            <span>{source.source}</span>
                            <Badge variant="outline" className="ml-2 text-xs">
                              {source.count}
                            </Badge>
                          </div>
                          {selectedSources.includes(source.source) && (
                            <CheckIcon className="h-4 w-4" />
                          )}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </TabsContent>
              
              <TabsContent value="company" className="p-0 m-0">
                <Command>
                  <CommandInput placeholder="Search company types..." />
                  <CommandList>
                    <CommandEmpty>No company types found.</CommandEmpty>
                    <CommandGroup>
                      <div className="px-3 py-2 border-b">
                        <div className="flex items-center space-x-2">
                          <Switch 
                            id="has-overview" 
                            checked={hasOverviewData}
                            onCheckedChange={onHasOverviewDataChange}
                          />
                          <Label htmlFor="has-overview">Has overview data</Label>
                        </div>
                      </div>
                      {companyTypes.map((type) => (
                        <CommandItem
                          key={type}
                          onSelect={() => onCompanyTypeSelect(type)}
                          className="flex items-center justify-between"
                        >
                          <span>{type}</span>
                          {selectedCompanyTypes.includes(type) && (
                            <CheckIcon className="h-4 w-4" />
                          )}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </TabsContent>
              
              <TabsContent value="property" className="p-0 m-0">
                <Command>
                  <CommandInput placeholder="Search property types..." />
                  <CommandList>
                    <CommandEmpty>No property types found.</CommandEmpty>
                    <CommandGroup>
                      {propertyTypes.map((type) => (
                        <CommandItem
                          key={type}
                          onSelect={() => onPropertyTypeSelect(type)}
                          className="flex items-center justify-between"
                        >
                          <span>{type}</span>
                          {selectedPropertyTypes.includes(type) && (
                            <CheckIcon className="h-4 w-4" />
                          )}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </TabsContent>
              
              <TabsContent value="investment" className="p-0 m-0">
                <Command>
                  <CommandInput placeholder="Search investment strategies..." />
                  <CommandList>
                    <CommandEmpty>No investment strategies found.</CommandEmpty>
                    <CommandGroup>
                      {investmentFocus.map((focus) => (
                        <CommandItem
                          key={focus}
                          onSelect={() => onInvestmentFocusSelect(focus)}
                          className="flex items-center justify-between"
                        >
                          <span>{focus}</span>
                          {selectedInvestmentFocus.includes(focus) && (
                            <CheckIcon className="h-4 w-4" />
                          )}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </TabsContent>
              
              <TabsContent value="region" className="p-0 m-0">
                <Command>
                  <CommandInput placeholder="Search regions..." />
                  <CommandList>
                    <CommandEmpty>No regions found.</CommandEmpty>
                    <CommandGroup>
                      {geographicFocus.map((region) => (
                        <CommandItem
                          key={region}
                          onSelect={() => onGeographicFocusSelect(region)}
                          className="flex items-center justify-between"
                        >
                          <span>{region}</span>
                          {selectedGeographicFocus.includes(region) && (
                            <CheckIcon className="h-4 w-4" />
                          )}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </TabsContent>
            </Tabs>
            
            <div className="p-3 border-t">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onClearFilters}
                disabled={!hasActiveFilters}
                className="w-full"
              >
                Clear all filters
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {selectedSources.map((source) => (
            <Badge key={source} variant="secondary" className="flex items-center gap-1">
              <span>Source: {source}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onSourceRemove(source)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          
          {selectedCompanyTypes.map((type) => (
            <Badge key={type} variant="secondary" className="flex items-center gap-1 bg-purple-50 text-purple-800 hover:bg-purple-100">
              <span>Type: {type}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onCompanyTypeRemove(type)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          
          {selectedPropertyTypes.map((type) => (
            <Badge key={type} variant="secondary" className="flex items-center gap-1 bg-green-50 text-green-800 hover:bg-green-100">
              <span>Property: {type}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onPropertyTypeRemove(type)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          
          {selectedInvestmentFocus.map((focus) => (
            <Badge key={focus} variant="secondary" className="flex items-center gap-1 bg-amber-50 text-amber-800 hover:bg-amber-100">
              <span>Strategy: {focus}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onInvestmentFocusRemove(focus)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          
          {selectedGeographicFocus.map((region) => (
            <Badge key={region} variant="secondary" className="flex items-center gap-1 bg-blue-50 text-blue-800 hover:bg-blue-100">
              <span>Region: {region}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onGeographicFocusRemove(region)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          
          {hasOverviewData && (
            <Badge variant="secondary" className="flex items-center gap-1 bg-violet-50 text-violet-800 hover:bg-violet-100">
              <span>Has Overview Data</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onHasOverviewDataChange(false)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}
    </div>
  )
} 