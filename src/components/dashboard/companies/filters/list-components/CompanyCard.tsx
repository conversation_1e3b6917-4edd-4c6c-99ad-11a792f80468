'use client'

import { Company } from '../../shared/types'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { MapPin, Users, ExternalLink, Building, ArrowRight, Globe, Briefcase, Calendar, DollarSign, Clock, BarChart3, Home, LayoutGrid } from 'lucide-react'
import { getCompanyInitials, getIndustryColor, formatLocation } from '../../shared/utils'
import { useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'

interface CompanyCardProps {
  company: Company
  isSelected?: boolean
  onToggleSelection?: (companyId: number, event: React.MouseEvent | React.ChangeEvent) => void
}

export default function CompanyCard({ company, isSelected = false, onToggleSelection }: CompanyCardProps) {
  const router = useRouter()
  const industryColorClass = getIndustryColor(company.industry || '')
  const companyInitials = getCompanyInitials(company.company_name)
  const location = formatLocation(company)
  
  // Use either founded_year or foundedyear
  const foundingYear = company.founded_year || company.foundedyear
  
  // Check if company has overview data
  const hasOverviewData = company.companytype || 
    (company.propertytypes && company.propertytypes.length > 0) ||
    (company.investmentfocus && company.investmentfocus.length > 0) ||
    (company.geographicfocus && company.geographicfocus.length > 0) ||
    company.aum ||
    company.dealsize ||
    company.holdperiod ||
    company.targetreturn

  return (
    <div 
      className="bg-white border border-gray-100 rounded-lg overflow-hidden hover:shadow-md transition-all duration-200 h-full flex flex-col cursor-pointer"
      onClick={() => router.push(`/dashboard/companies/${company.company_id}`)}
    >
      {/* Colored accent top border based on industry */}
      <div className={cn("h-1.5 w-full", industryColorClass)} />
      
      <div className="p-5 flex-grow flex flex-col">
        <div className="flex items-start gap-4 mb-4">
          {onToggleSelection && (
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => onToggleSelection(company.company_id, e)}
              onClick={(e) => e.stopPropagation()}
              className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          )}
          <Avatar className="h-12 w-12">
            <AvatarFallback className={cn("text-white font-semibold", industryColorClass)}>
              {companyInitials}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg leading-tight mb-1 text-gray-900 hover:text-blue-600 transition-colors">
              {company.company_name}
            </h3>
            
            {company.industry && (
              <div className="mb-2 flex items-center">
                <BarChart3 className="h-3.5 w-3.5 mr-1.5 flex-shrink-0 text-gray-400" />
                <span className="text-sm text-gray-600">{company.industry}</span>
              </div>
            )}
            
            <div className="flex flex-wrap gap-x-4 gap-y-1.5 text-sm text-gray-500">
              {location && (
                <div className="flex items-center">
                  <MapPin className="h-3.5 w-3.5 mr-1.5 flex-shrink-0 text-gray-400" />
                  <span className="truncate">{location}</span>
                </div>
              )}
              
              {company.contact_count !== undefined && (
                <div className="flex items-center">
                  <Users className="h-3.5 w-3.5 mr-1.5 flex-shrink-0 text-gray-400" />
                  <span>{company.contact_count} contacts</span>
                </div>
              )}
              
              {foundingYear && (
                <div className="flex items-center">
                  <Calendar className="h-3.5 w-3.5 mr-1.5 flex-shrink-0 text-gray-400" />
                  <span>Founded {foundingYear}</span>
                </div>
              )}
            </div>
          </div>
        </div>
        
        {company.company_website && (
          <a 
            href={company.company_website.startsWith('http') ? company.company_website : `https://${company.company_website}`}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-xs text-blue-600 hover:text-blue-800 mb-3 hover:underline underline-offset-2"
            onClick={(e) => e.stopPropagation()}
          >
            <Globe className="h-3.5 w-3.5 mr-1.5" />
            <span className="truncate">
              {company.company_website.replace(/^https?:\/\/(www\.)?/, '')}
            </span>
          </a>
        )}
        
        {/* Company Summary */}
        {company.summary && (
          <div className="mb-4 text-sm text-gray-600 line-clamp-2">
            {company.summary}
          </div>
        )}
        
        {/* Company Details */}
        {hasOverviewData && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2 mb-4">
            {/* Company Type */}
            {company.companytype && (
              <div className="flex items-start gap-2">
                <div className="bg-blue-50 p-1 rounded-md mt-0.5">
                  <Building className="h-3.5 w-3.5 text-blue-600" />
                </div>
                <div>
                  <div className="text-xs text-gray-500">Company Type</div>
                  <div className="text-sm font-medium">{company.companytype}</div>
                </div>
              </div>
            )}
            
            {/* AUM */}
            {company.aum && (
              <div className="flex items-start gap-2">
                <div className="bg-green-50 p-1 rounded-md mt-0.5">
                  <DollarSign className="h-3.5 w-3.5 text-green-600" />
                </div>
                <div>
                  <div className="text-xs text-gray-500">AUM</div>
                  <div className="text-sm font-medium">{company.aum}</div>
                </div>
              </div>
            )}
            
            {/* Deal Size */}
            {company.dealsize && (
              <div className="flex items-start gap-2">
                <div className="bg-amber-50 p-1 rounded-md mt-0.5">
                  <Briefcase className="h-3.5 w-3.5 text-amber-600" />
                </div>
                <div>
                  <div className="text-xs text-gray-500">Deal Size</div>
                  <div className="text-sm font-medium">{company.dealsize}</div>
                </div>
              </div>
            )}
            
            {/* Hold Period */}
            {company.holdperiod && (
              <div className="flex items-start gap-2">
                <div className="bg-purple-50 p-1 rounded-md mt-0.5">
                  <Clock className="h-3.5 w-3.5 text-purple-600" />
                </div>
                <div>
                  <div className="text-xs text-gray-500">Hold Period</div>
                  <div className="text-sm font-medium">{company.holdperiod}</div>
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* Status Section */}
        <div className="flex flex-wrap gap-1.5 mb-3">
          {/* Company Overview V2 Status */}
          {(company as any).overview_v2_status && (
            <Badge 
              variant="outline" 
              className={cn("text-xs px-2 py-0.5 rounded-full", {
                "bg-green-50 text-green-700 border-green-200 hover:bg-green-100": (company as any).overview_v2_status === 'completed',
                "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100": (company as any).overview_v2_status === 'pending',
                "bg-red-50 text-red-700 border-red-200 hover:bg-red-100": (company as any).overview_v2_status === 'failed',
                "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100": !['completed', 'pending', 'failed'].includes((company as any).overview_v2_status)
              })}
            >
              Overview: {(company as any).overview_v2_status}
            </Badge>
          )}
          
          {/* Website Scraping Status */}
          {company.website_scraping_status && (
            <Badge 
              variant="outline" 
              className={cn("text-xs px-2 py-0.5 rounded-full", {
                "bg-green-50 text-green-700 border-green-200 hover:bg-green-100": company.website_scraping_status === 'completed',
                "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100": company.website_scraping_status === 'pending',
                "bg-red-50 text-red-700 border-red-200 hover:bg-red-100": company.website_scraping_status === 'failed',
                "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100": !['completed', 'pending', 'failed'].includes(company.website_scraping_status)
              })}
            >
              Scraping: {company.website_scraping_status}
            </Badge>
          )}
          
          {/* Investment Criteria Status */}
          {(company as any).investment_criteria_status && (
            <Badge 
              variant="outline" 
              className={cn("text-xs px-2 py-0.5 rounded-full", {
                "bg-green-50 text-green-700 border-green-200 hover:bg-green-100": (company as any).investment_criteria_status === 'completed',
                "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100": (company as any).investment_criteria_status === 'pending',
                "bg-red-50 text-red-700 border-red-200 hover:bg-red-100": (company as any).investment_criteria_status === 'failed',
                "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100": !['completed', 'pending', 'failed'].includes((company as any).investment_criteria_status)
              })}
            >
              IC: {(company as any).investment_criteria_status}
            </Badge>
          )}
        </div>

        {/* Tags Section */}
        <div className="space-y-2.5 mt-auto">
          {/* Property Types */}
          {company.propertytypes && company.propertytypes.length > 0 && (
            <div className="flex flex-wrap gap-1.5">
              {company.propertytypes.slice(0, 3).map((type, index) => (
                <Badge key={index} variant="outline" className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 text-xs px-2 py-0.5 rounded-full">
                  {type}
                </Badge>
              ))}
              {company.propertytypes.length > 3 && (
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 text-xs px-2 py-0.5 rounded-full">
                  +{company.propertytypes.length - 3}
                </Badge>
              )}
            </div>
          )}
          
          {/* Investment Focus */}
          {company.investmentfocus && company.investmentfocus.length > 0 && (
            <div className="flex flex-wrap gap-1.5">
              {company.investmentfocus.slice(0, 3).map((focus, index) => (
                <Badge key={index} variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100 text-xs px-2 py-0.5 rounded-full">
                  {focus}
                </Badge>
              ))}
              {company.investmentfocus.length > 3 && (
                <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100 text-xs px-2 py-0.5 rounded-full">
                  +{company.investmentfocus.length - 3}
                </Badge>
              )}
            </div>
          )}
          
          {/* Geographic Focus */}
          {company.geographicfocus && company.geographicfocus.length > 0 && (
            <div className="flex flex-wrap gap-1.5">
              {company.geographicfocus.slice(0, 3).map((region, index) => (
                <Badge key={index} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 text-xs px-2 py-0.5 rounded-full">
                  {region}
                </Badge>
              ))}
              {company.geographicfocus.length > 3 && (
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 text-xs px-2 py-0.5 rounded-full">
                  +{company.geographicfocus.length - 3}
                </Badge>
              )}
            </div>
          )}
        </div>
      </div>
      
      <div className="px-5 py-3 bg-gray-50 border-t border-gray-100 flex justify-end">
        <Button 
          variant="ghost" 
          size="sm" 
          className="text-xs font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 group flex items-center"
          onClick={(e: React.MouseEvent) => {
            e.stopPropagation()
            router.push(`/dashboard/companies/${company.company_id}`)
          }}
        >
          View Details
          <ArrowRight className="h-3.5 w-3.5 ml-1.5 transition-transform group-hover:translate-x-1" />
        </Button>
      </div>
    </div>
  )
} 