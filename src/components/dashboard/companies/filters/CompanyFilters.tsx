'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  X, Filter, RotateCcw, Search, Building,
  SlidersHorizontal, BarChart3, ArrowUp, ArrowDown,
  DollarSign, MapPin, Building2
} from 'lucide-react'
import { CompanyFilters, CompanyFilterOptions, COMPANY_SORT_OPTIONS } from '@/types/company-filters'
import CompanyFilterPanel from './CompanyFilterPanel'

interface CompanyFiltersProps {
  filters: CompanyFilters
  filterOptions: CompanyFilterOptions
  onFiltersChange: (filters: CompanyFilters) => void
  onClearFilters: () => void
  isLoading?: boolean
}

export default function CompanyFiltersComponent({
  filters,
  filterOptions,
  onFiltersChange,
  onClearFilters,
  isLoading = false
}: CompanyFiltersProps) {
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false)
  const [localFilters, setLocalFilters] = useState<CompanyFilters>(filters)

  // Sync local filters with prop changes
  useEffect(() => {
    setLocalFilters(filters)
  }, [filters])

  // Update parent when local filters change
  const updateFilters = (newFilters: Partial<CompanyFilters>) => {
    const updatedFilters = { ...localFilters, ...newFilters, page: 1 }
    setLocalFilters(updatedFilters)
    onFiltersChange(updatedFilters)
  }

  // Format currency display - input values are in millions
  const formatCurrencyFromMillions = (millions?: number | null) => {
    if (millions === null || millions === undefined) return null
    if (millions === 0) return '$0'
    if (millions >= 1000) {
      return `$${(millions / 1000).toFixed(1)}B`
    }
    return `$${millions.toLocaleString()}M`
  }

  // Count active filters
  const getActiveFilterCount = () => {
    let count = 0
    
    // Basic filters
    if (localFilters.searchTerm) count++
    if (localFilters.companyName) count++
    if (localFilters.companyId) count++
    
    // Company profile metrics
    if (localFilters.companyType?.length) count++
    if (localFilters.businessModel) count++
    if (localFilters.fundSizeMin || localFilters.fundSizeMax) count++
    if (localFilters.aumMin || localFilters.aumMax) count++
    if (localFilters.numberOfPropertiesMin || localFilters.numberOfPropertiesMax) count++
    if (localFilters.headquarters?.length) count++
    if (localFilters.numberOfOfficesMin || localFilters.numberOfOfficesMax) count++
    if (localFilters.foundedYearMin || localFilters.foundedYearMax) count++
    if (localFilters.numberOfEmployeesMin || localFilters.numberOfEmployeesMax) count++
    
    // Basic company fields
    if (localFilters.industry?.length) count++
    if (localFilters.companyState?.length) count++
    if (localFilters.companyCity?.length) count++
    if (localFilters.companyCountry?.length) count++
    if (localFilters.processed !== undefined) count++
    if (localFilters.extracted !== undefined) count++
    
    // Overview structure fields
    if (localFilters.primaryIndustry?.length) count++
    if (localFilters.capitalPosition?.length) count++
    if (localFilters.investmentStrategy?.length) count++
    if (localFilters.propertyTypes?.length) count++
    if (localFilters.propertySubcategories?.length) count++
    if (localFilters.assetTypes?.length) count++
    if (localFilters.dealStructure?.length) count++
    
    // Investment criteria filters
    if (localFilters.hasInvestmentCriteria !== undefined) count++
    if (localFilters.targetReturnMin || localFilters.targetReturnMax) count++
    if (localFilters.dealSizeMin || localFilters.dealSizeMax) count++
    if (localFilters.criteriaRegions?.length) count++
    if (localFilters.criteriaStates?.length) count++
    if (localFilters.criteriaPropertyTypes?.length) count++
    
    return count
  }

  const activeFilterCount = getActiveFilterCount()

  return (
    <>
      {/* Modern Compact Filter Bar */}
      <div className="w-full bg-white border border-gray-200 rounded-xl shadow-sm mb-6">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            {/* Filter Button */}
            <Button
              onClick={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
              className={`flex items-center gap-3 px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 ${
                isFilterPanelOpen 
                  ? 'bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white' 
                  : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white'
              }`}
            >
              <SlidersHorizontal className="h-5 w-5" />
              <span className="font-medium">Company Filters</span>
              {activeFilterCount > 0 && (
                <Badge className="bg-white/20 text-white border border-white/20 ml-1">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>

            {/* Quick Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search company name, industry, or location..."
                value={localFilters.searchTerm || ''}
                onChange={(e) => updateFilters({ searchTerm: e.target.value || undefined })}
                className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
              />
            </div>
          </div>

          <div className="flex items-center gap-3">
            {/* Active Filters Count */}
            {activeFilterCount > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  {activeFilterCount} filter{activeFilterCount !== 1 ? 's' : ''} applied
                </span>
                <Button
                  onClick={onClearFilters}
                  className="text-gray-500 hover:text-red-600 bg-transparent border border-gray-200 hover:border-red-200 hover:bg-red-50 px-3 py-1 text-sm"
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Clear All
                </Button>
              </div>
            )}

            {/* Sort Controls */}
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Sort by:</span>
              </div>
              <div className="flex items-center gap-2">
                <Select 
                  value={localFilters.sortBy || 'updated_at'} 
                  onValueChange={(value) => updateFilters({ sortBy: value })}
                >
                  <SelectTrigger className="w-auto min-w-[200px] border-gray-200 bg-white shadow-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="max-h-[400px]">
                    {COMPANY_SORT_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4" />
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                {/* Sort Order Toggle */}
                <Button
                  onClick={() => updateFilters({ 
                    sortOrder: localFilters.sortOrder === 'asc' ? 'desc' : 'asc' 
                  })}
                  className={`p-2 rounded-lg border transition-all ${
                    localFilters.sortOrder === 'asc' 
                      ? 'bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100' 
                      : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                  }`}
                  title={`Sort ${localFilters.sortOrder === 'asc' ? 'Ascending' : 'Descending'}`}
                >
                  {localFilters.sortOrder === 'asc' ? (
                    <ArrowUp className="h-4 w-4" />
                  ) : (
                    <ArrowDown className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {activeFilterCount > 0 && (
          <div className="border-t border-gray-100 p-4 bg-gray-50/50">
            <div className="flex flex-wrap gap-2">
              {localFilters.searchTerm && (
                <Badge className="bg-blue-100 text-blue-800 border border-blue-200 px-3 py-1 text-sm">
                  <Search className="h-3 w-3 mr-1" />
                  Search: {localFilters.searchTerm}
                  <button
                    onClick={() => updateFilters({ searchTerm: undefined })}
                    className="ml-2 hover:text-red-600 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {localFilters.companyType?.map((type) => (
                <Badge key={type} className="bg-purple-100 text-purple-800 border border-purple-200 px-3 py-1 text-sm">
                  <Building2 className="h-3 w-3 mr-1" />
                  {type}
                  <button
                    onClick={() => updateFilters({ 
                      companyType: localFilters.companyType?.filter(t => t !== type) 
                    })}
                    className="ml-2 hover:text-red-600 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
              {(localFilters.fundSizeMin || localFilters.fundSizeMax) && (
                <Badge className="bg-emerald-100 text-emerald-800 border border-emerald-200 px-3 py-1 text-sm">
                  <DollarSign className="h-3 w-3 mr-1" />
                  Fund Size: {formatCurrencyFromMillions(localFilters.fundSizeMin) || '$0'} - {formatCurrencyFromMillions(localFilters.fundSizeMax) || '∞'}
                  <button
                    onClick={() => updateFilters({ fundSizeMin: undefined, fundSizeMax: undefined })}
                    className="ml-2 hover:text-red-600 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {localFilters.industry?.map((ind) => (
                <Badge key={ind} className="bg-orange-100 text-orange-800 border border-orange-200 px-3 py-1 text-sm">
                  <Building className="h-3 w-3 mr-1" />
                  {ind}
                  <button
                    onClick={() => updateFilters({ 
                      industry: localFilters.industry?.filter(i => i !== ind) 
                    })}
                    className="ml-2 hover:text-red-600 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
              {localFilters.companyState?.map((state) => (
                <Badge key={state} className="bg-teal-100 text-teal-800 border border-teal-200 px-3 py-1 text-sm">
                  <MapPin className="h-3 w-3 mr-1" />
                  {state}
                  <button
                    onClick={() => updateFilters({ 
                      companyState: localFilters.companyState?.filter(s => s !== state) 
                    })}
                    className="ml-2 hover:text-red-600 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Filter Panel */}
      <CompanyFilterPanel
        isOpen={isFilterPanelOpen}
        onClose={() => setIsFilterPanelOpen(false)}
        filters={localFilters}
        filterOptions={filterOptions}
        onFiltersChange={onFiltersChange}
        onClearFilters={onClearFilters}
        isLoading={isLoading}
      />
    </>
  )
} 