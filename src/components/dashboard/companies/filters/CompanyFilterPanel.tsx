'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { ReactMultiSelect } from '@/components/ui/react-multi-select'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  X, RotateCcw, Building2, DollarSign, BarChart3, Users, Home, Building,
  MapPin, Target, CheckCircle, Settings, Sparkles, Filter, ChevronRight,
  Factory, Briefcase, Calendar, Globe
} from 'lucide-react'
import { CompanyFilters, CompanyFilterOptions } from '@/types/company-filters'

interface CompanyFilterPanelProps {
  isOpen: boolean
  onClose: () => void
  filters: CompanyFilters
  filterOptions: CompanyFilterOptions
  onFiltersChange: (filters: CompanyFilters) => void
  onClearFilters: () => void
  isLoading?: boolean
}

export default function CompanyFilterPanel({
  isOpen,
  onClose,
  filters,
  filterOptions,
  onFiltersChange,
  onClearFilters,
  isLoading = false
}: CompanyFilterPanelProps) {
  
  const updateFilters = (newFilters: Partial<CompanyFilters>) => {
    const updatedFilters = { ...filters, ...newFilters, page: 1 }
    onFiltersChange(updatedFilters)
  }
  const getActiveFilterCount = () => {
    let count = 0
    if (filters.searchTerm) count++
    if (filters.companyType?.length) count++
    if (filters.fundSizeMin || filters.fundSizeMax) count++
    if (filters.aumMin || filters.aumMax) count++
    if (filters.industry?.length) count++
    if (filters.companyState?.length) count++
    if (filters.hasInvestmentCriteria !== undefined) count++
    return count
  }

  const activeFilterCount = getActiveFilterCount()

  return (
    <div className={`fixed top-0 right-0 h-full w-[600px] bg-white border-l border-gray-200 shadow-xl transform transition-transform duration-300 ease-in-out z-50 ${
      isOpen ? 'translate-x-0' : 'translate-x-full'
    }`}>
      <div className="flex flex-col h-full">
        {/* Panel Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50">
          <div className="flex items-center gap-4">
            <div className="relative">
              <div className="p-3 rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg">
                <Building2 className="h-6 w-6" />
              </div>
              {activeFilterCount > 0 && (
                <div className="absolute -top-2 -right-2 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center font-bold">
                  {activeFilterCount > 9 ? '9+' : activeFilterCount}
                </div>
              )}
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                Company Filters
                <Badge className="bg-blue-100 text-blue-700 border border-blue-200 ml-2">
                  Advanced
                </Badge>
              </h2>
              <p className="text-sm text-gray-600 mt-1 flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Filter companies by profile metrics and investment criteria
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            {activeFilterCount > 0 && (
              <Button
                onClick={onClearFilters}
                className="text-red-600 hover:text-red-700 hover:bg-red-50 border border-red-200 bg-white px-3 py-1 text-xs"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Clear All
              </Button>
            )}
            <Button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 bg-white border border-gray-200 p-2 hover:bg-gray-50 transition-colors"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* Panel Content - Scrollable */}
        <div className="flex-1 overflow-y-auto p-6 space-y-8">

          {/* Company Profile Section */}
          <Card className="border-0 shadow-sm bg-gradient-to-r from-blue-50 to-indigo-50">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-lg">
                <Building2 className="h-5 w-5 text-blue-600" />
                Company Profile
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Company Type */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <Factory className="h-4 w-4" />
                  Company Type
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.companyTypes || []).map(type => ({ value: type, label: type }))}
                  selected={filters.companyType || []}
                  onChange={(selected: string[]) => updateFilters({ companyType: selected })}
                  placeholder="Select company types..."
                  disabled={isLoading}
                />
              </div>
            </CardContent>
          </Card>

          {/* Continue with more sections... */}
        </div>

        {/* Panel Footer */}
        <div className="border-t border-gray-200 p-6 bg-gradient-to-r from-gray-50 to-blue-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-600">
                {activeFilterCount > 0 ? (
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4 text-blue-600" />
                    <span className="font-medium text-gray-700">
                      {activeFilterCount} Filter{activeFilterCount !== 1 ? 's' : ''} Active
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4 text-gray-400" />
                    <span>No filters applied - showing all companies</span>
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              {activeFilterCount > 0 && (
                <Button
                  onClick={onClearFilters}
                  className="text-gray-600 hover:text-red-600 bg-white border border-gray-200 hover:border-red-200 hover:bg-red-50 px-4 py-2 text-sm transition-colors"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reset All
                </Button>
              )}
              
              <Button
                onClick={onClose}
                className={`px-6 py-2 font-medium shadow-md hover:shadow-lg transition-all duration-200 ${
                  activeFilterCount > 0 
                    ? 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white' 
                    : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white'
                }`}
              >
                {activeFilterCount > 0 ? (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Apply {activeFilterCount} Filter{activeFilterCount !== 1 ? 's' : ''}
                  </>
                ) : (
                  <>
                    <ChevronRight className="h-4 w-4 mr-2" />
                    Close Panel
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 