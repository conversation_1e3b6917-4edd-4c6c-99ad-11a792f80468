'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  ChevronLeft, 
  ChevronRight, 
  Loader2, 
  AlertCircle,
  Target,
  Building2,
  MapPin,
  Calculator,
  TrendingUp,
  Plus,
  Check,
  X
} from 'lucide-react'
import { toast } from 'sonner'

interface InvestmentCriteriaData {
  investment_criteria_id: number;
  entity_type: string;
  entity_id: number;
  capital_position: string;
  minimum_deal_size?: number;
  maximum_deal_size?: number;
  country?: string[];
  region?: string[];
  state?: string[];
  city?: string[];
  property_types?: string[];
  property_subcategories?: string[];
  strategies?: string[];
  decision_making_process?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  
  // Relationship IDs to debt/equity records
  investment_criteria_debt_id?: number;
  investment_criteria_equity_id?: number;
  
  // Debt-specific fields (from joined debt table)
  debt_loan_type?: string;
  debt_loan_program?: string;
  debt_min_loan_term?: number;
  debt_max_loan_term?: number;
  debt_interest_rate?: number;
  debt_loan_to_value_min?: number;
  debt_loan_to_value_max?: number;
  debt_loan_to_cost_min?: number;
  debt_loan_to_cost_max?: number;
  debt_min_loan_dscr?: number;
  debt_max_loan_dscr?: number;
  debt_structured_loan_tranche?: string;
  debt_recourse_loan?: string;
  debt_closing_time?: number;
  debt_program_overview?: string;
  
  // Equity-specific fields (from joined equity table)
  equity_target_return?: number;
  equity_minimum_internal_rate_of_return?: number;
  equity_min_hold_period_years?: number;
  equity_max_hold_period_years?: number;
  equity_minimum_yield_on_cost?: number;
  equity_minimum_equity_multiple?: number;
  equity_ownership_requirement?: string;
  equity_program_overview?: string;
}

interface GroupedCriteria {
  [capitalPosition: string]: InvestmentCriteriaData[];
}

interface CompanyInvestmentCriteriaSliderProps {
  companyId: string | number
  companyName?: string
  onAttachToContact?: (selectedCriteria: InvestmentCriteriaData[]) => void
  className?: string
}

export default function CompanyInvestmentCriteriaSlider({ 
  companyId, 
  companyName,
  onAttachToContact,
  className = ''
}: CompanyInvestmentCriteriaSliderProps) {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [groupedCriteria, setGroupedCriteria] = useState<GroupedCriteria>({})
  const [totalCriteria, setTotalCriteria] = useState(0)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [selectedCriteria, setSelectedCriteria] = useState<Set<number>>(new Set())
  const [isAttaching, setIsAttaching] = useState(false)

  const fetchInvestmentCriteria = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/investment-criteria/entity/Company/${companyId}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch investment criteria: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        setGroupedCriteria(result.data.groupedCriteria || {})
        setTotalCriteria(result.data.totalCriteria || 0)
        
        // Set current index to 0 if we have criteria
        if (result.data.totalCriteria > 0) {
          setCurrentIndex(0)
        }
      } else {
        throw new Error(result.error || 'Unknown error occurred')
      }
    } catch (err) {
      console.error('Error fetching investment criteria:', err)
      setError(err instanceof Error ? err.message : 'Failed to load investment criteria')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchInvestmentCriteria()
  }, [companyId])

  // Flatten all criteria for navigation
  const allCriteria: InvestmentCriteriaData[] = []
  Object.entries(groupedCriteria).forEach(([group, criteriaList]) => {
    criteriaList.forEach(criteria => {
      allCriteria.push(criteria)
    })
  })

  const goToPrevious = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex > 0 ? prevIndex - 1 : allCriteria.length - 1
    )
  }

  const goToNext = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex < allCriteria.length - 1 ? prevIndex + 1 : 0
    )
  }

  // Generate IC label (capital position + loan type + deal size)
  const generateICLabel = (criteria: InvestmentCriteriaData) => {
    const parts: string[] = []
    
    // Add capital position
    if (criteria.capital_position) {
      parts.push(criteria.capital_position)
    }
    
    // Add loan type (from debt fields)
    if (criteria.debt_loan_type) {
      parts.push(criteria.debt_loan_type)
    }
    
    // Add deal size range
    if (criteria.minimum_deal_size || criteria.maximum_deal_size) {
      const formatCurrency = (value: number) => {
        if (value >= 1000000) {
          return `$${(value / 1000000).toFixed(1)}M`
        }
        if (value >= 1000) {
          return `$${(value / 1000).toFixed(0)}K`
        }
        return `$${value.toLocaleString()}`
      }
      
      let dealSizeStr = ''
      if (criteria.minimum_deal_size && criteria.maximum_deal_size) {
        dealSizeStr = `${formatCurrency(criteria.minimum_deal_size)} - ${formatCurrency(criteria.maximum_deal_size)}`
      } else if (criteria.minimum_deal_size) {
        dealSizeStr = `${formatCurrency(criteria.minimum_deal_size)}+`
      } else if (criteria.maximum_deal_size) {
        dealSizeStr = `up to ${formatCurrency(criteria.maximum_deal_size)}`
      }
      
      if (dealSizeStr) {
        parts.push(dealSizeStr)
      }
    }
    
    return parts.join(' | ') || 'Investment Criteria'
  }

  // Format currency display
  const formatCurrency = (value?: number) => {
    if (!value) return 'Not specified'
    if (value >= 1000000000) {
      return `$${(value / 1000000000).toFixed(1)}B`
    }
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`
    }
    return `$${value.toLocaleString()}`
  }

  // Format array display
  const formatArray = (arr?: string[]) => {
    if (!arr || arr.length === 0) return <span className="text-slate-400">Not specified</span>
    return (
      <div className="flex flex-wrap gap-2">
        {arr.map((item, index) => (
          <Badge key={index} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            {item}
          </Badge>
        ))}
      </div>
    )
  }

  const getCapitalPositionColor = (position: string) => {
    switch (position.toLowerCase()) {
      case 'senior debt':
      case 'stretch senior':
        return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'mezzanine':
        return 'bg-purple-50 text-purple-700 border-purple-200'
      case 'preferred equity':
      case 'common equity':
      case 'general partner (gp)':
      case 'limited partner (lp)':
      case 'joint venture (jv)':
      case 'co-gp':
        return 'bg-green-50 text-green-700 border-green-200'
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  const handleCriteriaSelection = (criteriaId: number, checked: boolean) => {
    const newSelected = new Set(selectedCriteria)
    if (checked) {
      newSelected.add(criteriaId)
    } else {
      newSelected.delete(criteriaId)
    }
    setSelectedCriteria(newSelected)
  }

  const handleAttachToContact = async () => {
    if (selectedCriteria.size === 0) {
      toast.error('Please select at least one investment criteria to attach')
      return
    }

    const selectedCriteriaData = allCriteria.filter(criteria => 
      selectedCriteria.has(criteria.investment_criteria_id)
    )

    if (onAttachToContact) {
      setIsAttaching(true)
      try {
        await onAttachToContact(selectedCriteriaData)
        toast.success(`Attached ${selectedCriteria.size} investment criteria to contact`)
        setSelectedCriteria(new Set()) // Clear selection
      } catch (error) {
        toast.error('Failed to attach investment criteria')
      } finally {
        setIsAttaching(false)
      }
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
          <p className="text-sm text-gray-600">Loading investment criteria...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Card className="bg-white shadow-sm">
        <CardContent className="pt-6">
          <div className="text-center py-6">
            <AlertCircle className="h-8 w-8 mx-auto text-red-500 mb-4" />
            <h3 className="text-sm font-medium text-gray-900 mb-2">
              Error Loading Investment Criteria
            </h3>
            <p className="text-red-500 text-sm mb-4">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (totalCriteria === 0) {
    return (
      <Card className="bg-white shadow-sm">
        <CardContent className="pt-6">
          <div className="text-center py-6">
            <Target className="h-8 w-8 mx-auto text-gray-400 mb-4" />
            <h3 className="text-sm font-medium text-gray-700 mb-2">
              No Investment Criteria Found
            </h3>
            <p className="text-gray-500 text-sm max-w-md mx-auto">
              No investment criteria records have been found for {companyName || 'this company'}.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const currentCriteria = allCriteria[currentIndex]

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header with selection controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Building2 className="h-5 w-5 text-blue-600" />
          <div>
            <h3 className="text-lg font-medium">Company Investment Criteria</h3>
            <p className="text-sm text-slate-500">
              Select criteria to attach to contact ({selectedCriteria.size} selected)
            </p>
          </div>
        </div>
        
        {selectedCriteria.size > 0 && onAttachToContact && (
          <Button 
            onClick={handleAttachToContact}
            disabled={isAttaching}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isAttaching ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Attaching...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Attach Selected ({selectedCriteria.size})
              </>
            )}
          </Button>
        )}
      </div>

      {/* Navigation Header */}
      {allCriteria.length > 1 && (
        <Card className="bg-white shadow-sm">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={selectedCriteria.has(currentCriteria.investment_criteria_id)}
                    onCheckedChange={(checked) => 
                      handleCriteriaSelection(currentCriteria.investment_criteria_id, checked as boolean)
                    }
                  />
                  <div>
                    <CardTitle className="text-lg font-medium">
                      {generateICLabel(currentCriteria)}
                    </CardTitle>
                    <p className="text-sm text-slate-500">
                      Showing {currentIndex + 1} of {allCriteria.length} criteria records
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevious}
                  disabled={allCriteria.length <= 1}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>
                
                <div className="flex items-center gap-1 px-3">
                  {allCriteria.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentIndex(index)}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index === currentIndex 
                          ? 'bg-blue-600' 
                          : 'bg-gray-300 hover:bg-gray-400'
                      }`}
                    />
                  ))}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNext}
                  disabled={allCriteria.length <= 1}
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>
      )}

      {/* Single criteria view for when there's only one */}
      {allCriteria.length === 1 && (
        <div className="flex items-center gap-2 mb-4">
          <Checkbox
            checked={selectedCriteria.has(currentCriteria.investment_criteria_id)}
            onCheckedChange={(checked) =>
              handleCriteriaSelection(currentCriteria.investment_criteria_id, checked as boolean)
            }
          />
          <span className="text-sm font-medium">{generateICLabel(currentCriteria)}</span>
        </div>
      )}

      {/* Current Criteria Detail View */}
      {currentCriteria && (
        <div className="bg-white rounded-lg space-y-6 p-6">
          {/* Basic Information Display */}
          <Card className="shadow-sm border-0 bg-white/70">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3">
                <Target className="h-5 w-5 text-purple-600" />
                <span>Basic Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="text-sm font-medium">Capital Position</span>
                  <Badge className={`${getCapitalPositionColor(currentCriteria.capital_position)} mt-1`}>
                    {currentCriteria.capital_position}
                  </Badge>
                </div>
                <div>
                  <span className="text-sm font-medium">Deal Size Range</span>
                  <p className="text-sm mt-1">
                    {currentCriteria.minimum_deal_size && currentCriteria.maximum_deal_size
                      ? `${formatCurrency(currentCriteria.minimum_deal_size)} - ${formatCurrency(currentCriteria.maximum_deal_size)}`
                      : currentCriteria.minimum_deal_size
                      ? `${formatCurrency(currentCriteria.minimum_deal_size)}+`
                      : currentCriteria.maximum_deal_size
                      ? `Up to ${formatCurrency(currentCriteria.maximum_deal_size)}`
                      : 'Not specified'}
                  </p>
                </div>
              </div>

              {currentCriteria.decision_making_process && (
                <div>
                  <span className="text-sm font-medium">Decision Making Process</span>
                  <p className="text-sm mt-1 bg-gray-50 p-3 rounded">{currentCriteria.decision_making_process}</p>
                </div>
              )}

              {currentCriteria.notes && (
                <div>
                  <span className="text-sm font-medium">Notes</span>
                  <p className="text-sm mt-1 bg-gray-50 p-3 rounded">{currentCriteria.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Geographic Focus Display */}
          <Card className="shadow-sm border-0 bg-white/70">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3">
                <MapPin className="h-5 w-5 text-red-600" />
                <span>Geographic Focus</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {currentCriteria.country && currentCriteria.country.length > 0 && (
                  <div>
                    <span className="text-sm font-medium">Countries</span>
                    <div className="mt-1">{formatArray(currentCriteria.country)}</div>
                  </div>
                )}
                {currentCriteria.region && currentCriteria.region.length > 0 && (
                  <div>
                    <span className="text-sm font-medium">Regions</span>
                    <div className="mt-1">{formatArray(currentCriteria.region)}</div>
                  </div>
                )}
                {currentCriteria.state && currentCriteria.state.length > 0 && (
                  <div>
                    <span className="text-sm font-medium">States</span>
                    <div className="mt-1">{formatArray(currentCriteria.state)}</div>
                  </div>
                )}
                {currentCriteria.city && currentCriteria.city.length > 0 && (
                  <div>
                    <span className="text-sm font-medium">Cities</span>
                    <div className="mt-1">{formatArray(currentCriteria.city)}</div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Property & Investment Focus Display */}
          <Card className="shadow-sm border-0 bg-white/70">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3">
                <Building2 className="h-5 w-5 text-orange-600" />
                <span>Property & Investment Focus</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {currentCriteria.property_types && currentCriteria.property_types.length > 0 && (
                  <div>
                    <span className="text-sm font-medium">Property Types</span>
                    <div className="mt-1">{formatArray(currentCriteria.property_types)}</div>
                  </div>
                )}
                {currentCriteria.property_subcategories && currentCriteria.property_subcategories.length > 0 && (
                  <div>
                    <span className="text-sm font-medium">Property Subcategories</span>
                    <div className="mt-1">{formatArray(currentCriteria.property_subcategories)}</div>
                  </div>
                )}
                {currentCriteria.strategies && currentCriteria.strategies.length > 0 && (
                  <div>
                    <span className="text-sm font-medium">Investment Strategies</span>
                    <div className="mt-1">{formatArray(currentCriteria.strategies)}</div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Debt-specific details */}
          {currentCriteria.investment_criteria_debt_id && (
            <Card className="shadow-sm border-0 bg-blue-50/70">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3">
                  <Calculator className="h-5 w-5 text-blue-600" />
                  <span className="text-blue-800">Debt Details</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {currentCriteria.debt_loan_type && (
                    <div>
                      <span className="text-sm font-medium text-blue-700">Loan Type</span>
                      <p className="text-sm mt-1 text-blue-800">{currentCriteria.debt_loan_type}</p>
                    </div>
                  )}
                  {currentCriteria.debt_loan_program && (
                    <div>
                      <span className="text-sm font-medium text-blue-700">Loan Program</span>
                      <p className="text-sm mt-1 text-blue-800">{currentCriteria.debt_loan_program}</p>
                    </div>
                  )}
                  {(currentCriteria.debt_min_loan_term || currentCriteria.debt_max_loan_term) && (
                    <div>
                      <span className="text-sm font-medium text-blue-700">Loan Term</span>
                      <p className="text-sm mt-1 text-blue-800">
                        {currentCriteria.debt_min_loan_term && currentCriteria.debt_max_loan_term
                          ? `${currentCriteria.debt_min_loan_term} - ${currentCriteria.debt_max_loan_term} months`
                          : currentCriteria.debt_min_loan_term
                          ? `${currentCriteria.debt_min_loan_term}+ months`
                          : `Up to ${currentCriteria.debt_max_loan_term} months`}
                      </p>
                    </div>
                  )}
                  {(currentCriteria.debt_loan_to_value_min || currentCriteria.debt_loan_to_value_max) && (
                    <div>
                      <span className="text-sm font-medium text-blue-700">Loan to Value</span>
                      <p className="text-sm mt-1 text-blue-800">
                        {currentCriteria.debt_loan_to_value_min && currentCriteria.debt_loan_to_value_max
                          ? `${currentCriteria.debt_loan_to_value_min}% - ${currentCriteria.debt_loan_to_value_max}%`
                          : currentCriteria.debt_loan_to_value_min
                          ? `${currentCriteria.debt_loan_to_value_min}%+`
                          : `Up to ${currentCriteria.debt_loan_to_value_max}%`}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Equity-specific details */}
          {currentCriteria.investment_criteria_equity_id && (
            <Card className="shadow-sm border-0 bg-green-50/70">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  <span className="text-green-800">Equity Details</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {currentCriteria.equity_target_return && (
                    <div>
                      <span className="text-sm font-medium text-green-700">Target Return</span>
                      <p className="text-sm mt-1 text-green-800">{currentCriteria.equity_target_return}%</p>
                    </div>
                  )}
                  {currentCriteria.equity_minimum_internal_rate_of_return && (
                    <div>
                      <span className="text-sm font-medium text-green-700">Minimum IRR</span>
                      <p className="text-sm mt-1 text-green-800">{currentCriteria.equity_minimum_internal_rate_of_return}%</p>
                    </div>
                  )}
                  {(currentCriteria.equity_min_hold_period_years || currentCriteria.equity_max_hold_period_years) && (
                    <div>
                      <span className="text-sm font-medium text-green-700">Hold Period</span>
                      <p className="text-sm mt-1 text-green-800">
                        {currentCriteria.equity_min_hold_period_years && currentCriteria.equity_max_hold_period_years
                          ? `${currentCriteria.equity_min_hold_period_years} - ${currentCriteria.equity_max_hold_period_years} years`
                          : currentCriteria.equity_min_hold_period_years
                          ? `${currentCriteria.equity_min_hold_period_years}+ years`
                          : `Up to ${currentCriteria.equity_max_hold_period_years} years`}
                      </p>
                    </div>
                  )}
                  {currentCriteria.equity_minimum_equity_multiple && (
                    <div>
                      <span className="text-sm font-medium text-green-700">Minimum Equity Multiple</span>
                      <p className="text-sm mt-1 text-green-800">{currentCriteria.equity_minimum_equity_multiple}x</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  )
}
