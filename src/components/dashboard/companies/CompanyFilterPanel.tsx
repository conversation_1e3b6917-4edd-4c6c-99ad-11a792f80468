'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { ReactMultiSelect } from '@/components/ui/react-multi-select'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { 
  X, Building, Factory, Crown, DollarSign, Building2, Home, Calendar, Users, 
  MapPin, Target, TrendingUp, Percent, BarChart3, Activity, Settings, 
  CheckCircle, AlertCircle, Clock, SlidersHorizontal, Globe, Landmark
} from 'lucide-react'
import { CompanyFilters, CompanyFilterOptions } from '@/types/company-filters'

interface CompanyFilterPanelProps {
  isOpen: boolean
  onClose: () => void
  filters: CompanyFilters
  filterOptions: CompanyFilterOptions
  onFiltersChange: (updates: Partial<CompanyFilters>) => void
  isLoading: boolean
  activeFilterCount: number
  formatCurrencyFromMillions: (millions: number) => string
}

export default function CompanyFilterPanel({
  isOpen,
  onClose,
  filters,
  filterOptions,
  onFiltersChange,
  isLoading,
  activeFilterCount,
  formatCurrencyFromMillions
}: CompanyFilterPanelProps) {
  // Hierarchical filtering functions
  const getAvailablePropertySubcategories = (selectedPropertyTypes?: string[]) => {
    const propertyTypesToUse = selectedPropertyTypes || filters.propertyTypes
    
    if (!filterOptions.hierarchicalMappings?.['Property Type']?.hierarchical || !propertyTypesToUse?.length) {
      return filterOptions.propertySubcategories || []
    }

    const availableSubcategories = new Set<string>()
    const hierarchicalMappings = filterOptions.hierarchicalMappings
    
    propertyTypesToUse.forEach(selectedPropertyType => {
      const hierarchicalData = hierarchicalMappings['Property Type']?.hierarchical?.[selectedPropertyType]
      if (hierarchicalData?.children?.['Property Subproperty Type']) {
        hierarchicalData.children['Property Subproperty Type'].forEach(subcategory => {
          availableSubcategories.add(subcategory)
        })
      }
    })

    return Array.from(availableSubcategories).sort()
  }

  if (!isOpen) return null

  return (
    <div className={`fixed top-0 right-0 h-full w-[500px] bg-white border-l border-gray-200 shadow-xl transform transition-transform duration-300 ease-in-out z-50 ${
      isOpen ? 'translate-x-0' : 'translate-x-full'
    }`}>
      <div className="flex flex-col h-full">
        {/* Enhanced Panel Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50">
          <div className="flex items-center gap-4">
            <div className="relative">
              <div className="p-3 rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg">
                <SlidersHorizontal className="h-6 w-6" />
              </div>
              {activeFilterCount > 0 && (
                <div className="absolute -top-2 -right-2 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center font-bold">
                  {activeFilterCount > 9 ? '9+' : activeFilterCount}
                </div>
              )}
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                Company Filters
                <Badge className="bg-blue-100 text-blue-700 border border-blue-200 ml-2">
                  Advanced
                </Badge>
              </h2>
              <p className="text-sm text-gray-600 mt-1 flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Comprehensive filtering across company profiles, metrics, and investment criteria
              </p>
            </div>
          </div>
          <Button
            onClick={onClose}
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-white/60 rounded-full"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Scrollable Filter Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-8">

          {/* Company Profile Section */}
          <Card className="border-0 shadow-sm bg-gradient-to-r from-blue-50 to-indigo-50">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-lg">
                <Building className="h-5 w-5 text-blue-600" />
                Company Profile
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">Filter by company characteristics and metrics</p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Company Type */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <Factory className="h-4 w-4" />
                  Company Type
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.companyTypes || []).map(type => ({ value: type, label: type }))}
                  selected={filters.companyType || []}
                  onChange={(selected: string[]) => onFiltersChange({ companyType: selected })}
                  placeholder="Select company types..."
                  disabled={isLoading}
                />
              </div>

              {/* Capital Position */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <Crown className="h-4 w-4" />
                  Capital Position
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.capitalPositions || []).map(pos => ({ value: pos, label: pos }))}
                  selected={filters.capitalPosition || []}
                  onChange={(selected: string[]) => onFiltersChange({ capitalPosition: selected })}
                  placeholder="Select capital positions..."
                  disabled={isLoading}
                />
              </div>

              {/* Company Industry */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  Company Industry
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.companyIndustries || []).map(industry => ({ value: industry, label: industry }))}
                  selected={filters.companyIndustry || []}
                  onChange={(selected: string[]) => onFiltersChange({ companyIndustry: selected })}
                  placeholder="Select industries..."
                  disabled={isLoading}
                />
              </div>

              {/* Business Model */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">
                  Business Model
                </Label>
                <Input
                  placeholder="Enter business model..."
                  value={filters.businessModel || ''}
                  onChange={(e) => onFiltersChange({ businessModel: e.target.value || undefined })}
                  disabled={isLoading}
                  className="bg-white border-gray-200"
                />
              </div>
            </CardContent>
          </Card>

          {/* Financial Metrics Section */}
          <Card className="border-0 shadow-sm bg-gradient-to-r from-green-50 to-emerald-50">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-lg">
                <DollarSign className="h-5 w-5 text-green-600" />
                Financial Metrics
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">Filter by fund size, AUM, and financial data</p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Fund Size Range */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">
                  Fund Size Range (Enter values in thousands)
                </Label>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-xs text-gray-500">
                      Minimum 
                      {filters.fundSizeMin && (
                        <span className="ml-2 text-xs text-green-600 font-medium">
                          = {formatCurrencyFromMillions(filters.fundSizeMin)}
                        </span>
                      )}
                    </Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        type="number"
                        placeholder="0 (e.g., 500 for $500M)"
                        value={filters.fundSizeMin || ''}
                        onChange={(e) => onFiltersChange({ 
                          fundSizeMin: e.target.value ? parseFloat(e.target.value) : undefined 
                        })}
                        disabled={isLoading}
                        className="pl-10 bg-white border-gray-200"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-xs text-gray-500">
                      Maximum
                      {filters.fundSizeMax && (
                        <span className="ml-2 text-xs text-green-600 font-medium">
                          = {formatCurrencyFromMillions(filters.fundSizeMax)}
                        </span>
                      )}
                    </Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        type="number"
                        placeholder="5000 (e.g., 5000 for $5B)"
                        value={filters.fundSizeMax || ''}
                        onChange={(e) => onFiltersChange({ 
                          fundSizeMax: e.target.value ? parseFloat(e.target.value) : undefined 
                        })}
                        disabled={isLoading}
                        className="pl-10 bg-white border-gray-200"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* AUM Range */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">
                  Assets Under Management (AUM) Range (Enter values in thousands)
                </Label>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-xs text-gray-500">
                      Minimum AUM
                      {filters.aumMin && (
                        <span className="ml-2 text-xs text-green-600 font-medium">
                          = {formatCurrencyFromMillions(filters.aumMin)}
                        </span>
                      )}
                    </Label>
                    <div className="relative">
                      <Landmark className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        type="number"
                        placeholder="0 (e.g., 1000 for $1B)"
                        value={filters.aumMin || ''}
                        onChange={(e) => onFiltersChange({ 
                          aumMin: e.target.value ? parseFloat(e.target.value) : undefined 
                        })}
                        disabled={isLoading}
                        className="pl-10 bg-white border-gray-200"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-xs text-gray-500">
                      Maximum AUM
                      {filters.aumMax && (
                        <span className="ml-2 text-xs text-green-600 font-medium">
                          = {formatCurrencyFromMillions(filters.aumMax)}
                        </span>
                      )}
                    </Label>
                    <div className="relative">
                      <Landmark className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        type="number"
                        placeholder="10000 (e.g., 10000 for $10B)"
                        value={filters.aumMax || ''}
                        onChange={(e) => onFiltersChange({ 
                          aumMax: e.target.value ? parseFloat(e.target.value) : undefined 
                        })}
                        disabled={isLoading}
                        className="pl-10 bg-white border-gray-200"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Company Size & Scale */}
          <Card className="border-0 shadow-sm bg-gradient-to-r from-purple-50 to-pink-50">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-lg">
                <Building2 className="h-5 w-5 text-purple-600" />
                Company Size & Scale
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Number of Properties */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <Home className="h-4 w-4" />
                  Number of Properties
                </Label>
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    type="number"
                    placeholder="Min Properties"
                    value={filters.numberOfPropertiesMin || ''}
                    onChange={(e) => onFiltersChange({ 
                      numberOfPropertiesMin: e.target.value ? parseInt(e.target.value) : undefined 
                    })}
                    disabled={isLoading}
                    className="bg-white border-gray-200"
                  />
                  <Input
                    type="number"
                    placeholder="Max Properties"
                    value={filters.numberOfPropertiesMax || ''}
                    onChange={(e) => onFiltersChange({ 
                      numberOfPropertiesMax: e.target.value ? parseInt(e.target.value) : undefined 
                    })}
                    disabled={isLoading}
                    className="bg-white border-gray-200"
                  />
                </div>
              </div>

              {/* Number of Offices */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Number of Offices
                </Label>
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    type="number"
                    placeholder="Min Offices"
                    value={filters.numberOfOfficesMin || ''}
                    onChange={(e) => onFiltersChange({ 
                      numberOfOfficesMin: e.target.value ? parseInt(e.target.value) : undefined 
                    })}
                    disabled={isLoading}
                    className="bg-white border-gray-200"
                  />
                  <Input
                    type="number"
                    placeholder="Max Offices"
                    value={filters.numberOfOfficesMax || ''}
                    onChange={(e) => onFiltersChange({ 
                      numberOfOfficesMax: e.target.value ? parseInt(e.target.value) : undefined 
                    })}
                    disabled={isLoading}
                    className="bg-white border-gray-200"
                  />
                </div>
              </div>

              {/* Number of Employees */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Number of Employees
                </Label>
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    type="number"
                    placeholder="Min Employees"
                    value={filters.numberOfEmployeesMin || ''}
                    onChange={(e) => onFiltersChange({ 
                      numberOfEmployeesMin: e.target.value ? parseInt(e.target.value) : undefined 
                    })}
                    disabled={isLoading}
                    className="bg-white border-gray-200"
                  />
                  <Input
                    type="number"
                    placeholder="Max Employees"
                    value={filters.numberOfEmployeesMax || ''}
                    onChange={(e) => onFiltersChange({ 
                      numberOfEmployeesMax: e.target.value ? parseInt(e.target.value) : undefined 
                    })}
                    disabled={isLoading}
                    className="bg-white border-gray-200"
                  />
                </div>
              </div>

              {/* Founded Year */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Founded Year
                </Label>
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    type="number"
                    placeholder="From Year (e.g., 1990)"
                    value={filters.foundedYearMin || ''}
                    onChange={(e) => onFiltersChange({ 
                      foundedYearMin: e.target.value ? parseInt(e.target.value) : undefined 
                    })}
                    disabled={isLoading}
                    className="bg-white border-gray-200"
                  />
                  <Input
                    type="number"
                    placeholder="To Year (e.g., 2024)"
                    value={filters.foundedYearMax || ''}
                    onChange={(e) => onFiltersChange({ 
                      foundedYearMax: e.target.value ? parseInt(e.target.value) : undefined 
                    })}
                    disabled={isLoading}
                    className="bg-white border-gray-200"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Geographic Focus */}
          <Card className="border-0 shadow-sm bg-gradient-to-r from-cyan-50 to-blue-50">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-lg">
                <MapPin className="h-5 w-5 text-cyan-600" />
                Geographic Focus
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Headquarters */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <Home className="h-4 w-4" />
                  Headquarters
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.headquarters || []).map(hq => ({ value: hq, label: hq }))}
                  selected={filters.headquarters || []}
                  onChange={(selected: string[]) => onFiltersChange({ headquarters: selected })}
                  placeholder="Select headquarters locations..."
                  disabled={isLoading}
                />
              </div>

              {/* Company States */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  States
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.states || []).map(state => ({ value: state, label: state }))}
                  selected={filters.companyState || []}
                  onChange={(selected: string[]) => onFiltersChange({ companyState: selected })}
                  placeholder="Select states..."
                  disabled={isLoading}
                />
              </div>

              {/* Geographic Focus Areas */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  Geographic Focus Areas
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.geographicFocus || []).map(geo => ({ value: geo, label: geo }))}
                  selected={filters.geographicFocus || []}
                  onChange={(selected: string[]) => onFiltersChange({ geographicFocus: selected })}
                  placeholder="Select geographic focus areas..."
                  disabled={isLoading}
                />
              </div>
            </CardContent>
          </Card>

          {/* Property & Asset Structure */}
          <Card className="border-0 shadow-sm bg-gradient-to-r from-orange-50 to-yellow-50">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-lg">
                <Building2 className="h-5 w-5 text-orange-600" />
                Property & Asset Structure
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Property Types */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">
                  Property Types
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.propertyTypes || []).map(type => ({ value: type, label: type }))}
                  selected={filters.propertyTypes || []}
                  onChange={(selected: string[]) => onFiltersChange({ 
                    propertyTypes: selected,
                    // Clear property subcategories when property types change
                    propertySubcategories: filters.propertySubcategories?.filter(sub => 
                      getAvailablePropertySubcategories(selected).includes(sub)
                    ) || undefined
                  })}
                  placeholder="Select property types..."
                  disabled={isLoading}
                />
              </div>

              {/* Property Subcategories - Hierarchical */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <Home className="h-4 w-4" />
                  Property Subcategories
                  {filters.propertyTypes?.length ? (
                    <Badge variant="secondary" className="text-xs">
                      {getAvailablePropertySubcategories().length} available
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-xs">
                      Select property types first
                    </Badge>
                  )}
                </Label>
                <ReactMultiSelect
                  options={getAvailablePropertySubcategories().map(type => ({ value: type, label: type }))}
                  selected={filters.propertySubcategories?.filter(sub => 
                    getAvailablePropertySubcategories().includes(sub)
                  ) || []}
                  onChange={(selected: string[]) => onFiltersChange({ propertySubcategories: selected })}
                  placeholder={filters.propertyTypes?.length ? "Select property subcategories..." : "Select property types first"}
                  disabled={isLoading || !filters.propertyTypes?.length}
                />
              </div>

              {/* Asset Types */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">
                  Asset Types
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.assetTypes || []).map(type => ({ value: type, label: type }))}
                  selected={filters.assetTypes || []}
                  onChange={(selected: string[]) => onFiltersChange({ assetTypes: selected })}
                  placeholder="Select asset types..."
                  disabled={isLoading}
                />
              </div>

              {/* Deal Structure */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">
                  Deal Structure
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.dealStructures || []).map(structure => ({ value: structure, label: structure }))}
                  selected={filters.dealStructure || []}
                  onChange={(selected: string[]) => onFiltersChange({ dealStructure: selected })}
                  placeholder="Select deal structures..."
                  disabled={isLoading}
                />
              </div>

              {/* Investment Focus */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Investment Focus
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.investmentFocus || []).map(focus => ({ value: focus, label: focus }))}
                  selected={filters.investmentFocus || []}
                  onChange={(selected: string[]) => onFiltersChange({ investmentFocus: selected })}
                  placeholder="Select investment focus areas..."
                  disabled={isLoading}
                />
              </div>
            </CardContent>
          </Card>

          {/* Investment Criteria Relationships */}
          <Card className="border-0 shadow-sm bg-gradient-to-r from-emerald-50 to-teal-50">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-lg">
                <Target className="h-5 w-5 text-emerald-600" />
                Investment Criteria Filters
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">Filter based on related investment criteria</p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Has Investment Criteria */}
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-gray-700">
                  Has Investment Criteria
                </Label>
                <Switch
                  checked={filters.hasInvestmentCriteria === true}
                  onCheckedChange={(checked) => onFiltersChange({ 
                    hasInvestmentCriteria: checked ? true : undefined 
                  })}
                  disabled={isLoading}
                />
              </div>

              {/* Target Return Range */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Target Return Range (%)
                </Label>
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    type="number"
                    step="0.1"
                    placeholder="Min Return %"
                    value={filters.targetReturnMin || ''}
                    onChange={(e) => onFiltersChange({ 
                      targetReturnMin: e.target.value ? parseFloat(e.target.value) : undefined 
                    })}
                    disabled={isLoading}
                    className="bg-white border-gray-200"
                  />
                  <Input
                    type="number"
                    step="0.1"
                    placeholder="Max Return %"
                    value={filters.targetReturnMax || ''}
                    onChange={(e) => onFiltersChange({ 
                      targetReturnMax: e.target.value ? parseFloat(e.target.value) : undefined 
                    })}
                    disabled={isLoading}
                    className="bg-white border-gray-200"
                  />
                </div>
              </div>

              {/* Criteria Property Types */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">
                  Investment Criteria Property Types
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.criteriaPropertyTypes || []).map(type => ({ value: type, label: type }))}
                  selected={filters.criteriaPropertyTypes || []}
                  onChange={(selected: string[]) => onFiltersChange({ criteriaPropertyTypes: selected })}
                  placeholder="Select property types from criteria..."
                  disabled={isLoading}
                />
              </div>

              {/* Criteria Strategies */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">
                  Investment Strategies
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.criteriaStrategies || []).map(strategy => ({ value: strategy, label: strategy }))}
                  selected={filters.criteriaStrategies || []}
                  onChange={(selected: string[]) => onFiltersChange({ criteriaStrategies: selected })}
                  placeholder="Select investment strategies..."
                  disabled={isLoading}
                />
              </div>
            </CardContent>
          </Card>

          {/* Processing Status */}
          <Card className="border-0 shadow-sm bg-gradient-to-r from-gray-50 to-slate-50">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-lg">
                <Settings className="h-5 w-5 text-gray-600" />
                Processing Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Processing State */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">
                  Processing State
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.processingStates || []).map(state => ({ value: state, label: state }))}
                  selected={filters.processingState || []}
                  onChange={(selected: string[]) => onFiltersChange({ processingState: selected })}
                  placeholder="Select processing states..."
                  disabled={isLoading}
                />
              </div>

              {/* Website Scraping Status */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">
                  Website Scraping Status
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.websiteScrapingStatuses || []).map(status => ({ value: status, label: status }))}
                  selected={filters.websiteScrapingStatus || []}
                  onChange={(selected: string[]) => onFiltersChange({ websiteScrapingStatus: selected })}
                  placeholder="Select scraping statuses..."
                  disabled={isLoading}
                />
              </div>

              {/* Company Overview Status */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">
                  Company Overview Status
                </Label>
                <ReactMultiSelect
                  options={(filterOptions.companyOverviewStatuses || []).map(status => ({ value: status, label: status }))}
                  selected={filters.companyOverviewStatus || []}
                  onChange={(selected: string[]) => onFiltersChange({ companyOverviewStatus: selected })}
                  placeholder="Select overview statuses..."
                  disabled={isLoading}
                />
              </div>

              {/* Processed/Extracted Toggle */}
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium text-gray-700">
                    Processed
                  </Label>
                  <Switch
                    checked={filters.processed === true}
                    onCheckedChange={(checked) => onFiltersChange({ 
                      processed: checked ? true : undefined 
                    })}
                    disabled={isLoading}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium text-gray-700">
                    Extracted
                  </Label>
                  <Switch
                    checked={filters.extracted === true}
                    onCheckedChange={(checked) => onFiltersChange({ 
                      extracted: checked ? true : undefined 
                    })}
                    disabled={isLoading}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Panel Footer */}
        <div className="border-t border-gray-200 p-6 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {activeFilterCount > 0 ? (
                <span className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  {activeFilterCount} active filter{activeFilterCount !== 1 ? 's' : ''}
                </span>
              ) : (
                <span className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-gray-400" />
                  No filters applied
                </span>
              )}
            </div>
            <Button
              onClick={onClose}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6"
            >
              Apply Filters
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
} 