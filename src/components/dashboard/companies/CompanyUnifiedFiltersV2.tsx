"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ReactMultiSelect } from '@/components/ui/react-multi-select';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { 
  X, Filter, RotateCcw, Search, SlidersHorizontal, ChevronRight, Sparkles,
  Building, DollarSign, MapPin, Settings, BarChart3, TrendingUp, Building2,
  Target, Briefcase, Calculator, Calendar, Users, Globe, ArrowUp, ArrowDown, 
  Clock, Banknote, Percent, Timer, LineChart, Activity, PieChart, User, UserCheck,
  Mail, CheckCircle, AlertCircle, MessageSquare, Send, Loader2, Brain, Eye,
  Database, Table, Shield, Minus, Plus, Eye as EyeIcon, EyeOff, Factory,
  TwitterIcon, Facebook, Instagram, Youtube, Phone, MapPinIcon, FileText,
  Briefcase as BriefcaseIcon, TrendingDown, Network, HandHeart, Zap, TreePine,
  Gavel, Landmark, Star, Award, Home, BarChart4, Wallet, CreditCard, TrendingUpIcon,
  Layers, Crown, Building2Icon, Building as Bank, Scale, Handshake, UserCog,
  Gauge, Lightbulb, Recycle, Smartphone, Construction, CheckSquare, ArchiveIcon,
  PieChartIcon, FileBarChartIcon
} from 'lucide-react';
import type { CompanyUnifiedFiltersV2 } from "../../../types/unified-filters-v2";

interface MappingsData {
  [type: string]: {
    parents: string[]
    children: string[]
    hierarchical: {
      [parent: string]: string[]
    }
  }
}

interface CompanyUnifiedFiltersV2Props {
  filters: CompanyUnifiedFiltersV2;
  mappings?: MappingsData;
  onFiltersChange: (filters: CompanyUnifiedFiltersV2) => void;
  onClearFilters: () => void;
  isLoading?: boolean;
}

export default function CompanyUnifiedFiltersV2({
  filters,
  mappings,
  onFiltersChange,
  onClearFilters,
  isLoading = false
}: CompanyUnifiedFiltersV2Props) {
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<CompanyUnifiedFiltersV2>(filters);
  const [pendingFilters, setPendingFilters] = useState<CompanyUnifiedFiltersV2>(filters);
  const [localRangeInputs, setLocalRangeInputs] = useState<{[key: string]: string}>({});

  // Core Company Filter Options
  const [coreCompanyOptions, setCoreCompanyOptions] = useState<{
    companyAddresses: Array<{value: string, label: string}>,
    companyCities: Array<{value: string, label: string}>,
    companyStates: Array<{value: string, label: string}>,
    companyWebsites: Array<{value: string, label: string}>,
    industries: Array<{value: string, label: string}>,
    companyCountries: Array<{value: string, label: string}>,
    sources: Array<{value: string, label: string}>,
    websiteScrapingStatuses: Array<{value: string, label: string}>,
    companyOverviewStatuses: Array<{value: string, label: string}>,
    overviewV2Statuses: Array<{value: string, label: string}>,
  }>({
    companyAddresses: [],
    companyCities: [],
    companyStates: [],
    companyWebsites: [],
    industries: [],
    companyCountries: [],
    sources: [],
    websiteScrapingStatuses: [],
    companyOverviewStatuses: [],
    overviewV2Statuses: [],
  });
  const [loadingCoreOptions, setLoadingCoreOptions] = useState(false);

  // Company Overview V2 Filter Options
  const [overviewV2Options, setOverviewV2Options] = useState<{
    companyTypes: Array<{value: string, label: string}>,
    investmentFocus: Array<{value: string, label: string}>,
    headquartersAddresses: Array<{value: string, label: string}>,
    headquartersCities: Array<{value: string, label: string}>,
    headquartersStates: Array<{value: string, label: string}>,
    headquartersCountries: Array<{value: string, label: string}>,
    officeLocations: Array<{value: string, label: string}>,
    balanceSheetStrengths: Array<{value: string, label: string}>,
    fundingSources: Array<{value: string, label: string}>,
    developmentFeeStructures: Array<{value: string, label: string}>,
    creditRatings: Array<{value: string, label: string}>,
    investmentVehicleTypes: Array<{value: string, label: string}>,
    activeFundNameSeries: Array<{value: string, label: string}>,
    fundraisingStatuses: Array<{value: string, label: string}>,
    lenderTypes: Array<{value: string, label: string}>,
    lendingOrigins: Array<{value: string, label: string}>,
    portfolioHealths: Array<{value: string, label: string}>,
    partnerships: Array<{value: string, label: string}>,
    keyEquityPartners: Array<{value: string, label: string}>,
    keyDebtPartners: Array<{value: string, label: string}>,
    boardOfDirectors: Array<{value: string, label: string}>,
    keyExecutives: Array<{value: string, label: string}>,
    founderBackgrounds: Array<{value: string, label: string}>,

    corporateStructures: Array<{value: string, label: string}>,
    parentCompanies: Array<{value: string, label: string}>,
    subsidiaries: Array<{value: string, label: string}>,
    stockTickerSymbols: Array<{value: string, label: string}>,
    stockExchanges: Array<{value: string, label: string}>,
    targetCustomerProfiles: Array<{value: string, label: string}>,
    majorCompetitors: Array<{value: string, label: string}>,
    industryAwardsRecognitions: Array<{value: string, label: string}>,
    rolesInPreviousDeals: Array<{value: string, label: string}>,
    internalRelationshipManagers: Array<{value: string, label: string}>,
    pipelineStatuses: Array<{value: string, label: string}>,
    recentNewsSentiments: Array<{value: string, label: string}>,
    dataSources: Array<{value: string, label: string}>,
  }>({
    companyTypes: [],
    investmentFocus: [],
    headquartersAddresses: [],
    headquartersCities: [],
    headquartersStates: [],
    headquartersCountries: [],
    officeLocations: [],
    balanceSheetStrengths: [],
    fundingSources: [],
    developmentFeeStructures: [],
    creditRatings: [],
    investmentVehicleTypes: [],
    activeFundNameSeries: [],
    fundraisingStatuses: [],
    lenderTypes: [],
    lendingOrigins: [],
    portfolioHealths: [],
    partnerships: [],
    keyEquityPartners: [],
    keyDebtPartners: [],
    boardOfDirectors: [],
    keyExecutives: [],
    founderBackgrounds: [],
    corporateStructures: [],
    parentCompanies: [],
    subsidiaries: [],
    stockTickerSymbols: [],
    stockExchanges: [],
    targetCustomerProfiles: [],
    majorCompetitors: [],
    industryAwardsRecognitions: [],
    rolesInPreviousDeals: [],
    internalRelationshipManagers: [],
    pipelineStatuses: [],
    recentNewsSentiments: [],
    dataSources: [],
  });
  const [loadingOverviewV2Options, setLoadingOverviewV2Options] = useState(false);

  // Processing status options with null handling
  const processingStatusOptions = [
    { value: 'not_started', label: 'Not Started', icon: Minus, color: 'from-gray-50 to-slate-50' },
    { value: 'pending', label: 'Pending', icon: Clock, color: 'from-yellow-50 to-amber-50' },
    { value: 'running', label: 'Running', icon: Loader2, color: 'from-blue-50 to-indigo-50' },
    { value: 'completed', label: 'Completed', icon: CheckCircle, color: 'from-green-50 to-emerald-50' },
    { value: 'failed', label: 'Failed', icon: AlertCircle, color: 'from-red-50 to-pink-50' },
    { value: 'error', label: 'Error', icon: AlertCircle, color: 'from-red-50 to-pink-50' }
  ];

  // Contact Processor Options - Using predefined status options
  const [contactProcessorOptions, setContactProcessorOptions] = useState<{
    contactsEmailVerificationStatuses: Array<{value: string, label: string}>,
    contactsEnrichmentStatuses: Array<{value: string, label: string}>,
    contactsEnrichmentV2Statuses: Array<{value: string, label: string}>,
    contactsEmailGenerationStatuses: Array<{value: string, label: string}>,
    contactsEmailSendingStatuses: Array<{value: string, label: string}>,
  }>({
    contactsEmailVerificationStatuses: processingStatusOptions.map(option => ({ value: option.value, label: option.label })),
    contactsEnrichmentStatuses: processingStatusOptions.map(option => ({ value: option.value, label: option.label })),
    contactsEnrichmentV2Statuses: processingStatusOptions.map(option => ({ value: option.value, label: option.label })),
    contactsEmailGenerationStatuses: processingStatusOptions.map(option => ({ value: option.value, label: option.label })),
    contactsEmailSendingStatuses: processingStatusOptions.map(option => ({ value: option.value, label: option.label })),
  }); 
  const [loadingContactProcessorOptions, setLoadingContactProcessorOptions] = useState(false);

  // Investment Criteria Filter Options (Enhanced with debt & equity fields)
  const [investmentCriteriaOptions, setInvestmentCriteriaOptions] = useState<{
    capitalPositions: Array<{value: string, label: string}>,
    propertyTypes: Array<{value: string, label: string}>,
    propertySubcategories: Array<{value: string, label: string}>,
    strategies: Array<{value: string, label: string}>,
    loanTypes: Array<{value: string, label: string}>,
    structuredLoanTranches: Array<{value: string, label: string}>,
    loanPrograms: Array<{value: string, label: string}>,
    recourseLoans: Array<{value: string, label: string}>,
    countries: Array<{value: string, label: string}>,
    regions: Array<{value: string, label: string}>,
    states: Array<{value: string, label: string}>,
    cities: Array<{value: string, label: string}>,
    decisionMakingProcesses: Array<{value: string, label: string}>,
    // Debt specific fields
    eligibleBorrowers: Array<{value: string, label: string}>,
    lienPositions: Array<{value: string, label: string}>,
    rateLocks: Array<{value: string, label: string}>,
    rateTypes: Array<{value: string, label: string}>,
    loanTypeNormalized: Array<{value: string, label: string}>,
    amortizations: Array<{value: string, label: string}>,
    loanMinDebtYield: Array<{value: string, label: string}>,
    futureFacilities: Array<{value: string, label: string}>,
    occupancyRequirements: Array<{value: string, label: string}>,
    prepayment: Array<{value: string, label: string}>,
    yieldMaintenance: Array<{value: string, label: string}>,
    // Equity specific fields
    ownershipRequirements: Array<{value: string, label: string}>,
  }>({
    capitalPositions: [],
    propertyTypes: [],
    propertySubcategories: [],
    strategies: [],
    loanTypes: [],
    structuredLoanTranches: [],
    loanPrograms: [],
    recourseLoans: [],
    countries: [],
    regions: [],
    states: [],
    cities: [],
    decisionMakingProcesses: [],
    eligibleBorrowers: [],
    lienPositions: [],
    rateLocks: [],
    rateTypes: [],
    loanTypeNormalized: [],
    amortizations: [],
    loanMinDebtYield: [],
    futureFacilities: [],
    occupancyRequirements: [],
    prepayment: [],
    yieldMaintenance: [],
    ownershipRequirements: [],
  });
  const [loadingInvestmentCriteriaOptions, setLoadingInvestmentCriteriaOptions] = useState(false);

  // Individual NOT filter modes
  const [filterNotModes, setFilterNotModes] = useState<{[key: string]: boolean}>({
    source: false,
    industry: false,
    companyType: false,
    websiteScrapingStatus: false,
    companyOverviewStatus: false,
    overviewV2Status: false,
    investmentCriteriaStatus: false,
    contactsEmailVerificationStatus: false,
    contactsEnrichmentStatus: false,
    contactsEnrichmentV2Status: false,
    contactsEmailGenerationStatus: false,
    contactsEmailSendingStatus: false,
    investmentFocus: false,
    capitalPosition: false,
    propertyTypes: false,
    strategies: false,
    partnerships: false,
    fundraisingStatus: false,
    lenderType: false,
  });

  // Track which table sections are expanded
  const [expandedSections, setExpandedSections] = useState<{[key: string]: boolean}>({
    companies: true,
    company_overview_v2: false,
    contact_processors: false,
    investment_criteria: false,
  });

  // Fetch all filter options on component mount
  useEffect(() => {
    fetchAllFilterOptions();
  }, []);

  const fetchAllFilterOptions = async () => {
    await Promise.all([
      fetchCoreCompanyOptions(),
      fetchOverviewV2Options(),
      fetchInvestmentCriteriaOptions(),
    ]);
  };

  async function fetchCoreCompanyOptions() {
    setLoadingCoreOptions(true);
    try {
      const response = await fetch('/api/companies/filter-options-v2?section=core_companies');
      if (response.ok) {
        const data = await response.json();
        setCoreCompanyOptions(data);
      }
    } catch (error) {
      console.error('Error fetching core company options:', error);
    } finally {
      setLoadingCoreOptions(false);
    }
  }

  async function fetchOverviewV2Options() {
    setLoadingOverviewV2Options(true);
    try {
      const response = await fetch('/api/companies/filter-options-v2?section=overview_v2');
      if (response.ok) {
        const data = await response.json();
        setOverviewV2Options(data);
      }
    } catch (error) {
      console.error('Error fetching overview V2 options:', error);
    } finally {
      setLoadingOverviewV2Options(false);
    }
  }

  async function fetchInvestmentCriteriaOptions() {
    setLoadingInvestmentCriteriaOptions(true);
    try {
      const response = await fetch('/api/companies/filter-options-v2?section=investment_criteria');
      if (response.ok) {
        const data = await response.json();
        setInvestmentCriteriaOptions(data);
      }
    } catch (error) {
      console.error('Error fetching investment criteria options:', error);
    } finally {
      setLoadingInvestmentCriteriaOptions(false);
    }
  }

  // Sync local filters with prop changes
  useEffect(() => {
    setLocalFilters(filters);
    setPendingFilters(filters);
  }, [filters]);

  // Update pending filters (not applied yet)
  const updatePendingFilters = (newFilters: Partial<CompanyUnifiedFiltersV2>) => {
    const updatedPendingFilters = { ...pendingFilters, ...newFilters, page: 1 };
    setPendingFilters(updatedPendingFilters);
  };

  // Apply filters when user clicks Apply button
  const applyFilters = () => {
    setLocalFilters(pendingFilters);
    onFiltersChange(pendingFilters);
    setIsFilterPanelOpen(false);
  };

  // Handle clear filters
  const handleClearFilters = () => {
    const defaultFilters = {
      page: 1,
      limit: 25,
      sortBy: 'updated_at',
      sortOrder: 'desc' as const
    };
    
    setLocalFilters(defaultFilters);
    setPendingFilters(defaultFilters);
    onFiltersChange(defaultFilters);
  };

  // Handle range input changes with local state
  const updateLocalRangeInput = (key: string, value: string) => {
    setLocalRangeInputs(prev => ({ ...prev, [key]: value }));
  };

  // Apply range filter when user finishes input
  const applyRangeFilter = (key: string, value: string) => {
    const numericValue = value.trim() === '' ? undefined : Number(value);
    updatePendingFilters({ [key]: numericValue } as Partial<CompanyUnifiedFiltersV2>);
    
    setLocalRangeInputs(prev => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  };

  // Get current value for range input
  const getRangeInputValue = (key: string, filterValue?: number) => {
    return localRangeInputs[key] !== undefined ? localRangeInputs[key] : (filterValue || '');
  };



  // Count active filters
  const getActiveFilterCount = () => {
    let count = 0;
    
    // Basic filters
    if (pendingFilters.searchTerm) count++;
    if (pendingFilters.sortBy && pendingFilters.sortBy !== 'updated_at') count++;
    if (pendingFilters.sortOrder && pendingFilters.sortOrder !== 'desc') count++;
    
    // Core company table filters
    if (pendingFilters.companyAddress?.length) count++;
    if (pendingFilters.companyCity?.length) count++;
    if (pendingFilters.companyState?.length) count++;
    if (pendingFilters.companyWebsite?.length) count++;
    if (pendingFilters.industry?.length) count++;
    if (pendingFilters.companyCountry?.length) count++;
    if (pendingFilters.source?.length) count++;
    if (pendingFilters.websiteScrapingStatus?.length) count++;
    if (pendingFilters.companyOverviewStatus?.length) count++;
    if (pendingFilters.overviewV2Status?.length) count++;
    if (pendingFilters.investmentCriteriaStatus?.length) count++;
    
    // Overview V2 filters
    if (pendingFilters.companyType?.length) count++;
    if (pendingFilters.foundedYearMin !== undefined) count++;
    if (pendingFilters.foundedYearMax !== undefined) count++;
    if (pendingFilters.investmentFocus?.length) count++;
    
    // V2 Contact Information filters
    if (pendingFilters.hasMainPhone !== undefined) count++;
    if (pendingFilters.hasSecondaryPhone !== undefined) count++;
    if (pendingFilters.hasMainEmail !== undefined) count++;
    if (pendingFilters.hasSecondaryEmail !== undefined) count++;
    if (pendingFilters.hasCompanyLinkedin !== undefined) count++;
    if (pendingFilters.hasTwitter !== undefined) count++;
    if (pendingFilters.hasFacebook !== undefined) count++;
    if (pendingFilters.hasInstagram !== undefined) count++;
    if (pendingFilters.hasYoutube !== undefined) count++;
    
    // V2 Location filters
    if (pendingFilters.headquartersAddress?.length) count++;
    if (pendingFilters.headquartersCity?.length) count++;
    if (pendingFilters.headquartersState?.length) count++;
    if (pendingFilters.headquartersCountry?.length) count++;
    if (pendingFilters.officeLocations?.length) count++;
    
    // V2 Financial metrics filters
    if (pendingFilters.fundSizeMin !== undefined) count++;
    if (pendingFilters.fundSizeMax !== undefined) count++;
    if (pendingFilters.aumMin !== undefined) count++;
    if (pendingFilters.aumMax !== undefined) count++;
    if (pendingFilters.numberOfPropertiesMin !== undefined) count++;
    if (pendingFilters.numberOfPropertiesMax !== undefined) count++;
    if (pendingFilters.numberOfOfficesMin !== undefined) count++;
    if (pendingFilters.numberOfOfficesMax !== undefined) count++;
    if (pendingFilters.numberOfEmployeesMin !== undefined) count++;
    if (pendingFilters.numberOfEmployeesMax !== undefined) count++;
    if (pendingFilters.annualRevenueMin !== undefined) count++;
    if (pendingFilters.annualRevenueMax !== undefined) count++;
    
    // V2 Financial information filters
    if (pendingFilters.balanceSheetStrength?.length) count++;
    if (pendingFilters.fundingSources?.length) count++;
    if (pendingFilters.creditRating?.length) count++;
    if (pendingFilters.dryPowderMin !== undefined) count++;
    if (pendingFilters.dryPowderMax !== undefined) count++;
    
    // V2 Investment & Fund information filters
    if (pendingFilters.investmentVehicleType?.length) count++;
    if (pendingFilters.fundraisingStatus?.length) count++;
    if (pendingFilters.lenderType?.length) count++;
    if (pendingFilters.annualLoanVolumeMin !== undefined) count++;
    if (pendingFilters.annualLoanVolumeMax !== undefined) count++;
    if (pendingFilters.portfolioHealth?.length) count++;
    
    // V2 Partnership & Leadership filters
    if (pendingFilters.partnerships?.length) count++;
    if (pendingFilters.keyEquityPartners?.length) count++;
    if (pendingFilters.keyDebtPartners?.length) count++;
    if (pendingFilters.keyExecutives?.length) count++;
    
    // V2 Market positioning filters
      if (pendingFilters.sustainabilityEsgFocus !== undefined) count++;
    if (pendingFilters.technologyProptechAdoption !== undefined) count++;
    if (pendingFilters.adaptiveReuseExperience !== undefined) count++;
    if (pendingFilters.regulatoryZoningExpertise !== undefined) count++;
    
    // V2 Corporate structure filters
    if (pendingFilters.corporateStructure?.length) count++;
    if (pendingFilters.parentCompany?.length) count++;
    if (pendingFilters.stockTickerSymbol?.length) count++;
    if (pendingFilters.stockExchange?.length) count++;
    
    // Contact processor flags
    if (pendingFilters.hasContacts !== undefined) count++;
    if (pendingFilters.contactsEmailVerificationStatus?.length) count++;
    if (pendingFilters.contactsEnrichmentStatus?.length) count++;
    if (pendingFilters.contactsEnrichmentV2Status?.length) count++;
    
    // Investment criteria filters
    if (pendingFilters.capitalPosition?.length) count++;
    if (pendingFilters.propertyTypes?.length) count++;
    if (pendingFilters.strategies?.length) count++;
    if (pendingFilters.regions?.length) count++;
    if (pendingFilters.states?.length) count++;
    if (pendingFilters.cities?.length) count++;
    if (pendingFilters.countries?.length) count++;
    if (pendingFilters.dealSizeMin !== undefined) count++;
    if (pendingFilters.dealSizeMax !== undefined) count++;
    
    // Investment criteria debt fields
    if (pendingFilters.loanTypes?.length) count++;
    if (pendingFilters.loanProgram?.length) count++;
    if (pendingFilters.structuredLoanTranche?.length) count++;
    if (pendingFilters.recourseLoan?.length) count++;
    if (pendingFilters.eligibleBorrower?.length) count++;
    if (pendingFilters.lienPosition?.length) count++;
    if (pendingFilters.rateLock?.length) count++;
    if (pendingFilters.rateType?.length) count++;
    if (pendingFilters.amortization?.length) count++;
    if (pendingFilters.loanTypeNormalized?.length) count++;
    if (pendingFilters.loanMinDebtYield?.length) count++;
    if (pendingFilters.futureFacilities?.length) count++;
    if (pendingFilters.occupancyRequirements?.length) count++;
    if (pendingFilters.prepayment?.length) count++;
    if (pendingFilters.yieldMaintenance?.length) count++;
    
    // Investment criteria debt range fields
    if (pendingFilters.closingTimeMin !== undefined) count++;
    if (pendingFilters.closingTimeMax !== undefined) count++;
    if (pendingFilters.minLoanDscrMin !== undefined) count++;
    if (pendingFilters.minLoanDscrMax !== undefined) count++;
    if (pendingFilters.maxLoanDscrMin !== undefined) count++;
    if (pendingFilters.maxLoanDscrMax !== undefined) count++;
    if (pendingFilters.loanOriginationMaxFeeMin !== undefined) count++;
    if (pendingFilters.loanOriginationMaxFeeMax !== undefined) count++;
    if (pendingFilters.loanOriginationMinFeeMin !== undefined) count++;
    if (pendingFilters.loanOriginationMinFeeMax !== undefined) count++;
    if (pendingFilters.loanExitMinFeeMin !== undefined) count++;
    if (pendingFilters.loanExitMinFeeMax !== undefined) count++;
    if (pendingFilters.loanExitMaxFeeMin !== undefined) count++;
    if (pendingFilters.loanExitMaxFeeMax !== undefined) count++;
    if (pendingFilters.loanInterestRateSofrMin !== undefined) count++;
    if (pendingFilters.loanInterestRateSofrMax !== undefined) count++;
    if (pendingFilters.loanInterestRateWsjMin !== undefined) count++;
    if (pendingFilters.loanInterestRateWsjMax !== undefined) count++;
    if (pendingFilters.loanInterestRatePrimeMin !== undefined) count++;
    if (pendingFilters.loanInterestRatePrimeMax !== undefined) count++;
    if (pendingFilters.loanInterestRate3ytMin !== undefined) count++;
    if (pendingFilters.loanInterestRate3ytMax !== undefined) count++;
    if (pendingFilters.loanInterestRate5ytMin !== undefined) count++;
    if (pendingFilters.loanInterestRate5ytMax !== undefined) count++;
    if (pendingFilters.loanInterestRate10ytMin !== undefined) count++;
    if (pendingFilters.loanInterestRate10ytMax !== undefined) count++;
    if (pendingFilters.loanInterestRate30ytMin !== undefined) count++;
    if (pendingFilters.loanInterestRate30ytMax !== undefined) count++;
    if (pendingFilters.loanToValueMinMin !== undefined) count++;
    if (pendingFilters.loanToValueMinMax !== undefined) count++;
    if (pendingFilters.loanToValueMaxMin !== undefined) count++;
    if (pendingFilters.loanToValueMaxMax !== undefined) count++;
    if (pendingFilters.loanToCostMinMin !== undefined) count++;
    if (pendingFilters.loanToCostMinMax !== undefined) count++;
    if (pendingFilters.loanToCostMaxMin !== undefined) count++;
    if (pendingFilters.loanToCostMaxMax !== undefined) count++;
    if (pendingFilters.minLoanTermMin !== undefined) count++;
    if (pendingFilters.minLoanTermMax !== undefined) count++;
    if (pendingFilters.maxLoanTermMin !== undefined) count++;
    if (pendingFilters.maxLoanTermMax !== undefined) count++;
    
    // Investment criteria equity fields
    if (pendingFilters.ownershipRequirement?.length) count++;
    if (pendingFilters.minimumYieldOnCostMin !== undefined) count++;
    if (pendingFilters.minimumYieldOnCostMax !== undefined) count++;
    if (pendingFilters.maxLeverageToleranceMin !== undefined) count++;
    if (pendingFilters.maxLeverageToleranceMax !== undefined) count++;
    
    // Investment criteria equity range fields
    if (pendingFilters.targetReturnMin !== undefined) count++;
    if (pendingFilters.targetReturnMax !== undefined) count++;
    if (pendingFilters.minimumIrrMin !== undefined) count++;
    if (pendingFilters.minimumIrrMax !== undefined) count++;
    if (pendingFilters.targetCashOnCashMin !== undefined) count++;
    if (pendingFilters.targetCashOnCashMax !== undefined) count++;
    if (pendingFilters.minHoldPeriodYearsMin !== undefined) count++;
    if (pendingFilters.minHoldPeriodYearsMax !== undefined) count++;
    if (pendingFilters.maxHoldPeriodYearsMin !== undefined) count++;
    if (pendingFilters.maxHoldPeriodYearsMax !== undefined) count++;
    
    // Investment criteria additional fields
    if (pendingFilters.decisionMakingProcess?.length) count++;
    if (pendingFilters.investmentCriteriaNotes?.length) count++;
    
    // NOT filters
    if (pendingFilters.notSource?.length) count++;
    if (pendingFilters.notIndustry?.length) count++;
    if (pendingFilters.notCompanyType?.length) count++;
    if (pendingFilters.notWebsiteScrapingStatus?.length) count++;
    if (pendingFilters.notCompanyOverviewStatus?.length) count++;
    if (pendingFilters.notOverviewV2Status?.length) count++;
    if (pendingFilters.notInvestmentCriteriaStatus?.length) count++;
    if (pendingFilters.notContactsEmailVerificationStatus?.length) count++;
    if (pendingFilters.notContactsEnrichmentStatus?.length) count++;
    if (pendingFilters.notContactsEnrichmentV2Status?.length) count++;
    if (pendingFilters.notContactsEmailGenerationStatus?.length) count++;
    if (pendingFilters.notContactsEmailSendingStatus?.length) count++;
    if (pendingFilters.notInvestmentFocus?.length) count++;
    if (pendingFilters.notCapitalPosition?.length) count++;
    if (pendingFilters.notPropertyTypes?.length) count++;
    if (pendingFilters.notStrategies?.length) count++;
    if (pendingFilters.notPartnerships?.length) count++;
    if (pendingFilters.notFundraisingStatus?.length) count++;
    if (pendingFilters.notLenderType?.length) count++;
    if (pendingFilters.notLoanTypes?.length) count++;
    if (pendingFilters.notStructuredLoanTranche?.length) count++;
    if (pendingFilters.notLoanProgram?.length) count++;
    if (pendingFilters.notRecourseLoan?.length) count++;
    if (pendingFilters.notEligibleBorrower?.length) count++;
    if (pendingFilters.notLienPosition?.length) count++;
    if (pendingFilters.notOwnershipRequirement?.length) count++;
    if (pendingFilters.notRateType?.length) count++;
    if (pendingFilters.notAmortization?.length) count++;
    
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  // Enhanced sort options
  const UNIFIED_SORT_OPTIONS = [
    { value: 'updated_at', label: 'Last Updated (Newest First)', icon: Clock },
    { value: 'created_at', label: 'Created Date', icon: Calendar },
    { value: 'company_name', label: 'Company Name', icon: Building },
    { value: 'founded_year', label: 'Founded Year', icon: Calendar },
    { value: 'number_of_employees', label: 'Employee Count', icon: Users },
    { value: 'aum', label: 'AUM (Highest First)', icon: DollarSign },
    { value: 'fund_size', label: 'Fund Size (Largest First)', icon: Banknote },
    { value: 'annual_revenue', label: 'Annual Revenue (Highest First)', icon: TrendingUp },
    { value: 'market_capitalization', label: 'Market Cap (Highest First)', icon: PieChart },
    { value: 'data_confidence_score', label: 'Data Confidence (Highest First)', icon: CheckCircle },
  ];


  // Toggle section expansion
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({ ...prev, [section]: !prev[section] }));
  };

  // Get NOT filter state for specific filter
  const getFilterNotState = (filterKey: string): boolean => {
    return filterNotModes[filterKey] || false;
  };

  // Toggle NOT filter mode for specific filter
  const toggleFilterNotMode = (filterKey: string) => {
    setFilterNotModes(prev => ({
      ...prev,
      [filterKey]: !prev[filterKey]
    }));
  };

  // Multi-select component with NOT filter support
  const EnhancedMultiSelect = ({ 
    options, 
    selected, 
    notSelected,
    onChange, 
    onNotChange,
    placeholder, 
    disabled,
    label,
    showNotFilter = false,
    filterKey
  }: {
    options: Array<{value: string, label: string}>,
    selected: string[],
    notSelected?: string[],
    onChange: (values: string[]) => void,
    onNotChange?: (values: string[]) => void,
    placeholder: string,
    disabled?: boolean,
    label: string,
    showNotFilter?: boolean,
    filterKey: string
  }) => {
    const isNotMode = getFilterNotState(filterKey);
    
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium text-gray-700">{label}</Label>
          {showNotFilter && (
            <div className="flex items-center gap-2">
              <EyeIcon className="h-4 w-4 text-gray-400" />
              <Switch
                checked={isNotMode}
                onCheckedChange={() => toggleFilterNotMode(filterKey)}
              />
              <EyeOff className="h-4 w-4 text-gray-400" />
            </div>
          )}
        </div>
        
        {!isNotMode ? (
          <ReactMultiSelect
            options={options}
            selected={selected || []}
            onChange={onChange}
            placeholder={placeholder}
            disabled={disabled}
            showSelectAll={true}
            selectAllLabel="Select All"
          />
        ) : (
          <ReactMultiSelect
            options={options}
            selected={notSelected || []}
            onChange={onNotChange || (() => {})}
            placeholder={`NOT ${placeholder.toLowerCase()}`}
            disabled={disabled}
            showSelectAll={true}
            selectAllLabel="Exclude All"
          />
        )}
      </div>
    );
  };

  return (
    <>
      {/* Unified Filter Bar V2 */}
      <div className="w-full bg-white border border-gray-200 rounded-xl shadow-sm mb-6">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            {/* Unified Filter Button V2 */}
            <Button
              onClick={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
              className={`flex items-center gap-3 px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 ${
                isFilterPanelOpen 
                  ? 'bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white' 
                  : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
              }`}
            >
              <Database className="h-5 w-5" />
              <span className="font-medium">Filters</span>
              {/* <Badge className="bg-amber-100 text-amber-800 border border-amber-200 ml-1 font-semibold">
                Enhanced
              </Badge> */}
              {activeFilterCount > 0 && (
                <Badge className="bg-white/20 text-white border border-white/20 ml-1">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>
          </div>

          <div className="flex items-center gap-3">
            {/* Active Filters Count */}
            {activeFilterCount > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  {activeFilterCount} filter{activeFilterCount !== 1 ? 's' : ''} applied
                </span>
                <Button
                  onClick={handleClearFilters}
                  className="text-gray-500 hover:text-red-600 bg-transparent border border-gray-200 hover:border-red-200 hover:bg-red-50 px-3 py-1 text-sm"
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Clear All
                </Button>
              </div>
            )}

            {/* Enhanced Sort Controls */}
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Sort by:</span>
              </div>
              <div className="flex items-center gap-2">
                <Select 
                  value={pendingFilters.sortBy || 'updated_at'} 
                  onValueChange={(value) => updatePendingFilters({ sortBy: value })}
                >
                  <SelectTrigger className="w-auto min-w-[250px] border-gray-200 bg-white shadow-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="max-h-[400px]">
                    {UNIFIED_SORT_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <option.icon className="h-4 w-4" />
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                {/* Sort Order Toggle */}
                <Button
                  onClick={() => updatePendingFilters({ 
                    sortOrder: pendingFilters.sortOrder === 'asc' ? 'desc' : 'asc' 
                  })}
                  className={`p-2 rounded-lg border transition-all ${
                    pendingFilters.sortOrder === 'asc' 
                      ? 'bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100' 
                      : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                  }`}
                  title={`Sort ${pendingFilters.sortOrder === 'asc' ? 'Ascending' : 'Descending'}`}
                >
                  {pendingFilters.sortOrder === 'asc' ? (
                    <ArrowUp className="h-4 w-4" />
                  ) : (
                    <ArrowDown className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Unified Right Side Filter Panel V2 */}
      <div className={`fixed top-0 right-0 h-full w-[900px] bg-white border-l border-gray-200 shadow-xl transform transition-transform duration-300 ease-in-out z-50 ${
        isFilterPanelOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Enhanced Panel Header V2 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 via-indigo-50 to-blue-50">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="p-3 rounded-xl bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-lg">
                  <Database className="h-6 w-6" />
                </div>
                {activeFilterCount > 0 && (
                  <div className="absolute -top-2 -right-2 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center font-bold">
                    {activeFilterCount > 9 ? '9+' : activeFilterCount}
                  </div>
                )}
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                  Filters
                  <Badge className="bg-amber-100 text-amber-800 border border-amber-200 ml-2 font-semibold">
                    Enhanced Analytics
                  </Badge>
                </h2>
                <p className="text-sm text-gray-600 mt-1 flex items-center gap-2">
                  <Table className="h-4 w-4" />
                  Advanced multi-table company filtering with insights data
                </p>
              </div>
            </div>
            <Button
              onClick={() => setIsFilterPanelOpen(false)}
              className="text-gray-500 hover:text-gray-700 bg-white border border-gray-200 p-2 hover:bg-gray-50 transition-colors"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Panel Content - Scrollable */}
          <div className="flex-1 overflow-y-auto p-6 space-y-6">

            {/* Global Search */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-gray-50 to-blue-50">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <Search className="h-5 w-5 text-blue-600" />
                  Global Search
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search across companies, names, industries, partners, executives..."
                    value={pendingFilters.searchTerm || ''}
                    onChange={(e) => updatePendingFilters({ searchTerm: e.target.value || undefined })}
                    onKeyDown={(e) => e.key === 'Enter' && applyFilters()}
                    className="pl-10 border-gray-200 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
                    disabled={isLoading}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  Enhanced search across company names, industries, executives, partners, and V2 overview data
                </p>
              </CardContent>
            </Card>

            {/* Core Companies Table Filters */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-cyan-50 to-blue-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-cyan-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('companies')}
                title={`Click to ${expandedSections.companies ? 'collapse' : 'expand'} Companies filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <Building className="h-5 w-5 text-cyan-600" />
                    Core Companies Table
                    <Badge className="bg-cyan-100 text-cyan-700 border border-cyan-200">
                      Primary Data
                    </Badge>
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.companies ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.companies ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.companies && (
                <CardContent className="space-y-6">
                  {/* Company Address Filter */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Company Address</Label>
                    <ReactMultiSelect
                      options={coreCompanyOptions.companyAddresses}
                      selected={pendingFilters.companyAddress || []}
                      onChange={(selected: string[]) => updatePendingFilters({ companyAddress: selected })}
                      placeholder={loadingCoreOptions ? "Loading addresses..." : "Select company addresses..."}
                      disabled={isLoading || loadingCoreOptions}
                      showSelectAll={true}
                      selectAllLabel="Select All Addresses"
                    />
                  </div>

                  {/* Company Location */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Company City</Label>
                      <ReactMultiSelect
                        options={coreCompanyOptions.companyCities}
                        selected={pendingFilters.companyCity || []}
                        onChange={(selected: string[]) => updatePendingFilters({ companyCity: selected })}
                        placeholder={loadingCoreOptions ? "Loading cities..." : "Select cities..."}
                        disabled={isLoading || loadingCoreOptions}
                        showSelectAll={true}
                        selectAllLabel="Select All Cities"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Company State</Label>
                      <ReactMultiSelect
                        options={coreCompanyOptions.companyStates}
                        selected={pendingFilters.companyState || []}
                        onChange={(selected: string[]) => updatePendingFilters({ companyState: selected })}
                        placeholder={loadingCoreOptions ? "Loading states..." : "Select states..."}
                        disabled={isLoading || loadingCoreOptions}
                        showSelectAll={true}
                        selectAllLabel="Select All States"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Company Country</Label>
                      <ReactMultiSelect
                        options={coreCompanyOptions.companyCountries}
                        selected={pendingFilters.companyCountry || []}
                        onChange={(selected: string[]) => updatePendingFilters({ companyCountry: selected })}
                        placeholder={loadingCoreOptions ? "Loading countries..." : "Select countries..."}
                        disabled={isLoading || loadingCoreOptions}
                        showSelectAll={true}
                        selectAllLabel="Select All Countries"
                      />
                    </div>
                  </div>

                  {/* Industry & Source */}
                  <div className="grid grid-cols-2 gap-4">
                    <EnhancedMultiSelect
                      label="Industry"
                      options={coreCompanyOptions.industries}
                      selected={pendingFilters.industry || []}
                      notSelected={pendingFilters.notIndustry || []}
                      onChange={(selected: string[]) => updatePendingFilters({ industry: selected })}
                      onNotChange={(selected: string[]) => updatePendingFilters({ notIndustry: selected })}
                      placeholder={loadingCoreOptions ? "Loading industries..." : "Select industries..."}
                      disabled={isLoading || loadingCoreOptions}
                      showNotFilter={true}
                      filterKey="industry"
                    />

                    <EnhancedMultiSelect
                      label="Source"
                      options={coreCompanyOptions.sources}
                      selected={pendingFilters.source || []}
                      notSelected={pendingFilters.notSource || []}
                      onChange={(selected: string[]) => updatePendingFilters({ source: selected })}
                      onNotChange={(selected: string[]) => updatePendingFilters({ notSource: selected })}
                      placeholder={loadingCoreOptions ? "Loading sources..." : "Select sources..."}
                      disabled={isLoading || loadingCoreOptions}
                      showNotFilter={true}
                      filterKey="source"
                    />
                  </div>

                  {/* Processing Status Groups */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Website Scraping</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.websiteScrapingStatus || []}
                        notSelected={pendingFilters.notWebsiteScrapingStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ websiteScrapingStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notWebsiteScrapingStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="websiteScrapingStatus"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Company Overview</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.overviewV2Status || []}
                        notSelected={pendingFilters.notOverviewV2Status || []}
                        onChange={(selected: string[]) => updatePendingFilters({ overviewV2Status: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notOverviewV2Status: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="overviewV2Status"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Investment Criteria</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.investmentCriteriaStatus || []}
                        notSelected={pendingFilters.notInvestmentCriteriaStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ investmentCriteriaStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notInvestmentCriteriaStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showNotFilter={true}
                        filterKey="investmentCriteriaStatus"
                      />
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Company Overview V2 Filters */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-emerald-50 to-teal-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-emerald-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('company_overview_v2')}
                title={`Click to ${expandedSections.company_overview_v2 ? 'collapse' : 'expand'} Company Overview V2 filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <Brain className="h-5 w-5 text-emerald-600" />
                    Company Overview
                    <Badge className="bg-emerald-100 text-emerald-700 border border-emerald-200">
                      AI Enhanced
                    </Badge>
                    <Badge className="bg-amber-100 text-amber-800 border border-amber-200 text-xs font-semibold">
                      NEW
                    </Badge>
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.company_overview_v2 ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.company_overview_v2 ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.company_overview_v2 && (
                <CardContent className="space-y-6">
                  {/* Core Company Information V2 */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Building2Icon className="h-5 w-5 text-blue-600" />
                      <h4 className="font-medium text-gray-800">Core Company Information V2</h4>
                    </div>
                    
                    <div className="grid grid-cols-1 gap-4">
                      <EnhancedMultiSelect
                        label="Company Type"
                        options={overviewV2Options.companyTypes}
                        selected={pendingFilters.companyType || []}
                        notSelected={pendingFilters.notCompanyType || []}
                        onChange={(selected: string[]) => updatePendingFilters({ companyType: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notCompanyType: selected })}
                        placeholder={loadingOverviewV2Options ? "Loading company types..." : "Select company types..."}
                        disabled={isLoading || loadingOverviewV2Options}
                        showNotFilter={true}
                        filterKey="companyType"
                      />
                    </div>

                    {/* Founded Year Range */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Founded Year Range</Label>
                      <div className="grid grid-cols-2 gap-4">
                        <Input
                          type="number"
                          placeholder="Min Year"
                          value={getRangeInputValue('foundedYearMin', pendingFilters.foundedYearMin)}
                          onChange={(e) => updateLocalRangeInput('foundedYearMin', e.target.value)}
                          onBlur={(e) => applyRangeFilter('foundedYearMin', e.target.value)}
                          onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('foundedYearMin', e.currentTarget.value)}
                          disabled={isLoading}
                          className="bg-white border-gray-200"
                        />
                        <Input
                          type="number"
                          placeholder="Max Year"
                          value={getRangeInputValue('foundedYearMax', pendingFilters.foundedYearMax)}
                          onChange={(e) => updateLocalRangeInput('foundedYearMax', e.target.value)}
                          onBlur={(e) => applyRangeFilter('foundedYearMax', e.target.value)}
                          onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('foundedYearMax', e.currentTarget.value)}
                          disabled={isLoading}
                          className="bg-white border-gray-200"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Investment & Strategy V2 */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Target className="h-5 w-5 text-green-600" />
                      <h4 className="font-medium text-gray-800">Investment & Strategy V2</h4>
                    </div>
                    
                    <EnhancedMultiSelect
                      label="Investment Focus"
                      options={overviewV2Options.investmentFocus}
                      selected={pendingFilters.investmentFocus || []}
                      notSelected={pendingFilters.notInvestmentFocus || []}
                      onChange={(selected: string[]) => updatePendingFilters({ investmentFocus: selected })}
                      onNotChange={(selected: string[]) => updatePendingFilters({ notInvestmentFocus: selected })}
                      placeholder={loadingOverviewV2Options ? "Loading investment focus..." : "Select investment focus..."}
                      disabled={isLoading || loadingOverviewV2Options}
                      showNotFilter={true}
                      filterKey="investmentFocus"
                    />
                  </div>

                  {/* Contact Information V2 */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Phone className="h-5 w-5 text-purple-600" />
                      <h4 className="font-medium text-gray-800">Contact Information V2</h4>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">Has Main Phone</span>
                          <Switch
                            checked={pendingFilters.hasMainPhone === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasMainPhone: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">Has Secondary Phone</span>
                          <Switch
                            checked={pendingFilters.hasSecondaryPhone === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasSecondaryPhone: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">Has Main Email</span>
                          <Switch
                            checked={pendingFilters.hasMainEmail === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasMainEmail: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">Has Secondary Email</span>
                          <Switch
                            checked={pendingFilters.hasSecondaryEmail === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasSecondaryEmail: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <TwitterIcon className="h-4 w-4 text-blue-500" />
                            <span className="text-sm">Has Twitter</span>
                          </div>
                          <Switch
                            checked={pendingFilters.hasTwitter === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasTwitter: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <span className="text-sm">Has LinkedIn</span>
                          <Switch
                            checked={pendingFilters.hasCompanyLinkedin === true}
                            onCheckedChange={(checked) => updatePendingFilters({ hasCompanyLinkedin: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Financial Metrics V2 */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-5 w-5 text-green-600" />
                      <h4 className="font-medium text-gray-800">Financial Metrics V2</h4>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      {/* Fund Size Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Fund Size Range (USD)</Label>
                        <div className="grid grid-cols-2 gap-2">
                          <Input
                            type="number"
                            placeholder="Min (USD)"
                            value={getRangeInputValue('fundSizeMin', pendingFilters.fundSizeMin)}
                            onChange={(e) => updateLocalRangeInput('fundSizeMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('fundSizeMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('fundSizeMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max (USD)"
                            value={getRangeInputValue('fundSizeMax', pendingFilters.fundSizeMax)}
                            onChange={(e) => updateLocalRangeInput('fundSizeMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('fundSizeMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('fundSizeMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* AUM Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">AUM Range (USD)</Label>
                        <div className="grid grid-cols-2 gap-2">
                          <Input
                            type="number"
                            placeholder="Min (USD)"
                            value={getRangeInputValue('aumMin', pendingFilters.aumMin)}
                            onChange={(e) => updateLocalRangeInput('aumMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('aumMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('aumMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max (USD)"
                            value={getRangeInputValue('aumMax', pendingFilters.aumMax)}
                            onChange={(e) => updateLocalRangeInput('aumMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('aumMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('aumMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      {/* Number of Properties Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Properties</Label>
                        <div className="space-y-2">
                          <Input
                            type="number"
                            placeholder="Min"
                            value={getRangeInputValue('numberOfPropertiesMin', pendingFilters.numberOfPropertiesMin)}
                            onChange={(e) => updateLocalRangeInput('numberOfPropertiesMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('numberOfPropertiesMin', e.target.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max"
                            value={getRangeInputValue('numberOfPropertiesMax', pendingFilters.numberOfPropertiesMax)}
                            onChange={(e) => updateLocalRangeInput('numberOfPropertiesMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('numberOfPropertiesMax', e.target.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Number of Offices Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Offices</Label>
                        <div className="space-y-2">
                          <Input
                            type="number"
                            placeholder="Min"
                            value={getRangeInputValue('numberOfOfficesMin', pendingFilters.numberOfOfficesMin)}
                            onChange={(e) => updateLocalRangeInput('numberOfOfficesMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('numberOfOfficesMin', e.target.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max"
                            value={getRangeInputValue('numberOfOfficesMax', pendingFilters.numberOfOfficesMax)}
                            onChange={(e) => updateLocalRangeInput('numberOfOfficesMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('numberOfOfficesMax', e.target.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Number of Employees Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Employees</Label>
                        <div className="space-y-2">
                          <Input
                            type="number"
                            placeholder="Min"
                            value={getRangeInputValue('numberOfEmployeesMin', pendingFilters.numberOfEmployeesMin)}
                            onChange={(e) => updateLocalRangeInput('numberOfEmployeesMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('numberOfEmployeesMin', e.target.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max"
                            value={getRangeInputValue('numberOfEmployeesMax', pendingFilters.numberOfEmployeesMax)}
                            onChange={(e) => updateLocalRangeInput('numberOfEmployeesMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('numberOfEmployeesMax', e.target.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Partnership & Leadership V2 */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Handshake className="h-5 w-5 text-blue-600" />
                      <h4 className="font-medium text-gray-800">Partnership & Leadership V2</h4>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <EnhancedMultiSelect
                        label="Partnerships"
                        options={overviewV2Options.partnerships}
                        selected={pendingFilters.partnerships || []}
                        notSelected={pendingFilters.notPartnerships || []}
                        onChange={(selected: string[]) => updatePendingFilters({ partnerships: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notPartnerships: selected })}
                        placeholder={loadingOverviewV2Options ? "Loading partnerships..." : "Select partnerships..."}
                        disabled={isLoading || loadingOverviewV2Options}
                        showNotFilter={true}
                        filterKey="partnerships"
                      />

                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Key Executives</Label>
                        <ReactMultiSelect
                          options={overviewV2Options.keyExecutives}
                          selected={pendingFilters.keyExecutives || []}
                          onChange={(selected: string[]) => updatePendingFilters({ keyExecutives: selected })}
                          placeholder={loadingOverviewV2Options ? "Loading executives..." : "Select executives..."}
                          disabled={isLoading || loadingOverviewV2Options}
                          showSelectAll={true}
                          selectAllLabel="Select All Executives"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Key Equity Partners</Label>
                        <ReactMultiSelect
                          options={overviewV2Options.keyEquityPartners}
                          selected={pendingFilters.keyEquityPartners || []}
                          onChange={(selected: string[]) => updatePendingFilters({ keyEquityPartners: selected })}
                          placeholder={loadingOverviewV2Options ? "Loading equity partners..." : "Select equity partners..."}
                          disabled={isLoading || loadingOverviewV2Options}
                          showSelectAll={true}
                          selectAllLabel="Select All Equity Partners"
                        />
                      </div>
                      
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Key Debt Partners</Label>
                        <ReactMultiSelect
                          options={overviewV2Options.keyDebtPartners}
                          selected={pendingFilters.keyDebtPartners || []}
                          onChange={(selected: string[]) => updatePendingFilters({ keyDebtPartners: selected })}
                          placeholder={loadingOverviewV2Options ? "Loading debt partners..." : "Select debt partners..."}
                          disabled={isLoading || loadingOverviewV2Options}
                          showSelectAll={true}
                          selectAllLabel="Select All Debt Partners"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Market Positioning & Strategy V2 */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-indigo-600" />
                      <h4 className="font-medium text-gray-800">Market Positioning & Strategy V2</h4>
                    </div>
                    

                    {/* Boolean Strategy Filters */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <TreePine className="h-4 w-4 text-green-500" />
                            <span className="text-sm">Sustainability/ESG Focus</span>
                          </div>
                          <Switch
                            checked={pendingFilters.sustainabilityEsgFocus === true}
                            onCheckedChange={(checked) => updatePendingFilters({ sustainabilityEsgFocus: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <Smartphone className="h-4 w-4 text-blue-500" />
                            <span className="text-sm">Technology/PropTech Adoption</span>
                          </div>
                          <Switch
                            checked={pendingFilters.technologyProptechAdoption === true}
                            onCheckedChange={(checked) => updatePendingFilters({ technologyProptechAdoption: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <Construction className="h-4 w-4 text-orange-500" />
                            <span className="text-sm">Adaptive Reuse Experience</span>
                          </div>
                          <Switch
                            checked={pendingFilters.adaptiveReuseExperience === true}
                            onCheckedChange={(checked) => updatePendingFilters({ adaptiveReuseExperience: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                        <div className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <Gavel className="h-4 w-4 text-purple-500" />
                            <span className="text-sm">Regulatory/Zoning Expertise</span>
                          </div>
                          <Switch
                            checked={pendingFilters.regulatoryZoningExpertise === true}
                            onCheckedChange={(checked) => updatePendingFilters({ regulatoryZoningExpertise: checked || undefined })}
                            disabled={isLoading}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Fund & Investment Information V2 */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <PieChart className="h-5 w-5 text-yellow-600" />
                      <h4 className="font-medium text-gray-800">Fund & Investment Information V2</h4>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <EnhancedMultiSelect
                        label="Fundraising Status"
                        options={overviewV2Options.fundraisingStatuses}
                        selected={pendingFilters.fundraisingStatus || []}
                        notSelected={pendingFilters.notFundraisingStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ fundraisingStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notFundraisingStatus: selected })}
                        placeholder={loadingOverviewV2Options ? "Loading fundraising statuses..." : "Select fundraising statuses..."}
                        disabled={isLoading || loadingOverviewV2Options}
                        showNotFilter={true}
                        filterKey="fundraisingStatus"
                      />

                      <EnhancedMultiSelect
                        label="Lender Type"
                        options={overviewV2Options.lenderTypes}
                        selected={pendingFilters.lenderType || []}
                        notSelected={pendingFilters.notLenderType || []}
                        onChange={(selected: string[]) => updatePendingFilters({ lenderType: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notLenderType: selected })}
                        placeholder={loadingOverviewV2Options ? "Loading lender types..." : "Select lender types..."}
                        disabled={isLoading || loadingOverviewV2Options}
                        showNotFilter={true}
                        filterKey="lenderType"
                      />
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Contact Processors Table Filters */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-orange-50 to-red-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-orange-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('contact_processors')}
                title={`Click to ${expandedSections.contact_processors ? 'collapse' : 'expand'} Contact Processors filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <UserCog className="h-5 w-5 text-orange-600" />
                    Contact Processors Table
                    <Badge className="bg-orange-100 text-orange-700 border border-orange-200">
                      Related Contact Data
                    </Badge>
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.contact_processors ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.contact_processors ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.contact_processors && (
                <CardContent className="space-y-6">
                  {/* Has Contacts Filter */}
                  <div className="flex items-center justify-between p-3 border rounded-lg bg-blue-50">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium">Has Associated Contacts</span>
                    </div>
                    <Switch
                      checked={pendingFilters.hasContacts === true}
                      onCheckedChange={(checked) => updatePendingFilters({ hasContacts: checked || undefined })}
                      disabled={isLoading}
                    />
                  </div>

                  {/* Contact Processing Status Groups */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Contact Email Verification</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={contactProcessorOptions.contactsEmailVerificationStatuses}
                        selected={pendingFilters.contactsEmailVerificationStatus || []}
                        notSelected={pendingFilters.notContactsEmailVerificationStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ contactsEmailVerificationStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notContactsEmailVerificationStatus: selected })}
                        placeholder={loadingContactProcessorOptions ? "Loading statuses..." : "Select status..."}
                        disabled={isLoading || loadingContactProcessorOptions}
                        showNotFilter={true}
                        filterKey="contactsEmailVerificationStatus"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Contact Enrichment</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={contactProcessorOptions.contactsEnrichmentStatuses}
                        selected={pendingFilters.contactsEnrichmentStatus || []}
                        notSelected={pendingFilters.notContactsEnrichmentStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ contactsEnrichmentStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notContactsEnrichmentStatus: selected })}
                        placeholder={loadingContactProcessorOptions ? "Loading statuses..." : "Select status..."}
                        disabled={isLoading || loadingContactProcessorOptions}
                        showNotFilter={true}
                        filterKey="contactsEnrichmentStatus"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Contact Enrichment V2</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={contactProcessorOptions.contactsEnrichmentV2Statuses}
                        selected={pendingFilters.contactsEnrichmentV2Status || []}
                        notSelected={pendingFilters.notContactsEnrichmentV2Status || []}
                        onChange={(selected: string[]) => updatePendingFilters({ contactsEnrichmentV2Status: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notContactsEnrichmentV2Status: selected })}
                        placeholder={loadingContactProcessorOptions ? "Loading statuses..." : "Select status..."}
                        disabled={isLoading || loadingContactProcessorOptions}
                        showNotFilter={true}
                        filterKey="contactsEnrichmentV2Status"
                      />
                    </div>
                  </div>
                  
                  {/* Email Processing Status Groups */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Contact Email Generation</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={contactProcessorOptions.contactsEmailGenerationStatuses}
                        selected={pendingFilters.contactsEmailGenerationStatus || []}
                        notSelected={pendingFilters.notContactsEmailGenerationStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ contactsEmailGenerationStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notContactsEmailGenerationStatus: selected })}
                        placeholder={loadingContactProcessorOptions ? "Loading statuses..." : "Select status..."}
                        disabled={isLoading || loadingContactProcessorOptions}
                        showNotFilter={true}
                        filterKey="contactsEmailGenerationStatus"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Contact Email Sending</Label>
                      <EnhancedMultiSelect
                        label=""
                        options={contactProcessorOptions.contactsEmailSendingStatuses}
                        selected={pendingFilters.contactsEmailSendingStatus || []}
                        notSelected={pendingFilters.notContactsEmailSendingStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ contactsEmailSendingStatus: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notContactsEmailSendingStatus: selected })}
                        placeholder={loadingContactProcessorOptions ? "Loading statuses..." : "Select status..."}
                        disabled={isLoading || loadingContactProcessorOptions}
                        showNotFilter={true}
                        filterKey="contactsEmailSendingStatus"
                      />
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Investment Criteria V2 Enhanced Filters */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-purple-50 to-pink-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-purple-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('investment_criteria')}
                title={`Click to ${expandedSections.investment_criteria ? 'collapse' : 'expand'} Investment Criteria V2 Enhanced filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <Target className="h-5 w-5 text-purple-600" />
                    Investment Criteria
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.investment_criteria ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.investment_criteria ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.investment_criteria && (
                <CardContent className="space-y-6">
                  {/* Capital Position */}
                  <EnhancedMultiSelect
                    label="Capital Position"
                    options={investmentCriteriaOptions.capitalPositions}
                    selected={pendingFilters.capitalPosition || []}
                    notSelected={pendingFilters.notCapitalPosition || []}
                    onChange={(selected: string[]) => updatePendingFilters({ capitalPosition: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notCapitalPosition: selected })}
                    placeholder={loadingInvestmentCriteriaOptions ? "Loading capital positions..." : "Select capital positions..."}
                    disabled={isLoading || loadingInvestmentCriteriaOptions}
                    showNotFilter={true}
                    filterKey="capitalPosition"
                  />

                  {/* Deal Size Range */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Deal Size Range (enter whole numbers, e.g., 1000000 for $1M)</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <Input
                        type="number"
                        placeholder="Min (whole number)"
                        value={getRangeInputValue('dealSizeMin', pendingFilters.dealSizeMin)}
                        onChange={(e) => updateLocalRangeInput('dealSizeMin', e.target.value)}
                        onBlur={(e) => applyRangeFilter('dealSizeMin', e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('dealSizeMin', e.currentTarget.value)}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                      <Input
                        type="number"
                        placeholder="Max (whole number)"
                        value={getRangeInputValue('dealSizeMax', pendingFilters.dealSizeMax)}
                        onChange={(e) => updateLocalRangeInput('dealSizeMax', e.target.value)}
                        onBlur={(e) => applyRangeFilter('dealSizeMax', e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('dealSizeMax', e.currentTarget.value)}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                    </div>
                  </div>

                  {/* Property Types */}
                  <EnhancedMultiSelect
                    label="Property Type"
                    options={investmentCriteriaOptions.propertyTypes}
                    selected={pendingFilters.propertyTypes || []}
                    notSelected={pendingFilters.notPropertyTypes || []}
                    onChange={(selected: string[]) => updatePendingFilters({ propertyTypes: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notPropertyTypes: selected })}
                    placeholder={loadingInvestmentCriteriaOptions ? "Loading property types..." : "Select property types..."}
                    disabled={isLoading || loadingInvestmentCriteriaOptions}
                    showNotFilter={true}
                    filterKey="propertyTypes"
                  />

                  {/* Strategies */}
                  <EnhancedMultiSelect
                    label="Strategies"
                    options={investmentCriteriaOptions.strategies}
                    selected={pendingFilters.strategies || []}
                    notSelected={pendingFilters.notStrategies || []}
                    onChange={(selected: string[]) => updatePendingFilters({ strategies: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notStrategies: selected })}
                    placeholder={loadingInvestmentCriteriaOptions ? "Loading strategies..." : "Select strategies..."}
                    disabled={isLoading || loadingInvestmentCriteriaOptions}
                    showNotFilter={true}
                    filterKey="strategies"
                  />

                  {/* Target Return Range */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Target Return Range (%)</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <Input
                        type="number"
                        placeholder="Min (%)"
                        value={getRangeInputValue('targetReturnMin', pendingFilters.targetReturnMin)}
                        onChange={(e) => updateLocalRangeInput('targetReturnMin', e.target.value)}
                        onBlur={(e) => applyRangeFilter('targetReturnMin', e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('targetReturnMin', e.currentTarget.value)}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                      <Input
                        type="number"
                        placeholder="Max (%)"
                        value={getRangeInputValue('targetReturnMax', pendingFilters.targetReturnMax)}
                        onChange={(e) => updateLocalRangeInput('targetReturnMax', e.target.value)}
                        onBlur={(e) => applyRangeFilter('targetReturnMax', e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('targetReturnMax', e.currentTarget.value)}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                    </div>
                  </div>

                  {/* Enhanced Equity Fields */}
                  <div className="border-t pt-4 mt-6">
                    <h4 className="font-medium text-gray-800 mb-4 text-sm bg-green-50 p-2 rounded border-l-4 border-green-400">
                      📈 Enhanced Equity Criteria  
                    </h4>
                  </div>

                  {/* Ownership Requirement */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Ownership Requirement</Label>
                    <ReactMultiSelect
                      options={investmentCriteriaOptions.ownershipRequirements || []}
                      selected={pendingFilters.ownershipRequirement || []}
                      onChange={(selected: string[]) => updatePendingFilters({ ownershipRequirement: selected })}
                      placeholder={loadingInvestmentCriteriaOptions ? "Loading ownership requirements..." : "Select ownership requirements..."}
                      disabled={isLoading || loadingInvestmentCriteriaOptions}
                    />
                  </div>

                  {/* Minimum Yield on Cost Range */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Minimum Yield on Cost Range (%)</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Min yield (%)"
                        value={getRangeInputValue('minimumYieldOnCostMin', pendingFilters.minimumYieldOnCostMin)}
                        onChange={(e) => updateLocalRangeInput('minimumYieldOnCostMin', e.target.value)}
                        onBlur={(e) => applyRangeFilter('minimumYieldOnCostMin', e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('minimumYieldOnCostMin', e.currentTarget.value)}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Max yield (%)"
                        value={getRangeInputValue('minimumYieldOnCostMax', pendingFilters.minimumYieldOnCostMax)}
                        onChange={(e) => updateLocalRangeInput('minimumYieldOnCostMax', e.target.value)}
                        onBlur={(e) => applyRangeFilter('minimumYieldOnCostMax', e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('minimumYieldOnCostMax', e.currentTarget.value)}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                    </div>
                  </div>

                  {/* Max Leverage Tolerance Range */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Max Leverage Tolerance Range (%)</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Min leverage (%)"
                        value={getRangeInputValue('maxLeverageToleranceMin', pendingFilters.maxLeverageToleranceMin)}
                        onChange={(e) => updateLocalRangeInput('maxLeverageToleranceMin', e.target.value)}
                        onBlur={(e) => applyRangeFilter('maxLeverageToleranceMin', e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('maxLeverageToleranceMin', e.currentTarget.value)}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Max leverage (%)"
                        value={getRangeInputValue('maxLeverageToleranceMax', pendingFilters.maxLeverageToleranceMax)}
                        onChange={(e) => updateLocalRangeInput('maxLeverageToleranceMax', e.target.value)}
                        onBlur={(e) => applyRangeFilter('maxLeverageToleranceMax', e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('maxLeverageToleranceMax', e.currentTarget.value)}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                    </div>
                  </div>

                  {/* Geographic Filters */}
                  <div className="grid grid-cols-2 gap-4">
                    <EnhancedMultiSelect
                      label="Countries"
                      options={investmentCriteriaOptions.countries}
                      selected={pendingFilters.countries || []}
                      onChange={(selected: string[]) => updatePendingFilters({ countries: selected })}
                      placeholder={loadingInvestmentCriteriaOptions ? "Loading countries..." : "Select countries..."}
                      disabled={isLoading || loadingInvestmentCriteriaOptions}
                      showNotFilter={false}
                      filterKey="countries"
                    />

                    <EnhancedMultiSelect
                      label="Regions"
                      options={investmentCriteriaOptions.regions}
                      selected={pendingFilters.regions || []}
                      onChange={(selected: string[]) => updatePendingFilters({ regions: selected })}
                      placeholder={loadingInvestmentCriteriaOptions ? "Loading regions..." : "Select regions..."}
                      disabled={isLoading || loadingInvestmentCriteriaOptions}
                      showNotFilter={false}
                      filterKey="regions"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <EnhancedMultiSelect
                      label="States"
                      options={investmentCriteriaOptions.states}
                      selected={pendingFilters.states || []}
                      onChange={(selected: string[]) => updatePendingFilters({ states: selected })}
                      placeholder={loadingInvestmentCriteriaOptions ? "Loading states..." : "Select states..."}
                      disabled={isLoading || loadingInvestmentCriteriaOptions}
                      showNotFilter={false}
                      filterKey="states"
                    />

                    <EnhancedMultiSelect
                      label="Cities"
                      options={investmentCriteriaOptions.cities}
                      selected={pendingFilters.cities || []}
                      onChange={(selected: string[]) => updatePendingFilters({ cities: selected })}
                      placeholder={loadingInvestmentCriteriaOptions ? "Loading cities..." : "Select cities..."}
                      disabled={isLoading || loadingInvestmentCriteriaOptions}
                      showNotFilter={false}
                      filterKey="cities"
                    />
                  </div>

                  {/* Investment Criteria Debt - Borrower & Closing */}
                  <div className="border-t pt-4 mt-6">
                    <h4 className="font-medium text-gray-800 mb-4 text-sm bg-amber-50 p-2 rounded border-l-4 border-amber-400">
                      💰 Debt - Borrower & Closing
                    </h4>
                  </div>

                      {/* Loan Types */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan Types</Label>
                        <ReactMultiSelect
                          options={investmentCriteriaOptions.loanTypes}
                          selected={pendingFilters.loanTypes || []}
                          onChange={(selected: string[]) => updatePendingFilters({ loanTypes: selected })}
                          placeholder={loadingInvestmentCriteriaOptions ? "Loading loan types..." : "Select loan types..."}
                          disabled={isLoading || loadingInvestmentCriteriaOptions}
                        />
                      </div>

                      {/* Structured Loan Tranches */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Structured Loan Tranches</Label>
                        <ReactMultiSelect
                          options={investmentCriteriaOptions.structuredLoanTranches}
                          selected={pendingFilters.structuredLoanTranche || []}
                          onChange={(selected: string[]) => updatePendingFilters({ structuredLoanTranche: selected })}
                          placeholder={loadingInvestmentCriteriaOptions ? "Loading tranches..." : "Select tranches..."}
                          disabled={isLoading || loadingInvestmentCriteriaOptions}
                        />
                      </div>

                      {/* Loan Programs */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan Programs</Label>
                        <ReactMultiSelect
                          options={investmentCriteriaOptions.loanPrograms}
                          selected={pendingFilters.loanProgram || []}
                          onChange={(selected: string[]) => updatePendingFilters({ loanProgram: selected })}
                          placeholder={loadingInvestmentCriteriaOptions ? "Loading programs..." : "Select programs..."}
                          disabled={isLoading || loadingInvestmentCriteriaOptions}
                        />
                      </div>

                      {/* Recourse Loans */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Recourse Loans</Label>
                        <ReactMultiSelect
                          options={investmentCriteriaOptions.recourseLoans}
                          selected={pendingFilters.recourseLoan || []}
                          onChange={(selected: string[]) => updatePendingFilters({ recourseLoan: selected })}
                          placeholder={loadingInvestmentCriteriaOptions ? "Loading recourse options..." : "Select recourse options..."}
                          disabled={isLoading || loadingInvestmentCriteriaOptions}
                        />
                      </div>

                      {/* New Enhanced Debt Fields */}
                      <div className="border-t pt-4 mt-6">
                        <h4 className="font-medium text-gray-800 mb-4 text-sm bg-blue-50 p-2 rounded border-l-4 border-blue-400">
                          🔗 Enhanced Debt Criteria
                        </h4>
                      </div>

                      {/* Eligible Borrower */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Eligible Borrower</Label>
                        <ReactMultiSelect
                          options={investmentCriteriaOptions.eligibleBorrowers || []}
                          selected={pendingFilters.eligibleBorrower || []}
                          onChange={(selected: string[]) => updatePendingFilters({ eligibleBorrower: selected })}
                          placeholder={loadingInvestmentCriteriaOptions ? "Loading borrower types..." : "Select eligible borrowers..."}
                          disabled={isLoading || loadingInvestmentCriteriaOptions}
                        />
                      </div>

                      {/* Lien Position */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Lien Position</Label>
                        <ReactMultiSelect
                          options={investmentCriteriaOptions.lienPositions || []}
                          selected={pendingFilters.lienPosition || []}
                          onChange={(selected: string[]) => updatePendingFilters({ lienPosition: selected })}
                          placeholder={loadingInvestmentCriteriaOptions ? "Loading lien positions..." : "Select lien positions..."}
                          disabled={isLoading || loadingInvestmentCriteriaOptions}
                        />
                      </div>

                      {/* Rate Lock */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Rate Lock</Label>
                        <ReactMultiSelect
                          options={investmentCriteriaOptions.rateLocks || []}
                          selected={pendingFilters.rateLock || []}
                          onChange={(selected: string[]) => updatePendingFilters({ rateLock: selected })}
                          placeholder={loadingInvestmentCriteriaOptions ? "Loading rate lock options..." : "Select rate lock options..."}
                          disabled={isLoading || loadingInvestmentCriteriaOptions}
                        />
                      </div>

                      {/* Rate Type */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Rate Type</Label>
                        <ReactMultiSelect
                          options={investmentCriteriaOptions.rateTypes || []}
                          selected={pendingFilters.rateType || []}
                          onChange={(selected: string[]) => updatePendingFilters({ rateType: selected })}
                          placeholder={loadingInvestmentCriteriaOptions ? "Loading rate types..." : "Select rate types..."}
                          disabled={isLoading || loadingInvestmentCriteriaOptions}
                        />
                      </div>

                      {/* Amortization */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Amortization</Label>
                        <ReactMultiSelect
                          options={investmentCriteriaOptions.amortizations || []}
                          selected={pendingFilters.amortization || []}
                          onChange={(selected: string[]) => updatePendingFilters({ amortization: selected })}
                          placeholder={loadingInvestmentCriteriaOptions ? "Loading amortization options..." : "Select amortization options..."}
                          disabled={isLoading || loadingInvestmentCriteriaOptions}
                        />
                      </div>

                      {/* Loan Type Normalized */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan Type (Normalized)</Label>
                        <ReactMultiSelect
                          options={investmentCriteriaOptions.loanTypeNormalized || []}
                          selected={pendingFilters.loanTypeNormalized || []}
                          onChange={(selected: string[]) => updatePendingFilters({ loanTypeNormalized: selected })}
                          placeholder={loadingInvestmentCriteriaOptions ? "Loading normalized loan types..." : "Select normalized loan types..."}
                          disabled={isLoading || loadingInvestmentCriteriaOptions}
                        />
                      </div>

                      {/* Loan Min Debt Yield */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan Min Debt Yield</Label>
                        <ReactMultiSelect
                          options={investmentCriteriaOptions.loanMinDebtYield || []}
                          selected={pendingFilters.loanMinDebtYield || []}
                          onChange={(selected: string[]) => updatePendingFilters({ loanMinDebtYield: selected })}
                          placeholder={loadingInvestmentCriteriaOptions ? "Loading debt yield options..." : "Select debt yield options..."}
                          disabled={isLoading || loadingInvestmentCriteriaOptions}
                        />
                      </div>

                      {/* Closing Time Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Closing Time Range (days)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            placeholder="Min days"
                            value={getRangeInputValue('closingTimeMin', pendingFilters.closingTimeMin)}
                            onChange={(e) => updateLocalRangeInput('closingTimeMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('closingTimeMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('closingTimeMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max days"
                            value={getRangeInputValue('closingTimeMax', pendingFilters.closingTimeMax)}
                            onChange={(e) => updateLocalRangeInput('closingTimeMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('closingTimeMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('closingTimeMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Loan Origination Fee Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan Origination Fee Range (%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Min fee (%)"
                            value={getRangeInputValue('loanOriginationMinFeeMin', pendingFilters.loanOriginationMinFeeMin)}
                            onChange={(e) => updateLocalRangeInput('loanOriginationMinFeeMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanOriginationMinFeeMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanOriginationMinFeeMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Max fee (%)"
                            value={getRangeInputValue('loanOriginationMaxFeeMax', pendingFilters.loanOriginationMaxFeeMax)}
                            onChange={(e) => updateLocalRangeInput('loanOriginationMaxFeeMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanOriginationMaxFeeMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanOriginationMaxFeeMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Loan Exit Fee Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan Exit Fee Range (%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Min fee (%)"
                            value={getRangeInputValue('loanExitMinFeeMin', pendingFilters.loanExitMinFeeMin)}
                            onChange={(e) => updateLocalRangeInput('loanExitMinFeeMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanExitMinFeeMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanExitMinFeeMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Max fee (%)"
                            value={getRangeInputValue('loanExitMaxFeeMax', pendingFilters.loanExitMaxFeeMax)}
                            onChange={(e) => updateLocalRangeInput('loanExitMaxFeeMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanExitMaxFeeMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanExitMaxFeeMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* Loan Interest Rate Fields */}
                      <div className="space-y-4">
                        <Label className="text-sm font-medium text-gray-700">Loan Interest Rate Ranges (%)</Label>
                        
                        {/* SOFR Rate */}
                        <div className="space-y-2">
                          <Label className="text-xs text-gray-600">SOFR Rate</Label>
                          <div className="grid grid-cols-2 gap-2">
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Min SOFR (%)"
                              value={getRangeInputValue('loanInterestRateSofrMin', pendingFilters.loanInterestRateSofrMin)}
                              onChange={(e) => updateLocalRangeInput('loanInterestRateSofrMin', e.target.value)}
                              onBlur={(e) => applyRangeFilter('loanInterestRateSofrMin', e.target.value)}
                              disabled={isLoading}
                              className="bg-white border-gray-200 text-xs"
                            />
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Max SOFR (%)"
                              value={getRangeInputValue('loanInterestRateSofrMax', pendingFilters.loanInterestRateSofrMax)}
                              onChange={(e) => updateLocalRangeInput('loanInterestRateSofrMax', e.target.value)}
                              onBlur={(e) => applyRangeFilter('loanInterestRateSofrMax', e.target.value)}
                              disabled={isLoading}
                              className="bg-white border-gray-200 text-xs"
                            />
                          </div>
                        </div>

                        {/* WSJ Rate */}
                        <div className="space-y-2">
                          <Label className="text-xs text-gray-600">WSJ Rate</Label>
                          <div className="grid grid-cols-2 gap-2">
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Min WSJ (%)"
                              value={getRangeInputValue('loanInterestRateWsjMin', pendingFilters.loanInterestRateWsjMin)}
                              onChange={(e) => updateLocalRangeInput('loanInterestRateWsjMin', e.target.value)}
                              onBlur={(e) => applyRangeFilter('loanInterestRateWsjMin', e.target.value)}
                              disabled={isLoading}
                              className="bg-white border-gray-200 text-xs"
                            />
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Max WSJ (%)"
                              value={getRangeInputValue('loanInterestRateWsjMax', pendingFilters.loanInterestRateWsjMax)}
                              onChange={(e) => updateLocalRangeInput('loanInterestRateWsjMax', e.target.value)}
                              onBlur={(e) => applyRangeFilter('loanInterestRateWsjMax', e.target.value)}
                              disabled={isLoading}
                              className="bg-white border-gray-200 text-xs"
                            />
                          </div>
                        </div>

                        {/* Prime Rate */}
                        <div className="space-y-2">
                          <Label className="text-xs text-gray-600">Prime Rate</Label>
                          <div className="grid grid-cols-2 gap-2">
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Min Prime (%)"
                              value={getRangeInputValue('loanInterestRatePrimeMin', pendingFilters.loanInterestRatePrimeMin)}
                              onChange={(e) => updateLocalRangeInput('loanInterestRatePrimeMin', e.target.value)}
                              onBlur={(e) => applyRangeFilter('loanInterestRatePrimeMin', e.target.value)}
                              disabled={isLoading}
                              className="bg-white border-gray-200 text-xs"
                            />
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Max Prime (%)"
                              value={getRangeInputValue('loanInterestRatePrimeMax', pendingFilters.loanInterestRatePrimeMax)}
                              onChange={(e) => updateLocalRangeInput('loanInterestRatePrimeMax', e.target.value)}
                              onBlur={(e) => applyRangeFilter('loanInterestRatePrimeMax', e.target.value)}
                              disabled={isLoading}
                              className="bg-white border-gray-200 text-xs"
                            />
                          </div>
                        </div>

                        {/* Treasury Rates */}
                        <div className="space-y-2">
                          <Label className="text-xs text-gray-600">Treasury Rates</Label>
                          <div className="grid grid-cols-3 gap-2">
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="3Y Min (%)"
                              value={getRangeInputValue('loanInterestRate3ytMin', pendingFilters.loanInterestRate3ytMin)}
                              onChange={(e) => updateLocalRangeInput('loanInterestRate3ytMin', e.target.value)}
                              onBlur={(e) => applyRangeFilter('loanInterestRate3ytMin', e.target.value)}
                              disabled={isLoading}
                              className="bg-white border-gray-200 text-xs"
                            />
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="5Y Min (%)"
                              value={getRangeInputValue('loanInterestRate5ytMin', pendingFilters.loanInterestRate5ytMin)}
                              onChange={(e) => updateLocalRangeInput('loanInterestRate5ytMin', e.target.value)}
                              onBlur={(e) => applyRangeFilter('loanInterestRate5ytMin', e.target.value)}
                              disabled={isLoading}
                              className="bg-white border-gray-200 text-xs"
                            />
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="10Y Min (%)"
                              value={getRangeInputValue('loanInterestRate10ytMin', pendingFilters.loanInterestRate10ytMin)}
                              onChange={(e) => updateLocalRangeInput('loanInterestRate10ytMin', e.target.value)}
                              onBlur={(e) => applyRangeFilter('loanInterestRate10ytMin', e.target.value)}
                              disabled={isLoading}
                              className="bg-white border-gray-200 text-xs"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Loan Sizing Ranges */}
                      <div className="space-y-4">
                        <Label className="text-sm font-medium text-gray-700">Loan Sizing Ranges (%)</Label>
                        
                        {/* Loan to Value */}
                        <div className="space-y-2">
                          <Label className="text-xs text-gray-600">Loan to Value (LTV)</Label>
                          <div className="grid grid-cols-2 gap-2">
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Min LTV (%)"
                              value={getRangeInputValue('loanToValueMinMin', pendingFilters.loanToValueMinMin)}
                              onChange={(e) => updateLocalRangeInput('loanToValueMinMin', e.target.value)}
                              onBlur={(e) => applyRangeFilter('loanToValueMinMin', e.target.value)}
                              disabled={isLoading}
                              className="bg-white border-gray-200 text-xs"
                            />
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Max LTV (%)"
                              value={getRangeInputValue('loanToValueMaxMax', pendingFilters.loanToValueMaxMax)}
                              onChange={(e) => updateLocalRangeInput('loanToValueMaxMax', e.target.value)}
                              onBlur={(e) => applyRangeFilter('loanToValueMaxMax', e.target.value)}
                              disabled={isLoading}
                              className="bg-white border-gray-200 text-xs"
                            />
                          </div>
                        </div>

                        {/* Loan to Cost */}
                        <div className="space-y-2">
                          <Label className="text-xs text-gray-600">Loan to Cost (LTC)</Label>
                          <div className="grid grid-cols-2 gap-2">
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Min LTC (%)"
                              value={getRangeInputValue('loanToCostMinMin', pendingFilters.loanToCostMinMin)}
                              onChange={(e) => updateLocalRangeInput('loanToCostMinMin', e.target.value)}
                              onBlur={(e) => applyRangeFilter('loanToCostMinMin', e.target.value)}
                              disabled={isLoading}
                              className="bg-white border-gray-200 text-xs"
                            />
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Max LTC (%)"
                              value={getRangeInputValue('loanToCostMaxMax', pendingFilters.loanToCostMaxMax)}
                              onChange={(e) => updateLocalRangeInput('loanToCostMaxMax', e.target.value)}
                              onBlur={(e) => applyRangeFilter('loanToCostMaxMax', e.target.value)}
                              disabled={isLoading}
                              className="bg-white border-gray-200 text-xs"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Loan Term Ranges */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan Term Range (months)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            placeholder="Min term"
                            value={getRangeInputValue('minLoanTermMin', pendingFilters.minLoanTermMin)}
                            onChange={(e) => updateLocalRangeInput('minLoanTermMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('minLoanTermMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('minLoanTermMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max term"
                            value={getRangeInputValue('maxLoanTermMax', pendingFilters.maxLoanTermMax)}
                            onChange={(e) => updateLocalRangeInput('maxLoanTermMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('maxLoanTermMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('maxLoanTermMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>
                </CardContent>
              )}
            </Card>

          </div>

          {/* Enhanced Panel Footer V2 */}
          <div className="border-t border-gray-200 p-6 bg-gradient-to-r from-gray-50 to-purple-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                {activeFilterCount > 0 ? (
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-purple-600" />
                    <span className="font-medium text-gray-700">
                      {activeFilterCount} Advanced Filter{activeFilterCount !== 1 ? 's' : ''} Active
                    </span>
                    <Badge className="bg-amber-100 text-amber-800 border border-amber-200 font-semibold">
                      V2 Enhanced Query
                    </Badge>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4 text-gray-400" />
                    <span>No filters applied - showing all company data with V2 overview</span>
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-3">
                {/* Quick Reset */}
                {activeFilterCount > 0 && (
                  <Button
                    onClick={handleClearFilters}
                    className="text-gray-600 hover:text-red-600 bg-white border border-gray-200 hover:border-red-200 hover:bg-red-50 px-4 py-2 text-sm transition-colors"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reset All
                  </Button>
                )}
                
                {/* Apply Button */}
                <Button
                  onClick={activeFilterCount > 0 ? applyFilters : () => setIsFilterPanelOpen(false)}
                  className={`px-6 py-2 font-medium shadow-md hover:shadow-lg transition-all duration-200 ${
                    activeFilterCount > 0 
                      ? 'bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white' 
                      : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
                  }`}
                >
                  {activeFilterCount > 0 ? (
                    <>
                      <Database className="h-4 w-4 mr-2" />
                      Apply {activeFilterCount} V2 Filter{activeFilterCount !== 1 ? 's' : ''}
                    </>
                  ) : (
                    <>
                      <ChevronRight className="h-4 w-4 mr-2" />
                      Close Panel
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
