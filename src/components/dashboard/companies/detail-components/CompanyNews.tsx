'use client'

import { CompanyDetail } from '../shared/types'
import { FileText, ExternalLink, Calendar, Globe } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

interface CompanyNewsProps {
  company: CompanyDetail
}

export default function CompanyNews({ company }: CompanyNewsProps) {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-2">
        <h2 className="text-xl font-semibold">News & Deals</h2>
      </div>
      
      {company.news && company.news.length > 0 ? (
        <div className="grid grid-cols-1 gap-6">
          {company.news.map((newsItem, index) => (
            <div key={index} className="bg-white rounded-lg border border-gray-100 overflow-hidden hover:shadow-md transition-shadow">
              <div className="p-5">
                <div className="flex justify-between items-start mb-3">
                  <Badge className={`${newsItem.sentiment_score > 0 ? 'bg-green-100 text-green-800 border-green-200' : newsItem.sentiment_score < 0 ? 'bg-red-100 text-red-800 border-red-200' : 'bg-gray-100 text-gray-800 border-gray-200'}`}>
                    {newsItem.sentiment_score > 0 ? 'Positive' : newsItem.sentiment_score < 0 ? 'Negative' : 'Neutral'}
                  </Badge>
                  <div className="text-sm text-gray-500 flex items-center">
                    <Calendar className="h-3.5 w-3.5 mr-1.5" />
                    {new Date(newsItem.published_date).toLocaleDateString(undefined, {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </div>
                </div>
                
                <h3 className="font-semibold text-lg text-gray-900 mb-2">
                  <a
                    href={newsItem.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-700 hover:underline"
                  >
                    {newsItem.title}
                  </a>
                </h3>
                
                <div className="text-sm text-gray-500 mb-4 flex items-center">
                  <Globe className="h-3.5 w-3.5 mr-1.5" />
                  <span className="font-medium">{newsItem.publisher}</span>
                </div>
                
                <p className="text-gray-700 leading-relaxed">{newsItem.content}</p>
                
                <div className="mt-4 flex justify-end">
                  <a
                    href={newsItem.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                  >
                    Read full article
                    <ExternalLink className="h-3.5 w-3.5 ml-1.5" />
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-16 bg-white rounded-lg border border-gray-100">
          <FileText className="h-16 w-16 mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No news found</h3>
          <p className="text-gray-500">There are no news items available for this company.</p>
        </div>
      )}
    </div>
  )
} 