'use client'

import { CompanyDetail } from '../shared/types'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Calendar, Globe, MapPin, Users } from 'lucide-react'
import { getCompanyInitials, getIndustryColor, formatLocation } from '../shared/utils'

interface CompanyHeaderProps {
  company: CompanyDetail
}

export default function CompanyHeader({ company }: CompanyHeaderProps) {
  const industryColorClass = getIndustryColor(company.industry || '')
  const companyInitials = getCompanyInitials(company.company_name)
  const location = formatLocation(company)

  return (
    <div className="flex items-start flex-col md:flex-row md:justify-between">
      <div className="flex items-center">
        <Avatar className="h-16 w-16 rounded-full mr-4 shadow-md">
          <AvatarFallback className={`${industryColorClass} text-xl font-bold`}>
            {companyInitials}
          </AvatarFallback>
        </Avatar>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{company.company_name}</h1>
          <div className="flex flex-col mt-1">
            {company.industry && (
              <Badge className="mb-2 max-w-fit" variant="outline">
                {company.industry}
              </Badge>
            )}
            <div className="flex flex-wrap items-center gap-3 text-sm text-gray-500">
              {location && (
                <div className="flex items-center">
                  <MapPin className="h-3.5 w-3.5 mr-1 text-gray-400" />
                  <span>{location}</span>
                </div>
              )}
              {company.founded_year && (
                <div className="flex items-center">
                  <Calendar className="h-3.5 w-3.5 mr-1 text-gray-400" />
                  <span>Founded {company.founded_year}</span>
                </div>
              )}
              {company.company_website && (
                <div className="flex items-center">
                  <Globe className="h-3.5 w-3.5 mr-1 text-gray-400" />
                  <a
                    href={company.company_website.startsWith('http') ? company.company_website : `https://${company.company_website}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    {company.company_website.replace(/^https?:\/\/(www\.)?/, '')}
                  </a>
                </div>
              )}
              {((company.contacts?.length || 0) > 0 || (company.scraped_contacts?.length || 0) > 0) && (
                <div className="flex items-center">
                  <Users className="h-3.5 w-3.5 mr-1 text-gray-400" />
                  <span>
                    {(company.contacts?.length || 0) + (company.scraped_contacts?.length || 0)} Contacts
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="mt-4 md:mt-0">
        {company.summary && (
          <div className="max-w-2xl">
            <h3 className="text-sm font-medium text-gray-500 mb-1">Company Summary</h3>
            <p className="text-gray-700">{company.summary}</p>
          </div>
        )}
      </div>
    </div>
  )
} 