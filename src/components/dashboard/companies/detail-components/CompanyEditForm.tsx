'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Plus, Check, X } from 'lucide-react'
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

// Define the company data structure based on the new schema
interface CompanyDetail {
  // Core identifiers
  company_id?: number;
  company_name: string;
  company_type?: string;
  industry?: string;
  
  // Business details
  business_model?: string;
  investment_focus?: string[];
  investment_strategy_mission?: string;
  investment_strategy_approach?: string;
  
  // Contact information
  company_website?: string;
  company_phone?: string;
  secondary_phone?: string;
  main_email?: string;
  secondary_email?: string;
  company_linkedin?: string;
  twitter?: string;
  facebook?: string;
  instagram?: string;
  youtube?: string;
  
  // Additional fields from database
  llm_response?: string;
  llm_token_usage?: number;
  llm_used?: string;
  overview_v2_error?: string;
  overview_v2_date?: string;
  overview_v2_error_count?: number;
  overview_v2_status?: string;
  
  // Location information
  company_address?: string;
  company_city?: string;
  company_state?: string;
  company_zip?: string;
  company_country?: string;
  additional_address?: string;
  additional_city?: string;
  additional_state?: string;
  additional_zipcode?: string;
  additional_country?: string;
  
  // Financial and operational metrics
  fund_size?: number;
  aum?: number;
  number_of_properties?: number;
  number_of_offices?: number;
  office_locations?: string[];
  founded_year?: number;
  number_of_employees?: number;
  partnerships?: string[];
  
  // Capital and funding information
  balance_sheet_strength?: string;
  funding_sources?: string[];
  recent_capital_raises?: string;
  typical_debt_to_equity_ratio?: number;
  development_fee_structure?: string;
  key_equity_partners?: string[];
  key_debt_partners?: string[];
  
  // Market positioning
  market_cycle_positioning?: string;
  urban_vs_suburban_preference?: string;
  sustainability_esg_focus?: boolean;
  technology_proptech_adoption?: boolean;
  adaptive_reuse_experience?: boolean;
  regulatory_zoning_expertise?: boolean;
  
  // Investment structure
  investment_vehicle_type?: string;
  active_fund_name_series?: string;
  fund_size_active_fund?: number;
  fundraising_status?: string;
  
  // Lending information
  lender_type?: string;
  annual_loan_volume?: number;
  lending_origin?: string;
  portfolio_health?: string;
  
  // Leadership and governance
  board_of_directors?: string[];
  key_executives?: string[];
  founder_background?: string;
  company_history?: string;
  
  // Public company information
  stock_ticker_symbol?: string;
  stock_exchange?: string;
  market_capitalization?: number;
  annual_revenue?: number;
  net_income?: number;
  ebitda?: number;
  profit_margin?: number;
  credit_rating?: string;
  quarterly_earnings_link?: string;
  
  // Market position
  products_services_description?: string;
  target_customer_profile?: string;
  major_competitors?: string[];
  market_share_percentage?: number;
  unique_selling_proposition?: string;
  industry_awards_recognitions?: string[];
  
  // Corporate structure
  corporate_structure?: string;
  parent_company?: string;
  subsidiaries?: string[];
  
  // Activity and relationship management
  dry_powder?: number;
  annual_deployment_target?: number;
  transactions_completed_last_12m?: number;
  internal_relationship_manager?: string;
  last_contact_date?: string;
  pipeline_status?: string;
  role_in_previous_deal?: string;
  total_transaction_volume_ytd?: number;
  deal_count_ytd?: number;
  average_deal_size?: number;
  portfolio_size_sqft?: number;
  portfolio_asset_count?: number;
  
  // Metadata
  recent_news_sentiment?: string;
  data_source?: string;
  last_updated_timestamp?: string;
  data_confidence_score?: number;
}

interface CompanyEditFormProps {
  company: CompanyDetail
  onChange: (updatedCompany: CompanyDetail) => void
}

interface FormOptions {
  companyTypes: string[]
  industries: string[]
  investmentStrategies: string[]
  investmentVehicleTypes: string[]
  lenderTypes: string[]
  stockExchanges: string[]
  creditRatings: string[]
  corporateStructures: string[]
  pipelineStatuses: string[]
  roleInPreviousDeals: string[]
  recentNewsSentiments: string[]
  fundingStatuses: string[]
}

interface CustomSelectProps {
  options: string[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  addNewLabel?: string;
  onAddNew?: (newValue: string) => void;
  disabled?: boolean;
}

interface ArrayInputProps {
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  label: string;
  addButtonText?: string;
}

// Validation functions
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const validatePhone = (phone: string): boolean => {
  // Accepts formats: (*************, ************, ************, 1234567890, ******-456-7890
  const phoneRegex = /^(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

const normalizeUrl = (url: string): string => {
  if (!url) return url;
  
  // Remove whitespace
  url = url.trim();
  
  // If it doesn't start with http:// or https://, add https://
  if (!url.match(/^https?:\/\//)) {
    url = 'https://' + url;
  }
  
  return url;
};

const isValidUrl = (url: string): boolean => {
  if (!url) return true; // Empty URLs are valid
  try {
    new URL(normalizeUrl(url));
    return true;
  } catch {
    return false;
  }
};

// Phone number validation - only allows digits, spaces, parentheses, dashes, dots, and plus sign
const validatePhoneNumber = (phone: string): boolean => {
  if (!phone) return true; // Empty phone numbers are valid
  const phoneRegex = /^[\d\s\(\)\-\+\.]+$/;
  return phoneRegex.test(phone);
};

// Format phone number input - remove invalid characters
const formatPhoneNumber = (phone: string): string => {
  return phone.replace(/[^\d\s\(\)\-\+\.]/g, '');
};

function CustomSelect({
  options,
  value,
  onValueChange,
  placeholder = "Select...",
  addNewLabel = "Add New Option...",
  onAddNew = (newValue: string) => {},
  disabled = false,
}: CustomSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [localOptions, setLocalOptions] = useState<string[]>(options || []);
  const inputRef = useRef<HTMLInputElement>(null);

  // Update local options when props change
  useEffect(() => {
    setLocalOptions(options || []);
  }, [options]);

  const filteredOptions = localOptions.filter((option) =>
    option.toLowerCase().includes(searchValue.toLowerCase())
  );

  const handleAddNew = () => {
    if (searchValue.trim()) {
      const newValue = searchValue.trim();
      // Add to local options for this session
      setLocalOptions(prev => [...prev, newValue]);
      // Call the onAddNew callback (this will update the form field)
      onAddNew(newValue);
      // Set the value
      onValueChange(newValue);
      setSearchValue("");
      setIsOpen(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (searchValue.trim() && !localOptions.some(option => 
        option.toLowerCase() === searchValue.trim().toLowerCase()
      )) {
        handleAddNew();
      } else if (filteredOptions.length > 0) {
        onValueChange(filteredOptions[0]);
        setIsOpen(false);
      }
    } else if (e.key === "Escape") {
      setIsOpen(false);
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={isOpen}
          className="w-full justify-between text-left"
          onClick={() => setIsOpen(!isOpen)}
          disabled={disabled}
        >
          <span className="truncate flex-1 text-left">
            {value || placeholder}
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0">
        <div className="p-2">
          <Input
            ref={inputRef}
            placeholder="Search..."
            value={searchValue}
            onChange={(e) => {
              setSearchValue(e.target.value);
            }}
            onKeyDown={handleKeyDown}
            className="mb-2"
          />
          <div className="max-h-60 overflow-auto">
            {filteredOptions.map((option) => (
              <div
                key={option}
                className="flex items-center px-2 py-1.5 text-sm hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  onValueChange(option);
                  setIsOpen(false);
                }}
              >
                {option}
                {value === option && <Check className="ml-auto h-4 w-4" />}
              </div>
            ))}
            {searchValue.trim() && !localOptions.some(option => 
              option.toLowerCase() === searchValue.trim().toLowerCase()
            ) && (
              <div
                className="flex items-center px-2 py-1.5 text-sm hover:bg-gray-100 cursor-pointer text-blue-600"
                onClick={handleAddNew}
              >
                <Plus className="mr-2 h-4 w-4" />
                {addNewLabel}
              </div>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

function ArrayInput({ value, onChange, placeholder, label, addButtonText = "Add Item" }: ArrayInputProps) {
  const [newItem, setNewItem] = useState("");

  const handleAdd = () => {
    if (newItem.trim()) {
      onChange([...value, newItem.trim()]);
      setNewItem("");
    }
  };

  const handleRemove = (index: number) => {
    onChange(value.filter((_, i) => i !== index));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAdd();
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex gap-2">
        <Input
          value={newItem}
          onChange={(e) => setNewItem(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          className="flex-1"
        />
        <Button
          type="button"
          onClick={handleAdd}
          disabled={!newItem.trim()}
          size="sm"
          className="px-3"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
      
      {value.length > 0 && (
        <div className="space-y-2">
          {value.map((item, index) => (
            <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
              <span className="flex-1 text-sm">{item}</span>
              <Button
                type="button"
                onClick={() => handleRemove(index)}
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default function CompanyEditForm({ company, onChange }: CompanyEditFormProps) {
  // Form options state
  const [formOptions, setFormOptions] = useState<FormOptions>({
    companyTypes: [],
    industries: [],
    investmentStrategies: [],
    investmentVehicleTypes: [],
    lenderTypes: [],
    stockExchanges: [],
    creditRatings: [],
    corporateStructures: [],
    pipelineStatuses: [],
    roleInPreviousDeals: [],
    recentNewsSentiments: [],
    fundingStatuses: []
  })
  const [isLoadingOptions, setIsLoadingOptions] = useState(true)
  const [urlErrors, setUrlErrors] = useState<{ 
    company_website?: string; 
    company_linkedin?: string; 
    twitter?: string; 
    facebook?: string; 
    instagram?: string; 
    youtube?: string; 
    quarterly_earnings_link?: string; 
  }>({})
  
  const [phoneErrors, setPhoneErrors] = useState<{
    company_phone?: string;
    secondary_phone?: string;
  }>({})
  
  // Fetch form options from database
  useEffect(() => {
    const fetchFormOptions = async () => {
      setIsLoadingOptions(true)
      try {
        const response = await fetch('/api/mapping-tables/form-options')
        if (response.ok) {
          const result = await response.json()
          if (result.success && result.data) {
            setFormOptions({
              companyTypes: result.data.companyTypes || [],
              industries: result.data.industries || [],
              investmentStrategies: result.data.investmentStrategies || [],
              investmentVehicleTypes: result.data.investmentVehicleTypes || [],
              lenderTypes: result.data.lenderTypes || [],
              stockExchanges: result.data.stockExchanges || [],
              creditRatings: result.data.creditRatings || [],
              corporateStructures: result.data.corporateStructures || [],
              pipelineStatuses: result.data.pipelineStatuses || [],
              roleInPreviousDeals: result.data.roleInPreviousDeals || [],
              recentNewsSentiments: result.data.recentNewsSentiments || [],
              fundingStatuses: result.data.fundingStatuses || [],
            });
          }
        }
      } catch (error) {
        console.error('Error fetching form options:', error)
      } finally {
        setIsLoadingOptions(false)
      }
    }

    fetchFormOptions()
  }, [])
  
  // Helper function to update a specific field
  const updateField = useCallback((field: string, value: any) => {
    onChange({
      ...company,
      [field]: value
    })
  }, [company, onChange])
  
  // Helper function to update phone fields with validation
  const updatePhoneField = useCallback((field: string, value: string) => {
    const formattedValue = formatPhoneNumber(value);
    
    // Validate phone number
    if (formattedValue && !validatePhoneNumber(formattedValue)) {
      setPhoneErrors(prev => ({
        ...prev,
        [field]: 'Phone number can only contain digits, spaces, parentheses, dashes, dots, and plus sign'
      }));
      return;
    }
    
    // Clear error if validation passes
    if (phoneErrors[field as keyof typeof phoneErrors]) {
      setPhoneErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
    
    onChange({
      ...company,
      [field]: formattedValue
    });
  }, [company, onChange, phoneErrors])
  
  // Handle URL field blur for validation and normalization
  const handleUrlBlur = useCallback(
    (fieldName: 'company_website' | 'company_linkedin' | 'twitter' | 'facebook' | 'instagram' | 'youtube' | 'quarterly_earnings_link') => {
      const value = company[fieldName as keyof CompanyDetail] as string;
      if (!value) return;

      if (!isValidUrl(value)) {
        setUrlErrors(prev => ({
          ...prev,
          [fieldName]: 'Please enter a valid URL'
        }));
        return;
      }

      // Clear any existing error
      setUrlErrors(prev => ({
        ...prev,
        [fieldName]: undefined
      }));

      // Normalize the URL
      const normalizedUrl = normalizeUrl(value);
      if (normalizedUrl !== value) {
        updateField(fieldName, normalizedUrl);
      }
    },
    [company, updateField]
  );

  const handleNumberChange = useCallback(
    (fieldName: keyof CompanyDetail) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const val = e.target.value ? parseFloat(e.target.value) : undefined;
      updateField(fieldName as string, val);
    },
    [updateField]
  );

  return (
    <div className="max-w-6xl mx-auto p-8 bg-gradient-to-br from-blue-50 to-indigo-50 min-h-screen">
      <div className="bg-white rounded-xl shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-8 py-6">
          <h1 className="text-3xl font-bold text-white mb-2">
            Edit Company
          </h1>
          <p className="text-blue-100">
            Update company information and details
          </p>
        </div>

        <div className="p-8 space-y-8">
          {/* Company Basics Section */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Company Information</h2>
                <p className="text-sm text-gray-500">Basic company details and identification</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="company_name" className="text-sm font-medium text-gray-700">
                  Company Name *
                </Label>
                <Input
                  id="company_name"
                  value={company.company_name || ""}
                  onChange={(e) => updateField('company_name', e.target.value)}
                  placeholder="Enter company name"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 truncate"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="company_type" className="text-sm font-medium text-gray-700">
                  Company Type
                </Label>
                <CustomSelect
                  options={formOptions.companyTypes}
                  value={company.company_type || ""}
                  onValueChange={(value: string) => updateField('company_type', value)}
                  placeholder={isLoadingOptions ? "Loading..." : "Select company type"}
                  addNewLabel="Add new company type..."
                  onAddNew={(newValue: string) => updateField('company_type', newValue)}
                  disabled={isLoadingOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="industry" className="text-sm font-medium text-gray-700">
                  Industry
                </Label>
                <Input
                  id="industry"
                  value={company.industry || ""}
                  onChange={(e) => updateField('industry', e.target.value)}
                  placeholder="e.g., Technology, Real Estate"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="founded_year" className="text-sm font-medium text-gray-700">
                  Founded Year
                </Label>
                <Input
                  id="founded_year"
                  type="number"
                  value={company.founded_year || ""}
                  onChange={handleNumberChange("founded_year")}
                  placeholder="e.g., 1995"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="business_model" className="text-sm font-medium text-gray-700">
                  Business Model
                </Label>
                <Input
                  id="business_model"
                  value={company.business_model || ""}
                  onChange={(e) => updateField('business_model', e.target.value)}
                  placeholder="e.g., B2B SaaS, Marketplace"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="number_of_employees" className="text-sm font-medium text-gray-700">
                  Number of Employees
                </Label>
                <Input
                  id="number_of_employees"
                  type="number"
                  value={company.number_of_employees || ""}
                  onChange={handleNumberChange("number_of_employees")}
                  placeholder="e.g., 500"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Contact & Social Section */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Contact & Social</h2>
                <p className="text-sm text-gray-500">Contact information and social media presence</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="company_website" className="text-sm font-medium text-gray-700">
                  Website
                </Label>
                <Input
                  id="company_website"
                  value={company.company_website || ""}
                  onChange={(e) => updateField('company_website', e.target.value)}
                  onBlur={() => handleUrlBlur('company_website')}
                  placeholder="https://example.com"
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 truncate ${urlErrors.company_website ? 'border-red-300' : ''}`}
                />
                {urlErrors.company_website && (
                  <p className="text-red-600 text-xs">{urlErrors.company_website}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="company_phone" className="text-sm font-medium text-gray-700">
                  Main Phone
                </Label>
                <Input
                  id="company_phone"
                  value={company.company_phone || ""}
                  onChange={(e) => updatePhoneField('company_phone', e.target.value)}
                  placeholder="+****************"
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 ${phoneErrors.company_phone ? 'border-red-300' : ''}`}
                />
                {phoneErrors.company_phone && (
                  <p className="text-red-600 text-xs">{phoneErrors.company_phone}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="main_email" className="text-sm font-medium text-gray-700">
                  Main Email
                </Label>
                <Input
                  id="main_email"
                  type="email"
                  value={company.main_email || ""}
                  onChange={(e) => updateField('main_email', e.target.value)}
                  placeholder="<EMAIL>"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="company_linkedin" className="text-sm font-medium text-gray-700">
                  LinkedIn
                </Label>
                <Input
                  id="company_linkedin"
                  value={company.company_linkedin || ""}
                  onChange={(e) => updateField('company_linkedin', e.target.value)}
                  onBlur={() => handleUrlBlur('company_linkedin')}
                  placeholder="https://linkedin.com/company/..."
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 truncate ${urlErrors.company_linkedin ? 'border-red-300' : ''}`}
                />
                {urlErrors.company_linkedin && (
                  <p className="text-red-600 text-xs">{urlErrors.company_linkedin}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="twitter" className="text-sm font-medium text-gray-700">
                  Twitter
                </Label>
                <Input
                  id="twitter"
                  value={company.twitter || ""}
                  onChange={(e) => updateField('twitter', e.target.value)}
                  onBlur={() => handleUrlBlur('twitter')}
                  placeholder="https://twitter.com/..."
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 truncate ${urlErrors.twitter ? 'border-red-300' : ''}`}
                />
                {urlErrors.twitter && (
                  <p className="text-red-600 text-xs">{urlErrors.twitter}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="facebook" className="text-sm font-medium text-gray-700">
                  Facebook
                </Label>
                <Input
                  id="facebook"
                  value={company.facebook || ""}
                  onChange={(e) => updateField('facebook', e.target.value)}
                  onBlur={() => handleUrlBlur('facebook')}
                  placeholder="https://facebook.com/..."
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 truncate ${urlErrors.facebook ? 'border-red-300' : ''}`}
                />
                {urlErrors.facebook && (
                  <p className="text-red-600 text-xs">{urlErrors.facebook}</p>
                )}
              </div>
            </div>
          </div>

          {/* Location Section */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Location</h2>
                <p className="text-sm text-gray-500">Company headquarters and office locations</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="company_address" className="text-sm font-medium text-gray-700">
                  Headquarters Address
                </Label>
                <Input
                  id="company_address"
                  value={company.company_address || ""}
                  onChange={(e) => updateField('company_address', e.target.value)}
                  placeholder="123 Main Street"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 truncate"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="company_city" className="text-sm font-medium text-gray-700">
                  City
                </Label>
                <Input
                  id="company_city"
                  value={company.company_city || ""}
                  onChange={(e) => updateField('company_city', e.target.value)}
                  placeholder="San Francisco"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="company_state" className="text-sm font-medium text-gray-700">
                  State
                </Label>
                <Input
                  id="company_state"
                  value={company.company_state || ""}
                  onChange={(e) => updateField('company_state', e.target.value)}
                  placeholder="CA"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="company_zip" className="text-sm font-medium text-gray-700">
                  ZIP Code
                </Label>
                <Input
                  id="company_zip"
                  value={company.company_zip || ""}
                  onChange={(e) => updateField('company_zip', e.target.value)}
                  placeholder="94105"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="company_country" className="text-sm font-medium text-gray-700">
                  Country
                </Label>
                <Input
                  id="company_country"
                  value={company.company_country || ""}
                  onChange={(e) => updateField('company_country', e.target.value)}
                  placeholder="United States"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Financial Section */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Financial Information</h2>
                <p className="text-sm text-gray-500">Financial metrics and performance data</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="fund_size" className="text-sm font-medium text-gray-700">
                  Fund Size
                </Label>
                <Input
                  id="fund_size"
                  type="number"
                  value={company.fund_size || ''}
                  onChange={handleNumberChange("fund_size")}
                  placeholder="e.g., **********"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="aum" className="text-sm font-medium text-gray-700">
                  AUM
                </Label>
                <Input
                  id="aum"
                  type="number"
                  value={company.aum || ''}
                  onChange={handleNumberChange("aum")}
                  placeholder="e.g., *********0"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="annual_revenue" className="text-sm font-medium text-gray-700">
                  Annual Revenue
                </Label>
                <Input
                  id="annual_revenue"
                  type="number"
                  value={company.annual_revenue || ''}
                  onChange={handleNumberChange("annual_revenue")}
                  placeholder="e.g., *********"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="market_capitalization" className="text-sm font-medium text-gray-700">
                  Market Cap
                </Label>
                <Input
                  id="market_capitalization"
                  type="number"
                  value={company.market_capitalization || ''}
                  onChange={handleNumberChange("market_capitalization")}
                  placeholder="e.g., **********0"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="credit_rating" className="text-sm font-medium text-gray-700">
                  Credit Rating
                </Label>
                <CustomSelect
                  options={formOptions.creditRatings}
                  value={company.credit_rating || ""}
                  onValueChange={(value: string) => updateField('credit_rating', value)}
                  placeholder={isLoadingOptions ? "Loading..." : "Select credit rating"}
                  addNewLabel="Add new credit rating..."
                  onAddNew={(newValue: string) => updateField('credit_rating', newValue)}
                  disabled={isLoadingOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stock_ticker_symbol" className="text-sm font-medium text-gray-700">
                  Stock Ticker
                </Label>
                <Input
                  id="stock_ticker_symbol"
                  value={company.stock_ticker_symbol || ""}
                  onChange={(e) => updateField('stock_ticker_symbol', e.target.value)}
                  placeholder="e.g., AAPL"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Investment Details Section */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Investment Details</h2>
                <p className="text-sm text-gray-500">Investment strategy and portfolio information</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="investment_vehicle_type" className="text-sm font-medium text-gray-700">
                  Investment Vehicle Type
                </Label>
                <CustomSelect
                  options={formOptions.investmentVehicleTypes}
                  value={company.investment_vehicle_type || ""}
                  onValueChange={(value: string) => updateField('investment_vehicle_type', value)}
                  placeholder={isLoadingOptions ? "Loading..." : "Select vehicle type"}
                  addNewLabel="Add new vehicle type..."
                  onAddNew={(newValue: string) => updateField('investment_vehicle_type', newValue)}
                  disabled={isLoadingOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lender_type" className="text-sm font-medium text-gray-700">
                  Lender Type
                </Label>
                <CustomSelect
                  options={formOptions.lenderTypes}
                  value={company.lender_type || ""}
                  onValueChange={(value: string) => updateField('lender_type', value)}
                  placeholder={isLoadingOptions ? "Loading..." : "Select lender type"}
                  addNewLabel="Add new lender type..."
                  onAddNew={(newValue: string) => updateField('lender_type', newValue)}
                  disabled={isLoadingOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="fundraising_status" className="text-sm font-medium text-gray-700">
                  Fundraising Status
                </Label>
                <CustomSelect
                  options={formOptions.fundingStatuses}
                  value={company.fundraising_status || ""}
                  onValueChange={(value: string) => updateField('fundraising_status', value)}
                  placeholder={isLoadingOptions ? "Loading..." : "Select status"}
                  addNewLabel="Add new funding status..."
                  onAddNew={(newValue: string) => updateField('fundraising_status', newValue)}
                  disabled={isLoadingOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="number_of_properties" className="text-sm font-medium text-gray-700">
                  Number of Properties
                </Label>
                <Input
                  id="number_of_properties"
                  type="number"
                  value={company.number_of_properties || ''}
                  onChange={handleNumberChange("number_of_properties")}
                  placeholder="e.g., 50"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="average_deal_size" className="text-sm font-medium text-gray-700">
                  Average Deal Size
                </Label>
                <Input
                  id="average_deal_size"
                  type="number"
                  value={company.average_deal_size || ''}
                  onChange={handleNumberChange("average_deal_size")}
                  placeholder="e.g., 50000000"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="portfolio_size_sqft" className="text-sm font-medium text-gray-700">
                  Portfolio Size (sq ft)
                </Label>
                <Input
                  id="portfolio_size_sqft"
                  type="number"
                  value={company.portfolio_size_sqft || ''}
                  onChange={handleNumberChange("portfolio_size_sqft")}
                  placeholder="e.g., 1000000"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="investment_focus" className="text-sm font-medium text-gray-700">
                  Investment Focus
                </Label>
                <ArrayInput
                  value={company.investment_focus || []}
                  onChange={(value) => updateField('investment_focus', value)}
                  placeholder="e.g., Multifamily, Office, Retail, Industrial"
                  label="Investment Focus"
                />
              </div>
            </div>
          </div>

          {/* Partnership & Leadership Section */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Partnership & Leadership</h2>
                <p className="text-sm text-gray-500">Strategic partnerships and key personnel</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="partnerships" className="text-sm font-medium text-gray-700">
                  Partnerships
                </Label>
                <ArrayInput
                  value={company.partnerships || []}
                  onChange={(value) => updateField('partnerships', value)}
                  placeholder="e.g., Joint Venture Partner A, Strategic Alliance B"
                  label="Partnerships"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="key_equity_partners" className="text-sm font-medium text-gray-700">
                  Key Equity Partners
                </Label>
                <ArrayInput
                  value={company.key_equity_partners || []}
                  onChange={(value) => updateField('key_equity_partners', value)}
                  placeholder="e.g., Equity Partner A, Equity Partner B"
                  label="Key Equity Partners"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="key_debt_partners" className="text-sm font-medium text-gray-700">
                  Key Debt Partners
                </Label>
                <ArrayInput
                  value={company.key_debt_partners || []}
                  onChange={(value) => updateField('key_debt_partners', value)}
                  placeholder="e.g., Debt Partner A, Debt Partner B"
                  label="Key Debt Partners"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="board_of_directors" className="text-sm font-medium text-gray-700">
                  Board of Directors
                </Label>
                <ArrayInput
                  value={company.board_of_directors || []}
                  onChange={(value) => updateField('board_of_directors', value)}
                  placeholder="e.g., John Doe, Jane Smith, Bob Johnson"
                  label="Board of Directors"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="key_executives" className="text-sm font-medium text-gray-700">
                  Key Executives
                </Label>
                <ArrayInput
                  value={company.key_executives || []}
                  onChange={(value) => updateField('key_executives', value)}
                  placeholder="e.g., CEO John Doe, CFO Jane Smith"
                  label="Key Executives"
                />
              </div>
            </div>
          </div>

          {/* Leadership & Structure Section */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Leadership & Structure</h2>
                <p className="text-sm text-gray-500">Corporate structure and key personnel</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="corporate_structure" className="text-sm font-medium text-gray-700">
                  Corporate Structure
                </Label>
                <CustomSelect
                  options={formOptions.corporateStructures}
                  value={company.corporate_structure || ""}
                  onValueChange={(value: string) => updateField('corporate_structure', value)}
                  placeholder={isLoadingOptions ? "Loading..." : "Select structure"}
                  addNewLabel="Add new corporate structure..."
                  onAddNew={(newValue: string) => updateField('corporate_structure', newValue)}
                  disabled={isLoadingOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="parent_company" className="text-sm font-medium text-gray-700">
                  Parent Company
                </Label>
                <Input
                  id="parent_company"
                  value={company.parent_company || ""}
                  onChange={(e) => updateField('parent_company', e.target.value)}
                  placeholder="Parent company name"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="active_fund_name_series" className="text-sm font-medium text-gray-700">
                  Active Fund Name/Series
                </Label>
                <Input
                  id="active_fund_name_series"
                  value={company.active_fund_name_series || ""}
                  onChange={(e) => updateField('active_fund_name_series', e.target.value)}
                  placeholder="e.g., Fund III, Series A"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="fund_size_active_fund" className="text-sm font-medium text-gray-700">
                  Active Fund Size
                </Label>
                <Input
                  id="fund_size_active_fund"
                  type="number"
                  value={company.fund_size_active_fund || ''}
                  onChange={handleNumberChange("fund_size_active_fund")}
                  placeholder="e.g., *********"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="internal_relationship_manager" className="text-sm font-medium text-gray-700">
                  Internal Relationship Manager
                </Label>
                <Input
                  id="internal_relationship_manager"
                  value={company.internal_relationship_manager || ""}
                  onChange={(e) => updateField('internal_relationship_manager', e.target.value)}
                  placeholder="Relationship manager name"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="last_contact_date" className="text-sm font-medium text-gray-700">
                  Last Contact Date
                </Label>
                <Input
                  id="last_contact_date"
                  type="date"
                  value={company.last_contact_date || ""}
                  onChange={(e) => updateField('last_contact_date', e.target.value)}
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Public Company Data Section */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Public Company Data</h2>
                <p className="text-sm text-gray-500">Public company financial and market information</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="stock_exchange" className="text-sm font-medium text-gray-700">
                  Stock Exchange
                </Label>
                <CustomSelect
                  options={formOptions.stockExchanges}
                  value={company.stock_exchange || ""}
                  onValueChange={(value: string) => updateField('stock_exchange', value)}
                  placeholder={isLoadingOptions ? "Loading..." : "Select exchange"}
                  addNewLabel="Add new stock exchange..."
                  onAddNew={(newValue: string) => updateField('stock_exchange', newValue)}
                  disabled={isLoadingOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="net_income" className="text-sm font-medium text-gray-700">
                  Net Income
                </Label>
                <Input
                  id="net_income"
                  type="number"
                  value={company.net_income || ''}
                  onChange={handleNumberChange("net_income")}
                  placeholder="e.g., 50000000"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="ebitda" className="text-sm font-medium text-gray-700">
                  EBITDA
                </Label>
                <Input
                  id="ebitda"
                  type="number"
                  value={company.ebitda || ''}
                  onChange={handleNumberChange("ebitda")}
                  placeholder="e.g., 75000000"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="profit_margin" className="text-sm font-medium text-gray-700">
                  Profit Margin (%)
                </Label>
                <Input
                  id="profit_margin"
                  type="number"
                  step="0.01"
                  value={company.profit_margin || ''}
                  onChange={handleNumberChange("profit_margin")}
                  placeholder="e.g., 15.5"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="market_share_percentage" className="text-sm font-medium text-gray-700">
                  Market Share (%)
                </Label>
                <Input
                  id="market_share_percentage"
                  type="number"
                  step="0.01"
                  value={company.market_share_percentage || ''}
                  onChange={handleNumberChange("market_share_percentage")}
                  placeholder="e.g., 12.5"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="quarterly_earnings_link" className="text-sm font-medium text-gray-700">
                  Quarterly Earnings Link
                </Label>
                <Input
                  id="quarterly_earnings_link"
                  value={company.quarterly_earnings_link || ""}
                  onChange={(e) => updateField('quarterly_earnings_link', e.target.value)}
                  onBlur={() => handleUrlBlur('quarterly_earnings_link')}
                  placeholder="https://investor.website.com/earnings"
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 ${urlErrors.quarterly_earnings_link ? 'border-red-300' : ''}`}
                />
                {urlErrors.quarterly_earnings_link && (
                  <p className="text-red-600 text-xs">{urlErrors.quarterly_earnings_link}</p>
                )}
              </div>
            </div>
          </div>

          {/* Relationship & Pipeline Section */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Relationship & Pipeline</h2>
                <p className="text-sm text-gray-500">Deal pipeline and relationship management</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="pipeline_status" className="text-sm font-medium text-gray-700">
                  Pipeline Status
                </Label>
                <CustomSelect
                  options={formOptions.pipelineStatuses}
                  value={company.pipeline_status || ""}
                  onValueChange={(value: string) => updateField('pipeline_status', value)}
                  placeholder={isLoadingOptions ? "Loading..." : "Select status"}
                  addNewLabel="Add new pipeline status..."
                  onAddNew={(newValue: string) => updateField('pipeline_status', newValue)}
                  disabled={isLoadingOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="role_in_previous_deal" className="text-sm font-medium text-gray-700">
                  Role in Previous Deal
                </Label>
                <CustomSelect
                  options={formOptions.roleInPreviousDeals}
                  value={company.role_in_previous_deal || ""}
                  onValueChange={(value: string) => updateField('role_in_previous_deal', value)}
                  placeholder={isLoadingOptions ? "Loading..." : "Select role"}
                  addNewLabel="Add new role..."
                  onAddNew={(newValue: string) => updateField('role_in_previous_deal', newValue)}
                  disabled={isLoadingOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="recent_news_sentiment" className="text-sm font-medium text-gray-700">
                  Recent News Sentiment
                </Label>
                <CustomSelect
                  options={formOptions.recentNewsSentiments}
                  value={company.recent_news_sentiment || ""}
                  onValueChange={(value: string) => updateField('recent_news_sentiment', value)}
                  placeholder={isLoadingOptions ? "Loading..." : "Select sentiment"}
                  addNewLabel="Add new sentiment..."
                  onAddNew={(newValue: string) => updateField('recent_news_sentiment', newValue)}
                  disabled={isLoadingOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="transactions_completed_last_12m" className="text-sm font-medium text-gray-700">
                  Transactions (Last 12M)
                </Label>
                <Input
                  id="transactions_completed_last_12m"
                  type="number"
                  value={company.transactions_completed_last_12m || ''}
                  onChange={handleNumberChange("transactions_completed_last_12m")}
                  placeholder="e.g., 25"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="total_transaction_volume_ytd" className="text-sm font-medium text-gray-700">
                  Transaction Volume YTD
                </Label>
                <Input
                  id="total_transaction_volume_ytd"
                  type="number"
                  value={company.total_transaction_volume_ytd || ''}
                  onChange={handleNumberChange("total_transaction_volume_ytd")}
                  placeholder="e.g., **********"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="deal_count_ytd" className="text-sm font-medium text-gray-700">
                  Deal Count YTD
                </Label>
                <Input
                  id="deal_count_ytd"
                  type="number"
                  value={company.deal_count_ytd || ''}
                  onChange={handleNumberChange("deal_count_ytd")}
                  placeholder="e.g., 15"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="portfolio_asset_count" className="text-sm font-medium text-gray-700">
                  Portfolio Asset Count
                </Label>
                <Input
                  id="portfolio_asset_count"
                  type="number"
                  value={company.portfolio_asset_count || ''}
                  onChange={handleNumberChange("portfolio_asset_count")}
                  placeholder="e.g., 100"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="dry_powder" className="text-sm font-medium text-gray-700">
                  Dry Powder
                </Label>
                <Input
                  id="dry_powder"
                  type="number"
                  value={company.dry_powder || ''}
                  onChange={handleNumberChange("dry_powder")}
                  placeholder="e.g., *********"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="annual_deployment_target" className="text-sm font-medium text-gray-700">
                  Annual Deployment Target
                </Label>
                <Input
                  id="annual_deployment_target"
                  type="number"
                  value={company.annual_deployment_target || ''}
                  onChange={handleNumberChange("annual_deployment_target")}
                  placeholder="e.g., *********"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Additional Contact & Social Section */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-cyan-100 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Additional Contact & Social</h2>
                <p className="text-sm text-gray-500">Secondary contact information and social media</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="secondary_phone" className="text-sm font-medium text-gray-700">
                  Secondary Phone
                </Label>
                <Input
                  id="secondary_phone"
                  value={company.secondary_phone || ""}
                  onChange={(e) => updatePhoneField('secondary_phone', e.target.value)}
                  placeholder="+****************"
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 ${phoneErrors.secondary_phone ? 'border-red-300' : ''}`}
                />
                {phoneErrors.secondary_phone && (
                  <p className="text-red-600 text-xs">{phoneErrors.secondary_phone}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="secondary_email" className="text-sm font-medium text-gray-700">
                  Secondary Email
                </Label>
                <Input
                  id="secondary_email"
                  type="email"
                  value={company.secondary_email || ""}
                  onChange={(e) => updateField('secondary_email', e.target.value)}
                  placeholder="<EMAIL>"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="instagram" className="text-sm font-medium text-gray-700">
                  Instagram
                </Label>
                <Input
                  id="instagram"
                  value={company.instagram || ""}
                  onChange={(e) => updateField('instagram', e.target.value)}
                  onBlur={() => handleUrlBlur('instagram')}
                  placeholder="https://instagram.com/..."
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 ${urlErrors.instagram ? 'border-red-300' : ''}`}
                />
                {urlErrors.instagram && (
                  <p className="text-red-600 text-xs">{urlErrors.instagram}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="youtube" className="text-sm font-medium text-gray-700">
                  YouTube
                </Label>
                <Input
                  id="youtube"
                  value={company.youtube || ""}
                  onChange={(e) => updateField('youtube', e.target.value)}
                  onBlur={() => handleUrlBlur('youtube')}
                  placeholder="https://youtube.com/..."
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 ${urlErrors.youtube ? 'border-red-300' : ''}`}
                />
                {urlErrors.youtube && (
                  <p className="text-red-600 text-xs">{urlErrors.youtube}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="additional_address" className="text-sm font-medium text-gray-700">
                  Additional Address
                </Label>
                <Input
                  id="additional_address"
                  value={company.additional_address || ""}
                  onChange={(e) => updateField('additional_address', e.target.value)}
                  placeholder="Secondary office address"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="additional_city" className="text-sm font-medium text-gray-700">
                  Additional City
                </Label>
                <Input
                  id="additional_city"
                  value={company.additional_city || ""}
                  onChange={(e) => updateField('additional_city', e.target.value)}
                  placeholder="Secondary office city"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="additional_state" className="text-sm font-medium text-gray-700">
                  Additional State
                </Label>
                <Input
                  id="additional_state"
                  value={company.additional_state || ""}
                  onChange={(e) => updateField('additional_state', e.target.value)}
                  placeholder="Secondary office state"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="additional_zipcode" className="text-sm font-medium text-gray-700">
                  Additional ZIP Code
                </Label>
                <Input
                  id="additional_zipcode"
                  value={company.additional_zipcode || ""}
                  onChange={(e) => updateField('additional_zipcode', e.target.value)}
                  placeholder="Secondary office ZIP"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="additional_country" className="text-sm font-medium text-gray-700">
                  Additional Country
                </Label>
                <Input
                  id="additional_country"
                  value={company.additional_country || ""}
                  onChange={(e) => updateField('additional_country', e.target.value)}
                  placeholder="Secondary office country"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Market Positioning & Strategy Section */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-emerald-100 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Market Positioning & Strategy</h2>
                <p className="text-sm text-gray-500">Strategic positioning and market approach</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="market_cycle_positioning" className="text-sm font-medium text-gray-700">
                  Market Cycle Positioning
                </Label>
                <Input
                  id="market_cycle_positioning"
                  value={company.market_cycle_positioning || ""}
                  onChange={(e) => updateField('market_cycle_positioning', e.target.value)}
                  placeholder="e.g., Growth, Mature, Decline"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="urban_vs_suburban_preference" className="text-sm font-medium text-gray-700">
                  Urban vs Suburban Preference
                </Label>
                <Input
                  id="urban_vs_suburban_preference"
                  value={company.urban_vs_suburban_preference || ""}
                  onChange={(e) => updateField('urban_vs_suburban_preference', e.target.value)}
                  placeholder="e.g., Urban, Suburban, Mixed"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lending_origin" className="text-sm font-medium text-gray-700">
                  Lending Origin
                </Label>
                <Input
                  id="lending_origin"
                  value={company.lending_origin || ""}
                  onChange={(e) => updateField('lending_origin', e.target.value)}
                  placeholder="e.g., Balance Sheet, Securitization"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="portfolio_health" className="text-sm font-medium text-gray-700">
                  Portfolio Health
                </Label>
                <Input
                  id="portfolio_health"
                  value={company.portfolio_health || ""}
                  onChange={(e) => updateField('portfolio_health', e.target.value)}
                  placeholder="e.g., Excellent, Good, Fair"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="annual_loan_volume" className="text-sm font-medium text-gray-700">
                  Annual Loan Volume
                </Label>
                <Input
                  id="annual_loan_volume"
                  type="number"
                  value={company.annual_loan_volume || ''}
                  onChange={handleNumberChange("annual_loan_volume")}
                  placeholder="e.g., **********"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="number_of_offices" className="text-sm font-medium text-gray-700">
                  Number of Offices
                </Label>
                <Input
                  id="number_of_offices"
                  type="number"
                  value={company.number_of_offices || ''}
                  onChange={handleNumberChange("number_of_offices")}
                  placeholder="e.g., 10"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Checkbox fields for boolean values */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="flex items-center space-x-2">
                <input
                  id="sustainability_esg_focus"
                  type="checkbox"
                  checked={company.sustainability_esg_focus || false}
                  onChange={(e) => updateField('sustainability_esg_focus', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <Label htmlFor="sustainability_esg_focus" className="text-sm font-medium text-gray-700">
                  Sustainability/ESG Focus
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  id="technology_proptech_adoption"
                  type="checkbox"
                  checked={company.technology_proptech_adoption || false}
                  onChange={(e) => updateField('technology_proptech_adoption', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <Label htmlFor="technology_proptech_adoption" className="text-sm font-medium text-gray-700">
                  Technology/PropTech Adoption
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  id="adaptive_reuse_experience"
                  type="checkbox"
                  checked={company.adaptive_reuse_experience || false}
                  onChange={(e) => updateField('adaptive_reuse_experience', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <Label htmlFor="adaptive_reuse_experience" className="text-sm font-medium text-gray-700">
                  Adaptive Reuse Experience
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  id="regulatory_zoning_expertise"
                  type="checkbox"
                  checked={company.regulatory_zoning_expertise || false}
                  onChange={(e) => updateField('regulatory_zoning_expertise', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <Label htmlFor="regulatory_zoning_expertise" className="text-sm font-medium text-gray-700">
                  Regulatory/Zoning Expertise
                </Label>
              </div>
            </div>
          </div>

          {/* Metadata Section */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Metadata</h2>
                <p className="text-sm text-gray-500">Data source and confidence information</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="data_source" className="text-sm font-medium text-gray-700">
                  Data Source
                </Label>
                <Input
                  id="data_source"
                  value={company.data_source || ""}
                  onChange={(e) => updateField('data_source', e.target.value)}
                  placeholder="e.g., Manual Entry, API, Web Scraping"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="data_confidence_score" className="text-sm font-medium text-gray-700">
                  Data Confidence Score
                </Label>
                <Input
                  id="data_confidence_score"
                  type="number"
                  step="0.01"
                  min="0"
                  max="1"
                  value={company.data_confidence_score || ''}
                  onChange={handleNumberChange("data_confidence_score")}
                  placeholder="e.g., 0.95"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="last_updated_timestamp" className="text-sm font-medium text-gray-700">
                  Last Updated
                </Label>
                <Input
                  id="last_updated_timestamp"
                  type="datetime-local"
                  value={company.last_updated_timestamp || ""}
                  onChange={(e) => updateField('last_updated_timestamp', e.target.value)}
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Additional Information Section */}
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Additional Information</h2>
                <p className="text-sm text-gray-500">Strategic details and descriptions</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="investment_strategy_mission" className="text-sm font-medium text-gray-700">
                  Investment Strategy Mission
                </Label>
                <Textarea
                  id="investment_strategy_mission"
                  value={company.investment_strategy_mission || ""}
                  onChange={(e) => updateField('investment_strategy_mission', e.target.value)}
                  placeholder="Describe the company's investment strategy and mission..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="products_services_description" className="text-sm font-medium text-gray-700">
                  Products/Services Description
                </Label>
                <Textarea
                  id="products_services_description"
                  value={company.products_services_description || ""}
                  onChange={(e) => updateField('products_services_description', e.target.value)}
                  placeholder="Describe the company's products or services..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="target_customer_profile" className="text-sm font-medium text-gray-700">
                  Target Customer Profile
                </Label>
                <Textarea
                  id="target_customer_profile"
                  value={company.target_customer_profile || ""}
                  onChange={(e) => updateField('target_customer_profile', e.target.value)}
                  placeholder="Describe the target customer profile..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="unique_selling_proposition" className="text-sm font-medium text-gray-700">
                  Unique Selling Proposition
                </Label>
                <Textarea
                  id="unique_selling_proposition"
                  value={company.unique_selling_proposition || ""}
                  onChange={(e) => updateField('unique_selling_proposition', e.target.value)}
                  placeholder="What makes this company unique..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="founder_background" className="text-sm font-medium text-gray-700">
                  Founder Background
                </Label>
                <Textarea
                  id="founder_background"
                  value={company.founder_background || ""}
                  onChange={(e) => updateField('founder_background', e.target.value)}
                  placeholder="Background information about the founders..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="company_history" className="text-sm font-medium text-gray-700">
                  Company History
                </Label>
                <Textarea
                  id="company_history"
                  value={company.company_history || ""}
                  onChange={(e) => updateField('company_history', e.target.value)}
                  placeholder="Historical background and milestones..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="major_competitors" className="text-sm font-medium text-gray-700">
                  Major Competitors
                </Label>
                <ArrayInput
                  value={company.major_competitors || []}
                  onChange={(value) => updateField('major_competitors', value)}
                  placeholder="e.g., Competitor A, Competitor B, Competitor C"
                  label="Major Competitors"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="industry_awards_recognitions" className="text-sm font-medium text-gray-700">
                  Industry Awards & Recognitions
                </Label>
                <ArrayInput
                  value={company.industry_awards_recognitions || []}
                  onChange={(value) => updateField('industry_awards_recognitions', value)}
                  placeholder="e.g., Best Company 2023, Industry Excellence Award"
                  label="Industry Awards & Recognitions"
                />
              </div>


            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
