'use client'

import { CompanyDetail } from '../shared/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Building, 
  Briefcase, 
  Globe, 
  DollarSign, 
  Clock, 
  Target, 
  Users, 
  MapPin, 
  Home, 
  TrendingUp, 
  Percent, 
  Calendar, 
  FileText, 
  CreditCard,
  Phone,
  Mail,
  Linkedin,
  ExternalLink,
  Factory,
  Award,
  PieChart,
  BarChart3,
  HandHeart,
  Settings,
  Crown,
  Building2,
  Landmark,
  Info
} from 'lucide-react'

interface CompanyOverviewProps {
  company: CompanyDetail
}

export default function CompanyOverview({ company }: CompanyOverviewProps) {
  if (!company) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-gray-500">No company data available</div>
      </div>
    )
  }

  const formatCurrency = (value: string | number | null | undefined) => {
    if (!value) return null
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]+/g, '')) : value
    if (isNaN(numValue)) return value
    if (numValue >= 1000) {
      return `$${(numValue / 1000).toFixed(1)}B`
    } else {
      return `$${numValue.toLocaleString()}M`
    }
  }

  const formatNumber = (value: string | number | null | undefined) => {
    if (!value) return null
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]+/g, '')) : value
    if (isNaN(numValue)) return value
    return new Intl.NumberFormat('en-US').format(numValue)
  }

  return (
    <div className="space-y-8">
      {/* SECTION 1: Company Info */}
      <div className="bg-white p-6 rounded-lg border border-gray-100">
        <h2 className="text-lg font-semibold mb-6 flex items-center gap-2">
          <Info className="h-5 w-5 text-blue-600" />
          Company Information
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Company Name */}
          <div className="flex items-start gap-3">
            <div className="bg-blue-50 p-2 rounded-md">
              <Building2 className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500">Company Name</div>
              <div className="font-medium">{company.company_name}</div>
            </div>
          </div>

          {/* Company Type */}
          {company.scraped_data?.companytype && (
            <div className="flex items-start gap-3">
              <div className="bg-purple-50 p-2 rounded-md">
                <Building className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500">Company Type</div>
                <div className="font-medium">{company.scraped_data.companytype}</div>
              </div>
            </div>
          )}

          {/* Industry */}
          {company.industry && (
            <div className="flex items-start gap-3">
              <div className="bg-indigo-50 p-2 rounded-md">
                <Factory className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500">Industry</div>
                <div className="font-medium">{company.industry}</div>
              </div>
            </div>
          )}

          {/* Capital Position */}
          {company.scraped_data?.capitalposition && (
            <div className="flex items-start gap-3">
              <div className="bg-green-50 p-2 rounded-md">
                <Crown className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500">Capital Position</div>
                <div className="font-medium">{company.scraped_data.capitalposition}</div>
              </div>
            </div>
          )}

          {/* Business Model */}
          {company.scraped_data?.businessmodel && (
            <div className="flex items-start gap-3">
              <div className="bg-orange-50 p-2 rounded-md">
                <Briefcase className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500">Business Model</div>
                <div className="font-medium">{company.scraped_data.businessmodel}</div>
              </div>
            </div>
          )}

          {/* Investment Focus */}
          {company.scraped_data?.investmentfocus && company.scraped_data.investmentfocus.length > 0 && (
            <div className="flex items-start gap-3">
              <div className="bg-emerald-50 p-2 rounded-md">
                <Target className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500">Investment Focus</div>
                <div className="flex flex-wrap gap-1 mt-1">
                  {company.scraped_data.investmentfocus.slice(0, 3).map((focus: string, index: number) => (
                    <Badge key={index} className="bg-emerald-50 text-emerald-700 border border-emerald-200 text-xs">
                      {focus}
                    </Badge>
                  ))}
                  {company.scraped_data.investmentfocus.length > 3 && (
                    <Badge className="bg-gray-50 text-gray-700 border border-gray-200 text-xs">
                      +{company.scraped_data.investmentfocus.length - 3} more
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Investment Strategy */}
          {(company.scraped_data?.mission || company.scraped_data?.approach) && (
            <div className="flex items-start gap-3">
              <div className="bg-blue-50 p-2 rounded-md">
                <Target className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500">Investment Strategy</div>
                <div className="text-sm text-gray-600">
                  {company.scraped_data.mission && <div className="mb-1">{company.scraped_data.mission}</div>}
                  {company.scraped_data.approach && <div>{company.scraped_data.approach}</div>}
                </div>
              </div>
            </div>
          )}

          {/* Website */}
          {company.company_website && (
            <div className="flex items-start gap-3">
              <div className="bg-blue-50 p-2 rounded-md">
                <Globe className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500">Website</div>
                <a href={company.company_website.startsWith('http') ? company.company_website : `https://${company.company_website}`} 
                   target="_blank" rel="noopener noreferrer" 
                   className="text-blue-600 hover:text-blue-800 flex items-center gap-1">
                  {company.company_website.replace(/^https?:\/\//, '')}
                  <ExternalLink className="h-3 w-3" />
                </a>
              </div>
            </div>
          )}

          {/* Phone */}
          {company.company_phone && (
            <div className="flex items-start gap-3">
              <div className="bg-green-50 p-2 rounded-md">
                <Phone className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500">Phone</div>
                <a href={`tel:${company.company_phone}`} className="text-green-600 hover:text-green-800">
                  {company.company_phone}
                </a>
              </div>
            </div>
          )}

          {/* Email */}
          {company.scraped_data?.mainemail && (
            <div className="flex items-start gap-3">
              <div className="bg-purple-50 p-2 rounded-md">
                <Mail className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500">Email</div>
                <a href={`mailto:${company.scraped_data.mainemail}`} className="text-purple-600 hover:text-purple-800">
                  {company.scraped_data.mainemail}
                </a>
              </div>
            </div>
          )}

          {/* LinkedIn */}
          {company.company_linkedin && (
            <div className="flex items-start gap-3">
              <div className="bg-blue-50 p-2 rounded-md">
                <Linkedin className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500">LinkedIn</div>
                <a href={company.company_linkedin} target="_blank" rel="noopener noreferrer" 
                   className="text-blue-600 hover:text-blue-800 flex items-center gap-1">
                  LinkedIn Profile
                  <ExternalLink className="h-3 w-3" />
                </a>
              </div>
            </div>
          )}

          {/* Founded Year */}
          {company.founded_year && (
            <div className="flex items-start gap-3">
              <div className="bg-indigo-50 p-2 rounded-md">
                <Calendar className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500">Founded Year</div>
                <div className="font-medium">{company.founded_year}</div>
              </div>
            </div>
          )}
        </div>

        {/* Company Summary */}
        {company.summary && (
          <div className="mt-6 pt-6 border-t border-gray-100">
            <div className="text-sm font-medium text-gray-500 mb-2">Company Summary</div>
            <p className="text-gray-700">{company.summary}</p>
          </div>
        )}

        {/* Address Information */}
        {(company.company_address || company.company_city || company.company_state || company.company_zip || company.company_country) && (
          <div className="mt-6 pt-6 border-t border-gray-100">
            <div className="text-sm font-medium text-gray-500 mb-3">Address</div>
            <div className="flex items-start gap-3">
              <div className="bg-gray-50 p-2 rounded-md">
                <MapPin className="h-5 w-5 text-gray-600" />
              </div>
              <div className="text-gray-700">
                {company.company_address && <div>{company.company_address}</div>}
                {(company.company_city || company.company_state || company.company_zip) && (
                  <div>
                    {[company.company_city, company.company_state, company.company_zip].filter(Boolean).join(', ')}
                  </div>
                )}
                {company.company_country && <div>{company.company_country}</div>}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* SECTION 2: Company Profile */}
      {(company.scraped_data?.fundsize || company.scraped_data?.aum || company.scraped_data?.headquarters || 
        company.scraped_data?.numberofoffices || company.scraped_data?.numberofproperties || 
        company.scraped_data?.foundedyear || company.scraped_data?.numberofemployees ||
        company.scraped_data?.officelocations || company.scraped_data?.partnerships) && (
        <div className="bg-white p-6 rounded-lg border border-gray-100">
          <h2 className="text-lg font-semibold mb-6 flex items-center gap-2">
            <Building className="h-5 w-5 text-green-600" />
            Company Profile
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Fund Size */}
            {company.scraped_data?.fundsize && (
              <div className="flex items-start gap-3">
                <div className="bg-green-50 p-2 rounded-md">
                  <DollarSign className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500">Fund Size</div>
                  <div className="font-medium">{company.scraped_data.fundsize}</div>
                </div>
              </div>
            )}

            {/* AUM */}
            {company.scraped_data?.aum && (
              <div className="flex items-start gap-3">
                <div className="bg-emerald-50 p-2 rounded-md">
                  <Landmark className="h-5 w-5 text-emerald-600" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500">Assets Under Management</div>
                  <div className="font-medium">{company.scraped_data.aum}</div>
                </div>
              </div>
            )}

            {/* Headquarters */}
            {company.scraped_data?.headquarters && (
              <div className="flex items-start gap-3">
                <div className="bg-blue-50 p-2 rounded-md">
                  <Home className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500">Headquarters</div>
                  <div className="font-medium">{company.scraped_data.headquarters}</div>
                </div>
              </div>
            )}

            {/* Number of Offices */}
            {company.scraped_data?.numberofoffices && (
              <div className="flex items-start gap-3">
                <div className="bg-cyan-50 p-2 rounded-md">
                  <Building className="h-5 w-5 text-cyan-600" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500">Number of Offices</div>
                  <div className="font-medium">{formatNumber(company.scraped_data.numberofoffices)}</div>
                </div>
              </div>
            )}

            {/* Number of Properties */}
            {company.scraped_data?.numberofproperties && (
              <div className="flex items-start gap-3">
                <div className="bg-orange-50 p-2 rounded-md">
                  <Building className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500">Number of Properties</div>
                  <div className="font-medium">{formatNumber(company.scraped_data.numberofproperties)}</div>
                </div>
              </div>
            )}

            {/* Number of Employees */}
            {company.scraped_data?.numberofemployees && (
              <div className="flex items-start gap-3">
                <div className="bg-pink-50 p-2 rounded-md">
                  <Users className="h-5 w-5 text-pink-600" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500">Number of Employees</div>
                  <div className="font-medium">{company.scraped_data.numberofemployees}</div>
                </div>
              </div>
            )}
          </div>

          {/* Office Locations */}
          {company.scraped_data?.officelocations && company.scraped_data.officelocations.length > 0 && (
            <div className="mt-6 pt-6 border-t border-gray-100">
              <div className="text-sm font-medium text-gray-500 mb-3">Office Locations</div>
              <div className="flex flex-wrap gap-2">
                {company.scraped_data.officelocations.map((location: string, index: number) => (
                  <Badge key={index} className="bg-blue-50 text-blue-700 border border-blue-200">
                    <MapPin className="h-3 w-3 mr-1" />
                    {location}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Partnerships */}
          {company.scraped_data?.partnerships && company.scraped_data.partnerships.length > 0 && (
            <div className="mt-6 pt-6 border-t border-gray-100">
              <div className="text-sm font-medium text-gray-500 mb-3">Partnerships</div>
              <div className="flex flex-wrap gap-2">
                {company.scraped_data.partnerships.map((partnership: any, index: number) => {
                  // Handle both string and object formats
                  const partnershipText = typeof partnership === 'string' 
                    ? partnership 
                    : partnership.partnerName || partnership.description || JSON.stringify(partnership);
                  
                  return (
                    <Badge key={index} className="bg-pink-50 text-pink-700 border border-pink-200">
                      {partnershipText}
                    </Badge>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Recent Deals */}
      {company.scraped_data?.recentdeals && company.scraped_data.recentdeals.length > 0 && (
        <div className="bg-white p-6 rounded-lg border border-gray-100">
          <h2 className="text-lg font-semibold mb-6 flex items-center gap-2">
            <Briefcase className="h-5 w-5 text-indigo-600" />
            Recent Deals
          </h2>
          
          <div className="space-y-4">
            {company.scraped_data.recentdeals.map((deal: any, index: number) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-medium text-gray-900">{deal.property}</h3>
                    <div className="text-sm text-gray-600 flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {deal.location}
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge className="bg-indigo-50 text-indigo-700 border border-indigo-200">
                      {deal.dealType}
                    </Badge>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  {deal.strategies && (
                    <div>
                      <div className="text-gray-500">Strategy</div>
                      <div className="font-medium">{deal.strategies}</div>
                    </div>
                  )}
                  {deal.units && (
                    <div>
                      <div className="text-gray-500">Units</div>
                      <div className="font-medium">{formatNumber(deal.units)}</div>
                    </div>
                  )}
                  {deal.squareFeet && (
                    <div>
                      <div className="text-gray-500">Square Feet</div>
                      <div className="font-medium">{formatNumber(deal.squareFeet)}</div>
                    </div>
                  )}
                  {deal.amount && (
                    <div>
                      <div className="text-gray-500">Amount</div>
                      <div className="font-medium">{formatCurrency(deal.amount)}</div>
                    </div>
                  )}
                </div>
                
                {deal.propertyTypes && deal.propertyTypes.length > 0 && (
                  <div className="mt-3 flex flex-wrap gap-1">
                    {deal.propertyTypes.map((type: string, typeIndex: number) => (
                      <Badge key={typeIndex} className="bg-gray-50 text-gray-700 border border-gray-200 text-xs">
                        {type}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Track Record */}
      {(company.scraped_data?.totaltransactions || company.scraped_data?.totalsquarefeet || 
        company.scraped_data?.totalunits || company.scraped_data?.historicalreturns || 
        company.scraped_data?.portfoliovalue) && (
        <div className="bg-white p-6 rounded-lg border border-gray-100">
          <h2 className="text-lg font-semibold mb-6 flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-blue-600" />
            Track Record
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {company.scraped_data.totaltransactions && (
              <div className="flex items-start gap-3">
                <div className="bg-green-50 p-2 rounded-md">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500">Total Transactions</div>
                  <div className="font-medium">{company.scraped_data.totaltransactions}</div>
                </div>
              </div>
            )}

            {company.scraped_data.totalsquarefeet && (
              <div className="flex items-start gap-3">
                <div className="bg-blue-50 p-2 rounded-md">
                  <Building className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500">Total Square Feet</div>
                  <div className="font-medium">{company.scraped_data.totalsquarefeet}</div>
                </div>
              </div>
            )}

            {company.scraped_data.totalunits && (
              <div className="flex items-start gap-3">
                <div className="bg-purple-50 p-2 rounded-md">
                  <Home className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500">Total Units</div>
                  <div className="font-medium">{company.scraped_data.totalunits}</div>
                </div>
              </div>
            )}

            {company.scraped_data.historicalreturns && (
              <div className="flex items-start gap-3">
                <div className="bg-emerald-50 p-2 rounded-md">
                  <Percent className="h-5 w-5 text-emerald-600" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500">Historical Returns</div>
                  <div className="font-medium">{company.scraped_data.historicalreturns}</div>
                </div>
              </div>
            )}

            {company.scraped_data.portfoliovalue && (
              <div className="flex items-start gap-3">
                <div className="bg-yellow-50 p-2 rounded-md">
                  <PieChart className="h-5 w-5 text-yellow-600" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500">Portfolio Value</div>
                  <div className="font-medium">{company.scraped_data.portfoliovalue}</div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
} 