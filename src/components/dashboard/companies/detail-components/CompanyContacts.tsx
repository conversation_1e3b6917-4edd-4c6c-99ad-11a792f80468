'use client'

import { useState, useMemo } from 'react'
import { CompanyDetail, CompanyContact, ScrapedContact } from '../shared/types'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Input } from '@/components/ui/input'
import { Mail, MapPin, Phone, LinkedinIcon, ExternalLink, Plus, UserPlus, Search, X } from 'lucide-react'
import { useRouter } from 'next/navigation'
import AddContact from '@/components/dashboard/people/AddContact'

interface CompanyContactsProps {
  company: CompanyDetail
}

export default function CompanyContacts({ company }: CompanyContactsProps) {
  const router = useRouter()
  const [showAddContact, setShowAddContact] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  
  // Add the handleOpenContact function
  const handleOpenContact = (contactId: number) => {
    // Navigate to contact detail using Next.js router
    router.push(`/dashboard/people/${contactId}`);
  };

  // Handle adding new contact
  const handleAddContact = () => {
    setShowAddContact(true);
  };

  const handleBackFromAddContact = () => {
    setShowAddContact(false);
    // Optionally refresh the company data to show new contact
    window.location.reload();
  };

  // Filter contacts based on search term
  const filteredContacts = useMemo(() => {
    if (!searchTerm.trim()) {
      return {
        contacts: company?.contacts || [],
        scrapedContacts: company?.scraped_contacts || []
      };
    }

    const searchLower = searchTerm.toLowerCase();
    
    const filteredRegularContacts = (company?.contacts || []).filter(contact => 
      contact.first_name?.toLowerCase().includes(searchLower) ||
      contact.last_name?.toLowerCase().includes(searchLower) ||
      contact.full_name?.toLowerCase().includes(searchLower) ||
      contact.title?.toLowerCase().includes(searchLower) ||
      contact.email?.toLowerCase().includes(searchLower) ||
      contact.personal_email?.toLowerCase().includes(searchLower) ||
      contact.headline?.toLowerCase().includes(searchLower)
    );

    const filteredScrapedContacts = (company?.scraped_contacts || []).filter(contact => 
      contact.first_name?.toLowerCase().includes(searchLower) ||
      contact.last_name?.toLowerCase().includes(searchLower) ||
      contact.full_name?.toLowerCase().includes(searchLower) ||
      contact.title?.toLowerCase().includes(searchLower) ||
      contact.email?.toLowerCase().includes(searchLower) ||
      contact.personal_email?.toLowerCase().includes(searchLower) ||
      contact.headline?.toLowerCase().includes(searchLower) ||
      (contact as any).category?.toLowerCase().includes(searchLower)
    );

    return {
      contacts: filteredRegularContacts,
      scrapedContacts: filteredScrapedContacts
    };
  }, [company?.contacts, company?.scraped_contacts, searchTerm]);

  const totalFilteredContacts = filteredContacts.contacts.length + filteredContacts.scrapedContacts.length;
  const totalContacts = (company?.contacts?.length || 0) + (company?.scraped_contacts?.length || 0);

  // Prepare pre-selected company data for the AddContact component with extracted data
  const preSelectedCompany = {
    company_id: company.company_id,
    company_name: company.company_name || '',
    company_website: company.company_website || '',
    industry: company.industry || '',
    company_address: company.company_address || '',
    company_city: company.company_city || '',
    company_state: company.company_state || '',
    company_country: company.company_country || '',
    // Map scraped_data to extracted_data format expected by AddContact
    extracted_data: company.scraped_data ? {
      companytype: company.scraped_data.companytype || company.companyProfile?.companyType || undefined,
      businessmodel: company.scraped_data.businessmodel || company.companyProfile?.businessModel || undefined,
      fundsize: company.scraped_data.fundsize || company.companyProfile?.fundSize || undefined,
      aum: company.scraped_data.aum || company.companyProfile?.aum || undefined,
      headquarters: company.scraped_data.headquarters || company.companyProfile?.headquarters || company.headquarters || undefined,
      foundedyear: company.scraped_data.foundedyear || company.founded_year || company.foundedyear || undefined,
      numberofemployees: company.scraped_data.numberofemployees || company.companyProfile?.numberOfEmployees || undefined,
      investmentfocus: company.scraped_data.investmentfocus || company.companyProfile?.investmentFocus || undefined,
      geographicfocus: company.scraped_data.geographicfocus || company.companyProfile?.geographicFocus || undefined,
      dealsize: company.scraped_data.dealsize || company.investmentCriteria?.dealSize || undefined,
      minimumdealsize: company.scraped_data.minimumdealsize || company.investmentCriteria?.minimumDealSize || undefined,
      maximumdealsize: company.scraped_data.maximumdealsize || company.investmentCriteria?.maximumDealSize || undefined,
      investment_criteria_property_types: company.scraped_data.investment_criteria_property_types || company.investmentCriteria?.propertyTypes || company.investmentStrategy?.propertyTypes || undefined,
      investment_criteria_asset_types: company.scraped_data.investment_criteria_asset_types || company.investmentCriteria?.assetTypes || company.investmentStrategy?.assetClasses || undefined,
      investment_criteria_loan_types: company.scraped_data.investment_criteria_loan_types || company.investmentCriteria?.loanTypes || undefined,
      investment_criteria_property_subcategories: company.scraped_data.investment_criteria_property_subcategories || company.investmentCriteria?.propertySubcategories || undefined,
      riskprofile: company.scraped_data.riskprofile || undefined,
      targetmarkets: company.scraped_data.targetmarkets || company.investmentCriteria?.targetMarkets || undefined,
      strategies: company.scraped_data.strategies || company.investmentStrategy?.strategies || undefined,
      propertytypes: company.scraped_data.propertytypes || company.investmentStrategy?.propertyTypes || undefined,
      assetclasses: company.scraped_data.assetclasses || company.investmentStrategy?.assetClasses || undefined,
      valuecreation: company.scraped_data.valuecreation || company.investmentStrategy?.valueCreation || undefined,
      holdperiod: company.scraped_data.holdperiod || company.investmentCriteria?.holdPeriod || company.overview?.investment_program?.hold_period || undefined,
      targetreturn: company.scraped_data.targetreturn || company.investmentStrategy?.targetReturn || undefined,
      approach: company.scraped_data.approach || company.investmentStrategy?.approach || undefined,
    } : undefined
  };

  // If showing AddContact, render it instead
  if (showAddContact) {
    return (
      <AddContact
        onBack={handleBackFromAddContact}
        companyId={company.company_id?.toString()}
        preSelectedCompany={preSelectedCompany}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with action buttons */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">
          Contacts ({totalFilteredContacts}{searchTerm && totalFilteredContacts !== totalContacts ? ` of ${totalContacts}` : ''})
        </h2>
        <Button className="flex items-center gap-2" onClick={handleAddContact}>
          <UserPlus className="h-4 w-4" />
          Add Contact
        </Button>
      </div>

      {/* Search Bar */}
      <div className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Search contacts by name, title, email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-10"
          />
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
      
      {(company?.contacts && company.contacts.length > 0) || (company?.scraped_contacts && company.scraped_contacts.length > 0) ? (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
          {/* Regular Contacts */}
          {filteredContacts.contacts.map((contact) => (
            <ContactCard 
              key={contact.contact_id}
              contact={contact}
              onOpenContact={handleOpenContact}
              type="regular"
            />
          ))}

          {/* Scraped Contacts */}
          {filteredContacts.scrapedContacts.map((contact, idx) => (
            <ContactCard 
              key={`scraped-${idx}`}
              contact={contact}
              onOpenContact={handleOpenContact}
              type="scraped"
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-16 bg-white rounded-lg border border-gray-100">
          <div className="h-16 w-16 mx-auto text-gray-300 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchTerm ? 'No contacts found' : 'No contacts found'}
          </h3>
          <p className="text-gray-500 mb-6">
            {searchTerm 
              ? `No contacts match "${searchTerm}". Try a different search term.`
              : "This company doesn't have any contacts yet."
            }
          </p>
          <Button className="flex items-center gap-2" onClick={handleAddContact}>
            <Plus className="h-4 w-4" />
            Add Contact
          </Button>
        </div>
      )}
    </div>
  )
}

interface ContactCardProps {
  contact: CompanyContact | ScrapedContact
  onOpenContact: (contactId: number) => void
  type: 'regular' | 'scraped'
}

function ContactCard({ contact, onOpenContact, type }: ContactCardProps) {
  const isScraped = type === 'scraped'
  const borderColor = isScraped ? 'border-purple-200' : 'border-blue-100'
  const avatarBgColor = isScraped ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'
  const iconColor = isScraped ? 'text-purple-600' : 'text-blue-600'

  return (
    <div 
      className={`bg-white rounded-lg border ${borderColor} overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer`}
      onClick={() => contact.contact_id ? onOpenContact(contact.contact_id) : null}
    >
      <div className="p-4">
        <div className="flex items-center gap-3 mb-4">
          <Avatar>
            <AvatarFallback className={avatarBgColor}>
              {contact.first_name?.[0]}{contact.last_name?.[0]}
            </AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-semibold text-gray-900">
              {contact.full_name || `${contact.first_name} ${contact.last_name}`}
            </h3>
            {contact.title && (
              <p className="text-sm text-gray-600">{contact.title}</p>
            )}
            {isScraped && 'category' in contact && contact.category && (
              <Badge className="mt-1 bg-purple-100 text-purple-800 border-purple-200">
                {contact.category || 'Scraped Contact'}
              </Badge>
            )}
          </div>
        </div>

        <div className="space-y-2 text-sm">
          {contact.email && (
            <div className="flex items-start">
              <Mail className="h-4 w-4 mr-2 mt-0.5 text-gray-400" />
              <span className="text-gray-700 break-all">{contact.email}</span>
            </div>
          )}

          {contact.personal_email && (
            <div className="flex items-start">
              <Mail className="h-4 w-4 mr-2 mt-0.5 text-gray-400" />
              <span className="text-gray-700 break-all">{contact.personal_email}</span>
            </div>
          )}

          {contact.linkedin_url && (
            <div className="flex items-start">
              <LinkedinIcon className="h-4 w-4 mr-2 mt-0.5 text-gray-400" />
              <a
                href={contact.linkedin_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline break-all"
                onClick={(e) => e.stopPropagation()}
              >
                LinkedIn Profile
              </a>
            </div>
          )}

          {'contact_city' in contact && (contact.contact_city || contact.contact_state || contact.contact_country) && (
            <div className="flex items-start">
              <MapPin className="h-4 w-4 mr-2 mt-0.5 text-gray-400" />
              <span className="text-gray-700">
                {contact.contact_city}
                {contact.contact_state && `, ${contact.contact_state}`}
                {contact.contact_country && `, ${contact.contact_country}`}
              </span>
            </div>
          )}

          {'phone' in contact && contact.phone && (
            <div className="flex items-start">
              <Phone className="h-4 w-4 mr-2 mt-0.5 text-gray-400" />
              <span className="text-gray-700">{contact.phone}</span>
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-end items-center gap-2 p-3 bg-gray-50 border-t border-gray-100">
        {contact.contact_id && (
          <Button variant="outline" size="sm" className="h-8 px-3" onClick={(e: React.MouseEvent) => {
            e.stopPropagation();
            onOpenContact(contact.contact_id);
          }}>
            <ExternalLink className={`h-3.5 w-3.5 mr-1.5 ${iconColor}`} />
            View Profile
          </Button>
        )}
        {contact.email && (
          <Button variant="ghost" size="sm" className="h-8 px-3" onClick={(e: React.MouseEvent) => e.stopPropagation()}>
            <Mail className={`h-3.5 w-3.5 mr-1.5 ${iconColor}`} />
            Email
          </Button>
        )}
      </div>
    </div>
  )
} 