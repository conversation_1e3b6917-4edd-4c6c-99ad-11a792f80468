import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink, Building2, DollarSign, MapPin, Calendar, User } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

interface CompanyLinkedDealsTabProps {
  companyId: string;
}

interface Contact {
  contact_id: number;
  first_name: string;
  last_name: string;
  email: string;
  title: string;
  full_name: string;
}

interface Deal {
  deal_id: string;
  deal_name: string;
  deal_type: string;
  deal_size: number;
  deal_stage: string;
  created_at: string;
  updated_at: string;
  deal_description?: string;
  deal_location?: string;
}

interface DealsByContact {
  contact: Contact;
  deals: Deal[];
}

const CompanyLinkedDealsTab: React.FC<CompanyLinkedDealsTabProps> = ({ companyId }) => {
  const router = useRouter();
  const [dealsByContact, setDealsByContact] = useState<DealsByContact[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchLinkedDeals = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/companies/${companyId}/deals`);
        if (!response.ok) {
          throw new Error('Failed to fetch linked deals');
        }
        const data = await response.json();
        setDealsByContact(data.dealsByContact || []);
      } catch (err) {
        console.error('Error fetching linked deals:', err);
        setError('Failed to load linked deals');
      } finally {
        setLoading(false);
      }
    };

    if (companyId) {
      fetchLinkedDeals();
    }
  }, [companyId]);

  const formatCurrency = (amount: number | string | null | undefined): string => {
    if (amount === null || amount === undefined) return "N/A";
    const num = typeof amount === "string" ? parseFloat(amount) : amount;
    if (isNaN(num)) return "N/A";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(num * 1000000); // Convert millions to actual amount
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getInitials = (firstName: string, lastName: string): string => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Linked Deals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="text-gray-500 mt-2">Loading linked deals...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Linked Deals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-500">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalDeals = dealsByContact.reduce((sum, contactGroup) => sum + contactGroup.deals.length, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Linked Deals
          {totalDeals > 0 && (
            <Badge variant="secondary" className="ml-2">
              {totalDeals} deal{totalDeals !== 1 ? 's' : ''} across {dealsByContact.length} contact{dealsByContact.length !== 1 ? 's' : ''}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {dealsByContact.length === 0 ? (
          <div className="text-center py-8">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No linked deals found</p>
            <p className="text-sm text-gray-400 mt-2">
              This company's contacts are not associated with any deals yet.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {dealsByContact.map((contactGroup) => (
              <div key={contactGroup.contact.contact_id} className="border rounded-lg p-4">
                {/* Contact Header */}
                <div className="flex items-center gap-3 mb-4 pb-3 border-b">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback className="bg-blue-100 text-blue-600">
                      {getInitials(contactGroup.contact.first_name, contactGroup.contact.last_name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold text-gray-900">
                        {contactGroup.contact.full_name}
                      </h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/dashboard/people/${contactGroup.contact.contact_id}`)}
                        className="h-6 px-2"
                      >
                        <User className="h-3 w-3" />
                      </Button>
                    </div>
                    {contactGroup.contact.title && (
                      <p className="text-sm text-gray-600">{contactGroup.contact.title}</p>
                    )}
                    {contactGroup.contact.email && (
                      <p className="text-sm text-gray-500">{contactGroup.contact.email}</p>
                    )}
                  </div>
                  <Badge variant="outline" className="ml-2">
                    {contactGroup.deals.length} deal{contactGroup.deals.length !== 1 ? 's' : ''}
                  </Badge>
                </div>

                {/* Deals for this contact */}
                <div className="space-y-3">
                  {contactGroup.deals.map((deal) => (
                    <div
                      key={deal.deal_id}
                      className="border rounded-lg p-3 hover:bg-gray-50 transition-colors cursor-pointer"
                      onClick={() => router.push(`/dashboard/deals/${deal.deal_id}`)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-medium text-gray-900">
                              {deal.deal_name}
                            </h4>
                            <Badge variant="outline" className="text-xs">
                              {deal.deal_type}
                            </Badge>
                            {deal.deal_stage && (
                              <Badge variant="secondary" className="text-xs">
                                {deal.deal_stage}
                              </Badge>
                            )}
                          </div>
                          
                          {deal.deal_description && (
                            <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                              {deal.deal_description}
                            </p>
                          )}
                          
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            {deal.deal_size && (
                              <div className="flex items-center gap-1">
                                <DollarSign className="h-3 w-3" />
                                <span>{formatCurrency(deal.deal_size)}</span>
                              </div>
                            )}
                            
                            {deal.deal_location && (
                              <div className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                <span>{deal.deal_location}</span>
                              </div>
                            )}
                            
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              <span>Created {formatDate(deal.created_at)}</span>
                            </div>
                          </div>
                        </div>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          className="ml-2"
                          onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                            e.stopPropagation();
                            router.push(`/dashboard/deals/${deal.deal_id}`);
                          }}
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CompanyLinkedDealsTab; 