'use client'

import { CompanyDetail } from '../shared/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Building, 
  Briefcase, 
  Globe, 
  DollarSign, 
  Target, 
  Users, 
  MapPin, 
  Home, 
  TrendingUp, 
  Percent, 
  Calendar, 
  FileText, 
  CreditCard,
  Phone,
  Mail,
  Linkedin,
  ExternalLink,
  Factory,
  Award,
  PieChart,
  BarChart3,
  HandHeart,
  Crown,
  Building2,
  Landmark,
  Info,
  Star,
  Shield,
  Activity,
  Network,
  TrendingDown,
  Eye,
  CheckCircle,
  AlertCircle,
  XCircle,
  Layers,
  BookOpen,
  Banknote,
  Calculator,
  Scale,
  Compass,
  TreePine,
  Cpu,
  Recycle,
  Gavel,
  ChevronDown,
  ChevronUp,
  Twitter,
  Facebook,
  Instagram,
  Youtube,
  Sparkles,
  BarChart,
  Database,
  Target as TargetIcon,
  Building as BuildingIcon,
  DollarSign as DollarSignIcon,
  Users as UsersIcon,
  MapPin as MapPinIcon,
  Briefcase as BriefcaseIcon,
  Globe as GlobeIcon,
  Phone as PhoneIcon,
  Mail as MailIcon,
  Linkedin as LinkedinIcon,
  Twitter as TwitterIcon,
  Facebook as FacebookIcon,
  Instagram as InstagramIcon,
  Youtube as YoutubeIcon,
  Award as AwardIcon,
  Star as StarIcon,
  Activity as ActivityIcon,
  Network as NetworkIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Eye as EyeIcon,
  CheckCircle as CheckCircleIcon,
  AlertCircle as AlertCircleIcon,
  XCircle as XCircleIcon,
  Layers as LayersIcon,
  BookOpen as BookOpenIcon,
  Banknote as BanknoteIcon,
  Calculator as CalculatorIcon,
  Scale as ScaleIcon,
  Compass as CompassIcon,
  TreePine as TreePineIcon,
  Cpu as CpuIcon,
  Recycle as RecycleIcon,
  Gavel as GavelIcon,
  ChevronDown as ChevronDownIcon,
  ChevronUp as ChevronUpIcon,
  Sparkles as SparklesIcon,
  BarChart as BarChartIcon
} from 'lucide-react'
import { useState } from 'react'

interface CompanyOverviewV2Props {
  company: CompanyDetail
}

export default function CompanyOverviewV2({ company }: CompanyOverviewV2Props) {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    'company-profile': true, // Company Profile always expanded
    'financial-health': true, // Financial Health & AUM always expanded
  })

  if (!company) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-gray-500">No company data available</div>
      </div>
    )
  }

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }))
  }

  // Helper function to check if a value has meaningful data
  const hasData = (value: any): boolean => {
    if (!value) return false
    if (Array.isArray(value)) return value.length > 0
    if (typeof value === 'string') return value.trim() !== '' && value.trim() !== 'Not specified'
    if (typeof value === 'number') return value > 0
    if (typeof value === 'boolean') return true
    return true
  }

  // Helper function to get available fields in a section
  const getAvailableFields = (fields: Record<string, any>): string[] => {
    return Object.entries(fields)
      .filter(([_, value]) => hasData(value))
      .map(([key, _]) => key)
  }

  // Enhanced currency formatting for large numbers
  const formatCurrency = (value: string | number | null | undefined) => {
    if (!value) return null
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]+/g, '')) : value
    if (isNaN(numValue)) return value
    
    if (numValue >= 1000000000) {
      return `$${(numValue / 1000000000).toFixed(1)}B`
    } else if (numValue >= 1000000) {
      return `$${(numValue / 1000000).toFixed(1)}M`
    } else if (numValue >= 1000) {
      return `$${(numValue / 1000).toFixed(1)}K`
    } else {
      return `$${numValue.toLocaleString()}`
    }
  }

  const formatNumber = (value: string | number | null | undefined) => {
    if (!value) return null
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]+/g, '')) : value
    if (isNaN(numValue)) return value
    return new Intl.NumberFormat('en-US').format(numValue)
  }

  const formatDecimal = (value: string | number | null | undefined) => {
    if (!value) return null
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]+/g, '')) : value
    if (isNaN(numValue)) return value
    return `${(numValue * 100).toFixed(1)}%`
  }

  const renderArrayField = (values: string[] | null | undefined, colorClass: string = 'blue', maxDisplay: number = 5) => {
    if (!values || values.length === 0) return null
    
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-700 border-blue-200',
      green: 'bg-green-50 text-green-700 border-green-200',
      purple: 'bg-purple-50 text-purple-700 border-purple-200',
      orange: 'bg-orange-50 text-orange-700 border-orange-200',
      red: 'bg-red-50 text-red-700 border-red-200',
      yellow: 'bg-yellow-50 text-yellow-700 border-yellow-200',
      gray: 'bg-gray-50 text-gray-700 border-gray-200',
      indigo: 'bg-indigo-50 text-indigo-700 border-indigo-200',
      emerald: 'bg-emerald-50 text-emerald-700 border-emerald-200',
      pink: 'bg-pink-50 text-pink-700 border-pink-200',
      cyan: 'bg-cyan-50 text-cyan-700 border-cyan-200'
    }

    return (
      <div className="flex flex-wrap gap-1 mt-1">
        {values.map((value: string, index: number) => (
          <Badge key={index} className={`${colorClasses[colorClass as keyof typeof colorClasses]} text-xs border`}>
            {value}
          </Badge>
        ))}
      </div>
    )
  }

  const renderInfoField = (label: string, value: any, icon: React.ReactNode, colorClass: string = 'blue', isLink: boolean = false) => {
    if (!value) return null

    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600',
      green: 'bg-green-50 text-green-600',
      purple: 'bg-purple-50 text-purple-600',
      orange: 'bg-orange-50 text-orange-600',
      red: 'bg-red-50 text-red-600',
      yellow: 'bg-yellow-50 text-yellow-600',
      gray: 'bg-gray-50 text-gray-600',
      indigo: 'bg-indigo-50 text-indigo-600',
      emerald: 'bg-emerald-50 text-emerald-600',
      pink: 'bg-pink-50 text-pink-600',
      cyan: 'bg-cyan-50 text-cyan-600'
    }

    return (
      <div className="flex items-start gap-3 p-3 rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
        <div className={`p-2 rounded-md ${colorClasses[colorClass as keyof typeof colorClasses]}`}>
          {icon}
        </div>
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-gray-500 mb-1">{label}</div>
          <div className="font-medium text-gray-900">
            {Array.isArray(value) ? renderArrayField(value, colorClass) : (
              isLink ? (
                <a href={value} target="_blank" rel="noopener noreferrer" 
                   className="text-blue-600 hover:text-blue-800 flex items-center gap-1">
                  {value}
                  <ExternalLink className="h-3 w-3" />
                </a>
              ) : value
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderExpandableSection = (
    title: string, 
    icon: React.ReactNode, 
    children: React.ReactNode, 
    sectionId: string, 
    defaultExpanded: boolean = false,
    fields: Record<string, any> = {},
    colorClass: string = 'blue'
  ) => {
    const isExpanded = expandedSections[sectionId] ?? defaultExpanded
    const availableFields = getAvailableFields(fields)
    
    return (
      <Card className="border border-gray-200 shadow-sm">
        <CardHeader 
          className="cursor-pointer hover:bg-gray-50 transition-colors"
          onClick={() => toggleSection(sectionId)}
        >
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {icon}
              {title}
              {/* Show available fields chips when collapsed */}
              {!isExpanded && availableFields.length > 0 && (
                <div className="flex items-center gap-1 ml-3">
                  <Badge className="bg-gray-100 text-gray-700 border border-gray-300 font-bold text-xs px-2 py-0.5">
                    {availableFields.length}
                  </Badge>
                  <span className="text-xs text-gray-500">•</span>
                  <div className="flex flex-wrap gap-1">
                    {availableFields.map((field, index) => (
                      <Badge 
                        key={index} 
                        className="bg-gray-50 text-gray-600 border border-gray-200 text-xs px-2 py-0.5"
                      >
                        {field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
            {isExpanded ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </CardTitle>
        </CardHeader>
        {isExpanded && (
          <CardContent>
            {children}
          </CardContent>
        )}
      </Card>
    )
  }

  // Helper function to check if a section has any meaningful data
  const sectionHasData = (fields: Record<string, any>): boolean => {
    return Object.values(fields).some(value => hasData(value))
  }

  return (
    <div className="space-y-6">
      {/* Company Profile - Always Visible */}
      <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-white">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-900">
            <Building2 className="h-5 w-5" />
            Company Profile
          </CardTitle>
        </CardHeader>
        <CardContent>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {renderInfoField("Company Name", company.company_name, <Building2 className="h-5 w-5" />, 'blue')}
            {renderInfoField("Company Type", company.company_type, <Building className="h-5 w-5" />, 'purple')}
            {renderInfoField("Industry", company.industry, <Factory className="h-5 w-5" />, 'indigo')}
            {renderInfoField("Business Model", company.business_model, <Briefcase className="h-5 w-5" />, 'orange')}
            {renderInfoField("Founded Year", company.founded_year, <Calendar className="h-5 w-5" />, 'indigo')}
            {renderInfoField("Number of Offices", company.number_of_offices, <Building className="h-5 w-5" />, 'cyan')}
            {renderInfoField("Number of Employees", company.number_of_employees, <Users className="h-5 w-5" />, 'pink')}
            {renderInfoField("Corporate Structure", company.corporate_structure, <Building2 className="h-5 w-5" />, 'gray')}
            {renderInfoField("Parent Company", company.parent_company, <Building className="h-5 w-5" />, 'blue')}
          </div>

          {company.subsidiaries && company.subsidiaries.length > 0 && (
            <div className="mt-6 pt-6 border-t border-blue-100">
              <div className="text-sm font-medium text-gray-500 mb-2">Subsidiaries</div>
              {renderArrayField(company.subsidiaries, 'blue')}
            </div>
          )}

          {company.summary && (
            <div className="mt-4">
              <div className="text-sm font-medium text-gray-500 mb-2">Company Summary</div>
              <p className="text-gray-700 bg-white p-4 rounded-lg border border-gray-200">{company.summary}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Investment Strategy */}
      {(() => {
        const strategyFields = {
          investment_focus: company.investment_focus,
          investment_strategy_mission: company.investment_strategy_mission,
          investment_strategy_approach: company.investment_strategy_approach,
          market_cycle_positioning: company.market_cycle_positioning,
          urban_vs_suburban_preference: company.urban_vs_suburban_preference,
          sustainability_esg_focus: company.sustainability_esg_focus
        }
        
        return sectionHasData(strategyFields) && 
          renderExpandableSection(
            "Investment Strategy", 
            <Target className="h-5 w-5 text-green-600" />,
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInfoField("Investment Focus", company.investment_focus, <Target className="h-5 w-5" />, 'emerald')}
                {renderInfoField("Market Cycle Positioning", company.market_cycle_positioning, <Compass className="h-5 w-5" />, 'indigo')}
                {renderInfoField("Urban vs Suburban Preference", company.urban_vs_suburban_preference, <MapPin className="h-5 w-5" />, 'gray')}
                {renderInfoField("ESG Focus", company.sustainability_esg_focus ? "Yes" : "No", <TreePine className="h-5 w-5" />, 'green')}
              </div>
              
              {company.investment_strategy_mission && (
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <div className="text-sm font-medium text-green-800 mb-2">Investment Strategy Mission</div>
                  <p className="text-green-700">{company.investment_strategy_mission}</p>
                </div>
              )}
              {company.investment_strategy_approach && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="text-sm font-medium text-blue-800 mb-2">Investment Strategy Approach</div>
                  <p className="text-blue-700">{company.investment_strategy_approach}</p>
                </div>
              )}
            </div>,
            'strategy',
            true,
            strategyFields,
            'emerald'
          )
      })()}

      {/* Financial Health & AUM */}
      {(() => {
        const financialFields = {
          fund_size: company.fund_size,
          aum: company.aum,
          balance_sheet_strength: company.balance_sheet_strength,
          dry_powder: company.dry_powder,
          annual_deployment_target: company.annual_deployment_target
        }
        
        return sectionHasData(financialFields) && 
          renderExpandableSection(
            "Financial Health & AUM", 
            <DollarSign className="h-5 w-5 text-green-600" />,
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInfoField("Fund Size", formatCurrency(company.fund_size), <DollarSign className="h-5 w-5" />, 'green')}
                {renderInfoField("Assets Under Management", formatCurrency(company.aum), <Landmark className="h-5 w-5" />, 'emerald')}
                {renderInfoField("Balance Sheet Strength", company.balance_sheet_strength, <Shield className="h-5 w-5" />, 'green')}
                {renderInfoField("Dry Powder", formatCurrency(company.dry_powder), <DollarSign className="h-5 w-5" />, 'green')}
                {renderInfoField("Annual Deployment Target", formatCurrency(company.annual_deployment_target), <Target className="h-5 w-5" />, 'emerald')}
              </div>
            </div>,
            'financial-health',
            true,
            financialFields,
            'green'
          )
      })()}
      
      {/* Public Company Data */}
      {(() => {
        const publicCompanyFields = {
          stock_ticker_symbol: company.stock_ticker_symbol,
          stock_exchange: company.stock_exchange,
          market_capitalization: company.market_capitalization,
          annual_revenue: company.annual_revenue,
          net_income: company.net_income,
          ebitda: company.ebitda,
          profit_margin: company.profit_margin,
          credit_rating: company.credit_rating,
          quarterly_earnings_link: company.quarterly_earnings_link
        }
        
        return sectionHasData(publicCompanyFields) && 
          renderExpandableSection(
            "Public Company Data", 
            <BarChart3 className="h-5 w-5 text-blue-600" />,
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInfoField("Stock Ticker", company.stock_ticker_symbol, <TrendingUp className="h-5 w-5" />, 'green')}
                {renderInfoField("Stock Exchange", company.stock_exchange, <BarChart3 className="h-5 w-5" />, 'blue')}
                {renderInfoField("Market Capitalization", formatCurrency(company.market_capitalization), <PieChart className="h-5 w-5" />, 'blue')}
                {renderInfoField("Annual Revenue", formatCurrency(company.annual_revenue), <TrendingUp className="h-5 w-5" />, 'green')}
                {renderInfoField("Net Income", formatCurrency(company.net_income), <DollarSign className="h-5 w-5" />, 'green')}
                {renderInfoField("EBITDA", formatCurrency(company.ebitda), <Calculator className="h-5 w-5" />, 'green')}
                {renderInfoField("Profit Margin", formatDecimal(company.profit_margin), <Percent className="h-5 w-5" />, 'emerald')}
                {renderInfoField("Credit Rating", company.credit_rating, <Star className="h-5 w-5" />, 'yellow')}
                {renderInfoField("Quarterly Earnings Link", company.quarterly_earnings_link, <ExternalLink className="h-5 w-5" />, 'blue', true)}
              </div>
            </div>,
            'public-company',
            false,
            publicCompanyFields,
            'blue'
          )
      })()}

            {/* Contact Information */}
      {(() => {
        const contactFields = {
          company_website: company.company_website,
          company_phone: company.company_phone,
          secondary_phone: company.secondary_phone,
          main_email: company.main_email,
          secondary_email: company.secondary_email
        }
        
        return sectionHasData(contactFields) && 
          renderExpandableSection(
            "Contact Information", 
            <Phone className="h-5 w-5 text-green-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("Website", company.company_website, <Globe className="h-5 w-5" />, 'blue', true)}
              {renderInfoField("Primary Phone", company.company_phone ? (
                <a href={`tel:${company.company_phone}`} className="text-green-600 hover:text-green-800">
                  {company.company_phone}
                </a>
              ) : null, <Phone className="h-5 w-5" />, 'green')}
              {renderInfoField("Secondary Phone", company.secondary_phone, <Phone className="h-5 w-5" />, 'green')}
              {renderInfoField("Primary Email", company.main_email ? (
                <a href={`mailto:${company.main_email}`} className="text-purple-600 hover:text-purple-800">
                  {company.main_email}
                </a>
              ) : null, <Mail className="h-5 w-5" />, 'purple')}
              {renderInfoField("Secondary Email", company.secondary_email, <Mail className="h-5 w-5" />, 'purple')}
            </div>,
            'contact',
            false,
            contactFields,
            'green'
          )
      })()}
      
      {/* Social Media */}
      {(() => {
        const socialFields = {
          company_linkedin: company.company_linkedin,
          twitter: company.twitter,
          facebook: company.facebook,
          instagram: company.instagram,
          youtube: company.youtube
        }
        
        return sectionHasData(socialFields) && 
          renderExpandableSection(
            "Social Media", 
            <Linkedin className="h-5 w-5 text-blue-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("LinkedIn", company.company_linkedin, <Linkedin className="h-5 w-5" />, 'blue', true)}
              {renderInfoField("Twitter", company.twitter, <Twitter className="h-5 w-5" />, 'cyan', true)}
              {renderInfoField("Facebook", company.facebook, <Facebook className="h-5 w-5" />, 'blue', true)}
              {renderInfoField("Instagram", company.instagram, <Instagram className="h-5 w-5" />, 'pink', true)}
              {renderInfoField("YouTube", company.youtube, <Youtube className="h-5 w-5" />, 'red', true)}
            </div>,
            'social-media',
            false,
            socialFields,
            'blue'
          )
      })()}

      {/* Headquarters */}
      {(() => {
        const headquartersFields = {
          company_address: company.company_address,
          company_city: company.company_city,
          company_state: company.company_state,
          company_zip: company.company_zip,
          company_country: company.company_country
        }
        
        return sectionHasData(headquartersFields) && 
          renderExpandableSection(
            "Headquarters", 
            <Building2 className="h-5 w-5 text-gray-600" />,
            <div className="space-y-6">
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <div className="flex items-start gap-3">
                  <div className="bg-gray-100 p-2 rounded-md">
                    <MapPin className="h-5 w-5 text-gray-600" />
                  </div>
                  <div className="text-gray-700">
                    {company.company_address && <div>{company.company_address}</div>}
                    {(company.company_city || company.company_state || company.company_zip) && (
                      <div>
                        {[company.company_city, company.company_state, company.company_zip].filter(Boolean).join(', ')}
                      </div>
                    )}
                    {company.company_country && <div>{company.company_country}</div>}
                  </div>
                </div>
              </div>
            </div>,
            'headquarters',
            false,
            headquartersFields,
            'gray'
          )
      })()}

            {/* Additional Locations */}
      {(() => {
        const additionalLocationsFields = {
          additional_address: company.additional_address,
          additional_city: company.additional_city,
          additional_state: company.additional_state,
          additional_zipcode: company.additional_zipcode,
          additional_country: company.additional_country,
          office_locations: company.office_locations
        }
        
        return sectionHasData(additionalLocationsFields) && 
          renderExpandableSection(
            "Additional Locations", 
            <MapPin className="h-5 w-5 text-blue-600" />,
            <div className="space-y-6">
              {/* Additional Address */}
              {(company.additional_address || company.additional_city || company.additional_state || 
                company.additional_zipcode || company.additional_country) && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="text-sm font-medium text-blue-800 mb-3">Additional Address</div>
                  <div className="flex items-start gap-3">
                    <div className="bg-blue-100 p-2 rounded-md">
                      <MapPin className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="text-blue-700">
                      {company.additional_address && <div>{company.additional_address}</div>}
                      {(company.additional_city || company.additional_state || company.additional_zipcode) && (
                        <div>
                          {[company.additional_city, company.additional_state, company.additional_zipcode].filter(Boolean).join(', ')}
                        </div>
                      )}
                      {company.additional_country && <div>{company.additional_country}</div>}
                    </div>
                  </div>
                </div>
              )}

              {/* Office Locations */}
              {company.office_locations && company.office_locations.length > 0 && (
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <div className="text-sm font-medium text-green-800 mb-3">Office Locations</div>
                  <div className="flex flex-wrap gap-2">
                    {company.office_locations.map((location: string, index: number) => (
                      <Badge key={index} className="bg-green-100 text-green-700 border border-green-200">
                        <MapPin className="h-3 w-3 mr-1" />
                        {location}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>,
            'additional-locations',
            false,
            additionalLocationsFields,
            'blue'
          )
      })()}

      {/* Partners & Funding */}
      {(() => {
        const partnersFundingFields = {
          partnerships: company.partnerships,
          funding_sources: company.funding_sources,
          recent_capital_raises: company.recent_capital_raises,
          key_equity_partners: company.key_equity_partners,
          key_debt_partners: company.key_debt_partners
        }
        
        return sectionHasData(partnersFundingFields) && 
          renderExpandableSection(
            "Partners & Funding", 
            <HandHeart className="h-5 w-5 text-green-600" />,
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInfoField("Partnerships", company.partnerships, <HandHeart className="h-5 w-5" />, 'pink')}
                {renderInfoField("Key Equity Partners", company.key_equity_partners, <Users className="h-5 w-5" />, 'green')}
                {renderInfoField("Key Debt Partners", company.key_debt_partners, <Banknote className="h-5 w-5" />, 'blue')}
              </div>

              {company.funding_sources && company.funding_sources.length > 0 && (
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <div className="text-sm font-medium text-green-800 mb-3">Funding Sources</div>
                  {renderArrayField(company.funding_sources, 'green')}
                </div>
              )}

              {company.recent_capital_raises && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="text-sm font-medium text-blue-800 mb-2">Recent Capital Raises</div>
                  <p className="text-blue-700">{company.recent_capital_raises}</p>
                </div>
              )}
            </div>,
            'partners-funding',
            false,
            partnersFundingFields,
            'pink'
          )
      })()}

      {/* Equity / Fund Profile */}
      {(() => {
        const equityFundFields = {
          investment_vehicle_type: company.investment_vehicle_type,
          active_fund_name_series: company.active_fund_name_series,
          fund_size_active_fund: company.fund_size_active_fund,
          fundraising_status: company.fundraising_status
        }
        
        return sectionHasData(equityFundFields) && 
          renderExpandableSection(
            "Equity / Fund Profile", 
            <Briefcase className="h-5 w-5 text-purple-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("Investment Vehicle Type", company.investment_vehicle_type, <Briefcase className="h-5 w-5" />, 'blue')}
              {renderInfoField("Active Fund Name/Series", company.active_fund_name_series, <FileText className="h-5 w-5" />, 'blue')}
              {renderInfoField("Active Fund Size", formatCurrency(company.fund_size_active_fund), <DollarSign className="h-5 w-5" />, 'green')}
              {renderInfoField("Fundraising Status", company.fundraising_status, <TrendingUp className="h-5 w-5" />, 'orange')}
            </div>,
            'equity-fund-profile',
            false,
            equityFundFields,
            'purple'
          )
      })()}
      
      {/* Debt / Lending Profile */}
      {(() => {
        const debtLendingFields = {
          lender_type: company.lender_type,
          annual_loan_volume: company.annual_loan_volume,
          lending_origin: company.lending_origin,
          typical_debt_to_equity_ratio: company.typical_debt_to_equity_ratio
        }
        
        return sectionHasData(debtLendingFields) && 
          renderExpandableSection(
            "Debt / Lending Profile", 
            <Banknote className="h-5 w-5 text-blue-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("Lender Type", company.lender_type, <Banknote className="h-5 w-5" />, 'blue')}
              {renderInfoField("Annual Loan Volume", formatCurrency(company.annual_loan_volume), <Calculator className="h-5 w-5" />, 'green')}
              {renderInfoField("Lending Origin", company.lending_origin, <MapPin className="h-5 w-5" />, 'gray')}
              {renderInfoField("Debt to Equity Ratio", formatDecimal(company.typical_debt_to_equity_ratio), <Scale className="h-5 w-5" />, 'orange')}
            </div>,
            'debt-lending-profile',
            false,
            debtLendingFields,
            'blue'
          )
      })()}

      {/* People & History */}
      {(() => {
        const peopleHistoryFields = {
          board_of_directors: company.board_of_directors,
          key_executives: company.key_executives,
          founder_background: company.founder_background,
          company_history: company.company_history
        }
        
        return sectionHasData(peopleHistoryFields) && 
          renderExpandableSection(
            "People & History", 
            <Users className="h-5 w-5 text-blue-600" />,
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInfoField("Board of Directors", company.board_of_directors, <Crown className="h-5 w-5" />, 'purple')}
                {renderInfoField("Key Executives", company.key_executives, <Users className="h-5 w-5" />, 'orange')}
              </div>

              {company.founder_background && (
                <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                  <div className="text-sm font-medium text-purple-800 mb-2">Founder Background</div>
                  <p className="text-purple-700">{company.founder_background}</p>
                </div>
              )}
              
              {company.company_history && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="text-sm font-medium text-blue-800 mb-2">Company History</div>
                  <p className="text-blue-700">{company.company_history}</p>
                </div>
              )}
            </div>,
            'people-history',
            false,
            peopleHistoryFields,
            'blue'
          )
      })()}

      {/* Expertise */}
      {(() => {
        const expertiseFields = {
          development_fee_structure: company.development_fee_structure,
          technology_proptech_adoption: company.technology_proptech_adoption,
          adaptive_reuse_experience: company.adaptive_reuse_experience,
          regulatory_zoning_expertise: company.regulatory_zoning_expertise
        }
        
        return sectionHasData(expertiseFields) && 
          renderExpandableSection(
            "Expertise", 
            <Star className="h-5 w-5 text-yellow-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("Development Fee Structure", company.development_fee_structure, <Calculator className="h-5 w-5" />, 'blue')}
              {renderInfoField("PropTech Adoption", company.technology_proptech_adoption ? "Yes" : "No", <Cpu className="h-5 w-5" />, 'blue')}
              {renderInfoField("Adaptive Reuse Experience", company.adaptive_reuse_experience ? "Yes" : "No", <Recycle className="h-5 w-5" />, 'emerald')}
              {renderInfoField("Regulatory/Zoning Expertise", company.regulatory_zoning_expertise ? "Yes" : "No", <Gavel className="h-5 w-5" />, 'purple')}
            </div>,
            'expertise',
            false,
            expertiseFields,
            'yellow'
          )
      })()}



      {/* Operational Profile */}
      {(() => {
        const operationalProfileFields = {
          products_services_description: company.products_services_description,
          target_customer_profile: company.target_customer_profile,
          major_competitors: company.major_competitors,
          unique_selling_proposition: company.unique_selling_proposition,
          industry_awards_recognitions: company.industry_awards_recognitions,
          market_share_percentage: company.market_share_percentage
        }
        
        return sectionHasData(operationalProfileFields) && 
          renderExpandableSection(
            "Operational Profile", 
            <Briefcase className="h-5 w-5 text-orange-600" />,
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInfoField("Major Competitors", company.major_competitors, <Activity className="h-5 w-5" />, 'red')}
                {renderInfoField("Industry Awards", company.industry_awards_recognitions, <Award className="h-5 w-5" />, 'yellow')}
                {renderInfoField("Market Share", formatDecimal(company.market_share_percentage), <PieChart className="h-5 w-5" />, 'blue')}
              </div>

              {company.products_services_description && (
                <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                  <div className="text-sm font-medium text-orange-800 mb-2">Products & Services</div>
                  <p className="text-orange-700">{company.products_services_description}</p>
                </div>
              )}

              {company.target_customer_profile && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="text-sm font-medium text-blue-800 mb-2">Target Customer Profile</div>
                  <p className="text-blue-700">{company.target_customer_profile}</p>
                </div>
              )}

              {company.unique_selling_proposition && (
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <div className="text-sm font-medium text-green-800 mb-2">Unique Selling Proposition</div>
                  <p className="text-green-700">{company.unique_selling_proposition}</p>
                </div>
              )}
            </div>,
            'operational-profile',
            false,
            operationalProfileFields,
            'orange'
          )
      })()}

      {/* Portfolio & Activity */}
      {(() => {
        const portfolioActivityFields = {
          number_of_properties: company.number_of_properties,
          portfolio_health: company.portfolio_health,
          transactions_completed_last_12m: company.transactions_completed_last_12m,
          total_transaction_volume_ytd: company.total_transaction_volume_ytd,
          deal_count_ytd: company.deal_count_ytd,
          average_deal_size: company.average_deal_size,
          portfolio_size_sqft: company.portfolio_size_sqft,
          portfolio_asset_count: company.portfolio_asset_count
        }
        
        return sectionHasData(portfolioActivityFields) && 
          renderExpandableSection(
            "Portfolio & Activity", 
            <Activity className="h-5 w-5 text-green-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("Number of Properties", formatNumber(company.number_of_properties), <Building className="h-5 w-5" />, 'orange')}
              {renderInfoField("Portfolio Health", company.portfolio_health, <Activity className="h-5 w-5" />, 'green')}
              {renderInfoField("Transactions (Last 12M)", formatNumber(company.transactions_completed_last_12m), <TrendingUp className="h-5 w-5" />, 'green')}
              {renderInfoField("Transaction Volume (YTD)", formatCurrency(company.total_transaction_volume_ytd), <DollarSign className="h-5 w-5" />, 'green')}
              {renderInfoField("Deal Count (YTD)", formatNumber(company.deal_count_ytd), <BarChart3 className="h-5 w-5" />, 'blue')}
              {renderInfoField("Average Deal Size", formatCurrency(company.average_deal_size), <Calculator className="h-5 w-5" />, 'orange')}
              {renderInfoField("Portfolio Size (SqFt)", formatNumber(company.portfolio_size_sqft), <Building className="h-5 w-5" />, 'purple')}
              {renderInfoField("Portfolio Asset Count", formatNumber(company.portfolio_asset_count), <Home className="h-5 w-5" />, 'indigo')}
            </div>,
            'portfolio-activity',
            false,
            portfolioActivityFields,
            'green'
          )
      })()}

      {/* Internal Relationship */}
      {(() => {
        const internalRelationshipFields = {
          internal_relationship_manager: company.internal_relationship_manager,
          last_contact_date: company.last_contact_date,
          pipeline_status: company.pipeline_status,
          role_in_previous_deal: company.role_in_previous_deal
        }
        
        return sectionHasData(internalRelationshipFields) && 
          renderExpandableSection(
            "Internal Relationship", 
            <Network className="h-5 w-5 text-blue-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("Relationship Manager", company.internal_relationship_manager, <Users className="h-5 w-5" />, 'blue')}
              {renderInfoField("Last Contact Date", company.last_contact_date, <Calendar className="h-5 w-5" />, 'gray')}
              {renderInfoField("Pipeline Status", company.pipeline_status, <Activity className="h-5 w-5" />, 'orange')}
              {renderInfoField("Previous Deal Role", company.role_in_previous_deal, <Briefcase className="h-5 w-5" />, 'gray')}
            </div>,
            'internal-relationship',
            false,
            internalRelationshipFields,
            'blue'
          )
      })()}
      
      {/* Data Provenance */}
      {(() => {
        const dataProvenanceFields = {
          recent_news_sentiment: company.recent_news_sentiment,
          data_source: company.data_source,
          last_updated_timestamp: company.last_updated_timestamp,
          data_confidence_score: company.data_confidence_score
        }
        
        return sectionHasData(dataProvenanceFields) && 
          renderExpandableSection(
            "Data Provenance", 
            <Info className="h-5 w-5 text-gray-600" />,
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderInfoField("Recent News Sentiment", company.recent_news_sentiment, <Eye className="h-5 w-5" />, 'purple')}
              {renderInfoField("Data Source", company.data_source, <Database className="h-5 w-5" />, 'gray')}
              {renderInfoField("Last Updated", company.last_updated_timestamp, <Calendar className="h-5 w-5" />, 'blue')}
              {renderInfoField("Data Confidence Score", company.data_confidence_score, <CheckCircle className="h-5 w-5" />, 'green')}
            </div>,
            'data-provenance',
            false,
            dataProvenanceFields,
            'purple'
          )
      })()}


    </div>
  )
}
