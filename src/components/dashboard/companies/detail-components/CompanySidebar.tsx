'use client'

import { CompanyDetail, ScrapedData } from '../shared/types'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Globe, MapPin, LinkedinIcon, ExternalLink, Mail, Phone, FileText, DollarSign } from 'lucide-react'
import { normalizeScrapedData } from '../shared/utils'

interface CompanySidebarProps {
  company: CompanyDetail
}

export default function CompanySidebar({ company }: CompanySidebarProps) {
  const normalizedScrapedData = normalizeScrapedData(company?.scraped_data)

  return (
    <div className="space-y-4">
      {/* LinkedIn Card */}
      {company.company_linkedin && (
        <div className="bg-white p-4 rounded-lg border border-gray-100">
          <h3 className="text-sm font-semibold flex items-center mb-3">
            <LinkedinIcon className="h-4 w-4 mr-2 text-blue-600" />
            LinkedIn
          </h3>
          <a
            href={company.company_linkedin}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:underline flex items-center text-sm"
          >
            View Company LinkedIn Profile
            <ExternalLink className="h-3.5 w-3.5 ml-1.5" />
          </a>
        </div>
      )}

      {/* Website Card */}
      {company.company_website && (
        <div className="bg-white p-4 rounded-lg border border-gray-100">
          <h3 className="text-sm font-semibold flex items-center mb-3">
            <Globe className="h-4 w-4 mr-2 text-blue-600" />
            Website
          </h3>
          <a
            href={company.company_website.startsWith('http') ? company.company_website : `https://${company.company_website}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:underline flex items-center text-sm"
          >
            {company.company_website.replace(/^https?:\/\/(www\.)?/, '')}
            <ExternalLink className="h-3.5 w-3.5 ml-1.5" />
          </a>
        </div>
      )}

      {/* Company Address Card */}
      {(company.company_address || company.company_city) && (
        <div className="bg-white p-4 rounded-lg border border-gray-100">
          <h3 className="text-sm font-semibold flex items-center mb-3">
            <MapPin className="h-4 w-4 mr-2 text-blue-600" />
            Company Address
          </h3>
          {company.company_address && (
            <p className="text-sm text-gray-700">{company.company_address}</p>
          )}
          {company.company_city && (
            <p className="text-sm text-gray-700 mt-1">
              {company.company_city}
              {company.company_state && `, ${company.company_state}`}
              {company.company_zip && ` ${company.company_zip}`}
              {company.company_country && `, ${company.company_country}`}
            </p>
          )}
        </div>
      )}

      {/* Contact Info Section */}
      {(normalizedScrapedData.contactInfo && 
        (normalizedScrapedData.contactInfo.mainPhone || 
         normalizedScrapedData.contactInfo.mainEmail || 
         (normalizedScrapedData.contactInfo.socialMedia && 
          Object.values(normalizedScrapedData.contactInfo.socialMedia || {}).some(val => val)))) && (
        <div className="bg-white p-4 rounded-lg border border-blue-100">
          <h3 className="text-sm font-semibold flex items-center mb-3 text-blue-800">
            <Mail className="h-4 w-4 mr-2 text-blue-600" />
            Contact Information
          </h3>
          <div className="space-y-3">
            {normalizedScrapedData.contactInfo?.mainPhone && (
              <div className="flex items-center text-sm">
                <Phone className="h-4 w-4 text-blue-600 mr-2 flex-shrink-0" />
                <span className="text-gray-600 mr-2">Phone:</span>
                <a href={`tel:${normalizedScrapedData.contactInfo.mainPhone}`} className="font-medium text-gray-800">
                  {normalizedScrapedData.contactInfo.mainPhone}
                </a>
              </div>
            )}
            
            {normalizedScrapedData.contactInfo?.mainEmail && (
              <div className="flex items-center text-sm">
                <Mail className="h-4 w-4 text-blue-600 mr-2 flex-shrink-0" />
                <span className="text-gray-600 mr-2">Email:</span>
                <a href={`mailto:${normalizedScrapedData.contactInfo.mainEmail}`} className="font-medium text-blue-600 hover:underline">
                  {normalizedScrapedData.contactInfo.mainEmail}
                </a>
              </div>
            )}
          </div>
          
          {/* Social Media Section */}
          {normalizedScrapedData.contactInfo?.socialMedia && 
           Object.values(normalizedScrapedData.contactInfo.socialMedia || {}).some(val => val) && (
            <div className="mt-4 pt-3 border-t border-blue-100">
              <h4 className="font-medium text-sm text-gray-700 mb-3">Social Media</h4>
              <div className="flex flex-wrap gap-2">
                {normalizedScrapedData.contactInfo.socialMedia?.linkedin && (
                  <a
                    href={normalizedScrapedData.contactInfo.socialMedia.linkedin}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center text-xs font-medium bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-md px-2.5 py-1.5 transition-colors"
                  >
                    <LinkedinIcon className="h-3.5 w-3.5 mr-1.5" />
                    LinkedIn
                  </a>
                )}
                {normalizedScrapedData.contactInfo.socialMedia?.twitter && (
                  <a
                    href={normalizedScrapedData.contactInfo.socialMedia.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center text-xs font-medium bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-md px-2.5 py-1.5 transition-colors"
                  >
                    <svg className="h-3.5 w-3.5 mr-1.5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                    </svg>
                    Twitter
                  </a>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Investment Criteria Section */}
      {(normalizedScrapedData.investmentCriteria && 
        (normalizedScrapedData.investmentCriteria.dealSize || 
         normalizedScrapedData.investmentCriteria.minimumDealSize || 
         (normalizedScrapedData.investmentCriteria.targetMarkets && 
          Array.isArray(normalizedScrapedData.investmentCriteria.targetMarkets)))) && (
        <div className="bg-white p-4 rounded-lg border border-blue-100">
          <h3 className="text-sm font-semibold flex items-center mb-3 text-blue-800">
            <FileText className="h-4 w-4 mr-2 text-blue-600" />
            Investment Criteria
          </h3>
          
          {/* Deal Size Related Fields */}
          {(normalizedScrapedData.investmentCriteria.dealSize || 
            normalizedScrapedData.investmentCriteria.minimumDealSize || 
            normalizedScrapedData.investmentCriteria.maximumDealSize) && (
            <div className="mb-4">
              <h4 className="font-medium text-sm text-gray-700 mb-2">Deal Size</h4>
              <div className="space-y-2">
                {normalizedScrapedData.investmentCriteria.dealSize && (
                  <div className="flex items-center text-sm">
                    <span className="text-gray-600">Overall:</span>
                    <span className="ml-2 font-medium">{normalizedScrapedData.investmentCriteria.dealSize}</span>
                  </div>
                )}
                {normalizedScrapedData.investmentCriteria.minimumDealSize && (
                  <div className="flex items-center text-sm">
                    <span className="text-gray-600">Minimum:</span>
                    <span className="ml-2 font-medium">{normalizedScrapedData.investmentCriteria.minimumDealSize}</span>
                  </div>
                )}
                {normalizedScrapedData.investmentCriteria.maximumDealSize && (
                  <div className="flex items-center text-sm">
                    <span className="text-gray-600">Maximum:</span>
                    <span className="ml-2 font-medium">{normalizedScrapedData.investmentCriteria.maximumDealSize}</span>
                  </div>
                )}
              </div>
            </div>
          )}
          
          {/* Markets */}
          {normalizedScrapedData.investmentCriteria.targetMarkets && normalizedScrapedData.investmentCriteria.targetMarkets.length > 0 && (
            <div className="mb-4 pt-3 border-t border-blue-100">
              <h4 className="font-medium text-sm text-gray-700 mb-2">Target Markets</h4>
              <div className="flex flex-wrap gap-1.5">
                {normalizedScrapedData.investmentCriteria.targetMarkets.map((market, idx) => (
                  <Badge key={idx} className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200 transition-colors px-2 py-0.5">
                    {market}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Risk Factors Card */}
      {company.risk_factors && company.risk_factors.length > 0 && (
        <div className="bg-white p-4 rounded-lg border border-yellow-200">
          <h3 className="text-sm font-semibold mb-3 text-yellow-800">Risk Factors</h3>
          <ul className="list-disc pl-5 space-y-1 text-sm text-gray-700">
            {company.risk_factors.map((risk, index) => (
              <li key={index}>{risk}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
} 