'use client'

import React, { useState } from 'react'
import { CompanyDetail } from '../shared/types'
import InvestmentCriteriaSlider from '../../investment-criteria/InvestmentCriteriaSlider'
import InvestmentCriteriaAddModal from '../../investment-criteria/InvestmentCriteriaAddModal'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'

interface CompanyInvestmentCriteriaTabProps {
  company: CompanyDetail
}

export default function CompanyInvestmentCriteriaTab({ company }: CompanyInvestmentCriteriaTabProps) {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)

  const handleAddSuccess = () => {
    // Refresh the investment criteria slider by updating the key
    setRefreshKey(prev => prev + 1)
  }

  return (
    <div className="space-y-6">
      {/* Header with Add Button */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Investment Criteria</h3>
          <p className="text-sm text-gray-600">Manage investment criteria for this company</p>
        </div>
        <Button
          onClick={() => setIsAddModalOpen(true)}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Investment Criteria
        </Button>
      </div>

      {/* Investment Criteria Slider */}
      <InvestmentCriteriaSlider
        key={refreshKey}
        entityType="company"
        entityId={company.company_id}
        entityName={company.company_name}
      />

      {/* Add Investment Criteria Modal */}
      <InvestmentCriteriaAddModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        entityType="company"
        entityId={company.company_id}
        entityName={company.company_name}
        onSuccess={handleAddSuccess}
      />
    </div>
  )
} 