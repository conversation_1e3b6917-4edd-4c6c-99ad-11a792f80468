"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import {
  Plus,
  Check,
  Search,
  ExternalLink,
  X
} from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useToast } from "@/hooks/use-toast";
import { ChevronDown, ChevronRight } from "lucide-react";
import InvestmentCriteriaForm from "@/components/dashboard/investment-criteria/InvestmentCriteriaForm";

// Debounce utility function
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Define the company data structure based on the new schema
interface CompanyFormData {
  // Core identifiers
  company_id?: number;
  company_name: string;
  company_type?: string;
  industry?: string;
  
  // Business details
  business_model?: string;
  investment_focus?: string[];
  investment_strategy_mission?: string;
  investment_strategy_approach?: string;
  
  // Contact information
    website?: string;
  main_phone?: string;
  secondary_phone?: string;
  main_email?: string;
  secondary_email?: string;
      linkedin?: string;
      twitter?: string;
      facebook?: string;
      instagram?: string;
      youtube?: string;
  
  // Additional fields from database
  llm_response?: string;
  llm_token_usage?: number;
  llm_used?: string;
  overview_v2_error?: string;
  overview_v2_date?: string;
  overview_v2_error_count?: number;
  overview_v2_status?: string;
  
  // Location information
  headquarters_address?: string;
  headquarters_city?: string;
  headquarters_state?: string;
  headquarters_zipcode?: string;
  headquarters_country?: string;
  additional_address?: string;
  additional_city?: string;
  additional_state?: string;
  additional_zipcode?: string;
  additional_country?: string;
  
  // Financial and operational metrics
  fund_size?: number;
  aum?: number;
  number_of_properties?: number;
  number_of_offices?: number;
  office_locations?: string[];
  founded_year?: number;
  number_of_employees?: number;
    partnerships?: string[];
  
  // Capital and funding information
  balance_sheet_strength?: string;
  funding_sources?: string[];
  recent_capital_raises?: string;
  typical_debt_to_equity_ratio?: number;
  development_fee_structure?: string;
  key_equity_partners?: string[];
  key_debt_partners?: string[];
  
  // Market positioning
  market_cycle_positioning?: string;
  urban_vs_suburban_preference?: string;
  sustainability_esg_focus?: boolean;
  technology_proptech_adoption?: boolean;
  adaptive_reuse_experience?: boolean;
  regulatory_zoning_expertise?: boolean;
  
  // Investment structure
  investment_vehicle_type?: string;
  active_fund_name_series?: string;
  fund_size_active_fund?: number;
  fundraising_status?: string;
  
  // Lending information
  lender_type?: string;
  annual_loan_volume?: number;
  lending_origin_balance_sheet_securitization?: string;
  portfolio_health?: string;
  
  // Leadership and governance
  board_of_directors?: string[];
  key_executives?: string[];
  founder_background?: string;
  company_history?: string;
  
  // Public company information
  stock_ticker_symbol?: string;
  stock_exchange?: string;
  market_capitalization?: number;
  annual_revenue?: number;
  net_income?: number;
  ebitda?: number;
  profit_margin?: number;
  credit_rating?: string;
  quarterly_earnings_link?: string;
  
  // Market position
  products_services_description?: string;
  target_customer_profile?: string;
  major_competitors?: string[];
  market_share_percentage?: number;
  unique_selling_proposition?: string;
  industry_awards_recognitions?: string[];
  
  // Corporate structure
  corporate_structure?: string;
  parent_company?: string;
  subsidiaries?: string[];
  
  // Activity and relationship management
  dry_powder?: number;
  annual_deployment_target?: number;
  transactions_completed_last_12m?: number;
  internal_relationship_manager?: string;
  last_contact_date?: string;
  pipeline_status?: string;
  role_in_previous_deal?: string;
  total_transaction_volume_ytd?: number;
  deal_count_ytd?: number;
  average_deal_size?: number;
  portfolio_size_sqft?: number;
  portfolio_asset_count?: number;
  
  // Metadata
  recent_news_sentiment?: string;
  data_source?: string;
  last_updated_timestamp?: string;
  data_confidence_score?: number;
}

interface FormOptions {
  companyTypes: string[]
  industries: string[]
  investmentStrategies: string[]
  investmentVehicleTypes: string[]
  lenderTypes: string[]
  stockExchanges: string[]
  creditRatings: string[]
  corporateStructures: string[]
  pipelineStatuses: string[]
  roleInPreviousDeals: string[]
  recentNewsSentiments: string[]
  fundingStatuses: string[]
}

interface InvestmentCriteriaFormData {
  // Central data
  capital_position: string;
  minimum_deal_size?: number;
  maximum_deal_size?: number;
  country?: string[];
  region?: string[];
  state?: string[];
  city?: string[];
  property_types?: string[];
  property_subcategories?: string[];
  strategies?: string[];
  decision_making_process?: string;
  notes?: string;
  
  // All debt fields (corrected field names)
  debt_notes?: string;
  debt_closing_time?: number;
  debt_future_facilities?: string;
  debt_eligible_borrower?: string;
  debt_occupancy_requirements?: string;
  debt_lien_position?: string;
  debt_min_loan_dscr?: number;
  debt_max_loan_dscr?: number;
  debt_recourse_loan?: string;
  debt_loan_min_debt_yield?: string;
  debt_prepayment?: string;
  debt_yield_maintenance?: string;
  debt_application_deposit?: number;
  debt_good_faith_deposit?: number;
  debt_loan_origination_max_fee?: number;
  debt_loan_origination_min_fee?: number;
  debt_loan_exit_min_fee?: number;
  debt_loan_exit_max_fee?: number;
  debt_loan_interest_rate?: number;
  debt_loan_interest_rate_based_off_sofr?: number;
  debt_loan_interest_rate_based_off_wsj?: number;
  debt_loan_interest_rate_based_off_prime?: number;
  debt_loan_interest_rate_based_off_3yt?: number;
  debt_loan_interest_rate_based_off_5yt?: number;
  debt_loan_interest_rate_based_off_10yt?: number;
  debt_loan_interest_rate_based_off_30yt?: number;
  debt_rate_lock?: string;
  debt_rate_type?: string;
  debt_loan_to_value_max?: number;
  debt_loan_to_value_min?: number;
  debt_loan_to_cost_min?: number;
  debt_loan_to_cost_max?: number;
  debt_program_overview?: string;
  loan_type?: string; // Fixed: was debt_loan_type
  debt_loan_type_normalized?: string;
  structured_loan_tranche?: string; // Fixed: was debt_structured_loan_tranche
  loan_program?: string; // Fixed: was debt_loan_program
  debt_min_loan_term?: number;
  debt_max_loan_term?: number;
  debt_amortization?: string;
  
  // All equity fields
  equity_target_return?: number;
  equity_minimum_internal_rate_of_return?: number;
  equity_minimum_yield_on_cost?: number;
  equity_minimum_equity_multiple?: number;
  equity_target_cash_on_cash_min?: number;
  equity_min_hold_period_years?: number;
  equity_max_hold_period_years?: number;
  equity_ownership_requirement?: string;
  equity_attachment_point?: number;
  equity_max_leverage_tolerance?: number;
  equity_typical_closing_timeline_days?: number;
  equity_proof_of_funds_requirement?: boolean;
  equity_notes?: string;
  equity_program_overview?: string;
  equity_occupancy_requirements?: string;
  equity_yield_on_cost?: number;
  equity_target_return_irr_on_equity?: number;
  equity_equity_multiple?: number;
  equity_position_specific_irr?: number;
  equity_position_specific_equity_multiple?: number;
}

interface CompanyFormProps {
  companyId?: number | string;
  initialData?: Partial<CompanyFormData>;
  isEmbedded?: boolean;
  onSuccess?: (companyId: number) => void;
  onCancel?: () => void;
}

interface CustomSelectProps {
  options: string[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  addNewLabel?: string;
  onAddNew?: (newValue: string) => void;
  disabled?: boolean;
}

interface ArrayInputProps {
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  label: string;
  addButtonText?: string;
}

function CustomSelect({
  options,
  value,
  onValueChange,
  placeholder = "Select...",
  addNewLabel = "Add New Option...",
  onAddNew = (newValue: string) => {},
  disabled = false,
}: CustomSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [localOptions, setLocalOptions] = useState<string[]>(options || []);
  const inputRef = useRef<HTMLInputElement>(null);

  // Update local options when props change
  useEffect(() => {
    setLocalOptions(options || []);
  }, [options]);

  const filteredOptions = localOptions.filter((option) =>
    option.toLowerCase().includes(searchValue.toLowerCase())
  );

  const handleAddNew = () => {
    if (searchValue.trim()) {
      const newValue = searchValue.trim();
      // Add to local options for this session
      setLocalOptions(prev => [...prev, newValue]);
      // Call the onAddNew callback (this will update the form field)
      onAddNew(newValue);
      // Set the value
      onValueChange(newValue);
      setSearchValue("");
      setIsOpen(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (searchValue.trim() && !localOptions.some(option => 
        option.toLowerCase() === searchValue.trim().toLowerCase()
      )) {
        handleAddNew();
      } else if (filteredOptions.length > 0) {
        onValueChange(filteredOptions[0]);
        setIsOpen(false);
      }
    } else if (e.key === "Escape") {
      setIsOpen(false);
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={isOpen}
          className="w-full justify-between text-left"
          onClick={() => setIsOpen(!isOpen)}
          disabled={disabled}
        >
          <span className="truncate flex-1 text-left">
            {value || placeholder}
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0">
        <div className="p-2">
          <Input
            ref={inputRef}
            placeholder="Search..."
            value={searchValue}
            onChange={(e) => {
              setSearchValue(e.target.value);
            }}
            onKeyDown={handleKeyDown}
            className="mb-2"
          />
          <div className="max-h-60 overflow-auto">
            {filteredOptions.map((option) => (
              <div
                key={option}
                className="flex items-center px-2 py-1.5 text-sm hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  onValueChange(option);
                  setIsOpen(false);
                }}
              >
                {option}
                {value === option && <Check className="ml-auto h-4 w-4" />}
              </div>
            ))}
            {searchValue.trim() && !localOptions.some(option => 
              option.toLowerCase() === searchValue.trim().toLowerCase()
            ) && (
              <div
                className="flex items-center px-2 py-1.5 text-sm hover:bg-gray-100 cursor-pointer text-blue-600"
                onClick={handleAddNew}
              >
                <Plus className="mr-2 h-4 w-4" />
                {addNewLabel}
              </div>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

function ArrayInput({ value, onChange, placeholder, label, addButtonText = "Add Item" }: ArrayInputProps) {
  const [newItem, setNewItem] = useState("");

  const handleAdd = () => {
    if (newItem.trim()) {
      onChange([...value, newItem.trim()]);
      setNewItem("");
    }
  };

  const handleRemove = (index: number) => {
    onChange(value.filter((_, i) => i !== index));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAdd();
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex gap-2">
        <Input
          value={newItem}
          onChange={(e) => setNewItem(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          className="flex-1"
        />
        <Button
          type="button"
          onClick={handleAdd}
          disabled={!newItem.trim()}
          size="sm"
          className="px-3"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
      
      {value.length > 0 && (
        <div className="space-y-2">
          {value.map((item, index) => (
            <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
              <span className="flex-1 text-sm">{item}</span>
              <Button
                type="button"
                onClick={() => handleRemove(index)}
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

const ContentWrapper = ({
  children,
  isEmbedded,
}: {
  children: React.ReactNode;
  isEmbedded: boolean;
}) => {
  if (isEmbedded) {
    return <div className="space-y-6">{children}</div>;
  }
  return (
    <Card>
      <CardContent className="p-6">{children}</CardContent>
    </Card>
  );
};

// Collapsible Section Component
interface CollapsibleSectionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  isOpen: boolean;
  onToggle: () => void;
  children: React.ReactNode;
  bgColor?: string;
  iconColor?: string;
}

const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  description,
  icon,
  isOpen,
  onToggle,
  children,
  bgColor = "bg-blue-100",
  iconColor = "text-blue-600"
}) => {
  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100">
      <button
        type="button"
        onClick={onToggle}
        className="w-full p-6 flex items-center justify-between hover:bg-gray-50 transition-colors"
      >
        <div className="flex items-center">
          <div className={`w-10 h-10 ${bgColor} rounded-full flex items-center justify-center mr-4`}>
            <div className={iconColor}>
              {icon}
            </div>
          </div>
          <div className="text-left">
            <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
            <p className="text-sm text-gray-500">{description}</p>
          </div>
        </div>
        {isOpen ? (
          <ChevronDown className="h-6 w-6 text-gray-400" />
        ) : (
          <ChevronRight className="h-6 w-6 text-gray-400" />
        )}
      </button>
      {isOpen && (
        <div className="px-6 pb-6">
          {children}
        </div>
      )}
    </div>
  );
};

const normalizeUrl = (url: string): string => {
  if (!url) return url;
  
  // Remove whitespace
  url = url.trim();
  
  // If it doesn't start with http:// or https://, add https://
  if (!url.match(/^https?:\/\//)) {
    url = 'https://' + url;
  }
  
  return url;
};

const isValidUrl = (url: string): boolean => {
  if (!url) return true; // Empty URLs are valid
  try {
    new URL(normalizeUrl(url));
    return true;
  } catch {
    return false;
  }
};

// Phone number validation - only allows digits, spaces, parentheses, dashes, dots, and plus sign
const validatePhoneNumber = (phone: string): boolean => {
  if (!phone) return true; // Empty phone numbers are valid
  const phoneRegex = /^[\d\s\(\)\-\+\.]+$/;
  return phoneRegex.test(phone);
};

// Format phone number input - remove invalid characters
const formatPhoneNumber = (phone: string): string => {
  return phone.replace(/[^\d\s\(\)\-\+\.]/g, '');
};

export default function CompanyForm({
  companyId,
  initialData,
  isEmbedded = false,
  onSuccess,
  onCancel,
}: CompanyFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [urlErrors, setUrlErrors] = useState<{ 
    website?: string; 
    linkedin?: string; 
    twitter?: string; 
    facebook?: string; 
    instagram?: string; 
    youtube?: string; 
    quarterly_earnings_link?: string; 
  }>({});
  
  // Phone number validation state
  const [phoneErrors, setPhoneErrors] = useState<{
    main_phone?: string;
    secondary_phone?: string;
  }>({});
  
  // Form options state for dynamic mappings
  const [formOptions, setFormOptions] = useState<FormOptions>({
    companyTypes: [],
    industries: [],
    investmentStrategies: [],
    investmentVehicleTypes: [],
    lenderTypes: [],
    stockExchanges: [],
    creditRatings: [],
    corporateStructures: [],
    pipelineStatuses: [],
    roleInPreviousDeals: [],
    recentNewsSentiments: [],
    fundingStatuses: []
  });
  const [isLoadingFormOptions, setIsLoadingFormOptions] = useState(true);

  // Company search functionality
  const [companySuggestions, setCompanySuggestions] = useState<any[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);

  // Domain search functionality
  const [domainSuggestions, setDomainSuggestions] = useState<any[]>([]);
  const [showDomainSuggestions, setShowDomainSuggestions] = useState(false);
  const [isSearchingDomain, setIsSearchingDomain] = useState(false);
  const [selectedDomainSuggestionIndex, setSelectedDomainSuggestionIndex] = useState(-1);

  // Investment Criteria state
  const [investmentCriteria, setInvestmentCriteria] = useState<InvestmentCriteriaFormData[]>([]);
  const [showAddICForm, setShowAddICForm] = useState(false);
  const [isSavingIC, setIsSavingIC] = useState(false);

  // Collapsible sections state
  const [openSections, setOpenSections] = useState({
    companyInfo: true,
    contactSocial: true,
    location: false,
    financial: false,
    investment: false,
    partnership: false,
    additional: false,
    leadership: false,
    publicCompany: false,
    relationship: false,
    additionalContact: false,
    marketPositioning: false,
    metadata: false,
    investmentCriteria: false
  });

  const isEditing = !!companyId;

  // Fetch form options from database mappings
  useEffect(() => {
    const fetchFormOptions = async () => {
      setIsLoadingFormOptions(true);
      try {
        const response = await fetch('/api/mapping-tables/form-options');
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data) {
            setFormOptions({
              companyTypes: result.data.companyTypes || [],
              industries: result.data.industries || [],
              investmentStrategies: result.data.investmentStrategies || [],
              investmentVehicleTypes: result.data.investmentVehicleTypes || [],
              lenderTypes: result.data.lenderTypes || [],
              stockExchanges: result.data.stockExchanges || [],
              creditRatings: result.data.creditRatings || [],
              corporateStructures: result.data.corporateStructures || [],
              pipelineStatuses: result.data.pipelineStatuses || [],
              roleInPreviousDeals: result.data.roleInPreviousDeals || [],
              recentNewsSentiments: result.data.recentNewsSentiments || [],
              fundingStatuses: result.data.fundingStatuses || [],
            });
          } else {
            console.warn('Form options API returned invalid data structure:', result);
          }
        } else {
          console.error('Failed to fetch form options:', response.status, response.statusText);
        }
      } catch (error) {
        console.error('Error fetching form options:', error);
      } finally {
        setIsLoadingFormOptions(false);
      }
    };

    fetchFormOptions();
  }, []);

  const [formData, setFormData] = useState<CompanyFormData>({
    company_name: "",
    company_type: "",
    industry: "",
    business_model: "",
    investment_focus: [],
    investment_strategy_mission: "",
    investment_strategy_approach: "",
    website: "",
    main_phone: "",
    secondary_phone: "",
    main_email: "",
    secondary_email: "",
    linkedin: "",
    twitter: "",
    facebook: "",
    instagram: "",
    youtube: "",
    headquarters_address: "",
    headquarters_city: "",
    headquarters_state: "",
    headquarters_zipcode: "",
    headquarters_country: "",
    additional_address: "",
    additional_city: "",
    additional_state: "",
    additional_zipcode: "",
    additional_country: "",
    founded_year: undefined,
    number_of_employees: undefined,
    office_locations: [],
    partnerships: [],
    funding_sources: [],
    key_equity_partners: [],
    key_debt_partners: [],
    board_of_directors: [],
    key_executives: [],
    major_competitors: [],
    industry_awards_recognitions: [],
    subsidiaries: [],
    ...initialData,
  });



  useEffect(() => {
    if (companyId && !initialData) {
      const fetchCompanyData = async () => {
        try {
          const response = await fetch(`/api/companies/${companyId}`);
          if (!response.ok) {
            throw new Error("Failed to fetch company data");
          }
          const data = await response.json();
          setFormData({
            company_id: data.company_id,
            company_name: data.company_name || "",
            company_type: data.company_type || "",
            industry: data.industry || "", // Map from DB field 'industry' to form field
            business_model: data.business_model || "",
            investment_focus: data.investment_focus || [],
            investment_strategy_mission: data.investment_strategy_mission || "",
            investment_strategy_approach: data.investment_strategy_approach || "",
            website: data.company_website || "", // Map from DB field 'company_website' to form field
            main_phone: data.company_phone || "", // Map from DB field 'company_phone' to form field
            secondary_phone: data.secondary_phone || "",
            main_email: data.main_email || "",
            secondary_email: data.secondary_email || "",
            linkedin: data.company_linkedin || "", // Map from DB field 'company_linkedin' to form field
            twitter: data.twitter || "",
            facebook: data.facebook || "",
            instagram: data.instagram || "",
            youtube: data.youtube || "",
            headquarters_address: data.company_address || "", // Map from DB field 'company_address' to form field
            headquarters_city: data.company_city || "", // Map from DB field 'company_city' to form field
            headquarters_state: data.company_state || "", // Map from DB field 'company_state' to form field
            headquarters_zipcode: data.company_zip || "", // Map from DB field 'company_zip' to form field
            headquarters_country: data.company_country || "", // Map from DB field 'company_country' to form field
            additional_address: data.additional_address || "",
            additional_city: data.additional_city || "",
            additional_state: data.additional_state || "",
            additional_zipcode: data.additional_zipcode || "",
            additional_country: data.additional_country || "",
            fund_size: data.fund_size || undefined,
            aum: data.aum || undefined,
            number_of_properties: data.number_of_properties || undefined,
            number_of_offices: data.number_of_offices || undefined,
            office_locations: data.office_locations || [],
            founded_year: data.founded_year || undefined,
            number_of_employees: data.number_of_employees || undefined,
            partnerships: data.partnerships || [],
            balance_sheet_strength: data.balance_sheet_strength || "",
            funding_sources: data.funding_sources || [],
            recent_capital_raises: data.recent_capital_raises || "",
            typical_debt_to_equity_ratio: data.typical_debt_to_equity_ratio || undefined,
            development_fee_structure: data.development_fee_structure || "",
            key_equity_partners: data.key_equity_partners || [],
            key_debt_partners: data.key_debt_partners || [],
            market_cycle_positioning: data.market_cycle_positioning || "",
            urban_vs_suburban_preference: data.urban_vs_suburban_preference || "",
            sustainability_esg_focus: data.sustainability_esg_focus || false,
            technology_proptech_adoption: data.technology_proptech_adoption || false,
            adaptive_reuse_experience: data.adaptive_reuse_experience || false,
            regulatory_zoning_expertise: data.regulatory_zoning_expertise || false,
            investment_vehicle_type: data.investment_vehicle_type || "",
            active_fund_name_series: data.active_fund_name_series || "",
            fund_size_active_fund: data.fund_size_active_fund || undefined,
            fundraising_status: data.fundraising_status || "",
            lender_type: data.lender_type || "",
            annual_loan_volume: data.annual_loan_volume || undefined,
            lending_origin_balance_sheet_securitization: data.lending_origin || "", // Map from DB field 'lending_origin' to form field
            portfolio_health: data.portfolio_health || "",
            board_of_directors: data.board_of_directors || [],
            key_executives: data.key_executives || [],
            founder_background: data.founder_background || "",
            company_history: data.company_history || "",
            stock_ticker_symbol: data.stock_ticker_symbol || "",
            stock_exchange: data.stock_exchange || "",
            market_capitalization: data.market_capitalization || undefined,
            annual_revenue: data.annual_revenue || undefined,
            net_income: data.net_income || undefined,
            ebitda: data.ebitda || undefined,
            profit_margin: data.profit_margin || undefined,
            credit_rating: data.credit_rating || "",
            quarterly_earnings_link: data.quarterly_earnings_link || "",
            products_services_description: data.products_services_description || "",
            target_customer_profile: data.target_customer_profile || "",
            major_competitors: data.major_competitors || [],
            market_share_percentage: data.market_share_percentage || undefined,
            unique_selling_proposition: data.unique_selling_proposition || "",
            industry_awards_recognitions: data.industry_awards_recognitions || [],
            corporate_structure: data.corporate_structure || "",
            parent_company: data.parent_company || "",
            subsidiaries: data.subsidiaries || [],
            dry_powder: data.dry_powder || undefined,
            annual_deployment_target: data.annual_deployment_target || undefined,
            transactions_completed_last_12m: data.transactions_completed_last_12m || undefined,
            internal_relationship_manager: data.internal_relationship_manager || "",
            last_contact_date: data.last_contact_date || "",
            pipeline_status: data.pipeline_status || "",
            role_in_previous_deal: data.role_in_previous_deal || "",
            total_transaction_volume_ytd: data.total_transaction_volume_ytd || undefined,
            deal_count_ytd: data.deal_count_ytd || undefined,
            average_deal_size: data.average_deal_size || undefined,
            portfolio_size_sqft: data.portfolio_size_sqft || undefined,
            portfolio_asset_count: data.portfolio_asset_count || undefined,
            recent_news_sentiment: data.recent_news_sentiment || "",
            data_source: data.data_source || "",
            last_updated_timestamp: data.last_updated_timestamp || "",
            data_confidence_score: data.data_confidence_score || undefined,
          });
        } catch (error) {
          console.error("Error fetching company data:", error);
          setError("Failed to load company data");
        }
      };

      fetchCompanyData();
    }
  }, [companyId, initialData]);

  // Update form data when initialData changes
  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        ...initialData,
      }));
    }
  }, [initialData]);

  // Cleanup effect to clear suggestions when component unmounts
  useEffect(() => {
    return () => {
      setCompanySuggestions([]);
      setShowSuggestions(false);
      setIsSearching(false);
      setDomainSuggestions([]);
      setShowDomainSuggestions(false);
      setIsSearchingDomain(false);
    };
  }, []);

  // Debounced company search function
  const debouncedSearchCompanies = useCallback(
    debounce(async (searchTerm: string) => {
      console.log('Debounced search called with:', searchTerm, 'Length:', searchTerm.length);
      
      if (searchTerm.length < 2) {
        console.log('Search term too short, clearing suggestions');
        setCompanySuggestions([]);
        setShowSuggestions(false);
        setIsSearching(false);
        return;
      }

      setIsSearching(true);
      try {
        console.log('Making API call for:', searchTerm);
        const url = `/api/companies/search?q=${encodeURIComponent(searchTerm)}`;
        console.log('Full URL:', url);
        
        const response = await fetch(url);
        console.log('Response status:', response.status, response.ok);
        
        if (response.ok) {
          const suggestions = await response.json();
          console.log('Suggestions received:', suggestions);
          setCompanySuggestions(suggestions);
          setShowSuggestions(true);
        } else {
          console.error('Search failed with status:', response.status);
          toast({
            title: "Search Failed",
            description: `Search failed: ${response.status} ${response.statusText}`,
            variant: "destructive",
          });
          setCompanySuggestions([]);
          setShowSuggestions(false);
        }
      } catch (error) {
        console.error('Error searching companies:', error);
        toast({
          title: "Search Error",
          description: "Failed to search companies. Please check your connection and try again.",
          variant: "destructive",
        });
        setCompanySuggestions([]);
        setShowSuggestions(false);
      } finally {
        setIsSearching(false);
      }
    }, 300),
    [toast]
  );

  // Debounced domain search function
  const debouncedSearchByDomain = useCallback(
    debounce(async (domain: string) => {
      console.log('Domain search called with:', domain, 'Length:', domain.length);
      
      if (domain.length < 2) {
        console.log('Domain too short, clearing suggestions');
        setDomainSuggestions([]);
        setShowDomainSuggestions(false);
        setIsSearchingDomain(false);
        return;
      }

      setIsSearchingDomain(true);
      try {
        console.log('Making domain API call for:', domain);
        const url = `/api/companies/search?domain=${encodeURIComponent(domain)}`;
        console.log('Full URL:', url);
        
        const response = await fetch(url);
        console.log('Response status:', response.status, response.ok);
        
        if (response.ok) {
          const suggestions = await response.json();
          console.log('Domain suggestions received:', suggestions);
          setDomainSuggestions(suggestions);
          setShowDomainSuggestions(true);
        } else {
          console.error('Domain search failed with status:', response.status);
          toast({
            title: "Domain Search Failed",
            description: `Domain search failed: ${response.status} ${response.statusText}`,
            variant: "destructive",
          });
          setDomainSuggestions([]);
          setShowDomainSuggestions(false);
        }
      } catch (error) {
        console.error('Error searching companies by domain:', error);
        toast({
          title: "Domain Search Error",
          description: "Failed to search companies by domain. Please check your connection and try again.",
          variant: "destructive",
        });
        setDomainSuggestions([]);
        setShowDomainSuggestions(false);
      } finally {
        setIsSearchingDomain(false);
      }
    }, 300),
    [toast]
  );

  // Handle company name input change
  const handleCompanyNameChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setFormData(prev => ({ ...prev, company_name: value }));
      
      if (value.length >= 2) {
        debouncedSearchCompanies(value);
      } else {
        setCompanySuggestions([]);
        setShowSuggestions(false);
      }
    },
    [debouncedSearchCompanies]
  );

  // Handle company selection from suggestions
  const handleCompanySelect = useCallback((company: any) => {
    console.log('Company selected:', company);
    setFormData(prev => ({ ...prev, company_name: company.company_name }));
    setCompanySuggestions([]);
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);
  }, []);

  // Handle keyboard navigation
  const handleCompanyNameKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < companySuggestions.length - 1 ? prev + 1 : prev
        );
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : -1);
      } else if (e.key === 'Enter' && selectedSuggestionIndex >= 0) {
        e.preventDefault();
        handleCompanySelect(companySuggestions[selectedSuggestionIndex]);
      } else if (e.key === 'Escape') {
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
      }
    },
    [companySuggestions, selectedSuggestionIndex, handleCompanySelect]
  );

  // Handle company link click
  const handleCompanyLinkClick = useCallback((e: React.MouseEvent, companyId: number) => {
    e.stopPropagation();
    router.push(`/dashboard/companies/${companyId}`);
  }, [router]);

  // Handle domain selection from suggestions
  const handleDomainSelect = useCallback((company: any) => {
    console.log('Company selected by domain:', company);
    setFormData(prev => ({ 
      ...prev, 
      company_name: company.company_name,
      website: company.company_website || prev.website,
      industry: company.industry || prev.industry,
      headquarters_address: company.company_address || prev.headquarters_address,
      headquarters_city: company.company_city || prev.headquarters_city,
      headquarters_state: company.company_state || prev.headquarters_state,
      headquarters_country: company.company_country || prev.headquarters_country,
    }));
    setDomainSuggestions([]);
    setShowDomainSuggestions(false);
    setSelectedDomainSuggestionIndex(-1);
  }, []);

  // Handle domain search button click
  const handleDomainSearch = useCallback(() => {
    const domain = formData.website;
    if (domain && domain.length >= 2) {
      // Extract domain from URL if it's a full URL
      const domainToSearch = domain.includes('://') 
        ? new URL(domain).hostname 
        : domain.replace(/^https?:\/\//, '').replace(/^www\./, '');
      
      debouncedSearchByDomain(domainToSearch);
    }
  }, [formData.website, debouncedSearchByDomain]);

  // Handle domain keyboard navigation
  const handleDomainKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedDomainSuggestionIndex(prev => 
          prev < domainSuggestions.length - 1 ? prev + 1 : prev
        );
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedDomainSuggestionIndex(prev => prev > 0 ? prev - 1 : -1);
      } else if (e.key === 'Enter' && selectedDomainSuggestionIndex >= 0) {
        e.preventDefault();
        handleDomainSelect(domainSuggestions[selectedDomainSuggestionIndex]);
      } else if (e.key === 'Escape') {
        setShowDomainSuggestions(false);
        setSelectedDomainSuggestionIndex(-1);
      }
    },
    [domainSuggestions, selectedDomainSuggestionIndex, handleDomainSelect]
  );

  // Investment Criteria handlers
  const handleAddInvestmentCriteria = async (formData: InvestmentCriteriaFormData): Promise<void> => {
    setIsSavingIC(true);
    try {
      const updatedList = [...investmentCriteria, formData];
      setInvestmentCriteria(updatedList);
      setShowAddICForm(false);
      toast({
        title: "Success",
        description: "Investment criteria added successfully",
      });
    } catch (error) {
      console.error('Error adding investment criteria:', error);
      toast({
        title: "Error",
        description: "Failed to add investment criteria",
        variant: "destructive",
      });
    } finally {
      setIsSavingIC(false);
    }
  };

  const handleCancelICForm = () => {
    setShowAddICForm(false);
  };

  const removeInvestmentCriteria = (index: number) => {
    const updatedList = investmentCriteria.filter((_, i) => i !== index);
    setInvestmentCriteria(updatedList);
    toast({
      title: "Success",
      description: "Investment criteria removed",
    });
  };

  // Toggle section visibility
  const toggleSection = (sectionName: keyof typeof openSections) => {
    setOpenSections(prev => ({
      ...prev,
      [sectionName]: !prev[sectionName]
    }));
  };

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      setLoading(true);
      setError(null);

      try {
        const url = isEditing
          ? `/api/companies/${companyId}`
          : "/api/companies";

        const method = isEditing ? "PUT" : "POST";

        // Transform form data to match database column names
        const apiData = {
          ...formData,
          // Map form field names to database column names
          industry: formData.industry, // stays the same
          website: formData.website, // form field -> company_website in DB (will be mapped in API)
          main_phone: formData.main_phone, // form field -> company_phone in DB (will be mapped in API) 
          linkedin: formData.linkedin, // form field -> company_linkedin in DB (will be mapped in API)
          headquarters_address: formData.headquarters_address, // form field -> company_address in DB (will be mapped in API)
          headquarters_city: formData.headquarters_city, // form field -> company_city in DB (will be mapped in API)
          headquarters_state: formData.headquarters_state, // form field -> company_state in DB (will be mapped in API)
          headquarters_zipcode: formData.headquarters_zipcode, // form field -> company_zip in DB (will be mapped in API)
          headquarters_country: formData.headquarters_country, // form field -> company_country in DB (will be mapped in API)
          lending_origin_balance_sheet_securitization: formData.lending_origin_balance_sheet_securitization, // form field -> lending_origin in DB (will be mapped in API)
        };

        const response = await fetch(url, {
          method,
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(apiData),
        });

        if (!response.ok) {
          throw new Error(
            `Failed to ${isEditing ? "update" : "create"} company`
          );
        }

        const data = await response.json();
        const savedCompanyId = data.company_id;

        if (onSuccess) {
          onSuccess(savedCompanyId);
        } else {
          router.push(`/dashboard/companies/${savedCompanyId}`);
          router.refresh();
        }
      } catch (error) {
        console.error(
          `Error ${isEditing ? "updating" : "creating"} company:`,
          error
        );
        setError(
          `Failed to ${
            isEditing ? "update" : "create"
          } company. Please try again.`
        );
      } finally {
        setLoading(false);
      }
    },
    [formData, isEditing, companyId, onSuccess, router]
  );

  // Helper function to update phone fields with validation
  const updatePhoneField = useCallback((field: string, value: string) => {
    const formattedValue = formatPhoneNumber(value);
    
    // Validate phone number
    if (formattedValue && !validatePhoneNumber(formattedValue)) {
      setPhoneErrors(prev => ({
        ...prev,
        [field]: 'Phone number can only contain digits, spaces, parentheses, dashes, dots, and plus sign'
      }));
      return;
    }
    
    // Clear error if validation passes
    if (phoneErrors[field as keyof typeof phoneErrors]) {
      setPhoneErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
    
    setFormData(prev => ({
      ...prev,
      [field]: formattedValue
    }));
  }, [phoneErrors]);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;
      
      // Handle phone number fields with validation
      if (name === 'main_phone' || name === 'secondary_phone') {
        updatePhoneField(name, value);
        return;
      }
      
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));

      // Clear URL validation errors when user types
      if (['website', 'linkedin', 'twitter', 'facebook', 'instagram', 'youtube', 'quarterly_earnings_link'].includes(name)) {
        setUrlErrors(prev => ({
          ...prev,
          [name]: undefined
        }));
      }
    },
    [updatePhoneField]
  );

  // Handle URL field blur for validation and normalization
  const handleUrlBlur = useCallback(
    (fieldName: 'website' | 'linkedin' | 'twitter' | 'facebook' | 'instagram' | 'youtube' | 'quarterly_earnings_link') => {
      const value = formData[fieldName];
      if (!value) return;

      if (!isValidUrl(value)) {
        setUrlErrors(prev => ({
          ...prev,
          [fieldName]: 'Please enter a valid URL'
        }));
        return;
      }

      // Normalize the URL
      const normalizedUrl = normalizeUrl(value);
      if (normalizedUrl !== value) {
        setFormData(prev => ({
          ...prev,
          [fieldName]: normalizedUrl
        }));
      }
    },
    [formData]
  );



  const handleNumberChange = useCallback(
    (fieldName: keyof CompanyFormData) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const val = e.target.value ? parseFloat(e.target.value) : undefined;
      setFormData((prev) => ({
        ...prev,
        [fieldName]: val,
      }));
    },
    []
  );

  return (
    <div className="max-w-6xl mx-auto p-8 bg-gradient-to-br from-blue-50 to-indigo-50 min-h-screen">
      <div className="bg-white rounded-xl shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-8 py-6">
          <h1 className="text-3xl font-bold text-white mb-2">
            {isEditing ? "Edit Company" : "Add New Company"}
          </h1>
          <p className="text-blue-100">
            {isEditing ? "Update company information" : "Enter company details to add to the database"}
          </p>
        </div>

        {error && (
          <div className="mx-8 mt-6 p-4 bg-red-50 border-l-4 border-red-400 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-red-800 font-medium">{error}</p>
              </div>
            </div>
        </div>
      )}

        <form onSubmit={handleSubmit} className="p-8 space-y-8">
          {/* Company Information Section */}
          <CollapsibleSection
            title="Company Information"
            description="Basic company details and identification"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            }
            isOpen={openSections.companyInfo}
            onToggle={() => toggleSection('companyInfo')}
            bgColor="bg-blue-100"
            iconColor="text-blue-600"
          >
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="space-y-2">
                <Label htmlFor="company_name" className="text-sm font-medium text-gray-700">
                        Company Name *
                      </Label>
                      <div className="relative">
                      <Input
                        id="company_name"
                        name="company_name"
                        value={formData.company_name}
                          onChange={handleCompanyNameChange}
                          onKeyDown={handleCompanyNameKeyDown}
                                                  placeholder="Enter company name"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 truncate"
                        required
                        />
                        
                        {/* Company Search Results */}
                        {showSuggestions && (
                          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                            {companySuggestions.length > 0 ? (
                              <div className="p-2 space-y-1">
                                {companySuggestions.map((company, index) => (
                                  <div
                                    key={company.company_id}
                                    className={`p-3 rounded-lg cursor-pointer border transition-all duration-200 ${
                                      index === selectedSuggestionIndex 
                                        ? 'bg-emerald-50 border-emerald-300 shadow-sm' 
                                        : 'bg-white border-slate-200 hover:bg-slate-50 hover:border-slate-300'
                                    }`}
                                    onClick={() => handleCompanySelect(company)}
                                  >
                                    <div className="flex items-start justify-between">
                                      <div className="flex-1">
                                        <div className="font-medium text-gray-900 text-sm flex items-center">
                                          {company.company_name}
                                          <button
                                            type="button"
                                            onClick={(e) => handleCompanyLinkClick(e, company.company_id)}
                                            className="ml-2 p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                            title="View company details"
                                          >
                                            <ExternalLink className="h-3 w-3" />
                                          </button>
                                        </div>
                                        {company.industry && (
                                          <div className="text-xs text-gray-500 mt-1">
                                            {company.industry}
                                          </div>
                                        )}
                                        {company.headquarters_city && company.headquarters_state && (
                                          <div className="text-xs text-gray-500">
                                            {company.headquarters_city}, {company.headquarters_state}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : isSearching ? (
                              <div className="p-4 text-center border rounded-xl bg-slate-50">
                                <div className="text-sm text-gray-600">
                                  <Search className="h-4 w-4 mx-auto mb-2 text-gray-400" />
                                  Searching for companies...
                                </div>
                              </div>
                            ) : (
                              <div className="p-4 text-center border rounded-xl bg-slate-50">
                                <div className="text-sm text-gray-600">
                                  <Search className="h-4 w-4 mx-auto mb-2 text-gray-400" />
                                  No companies found matching "{formData.company_name}"
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                  Try a different search term or continue with a new company
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="company_type" className="text-sm font-medium text-gray-700">
                  Company Type
                </Label>
                      <CustomSelect
                        options={formOptions.companyTypes}
                  value={formData.company_type || ""}
                        onValueChange={(value: string) => {
                    setFormData(prev => ({ ...prev, company_type: value }));
                        }}
                  placeholder={isLoadingFormOptions ? "Loading..." : "Select company type"}
                        addNewLabel="Add new company type..."
                        onAddNew={(newValue: string) => {
                    setFormData(prev => ({ ...prev, company_type: newValue }));
                        }}
                        disabled={isLoadingFormOptions}
                      />
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="industry" className="text-sm font-medium text-gray-700">
                        Industry
                      </Label>
                      <Input
                        id="industry"
                        name="industry"
                  value={formData.industry || ""}
                        onChange={handleChange}
                  placeholder="e.g., Technology, Real Estate"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="founded_year" className="text-sm font-medium text-gray-700">
                  Founded Year
                </Label>
                                  <Input
                    id="founded_year"
                    name="founded_year"
                    type="number"
                    value={formData.founded_year || ""}
                    onChange={handleNumberChange("founded_year")}
                    placeholder="e.g., 1995"
                    className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="business_model" className="text-sm font-medium text-gray-700">
                  Business Model
                </Label>
                <Input
                        id="business_model"
                  name="business_model"
                  value={formData.business_model || ""}
                  onChange={handleChange}
                  placeholder="e.g., B2B SaaS, Marketplace"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="number_of_employees" className="text-sm font-medium text-gray-700">
                  Number of Employees
                </Label>
                                 <Input
                   id="number_of_employees"
                   name="number_of_employees"
                   type="number"
                   value={formData.number_of_employees || ""}
                   onChange={handleNumberChange("number_of_employees")}
                   placeholder="e.g., 500"
                   className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
            </div>
          </CollapsibleSection>

          {/* Contact & Social Section */}
          <CollapsibleSection
            title="Contact & Social"
            description="Contact information and social media presence"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            }
            isOpen={openSections.contactSocial}
            onToggle={() => toggleSection('contactSocial')}
            bgColor="bg-green-100"
            iconColor="text-green-600"
          >

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="space-y-2">
                <Label htmlFor="website" className="text-sm font-medium text-gray-700">
                  Website
                      </Label>
                      <div className="relative">
                        <Input
                  id="website"
                  name="website"
                  value={formData.website}
                          onChange={handleChange}
                  onBlur={() => handleUrlBlur('website')}
                  onKeyDown={handleDomainKeyDown}
                  placeholder="https://example.com"
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 truncate pr-12 ${urlErrors.website ? 'border-red-300' : ''}`}
                        />
                        <Button
                          type="button"
                          onClick={handleDomainSearch}
                          disabled={!formData.website || formData.website.length < 2 || isSearchingDomain}
                          className="absolute right-1 top-1 h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700 text-white"
                          title="Search by domain"
                        >
                          {isSearchingDomain ? (
                            <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          ) : (
                            <Search className="h-4 w-4" />
                          )}
                        </Button>
                        
                        {/* Domain Search Results */}
                        {showDomainSuggestions && (
                          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                            {domainSuggestions.length > 0 ? (
                              <div className="p-2 space-y-1">
                                {domainSuggestions.map((company, index) => (
                                  <div
                                    key={company.company_id}
                                    className={`p-3 rounded-lg cursor-pointer border transition-all duration-200 ${
                                      index === selectedDomainSuggestionIndex 
                                        ? 'bg-emerald-50 border-emerald-300 shadow-sm' 
                                        : 'bg-white border-slate-200 hover:bg-slate-50 hover:border-slate-300'
                                    }`}
                                    onClick={() => handleDomainSelect(company)}
                                  >
                                    <div className="flex items-start justify-between">
                                      <div className="flex-1">
                                        <div className="font-medium text-gray-900 text-sm flex items-center">
                                          {company.company_name}
                                          <button
                                            type="button"
                                            onClick={(e) => handleCompanyLinkClick(e, company.company_id)}
                                            className="ml-2 p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                            title="View company details"
                                          >
                                            <ExternalLink className="h-3 w-3" />
                                          </button>
                                        </div>
                                        {company.company_website && (
                                          <div className="text-xs text-gray-500 mt-1">
                                            {company.company_website}
                                          </div>
                                        )}
                                        {company.industry && (
                                          <div className="text-xs text-gray-500">
                                            {company.industry}
                                          </div>
                                        )}
                                        {company.company_city && company.company_state && (
                                          <div className="text-xs text-gray-500">
                                            {company.company_city}, {company.company_state}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : isSearchingDomain ? (
                              <div className="p-4 text-center border rounded-xl bg-slate-50">
                                <div className="text-sm text-gray-600">
                                  <Search className="h-4 w-4 mx-auto mb-2 text-gray-400" />
                                  Searching for companies...
                                </div>
                              </div>
                            ) : (
                              <div className="p-4 text-center border rounded-xl bg-slate-50">
                                <div className="text-sm text-gray-600">
                                  <Search className="h-4 w-4 mx-auto mb-2 text-gray-400" />
                                  No companies found with domain "{formData.website}"
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                  Try a different domain or continue with a new company
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                      {urlErrors.website && (
                  <p className="text-red-600 text-xs">{urlErrors.website}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="main_phone" className="text-sm font-medium text-gray-700">
                  Main Phone
                      </Label>
                      <Input
                  id="main_phone"
                  name="main_phone"
                  value={formData.main_phone}
                        onChange={handleChange}
                  placeholder="+****************"
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 ${phoneErrors.main_phone ? 'border-red-300' : ''}`}
                      />
                      {phoneErrors.main_phone && (
                        <p className="text-red-600 text-xs">{phoneErrors.main_phone}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="main_email" className="text-sm font-medium text-gray-700">
                  Main Email
                      </Label>
                      <Input
                  id="main_email"
                  name="main_email"
                  type="email"
                  value={formData.main_email}
                        onChange={handleChange}
                  placeholder="<EMAIL>"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="linkedin" className="text-sm font-medium text-gray-700">
                  LinkedIn
                </Label>
                                 <Input
                   id="linkedin"
                   name="linkedin"
                   value={formData.linkedin}
                   onChange={handleChange}
                   onBlur={() => handleUrlBlur('linkedin')}
                   placeholder="https://linkedin.com/company/..."
                   className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 truncate ${urlErrors.linkedin ? 'border-red-300' : ''}`}
                      />
                      {urlErrors.linkedin && (
                  <p className="text-red-600 text-xs">{urlErrors.linkedin}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="twitter" className="text-sm font-medium text-gray-700">
                  Twitter
                </Label>
                      <Input
                   id="twitter"
                   name="twitter"
                   value={formData.twitter}
                   onChange={handleChange}
                   onBlur={() => handleUrlBlur('twitter')}
                   placeholder="https://twitter.com/..."
                   className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 truncate ${urlErrors.twitter ? 'border-red-300' : ''}`}
                 />
                {urlErrors.twitter && (
                  <p className="text-red-600 text-xs">{urlErrors.twitter}</p>
                )}
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="facebook" className="text-sm font-medium text-gray-700">
                  Facebook
                      </Label>
                        <Input
                   id="facebook"
                   name="facebook"
                   value={formData.facebook}
                   onChange={handleChange}
                   onBlur={() => handleUrlBlur('facebook')}
                   placeholder="https://facebook.com/..."
                   className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 truncate ${urlErrors.facebook ? 'border-red-300' : ''}`}
                 />
                {urlErrors.facebook && (
                  <p className="text-red-600 text-xs">{urlErrors.facebook}</p>
                )}
                      </div>
                    </div>
          </CollapsibleSection>

          {/* Location Section */}
          <CollapsibleSection
            title="Location"
            description="Company headquarters and office locations"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            }
            isOpen={openSections.location}
            onToggle={() => toggleSection('location')}
            bgColor="bg-purple-100"
            iconColor="text-purple-600"
          >

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="headquarters_address" className="text-sm font-medium text-gray-700">
                  Headquarters Address
                      </Label>
                      <Input
                  id="headquarters_address"
                  name="headquarters_address"
                  value={formData.headquarters_address}
                        onChange={handleChange}
                  placeholder="123 Main Street"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 truncate"
                      />
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="headquarters_city" className="text-sm font-medium text-gray-700">
                        City
                      </Label>
                      <Input
                  id="headquarters_city"
                  name="headquarters_city"
                  value={formData.headquarters_city}
                        onChange={handleChange}
                  placeholder="San Francisco"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="headquarters_state" className="text-sm font-medium text-gray-700">
                  State
                      </Label>
                      <Input
                  id="headquarters_state"
                  name="headquarters_state"
                  value={formData.headquarters_state}
                        onChange={handleChange}
                  placeholder="CA"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="headquarters_zipcode" className="text-sm font-medium text-gray-700">
                  ZIP Code
                      </Label>
                      <Input
                  id="headquarters_zipcode"
                  name="headquarters_zipcode"
                  value={formData.headquarters_zipcode}
                        onChange={handleChange}
                  placeholder="94105"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="headquarters_country" className="text-sm font-medium text-gray-700">
                        Country
                      </Label>
                      <Input
                  id="headquarters_country"
                  name="headquarters_country"
                  value={formData.headquarters_country}
                        onChange={handleChange}
                  placeholder="United States"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
          </CollapsibleSection>

          {/* Financial Section */}
          <CollapsibleSection
            title="Financial Information"
            description="Financial metrics and performance data"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            }
            isOpen={openSections.financial}
            onToggle={() => toggleSection('financial')}
            bgColor="bg-yellow-100"
            iconColor="text-yellow-600"
          >
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="space-y-2">
                <Label htmlFor="fund_size" className="text-sm font-medium text-gray-700">
                  Fund Size
                </Label>
                      <Input
                        id="fund_size"
                   name="fund_size"
                   type="number"
                   value={formData.fund_size || ''}
                   onChange={handleNumberChange("fund_size")}
                   placeholder="e.g., **********"
                   className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="aum" className="text-sm font-medium text-gray-700">
                  AUM
                </Label>
                      <Input
                        id="aum"
                  name="aum"
                  type="number"
                  value={formData.aum || ''}
                  onChange={handleNumberChange("aum")}
                  placeholder="e.g., 5000000000"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="annual_revenue" className="text-sm font-medium text-gray-700">
                  Annual Revenue
                </Label>
                      <Input
                  id="annual_revenue"
                  name="annual_revenue"
                  type="number"
                  value={formData.annual_revenue || ''}
                  onChange={handleNumberChange("annual_revenue")}
                  placeholder="e.g., 100000000"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="market_capitalization" className="text-sm font-medium text-gray-700">
                  Market Cap
                </Label>
                      <Input
                  id="market_capitalization"
                  name="market_capitalization"
                        type="number"
                  value={formData.market_capitalization || ''}
                  onChange={handleNumberChange("market_capitalization")}
                  placeholder="e.g., **********0"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="credit_rating" className="text-sm font-medium text-gray-700">
                  Credit Rating
                </Label>
                <CustomSelect
                  options={formOptions.creditRatings}
                  value={formData.credit_rating || ""}
                  onValueChange={(value: string) => {
                    setFormData(prev => ({ ...prev, credit_rating: value }));
                  }}
                  placeholder={isLoadingFormOptions ? "Loading..." : "Select credit rating"}
                  addNewLabel="Add new credit rating..."
                  onAddNew={(newValue: string) => {
                    setFormData(prev => ({ ...prev, credit_rating: newValue }));
                  }}
                  disabled={isLoadingFormOptions}
                      />
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="stock_ticker_symbol" className="text-sm font-medium text-gray-700">
                  Stock Ticker
                </Label>
                <Input
                  id="stock_ticker_symbol"
                  name="stock_ticker_symbol"
                  value={formData.stock_ticker_symbol}
                  onChange={handleChange}
                  placeholder="e.g., AAPL"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
          </CollapsibleSection>

          {/* Investment Section */}
          <CollapsibleSection
            title="Investment Details"
            description="Investment strategy and portfolio information"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            }
            isOpen={openSections.investment}
            onToggle={() => toggleSection('investment')}
            bgColor="bg-indigo-100"
            iconColor="text-indigo-600"
          >
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="investment_vehicle_type" className="text-sm font-medium text-gray-700">
                  Investment Vehicle Type
                </Label>
                <CustomSelect
                  options={formOptions.investmentVehicleTypes}
                  value={formData.investment_vehicle_type || ""}
                  onValueChange={(value: string) => {
                    setFormData(prev => ({ ...prev, investment_vehicle_type: value }));
                  }}
                  placeholder={isLoadingFormOptions ? "Loading..." : "Select vehicle type"}
                  addNewLabel="Add new vehicle type..."
                  onAddNew={(newValue: string) => {
                    setFormData(prev => ({ ...prev, investment_vehicle_type: newValue }));
                  }}
                  disabled={isLoadingFormOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lender_type" className="text-sm font-medium text-gray-700">
                  Lender Type
                </Label>
                <CustomSelect
                  options={formOptions.lenderTypes}
                  value={formData.lender_type || ""}
                  onValueChange={(value: string) => {
                    setFormData(prev => ({ ...prev, lender_type: value }));
                  }}
                  placeholder={isLoadingFormOptions ? "Loading..." : "Select lender type"}
                  addNewLabel="Add new lender type..."
                  onAddNew={(newValue: string) => {
                    setFormData(prev => ({ ...prev, lender_type: newValue }));
                  }}
                  disabled={isLoadingFormOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="fundraising_status" className="text-sm font-medium text-gray-700">
                  Fundraising Status
                </Label>
                <CustomSelect
                  options={formOptions.fundingStatuses}
                  value={formData.fundraising_status || ""}
                  onValueChange={(value: string) => {
                    setFormData(prev => ({ ...prev, fundraising_status: value }));
                  }}
                  placeholder={isLoadingFormOptions ? "Loading..." : "Select status"}
                  addNewLabel="Add new funding status..."
                  onAddNew={(newValue: string) => {
                    setFormData(prev => ({ ...prev, fundraising_status: newValue }));
                  }}
                  disabled={isLoadingFormOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="number_of_properties" className="text-sm font-medium text-gray-700">
                  Number of Properties
                </Label>
                      <Input
                        id="number_of_properties"
                  name="number_of_properties"
                        type="number"
                  value={formData.number_of_properties || ''}
                  onChange={handleNumberChange("number_of_properties")}
                  placeholder="e.g., 50"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="average_deal_size" className="text-sm font-medium text-gray-700">
                  Average Deal Size
                </Label>
                      <Input
                  id="average_deal_size"
                  name="average_deal_size"
                  type="number"
                  value={formData.average_deal_size || ''}
                  onChange={handleNumberChange("average_deal_size")}
                  placeholder="e.g., 50000000"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="portfolio_size_sqft" className="text-sm font-medium text-gray-700">
                  Portfolio Size (sq ft)
                </Label>
                <Input
                  id="portfolio_size_sqft"
                  name="portfolio_size_sqft"
                  type="number"
                  value={formData.portfolio_size_sqft || ''}
                  onChange={handleNumberChange("portfolio_size_sqft")}
                  placeholder="e.g., 1000000"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="investment_focus" className="text-sm font-medium text-gray-700">
                  Investment Focus
                </Label>
                <ArrayInput
                  value={formData.investment_focus || []}
                  onChange={(value) => setFormData(prev => ({ ...prev, investment_focus: value }))}
                  placeholder="e.g., Multifamily, Office, Retail, Industrial"
                  label="Investment Focus"
                />
              </div>
            </div>
          </CollapsibleSection>

          {/* Partnership & Leadership Section */}
          <CollapsibleSection
            title="Partnership & Leadership"
            description="Strategic partnerships and key personnel"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            }
            isOpen={openSections.partnership}
            onToggle={() => toggleSection('partnership')}
            bgColor="bg-purple-100"
            iconColor="text-purple-600"
          >
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="partnerships" className="text-sm font-medium text-gray-700">
                  Partnerships
                </Label>
                <ArrayInput
                  value={formData.partnerships || []}
                  onChange={(value) => setFormData(prev => ({ ...prev, partnerships: value }))}
                  placeholder="e.g., Joint Venture Partner A, Strategic Alliance B"
                  label="Partnerships"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="key_equity_partners" className="text-sm font-medium text-gray-700">
                  Key Equity Partners
                </Label>
                <ArrayInput
                  value={formData.key_equity_partners || []}
                  onChange={(value) => setFormData(prev => ({ ...prev, key_equity_partners: value }))}
                  placeholder="e.g., Equity Partner A, Equity Partner B"
                  label="Key Equity Partners"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="key_debt_partners" className="text-sm font-medium text-gray-700">
                  Key Debt Partners
                </Label>
                <ArrayInput
                  value={formData.key_debt_partners || []}
                  onChange={(value) => setFormData(prev => ({ ...prev, key_debt_partners: value }))}
                  placeholder="e.g., Debt Partner A, Debt Partner B"
                  label="Key Debt Partners"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="board_of_directors" className="text-sm font-medium text-gray-700">
                  Board of Directors
                </Label>
                <ArrayInput
                  value={formData.board_of_directors || []}
                  onChange={(value) => setFormData(prev => ({ ...prev, board_of_directors: value }))}
                  placeholder="e.g., John Doe, Jane Smith, Bob Johnson"
                  label="Board of Directors"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="key_executives" className="text-sm font-medium text-gray-700">
                  Key Executives
                </Label>
                <ArrayInput
                  value={formData.key_executives || []}
                  onChange={(value) => setFormData(prev => ({ ...prev, key_executives: value }))}
                  placeholder="e.g., CEO John Doe, CFO Jane Smith"
                  label="Key Executives"
                />
              </div>
            </div>
          </CollapsibleSection>

          {/* Additional Information Section */}
          <CollapsibleSection
            title="Additional Information"
            description="Strategic details and descriptions"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
            }
            isOpen={openSections.additional}
            onToggle={() => toggleSection('additional')}
            bgColor="bg-pink-100"
            iconColor="text-pink-600"
          >
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="investment_strategy_mission" className="text-sm font-medium text-gray-700">
                  Investment Strategy Mission
                </Label>
                <textarea
                  id="investment_strategy_mission"
                  name="investment_strategy_mission"
                  value={formData.investment_strategy_mission}
                  onChange={handleChange}
                  placeholder="Describe the company's investment strategy and mission..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="products_services_description" className="text-sm font-medium text-gray-700">
                  Products/Services Description
                </Label>
                <textarea
                  id="products_services_description"
                  name="products_services_description"
                  value={formData.products_services_description}
                  onChange={handleChange}
                  placeholder="Describe the company's products or services..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="target_customer_profile" className="text-sm font-medium text-gray-700">
                  Target Customer Profile
                </Label>
                <textarea
                  id="target_customer_profile"
                  name="target_customer_profile"
                  value={formData.target_customer_profile}
                  onChange={handleChange}
                  placeholder="Describe the target customer profile..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="unique_selling_proposition" className="text-sm font-medium text-gray-700">
                  Unique Selling Proposition
                </Label>
                <textarea
                  id="unique_selling_proposition"
                  name="unique_selling_proposition"
                  value={formData.unique_selling_proposition}
                  onChange={handleChange}
                  placeholder="What makes this company unique..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="founder_background" className="text-sm font-medium text-gray-700">
                  Founder Background
                </Label>
                <textarea
                  id="founder_background"
                  name="founder_background"
                  value={formData.founder_background}
                  onChange={handleChange}
                  placeholder="Background information about the founders..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

                            <div className="space-y-2">
                <Label htmlFor="company_history" className="text-sm font-medium text-gray-700">
                  Company History
                </Label>
                <textarea
                  id="company_history"
                  name="company_history"
                  value={formData.company_history}
                  onChange={handleChange}
                  placeholder="Historical background and milestones..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="major_competitors" className="text-sm font-medium text-gray-700">
                  Major Competitors
                </Label>
                <ArrayInput
                  value={formData.major_competitors || []}
                  onChange={(value) => setFormData(prev => ({ ...prev, major_competitors: value }))}
                  placeholder="e.g., Competitor A, Competitor B, Competitor C"
                  label="Major Competitors"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="industry_awards_recognitions" className="text-sm font-medium text-gray-700">
                  Industry Awards & Recognitions
                </Label>
                <ArrayInput
                  value={formData.industry_awards_recognitions || []}
                  onChange={(value) => setFormData(prev => ({ ...prev, industry_awards_recognitions: value }))}
                  placeholder="e.g., Best Company 2023, Industry Excellence Award"
                  label="Industry Awards & Recognitions"
                />
              </div>
            </div>
          </CollapsibleSection>

          {/* Leadership & Structure Section */}
          <CollapsibleSection
            title="Leadership & Structure"
            description="Corporate structure and key personnel"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            }
            isOpen={openSections.leadership}
            onToggle={() => toggleSection('leadership')}
            bgColor="bg-orange-100"
            iconColor="text-orange-600"
          >
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="corporate_structure" className="text-sm font-medium text-gray-700">
                  Corporate Structure
                </Label>
                <CustomSelect
                  options={formOptions.corporateStructures}
                  value={formData.corporate_structure || ""}
                  onValueChange={(value: string) => {
                    setFormData(prev => ({ ...prev, corporate_structure: value }));
                  }}
                  placeholder={isLoadingFormOptions ? "Loading..." : "Select structure"}
                  addNewLabel="Add new corporate structure..."
                  onAddNew={(newValue: string) => {
                    setFormData(prev => ({ ...prev, corporate_structure: newValue }));
                  }}
                  disabled={isLoadingFormOptions}
                />
                    </div>

              <div className="space-y-2">
                <Label htmlFor="parent_company" className="text-sm font-medium text-gray-700">
                  Parent Company
                </Label>
                      <Input
                  id="parent_company"
                  name="parent_company"
                  value={formData.parent_company}
                  onChange={handleChange}
                  placeholder="Parent company name"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

              <div className="space-y-2">
                <Label htmlFor="active_fund_name_series" className="text-sm font-medium text-gray-700">
                  Active Fund Name/Series
                </Label>
                <Input
                  id="active_fund_name_series"
                  name="active_fund_name_series"
                  value={formData.active_fund_name_series}
                  onChange={handleChange}
                  placeholder="e.g., Fund III, Series A"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
                  </div>

              <div className="space-y-2">
                <Label htmlFor="fund_size_active_fund" className="text-sm font-medium text-gray-700">
                  Active Fund Size
                </Label>
                <Input
                  id="fund_size_active_fund"
                  name="fund_size_active_fund"
                  type="number"
                  value={formData.fund_size_active_fund || ''}
                  onChange={handleNumberChange("fund_size_active_fund")}
                  placeholder="e.g., 500000000"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
                    </div>

              <div className="space-y-2">
                <Label htmlFor="internal_relationship_manager" className="text-sm font-medium text-gray-700">
                  Internal Relationship Manager
                </Label>
                      <Input
                  id="internal_relationship_manager"
                  name="internal_relationship_manager"
                  value={formData.internal_relationship_manager}
                  onChange={handleChange}
                  placeholder="Relationship manager name"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="last_contact_date" className="text-sm font-medium text-gray-700">
                  Last Contact Date
                </Label>
                <Input
                  id="last_contact_date"
                  name="last_contact_date"
                  type="date"
                  value={formData.last_contact_date}
                  onChange={handleChange}
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
          </CollapsibleSection>

          {/* Public Company Data Section */}
          <CollapsibleSection
            title="Public Company Data"
            description="Public company financial and market information"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            }
            isOpen={openSections.publicCompany}
            onToggle={() => toggleSection('publicCompany')}
            bgColor="bg-teal-100"
            iconColor="text-teal-600"
          >
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="space-y-2">
                <Label htmlFor="stock_exchange" className="text-sm font-medium text-gray-700">
                  Stock Exchange
                </Label>
                <CustomSelect
                  options={formOptions.stockExchanges}
                  value={formData.stock_exchange || ""}
                  onValueChange={(value: string) => {
                    setFormData(prev => ({ ...prev, stock_exchange: value }));
                  }}
                  placeholder={isLoadingFormOptions ? "Loading..." : "Select exchange"}
                  addNewLabel="Add new stock exchange..."
                  onAddNew={(newValue: string) => {
                    setFormData(prev => ({ ...prev, stock_exchange: newValue }));
                  }}
                  disabled={isLoadingFormOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="net_income" className="text-sm font-medium text-gray-700">
                  Net Income
                </Label>
                <Input
                  id="net_income"
                  name="net_income"
                  type="number"
                  value={formData.net_income || ''}
                  onChange={handleNumberChange("net_income")}
                  placeholder="e.g., 50000000"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="ebitda" className="text-sm font-medium text-gray-700">
                  EBITDA
                </Label>
                <Input
                  id="ebitda"
                  name="ebitda"
                  type="number"
                  value={formData.ebitda || ''}
                  onChange={handleNumberChange("ebitda")}
                  placeholder="e.g., 75000000"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="profit_margin" className="text-sm font-medium text-gray-700">
                  Profit Margin (%)
                </Label>
                <Input
                  id="profit_margin"
                  name="profit_margin"
                  type="number"
                  step="0.01"
                  value={formData.profit_margin || ''}
                  onChange={handleNumberChange("profit_margin")}
                  placeholder="e.g., 15.5"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="market_share_percentage" className="text-sm font-medium text-gray-700">
                  Market Share (%)
                </Label>
                <Input
                  id="market_share_percentage"
                  name="market_share_percentage"
                  type="number"
                  step="0.01"
                  value={formData.market_share_percentage || ''}
                  onChange={handleNumberChange("market_share_percentage")}
                  placeholder="e.g., 12.5"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="quarterly_earnings_link" className="text-sm font-medium text-gray-700">
                  Quarterly Earnings Link
                </Label>
                <Input
                  id="quarterly_earnings_link"
                  name="quarterly_earnings_link"
                  value={formData.quarterly_earnings_link}
                  onChange={handleChange}
                  onBlur={() => handleUrlBlur('quarterly_earnings_link')}
                  placeholder="https://investor.website.com/earnings"
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 ${urlErrors.quarterly_earnings_link ? 'border-red-300' : ''}`}
                />
                {urlErrors.quarterly_earnings_link && (
                  <p className="text-red-600 text-xs">{urlErrors.quarterly_earnings_link}</p>
                      )}
                    </div>
            </div>
          </CollapsibleSection>

          {/* Relationship & Pipeline Section */}
          <CollapsibleSection
            title="Relationship & Pipeline"
            description="Deal pipeline and relationship management"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            }
            isOpen={openSections.relationship}
            onToggle={() => toggleSection('relationship')}
            bgColor="bg-red-100"
            iconColor="text-red-600"
          >
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="pipeline_status" className="text-sm font-medium text-gray-700">
                  Pipeline Status
                </Label>
                      <CustomSelect
                  options={formOptions.pipelineStatuses}
                  value={formData.pipeline_status || ""}
                        onValueChange={(value: string) => {
                    setFormData(prev => ({ ...prev, pipeline_status: value }));
                  }}
                  placeholder={isLoadingFormOptions ? "Loading..." : "Select status"}
                  addNewLabel="Add new pipeline status..."
                  onAddNew={(newValue: string) => {
                    setFormData(prev => ({ ...prev, pipeline_status: newValue }));
                  }}
                  disabled={isLoadingFormOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="role_in_previous_deal" className="text-sm font-medium text-gray-700">
                  Role in Previous Deal
                </Label>
                <CustomSelect
                  options={formOptions.roleInPreviousDeals}
                  value={formData.role_in_previous_deal || ""}
                  onValueChange={(value: string) => {
                    setFormData(prev => ({ ...prev, role_in_previous_deal: value }));
                  }}
                  placeholder={isLoadingFormOptions ? "Loading..." : "Select role"}
                  addNewLabel="Add new role..."
                  onAddNew={(newValue: string) => {
                    setFormData(prev => ({ ...prev, role_in_previous_deal: newValue }));
                  }}
                  disabled={isLoadingFormOptions}
                />
                  </div>

                  <div className="space-y-2">
                <Label htmlFor="recent_news_sentiment" className="text-sm font-medium text-gray-700">
                  Recent News Sentiment
                </Label>
                <CustomSelect
                  options={formOptions.recentNewsSentiments}
                  value={formData.recent_news_sentiment || ""}
                  onValueChange={(value: string) => {
                    setFormData(prev => ({ ...prev, recent_news_sentiment: value }));
                  }}
                  placeholder={isLoadingFormOptions ? "Loading..." : "Select sentiment"}
                  addNewLabel="Add new sentiment..."
                  onAddNew={(newValue: string) => {
                    setFormData(prev => ({ ...prev, recent_news_sentiment: newValue }));
                  }}
                  disabled={isLoadingFormOptions}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="transactions_completed_last_12m" className="text-sm font-medium text-gray-700">
                  Transactions (Last 12M)
                </Label>
                <Input
                  id="transactions_completed_last_12m"
                  name="transactions_completed_last_12m"
                  type="number"
                  value={formData.transactions_completed_last_12m || ''}
                  onChange={handleNumberChange("transactions_completed_last_12m")}
                  placeholder="e.g., 25"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="total_transaction_volume_ytd" className="text-sm font-medium text-gray-700">
                  Transaction Volume YTD
                </Label>
                <Input
                  id="total_transaction_volume_ytd"
                  name="total_transaction_volume_ytd"
                  type="number"
                  value={formData.total_transaction_volume_ytd || ''}
                  onChange={handleNumberChange("total_transaction_volume_ytd")}
                  placeholder="e.g., **********"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="deal_count_ytd" className="text-sm font-medium text-gray-700">
                  Deal Count YTD
                </Label>
                <Input
                  id="deal_count_ytd"
                  name="deal_count_ytd"
                  type="number"
                  value={formData.deal_count_ytd || ''}
                  onChange={handleNumberChange("deal_count_ytd")}
                  placeholder="e.g., 15"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="portfolio_asset_count" className="text-sm font-medium text-gray-700">
                  Portfolio Asset Count
                </Label>
                <Input
                  id="portfolio_asset_count"
                  name="portfolio_asset_count"
                  type="number"
                  value={formData.portfolio_asset_count || ''}
                  onChange={handleNumberChange("portfolio_asset_count")}
                  placeholder="e.g., 100"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="dry_powder" className="text-sm font-medium text-gray-700">
                  Dry Powder
                </Label>
                <Input
                  id="dry_powder"
                  name="dry_powder"
                  type="number"
                  value={formData.dry_powder || ''}
                  onChange={handleNumberChange("dry_powder")}
                  placeholder="e.g., 200000000"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="annual_deployment_target" className="text-sm font-medium text-gray-700">
                  Annual Deployment Target
                </Label>
                <Input
                  id="annual_deployment_target"
                  name="annual_deployment_target"
                  type="number"
                  value={formData.annual_deployment_target || ''}
                  onChange={handleNumberChange("annual_deployment_target")}
                  placeholder="e.g., 500000000"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
          </CollapsibleSection>

          {/* Additional Contact & Social Section */}
          <CollapsibleSection
            title="Additional Contact & Social"
            description="Secondary contact information and social media"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            }
            isOpen={openSections.additionalContact}
            onToggle={() => toggleSection('additionalContact')}
            bgColor="bg-cyan-100"
            iconColor="text-cyan-600"
          >
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="secondary_phone" className="text-sm font-medium text-gray-700">
                  Secondary Phone
                </Label>
                <Input
                  id="secondary_phone"
                  name="secondary_phone"
                  value={formData.secondary_phone}
                  onChange={handleChange}
                  placeholder="+****************"
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 ${phoneErrors.secondary_phone ? 'border-red-300' : ''}`}
                />
                {phoneErrors.secondary_phone && (
                  <p className="text-red-600 text-xs">{phoneErrors.secondary_phone}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="secondary_email" className="text-sm font-medium text-gray-700">
                  Secondary Email
                </Label>
                <Input
                  id="secondary_email"
                  name="secondary_email"
                  type="email"
                  value={formData.secondary_email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="instagram" className="text-sm font-medium text-gray-700">
                  Instagram
                </Label>
                <Input
                  id="instagram"
                  name="instagram"
                  value={formData.instagram}
                  onChange={handleChange}
                  onBlur={() => handleUrlBlur('instagram')}
                  placeholder="https://instagram.com/..."
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 ${urlErrors.instagram ? 'border-red-300' : ''}`}
                />
                {urlErrors.instagram && (
                  <p className="text-red-600 text-xs">{urlErrors.instagram}</p>
                      )}
                    </div>

              <div className="space-y-2">
                <Label htmlFor="youtube" className="text-sm font-medium text-gray-700">
                  YouTube
                </Label>
                <Input
                  id="youtube"
                  name="youtube"
                  value={formData.youtube}
                  onChange={handleChange}
                  onBlur={() => handleUrlBlur('youtube')}
                  placeholder="https://youtube.com/..."
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 ${urlErrors.youtube ? 'border-red-300' : ''}`}
                />
                {urlErrors.youtube && (
                  <p className="text-red-600 text-xs">{urlErrors.youtube}</p>
                )}
                    </div>

              <div className="space-y-2">
                <Label htmlFor="additional_address" className="text-sm font-medium text-gray-700">
                  Additional Address
                </Label>
                <Input
                  id="additional_address"
                  name="additional_address"
                  value={formData.additional_address}
                  onChange={handleChange}
                  placeholder="Secondary office address"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
                  </div>

              <div className="space-y-2">
                <Label htmlFor="additional_city" className="text-sm font-medium text-gray-700">
                  Additional City
                </Label>
                <Input
                  id="additional_city"
                  name="additional_city"
                  value={formData.additional_city}
                  onChange={handleChange}
                  placeholder="Secondary office city"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
                </div>

                  <div className="space-y-2">
                <Label htmlFor="additional_state" className="text-sm font-medium text-gray-700">
                  Additional State
                </Label>
                    <Input
                  id="additional_state"
                  name="additional_state"
                  value={formData.additional_state}
                  onChange={handleChange}
                  placeholder="Secondary office state"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>

                  <div className="space-y-2">
                <Label htmlFor="additional_zipcode" className="text-sm font-medium text-gray-700">
                  Additional ZIP Code
                </Label>
                    <Input
                  id="additional_zipcode"
                  name="additional_zipcode"
                  value={formData.additional_zipcode}
                  onChange={handleChange}
                  placeholder="Secondary office ZIP"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>

                  <div className="space-y-2">
                <Label htmlFor="additional_country" className="text-sm font-medium text-gray-700">
                  Additional Country
                </Label>
                    <Input
                  id="additional_country"
                  name="additional_country"
                  value={formData.additional_country}
                  onChange={handleChange}
                  placeholder="Secondary office country"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
          </CollapsibleSection>

          {/* Market Positioning & Strategy Section */}
          <CollapsibleSection
            title="Market Positioning & Strategy"
            description="Strategic positioning and market approach"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            }
            isOpen={openSections.marketPositioning}
            onToggle={() => toggleSection('marketPositioning')}
            bgColor="bg-emerald-100"
            iconColor="text-emerald-600"
          >
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="space-y-2">
                <Label htmlFor="market_cycle_positioning" className="text-sm font-medium text-gray-700">
                  Market Cycle Positioning
                </Label>
                    <Input
                  id="market_cycle_positioning"
                  name="market_cycle_positioning"
                  value={formData.market_cycle_positioning}
                  onChange={handleChange}
                  placeholder="e.g., Growth, Mature, Decline"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>

                  <div className="space-y-2">
                <Label htmlFor="urban_vs_suburban_preference" className="text-sm font-medium text-gray-700">
                  Urban vs Suburban Preference
                </Label>
                    <Input
                  id="urban_vs_suburban_preference"
                  name="urban_vs_suburban_preference"
                  value={formData.urban_vs_suburban_preference}
                  onChange={handleChange}
                  placeholder="e.g., Urban, Suburban, Mixed"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>

                  <div className="space-y-2">
                <Label htmlFor="lending_origin_balance_sheet_securitization" className="text-sm font-medium text-gray-700">
                  Lending Origin
                </Label>
                    <Input
                  id="lending_origin_balance_sheet_securitization"
                  name="lending_origin_balance_sheet_securitization"
                  value={formData.lending_origin_balance_sheet_securitization}
                  onChange={handleChange}
                  placeholder="e.g., Balance Sheet, Securitization"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>

                  <div className="space-y-2">
                <Label htmlFor="portfolio_health" className="text-sm font-medium text-gray-700">
                  Portfolio Health
                </Label>
                    <Input
                  id="portfolio_health"
                  name="portfolio_health"
                  value={formData.portfolio_health}
                  onChange={handleChange}
                  placeholder="e.g., Excellent, Good, Fair"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>

                  <div className="space-y-2">
                <Label htmlFor="annual_loan_volume" className="text-sm font-medium text-gray-700">
                  Annual Loan Volume
                </Label>
                    <Input
                  id="annual_loan_volume"
                  name="annual_loan_volume"
                  type="number"
                  value={formData.annual_loan_volume || ''}
                  onChange={handleNumberChange("annual_loan_volume")}
                  placeholder="e.g., **********"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>

              <div className="space-y-2">
                <Label htmlFor="number_of_offices" className="text-sm font-medium text-gray-700">
                  Number of Offices
                </Label>
                <Input
                  id="number_of_offices"
                  name="number_of_offices"
                  type="number"
                  value={formData.number_of_offices || ''}
                  onChange={handleNumberChange("number_of_offices")}
                  placeholder="e.g., 10"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
                </div>
              </div>

            {/* Checkbox fields for boolean values */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="flex items-center space-x-2">
                <input
                  id="sustainability_esg_focus"
                  name="sustainability_esg_focus"
                  type="checkbox"
                  checked={formData.sustainability_esg_focus || false}
                  onChange={(e) => setFormData(prev => ({ ...prev, sustainability_esg_focus: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <Label htmlFor="sustainability_esg_focus" className="text-sm font-medium text-gray-700">
                  Sustainability/ESG Focus
                </Label>
          </div>

              <div className="flex items-center space-x-2">
                <input
                  id="technology_proptech_adoption"
                  name="technology_proptech_adoption"
                  type="checkbox"
                  checked={formData.technology_proptech_adoption || false}
                  onChange={(e) => setFormData(prev => ({ ...prev, technology_proptech_adoption: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <Label htmlFor="technology_proptech_adoption" className="text-sm font-medium text-gray-700">
                  Technology/PropTech Adoption
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  id="adaptive_reuse_experience"
                  name="adaptive_reuse_experience"
                  type="checkbox"
                  checked={formData.adaptive_reuse_experience || false}
                  onChange={(e) => setFormData(prev => ({ ...prev, adaptive_reuse_experience: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <Label htmlFor="adaptive_reuse_experience" className="text-sm font-medium text-gray-700">
                  Adaptive Reuse Experience
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  id="regulatory_zoning_expertise"
                  name="regulatory_zoning_expertise"
                  type="checkbox"
                  checked={formData.regulatory_zoning_expertise || false}
                  onChange={(e) => setFormData(prev => ({ ...prev, regulatory_zoning_expertise: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <Label htmlFor="regulatory_zoning_expertise" className="text-sm font-medium text-gray-700">
                  Regulatory/Zoning Expertise
                </Label>
              </div>
            </div>
          </CollapsibleSection>

          {/* Metadata Section */}
          <CollapsibleSection
            title="Metadata"
            description="Data source and confidence information"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            }
            isOpen={openSections.metadata}
            onToggle={() => toggleSection('metadata')}
            bgColor="bg-gray-100"
            iconColor="text-gray-600"
          >
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="data_source" className="text-sm font-medium text-gray-700">
                  Data Source
                </Label>
                <Input
                  id="data_source"
                  name="data_source"
                  value={formData.data_source}
                  onChange={handleChange}
                  placeholder="e.g., Manual Entry, API, Web Scraping"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="data_confidence_score" className="text-sm font-medium text-gray-700">
                  Data Confidence Score
                </Label>
                <Input
                  id="data_confidence_score"
                  name="data_confidence_score"
                  type="number"
                  step="0.01"
                  min="0"
                  max="1"
                  value={formData.data_confidence_score || ''}
                  onChange={handleNumberChange("data_confidence_score")}
                  placeholder="e.g., 0.95"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="last_updated_timestamp" className="text-sm font-medium text-gray-700">
                  Last Updated
                </Label>
                <Input
                  id="last_updated_timestamp"
                  name="last_updated_timestamp"
                  type="datetime-local"
                  value={formData.last_updated_timestamp}
                  onChange={handleChange}
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
          </CollapsibleSection>

          {/* Investment Criteria Section */}
          <CollapsibleSection
            title="Investment Criteria"
            description="Define investment criteria for the company"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            }
            isOpen={openSections.investmentCriteria}
            onToggle={() => toggleSection('investmentCriteria')}
            bgColor="bg-indigo-100"
            iconColor="text-indigo-600"
          >
            <div className="space-y-6">
              {/* Existing Investment Criteria List */}
              {investmentCriteria.length > 0 && (
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-slate-700">Added Investment Criteria:</h4>
                  <div className="space-y-2">
                    {investmentCriteria.map((criteria, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg border border-slate-200">
                        <div className="flex items-center gap-3">
                          <span className="text-sm font-medium text-slate-700">
                            {criteria.capital_position || 'Investment Criteria'}
                          </span>
                          <span className="text-xs text-slate-500">
                            {criteria.minimum_deal_size && criteria.maximum_deal_size 
                              ? `$${(criteria.minimum_deal_size / 1000000).toFixed(1)}M - $${(criteria.maximum_deal_size / 1000000).toFixed(1)}M`
                              : criteria.minimum_deal_size 
                                ? `$${(criteria.minimum_deal_size / 1000000).toFixed(1)}M+`
                                : criteria.maximum_deal_size 
                                  ? `Up to $${(criteria.maximum_deal_size / 1000000).toFixed(1)}M`
                                  : 'Deal size not specified'
                            }
                          </span>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeInvestmentCriteria(index)}
                          className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Add Investment Criteria Form */}
              {showAddICForm ? (
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-xl shadow-lg">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="text-lg font-semibold text-blue-900">Add Investment Criteria</h4>
                      <p className="text-sm text-blue-700">
                        Define investment criteria for this company
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCancelICForm}
                      className="h-8 w-8 p-0 text-blue-400 hover:text-blue-600"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <InvestmentCriteriaForm
                    onSave={handleAddInvestmentCriteria}
                    onCancel={handleCancelICForm}
                    isEditing={false}
                    isSaving={isSavingIC}
                    showCompanySelection={false}
                  />
                </div>
              ) : (
                <Button
                  type="button"
                  onClick={() => setShowAddICForm(true)}
                  className="w-full py-3 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-md transition-all duration-200"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Investment Criteria
                </Button>
              )}
            </div>
          </CollapsibleSection>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
              onClick={() => router.back()}
              className="px-6 py-2"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading}
                 className="px-6 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium"
          >
            {loading ? (
                <div className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {isEditing ? 'Updating...' : 'Creating...'}
                </div>
              ) : (
                <div className="flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  {isEditing ? 'Update Company' : 'Create Company'}
                </div>
            )}
          </Button>
        </div>
      </form>
      </div>
    </div>
  );
}
