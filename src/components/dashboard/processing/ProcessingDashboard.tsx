"use client"

import React, { use<PERSON><PERSON>, use<PERSON>ffe<PERSON> } from 'react'
import { <PERSON>, CardHeader, CardTitle, CardContent } from '../../ui/card'
import { Button } from '../../ui/button'
import { Badge } from '../../ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../../ui/tabs'
import { Input } from '../../ui/input'
import { Label } from '../../ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select'
import { ReactMultiSelect } from '../../ui/react-multi-select'
import { 
  Play, 
  Pause, 
  Building, 
  Mail, 
  Search, 
  Sparkles,
  Target, 
  PenTool,
  CheckCircle2,
  Clock,
  Activity,
  RefreshCw,
  Filter,
  X,
  Upload,
  AlertTriangle,
  Users,
  TrendingUp,
  AlertCircle,
  Download,
  Globe,
  FileText,
  Inbox,
  Send,
  RotateCcw,
  List,
  Zap,
  CheckCircle,
  XC<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>rash2,
  <PERSON><PERSON><PERSON>,
  <PERSON>r,
  Loader2
} from 'lucide-react'
import { ProcessingStage, StageStats} from '../../../types/processing'

interface ProcessingJob {
  id: string
  stage: ProcessingStage
  processor: string
  schedule: string
  enabled: boolean
  lastRun?: Date
  nextRun?: Date
  isRunning: boolean
}

// No filter state needed - simplified dashboard

interface MultiIdState {
  inputValue: string
  parsedIds: number[]
  isValid: boolean
}

interface CampaignState {
  selectedCampaignId: string
  campaigns: Array<{ id: string; name: string }>
  isLoading: boolean
}

interface JobTierCount {
  job_tier: string
  count: number
}

interface SourceCount {
  source: string
  count: string
}

const ProcessingDashboard = () => {
  const [jobs, setJobs] = useState<ProcessingJob[]>([])
  const [stats, setStats] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [executing, setExecuting] = useState<Set<string>>(new Set())
  const [processingLimit, setProcessingLimit] = useState(50)
  const [singleEntityId, setSingleEntityId] = useState('')
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [multiIdState, setMultiIdState] = useState<MultiIdState>({
    inputValue: '',
    parsedIds: [],
    isValid: false
  })

  const [campaignState, setCampaignState] = useState<CampaignState>({
    selectedCampaignId: '',
    campaigns: [],
    isLoading: false
  })

  // Stage categorization
  const contactStages: ProcessingStage[] = ['email_validation', 'contact_enrichment_v2', 'contact_investment_criteria', 'email_generation']
  const companyStages: ProcessingStage[] = ['website_scraping','company_overview_v2', 'company_investment_criteria']

  const newsStages: ProcessingStage[] = ['news_fetch', 'news_enrichment']

  useEffect(() => {
    fetchJobs()
    fetchStats()
    loadCampaigns()
  }, [])

  useEffect(() => {
    if (!autoRefreshEnabled) return

    const interval = setInterval(() => {
      if (!isRefreshing) {
        setIsRefreshing(true)
        Promise.all([fetchJobs(), fetchStats()])
          .finally(() => setIsRefreshing(false))
      }
    }, 30000)

    return () => clearInterval(interval)
  }, [autoRefreshEnabled, isRefreshing])

  const fetchJobs = async () => {
    try {
      const response = await fetch('/api/processing/trigger?action=jobs', {
        signal: AbortSignal.timeout(15000)
      })
      if (!response.ok) throw new Error(`HTTP ${response.status}`)
      const data = await response.json()
      if (data.success) {
        setJobs(data.data.jobs)
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Failed to fetch jobs:', error)
      }
    }
  }

  const fetchStats = async () => {
    try {
      setError(null)
      
      // Fetch stats from both contacts and companies unified filters API
      // Using stats=only for processing dashboard to get stats efficiently
      const [contactsResponse, companiesResponse] = await Promise.all([
        fetch('/api/contacts/unified-filters-v2?stats=only', {
          signal: AbortSignal.timeout(20000)
        }),
        fetch('/api/companies/unified-filters-v2?stats=only', {
          signal: AbortSignal.timeout(20000)
        })
      ])

      if (!contactsResponse.ok || !companiesResponse.ok) {
        throw new Error(`HTTP ${contactsResponse.status} or ${companiesResponse.status}`)
      }

      const [contactsData, companiesData] = await Promise.all([
        contactsResponse.json(),
        companiesResponse.json()
      ])

      if (contactsData.error || companiesData.error) {
        throw new Error(contactsData.error || companiesData.error)
      }

      // Combine stats from both APIs
      const combinedStats = {
        contacts: contactsData.stats,
        companies: companiesData.stats,
        processing: {
          total_contacts: contactsData.stats?.total_contacts || 0,
          total_companies: companiesData.stats?.total_companies || 0,
        }
      }
      
      setStats(combinedStats)
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Failed to fetch stats:', error)
        setError(error instanceof Error ? error.message : 'Unknown error occurred')
      }
    } finally {
      setLoading(false)
    }
  }

  const loadCampaigns = async () => {
    setCampaignState(prev => ({ ...prev, isLoading: true }))
    try {
      const response = await fetch('/api/smartlead/campaigns')
      if (response.ok) {
        const data = await response.json()
        if (data.success && data.campaigns) {
          setCampaignState(prev => ({
            ...prev,
            campaigns: data.campaigns,
            selectedCampaignId: data.campaigns[0]?.id || '',
            isLoading: false
          }))
        }
      }
    } catch (error) {
      console.error('Failed to load campaigns:', error)
      setCampaignState(prev => ({ ...prev, isLoading: false }))
    }
  }

  const rerunAllErrorsForStage = async (stage: ProcessingStage) => {
    // Note: Error tracking is not yet implemented in unified filters API
    // This function is kept for future implementation
    console.log('Error rerun not yet implemented for unified filters API')
    return
  }

  const manualRefresh = async () => {
    setIsRefreshing(true)
    try {
      await Promise.all([fetchJobs(), fetchStats()])
    } finally {
      setIsRefreshing(false)
    }
  }

  const getEntityTypeForStage = (stage: ProcessingStage): 'contact' | 'company' | 'both' => {
    switch (stage) {
      case 'email_validation':
      case 'contact_enrichment_v2':
      case 'contact_investment_criteria':
      case 'email_generation':
        return 'contact'
      case 'website_scraping':
      case 'company_overview_v2':
      case 'company_investment_criteria':
        return 'company'
      case 'news_fetch':
      case 'news_enrichment':
        return 'both' // news processors handle their own entity selection
      default:
        return 'both'
    }
  }

  const executeJob = async (stage: ProcessingStage, options: { limit?: number; singleId?: number; source?: string } = {}) => {
    const jobKey = `${stage}_${options.singleId || 'batch'}`
    setExecuting(prev => new Set([...prev, jobKey]))

    try {
      // Prepare options with campaign ID for email generation
      // Determine entity type based on stage
      const entityType = getEntityTypeForStage(stage)

      const jobOptions: any = {
        limit: options.limit,
        singleId: options.singleId,
        filters: buildFilters()
      }

      // Add campaign ID for email generation stage
      if (stage === 'email_generation' && campaignState.selectedCampaignId) {
        jobOptions.campaignId = campaignState.selectedCampaignId
      }

      const response = await fetch('/api/processing/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'execute_manual',
          stage,
          entityType,
          options: jobOptions
        })
      })

      const data = await response.json()
      if (data.success) {
        await fetchJobs()
        await fetchStats()
      }
    } catch (error) {
      console.error('Failed to execute job:', error)
    } finally {
      setExecuting(prev => {
        const newSet = new Set(prev)
        newSet.delete(jobKey)
        return newSet
      })
    }
  }

  const buildFilters = () => {
    return {} // No filters needed for simplified dashboard
  }

  const toggleJob = async (jobId: string, enabled: boolean) => {
    try {
      await fetch('/api/processing/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'toggle_scheduled_job',
          options: { jobId, enabled }
        })
      })
      await fetchJobs()
    } catch (error) {
      console.error('Failed to toggle job:', error)
    }
  }

  const runJobNow = async (jobId: string, stage: ProcessingStage) => {
    const jobKey = `scheduled_${jobId}`
    setExecuting(prev => new Set([...prev, jobKey]))

    try {
      const response = await fetch('/api/processing/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'execute_scheduled_job',
          options: { jobId, stage }
        })
      })

      const data = await response.json()
      if (data.success) {
        await fetchJobs()
        await fetchStats()
      } else {
        console.error('Failed to run scheduled job:', data.error)
      }
    } catch (error) {
      console.error('Failed to run scheduled job:', error)
    } finally {
      setExecuting(prev => {
        const newSet = new Set(prev)
        newSet.delete(jobKey)
        return newSet
      })
    }
  }

  // No filter handling needed for simplified dashboard

  const handleMultiIdInputChange = (value: string) => {
    const parsedIds = value
      .split(/[,\s\n]+/)
      .map(id => id.trim())
      .filter(id => id !== '')
      .map(id => parseInt(id, 10))
      .filter(id => !isNaN(id))
    
    setMultiIdState({
      inputValue: value,
      parsedIds,
      isValid: parsedIds.length > 0
    })
  }

  const executeMultiIdJob = async (stage: ProcessingStage, ids: number[]) => {
    if (ids.length === 0) return
    
    const jobKey = `${stage}_multi_${ids.join('_')}`
    setExecuting(prev => new Set([...prev, jobKey]))

    try {
      // Prepare options with campaign ID for email generation
      // Determine entity type based on stage
      const entityType = getEntityTypeForStage(stage)

      const jobOptions: any = {
        multiIds: ids,
        filters: buildFilters()
      }

      // Add campaign ID for email generation stage
      if (stage === 'email_generation' && campaignState.selectedCampaignId) {
        jobOptions.campaignId = campaignState.selectedCampaignId
      }

      const response = await fetch('/api/processing/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'execute_manual',
          stage,
          entityType,
          options: jobOptions
        })
      })

      const data = await response.json()
      if (data.success) {
        await fetchStats()
      }
    } catch (error) {
      console.error('Failed to execute multi-ID job:', error)
    } finally {
      setExecuting(prev => {
        const newSet = new Set(prev)
        newSet.delete(jobKey)
        return newSet
      })
    }
  }

  const retryFailedEntity = async (stage: ProcessingStage, entityId: number) => {
    await executeJob(stage, { singleId: entityId })
  }

  const retryAllFailedEntities = async (stage: ProcessingStage, entityIds: number[]) => {
    await executeMultiIdJob(stage, entityIds)
  }

  const extractErrorIds = (errors: any[], stage?: ProcessingStage) => {
    return errors
      .filter(error => !stage || error.stage === stage)
      .map(error => error.entity_id)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      // Could show a toast notification here
    }).catch(err => {
      console.error('Failed to copy:', err)
    })
  }

  const getStageIcon = (stage: ProcessingStage) => {
    switch (stage) {
      case 'email_validation': return <Mail className="h-4 w-4" />
      case 'contact_investment_criteria': return <Target className="h-4 w-4" />
      case 'email_generation': return <PenTool className="h-4 w-4" />
      case 'company_overview_v2': return <Building className="h-4 w-4" />
      case 'company_investment_criteria': return <Target className="h-4 w-4" />
      case 'website_scraping': return <Search className="h-4 w-4" />
      case 'news_fetch': return <Download className="h-4 w-4" />
      case 'news_enrichment': return <Sparkles className="h-4 w-4" />
      default: return <Activity className="h-4 w-4" />
    }
  }

  const getStageColor = (stage: ProcessingStage) => {
    switch (stage) {
      case 'email_validation': return 'bg-blue-500'
      case 'contact_investment_criteria': return 'bg-rose-500'
      case 'email_generation': return 'bg-pink-500'
      case 'company_overview_v2': return 'bg-indigo-500'
      case 'company_investment_criteria': return 'bg-cyan-500'
      case 'website_scraping': return 'bg-purple-500'
      case 'news_fetch': return 'bg-amber-500'
      case 'news_enrichment': return 'bg-teal-500'
      default: return 'bg-gray-500'
    }
  }

  const formatStageTitle = (stage: ProcessingStage) => {
    switch (stage) {
      case 'email_validation': return 'Email Validation'
      case 'contact_enrichment_v2': return 'Contact Enrichment V2'
      case 'contact_investment_criteria': return 'Contact Investment Criteria'
      case 'email_generation': return 'Email Generation'
      case 'company_overview_v2': return 'Company Overview V2'
      case 'company_investment_criteria': return 'Company Investment Criteria'
      case 'website_scraping': return 'Website Scraping'
      case 'news_fetch': return 'News Fetch'
      case 'news_enrichment': return 'News Enrichment'
      default: return String(stage).replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())
    }
  }

  const getStageStats = (stage: ProcessingStage): StageStats => {
    if (!stats) return { total: 0, pending: 0, running: 0, completed: 0, failed: 0, error: 0, success_rate: 0 }
    
    // Transform unified filters API response into stage stats
    const transformStats = (entityType: 'contacts' | 'companies', stageName: string) => {
      const entityStats = stats[entityType]
      if (!entityStats) return { total: 0, pending: 0, running: 0, completed: 0, failed: 0, error: 0, success_rate: 0 }
      
      // Map stage names to the unified filters API field names
      const stageFieldMap: { [key: string]: string } = {
        'email_validation': 'email_validation',
        'contact_enrichment_v2': 'contact_enrichment_v2',
        'contact_investment_criteria': 'contact_investment_criteria',
        'email_generation': 'email_generation',
        'company_overview_v2': 'company_overview_v2',
        'company_investment_criteria': 'company_investment_criteria',
        'website_scraping': 'web_crawler'
      }
      
      const fieldName = stageFieldMap[stageName] || stageName
      const pending = entityStats[`${fieldName}_pending`] || 0
      const running = entityStats[`${fieldName}_running`] || 0
      const completed = entityStats[`${fieldName}_completed`] || 0
      const failed = entityStats[`${fieldName}_failed`] || 0
      const error = entityStats[`${fieldName}_error`] || 0
      const total = entityStats[`${fieldName}_total`] || entityStats[`total_${entityType.slice(0, -1)}`] || 0
      
      const success_rate = total > 0 ? ((completed / total) * 100) : 0
      
      return {
        total: Number(total),
        pending: Number(pending),
        running: Number(running),
        completed: Number(completed),
        failed: Number(failed),
        error: Number(error),
        success_rate
      }
    }
    
    switch (stage) {
      case 'email_validation':
        return transformStats('contacts', 'email_validation')
      case 'contact_enrichment_v2':
        return transformStats('contacts', 'contact_enrichment_v2')
      case 'contact_investment_criteria':
        return transformStats('contacts', 'contact_investment_criteria')
      case 'email_generation':
        return transformStats('contacts', 'email_generation')
      case 'company_overview_v2':
        return transformStats('companies', 'company_overview_v2')
      case 'company_investment_criteria':
        return transformStats('companies', 'company_investment_criteria')
      case 'website_scraping':
        return transformStats('companies', 'website_scraping')
      case 'news_fetch':
      case 'news_enrichment':
        // News stats are not yet implemented in unified filters API
        return { total: 0, pending: 0, running: 0, completed: 0, failed: 0, error: 0, success_rate: 0 }
      default:
        return { total: 0, pending: 0, running: 0, completed: 0, failed: 0, error: 0, success_rate: 0 }
    }
  }

  const getProcessButtonText = (stage: ProcessingStage, stageStats: StageStats) => {
    const willProcess = Math.min(processingLimit, stageStats.pending)
    return `Process ${willProcess}${stageStats.pending === 0 ? ' (none available)' : ''}`
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-600'
      case 'running': return 'text-blue-600'
      case 'completed': return 'text-green-600'
      case 'failed': return 'text-red-600'
      case 'error': return 'text-red-800'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-3 w-3" />
      case 'running': return <RefreshCw className="h-3 w-3 animate-spin" />
      case 'completed': return <CheckCircle2 className="h-3 w-3" />
      case 'failed': return <AlertTriangle className="h-3 w-3" />
      case 'error': return <AlertCircle className="h-3 w-3" />
      default: return <Activity className="h-3 w-3" />
    }
  }

  const renderStageCard = (stage: ProcessingStage) => {
    const stageStats = getStageStats(stage)
    
    return (
      <Card key={stage} className="border-l-4" style={{ borderLeftColor: getStageColor(stage).replace('bg-', '') }}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              {getStageIcon(stage)}
              <h3 className="font-medium">{formatStageTitle(stage)}</h3>
            </div>
          </div>

          {/* Progress Bar */}
          {stageStats.total > 0 && (
            <div className="mb-3">
              <div className="flex justify-between text-xs text-gray-600 mb-1">
                <span>Progress</span>
                <span>{Math.round(stageStats.success_rate)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${stageStats.success_rate}%` }}
                />
              </div>
            </div>
          )}

          {/* Status breakdown */}
          <div className="grid grid-cols-3 gap-2 mb-3 text-xs">
            <div className={`flex items-center gap-1 ${getStatusColor('pending')}`}>
              {getStatusIcon('pending')}
              <span>P: {stageStats.pending}</span>
            </div>
            <div className={`flex items-center gap-1 ${getStatusColor('running')}`}>
              {getStatusIcon('running')}
              <span>R: {stageStats.running}</span>
            </div>
            <div className={`flex items-center gap-1 ${getStatusColor('completed')}`}>
              {getStatusIcon('completed')}
              <span>C: {stageStats.completed}</span>
            </div>
            <div className={`flex items-center gap-1 ${getStatusColor('failed')}`}>
              {getStatusIcon('failed')}
              <span>F: {stageStats.failed}</span>
            </div>
            <div className={`flex items-center gap-1 ${getStatusColor('error')}`}>
              {getStatusIcon('error')}
              <span>E: {stageStats.error}</span>
            </div>
            <div className="flex items-center gap-1 text-purple-600">
              <TrendingUp className="h-3 w-3" />
              <span>{Math.round(stageStats.success_rate)}%</span>
            </div>
          </div>
          
          <Button
            onClick={() => executeJob(stage, { limit: processingLimit })}
            disabled={executing.has(`${stage}_batch`) || stageStats.pending === 0}
            className="w-full"
            size="sm"
          >
            {executing.has(`${stage}_batch`) ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                {(() => {
                  const willProcess = Math.min(processingLimit, stageStats.pending)
                  return `Process ${willProcess}${stageStats.pending === 0 ? ' (none available)' : ''}`
                })()}
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    )
  }

  const renderStageColumn = (title: string, stages: ProcessingStage[], icon: React.ReactNode, color: string) => (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-4">
        <div className={`p-2 rounded-lg ${color}`}>
          {icon}
        </div>
        <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
      </div>
      <div className="space-y-4">
        {stages.map(stage => renderStageCard(stage))}
      </div>
    </div>
  )

  const renderMultiEntityColumn = (title: string, stages: ProcessingStage[], icon: React.ReactNode, color: string) => (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-4">
        <div className={`p-2 rounded-lg ${color}`}>
          {icon}
        </div>
        <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
      </div>
      <div className="space-y-4">
        {stages.map(stage => (
          <Card key={stage} className="border-l-4" style={{ borderLeftColor: getStageColor(stage).replace('bg-', '') }}>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-3">
                {getStageIcon(stage)}
                <h3 className="font-medium">{formatStageTitle(stage)}</h3>
              </div>
              
              <Button
                onClick={() => executeMultiIdJob(stage, multiIdState.parsedIds)}
                disabled={!multiIdState.isValid || executing.has(`${stage}_multi_${multiIdState.parsedIds.join('_')}`)}
                className="w-full"
                size="sm"
              >
                {executing.has(`${stage}_multi_${multiIdState.parsedIds.join('_')}`) ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Process {multiIdState.parsedIds.length} IDs
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )

  const renderRerunStageView = (stage: ProcessingStage) => {
    // Note: Error tracking is not yet implemented in unified filters API
    // This view is simplified to show stage stats only
    const stageStats = getStageStats(stage)

    return (
      <Card key={stage} className="border-l-4" style={{ borderLeftColor: getStageColor(stage).replace('bg-', '') }}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              {getStageIcon(stage)}
              <h3 className="font-medium">{formatStageTitle(stage)}</h3>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {stageStats.pending} pending
              </Badge>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-2 mb-4">
            <Button
              onClick={() => executeJob(stage, { limit: processingLimit })}
              disabled={executing.has(`${stage}_batch`) || stageStats.pending === 0}
              className="w-full"
              size="sm"
            >
              {executing.has(`${stage}_batch`) ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  {getProcessButtonText(stage, stageStats)}
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-2" />
          <p className="text-gray-600">Loading processing stats...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center text-red-500">
          <AlertCircle className="h-8 w-8 mx-auto mb-2" />
          <p className="font-medium">Failed to load processing stats</p>
          <p className="text-sm text-gray-600 mt-1">{error}</p>
          <Button onClick={fetchStats} variant="outline" className="mt-4">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center text-gray-500">
          <AlertCircle className="h-8 w-8 mx-auto mb-2" />
          <p>No processing stats available</p>
        </div>
      </div>
    )
  }

  // No active filters check needed for simplified dashboard

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Processing Dashboard</h1>
        <div className="flex gap-2">
          <Button onClick={fetchStats} disabled={loading}>
            Refresh Stats
          </Button>
          <Button onClick={fetchJobs} disabled={loading}>
            Refresh Jobs
          </Button>
        </div>
      </div>

      {/* Unified View Section */}
      {stats && (stats.contacts || stats.companies) && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Total Entities
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {(Number(stats.contacts?.total_contacts || 0) + Number(stats.companies?.total_companies || 0)).toLocaleString()}
              </div>
              <p className="text-sm text-muted-foreground">
                {stats.contacts?.total_contacts?.toLocaleString() || 0} contacts + {stats.companies?.total_companies?.toLocaleString() || 0} companies
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Contacts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.contacts?.total_contacts?.toLocaleString() || 0}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Companies
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.companies?.total_companies?.toLocaleString() || 0}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="manual" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="manual">Manual Processing</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled Jobs</TabsTrigger>
          <TabsTrigger value="multi">Multi-ID</TabsTrigger>
        </TabsList>

        {/* Manual Processing */}
        <TabsContent value="manual" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5" />
                Manual Processing
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4 mb-6">
                <div className="flex items-center gap-2">
                  <Label htmlFor="limit">Batch Limit:</Label>
                  <Input
                    id="limit"
                    type="number"
                    value={processingLimit}
                    onChange={(e) => setProcessingLimit(parseInt(e.target.value) || 50)}
                    className="w-20"
                    min="1"
                    max="1000"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {renderStageColumn("Contacts", contactStages, <Users className="h-5 w-5 text-white" />, "bg-blue-500")}
                {renderStageColumn("Companies", companyStages, <Building className="h-5 w-5 text-white" />, "bg-green-500")}
                {renderStageColumn("News", newsStages, <FileText className="h-5 w-5 text-white" />, "bg-purple-500")}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Scheduled Jobs */}
        <TabsContent value="scheduled" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Scheduled Jobs
                <Badge variant="outline" className="ml-2">
                  {jobs.filter(job => job.enabled).length} enabled / {jobs.length} total
                </Badge>
              </CardTitle>
              <p className="text-sm text-gray-600">
                Manage scheduled jobs and run them immediately when needed. Jobs will use their configured limits and batch sizes.
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {jobs.map((job) => (
                  <div key={job.id} className={`flex items-center justify-between p-4 border rounded-lg ${job.isRunning ? 'bg-blue-50 border-blue-200' : ''}`}>
                    <div className="flex items-center gap-4">
                      <div className={`w-3 h-3 rounded-full ${job.enabled ? 'bg-green-500' : 'bg-gray-400'}`} />
                      <div>
                        <div className="flex items-center gap-2">
                          {getStageIcon(job.stage)}
                          <h3 className="font-medium">{formatStageTitle(job.stage)}</h3>
                          {job.isRunning && (
                            <Badge variant="secondary" className="text-xs">
                              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                              Currently Running
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600">Schedule: {job.schedule}</p>
                        {job.lastRun && (
                          <p className="text-xs text-gray-500">
                            Last run: {new Date(job.lastRun).toLocaleString()}
                          </p>
                        )}
                        {job.nextRun && (
                          <p className="text-xs text-blue-600">
                            Next run: {new Date(job.nextRun).toLocaleString()}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={() => runJobNow(job.id, job.stage)}
                        disabled={executing.has(`scheduled_${job.id}`) || job.isRunning}
                        variant="default"
                        size="sm"
                      >
                        {executing.has(`scheduled_${job.id}`) ? (
                          <>
                            <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
                            Running...
                          </>
                        ) : (
                          <>
                            <Zap className="h-4 w-4 mr-1" />
                            Run Now
                          </>
                        )}
                      </Button>
                      
                      <Button
                        onClick={() => toggleJob(job.id, !job.enabled)}
                        variant={job.enabled ? "secondary" : "outline"}
                        size="sm"
                      >
                        {job.enabled ? (
                          <>
                            <Pause className="h-4 w-4 mr-1" />
                            Disable
                          </>
                        ) : (
                          <>
                            <Play className="h-4 w-4 mr-1" />
                            Enable
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Multi-ID Processing */}
        <TabsContent value="multi" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <List className="h-5 w-5" />
                Multi-ID Processing
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="multiIds">Entity IDs (comma, space, or newline separated):</Label>
                  <textarea
                    id="multiIds"
                    value={multiIdState.inputValue}
                    onChange={(e) => handleMultiIdInputChange(e.target.value)}
                    placeholder="Enter multiple IDs: 1, 2, 3 or one per line"
                    className="w-full min-h-[100px] p-3 border rounded-md resize-vertical"
                  />
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span>{multiIdState.parsedIds.length} IDs parsed</span>
                    {multiIdState.isValid && <CheckCircle className="h-4 w-4 text-green-500" />}
                    {multiIdState.inputValue && !multiIdState.isValid && <XCircle className="h-4 w-4 text-red-500" />}
                  </div>
                  {multiIdState.parsedIds.length > 0 && (
                    <div className="text-sm text-gray-500 max-h-20 overflow-y-auto">
                      <strong>Parsed IDs:</strong> {multiIdState.parsedIds.join(', ')}
                    </div>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {renderMultiEntityColumn("Contacts", contactStages, <Users className="h-5 w-5 text-white" />, "bg-blue-500")}
                {renderMultiEntityColumn("Companies", companyStages, <Building className="h-5 w-5 text-white" />, "bg-green-500")}
                {renderMultiEntityColumn("News", newsStages, <FileText className="h-5 w-5 text-white" />, "bg-purple-500")}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ProcessingDashboard 