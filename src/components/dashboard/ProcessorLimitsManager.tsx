'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Alert, AlertDescription } from '../ui/alert'
import { Badge } from '../ui/badge'
import { Loader2, Save, RefreshCw } from 'lucide-react'

interface ProcessorLimitConfig {
  defaultLimit: number
  maxLimit: number
  batchSize: number
}

interface ProcessorLimits {
  [key: string]: ProcessorLimitConfig
}

export function ProcessorLimitsManager() {
  const [limits, setLimits] = useState<ProcessorLimits>({})
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const processorNames = [
    'email_validator',
    'company_web_crawler',
    'company_overview_v2',
    'company_investment_criteria',
    'contact_enrichment_v2',
    'contact_investment_criteria',
    'email_generation',
    'news_html_fetcher',
    'news_enrichment'
  ]

  const processorDisplayNames: Record<string, string> = {
    email_validator: 'Email Validator',
    company_web_crawler: 'Company Web Crawler',
    company_overview_v2: 'Company Overview V2',
    company_investment_criteria: 'Company Investment Criteria',
    contact_enrichment_v2: 'Contact Enrichment V2',
    contact_investment_criteria: 'Contact Investment Criteria',
    email_generation: 'Email Generation',
    news_html_fetcher: 'News HTML Fetcher',
    news_enrichment: 'News Enrichment'
  }

  const loadLimits = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/processor-limits')
      const data = await response.json()
      
      if (data.success) {
        setLimits(data.data)
      } else {
        setError(data.error || 'Failed to load processor limits')
      }
    } catch (err) {
      setError('Failed to load processor limits')
      console.error('Error loading processor limits:', err)
    } finally {
      setLoading(false)
    }
  }

  const updateLimit = async (processorName: string, field: keyof ProcessorLimitConfig, value: number) => {
    try {
      setSaving(processorName)
      setError(null)
      setSuccess(null)

      const response = await fetch('/api/processor-limits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          processorName,
          [field]: value
        })
      })

      const data = await response.json()

      if (data.success) {
        setSuccess(`Updated ${processorDisplayNames[processorName]} ${field}`)
        // Reload limits to get updated values
        await loadLimits()
      } else {
        setError(data.error || 'Failed to update processor limit')
      }
    } catch (err) {
      setError('Failed to update processor limit')
      console.error('Error updating processor limit:', err)
    } finally {
      setSaving(null)
    }
  }

  useEffect(() => {
    loadLimits()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading processor limits...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Processor Limits Configuration</h2>
          <p className="text-muted-foreground">
            Configure how many records each processor will process per run
          </p>
        </div>
        <Button onClick={loadLimits} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert>
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {processorNames.map((processorName) => {
          const processorLimit = limits[processorName]
          if (!processorLimit) return null

          return (
            <Card key={processorName}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    {processorDisplayNames[processorName]}
                  </span>
                  <Badge variant="secondary" className="text-xs">
                    {processorName}
                  </Badge>
                </CardTitle>
                <CardDescription>
                  Configure processing limits for {processorDisplayNames[processorName]}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor={`${processorName}-defaultLimit`}>
                    Default Limit
                  </Label>
                  <div className="flex space-x-2">
                    <Input
                      id={`${processorName}-defaultLimit`}
                      type="number"
                      min="1"
                      max={processorLimit.maxLimit}
                      value={processorLimit.defaultLimit}
                      onChange={(e) => {
                        const newLimits = { ...limits }
                        newLimits[processorName] = {
                          ...newLimits[processorName],
                          defaultLimit: parseInt(e.target.value) || 0
                        }
                        setLimits(newLimits)
                      }}
                    />
                    <Button
                      size="sm"
                      onClick={() => updateLimit(processorName, 'defaultLimit', processorLimit.defaultLimit)}
                      disabled={saving === processorName}
                    >
                      {saving === processorName ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor={`${processorName}-maxLimit`}>
                    Max Limit
                  </Label>
                  <div className="flex space-x-2">
                    <Input
                      id={`${processorName}-maxLimit`}
                      type="number"
                      min="1"
                      value={processorLimit.maxLimit}
                      onChange={(e) => {
                        const newLimits = { ...limits }
                        newLimits[processorName] = {
                          ...newLimits[processorName],
                          maxLimit: parseInt(e.target.value) || 0
                        }
                        setLimits(newLimits)
                      }}
                    />
                    <Button
                      size="sm"
                      onClick={() => updateLimit(processorName, 'maxLimit', processorLimit.maxLimit)}
                      disabled={saving === processorName}
                    >
                      {saving === processorName ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor={`${processorName}-batchSize`}>
                    Batch Size
                  </Label>
                  <div className="flex space-x-2">
                    <Input
                      id={`${processorName}-batchSize`}
                      type="number"
                      min="1"
                      value={processorLimit.batchSize}
                      onChange={(e) => {
                        const newLimits = { ...limits }
                        newLimits[processorName] = {
                          ...newLimits[processorName],
                          batchSize: parseInt(e.target.value) || 0
                        }
                        setLimits(newLimits)
                      }}
                    />
                    <Button
                      size="sm"
                      onClick={() => updateLimit(processorName, 'batchSize', processorLimit.batchSize)}
                      disabled={saving === processorName}
                    >
                      {saving === processorName ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Environment Variables</CardTitle>
          <CardDescription>
            You can also configure these limits using environment variables. 
            The format is: PROCESSOR_NAME_SETTING
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>Examples:</strong></p>
            <ul className="list-disc list-inside space-y-1 text-muted-foreground">
              <li>EMAIL_VALIDATOR_DEFAULT_LIMIT=100</li>
              <li>EMAIL_VALIDATOR_MAX_LIMIT=1000</li>
              <li>EMAIL_VALIDATOR_BATCH_SIZE=20</li>
              <li>COMPANY_WEB_CRAWLER_DEFAULT_LIMIT=50</li>
              <li>CONTACT_ENRICHMENT_V2_DEFAULT_LIMIT=100</li>
            </ul>
            <p className="text-xs text-muted-foreground mt-4">
              Note: Environment variable changes require a server restart to take effect.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
