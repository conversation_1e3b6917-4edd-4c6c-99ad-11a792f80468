import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Search, 
  Filter, 
  Mail, 
  MessageSquare, 
  Eye,
  TrendingUp,
  Clock,
  User
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

interface Contact {
  contact_id: number;
  first_name: string;
  last_name: string;
  email: string;
  company_name?: string;
  smartlead_lead_id?: string;
  smartlead_status?: string;
  smartlead_campaign_id?: string;
  last_email_sent_at?: string;
  open_rate?: number;
  reply_rate?: number;
  total_emails_sent?: number;
  last_activity?: string;
}

interface ContactSelectorProps {
  onContactSelect: (contactId: number) => void;
  selectedContactId?: number | null;
}

export function ContactSelector({ onContactSelect, selectedContactId }: ContactSelectorProps) {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [campaignFilter, setCampaignFilter] = useState('all');
  const [sortBy, setSortBy] = useState('last_activity');
  const [isLoading, setIsLoading] = useState(false);

  // Fetch contacts from API
  useEffect(() => {
    const fetchContacts = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/smartlead/contacts');
        if (!response.ok) {
          throw new Error(`Failed to fetch contacts: ${response.status}`);
        }
        const result = await response.json();
        setContacts(result.contacts || []);
        setFilteredContacts(result.contacts || []);
      } catch (error) {
        console.error('Error fetching contacts:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchContacts();
  }, []);

  // Filter and sort contacts
  useEffect(() => {
    let filtered = contacts;

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(contact => 
        contact.first_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        contact.last_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        contact.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        contact.company_name?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(contact => contact.smartlead_status === statusFilter);
    }

    // Apply campaign filter
    if (campaignFilter !== 'all') {
      filtered = filtered.filter(contact => contact.smartlead_campaign_id === campaignFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'last_activity':
          return new Date(b.last_activity || 0).getTime() - new Date(a.last_activity || 0).getTime();
        case 'name':
          return `${a.first_name} ${a.last_name}`.localeCompare(`${b.first_name} ${b.last_name}`);
        case 'email':
          return a.email.localeCompare(b.email);
        case 'open_rate':
          return (b.open_rate || 0) - (a.open_rate || 0);
        case 'reply_rate':
          return (b.reply_rate || 0) - (a.reply_rate || 0);
        default:
          return 0;
      }
    });

    setFilteredContacts(filtered);
  }, [contacts, searchQuery, statusFilter, campaignFilter, sortBy]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SENT': return 'bg-purple-100 text-purple-800 border-purple-300';
      case 'DELIVERED': return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'OPENED': return 'bg-green-100 text-green-800 border-green-300';
      case 'REPLIED': return 'bg-emerald-100 text-emerald-800 border-emerald-300';
      case 'BOUNCED': return 'bg-red-100 text-red-800 border-red-300';
      case 'UNSUBSCRIBED': return 'bg-gray-100 text-gray-800 border-gray-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Campaign Contacts</CardTitle>
            <p className="text-sm text-muted-foreground">
              {filteredContacts.length} of {contacts.length} contacts
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Input
              placeholder="Search contacts..."
              className="w-64"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={<Search className="h-4 w-4" />}
            />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="SENT">Sent</SelectItem>
                <SelectItem value="DELIVERED">Delivered</SelectItem>
                <SelectItem value="OPENED">Opened</SelectItem>
                <SelectItem value="REPLIED">Replied</SelectItem>
                <SelectItem value="BOUNCED">Bounced</SelectItem>
                <SelectItem value="UNSUBSCRIBED">Unsubscribed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="last_activity">Last Activity</SelectItem>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="email">Email</SelectItem>
                <SelectItem value="open_rate">Open Rate</SelectItem>
                <SelectItem value="reply_rate">Reply Rate</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Contact</TableHead>
              <TableHead>Company</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Performance</TableHead>
              <TableHead>Last Activity</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredContacts.map((contact) => (
              <TableRow 
                key={contact.contact_id}
                className={`cursor-pointer hover:bg-gray-50 ${
                  selectedContactId === contact.contact_id ? 'bg-blue-50' : ''
                }`}
                onClick={() => onContactSelect(contact.contact_id)}
              >
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback>
                        {getInitials(contact.first_name, contact.last_name)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">
                        {contact.first_name} {contact.last_name}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {contact.email}
                      </div>
                      {contact.smartlead_lead_id && (
                        <div className="text-xs text-blue-600">
                          ID: {contact.smartlead_lead_id}
                        </div>
                      )}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    {contact.company_name || 'N/A'}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge 
                    variant="outline"
                    className={getStatusColor(contact.smartlead_status || '')}
                  >
                    {contact.smartlead_status || 'Unknown'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <Eye className="h-3 w-3 text-green-600" />
                      <span className="text-sm">
                        {contact.open_rate || 0}% open rate
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MessageSquare className="h-3 w-3 text-blue-600" />
                      <span className="text-sm">
                        {contact.reply_rate || 0}% reply rate
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Mail className="h-3 w-3 text-purple-600" />
                      <span className="text-sm">
                        {contact.total_emails_sent || 0} emails sent
                      </span>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    {contact.last_activity ? (
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3 text-muted-foreground" />
                        <span>
                          {new Date(contact.last_activity).toLocaleDateString()}
                        </span>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">No activity</span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onContactSelect(contact.contact_id);
                      }}
                    >
                      <MessageSquare className="h-4 w-4 mr-1" />
                      View Thread
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Open contact details
                      }}
                    >
                      <User className="h-4 w-4 mr-1" />
                      Details
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        
        {filteredContacts.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            {searchQuery || statusFilter !== 'all' || campaignFilter !== 'all' 
              ? 'No contacts match your filters' 
              : 'No contacts found'}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 