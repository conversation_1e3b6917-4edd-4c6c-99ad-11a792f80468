"use client"

import React, { useState, useRef } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import Link from 'next/link'
import { MappingTablesViewRef } from './people/MappingTablesView'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface PeopleStuffProps {
  isActive: boolean;
}

export const PeopleStuff: React.FC<PeopleStuffProps> = ({ isActive }) => {
  // Return null to hide the entire component
  return null;
}