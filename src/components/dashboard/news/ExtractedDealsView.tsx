"use client"

import { useState, useEffect, useCallback } from 'react'
import { 
  Handshake, Search, Users, Globe, MapPin, 
  ChevronRight, Plus, ChevronLeft, ChevronDown, Calendar,
  ArrowUpDown, Filter, Building, ArrowRight, DollarSign,
  Percent, Building2, Tag, Home, Clock, BarChart3, 
  BarChart, Newspaper, BookOpen, Rss, TrendingUp, Info
} from 'lucide-react'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Bar<PERSON><PERSON> as RechartsBarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useRouter } from 'next/navigation'
import { Badge } from '@/components/ui/badge'
import debounce from 'lodash/debounce'
import { <PERSON><PERSON>, AvatarFallback } from '@/components/ui/avatar'

interface Company {
  id: number
  company_name: string
  role: string
}

interface Deal {
  id: number
  news_id: number
  deal_type: string[] | string // Handle both array and string formats for backward compatibility
  deal_status: string
  deal_date: string
  deal_value: number
  currency: string
  cap_rate: number
  ltv: number
  confidence_score: number
  property_type: string[] | string // Handle both array and string formats
  property_size: number
  property_size_unit: string
  property_address: string
  news_title: string
  news_date: string
  news_source: string
  companies: Company[]
  created_at: string
}

interface SourceStat {
  news_source: string
  deal_count: number
  today_count: string
}

interface DailyStat {
  news_source: string
  date: string
  count: number
}

interface PaginatedResponse {
  deals: Deal[]
  total: number
  page: number
  totalPages: number
  pageSize: number
  sources: SourceStat[]
  dailyStats: DailyStat[]
}

export default function ExtractedDealsView() {
  const router = useRouter()
  const [deals, setDeals] = useState<Deal[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [totalDeals, setTotalDeals] = useState(0)
  const [pageSize, setPageSize] = useState(20)
  const [sortBy, setSortBy] = useState('date_desc')
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('grid')
  const [sourceStats, setSourceStats] = useState<SourceStat[]>([])
  const [dailyStats, setDailyStats] = useState<DailyStat[]>([])
  const [selectedSource, setSelectedSource] = useState<string>("")

  // Utility function to extract first value from JSONB array or return string directly
  const getDisplayValue = (value: string[] | string | null | undefined): string => {
    if (!value) return '';
    if (Array.isArray(value)) {
      return value.length > 0 ? value[0] : '';
    }
    return value;
  };

  // Utility function to get all values from JSONB array or single string as array
  const getArrayValue = (value: string[] | string | null | undefined): string[] => {
    if (!value) return [];
    if (Array.isArray(value)) {
      return value;
    }
    return [value];
  };

  // Get a color based on deal type
  const getDealTypeColor = (dealType: string[] | string): string => {
    const displayType = getDisplayValue(dealType);
    const colors: Record<string, string> = {
      'Acquisition': 'bg-blue-500 text-white',
      'Sale': 'bg-green-500 text-white',
      'Financing': 'bg-purple-500 text-white',
      'Refinancing': 'bg-indigo-500 text-white',
      'Investment': 'bg-orange-500 text-white',
      'Development': 'bg-red-500 text-white',
      'Lease': 'bg-yellow-500 text-black',
      'Joint Venture': 'bg-teal-500 text-white',
      'default': 'bg-gray-500 text-white'
    }
    
    return colors[displayType] || colors.default;
  }

  // Get a color based on property type
  const getPropertyTypeColor = (propertyType: string | string[] | any): string => {
    // Safely convert to string
    const propertyTypeString = Array.isArray(propertyType) 
      ? propertyType[0] || ''
      : typeof propertyType === 'string' 
        ? propertyType 
        : '';
    
    const colors: Record<string, string> = {
      'Office': 'bg-blue-100 text-blue-800',
      'Retail': 'bg-green-100 text-green-800',
      'Multifamily': 'bg-purple-100 text-purple-800',
      'Industrial': 'bg-yellow-100 text-yellow-800',
      'Mixed-Use': 'bg-pink-100 text-pink-800',
      'Hotel': 'bg-indigo-100 text-indigo-800',
      'Land': 'bg-teal-100 text-teal-800',
      'default': 'bg-gray-100 text-gray-800'
    }
    
    return colors[propertyTypeString] || colors.default;
  }

  // Get a color based on news source
  const getSourceColor = (source: string): string => {
    const colors: Record<string, string> = {
      'globest': '#22c55e',
      'bisnow': '#6366f1',
      'Pincus': '#8b5cf6',
      'therealdeal': '#ef4444',
      'default': '#3b82f6'
    };
    
    return colors[source] || colors.default;
  }

  // Format a date string
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  // Format a date with time
  const formatDateTime = (dateString: string) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // Create a debounced search function
  const debouncedSearch = useCallback(
    debounce((term: string) => {
      setDebouncedSearchTerm(term)
    }, 300),
    []
  )

  // Handle search input changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchTerm(value)
    debouncedSearch(value)
  }

  // Handle source selection
  const handleSourceChange = (source: string) => {
    setSelectedSource(source);
    setPage(1); // Reset to first page when changing source
  }

  useEffect(() => {
    fetchDeals()
  }, [page, pageSize, debouncedSearchTerm, sortBy, selectedSource])

  const fetchDeals = async () => {
    try {
      setLoading(true)
      const response = await fetch(
        `/api/deal-news/extracted-deals?page=${page}&pageSize=${pageSize}&search=${searchTerm}&sort=${sortBy}${selectedSource ? `&source=${selectedSource}` : ''}`
      )
      const data: PaginatedResponse = await response.json()
      
      // Calculate total today count
      const totalTodayCount = data.sources.reduce((acc, source) => 
        acc + (Number(source.today_count) || 0), 0
      );
      
      setDeals(data.deals || [])
      setTotalPages(data.totalPages || 0)
      setTotalDeals(data.total || 0)
      setSourceStats(data.sources || [])
      setDailyStats(data.dailyStats || [])
      // console.log(`Source Stats: ${JSON.stringify(data.sources, null, 2)}`)
      // console.log(`Daily Stats: ${JSON.stringify(data.dailyStats, null, 2)}`)
    } catch (error) {
      console.error('Error fetching deals:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDealClick = (dealId: number) => {
    router.push(`/dashboard/dealnews/extracted-deals/${dealId}`)
  }

  return (
    <div className="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Handshake className="h-8 w-8 mr-3 text-blue-600" />
              Extracted Deals
            </h1>
            <p className="text-gray-500 mt-1">
              Showing {deals?.length} of {totalDeals} deals
            </p>
          </div>
        </div>

        {/* Source Stats - Updated with All Sources option and to serve as selector */}
        <div className="mb-4 bg-white p-4 rounded-lg shadow-sm">
          <div className="flex items-center justify-between mb-3 border-b pb-2">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <BarChart3 className="h-5 w-5 mr-2 text-blue-500" />
              Source Statistics
            </h3>
            {sourceStats.length > 0 && (
              <div className="text-sm text-blue-600 font-medium flex items-center">
                <Clock className="h-4 w-4 mr-1" />
                {sourceStats.reduce((total, source) => total + (Number(source.today_count) || 0), 0)} new deals today
              </div>
            )}
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-3">
            {/* Add All Sources option */}
            <div 
              key="all-sources"
              className={`flex items-center p-2 rounded-lg transition-colors cursor-pointer ${selectedSource === "" ? 'bg-blue-50 border border-blue-200' : 'border border-gray-100 hover:bg-gray-50'}`}
              onClick={() => handleSourceChange("")}
            >
              <div className="w-8 h-8 rounded-md flex items-center justify-center mr-2 bg-blue-600 text-white">
                <Globe className="h-4 w-4" />
              </div>
              <div>
                <div className="flex items-center">
                  <span className="font-medium text-sm text-gray-900">All Sources</span>
                  {sourceStats.length > 0 && (
                    <Badge className="ml-1.5 text-xs h-5 px-1.5 bg-blue-100 text-blue-800 hover:bg-blue-100">
                      +{sourceStats.reduce((total, source) => total + (Number(source.today_count) || 0), 0)}
                    </Badge>
                  )}
                </div>
                <span className="text-xs text-gray-500">{totalDeals} total</span>
              </div>
            </div>
            
            {/* Existing sources */}
              {sourceStats.map((source) => (
                <div 
                  key={source.news_source} 
                className={`flex items-center p-2 rounded-lg transition-colors cursor-pointer ${selectedSource === source.news_source ? 'bg-blue-50 border border-blue-200' : 'border border-gray-100 hover:bg-gray-50'}`}
                  onClick={() => handleSourceChange(source.news_source)}
                >
                <div className={`w-8 h-8 rounded-md flex items-center justify-center mr-2 ${getSourceColor(source.news_source)}`}>
                  <Newspaper className="h-4 w-4" />
                  </div>
                <div>
                      <div className="flex items-center">
                    <span className="font-medium text-sm text-gray-900">{source.news_source}</span>
                    {Number(source.today_count) > 0 && (
                      <Badge className="ml-1.5 text-xs h-5 px-1.5 bg-blue-100 text-blue-800 hover:bg-blue-100">
                        +{Number(source.today_count)}
                      </Badge>
                      )}
                    </div>
                  <span className="text-xs text-gray-500">{source.deal_count} total</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

        {/* Daily Stats Chart - Make it professional and filter by selected source */}
        {dailyStats.length > 0 && (
          <Card className="mb-4 overflow-hidden bg-white shadow-sm">
            <div 
              className="p-4 border-b border-gray-100 flex items-center justify-between cursor-pointer" 
              onClick={() => {
                // Toggle the collapsible section
                const detailsEl = document.getElementById('daily-volume-details') as HTMLDetailsElement;
                if (detailsEl) {
                  detailsEl.open = !detailsEl.open;
                }
              }}
            >
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-blue-500" />
                Deal Volume Trend (Last 7 Days)
              </h3>
              <ChevronDown className="h-5 w-5 text-gray-400" />
            </div>
            
            <details id="daily-volume-details" className="group">
              <summary className="sr-only">View daily volume details</summary>
              <div className="p-5">
                {(() => {
                  // Filter data based on selected source
                  const filteredStats = selectedSource 
                    ? dailyStats.filter(stat => stat.news_source === selectedSource)
                    : dailyStats;
                  
                  // Use key to force redraw when source changes
                  const chartKey = selectedSource || 'all-sources';
                  
                  // Debug data
                  console.log("Raw daily stats:", dailyStats);
                  
                  // Process data to group by date
                  const dateGroups = filteredStats.reduce((acc, stat) => {
                    // Ensure date is properly formatted
                    const date = stat.date;
                    if (!date) return acc; // Skip entries with no date
                    
                    // Ensure count is a number
                    const count = typeof stat.count === 'number' ? stat.count : Number(stat.count) || 0;
                    
                    if (!acc[date]) {
                      acc[date] = {
                        date,
                        total: count,
                        sources: [{ source: stat.news_source, count: count }]
                      };
                    } else {
                      acc[date].total += count;
                      // Always add the source data
                      const existingSource = acc[date].sources.find(s => s.source === stat.news_source);
                      if (existingSource) {
                        existingSource.count += count;
                      } else {
                        acc[date].sources.push({ source: stat.news_source, count: count });
                      }
                    }
                    return acc;
                  }, {} as Record<string, { date: string; total: number; sources: Array<{ source: string; count: number }> }>);
                  
                  // Convert to array and sort by date
                  const sortedData = Object.values(dateGroups).sort((a, b) => 
                    new Date(a.date).getTime() - new Date(b.date).getTime()
                  );
                  
                  // Get all unique sources for consistent rendering
                  const allSources = Array.from(new Set(dailyStats.map(stat => stat.news_source)));
                  
                  // Fix any source name discrepancies
                  const sourceMapping: Record<string, string> = {
                    'therealdeall': 'therealdeal'
                  };
                  
                  // Transform data for recharts - simplified version with just total values
                  const simplifiedChartData = sortedData.map(day => ({
                    date: day.date,
                    dateLabel: new Date(day.date).toLocaleDateString('en-US', {weekday: 'short'}),
                    value: Number(day.total)
                  }));
                  
                  console.log("Simplified chart data:", JSON.stringify(simplifiedChartData, null, 2));
                  
                  // Make sure we have valid data to render
                  if (!Array.isArray(simplifiedChartData) || simplifiedChartData.length === 0) {
                    return (
                      <div className="flex flex-col items-center justify-center py-10 bg-gray-50 rounded-lg">
                        <BarChart3 className="h-12 w-12 text-gray-300 mb-3" />
                        <h3 className="text-lg font-medium text-gray-600">No data available</h3>
                        <p className="text-sm text-gray-500 mt-1">
                          {selectedSource 
                            ? `No deals found for ${selectedSource} in the last 7 days` 
                            : 'No deals found in the last 7 days'}
                        </p>
                      </div>
                    );
                  }
                  
                  // Check if we have any data with values > 0
                  const hasData = simplifiedChartData.some(day => Number(day.value) > 0);
                  
                  if (!hasData) {
                    return (
                      <div className="flex flex-col items-center justify-center py-10 bg-gray-50 rounded-lg">
                        <BarChart3 className="h-12 w-12 text-gray-300 mb-3" />
                        <h3 className="text-lg font-medium text-gray-600">No deals found</h3>
                        <p className="text-sm text-gray-500 mt-1">
                          {selectedSource 
                            ? `No deals found for ${selectedSource} in the last 7 days` 
                            : 'No deals found in the last 7 days'}
                        </p>
                      </div>
                    );
                  }
                  
                  // Find max value for scaling
                  const maxValue = Math.max(...simplifiedChartData.map(d => Number(d.value)), 1);
                  
                  // Calculate nice rounded scale values
                  const calculateYAxisScale = (maxVal: number) => {
                    // Create nice, rounded values for y-axis
                    const niceScale = (max: number) => {
                      // Find a nice round number for the top of the scale
                      const magnitude = Math.pow(10, Math.floor(Math.log10(max)));
                      const normalized = max / magnitude;
                      
                      let roundedMax;
                      if (normalized < 1.5) roundedMax = 1.5 * magnitude;
                      else if (normalized < 2) roundedMax = 2 * magnitude;
                      else if (normalized < 2.5) roundedMax = 2.5 * magnitude;
                      else if (normalized < 3) roundedMax = 3 * magnitude;
                      else if (normalized < 4) roundedMax = 4 * magnitude;
                      else if (normalized < 5) roundedMax = 5 * magnitude;
                      else if (normalized < 6) roundedMax = 6 * magnitude;
                      else if (normalized < 8) roundedMax = 8 * magnitude;
                      else roundedMax = 10 * magnitude;
                      
                      // Generate even divisions
                      const intervals = 6;
                      const step = roundedMax / (intervals - 1);
                      return {
                        max: roundedMax,
                        ticks: Array.from({length: intervals}, (_, i) => Math.round(i * step))
                      };
                    };
                    
                    const scale = niceScale(maxVal);
                    return scale.ticks.reverse();
                  };
                  
                  const yAxisValues = calculateYAxisScale(maxValue);
                  const chartMax = Math.max(...yAxisValues);
                  
                  // Get appropriate source color
                  const getChartColor = () => {
                    if (selectedSource) {
                      const colors: Record<string, { light: string, base: string, dark: string }> = {
                        'therealdeall': { light: '#fecaca', base: '#ef4444', dark: '#b91c1c' },
                        'bisnow': { light: '#a5b4fc', base: '#6366f1', dark: '#4338ca' },
                        'Pincus': { light: '#c4b5fd', base: '#8b5cf6', dark: '#6d28d9' },
                        'globest': { light: '#86efac', base: '#22c55e', dark: '#15803d' }
                      };
                      
                      return colors[selectedSource] || { light: '#93c5fd', base: '#3b82f6', dark: '#1d4ed8' };
                    } else {
                      return { light: '#93c5fd', base: '#3b82f6', dark: '#1d4ed8' };
                    }
                  };
                  
                  const chartColor = getChartColor();
                  
                  return (
                    <div>
                      
                      {/* Sleek, Professional Bar Chart */}
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%" key={chartKey}>
                          <RechartsBarChart
                            data={simplifiedChartData}
                            margin={{ top: 30, right: 30, left: 20, bottom: 20 }}
                            barSize={30}
                            barGap={3}
                          >
                            <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#e5e7eb" />
                            <XAxis 
                              dataKey="dateLabel" 
                              tick={{ fontSize: 12 }}
                              axisLine={{ stroke: '#e5e7eb' }}
                              tickLine={{ stroke: '#e5e7eb' }}
                            />
                            <YAxis 
                              tick={{ fontSize: 12 }}
                              axisLine={{ stroke: '#e5e7eb' }}
                              tickLine={{ stroke: '#e5e7eb' }}
                              width={40}
                            />
                            <Tooltip
                              formatter={(value, name) => {
                                if (Number(value) === 0) return null; // Don't show zero values in tooltip
                                return [Number(value).toLocaleString(), selectedSource || 'All Sources'];
                              }}
                              labelFormatter={(label) => {
                                const item = simplifiedChartData.find(day => 
                                  day.dateLabel === label
                                );
                                if (item) {
                                  return new Date(item.date as string).toLocaleDateString('en-US', {
                                    weekday: 'long',
                                    month: 'short',
                                    day: 'numeric'
                                  });
                                }
                                return label;
                              }}
                              contentStyle={{ backgroundColor: 'white', border: '1px solid #e5e7eb', borderRadius: '6px', padding: '8px' }}
                              itemStyle={{ padding: '2px 0' }}
                              cursor={{ fill: 'rgba(224, 231, 255, 0.2)' }}
                            />
                            <Bar 
                              dataKey="value" 
                              fill={selectedSource ? getSourceColor(selectedSource) : "#3b82f6"}
                              name={selectedSource || "All Sources"}
                              radius={[4, 4, 0, 0]}
                              label={{ 
                                position: 'top', 
                                fontSize: 11,
                                formatter: (value: number) => value > 0 ? value : '' 
                              }}
                              animationDuration={500}
                              isAnimationActive={true}
                            />
                            <Legend 
                              layout="horizontal"
                              verticalAlign="bottom"
                              align="center"
                              wrapperStyle={{ paddingTop: '15px' }}
                              iconType="circle"
                              iconSize={8}
                              payload={[
                                { value: selectedSource || 'All Sources', type: 'circle', color: selectedSource ? getSourceColor(selectedSource) : '#3b82f6' }
                              ]}
                            />
                          </RechartsBarChart>
                        </ResponsiveContainer>
                      </div>
                    </div>
                  );
                })()}
              </div>
            </details>
          </Card>
        )}

        {/* Search and Filters */}
        <div className="bg-white p-4 rounded-xl shadow-sm mb-6">
          <div className="flex flex-col md:flex-row items-stretch md:items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search deals by type, property, company..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="pl-10 bg-gray-50 border-gray-200 focus:bg-white"
              />
            </div>
            
            <div className="flex flex-wrap gap-3">
              <Select
                value={sortBy}
                onValueChange={(value) => {
                  setSortBy(value)
                  setPage(1)
                }}
              >
                <SelectTrigger className="w-[160px] bg-gray-50 border-gray-200">
                  <div className="flex items-center">
                    <ArrowUpDown className="h-3.5 w-3.5 mr-2 text-gray-500" />
                    <span>Sort by</span>
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date_desc">Newest First</SelectItem>
                  <SelectItem value="date_asc">Oldest First</SelectItem>
                  <SelectItem value="value_desc">Highest Value</SelectItem>
                  <SelectItem value="value_asc">Lowest Value</SelectItem>
                </SelectContent>
              </Select>
              
              <Select
                value={pageSize.toString()}
                onValueChange={(value) => {
                  setPageSize(Number(value))
                  setPage(1)
                }}
              >
                <SelectTrigger className="w-[140px] bg-gray-50 border-gray-200">
                  <div className="flex items-center">
                    <Filter className="h-3.5 w-3.5 mr-2 text-gray-500" />
                    <span>Per page</span>
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10 per page</SelectItem>
                  <SelectItem value="20">20 per page</SelectItem>
                  <SelectItem value="50">50 per page</SelectItem>
                  <SelectItem value="100">100 per page</SelectItem>
                </SelectContent>
              </Select>
              
              <div className="flex border rounded-md overflow-hidden">
                <Button 
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  className={`rounded-none px-3 ${viewMode === 'list' ? 'bg-blue-50 text-blue-700 border-blue-200' : ''}`}
                  onClick={() => setViewMode('list')}
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
                <Button 
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  className={`rounded-none px-3 ${viewMode === 'grid' ? 'bg-blue-50 text-blue-700 border-blue-200' : ''}`}
                  onClick={() => setViewMode('grid')}
                >
                  <div className="grid grid-cols-2 gap-0.5">
                    <div className="w-1.5 h-1.5 bg-current rounded-sm"></div>
                    <div className="w-1.5 h-1.5 bg-current rounded-sm"></div>
                    <div className="w-1.5 h-1.5 bg-current rounded-sm"></div>
                    <div className="w-1.5 h-1.5 bg-current rounded-sm"></div>
                  </div>
                </Button>
              </div>
            </div>
          </div>
        </div>


        {/* Deals List */}
        {loading ? (
          <div className="flex flex-col items-center justify-center py-20 bg-white rounded-xl shadow-sm">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-gray-500">Loading deals...</p>
          </div>
        ) : deals.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-20 bg-white rounded-xl shadow-sm">
            <Handshake className="h-16 w-16 text-gray-300 mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-1">No deals found</h3>
            <p className="text-gray-500 mb-6">Try changing your search criteria</p>
          </div>
        ) : (
          <>
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {deals.map((deal) => {
                  const displayDealType = getDisplayValue(deal.deal_type);
                  const dealTypeColorClass = getDealTypeColor(deal.deal_type);
                  const propertyTypeColorClass = getPropertyTypeColor(deal.property_type);
                  
                  // Safely handle property_type which might be an array or other type
                  const propertyTypeString = Array.isArray(deal.property_type) 
                    ? deal.property_type[0] || ''
                    : typeof deal.property_type === 'string' 
                      ? deal.property_type 
                      : '';
                  
                  // Ensure propertyTypeString is a string before calling substring
                  const safePropertyTypeString = String(propertyTypeString || '');
                  
                  const dealInitials = (displayDealType?.substring(0, 1) || 'D') + (safePropertyTypeString?.substring(0, 1) || 'P');
                  
                  return (
                    <Card 
                      key={deal.id} 
                      className="overflow-hidden hover:shadow-md transition-all duration-200 hover:translate-y-[-2px] cursor-pointer"
                      onClick={() => handleDealClick(deal.id)}
                    >
                      <div className="p-6">
                        <div className="flex items-center mb-4">
                          <Avatar className="h-12 w-12 rounded-lg mr-3">
                            <AvatarFallback className={dealTypeColorClass}>
                              {dealInitials}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h3 className="font-semibold text-gray-900 line-clamp-2 leading-tight">
                              {deal.news_title || `${displayDealType} Deal`}
                            </h3>
                            <div className="flex flex-wrap gap-2 mt-1">
                              {displayDealType && (
                                <Badge className={`${dealTypeColorClass}`}>
                                  {displayDealType}
                                </Badge>
                              )}
                              {/* Show multiple deal types if available */}
                              {getArrayValue(deal.deal_type).slice(1, 3).map((type, index) => (
                                <Badge key={index} className="bg-blue-100 text-blue-800 text-xs">
                                  {type}
                                </Badge>
                              ))}
                              {deal.property_type && (
                                <Badge className={`${propertyTypeColorClass}`}>
                                  {safePropertyTypeString}
                                </Badge>
                              )}
                              {deal.news_source && (
                                <Badge className={`bg-gray-100 text-gray-800`}>
                                  {deal.news_source}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 mb-4">
                          {deal.deal_date && (
                            <div className="flex items-center">
                              <Calendar className="h-3.5 w-3.5 mr-1.5 text-gray-400" />
                              <span>{formatDate(deal.deal_date)}</span>
                            </div>
                          )}
                          
                          {deal.deal_value > 0 && (
                            <div className="flex items-center">
                              <DollarSign className="h-3.5 w-3.5 mr-1.5 text-gray-400" />
                              <span>{deal.deal_value.toLocaleString('en-US', {
                                style: 'currency',
                                currency: deal.currency || 'USD',
                                maximumFractionDigits: 0
                              })}</span>
                            </div>
                          )}
                          
                          {deal.property_address && (
                            <div className="flex items-center col-span-2">
                              <MapPin className="h-3.5 w-3.5 mr-1.5 text-gray-400" />
                              <span className="truncate">{deal.property_address}</span>
                            </div>
                          )}
                          
                          {deal.cap_rate > 0 && (
                            <div className="flex items-center">
                              <Percent className="h-3.5 w-3.5 mr-1.5 text-gray-400" />
                              <span>Cap Rate: {deal.cap_rate}%</span>
                            </div>
                          )}
                          
                          {deal.companies && deal.companies.length > 0 && (
                            <div className="flex items-center col-span-2">
                              <Building2 className="h-3.5 w-3.5 mr-1.5 text-gray-400" />
                              <span className="truncate">
                                {deal.companies.map(c => c.company_name).join(', ')}
                              </span>
                            </div>
                          )}
                          
                          {deal.created_at && (
                            <div className="flex items-center col-span-2 mt-1 text-gray-400">
                              <Clock className="h-3.5 w-3.5 mr-1.5" />
                              <span>Created: {formatDateTime(deal.created_at)}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className={`h-1.5 ${getSourceColor(deal.news_source)}`}></div>
                    </Card>
                  );
                })}
              </div>
            ) : (
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="divide-y">
                  {deals.map((deal) => {
                    const displayDealType = getDisplayValue(deal.deal_type);
                    const dealTypeColorClass = getDealTypeColor(deal.deal_type);
                    const propertyTypeColorClass = getPropertyTypeColor(deal.property_type);
                    
                    // Safely handle property_type which might be an array or other type
                    const propertyTypeString = Array.isArray(deal.property_type) 
                      ? deal.property_type[0] || ''
                      : typeof deal.property_type === 'string' 
                        ? deal.property_type 
                        : '';
                    
                    // Ensure propertyTypeString is a string before calling substring
                    const safePropertyTypeString = String(propertyTypeString || '');
                    
                    const dealInitials = (displayDealType?.substring(0, 1) || 'D') + (safePropertyTypeString?.substring(0, 1) || 'P');
                    
                    return (
                      <div 
                        key={deal.id} 
                        className="p-4 hover:bg-gray-50 cursor-pointer transition-colors duration-200 flex items-center"
                        onClick={() => handleDealClick(deal.id)}
                      >
                        <Avatar className="h-10 w-10 rounded-lg mr-4 flex-shrink-0">
                          <AvatarFallback className={dealTypeColorClass}>
                            {dealInitials}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="min-w-0 flex-1">
                          <div className="flex items-center mb-1">
                            <h3 className="font-medium text-gray-900 truncate mr-2">
                              {deal.news_title || `${displayDealType} Deal`}
                            </h3>
                            {deal.deal_status && (
                              <Badge className="bg-gray-100 text-gray-800">
                                {deal.deal_status}
                              </Badge>
                            )}
                          </div>
                          
                          <div className="flex flex-wrap items-center gap-x-4 gap-y-1 mb-1">
                            {displayDealType && (
                              <Badge className={`${dealTypeColorClass}`}>
                                {displayDealType}
                              </Badge>
                            )}
                            {/* Show additional deal types */}
                            {getArrayValue(deal.deal_type).slice(1, 2).map((type, index) => (
                              <Badge key={index} className="bg-blue-100 text-blue-800">
                                {type}
                              </Badge>
                            ))}
                            {deal.property_type && (
                              <Badge className={`${propertyTypeColorClass}`}>
                                {safePropertyTypeString}
                              </Badge>
                            )}
                            {deal.news_source && (
                              <Badge className={`bg-gray-100 text-gray-800`}>
                                {deal.news_source}
                              </Badge>
                            )}
                          </div>
                          
                          <div className="flex flex-wrap items-center gap-x-4 gap-y-1 text-xs text-gray-500">
                            {deal.deal_date && (
                              <div className="flex items-center">
                                <Calendar className="h-3.5 w-3.5 mr-1 text-gray-400" />
                                <span>{formatDate(deal.deal_date)}</span>
                              </div>
                            )}
                            
                            {deal.deal_value > 0 && (
                              <div className="flex items-center">
                                <DollarSign className="h-3.5 w-3.5 mr-1 text-gray-400" />
                                <span>{deal.deal_value.toLocaleString('en-US', {
                                  style: 'currency',
                                  currency: deal.currency || 'USD',
                                  maximumFractionDigits: 0
                                })}</span>
                              </div>
                            )}
                            
                            {deal.companies && deal.companies.length > 0 && (
                              <div className="flex items-center">
                                <Building2 className="h-3.5 w-3.5 mr-1 text-gray-400" />
                                <span className="truncate">
                                  {deal.companies.map(c => c.company_name).join(', ')}
                                </span>
                              </div>
                            )}
                            
                            {deal.created_at && (
                              <div className="flex items-center">
                                <Clock className="h-3.5 w-3.5 mr-1 text-gray-400" />
                                <span>Created: {formatDateTime(deal.created_at)}</span>
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <Button variant="ghost" size="icon" className="flex-shrink-0 text-gray-400 ml-4">
                          <ArrowRight className="h-4 w-4" />
                        </Button>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Pagination Controls */}
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-500">
                Page {page} of {totalPages}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setPage(p => Math.max(1, p - 1))}
                  disabled={page === 1}
                  className="border-gray-200 bg-white"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                  disabled={page === totalPages}
                  className="border-gray-200 bg-white"
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}