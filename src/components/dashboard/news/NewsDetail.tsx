'use client'

import { useState, useEffect } from 'react'
import { 
  Newspaper, Building2, Calendar, 
  MapPin, Globe, ArrowLeft, FileText, User,
  Clock, CheckCircle, XCircle, Tag, ExternalLink, Handshake,
  ChevronDown, ChevronUp, DollarSign, Flag, BarChart3, Percent,
  TrendingUp, AlertCircle, MapPin as Location, Target, Zap
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface NewsDetailProps {
  id: string;
  onBack: () => void;
}

interface Company {
  id: number;
  news_id: number;
  deal_id: number | null;
  company_name: string;
  role: string;
  confidence_score: number;
  participation_details: any;
  created_at: string;
  company_context: string;
}

interface Person {
  id: number;
  news_id: number;
  deal_id: number | null;
  company_id: number | null;
  person_name: string;
  title: string;
  role: string;
  confidence_score: number;
  involvement_details: any;
  created_at: string;
  person_context: string;
  company_name: string;
}

interface Deal {
  id: number;
  news_id: number;
  deal_type: string[] | string; // Handle both array and string formats
  deal_status: string;
  deal_date: string;
  deal_value: number;
  currency: string;
  cap_rate: number;
  ltv: number;
  confidence_score: number;
  property_type: string;
  property_size: number;
  property_size_unit: string;
  property_address: string;
  deal_details: any;
  created_at: string;
  property_name: string;
  location_details: any; // Changed to any to handle object format
  deal_type_normalized: string[] | string; // Handle array format
  capital_position: any;
  strategies: any;
  property_types: any;
  property_subcategories: any;
  square_feet: number;
  units: number;
  participant_companies: any;
  deal_type_normalized_specialty: string;
}

interface NewsDetails {
  id: number;
  news_source: string;
  url: string;
  news_text: string;
  news_date: string;
  created_at: string;
  updated_at: string;
  news_title: string;
  raw_html: string;
  is_relevant: boolean;
  enriched: boolean;
  fetched: boolean;
  bad_url: boolean;
  fetch_error: string | null;
  enrichment_error: string | null;
  enrichment_status: string | null;
  deals: Deal[];
  companies: Company[];
  persons: Person[];
}

export default function NewsDetail({ id, onBack }: NewsDetailProps) {
  const [news, setNews] = useState<NewsDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'content' | 'raw'>('content')
  const [fetchingHtml, setFetchingHtml] = useState(false)
  const [fetchMessage, setFetchMessage] = useState<string | null>(null)
  const [markingBadUrl, setMarkingBadUrl] = useState(false)
  const [markBadUrlMessage, setMarkBadUrlMessage] = useState<string | null>(null)
  
  // Add collapsible sections state
  const [sections, setSections] = useState({
    overview: true,
    processing: false,
    content: true,
    deals: true,
    companies: false,
    persons: false,
    market: false,
    technical: false
  })

  // Toggle section visibility
  const toggleSection = (section: keyof typeof sections) => {
    setSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  useEffect(() => {
    const fetchNewsDetails = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/deal-news/${id}`)
        
        if (!response.ok) {
          throw new Error(`Error ${response.status}: ${response.statusText}`)
        }
        
        const data = await response.json()
        // console.log(`News details: ${JSON.stringify(data, null, 2)}`)
        setNews(data)
      } catch (err) {
        console.error('Error fetching news details:', err)
        setError('Failed to load news details. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      fetchNewsDetails()
    }
  }, [id])

  // Format a date string
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Unknown'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Format a date with time string
  const formatDateTime = (dateString: string) => {
    if (!dateString) return 'Unknown'
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Format percentage
  const formatPercentage = (value: number | null) => {
    if (value === null || value === undefined) return 'N/A'
    return `${(value * 100).toFixed(2)}%`
  }

  // Format currency
  const formatCurrency = (value: number | null) => {
    if (value === null || value === undefined) return 'N/A'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value)
  }

  // Utility function to extract first value from JSONB array or return string directly
  const getDisplayValue = (value: string[] | string | null | undefined): string => {
    if (!value) return '';
    if (Array.isArray(value)) {
      return value.length > 0 ? value[0] : '';
    }
    return value;
  };

  // Utility function to get all values from JSONB array or single string as array
  const getArrayValue = (value: string[] | string | null | undefined): string[] => {
    if (!value) return [];
    if (Array.isArray(value)) {
      return value;
    }
    return [value];
  };

  // Function to mark URL as bad
  const markUrlAsBad = async () => {
    if (!news?.id) return
    
    try {
      setMarkingBadUrl(true)
      setMarkBadUrlMessage(null)
      
      const response = await fetch(`/api/deal-news/${news.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'mark_bad_url' })
      })

      const result = await response.json()
      
      if (response.ok && result.success) {
        setMarkBadUrlMessage('URL has been marked as bad and will be excluded from future processing.')
        // Update the news object to reflect the change
        setNews(prev => prev ? { ...prev, bad_url: true } : null)
      } else {
        throw new Error(result.error || 'Failed to mark URL as bad')
      }
    } catch (error) {
      console.error('Error marking URL as bad:', error)
      setMarkBadUrlMessage('Failed to mark URL as bad. Please try again.')
    } finally {
      setMarkingBadUrl(false)
    }
  }

  // Get a color based on deal type
  const getDealTypeColor = (dealType: string[] | string): string => {
    const displayType = getDisplayValue(dealType);
    const colors: Record<string, string> = {
      'Acquisition': 'bg-blue-500 text-white',
      'Sale': 'bg-green-500 text-white', 
      'Financing': 'bg-purple-500 text-white',
      'Refinancing': 'bg-indigo-500 text-white',
      'Investment': 'bg-orange-500 text-white',
      'Development': 'bg-red-500 text-white',
      'Lease': 'bg-yellow-500 text-black',
      'Joint Venture': 'bg-teal-500 text-white',
      'default': 'bg-gray-500 text-white'
    };
    
    return colors[displayType] || colors.default;
  }

  // Get a color based on source
  const getSourceColor = (source: string): string => {
    const colors: Record<string, string> = {
      'bisnow': 'bg-blue-100 text-blue-800',
      'therealdeal': 'bg-green-100 text-green-800',
      'pincus': 'bg-purple-100 text-purple-800',
      'globest': 'bg-yellow-100 text-yellow-800',
      'default': 'bg-gray-100 text-gray-800'
    }
    
    return colors[source.toLowerCase()] || colors.default
  }

  // Get status color
  const getStatusColor = (status: string): string => {
    const colors: Record<string, string> = {
      'completed': 'bg-green-100 text-green-800',
      'pending': 'bg-yellow-100 text-yellow-800',
      'failed': 'bg-red-100 text-red-800',
      'processing': 'bg-blue-100 text-blue-800',
      'default': 'bg-gray-100 text-gray-800'
    }
    
    return colors[status?.toLowerCase()] || colors.default
  }

  // Get sentiment color
  const getSentimentColor = (sentiment: string): string => {
    const colors: Record<string, string> = {
      'positive': 'bg-green-100 text-green-800',
      'negative': 'bg-red-100 text-red-800',
      'neutral': 'bg-gray-100 text-gray-800',
      'default': 'bg-gray-100 text-gray-800'
    }
    
    return colors[sentiment?.toLowerCase()] || colors.default
  }

  // Handle HTML fetch using the existing processing API
  const handleFetchHtml = async () => {
    if (!news) return
    
    try {
      setFetchingHtml(true)
      setFetchMessage(null)
      
      const response = await fetch('/api/processing/trigger', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'execute_manual',
          stage: 'news_fetch',
          options: {
            singleId: news.id
          }
        })
      })
      
      const data = await response.json()
      
      if (response.ok && data.success) {
        setFetchMessage('HTML fetch completed successfully! Refreshing data...')
        
        // Refresh the news data after successful fetch
        setTimeout(async () => {
          try {
            const newsResponse = await fetch(`/api/deal-news/${id}`)
            if (newsResponse.ok) {
              const updatedNews = await newsResponse.json()
              setNews(updatedNews)
            }
          } catch (refreshError) {
            console.error('Error refreshing news data:', refreshError)
          }
          setFetchMessage(null)
        }, 3000) // Increased timeout for processing
      } else {
        setFetchMessage(`Error: ${data.error || 'Processing failed'}`)
      }
    } catch (error) {
      console.error('Error fetching HTML:', error)
      setFetchMessage('Failed to fetch HTML content')
    } finally {
      setFetchingHtml(false)
    }
  }

  // Section header component
  const SectionHeader = ({ title, icon, isOpen, onToggle, count }: { 
    title: string; 
    icon: React.ReactNode; 
    isOpen: boolean; 
    onToggle: () => void;
    count?: number;
  }) => (
    <div 
      className="flex items-center justify-between cursor-pointer py-3 px-3 bg-gray-50 rounded-t-md" 
      onClick={onToggle}
    >
      <div className="flex items-center">
        {icon}
        <h3 className="text-base font-medium text-gray-800 ml-2">
          {title} {count !== undefined && <span className="text-sm text-gray-500">({count})</span>}
        </h3>
      </div>
      {isOpen ? <ChevronUp className="h-4 w-4 text-gray-500" /> : <ChevronDown className="h-4 w-4 text-gray-500" />}
    </div>
  )

  return (
    <div className="bg-white rounded-lg border overflow-hidden">
      {/* Top header */}
      <div className="border-b px-4 py-3 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-lg font-bold text-gray-900 line-clamp-2">
              {news?.news_title || 'News Details'}
            </h1>
            <div className="flex flex-wrap gap-2 mt-2 mb-1">
              {news?.news_source && (
                <Badge className={getSourceColor(news.news_source)}>
                  {news.news_source}
                </Badge>
              )}
              {news?.is_relevant !== undefined && (
                <Badge variant="outline" className={news.is_relevant ? "border-green-500 text-green-600" : "border-gray-300 text-gray-600"}>
                  {news.is_relevant ? 'Relevant' : 'Not Relevant'}
                </Badge>
              )}

              {news?.enrichment_status && (
                <Badge className={getStatusColor(news.enrichment_status)}>
                  {news.enrichment_status}
                </Badge>
              )}
              {news?.deals && news.deals.length > 0 && (
                <Badge variant="outline" className="border-purple-500 text-purple-600">
                  {news.deals.length} Deal{news.deals.length !== 1 ? 's' : ''}
                </Badge>
              )}
            </div>
          </div>
          <Button 
            variant="ghost" 
            size="sm"
            onClick={onBack}
            className="h-8 w-8 p-0"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {/* Loading, error states and main content */}
      {loading ? (
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : error || !news ? (
        <div className="flex flex-col items-center justify-center p-8">
          <Newspaper className="h-12 w-12 text-gray-300 mb-4" />
          <h3 className="text-base font-medium text-gray-900 mb-2">News Not Found</h3>
          <p className="text-sm text-gray-500 mb-4">{error || 'Unable to load the requested news item.'}</p>
          <Button 
            onClick={onBack}
            variant="outline"
            size="sm"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>
      ) : (
        <div className="divide-y">
          {/* Overview Section */}
          <div className="mb-2">
            <SectionHeader 
              title="Overview" 
              icon={<Newspaper className="h-4 w-4 text-blue-600" />} 
              isOpen={sections.overview} 
              onToggle={() => toggleSection('overview')} 
            />
            
            {sections.overview && (
              <div className="p-3">
                <div className="grid grid-cols-2 gap-3">
                  {news.news_date && (
                    <div className="bg-gray-50 p-2 rounded">
                      <p className="text-xs text-gray-500">Publication</p>
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1 text-gray-500" />
                        <p className="text-sm font-medium">{formatDate(news.news_date)}</p>
                      </div>
                    </div>
                  )}
                  
                  {news.created_at && (
                    <div className="bg-gray-50 p-2 rounded">
                      <p className="text-xs text-gray-500">Added</p>
                      <div className="flex items-center">
                        <Clock className="h-3 w-3 mr-1 text-gray-500" />
                        <p className="text-sm font-medium">{formatDateTime(news.created_at).split(',')[0]}</p>
                      </div>
                    </div>
                  )}
                  

                  
                  <div className="bg-gray-50 p-2 rounded">
                    <p className="text-xs text-gray-500">Status</p>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center">
                        <Flag className="h-3 w-3 mr-1 text-gray-500" />
                        <p className="text-sm font-medium">{news.is_relevant ? "Relevant" : "Not Relevant"}</p>
                      </div>
                      {news.is_relevant ? (
                        <CheckCircle className="h-3 w-3 text-green-500" />
                      ) : (
                        <XCircle className="h-3 w-3 text-gray-400" />
                      )}
                    </div>
                  </div>

                </div>

                {news.url && (
                  <div className="mt-3">
                    <a 
                      href={news.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-xs text-blue-600 hover:underline inline-flex items-center"
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      View Original Article
                    </a>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Processing Status Section */}
          <div className="mb-2">
            <SectionHeader 
              title="Processing Status" 
              icon={<Zap className="h-4 w-4 text-blue-600" />} 
              isOpen={sections.processing} 
              onToggle={() => toggleSection('processing')} 
            />
            
            {sections.processing && (
              <div className="p-3">
                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-gray-50 p-2 rounded">
                    <p className="text-xs text-gray-500">Fetched</p>
                    <div className="flex items-center">
                      {news.fetched ? (
                        <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                      ) : (
                        <XCircle className="h-3 w-3 mr-1 text-red-500" />
                      )}
                      <p className="text-sm font-medium">{news.fetched ? 'Yes' : 'No'}</p>
                    </div>
                  </div>

                  <div className="bg-gray-50 p-2 rounded">
                    <p className="text-xs text-gray-500">Enriched</p>
                    <div className="flex items-center">
                      {news.enriched ? (
                        <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                      ) : (
                        <XCircle className="h-3 w-3 mr-1 text-red-500" />
                      )}
                      <p className="text-sm font-medium">{news.enriched ? 'Yes' : 'No'}</p>
                    </div>
                  </div>

                  <div className="bg-gray-50 p-2 rounded">
                    <p className="text-xs text-gray-500">Has Deals</p>
                    <div className="flex items-center">
                      {news.deals && news.deals.length > 0 ? (
                        <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                      ) : (
                        <XCircle className="h-3 w-3 mr-1 text-red-500" />
                      )}
                      <p className="text-sm font-medium">{news.deals && news.deals.length > 0 ? `Yes (${news.deals.length})` : 'No'}</p>
                    </div>
                  </div>
                </div>

                {news.updated_at && (
                  <div className="mt-3">
                    <p className="text-xs text-gray-500">Last Updated</p>
                    <p className="text-sm font-medium">{formatDateTime(news.updated_at)}</p>
                  </div>
                )}

                {(news.fetch_error || news.enrichment_error) && (
                  <div className="mt-3">
                    <p className="text-xs text-red-500 mb-2">Errors</p>
                    {news.fetch_error && (
                      <div className="bg-red-50 p-2 rounded mb-2">
                        <p className="text-xs text-red-600">Fetch Error: {news.fetch_error}</p>
                      </div>
                    )}
                    {news.enrichment_error && (
                      <div className="bg-red-50 p-2 rounded">
                        <p className="text-xs text-red-600">Enrichment Error: {news.enrichment_error}</p>
                      </div>
                    )}
                  </div>
                )}

                {/* HTML Fetch Action */}
                <div className="mt-3 pt-3 border-t">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs text-gray-500 mb-1">HTML Content Fetcher</p>
                      <p className="text-xs text-gray-400">Re-fetch and extract HTML content, title, and text</p>
                    </div>
                    <Button
                      onClick={handleFetchHtml}
                      disabled={fetchingHtml}
                      size="sm"
                      className="h-8 text-xs"
                    >
                      {fetchingHtml ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border-t-2 border-b-2 border-white mr-2"></div>
                          Fetching...
                        </>
                      ) : (
                        <>
                          <Globe className="h-3 w-3 mr-1" />
                          Fetch HTML
                        </>
                      )}
                    </Button>
                  </div>
                  
                  {fetchMessage && (
                    <div className={`mt-2 p-2 rounded text-xs ${
                      fetchMessage.includes('Error') 
                        ? 'bg-red-50 text-red-600' 
                        : 'bg-green-50 text-green-600'
                    }`}>
                      {fetchMessage}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          
          {/* Content Section */}
          <div>
            <SectionHeader 
              title="News Content" 
              icon={<FileText className="h-4 w-4 text-blue-600" />} 
              isOpen={sections.content} 
              onToggle={() => toggleSection('content')} 
            />
            
            {sections.content && (
              <div className="p-3">
                <div className="flex justify-end mb-2">
                  <div className="inline-flex rounded-md shadow-sm text-xs">
                    <button
                      type="button"
                      className={`px-3 py-1 rounded-l-md border ${viewMode === 'content' 
                        ? 'bg-blue-50 border-blue-200 text-blue-700' 
                        : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'}`}
                      onClick={() => setViewMode('content')}
                    >
                      Content
                    </button>
                    <button
                      type="button"
                      className={`px-3 py-1 rounded-r-md border-t border-r border-b ${viewMode === 'raw' 
                        ? 'bg-blue-50 border-blue-200 text-blue-700' 
                        : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'}`}
                      onClick={() => setViewMode('raw')}
                    >
                      Raw HTML
                    </button>
                  </div>
                </div>
                
                <div className="bg-gray-50 p-3 rounded-md max-h-[300px] overflow-y-auto">
                  {viewMode === 'content' ? (
                    <div className="prose prose-sm max-w-none">
                      {news.news_text ? (
                        news.news_text.split('\n').map((paragraph, idx) => (
                          <p key={idx} className="text-sm mb-2">{paragraph}</p>
                        ))
                      ) : (
                        <p className="text-sm text-gray-500 italic">No content available</p>
                      )}
                    </div>
                  ) : (
                    <pre className="text-xs font-mono whitespace-pre-wrap">
                      {news.raw_html || 'No raw HTML available'}
                    </pre>
                  )}
                </div>
              </div>
            )}
          </div>
          

          
          {/* Deals Section */}
          <div>
            <SectionHeader 
              title="Extracted Deals" 
              icon={<Handshake className="h-4 w-4 text-blue-600" />} 
              isOpen={sections.deals} 
              onToggle={() => toggleSection('deals')} 
              count={news.deals.length}
            />
            
            {sections.deals && (
              <div className="p-3">
                {news.deals.length === 0 ? (
                  <div className="bg-gray-50 p-3 rounded-md text-center">
                    <p className="text-sm text-gray-500">No deals have been extracted from this article</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {news.deals.map((deal) => (
                      <div key={deal.id} className="bg-gray-50 p-3 rounded-lg">
                        <div className="flex flex-wrap gap-1 mb-2">
                          <Badge className={getDealTypeColor(deal.deal_type || 'default')}>
                            {getDisplayValue(deal.deal_type) || 'Unknown Type'}
                          </Badge>
                          {/* Show additional deal types if available */}
                          {getArrayValue(deal.deal_type).slice(1, 3).map((type, index) => (
                            <Badge key={index} className="bg-blue-100 text-blue-800 text-xs">
                              {type}
                            </Badge>
                          ))}
                          {getDisplayValue(deal.deal_type_normalized) && getDisplayValue(deal.deal_type_normalized) !== getDisplayValue(deal.deal_type) && (
                            <Badge className="bg-green-100 text-green-800 text-xs">
                              {getDisplayValue(deal.deal_type_normalized)}
                            </Badge>
                          )}
                          {deal.deal_status && (
                            <Badge variant="outline">
                              {deal.deal_status}
                            </Badge>
                          )}
                        </div>
                        
                        <div className="flex items-center mb-2 text-xs">
                          <DollarSign className="h-3 w-3 mr-1 text-gray-400" />
                          <span className="font-medium text-gray-900">
                            {formatCurrency(deal.deal_value)}
                          </span>
                        </div>

                        {deal.property_type && (
                          <p className="text-xs text-gray-600 mb-1">
                            Type: {deal.property_type}
                          </p>
                        )}
                        
                        {deal.property_address && (
                          <p className="text-xs text-gray-600 mb-1 flex items-center">
                            <MapPin className="inline-block h-3 w-3 mr-1 text-gray-400" />
                            {deal.property_address}
                          </p>
                        )}
                        
                        {/* Deal details grid */}
                        <div className="grid grid-cols-2 gap-2 mt-2 text-xs">
                          {deal.deal_date && (
                            <div className="bg-white p-2 rounded border">
                              <span className="text-gray-500">Deal Date:</span>
                              <div className="flex items-center mt-1">
                                <Calendar className="h-3 w-3 mr-1 text-gray-400" />
                                <span className="font-medium">{formatDate(deal.deal_date)}</span>
                              </div>
                            </div>
                          )}
                          
                          {deal.deal_status && (
                            <div className="bg-white p-2 rounded border">
                              <span className="text-gray-500">Deal Status:</span>
                              <div className="font-medium mt-1">{deal.deal_status}</div>
                            </div>
                          )}
                          
                          {deal.property_size > 0 && (
                            <div className="bg-white p-2 rounded border">
                              <span className="text-gray-500">Property Size:</span>
                              <div className="font-medium mt-1">
                                {deal.property_size.toLocaleString()} {deal.property_size_unit || 'sq ft'}
                              </div>
                            </div>
                          )}
                          
                          {deal.square_feet > 0 && (
                            <div className="bg-white p-2 rounded border">
                              <span className="text-gray-500">Square Feet:</span>
                              <div className="font-medium mt-1">
                                {deal.square_feet.toLocaleString()} sq ft
                              </div>
                            </div>
                          )}
                          
                          {deal.units > 0 && (
                            <div className="bg-white p-2 rounded border">
                              <span className="text-gray-500">Units:</span>
                              <div className="font-medium mt-1">
                                {deal.units.toLocaleString()}
                              </div>
                            </div>
                          )}
                          
                          {deal.cap_rate > 0 && (
                            <div className="bg-white p-2 rounded border">
                              <span className="text-gray-500">Cap Rate:</span>
                              <div className="flex items-center mt-1">
                                <Percent className="h-3 w-3 mr-1 text-gray-400" />
                                <span className="font-medium">{(deal.cap_rate * 100).toFixed(2)}%</span>
                              </div>
                            </div>
                          )}
                          
                          {deal.ltv > 0 && (
                            <div className="bg-white p-2 rounded border">
                              <span className="text-gray-500">LTV Ratio:</span>
                              <div className="flex items-center mt-1">
                                <BarChart3 className="h-3 w-3 mr-1 text-gray-400" />
                                <span className="font-medium">{(deal.ltv * 100).toFixed(2)}%</span>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Location details section - handle new object format */}
                        {deal.location_details && (
                          <div className="bg-white p-2 rounded border col-span-2 mt-2">
                            <span className="text-gray-500">Location Details:</span>
                            <div className="font-medium mt-1">
                              {typeof deal.location_details === 'object' ? (
                                <div className="space-y-1">
                                  {deal.location_details.city && (
                                    <div className="text-xs">City: {deal.location_details.city}</div>
                                  )}
                                  {deal.location_details.state && (
                                    <div className="text-xs">State: {deal.location_details.state}</div>
                                  )}
                                  {deal.location_details.neighborhood && (
                                    <div className="text-xs">Neighborhood: {deal.location_details.neighborhood}</div>
                                  )}
                                  {deal.location_details.zipCode && (
                                    <div className="text-xs">ZIP: {deal.location_details.zipCode}</div>
                                  )}
                                </div>
                              ) : (
                                deal.location_details
                              )}
                            </div>
                          </div>
                        )}

                        {/* Participant companies section - handle new object format */}
                        {deal.participant_companies && typeof deal.participant_companies === 'object' && (
                          <div className="bg-white p-2 rounded border col-span-2 mt-2">
                            <span className="text-gray-500">Participants:</span>
                            <div className="mt-1 space-y-1">
                              {deal.participant_companies.buyers && getArrayValue(deal.participant_companies.buyers).length > 0 && (
                                <div className="text-xs">
                                  <span className="font-medium">Buyers:</span> {getArrayValue(deal.participant_companies.buyers).join(', ')}
                                </div>
                              )}
                              {deal.participant_companies.sellers && getArrayValue(deal.participant_companies.sellers).length > 0 && (
                                <div className="text-xs">
                                  <span className="font-medium">Sellers:</span> {getArrayValue(deal.participant_companies.sellers).join(', ')}
                                </div>
                              )}
                              {deal.participant_companies.lenders && getArrayValue(deal.participant_companies.lenders).length > 0 && (
                                <div className="text-xs">
                                  <span className="font-medium">Lenders:</span> {getArrayValue(deal.participant_companies.lenders).join(', ')}
                                </div>
                              )}
                              {deal.participant_companies.brokers && getArrayValue(deal.participant_companies.brokers).length > 0 && (
                                <div className="text-xs">
                                  <span className="font-medium">Brokers:</span> {getArrayValue(deal.participant_companies.brokers).join(', ')}
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        {/* Strategies section - handle array format */}
                        {deal.strategies && getArrayValue(deal.strategies).length > 0 && (
                          <div className="bg-white p-2 rounded border col-span-2 mt-2">
                            <span className="text-gray-500">Strategies:</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {getArrayValue(deal.strategies).map((strategy, index) => (
                                <Badge key={index} className="bg-purple-100 text-purple-800 text-xs">
                                  {strategy}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {/* Property Types */}
                        {deal.property_types && Array.isArray(deal.property_types) && deal.property_types.length > 0 && (
                          <div className="mt-2">
                            <p className="text-xs text-gray-500 mb-1">Property Types:</p>
                            <div className="flex flex-wrap gap-1">
                              {deal.property_types.map((type, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs">
                                  {type}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {/* Property Subcategories */}
                        {deal.property_subcategories && Array.isArray(deal.property_subcategories) && deal.property_subcategories.length > 0 && (
                          <div className="mt-2">
                            <p className="text-xs text-gray-500 mb-1">Property Subcategories:</p>
                            <div className="flex flex-wrap gap-1">
                              {deal.property_subcategories.map((subcat, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs">
                                  {subcat}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {/* Capital Position */}
                        {deal.capital_position && Array.isArray(deal.capital_position) && deal.capital_position.length > 0 && (
                          <div className="mt-2">
                            <p className="text-xs text-gray-500 mb-1">Capital Position:</p>
                            <div className="flex flex-wrap gap-1">
                              {deal.capital_position.map((position, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs text-purple-600">
                                  {position}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {/* Deal Details JSON */}
                        {deal.deal_details && (
                          <div className="mt-2">
                            <p className="text-xs text-gray-500 mb-1">Additional Deal Details:</p>
                            <div className="bg-white p-2 rounded border text-xs">
                              <pre className="text-xs font-mono whitespace-pre-wrap max-h-32 overflow-y-auto">
                                {JSON.stringify(deal.deal_details, null, 2)}
                              </pre>
                            </div>
                          </div>
                        )}
                        
                        {/* Deal ID and News ID for reference */}
                        {/* <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                          <div className="bg-white p-2 rounded border">
                            <span className="text-gray-500">Deal ID:</span>
                            <div className="font-medium mt-1">{deal.id}</div>
                          </div>
                          <div className="bg-white p-2 rounded border">
                            <span className="text-gray-500">News ID:</span>
                            <div className="font-medium mt-1">{deal.news_id}</div>
                          </div>
                        </div> */}
                        
                        {deal.confidence_score > 0 && (
                          <div className="mt-2 flex items-center">
                            <span className="text-xs text-gray-500 mr-2">Extraction Confidence:</span>
                            <div className="w-full max-w-[120px] bg-gray-200 rounded-full h-1.5 mr-2">
                              <div 
                                className="bg-blue-600 h-1.5 rounded-full" 
                                style={{ width: `${Math.round(deal.confidence_score * 100)}%` }}
                              ></div>
                            </div>
                            <span className="text-xs text-gray-600">{Math.round(deal.confidence_score * 100)}%</span>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* Companies Section */}
          <div>
            <SectionHeader 
              title="Companies" 
              icon={<Building2 className="h-4 w-4 text-blue-600" />} 
              isOpen={sections.companies} 
              onToggle={() => toggleSection('companies')} 
              count={news.companies.length}
            />
            
            {sections.companies && (
              <div className="p-3">
                {news.companies.length === 0 ? (
                  <div className="bg-gray-50 p-3 rounded-md text-center">
                    <p className="text-sm text-gray-500">No companies have been extracted from this article</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {news.companies.map((company) => (
                      <div key={company.id} className="bg-gray-50 p-3 rounded-lg">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="text-sm font-medium text-gray-900">{company.company_name}</h3>
                          {company.role && (
                            <Badge variant="outline" className="text-xs">{company.role}</Badge>
                          )}
                        </div>
                        
                        {/* <div className="grid grid-cols-2 gap-2 text-xs mb-2">
                          <div className="bg-white p-2 rounded border">
                            <span className="text-gray-500">Company ID:</span>
                            <div className="font-medium mt-1">{company.id}</div>
                          </div>
                          <div className="bg-white p-2 rounded border">
                            <span className="text-gray-500">News ID:</span>
                            <div className="font-medium mt-1">{company.news_id}</div>
                          </div>
                        </div> */}
                        
                        {/* {company.deal_id && (
                          <div className="bg-white p-2 rounded border mb-2 text-xs">
                            <span className="text-gray-500">Associated Deal ID:</span>
                            <div className="font-medium mt-1">{company.deal_id}</div>
                          </div>
                        )} */}
                        
                        {company.company_context && (
                          <div className="mb-2">
                            <p className="text-xs text-gray-500 mb-1">Context:</p>
                            <p className="text-xs text-gray-700 bg-white p-2 rounded border">{company.company_context}</p>
                          </div>
                        )}
                        
                        {/* Participation Details JSON
                        {company.participation_details && (
                          <div className="mb-2">
                            <p className="text-xs text-gray-500 mb-1">Participation Details:</p>
                            <div className="bg-white p-2 rounded border text-xs">
                              <pre className="text-xs font-mono whitespace-pre-wrap max-h-32 overflow-y-auto">
                                {JSON.stringify(company.participation_details, null, 2)}
                              </pre>
                            </div>
                          </div>
                        )} */}
                        
                        <div className="grid grid-cols-2 gap-2 text-xs mb-2">
                          {/* <div className="bg-white p-2 rounded border">
                            <span className="text-gray-500">Created:</span>
                            <div className="font-medium mt-1">{formatDateTime(company.created_at)}</div>
                          </div> */}
                          {/* {company.confidence_score > 0 && (
                            <div className="bg-white p-2 rounded border">
                              <span className="text-gray-500">Confidence:</span>
                              <div className="flex items-center mt-1">
                                <div className="w-full max-w-[60px] bg-gray-200 rounded-full h-1.5 mr-2">
                                  <div 
                                    className="bg-blue-600 h-1.5 rounded-full" 
                                    style={{ width: `${Math.round(company.confidence_score * 100)}%` }}
                                  ></div>
                                </div>
                                <span className="text-xs text-gray-600">{Math.round(company.confidence_score * 100)}%</span>
                              </div>
                            </div>
                          )} */}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* Persons Section */}
          <div>
            <SectionHeader 
              title="People" 
              icon={<User className="h-4 w-4 text-blue-600" />} 
              isOpen={sections.persons} 
              onToggle={() => toggleSection('persons')} 
              count={news.persons.length}
            />
            
            {sections.persons && (
              <div className="p-3">
                {news.persons.length === 0 ? (
                  <div className="bg-gray-50 p-3 rounded-md text-center">
                    <p className="text-sm text-gray-500">No people have been extracted from this article</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {news.persons.map((person) => (
                      <div key={person.id} className="bg-gray-50 p-3 rounded-lg">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h3 className="text-sm font-medium text-gray-900">{person.person_name}</h3>
                            {person.title && (
                              <p className="text-xs text-gray-600">{person.title}</p>
                            )}
                          </div>
                          {person.role && (
                            <Badge variant="outline" className="text-xs">{person.role}</Badge>
                          )}
                        </div>
                        
                        {/* <div className="grid grid-cols-2 gap-2 text-xs mb-2">
                          <div className="bg-white p-2 rounded border">
                            <span className="text-gray-500">Person ID:</span>
                            <div className="font-medium mt-1">{person.id}</div>
                          </div>
                          <div className="bg-white p-2 rounded border">
                            <span className="text-gray-500">News ID:</span>
                            <div className="font-medium mt-1">{person.news_id}</div>
                          </div>
                        </div> */}
                        
                        {/* {(person.deal_id || person.company_id) && (
                          <div className="grid grid-cols-2 gap-2 text-xs mb-2">
                            {person.deal_id && (
                              <div className="bg-white p-2 rounded border">
                                <span className="text-gray-500">Associated Deal ID:</span>
                                <div className="font-medium mt-1">{person.deal_id}</div>
                              </div>
                            )}
                            {person.company_id && (
                              <div className="bg-white p-2 rounded border">
                                <span className="text-gray-500">Company ID:</span>
                                <div className="font-medium mt-1">{person.company_id}</div>
                              </div>
                            )}
                          </div>
                        )} */}
                        
                        {person.company_name && (
                          <div className="mb-2">
                            <p className="text-xs text-gray-500 mb-1">Company:</p>
                            <div className="bg-white p-2 rounded border text-xs flex items-center">
                              <Building2 className="h-3 w-3 mr-1 text-gray-400" />
                              <span className="font-medium">{person.company_name}</span>
                            </div>
                          </div>
                        )}
                        
                        {person.company_id && news.companies && !person.company_name && (
                          <div className="mb-2">
                            <p className="text-xs text-gray-500 mb-1">Linked Company:</p>
                            <div className="bg-white p-2 rounded border text-xs flex items-center">
                              <Building2 className="h-3 w-3 mr-1 text-gray-400" />
                              <span className="font-medium">
                                {news.companies.find(c => c.id === person.company_id)?.company_name || `Company ID: ${person.company_id}`}
                              </span>
                            </div>
                          </div>
                        )}
                        
                        {person.person_context && (
                          <div className="mb-2">
                            <p className="text-xs text-gray-500 mb-1">Context:</p>
                            <p className="text-xs text-gray-700 bg-white p-2 rounded border">{person.person_context}</p>
                          </div>
                        )}
                        
                        {/* Involvement Details JSON
                        {person.involvement_details && (
                          <div className="mb-2">
                            <p className="text-xs text-gray-500 mb-1">Involvement Details:</p>
                            <div className="bg-white p-2 rounded border text-xs">
                              <pre className="text-xs font-mono whitespace-pre-wrap max-h-32 overflow-y-auto">
                                {JSON.stringify(person.involvement_details, null, 2)}
                              </pre>
                            </div>
                          </div>
                        )} */}
                        
                        <div className="grid grid-cols-2 gap-2 text-xs mb-2">
                          {/* <div className="bg-white p-2 rounded border">
                            <span className="text-gray-500">Created:</span>
                            <div className="font-medium mt-1">{formatDateTime(person.created_at)}</div>
                          </div>
                          {person.confidence_score > 0 && (
                            <div className="bg-white p-2 rounded border">
                              <span className="text-gray-500">Confidence:</span>
                              <div className="flex items-center mt-1">
                                <div className="w-full max-w-[60px] bg-gray-200 rounded-full h-1.5 mr-2">
                                  <div 
                                    className="bg-blue-600 h-1.5 rounded-full" 
                                    style={{ width: `${Math.round(person.confidence_score * 100)}%` }}
                                  ></div>
                                </div>
                                <span className="text-xs text-gray-600">{Math.round(person.confidence_score * 100)}%</span>
                              </div>
                            </div>
                          )} */}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Technical Details Section */}
          <div>
            <SectionHeader 
              title="Technical Details" 
              icon={<AlertCircle className="h-4 w-4 text-blue-600" />} 
              isOpen={sections.technical} 
              onToggle={() => toggleSection('technical')} 
            />
            
            {sections.technical && (
              <div className="p-3">
                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-gray-50 p-2 rounded">
                    <p className="text-xs text-gray-500">News ID</p>
                    <p className="text-sm font-medium">{news.id}</p>
                  </div>

                  <div className="bg-gray-50 p-2 rounded">
                    <p className="text-xs text-gray-500">Source</p>
                    <p className="text-sm font-medium">{news.news_source}</p>
                  </div>

                  <div className="bg-gray-50 p-2 rounded">
                    <p className="text-xs text-gray-500">Created At</p>
                    <p className="text-sm font-medium">{formatDateTime(news.created_at)}</p>
                  </div>

                  <div className="bg-gray-50 p-2 rounded">
                    <p className="text-xs text-gray-500">Updated At</p>
                    <p className="text-sm font-medium">{formatDateTime(news.updated_at)}</p>
                  </div>

                  <div className="bg-gray-50 p-2 rounded">
                    <p className="text-xs text-gray-500">Deals Extracted</p>
                    <p className="text-sm font-medium">{news.deals?.length || 0}</p>
                  </div>

                  <div className="bg-gray-50 p-2 rounded">
                    <p className="text-xs text-gray-500">Companies Extracted</p>
                    <p className="text-sm font-medium">{news.companies?.length || 0}</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Actions Section - Mark as Bad URL */}
          <div className="border-t">
            <SectionHeader 
              title="Actions" 
              icon={<Flag className="h-4 w-4 text-red-600" />} 
              isOpen={true}
              onToggle={() => {}} 
            />
            
            <div className="p-3">
              {news.bad_url ? (
                <div className="bg-red-50 p-3 rounded-lg">
                  <div className="flex items-center">
                    <Flag className="h-4 w-4 text-red-500 mr-2" />
                    <p className="text-sm font-medium text-red-700">This URL has been marked as bad</p>
                  </div>
                  <p className="text-xs text-red-600 mt-1">
                    This URL will be excluded from future processing and scraping operations.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="bg-amber-50 p-3 rounded-lg border border-amber-200">
                    <h4 className="text-sm font-medium text-amber-800 mb-2">Mark URL as Bad</h4>
                    <p className="text-xs text-amber-700 mb-3">
                      Use this action if this URL contains invalid content, is broken, or should not be processed 
                      by our system. Marked URLs will be excluded from all future scraping and enrichment operations.
                    </p>
                    
                    <Button
                      onClick={markUrlAsBad}
                      disabled={markingBadUrl}
                      size="sm"
                      variant="destructive"
                      className="w-full"
                    >
                      {markingBadUrl ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-3 w-3 border-t border-b border-white mr-2"></div>
                          Marking as Bad...
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <Flag className="h-3 w-3 mr-2" />
                          Mark URL as Bad
                        </div>
                      )}
                    </Button>
                  </div>
                  
                  {markBadUrlMessage && (
                    <div className={`p-3 rounded-lg text-sm ${
                      markBadUrlMessage.includes('Failed') || markBadUrlMessage.includes('Error')
                        ? 'bg-red-50 text-red-700 border border-red-200'
                        : 'bg-green-50 text-green-700 border border-green-200'
                    }`}>
                      <div className="flex items-center">
                        {markBadUrlMessage.includes('Failed') || markBadUrlMessage.includes('Error') ? (
                          <XCircle className="h-4 w-4 mr-2" />
                        ) : (
                          <CheckCircle className="h-4 w-4 mr-2" />
                        )}
                        {markBadUrlMessage}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 