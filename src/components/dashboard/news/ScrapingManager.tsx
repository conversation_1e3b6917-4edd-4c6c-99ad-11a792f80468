'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Square, 
  RefreshCw, 
  Globe, 
  FileText, 
  Database,
  AlertCircle,
  CheckCircle,
  Clock,
  Loader
} from 'lucide-react';

interface ScrapingOperation {
  id: string;
  type: 'scraping' | 'fetching' | 'manager';
  startTime: string;
  duration: number;
  siteName?: string;
}

interface ScrapingManagerProps {
  onRefresh?: () => void;
}

export default function ScrapingManager({ onRefresh }: ScrapingManagerProps) {
  const [activeOperations, setActiveOperations] = useState<ScrapingOperation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedSite, setSelectedSite] = useState<string>('');
  const [maxPages, setMaxPages] = useState(30);
  const [tryLogin, setTryLogin] = useState(true);

  const sites = [
    { name: 'bisnow', label: 'Bisnow', description: 'Real Estate News' },
    { name: 'therealdeal', label: 'The Real Deal', description: 'Market Intelligence' },
    { name: 'globest', label: 'Globest', description: 'Real Estate News' },
    { name: 'pincus', label: 'Pincus', description: 'Real Estate News' }
  ];

  const fetchActiveOperations = async () => {
    try {
      const response = await fetch('/api/scraping?action=status');
      if (response.ok) {
        const data = await response.json();
        setActiveOperations(data.activeProcessors || []);
      }
    } catch (error) {
      console.error('Error fetching active operations:', error);
    }
  };

  useEffect(() => {
    fetchActiveOperations();
    const interval = setInterval(fetchActiveOperations, 5000); // Poll every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const startScraping = async (type: 'scraping' | 'fetching' | 'manager', siteName?: string) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/scraping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          siteName,
          maxPages,
          tryLogin
        }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Scraping started:', data);
        fetchActiveOperations();
        if (onRefresh) {
          setTimeout(onRefresh, 2000); // Refresh data after 2 seconds
        }
      } else {
        const error = await response.json();
        alert(`Failed to start scraping: ${error.error}`);
      }
    } catch (error) {
      console.error('Error starting scraping:', error);
      alert('Failed to start scraping operation');
    } finally {
      setIsLoading(false);
    }
  };

  const stopOperation = async (operationId: string) => {
    try {
      const response = await fetch(`/api/scraping?id=${operationId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        console.log('Operation stopped:', operationId);
        fetchActiveOperations();
      } else {
        alert('Failed to stop operation');
      }
    } catch (error) {
      console.error('Error stopping operation:', error);
      alert('Failed to stop operation');
    }
  };

  const getOperationIcon = (type: string) => {
    switch (type) {
      case 'scraping':
        return <FileText className="h-4 w-4" />;
      case 'fetching':
        return <Database className="h-4 w-4" />;
      case 'manager':
        return <Globe className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const formatDuration = (duration: number) => {
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  return (
    <div className="space-y-6">
      {/* Scraping Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Scraping Operations
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Site Selection */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Site
              </label>
              <select
                value={selectedSite}
                onChange={(e) => setSelectedSite(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Sites</option>
                {sites.map((site) => (
                  <option key={site.name} value={site.name}>
                    {site.label} - {site.description}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Pages
              </label>
              <input
                type="number"
                value={maxPages}
                onChange={(e) => setMaxPages(Number(e.target.value))}
                min="1"
                max="100"
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Options */}
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={tryLogin}
                onChange={(e) => setTryLogin(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Try Login</span>
            </label>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              onClick={() => startScraping('manager', selectedSite || undefined)}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isLoading ? (
                <Loader className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Play className="h-4 w-4 mr-2" />
              )}
              Start Scraping
            </Button>

            <Button
              onClick={() => startScraping('fetching')}
              disabled={isLoading}
              variant="outline"
            >
              <Database className="h-4 w-4 mr-2" />
              Fetch HTML
            </Button>

            <Button
              onClick={fetchActiveOperations}
              variant="outline"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Status
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Active Operations */}
      {activeOperations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Active Operations ({activeOperations.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {activeOperations.map((operation) => (
                <div
                  key={operation.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
                >
                  <div className="flex items-center gap-3">
                    {getOperationIcon(operation.type)}
                    <div>
                      <div className="font-medium text-gray-900">
                        {operation.type.charAt(0).toUpperCase() + operation.type.slice(1)}
                        {operation.siteName && ` - ${operation.siteName}`}
                      </div>
                      <div className="text-sm text-gray-500">
                        Running for {formatDuration(operation.duration)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      <Loader className="h-3 w-3 mr-1 animate-spin" />
                      Active
                    </Badge>
                    <Button
                      onClick={() => stopOperation(operation.id)}
                      size="sm"
                      variant="outline"
                      className="text-red-600 hover:text-red-700"
                    >
                      <Square className="h-4 w-4" />
                      Stop
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-3">
            {sites.map((site) => (
              <Button
                key={site.name}
                onClick={() => startScraping('manager', site.name)}
                disabled={isLoading}
                variant="outline"
                className="justify-start"
              >
                <Globe className="h-4 w-4 mr-2" />
                {site.label}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 