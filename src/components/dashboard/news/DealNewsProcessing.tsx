import { Button } from "@/components/ui/button";
import { ProcessingResult } from '@/types/deal-news';

interface DealNewsProcessingProps {
  processingLogs: string[];
  processingResults: ProcessingResult[];
  onStartProcessing: (testMode: boolean) => Promise<void>;
}

// Add components to render logs and results
interface ProcessingLogsProps { logs: string[]; }
function ProcessingLogs({ logs }: ProcessingLogsProps) {
  return (
    <div className="space-y-2 mb-4">
      <h3 className="text-sm font-medium text-gray-900">Processing Logs</h3>
      <div className="h-[200px] overflow-y-auto font-mono text-sm bg-gray-50 border rounded p-2">
        {logs.length === 0 ? (
          <div className="text-gray-500 text-center">No logs yet.</div>
        ) : (
          logs.map((log, idx) => (
            <div key={idx} className="text-gray-700 whitespace-pre-wrap">{log}</div>
          ))
        )}
      </div>
    </div>
  );
}

interface ProcessingResultsProps { results: ProcessingResult[]; }
function ProcessingResults({ results }: ProcessingResultsProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium text-gray-900 mb-2">Processing Results</h3>
      <div className="space-y-4">
        {results.map(result => (
          <div key={result.id} className="bg-white border rounded p-4">
            <h4 className="font-medium text-gray-900 mb-2">Article ID: {result.id}</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-1">Original HTML:</h5>
                <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto max-h-40 border">{result.originalHtml}</pre>
              </div>
              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-1">Cleaned Text:</h5>
                <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto max-h-40 border">{result.cleanedText}</pre>
              </div>
            </div>
            <div className="mt-4">
              <h5 className="text-sm font-medium text-gray-700 mb-1">Extracted Content:</h5>
              <div className="bg-blue-50 p-2 rounded">
                <p><strong>Title:</strong> {result.extractedContent.title}</p>
                <p><strong>Date:</strong> {result.extractedContent.date || 'Not found'}</p>
                <p><strong>Body:</strong> {result.extractedContent.body.substring(0, 200)}...</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export function DealNewsProcessing({
  processingLogs,
  processingResults,
  onStartProcessing
}: DealNewsProcessingProps) {
  return (
    <div className="space-y-4">
      <div className="bg-white rounded-lg border p-6">
        {/* Processing controls */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-2">HTML Processing</h2>
          <p className="text-gray-600 mb-6">
            Extract and process content from raw HTML news articles.
          </p>
          
          <div className="flex gap-4">
            {/* Extract Posts Section */}
            <div className="flex-1 p-4 border rounded-lg bg-gray-50">
              <h3 className="font-medium text-gray-900 mb-2">1. Extract Posts</h3>
              <p className="text-sm text-gray-600 mb-4">
                Process raw HTML content into structured data using AI.
              </p>
              <div className="flex gap-2">
                <Button
                  onClick={() => onStartProcessing(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Test (3 items)
                </Button>
                
                <Button
                  onClick={() => {
                    if (confirm('Are you sure you want to process all items? This may take a while.')) {
                      onStartProcessing(false);
                    }
                  }}
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  Process All
                </Button>
              </div>
            </div>

            {/* Test Section */}
            <div className="flex-1 p-4 border rounded-lg bg-gray-50">
              <h3 className="font-medium text-gray-900 mb-2">2. Test Post Extraction</h3>
              <p className="text-sm text-gray-600 mb-4">
                Test the extraction process on specific posts.
              </p>
              <Button
                onClick={() => {
                  console.log("Test extraction - Coming soon");
                }}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                Test Extraction
              </Button>
            </div>
          </div>
        </div>

        {/* Processing Logs */}
        <ProcessingLogs logs={processingLogs} />

        {/* Results Section */}
        {processingResults.length > 0 && (
          <ProcessingResults results={processingResults} />
        )}
      </div>
    </div>
  );
} 