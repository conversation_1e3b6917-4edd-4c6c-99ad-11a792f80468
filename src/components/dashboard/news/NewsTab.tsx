import { RefreshCcw } from 'lucide-react';
import { format } from 'date-fns';
import { NewsState, NewsItem } from '@/types/deal-news';

interface NewsTabProps {
  state: NewsState;
  setState: React.Dispatch<React.SetStateAction<NewsState>>;
  loadMoreRef: (node?: Element | null) => void;
  onRelevanceChange: (id: number, isRelevant: boolean) => Promise<void>;
}

export function NewsTab({
  state,
  setState,
  loadMoreRef,
  onRelevanceChange
}: NewsTabProps) {
  return (
    <div className="flex gap-4">
      {/* Left column - news list */}
      <div className="w-1/2 space-y-4 max-h-[calc(100vh-200px)] overflow-y-auto">
        {state.news.map((item) => (
          <NewsItem
            key={item.id}
            item={item}
            isSelected={state.selectedNews?.id === item.id}
            onSelect={() => setState(prev => ({ 
              ...prev, 
              selectedNews: item,
              showRawHtml: false 
            }))}
            onRelevanceChange={onRelevanceChange}
          />
        ))}
        
        {/* Load more indicator */}
        {state.hasMore && (
          <div ref={loadMoreRef} className="py-4 text-center">
            {state.isLoading && (
              <div className="flex items-center justify-center gap-2">
                <RefreshCcw className="h-4 w-4 animate-spin" />
                Loading...
              </div>
            )}
          </div>
        )}
        
        {/* Count indicator */}
        {state.totalCount > 0 && (
          <div className="sticky bottom-0 bg-white py-2 text-sm text-gray-500 text-center border-t">
            Showing {state.news.length} of {state.totalCount} articles
          </div>
        )}
      </div>
      
      {/* Right column - selected news content */}
      <div className="w-1/2">
        {state.selectedNews ? (
          <SelectedNewsContent
            news={state.selectedNews}
            showRawHtml={state.showRawHtml}
          />
        ) : (
          <div className="h-full flex items-center justify-center text-gray-500">
            Select a news item to view its content
          </div>
        )}
      </div>
    </div>
  );
} 