import { Check, Clock, AlertCircle, Newspaper } from 'lucide-react';
import { format } from 'date-fns';
import { Source, StatusConfig, StatusIcons } from '@/types/deal-news';

export function SourceItem({ name, status, lastChecked, type }: Source) {
  const statusColors: StatusConfig = {
    active: 'bg-green-50 text-green-700',
    pending: 'bg-yellow-50 text-yellow-700',
    error: 'bg-red-50 text-red-700'
  };

  const statusIcons: StatusIcons = {
    active: Check,
    pending: Clock,
    error: AlertCircle
  };

  const Icon = statusIcons[status];

  const formattedLastChecked = lastChecked && !isNaN(Date.parse(lastChecked))
    ? format(new Date(lastChecked), 'MMM d, yyyy h:mm a')
    : 'N/A';

  return (
    <div className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0">
      <div className="flex items-center gap-3">
        <div className="flex-shrink-0">
          <Newspaper className="h-6 w-6 text-gray-400" />
        </div>
        <div>
          <h4 className="text-base font-medium text-gray-900">{name}</h4>
          <p className="text-sm text-gray-600">{type}</p>
        </div>
      </div>
      <div className="flex items-center gap-4">
        <span className="text-sm text-gray-600">
          Last checked: {formattedLastChecked}
        </span>
        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusColors[status]}`}>
          <Icon className="w-4 h-4 mr-1" />
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </span>
      </div>
    </div>
  );
} 