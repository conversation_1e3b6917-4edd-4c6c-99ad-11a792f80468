'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { 
  Handshake, Building2, Calendar, DollarSign, 
  Percent, MapPin, Globe, ArrowLeft, FileText, User,
  Clock, Shield
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface ExtractedDealDetailProps {
  id: string
}

interface Company {
  id: number
  company_name: string
  role: string
  confidence_score: number
  participation_details: any
  created_at: string
}

interface Person {
  id: number
  person_name: string
  title: string
  role: string
  confidence_score: number
  involvement_details: any
  company_id: number
  created_at: string
}

interface DealDetails {
  id: number
  news_id: number
  deal_type: string[] | string // Handle both array and string formats
  deal_status: string
  deal_date: string
  deal_value: number
  currency: string
  cap_rate: number
  ltv: number
  confidence_score: number
  property_type: string
  property_size: number
  property_size_unit: string
  property_address: string
  deal_details: any
  created_at: string
  news_title: string
  news_date: string
  news_text: string
  news_url: string
  companies: Company[]
  persons: Person[]
  
  // New enrichment fields
  market_context?: {
    marketName?: string[]
    submarketName?: string[]
    sentimentSummary?: string
    forecastSummary?: string
    macroeconomicCommentary?: string
  }
  financial_context?: {
    financingType?: string[]
    capitalStackNotes?: string[]
    fundName?: string[]
    fundType?: string[]
    fundSize?: number
    fundStrategy?: string[]
    distressFlag?: boolean
    loanPerformance?: string
  }
  extraction_metadata?: {
    llmTags?: string[]
    quotesLlmTags?: any[]
    extractionNotes?: string
    sourceConfidence?: number
  }
}

export default function ExtractedDealDetail({ id }: ExtractedDealDetailProps) {
  const router = useRouter()
  const [deal, setDeal] = useState<DealDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Utility function to extract first value from JSONB array or return string directly
  const getDisplayValue = (value: string[] | string | null | undefined): string => {
    if (!value) return '';
    if (Array.isArray(value)) {
      return value.length > 0 ? value[0] : '';
    }
    return value;
  };

  // Utility function to get all values from JSONB array or single string as array
  const getArrayValue = (value: string[] | string | null | undefined): string[] => {
    if (!value) return [];
    if (Array.isArray(value)) {
      return value;
    }
    return [value];
  };

  useEffect(() => {
    const fetchDealDetails = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/deal-news/extracted-deals/${id}`)
        
        if (!response.ok) {
          throw new Error(`Error ${response.status}: ${response.statusText}`)
        }
        
        const data = await response.json()
        console.log(`Deal Details: ${JSON.stringify(data, null, 2)}`)
        setDeal(data)
      } catch (err) {
        console.error('Error fetching deal details:', err)
        setError('Failed to load deal details. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      fetchDealDetails()
    }
  }, [id])

  // Format a date string
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Unknown'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Format a date with time string
  const formatDateTime = (dateString: string) => {
    if (!dateString) return 'Unknown'
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Get a color based on deal type
  const getDealTypeColor = (dealType: string[] | string): string => {
    const displayType = getDisplayValue(dealType);
    const colors: Record<string, string> = {
      'Acquisition': 'bg-blue-500 text-white',
      'Sale': 'bg-green-500 text-white',
      'Financing': 'bg-purple-500 text-white',
      'Refinancing': 'bg-indigo-500 text-white',
      'Investment': 'bg-orange-500 text-white',
      'Development': 'bg-red-500 text-white',
      'Lease': 'bg-yellow-500 text-black',
      'Joint Venture': 'bg-teal-500 text-white',
      'default': 'bg-gray-500 text-white'
    }
    
    return colors[displayType] || colors.default;
  }

  // Get a color based on property type
  const getPropertyTypeColor = (propertyType: string): string => {
    const colors: Record<string, string> = {
      'Office': 'bg-blue-100 text-blue-800',
      'Retail': 'bg-green-100 text-green-800',
      'Multifamily': 'bg-purple-100 text-purple-800',
      'Industrial': 'bg-yellow-100 text-yellow-800',
      'Mixed-Use': 'bg-pink-100 text-pink-800',
      'Hotel': 'bg-indigo-100 text-indigo-800',
      'Land': 'bg-teal-100 text-teal-800',
      'default': 'bg-gray-100 text-gray-800'
    }
    
    return colors[propertyType] || colors.default
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-5xl mx-auto p-4 md:p-8">
        {/* Top navigation */}
        <div className="mb-8">
          <Button 
            variant="outline" 
            onClick={() => router.back()}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Deals
          </Button>
          
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
            {deal?.news_title || `${getDisplayValue(deal?.deal_type)} Deal`}
          </h1>
          
          <div className="flex flex-wrap gap-2 mt-2 mb-4">
            {deal?.deal_type && (
              <Badge className={getDealTypeColor(deal.deal_type)}>
                {getDisplayValue(deal.deal_type)}
              </Badge>
            )}
            {/* Show additional deal types */}
            {getArrayValue(deal?.deal_type).slice(1, 3).map((type, index) => (
              <Badge key={index} className="bg-blue-100 text-blue-800">
                {type}
              </Badge>
            ))}
            {deal?.property_type && (
              <Badge className={getPropertyTypeColor(deal.property_type)}>
                {deal.property_type}
              </Badge>
            )}
            {deal?.deal_status && (
              <Badge variant="outline">
                Status: {deal.deal_status}
              </Badge>
            )}
          </div>
        </div>
        
        {/* Rest of the content - loading, error states and main content */}
        {loading ? (
          <div className="flex items-center justify-center min-h-[60vh]">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error || !deal ? (
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <Handshake className="h-16 w-16 text-gray-300 mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">Deal Not Found</h3>
            <p className="text-gray-500 mb-6">{error || 'Unable to load the requested deal.'}</p>
            <Button 
              onClick={() => router.back()}
              variant="outline"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Deals
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Main deal information */}
            <div className="md:col-span-2 space-y-6">
              <Card className="p-6">
                <h2 className="text-xl font-semibold mb-4 flex items-center">
                  <Handshake className="h-5 w-5 mr-2 text-blue-600" />
                  Deal Overview
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {deal.deal_date && (
                    <div>
                      <p className="text-sm text-gray-500">Date</p>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1.5 text-gray-500" />
                        <p className="font-medium">{formatDate(deal.deal_date)}</p>
                      </div>
                    </div>
                  )}
                  
                  {deal.deal_value > 0 && (
                    <div>
                      <p className="text-sm text-gray-500">Deal Value</p>
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-1.5 text-gray-500" />
                        <p className="font-medium">
                          {deal.deal_value.toLocaleString('en-US', {
                            style: 'currency',
                            currency: deal.currency || 'USD',
                            maximumFractionDigits: 0
                          })}
                        </p>
                      </div>
                    </div>
                  )}
                  
                  {deal.cap_rate > 0 && (
                    <div>
                      <p className="text-sm text-gray-500">Cap Rate</p>
                      <div className="flex items-center">
                        <Percent className="h-4 w-4 mr-1.5 text-gray-500" />
                        <p className="font-medium">{deal.cap_rate}%</p>
                      </div>
                    </div>
                  )}
                  
                  {deal.ltv > 0 && (
                    <div>
                      <p className="text-sm text-gray-500">Loan-to-Value</p>
                      <div className="flex items-center">
                        <Percent className="h-4 w-4 mr-1.5 text-gray-500" />
                        <p className="font-medium">{deal.ltv}%</p>
                      </div>
                    </div>
                  )}
                  
                  {deal.confidence_score > 0 && (
                    <div>
                      <p className="text-sm text-gray-500">AI Confidence</p>
                      <div className="flex items-center">
                        <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                          <div 
                            className="bg-blue-600 h-2.5 rounded-full" 
                            style={{ width: `${Math.round(deal.confidence_score * 100)}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600">{Math.round(deal.confidence_score * 100)}%</span>
                      </div>
                    </div>
                  )}

                  {deal.created_at && (
                    <div>
                      <p className="text-sm text-gray-500">Created At</p>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1.5 text-gray-500" />
                        <p className="font-medium">{formatDateTime(deal.created_at)}</p>
                      </div>
                    </div>
                  )}
                </div>
              </Card>
              
              {/* Property information */}
              <Card className="p-6">
                <h2 className="text-xl font-semibold mb-4 flex items-center">
                  <Building2 className="h-5 w-5 mr-2 text-blue-600" />
                  Property Details
                </h2>
                
                <div className="space-y-4">
                  {deal.property_address && (
                    <div>
                      <p className="text-sm text-gray-500">Address</p>
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1.5 text-gray-500" />
                        <p className="font-medium">{deal.property_address}</p>
                      </div>
                    </div>
                  )}
                  
                  {deal.property_size > 0 && (
                    <div>
                      <p className="text-sm text-gray-500">Size</p>
                      <p className="font-medium">
                        {deal.property_size.toLocaleString()} {deal.property_size_unit || 'sq ft'}
                      </p>
                    </div>
                  )}
                  
                  {deal.deal_details && Object.keys(deal.deal_details).length > 0 && (
                    // Only show Additional Details if there's actual content to display
                    (() => {
                      // Check if there's meaningful content in deal_details
                      const hasPropertyAdditional = deal.deal_details.property_additional && 
                        Object.keys(deal.deal_details.property_additional || {}).length > 0;
                      
                      const hasFinancialAdditional = deal.deal_details.financial_additional && 
                        Object.keys(deal.deal_details.financial_additional || {}).length > 0;
                      
                      const hasDealValueConfidence = 
                        deal.deal_details.deal_value_confidence !== null && 
                        deal.deal_details.deal_value_confidence !== undefined;
                      
                      // Get other properties that aren't these three special ones
                      const otherProps = Object.entries(deal.deal_details).filter(([key]) => 
                        !['property_additional', 'financial_additional', 'deal_value_confidence'].includes(key)
                      );
                      
                      // Only render if there's something to show
                      if (hasPropertyAdditional || hasFinancialAdditional || hasDealValueConfidence || otherProps.length > 0) {
                        return (
                          <div>
                            <p className="text-sm text-gray-500 mb-2">Additional Details</p>
                            <div className="bg-gray-50 p-3 rounded text-sm space-y-1">
                              {hasDealValueConfidence && (
                                <div className="grid grid-cols-3 gap-2">
                                  <span className="text-gray-600 capitalize col-span-1">Deal Value Confidence:</span>
                                  <span className="col-span-2">
                                    {deal.deal_details.deal_value_confidence}
                                  </span>
                                </div>
                              )}
                              
                              {hasPropertyAdditional && (
                                <div className="grid grid-cols-3 gap-2">
                                  <span className="text-gray-600 capitalize col-span-1">Property Additional:</span>
                                  <span className="col-span-2">
                                    {JSON.stringify(deal.deal_details.property_additional)}
                                  </span>
                                </div>
                              )}
                              
                              {hasFinancialAdditional && (
                                <div className="grid grid-cols-3 gap-2">
                                  <span className="text-gray-600 capitalize col-span-1">Financial Additional:</span>
                                  <span className="col-span-2">
                                    {JSON.stringify(deal.deal_details.financial_additional)}
                                  </span>
                                </div>
                              )}
                              
                              {/* Display other properties normally */}
                              {otherProps.map(([key, value]) => (
                                <div key={key} className="grid grid-cols-3 gap-2">
                                  <span className="text-gray-600 capitalize col-span-1">{key.replace(/_/g, ' ')}:</span>
                                  <span className="col-span-2">
                                    {typeof value === 'object' 
                                      ? JSON.stringify(value) 
                                      : String(value)}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      }
                      
                      // Return null if nothing to show
                      return null;
                    })()
                  )}
                </div>
              </Card>
              
              {/* Companies information */}
              {deal.companies && deal.companies.length > 0 && (
                <Card className="p-6">
                  <h2 className="text-xl font-semibold mb-4 flex items-center">
                    <Building2 className="h-5 w-5 mr-2 text-blue-600" />
                    Companies Involved
                  </h2>
                  
                  <div className="divide-y">
                    {deal.companies.map((company) => (
                      <div key={company.id} className="py-3">
                        <div className="flex justify-between">
                          <h3 className="font-medium text-gray-900">{company.company_name}</h3>
                          {company.role && (
                            <Badge variant="outline">{company.role}</Badge>
                          )}
                        </div>
                        
                        {company.created_at && (
                          <div className="flex items-center text-xs text-gray-500 mt-1 mb-2">
                            <Clock className="h-3 w-3 mr-1" /> 
                            <span>Added: {formatDateTime(company.created_at)}</span>
                          </div>
                        )}
                        
                        {company.confidence_score > 0 && (
                          <div className="flex items-center mt-2">
                            <span className="text-xs text-gray-500 mr-2">Confidence:</span>
                            <div className="w-24 bg-gray-200 rounded-full h-1.5 mr-2">
                              <div 
                                className="bg-blue-600 h-1.5 rounded-full" 
                                style={{ width: `${Math.round(company.confidence_score * 100)}%` }}
                              ></div>
                            </div>
                            <span className="text-xs text-gray-600">{Math.round(company.confidence_score * 100)}%</span>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </Card>
              )}
              
              {/* People information */}
              {deal.persons && deal.persons.length > 0 && (
                <Card className="p-6">
                  <h2 className="text-xl font-semibold mb-4 flex items-center">
                    <User className="h-5 w-5 mr-2 text-blue-600" />
                    People Involved
                  </h2>
                  
                  <div className="divide-y">
                    {deal.persons.map((person) => (
                      <div key={person.id} className="py-3">
                        <div className="flex justify-between">
                          <h3 className="font-medium text-gray-900">{person.person_name}</h3>
                          {person.role && (
                            <Badge variant="outline">{person.role}</Badge>
                          )}
                        </div>
                        
                        {person.created_at && (
                          <div className="flex items-center text-xs text-gray-500 mt-1 mb-1">
                            <Clock className="h-3 w-3 mr-1" /> 
                            <span>Added: {formatDateTime(person.created_at)}</span>
                          </div>
                        )}
                        
                        <div className="text-sm text-gray-600 mt-1">
                          {person.title && <span>{person.title}</span>}
                          
                          {person.company_id && deal.companies && (
                            <span className="ml-1">
                              at {deal.companies.find(c => c.id === person.company_id)?.company_name}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              )}

              {/* Add new enrichment sections */}
              
              {/* Market Context Section */}
              {deal?.market_context && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <MapPin className="h-5 w-5 mr-2 text-blue-500" />
                    Market Context
                  </h3>
                  <div className="space-y-3">
                    {deal.market_context.marketName && getArrayValue(deal.market_context.marketName).length > 0 && (
                      <div>
                        <span className="text-sm text-gray-500">Markets:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {getArrayValue(deal.market_context.marketName).map((market, index) => (
                            <Badge key={index} className="bg-green-100 text-green-800">
                              {market}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {deal.market_context.submarketName && getArrayValue(deal.market_context.submarketName).length > 0 && (
                      <div>
                        <span className="text-sm text-gray-500">Submarkets:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {getArrayValue(deal.market_context.submarketName).map((submarket, index) => (
                            <Badge key={index} className="bg-blue-100 text-blue-800">
                              {submarket}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {deal.market_context.sentimentSummary && (
                      <div>
                        <span className="text-sm text-gray-500">Market Sentiment:</span>
                        <p className="text-sm text-gray-700 mt-1">{deal.market_context.sentimentSummary}</p>
                      </div>
                    )}
                    
                    {deal.market_context.forecastSummary && (
                      <div>
                        <span className="text-sm text-gray-500">Forecast:</span>
                        <p className="text-sm text-gray-700 mt-1">{deal.market_context.forecastSummary}</p>
                      </div>
                    )}
                    
                    {deal.market_context.macroeconomicCommentary && (
                      <div>
                        <span className="text-sm text-gray-500">Macroeconomic Commentary:</span>
                        <p className="text-sm text-gray-700 mt-1">{deal.market_context.macroeconomicCommentary}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Financial Context Section */}
              {deal?.financial_context && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <DollarSign className="h-5 w-5 mr-2 text-green-500" />
                    Financial Context
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {deal.financial_context.financingType && getArrayValue(deal.financial_context.financingType).length > 0 && (
                      <div>
                        <span className="text-sm text-gray-500">Financing Types:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {getArrayValue(deal.financial_context.financingType).map((type, index) => (
                            <Badge key={index} className="bg-purple-100 text-purple-800">
                              {type}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {deal.financial_context.fundName && getArrayValue(deal.financial_context.fundName).length > 0 && (
                      <div>
                        <span className="text-sm text-gray-500">Funds:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {getArrayValue(deal.financial_context.fundName).map((fund, index) => (
                            <Badge key={index} className="bg-indigo-100 text-indigo-800">
                              {fund}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {deal.financial_context.fundSize && (
                      <div>
                        <span className="text-sm text-gray-500">Fund Size:</span>
                        <p className="text-sm font-medium text-gray-900">
                          {(deal.financial_context.fundSize).toLocaleString('en-US', {
                            style: 'currency',
                            currency: 'USD',
                            maximumFractionDigits: 0
                          })}
                        </p>
                      </div>
                    )}
                    
                    {deal.financial_context.distressFlag && (
                      <div>
                        <span className="text-sm text-gray-500">Distress Status:</span>
                        <Badge className="bg-red-100 text-red-800 ml-2">
                          Distressed Asset
                        </Badge>
                      </div>
                    )}
                  </div>
                  
                  {deal.financial_context.capitalStackNotes && getArrayValue(deal.financial_context.capitalStackNotes).length > 0 && (
                    <div className="mt-4">
                      <span className="text-sm text-gray-500">Capital Stack Notes:</span>
                      <div className="mt-1 space-y-1">
                        {getArrayValue(deal.financial_context.capitalStackNotes).map((note, index) => (
                          <p key={index} className="text-sm text-gray-700">{note}</p>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Extraction Metadata Section */}
              {deal?.extraction_metadata && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Shield className="h-5 w-5 mr-2 text-gray-500" />
                    Extraction Metadata
                  </h3>
                  <div className="space-y-3">
                    {deal.extraction_metadata.sourceConfidence && (
                      <div>
                        <span className="text-sm text-gray-500">Source Confidence:</span>
                        <div className="flex items-center mt-1">
                          <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${(deal.extraction_metadata.sourceConfidence * 100)}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium text-gray-900">
                            {Math.round(deal.extraction_metadata.sourceConfidence * 100)}%
                          </span>
                        </div>
                      </div>
                    )}
                    
                    {deal.extraction_metadata.llmTags && deal.extraction_metadata.llmTags.length > 0 && (
                      <div>
                        <span className="text-sm text-gray-500">AI Tags:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {deal.extraction_metadata.llmTags.map((tag, index) => (
                            <Badge key={index} className="bg-gray-100 text-gray-700">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {deal.extraction_metadata.extractionNotes && (
                      <div>
                        <span className="text-sm text-gray-500">Extraction Notes:</span>
                        <p className="text-sm text-gray-700 mt-1">{deal.extraction_metadata.extractionNotes}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            {/* Source information */}
            <div className="space-y-6">
              <Card className="p-6">
                <h2 className="text-xl font-semibold mb-4 flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-blue-600" />
                  Source
                </h2>
                
                {deal.news_title && (
                  <div className="mb-3">
                    <p className="text-sm text-gray-500">News Title</p>
                    <p className="font-medium">{deal.news_title}</p>
                  </div>
                )}
                
                {deal.news_date && (
                  <div className="mb-3">
                    <p className="text-sm text-gray-500">Publication Date</p>
                    <p className="font-medium">{formatDate(deal.news_date)}</p>
                  </div>
                )}
                
                {deal.news_url && (
                  <div className="mb-3">
                    <p className="text-sm text-gray-500">Source URL</p>
                    <a 
                      href={deal.news_url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline flex items-center"
                    >
                      <Globe className="h-4 w-4 mr-1.5" />
                      View Original
                    </a>
                  </div>
                )}
                
                <hr className="my-4" />
                
                {deal.news_text && (
                  <div>
                    <p className="text-sm text-gray-500 mb-2">News Text</p>
                    <div className="bg-gray-50 p-3 rounded text-sm max-h-[500px] overflow-y-auto">
                      {deal.news_text.split('\n').map((line, index) => (
                        <p key={index} className="mb-2">{line}</p>
                      ))}
                    </div>
                  </div>
                )}
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 