import { RefreshCcw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { format } from "date-fns";
import { SourceItem } from "./SourceItem";
import { Source } from "@/types/deal-news";

interface DealNewsOverviewProps {
  overviewData: any;
  onRefresh: () => void;
  onProcessingTab: () => void;
}

export function DealNewsOverview({
  overviewData,
  onRefresh,
  onProcessingTab,
}: DealNewsOverviewProps) {
  // Helper function to safely get last checked time for a source
  const getLastChecked = (sourceName: string): string => {
    if (
      !overviewData?.sourcesLastUpdated ||
      !Array.isArray(overviewData.sourcesLastUpdated)
    ) {
      return "N/A";
    }

    const source = overviewData.sourcesLastUpdated.find(
      (source: any) => source?.name?.toLowerCase() === sourceName.toLowerCase()
    );

    return source?.lastChecked || "N/A";
  };

  const sources: Source[] = [
    {
      name: "Bisnow",
      type: "Real Estate News",
      status: "active",
      lastChecked: getLastChecked("bisnow"),
    },
    {
      name: "The Real Deal",
      type: "Real Estate News",
      status: "active",
      lastChecked: getLastChecked("trd"),
    },
    {
      name: "Pincus",
      type: "Real Estate News",
      status: "active",
      lastChecked: getLastChecked("pincus"),
    },
    {
      name: "GlobeSt",
      type: "Real Estate News",
      status: "active",
      lastChecked: getLastChecked("globest"),
    },
  ];

  return (
    <div>
      <div className="relative mb-6">
        <div className="grid grid-cols-5 gap-4">
          <div className="bg-blue-50 rounded-lg p-2">
            <dt className="text-sm font-medium text-blue-600">News URLs</dt>
            <dd className="mt-1 text-xl font-semibold text-blue-700">
              {overviewData?.totalUrls || 0}
            </dd>
          </div>
          <div className="bg-green-50 rounded-lg p-2">
            <dt className="text-sm font-medium text-green-600">
              Today's Articles
            </dt>
            <dd className="mt-1 text-xl font-semibold text-green-700">
              {overviewData?.todayCount || 0}
            </dd>
          </div>
          <div className="bg-purple-50 rounded-lg p-2">
            <dt className="text-sm font-medium text-purple-600">
              Active Sources
            </dt>
            <dd className="mt-1 text-xl font-semibold text-purple-700">
              {overviewData?.activeSources || 0}
            </dd>
          </div>
          <div className="bg-yellow-50 rounded-lg p-2">
            <dt className="text-sm font-medium text-yellow-600">
              Processing Queue
            </dt>
            <dd className="mt-1 text-xl font-semibold text-yellow-700">
              {overviewData?.processingQueue || 0}
            </dd>
          </div>
          <div className="bg-orange-50 rounded-lg p-2">
            <dt className="text-sm font-medium text-orange-600">
              Process News
            </dt>
            <dd className="mt-1">
              <Button
                onClick={onProcessingTab}
                className="w-full bg-orange-600 hover:bg-orange-700 text-white"
              >
                Process HTML Content
              </Button>
            </dd>
          </div>
        </div>
        <button
          className="absolute top-0 right-0 inline-flex items-center gap-1 text-sm text-gray-500 hover:text-gray-700"
          onClick={onRefresh}
        >
          <RefreshCcw className="h-4 w-4" />
          Refresh
        </button>
      </div>

      <div className="space-y-1">
        {sources.map((source, idx) => (
          <SourceItem key={idx} {...source} />
        ))}
      </div>
    </div>
  );
}
