"use client"

import React, { useState, useRef, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ArrowUp, Loader2, Info } from 'lucide-react';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  debug?: any;
}

export function DealNewsChat() {
  const [messages, setMessages] = useState<Message[]>([
    {
      role: 'assistant',
      content: 'Hello! I can help you find information about real estate deals. Ask me questions like "What are the top deals of last week?" or "What are the notable deals in Atlanta?"'
    }
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showDebug, setShowDebug] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!input.trim()) return;
    
    const userMessage = input.trim();
    setInput('');
    
    // Add user message to chat
    setMessages(prev => [...prev, { role: 'user', content: userMessage }]);
    setIsLoading(true);

    // Add a processing message that will be replaced
    const processingId = Date.now();
    setMessages(prev => [...prev, { 
      role: 'assistant', 
      content: '...',
      debug: { processingId }
    }]);
    
    try {
      // Call backend API to process the query
      const response = await fetch('/api/deal-news/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          query: userMessage,
          messages: messages.filter(m => !m.debug?.processingId) // Don't include processing message
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to get response');
      }
      
      const data = await response.json();
      
      // Remove the processing message and add the real response
      setMessages(prev => {
        const filtered = prev.filter(m => !m.debug?.processingId);
        return [...filtered, { 
          role: 'assistant', 
          content: data.response,
          debug: data.debug
        }];
      });
    } catch (error) {
      console.error('Error fetching chat response:', error);
      // Remove the processing message and add error message
      setMessages(prev => {
        const filtered = prev.filter(m => !m.debug?.processingId);
        return [...filtered, { 
          role: 'assistant', 
          content: 'Sorry, I encountered an error while processing your request. Please try again.' 
        }];
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderDebugInfo = (debug: any) => {
    if (!debug) return null;
    
    return (
      <div className="mt-2 border-t border-gray-200 pt-2 text-xs text-gray-500">
        <details>
          <summary className="cursor-pointer">Debug Info</summary>
          <div className="mt-1 p-2 bg-gray-50 rounded overflow-auto max-h-60">
            <div>Query interpretation: {JSON.stringify(debug.queryInfo)}</div>
            <div>Results: {debug.dealsCount} deals found</div>
            {debug.sampleData && debug.sampleData.length > 0 && (
              <div className="mt-1">
                <div>Sample data:</div>
                <pre className="text-xxs">{JSON.stringify(debug.sampleData, null, 2)}</pre>
              </div>
            )}
          </div>
        </details>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-[700px] bg-white rounded-lg border">
      {/* Chat header */}
      <div className="px-4 py-2 border-b flex justify-between items-center">
        <h3 className="font-medium">Deal News Chat</h3>
        <button 
          onClick={() => setShowDebug(!showDebug)}
          className="text-gray-500 hover:text-gray-700 p-1"
          title={showDebug ? "Hide debug info" : "Show debug info"}
        >
          <Info className="h-4 w-4" />
        </button>
      </div>
      
      {/* Chat messages area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message, idx) => (
          <div 
            key={idx} 
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div 
              className={`max-w-[80%] rounded-lg px-4 py-2 ${
                message.role === 'user' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-100 text-gray-800'
              }`}
            >
              {message.debug?.processingId ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Processing your question...</span>
                </div>
              ) : (
                <>
                  <div className="whitespace-pre-wrap">{message.content}</div>
                  {showDebug && message.debug && renderDebugInfo(message.debug)}
                </>
              )}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      
      {/* Input area */}
      <form onSubmit={handleSubmit} className="border-t p-4">
        <div className="flex items-end gap-2">
          <Textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask about real estate deals..."
            className="flex-1 min-h-[80px] resize-none"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSubmit(e);
              }
            }}
          />
          <Button 
            type="submit" 
            disabled={isLoading || !input.trim()} 
            className="h-10 w-10 p-2"
          >
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <ArrowUp className="h-4 w-4" />}
          </Button>
        </div>
      </form>
    </div>
  );
} 