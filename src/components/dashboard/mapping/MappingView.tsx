'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Upload, Plus, Edit, Trash2, Search, RefreshCw, Database, Filter } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import CSVUploader from './CSVUploader';
import type { HierarchicalTypeData, MappingType, HierarchyRow } from '@/types/mapping';

export default function MappingView() {
  const [types, setTypes] = useState<MappingType[]>([]);
  const [activeType, setActiveType] = useState<string>('');
  const [hierarchicalData, setHierarchicalData] = useState<HierarchicalTypeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showUploader, setShowUploader] = useState(false);
  const [editingRow, setEditingRow] = useState<HierarchyRow | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const { toast } = useToast();

  const [newRowData, setNewRowData] = useState<string[]>([]);

  // Fetch all mapping types
  const fetchTypes = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/mapping-tables/types');
      const result = await response.json();
      
      if (result.success) {
        setTypes(result.data);
        if (result.data.length > 0 && !activeType) {
          setActiveType(result.data[0].type);
        }
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to fetch mapping types',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching types:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch mapping types',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch hierarchical data for active type
  const fetchHierarchicalData = async (type: string) => {
    if (!type) return;
    
    try {
      setLoading(true);
      const response = await fetch(`/api/mapping-tables/types?type=${encodeURIComponent(type)}`);
      const result = await response.json();
      
      if (result.success) {
        setHierarchicalData(result.data);
        // Initialize new row data array with empty strings based on levels
        setNewRowData(new Array(result.data.levels.length).fill(''));
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to fetch hierarchical data',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching hierarchical data:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch hierarchical data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter hierarchy rows based on search
  const filteredRows = hierarchicalData?.hierarchyRows.filter(row => {
    if (!searchTerm) return true;
    return row.values.some(value => 
      value.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }) || [];

  // Handle adding new row
  const handleAddRow = async () => {
    if (!hierarchicalData || !activeType) return;
    
    try {
      const mappingData = {
        type: activeType,
        level_1: hierarchicalData.levels[0] || '',
        value_1: newRowData[0] || '',
        level_2: hierarchicalData.levels[1] || '',
        value_2: newRowData[1] || '',
        level_3: hierarchicalData.levels[2] || '',
        value_3: newRowData[2] || '',
        is_active: true
      };

      const response = await fetch('/api/mapping-tables', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(mappingData),
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: 'Success',
          description: 'New hierarchy row added successfully',
        });
        
        setIsAddDialogOpen(false);
        setNewRowData(new Array(hierarchicalData.levels.length).fill(''));
        fetchHierarchicalData(activeType);
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to add hierarchy row',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error adding hierarchy row:', error);
      toast({
        title: 'Error',
        description: 'Failed to add hierarchy row',
        variant: 'destructive',
      });
    }
  };

  // Handle editing row
  const handleEditRow = (row: HierarchyRow) => {
    setEditingRow(row);
    setNewRowData([...row.values]);
    setIsEditDialogOpen(true);
  };

  // Handle updating row
  const handleUpdateRow = async () => {
    if (!editingRow || !hierarchicalData || !activeType) return;
    
    try {
      const mappingData = {
        id: editingRow.id,
        type: activeType,
        level_1: hierarchicalData.levels[0] || '',
        value_1: newRowData[0] || '',
        level_2: hierarchicalData.levels[1] || '',
        value_2: newRowData[1] || '',
        level_3: hierarchicalData.levels[2] || '',
        value_3: newRowData[2] || '',
        is_active: true
      };

      const response = await fetch('/api/mapping-tables', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(mappingData),
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: 'Success',
          description: 'Hierarchy row updated successfully',
        });
        
        setIsEditDialogOpen(false);
        setEditingRow(null);
        setNewRowData([]);
        fetchHierarchicalData(activeType);
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to update hierarchy row',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating hierarchy row:', error);
      toast({
        title: 'Error',
        description: 'Failed to update hierarchy row',
        variant: 'destructive',
      });
    }
  };

  // Handle deleting row
  const handleDeleteRow = async (row: HierarchyRow) => {
    if (!confirm('Are you sure you want to delete this hierarchy row?')) {
      return;
    }

    try {
      const response = await fetch(`/api/mapping-tables?id=${row.id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: 'Success',
          description: 'Hierarchy row deleted successfully',
        });
        fetchHierarchicalData(activeType);
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to delete hierarchy row',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error deleting hierarchy row:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete hierarchy row',
        variant: 'destructive',
      });
    }
  };

  // Handle upload completion
  const handleUploadComplete = () => {
    setShowUploader(false);
    fetchTypes();
    if (activeType) {
      fetchHierarchicalData(activeType);
    }
  };

  // Handle tab change
  const handleTabChange = (newType: string) => {
    setActiveType(newType);
    fetchHierarchicalData(newType);
  };

  useEffect(() => {
    fetchTypes();
  }, []);

  useEffect(() => {
    if (activeType) {
      fetchHierarchicalData(activeType);
    }
  }, [activeType]);

  if (showUploader) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Modern Header */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                Upload Mapping Data
              </h1>
              <p className="text-slate-600 text-lg">
                Upload CSV or XLSX files containing hierarchical mapping data
              </p>
            </div>
            <Button 
              variant="outline" 
              onClick={() => setShowUploader(false)}
              className="flex items-center gap-2 hover:bg-slate-100 transition-colors"
            >
              ← Back to Mappings
            </Button>
          </div>
          
          {/* CSV Uploader Component */}
          <CSVUploader onUploadComplete={handleUploadComplete} />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Modern Header */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
              Central Mapping Management
            </h1>
            <p className="text-slate-600 text-lg flex items-center gap-2">
              <Database className="h-5 w-5" />
              Manage hierarchical mapping data organized by type
            </p>
          </div>
          <div className="flex gap-3">
            <Button 
              onClick={fetchTypes} 
              variant="outline" 
              size="sm"
              className="hover:bg-slate-100 transition-colors"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button 
              variant="outline"
              onClick={() => setShowUploader(true)}
              className="hover:bg-slate-100 transition-colors"
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload Data
            </Button>
          </div>
        </div>

        {/* Modern Search Card */}
        <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
          <CardContent className="pt-6">
            <div className="flex gap-4 items-end">
              <div className="flex-1">
                <Label htmlFor="search" className="text-sm font-medium text-slate-700">
                  Search Hierarchy
                </Label>
                <div className="relative mt-2">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                  <Input
                    id="search"
                    placeholder="Search hierarchy values..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 border-slate-200 focus:border-slate-400 focus:ring-slate-400"
                  />
                </div>
              </div>
              {searchTerm && (
                <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4 text-slate-500" />
                  <Badge variant="secondary" className="bg-slate-100">
                    {filteredRows.length} results
                  </Badge>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Modern Type Tabs */}
        {types.length > 0 && (
          <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
            <CardContent className="p-0">
              <Tabs value={activeType} onValueChange={handleTabChange}>
                <div className="border-b border-slate-200 bg-slate-50/50 rounded-t-lg">
                  <TabsList className="w-full h-auto p-2 bg-transparent justify-start flex-wrap gap-1">
                    {types.map((type) => (
                      <TabsTrigger 
                        key={type.type} 
                        value={type.type} 
                        className="flex items-center gap-2 px-4 py-2 rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-md transition-all duration-200 hover:bg-white/50"
                      >
                        <span className="font-medium">{type.type}</span>
                        <Badge variant="secondary" className="bg-slate-200 text-slate-700 text-xs">
                          {type.count}
                        </Badge>
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </div>

                {types.map((type) => (
                  <TabsContent key={type.type} value={type.type} className="p-6">
                    <div className="space-y-6">
                      {/* Tab Header */}
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <h2 className="text-2xl font-semibold text-slate-900">
                            {type.type} Hierarchy
                          </h2>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                              {filteredRows.length} entries
                            </Badge>
                            {searchTerm && (
                              <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                                Filtered
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                          <DialogTrigger asChild>
                            <Button className="bg-slate-900 hover:bg-slate-800 text-white shadow-lg">
                              <Plus className="h-4 w-4 mr-2" />
                              Add Row
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="sm:max-w-md">
                            <DialogHeader>
                              <DialogTitle className="text-xl">Add New Hierarchy Row</DialogTitle>
                              <p className="text-slate-600">Add a new entry to {type.type}</p>
                            </DialogHeader>
                            <div className="space-y-4 pt-4">
                              {hierarchicalData?.levels.map((level, index) => (
                                <div key={index} className="space-y-2">
                                  <Label htmlFor={`value_${index}`} className="text-sm font-medium">
                                    {level}
                                  </Label>
                                  <Input
                                    id={`value_${index}`}
                                    value={newRowData[index] || ''}
                                    onChange={(e) => {
                                      const updated = [...newRowData];
                                      updated[index] = e.target.value;
                                      setNewRowData(updated);
                                    }}
                                    placeholder={`Enter ${level} value`}
                                    className="border-slate-200 focus:border-slate-400"
                                  />
                                </div>
                              ))}
                              <div className="flex justify-end gap-3 pt-4">
                                <Button 
                                  type="button" 
                                  variant="outline" 
                                  onClick={() => {
                                    setIsAddDialogOpen(false);
                                    setNewRowData(new Array(hierarchicalData?.levels.length || 0).fill(''));
                                  }}
                                >
                                  Cancel
                                </Button>
                                <Button 
                                  onClick={handleAddRow}
                                  className="bg-slate-900 hover:bg-slate-800"
                                >
                                  Add Row
                                </Button>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>

                      {/* Modern Table */}
                      {loading ? (
                        <div className="flex items-center justify-center py-12">
                          <div className="flex items-center gap-3">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-slate-900"></div>
                            <span className="text-slate-600">Loading hierarchy data...</span>
                          </div>
                        </div>
                      ) : (
                        <div className="bg-white rounded-xl shadow-lg border border-slate-200 overflow-hidden">
                          <Table>
                            <TableHeader>
                              <TableRow className="bg-slate-50/50 border-b border-slate-200">
                                {hierarchicalData?.levels.map((level, index) => (
                                  <TableHead key={index} className="font-semibold text-slate-700 py-4">
                                    {level}
                                  </TableHead>
                                ))}
                                <TableHead className="text-right font-semibold text-slate-700 py-4">
                                  Actions
                                </TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {filteredRows.length === 0 ? (
                                <TableRow>
                                  <TableCell 
                                    colSpan={(hierarchicalData?.levels.length || 0) + 1} 
                                    className="text-center py-12 text-slate-500"
                                  >
                                    <div className="space-y-3">
                                      <Database className="h-12 w-12 mx-auto text-slate-300" />
                                      <div>
                                        <p className="font-medium">
                                          {searchTerm 
                                            ? 'No hierarchy rows match your search criteria.' 
                                            : 'No hierarchy data found.'
                                          }
                                        </p>
                                        {!searchTerm && (
                                          <p className="text-sm text-slate-400 mt-1">
                                            Upload CSV data to get started.
                                          </p>
                                        )}
                                      </div>
                                    </div>
                                  </TableCell>
                                </TableRow>
                              ) : (
                                filteredRows.map((row, rowIndex) => (
                                  <TableRow 
                                    key={row.id} 
                                    className="hover:bg-slate-50/50 transition-colors border-b border-slate-100"
                                  >
                                    {row.values.map((value, index) => (
                                      <TableCell key={index} className="py-4 font-medium text-slate-900">
                                        {value || (
                                          <span className="text-slate-400 italic">—</span>
                                        )}
                                      </TableCell>
                                    ))}
                                    {/* Fill empty cells if row has fewer values than levels */}
                                    {Array.from({ length: Math.max(0, (hierarchicalData?.levels.length || 0) - row.values.length) })
                                      .map((_, index) => (
                                        <TableCell key={`empty_${index}`} className="py-4">
                                          <span className="text-slate-400 italic">—</span>
                                        </TableCell>
                                      ))
                                    }
                                    <TableCell className="text-right py-4">
                                      <div className="flex justify-end gap-1">
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleEditRow(row)}
                                          className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleDeleteRow(row)}
                                          className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600"
                                        >
                                          <Trash2 className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </TableCell>
                                  </TableRow>
                                ))
                              )}
                            </TableBody>
                          </Table>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            </CardContent>
          </Card>
        )}

        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="text-xl">Edit Hierarchy Row</DialogTitle>
              <p className="text-slate-600">Update entry in {activeType}</p>
            </DialogHeader>
            <div className="space-y-4 pt-4">
              {hierarchicalData?.levels.map((level, index) => (
                <div key={index} className="space-y-2">
                  <Label htmlFor={`edit_value_${index}`} className="text-sm font-medium">
                    {level}
                  </Label>
                  <Input
                    id={`edit_value_${index}`}
                    value={newRowData[index] || ''}
                    onChange={(e) => {
                      const updated = [...newRowData];
                      updated[index] = e.target.value;
                      setNewRowData(updated);
                    }}
                    placeholder={`Enter ${level} value`}
                    className="border-slate-200 focus:border-slate-400"
                  />
                </div>
              ))}
              <div className="flex justify-end gap-3 pt-4">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => {
                    setIsEditDialogOpen(false);
                    setEditingRow(null);
                    setNewRowData([]);
                  }}
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleUpdateRow}
                  className="bg-slate-900 hover:bg-slate-800"
                >
                  Update Row
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
} 