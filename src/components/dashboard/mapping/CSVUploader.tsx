'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Upload, 
  FileText, 
  AlertCircle, 
  CheckCircle, 
  Database,
  Activity,
  Eye,
  XCircle,
  RefreshCw,
  Map
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import Papa from 'papaparse';
import * as ExcelJS from 'exceljs';
import type { MappingUploadResponse } from '@/types/mapping';

interface CSVRow {
  [key: string]: string;
}

interface ParsedMappingRow {
  type: string;
  level_1: string;
  value_1: string;
  level_2: string;
  value_2: string | null;
  level_3: string;
  value_3: string | null;
  selected: boolean;
  index: number;
}

interface CSVUploaderProps {
  onUploadComplete?: () => void;
}

export default function CSVUploader({ onUploadComplete }: CSVUploaderProps) {
  const [file, setFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [parsedData, setParsedData] = useState<ParsedMappingRow[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [clearExisting, setClearExisting] = useState(false);
  const [result, setResult] = useState<MappingUploadResponse | null>(null);
  const { toast } = useToast();

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    const validFile = files.find(
      (file) =>
        file.type === 'text/csv' ||
        file.name.endsWith('.csv') ||
        file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.name.endsWith('.xlsx')
    );

    if (validFile) {
      setFile(validFile);
      parseFile(validFile);
    } else {
      toast({
        title: 'Invalid File',
        description: 'Please upload a CSV or XLSX file.',
        variant: 'destructive',
      });
    }
  }, [toast]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      parseFile(selectedFile);
    }
  };

  const parseFile = async (file: File) => {
    setLoading(true);
    setResult(null);
    setShowPreview(false);

    try {
      const content = await file.arrayBuffer();
      let parsedData: CSVRow[] = [];
      let headers: string[] = [];

      if (file.name.endsWith('.xlsx')) {
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.load(content);
        const worksheet = workbook.getWorksheet(1);
        
        if (!worksheet) {
          throw new Error('No worksheet found in Excel file');
        }

        // Get headers from first row
        const headerRow = worksheet.getRow(1);
        headers = [];
        headerRow.eachCell((cell, colNumber) => {
          if (cell.value) {
            headers[colNumber - 1] = cell.value.toString().trim();
          }
        });

        // Parse data rows
        parsedData = [];
        worksheet.eachRow((row, rowNumber) => {
          if (rowNumber === 1) return; // Skip header row
          
          const rowData: CSVRow = {};
          row.eachCell((cell, colNumber) => {
            const header = headers[colNumber - 1];
            if (header && cell.value) {
              rowData[header] = cell.value.toString().trim();
            }
          });
          
          if (Object.keys(rowData).length > 0) {
            parsedData.push(rowData);
          }
        });
      } else {
        const text = new TextDecoder().decode(content);
        const parseResult = Papa.parse<CSVRow>(text, {
          header: true,
          skipEmptyLines: true,
          transformHeader: (header) => header.trim(),
        });

        if (parseResult.errors.length > 0) {
          throw new Error('Failed to parse CSV: ' + parseResult.errors[0].message);
        }

        parsedData = parseResult.data;
        headers = parseResult.meta.fields || [];
      }

      // Validate and transform data for mapping structure
      const mappingRows: ParsedMappingRow[] = [];
      
      for (let i = 0; i < parsedData.length; i++) {
        const row = parsedData[i];
        
        // Expected CSV structure: type, level 1, Value 1, Level 2, Value 2, Level 3, Value 3
        // Access data by header names or fallback to positional access
        const type = row[headers[0]] || row['type'] || '';
        const level1 = row[headers[1]] || row['level 1'] || '';
        const value1 = row[headers[2]] || row['Value 1'] || '';
        const level2 = row[headers[3]] || row['Level 2'] || '';
        const value2 = row[headers[4]] || row['Value 2'] || '';
        const level3 = row[headers[5]] || row['Level 3'] || '';
        const value3 = row[headers[6]] || row['Value 3'] || '';

        // Skip rows without required fields
        if (!type?.trim() || !level1?.trim() || !value1?.trim()) continue;

        mappingRows.push({
          type: type.trim(),
          level_1: level1.trim(),
          value_1: value1.trim(),
          level_2: level2?.trim() || '',
          value_2: value2?.trim() || null,
          level_3: level3?.trim() || '',
          value_3: value3?.trim() || null,
          selected: true,
          index: i,
        });
      }

      if (mappingRows.length === 0) {
        throw new Error('No valid mapping data found. Ensure your CSV has the required structure.');
      }

      setParsedData(mappingRows);
      setShowPreview(true);
      
      toast({
        title: 'Success',
        description: `Parsed ${mappingRows.length} mapping entries from ${file.name}`,
      });
    } catch (error) {
      console.error('File parsing error:', error);
      toast({
        title: 'Parse Error',
        description: error instanceof Error ? error.message : 'Failed to parse file',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async () => {
    const selectedRows = parsedData.filter(row => row.selected);
    
    if (selectedRows.length === 0) {
      toast({
        title: 'No Data Selected',
        description: 'Please select at least one row to upload.',
        variant: 'destructive',
      });
      return;
    }

    setUploading(true);
    setProgress(0);

    const progressInterval = setInterval(() => {
      setProgress(prev => Math.min(prev + 10, 90));
    }, 300);

    try {
      const response = await fetch('/api/mapping-tables/upload', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mappings: selectedRows.map(row => ({
            type: row.type,
            level_1: row.level_1,
            value_1: row.value_1,
            level_2: row.level_2,
            value_2: row.value_2,
            level_3: row.level_3,
            value_3: row.value_3,
          })),
          clearExisting: clearExisting
        }),
      });

      const uploadResult: MappingUploadResponse = await response.json();
      setProgress(100);
      setResult(uploadResult);

      if (uploadResult.success) {
        toast({
          title: 'Upload Successful',
          description: `Successfully uploaded ${uploadResult.stats?.insertedCount || 0} mapping entries`,
        });
        
        if (onUploadComplete) {
          onUploadComplete();
        }
      } else {
        toast({
          title: 'Upload Failed',
          description: uploadResult.error || 'Failed to upload data',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error uploading data:', error);
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      });
      toast({
        title: 'Upload Error',
        description: 'Failed to upload data to server',
        variant: 'destructive',
      });
    } finally {
      clearInterval(progressInterval);
      setUploading(false);
    }
  };

  const resetUpload = () => {
    setFile(null);
    setParsedData([]);
    setShowPreview(false);
    setResult(null);
    setProgress(0);
    setClearExisting(false);
  };

  const toggleRowSelection = (index: number) => {
    const updated = parsedData.map(row =>
      row.index === index ? { ...row, selected: !row.selected } : row
    );
    setParsedData(updated);
  };

  const toggleSelectAll = () => {
    const allSelected = parsedData.every(row => row.selected);
    const updated = parsedData.map(row => ({
      ...row,
      selected: !allSelected,
    }));
    setParsedData(updated);
  };

  const loadSampleData = () => {
    // Create a sample file and parse it
    const sampleCSV = `level 1,Value 1,Level 2,Value 2,Level 3,Value 3
Property Type,Multi-Family,Subproperty Type,Affordable Housing,,
Property Type,Office,Subproperty Type,Medical Office Buildings,,
U.S Regions,Southeast,U.S Regions States,Florida,,
Capital Position,Limited Partner (LP),,,,
Loan Type,Bridge,,,,`;
    
    const blob = new Blob([sampleCSV], { type: 'text/csv' });
    const sampleFile = new File([blob], 'sample-mapping-data.csv', { type: 'text/csv' });
    
    setFile(sampleFile);
    parseFile(sampleFile);
  };

  const selectedCount = parsedData.filter(row => row.selected).length;
  const typeGroups = parsedData.reduce((acc, row) => {
    if (!acc[row.type]) acc[row.type] = 0;
    acc[row.type]++;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="space-y-6">
      {/* Upload Section */}
      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl flex items-center gap-2">
            <Map className="h-6 w-6 text-blue-600" />
            Upload Mapping Data
          </CardTitle>
          <p className="text-gray-600">
            Upload CSV or XLSX files containing hierarchical mapping data with 3 levels of structure.
          </p>
        </CardHeader>
        <CardContent>
          {/* Instructions */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg mb-6 border border-blue-200">
            <h3 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Expected CSV Format:
            </h3>
            <p className="text-blue-800 text-sm mb-2">
              Your file should have 6 columns: <strong>level 1, Value 1, Level 2, Value 2, Level 3, Value 3</strong>
            </p>
            <div className="text-blue-700 text-xs space-y-1">
              <p>• <strong>Value 1</strong> is required and will be used as the mapping type</p>
              <p>• <strong>Value 2</strong> and <strong>Value 3</strong> are optional</p>
              <p>• Empty rows will be automatically skipped</p>
              <p>• Supports both CSV and XLSX file formats</p>
            </div>
          </div>

          {!file && (
            <div
              className={`border-2 border-dashed rounded-xl p-12 text-center transition-all duration-200 ${
                dragActive
                  ? 'border-blue-500 bg-blue-50/50 scale-[1.02]'
                  : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50/50'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <div className="flex flex-col items-center">
                <div className="p-4 bg-blue-100 rounded-full mb-4">
                  <FileText className="h-12 w-12 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Drop your files here
                </h3>
                <p className="text-gray-500 mb-6 max-w-sm">
                  Support for CSV and XLSX files. Drag and drop or click to browse.
                </p>
                <div className="flex gap-3">
                  <input
                    type="file"
                    accept=".csv,.xlsx,text/csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    onChange={handleFileChange}
                    className="hidden"
                    id="mapping-file-input"
                  />
                  <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700">
                    <label htmlFor="mapping-file-input" className="cursor-pointer">
                      <Upload className="h-5 w-5 mr-2" />
                      Browse Files
                    </label>
                  </Button>
                  <Button variant="outline" size="lg" onClick={loadSampleData}>
                    <Database className="h-5 w-5 mr-2" />
                    Load Sample
                  </Button>
                </div>
              </div>
            </div>
          )}

          {file && loading && (
            <div className="text-center py-12">
              <div className="inline-flex items-center gap-3 bg-white p-6 rounded-xl shadow-sm">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <div className="text-left">
                  <p className="font-medium text-gray-900">Processing file...</p>
                  <p className="text-sm text-gray-500">Analyzing mapping structure</p>
                </div>
              </div>
            </div>
          )}

          {file && showPreview && !result && (
            <div className="space-y-6">
              {/* File Info Card */}
              <Card className="bg-gradient-to-r from-gray-50 to-slate-50 border border-gray-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="p-3 bg-blue-100 rounded-lg">
                        <FileText className="h-8 w-8 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{file.name}</h3>
                        <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                          <span className="flex items-center gap-1">
                            <Database className="h-4 w-4" />
                            {parsedData.length} rows
                          </span>
                          <span className="flex items-center gap-1">
                            <Activity className="h-4 w-4" />
                            {(file.size / 1024).toFixed(1)} KB
                          </span>
                          <span className="flex items-center gap-1">
                            <Map className="h-4 w-4" />
                            {Object.keys(typeGroups).length} types
                          </span>
                        </div>
                      </div>
                    </div>
                    <Button variant="outline" onClick={resetUpload}>
                      Remove
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-blue-700">Total Records</p>
                        <p className="text-2xl font-bold text-blue-900">{parsedData.length}</p>
                      </div>
                      <Database className="h-8 w-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-green-700">Selected</p>
                        <p className="text-2xl font-bold text-green-900">{selectedCount}</p>
                      </div>
                      <Eye className="h-8 w-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-purple-700">Mapping Types</p>
                        <p className="text-2xl font-bold text-purple-900">{Object.keys(typeGroups).length}</p>
                      </div>
                      <Map className="h-8 w-8 text-purple-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-orange-700">File Size</p>
                        <p className="text-2xl font-bold text-orange-900">{(file.size / 1024).toFixed(0)}</p>
                        <p className="text-xs text-orange-600">KB</p>
                      </div>
                      <FileText className="h-8 w-8 text-orange-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Upload Options */}
              <Card className="border-yellow-200 bg-gradient-to-r from-yellow-50 to-amber-50">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-yellow-100 rounded-lg">
                      <AlertCircle className="h-5 w-5 text-yellow-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-yellow-900">Upload Options</h3>
                      <p className="text-sm text-yellow-700 mt-1">
                        Configure how the data should be uploaded to the system.
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="clearExisting"
                        checked={clearExisting}
                        onCheckedChange={setClearExisting}
                      />
                      <Label htmlFor="clearExisting" className="text-sm font-medium">
                        Clear existing data
                      </Label>
                    </div>
                  </div>
                  {clearExisting && (
                    <div className="mt-4 p-3 bg-red-50 rounded-lg border border-red-200">
                      <p className="text-sm text-red-800 flex items-center gap-2">
                        <AlertCircle className="h-4 w-4" />
                        <strong>Warning:</strong> This will delete all existing mapping data before uploading new data.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Action Bar */}
              <div className="flex items-center justify-between p-4 bg-white rounded-lg border shadow-sm">
                <div className="flex items-center gap-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleSelectAll}
                    className="flex items-center gap-2"
                  >
                    <Checkbox
                      checked={parsedData.length > 0 && parsedData.every(row => row.selected)}
                    />
                    Select All ({selectedCount}/{parsedData.length})
                  </Button>

                  <div className="flex gap-2">
                    {Object.entries(typeGroups).slice(0, 3).map(([type, count]) => (
                      <Badge key={type} variant="outline" className="bg-blue-50 text-blue-800 border-blue-200">
                        {type} ({count})
                      </Badge>
                    ))}
                    {Object.keys(typeGroups).length > 3 && (
                      <Badge variant="outline" className="bg-gray-50 text-gray-600">
                        +{Object.keys(typeGroups).length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>

                {selectedCount > 0 && (
                  <Button
                    onClick={handleUpload}
                    disabled={uploading}
                    size="lg"
                    className="bg-blue-600 hover:bg-blue-700 shadow-lg"
                  >
                    {uploading ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Processing...
                      </>
                    ) : (
                      <>
                        <Upload className="h-5 w-5 mr-2" />
                        Upload {selectedCount} Records
                      </>
                    )}
                  </Button>
                )}
              </div>

              {uploading && (
                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                          <div>
                            <p className="font-medium text-blue-900">
                              Processing {selectedCount} mapping records...
                            </p>
                            <p className="text-sm text-blue-700">
                              {clearExisting ? 'Clearing existing data and ' : ''}Uploading to database
                            </p>
                          </div>
                        </div>
                        <span className="text-lg font-bold text-blue-900">{progress}%</span>
                      </div>
                      <Progress value={progress} className="w-full h-2" />
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Preview Table */}
              <Card className="overflow-hidden shadow-lg">
                <CardHeader className="bg-gray-50 border-b">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Data Preview</CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                        6 columns
                      </Badge>
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        {parsedData.length} rows
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="max-h-96 overflow-auto">
                    <Table>
                      <TableHeader className="sticky top-0 bg-gray-100">
                        <TableRow>
                          <TableHead className="w-16 sticky left-0 bg-gray-100 z-10 border-r">Select</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Level 1</TableHead>
                          <TableHead>Value 1</TableHead>
                          <TableHead>Level 2</TableHead>
                          <TableHead>Value 2</TableHead>
                          <TableHead>Level 3</TableHead>
                          <TableHead>Value 3</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {parsedData.slice(0, 10).map((row) => (
                          <TableRow
                            key={row.index}
                            className={`hover:bg-gray-50 transition-colors ${
                              row.selected ? 'bg-blue-50' : ''
                            }`}
                          >
                            <TableCell className="sticky left-0 bg-white z-10 border-r">
                              <Checkbox
                                checked={row.selected}
                                onCheckedChange={() => toggleRowSelection(row.index)}
                              />
                            </TableCell>
                            <TableCell className="font-medium">{row.type}</TableCell>
                            <TableCell>{row.level_1}</TableCell>
                            <TableCell>{row.value_1}</TableCell>
                            <TableCell>{row.level_2 || '-'}</TableCell>
                            <TableCell>{row.value_2 || '-'}</TableCell>
                            <TableCell>{row.level_3 || '-'}</TableCell>
                            <TableCell>{row.value_3 || '-'}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                    {parsedData.length > 10 && (
                      <div className="p-4 bg-gray-50 border-t text-center">
                        <p className="text-sm text-gray-600">
                          Showing first 10 rows of {parsedData.length} total rows
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {result && (
            <div className="space-y-6">
              {result.success ? (
                <Alert className="border-green-200 bg-green-50">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                                     <AlertDescription className="text-green-800">
                     <span className="font-semibold">Success!</span> 
                     Processed {selectedCount} mapping records successfully.
                     {result.stats?.insertedCount && ` Added ${result.stats.insertedCount} new entries.`}
                     {result.stats?.skippedCount && ` Skipped ${result.stats.skippedCount} entries.`}
                   </AlertDescription>
                </Alert>
              ) : (
                <Alert variant="destructive">
                  <XCircle className="h-5 w-5" />
                  <AlertDescription>
                    <span className="font-semibold">Upload Failed:</span> {result.error || 'Unknown error occurred'}
                  </AlertDescription>
                </Alert>
              )}

              {result.success && result.stats && (
                <Card className="shadow-lg">
                  <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Database className="h-5 w-5 text-green-600" />
                      Upload Results
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                                         <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                       <div className="text-center p-3 bg-blue-50 rounded-lg">
                         <p className="text-2xl font-bold text-blue-600">{result.stats.totalMappings || selectedCount}</p>
                         <p className="text-sm text-blue-700">Total Processed</p>
                       </div>
                       <div className="text-center p-3 bg-green-50 rounded-lg">
                         <p className="text-2xl font-bold text-green-600">{result.stats.insertedCount || 0}</p>
                         <p className="text-sm text-green-700">New Added</p>
                       </div>
                       <div className="text-center p-3 bg-yellow-50 rounded-lg">
                         <p className="text-2xl font-bold text-yellow-600">{result.stats.skippedCount || 0}</p>
                         <p className="text-sm text-yellow-700">Skipped</p>
                       </div>
                     </div>
                  </CardContent>
                </Card>
              )}

              <div className="flex flex-wrap gap-3">
                <Button onClick={resetUpload} variant="outline" size="lg">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Another File
                </Button>
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                  size="lg"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh View
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 