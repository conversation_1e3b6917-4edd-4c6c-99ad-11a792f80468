"use client";

import React, { useEffect, useState, useMemo } from "react";
import {
  Newspaper,
  Check,
  AlertCircle,
  Clock,
  ArrowUpRight,
  RefreshCcw,
  LucideIcon,
  Calendar as CalendarIcon,
  ChevronUp,
  ChevronDown,
  BarChart,
  BarChart2,
  ChevronLeft,
  ChevronRight,
  Globe,
  FileText,
  Handshake,
  Plus,
  Building2,
  Users,
  Cpu,
  Database,
} from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { format, subDays, addDays } from "date-fns";
import { useInView } from "react-intersection-observer";
import { DealNewsChat } from "./news/DealNewsChat";
import NewsDetail from "./news/NewsDetail";
import ExtractedDealsView from "./news/ExtractedDealsView";
import ScrapingManager from "./news/ScrapingManager";

// New imports for date picker
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import {
  Pop<PERSON>,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

// @ts-nocheck

interface CardProps {
  children: React.ReactNode;
  className?: string;
}

interface CardSectionProps {
  children: React.ReactNode;
}

type SourceStatus = "active" | "pending" | "error";

interface Source {
  name: string;
  type: string;
  status: SourceStatus;
  lastChecked: string;
}

interface SourceItemProps extends Source {}

interface StatusConfig {
  [key: string]: string;
}

interface StatusIcons {
  [key: string]: LucideIcon;
}

interface NewsMonitorProps {
  isActive: boolean;
}

interface NewsItem {
  id: number;
  news_title: string;
  news_date: string;
  news_text: string;
  raw_html: string;
  url: string;
  is_relevant?: boolean;
  created_at: string;
  extracted?: boolean;
  processed?: boolean;
  fetched?: boolean;
}

interface NewsState {
  news: NewsItem[];
  isLoading: boolean;
  selectedNews: NewsItem | null;
  page: number;
  hasMore: boolean;
  totalCount: number;
  lastId?: number;
  error?: string;
  showRawHtml?: boolean;
}

interface ProcessingResult {
  id: number;
  originalHtml: string;
  cleanedText: string;
  extractedContent: {
    title: string;
    date: string | null;
    body: string;
  };
}

interface SourceStats {
  news_source: string;
  total_count: number;
  today_count: number; // For overview data
  date_count?: number; // For date-specific stats
}

interface DateStats {
  dateTotal: number;
  sourceStats: SourceStats[];
  hourlyStats: HourlyStats[];
  date: string;
}

interface HourlyStats {
  hour: number;
  count: number;
}

const Card: React.FC<CardProps> = ({ children, className = "" }) => (
  <div
    className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}
  >
    {children}
  </div>
);

const CardHeader: React.FC<CardSectionProps> = ({ children }) => (
  <div className="border-b border-gray-200 p-4 sm:p-6">{children}</div>
);

const CardContent: React.FC<CardSectionProps> = ({ children }) => (
  <div className="p-4 sm:p-6">{children}</div>
);

const SourceItem: React.FC<SourceItemProps> = ({
  name,
  status,
  lastChecked,
  type,
}) => {
  const statusColors: StatusConfig = {
    active: "bg-green-50 text-green-700",
    pending: "bg-yellow-50 text-yellow-700",
    error: "bg-red-50 text-red-700",
  };

  const statusIcons: StatusIcons = {
    active: Check,
    pending: Clock,
    error: AlertCircle,
  };

  const Icon = statusIcons[status];

  const formattedLastChecked =
    lastChecked && !isNaN(Date.parse(lastChecked))
      ? format(new Date(lastChecked), "MMM d, yyyy h:mm a")
      : "N/A";

  return (
    <div className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0">
      <div className="flex items-center gap-3">
        <div className="flex-shrink-0">
          <Newspaper className="h-6 w-6 text-gray-400" />
        </div>
        <div>
          <h4 className="text-base font-medium text-gray-900">{name}</h4>
          <p className="text-sm text-gray-600">{type}</p>
        </div>
      </div>
      <div className="flex items-center gap-4">
        <span className="text-sm text-gray-600">
          Last checked: {formattedLastChecked}
        </span>
        <span
          className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusColors[status]}`}
        >
          <Icon className="w-4 h-4 mr-1" />
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </span>
      </div>
    </div>
  );
};

// Replace the SimpleBarChart component with a more compact visualization
const SourceBadges = ({
  data,
  title,
}: {
  data: SourceStats[];
  title: string;
}) => {
  if (!data || data.length === 0) {
    return (
      <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
        No data available
      </div>
    );
  }

  // Only show sources with count > 0
  const filteredData = data.filter((item) => {
    const count =
      "date_count" in item ? item.date_count || 0 : item.today_count;
    return count > 0;
  });

  if (filteredData.length === 0) {
    return (
      <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
        No articles for the selected period
      </div>
    );
  }

  // Different colors for different sources
  const getSourceColor = (source: string) => {
    const colors: Record<string, string> = {
      bisnow: "bg-blue-100 border-blue-300 text-blue-700",
      therealdeal: "bg-green-100 border-green-300 text-green-700",
      pincus: "bg-purple-100 border-purple-300 text-purple-700",
      globest: "bg-yellow-100 border-yellow-300 text-yellow-700",
    };
    return (
      colors[source.toLowerCase()] ||
      "bg-gray-100 border-gray-300 text-gray-700"
    );
  };

  // Sort by count (highest first)
  const sortedData = [...filteredData].sort((a, b) => {
    const countA = "date_count" in a ? a.date_count || 0 : a.today_count;
    const countB = "date_count" in b ? b.date_count || 0 : b.today_count;
    return countB - countA;
  });

  return (
    <div className="p-4 bg-white rounded-lg border">
      <h3 className="text-sm font-medium text-gray-900 mb-3">{title}</h3>
      <div className="flex flex-wrap gap-2">
        {sortedData.map((item) => {
          const count =
            "date_count" in item ? item.date_count || 0 : item.today_count;
          const sourceColor = getSourceColor(item.news_source);

          return (
            <div
              key={item.news_source}
              className={`px-3 py-1.5 rounded-full border ${sourceColor} flex items-center`}
            >
              <span className="text-sm font-medium">{item.news_source}</span>
              <span className="ml-2 bg-white text-xs font-semibold rounded-full px-2 py-0.5">
                {count}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Replace the HourlyNodes component with a badge-style visualization similar to SourceBadges
const HourlyNodes = ({ data }: { data: HourlyStats[] }) => {
  if (!data || data.length === 0) {
    return (
      <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
        No hourly data available
      </div>
    );
  }

  // Filter out hours with 0 count
  const filteredData = data.filter((item) => item.count > 0);

  if (filteredData.length === 0) {
    return (
      <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
        No articles for the selected period
      </div>
    );
  }

  // Sort by count (highest first)
  const sortedData = [...filteredData].sort((a, b) => b.count - a.count);

  // Color based on time of day
  const getTimeColor = (hour: number) => {
    const colors: Record<string, string> = {
      morning: "bg-blue-100 border-blue-300 text-blue-700", // 5am-11am
      afternoon: "bg-yellow-100 border-yellow-300 text-yellow-700", // 12pm-4pm
      evening: "bg-orange-100 border-orange-300 text-orange-700", // 5pm-8pm
      night: "bg-indigo-100 border-indigo-300 text-indigo-700", // 9pm-4am
    };

    if (hour >= 5 && hour <= 11) return colors["morning"];
    if (hour >= 12 && hour <= 16) return colors["afternoon"];
    if (hour >= 17 && hour <= 20) return colors["evening"];
    return colors["night"];
  };

  // Format hour display
  const formatHour = (hour: number) => {
    if (hour === 0) return "12 AM";
    if (hour === 12) return "12 PM";
    return hour < 12 ? `${hour} AM` : `${hour - 12} PM`;
  };

  return (
    <div className="p-4 bg-white rounded-lg border">
      <h3 className="text-sm font-medium text-gray-900 mb-3">
        Hourly Distribution
      </h3>
      <div className="flex flex-wrap gap-2">
        {sortedData.map((item) => {
          const timeColor = getTimeColor(item.hour);

          return (
            <div
              key={item.hour}
              className={`px-3 py-1.5 rounded-full border ${timeColor} flex items-center`}
              title={`${item.count} articles at ${formatHour(item.hour)}`}
            >
              <span className="text-sm font-medium">
                {formatHour(item.hour)}
              </span>
              <span className="ml-2 bg-white text-xs font-semibold rounded-full px-2 py-0.5">
                {item.count}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const NewsMonitor: React.FC<NewsMonitorProps> = ({ isActive }) => {
  // Get today in UTC for initial state
  const getTodayInUTC = () => {
    const today = new Date();
    return new Date(
      Date.UTC(today.getFullYear(), today.getMonth(), today.getDate())
    );
  };

  const [activeTab, setActiveTab] = useState("overview");
  const [overviewData, setOverviewData] = useState<any>({
    sourcesLastUpdated: [],
    sourceStats: [],
    todayTotal: 0,
  });
  const [isOverviewLoading, setIsOverviewLoading] = useState(false);
  const [dateStats, setDateStats] = useState<DateStats>({
    dateTotal: 0,
    sourceStats: [],
    hourlyStats: [],
    date: new Date().toISOString().split("T")[0],
  });
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [datePickerOpen, setDatePickerOpen] = useState(false);
  const [processingResults, setProcessingResults] = useState<
    ProcessingResult[]
  >([]);
  const [processingLogs, setProcessingLogs] = useState<string[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const [bisnowState, setBisnowState] = useState<NewsState>({
    news: [],
    isLoading: false,
    selectedNews: null,
    page: 0,
    hasMore: true,
    totalCount: 0,
  });

  const [trdState, setTrdState] = useState<NewsState>({
    news: [],
    isLoading: false,
    selectedNews: null,
    page: 0,
    hasMore: true,
    totalCount: 0,
  });

  const [pincusState, setPincusState] = useState<NewsState>({
    news: [],
    isLoading: false,
    selectedNews: null,
    page: 0,
    hasMore: true,
    totalCount: 0,
  });

  const [globestState, setGlobestState] = useState<NewsState>({
    news: [],
    isLoading: false,
    selectedNews: null,
    page: 0,
    hasMore: true,
    totalCount: 0,
  });

  const [bisnowLoadMoreRef, bisnowInView] = useInView();
  const [trdLoadMoreRef, trdInView] = useInView();
  const [pincusLoadMoreRef, pincusInView] = useInView();
  const [globestLoadMoreRef, globestInView] = useInView();

  // New state for detail view
  const [detailView, setDetailView] = useState<{
    isVisible: boolean;
    newsId: string | null;
    sourceTab: string;
  }>({
    isVisible: false,
    newsId: null,
    sourceTab: "",
  });

  // Inside the DealNewsMonitor component, add the state variable to track collapsible sections
  const [expandedSections, setExpandedSections] = useState({
    dateStats: false,
  });

  // Add filter state
  const [newsFilters, setNewsFilters] = useState({
    onlyFetched: false,
    onlyExtracted: false,
    titleSearch: '',
    orderBy: 'id' as 'id' | 'updated_at'
  });

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  // Create a reusable section header component
  const SectionHeader = ({
    title,
    isOpen,
    onToggle,
    icon,
    actions,
  }: {
    title: string;
    isOpen: boolean;
    onToggle: () => void;
    icon?: React.ReactNode;
    actions?: React.ReactNode;
  }) => (
    <div
      className="flex justify-between items-center border-b border-gray-200 py-3 px-4 bg-gray-50 rounded-t-lg cursor-pointer"
      onClick={onToggle}
    >
      <div className="flex items-center gap-2">
        {icon}
        <h3 className="text-base font-medium text-gray-800">{title}</h3>
      </div>
      <div className="flex items-center gap-2">
        {actions}
        {isOpen ? (
          <ChevronUp className="h-4 w-4 text-gray-500" />
        ) : (
          <ChevronDown className="h-4 w-4 text-gray-500" />
        )}
      </div>
    </div>
  );

  const fetchOverviewData = async () => {
    setIsOverviewLoading(true);
    try {
      console.log('Fetching overview data...');
      const response = await fetch("/api/deal-news?source=stats");
      if (!response.ok) {
        throw new Error(`Failed to fetch overview data: ${response.status} ${response.statusText}`);
      }
      const data = await response.json();
      console.log('Overview data received:', data);
      
      // Ensure we have default values
      const processedData = {
        totalUrls: data.totalUrls || 0,
        processedItems: data.processedItems || 0,
        activeSources: data.activeSources || 0,
        dealsCaptured: data.dealsCaptured || 0,
        todayTotal: data.todayTotal || 0,
        todayDeals: data.todayDeals || 0,
        todayCompanies: data.todayCompanies || 0,
        todayPersons: data.todayPersons || 0,
        sourcesLastUpdated: data.sourcesLastUpdated || [],
        sourceStats: data.sourceStats || [],
        lastUpdated: data.lastUpdated && !isNaN(Date.parse(data.lastUpdated))
          ? format(new Date(data.lastUpdated), "MMM d, yyyy h:mm a")
          : "N/A"
      };
      
      console.log('Processed overview data:', processedData);
      setOverviewData(processedData);
    } catch (error: any) {
      console.error("Error fetching overview data:", error);
      // Set default values on error
      setOverviewData({
        totalUrls: 0,
        processedItems: 0,
        activeSources: 0,
        dealsCaptured: 0,
        todayTotal: 0,
        todayDeals: 0,
        todayCompanies: 0,
        todayPersons: 0,
        sourcesLastUpdated: [],
        sourceStats: [],
        lastUpdated: "Error loading data"
      });
    } finally {
      setIsOverviewLoading(false);
    }
  };

  const fetchDateStats = async (date?: string) => {
    try {
      console.log('Fetching date stats for:', date || 'today');
      // Construct URL with optional date parameter
      const url = new URL("/api/deal-news/stats", window.location.origin);
      if (date && date !== "today") {
        url.searchParams.set("date", date);
      }

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch date stats: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Date stats received:', data);

      // Update the selectedDate state to match the date returned from the API in UTC
      if (data.date) {
        // Create a UTC date from the API response
        const [year, month, day] = data.date.split("-").map(Number);
        const apiDate = new Date(Date.UTC(year, month - 1, day)); // month is 0-indexed

        // Only update if it's a valid date and different from current selection
        if (!isNaN(apiDate.getTime())) {
          const currentDateStr = format(selectedDate, "yyyy-MM-dd");
          const apiDateStr = format(apiDate, "yyyy-MM-dd");

          if (currentDateStr !== apiDateStr) {
            setSelectedDate(apiDate);
          }
        }
      }

      // Ensure we have default values
      const processedData = {
        dateTotal: data.dateTotal || 0,
        sourceStats: data.sourceStats || [],
        hourlyStats: data.hourlyStats || [],
        date: data.date || format(new Date(), "yyyy-MM-dd")
      };

      console.log('Processed date stats:', processedData);
      setDateStats(processedData);
    } catch (error: any) {
      console.error("Error fetching date stats:", error);
      // Set default values on error
      setDateStats({
        dateTotal: 0,
        sourceStats: [],
        hourlyStats: [],
        date: format(new Date(), "yyyy-MM-dd")
      });
    }
  };

  useEffect(() => {
    if (isActive && activeTab === "overview") {
      fetchOverviewData();
      fetchDateStats(); // Load today's stats by default
    }
  }, [isActive, activeTab]);

  const loadNews = async (
    source: string,
    page: number,
    setState: React.Dispatch<React.SetStateAction<NewsState>>,
    lastId?: number,
    filters?: { onlyFetched?: boolean; onlyExtracted?: boolean; titleSearch?: string; orderBy?: 'id' | 'updated_at' }
  ) => {
    setState((prev) => ({ ...prev, isLoading: true }));

    try {
      const url = new URL("/api/deal-news", window.location.origin);
      url.searchParams.set("source", source);
      url.searchParams.set("page", page.toString());
      if (page > 0 && lastId) {
        url.searchParams.set("lastId", lastId.toString());
      }
      
      // Add filter parameters
      if (filters?.onlyFetched) {
        url.searchParams.set("onlyFetched", "true");
      }
      if (filters?.onlyExtracted) {
        url.searchParams.set("onlyExtracted", "true");
      }
      if (filters?.titleSearch && filters.titleSearch.trim()) {
        url.searchParams.set("titleSearch", filters.titleSearch.trim());
      }
      if (filters?.orderBy) {
        url.searchParams.set("orderBy", filters.orderBy);
      }

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      setState((prev) => {
        if (page === 0) {
          return {
            ...prev,
            news: data.news,
            hasMore: data.hasMore,
            totalCount: data.totalCount,
            page: data.currentPage,
            lastId: data.lastId,
            isLoading: false,
          };
        }

        const newNews = [...prev.news, ...data.news];

        // Verify no duplicates
        const ids = new Set();
        const duplicates = newNews.filter((item) => {
          if (ids.has(item.id)) return true;
          ids.add(item.id);
          return false;
        });

        if (duplicates.length > 0) {
          console.warn("Duplicate news items found:", duplicates);
        }

        return {
          ...prev,
          news: Array.from(
            new Map(newNews.map((item) => [item.id, item])).values()
          ),
          hasMore: data.hasMore,
          totalCount: data.totalCount,
          page: data.currentPage,
          lastId: data.lastId,
          isLoading: false,
        };
      });
    } catch (error: any) {
      setState((prev) => ({ ...prev, isLoading: false, error: error.message }));
      console.error("Error loading news:", error);
    }
  };

  useEffect(() => {
    if (bisnowInView && !bisnowState.isLoading && bisnowState.hasMore) {
      loadNews(
        "bisnow",
        bisnowState.page + 1,
        setBisnowState,
        bisnowState.lastId,
        newsFilters
      );
    }
  }, [bisnowInView, newsFilters]);

  useEffect(() => {
    if (trdInView && !trdState.isLoading && trdState.hasMore) {
      loadNews("therealdeal", trdState.page + 1, setTrdState, trdState.lastId, newsFilters);
    }
  }, [trdInView, newsFilters]);

  useEffect(() => {
    if (pincusInView && !pincusState.isLoading && pincusState.hasMore) {
      loadNews(
        "pincus",
        pincusState.page + 1,
        setPincusState,
        pincusState.lastId,
        newsFilters
      );
    }
  }, [pincusInView, newsFilters]);

  useEffect(() => {
    if (globestInView && !globestState.isLoading && globestState.hasMore) {
      loadNews(
        "globest",
        globestState.page + 1,
        setGlobestState,
        globestState.lastId,
        newsFilters
      );
    }
  }, [globestInView, newsFilters]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    switch (value) {
      case "bisnow":
        if (bisnowState.news.length === 0) {
          loadNews("bisnow", 0, setBisnowState, undefined, newsFilters);
        }
        break;
      case "trd":
        if (trdState.news.length === 0) {
          loadNews("therealdeal", 0, setTrdState, undefined, newsFilters);
        }
        break;
      case "pincus":
        if (pincusState.news.length === 0) {
          loadNews("pincus", 0, setPincusState, undefined, newsFilters);
        }
        break;
      case "globest":
        if (globestState.news.length === 0) {
          loadNews("globest", 0, setGlobestState, undefined, newsFilters);
        }
        break;
      case "overview":
        fetchOverviewData();
        break;
      case "scraping":
        // Scraping tab doesn't need special handling
        break;
    }
  };

  const handleRelevanceChange = async (id: number, isRelevant: boolean) => {
    try {
      const response = await fetch("/api/deal-news", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id,
          is_relevant: isRelevant,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update relevance");
      }

      // Update state for all news sources
      const updateNewsState = (
        state: NewsState,
        setState: React.Dispatch<React.SetStateAction<NewsState>>
      ) => {
        setState((prev) => ({
          ...prev,
          news: prev.news.map((item) =>
            item.id === id ? { ...item, is_relevant: isRelevant } : item
          ),
        }));
      };

      updateNewsState(bisnowState, setBisnowState);
      updateNewsState(trdState, setTrdState);
      updateNewsState(pincusState, setPincusState);
      updateNewsState(globestState, setGlobestState);
    } catch (error: any) {
      console.error("Error updating relevance:", error);
    }
  };

  const showNewsDetail = (newsId: number, sourceTab: string) => {
    setDetailView({
      isVisible: true,
      newsId: newsId.toString(),
      sourceTab,
    });
  };

  const hideNewsDetail = () => {
    setDetailView({
      isVisible: false,
      newsId: null,
      sourceTab: detailView.sourceTab,
    });
  };

  // Function to handle filter changes and reload data
  const handleFilterChange = (newFilters: { onlyFetched?: boolean; onlyExtracted?: boolean; titleSearch?: string; orderBy?: 'id' | 'updated_at' }) => {
    const updatedFilters = { ...newsFilters, ...newFilters };
    setNewsFilters(updatedFilters);
    
    // Reset all news states and reload with new filters
    const resetAndReload = (
      setState: React.Dispatch<React.SetStateAction<NewsState>>,
      source: string
    ) => {
      setState(prev => ({
        ...prev,
        news: [],
        page: 0,
        hasMore: true,
        selectedNews: null
      }));
      loadNews(source, 0, setState, undefined, updatedFilters);
    };

    // Only reload if the tab has data
    if (bisnowState.news.length > 0) {
      resetAndReload(setBisnowState, "bisnow");
    }
    if (trdState.news.length > 0) {
      resetAndReload(setTrdState, "therealdeal");
    }
    if (pincusState.news.length > 0) {
      resetAndReload(setPincusState, "pincus");
    }
    if (globestState.news.length > 0) {
      resetAndReload(setGlobestState, "globest");
    }
  };

  const renderNewsContent = (
    state: NewsState,
    setState: React.Dispatch<React.SetStateAction<NewsState>>,
    loadMoreRef: (node?: Element | null) => void,
    sourceTab: string
  ) => (
    <>
      {/* Filter Controls */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg border">
        <div className="flex flex-col gap-3">
          {/* First row - Checkboxes */}
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="onlyFetched"
                checked={newsFilters.onlyFetched}
                onChange={(e) => handleFilterChange({ onlyFetched: e.target.checked })}
                className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
              />
              <label htmlFor="onlyFetched" className="text-sm font-medium text-gray-700">
                Show only fetched articles
              </label>
            </div>
            
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="onlyExtracted"
                checked={newsFilters.onlyExtracted}
                onChange={(e) => handleFilterChange({ onlyExtracted: e.target.checked })}
                className="h-4 w-4 text-green-600 rounded border-gray-300 focus:ring-green-500"
              />
              <label htmlFor="onlyExtracted" className="text-sm font-medium text-gray-700">
                Show only extracted articles
              </label>
            </div>
          </div>
          
          {/* Second row - Search and Sort */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 flex-1">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                <FileText className="w-4 h-4" />
                Search title:
              </label>
              <input
                type="text"
                placeholder="Enter part of the title to search..."
                value={newsFilters.titleSearch}
                onChange={(e) => handleFilterChange({ titleSearch: e.target.value })}
                className="flex-1 text-sm border border-gray-300 rounded px-3 py-1 focus:ring-blue-500 focus:border-blue-500"
              />
              {newsFilters.titleSearch.trim() && (
                <button
                  onClick={() => handleFilterChange({ titleSearch: '' })}
                  className="text-gray-400 hover:text-gray-600"
                  title="Clear search"
                >
                  ×
                </button>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Sort by:</label>
              <select
                value={newsFilters.orderBy}
                onChange={(e) => handleFilterChange({ orderBy: e.target.value as 'id' | 'updated_at' })}
                className="text-sm border border-gray-300 rounded px-2 py-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="id">Newest First (ID)</option>
                <option value="updated_at">Recently Updated</option>
              </select>
            </div>
          </div>
          
          {/* Status indicators */}
          <div className="flex items-center justify-between">
            <div className="text-xs text-gray-500">
              {(newsFilters.onlyFetched || newsFilters.onlyExtracted || newsFilters.titleSearch.trim()) && (
                <span>
                  Filters active: 
                  {newsFilters.onlyFetched && " • Fetched only"}
                  {newsFilters.onlyExtracted && " • Extracted only"}
                  {newsFilters.titleSearch.trim() && ` • Title: "${newsFilters.titleSearch}"`}
                  {newsFilters.orderBy === 'updated_at' && " • By Update Time"}
                </span>
              )}
            </div>
            
            {(newsFilters.onlyFetched || newsFilters.onlyExtracted || newsFilters.titleSearch.trim() || newsFilters.orderBy === 'updated_at') && (
              <button
                onClick={() => handleFilterChange({ 
                  onlyFetched: false, 
                  onlyExtracted: false, 
                  titleSearch: '', 
                  orderBy: 'id' 
                })}
                className="text-xs text-blue-600 hover:text-blue-800 underline"
              >
                Clear all filters
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="flex gap-4 h-[calc(100vh-260px)]">
        <div className="w-1/2 overflow-hidden flex flex-col">
          <div className="flex justify-between items-center sticky top-0 bg-white py-2 border-b z-10">
            <h3 className="text-sm font-medium text-gray-700">
              Articles ({state.news.length} of {state.totalCount})
              {state.totalCount !== state.news.length && state.hasMore && (
                <span className="text-xs text-gray-500 ml-2">
                  • Loading more available
                </span>
              )}
            </h3>
            <div className="flex items-center gap-3 text-xs text-gray-500">
              <div className="flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                Sorted by: <span className="font-medium ml-1">
                  {newsFilters.orderBy === 'updated_at' ? 'Recently Updated' : 'Newest First'}
                </span>
              </div>
              {(newsFilters.onlyFetched || newsFilters.onlyExtracted || newsFilters.titleSearch.trim()) && (
                <div className="flex items-center text-blue-600">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-1"></span>
                  Filtered
                </div>
              )}
            </div>
          </div>

          <div className="overflow-y-auto flex-1 space-y-3 pr-2 pt-2">
            {state.news.map((item) => (
              <div
                key={item.id}
                className={`p-4 rounded-lg border cursor-pointer transition-all 
                  ${
                    state.selectedNews?.id === item.id ||
                    (detailView.isVisible &&
                      detailView.newsId === item.id.toString())
                      ? "bg-blue-50 border-blue-300 shadow-sm"
                      : "bg-white border-gray-200 hover:bg-gray-50"
                  }`}
                onClick={() => {
                  setState((prev) => ({
                    ...prev,
                    selectedNews: item,
                    showRawHtml: false,
                  }));
                  // Show full detail view when item is clicked
                  showNewsDetail(item.id, sourceTab);
                }}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900 line-clamp-2">
                      {item.news_title || item.url}
                    </h3>
                    {/* Status indicators */}
                    <div className="flex items-center gap-2 mt-1">
                      {item.fetched && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                          <Check className="w-3 h-3 mr-1" />
                          Fetched
                        </span>
                      )}
                      {item.processed && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-amber-100 text-amber-800">
                          <Cpu className="w-3 h-3 mr-1" />
                          Processed
                        </span>
                      )}
                      {item.extracted && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                          <Database className="w-3 h-3 mr-1" />
                          Extracted
                        </span>
                      )}
                    </div>
                  </div>
                  <div
                    onClick={(e) => e.stopPropagation()}
                    className="ml-2 flex items-center"
                  >
                    <input
                      type="checkbox"
                      checked={!!item.is_relevant}
                      onChange={(e) =>
                        handleRelevanceChange(item.id, e.target.checked)
                      }
                      className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                      title="Mark as relevant for training"
                    />
                    <span className="ml-2 text-xs text-gray-500">Relevant</span>
                  </div>
                </div>

                <div className="mt-1 flex flex-wrap items-center gap-2">
                  {item.news_date && (
                    <p className="flex items-center text-xs text-gray-500 m-0">
                      <Clock className="w-3 h-3 mr-1" />
                      Published:{" "}
                      {format(new Date(item.news_date), "MMM d, yyyy")}
                    </p>
                  )}
                  {item.created_at && (
                    <p className="flex items-center text-xs text-gray-500 m-0">
                      <Clock className="w-3 h-3 mr-1" />
                      Added:{" "}
                      {format(new Date(item.created_at), "MMM d, yyyy HH:mm")}
                    </p>
                  )}
                  <a
                    href={item.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs text-blue-600 hover:underline ml-auto"
                    onClick={(e) => e.stopPropagation()}
                  >
                    Source
                  </a>
                </div>
              </div>
            ))}

            {state.hasMore && (
              <div ref={loadMoreRef} className="py-4 text-center">
                {state.isLoading && (
                  <div className="flex items-center justify-center gap-2">
                    <RefreshCcw className="h-4 w-4 animate-spin" />
                    Loading...
                  </div>
                )}
              </div>
            )}
          </div>

          {state.totalCount > 0 && (
            <div className="sticky bottom-0 bg-white py-2 text-sm text-gray-500 text-center border-t">
              <div className="flex items-center justify-between">
                <span>
                  Showing {state.news.length} of {state.totalCount} articles
                </span>
                <div className="flex items-center gap-3">
                  {state.hasMore && (
                    <span className="text-xs text-blue-600">
                      Scroll for more • Auto-loading enabled
                    </span>
                  )}
                  {state.isLoading && (
                    <div className="flex items-center gap-1 text-xs text-amber-600">
                      <RefreshCcw className="h-3 w-3 animate-spin" />
                      Loading...
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="w-1/2 overflow-hidden h-full">
          {detailView.isVisible &&
          detailView.newsId &&
          detailView.sourceTab === sourceTab ? (
            <div className="h-full overflow-y-auto rounded-lg">
              <NewsDetail id={detailView.newsId} onBack={hideNewsDetail} />
            </div>
          ) : state.selectedNews ? (
            <div className="h-full overflow-y-auto bg-white rounded-lg border border-gray-200 p-5">
              <h2 className="text-xl font-semibold mb-4">
                {state.selectedNews.news_title || state.selectedNews.url}
              </h2>
              <div className="flex flex-col gap-1 mb-4 text-sm text-gray-500">
                {state.selectedNews.news_date && (
                  <p className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                    Published:{" "}
                    {format(
                      new Date(state.selectedNews.news_date),
                      "MMMM d, yyyy"
                    )}
                  </p>
                )}
                {state.selectedNews.created_at && (
                  <p className="flex items-center">
                    <Clock className="h-4 w-4 mr-2 text-gray-400" />
                    Added:{" "}
                    {format(
                      new Date(state.selectedNews.created_at),
                      "MMMM d, yyyy HH:mm"
                    )}
                  </p>
                )}
              </div>

              <div className="prose prose-sm max-w-none mb-4">
                {state.showRawHtml ? (
                  <pre className="whitespace-pre-wrap text-sm font-mono bg-gray-50 p-4 rounded border">
                    {state.selectedNews.raw_html || "No raw HTML available"}
                  </pre>
                ) : (
                  <div className="bg-gray-50 p-4 rounded border">
                    {state.selectedNews.news_text}
                  </div>
                )}
              </div>

              <div className="mt-4 pt-4 border-t flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setState((prev) => ({
                      ...prev,
                      showRawHtml: !prev.showRawHtml,
                    }))
                  }
                >
                  {state.showRawHtml
                    ? "Show Processed Content"
                    : "Show Raw HTML"}
                </Button>

                <Button
                  variant="default"
                  size="sm"
                  onClick={() =>
                    showNewsDetail(state.selectedNews!.id, sourceTab)
                  }
                >
                  View Full Details
                </Button>
              </div>
            </div>
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500 bg-gray-50 rounded-lg border border-gray-200">
              <div className="text-center p-6">
                <Newspaper className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                <p>Select a news item to view details</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );

  const startProcessing = async (testMode: boolean) => {
    setProcessingLogs(["Starting processing..."]);
    setProcessingResults([]);
    setIsProcessing(true);

    try {
      // Build request body, include limit only for test runs
      const requestBody: any = { testMode };
      if (testMode) {
        requestBody.limit = 3;
      }
      const startResponse = await fetch("/api/deal-news/process", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      if (!startResponse.ok) {
        throw new Error(`Failed to start processing: ${startResponse.status}`);
      }

      const startData = await startResponse.json();
      setProcessingLogs(startData.logs || []);

      // Set up polling
      const pollInterval = setInterval(async () => {
        try {
          const statusResponse = await fetch("/api/deal-news/process", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ action: "status" }),
          });

          if (!statusResponse.ok) {
            throw new Error("Status check failed");
          }

          const status = await statusResponse.json();
          console.log("Status update received:", status); // Debug log

          // Update logs and results
          if (status.logs?.length > 0) {
            setProcessingLogs(status.logs);
          }

          if (status.results?.length > 0) {
            setProcessingResults(status.results);
          }

          // Check if processing is complete
          if (!status.isProcessing) {
            clearInterval(pollInterval);
            setIsProcessing(false);
            if (!testMode) {
              fetchOverviewData();
            }
          }
        } catch (error: any) {
          console.error("Polling error:", error);
          clearInterval(pollInterval);
          setIsProcessing(false);
          setProcessingLogs((prev) => [
            ...prev,
            `Error checking status: ${error.message}`,
          ]);
        }
      }, 1000); // Poll every second

      // Safety cleanup after 5 minutes
      setTimeout(() => {
        clearInterval(pollInterval);
        setIsProcessing(false);
        setProcessingLogs((prev) => [
          ...prev,
          "Polling stopped after 5 minutes",
        ]);
      }, 300000);
    } catch (error: any) {
      console.error("Processing error:", error);
      setIsProcessing(false);
      setProcessingLogs((prev) => [...prev, `Error: ${error.message}`]);
    }
  };

  // Only show the last 100 logs to keep the UI responsive
  const logsToShow = processingLogs.slice(-100);

  // Update the handleDateSelect function to use UTC dates
  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      // Convert to UTC date
      const utcDate = new Date(
        Date.UTC(date.getFullYear(), date.getMonth(), date.getDate())
      );
      setSelectedDate(utcDate);
      // Format as YYYY-MM-DD in UTC
      const formattedDate = format(utcDate, "yyyy-MM-dd");
      fetchDateStats(formattedDate);
    } else {
      // If no date provided, use today in UTC
      const today = new Date();
      const utcToday = new Date(
        Date.UTC(today.getFullYear(), today.getMonth(), today.getDate())
      );
      setSelectedDate(utcToday);
      fetchDateStats("today");
    }
    setDatePickerOpen(false);
  };

  // Update the date navigation UI that replaces the Calendar
  const dateNavigationControls = (
    <div
      className="flex items-center gap-3"
      onClick={(e) => e.stopPropagation()}
    >
      {/* Date Navigation */}
      <div className="flex rounded-md shadow-sm">
        <Button
          variant="outline"
          size="sm"
          onClick={(e: React.MouseEvent) => {
            e.stopPropagation();
            const yesterday = subDays(new Date(dateStats.date), 1);
            const formattedDate = format(yesterday, "yyyy-MM-dd");
            fetchDateStats(formattedDate);
          }}
          className="h-8 w-8 p-0 rounded-l-md"
          title="Previous day"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={(e: React.MouseEvent) => {
            e.stopPropagation();
            fetchDateStats("today");
          }}
          className="h-8 px-4 text-xs rounded-none"
        >
          Today:{" "}
          {dateStats.date
            ? format(new Date(dateStats.date), "MMM d, yyyy")
            : "Loading..."}
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={(e: React.MouseEvent) => {
            e.stopPropagation();
            // Only go to next day if not already at today
            const currentDate = new Date(dateStats.date);
            const today = new Date();
            const todayDate = new Date(
              today.getFullYear(),
              today.getMonth(),
              today.getDate()
            );

            if (currentDate < todayDate) {
              const nextDay = addDays(currentDate, 1);
              const formattedDate = format(nextDay, "yyyy-MM-dd");
              fetchDateStats(formattedDate);
            }
          }}
          className="h-8 w-8 p-0 rounded-r-md"
          title="Next day"
          disabled={dateStats.date === format(new Date(), "yyyy-MM-dd")}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  if (!isActive) return null;

  // Helper function to safely get last checked time for a source
  const getLastChecked = (sourceName: string): string => {
    if (
      !overviewData?.sourcesLastUpdated ||
      !Array.isArray(overviewData.sourcesLastUpdated)
    ) {
      return "N/A";
    }

    const source = overviewData.sourcesLastUpdated.find(
      (source: any) => source?.name?.toLowerCase() === sourceName.toLowerCase()
    );

    return source?.lastChecked || "N/A";
  };

  const sources: Source[] = [
    {
      name: "Bisnow",
      type: "Real Estate News",
      status: "active",
      lastChecked: getLastChecked("bisnow"),
    },
    {
      name: "The Real Deal",
      type: "Market Intelligence",
      status: "active",
      lastChecked: getLastChecked("therealdeal"),
    },
    {
      name: "Pincus",
      type: "Real Estate News",
      status: "active",
      lastChecked: getLastChecked("pincus"),
    },
    {
      name: "Globest",
      type: "Real Estate News",
      status: "active",
      lastChecked: getLastChecked("globest"),
    },
  ];

  return (
    <Card>
      <CardContent>
        <Tabs
          defaultValue="overview"
          className="w-full"
          value={activeTab}
          onValueChange={handleTabChange}
        >
          <TabsList className="mb-6 flex flex-wrap gap-2">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="bisnow">Bisnow</TabsTrigger>
            <TabsTrigger value="trd">The Real Deal</TabsTrigger>
            <TabsTrigger value="pincus">Pincus</TabsTrigger>
            <TabsTrigger value="globest">Globest</TabsTrigger>
            <TabsTrigger value="scraping">Scraping</TabsTrigger>
            <TabsTrigger value="extracteddeals">Extracted Deals</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            {isOverviewLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <RefreshCcw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
                  <p className="text-gray-600">Loading overview data...</p>
                </div>
              </div>
            ) : (
              <div className="mb-6">
                {/* First row of metrics cards */}
                <div className="grid grid-cols-4 gap-4 mb-4">
                  <div className="bg-blue-50 rounded-xl p-4 border border-blue-100 transition-all hover:shadow-sm">
                    <div className="flex items-center gap-2 mb-2 text-blue-600">
                      <Globe className="h-5 w-5" />
                      <h3 className="text-sm font-medium">News URLs</h3>
                    </div>
                    <p className="text-2xl font-bold text-blue-700">
                      {(overviewData.totalUrls || 0).toLocaleString()}
                    </p>
                  </div>

                  <div className="bg-amber-50 rounded-xl p-4 border border-amber-100 transition-all hover:shadow-sm">
                    <div className="flex items-center gap-2 mb-2 text-amber-600">
                      <FileText className="h-5 w-5" />
                      <h3 className="text-sm font-medium">News Items</h3>
                    </div>
                    <p className="text-2xl font-bold text-amber-700">
                      {(overviewData.processedItems || 0).toLocaleString()}
                    </p>
                  </div>

                  <div className="bg-green-50 rounded-xl p-4 border border-green-100 transition-all hover:shadow-sm">
                    <div className="flex items-center gap-2 mb-2 text-green-600">
                      <Database className="h-5 w-5" />
                      <h3 className="text-sm font-medium">Active Sources</h3>
                    </div>
                    <p className="text-2xl font-bold text-green-700">
                      {overviewData.activeSources || 0}
                    </p>
                  </div>

                  <div className="bg-purple-50 rounded-xl p-4 border border-purple-100 transition-all hover:shadow-sm">
                    <div className="flex items-center gap-2 mb-2 text-purple-600">
                      <Handshake className="h-5 w-5" />
                      <h3 className="text-sm font-medium">Deals Captured</h3>
                    </div>
                    <p className="text-2xl font-bold text-purple-700">
                      {(overviewData.dealsCaptured || 0).toLocaleString()}
                    </p>
                  </div>
                </div>

                {/* Second row of metrics cards */}
                <div className="grid grid-cols-4 gap-4 mb-4">
                  <div className="bg-teal-50 rounded-xl p-4 border border-teal-100 transition-all hover:shadow-sm">
                    <div className="flex items-center gap-2 mb-2 text-teal-600">
                      <Plus className="h-5 w-5" />
                      <h3 className="text-sm font-medium">News Added Today</h3>
                    </div>
                    <p className="text-2xl font-bold text-teal-700">
                      {overviewData.todayTotal || 0}
                    </p>
                  </div>

                  <div className="bg-cyan-50 rounded-xl p-4 border border-cyan-100 transition-all hover:shadow-sm">
                    <div className="flex items-center gap-2 mb-2 text-cyan-600">
                      <BarChart2 className="h-5 w-5" />
                      <h3 className="text-sm font-medium">Deals Added Today</h3>
                    </div>
                    <p className="text-2xl font-bold text-cyan-700">
                      {overviewData.todayDeals || 0}
                    </p>
                  </div>

                  <div className="bg-cyan-50 rounded-xl p-4 border border-cyan-100 transition-all hover:shadow-sm">
                    <div className="flex items-center gap-2 mb-2 text-cyan-600">
                      <Building2 className="h-5 w-5" />
                      <h3 className="text-sm font-medium">
                        Companies Added Today
                      </h3>
                    </div>
                    <p className="text-2xl font-bold text-cyan-700">
                      {overviewData.todayCompanies || 0}
                    </p>
                  </div>

                  <div className="bg-indigo-50 rounded-xl p-4 border border-indigo-100 transition-all hover:shadow-sm">
                    <div className="flex items-center gap-2 mb-2 text-indigo-600">
                      <Users className="h-5 w-5" />
                      <h3 className="text-sm font-medium">Persons Added Today</h3>
                    </div>
                    <p className="text-2xl font-bold text-indigo-700">
                      {overviewData.todayPersons || 0}
                    </p>
                  </div>
                </div>

                {/* Last updated and process row */}
                <div className="grid grid-cols-4 gap-4">
                  <div className="bg-gray-50 rounded-xl p-4 border border-gray-100 transition-all hover:shadow-sm">
                    <div className="flex items-center gap-2 mb-2 text-gray-600">
                      <RefreshCcw className="h-5 w-5" />
                      <h3 className="text-sm font-medium">Last Updated</h3>
                    </div>
                    <p className="text-lg font-bold text-gray-700">
                      {overviewData.lastUpdated || "N/A"}
                    </p>
                  </div>

                  <div className="col-span-3">
                    <Button
                      onClick={() => setActiveTab("processing")}
                      className="w-full h-full bg-orange-500 hover:bg-orange-600 text-white shadow-sm flex items-center justify-center gap-2 text-sm"
                    >
                      <Cpu className="h-5 w-5" />
                      Process HTML Content
                    </Button>
                  </div>
                </div>
              </div>
            )}

            <button
              className="absolute top-2 right-2 inline-flex items-center gap-1 text-sm text-gray-500 hover:text-gray-700 px-2 py-1 rounded-md hover:bg-gray-100"
              onClick={fetchOverviewData}
              title="Refresh data"
              disabled={isOverviewLoading}
            >
              <RefreshCcw className={`h-4 w-4 ${isOverviewLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>

            {/* Today's Stats Section - Collapsible */}
            {/* 
              Articles by Date - Collapsible Section
              This section shows date-specific statistics with source breakdown and hourly distribution.
              Users can select today, yesterday, or a custom date using the date picker.
            */}
            <div className="mb-6 border border-gray-200 rounded-lg">
              <SectionHeader
                title="Articles by Date"
                isOpen={expandedSections.dateStats}
                onToggle={() => toggleSection("dateStats")}
                icon={<BarChart2 className="h-4 w-4 text-blue-600" />}
                actions={dateNavigationControls}
              />

              {expandedSections.dateStats && (
                <div className="p-4">
                  <div className="flex gap-4 items-start">
                    <div className="bg-white rounded-lg border p-4 w-48">
                      <div className="text-center">
                        <h4 className="text-sm text-gray-500">
                          {(() => {
                            // Get today's date in UTC
                            const today = new Date();
                            const utcToday = new Date(
                              Date.UTC(
                                today.getFullYear(),
                                today.getMonth(),
                                today.getDate()
                              )
                            );

                            // Format both dates for comparison
                            const selectedFormatted = format(
                              selectedDate,
                              "yyyy-MM-dd"
                            );
                            const todayFormatted = format(
                              utcToday,
                              "yyyy-MM-dd"
                            );

                            if (selectedFormatted === todayFormatted) {
                              return "Today's Articles";
                            } else {
                              return `Articles on ${format(
                                selectedDate,
                                "MMM d, yyyy"
                              )}`;
                            }
                          })()}
                        </h4>
                        <p className="text-3xl font-bold text-blue-600 mt-2">
                          {dateStats.dateTotal}
                        </p>
                        {dateStats.dateTotal > 0 ? (
                          <div className="mt-1 text-xs text-gray-500">
                            from{" "}
                            {
                              dateStats.sourceStats.filter(
                                (s) => (s.date_count || 0) > 0
                              ).length
                            }{" "}
                            sources
                          </div>
                        ) : (
                          <div className="mt-1 text-xs text-gray-500">
                            No articles on this date
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex-1 grid grid-cols-2 gap-4">
                      <SourceBadges
                        data={dateStats.sourceStats.map((s) => ({
                          news_source: s.news_source,
                          total_count: s.total_count,
                          today_count: 0, // Default value to satisfy the interface
                          date_count: s.date_count || 0,
                        }))}
                        title="Articles by Source"
                      />

                      <HourlyNodes data={dateStats.hourlyStats} />
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-1">
              {sources.map((source, idx) => (
                <SourceItem key={idx} {...source} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="bisnow">
            {renderNewsContent(
              bisnowState,
              setBisnowState,
              bisnowLoadMoreRef,
              "bisnow"
            )}
          </TabsContent>

          <TabsContent value="trd">
            {renderNewsContent(trdState, setTrdState, trdLoadMoreRef, "trd")}
          </TabsContent>

          <TabsContent value="pincus">
            {renderNewsContent(
              pincusState,
              setPincusState,
              pincusLoadMoreRef,
              "pincus"
            )}
          </TabsContent>

          <TabsContent value="globest">
            {renderNewsContent(
              globestState,
              setGlobestState,
              globestLoadMoreRef,
              "globest"
            )}
          </TabsContent>

          <TabsContent value="scraping">
            <ScrapingManager onRefresh={fetchOverviewData} />
          </TabsContent>

          <TabsContent value="extracteddeals">
            <ExtractedDealsView />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export { NewsMonitor };
export default NewsMonitor;
