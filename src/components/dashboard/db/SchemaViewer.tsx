import React, { useEffect } from "react";
import mermaid from "mermaid";

const Sc<PERSON>a<PERSON>iewer = () => {
  useEffect(() => {
    // Initialize mermaid
    mermaid.initialize({
      startOnLoad: true,
      securityLevel: "loose",
      theme: "default",
      logLevel: "error",
      flowchart: { curve: "basis" },
      themeVariables: {
        fontFamily: "Inter",
      },
    });

    // Trigger rendering
    mermaid.contentLoaded();
  }, []);

  const diagram = `
    erDiagram
    %% Core Business Entities
    companies {
        int company_id PK
        text company_name
        text company_linkedin
        text company_address
        text company_city
        text company_state
        text company_zip
        text company_website
        text industry
        text contacts_guid
        timestamp created_at
        timestamp updated_at
    }

    persons {
        int person_id PK
        varchar email
        varchar first_name
        varchar last_name
        varchar job_title
        varchar phone_number
        text notes
        varchar person_linkedin
        uuid contacts_guid
        int company_id FK
        timestamp created_at
        timestamp updated_at
    }

    contacts {
        int id
        uuid guid
        text email
        text first_name
        text last_name
        text phone_number
        text company_name
        text person_linkedin
        text job_title
        text industry
        text notes
        text active_campaign_id
    }

    %% Conversation and NBA Tables
    conversations {
        uuid conversation_id PK
        int person_id FK
        text title
        varchar status
        jsonb metadata
        timestamp created_at
        timestamp updated_at
    }

    messages {
        uuid message_id PK
        uuid conversation_id FK
        varchar role
        text content
        int tokens_used
        jsonb metadata
        timestamp created_at
    }

    next_best_actions {
        uuid nba_id PK
        uuid conversation_id FK
        varchar action_type
        int priority
        varchar status
        timestamp due_date
        text description
        text reasoning
        varchar created_by
        jsonb metadata
        timestamp created_at
        timestamp completed_at
    }

    action_results {
        uuid result_id PK
        uuid nba_id FK
        varchar outcome_type
        jsonb result_data
        text notes
        timestamp created_at
    }

    %% Investment Criteria Tables
    investment_criteria_types {
        int type_id PK
        varchar type_name UK
        varchar category
        text description
        boolean is_active
        timestamp created_at
    }

    investment_criteria_values {
        int value_id PK
        int type_id FK
        text value
        int parent_value_id FK
        jsonb metadata
        boolean is_active
        timestamp created_at
    }

    %% Relationships
    companies ||--o{ persons : "has"
    persons ||--o{ conversations : "participates in"
    conversations ||--o{ messages : "contains"
    conversations ||--o{ next_best_actions : "generates"
    next_best_actions ||--o{ action_results : "produces"
    investment_criteria_types ||--o{ investment_criteria_values : "has"
    investment_criteria_values ||--o{ investment_criteria_values : "parent of"
  `;

  return (
    <div className="w-full overflow-x-auto bg-white p-4 rounded-lg shadow">
      <div className="mermaid text-center min-w-[800px]">{diagram}</div>
    </div>
  );
};

export default SchemaViewer;
