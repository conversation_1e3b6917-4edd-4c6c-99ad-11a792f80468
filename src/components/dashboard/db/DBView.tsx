"use client";

import React, { useState } from "react";
import { TableViewer } from "./TableViewer";
import SchemaViewer from "./SchemaViewer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface DBViewProps {
  isActive: boolean;
}

export const DBView: React.FC<DBViewProps> = ({ isActive }) => {
  const [activeTab, setActiveTab] = useState("tables");

  if (!isActive) return null;

  const tabs = [
    { id: "tables", name: "Tables" },
    { id: "schema", name: "<PERSON>hema" },
  ];

  return (
    <div className="space-y-6">
      <div className="flex space-x-4 border-b">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`pb-4 px-4 text-sm font-medium border-b-2 transition-all ${
              activeTab === tab.id
                ? "border-blue-600 text-blue-600"
                : "border-transparent hover:border-gray-200"
            }`}
          >
            {tab.name}
          </button>
        ))}
      </div>

      {activeTab === "tables" && <TableViewer />}
      {activeTab === "schema" && <SchemaViewer />}
    </div>
  );
};
