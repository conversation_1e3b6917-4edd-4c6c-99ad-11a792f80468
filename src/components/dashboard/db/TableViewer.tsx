import React, { useState, useEffect } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Database, ChevronLeft, ChevronRight } from "lucide-react";
import { Card, CardHeader, CardContent } from "@/components/ui/card";

// Helper function to format cell values
const formatCellValue = (value: any): string => {
  if (value === null || value === undefined) return "";

  // Handle numeric values
  if (typeof value === "number") {
    // Format percentages (cap_rate, occupancy_rate, etc.)
    if (value <= 1) return `${(value * 100).toFixed(2)}%`;
    // Format currency with 2 decimal places
    return value.toFixed(2);
  }

  // Handle dates
  if (value instanceof Date) {
    return value.toLocaleDateString();
  }

  // Handle all other types
  return String(value);
};

interface TableData {
  columns: string[];
  rows: any[];
  totalRows: number;
}

export const TableViewer = () => {
  const [tables, setTables] = useState<string[]>([]);
  const [selectedTable, setSelectedTable] = useState<string>("");
  const [tableData, setTableData] = useState<TableData | null>(null);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const rowsPerPage = 10;

  useEffect(() => {
    fetchTables();
  }, []);

  useEffect(() => {
    if (selectedTable) {
      fetchTableData();
    }
  }, [selectedTable, page]);

  const fetchTables = async () => {
    try {
      const response = await fetch("/api/db/tables");
      const data = await response.json();
      setTables(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error("Error fetching tables:", error);
      setTables([]);
    }
  };

  const fetchTableData = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/db/data?table=${selectedTable}&page=${page}&limit=${rowsPerPage}`
      );
      const data = await response.json();

      if (!data || !data.columns || !data.rows) {
        console.error("Invalid data structure received:", data);
        setTableData(null);
        return;
      }

      setTableData(data);
    } catch (error) {
      console.error("Error fetching table data:", error);
      setTableData(null);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <Card className="mb-2.5">
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-2">
            {tables && tables.length > 0 ? (
              [...tables].sort().map((table) => (
                <button
                  key={table}
                  onClick={() => setSelectedTable(table)}
                  className={`px-4 py-2 text-base rounded-md transition-colors
                    ${
                      selectedTable === table
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "bg-gray-100 hover:bg-gray-200 text-gray-700"
                    }`}
                >
                  {table}
                </button>
              ))
            ) : (
              <div className="text-gray-500">No tables available</div>
            )}
          </div>
        </CardContent>
      </Card>

      {selectedTable && (
        <Card>
          <CardHeader className="pb-0">
            <h3 className="text-lg font-medium">{selectedTable}</h3>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-4">Loading...</div>
            ) : tableData && tableData.columns && tableData.rows ? (
              <>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        {tableData.columns.map((column, i) => (
                          <TableHead key={i}>{column}</TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {tableData.rows.map((row, i) => (
                        <TableRow key={i}>
                          {tableData.columns.map((column, j) => (
                            <TableCell key={j}>
                              {formatCellValue(row[column])}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-gray-500">
                    Showing {(page - 1) * rowsPerPage + 1} to{" "}
                    {Math.min(page * rowsPerPage, tableData.totalRows)} of{" "}
                    {tableData.totalRows} results
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage((p) => Math.max(1, p - 1))}
                      disabled={page === 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage((p) => p + 1)}
                      disabled={page * rowsPerPage >= tableData.totalRows}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-4 text-red-600">
                Error loading table data. Please try again.
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
