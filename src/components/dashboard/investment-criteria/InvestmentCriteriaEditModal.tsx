'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { toast } from 'sonner'
import { 
  Save, 
  X, 
  Plus, 
  Trash2, 
  DollarSign, 
  MapPin, 
  Building2, 
  Target,
  TrendingUp,
  Percent,
  Calendar,
  FileText,
  Loader2
} from 'lucide-react'
import { InvestmentCriteria } from '@/types/investment-criteria'

interface InvestmentCriteriaEditModalProps {
  criteria: InvestmentCriteria | null
  isOpen: boolean
  onClose: () => void
  onSave: (updatedCriteria: InvestmentCriteria) => void
  isLoading?: boolean
}

export default function InvestmentCriteriaEditModal({
  criteria,
  isOpen,
  onClose,
  onSave,
  isLoading = false
}: InvestmentCriteriaEditModalProps) {
  const [editedCriteria, setEditedCriteria] = useState<InvestmentCriteria | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [mappings, setMappings] = useState<any>(null)
  const [isLoadingMappings, setIsLoadingMappings] = useState(false)

  useEffect(() => {
    if (criteria) {
      setEditedCriteria({ ...criteria })
    }
  }, [criteria])

  // Fetch mappings when modal opens
  useEffect(() => {
    if (isOpen && !mappings) {
      const fetchMappings = async () => {
        setIsLoadingMappings(true)
        try {
          const response = await fetch('/api/investment-criteria/mappings')
          if (response.ok) {
            const data = await response.json()
            setMappings(data)
          }
        } catch (error) {
          console.error('Error fetching mappings:', error)
        } finally {
          setIsLoadingMappings(false)
        }
      }
      fetchMappings()
    }
  }, [isOpen, mappings])

  const handleInputChange = (field: keyof InvestmentCriteria, value: any) => {
    if (!editedCriteria) return
    
    setEditedCriteria({
      ...editedCriteria,
      [field]: value
    })
  }

  const handleArrayFieldChange = (
    field: keyof InvestmentCriteria,
    value: string,
    index: number
  ) => {
    if (!editedCriteria) return
    
    const fieldValue = editedCriteria[field]
    const currentArray = Array.isArray(fieldValue) ? [...fieldValue] : []
    
    if (index >= currentArray.length) {
      currentArray.push(value)
    } else {
      currentArray[index] = value
    }
    
    setEditedCriteria({
      ...editedCriteria,
      [field]: currentArray
    })
  }

  const addArrayItem = (field: keyof InvestmentCriteria) => {
    if (!editedCriteria) return
    
    const fieldValue = editedCriteria[field]
    const currentArray = Array.isArray(fieldValue) ? [...fieldValue] : []
    currentArray.push('')
    
    setEditedCriteria({
      ...editedCriteria,
      [field]: currentArray
    })
  }

  const removeArrayItem = (field: keyof InvestmentCriteria, index: number) => {
    if (!editedCriteria) return
    
    const fieldValue = editedCriteria[field]
    const currentArray = Array.isArray(fieldValue) ? [...fieldValue] : []
    currentArray.splice(index, 1)
    
    setEditedCriteria({
      ...editedCriteria,
      [field]: currentArray
    })
  }

  const renderArrayField = (
    field: keyof InvestmentCriteria,
    label: string,
    placeholder?: string,
    useDropdown: boolean = false,
    dropdownOptions: string[] = []
  ) => {
    if (!editedCriteria) return null
    
    const fieldValue = editedCriteria[field]
    const values = Array.isArray(fieldValue) ? fieldValue : []
    
    return (
      <div className="space-y-2">
        <Label>{label}</Label>
        {values.map((value, index) => (
          <div key={index} className="flex gap-2">
            {useDropdown ? (
              <Select
                value={value}
                onValueChange={(newValue) => handleArrayFieldChange(field, newValue, index)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={placeholder} />
                </SelectTrigger>
                <SelectContent>
                  {dropdownOptions.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <Input
                value={value}
                onChange={(e) => handleArrayFieldChange(field, e.target.value, index)}
                placeholder={placeholder}
              />
            )}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => removeArrayItem(field, index)}
              className="px-2"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        ))}
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => addArrayItem(field)}
          className="w-full"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add {label}
        </Button>
      </div>
    )
  }

  const handleSave = async () => {
    if (!editedCriteria) return
    
    setIsSaving(true)
    try {
      await onSave(editedCriteria)
      toast.success('Investment criteria updated successfully')
      onClose()
    } catch (error) {
      console.error('Error saving investment criteria:', error)
      toast.error('Failed to save investment criteria')
    } finally {
      setIsSaving(false)
    }
  }

  if (!editedCriteria) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Edit Investment Criteria
            {isLoadingMappings && (
              <Loader2 className="h-4 w-4 animate-spin ml-2" />
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <Tabs defaultValue="financial" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="financial">Financial</TabsTrigger>
              <TabsTrigger value="property">Property</TabsTrigger>
              <TabsTrigger value="loan">Loan Terms</TabsTrigger>
              <TabsTrigger value="geography">Geography</TabsTrigger>
            </TabsList>

            {/* Financial Criteria Tab */}
            <TabsContent value="financial" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Financial Criteria
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="target_return">Target Return (%)</Label>
                      <Input
                        id="target_return"
                        type="number"
                        step="0.01"
                        value={editedCriteria.target_return || ''}
                        onChange={(e) => handleInputChange('target_return', parseFloat(e.target.value) || null)}
                        placeholder="Enter target return"
                      />
                    </div>
                    <div>
                      <Label htmlFor="historical_irr">Historical IRR (%)</Label>
                      <Input
                        id="historical_irr"
                        type="number"
                        step="0.01"
                        value={editedCriteria.historical_irr || ''}
                        onChange={(e) => handleInputChange('historical_irr', parseFloat(e.target.value) || null)}
                        placeholder="Enter historical IRR"
                      />
                    </div>
                    <div>
                      <Label htmlFor="historical_em">Historical Equity Multiple</Label>
                      <Input
                        id="historical_em"
                        type="number"
                        step="0.01"
                        value={editedCriteria.historical_em || ''}
                        onChange={(e) => handleInputChange('historical_em', parseFloat(e.target.value) || null)}
                        placeholder="Enter historical equity multiple"
                      />
                    </div>
                    <div>
                      <Label htmlFor="minimum_deal_size">Minimum Deal Size ($M)</Label>
                      <Input
                        id="minimum_deal_size"
                        type="number"
                        step="0.01"
                        value={editedCriteria.minimum_deal_size || ''}
                        onChange={(e) => handleInputChange('minimum_deal_size', parseFloat(e.target.value) || null)}
                        placeholder="Enter minimum deal size"
                      />
                    </div>
                    <div>
                      <Label htmlFor="maximum_deal_size">Maximum Deal Size ($M)</Label>
                      <Input
                        id="maximum_deal_size"
                        type="number"
                        step="0.01"
                        value={editedCriteria.maximum_deal_size || ''}
                        onChange={(e) => handleInputChange('maximum_deal_size', parseFloat(e.target.value) || null)}
                        placeholder="Enter maximum deal size"
                      />
                    </div>
                    <div>
                      <Label htmlFor="min_hold_period">Minimum Hold Period (Years)</Label>
                      <Input
                        id="min_hold_period"
                        type="number"
                        step="0.01"
                        value={editedCriteria.min_hold_period || ''}
                        onChange={(e) => handleInputChange('min_hold_period', parseFloat(e.target.value) || null)}
                        placeholder="Enter minimum hold period"
                      />
                    </div>
                    <div>
                      <Label htmlFor="max_hold_period">Maximum Hold Period (Years)</Label>
                      <Input
                        id="max_hold_period"
                        type="number"
                        step="0.01"
                        value={editedCriteria.max_hold_period || ''}
                        onChange={(e) => handleInputChange('max_hold_period', parseFloat(e.target.value) || null)}
                        placeholder="Enter maximum hold period"
                      />
                    </div>
                  </div>
                  
                  {renderArrayField(
                    'financial_products', 
                    'Financial Products', 
                    'Enter financial product'
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Property Criteria Tab */}
            <TabsContent value="property" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Property & Asset Criteria
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {renderArrayField(
                    'property_types', 
                    'Property Types', 
                    'Select property type',
                    true,
                    mappings?.['Property Type']?.parents || []
                  )}
                  {renderArrayField(
                    'property_sub_categories', 
                    'Property Sub Categories', 
                    'Select sub category',
                    true,
                    mappings?.['Property Type']?.children || []
                  )}
                  {renderArrayField(
                    'strategies', 
                    'Strategies', 
                    'Select strategy',
                    true,
                    mappings?.['Strategies']?.parents || []
                  )}
                  {renderArrayField(
                    'capital_position', 
                    'Capital Position', 
                    'Select capital position',
                    true,
                    mappings?.['Capital Position']?.parents || []
                  )}
                  <div>
                    <Label htmlFor="capital_source">Capital Source</Label>
                    <Input
                      id="capital_source"
                      value={editedCriteria.capital_source || ''}
                      onChange={(e) => handleInputChange('capital_source', e.target.value)}
                      placeholder="Enter capital source"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Loan Terms Tab */}
            <TabsContent value="loan" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Loan Terms & Conditions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="min_loan_term">Minimum Loan Term (Years)</Label>
                      <Input
                        id="min_loan_term"
                        type="number"
                        step="0.01"
                        value={editedCriteria.min_loan_term || ''}
                        onChange={(e) => handleInputChange('min_loan_term', parseFloat(e.target.value) || null)}
                        placeholder="Enter minimum loan term"
                      />
                    </div>
                    <div>
                      <Label htmlFor="max_loan_term">Maximum Loan Term (Years)</Label>
                      <Input
                        id="max_loan_term"
                        type="number"
                        step="0.01"
                        value={editedCriteria.max_loan_term || ''}
                        onChange={(e) => handleInputChange('max_loan_term', parseFloat(e.target.value) || null)}
                        placeholder="Enter maximum loan term"
                      />
                    </div>
                    <div>
                      <Label htmlFor="interest_rate">Interest Rate (%)</Label>
                      <Input
                        id="interest_rate"
                        type="number"
                        step="0.01"
                        value={editedCriteria.interest_rate || ''}
                        onChange={(e) => handleInputChange('interest_rate', parseFloat(e.target.value) || null)}
                        placeholder="Enter interest rate"
                      />
                    </div>
                    <div>
                      <Label htmlFor="loan_to_value_min">Minimum LTV (%)</Label>
                      <Input
                        id="loan_to_value_min"
                        type="number"
                        step="0.01"
                        value={editedCriteria.loan_to_value_min || ''}
                        onChange={(e) => handleInputChange('loan_to_value_min', parseFloat(e.target.value) || null)}
                        placeholder="Enter minimum LTV"
                      />
                    </div>
                    <div>
                      <Label htmlFor="loan_to_value_max">Maximum LTV (%)</Label>
                      <Input
                        id="loan_to_value_max"
                        type="number"
                        step="0.01"
                        value={editedCriteria.loan_to_value_max || ''}
                        onChange={(e) => handleInputChange('loan_to_value_max', parseFloat(e.target.value) || null)}
                        placeholder="Enter maximum LTV"
                      />
                    </div>
                    <div>
                      <Label htmlFor="loan_to_cost_min">Minimum LTC (%)</Label>
                      <Input
                        id="loan_to_cost_min"
                        type="number"
                        step="0.01"
                        value={editedCriteria.loan_to_cost_min || ''}
                        onChange={(e) => handleInputChange('loan_to_cost_min', parseFloat(e.target.value) || null)}
                        placeholder="Enter minimum LTC"
                      />
                    </div>
                    <div>
                      <Label htmlFor="loan_to_cost_max">Maximum LTC (%)</Label>
                      <Input
                        id="loan_to_cost_max"
                        type="number"
                        step="0.01"
                        value={editedCriteria.loan_to_cost_max || ''}
                        onChange={(e) => handleInputChange('loan_to_cost_max', parseFloat(e.target.value) || null)}
                        placeholder="Enter maximum LTC"
                      />
                    </div>
                    <div>
                      <Label htmlFor="min_loan_dscr">Minimum DSCR</Label>
                      <Input
                        id="min_loan_dscr"
                        type="number"
                        step="0.01"
                        value={editedCriteria.min_loan_dscr || ''}
                        onChange={(e) => handleInputChange('min_loan_dscr', parseFloat(e.target.value) || null)}
                        placeholder="Enter minimum DSCR"
                      />
                    </div>
                    <div>
                      <Label htmlFor="max_loan_dscr">Maximum DSCR</Label>
                      <Input
                        id="max_loan_dscr"
                        type="number"
                        step="0.01"
                        value={editedCriteria.max_loan_dscr || ''}
                        onChange={(e) => handleInputChange('max_loan_dscr', parseFloat(e.target.value) || null)}
                        placeholder="Enter maximum DSCR"
                      />
                    </div>
                    <div>
                      <Label htmlFor="loan_origination_fee_min">Minimum Origination Fee (%)</Label>
                      <Input
                        id="loan_origination_fee_min"
                        type="number"
                        step="0.01"
                        value={editedCriteria.loan_origination_fee_min || ''}
                        onChange={(e) => handleInputChange('loan_origination_fee_min', parseFloat(e.target.value) || null)}
                        placeholder="Enter minimum origination fee"
                      />
                    </div>
                    <div>
                      <Label htmlFor="loan_origination_fee_max">Maximum Origination Fee (%)</Label>
                      <Input
                        id="loan_origination_fee_max"
                        type="number"
                        step="0.01"
                        value={editedCriteria.loan_origination_fee_max || ''}
                        onChange={(e) => handleInputChange('loan_origination_fee_max', parseFloat(e.target.value) || null)}
                        placeholder="Enter maximum origination fee"
                      />
                    </div>
                    <div>
                      <Label htmlFor="loan_exit_fee_min">Minimum Exit Fee (%)</Label>
                      <Input
                        id="loan_exit_fee_min"
                        type="number"
                        step="0.01"
                        value={editedCriteria.loan_exit_fee_min || ''}
                        onChange={(e) => handleInputChange('loan_exit_fee_min', parseFloat(e.target.value) || null)}
                        placeholder="Enter minimum exit fee"
                      />
                    </div>
                    <div>
                      <Label htmlFor="loan_exit_fee_max">Maximum Exit Fee (%)</Label>
                      <Input
                        id="loan_exit_fee_max"
                        type="number"
                        step="0.01"
                        value={editedCriteria.loan_exit_fee_max || ''}
                        onChange={(e) => handleInputChange('loan_exit_fee_max', parseFloat(e.target.value) || null)}
                        placeholder="Enter maximum exit fee"
                      />
                    </div>
                    <div>
                      <Label htmlFor="closing_time_weeks">Closing Time (Weeks)</Label>
                      <Input
                        id="closing_time_weeks"
                        type="number"
                        step="0.01"
                        value={editedCriteria.closing_time_weeks || ''}
                        onChange={(e) => handleInputChange('closing_time_weeks', parseFloat(e.target.value) || null)}
                        placeholder="Enter closing time in weeks"
                      />
                    </div>
                  </div>
                  
                  {renderArrayField(
                    'loan_type', 
                    'Loan Types', 
                    'Select loan type',
                    true,
                    mappings?.['Capital Position']?.children || []
                  )}
                  {renderArrayField(
                    'loan_program', 
                    'Loan Programs', 
                    'Select loan program',
                    true,
                    mappings?.['Loan Program']?.parents || []
                  )}
                  {renderArrayField(
                    'structured_loan_tranche', 
                    'Structured Loan Tranches', 
                    'Select loan tranche',
                    true,
                    mappings?.['Structured Loan Tranches']?.parents || []
                  )}
                  {renderArrayField(
                    'recourse_loan', 
                    'Recourse Loans', 
                    'Select recourse loan type',
                    true,
                    mappings?.['Recourse Loan']?.parents || []
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Geography Tab */}
            <TabsContent value="geography" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Geographic Focus
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {renderArrayField('country', 'Countries', 'Enter country')}
                  {renderArrayField(
                    'region', 
                    'Regions', 
                    'Select region',
                    true,
                    mappings?.['U.S Regions']?.parents || []
                  )}
                  {renderArrayField('state', 'States', 'Enter state')}
                  {renderArrayField('city', 'Cities', 'Enter city')}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Notes Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Additional Notes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={editedCriteria.notes || ''}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Enter any additional notes..."
                rows={4}
              />
            </CardContent>
          </Card>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose} disabled={isSaving}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
} 