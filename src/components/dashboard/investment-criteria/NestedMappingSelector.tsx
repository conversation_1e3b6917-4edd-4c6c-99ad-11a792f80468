'use client'

import React, { useState, useEffect } from 'react'
import { Label } from "@/components/ui/label"
import { ReactMultiSelect } from '@/components/ui/react-multi-select'
import { Loader2 } from "lucide-react"
import { toast } from "sonner"

interface MappingHierarchy {
  id: number;
  values: string[];
  fullMapping: any;
}

interface MappingData {
  type: string;
  levels: string[];
  hierarchyRows: MappingHierarchy[];
}

interface NestedMappingSelectorProps {
  mappingType: string;
  selectedValues: string[];
  onSelectionChange: (values: string[]) => void;
  label: string;
  placeholder?: string;
  disabled?: boolean;
  showSelectAll?: boolean;
  selectAllLabel?: string;
  mappingData?: MappingData | null; // Allow pre-fetched data
  allowNewOptions?: boolean; // Allow adding new options
  onAddNewOption?: (newValue: string) => void; // Callback for new options
}

export default function NestedMappingSelector({
  mappingType,
  selectedValues,
  onSelectionChange,
  label,
  placeholder = "Select options",
  disabled = false,
  showSelectAll = true,
  selectAllLabel = "Select All",
  mappingData: propMappingData,
  allowNewOptions = false,
  onAddNewOption
}: NestedMappingSelectorProps) {
  const [mappingData, setMappingData] = useState<MappingData | null>(propMappingData || null);
  const [loading, setLoading] = useState(!propMappingData);
  const [newOptionInput, setNewOptionInput] = useState('');
  const [showNewOptionInput, setShowNewOptionInput] = useState(false);

  useEffect(() => {
    if (propMappingData) {
      setMappingData(propMappingData);
      setLoading(false);
    } else {
      fetchMappingData();
    }
  }, [mappingType, propMappingData]);

  const fetchMappingData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/mapping-tables/types?type=${encodeURIComponent(mappingType)}`);

      if (!response.ok) {
        throw new Error('Failed to fetch mapping data');
      }

      const data = await response.json();

      if (data.success) {
        setMappingData(data.data);
      } else {
        throw new Error(data.error || 'Failed to load mapping data');
      }
    } catch (error) {
      console.error('Error fetching mapping data:', error);
      toast.error(`Failed to load ${label.toLowerCase()}`);
    } finally {
      setLoading(false);
    }
  };

  const getUniqueOptions = () => {
    if (!mappingData) return [];

    const allValues = new Set<string>();
    mappingData.hierarchyRows.forEach(row => {
      row.values.forEach(value => {
        if (value && value.trim()) {
          allValues.add(value.trim());
        }
      });
    });

    return Array.from(allValues).sort().map(value => ({
      value,
      label: value
    }));
  };

  const handleAddNewOption = () => {
    if (newOptionInput.trim() && onAddNewOption) {
      onAddNewOption(newOptionInput.trim());
      setNewOptionInput('');
      setShowNewOptionInput(false);
    }
  };

  // Simple render function using ReactMultiSelect
  const renderSelector = () => {
    const options = getUniqueOptions();

    return (
      <div className="space-y-2">
        <ReactMultiSelect
          options={options}
          selected={selectedValues}
          onChange={onSelectionChange}
          placeholder={placeholder}
          disabled={disabled || loading}
          showSelectAll={showSelectAll}
          selectAllLabel={selectAllLabel}
          isSearchable={true}
        />
        
        {/* Add New Option Section */}
        {allowNewOptions && (
          <div className="space-y-2">
            {!showNewOptionInput ? (
              <button
                type="button"
                onClick={() => setShowNewOptionInput(true)}
                className="text-sm text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1"
              >
                <span>+</span>
                Add new {label.toLowerCase()}
              </button>
            ) : (
              <div className="flex items-center gap-2">
                <input
                  type="text"
                  value={newOptionInput}
                  onChange={(e) => setNewOptionInput(e.target.value)}
                  placeholder={`Enter new ${label.toLowerCase()}`}
                  className="flex-1 px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleAddNewOption();
                    }
                  }}
                />
                <button
                  type="button"
                  onClick={handleAddNewOption}
                  disabled={!newOptionInput.trim()}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Add
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowNewOptionInput(false);
                    setNewOptionInput('');
                  }}
                  className="px-3 py-1 text-sm text-gray-600 hover:text-gray-700"
                >
                  Cancel
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="space-y-3">
        <Label className="text-sm font-medium text-gray-700">{label}</Label>
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <Loader2 className="h-4 w-4 animate-spin" />
          Loading {label.toLowerCase()}...
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <Label className="text-sm font-medium text-gray-700">{label}</Label>
      {renderSelector()}
    </div>
  );
}
