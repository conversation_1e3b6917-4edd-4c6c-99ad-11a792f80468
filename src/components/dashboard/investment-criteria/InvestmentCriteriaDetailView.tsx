'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  ArrowLeft, 
  Building, 
  User, 
  MapPin, 
  DollarSign, 
  Calendar,
  Globe,
  Home,
  TrendingUp,
  Percent,
  Clock,
  FileText,
  Loader2,
  Edit,
  Trash2,
  Target,
  CreditCard,
  Banknote,
  Calculator,
  Timer,
  Shield,
  Building2,
  Briefcase,
  Save,
  X,
  BarChart3
} from 'lucide-react'
import { InvestmentCriteria } from '@/types/investment-criteria'
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { format } from 'date-fns'

interface InvestmentCriteriaDetailViewProps {
  id: string
}

export default function InvestmentCriteriaDetailView({ 
  id 
}: InvestmentCriteriaDetailViewProps) {
  const [criteria, setCriteria] = useState<InvestmentCriteria | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchCriteria = async () => {
      try {
        const response = await fetch(`/api/investment-criteria/${id}`)
        if (!response.ok) {
          throw new Error('Failed to fetch investment criteria')
        }
        const data = await response.json()
        setCriteria(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchCriteria()
  }, [id])

  const [isEditing, setIsEditing] = useState(false)
  const [editedCriteria, setEditedCriteria] = useState<InvestmentCriteria | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [mappings, setMappings] = useState<any>({})
  const [isLoadingMappings, setIsLoadingMappings] = useState(false)

  const fetchMappings = async () => {
    setIsLoadingMappings(true)
    try {
      const response = await fetch('/api/investment-criteria/mappings')
      if (response.ok) {
        const data = await response.json()
        setMappings(data)
      }
    } catch (error) {
      console.error('Error fetching mappings:', error)
    } finally {
      setIsLoadingMappings(false)
    }
  }

  const handleEdit = () => {
    setIsEditing(true)
    setEditedCriteria(criteria ? { ...criteria } : null)
    fetchMappings()
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    setEditedCriteria(null)
  }

  const handleSaveEdit = async () => {
    if (!editedCriteria) return
    
    setIsSaving(true)
    try {
      // Debug: Log what we're sending
      console.log('Sending edited criteria:', editedCriteria)
      console.log('JSON stringified:', JSON.stringify(editedCriteria, null, 2))
      
      const response = await fetch(`/api/investment-criteria/${criteria?.criteria_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editedCriteria),
      })
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('Response error:', errorText)
        throw new Error(`Failed to update investment criteria: ${errorText}`)
      }
      
      // Update the local state with the updated criteria
      setCriteria(editedCriteria)
      setIsEditing(false)
      setEditedCriteria(null)
    } catch (error) {
      console.error('Error updating investment criteria:', error)
      throw error
    } finally {
      setIsSaving(false)
    }
  }

  const handleInputChange = (field: keyof InvestmentCriteria, value: any) => {
    if (!editedCriteria) return
    
    setEditedCriteria({
      ...editedCriteria,
      [field]: value
    })
  }

  const handleArrayFieldChange = (
    field: keyof InvestmentCriteria,
    value: string,
    index: number
  ) => {
    if (!editedCriteria) return
    
    const fieldValue = editedCriteria[field]
    const currentArray = Array.isArray(fieldValue) ? [...fieldValue] : []
    
    if (index >= currentArray.length) {
      currentArray.push(value)
    } else {
      currentArray[index] = value
    }
    
    setEditedCriteria({
      ...editedCriteria,
      [field]: currentArray
    })
  }

  const addArrayItem = (field: keyof InvestmentCriteria) => {
    if (!editedCriteria) return
    
    const fieldValue = editedCriteria[field]
    const currentArray = Array.isArray(fieldValue) ? [...fieldValue] : []
    currentArray.push('')
    
    setEditedCriteria({
      ...editedCriteria,
      [field]: currentArray
    })
  }

  const removeArrayItem = (field: keyof InvestmentCriteria, index: number) => {
    if (!editedCriteria) return
    
    const fieldValue = editedCriteria[field]
    const currentArray = Array.isArray(fieldValue) ? [...fieldValue] : []
    currentArray.splice(index, 1)
    
    setEditedCriteria({
      ...editedCriteria,
      [field]: currentArray
    })
  }

  const handleDelete = async () => {
    if (!criteria) return
    
    if (window.confirm('Are you sure you want to delete this investment criteria? This action cannot be undone.')) {
      try {
        const response = await fetch(`/api/investment-criteria/${criteria.criteria_id}`, {
          method: 'DELETE',
        })
        
        if (response.ok) {
          // Redirect back to the list after successful deletion
          window.location.href = '/dashboard/investment-criteria'
        } else {
          alert('Failed to delete investment criteria')
        }
      } catch (error) {
        console.error('Error deleting investment criteria:', error)
        alert('An error occurred while deleting the investment criteria')
      }
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin text-blue-600" />
          <p className="text-lg text-slate-600 font-medium">Loading investment criteria...</p>
        </div>
      </div>
    )
  }

  if (error || !criteria) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
        <div className="text-center py-16 px-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <FileText className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-2xl font-bold text-slate-900 mb-2">Investment Criteria Not Found</h2>
          <p className="text-lg text-slate-600 mb-8">
            {error || 'The investment criteria you are looking for does not exist or has been removed.'}
          </p>
          <Button variant="outline" asChild className="bg-white shadow-sm hover:shadow-md transition-shadow">
            <Link href="/dashboard/investment-criteria">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Investment Criteria
            </Link>
          </Button>
        </div>
      </div>
    )
  }
  
  // Format currency display already in millions
  const formatCurrency = (value?: number | string) => {
    if (!value) return 'Not specified'
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    if (isNaN(numValue)) return 'Not specified'
    if (numValue >= 1000000000) {
      return `$${(numValue / 1000000000).toFixed(1)}B`
    }
    if (numValue >= 1000000) {
      return `$${(numValue / 1000000).toFixed(1)}M`
    }
    if (numValue >= 1000) {
      return `$${(numValue / 1000).toFixed(0)}K`
    }
    return `$${numValue.toLocaleString()}`
  }

  // Format percentage display
  const formatPercentage = (value?: number | string) => {
    if (!value) return 'Not specified'
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return !isNaN(numValue) ? `${numValue}%` : 'Not specified'
  }

  // Format date display
  const formatDate = (dateStr?: string) => {
    if (!dateStr) return 'Not available'
    return format(new Date(dateStr), 'MMM dd, yyyy • HH:mm')
  }

  // Format array display
  const formatArray = (arr?: string[]) => {
    if (!arr || arr.length === 0) return <span className="text-slate-400">Not specified</span>
    return (
      <div className="flex flex-wrap gap-2">
        {arr.map((item, index) => (
          <Badge key={index} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 transition-colors">
            {item}
          </Badge>
        ))}
      </div>
    )
  }

  // Get entity icon and color
  const getEntityInfo = (entityType: string) => {
    if (entityType.startsWith('Company')) {
      return { 
        icon: <Building2 className="h-5 w-5" />, 
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200'
      }
    } else if (entityType === 'Contact') {
      return { 
        icon: <User className="h-5 w-5" />, 
        color: 'text-emerald-600',
        bgColor: 'bg-emerald-50',
        borderColor: 'border-emerald-200'
      }
    } else if (entityType === 'Deal') {
      return { 
        icon: <Briefcase className="h-5 w-5" />, 
        color: 'text-purple-600',
        bgColor: 'bg-purple-50',
        borderColor: 'border-purple-200'
      }
    } else {
      return { 
        icon: <Building className="h-5 w-5" />, 
        color: 'text-slate-600',
        bgColor: 'bg-slate-50',
        borderColor: 'border-slate-200'
      }
    }
  }

  // Helper to prettify extra_fields
  const prettifyExtraFields = (extra: any) => {
    if (!extra || typeof extra !== 'object') return null;
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {Object.entries(extra).map(([key, value]) => (
          <div key={key} className="space-y-2">
            <label className="text-sm font-semibold text-slate-700 uppercase tracking-wide">
              {key.replace(/_/g, ' ')}
            </label>
            <div className="text-slate-900 bg-slate-50 p-3 rounded-lg border text-sm font-mono">
              {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Helper for booleans
  const formatBoolean = (value?: boolean) => {
    if (value === undefined || value === null) return null;
    return (
      <Badge className={`${value ? 'bg-emerald-100 text-emerald-800 border-emerald-200' : 'bg-red-100 text-red-800 border-red-200'} font-medium`}>
        {value ? 'Yes' : 'No'}
      </Badge>
    );
  };

  // Helper for notes
  const formatNotes = (notes?: string) => {
    if (!notes) return null;
    return (
      <Alert className="border-amber-200 bg-amber-50">
        <FileText className="h-4 w-4 text-amber-600" />
        <AlertTitle className="text-amber-800">Notes</AlertTitle>
        <AlertDescription className="text-amber-700">{notes}</AlertDescription>
      </Alert>
    );
  };

  // Helper for compact badges with show more
  const CompactBadges = ({ items, max = 12 }: { items: string[]; max?: number }) => {
    const [showAll, setShowAll] = useState(false);
    if (!items || items.length === 0) return <span className="text-slate-400 text-sm">Not specified</span>;
    
    const displayItems = showAll ? items : items.slice(0, max);
    return (
      <div className="space-y-3">
        <div className="flex flex-wrap gap-2">
          {displayItems.map((item, idx) => (
            <Badge 
              key={idx} 
              variant="outline" 
              className="bg-slate-50 text-slate-700 border-slate-200 hover:bg-slate-100 transition-colors px-3 py-1"
            >
              {item}
            </Badge>
          ))}
        </div>
        {items.length > max && (
          <button 
            className="text-sm text-blue-600 hover:text-blue-800 font-medium underline underline-offset-2 transition-colors" 
            onClick={() => setShowAll(!showAll)}
          >
            {showAll ? 'Show less' : `Show ${items.length - max} more`}
          </button>
        )}
      </div>
    );
  };

  // Add a helper for formatting percentages
  const formatPercent = (value?: number | string) => {
    if (value === undefined || value === null || value === '') return undefined;
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return !isNaN(numValue) ? `${numValue}%` : undefined;
  };

  // For DSCR formatting
  const formatDSCRPercent = (value?: number | string) => {
    if (value === undefined || value === null || value === '') return undefined;
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return !isNaN(numValue) ? `${numValue.toFixed(2)}x` : undefined;
  };

  // Define a helper to check if any key loan field has a value
  const hasAnyLoanInfo = !!(
    criteria.min_loan_term || criteria.max_loan_term || criteria.interest_rate || 
    criteria.interest_rate_sofr || criteria.interest_rate_wsj || criteria.interest_rate_prime || 
    criteria.loan_to_value_min || criteria.loan_to_value_max || criteria.loan_to_cost_min || 
    criteria.loan_to_cost_max || criteria.loan_origination_fee_min || criteria.loan_origination_fee_max || 
    criteria.loan_exit_fee_min || criteria.loan_exit_fee_max || criteria.min_loan_dscr || 
    criteria.max_loan_dscr || (criteria.loan_type && criteria.loan_type.length > 0) || 
    (criteria.loan_type_normalized && criteria.loan_type_normalized.length > 0) || 
    (criteria.structured_loan_tranche && criteria.structured_loan_tranche.length > 0) || 
    (criteria.recourse_loan && criteria.recourse_loan.length > 0) || criteria.capital_source || 
    criteria.closing_time_weeks
  );

  const entityInfo = getEntityInfo(criteria.entity_type);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      {/* Header with gradient background */}
      <div className="bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 text-white">
        <div className="max-w-6xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <Button variant="ghost" asChild className="text-white hover:bg-white/10 border-white/20">
                <Link href="/dashboard/investment-criteria">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Investment Criteria
                </Link>
              </Button>
              <div className="flex items-center space-x-4">
                <div className={`p-3 rounded-xl ${entityInfo.bgColor} ${entityInfo.borderColor} border`}>
                  <span className={entityInfo.color}>{entityInfo.icon}</span>
                </div>
                <div>
                  <h1 className="text-2xl font-bold">
                    {criteria.entity_name || 'Investment Criteria'}
                  </h1>
                  <p className="text-slate-300 text-sm">
                    {criteria.entity_type} • Created {formatDate(criteria.created_at)}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {!isEditing ? (
                <>
                  <Button variant="ghost" onClick={handleEdit} className="text-white hover:bg-white/10 border-white/20">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  <Button variant="destructive" onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </>
              ) : (
                <>
                  <Button 
                    variant="ghost" 
                    onClick={handleSaveEdit} 
                    disabled={isSaving}
                    className="text-white hover:bg-white/10 border-white/20"
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save
                      </>
                    )}
                  </Button>
                  <Button variant="ghost" onClick={handleCancelEdit} className="text-white hover:bg-white/10 border-white/20">
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
˘
      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-6 py-8 space-y-8">
        
        {/* Entity Information */}
        {(criteria.entity_name || criteria.entity_type || criteria.entity_location || criteria.entity_website || criteria.entity_industry || criteria.created_at) && (
          <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl text-slate-800">
                <div className={`p-2 rounded-lg ${entityInfo.bgColor}`}>
                  <span className={entityInfo.color}>{entityInfo.icon}</span>
                </div>
                Entity Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {criteria.entity_name && (
                  <div className="space-y-2">
                    <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Entity Name</label>
                    <p className="text-lg font-semibold text-slate-900">{criteria.entity_name}</p>
                  </div>
                )}
                {criteria.entity_type && (
                  <div className="space-y-2">
                    <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Entity Type</label>
                    <Badge className={`${entityInfo.bgColor} ${entityInfo.color} border-0 text-sm font-medium px-3 py-1`}>
                      {criteria.entity_type}
                    </Badge>
                  </div>
                )}
                {criteria.entity_website && (
                  <div className="space-y-2">
                    <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Website</label>
                    <a 
                      href={criteria.entity_website} 
                      target="_blank" 
                      rel="noopener noreferrer" 
                      className="text-blue-600 hover:text-blue-800 flex items-center gap-2 font-medium transition-colors"
                    >
                      <Globe className="h-4 w-4" />
                      {criteria.entity_website}
                    </a>
                  </div>
                )}
                {criteria.entity_industry && (
                  <div className="space-y-2">
                    <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Industry</label>
                    <p className="text-slate-900 font-medium">{criteria.entity_industry}</p>
                  </div>
                )}
                {criteria.created_at && (
                  <div className="space-y-2">
                    <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Created</label>
                    <p className="text-slate-700 flex items-center gap-2 font-medium">
                      <Calendar className="h-4 w-4 text-slate-500" />
                      {formatDate(criteria.created_at)}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Financial Criteria - Always show in edit mode */}
        {(isEditing || criteria.target_return || criteria.minimum_deal_size || criteria.maximum_deal_size || criteria.historical_irr || criteria.historical_em || (criteria.financial_products && criteria.financial_products.length > 0)) && (
          <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl text-slate-800">
                <div className="p-2 rounded-lg bg-green-50">
                  <DollarSign className="h-5 w-5 text-green-600" />
                </div>
                Financial Criteria
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Target Return - Always show in edit mode */}
              {(isEditing || criteria.target_return) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-slate-600" />
                    Target Return
                  </h4>
                  {isEditing ? (
                    <Input
                      type="number"
                      step="0.01"
                      value={editedCriteria?.target_return || ''}
                      onChange={(e) => handleInputChange('target_return', parseFloat(e.target.value) || null)}
                      placeholder="Enter target return percentage"
                    />
                  ) : (
                    criteria.target_return && (
                      <div className="bg-slate-50 p-4 rounded-xl border">
                        <p className="text-slate-900">{formatPercentage(criteria.target_return)}</p>
                      </div>
                    )
                  )}
                </div>
              )}

              {/* Deal Size Range - Always show in edit mode */}
              {(isEditing || criteria.minimum_deal_size || criteria.maximum_deal_size) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg flex items-center gap-2">
                    <DollarSign className="h-5 w-5 text-slate-600" />
                    Deal Size Range
                  </h4>
                  {isEditing ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="minimum_deal_size">Minimum Deal Size ($M)</Label>
                        <Input
                          id="minimum_deal_size"
                          type="number"
                          step="0.01"
                          value={editedCriteria?.minimum_deal_size || ''}
                          onChange={(e) => handleInputChange('minimum_deal_size', parseFloat(e.target.value) || null)}
                          placeholder="Enter minimum deal size"
                        />
                      </div>
                      <div>
                        <Label htmlFor="maximum_deal_size">Maximum Deal Size ($M)</Label>
                        <Input
                          id="maximum_deal_size"
                          type="number"
                          step="0.01"
                          value={editedCriteria?.maximum_deal_size || ''}
                          onChange={(e) => handleInputChange('maximum_deal_size', parseFloat(e.target.value) || null)}
                          placeholder="Enter maximum deal size"
                        />
                      </div>
                    </div>
                  ) : (
                    (criteria.minimum_deal_size || criteria.maximum_deal_size) && (
                      <div className="bg-slate-50 p-4 rounded-xl border">
                        <p className="text-slate-900">
                          {criteria.minimum_deal_size && criteria.maximum_deal_size
                            ? `${formatCurrency(criteria.minimum_deal_size)} - ${formatCurrency(criteria.maximum_deal_size)}`
                            : criteria.minimum_deal_size
                            ? `${formatCurrency(criteria.minimum_deal_size)}+`
                            : `up to ${formatCurrency(criteria.maximum_deal_size)}`}
                        </p>
                      </div>
                    )
                  )}
                </div>
              )}

              {/* Historical Performance - Always show in edit mode */}
              {(isEditing || criteria.historical_irr || criteria.historical_em) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-slate-600" />
                    Historical Performance
                  </h4>
                  {isEditing ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="historical_irr">Historical IRR (%)</Label>
                        <Input
                          id="historical_irr"
                          type="number"
                          step="0.01"
                          value={editedCriteria?.historical_irr || ''}
                          onChange={(e) => handleInputChange('historical_irr', parseFloat(e.target.value) || null)}
                          placeholder="Enter historical IRR"
                        />
                      </div>
                      <div>
                        <Label htmlFor="historical_em">Historical Equity Multiple</Label>
                        <Input
                          id="historical_em"
                          type="number"
                          step="0.01"
                          value={editedCriteria?.historical_em || ''}
                          onChange={(e) => handleInputChange('historical_em', parseFloat(e.target.value) || null)}
                          placeholder="Enter historical equity multiple"
                        />
                      </div>
                    </div>
                  ) : (
                    (criteria.historical_irr || criteria.historical_em) && (
                      <div className="bg-slate-50 p-4 rounded-xl border">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {criteria.historical_irr && (
                            <div>
                              <p className="text-sm text-slate-600">Historical IRR</p>
                              <p className="text-slate-900">{formatPercentage(criteria.historical_irr)}</p>
                            </div>
                          )}
                          {criteria.historical_em && (
                            <div>
                              <p className="text-sm text-slate-600">Historical Equity Multiple</p>
                              <p className="text-slate-900">{criteria.historical_em}x</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  )}
                </div>
              )}

              {/* Hold Period - Always show in edit mode */}
              {(isEditing || criteria.min_hold_period || criteria.max_hold_period) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg flex items-center gap-2">
                    <Clock className="h-5 w-5 text-slate-600" />
                    Hold Period (Years)
                  </h4>
                  {isEditing ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="min_hold_period">Minimum Hold Period</Label>
                        <Input
                          id="min_hold_period"
                          type="number"
                          step="0.01"
                          value={editedCriteria?.min_hold_period || ''}
                          onChange={(e) => handleInputChange('min_hold_period', parseFloat(e.target.value) || null)}
                          placeholder="Enter minimum hold period"
                        />
                      </div>
                      <div>
                        <Label htmlFor="max_hold_period">Maximum Hold Period</Label>
                        <Input
                          id="max_hold_period"
                          type="number"
                          step="0.01"
                          value={editedCriteria?.max_hold_period || ''}
                          onChange={(e) => handleInputChange('max_hold_period', parseFloat(e.target.value) || null)}
                          placeholder="Enter maximum hold period"
                        />
                      </div>
                    </div>
                  ) : (
                    (criteria.min_hold_period || criteria.max_hold_period) && (
                      <div className="bg-slate-50 p-4 rounded-xl border">
                        <p className="text-slate-900">
                          {criteria.min_hold_period && criteria.max_hold_period
                            ? `${criteria.min_hold_period} - ${criteria.max_hold_period} years`
                            : criteria.min_hold_period
                            ? `${criteria.min_hold_period}+ years`
                            : `up to ${criteria.max_hold_period} years`}
                        </p>
                      </div>
                    )
                  )}
                </div>
              )}

              {/* Financial Products - Always show in edit mode */}
              {(isEditing || (criteria.financial_products && criteria.financial_products.length > 0)) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg flex items-center gap-2">
                    <CreditCard className="h-5 w-5 text-slate-600" />
                    Financial Products
                  </h4>
                  {isEditing ? (
                    <div className="space-y-2">
                      {(editedCriteria?.financial_products || []).map((item, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            value={item}
                            onChange={(e) => handleArrayFieldChange('financial_products', e.target.value, index)}
                            placeholder="Enter financial product"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeArrayItem('financial_products', index)}
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        onClick={() => addArrayItem('financial_products')}
                        className="w-full"
                      >
                        Add Financial Product
                      </Button>
                    </div>
                  ) : (
                    <CompactBadges items={criteria.financial_products ?? []} />
                  )}
                </div>
              )}

              {/* Capital Source - Always show in edit mode */}
              {(isEditing || criteria.capital_source) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg">Capital Source</h4>
                  {isEditing ? (
                    <Input
                      value={editedCriteria?.capital_source || ''}
                      onChange={(e) => handleInputChange('capital_source', e.target.value)}
                      placeholder="Enter capital source"
                    />
                  ) : (
                    criteria.capital_source && (
                      <div className="bg-slate-50 p-4 rounded-xl border">
                        <p className="text-slate-900">{criteria.capital_source}</p>
                      </div>
                    )
                  )}
                </div>
              )}

              {/* Notes Section - Always show in edit mode */}
              {(isEditing || criteria.notes) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg">Notes</h4>
                  {isEditing ? (
                    <Textarea
                      value={editedCriteria?.notes || ''}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      placeholder="Enter any additional notes..."
                      rows={4}
                    />
                  ) : (
                    criteria.notes && (
                      <div className="bg-slate-50 p-4 rounded-xl border">
                        <p className="text-slate-900">{criteria.notes}</p>
                      </div>
                    )
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Property & Investment Focus - Always show in edit mode */}
        {(isEditing || (criteria.property_types && criteria.property_types.length > 0) || (criteria.property_sub_categories && criteria.property_sub_categories.length > 0) || (criteria.strategies && criteria.strategies.length > 0) || (criteria.capital_position && criteria.capital_position.length > 0)) && (
          <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl text-slate-800">
                <div className="p-2 rounded-lg bg-orange-50">
                  <Home className="h-5 w-5 text-orange-600" />
                </div>
                Property & Investment Focus
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Property Types - Always show in edit mode */}
              {(isEditing || (criteria.property_types && criteria.property_types.length > 0)) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg flex items-center gap-2">
                    <Building2 className="h-5 w-5 text-slate-600" />
                    Property Types
                  </h4>
                  {isEditing ? (
                    <div className="space-y-2">
                      {(editedCriteria?.property_types || []).map((item, index) => (
                        <div key={index} className="flex gap-2">
                          <Select
                            value={item}
                            onValueChange={(value) => handleArrayFieldChange('property_types', value, index)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select property type" />
                            </SelectTrigger>
                            <SelectContent>
                              {mappings['Property Type']?.parents?.map((mapping: string) => (
                                <SelectItem key={mapping} value={mapping}>
                                  {mapping}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeArrayItem('property_types', index)}
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        onClick={() => addArrayItem('property_types')}
                        className="w-full"
                      >
                        Add Property Type
                      </Button>
                    </div>
                  ) : (
                    <CompactBadges items={criteria.property_types ?? []} />
                  )}
                </div>
              )}
              
              {/* Property Subcategories - Always show in edit mode */}
              {(isEditing || (criteria.property_sub_categories && criteria.property_sub_categories.length > 0)) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg">Property Subcategories</h4>
                  {isEditing ? (
                    <div className="space-y-2">
                      {(editedCriteria?.property_sub_categories || []).map((item, index) => (
                        <div key={index} className="flex gap-2">
                          <Select
                            value={item}
                            onValueChange={(value) => handleArrayFieldChange('property_sub_categories', value, index)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select property subcategory" />
                            </SelectTrigger>
                            <SelectContent>
                              {mappings['Property Type']?.children?.map((child: string) => (
                                <SelectItem key={child} value={child}>
                                  {child}
                                </SelectItem>
                              )) || []}
                            </SelectContent>
                          </Select>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeArrayItem('property_sub_categories', index)}
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        onClick={() => addArrayItem('property_sub_categories')}
                        className="w-full"
                      >
                        Add Property Subcategory
                      </Button>
                    </div>
                  ) : (
                    <CompactBadges items={criteria.property_sub_categories ?? []} />
                  )}
                </div>
              )}
              
              {/* Investment Strategies - Always show in edit mode */}
              {(isEditing || (criteria.strategies && criteria.strategies.length > 0)) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg flex items-center gap-2">
                    <Target className="h-5 w-5 text-slate-600" />
                    Investment Strategies
                  </h4>
                  {isEditing ? (
                    <div className="space-y-2">
                      {(editedCriteria?.strategies || []).map((item, index) => (
                        <div key={index} className="flex gap-2">
                          <Select
                            value={item}
                            onValueChange={(value) => handleArrayFieldChange('strategies', value, index)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select strategy" />
                            </SelectTrigger>
                            <SelectContent>
                              {mappings['Strategies']?.parents?.map((mapping: string) => (
                                <SelectItem key={mapping} value={mapping}>
                                  {mapping}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeArrayItem('strategies', index)}
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        onClick={() => addArrayItem('strategies')}
                        className="w-full"
                      >
                        Add Strategy
                      </Button>
                    </div>
                  ) : (
                    <CompactBadges items={criteria.strategies ?? []} />
                  )}
                </div>
              )}
              
              {/* Capital Position - Always show in edit mode */}
              {(isEditing || (criteria.capital_position && criteria.capital_position.length > 0)) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-slate-600" />
                    Capital Position
                  </h4>
                  {isEditing ? (
                    <div className="space-y-2">
                      {(editedCriteria?.capital_position || []).map((item, index) => (
                        <div key={index} className="flex gap-2">
                          <Select
                            value={item}
                            onValueChange={(value) => handleArrayFieldChange('capital_position', value, index)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select capital position" />
                            </SelectTrigger>
                            <SelectContent>
                              {mappings['Capital Position']?.parents?.map((mapping: string) => (
                                <SelectItem key={mapping} value={mapping}>
                                  {mapping}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeArrayItem('capital_position', index)}
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        onClick={() => addArrayItem('capital_position')}
                        className="w-full"
                      >
                        Add Capital Position
                      </Button>
                    </div>
                  ) : (
                    (criteria.capital_position && criteria.capital_position.length > 0) ? (
                      <CompactBadges items={criteria.capital_position} />
                    ) : (
                      <span className="text-slate-400 text-sm">Not specified</span>
                    )
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Geographic Focus - Always show in edit mode */}
        {(isEditing || (criteria.country?.length ?? 0) > 0 || (criteria.region?.length ?? 0) > 0 || (criteria.state?.length ?? 0) > 0 || (criteria.city?.length ?? 0) > 0) && (
          <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl text-slate-800">
                <div className="p-2 rounded-lg bg-red-50">
                  <MapPin className="h-5 w-5 text-red-600" />
                </div>
                Geographic Focus
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Countries - Always show in edit mode */}
              {(isEditing || (criteria.country?.length ?? 0) > 0) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg">Countries</h4>
                  {isEditing ? (
                    <div className="space-y-2">
                      {(editedCriteria?.country || []).map((item, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            value={item}
                            onChange={(e) => handleArrayFieldChange('country', e.target.value, index)}
                            placeholder="Enter country name"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeArrayItem('country', index)}
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        onClick={() => addArrayItem('country')}
                        className="w-full"
                      >
                        Add Country
                      </Button>
                    </div>
                  ) : (
                    <CompactBadges items={criteria.country ?? []} />
                  )}
                </div>
              )}
              
              {/* Regions - Always show in edit mode */}
              {(isEditing || (criteria.region?.length ?? 0) > 0) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg">Regions</h4>
                  {isEditing ? (
                    <div className="space-y-2">
                      {(editedCriteria?.region || []).map((item, index) => (
                        <div key={index} className="flex gap-2">
                          <Select
                            value={item}
                            onValueChange={(value) => handleArrayFieldChange('region', value, index)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select region" />
                            </SelectTrigger>
                            <SelectContent>
                              {mappings['U.S Regions']?.parents?.map((mapping: string) => (
                                <SelectItem key={mapping} value={mapping}>
                                  {mapping}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeArrayItem('region', index)}
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        onClick={() => addArrayItem('region')}
                        className="w-full"
                      >
                        Add Region
                      </Button>
                    </div>
                  ) : (
                    <CompactBadges items={criteria.region ?? []} />
                  )}
                </div>
              )}
              
              {/* States - Always show in edit mode */}
              {(isEditing || (criteria.state?.length ?? 0) > 0) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg">States</h4>
                  {isEditing ? (
                    <div className="space-y-2">
                      {(editedCriteria?.state || []).map((item, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            value={item}
                            onChange={(e) => handleArrayFieldChange('state', e.target.value, index)}
                            placeholder="Enter state name"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeArrayItem('state', index)}
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        onClick={() => addArrayItem('state')}
                        className="w-full"
                      >
                        Add State
                      </Button>
                    </div>
                  ) : (
                    <CompactBadges items={criteria.state ?? []} />
                  )}
                </div>
              )}
              
              {/* Cities - Always show in edit mode */}
              {(isEditing || (criteria.city?.length ?? 0) > 0) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg">Cities</h4>
                  {isEditing ? (
                    <div className="space-y-2">
                      {(editedCriteria?.city || []).map((item, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            value={item}
                            onChange={(e) => handleArrayFieldChange('city', e.target.value, index)}
                            placeholder="Enter city name"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeArrayItem('city', index)}
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        onClick={() => addArrayItem('city')}
                        className="w-full"
                      >
                        Add City
                      </Button>
                    </div>
                  ) : (
                    <CompactBadges items={criteria.city ?? []} />
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Loan Information - Always show in edit mode */}
        {(isEditing || hasAnyLoanInfo) && (
          <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl text-slate-800">
                <div className="p-2 rounded-lg bg-purple-50">
                  <Calculator className="h-5 w-5 text-purple-600" />
                </div>
                Loan Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Loan Terms - Always show in edit mode */}
              {(isEditing || criteria.min_loan_term || criteria.max_loan_term) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg flex items-center gap-2">
                    <Timer className="h-5 w-5 text-slate-600" />
                    Loan Term (years)
                  </h4>
                  {isEditing ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="min_loan_term">Minimum Loan Term (years)</Label>
                        <Input
                          id="min_loan_term"
                          type="number"
                          step="0.01"
                          value={editedCriteria?.min_loan_term || ''}
                          onChange={(e) => handleInputChange('min_loan_term', parseFloat(e.target.value) || null)}
                          placeholder="Enter minimum loan term"
                        />
                      </div>
                      <div>
                        <Label htmlFor="max_loan_term">Maximum Loan Term (years)</Label>
                        <Input
                          id="max_loan_term"
                          type="number"
                          step="0.01"
                          value={editedCriteria?.max_loan_term || ''}
                          onChange={(e) => handleInputChange('max_loan_term', parseFloat(e.target.value) || null)}
                          placeholder="Enter maximum loan term"
                        />
                      </div>
                    </div>
                  ) : (
                    (criteria.min_loan_term || criteria.max_loan_term) && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {criteria.min_loan_term && (
                          <div className="bg-slate-50 p-4 rounded-xl border">
                            <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Minimum</label>
                            <p className="text-2xl font-bold text-slate-900 mt-1">{criteria.min_loan_term}</p>
                          </div>
                        )}
                        {criteria.max_loan_term && (
                          <div className="bg-slate-50 p-4 rounded-xl border">
                            <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Maximum</label>
                            <p className="text-2xl font-bold text-slate-900 mt-1">{criteria.max_loan_term}</p>
                          </div>
                        )}
                      </div>
                    )
                  )}
                </div>
              )}

              {/* Interest Rates - Always show in edit mode */}
              {(isEditing || criteria.interest_rate || criteria.interest_rate_sofr || criteria.interest_rate_wsj || criteria.interest_rate_prime) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg flex items-center gap-2">
                    <Percent className="h-5 w-5 text-slate-600" />
                    Interest Rates
                  </h4>
                  {isEditing ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div>
                        <Label htmlFor="interest_rate">Base Rate (%)</Label>
                        <Input
                          id="interest_rate"
                          type="number"
                          step="0.01"
                          value={editedCriteria?.interest_rate || ''}
                          onChange={(e) => handleInputChange('interest_rate', parseFloat(e.target.value) || null)}
                          placeholder="Enter base rate"
                        />
                      </div>
                      <div>
                        <Label htmlFor="interest_rate_sofr">SOFR (%)</Label>
                        <Input
                          id="interest_rate_sofr"
                          type="number"
                          step="0.01"
                          value={editedCriteria?.interest_rate_sofr || ''}
                          onChange={(e) => handleInputChange('interest_rate_sofr', parseFloat(e.target.value) || null)}
                          placeholder="Enter SOFR rate"
                        />
                      </div>
                      <div>
                        <Label htmlFor="interest_rate_wsj">WSJ (%)</Label>
                        <Input
                          id="interest_rate_wsj"
                          type="number"
                          step="0.01"
                          value={editedCriteria?.interest_rate_wsj || ''}
                          onChange={(e) => handleInputChange('interest_rate_wsj', parseFloat(e.target.value) || null)}
                          placeholder="Enter WSJ rate"
                        />
                      </div>
                      <div>
                        <Label htmlFor="interest_rate_prime">Prime (%)</Label>
                        <Input
                          id="interest_rate_prime"
                          type="number"
                          step="0.01"
                          value={editedCriteria?.interest_rate_prime || ''}
                          onChange={(e) => handleInputChange('interest_rate_prime', parseFloat(e.target.value) || null)}
                          placeholder="Enter Prime rate"
                        />
                      </div>
                    </div>
                  ) : (
                    (criteria.interest_rate || criteria.interest_rate_sofr || criteria.interest_rate_wsj || criteria.interest_rate_prime) && (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        {criteria.interest_rate && (
                          <div className="bg-slate-50 p-4 rounded-xl border">
                            <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Base Rate</label>
                            <p className="text-2xl font-bold text-slate-900 mt-1">{formatPercentage(criteria.interest_rate)}</p>
                          </div>
                        )}
                        {criteria.interest_rate_sofr && (
                          <div className="bg-slate-50 p-4 rounded-xl border">
                            <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">SOFR</label>
                            <p className="text-2xl font-bold text-slate-900 mt-1">{formatPercentage(criteria.interest_rate_sofr)}</p>
                          </div>
                        )}
                        {criteria.interest_rate_wsj && (
                          <div className="bg-slate-50 p-4 rounded-xl border">
                            <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">WSJ</label>
                            <p className="text-2xl font-bold text-slate-900 mt-1">{formatPercentage(criteria.interest_rate_wsj)}</p>
                          </div>
                        )}
                        {criteria.interest_rate_prime && (
                          <div className="bg-slate-50 p-4 rounded-xl border">
                            <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Prime</label>
                            <p className="text-2xl font-bold text-slate-900 mt-1">{formatPercentage(criteria.interest_rate_prime)}</p>
                          </div>
                        )}
                      </div>
                    )
                  )}
                </div>
              )}

              {/* Loan Ratios - Always show in edit mode */}
              {(isEditing || criteria.loan_to_value_min || criteria.loan_to_value_max || criteria.loan_to_cost_min || criteria.loan_to_cost_max) && (
                <div className="space-y-6">
                  <h4 className="font-semibold text-slate-800 text-lg">Loan Ratios</h4>
                  
                  {(isEditing || criteria.loan_to_value_min || criteria.loan_to_value_max) && (
                    <div className="space-y-3">
                      <h5 className="font-medium text-slate-700">LTV (Loan to Value)</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {isEditing ? (
                          <>
                            <div>
                              <Label htmlFor="loan_to_value_min">Minimum LTV (%)</Label>
                              <Input
                                id="loan_to_value_min"
                                type="number"
                                step="0.01"
                                value={editedCriteria?.loan_to_value_min || ''}
                                onChange={(e) => handleInputChange('loan_to_value_min', parseFloat(e.target.value) || null)}
                                placeholder="Enter minimum LTV"
                              />
                            </div>
                            <div>
                              <Label htmlFor="loan_to_value_max">Maximum LTV (%)</Label>
                              <Input
                                id="loan_to_value_max"
                                type="number"
                                step="0.01"
                                value={editedCriteria?.loan_to_value_max || ''}
                                onChange={(e) => handleInputChange('loan_to_value_max', parseFloat(e.target.value) || null)}
                                placeholder="Enter maximum LTV"
                              />
                            </div>
                          </>
                        ) : (
                          <>
                            {criteria.loan_to_value_min && (
                              <div className="bg-slate-50 p-3 rounded-lg border">
                                <span className="text-sm text-slate-600">Min: </span>
                                <span className="font-semibold text-slate-900">{formatPercent(criteria.loan_to_value_min)}</span>
                              </div>
                            )}
                            {criteria.loan_to_value_max && (
                              <div className="bg-slate-50 p-3 rounded-lg border">
                                <span className="text-sm text-slate-600">Max: </span>
                                <span className="font-semibold text-slate-900">{formatPercent(criteria.loan_to_value_max)}</span>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  )}

                  {(isEditing || criteria.loan_to_cost_min || criteria.loan_to_cost_max) && (
                    <div className="space-y-3">
                      <h5 className="font-medium text-slate-700">LTC (Loan to Cost)</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {isEditing ? (
                          <>
                            <div>
                              <Label htmlFor="loan_to_cost_min">Minimum LTC (%)</Label>
                              <Input
                                id="loan_to_cost_min"
                                type="number"
                                step="0.01"
                                value={editedCriteria?.loan_to_cost_min || ''}
                                onChange={(e) => handleInputChange('loan_to_cost_min', parseFloat(e.target.value) || null)}
                                placeholder="Enter minimum LTC"
                              />
                            </div>
                            <div>
                              <Label htmlFor="loan_to_cost_max">Maximum LTC (%)</Label>
                              <Input
                                id="loan_to_cost_max"
                                type="number"
                                step="0.01"
                                value={editedCriteria?.loan_to_cost_max || ''}
                                onChange={(e) => handleInputChange('loan_to_cost_max', parseFloat(e.target.value) || null)}
                                placeholder="Enter maximum LTC"
                              />
                            </div>
                          </>
                        ) : (
                          <>
                            {criteria.loan_to_cost_min && (
                              <div className="bg-slate-50 p-3 rounded-lg border">
                                <span className="text-sm text-slate-600">Min: </span>
                                <span className="font-semibold text-slate-900">{formatPercent(criteria.loan_to_cost_min)}</span>
                              </div>
                            )}
                            {criteria.loan_to_cost_max && (
                              <div className="bg-slate-50 p-3 rounded-lg border">
                                <span className="text-sm text-slate-600">Max: </span>
                                <span className="font-semibold text-slate-900">{formatPercent(criteria.loan_to_cost_max)}</span>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Fees - Always show in edit mode */}
              {(isEditing || criteria.loan_origination_fee_min || criteria.loan_origination_fee_max || criteria.loan_exit_fee_min || criteria.loan_exit_fee_max) && (
                <div className="space-y-6">
                  <h4 className="font-semibold text-slate-800 text-lg">Loan Fees</h4>
                  
                  {(isEditing || criteria.loan_origination_fee_min || criteria.loan_origination_fee_max) && (
                    <div className="space-y-3">
                      <h5 className="font-medium text-slate-700">Origination Fee</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {isEditing ? (
                          <>
                            <div>
                              <Label htmlFor="loan_origination_fee_min">Minimum Origination Fee (%)</Label>
                              <Input
                                id="loan_origination_fee_min"
                                type="number"
                                step="0.01"
                                value={editedCriteria?.loan_origination_fee_min || ''}
                                onChange={(e) => handleInputChange('loan_origination_fee_min', parseFloat(e.target.value) || null)}
                                placeholder="Enter minimum origination fee"
                              />
                            </div>
                            <div>
                              <Label htmlFor="loan_origination_fee_max">Maximum Origination Fee (%)</Label>
                              <Input
                                id="loan_origination_fee_max"
                                type="number"
                                step="0.01"
                                value={editedCriteria?.loan_origination_fee_max || ''}
                                onChange={(e) => handleInputChange('loan_origination_fee_max', parseFloat(e.target.value) || null)}
                                placeholder="Enter maximum origination fee"
                              />
                            </div>
                          </>
                        ) : (
                          <>
                            {criteria.loan_origination_fee_min && (
                              <div className="bg-slate-50 p-3 rounded-lg border">
                                <span className="text-sm text-slate-600">Min: </span>
                                <span className="font-semibold text-slate-900">{formatPercent(criteria.loan_origination_fee_min)}</span>
                              </div>
                            )}
                            {criteria.loan_origination_fee_max && (
                              <div className="bg-slate-50 p-3 rounded-lg border">
                                <span className="text-sm text-slate-600">Max: </span>
                                <span className="font-semibold text-slate-900">{formatPercent(criteria.loan_origination_fee_max)}</span>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  )}

                  {(isEditing || criteria.loan_exit_fee_min || criteria.loan_exit_fee_max) && (
                    <div className="space-y-3">
                      <h5 className="font-medium text-slate-700">Exit Fee</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {isEditing ? (
                          <>
                            <div>
                              <Label htmlFor="loan_exit_fee_min">Minimum Exit Fee (%)</Label>
                              <Input
                                id="loan_exit_fee_min"
                                type="number"
                                step="0.01"
                                value={editedCriteria?.loan_exit_fee_min || ''}
                                onChange={(e) => handleInputChange('loan_exit_fee_min', parseFloat(e.target.value) || null)}
                                placeholder="Enter minimum exit fee"
                              />
                            </div>
                            <div>
                              <Label htmlFor="loan_exit_fee_max">Maximum Exit Fee (%)</Label>
                              <Input
                                id="loan_exit_fee_max"
                                type="number"
                                step="0.01"
                                value={editedCriteria?.loan_exit_fee_max || ''}
                                onChange={(e) => handleInputChange('loan_exit_fee_max', parseFloat(e.target.value) || null)}
                                placeholder="Enter maximum exit fee"
                              />
                            </div>
                          </>
                        ) : (
                          <>
                            {criteria.loan_exit_fee_min && (
                              <div className="bg-slate-50 p-3 rounded-lg border">
                                <span className="text-sm text-slate-600">Min: </span>
                                <span className="font-semibold text-slate-900">{formatPercent(criteria.loan_exit_fee_min)}</span>
                              </div>
                            )}
                            {criteria.loan_exit_fee_max && (
                              <div className="bg-slate-50 p-3 rounded-lg border">
                                <span className="text-sm text-slate-600">Max: </span>
                                <span className="font-semibold text-slate-900">{formatPercent(criteria.loan_exit_fee_max)}</span>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* DSCR - Always show in edit mode */}
              {(isEditing || criteria.min_loan_dscr || criteria.max_loan_dscr) && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-slate-800 text-lg flex items-center gap-2">
                    <Shield className="h-5 w-5 text-slate-600" />
                    DSCR (Debt Service Coverage Ratio)
                  </h4>
                  {isEditing ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="min_loan_dscr">Minimum DSCR</Label>
                        <Input
                          id="min_loan_dscr"
                          type="number"
                          step="0.01"
                          value={editedCriteria?.min_loan_dscr || ''}
                          onChange={(e) => handleInputChange('min_loan_dscr', parseFloat(e.target.value) || null)}
                          placeholder="Enter minimum DSCR"
                        />
                      </div>
                      <div>
                        <Label htmlFor="max_loan_dscr">Maximum DSCR</Label>
                        <Input
                          id="max_loan_dscr"
                          type="number"
                          step="0.01"
                          value={editedCriteria?.max_loan_dscr || ''}
                          onChange={(e) => handleInputChange('max_loan_dscr', parseFloat(e.target.value) || null)}
                          placeholder="Enter maximum DSCR"
                        />
                      </div>
                    </div>
                  ) : (
                    (criteria.min_loan_dscr || criteria.max_loan_dscr) && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {criteria.min_loan_dscr && (
                          <div className="bg-slate-50 p-4 rounded-xl border">
                            <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Minimum</label>
                            <p className="text-2xl font-bold text-slate-900 mt-1">{formatDSCRPercent(criteria.min_loan_dscr)}</p>
                          </div>
                        )}
                        {criteria.max_loan_dscr && (
                          <div className="bg-slate-50 p-4 rounded-xl border">
                            <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Maximum</label>
                            <p className="text-2xl font-bold text-slate-900 mt-1">{formatDSCRPercent(criteria.max_loan_dscr)}</p>
                          </div>
                        )}
                      </div>
                    )
                  )}
                </div>
              )}

              {/* Loan Types and Programs - Always show in edit mode */}
              <div className="space-y-6">
                {criteria.loan_type && criteria.loan_type.length > 0 && (
                  <div className="space-y-4">
                    <h4 className="font-semibold text-slate-800 text-lg">Loan Types</h4>
                    {isEditing ? (
                      <div className="space-y-2">
                        {(editedCriteria?.loan_type || []).map((item, index) => (
                          <div key={index} className="flex gap-2">
                            <Select
                              value={item}
                              onValueChange={(value) => handleArrayFieldChange('loan_type', value, index)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select loan type" />
                              </SelectTrigger>
                              <SelectContent>
                                {mappings['Capital Position']?.children?.map((child: string) => (
                                  <SelectItem key={child} value={child}>
                                    {child}
                                  </SelectItem>
                                )) || []}
                              </SelectContent>
                            </Select>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => removeArrayItem('loan_type', index)}
                            >
                              Remove
                            </Button>
                          </div>
                        ))}
                        <Button
                          variant="outline"
                          onClick={() => addArrayItem('loan_type')}
                          className="w-full"
                        >
                          Add Loan Type
                        </Button>
                      </div>
                    ) : (
                      <CompactBadges items={criteria.loan_type ?? []} />
                    )}
                  </div>
                )}
                
                {criteria.loan_type_normalized && criteria.loan_type_normalized.length > 0 && (
                  <div className="space-y-4">
                    <h4 className="font-semibold text-slate-800 text-lg">Normalized Loan Types</h4>
                    <CompactBadges items={criteria.loan_type_normalized ?? []} />
                  </div>
                )}
                
                {criteria.loan_program && criteria.loan_program.length > 0 && (
                  <div className="space-y-4">
                    <h4 className="font-semibold text-slate-800 text-lg">Loan Programs</h4>
                    {isEditing ? (
                      <div className="space-y-2">
                        {(editedCriteria?.loan_program || []).map((item, index) => (
                          <div key={index} className="flex gap-2">
                            <Select
                              value={item}
                              onValueChange={(value) => handleArrayFieldChange('loan_program', value, index)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select loan program" />
                              </SelectTrigger>
                              <SelectContent>
                                {mappings['Loan Program']?.parents?.map((mapping: string) => (
                                  <SelectItem key={mapping} value={mapping}>
                                    {mapping}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => removeArrayItem('loan_program', index)}
                            >
                              Remove
                            </Button>
                          </div>
                        ))}
                        <Button
                          variant="outline"
                          onClick={() => addArrayItem('loan_program')}
                          className="w-full"
                        >
                          Add Loan Program
                        </Button>
                      </div>
                    ) : (
                      <CompactBadges items={criteria.loan_program} />
                    )}
                  </div>
                )}
                
                {criteria.structured_loan_tranche && criteria.structured_loan_tranche.length > 0 && (
                  <div className="space-y-4">
                    <h4 className="font-semibold text-slate-800 text-lg">Structured Loan Tranche</h4>
                    {isEditing ? (
                      <div className="space-y-2">
                        {(editedCriteria?.structured_loan_tranche || []).map((item, index) => (
                          <div key={index} className="flex gap-2">
                            <Select
                              value={item}
                              onValueChange={(value) => handleArrayFieldChange('structured_loan_tranche', value, index)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select loan tranche" />
                              </SelectTrigger>
                              <SelectContent>
                                {mappings['Structured Loan Tranches']?.parents?.map((mapping: string) => (
                                  <SelectItem key={mapping} value={mapping}>
                                    {mapping}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => removeArrayItem('structured_loan_tranche', index)}
                            >
                              Remove
                            </Button>
                          </div>
                        ))}
                        <Button
                          variant="outline"
                          onClick={() => addArrayItem('structured_loan_tranche')}
                          className="w-full"
                        >
                          Add Loan Tranche
                        </Button>
                      </div>
                    ) : (
                      <CompactBadges items={criteria.structured_loan_tranche} />
                    )}
                  </div>
                )}
                
                {criteria.recourse_loan && criteria.recourse_loan.length > 0 && (
                  <div className="space-y-4">
                    <h4 className="font-semibold text-slate-800 text-lg">Recourse Loan</h4>
                    {isEditing ? (
                      <div className="space-y-2">
                        {(editedCriteria?.recourse_loan || []).map((item, index) => (
                          <div key={index} className="flex gap-2">
                            <Select
                              value={item}
                              onValueChange={(value) => handleArrayFieldChange('recourse_loan', value, index)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select recourse loan type" />
                              </SelectTrigger>
                              <SelectContent>
                                {mappings['Recourse Loan']?.parents?.map((mapping: string) => (
                                  <SelectItem key={mapping} value={mapping}>
                                    {mapping}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => removeArrayItem('recourse_loan', index)}
                            >
                              Remove
                            </Button>
                          </div>
                        ))}
                        <Button
                          variant="outline"
                          onClick={() => addArrayItem('recourse_loan')}
                          className="w-full"
                        >
                          Add Recourse Loan
                        </Button>
                      </div>
                    ) : (
                      <CompactBadges items={criteria.recourse_loan ?? []} />
                    )}
                  </div>
                )}
              </div>

              {/* Other Loan Details - Always show in edit mode */}
              {(isEditing || criteria.capital_source || criteria.closing_time_weeks) && (
                <div className="space-y-6">
                  <h4 className="font-semibold text-slate-800 text-lg">Additional Details</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {isEditing ? (
                      <>
                        <div>
                          <Label htmlFor="capital_source">Capital Source</Label>
                          <Input
                            id="capital_source"
                            value={editedCriteria?.capital_source || ''}
                            onChange={(e) => handleInputChange('capital_source', e.target.value)}
                            placeholder="Enter capital source"
                          />
                        </div>
                        <div>
                          <Label htmlFor="closing_time_weeks">Closing Time (weeks)</Label>
                          <Input
                            id="closing_time_weeks"
                            type="number"
                            step="0.01"
                            value={editedCriteria?.closing_time_weeks || ''}
                            onChange={(e) => handleInputChange('closing_time_weeks', parseFloat(e.target.value) || null)}
                            placeholder="Enter closing time in weeks"
                          />
                        </div>
                      </>
                    ) : (
                      <>
                        {criteria.capital_source && (
                          <div className="bg-slate-50 p-4 rounded-xl border">
                            <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Capital Source</label>
                            <p className="text-lg font-semibold text-slate-900 mt-1">{criteria.capital_source}</p>
                          </div>
                        )}
                        {criteria.closing_time_weeks && (
                          <div className="bg-slate-50 p-4 rounded-xl border">
                            <label className="text-sm font-semibold text-slate-600 uppercase tracking-wide">Closing Time</label>
                            <p className="text-lg font-semibold text-slate-900 mt-1">{criteria.closing_time_weeks} weeks</p>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Notes */}
        {criteria.extra_fields?.notes && (
          <div className="space-y-4">
            {formatNotes(criteria.extra_fields.notes)}
          </div>
        )}

        {/* Extra Fields */}
        {criteria.extra_fields && Object.keys(criteria.extra_fields).filter(key => key !== 'notes').length > 0 && (
          <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl text-slate-800">
                <div className="p-2 rounded-lg bg-slate-50">
                  <FileText className="h-5 w-5 text-slate-600" />
                </div>
                Additional Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              {prettifyExtraFields(Object.fromEntries(
                Object.entries(criteria.extra_fields).filter(([key]) => key !== 'notes')
              ))}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
  
} 