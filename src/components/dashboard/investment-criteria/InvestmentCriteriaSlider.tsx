'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  ChevronLeft, 
  ChevronRight, 
  FileText, 
  Loader2, 
  AlertCircle,
  Target,
  Building2,
  User,
  Briefcase
} from 'lucide-react'
import { InvestmentCriteria } from '@/types/investment-criteria'
import InvestmentCriteriaDetailViewV2 from './InvestmentCriteriaDetailViewV2'

interface InvestmentCriteriaSliderProps {
  entityType: 'contact' | 'company'
  entityId: string | number
  entityEmail?: string
  entityName?: string
}

export default function InvestmentCriteriaSlider({ 
  entityType, 
  entityId, 
  entityEmail, 
  entityName 
}: InvestmentCriteriaSliderProps) {
  const [criteriaList, setCriteriaList] = useState<InvestmentCriteria[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const fetchInvestmentCriteria = async () => {
      try {
        setLoading(true)
        let response: Response

        // Choose the appropriate API endpoint based on entity type
        if (entityType === 'contact') {
          response = await fetch(`/api/investment-criteria/by-contact/${entityId}`)
          
          // Fallback to search by email if that fails
          if (!response.ok && entityEmail) {
            response = await fetch(`/api/investment-criteria?email=${encodeURIComponent(entityEmail)}`)
          }
        } else {
          response = await fetch(`/api/investment-criteria/by-company/${entityId}`)
          
          // Fallback to search by company name if that fails
          if (!response.ok && entityName) {
            response = await fetch(`/api/investment-criteria?entityName=${encodeURIComponent(entityName)}`)
          }
        }

        if (!response.ok) {
          throw new Error('Failed to fetch investment criteria')
        }

        const data = await response.json()
        const criteria = Array.isArray(data) ? data : (data.criteria || [])
        
        // Debug logging
        console.log('V1 InvestmentCriteriaSlider - Fetched data:', {
          entityType,
          entityId,
          criteriaCount: criteria.length,
          firstCriteria: criteria[0],
          allCriteria: criteria
        })
        
        setCriteriaList(criteria)
        
        // Reset to first item if we have criteria
        if (criteria.length > 0) {
          setCurrentIndex(0)
        }
      } catch (err) {
        console.error('Error fetching investment criteria:', err)
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchInvestmentCriteria()
  }, [entityType, entityId, entityEmail, entityName])

  const goToPrevious = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex > 0 ? prevIndex - 1 : criteriaList.length - 1
    )
  }

  const goToNext = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex < criteriaList.length - 1 ? prevIndex + 1 : 0
    )
  }

  const getEntityIcon = (entityType: string) => {
    if (entityType.startsWith('Company')) {
      return <Building2 className="h-4 w-4 text-blue-600" />
    } else if (entityType === 'Contact') {
      return <User className="h-4 w-4 text-emerald-600" />
    } else if (entityType === 'Deal') {
      return <Briefcase className="h-4 w-4 text-purple-600" />
    } else {
      return <FileText className="h-4 w-4 text-slate-600" />
    }
  }

  // Generate IC label (capital position + loan type + deal size)
  const generateICLabel = (criteria: InvestmentCriteria) => {
    const parts: string[] = []
    
    // Add capital position (first value)
    if (criteria.capital_position && criteria.capital_position.length > 0) {
      parts.push(criteria.capital_position[0])
    }
    
    // Add loan type (first value)
    if (criteria.loan_type && criteria.loan_type.length > 0) {
      parts.push(criteria.loan_type[0])
    }
    
    // Add deal size range
    if (criteria.minimum_deal_size || criteria.maximum_deal_size) {
      const formatCurrency = (value: number) => {
        if (value >= 1000000000) {
          return `$${(value / 1000000000).toFixed(1)}B`
        }
        if (value >= 1000000) {
          return `$${(value / 1000000).toFixed(1)}M`
        }
        if (value >= 1000) {
          return `$${(value / 1000).toFixed(0)}K`
        }
        return `$${value.toLocaleString()}`
      }
      
      let dealSizeStr = ''
      if (criteria.minimum_deal_size && criteria.maximum_deal_size) {
        dealSizeStr = `${formatCurrency(criteria.minimum_deal_size)} - ${formatCurrency(criteria.maximum_deal_size)}`
      } else if (criteria.minimum_deal_size) {
        dealSizeStr = `${formatCurrency(criteria.minimum_deal_size)}+`
      } else if (criteria.maximum_deal_size) {
        dealSizeStr = `up to ${formatCurrency(criteria.maximum_deal_size)}`
      }
      
      if (dealSizeStr) {
        parts.push(dealSizeStr)
      }
    }
    
    return parts.join(' | ') || 'Investment Criteria'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="text-gray-600">Loading investment criteria...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Card className="bg-white shadow-sm">
        <CardContent className="pt-6">
          <div className="text-center py-6">
            <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Error Loading Investment Criteria
            </h3>
            <p className="text-red-500 mb-4">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (criteriaList.length === 0) {
    return (
      <Card className="bg-white shadow-sm">
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Target className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-700 mb-2">
              No Investment Criteria Found
            </h3>
            <p className="text-gray-500 max-w-md mx-auto">
              No investment criteria records have been found for this {entityType}.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const currentCriteria = criteriaList[currentIndex]

  return (
    <div className="space-y-4">
      {/* Navigation Header */}
      {criteriaList.length > 1 && (
        <Card className="bg-white shadow-sm">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getEntityIcon(currentCriteria.entity_type)}
                <div>
                  <CardTitle className="text-lg font-medium">
                    {generateICLabel(currentCriteria)}
                  </CardTitle>
                  <p className="text-sm text-slate-500">
                    Showing {currentIndex + 1} of {criteriaList.length} criteria records
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevious}
                  disabled={criteriaList.length <= 1}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>
                
                <div className="flex items-center gap-1 px-3">
                  {criteriaList.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentIndex(index)}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index === currentIndex 
                          ? 'bg-blue-600' 
                          : 'bg-gray-300 hover:bg-gray-400'
                      }`}
                    />
                  ))}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNext}
                  disabled={criteriaList.length <= 1}
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="pt-0">
            <div className="flex flex-wrap gap-2">
              {criteriaList.map((criteria, index) => (
                <button
                  key={criteria.criteria_id}
                  onClick={() => setCurrentIndex(index)}
                  className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                    index === currentIndex
                      ? 'bg-blue-100 text-blue-800 border border-blue-200'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {generateICLabel(criteria)}
                </button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Full Detail View */}
      <div className="bg-white rounded-lg">
        <InvestmentCriteriaDetailViewV2 
          entityType={entityType}
          entityId={entityId}
        />
      </div>
    </div>
  )
} 