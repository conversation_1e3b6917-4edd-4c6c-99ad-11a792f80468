'use client'

import React from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Trash2, 
  Building, 
  User, 
  MapPin, 
  DollarSign, 
  Calendar,
  TrendingUp,
  Percent,
  Home,
  Globe,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Target,
  Briefcase
} from 'lucide-react'
import { InvestmentCriteria, InvestmentCriteriaResponse } from '@/types/investment-criteria'

interface InvestmentCriteriaCardViewProps {
  data?: InvestmentCriteriaResponse
  isLoading?: boolean
  onPageChange?: (page: number, limit?: number) => void
  onEdit?: (criteria: InvestmentCriteria) => void
  onDelete?: (criteriaId: number) => void
}

export default function InvestmentCriteriaCardView({
  data,
  isLoading = false,
  onPageChange,
  onEdit,
  onDelete
}: InvestmentCriteriaCardViewProps) {
  
  // Format currency display already in millions
  const formatCurrency = (value?: number) => {
    if (!value) return 'Not specified'
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}B`
    }
    return `$${value.toLocaleString()}M`
  }

  // Format percentage display  
  const formatPercentage = (value?: number | null) => {
    if (value === null || value === undefined) return null
    if (value === 0) return null // Don't show 0% - treat as no value
    return `${value}%`
  }

  // Format date display
  const formatDate = (dateStr?: string) => {
    if (!dateStr) return 'Unknown'
    return new Date(dateStr).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Format array display - compact version
  const formatArrayCompact = (arr?: string[], maxItems = 3) => {
    if (!arr || arr.length === 0) return null
    
    const displayItems = arr.slice(0, maxItems)
    const remainingCount = arr.length - maxItems
    
    return (
      <div className="flex flex-wrap gap-1">
        {displayItems.map((item, index) => (
          <Badge key={index} variant="secondary" className="text-xs px-1.5 py-0.5 bg-gray-100 text-gray-700 font-normal">
            {item}
          </Badge>
        ))}
        {remainingCount > 0 && (
          <Badge variant="outline" className="text-xs px-1.5 py-0.5 border-gray-300 text-gray-600">
            +{remainingCount}
          </Badge>
        )}
      </div>
    )
  }

  // Get entity icon
  const getEntityIcon = (entityType: string) => {
    if (entityType?.toLowerCase().includes('company')) {
      return <Building className="h-4 w-4 text-blue-600" />
    } else if (entityType?.toLowerCase().includes('contact')) {
      return <User className="h-4 w-4 text-green-600" />
    } else if (entityType?.toLowerCase().includes('deal')) {
      return <Briefcase className="h-4 w-4 text-purple-600" />
    } else {
      return <Building className="h-4 w-4 text-gray-600" />
    }
  }

  // Get display name - prioritize entity_name, fallback to entity_id if needed
  const getDisplayName = (criteria: InvestmentCriteria) => {
    // Check if entity_name exists and is not just the entity_id repeated
    if (criteria.entity_name && criteria.entity_name !== criteria.entity_id) {
      return criteria.entity_name
    }
    
    // Fallback: try to make entity_id more readable if it's all we have
    if (criteria.entity_id) {
      // If it's a numeric ID, prefix with entity type
      if (/^\d+$/.test(criteria.entity_id)) {
        const entityTypeShort = criteria.entity_type?.split(' ')[0] || 'Entity'
        return `${entityTypeShort} #${criteria.entity_id}`
      }
      return criteria.entity_id
    }
    
    return 'Unknown Entity'
  }

  // Handle page size change
  const handlePageSizeChange = (newLimit: string) => {
    const limit = parseInt(newLimit)
    if (onPageChange && data?.pagination) {
      onPageChange(1, limit) // Reset to page 1 when changing page size
    }
  }

  // Generate page numbers for pagination
  const generatePageNumbers = () => {
    if (!data?.pagination) return []
    
    const { page, totalPages } = data.pagination
    const pages = []
    const maxVisiblePages = 5
    
    let startPage = Math.max(1, page - Math.floor(maxVisiblePages / 2))
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1)
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i)
    }
    
    return pages
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-xl font-semibold text-gray-900">Investment Criteria</CardTitle>
          </CardHeader>
        </Card>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="border-0 shadow-sm">
              <CardHeader className="pb-2">
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-8 w-8 rounded" />
                  <div className="space-y-1 flex-1">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-2">
                <Skeleton className="h-3 w-full" />
                <Skeleton className="h-3 w-2/3" />
                <div className="flex gap-1">
                  <Skeleton className="h-5 w-12" />
                  <Skeleton className="h-5 w-12" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!data || data.data.length === 0) {
    return (
      <div className="space-y-4">
        <Card className="border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-gray-900">Investment Criteria</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <div className="mx-auto h-16 w-16 text-gray-300 mb-4">
                <Target className="h-full w-full" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No investment criteria found</h3>
              <p className="text-gray-600 text-sm">
                Try adjusting your filters or check back later.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Compact Header */}
      <Card className="border-0 shadow-sm bg-white">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-semibold text-gray-900">Investment Criteria</CardTitle>
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-600">
                {((data.pagination.page - 1) * data.pagination.limit) + 1}-{Math.min(data.pagination.page * data.pagination.limit, data.pagination.total)} of {data.pagination.total.toLocaleString()}
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Show</span>
                <Select value={data.pagination.limit.toString()} onValueChange={handlePageSizeChange}>
                  <SelectTrigger className="w-16 h-8 border-gray-200">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Compact Dashboard Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {data.data.map((criteria) => (
          <Link key={criteria.criteria_id} href={`/dashboard/investment-criteria/${criteria.criteria_id}`}>
            <Card className="border-0 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer group bg-white">
              <CardHeader className="pb-2 px-4 pt-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-2 flex-1 min-w-0">
                    <div className="flex-shrink-0 p-1 bg-gray-50 rounded">
                      {getEntityIcon(criteria.entity_type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-sm leading-tight text-gray-900 group-hover:text-blue-600 transition-colors truncate">
                        {getDisplayName(criteria)}
                      </h3>
                      <Badge 
                        variant="outline" 
                        className="text-xs mt-1 font-normal border-gray-200 text-gray-600 px-1.5 py-0"
                      >
                        {criteria.entity_type?.split(' ')[0] || 'Unknown'}
                      </Badge>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button 
                        variant="ghost" 
                        className="h-6 w-6 p-0 hover:bg-gray-100 transition-colors"
                        onClick={(e: React.MouseEvent<HTMLButtonElement>) => e.preventDefault()}
                      >
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-40">
                      <DropdownMenuItem asChild>
                        <Link 
                          href={`/dashboard/investment-criteria/${criteria.criteria_id}`}
                          className="flex items-center"
                        >
                          <Eye className="mr-2 h-3 w-3" />
                          View
                        </Link>
                      </DropdownMenuItem>
                      {onEdit && (
                        <DropdownMenuItem onClick={(e) => {
                          e.preventDefault();
                          onEdit(criteria);
                        }}>
                          <Edit className="mr-2 h-3 w-3" />
                          Edit
                        </DropdownMenuItem>
                      )}
                      {onDelete && (
                        <DropdownMenuItem 
                          onClick={(e) => {
                            e.preventDefault();
                            onDelete(criteria.criteria_id);
                          }}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="mr-2 h-3 w-3" />
                          Delete
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
            
              <CardContent className="px-4 pb-3 space-y-2">
                {/* Deal Size - Prominent Display */}
                <div className="bg-green-50 rounded-md p-2 border border-green-100">
                  <div className="flex items-center gap-1 mb-1">
                    <DollarSign className="h-3 w-3 text-green-600" />
                    <span className="font-medium text-xs text-green-800">Deal Size</span>
                  </div>
                  {(() => {
                    const minFormatted = formatCurrency(criteria.minimum_deal_size)
                    const maxFormatted = formatCurrency(criteria.maximum_deal_size)
                    
                    if (!minFormatted && !maxFormatted) {
                      return <span className="text-xs text-gray-500">Not specified</span>
                    }
                    
                    let rangeText = ''
                    if (minFormatted && maxFormatted) {
                      rangeText = `${minFormatted} - ${maxFormatted}`
                    } else if (minFormatted) {
                      rangeText = `From ${minFormatted}`
                    } else if (maxFormatted) {
                      rangeText = `Up to ${maxFormatted}`
                    }
                    
                    return (
                      <div className="text-sm font-semibold text-green-900">
                        {rangeText}
                        {(() => {
                          const targetReturn = formatPercentage(criteria.target_return)
                          if (!targetReturn) return null
                          return (
                            <span className="text-xs font-normal text-green-700 ml-2">
                              ({targetReturn} target)
                            </span>
                          )
                        })()}
                      </div>
                    )
                  })()}
                </div>

                {/* Compact Info Grid */}
                <div className="grid grid-cols-1 gap-2">
                  {/* Capital & Property Types */}
                  {(() => {
                    const capitalTypes = [...(criteria.capital_position || [])]
                    const propertyTypes = criteria.property_types || []
                    
                    if (capitalTypes.length === 0 && propertyTypes.length === 0) return null
                    
                    return (
                      <div className="space-y-1">
                        {capitalTypes.length > 0 && (
                          <div>
                            <div className="flex items-center gap-1 mb-0.5">
                              <TrendingUp className="h-3 w-3 text-purple-600" />
                              <span className="text-xs font-medium text-gray-700">Capital</span>
                            </div>
                            {formatArrayCompact(capitalTypes, 2)}
                          </div>
                        )}
                        {propertyTypes.length > 0 && (
                          <div>
                            <div className="flex items-center gap-1 mb-0.5">
                              <Home className="h-3 w-3 text-orange-600" />
                              <span className="text-xs font-medium text-gray-700">Property</span>
                            </div>
                            {formatArrayCompact(propertyTypes, 2)}
                          </div>
                        )}
                      </div>
                    )
                  })()}

                  {/* Geographic Focus */}
                  {(() => {
                    const countries = criteria.country || []
                    const states = criteria.state || []
                    
                    if (countries.length === 0 && states.length === 0) return null
                    
                    return (
                      <div>
                        <div className="flex items-center gap-1 mb-0.5">
                          <Globe className="h-3 w-3 text-blue-600" />
                          <span className="text-xs font-medium text-gray-700">Location</span>
                        </div>
                        <div className="space-y-0.5">
                          {countries.length > 0 && formatArrayCompact(countries, 2)}
                        </div>
                      </div>
                    )
                  })()}

                  {/* Loan Ratios - Compact */}
                  {(() => {
                    const ltvMin = formatPercentage(criteria.loan_to_value_min)
                    const ltvMax = formatPercentage(criteria.loan_to_value_max)
                    const ltcMin = formatPercentage(criteria.loan_to_cost_min)
                    const ltcMax = formatPercentage(criteria.loan_to_cost_max)
                    
                    const hasLTV = ltvMin || ltvMax
                    const hasLTC = ltcMin || ltcMax
                    
                    if (!hasLTV && !hasLTC) return null
                    
                    return (
                      <div>
                        <div className="flex items-center gap-1 mb-0.5">
                          <Percent className="h-3 w-3 text-indigo-600" />
                          <span className="text-xs font-medium text-gray-700">Ratios</span>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {hasLTV && (
                            <Badge variant="outline" className="text-xs px-1.5 py-0.5 border-indigo-200 text-indigo-700">
                              LTV: {ltvMin && ltvMax ? `${ltvMin}-${ltvMax}` : ltvMin || ltvMax}
                            </Badge>
                          )}
                          {hasLTC && (
                            <Badge variant="outline" className="text-xs px-1.5 py-0.5 border-indigo-200 text-indigo-700">
                              LTC: {ltcMin && ltcMax ? `${ltcMin}-${ltcMax}` : ltcMin || ltcMax}
                            </Badge>
                          )}
                        </div>
                      </div>
                    )
                  })()}
                </div>

                {/* Footer */}
                <div className="pt-1 border-t border-gray-100 flex items-center justify-between">
                  <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="h-3 w-3 mr-1" />
                    <span>{formatDate(criteria.updated_at)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>

      {/* Compact Pagination */}
      {data.pagination.totalPages > 1 && (
        <Card className="border-0 shadow-sm bg-white">
          <CardContent className="py-4">
            <div className="flex items-center justify-center space-x-1">
              {/* First Page */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange?.(1)}
                disabled={data.pagination.page === 1}
                className="h-8 w-8 p-0 border-gray-200 hover:bg-gray-50 disabled:opacity-50"
              >
                <ChevronsLeft className="h-3 w-3" />
              </Button>
              
              {/* Previous Page */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange?.(data.pagination.page - 1)}
                disabled={!data.pagination.hasPrev}
                className="h-8 w-8 p-0 border-gray-200 hover:bg-gray-50 disabled:opacity-50"
              >
                <ChevronLeft className="h-3 w-3" />
              </Button>

              {/* Page Numbers */}
              <div className="flex items-center space-x-1 mx-2">
                {generatePageNumbers().map((pageNum) => (
                  <Button
                    key={pageNum}
                    variant={pageNum === data.pagination.page ? "default" : "outline"}
                    size="sm"
                    onClick={() => onPageChange?.(pageNum)}
                    className={
                      pageNum === data.pagination.page
                        ? "h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
                        : "h-8 w-8 p-0 border-gray-200 hover:bg-gray-50 text-gray-700"
                    }
                  >
                    {pageNum}
                  </Button>
                ))}
              </div>

              {/* Next Page */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange?.(data.pagination.page + 1)}
                disabled={!data.pagination.hasNext}
                className="h-8 w-8 p-0 border-gray-200 hover:bg-gray-50 disabled:opacity-50"
              >
                <ChevronRight className="h-3 w-3" />
              </Button>

              {/* Last Page */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange?.(data.pagination.totalPages)}
                disabled={data.pagination.page === data.pagination.totalPages}
                className="h-8 w-8 p-0 border-gray-200 hover:bg-gray-50 disabled:opacity-50"
              >
                <ChevronsRight className="h-3 w-3" />
              </Button>
            </div>
            
            {/* Pagination Info */}
            <div className="text-center mt-3 text-sm text-gray-600">
              Page {data.pagination.page} of {data.pagination.totalPages}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 