'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { ReactMultiSelect } from '@/components/ui/react-multi-select'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  X, Filter, RotateCcw, TreePine, Search, Building, User,
  DollarSign, Percent, MapPin, TrendingUp, Building2, Globe, Settings, Sparkles,
  SlidersHorizontal, ChevronRight, Calendar, Clock, Target, PieChart, BarChart3,
  Briefcase, CreditCard, LineChart, Activity, Banknote, ArrowUpDown, ArrowUp, ArrowDown,
  Calculator, Timer, TrendingDown, CheckCircle
} from 'lucide-react'
import { InvestmentCriteriaFilters, FilterOptions, SORT_OPTIONS } from '@/types/investment-criteria'

interface MappingsData {
  [type: string]: {
    parents: string[]     // All unique value_1 entries
    children: string[]    // All unique value_2 entries
    hierarchical: {
      [parent: string]: string[]  // parent -> children mapping
    }
  }
}

interface InvestmentCriteriaFiltersProps {
  filters: InvestmentCriteriaFilters
  mappings?: MappingsData
  onFiltersChange: (filters: InvestmentCriteriaFilters) => void
  onClearFilters: () => void
  isLoading?: boolean
}

export default function InvestmentCriteriaFiltersComponent({
  filters,
  mappings,
  onFiltersChange,
  onClearFilters,
  isLoading = false
}: InvestmentCriteriaFiltersProps) {
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false)
  const [localFilters, setLocalFilters] = useState<InvestmentCriteriaFilters>(filters)

  // Sync local filters with prop changes
  useEffect(() => {
    setLocalFilters(filters)
  }, [filters])

  // Update parent when local filters change
  const updateFilters = (newFilters: Partial<InvestmentCriteriaFilters>) => {
    const updatedFilters = { ...localFilters, ...newFilters, page: 1 }
    setLocalFilters(updatedFilters)
    onFiltersChange(updatedFilters)
  }

  // Helper function to get parent values (for main dropdowns)
  const getParentValues = (type: string) => {
    return mappings?.[type]?.parents || []
  }

  // Helper function to get all child values (for initial child dropdown)
  const getAllChildValues = (type: string) => {
    return mappings?.[type]?.children || []
  }

  // Helper function to get filtered child values based on selected parents
  const getFilteredChildValues = (type: string, selectedParents: string[]) => {
    if (!selectedParents?.length) {
      return getAllChildValues(type)
    }
    
    const filteredChildren = new Set<string>()
    selectedParents.forEach(parent => {
      const children = mappings?.[type]?.hierarchical?.[parent] || []
      children.forEach(child => filteredChildren.add(child))
    })
    
    return Array.from(filteredChildren).sort()
  }

  // Count active filters - comprehensive coverage of all filter fields
  const getActiveFilterCount = () => {
    let count = 0
    
    // Basic filters
    if (localFilters.searchTerm) count++
    if (localFilters.entityType) count++
    if (localFilters.entityId) count++
    if (localFilters.entityName) count++
    if (localFilters.criteriaId) count++
    if (localFilters.isRequested) count++
    
    // Financial metrics
    if (localFilters.dealSizeMin || localFilters.dealSizeMax) count++
    if (localFilters.targetReturnMin || localFilters.targetReturnMax) count++
    if (localFilters.historicalIrrMin || localFilters.historicalIrrMax) count++
    if (localFilters.historicalEmMin || localFilters.historicalEmMax) count++
    
    // Hold periods
    if (localFilters.minHoldPeriod) count++
    if (localFilters.maxHoldPeriod) count++
    
    // Capital & financing
    if (localFilters.capitalPosition?.length) count++
    if (localFilters.capitalSource) count++
    if (localFilters.loanTypes?.length) count++
    if (localFilters.loanTypesNormalized?.length) count++
    if (localFilters.loanProgram?.length) count++
    if (localFilters.structuredLoanTranche?.length) count++
    if (localFilters.recourseLoan?.length) count++
    
    // Loan terms
    if (localFilters.minLoanTerm) count++
    if (localFilters.maxLoanTerm) count++
    if (localFilters.interestRateMin || localFilters.interestRateMax) count++
    if (localFilters.interestRateSofrMin || localFilters.interestRateSofrMax) count++
    if (localFilters.interestRateWsjMin || localFilters.interestRateWsjMax) count++
    if (localFilters.interestRatePrimeMin || localFilters.interestRatePrimeMax) count++
    
    // LTV/LTC ratios
    if (localFilters.loanToValueMin || localFilters.loanToValueMax) count++
    if (localFilters.loanToCostMin || localFilters.loanToCostMax) count++
    
    // Fees
    if (localFilters.loanOriginationFeeMin || localFilters.loanOriginationFeeMax) count++
    if (localFilters.loanExitFeeMin || localFilters.loanExitFeeMax) count++
    
    // DSCR
    if (localFilters.minLoanDscr) count++
    if (localFilters.maxLoanDscr) count++
    
    // Property & assets
    if (localFilters.propertyTypes?.length) count++
    if (localFilters.propertySubcategories?.length) count++
    if (localFilters.strategies?.length) count++
    if (localFilters.financialProducts?.length) count++
    
    // Geographic
    if (localFilters.countries?.length) count++
    if (localFilters.regions?.length) count++
    if (localFilters.states?.length) count++
    if (localFilters.cities?.length) count++
    
    // Timeline
    if (localFilters.closingTimeWeeks) count++
    
    // Status filters
    if (localFilters.isActive !== undefined) count++
    if (localFilters.createdBy) count++
    if (localFilters.updatedBy) count++
    if (localFilters.createdAtFrom || localFilters.createdAtTo) count++
    if (localFilters.updatedAtFrom || localFilters.updatedAtTo) count++
    
    return count
  }

  const activeFilterCount = getActiveFilterCount()

  return (
    <>
      {/* Modern Compact Filter Bar */}
      <div className="w-full bg-white border border-gray-200 rounded-xl shadow-sm mb-6">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            {/* Filter Button */}
            <Button
              onClick={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
              className={`flex items-center gap-3 px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 ${
                isFilterPanelOpen 
                  ? 'bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white' 
                  : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white'
              }`}
            >
              <SlidersHorizontal className="h-5 w-5" />
              <span className="font-medium">Advanced Filters</span>
              {activeFilterCount > 0 && (
                <Badge className="bg-white/20 text-white border border-white/20 ml-1">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>

            {/* Quick Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search criteria, entity, or ID..."
                value={localFilters.searchTerm || ''}
                onChange={(e) => updateFilters({ searchTerm: e.target.value || undefined })}
                className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
              />
            </div>
          </div>

          <div className="flex items-center gap-3">
            {/* Active Filters Count */}
            {activeFilterCount > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  {activeFilterCount} filter{activeFilterCount !== 1 ? 's' : ''} applied
                </span>
                <Button
                  onClick={onClearFilters}
                  className="text-gray-500 hover:text-red-600 bg-transparent border border-gray-200 hover:border-red-200 hover:bg-red-50 px-3 py-1 text-sm"
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Clear All
                </Button>
              </div>
            )}

            {/* Enhanced Sort Controls */}
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Sort by:</span>
              </div>
              <div className="flex items-center gap-2">
                <Select 
                  value={localFilters.sortBy || 'updated_at'} 
                  onValueChange={(value) => updateFilters({ sortBy: value })}
                >
                  <SelectTrigger className="w-auto min-w-[200px] border-gray-200 bg-white shadow-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="max-h-[400px]">
                    <div className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100">
                      Default
                    </div>
                    <SelectItem value="updated_at">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        Last Updated (Newest First)
                      </div>
                    </SelectItem>
                    
                    <div className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100 mt-2">
                      Financial Metrics
                    </div>
                    <SelectItem value="target_return">
                      <div className="flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        Target Return (Highest First)
                      </div>
                    </SelectItem>
                    <SelectItem value="historical_irr">
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-4 w-4" />
                        Historical IRR (Highest First)
                      </div>
                    </SelectItem>
                    <SelectItem value="historical_em">
                      <div className="flex items-center gap-2">
                        <Calculator className="h-4 w-4" />
                        Historical EM (Highest First)
                      </div>
                    </SelectItem>
                    
                    <div className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100 mt-2">
                      Deal Size
                    </div>
                    <SelectItem value="minimum_deal_size">
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        Min Deal Size (Largest First)
                      </div>
                    </SelectItem>
                    <SelectItem value="maximum_deal_size">
                      <div className="flex items-center gap-2">
                        <Banknote className="h-4 w-4" />
                        Max Deal Size (Largest First)
                      </div>
                    </SelectItem>
                    
                    <div className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100 mt-2">
                      Loan Terms
                    </div>
                    <SelectItem value="interest_rate">
                      <div className="flex items-center gap-2">
                        <Percent className="h-4 w-4" />
                        Interest Rate (Highest First)
                      </div>
                    </SelectItem>
                    <SelectItem value="min_loan_term">
                      <div className="flex items-center gap-2">
                        <Timer className="h-4 w-4" />
                        Min Loan Term (Longest First)
                      </div>
                    </SelectItem>
                    <SelectItem value="max_loan_term">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        Max Loan Term (Longest First)
                      </div>
                    </SelectItem>
                    
                    <div className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100 mt-2">
                      LTV/LTC Ratios
                    </div>
                    <SelectItem value="loan_to_value_max">
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-4 w-4" />
                        Max LTV (Highest First)
                      </div>
                    </SelectItem>
                    <SelectItem value="loan_to_value_min">
                      <div className="flex items-center gap-2">
                        <TrendingDown className="h-4 w-4" />
                        Min LTV (Lowest First)
                      </div>
                    </SelectItem>
                    <SelectItem value="loan_to_cost_max">
                      <div className="flex items-center gap-2">
                        <LineChart className="h-4 w-4" />
                        Max LTC (Highest First)
                      </div>
                    </SelectItem>
                    <SelectItem value="loan_to_cost_min">
                      <div className="flex items-center gap-2">
                        <BarChart3 className="h-4 w-4" />
                        Min LTC (Lowest First)
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                
                {/* Sort Order Toggle */}
                <Button
                  onClick={() => updateFilters({ 
                    sortOrder: localFilters.sortOrder === 'asc' ? 'desc' : 'asc' 
                  })}
                  className={`p-2 rounded-lg border transition-all ${
                    localFilters.sortOrder === 'asc' 
                      ? 'bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100' 
                      : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                  }`}
                  title={`Sort ${localFilters.sortOrder === 'asc' ? 'Ascending' : 'Descending'}`}
                >
                  {localFilters.sortOrder === 'asc' ? (
                    <ArrowUp className="h-4 w-4" />
                  ) : (
                    <ArrowDown className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {activeFilterCount > 0 && (
          <div className="border-t border-gray-100 p-4 bg-gray-50/50">
            <div className="flex flex-wrap gap-2">
              {localFilters.searchTerm && (
                <Badge className="bg-blue-100 text-blue-800 border border-blue-200 px-3 py-1 text-sm">
                  <Search className="h-3 w-3 mr-1" />
                  Search: {localFilters.searchTerm}
                  <button
                    onClick={() => updateFilters({ searchTerm: undefined })}
                    className="ml-2 hover:text-red-600 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {localFilters.capitalPosition?.map((type) => (
                <Badge key={type} className="bg-purple-100 text-purple-800 border border-purple-200 px-3 py-1 text-sm">
                  <Building2 className="h-3 w-3 mr-1" />
                  {type}
                  <button
                    onClick={() => updateFilters({ 
                      capitalPosition: localFilters.capitalPosition?.filter(t => t !== type) 
                    })}
                    className="ml-2 hover:text-red-600 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
              {(localFilters.dealSizeMin || localFilters.dealSizeMax) && (
                <Badge className="bg-emerald-100 text-emerald-800 border border-emerald-200 px-3 py-1 text-sm">
                  <DollarSign className="h-3 w-3 mr-1" />
                  Deal Size: {localFilters.dealSizeMin ? localFilters.dealSizeMin.toLocaleString() + 'K' : '0K'} - {localFilters.dealSizeMax ? localFilters.dealSizeMax.toLocaleString() + 'K' : '∞K'}
                  <button
                    onClick={() => updateFilters({ dealSizeMin: undefined, dealSizeMax: undefined })}
                    className="ml-2 hover:text-red-600 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {(localFilters.targetReturnMin || localFilters.targetReturnMax) && (
                <Badge className="bg-amber-100 text-amber-800 border border-amber-200 px-3 py-1 text-sm">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  Target Return: {localFilters.targetReturnMin ? localFilters.targetReturnMin.toFixed(2) + '%' : '0%'} - {localFilters.targetReturnMax ? localFilters.targetReturnMax.toFixed(2) + '%' : '∞%'}
                  <button
                    onClick={() => updateFilters({ targetReturnMin: undefined, targetReturnMax: undefined })}
                    className="ml-2 hover:text-red-600 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {(localFilters.loanToValueMin || localFilters.loanToValueMax) && (
                <Badge className="bg-indigo-100 text-indigo-800 border border-indigo-200 px-3 py-1 text-sm">
                  <Percent className="h-3 w-3 mr-1" />
                  LTV: {localFilters.loanToValueMin ? localFilters.loanToValueMin.toFixed(2) + '%' : '0%'} - {localFilters.loanToValueMax ? localFilters.loanToValueMax.toFixed(2) + '%' : '∞%'}
                  <button
                    onClick={() => updateFilters({ loanToValueMin: undefined, loanToValueMax: undefined })}
                    className="ml-2 hover:text-red-600 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {(localFilters.loanToCostMin || localFilters.loanToCostMax) && (
                <Badge className="bg-cyan-100 text-cyan-800 border border-cyan-200 px-3 py-1 text-sm">
                  <Percent className="h-3 w-3 mr-1" />
                  LTC: {localFilters.loanToCostMin ? localFilters.loanToCostMin.toFixed(2) + '%' : '0%'} - {localFilters.loanToCostMax ? localFilters.loanToCostMax.toFixed(2) + '%' : '∞%'}
                  <button
                    onClick={() => updateFilters({ loanToCostMin: undefined, loanToCostMax: undefined })}
                    className="ml-2 hover:text-red-600 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {localFilters.propertyTypes?.map((type) => (
                <Badge key={type} className="bg-orange-100 text-orange-800 border border-orange-200 px-3 py-1 text-sm">
                  <Building className="h-3 w-3 mr-1" />
                  {type}
                  <button
                    onClick={() => updateFilters({ 
                      propertyTypes: localFilters.propertyTypes?.filter(t => t !== type) 
                    })}
                    className="ml-2 hover:text-red-600 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
              {localFilters.regions?.map((region) => (
                <Badge key={region} className="bg-blue-100 text-blue-800 border border-blue-200 px-3 py-1 text-sm">
                  <Globe className="h-3 w-3 mr-1" />
                  {region}
                  <button
                    onClick={() => updateFilters({ 
                      regions: localFilters.regions?.filter(r => r !== region) 
                    })}
                    className="ml-2 hover:text-red-600 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
              {localFilters.states?.map((state) => (
                <Badge key={state} className="bg-teal-100 text-teal-800 border border-teal-200 px-3 py-1 text-sm">
                  <MapPin className="h-3 w-3 mr-1" />
                  {state}
                  <button
                    onClick={() => updateFilters({ 
                      states: localFilters.states?.filter(s => s !== state) 
                    })}
                    className="ml-2 hover:text-red-600 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
              {localFilters.isRequested && (
                <Badge className="bg-green-100 text-green-800 border border-green-200 px-3 py-1 text-sm">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Requested Only
                  <button
                    onClick={() => updateFilters({ isRequested: undefined })}
                    className="ml-2 hover:text-red-600 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Comprehensive Right Side Filter Panel */}
      <div className={`fixed top-0 right-0 h-full w-[500px] bg-white border-l border-gray-200 shadow-xl transform transition-transform duration-300 ease-in-out z-50 ${
        isFilterPanelOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Enhanced Panel Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="p-3 rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg">
                  <SlidersHorizontal className="h-6 w-6" />
                </div>
                {activeFilterCount > 0 && (
                  <div className="absolute -top-2 -right-2 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center font-bold">
                    {activeFilterCount > 9 ? '9+' : activeFilterCount}
                  </div>
                )}
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                  Investment Criteria Filters
                  <Badge className="bg-blue-100 text-blue-700 border border-blue-200 ml-2">
                    Advanced
                  </Badge>
                </h2>
                <p className="text-sm text-gray-600 mt-1 flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Comprehensive filtering across 40+ financial and structural criteria fields
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {activeFilterCount > 0 && (
                <div className="flex flex-col items-end gap-1">
                  <Badge className="bg-green-100 text-green-700 border border-green-200 text-xs">
                    {activeFilterCount} Active Filter{activeFilterCount !== 1 ? 's' : ''}
                  </Badge>
                  <Button
                    onClick={onClearFilters}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 border border-red-200 bg-white px-3 py-1 text-xs"
                  >
                    <RotateCcw className="h-3 w-3 mr-1" />
                    Clear All
                  </Button>
                </div>
              )}
              <Button
                onClick={() => setIsFilterPanelOpen(false)}
                className="text-gray-500 hover:text-gray-700 bg-white border border-gray-200 p-2 hover:bg-gray-50 transition-colors"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Panel Content - Scrollable */}
          <div className="flex-1 overflow-y-auto p-6 space-y-8">

            {/* Deal Size Section */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-green-50 to-emerald-50">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <DollarSign className="h-5 w-5 text-green-600" />
                  Deal Size Range
                </CardTitle>
                <p className="text-sm text-gray-600 mt-1">Enter values in thousands</p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">
                      Minimum 
                      {localFilters.dealSizeMin && (
                        <span className="ml-2 text-xs text-green-600 font-medium">
                          = {localFilters.dealSizeMin.toLocaleString()}K
                        </span>
                      )}
                    </Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        type="number"
                        placeholder="0 (e.g., 500 for $500K)"
                        value={localFilters.dealSizeMin || ''}
                        onChange={(e) => updateFilters({ 
                          dealSizeMin: e.target.value ? parseFloat(e.target.value) : undefined 
                        })}
                        disabled={isLoading}
                        className="pl-10 bg-white border-gray-200"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">
                      Maximum
                      {localFilters.dealSizeMax && (
                        <span className="ml-2 text-xs text-green-600 font-medium">
                          = {localFilters.dealSizeMax.toLocaleString()}K
                        </span>
                      )}
                    </Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        type="number"
                        placeholder="50000 (e.g., 50000 for $50M)"
                        value={localFilters.dealSizeMax || ''}
                        onChange={(e) => updateFilters({ 
                          dealSizeMax: e.target.value ? parseFloat(e.target.value) : undefined 
                        })}
                        disabled={isLoading}
                        className="pl-10 bg-white border-gray-200"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Capital & Financing Structure */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-purple-50 to-pink-50">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <TreePine className="h-5 w-5 text-purple-600" />
                  Capital & Financing Structure
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Capital Types */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    Capital Position
                  </Label>
                  <ReactMultiSelect
                    options={getParentValues('Capital Position').map((type: string) => ({ value: type, label: type }))}
                    selected={localFilters.capitalPosition || []}
                                          onChange={(selected: string[]) => {
                        updateFilters({ 
                          capitalPosition: selected,
                          loanTypes: localFilters.loanTypes?.filter(type => getFilteredChildValues('Capital Position', selected).includes(type)) || []
                        })
                      }}
                    placeholder="Select capital positions..."
                    disabled={isLoading}
                  />
                </div>

                {/* Loan Types - Always Visible */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Loan Types
                                          {(localFilters.capitalPosition?.length ?? 0) > 0 && (
                        <Badge className="bg-blue-100 text-blue-700 ml-2 border border-blue-200">
                          {getFilteredChildValues('Capital Position', localFilters.capitalPosition || []).length} available
                        </Badge>
                      )}
                  </Label>
                                      <ReactMultiSelect
                     options={getFilteredChildValues('Capital Position', localFilters.capitalPosition || []).map((type: string) => ({ value: type, label: type }))}
                     selected={localFilters.loanTypes?.filter(type => getFilteredChildValues('Capital Position', localFilters.capitalPosition || []).includes(type)) || []}
                     onChange={(selected: string[]) => updateFilters({ loanTypes: selected })}
                     placeholder="Select loan types..."
                     disabled={isLoading}
                   />
                </div>
              </CardContent>
            </Card>

            {/* Property & Asset Structure */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-indigo-50 to-purple-50">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <Building className="h-5 w-5 text-indigo-600" />
                  Property & Asset Structure
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Property Types */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-700">Property Types</Label>
                  <ReactMultiSelect
                    options={getParentValues('Property Type').map((type: string) => ({ value: type, label: type }))}
                    selected={localFilters.propertyTypes || []}
                    onChange={(selected: string[]) => {
                                              updateFilters({ 
                          propertyTypes: selected,
                          propertySubcategories: localFilters.propertySubcategories?.filter(type => getFilteredChildValues('Property Type', selected).includes(type)) || []
                        })
                    }}
                    placeholder="Select property types..."
                    disabled={isLoading}
                  />
                </div>

                {/* Property Subcategories - Always Visible */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    Property Subcategories
                                          {(localFilters.propertyTypes?.length ?? 0) > 0 && (
                        <Badge className="bg-purple-100 text-purple-700 ml-2 border border-purple-200">
                          {getFilteredChildValues('Property Type', localFilters.propertyTypes || []).length} available
                        </Badge>
                      )}
                  </Label>
                                      <ReactMultiSelect
                     options={getFilteredChildValues('Property Type', localFilters.propertyTypes || []).map((type: string) => ({ value: type, label: type }))}
                     selected={localFilters.propertySubcategories?.filter(type => getFilteredChildValues('Property Type', localFilters.propertyTypes || []).includes(type)) || []}
                     onChange={(selected: string[]) => updateFilters({ propertySubcategories: selected })}
                     placeholder="Select property subcategories..."
                     disabled={isLoading}
                   />
                </div>

                {/* Strategies */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    Investment Strategies
                  </Label>
                  <ReactMultiSelect
                    options={getParentValues('Strategies').map((strategy: string) => ({ value: strategy, label: strategy }))}
                    selected={localFilters.strategies || []}
                    onChange={(selected: string[]) => updateFilters({ strategies: selected })}
                    placeholder="Select strategies..."
                    disabled={isLoading}
                  />
                </div>
              </CardContent>
            </Card>

            {/* LTV/LTC Ratios & Loan Terms */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-orange-50 to-red-50">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <Percent className="h-5 w-5 text-orange-600" />
                  Loan Ratios & Terms
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* LTV Ranges - Database Min/Max Fields */}
                <div className="space-y-4">
                  <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <Percent className="h-4 w-4" />
                    Loan-to-Value (LTV) Ranges (%)
                  </Label>
                  
                  <div className="grid grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Minimum LTV</Label>
                      <div className="relative">
                        <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          type="number"
                          placeholder="Min LTV % (from db)"
                          value={localFilters.loanToValueMin || ''}
                          onChange={(e) => updateFilters({ 
                            loanToValueMin: e.target.value ? parseFloat(e.target.value) : undefined 
                          })}
                          disabled={isLoading}
                          className="pl-10 bg-white border-gray-200"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Maximum LTV</Label>
                      <div className="relative">
                        <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          type="number"
                          placeholder="Max LTV % (from db)"
                          value={localFilters.loanToValueMax || ''}
                          onChange={(e) => updateFilters({ 
                            loanToValueMax: e.target.value ? parseFloat(e.target.value) : undefined 
                          })}
                          disabled={isLoading}
                          className="pl-10 bg-white border-gray-200"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* LTC Ranges - Database Min/Max Fields */}
                <div className="space-y-4">
                  <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <Percent className="h-4 w-4" />
                    Loan-to-Cost (LTC) Ranges (%)
                  </Label>
                  
                  <div className="grid grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Minimum LTC</Label>
                      <div className="relative">
                        <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          type="number"
                          placeholder="Min LTC % (from db)"
                          value={localFilters.loanToCostMin || ''}
                          onChange={(e) => updateFilters({ 
                            loanToCostMin: e.target.value ? parseFloat(e.target.value) : undefined 
                          })}
                          disabled={isLoading}
                          className="pl-10 bg-white border-gray-200"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Maximum LTC</Label>
                      <div className="relative">
                        <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          type="number"
                          placeholder="Max LTC % (from db)"
                          value={localFilters.loanToCostMax || ''}
                          onChange={(e) => updateFilters({ 
                            loanToCostMax: e.target.value ? parseFloat(e.target.value) : undefined 
                          })}
                          disabled={isLoading}
                          className="pl-10 bg-white border-gray-200"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Interest Rate Range */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <Percent className="h-4 w-4" />
                    Interest Rate Range (%)
                  </Label>
                  <div className="grid grid-cols-2 gap-4">
                    <Input
                      type="number"
                      placeholder="Min Rate %"
                      value={localFilters.interestRateMin || ''}
                      onChange={(e) => updateFilters({ 
                        interestRateMin: e.target.value ? parseFloat(e.target.value) : undefined 
                      })}
                      disabled={isLoading}
                      className="bg-white border-gray-200"
                    />
                    <Input
                      type="number"
                      placeholder="Max Rate %"
                      value={localFilters.interestRateMax || ''}
                      onChange={(e) => updateFilters({ 
                        interestRateMax: e.target.value ? parseFloat(e.target.value) : undefined 
                      })}
                      disabled={isLoading}
                      className="bg-white border-gray-200"
                    />
                  </div>
                </div>

                {/* Loan Term Range */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Loan Term Range (Months)
                  </Label>
                  <div className="grid grid-cols-2 gap-4">
                    <Input
                      type="number"
                      placeholder="Min Term"
                      value={localFilters.minLoanTerm || ''}
                      onChange={(e) => updateFilters({ 
                        minLoanTerm: e.target.value ? parseInt(e.target.value) : undefined 
                      })}
                      disabled={isLoading}
                      className="bg-white border-gray-200"
                    />
                    <Input
                      type="number"
                      placeholder="Max Term"
                      value={localFilters.maxLoanTerm || ''}
                      onChange={(e) => updateFilters({ 
                        maxLoanTerm: e.target.value ? parseInt(e.target.value) : undefined 
                      })}
                      disabled={isLoading}
                      className="bg-white border-gray-200"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Hold Periods & Timeline */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-pink-50 to-rose-50">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <Calendar className="h-5 w-5 text-pink-600" />
                  Hold Periods & Timeline
                </CardTitle>
                <p className="text-sm text-gray-600 mt-1">Filter by investment hold period requirements</p>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Hold Period Filters - Corrected Logic */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Minimum Hold Period
                      <Badge className="bg-blue-100 text-blue-700 ml-2 border border-blue-200 text-xs">
                        Months
                      </Badge>
                    </Label>
                    <Input
                      type="number"
                      placeholder="Min hold period (e.g., 36)"
                      value={localFilters.minHoldPeriod || ''}
                      onChange={(e) => updateFilters({ 
                        minHoldPeriod: e.target.value ? parseInt(e.target.value) : undefined 
                      })}
                      disabled={isLoading}
                      className="bg-white border-gray-200"
                    />
                    <p className="text-xs text-gray-500">Find criteria with minimum hold period ≥ this value</p>
                  </div>
                  
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      <Timer className="h-4 w-4" />
                      Maximum Hold Period
                      <Badge className="bg-blue-100 text-blue-700 ml-2 border border-blue-200 text-xs">
                        Months
                      </Badge>
                    </Label>
                    <Input
                      type="number"
                      placeholder="Max hold period (e.g., 120)"
                      value={localFilters.maxHoldPeriod || ''}
                      onChange={(e) => updateFilters({ 
                        maxHoldPeriod: e.target.value ? parseInt(e.target.value) : undefined 
                      })}
                      disabled={isLoading}
                      className="bg-white border-gray-200"
                    />
                    <p className="text-xs text-gray-500">Find criteria with maximum hold period ≤ this value</p>
                  </div>
                </div>

                {/* Closing Time - Corrected Logic */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Maximum Closing Time
                    <Badge className="bg-orange-100 text-orange-700 ml-2 border border-orange-200 text-xs">
                      Weeks
                    </Badge>
                  </Label>
                  <Input
                    type="number"
                    placeholder="Max closing time (e.g., 8 weeks)"
                    value={localFilters.closingTimeWeeks || ''}
                    onChange={(e) => updateFilters({ 
                      closingTimeWeeks: e.target.value ? parseInt(e.target.value) : undefined 
                    })}
                    disabled={isLoading}
                    className="bg-white border-gray-200"
                  />
                  <p className="text-xs text-gray-500">Find criteria with closing time ≤ this value</p>
                </div>
              </CardContent>
            </Card>

            {/* DSCR & Fees */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-yellow-50 to-amber-50">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <Calculator className="h-5 w-5 text-yellow-600" />
                  DSCR & Fees
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* DSCR Ranges */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      <BarChart3 className="h-4 w-4" />
                      Min DSCR Range
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Min"
                        value={localFilters.minLoanDscr || ''}
                        onChange={(e) => updateFilters({ 
                          minLoanDscr: e.target.value ? parseFloat(e.target.value) : undefined 
                        })}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Max"
                        value={localFilters.maxLoanDscr || ''}
                        onChange={(e) => updateFilters({ 
                          maxLoanDscr: e.target.value ? parseFloat(e.target.value) : undefined 
                        })}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      <LineChart className="h-4 w-4" />
                      Max DSCR Range
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Min"
                        value={localFilters.maxLoanDscr || ''}
                        onChange={(e) => updateFilters({ 
                          maxLoanDscr: e.target.value ? parseFloat(e.target.value) : undefined 
                        })}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Max"
                        value={localFilters.maxLoanDscr || ''}
                        onChange={(e) => updateFilters({ 
                          maxLoanDscr: e.target.value ? parseFloat(e.target.value) : undefined 
                        })}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                    </div>
                  </div>
                </div>

                {/* Fee Ranges */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      Origination Fee Range (%)
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Min %"
                        value={localFilters.loanOriginationFeeMin || ''}
                        onChange={(e) => updateFilters({ 
                          loanOriginationFeeMin: e.target.value ? parseFloat(e.target.value) : undefined 
                        })}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Max %"
                        value={localFilters.loanOriginationFeeMax || ''}
                        onChange={(e) => updateFilters({ 
                          loanOriginationFeeMax: e.target.value ? parseFloat(e.target.value) : undefined 
                        })}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      <Banknote className="h-4 w-4" />
                      Exit Fee Range (%)
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Min %"
                        value={localFilters.loanExitFeeMin || ''}
                        onChange={(e) => updateFilters({ 
                          loanExitFeeMin: e.target.value ? parseFloat(e.target.value) : undefined 
                        })}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Max %"
                        value={localFilters.loanExitFeeMax || ''}
                        onChange={(e) => updateFilters({ 
                          loanExitFeeMax: e.target.value ? parseFloat(e.target.value) : undefined 
                        })}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Geographic Focus */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-emerald-50 to-green-50">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <Globe className="h-5 w-5 text-emerald-600" />
                  Geographic Focus
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 gap-6">
                  {/* Regions */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      U.S. Regions
                    </Label>
                    <ReactMultiSelect
                      options={getParentValues('U.S Regions').map((region: string) => ({ value: region, label: region }))}
                      selected={localFilters.regions || []}
                      onChange={(selected: string[]) => updateFilters({ 
                        regions: selected,
                        states: localFilters.states?.filter(state => getFilteredChildValues('U.S Regions', selected).includes(state)) || []
                      })}
                      placeholder="Select U.S. regions..."
                      disabled={isLoading}
                    />
                  </div>

                  {/* States - Always Visible */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      States
                                              {(localFilters.regions?.length ?? 0) > 0 && (
                          <Badge className="bg-green-100 text-green-700 ml-2 border border-green-200">
                            {getFilteredChildValues('U.S Regions', localFilters.regions || []).length} available in selected regions
                          </Badge>
                        )}
                    </Label>
                    <ReactMultiSelect
                                           options={getFilteredChildValues('U.S Regions', localFilters.regions || []).map(state => ({ value: state, label: state }))}
                     selected={localFilters.states?.filter(state => getFilteredChildValues('U.S Regions', localFilters.regions || []).includes(state)) || []}
                      onChange={(selected: string[]) => updateFilters({ states: selected })}
                      placeholder="Select states..."
                      disabled={isLoading}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Status & Meta Filters */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-gray-50 to-slate-50">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <Settings className="h-5 w-5 text-gray-600" />
                  Status & Meta Filters
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Status Filters */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      <Activity className="h-4 w-4" />
                      Active Status
                    </Label>
                    <Select
                      value={localFilters.isActive !== undefined ? localFilters.isActive.toString() : 'all'}
                      onValueChange={(value) => updateFilters({ 
                        isActive: value === 'all' ? undefined : value === 'true' 
                      })}
                      disabled={isLoading}
                    >
                      <SelectTrigger className="bg-white border-gray-200">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="true">Active Only</SelectItem>
                        <SelectItem value="false">Inactive Only</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4" />
                      Requested Status
                    </Label>
                    <Select
                      value={localFilters.isRequested !== undefined ? localFilters.isRequested.toString() : 'all'}
                      onValueChange={(value) => updateFilters({ 
                        isRequested: value === 'all' ? undefined : value === 'true' 
                      })}
                      disabled={isLoading}
                    >
                      <SelectTrigger className="bg-white border-gray-200">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Criteria</SelectItem>
                        <SelectItem value="true">Requested Only</SelectItem>
                        <SelectItem value="false">Not Requested</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Panel Footer */}
          <div className="border-t border-gray-200 p-6 bg-gradient-to-r from-gray-50 to-blue-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="text-sm text-gray-600">
                  {activeFilterCount > 0 ? (
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1">
                        <Sparkles className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-gray-700">
                          {activeFilterCount} Filter{activeFilterCount !== 1 ? 's' : ''} Active
                        </span>
                      </div>
                      <div className="h-4 w-px bg-gray-300"></div>
                      <Badge className="bg-green-100 text-green-700 border border-green-200">
                        Ready to Apply
                      </Badge>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Filter className="h-4 w-4 text-gray-400" />
                      <span>No filters applied - showing all investment criteria</span>
                    </div>
                  )}
                </div>
                
                {/* Filter Summary */}
                {activeFilterCount > 0 && (
                  <div className="text-xs text-gray-500">
                    Filtering across {activeFilterCount} criteria field{activeFilterCount !== 1 ? 's' : ''}
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-3">
                {/* Quick Reset */}
                {activeFilterCount > 0 && (
                  <Button
                    onClick={onClearFilters}
                    className="text-gray-600 hover:text-red-600 bg-white border border-gray-200 hover:border-red-200 hover:bg-red-50 px-4 py-2 text-sm transition-colors"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reset All
                  </Button>
                )}
                
                {/* Apply Button */}
                <Button
                  onClick={() => setIsFilterPanelOpen(false)}
                  className={`px-6 py-2 font-medium shadow-md hover:shadow-lg transition-all duration-200 ${
                    activeFilterCount > 0 
                      ? 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white' 
                      : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white'
                  }`}
                >
                  {activeFilterCount > 0 ? (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Apply {activeFilterCount} Filter{activeFilterCount !== 1 ? 's' : ''}
                    </>
                  ) : (
                    <>
                      <ChevronRight className="h-4 w-4 mr-2" />
                      Close Panel
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
} 