'use client'

import React from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination'
import { MoreHorizontal, Eye, Edit, Trash2, Building, User, MapPin, DollarSign, Calendar, Home, CheckCircle } from 'lucide-react'
import { InvestmentCriteria, InvestmentCriteriaResponse } from '@/types/investment-criteria'

interface InvestmentCriteriaTableProps {
  data?: InvestmentCriteriaResponse
  isLoading?: boolean
  onPageChange?: (page: number, limit?: number) => void
  onEdit?: (criteria: InvestmentCriteria) => void
  onDelete?: (criteriaId: number) => void
}

export default function InvestmentCriteriaTable({
  data,
  isLoading = false,
  onPageChange,
  onEdit,
  onDelete
}: InvestmentCriteriaTableProps) {
  
  // Format percentage display
  const formatPercentage = (value?: number | string) => {
    if (!value || value === '' || value === null || value === undefined) return '-'
    // Convert to number if string
    const numValue = typeof value === 'string' ? parseFloat(value) : value
    if (isNaN(numValue)) return '-'
    // Convert decimal to percentage for display
    const percentValue = numValue < 1 ? numValue * 100 : numValue
    return `${percentValue.toFixed(1)}%`
  }

  // Format currency display
  const formatCurrency = (value?: number) => {
    if (!value) return '-'
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`
    }
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}K`
    }
    return `$${value.toLocaleString()}`
  }

  // Format decimal display
  const formatDecimal = (value?: number) => {
    if (!value) return '-'
    return value.toFixed(1)
  }

  // Format closing time display
  const formatClosingTime = (weeks?: number, days?: number) => {
    if (weeks && days) {
      return `${formatDecimal(weeks)} weeks (${formatDecimal(days)} days)`
    } else if (weeks) {
      return `${formatDecimal(weeks)} weeks`
    } else if (days) {
      return `${formatDecimal(days)} days`
    }
    return '-'
  }

  // Format range display with proper units
  const formatRange = (min: number | undefined, max: number | undefined, formatter: (val: number) => string) => {
    if (min && max) {
      return `${formatter(min)} - ${formatter(max)}`
    } else if (min) {
      return `${formatter(min)}+`
    } else if (max) {
      return `up to ${formatter(max)}`
    }
    return '-'
  }

  // Format date display
  const formatDate = (dateStr?: string) => {
    if (!dateStr) return '-'
    return new Date(dateStr).toLocaleDateString()
  }

  // Format array display
  const formatArray = (arr?: string[], maxItems = 2) => {
    if (!arr || arr.length === 0) return '-'
    const displayItems = arr.slice(0, maxItems)
    const remainingCount = arr.length - maxItems
    
    return (
      <div className="flex flex-wrap gap-1">
        {displayItems.map((item, index) => (
          <Badge key={index} variant="outline" className="text-xs">
            {item}
          </Badge>
        ))}
        {remainingCount > 0 && (
          <Badge variant="secondary" className="text-xs">
            +{remainingCount} more
          </Badge>
        )}
      </div>
    )
  }

  // Get entity icon
  const getEntityIcon = (entityType: string) => {
    if (entityType.startsWith('Company')) {
      return <Building className="h-4 w-4 text-blue-600" />
    } else if (entityType === 'Contact') {
      return <User className="h-4 w-4 text-green-600" />
    } else if (entityType === 'Deal') {
      return <Home className="h-4 w-4 text-purple-600" />
    } else {
      return <Building className="h-4 w-4 text-gray-600" />
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Investment Criteria</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[250px]" />
                  <Skeleton className="h-4 w-[200px]" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!data || data.data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Investment Criteria</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-10">
            <div className="mx-auto h-24 w-24 text-gray-400">
              <DollarSign className="h-full w-full" />
            </div>
            <h3 className="mt-4 text-lg font-semibold">No investment criteria found</h3>
            <p className="mt-2 text-gray-600">
              Try adjusting your filters or check back later.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Investment Criteria</CardTitle>
          <div className="text-sm text-muted-foreground">
            Showing {data.data.length} of {data.pagination.total} results
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Entity</TableHead>
                <TableHead>Deal Size Range</TableHead>
                <TableHead>Capital Types</TableHead>
                <TableHead>Property Types</TableHead>
                <TableHead>Geographic Focus</TableHead>
                <TableHead>LTV/LTC</TableHead>
                <TableHead>Updated</TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.data.map((criteria) => (
                <TableRow key={criteria.criteria_id} className="hover:bg-muted/50">
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      {getEntityIcon(criteria.entity_type)}
                      <div>
                        <div className="font-medium flex items-center gap-2">
                          {criteria.entity_name || 'Unknown'}
                          {criteria.is_requested && (
                            <Badge variant="secondary" className="text-xs bg-green-100 text-green-800 border-green-200">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Requested
                            </Badge>
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground flex items-center">
                          <MapPin className="h-3 w-3 mr-1" />
                          {criteria.entity_location || 'Location unknown'}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="text-sm">
                      {criteria.minimum_deal_size || criteria.maximum_deal_size ? (
                        <div>
                          <div className="font-medium">
                            {formatCurrency(criteria.minimum_deal_size)} - {formatCurrency(criteria.maximum_deal_size)}
                          </div>
                          {criteria.target_return && (
                            <div className="text-xs text-muted-foreground">
                              Target: {formatPercentage(criteria.target_return)}
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-muted-foreground">Not specified</span>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    {formatArray(criteria.financial_products)}
                  </TableCell>
                  
                  <TableCell>
                    {formatArray(criteria.property_types)}
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-1">
                      {criteria.country && criteria.country.length > 0 && (
                        <div className="text-xs">
                          <span className="font-medium">Countries:</span> {formatArray(criteria.country, 1)}
                        </div>
                      )}
                      {criteria.state && criteria.state.length > 0 && (
                        <div className="text-xs">
                          <span className="font-medium">States:</span> {formatArray(criteria.state, 1)}
                        </div>
                      )}
                      {!criteria.country?.length && !criteria.state?.length && (
                        <span className="text-muted-foreground text-xs">Global</span>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-1">
                      {(criteria.loan_to_value_min || criteria.loan_to_value_max) && (
                        <div className="text-xs">
                          <span className="font-medium">LTV:</span> {formatRange(criteria.loan_to_value_min, criteria.loan_to_value_max, formatPercentage)}
                        </div>
                      )}
                      {(criteria.loan_to_cost_min || criteria.loan_to_cost_max) && (
                        <div className="text-xs">
                          <span className="font-medium">LTC:</span> {formatRange(criteria.loan_to_cost_min, criteria.loan_to_cost_max, formatPercentage)}
                        </div>
                      )}
                      {(criteria.min_closing_time_weeks || criteria.max_closing_time_weeks || criteria.min_closing_time_days || criteria.max_closing_time_days) && (
                        <div className="text-xs">
                          <span className="font-medium">Closing:</span> {formatRange(criteria.min_closing_time_weeks, criteria.max_closing_time_weeks, formatDecimal)} weeks
                        </div>
                      )}
                      {!criteria.loan_to_value_min && !criteria.loan_to_value_max && !criteria.loan_to_cost_min && !criteria.loan_to_cost_max && (
                        <span className="text-muted-foreground text-xs">Not specified</span>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3 mr-1" />
                      {formatDate(criteria.updated_at)}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem asChild>
                          <Link 
                            href={`/dashboard/investment-criteria/${criteria.criteria_id}`}
                            className="flex items-center"
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        {onEdit && (
                          <DropdownMenuItem onClick={() => onEdit(criteria)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                        )}
                        {onDelete && (
                          <DropdownMenuItem 
                            onClick={() => onDelete(criteria.criteria_id)}
                            className="text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination - Always show when there's data */}
        {data.data.length > 0 && (
          <div className="mt-6 flex items-center justify-between">
            {/* Results info and page size selector */}
            <div className="flex items-center gap-4">
              <div className="text-sm text-muted-foreground">
                Showing {((data.pagination.page - 1) * data.pagination.limit) + 1} to{' '}
                {Math.min(data.pagination.page * data.pagination.limit, data.pagination.total)} of{' '}
                {data.pagination.total} results
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Show</span>
                <select
                  value={data.pagination.limit}
                  onChange={(e) => onPageChange?.(1, Number(e.target.value))}
                  className="px-2 py-1 text-sm border border-input rounded focus:outline-none focus:ring-2 focus:ring-ring"
                >
                  <option value={10}>10</option>
                  <option value={25}>25</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
                <span className="text-sm text-muted-foreground">per page</span>
              </div>
            </div>

            {/* Pagination controls */}
            {data.pagination.totalPages > 1 && (
              <Pagination>
                <PaginationContent>
                  {data.pagination.hasPrev && (
                    <PaginationItem>
                      <PaginationPrevious 
                        size="sm"
                        onClick={() => onPageChange?.(data.pagination.page - 1)}
                        className="cursor-pointer"
                      />
                    </PaginationItem>
                  )}
                  
                  {/* Show page numbers */}
                  {[...Array(Math.min(5, data.pagination.totalPages))].map((_, i) => {
                    const pageNum = Math.max(1, data.pagination.page - 2) + i
                    if (pageNum > data.pagination.totalPages) return null
                    
                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          size="sm"
                          onClick={() => onPageChange?.(pageNum)}
                          isActive={pageNum === data.pagination.page}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    )
                  })}
                  
                  {data.pagination.hasNext && (
                    <PaginationItem>
                      <PaginationNext 
                        size="sm"
                        onClick={() => onPageChange?.(data.pagination.page + 1)}
                        className="cursor-pointer"
                      />
                    </PaginationItem>
                  )}
                </PaginationContent>
              </Pagination>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
} 