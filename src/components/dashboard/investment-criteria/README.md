# Investment Criteria Components

This directory contains components for displaying and managing investment criteria for contacts and companies.

## Components Overview

### InvestmentCriteriaSlider.tsx (Original)
- **Purpose**: Original slider-based investment criteria display
- **Features**: 
  - Slider navigation between multiple criteria records
  - Basic filtering and validation
  - Legacy compatibility
  - Simple safety check for toString() error
- **Usage**: Used in original investment criteria tabs
- **Status**: Fixed - Minimal safety check to prevent toString() error

### InvestmentCriteriaSliderV2.tsx (New)
- **Purpose**: Enhanced V2 version using InvestmentCriteriaSectionV2
- **Features**:
  - Modern grouped display of investment criteria
  - Enhanced filtering and visualization
  - Better error handling and loading states
  - Collapsible sections by capital position
  - Detailed breakdown of debt and equity criteria
- **Usage**: Used in new "Investment Criteria V2" tabs

### InvestmentCriteriaDetailView.tsx
- **Purpose**: Detailed view of a single investment criteria record
- **Features**:
  - Full criteria details
  - Edit functionality
  - Form validation

## Integration

### Contact Detail
- **Original Tab**: "Investment Criteria" - Uses InvestmentCriteriaSlider
- **New Tab**: "Investment Criteria V2" - Uses InvestmentCriteriaSliderV2

### Company Detail
- **Original Tab**: "Investment Criteria" - Uses InvestmentCriteriaSlider  
- **New Tab**: "Investment Criteria V2" - Uses InvestmentCriteriaSliderV2

## Key Differences

| Feature | Original (V1) | V2 |
|---------|---------------|----|
| Display Style | Slider navigation | Grouped by capital position |
| Data Source | Direct API calls | Centralized API endpoint |
| Error Handling | Basic | Enhanced with retry options |
| Loading States | Simple spinner | Detailed loading states |
| Filtering | None | Built-in filtering |
| Responsive Design | Basic | Enhanced mobile support |
| Accessibility | Basic | Improved ARIA support |

## API Endpoints

### V1 (Original)
- `/api/investment-criteria/by-contact/{id}`
- `/api/investment-criteria/by-company/{id}`

### V2 (New)
- `/api/investment-criteria/entity/{entityType}/{entityId}`

## Migration Guide

To migrate from V1 to V2:

1. Replace `InvestmentCriteriaSlider` with `InvestmentCriteriaSliderV2`
2. Update entity type mapping (contact → Contact, company → Company)
3. Update API endpoint calls if needed
4. Test error handling and loading states

## Future Enhancements

- Add export functionality
- Implement advanced filtering
- Add comparison tools
- Integrate with deal matching
- Add bulk operations
