'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { 
  Building2, 
  MapPin, 
  DollarSign, 
  TrendingUp, 
  Calculator,
  Percent,
  Clock,
  FileText,
  Loader2,
  Edit,
  Trash2,
  Target,
  Plus,
  Save,
  X,
  AlertCircle
} from 'lucide-react'
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import InvestmentCriteriaForm from './InvestmentCriteriaForm'

interface InvestmentCriteriaData {
  investment_criteria_id: number;
  entity_type: string;
  entity_id: number;
  entity_name?: string;
  entity_location?: string;
  capital_position: string;
  minimum_deal_size?: number;
  maximum_deal_size?: number;
  country?: string[];
  region?: string[];
  state?: string[];
  city?: string[];
  property_types?: string[];
  property_subcategories?: string[];
  strategies?: string[];
  decision_making_process?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  
  // Relationship IDs to debt/equity records
  investment_criteria_debt_id?: number;
  investment_criteria_equity_id?: number;
  
  // Debt-specific fields
  loan_type?: string;
  loan_program?: string;
  min_loan_term?: number;
  max_loan_term?: number;
  loan_interest_rate?: number;
  loan_interest_rate_based_off_sofr?: number;
  loan_interest_rate_based_off_wsj?: number;
  loan_interest_rate_based_off_prime?: number;
  loan_interest_rate_based_off_3yt?: number;
  loan_interest_rate_based_off_5yt?: number;
  loan_interest_rate_based_off_10yt?: number;
  loan_interest_rate_based_off_30yt?: number;
  loan_to_value_min?: number;
  loan_to_value_max?: number;
  loan_to_cost_min?: number;
  loan_to_cost_max?: number;
  loan_origination_min_fee?: number;
  loan_origination_max_fee?: number;
  loan_exit_min_fee?: number;
  loan_exit_max_fee?: number;
  min_loan_dscr?: number;
  max_loan_dscr?: number;
  structured_loan_tranche?: string;
  recourse_loan?: string;
  closing_time?: number;
  debt_program_overview?: string;
  lien_position?: string;
  loan_min_debt_yield?: string;
  prepayment?: string;
  yield_maintenance?: string;
  amortization?: string;
  application_deposit?: number;
  good_faith_deposit?: number;
  future_facilities?: string;
  eligible_borrower?: string;
  occupancy_requirements?: string;
  rate_lock?: string;
  rate_type?: string;
  loan_type_normalized?: string;
  debt_notes?: string;
  
  // Equity-specific fields
  target_return?: number;
  minimum_internal_rate_of_return?: number;
  min_hold_period_years?: number;
  max_hold_period_years?: number;
  minimum_yield_on_cost?: number;
  minimum_equity_multiple?: number;
  ownership_requirement?: string;
  equity_program_overview?: string;
  target_cash_on_cash_min?: number;
  attachment_point?: number;
  max_leverage_tolerance?: number;
  typical_closing_timeline_days?: number;
  proof_of_funds_requirement?: boolean;
  equity_occupancy_requirements?: string;
  equity_notes?: string;
  yield_on_cost?: number;
  target_return_irr_on_equity?: number;
  equity_multiple?: number;
  position_specific_irr?: number;
  position_specific_equity_multiple?: number;
}

interface GroupedCriteria {
  [capitalPosition: string]: InvestmentCriteriaData[];
}

interface InvestmentCriteriaDetailViewV2Props {
  entityType: 'company' | 'contact' | 'deal';
  entityId: string | number;
  onBack?: () => void;
  className?: string;
  isAddingNew?: boolean;
  onSave?: (formData: any) => Promise<void>;
  initialCriteria?: InvestmentCriteriaData;
  onDataUpdate?: () => void; // Callback to notify parent of data updates
  onDelete?: (criteriaId: number) => Promise<void>; // Callback for deletion
}

export default function InvestmentCriteriaDetailViewV2({ 
  entityType,
  entityId,
  onBack,
  className = '',
  isAddingNew = false,
  onSave,
  initialCriteria,
  onDataUpdate,
  onDelete
}: InvestmentCriteriaDetailViewV2Props) {
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [groupedCriteria, setGroupedCriteria] = useState<GroupedCriteria>({})
  const [totalCriteria, setTotalCriteria] = useState(0)
  const [isEditing, setIsEditing] = useState(false)
  const [editingCriteria, setEditingCriteria] = useState<InvestmentCriteriaData | null>(null)
  const [isSaving, setIsSaving] = useState(false)

  const fetchInvestmentCriteria = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Use the correct API endpoint based on entity type
      const endpoint = entityType === 'contact' 
        ? `/api/investment-criteria/by-contact/${entityId}`
        : `/api/investment-criteria/by-company/${entityId}`
      
      const response = await fetch(endpoint)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch investment criteria: ${response.statusText}`)
      }
      
      const criteria = await response.json()
      
      // Group criteria by capital position
      const grouped = criteria.reduce((acc: any, item: any) => {
        const position = item.capital_position || 'Unknown'
        if (!acc[position]) {
          acc[position] = []
        }
        acc[position].push(item)
        return acc
      }, {})
      
      setGroupedCriteria(grouped)
      setTotalCriteria(criteria.length)
    } catch (err) {
      console.error('Error fetching investment criteria:', err)
      setError(err instanceof Error ? err.message : 'Failed to load investment criteria')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (initialCriteria) {
      // Use the provided initial criteria
      const grouped = {
        [initialCriteria.capital_position || 'Unknown']: [initialCriteria]
      }
      setGroupedCriteria(grouped)
      setTotalCriteria(1)
      setLoading(false)
      
      // If we're currently editing, update the editing criteria with the new data
      if (isEditing && editingCriteria) {
        setEditingCriteria(initialCriteria)
      }
    } else {
      // Fetch from API as before
      fetchInvestmentCriteria()
    }
  }, [entityType, entityId, initialCriteria])

  const handleEdit = (criteria: InvestmentCriteriaData) => {
    setIsEditing(true)
    // Use the most up-to-date data - prefer initialCriteria if available
    const dataToEdit = initialCriteria || criteria
    setEditingCriteria({ ...dataToEdit })
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    setEditingCriteria(null)
  }

  const handleSaveEdit = async (formData: any) => {
    setIsSaving(true)
    try {
      const response = await fetch(`/api/investment-criteria-entity/${entityType}/${entityId}`, {
        method: editingCriteria?.investment_criteria_id ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          criteria_id: editingCriteria?.investment_criteria_id,
          updates: formData
        }),
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `Failed to ${editingCriteria?.investment_criteria_id ? 'update' : 'create'} investment criteria: ${response.statusText}`)
      }
      
      toast({
        title: "Success",
        description: `Investment criteria ${editingCriteria?.investment_criteria_id ? 'updated' : 'created'} successfully`,
      })
      
      // If we have initialCriteria, we need to notify the parent to refresh data
      if (initialCriteria) {
        onDataUpdate?.()
        // Exit edit mode immediately after notifying parent
        setIsEditing(false)
        setEditingCriteria(null)
      } else {
        await fetchInvestmentCriteria()
        setIsEditing(false)
        setEditingCriteria(null)
      }
    } catch (error) {
      console.error('Error saving investment criteria:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to save investment criteria',
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleDelete = async (criteriaId: number) => {
    setIsSaving(true)
    try {
      // If we have an onDelete prop, use it (for parent component handling)
      if (onDelete) {
        await onDelete(criteriaId)
        return
      }
      
      // Otherwise, handle deletion internally with confirmation and toast
      if (!confirm('Are you sure you want to delete this investment criteria? This action cannot be undone and will permanently remove all associated data including debt and equity information.')) {
        return
      }
      
      const response = await fetch(`/api/investment-criteria-entity/${entityType}/${entityId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          criteria_id: criteriaId
        }),
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `Failed to delete investment criteria: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        toast({
          title: "Success",
          description: "Investment criteria deleted successfully",
        })
        
        // If we have initialCriteria, we need to notify the parent to refresh data
        if (initialCriteria) {
          onDataUpdate?.()
        } else {
          await fetchInvestmentCriteria()
        }
      } else {
        throw new Error(result.error || 'Failed to delete investment criteria')
      }
    } catch (error) {
      console.error('Error deleting investment criteria:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to delete investment criteria',
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleAddNew = () => {
    setIsEditing(true)
    setEditingCriteria(null) // null means creating new
  }

  // Format currency display
  const formatCurrency = (value?: number) => {
    if (!value) return 'Not specified'
    if (value >= 1000000000) {
      return `$${(value / 1000000000).toFixed(1)}B`
    }
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`
    }
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}K`
    }
    return `$${value.toLocaleString()}`
  }

  // Format percentage display
  const formatPercentage = (value?: number) => {
    if (!value) return 'Not specified'
    return `${value}%`
  }

  // Format array display
  const formatArray = (arr?: string[]) => {
    if (!arr || arr.length === 0) return <span className="text-slate-400">Not specified</span>
    return (
      <div className="flex flex-wrap gap-2">
        {arr.map((item, index) => (
          <Badge key={index} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            {item}
          </Badge>
        ))}
      </div>
    )
  }

  const getCapitalPositionColor = (position: string) => {
    switch (position.toLowerCase()) {
      case 'debt':
        return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'equity':
        return 'bg-green-50 text-green-700 border-green-200'
      case 'mezzanine':
        return 'bg-purple-50 text-purple-700 border-purple-200'
      case 'preferred equity':
        return 'bg-emerald-50 text-emerald-700 border-emerald-200'
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  const renderCriteriaDetails = (criteria: InvestmentCriteriaData) => (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card className="shadow-sm border-0 bg-white/70">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Target className="h-5 w-5 text-purple-600" />
              <span>Basic Information</span>
            </div>
            {!isEditing && (
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" onClick={() => handleEdit(criteria)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => handleDelete(criteria.investment_criteria_id)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium">Capital Position</span>
              <Badge className={`${getCapitalPositionColor(criteria.capital_position)} mt-1`}>
                {criteria.capital_position}
              </Badge>
            </div>
            <div>
              <span className="text-sm font-medium">Deal Size Range</span>
              <p className="text-sm mt-1">
                {criteria.minimum_deal_size && criteria.maximum_deal_size
                  ? `${formatCurrency(criteria.minimum_deal_size)} - ${formatCurrency(criteria.maximum_deal_size)}`
                  : criteria.minimum_deal_size
                  ? `${formatCurrency(criteria.minimum_deal_size)}+`
                  : criteria.maximum_deal_size
                  ? `Up to ${formatCurrency(criteria.maximum_deal_size)}`
                  : 'Not specified'}
              </p>
            </div>
          </div>
          
          {criteria.decision_making_process && (
            <div>
              <span className="text-sm font-medium">Decision Making Process</span>
              <p className="text-sm mt-1 bg-gray-50 p-3 rounded">{criteria.decision_making_process}</p>
            </div>
          )}
          
          {criteria.notes && (
            <div>
              <span className="text-sm font-medium">Notes</span>
              <p className="text-sm mt-1 bg-gray-50 p-3 rounded">{criteria.notes}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Geographic Focus */}
      {(criteria.country || criteria.region || criteria.state || criteria.city) && (
        <Card className="shadow-sm border-0 bg-white/70">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3">
              <MapPin className="h-5 w-5 text-red-600" />
              <span>Geographic Focus</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {criteria.country && (
                <div>
                  <span className="text-sm font-medium">Countries</span>
                  <div className="mt-2">{formatArray(criteria.country)}</div>
                </div>
              )}
              {criteria.region && (
                <div>
                  <span className="text-sm font-medium">Regions</span>
                  <div className="mt-2">{formatArray(criteria.region)}</div>
                </div>
              )}
              {criteria.state && (
                <div>
                  <span className="text-sm font-medium">States</span>
                  <div className="mt-2">{formatArray(criteria.state)}</div>
                </div>
              )}
              {criteria.city && (
                <div>
                  <span className="text-sm font-medium">Cities</span>
                  <div className="mt-2">{formatArray(criteria.city)}</div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

            {/* Property & Investment Focus */}
      {(criteria.property_types || criteria.property_subcategories || criteria.strategies) && (
        <Card className="shadow-sm border-0 bg-white/70">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3">
              <Building2 className="h-5 w-5 text-orange-600" />
              <span>Property & Investment Focus</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {criteria.property_types && (
                <div>
                  <span className="text-sm font-medium">Property Types</span>
                  <div className="mt-2">{formatArray(criteria.property_types)}</div>
                </div>
              )}
              {criteria.property_subcategories && (
                <div>
                  <span className="text-sm font-medium">Property Subcategories</span>
                  <div className="mt-2">{formatArray(criteria.property_subcategories)}</div>
                </div>
              )}
              {criteria.strategies && (
                <div>
                  <span className="text-sm font-medium">Investment Strategies</span>
                  <div className="mt-2">{formatArray(criteria.strategies)}</div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Debt-specific Details */}
      {criteria.investment_criteria_debt_id && (
        <Card className="shadow-sm border-0 bg-blue-50/70">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3">
              <Calculator className="h-5 w-5 text-blue-600" />
              <span className="text-blue-800">Debt Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Loan Terms */}
            {(criteria.min_loan_term || criteria.max_loan_term) && (
              <div>
                <span className="text-sm font-medium text-blue-700">Loan Terms</span>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  {criteria.min_loan_term && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Minimum Term</span>
                      <p className="font-medium">{criteria.min_loan_term} months</p>
                    </div>
                  )}
                  {criteria.max_loan_term && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Maximum Term</span>
                      <p className="font-medium">{criteria.max_loan_term} months</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Interest Rates */}
            {(criteria.loan_interest_rate || criteria.loan_interest_rate_based_off_sofr || criteria.loan_interest_rate_based_off_wsj || criteria.loan_interest_rate_based_off_prime) && (
              <div>
                <span className="text-sm font-medium text-blue-700">Interest Rates</span>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
                  {criteria.loan_interest_rate && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Base Rate</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_interest_rate)}</p>
                    </div>
                  )}
                  {criteria.loan_interest_rate_based_off_sofr && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">SOFR Based</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_interest_rate_based_off_sofr)}</p>
                    </div>
                  )}
                  {criteria.loan_interest_rate_based_off_wsj && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">WSJ Based</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_interest_rate_based_off_wsj)}</p>
                    </div>
                  )}
                  {criteria.loan_interest_rate_based_off_prime && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Prime Based</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_interest_rate_based_off_prime)}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Loan Ratios */}
            {(criteria.loan_to_value_min || criteria.loan_to_value_max || criteria.loan_to_cost_min || criteria.loan_to_cost_max) && (
              <div>
                <span className="text-sm font-medium text-blue-700">Loan Ratios</span>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  {criteria.loan_to_value_min && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Min LTV</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_to_value_min)}</p>
                    </div>
                  )}
                  {criteria.loan_to_value_max && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Max LTV</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_to_value_max)}</p>
                    </div>
                  )}
                  {criteria.loan_to_cost_min && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Min LTC</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_to_cost_min)}</p>
                    </div>
                  )}
                  {criteria.loan_to_cost_max && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Max LTC</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_to_cost_max)}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* DSCR */}
            {(criteria.min_loan_dscr || criteria.max_loan_dscr) && (
              <div>
                <span className="text-sm font-medium text-blue-700">Debt Service Coverage Ratio</span>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  {criteria.min_loan_dscr && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Minimum DSCR</span>
                      <p className="font-medium">{criteria.min_loan_dscr}x</p>
                    </div>
                  )}
                  {criteria.max_loan_dscr && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Maximum DSCR</span>
                      <p className="font-medium">{criteria.max_loan_dscr}x</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Fees */}
            {(criteria.loan_origination_min_fee || criteria.loan_origination_max_fee || criteria.loan_exit_min_fee || criteria.loan_exit_max_fee) && (
              <div>
                <span className="text-sm font-medium text-blue-700">Fees</span>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  {criteria.loan_origination_min_fee && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Min Origination Fee</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_origination_min_fee)}</p>
                    </div>
                  )}
                  {criteria.loan_origination_max_fee && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Max Origination Fee</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_origination_max_fee)}</p>
                    </div>
                  )}
                  {criteria.loan_exit_min_fee && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Min Exit Fee</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_exit_min_fee)}</p>
                    </div>
                  )}
                  {criteria.loan_exit_max_fee && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-blue-600">Max Exit Fee</span>
                      <p className="font-medium">{formatPercentage(criteria.loan_exit_max_fee)}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Additional Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {criteria.loan_type && (
                <div>
                  <span className="text-sm font-medium text-blue-700">Loan Type</span>
                  <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.loan_type}</p>
                </div>
              )}
              {criteria.loan_program && (
                <div>
                  <span className="text-sm font-medium text-blue-700">Loan Program</span>
                  <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.loan_program}</p>
                </div>
              )}
              {criteria.structured_loan_tranche && (
                <div>
                  <span className="text-sm font-medium text-blue-700">Structured Loan Tranche</span>
                  <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.structured_loan_tranche}</p>
                </div>
              )}
              {criteria.recourse_loan && (
                <div>
                  <span className="text-sm font-medium text-blue-700">Recourse Loan</span>
                  <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.recourse_loan}</p>
                </div>
              )}
              {criteria.closing_time && (
                <div>
                  <span className="text-sm font-medium text-blue-700">Closing Time</span>
                  <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.closing_time} weeks</p>
                </div>
              )}
              {criteria.lien_position && (
                <div>
                  <span className="text-sm font-medium text-blue-700">Lien Position</span>
                  <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.lien_position}</p>
                </div>
              )}
            </div>

            {/* Program Overview */}
            {criteria.debt_program_overview && (
              <div>
                <span className="text-sm font-medium text-blue-700">Program Overview</span>
                <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.debt_program_overview}</p>
              </div>
            )}

            {/* Notes */}
            {criteria.debt_notes && (
              <div>
                <span className="text-sm font-medium text-blue-700">Notes</span>
                <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.debt_notes}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Equity-specific Details */}
      {criteria.investment_criteria_equity_id && (
        <Card className="shadow-sm border-0 bg-green-50/70">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <span className="text-green-800">Equity Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Performance Targets */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {criteria.target_return && (
                <div className="bg-white p-4 rounded border">
                  <span className="text-xs text-green-600">Target Return</span>
                  <p className="font-medium text-lg">{formatPercentage(criteria.target_return)}</p>
                </div>
              )}
              {criteria.minimum_internal_rate_of_return && (
                <div className="bg-white p-4 rounded border">
                  <span className="text-xs text-green-600">Minimum IRR</span>
                  <p className="font-medium text-lg">{formatPercentage(criteria.minimum_internal_rate_of_return)}</p>
                </div>
              )}
              {criteria.minimum_yield_on_cost && (
                <div className="bg-white p-4 rounded border">
                  <span className="text-xs text-green-600">Min Yield on Cost</span>
                  <p className="font-medium text-lg">{formatPercentage(criteria.minimum_yield_on_cost)}</p>
                </div>
              )}
              {criteria.minimum_equity_multiple && (
                <div className="bg-white p-4 rounded border">
                  <span className="text-xs text-green-600">Min Equity Multiple</span>
                  <p className="font-medium text-lg">{criteria.minimum_equity_multiple}x</p>
                </div>
              )}
              {criteria.target_cash_on_cash_min && (
                <div className="bg-white p-4 rounded border">
                  <span className="text-xs text-green-600">Target Cash on Cash</span>
                  <p className="font-medium text-lg">{formatPercentage(criteria.target_cash_on_cash_min)}</p>
                </div>
              )}
              {criteria.attachment_point && (
                <div className="bg-white p-4 rounded border">
                  <span className="text-xs text-green-600">Attachment Point</span>
                  <p className="font-medium text-lg">{formatPercentage(criteria.attachment_point)}</p>
                </div>
              )}
            </div>

            {/* Hold Periods */}
            {(criteria.min_hold_period_years || criteria.max_hold_period_years) && (
              <div>
                <span className="text-sm font-medium text-green-700">Hold Period</span>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  {criteria.min_hold_period_years && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-green-600">Minimum</span>
                      <p className="font-medium">{criteria.min_hold_period_years} years</p>
                    </div>
                  )}
                  {criteria.max_hold_period_years && (
                    <div className="bg-white p-3 rounded border">
                      <span className="text-xs text-green-600">Maximum</span>
                      <p className="font-medium">{criteria.max_hold_period_years} years</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Additional Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {criteria.ownership_requirement && (
                <div>
                  <span className="text-sm font-medium text-green-700">Ownership Requirement</span>
                  <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.ownership_requirement}</p>
                </div>
              )}
              {criteria.max_leverage_tolerance && (
                <div>
                  <span className="text-sm font-medium text-green-700">Max Leverage Tolerance</span>
                  <p className="text-sm mt-1 bg-white p-3 rounded border">{formatPercentage(criteria.max_leverage_tolerance)}</p>
                </div>
              )}
              {criteria.typical_closing_timeline_days && (
                <div>
                  <span className="text-sm font-medium text-green-700">Typical Closing Timeline</span>
                  <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.typical_closing_timeline_days} days</p>
                </div>
              )}
              {criteria.proof_of_funds_requirement !== undefined && (
                <div>
                  <span className="text-sm font-medium text-green-700">Proof of Funds Required</span>
                  <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.proof_of_funds_requirement ? 'Yes' : 'No'}</p>
                </div>
              )}
            </div>

            {/* Program Overview */}
            {criteria.equity_program_overview && (
              <div>
                <span className="text-sm font-medium text-green-700">Program Overview</span>
                <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.equity_program_overview}</p>
              </div>
            )}

            {/* Notes */}
            {criteria.equity_notes && (
              <div>
                <span className="text-sm font-medium text-green-700">Notes</span>
                <p className="text-sm mt-1 bg-white p-3 rounded border">{criteria.equity_notes}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="text-gray-600">Loading investment criteria...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Card className="bg-white shadow-sm">
        <CardContent className="pt-6">
          <div className="text-center py-6">
            <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Error Loading Investment Criteria
            </h3>
            <p className="text-red-500 mb-4">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (totalCriteria === 0) {
    return (
      <Card className="bg-white shadow-sm">
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Target className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-700 mb-2">
              No Investment Criteria Found
            </h3>
            <p className="text-gray-500 max-w-md mx-auto">
              No investment criteria records have been found for this {entityType}.
            </p>
            <Button onClick={handleAddNew} className="mt-4">
              <Plus className="h-4 w-4 mr-2" />
              Add Investment Criteria
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Transform InvestmentCriteriaData to InvestmentCriteriaFormData format
  const transformToFormData = (criteria: InvestmentCriteriaData) => {
    const result = {
      // Central data
      capital_position: criteria.capital_position,
      minimum_deal_size: criteria.minimum_deal_size ? Number(criteria.minimum_deal_size) : undefined,
      maximum_deal_size: criteria.maximum_deal_size ? Number(criteria.maximum_deal_size) : undefined,
      country: criteria.country,
      region: criteria.region,
      state: criteria.state,
      city: criteria.city,
      property_types: criteria.property_types,
      property_subcategories: criteria.property_subcategories,
      strategies: criteria.strategies,
      decision_making_process: criteria.decision_making_process,
      notes: criteria.notes,
      
      // Debt fields - transform from API format to form format
      debt_notes: criteria.debt_notes,
      debt_closing_time: criteria.closing_time ? Number(criteria.closing_time) : undefined,
      debt_future_facilities: criteria.future_facilities,
      debt_eligible_borrower: criteria.eligible_borrower,
      debt_occupancy_requirements: criteria.occupancy_requirements,
      debt_lien_position: criteria.lien_position,
      debt_min_loan_dscr: criteria.min_loan_dscr ? Number(criteria.min_loan_dscr) : undefined,
      debt_max_loan_dscr: criteria.max_loan_dscr ? Number(criteria.max_loan_dscr) : undefined,
      debt_recourse_loan: criteria.recourse_loan,
      debt_loan_min_debt_yield: criteria.loan_min_debt_yield,
      debt_prepayment: criteria.prepayment,
      debt_yield_maintenance: criteria.yield_maintenance,
      debt_application_deposit: criteria.application_deposit ? Number(criteria.application_deposit) : undefined,
      debt_good_faith_deposit: criteria.good_faith_deposit ? Number(criteria.good_faith_deposit) : undefined,
      debt_loan_origination_max_fee: criteria.loan_origination_max_fee ? Number(criteria.loan_origination_max_fee) : undefined,
      debt_loan_origination_min_fee: criteria.loan_origination_min_fee ? Number(criteria.loan_origination_min_fee) : undefined,
      debt_loan_exit_min_fee: criteria.loan_exit_min_fee ? Number(criteria.loan_exit_min_fee) : undefined,
      debt_loan_exit_max_fee: criteria.loan_exit_max_fee ? Number(criteria.loan_exit_max_fee) : undefined,
      debt_loan_interest_rate: criteria.loan_interest_rate ? Number(criteria.loan_interest_rate) : undefined,
      debt_loan_interest_rate_based_off_sofr: criteria.loan_interest_rate_based_off_sofr ? Number(criteria.loan_interest_rate_based_off_sofr) : undefined,
      debt_loan_interest_rate_based_off_wsj: criteria.loan_interest_rate_based_off_wsj ? Number(criteria.loan_interest_rate_based_off_wsj) : undefined,
      debt_loan_interest_rate_based_off_prime: criteria.loan_interest_rate_based_off_prime ? Number(criteria.loan_interest_rate_based_off_prime) : undefined,
      debt_loan_interest_rate_based_off_3yt: criteria.loan_interest_rate_based_off_3yt ? Number(criteria.loan_interest_rate_based_off_3yt) : undefined,
      debt_loan_interest_rate_based_off_5yt: criteria.loan_interest_rate_based_off_5yt ? Number(criteria.loan_interest_rate_based_off_5yt) : undefined,
      debt_loan_interest_rate_based_off_10yt: criteria.loan_interest_rate_based_off_10yt ? Number(criteria.loan_interest_rate_based_off_10yt) : undefined,
      debt_loan_interest_rate_based_off_30yt: criteria.loan_interest_rate_based_off_30yt ? Number(criteria.loan_interest_rate_based_off_30yt) : undefined,
      debt_rate_lock: criteria.rate_lock,
      debt_rate_type: criteria.rate_type,
      debt_loan_to_value_max: criteria.loan_to_value_max ? Number(criteria.loan_to_value_max) : undefined,
      debt_loan_to_value_min: criteria.loan_to_value_min ? Number(criteria.loan_to_value_min) : undefined,
      debt_loan_to_cost_min: criteria.loan_to_cost_min ? Number(criteria.loan_to_cost_min) : undefined,
      debt_loan_to_cost_max: criteria.loan_to_cost_max ? Number(criteria.loan_to_cost_max) : undefined,
      debt_program_overview: criteria.debt_program_overview,
      loan_type: criteria.loan_type,
      debt_loan_type_normalized: criteria.loan_type_normalized,
      structured_loan_tranche: criteria.structured_loan_tranche,
      loan_program: criteria.loan_program,
      debt_min_loan_term: criteria.min_loan_term ? Number(criteria.min_loan_term) : undefined,
      debt_max_loan_term: criteria.max_loan_term ? Number(criteria.max_loan_term) : undefined,
      debt_amortization: criteria.amortization,
      
      // Equity fields - transform from API format to form format
      equity_target_return: criteria.target_return ? Number(criteria.target_return) : undefined,
      equity_minimum_internal_rate_of_return: criteria.minimum_internal_rate_of_return ? Number(criteria.minimum_internal_rate_of_return) : undefined,
      equity_minimum_yield_on_cost: criteria.minimum_yield_on_cost ? Number(criteria.minimum_yield_on_cost) : undefined,
      equity_minimum_equity_multiple: criteria.minimum_equity_multiple ? Number(criteria.minimum_equity_multiple) : undefined,
      equity_target_cash_on_cash_min: criteria.target_cash_on_cash_min ? Number(criteria.target_cash_on_cash_min) : undefined,
      equity_min_hold_period_years: criteria.min_hold_period_years ? Number(criteria.min_hold_period_years) : undefined,
      equity_max_hold_period_years: criteria.max_hold_period_years ? Number(criteria.max_hold_period_years) : undefined,
      equity_ownership_requirement: criteria.ownership_requirement,
      equity_attachment_point: criteria.attachment_point ? Number(criteria.attachment_point) : undefined,
      equity_max_leverage_tolerance: criteria.max_leverage_tolerance ? Number(criteria.max_leverage_tolerance) : undefined,
      equity_typical_closing_timeline_days: criteria.typical_closing_timeline_days ? Number(criteria.typical_closing_timeline_days) : undefined,
      equity_proof_of_funds_requirement: criteria.proof_of_funds_requirement,
      equity_notes: criteria.equity_notes,
      equity_program_overview: criteria.equity_program_overview,
      equity_occupancy_requirements: criteria.equity_occupancy_requirements,
      equity_yield_on_cost: criteria.yield_on_cost ? Number(criteria.yield_on_cost) : undefined,
      equity_target_return_irr_on_equity: criteria.target_return_irr_on_equity ? Number(criteria.target_return_irr_on_equity) : undefined,
      equity_equity_multiple: criteria.equity_multiple ? Number(criteria.equity_multiple) : undefined,
      equity_position_specific_irr: criteria.position_specific_irr ? Number(criteria.position_specific_irr) : undefined,
      equity_position_specific_equity_multiple: criteria.position_specific_equity_multiple ? Number(criteria.position_specific_equity_multiple) : undefined,
    };
    
    return result;
  }

  // Show the form when editing or adding new
  if (isEditing || isAddingNew) {
    return (
      <InvestmentCriteriaForm
        key={`${editingCriteria?.investment_criteria_id || 'new'}-${editingCriteria?.updated_at || Date.now()}`} // Force re-render when criteria changes
        initialData={editingCriteria ? transformToFormData(editingCriteria) : undefined}
        onSave={isAddingNew ? onSave! : handleSaveEdit}
        onCancel={isAddingNew ? onBack! : handleCancelEdit}
        isEditing={!!editingCriteria}
        isSaving={isSaving}
        showCompanySelection={false}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Target className="h-5 w-5 text-blue-600" />
          <h2 className="text-lg font-semibold">Investment Criteria Details</h2>
        </div>
      </div>

      {/* Criteria Details */}
      {Object.entries(groupedCriteria).map(([capitalPosition, criteriaList]) => (
        <div key={capitalPosition} className="space-y-4">
          <div className="flex items-center space-x-3">
            <Badge className={`${getCapitalPositionColor(capitalPosition)} text-lg px-4 py-2 font-semibold`}>
              {capitalPosition}
            </Badge>
            <span className="text-gray-600">
              {criteriaList.length} {criteriaList.length === 1 ? 'criterion' : 'criteria'}
            </span>
          </div>

          <div className="space-y-6">
            {criteriaList.map((criteria, index) => (
              <div key={criteria.investment_criteria_id} className="space-y-6">
                {index > 0 && <Separator className="my-8" />}
                {renderCriteriaDetails(criteria)}
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}
