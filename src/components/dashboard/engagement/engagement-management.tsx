"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Play,
  Pause,
  RotateCw,
  Check,
  Clock,
  MessageSquare,
  AlertCircle,
  Plus,
  Users,
  Target,
  TrendingUp,
  Calendar,
  Filter,
  Search,
} from "lucide-react";

interface SequenceItemProps {
  title: string;
  description: string;
  status: "active" | "paused" | "draft";
  contacts: number;
  responses: number;
  meetings: number;
  progress: number;
}

interface SequenceCardProps {
  title: string;
  description: string;
  status: "active" | "paused" | "draft";
  contacts: number;
  responses: number;
  meetings: number;
  progress: number;
}

interface ContactRowProps {
  name: string;
  company: string;
  role: string;
  status: "engaged" | "meeting_scheduled" | "interested" | "no_response";
  lastContact: string;
  sequence: string;
}

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  icon: React.ComponentType<{ className?: string }>;
  color: "blue" | "green" | "purple" | "orange";
}

interface ActivityItemProps {
  type: "response" | "meeting" | "alert";
  contact: string;
  company: string;
  time: string;
  message: string;
}

const EngagementManagement = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [searchTerm, setSearchTerm] = useState("");

  return (
    <div className="min-h-screen bg-white">
      <main className="w-full px-6 py-6">
        {/* Header */}
        <header className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Engagement Management
            </h1>
            <p className="text-sm text-gray-500 mt-1">
              Manage and track AI-driven engagement sequences
            </p>
          </div>
          <Button className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white">
            <Plus className="h-4 w-4" />
            Create New Sequence
          </Button>
        </header>

        {/* Tab Navigation */}
        <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
          {[
            { id: "overview", label: "Overview", icon: TrendingUp },
            { id: "sequences", label: "Sequences", icon: Target },
            { id: "contacts", label: "Contacts", icon: Users },
            { id: "analytics", label: "Analytics", icon: TrendingUp },
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? "bg-white text-blue-600 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                <Icon className="h-4 w-4" />
                {tab.label}
              </button>
            );
          })}
        </div>

        {/* Search and Filters */}
        <div className="flex gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search sequences, contacts, or companies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filters
          </Button>
        </div>

        {/* Content based on active tab */}
        {activeTab === "overview" && <OverviewTab />}
        {activeTab === "sequences" && <SequencesTab />}
        {activeTab === "contacts" && <ContactsTab />}
        {activeTab === "analytics" && <AnalyticsTab />}
      </main>
    </div>
  );
};

const OverviewTab = () => (
  <div className="space-y-6">
    {/* Key Metrics */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <MetricCard
        title="Total Active Contacts"
        value="156"
        change="+12"
        icon={Users}
        color="blue"
      />
      <MetricCard
        title="Response Rate"
        value="38%"
        change="+5%"
        icon={MessageSquare}
        color="green"
      />
      <MetricCard
        title="Meetings Scheduled"
        value="28"
        change="+8"
        icon={Calendar}
        color="purple"
      />
      <MetricCard
        title="Active Sequences"
        value="12"
        change="+2"
        icon={Target}
        color="orange"
      />
    </div>

    {/* Active Sequences */}
    <Card>
      <CardHeader>
        <CardTitle>Active Sequences</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <SequenceItem
            title="Q4 Capital Raising"
            description="Outreach to potential investors for Downtown Office Portfolio"
            status="active"
            contacts={12}
            responses={5}
            meetings={2}
            progress={75}
          />
          <SequenceItem
            title="Rescue Capital Campaign"
            description="Targeting distressed asset opportunities in Northeast"
            status="active"
            contacts={24}
            responses={8}
            meetings={3}
            progress={60}
          />
          <SequenceItem
            title="Tech Investment Outreach"
            description="Proptech and real estate technology investment opportunities"
            status="paused"
            contacts={18}
            responses={3}
            meetings={1}
            progress={45}
          />
        </div>
      </CardContent>
    </Card>

    {/* Recent Activity */}
    <Card>
      <CardHeader>
        <CardTitle>Recent Engagement Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <ActivityItem
            type="response"
            contact="Sarah Chen"
            company="Goldman Sachs"
            time="10 minutes ago"
            message="Interested in learning more about the investment opportunity"
          />
          <ActivityItem
            type="meeting"
            contact="Michael Bloomberg"
            company="Vista Equity"
            time="1 hour ago"
            message="Meeting scheduled for next Tuesday"
          />
          <ActivityItem
            type="alert"
            contact="David Park"
            company="Blackstone"
            time="2 hours ago"
            message="No response after 3 follow-ups - consider changing approach"
          />
          <ActivityItem
            type="response"
            contact="Jennifer Lopez"
            company="KKR"
            time="3 hours ago"
            message="Requested additional financial documentation"
          />
        </div>
      </CardContent>
    </Card>
  </div>
);

const SequencesTab = () => (
  <div className="space-y-6">
    <div className="flex justify-between items-center">
      <h2 className="text-xl font-semibold">All Sequences</h2>
      <Button className="flex items-center gap-2">
        <Plus className="h-4 w-4" />
        New Sequence
      </Button>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <SequenceCard
        title="Q4 Capital Raising"
        description="Outreach to potential investors"
        status="active"
        contacts={12}
        responses={5}
        meetings={2}
        progress={75}
      />
      <SequenceCard
        title="Rescue Capital Campaign"
        description="Distressed asset opportunities"
        status="active"
        contacts={24}
        responses={8}
        meetings={3}
        progress={60}
      />
      <SequenceCard
        title="Tech Investment Outreach"
        description="Proptech investment opportunities"
        status="paused"
        contacts={18}
        responses={3}
        meetings={1}
        progress={45}
      />
      <SequenceCard
        title="International Expansion"
        description="Global real estate opportunities"
        status="draft"
        contacts={0}
        responses={0}
        meetings={0}
        progress={0}
      />
    </div>
  </div>
);

const ContactsTab = () => (
  <div className="space-y-6">
    <div className="flex justify-between items-center">
      <h2 className="text-xl font-semibold">Engagement Contacts</h2>
      <Button className="flex items-center gap-2">
        <Plus className="h-4 w-4" />
        Add Contacts
      </Button>
    </div>

    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          <ContactRow
            name="Sarah Chen"
            company="Goldman Sachs"
            role="Managing Director"
            status="engaged"
            lastContact="2 hours ago"
            sequence="Q4 Capital Raising"
          />
          <ContactRow
            name="Michael Bloomberg"
            company="Vista Equity"
            role="Partner"
            status="meeting_scheduled"
            lastContact="1 day ago"
            sequence="Rescue Capital Campaign"
          />
          <ContactRow
            name="David Park"
            company="Blackstone"
            role="Senior Associate"
            status="no_response"
            lastContact="3 days ago"
            sequence="Q4 Capital Raising"
          />
          <ContactRow
            name="Jennifer Lopez"
            company="KKR"
            role="Principal"
            status="interested"
            lastContact="5 hours ago"
            sequence="Tech Investment Outreach"
          />
        </div>
      </CardContent>
    </Card>
  </div>
);

const AnalyticsTab = () => (
  <div className="space-y-6">
    <h2 className="text-xl font-semibold">Engagement Analytics</h2>

    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Response Rate by Sequence</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span>Q4 Capital Raising</span>
              <div className="flex items-center gap-2">
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: "42%" }}
                  ></div>
                </div>
                <span className="text-sm font-medium">42%</span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span>Rescue Capital Campaign</span>
              <div className="flex items-center gap-2">
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-600 h-2 rounded-full"
                    style={{ width: "33%" }}
                  ></div>
                </div>
                <span className="text-sm font-medium">33%</span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span>Tech Investment Outreach</span>
              <div className="flex items-center gap-2">
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-purple-600 h-2 rounded-full"
                    style={{ width: "17%" }}
                  ></div>
                </div>
                <span className="text-sm font-medium">17%</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Engagement Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">Initial Contact</div>
                <div className="text-sm text-gray-500">Day 1</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-600 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">First Response</div>
                <div className="text-sm text-gray-500">Day 2-3</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-purple-600 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">Meeting Scheduled</div>
                <div className="text-sm text-gray-500">Day 5-7</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-orange-600 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">Deal Discussion</div>
                <div className="text-sm text-gray-500">Day 10-14</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
);

const SequenceItem = ({
  title,
  description,
  status,
  contacts,
  responses,
  meetings,
  progress,
}: SequenceItemProps) => (
  <div className="flex items-center justify-between p-4 border rounded-lg">
    <div className="flex-1">
      <div className="flex items-center space-x-3">
        <h3 className="font-medium">{title}</h3>
        <Badge
          className={
            status === "active"
              ? "bg-green-100 text-green-800"
              : "bg-yellow-100 text-yellow-800"
          }
        >
          {status === "active" ? "Active" : "Paused"}
        </Badge>
      </div>
      <p className="text-sm text-gray-500 mt-1">{description}</p>
      <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
        <div
          className="bg-blue-600 h-2 rounded-full"
          style={{ width: `${progress}%` }}
        ></div>
      </div>
      <div className="text-xs text-gray-500 mt-1">{progress}% complete</div>
    </div>
    <div className="flex items-center space-x-8">
      <div className="text-center">
        <div className="text-2xl font-bold">{contacts}</div>
        <div className="text-sm text-gray-500">Contacts</div>
      </div>
      <div className="text-center">
        <div className="text-2xl font-bold">{responses}</div>
        <div className="text-sm text-gray-500">Responses</div>
      </div>
      <div className="text-center">
        <div className="text-2xl font-bold">{meetings}</div>
        <div className="text-sm text-gray-500">Meetings</div>
      </div>
      <div className="flex space-x-2">
        {status === "active" ? (
          <Button variant="outline" size="icon">
            <Pause className="h-4 w-4" />
          </Button>
        ) : (
          <Button variant="outline" size="icon">
            <Play className="h-4 w-4" />
          </Button>
        )}
        <Button variant="outline" size="icon">
          <RotateCw className="h-4 w-4" />
        </Button>
      </div>
    </div>
  </div>
);

const SequenceCard = ({
  title,
  description,
  status,
  contacts,
  responses,
  meetings,
  progress,
}: SequenceCardProps) => (
  <Card className="hover:shadow-md transition-shadow">
    <CardContent className="p-6">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold">{title}</h3>
        <Badge
          className={
            status === "active"
              ? "bg-green-100 text-green-800"
              : status === "paused"
              ? "bg-yellow-100 text-yellow-800"
              : "bg-gray-100 text-gray-800"
          }
        >
          {status === "active"
            ? "Active"
            : status === "paused"
            ? "Paused"
            : "Draft"}
        </Badge>
      </div>
      <p className="text-sm text-gray-600 mb-4">{description}</p>

      <div className="space-y-3">
        <div className="flex justify-between text-sm">
          <span>Progress</span>
          <span>{progress}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full"
            style={{ width: `${progress}%` }}
          ></div>
        </div>

        <div className="grid grid-cols-3 gap-2 text-center">
          <div>
            <div className="font-bold">{contacts}</div>
            <div className="text-xs text-gray-500">Contacts</div>
          </div>
          <div>
            <div className="font-bold">{responses}</div>
            <div className="text-xs text-gray-500">Responses</div>
          </div>
          <div>
            <div className="font-bold">{meetings}</div>
            <div className="text-xs text-gray-500">Meetings</div>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
);

const ContactRow = ({
  name,
  company,
  role,
  status,
  lastContact,
  sequence,
}: ContactRowProps) => (
  <div className="flex items-center justify-between p-4 border rounded-lg">
    <div className="flex items-center space-x-4">
      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
        <span className="font-semibold text-blue-600">
          {name
            .split(" ")
            .map((n) => n[0])
            .join("")}
        </span>
      </div>
      <div>
        <div className="font-medium">{name}</div>
        <div className="text-sm text-gray-500">
          {role} at {company}
        </div>
      </div>
    </div>
    <div className="flex items-center space-x-4">
      <div className="text-right">
        <div className="text-sm font-medium">{sequence}</div>
        <div className="text-xs text-gray-500">Last: {lastContact}</div>
      </div>
      <Badge
        className={
          status === "engaged"
            ? "bg-green-100 text-green-800"
            : status === "meeting_scheduled"
            ? "bg-blue-100 text-blue-800"
            : status === "interested"
            ? "bg-purple-100 text-purple-800"
            : "bg-red-100 text-red-800"
        }
      >
        {status.replace("_", " ")}
      </Badge>
    </div>
  </div>
);

const MetricCard = ({
  title,
  value,
  change,
  icon: Icon,
  color,
}: MetricCardProps) => {
  const colorClasses = {
    blue: "bg-blue-100 text-blue-600",
    green: "bg-green-100 text-green-600",
    purple: "bg-purple-100 text-purple-600",
    orange: "bg-orange-100 text-orange-600",
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-gray-500 text-sm">{title}</div>
            <div className="mt-2 flex items-baseline">
              <div className="text-2xl font-bold">{value}</div>
              <div
                className={`text-sm font-medium ml-2 ${
                  change.startsWith("+") ? "text-green-600" : "text-red-600"
                }`}
              >
                {change}
              </div>
            </div>
          </div>
          <div
            className={`h-12 w-12 rounded-lg flex items-center justify-center ${colorClasses[color]}`}
          >
            <Icon className="h-6 w-6" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const ActivityItem = ({
  type,
  contact,
  company,
  time,
  message,
}: ActivityItemProps) => {
  const icons = {
    response: <MessageSquare className="h-4 w-4" />,
    meeting: <Check className="h-4 w-4" />,
    alert: <AlertCircle className="h-4 w-4" />,
  };

  const colors = {
    response: "bg-blue-100 text-blue-600",
    meeting: "bg-green-100 text-green-600",
    alert: "bg-yellow-100 text-yellow-600",
  };

  return (
    <div className="flex items-start space-x-3">
      <div
        className={`h-8 w-8 rounded-full flex items-center justify-center ${colors[type]}`}
      >
        {icons[type]}
      </div>
      <div className="flex-1">
        <div className="flex items-center space-x-2">
          <span className="font-medium">{contact}</span>
          <span className="text-gray-500">at</span>
          <span className="font-medium">{company}</span>
        </div>
        <p className="text-sm text-gray-600 mt-1">{message}</p>
      </div>
      <div className="text-sm text-gray-500">
        <Clock className="h-4 w-4 inline mr-1" />
        {time}
      </div>
    </div>
  );
};

export default EngagementManagement;
