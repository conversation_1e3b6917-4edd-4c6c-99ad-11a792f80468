"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ReactMultiSelect } from '@/components/ui/react-multi-select';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { 
  X, Filter, RotateCcw, Search, SlidersHorizontal, ChevronRight, Sparkles,
  Building, DollarSign, MapPin, Settings, BarChart3, TrendingUp, Building2,
  Target, Briefcase, Calculator, Calendar, Users, Globe, ArrowUp, ArrowDown, 
  Clock, Banknote, Percent, Timer, LineChart, Activity, Pie<PERSON><PERSON>, User, <PERSON>r<PERSON><PERSON><PERSON>,
  Mail, CheckCircle, AlertCircle, MessageSquare, Send, Loader2, Brain, Eye,
  Database, Table, Shield, Minus, Plus, Eye as EyeIcon, EyeOff
} from 'lucide-react';
import type { ContactUnifiedFilters } from "./shared/types";

interface MappingsData {
  [type: string]: {
    parents: string[]
    children: string[]
    hierarchical: {
      [parent: string]: string[]
    }
  }
}

interface SourceCount {
  source: string;
  count: string;
}

interface JobTierCount {
  job_tier: string;
  count: number;
}

interface ContactUnifiedFiltersProps {
  filters: ContactUnifiedFilters;
  mappings?: MappingsData;
  onFiltersChange: (filters: ContactUnifiedFilters) => void;
  onClearFilters: () => void;
  isLoading?: boolean;
}

export default function ContactUnifiedFilters({
  filters,
  mappings,
  onFiltersChange,
  onClearFilters,
  isLoading = false
}: ContactUnifiedFiltersProps) {
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<ContactUnifiedFilters>(filters);
  const [pendingFilters, setPendingFilters] = useState<ContactUnifiedFilters>(filters);
  const [localRangeInputs, setLocalRangeInputs] = useState<{[key: string]: string}>({});
  const [sources, setSources] = useState<SourceCount[]>([]);
  const [loadingSources, setLoadingSources] = useState(false);
  const [jobTiers, setJobTiers] = useState<JobTierCount[]>([]);
  const [loadingJobTiers, setLoadingJobTiers] = useState(false);

  const [enrichmentCapitalPositions, setEnrichmentCapitalPositions] = useState<Array<{value: string, label: string}>>([]);
  const [loadingEnrichmentCapitalPositions, setLoadingEnrichmentCapitalPositions] = useState(false);
  const [enrichmentCompanyTypes, setEnrichmentCompanyTypes] = useState<Array<{value: string, label: string}>>([]);
  const [loadingEnrichmentCompanyTypes, setLoadingEnrichmentCompanyTypes] = useState(false);
  const [enrichmentStatuses, setEnrichmentStatuses] = useState<Array<{value: string, label: string}>>([]);
  const [loadingEnrichmentStatuses, setLoadingEnrichmentStatuses] = useState(false);

  // Company Processing Flags Options
  const [companyProcessingFlags, setCompanyProcessingFlags] = useState<{
    websiteScrapingStatuses: Array<{value: string, label: string}>,
    companyOverviewStatuses: Array<{value: string, label: string}>,
    companyOverviewV2Statuses: Array<{value: string, label: string}>,
    companyInvestmentCriteriaStatuses: Array<{value: string, label: string}>
  }>({
    websiteScrapingStatuses: [],
    companyOverviewStatuses: [],
    companyOverviewV2Statuses: [],
    companyInvestmentCriteriaStatuses: []
  });
  const [loadingCompanyProcessingFlags, setLoadingCompanyProcessingFlags] = useState(false);

  // Investment Criteria Filter Options (shared with companies)
  const [investmentCriteriaOptions, setInvestmentCriteriaOptions] = useState<{
    capitalPositions: Array<{value: string, label: string}>,
    propertyTypes: Array<{value: string, label: string}>,
    propertySubcategories: Array<{value: string, label: string}>,
    strategies: Array<{value: string, label: string}>,
    loanTypes: Array<{value: string, label: string}>,
    structuredLoanTranches: Array<{value: string, label: string}>,
    loanPrograms: Array<{value: string, label: string}>,
    recourseLoans: Array<{value: string, label: string}>,
    countries: Array<{value: string, label: string}>,
    regions: Array<{value: string, label: string}>,
    states: Array<{value: string, label: string}>,
    cities: Array<{value: string, label: string}>,
  }>({
    capitalPositions: [],
    propertyTypes: [],
    propertySubcategories: [],
    strategies: [],
    loanTypes: [],
    structuredLoanTranches: [],
    loanPrograms: [],
    recourseLoans: [],
    countries: [],
    regions: [],
    states: [],
    cities: [],
  });
  const [loadingInvestmentCriteriaOptions, setLoadingInvestmentCriteriaOptions] = useState(false);
  // Individual NOT filter modes for each specific filter
  const [filterNotModes, setFilterNotModes] = useState<{[key: string]: boolean}>({
    source: false,
    emailStatus: false,
    propertyTypes: false,
    propertySubcategories: false,
    strategies: false,
    capitalPosition: false,
    loanTypes: false,
    structuredLoanTranche: false,
    loanProgram: false,
    recourseLoan: false,
    enrichmentCompanyType: false,
    enrichmentCapitalPositions: false,
    // Add other filters as needed
  });

  // Track which table sections are expanded
  const [expandedSections, setExpandedSections] = useState<{[key: string]: boolean}>({
    contacts: true,
    investment_criteria: false,
    contact_enrichment: false,
    gmail_outreach: false,
    company_flags: false
  });

  // Fetch sources and job tiers on component mount
  useEffect(() => {
    async function fetchSources() {
      setLoadingSources(true);
      try {
        const response = await fetch('/api/contacts/sources');
        if (response.ok) {
          const data = await response.json();
          setSources(data);
        }
      } catch (error) {
        console.error('Error fetching sources:', error);
      } finally {
        setLoadingSources(false);
      }
    }

    async function fetchJobTiers() {
      setLoadingJobTiers(true);
      try {
        const response = await fetch('/api/contacts/job-tiers');
        if (response.ok) {
          const data = await response.json();
          setJobTiers(data);
        }
      } catch (error) {
        console.error('Error fetching job tiers:', error);
      } finally {
        setLoadingJobTiers(false);
      }
    }

    async function fetchEnrichmentCapitalPositions() {
      setLoadingEnrichmentCapitalPositions(true);
      try {
        const response = await fetch('/api/contacts/filter-options?type=enrichment_capital_positions');
        if (response.ok) {
          const data = await response.json();
          setEnrichmentCapitalPositions(data.data || []);
        }
      } catch (error) {
        console.error('Error fetching enrichment capital positions:', error);
      } finally {
        setLoadingEnrichmentCapitalPositions(false);
      }
    }

    async function fetchEnrichmentCompanyTypes() {
      setLoadingEnrichmentCompanyTypes(true);
      try {
        const response = await fetch('/api/contacts/filter-options?type=enrichment_company_types');
        if (response.ok) {
          const data = await response.json();
          setEnrichmentCompanyTypes(data.data || []);
        }
      } catch (error) {
        console.error('Error fetching enrichment company types:', error);
      } finally {
        setLoadingEnrichmentCompanyTypes(false);
      }
    }

    async function fetchEnrichmentStatuses() {
      setLoadingEnrichmentStatuses(true);
      try {
        const response = await fetch('/api/contacts/filter-options?type=enrichment_statuses');
        if (response.ok) {
          const data = await response.json();
          setEnrichmentStatuses(data.data || []);
        }
      } catch (error) {
        console.error('Error fetching enrichment statuses:', error);
      } finally {
        setLoadingEnrichmentStatuses(false);
      }
    }

    async function fetchCompanyProcessingFlags() {
      setLoadingCompanyProcessingFlags(true);
      try {
        const response = await fetch('/api/contacts/filter-options-v2?type=company_processors');
        if (response.ok) {
          const data = await response.json();
          setCompanyProcessingFlags({
            websiteScrapingStatuses: data.websiteScrapingStatuses || [],
            companyOverviewStatuses: data.companyOverviewStatuses || [],
            companyOverviewV2Statuses: data.companyOverviewV2Statuses || [],
            companyInvestmentCriteriaStatuses: data.companyInvestmentCriteriaStatuses || []
          });
        }
      } catch (error) {
        console.error('Error fetching company processing flags:', error);
      } finally {
        setLoadingCompanyProcessingFlags(false);
      }
    }

    fetchSources();
    fetchJobTiers();
    fetchEnrichmentCapitalPositions();
    fetchEnrichmentCompanyTypes();
    fetchEnrichmentStatuses();
    fetchCompanyProcessingFlags();
  }, []);

  // Fetch investment criteria filter options
  useEffect(() => {
    async function fetchInvestmentCriteriaOptions() {
      setLoadingInvestmentCriteriaOptions(true);
      try {
        const response = await fetch('/api/investment-criteria/filter-options?entityType=Contact');
        if (response.ok) {
          const data = await response.json();
          setInvestmentCriteriaOptions(data);
        }
      } catch (error) {
        console.error('Error fetching investment criteria options:', error);
      } finally {
        setLoadingInvestmentCriteriaOptions(false);
      }
    }

    fetchInvestmentCriteriaOptions();
  }, []);

  // Sync local filters with prop changes
  useEffect(() => {
    setLocalFilters(filters);
    setPendingFilters(filters);
  }, [filters]);

  // Update pending filters (not applied yet)
  const updatePendingFilters = (newFilters: Partial<ContactUnifiedFilters>) => {
    const updatedPendingFilters = { ...pendingFilters, ...newFilters, page: 1 };
    setPendingFilters(updatedPendingFilters);
  };

  // Apply filters when user clicks Apply button
  const applyFilters = () => {
    setLocalFilters(pendingFilters);
    onFiltersChange(pendingFilters);
    setIsFilterPanelOpen(false);
  };

  // Handle clear filters
  const handleClearFilters = () => {
    const defaultFilters = {
      page: 1,
      limit: 25,
      sortBy: 'updated_at',
      sortOrder: 'desc' as const
    };
    
    setLocalFilters(defaultFilters);
    setPendingFilters(defaultFilters);
    onFiltersChange(defaultFilters);
  };

  // Handle range input changes with local state
  const updateLocalRangeInput = (key: string, value: string) => {
    setLocalRangeInputs(prev => ({ ...prev, [key]: value }));
  };

  // Apply range filter when user finishes input
  const applyRangeFilter = (key: string, value: string) => {
    const numericValue = value.trim() === '' ? undefined : Number(value);
    updatePendingFilters({ [key]: numericValue } as Partial<ContactUnifiedFilters>);
    
    setLocalRangeInputs(prev => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  };

  // Get current value for range input
  const getRangeInputValue = (key: string, filterValue?: number) => {
    return localRangeInputs[key] !== undefined ? localRangeInputs[key] : (filterValue || '');
  };

  // Helper functions for mappings
  const getParentValues = (type: string) => {
    return mappings?.[type]?.parents || [];
  };

  const getAllChildValues = (type: string) => {
    return mappings?.[type]?.children || [];
  };

  const getFilteredChildValues = (type: string, selectedParents: string[]) => {
    if (!selectedParents?.length) {
      return getAllChildValues(type);
    }
    
    const filteredChildren = new Set<string>();
    selectedParents.forEach(parent => {
      const children = mappings?.[type]?.hierarchical?.[parent] || [];
      children.forEach(child => filteredChildren.add(child));
    });
    
    return Array.from(filteredChildren).sort();
  };

  // Count active filters
  const getActiveFilterCount = () => {
    let count = 0;
    
    // Basic filters
    if (pendingFilters.searchTerm) count++;
    if (pendingFilters.sortBy && pendingFilters.sortBy !== 'updated_at') count++;
    if (pendingFilters.sortOrder && pendingFilters.sortOrder !== 'desc') count++;
    
    // Contacts table filters
    if (pendingFilters.source?.length) count++;
    if (pendingFilters.emailStatus?.length) count++;
    if (pendingFilters.contactCompanyType?.length) count++;
    if (pendingFilters.contactCapitalPosition?.length) count++;
    if (pendingFilters.jobTier?.length) count++;
    if (pendingFilters.contactCountries?.length) count++;
    if (pendingFilters.contactStates?.length) count++;
    if (pendingFilters.contactCities?.length) count++;
    
    // Processing status filters
    if (pendingFilters.emailVerificationStatus?.length) count++;
    if (pendingFilters.contactEnrichmentStatus?.length) count++;
    if (pendingFilters.contactEnrichmentV2Status?.length) count++;
    if (pendingFilters.contactInvestmentCriteriaStatus?.length) count++;
    if (pendingFilters.emailGenerationStatus?.length) count++;
    if (pendingFilters.emailSendingStatus?.length) count++;
    
    // Boolean flags
    if (pendingFilters.extracted !== undefined) count++;
    if (pendingFilters.searched !== undefined) count++;
    if (pendingFilters.emailGenerated !== undefined) count++;
    if (pendingFilters.enriched !== undefined) count++;
    if (pendingFilters.hasSmartleadId !== undefined) count++;
    
    // Investment criteria filters
    if (pendingFilters.capitalPosition?.length) count++;
    if (pendingFilters.propertyTypes?.length) count++;
    if (pendingFilters.strategies?.length) count++;
    if (pendingFilters.dealSizeMin !== undefined) count++;
    if (pendingFilters.dealSizeMax !== undefined) count++;
    if (pendingFilters.targetReturnMin !== undefined) count++;
    if (pendingFilters.targetReturnMax !== undefined) count++;
    if (pendingFilters.regions?.length) count++;
    if (pendingFilters.states?.length) count++;
    if (pendingFilters.cities?.length) count++;
    
    // Contact enrichment filters
    if (pendingFilters.enrichmentCompanyType?.length) count++;
    if (pendingFilters.enrichmentCapitalPositions?.length) count++;
    if (pendingFilters.enrichmentStatus?.length) count++;
    
    // Gmail outreach filters
    if (pendingFilters.hasBeenReachedOut !== undefined) count++;
    
    // NOT filters
    if (pendingFilters.notCapitalPosition?.length) count++;
    if (pendingFilters.notPropertyTypes?.length) count++;
    if (pendingFilters.notStrategies?.length) count++;
    if (pendingFilters.notSource?.length) count++;
    if (pendingFilters.notEmailStatus?.length) count++;
    if (pendingFilters.notEnrichmentCompanyType?.length) count++;
    if (pendingFilters.notEnrichmentCapitalPositions?.length) count++;
    
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  // Enhanced sort options
  const UNIFIED_SORT_OPTIONS = [
    { value: 'updated_at', label: 'Last Updated (Newest First)', icon: Clock },
    { value: 'created_at', label: 'Created Date', icon: Calendar },
    { value: 'first_name', label: 'First Name', icon: User },
    { value: 'last_name', label: 'Last Name', icon: User },
    { value: 'email', label: 'Email Address', icon: Mail },
    { value: 'company_name', label: 'Company Name', icon: Building },
    { value: 'processing_error_count', label: 'Error Count (Most First)', icon: AlertCircle },
    { value: 'target_return', label: 'Target Return (Highest First)', icon: Target },
    { value: 'minimum_deal_size', label: 'Min Deal Size (Largest First)', icon: DollarSign },
    { value: 'maximum_deal_size', label: 'Max Deal Size (Largest First)', icon: Banknote },
  ];

  // Processing status options
  const processingStatusOptions = [
    { value: 'not_started', label: 'Not Started', icon: Minus, color: 'from-gray-50 to-slate-50' },
    { value: 'pending', label: 'Pending', icon: Clock, color: 'from-yellow-50 to-amber-50' },
    { value: 'running', label: 'Running', icon: Loader2, color: 'from-blue-50 to-indigo-50' },
    { value: 'completed', label: 'Completed', icon: CheckCircle, color: 'from-green-50 to-emerald-50' },
    { value: 'failed', label: 'Failed', icon: AlertCircle, color: 'from-red-50 to-pink-50' }
  ];

  const emailStatusOptions = ['Verified', 'Invalid', 'Unknown', 'Failed'];

  // Toggle section expansion
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({ ...prev, [section]: !prev[section] }));
  };

  // Get NOT filter state for specific filter
  const getFilterNotState = (filterKey: string): boolean => {
    return filterNotModes[filterKey] || false;
  };

  // Toggle NOT filter mode for specific filter
  const toggleFilterNotMode = (filterKey: string) => {
    setFilterNotModes(prev => ({
      ...prev,
      [filterKey]: !prev[filterKey]
    }));
  };

  // Check if conditional loan fields should be shown
  const shouldShowLoanFields = () => {
    const capitalPositions = pendingFilters.capitalPosition || [];
    return capitalPositions.some(position => 
      ['Mezzanine', 'Senior Debt', 'Stretch Senior'].includes(position)
    );
  };

  // Multi-select component with NOT filter support
  const EnhancedMultiSelect = ({ 
    options, 
    selected, 
    notSelected,
    onChange, 
    onNotChange,
    placeholder, 
    disabled,
    label,
    showNotFilter = false,
    filterKey
  }: {
    options: Array<{value: string, label: string}>,
    selected: string[],
    notSelected?: string[],
    onChange: (values: string[]) => void,
    onNotChange?: (values: string[]) => void,
    placeholder: string,
    disabled?: boolean,
    label: string,
    showNotFilter?: boolean,
    filterKey: string
  }) => {
    const isNotMode = getFilterNotState(filterKey);
    
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium text-gray-700">{label}</Label>
          {showNotFilter && (
            <div className="flex items-center gap-2">
              <EyeIcon className="h-4 w-4 text-gray-400" />
              <Switch
                checked={isNotMode}
                onCheckedChange={() => toggleFilterNotMode(filterKey)}
              />
              <EyeOff className="h-4 w-4 text-gray-400" />
            </div>
          )}
        </div>
        
        {!isNotMode ? (
          <ReactMultiSelect
            options={options}
            selected={selected || []}
            onChange={onChange}
            placeholder={placeholder}
            disabled={disabled}
            showSelectAll={true}
            selectAllLabel="Select All"
          />
        ) : (
          <ReactMultiSelect
            options={options}
            selected={notSelected || []}
            onChange={onNotChange || (() => {})}
            placeholder={`NOT ${placeholder.toLowerCase()}`}
            disabled={disabled}
            showSelectAll={true}
            selectAllLabel="Exclude All"
          />
        )}
      </div>
    );
  };

  return (
    <>
      {/* Unified Filter Bar */}
      <div className="w-full bg-white border border-gray-200 rounded-xl shadow-sm mb-6">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            {/* Unified Filter Button */}
            <Button
              onClick={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
              className={`flex items-center gap-3 px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 ${
                isFilterPanelOpen 
                  ? 'bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white' 
                  : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
              }`}
            >
              <Database className="h-5 w-5" />
              <span className="font-medium">Table Filters</span>
              {activeFilterCount > 0 && (
                <Badge className="bg-white/20 text-white border border-white/20 ml-1">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>
          </div>

          <div className="flex items-center gap-3">
            {/* Active Filters Count */}
            {activeFilterCount > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  {activeFilterCount} filter{activeFilterCount !== 1 ? 's' : ''} applied
                </span>
                <Button
                  onClick={handleClearFilters}
                  className="text-gray-500 hover:text-red-600 bg-transparent border border-gray-200 hover:border-red-200 hover:bg-red-50 px-3 py-1 text-sm"
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Clear All
                </Button>
              </div>
            )}

            {/* Enhanced Sort Controls */}
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Sort by:</span>
              </div>
              <div className="flex items-center gap-2">
                <Select 
                  value={pendingFilters.sortBy || 'updated_at'} 
                  onValueChange={(value) => updatePendingFilters({ sortBy: value })}
                >
                  <SelectTrigger className="w-auto min-w-[250px] border-gray-200 bg-white shadow-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="max-h-[400px]">
                    {UNIFIED_SORT_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <option.icon className="h-4 w-4" />
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                {/* Sort Order Toggle */}
                <Button
                  onClick={() => updatePendingFilters({ 
                    sortOrder: pendingFilters.sortOrder === 'asc' ? 'desc' : 'asc' 
                  })}
                  className={`p-2 rounded-lg border transition-all ${
                    pendingFilters.sortOrder === 'asc' 
                      ? 'bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100' 
                      : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                  }`}
                  title={`Sort ${pendingFilters.sortOrder === 'asc' ? 'Ascending' : 'Descending'}`}
                >
                  {pendingFilters.sortOrder === 'asc' ? (
                    <ArrowUp className="h-4 w-4" />
                  ) : (
                    <ArrowDown className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Unified Right Side Filter Panel */}
      <div className={`fixed top-0 right-0 h-full w-[700px] bg-white border-l border-gray-200 shadow-xl transform transition-transform duration-300 ease-in-out z-50 ${
        isFilterPanelOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Enhanced Panel Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 via-indigo-50 to-blue-50">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="p-3 rounded-xl bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-lg">
                  <Database className="h-6 w-6" />
                </div>
                {activeFilterCount > 0 && (
                  <div className="absolute -top-2 -right-2 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center font-bold">
                    {activeFilterCount > 9 ? '9+' : activeFilterCount}
                  </div>
                )}
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                  Database Table Filters
                  <Badge className="bg-purple-100 text-purple-700 border border-purple-200 ml-2">
                    Multi-Table Query Builder
                  </Badge>
                </h2>
                <p className="text-sm text-gray-600 mt-1 flex items-center gap-2">
                  <Table className="h-4 w-4" />
                  Filter contacts across multiple database tables with conditional joins
                </p>
              </div>
            </div>
            <Button
              onClick={() => setIsFilterPanelOpen(false)}
              className="text-gray-500 hover:text-gray-700 bg-white border border-gray-200 p-2 hover:bg-gray-50 transition-colors"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Panel Content - Scrollable */}
          <div className="flex-1 overflow-y-auto p-6 space-y-6">

            {/* Global Search */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-gray-50 to-blue-50">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <Search className="h-5 w-5 text-blue-600" />
                  Global Search
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Universal Search */}
                <div className="space-y-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search by name, email, title, or company (e.g., 'john smith', '@gmail.com', 'CEO')"
                      value={pendingFilters.searchTerm || ''}
                      onChange={(e) => updatePendingFilters({ searchTerm: e.target.value || undefined })}
                      onKeyDown={(e) => e.key === 'Enter' && applyFilters()}
                      className="pl-10 border-gray-200 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
                      disabled={isLoading}
                    />
                  </div>
                  <p className="text-xs text-gray-500">
                    Uses LIKE search across names, emails, job titles, and company names with partial matching
                  </p>
                </div>


              </CardContent>
            </Card>

            {/* Contacts Table Filters */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-cyan-50 to-blue-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-cyan-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('contacts')}
                title={`Click to ${expandedSections.contacts ? 'collapse' : 'expand'} Contacts filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <User className="h-5 w-5 text-cyan-600" />
                    Contacts Table
                    <Badge className="bg-cyan-100 text-cyan-700 border border-cyan-200">
                      Core Data
                    </Badge>
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.contacts ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.contacts ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.contacts && (
                <CardContent className="space-y-6">
                  {/* Source Filter */}
                  <EnhancedMultiSelect
                    label="Source"
                    options={sources.map(source => ({ 
                      value: source.source, 
                      label: `${source.source} (${source.count})` 
                    }))}
                    selected={pendingFilters.source || []}
                    notSelected={pendingFilters.notSource || []}
                    onChange={(selected: string[]) => updatePendingFilters({ source: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notSource: selected })}
                    placeholder="Select sources..."
                    disabled={isLoading || loadingSources}
                    showNotFilter={true}
                    filterKey="source"
                  />

                  {/* Email Status */}
                  <EnhancedMultiSelect
                    label="Email Status"
                    options={emailStatusOptions.map(status => ({ value: status, label: status }))}
                    selected={pendingFilters.emailStatus || []}
                    notSelected={pendingFilters.notEmailStatus || []}
                    onChange={(selected: string[]) => updatePendingFilters({ emailStatus: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notEmailStatus: selected })}
                    placeholder="Select email status..."
                    disabled={isLoading}
                    showNotFilter={true}
                    filterKey="emailStatus"
                  />

                  {/* Job Tier Filter */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Job Tier</Label>
                    {loadingJobTiers ? (
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Loading job tiers...
                      </div>
                    ) : (
                      <ReactMultiSelect
                        options={jobTiers.map(tier => ({ 
                          value: tier.job_tier, 
                          label: `${tier.job_tier} (${tier.count})` 
                        }))}
                        selected={pendingFilters.jobTier || []}
                        onChange={(selected: string[]) => updatePendingFilters({ jobTier: selected })}
                        placeholder="Select job tiers..."
                        disabled={isLoading}
                        showSelectAll={true}
                        selectAllLabel="Select All Job Tiers"
                      />
                    )}
                  </div>

                  {/* Contact Location */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Contact Location</Label>
                    <div className="space-y-2">
                      <Input
                        placeholder="Country"
                        value={pendingFilters.contactCountries?.[0] || ''}
                        onChange={(e) => updatePendingFilters({ contactCountries: e.target.value ? [e.target.value] : undefined })}
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                      <Input
                        placeholder="State"
                        value={pendingFilters.contactStates?.[0] || ''}
                        onChange={(e) => updatePendingFilters({ contactStates: e.target.value ? [e.target.value] : undefined })}
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                      <Input
                        placeholder="City"
                        value={pendingFilters.contactCities?.[0] || ''}
                        onChange={(e) => updatePendingFilters({ contactCities: e.target.value ? [e.target.value] : undefined })}
                        className="bg-white border-gray-200"
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  {/* Processing Status Groups */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Email Verification</Label>
                      <ReactMultiSelect
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.emailVerificationStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ emailVerificationStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showSelectAll={true}
                        selectAllLabel="Select All Statuses"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Contact Enrichment</Label>
                      <ReactMultiSelect
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.contactEnrichmentStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ contactEnrichmentStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showSelectAll={true}
                        selectAllLabel="Select All Statuses"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Contact Enrichment V2</Label>
                      <ReactMultiSelect
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.contactEnrichmentV2Status || []}
                        onChange={(selected: string[]) => updatePendingFilters({ contactEnrichmentV2Status: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showSelectAll={true}
                        selectAllLabel="Select All Statuses"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Investment Criteria Status</Label>
                      <ReactMultiSelect
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.contactInvestmentCriteriaStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ contactInvestmentCriteriaStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showSelectAll={true}
                        selectAllLabel="Select All Statuses"
                      />
                    </div>
                  </div>
                  
                  {/* Email Processing Status Groups */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Email Generation</Label>
                      <ReactMultiSelect
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.emailGenerationStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ emailGenerationStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showSelectAll={true}
                        selectAllLabel="Select All Statuses"
                      />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Email Sending</Label>
                      <ReactMultiSelect
                        options={processingStatusOptions.map(option => ({ 
                          value: option.value, 
                          label: option.label 
                        }))}
                        selected={pendingFilters.emailSendingStatus || []}
                        onChange={(selected: string[]) => updatePendingFilters({ emailSendingStatus: selected })}
                        placeholder="Select status..."
                        disabled={isLoading}
                        showSelectAll={true}
                        selectAllLabel="Select All Statuses"
                      />
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Investment Criteria Table Filters */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-orange-50 to-red-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-orange-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('investment_criteria')}
                title={`Click to ${expandedSections.investment_criteria ? 'collapse' : 'expand'} Investment Criteria filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <Target className="h-5 w-5 text-orange-600" />
                    Investment Criteria Table
                    <Badge className="bg-orange-100 text-orange-700 border border-orange-200">
                      Conditional JOIN
                    </Badge>
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.investment_criteria ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.investment_criteria ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.investment_criteria && (
                <CardContent className="space-y-6">
                  {/* 1. Capital Position */}
                  <EnhancedMultiSelect
                    label="Capital Position"
                    options={investmentCriteriaOptions.capitalPositions}
                    selected={pendingFilters.capitalPosition || []}
                    notSelected={pendingFilters.notCapitalPosition || []}
                    onChange={(selected: string[]) => updatePendingFilters({ capitalPosition: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notCapitalPosition: selected })}
                    placeholder={loadingInvestmentCriteriaOptions ? "Loading capital positions..." : "Select capital positions..."}
                    disabled={isLoading || loadingInvestmentCriteriaOptions}
                    showNotFilter={true}
                    filterKey="capitalPosition"
                  />

                  {/* 2. Deal Size Range */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Deal Size Range (e.g., $1M-$10M)</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <Input
                        type="number"
                        placeholder="Min (M)"
                        value={getRangeInputValue('dealSizeMin', pendingFilters.dealSizeMin)}
                        onChange={(e) => updateLocalRangeInput('dealSizeMin', e.target.value)}
                        onBlur={(e) => applyRangeFilter('dealSizeMin', e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('dealSizeMin', e.currentTarget.value)}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                      <Input
                        type="number"
                        placeholder="Max (M)"
                        value={getRangeInputValue('dealSizeMax', pendingFilters.dealSizeMax)}
                        onChange={(e) => updateLocalRangeInput('dealSizeMax', e.target.value)}
                        onBlur={(e) => applyRangeFilter('dealSizeMax', e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('dealSizeMax', e.currentTarget.value)}
                        disabled={isLoading}
                        className="bg-white border-gray-200"
                      />
                    </div>
                  </div>

                  {/* 3. Location (nested: Country, Region, State, City) */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Location</Label>
                    <div className="space-y-2">
                      <ReactMultiSelect
                        options={investmentCriteriaOptions.countries}
                        selected={pendingFilters.countries || []}
                        onChange={(selected: string[]) => updatePendingFilters({ countries: selected })}
                        placeholder={loadingInvestmentCriteriaOptions ? "Loading countries..." : "Select countries..."}
                        disabled={isLoading || loadingInvestmentCriteriaOptions}
                        showSelectAll={true}
                        selectAllLabel="Select All Countries"
                      />
                      <ReactMultiSelect
                        options={investmentCriteriaOptions.regions}
                        selected={pendingFilters.regions || []}
                        onChange={(selected: string[]) => updatePendingFilters({ regions: selected })}
                        placeholder={loadingInvestmentCriteriaOptions ? "Loading regions..." : "Select regions..."}
                        disabled={isLoading || loadingInvestmentCriteriaOptions}
                        showSelectAll={true}
                        selectAllLabel="Select All Regions"
                      />
                      <ReactMultiSelect
                        options={investmentCriteriaOptions.states}
                        selected={pendingFilters.states || []}
                        onChange={(selected: string[]) => updatePendingFilters({ states: selected })}
                        placeholder={loadingInvestmentCriteriaOptions ? "Loading states..." : "Select states..."}
                        disabled={isLoading || loadingInvestmentCriteriaOptions}
                        showSelectAll={true}
                        selectAllLabel="Select All States"
                      />
                      <ReactMultiSelect
                        options={investmentCriteriaOptions.cities}
                        selected={pendingFilters.cities || []}
                        onChange={(selected: string[]) => updatePendingFilters({ cities: selected })}
                        placeholder={loadingInvestmentCriteriaOptions ? "Loading cities..." : "Select cities..."}
                        disabled={isLoading || loadingInvestmentCriteriaOptions}
                        showSelectAll={true}
                        selectAllLabel="Select All Cities"
                      />
                    </div>
                  </div>

                  {/* 4. Property Type */}
                  <EnhancedMultiSelect
                    label="Property Type"
                    options={investmentCriteriaOptions.propertyTypes}
                    selected={pendingFilters.propertyTypes || []}
                    notSelected={pendingFilters.notPropertyTypes || []}
                    onChange={(selected: string[]) => updatePendingFilters({ propertyTypes: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notPropertyTypes: selected })}
                    placeholder={loadingInvestmentCriteriaOptions ? "Loading property types..." : "Select property types..."}
                    disabled={isLoading || loadingInvestmentCriteriaOptions}
                    showNotFilter={true}
                    filterKey="propertyTypes"
                  />

                  {/* 5. Sub Property Type */}
                  <EnhancedMultiSelect
                    label="Sub Property Type"
                    options={investmentCriteriaOptions.propertySubcategories}
                    selected={pendingFilters.propertySubcategories || []}
                    notSelected={pendingFilters.notPropertySubcategories || []}
                    onChange={(selected: string[]) => updatePendingFilters({ propertySubcategories: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notPropertySubcategories: selected })}
                    placeholder={loadingInvestmentCriteriaOptions ? "Loading sub property types..." : "Select sub property types..."}
                    disabled={isLoading || loadingInvestmentCriteriaOptions}
                    showNotFilter={true}
                    filterKey="propertySubcategories"
                  />

                  {/* 6. Strategies */}
                  <EnhancedMultiSelect
                    label="Strategies"
                    options={investmentCriteriaOptions.strategies}
                    selected={pendingFilters.strategies || []}
                    notSelected={pendingFilters.notStrategies || []}
                    onChange={(selected: string[]) => updatePendingFilters({ strategies: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notStrategies: selected })}
                    placeholder={loadingInvestmentCriteriaOptions ? "Loading strategies..." : "Select strategies..."}
                    disabled={isLoading || loadingInvestmentCriteriaOptions}
                    showNotFilter={true}
                    filterKey="strategies"
                  />

                  {/* Conditional Fields - Show only if Capital Position includes Mezzanine, Senior Debt, or Stretch Senior */}
                  {shouldShowLoanFields() && (
                    <>
                      <div className="border-t pt-4 mt-6">
                        <h4 className="font-medium text-gray-800 mb-4 text-sm bg-amber-50 p-2 rounded border-l-4 border-amber-400">
                          💰 Loan-Specific Fields (Conditional on Capital Position)
                        </h4>
                      </div>

                      {/* 7. Loan Type */}
                      <EnhancedMultiSelect
                        label="Loan Type"
                        options={investmentCriteriaOptions.loanTypes}
                        selected={pendingFilters.loanTypes || []}
                        notSelected={pendingFilters.notLoanTypes || []}
                        onChange={(selected: string[]) => updatePendingFilters({ loanTypes: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notLoanTypes: selected })}
                        placeholder={loadingInvestmentCriteriaOptions ? "Loading loan types..." : "Select loan types..."}
                        disabled={isLoading || loadingInvestmentCriteriaOptions}
                        showNotFilter={true}
                        filterKey="loanTypes"
                      />

                      {/* 8. Structured Loan Tranche */}
                      <EnhancedMultiSelect
                        label="Structured Loan Tranche"
                        options={investmentCriteriaOptions.structuredLoanTranches}
                        selected={pendingFilters.structuredLoanTranche || []}
                        notSelected={pendingFilters.notStructuredLoanTranche || []}
                        onChange={(selected: string[]) => updatePendingFilters({ structuredLoanTranche: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notStructuredLoanTranche: selected })}
                        placeholder={loadingInvestmentCriteriaOptions ? "Loading structured loan tranches..." : "Select structured loan tranches..."}
                        disabled={isLoading || loadingInvestmentCriteriaOptions}
                        showNotFilter={true}
                        filterKey="structuredLoanTranche"
                      />

                      {/* 9. Loan Program */}
                      <EnhancedMultiSelect
                        label="Loan Program"
                        options={investmentCriteriaOptions.loanPrograms}
                        selected={pendingFilters.loanProgram || []}
                        notSelected={pendingFilters.notLoanProgram || []}
                        onChange={(selected: string[]) => updatePendingFilters({ loanProgram: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notLoanProgram: selected })}
                        placeholder={loadingInvestmentCriteriaOptions ? "Loading loan programs..." : "Select loan programs..."}
                        disabled={isLoading || loadingInvestmentCriteriaOptions}
                        showNotFilter={true}
                        filterKey="loanProgram"
                      />

                      {/* 10. Loan Term Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan Term (Years, e.g., 1-30)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            placeholder="Min Years"
                            value={getRangeInputValue('minLoanTerm', pendingFilters.minLoanTerm)}
                            onChange={(e) => updateLocalRangeInput('minLoanTerm', e.target.value)}
                            onBlur={(e) => applyRangeFilter('minLoanTerm', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('minLoanTerm', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max Years"
                            value={getRangeInputValue('maxLoanTerm', pendingFilters.maxLoanTerm)}
                            onChange={(e) => updateLocalRangeInput('maxLoanTerm', e.target.value)}
                            onBlur={(e) => applyRangeFilter('maxLoanTerm', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('maxLoanTerm', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* 11. Loan Interest Rate Based On Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan Interest Rate Based On (Basis Points, e.g., +200)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            placeholder="Min +bp (e.g., 200)"
                            value={getRangeInputValue('interestRateSofrMin', pendingFilters.interestRateSofrMin)}
                            onChange={(e) => updateLocalRangeInput('interestRateSofrMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('interestRateSofrMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('interestRateSofrMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max +bp (e.g., 400)"
                            value={getRangeInputValue('interestRateSofrMax', pendingFilters.interestRateSofrMax)}
                            onChange={(e) => updateLocalRangeInput('interestRateSofrMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('interestRateSofrMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('interestRateSofrMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                        <p className="text-xs text-gray-500">SOFR, WSJ, Prime, Libor, 5YT, 10YT basis point spreads</p>
                      </div>

                      {/* 12. Loan Interest Rate Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan Interest Rate (%, e.g., 3.5%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="Min % (e.g., 3.5)"
                            value={getRangeInputValue('interestRateMin', pendingFilters.interestRateMin)}
                            onChange={(e) => updateLocalRangeInput('interestRateMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('interestRateMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('interestRateMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="Max % (e.g., 8.0)"
                            value={getRangeInputValue('interestRateMax', pendingFilters.interestRateMax)}
                            onChange={(e) => updateLocalRangeInput('interestRateMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('interestRateMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('interestRateMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* 13. Loan To Value Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan To Value (%, e.g., 70%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            placeholder="Min % (e.g., 60)"
                            value={getRangeInputValue('loanToValueMin', pendingFilters.loanToValueMin)}
                            onChange={(e) => updateLocalRangeInput('loanToValueMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanToValueMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanToValueMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max % (e.g., 80)"
                            value={getRangeInputValue('loanToValueMax', pendingFilters.loanToValueMax)}
                            onChange={(e) => updateLocalRangeInput('loanToValueMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanToValueMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanToValueMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* 14. Loan To Cost Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan To Cost (%, e.g., 80%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            placeholder="Min % (e.g., 70)"
                            value={getRangeInputValue('loanToCostMin', pendingFilters.loanToCostMin)}
                            onChange={(e) => updateLocalRangeInput('loanToCostMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanToCostMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanToCostMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            placeholder="Max % (e.g., 90)"
                            value={getRangeInputValue('loanToCostMax', pendingFilters.loanToCostMax)}
                            onChange={(e) => updateLocalRangeInput('loanToCostMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanToCostMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanToCostMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* 15. Loan Origination Fee Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan Origination Fee (%, e.g., 1%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="Min % (e.g., 0.5)"
                            value={getRangeInputValue('loanOriginationFeeMin', pendingFilters.loanOriginationFeeMin)}
                            onChange={(e) => updateLocalRangeInput('loanOriginationFeeMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanOriginationFeeMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanOriginationFeeMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="Max % (e.g., 2.0)"
                            value={getRangeInputValue('loanOriginationFeeMax', pendingFilters.loanOriginationFeeMax)}
                            onChange={(e) => updateLocalRangeInput('loanOriginationFeeMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanOriginationFeeMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanOriginationFeeMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* 16. Loan Exit Fee Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan Exit Fee (%, e.g., 0.5%)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="Min % (e.g., 0.0)"
                            value={getRangeInputValue('loanExitFeeMin', pendingFilters.loanExitFeeMin)}
                            onChange={(e) => updateLocalRangeInput('loanExitFeeMin', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanExitFeeMin', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanExitFeeMin', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="Max % (e.g., 1.0)"
                            value={getRangeInputValue('loanExitFeeMax', pendingFilters.loanExitFeeMax)}
                            onChange={(e) => updateLocalRangeInput('loanExitFeeMax', e.target.value)}
                            onBlur={(e) => applyRangeFilter('loanExitFeeMax', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('loanExitFeeMax', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* 17. Recourse Loan */}
                      <EnhancedMultiSelect
                        label="Recourse Loan"
                        options={investmentCriteriaOptions.recourseLoans}
                        selected={pendingFilters.recourseLoan || []}
                        notSelected={pendingFilters.notRecourseLoan || []}
                        onChange={(selected: string[]) => updatePendingFilters({ recourseLoan: selected })}
                        onNotChange={(selected: string[]) => updatePendingFilters({ notRecourseLoan: selected })}
                        placeholder={loadingInvestmentCriteriaOptions ? "Loading recourse loan types..." : "Select recourse loan types..."}
                        disabled={isLoading || loadingInvestmentCriteriaOptions}
                        showNotFilter={true}
                        filterKey="recourseLoan"
                      />

                      {/* 18. Loan DSCR Range */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Loan DSCR (e.g., 1.20x)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="Min (e.g., 1.0)"
                            value={getRangeInputValue('minLoanDscr', pendingFilters.minLoanDscr)}
                            onChange={(e) => updateLocalRangeInput('minLoanDscr', e.target.value)}
                            onBlur={(e) => applyRangeFilter('minLoanDscr', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('minLoanDscr', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="Max (e.g., 2.0)"
                            value={getRangeInputValue('maxLoanDscr', pendingFilters.maxLoanDscr)}
                            onChange={(e) => updateLocalRangeInput('maxLoanDscr', e.target.value)}
                            onBlur={(e) => applyRangeFilter('maxLoanDscr', e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && applyRangeFilter('maxLoanDscr', e.currentTarget.value)}
                            disabled={isLoading}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>

                      {/* 19. Closing Time */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Closing Time</Label>
                        <Input
                          type="text"
                          placeholder="e.g., 30-90 days"
                          value={pendingFilters.closingTime?.join(', ') || ''}
                          onChange={(e) => updatePendingFilters({ closingTime: e.target.value.split(',').map(s => s.trim()).filter(Boolean) })}
                          disabled={isLoading}
                          className="bg-white border-gray-200"
                        />
                        <p className="text-xs text-gray-500">Enter closing time ranges separated by commas</p>
                      </div>
                    </>
                  )}
                </CardContent>
              )}
            </Card>

            {/* Contact Enrichment Table Filters */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-purple-50 to-pink-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-purple-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('contact_enrichment')}
                title={`Click to ${expandedSections.contact_enrichment ? 'collapse' : 'expand'} Contact Enrichment filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <Brain className="h-5 w-5 text-purple-600" />
                    Contact Enrichment Table
                    <Badge className="bg-purple-100 text-purple-700 border border-purple-200">
                      AI Enhanced
                    </Badge>
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.contact_enrichment ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.contact_enrichment ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.contact_enrichment && (
                <CardContent className="space-y-6">
                  {/* Enrichment Company Type */}
                  <EnhancedMultiSelect
                    label="AI-Detected Company Type"
                    options={loadingEnrichmentCompanyTypes ? [] : enrichmentCompanyTypes}
                    selected={pendingFilters.enrichmentCompanyType || []}
                    notSelected={pendingFilters.notEnrichmentCompanyType || []}
                    onChange={(selected: string[]) => updatePendingFilters({ enrichmentCompanyType: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notEnrichmentCompanyType: selected })}
                    placeholder={loadingEnrichmentCompanyTypes ? "Loading company types..." : "Select company types..."}
                    disabled={isLoading || loadingEnrichmentCompanyTypes}
                    showNotFilter={true}
                    filterKey="enrichmentCompanyType"
                  />

                  {/* Enrichment Capital Positions */}
                  <EnhancedMultiSelect
                    label="AI-Detected Capital Positions"
                    options={loadingEnrichmentCapitalPositions ? [] : enrichmentCapitalPositions}
                    selected={pendingFilters.enrichmentCapitalPositions || []}
                    notSelected={pendingFilters.notEnrichmentCapitalPositions || []}
                    onChange={(selected: string[]) => updatePendingFilters({ enrichmentCapitalPositions: selected })}
                    onNotChange={(selected: string[]) => updatePendingFilters({ notEnrichmentCapitalPositions: selected })}
                    placeholder={loadingEnrichmentCapitalPositions ? "Loading capital positions..." : "Select capital positions..."}
                    disabled={isLoading || loadingEnrichmentCapitalPositions}
                    showNotFilter={true}
                    filterKey="enrichmentCapitalPositions"
                  />

                  {/* Enrichment Status */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Enrichment Status</Label>
                    <ReactMultiSelect
                      options={loadingEnrichmentStatuses ? [] : enrichmentStatuses}
                      selected={pendingFilters.enrichmentStatus || []}
                      onChange={(selected: string[]) => updatePendingFilters({ enrichmentStatus: selected })}
                      placeholder={loadingEnrichmentStatuses ? "Loading enrichment statuses..." : "Select enrichment status..."}
                      disabled={isLoading || loadingEnrichmentStatuses}
                      showSelectAll={true}
                      selectAllLabel="Select All Statuses"
                    />
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Gmail Outreach Table Filters */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-pink-50 to-rose-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-pink-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('gmail_outreach')}
                title={`Click to ${expandedSections.gmail_outreach ? 'collapse' : 'expand'} Gmail Outreach filters`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <Send className="h-5 w-5 text-pink-600" />
                    Gmail Outreach Table
                    <Badge className="bg-pink-100 text-pink-700 border border-pink-200">
                      Email Tracking
                    </Badge>
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.gmail_outreach ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.gmail_outreach ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.gmail_outreach && (
                <CardContent className="space-y-6">
                  {/* Has Been Reached Out */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">Email Outreach Status</Label>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <input
                          type="radio"
                          id="reached-out-yes"
                          name="hasBeenReachedOut"
                          checked={pendingFilters.hasBeenReachedOut === true}
                          onChange={() => updatePendingFilters({ hasBeenReachedOut: true })}
                          className="h-4 w-4 text-pink-600 focus:ring-pink-500"
                          disabled={isLoading}
                        />
                        <Label htmlFor="reached-out-yes" className="text-sm text-gray-700">
                          Already Reached Out
                        </Label>
                      </div>
                      <div className="flex items-center gap-2">
                        <input
                          type="radio"
                          id="reached-out-no"
                          name="hasBeenReachedOut"
                          checked={pendingFilters.hasBeenReachedOut === false}
                          onChange={() => updatePendingFilters({ hasBeenReachedOut: false })}
                          className="h-4 w-4 text-pink-600 focus:ring-pink-500"
                          disabled={isLoading}
                        />
                        <Label htmlFor="reached-out-no" className="text-sm text-gray-700">
                          Not Yet Reached Out
                        </Label>
                      </div>
                      <div className="flex items-center gap-2">
                        <input
                          type="radio"
                          id="reached-out-all"
                          name="hasBeenReachedOut"
                          checked={pendingFilters.hasBeenReachedOut === undefined}
                          onChange={() => updatePendingFilters({ hasBeenReachedOut: undefined })}
                          className="h-4 w-4 text-pink-600 focus:ring-pink-500"
                          disabled={isLoading}
                        />
                        <Label htmlFor="reached-out-all" className="text-sm text-gray-700">
                          All Contacts
                        </Label>
                      </div>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
            
            {/* Company Processing Flags */}
            <Card className="border-0 shadow-sm bg-gradient-to-r from-orange-50 to-red-50">
              <CardHeader 
                className="pb-4 cursor-pointer hover:bg-orange-100/50 transition-colors rounded-t-lg" 
                onClick={() => toggleSection('company_flags')}
                title={`Click to ${expandedSections.company_flags ? 'collapse' : 'expand'} Company Processing flags`}
              >
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-3">
                    <Building className="h-5 w-5 text-orange-600" />
                    Company Processing Flags
                    <Badge className="bg-orange-100 text-orange-700 border border-orange-200">
                      Company Status
                    </Badge>
                    <Badge className="bg-gray-100 text-gray-600 border border-gray-200 text-xs">
                      {expandedSections.company_flags ? 'Expanded' : 'Click to expand'}
                    </Badge>
                  </div>
                  <ChevronRight className={`h-5 w-5 transition-transform ${expandedSections.company_flags ? 'rotate-90' : ''}`} />
                </CardTitle>
              </CardHeader>
              {expandedSections.company_flags && (
                <CardContent className="space-y-6">
                  <p className="text-sm text-orange-700 bg-orange-50 p-3 rounded-lg border border-orange-200">
                    Filter contacts based on their associated companies' processing status and data extraction
                  </p>
                  
                  {/* Company Status Groups */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Website Scraping Status</Label>
                      {loadingCompanyProcessingFlags ? (
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Loading statuses...
                        </div>
                      ) : (
                        <ReactMultiSelect
                          options={companyProcessingFlags.websiteScrapingStatuses}
                          selected={pendingFilters.companyWebsiteScrapingStatus || []}
                          onChange={(selected: string[]) => updatePendingFilters({ companyWebsiteScrapingStatus: selected })}
                          placeholder="Select status..."
                          disabled={isLoading}
                          showSelectAll={true}
                          selectAllLabel="Select All Statuses"
                        />
                      )}
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Company Overview Status</Label>
                      {loadingCompanyProcessingFlags ? (
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Loading statuses...
                        </div>
                      ) : (
                        <ReactMultiSelect
                          options={companyProcessingFlags.companyOverviewStatuses}
                          selected={pendingFilters.companyOverviewStatus || []}
                          onChange={(selected: string[]) => updatePendingFilters({ companyOverviewStatus: selected })}
                          placeholder="Select status..."
                          disabled={isLoading}
                          showSelectAll={true}
                          selectAllLabel="Select All Statuses"
                        />
                      )}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Company Overview V2 Status</Label>
                      {loadingCompanyProcessingFlags ? (
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Loading statuses...
                        </div>
                      ) : (
                        <ReactMultiSelect
                          options={companyProcessingFlags.companyOverviewV2Statuses}
                          selected={pendingFilters.companyOverviewV2Status || []}
                          onChange={(selected: string[]) => updatePendingFilters({ companyOverviewV2Status: selected })}
                          placeholder="Select status..."
                          disabled={isLoading}
                          showSelectAll={true}
                          selectAllLabel="Select All Statuses"
                        />
                      )}
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700">Company Investment Criteria Status</Label>
                      {loadingCompanyProcessingFlags ? (
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Loading statuses...
                        </div>
                      ) : (
                        <ReactMultiSelect
                          options={companyProcessingFlags.companyInvestmentCriteriaStatuses}
                          selected={pendingFilters.companyInvestmentCriteriaStatus || []}
                          onChange={(selected: string[]) => updatePendingFilters({ companyInvestmentCriteriaStatus: selected })}
                          placeholder="Select status..."
                          disabled={isLoading}
                          showSelectAll={true}
                          selectAllLabel="Select All Statuses"
                        />
                      )}
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>

          </div>

          {/* Enhanced Panel Footer */}
          <div className="border-t border-gray-200 p-6 bg-gradient-to-r from-gray-50 to-purple-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                {activeFilterCount > 0 ? (
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-purple-600" />
                    <span className="font-medium text-gray-700">
                      {activeFilterCount} Table Filter{activeFilterCount !== 1 ? 's' : ''} Active
                    </span>
                    <Badge className="bg-purple-100 text-purple-700 border border-purple-200">
                      Multi-Table JOIN Query
                    </Badge>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4 text-gray-400" />
                    <span>No filters applied - showing all contact data</span>
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-3">
                {/* Quick Reset */}
                {activeFilterCount > 0 && (
                  <Button
                    onClick={handleClearFilters}
                    className="text-gray-600 hover:text-red-600 bg-white border border-gray-200 hover:border-red-200 hover:bg-red-50 px-4 py-2 text-sm transition-colors"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reset All
                  </Button>
                )}
                
                {/* Apply Button */}
                <Button
                  onClick={activeFilterCount > 0 ? applyFilters : () => setIsFilterPanelOpen(false)}
                  className={`px-6 py-2 font-medium shadow-md hover:shadow-lg transition-all duration-200 ${
                    activeFilterCount > 0 
                      ? 'bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white' 
                      : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
                  }`}
                >
                  {activeFilterCount > 0 ? (
                    <>
                      <Database className="h-4 w-4 mr-2" />
                      Apply {activeFilterCount} Filter{activeFilterCount !== 1 ? 's' : ''}
                    </>
                  ) : (
                    <>
                      <ChevronRight className="h-4 w-4 mr-2" />
                      Close Panel
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
} 