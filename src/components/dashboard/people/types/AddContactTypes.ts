export interface AddContactProps {
  onBack: () => void;
  companyId?: string; // Optional company ID if coming from company page
  preSelectedCompany?: CompanySuggestion; // Pre-selected company data
}

export interface CompanySuggestion {
  company_id?: number;
  company_name: string;
  company_website?: string;
  industry?: string;
  company_address?: string;
  company_city?: string;
  company_state?: string;
  company_country?: string;
  // Extracted data for enhanced auto-fill
  extracted_data?: {
    companytype?: string;
    businessmodel?: string;
    fundsize?: string;
    aum?: string;
    headquarters?: string;
    foundedyear?: number;
    numberofemployees?: string;
    investmentfocus?: string[];
    geographicfocus?: string[];
    dealsize?: string;
    minimumdealsize?: string;
    maximumdealsize?: string;
    investment_criteria_property_types?: string[];
    investment_criteria_asset_types?: string[];
    investment_criteria_loan_types?: string[];
    investment_criteria_property_subcategories?: string[];
    riskprofile?: string;
    targetmarkets?: string[];
    strategies?: string[];
    propertytypes?: string[];
    assetclasses?: string[];
    valuecreation?: string[];
    holdperiod?: string;
    targetreturn?: string;
    approach?: string;
  };
}

export interface ExistingContact {
  contact_id: number;
  first_name: string;
  last_name: string;
  email?: string;
  personal_email?: string;
  linkedin_url?: string;
  company_name?: string;
  title?: string;
  phone_number?: string;
  error?: string; // Add error property for duplicate contact handling
}

export interface InvestmentCriteria {
  id: string;
  capital_type: string;
  investment_criteria_country: string;
  investment_criteria_state: string;
  investment_criteria_city: string;
  investment_criteria_property_type: string;
  investment_criteria_asset_type: string;
  investment_criteria_deal_size: string;
  
  // Debt/Loan specific fields
  investment_criteria_loan_type: string;
  loan_term_months: string;
  interest_rate_index: string;
  origination_fee_percent: string;
  exit_fee_percent: string;
  recourse: string;
  closing_time_weeks: string;
  
  // Equity specific fields
  hold_period_years: string;
  expected_irr_percent: string;
  expected_equity_multiple: string;
}

export interface ContactFormData {
  // Personal Information
  first_name: string;
  last_name: string;
  job_title: string;
  job_tier: string;
  company_name: string;
  
  // Contact Information  
  email: string;
  additional_email: string;
  phone: string;
  additional_phone: string;
  
  // Social Media
  linkedin: string;
  twitter: string;
  facebook: string;
  instagram: string;
  youtube: string;
  
  // Personal Information (continued)
  executive_summary: string;
  career_timeline: string[];
  
  // Education
  education_college: string;
  education_college_year_graduated: string;
  education_high_school: string;
  education_high_school_year_graduated: string;
  
  // Personal Details
  honorable_achievments: string[];
  hobbies: string[];
  age: string;
  
  // Contact Information (continued)
  address: string;
  city: string;
  state: string;
  zipcode: string;
  country: string;
  contact_type: string;
  relationship_owner: string;
  role_in_decision_making: string;
  last_contact_date: string;
  source_of_introduction: string;
  accredited_investor_status: boolean;
  kyc_status: string;
  
  // Company Information (for selection, not stored in contact table)
  company_website?: string;
  industry?: string;
  company_address?: string;
  company_city?: string;
  company_state?: string;
  company_country?: string;
  company_zip?: string;
  
  // Legacy field for capital type
  capital_type: string;
}

export interface QueuedContact {
  id: string;
  formData: ContactFormData;
  investmentCriteria: InvestmentCriteria[];
  status: 'pending' | 'saved' | 'error';
  error?: string;
} 