"use client"

import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  RefreshCw, 
  ExternalLink,
  DollarSign,
  MapPin,
  Building2,
  TrendingUp,
  Calendar,
  FileText,
  Newspaper
} from 'lucide-react';
import { toast } from "sonner";
import Link from "next/link";

interface CategorizedCriteria {
  deal_id: string;
  criteria_id: number;
  capital_position: string[];
  state: string[];
  property_type: string[];
  strategy: string[];
  deal_size: string;
  target_return: string | null;
  entity_type: string;
}

interface CategorizedNews {
  news_id: string | number;
  headline?: string;
  summary?: string;
  deal_size?: number;
  publication_date?: string;
  source_name?: string;
  source_url?: string;
  location_city?: string;
  location_state?: string;
  property_type?: string;
  deal_type?: string;
  buyer_name?: string;
  seller_name?: string;
  score?: number;
}

interface CategorizedData {
  analysis: {
    totalDeals: number;
    totalMatchedCriteria: number;
    totalCriteria: number;
  };
  categorizedCriteria: CategorizedCriteria[];
  categorizedNews?: CategorizedNews[];
}

interface CategorizedDealsSectionProps {
  contactId: string | number;
}

const CategorizedDealsSection: React.FC<CategorizedDealsSectionProps> = ({
  contactId
}) => {
  const [data, setData] = useState<CategorizedData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadCategorizedData = async () => {
    if (!contactId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/deals/contact-email-support/categorization?contactId=${contactId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch categorized data');
      }
      
      const result = await response.json();
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(result.message || 'Failed to load data');
      }
    } catch (error) {
      console.error('Error loading categorized data:', error);
      setError((error as Error).message);
      toast.error('Failed to load categorized deals and news');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCategorizedData();
  }, [contactId]);

  const formatDealSize = (dealSize: string) => {
    if (!dealSize) return 'N/A';
    return dealSize.startsWith('$') ? dealSize : `$${dealSize}`;
  };

  const formatTargetReturn = (targetReturn: string | null) => {
    if (!targetReturn || targetReturn === 'null') return 'N/A';
    const returnValue = parseFloat(targetReturn);
    if (isNaN(returnValue)) return 'N/A';
    return `${(returnValue * 100).toFixed(1)}%`;
  };

  if (loading) {
    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            AI Email Context - Deals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
            <span className="ml-2 text-gray-600">Loading categorized data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            AI Email Context - Deals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={loadCategorizedData} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            AI Email Context - Deals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-gray-500">No categorized data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mt-6">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              AI Email Context - Deals
            </CardTitle>
            <p className="text-sm text-gray-500 mt-1">
              Data sent to AI for personalized email generation
            </p>
          </div>
          <div className="flex items-center gap-4 text-sm">
            <div className="text-center">
              <div className="font-semibold text-blue-600">{data.analysis.totalDeals}</div>
              <div className="text-gray-500">Deals</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-green-600">{data.analysis.totalMatchedCriteria}</div>
              <div className="text-gray-500">Matches</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-purple-600">{data.analysis.totalCriteria}</div>
              <div className="text-gray-500">Criteria</div>
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Deals Section */}
        {data.categorizedCriteria && data.categorizedCriteria.length > 0 && (
          <div className="mb-6">
            <div className="flex items-center gap-2 mb-4">
              <Building2 className="h-4 w-4 text-blue-600" />
              <h3 className="font-medium">Matching Deals ({data.categorizedCriteria.length})</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {data.categorizedCriteria.map((deal, index) => (
                <Link 
                  key={`${deal.deal_id}-${index}`} 
                  href={`/dashboard/deals/${deal.deal_id}`}
                  className="block"
                >
                  <Card className="h-full hover:shadow-md transition-shadow cursor-pointer border-l-4 border-l-blue-500">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex items-center gap-2">
                          <Building2 className="h-4 w-4 text-blue-600" />
                          <span className="font-medium text-sm">Deal #{deal.deal_id}</span>
                        </div>
                        <ExternalLink className="h-3 w-3 text-gray-400" />
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-3 w-3 text-green-600" />
                          <span className="text-sm font-medium">{formatDealSize(deal.deal_size)}</span>
                          {deal.target_return && (
                            <Badge variant="secondary" className="text-xs">
                              {formatTargetReturn(deal.target_return)} Return
                            </Badge>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <MapPin className="h-3 w-3 text-red-500" />
                          <span className="text-xs text-gray-600">
                            {deal.state.join(', ')}
                          </span>
                        </div>
                        
                        <div className="flex flex-wrap gap-1 mt-2">
                          {deal.capital_position.map((pos, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {pos}
                            </Badge>
                          ))}
                        </div>
                        
                        <div className="flex flex-wrap gap-1">
                          {deal.property_type.slice(0, 2).map((type, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              {type}
                            </Badge>
                          ))}
                          {deal.property_type.length > 2 && (
                            <Badge variant="secondary" className="text-xs">
                              +{deal.property_type.length - 2} more
                            </Badge>
                          )}
                        </div>
                        
                        <div className="flex flex-wrap gap-1">
                          {deal.strategy.slice(0, 2).map((strat, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {strat}
                            </Badge>
                          ))}
                          {deal.strategy.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{deal.strategy.length - 2} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        )}
        
        {/* News Section */}
        {data.categorizedNews && data.categorizedNews.length > 0 && (
          <div>
            <div className="flex items-center gap-2 mb-4">
              <Newspaper className="h-4 w-4 text-purple-600" />
              <h3 className="font-medium">Related News ({data.categorizedNews.length})</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {data.categorizedNews.map((news, index) => (
                <Card key={`${news.news_id}-${index}`} className="border-l-4 border-l-purple-500">
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      {news.headline && (
                        <h4 className="font-medium text-sm line-clamp-2">{news.headline}</h4>
                      )}
                      
                      {news.summary && (
                        <p className="text-xs text-gray-600 line-clamp-3">{news.summary}</p>
                      )}
                      
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        {news.source_name && (
                          <span className="flex items-center gap-1">
                            <FileText className="h-3 w-3" />
                            {news.source_name}
                          </span>
                        )}
                        
                        {news.publication_date && (
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(news.publication_date).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                      
                      <div className="flex flex-wrap gap-1 mt-2">
                        {news.property_type && (
                          <Badge variant="secondary" className="text-xs">
                            {news.property_type}
                          </Badge>
                        )}
                        {news.deal_type && (
                          <Badge variant="outline" className="text-xs">
                            {news.deal_type}
                          </Badge>
                        )}
                        {news.deal_size && (
                          <Badge variant="secondary" className="text-xs">
                            ${news.deal_size}M
                          </Badge>
                        )}
                      </div>
                      
                      {news.source_url && (
                        <div className="pt-2">
                          <a 
                            href={news.source_url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-xs text-blue-600 hover:underline flex items-center gap-1"
                          >
                            Read full article <ExternalLink className="h-3 w-3" />
                          </a>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
        
        {data.categorizedCriteria.length === 0 && (!data.categorizedNews || data.categorizedNews.length === 0) && (
          <div className="text-center py-8">
            <Building2 className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <p className="text-gray-500">No matching deals or news found for this contact</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CategorizedDealsSection; 