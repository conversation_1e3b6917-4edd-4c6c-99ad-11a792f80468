"use client"

import React, { useState, useRef, useEffect } from 'react';
import { 
  <PERSON>, 
  Card<PERSON>ontent, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import RichTextEditor, { RichTextEditorRef } from '@/components/common/RichTextEditor';
import { toast } from "sonner";
import { 
  Filter, 
  RefreshCw, 
  MessageCircle, 
  Edit, 
  Trash2, 
  CheckCircle, 
  Send,
  FileText,
  Variable,
  Download,
  ArrowDownToLine,
  Import,
  Type,
  Code2,
  Plus,
  X,
  Eye
} from 'lucide-react';
import { Message, Contact, CampaignSequence } from '../types';
import { replaceVariables } from '../utils';
import { cn } from '@/lib/utils';
import { fetchCampaignSequence } from '../services/campaignService';

interface MessagesSectionProps {
  messages: Message[];
  isLoading: boolean;
  selectedCampaignId: string;
  campaignName?: string;
  onMessageDelete: (messageId: string) => Promise<void>;
  onMessageEdit: (messageId: string, subject: string, body: string, metadata?: any, sync?: boolean) => Promise<void>;
  messageDirection?: string;
  onDirectionChange?: (direction: string) => void;
  messagesSort?: string;
  onSortChange?: (sort: string) => void;
  contactInfo?: Contact;
}

const MessagesSection: React.FC<MessagesSectionProps> = ({
  messages,
  isLoading,
  selectedCampaignId,
  campaignName,
  onMessageDelete,
  onMessageEdit,
  messageDirection = 'all',
  onDirectionChange,
  messagesSort = 'newest',
  onSortChange,
  contactInfo
}) => {
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editMessageSubject, setEditMessageSubject] = useState('');
  const [editMessageBody, setEditMessageBody] = useState('');
  const [editVariables, setEditVariables] = useState<Record<string, string>>({});
  const [sequenceVariables, setSequenceVariables] = useState<Record<string, string>>({});
  const [loadingSequence, setLoadingSequence] = useState(false);
  const [currentSequence, setCurrentSequence] = useState<CampaignSequence | null>(null);
  const [selectedVariant, setSelectedVariant] = useState<string>('');
  const [editSaving, setEditSaving] = useState(false);
  const [subjectRichMode, setSubjectRichMode] = useState(false);
  const [bodyRichMode, setBodyRichMode] = useState(true);
  const [newVariableKey, setNewVariableKey] = useState('');
  const [newVariableValue, setNewVariableValue] = useState('');
  const [variableRichModes, setVariableRichModes] = useState<Record<string, boolean>>({});
  const [newVariableRichMode, setNewVariableRichMode] = useState(false);
  const editEditorRef = useRef<RichTextEditorRef>(null);
  const subjectEditorRef = useRef<RichTextEditorRef>(null);
  const variableEditorsRef = useRef<Record<string, RichTextEditorRef>>({});
  const newVariableEditorRef = useRef<RichTextEditorRef>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Load campaign sequence when editing a message that has a campaign ID
  const loadCampaignSequence = async (campaignId: string, variantLabel?: string) => {
    if (!campaignId) return;
    
    setLoadingSequence(true);
    try {
      const sequenceData = await fetchCampaignSequence(campaignId);
      setCurrentSequence(sequenceData);
      
      if (sequenceData?.sequence_variants?.length) {
        // Find the appropriate variant
        const variant = variantLabel && sequenceData.sequence_variants.find(v => v.variant_label === variantLabel)
          ? sequenceData.sequence_variants.find(v => v.variant_label === variantLabel)
          : sequenceData.sequence_variants[0];
        
        if (variant) {
          setSelectedVariant(variant.variant_label);
          
          // IMPORTANT: Extract variables from ALL sequence variants, not just the selected one
          const allVarsFromAllSequences = extractAllVariablesFromAllSequences(sequenceData.sequence_variants);
          
          // Additionally, extract any template variables defined in the sequence
          const templateVars = sequenceData.variables?.map(v => v.name) || [];
          
          // Combine all variables from all sequences and template variables
          const allVars = [...new Set([...allVarsFromAllSequences, ...templateVars])];
          
          // IMPORTANT: First get the current message metadata variables
          const message = messages.find(m => m.message_id === editingMessageId);
          const messageVariables = message?.metadata?.variables || {};
          
          // Store a copy of the current edit variables
          const currentEditVariables = { ...editVariables };
          
          // CRITICAL FIX: Only create sequence defaults for variables that DON'T exist in metadata
          // This ensures sequence variables don't overwrite metadata values
          const seqVariables: Record<string, string> = {};
          allVars.forEach(variable => {
            // Only add to seqVariables if NOT already in currentEditVariables
            if (currentEditVariables[variable] === undefined) {
              const sequenceVar = sequenceData.variables?.find(v => v.name === variable);
              seqVariables[variable] = sequenceVar?.default_value || `[${variable}]`;
            }
          });
          
          // Store sequence defaults in state - only for reference/importing
          setSequenceVariables(seqVariables);
          
          // IMPORTANT: DON'T combine or modify the existing editVariables
          // This way metadata values are preserved
        }
      }
    } catch (error) {
      console.error('Error loading sequence:', error);
      toast.error('Failed to load campaign sequence');
    } finally {
      setLoadingSequence(false);
    }
  };

  // NEW FUNCTION: Extract variables from ALL sequence variants
  const extractAllVariablesFromAllSequences = (sequenceVariants: any[]): string[] => {
    const allVariables: string[] = [];
    
    sequenceVariants.forEach(variant => {
      // Extract variables from each variant's subject and body
      const subjectVars = extractVariables(variant.subject || '');
      const bodyVars = extractVariables(variant.email_body || '');
      
      // Add to comprehensive list
      allVariables.push(...subjectVars, ...bodyVars);
    });
    
    // Return unique variables only
    return [...new Set(allVariables)];
  };

  // Import sequence variables to the current edit
  const importSequenceVariables = () => {
    if (Object.keys(sequenceVariables).length === 0) {
      toast.info('No sequence variables to import');
      return;
    }
    
    // Get the current message to check for metadata values
    const message = messages.find(m => m.message_id === editingMessageId);
    const messageVariables = message?.metadata?.variables || {};
    
    // Only import sequence variables if they don't already have a metadata value
    const mergedVariables = { ...editVariables };
    
    Object.entries(sequenceVariables).forEach(([key, value]) => {
      // Only set from sequence if not already set from metadata
      if (messageVariables[key] === undefined) {
        mergedVariables[key] = value;
      }
    });
    
    setEditVariables(mergedVariables);
    toast.success('Sequence variables imported');
  };

  // Start editing a message
  const startEditMessage = (message: Message) => {
    setEditingMessageId(message.message_id);
    setEditMessageSubject(message.subject || '');
    setEditMessageBody(message.body || '');
    
    // Determine if content is HTML-rich to set initial editor modes
    const hasHtmlSubject = message.subject?.includes('<') || false;
    const hasHtmlBody = message.body?.includes('<') || false;
    setSubjectRichMode(hasHtmlSubject);
    setBodyRichMode(hasHtmlBody);
    
    // Reset sequence data first
    setCurrentSequence(null);
    setSequenceVariables({});
    
    // Extract variables from metadata first - critical to set these before loading sequence
    const messageVariables = message.metadata?.variables || {};
    
    // Set the metadata variables immediately
    setEditVariables(messageVariables);
    
    // Determine if variables contain HTML and set rich modes
    const varRichModes: Record<string, boolean> = {};
    Object.entries(messageVariables).forEach(([key, value]) => {
      varRichModes[key] = typeof value === 'string' && value.includes('<');
    });
    setVariableRichModes(varRichModes);
    
    // Load sequence if campaign ID exists
    if (message.smartlead_campaign_id) {
      const campaignId = message.smartlead_campaign_id;
      const variantLabel = message.metadata?.variant_label;
      
      if (campaignId) {
        setTimeout(() => {
          loadCampaignSequence(campaignId, variantLabel);
        }, 100);
      }
    }
    
    // Wait for the next render cycle then focus the editor
    setTimeout(() => {
      if (bodyRichMode && editEditorRef.current) {
        editEditorRef.current.focus();
      }
    }, 200);
  };

  // Preview content in iframe with variables applied
  useEffect(() => {
    if (iframeRef.current && iframeRef.current.contentDocument && editingMessageId) {
      const doc = iframeRef.current.contentDocument;
      const message = messages.find(m => m.message_id === editingMessageId);
      
      if (!message) return;
      
      // Get body content to preview
      const bodyContent = editMessageBody;
      
      // Start with sequence and edited variables
      const allVariables = { ...sequenceVariables, ...editVariables };
      
      // Always prioritize contact fields for personalization
      if (contactInfo?.first_name) {
        allVariables.first_name = contactInfo.first_name;
      }
      if (contactInfo?.last_name) {
        allVariables.last_name = contactInfo.last_name;
      }
      if (contactInfo?.email) {
        allVariables.email = contactInfo.email;
      }
      if (contactInfo?.company_name) {
        allVariables.company_name = contactInfo.company_name;
      }
      if (contactInfo?.job_title) {
        allVariables.job_title = contactInfo.job_title;
      }

      const processedContent = getPreviewContent(bodyContent, allVariables);
      
      // Add base styling to the iframe content
      const content = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body {
              margin: 0;
              padding: 16px;
              font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
              font-size: 14px;
              line-height: 1.6;
              color: #374151;
            }
            ul, ol {
              padding-left: 24px;
            }
            a {
              color: #2563eb;
            }
            .variable-highlight {
              background-color: #f0f9ff;
              border-bottom: 1px dashed #3b82f6;
              padding: 1px 2px;
            }
          </style>
        </head>
        <body>${processedContent}</body>
        </html>
      `;
      
      doc.open();
      doc.write(content);
      doc.close();
    }
  }, [editingMessageId, editMessageBody, editVariables, messages, sequenceVariables, contactInfo]);

  // Update all visible message iframes when contact info changes
  useEffect(() => {
    // Find all message iframes and update their content
    messages.forEach(message => {
      if (editingMessageId !== message.message_id) {
        const iframe = document.querySelector(`iframe[title="Message Content - ${message.message_id}"]`) as HTMLIFrameElement;
        if (iframe && iframe.contentDocument) {
          updateIframeContent(iframe, message);
        }
      }
    });
  }, [contactInfo, messages, editingMessageId]);

  // Cancel editing
  const cancelEditMessage = () => {
    setEditingMessageId(null);
    setEditMessageSubject('');
    setEditMessageBody('');
    setEditVariables({});
    setCurrentSequence(null);
    setSequenceVariables({});
    setSubjectRichMode(false);
    setBodyRichMode(true);
    setNewVariableKey('');
    setNewVariableValue('');
    setVariableRichModes({});
    setNewVariableRichMode(false);
    variableEditorsRef.current = {};
  };

  // Save edited message
  const saveEditMessage = async (sync: boolean = false) => {
    if (!editingMessageId) return;
    
    // Get content from editors if in rich mode
    let subjectContent = editMessageSubject;
    let bodyContent = editMessageBody;
    
    if (subjectRichMode && subjectEditorRef.current) {
      try {
        subjectContent = subjectEditorRef.current.getContent();
      } catch (error) {
        console.error('Error getting subject editor content:', error);
        subjectContent = editMessageSubject;
      }
    }
    
    if (bodyRichMode && editEditorRef.current) {
      try {
        bodyContent = editEditorRef.current.getContent();
      } catch (error) {
        console.error('Error getting body editor content:', error);
        bodyContent = editMessageBody;
      }
    }
    
    // Get variable content from rich text editors if applicable
    const processedVariables = { ...editVariables };
    Object.keys(editVariables).forEach(key => {
      if (variableRichModes[key] && variableEditorsRef.current[key]) {
        try {
          processedVariables[key] = variableEditorsRef.current[key].getContent();
        } catch (error) {
          console.error(`Error getting variable ${key} editor content:`, error);
          // Keep the current value if editor fails
        }
      }
    });
    
    // Handle new variable if it's in rich mode
    if (newVariableKey && newVariableRichMode && newVariableEditorRef.current) {
      try {
        const richContent = newVariableEditorRef.current.getContent();
        processedVariables[newVariableKey] = richContent;
      } catch (error) {
        console.error('Error getting new variable editor content:', error);
        processedVariables[newVariableKey] = newVariableValue;
      }
    }
    
    setEditSaving(true);
    try {
      // Get the original message to preserve metadata
      const originalMessage = messages.find(m => m.message_id === editingMessageId);
      const originalMetadata = originalMessage?.metadata || {};
      
      // Combine all variables (processed variables + any sequence variables not in processed variables)
      const combinedVariables = { ...processedVariables };
      
      // Add any sequence variables not already in processedVariables
      Object.entries(sequenceVariables).forEach(([key, value]) => {
        if (combinedVariables[key] === undefined) {
          combinedVariables[key] = value;
        }
      });
      
      // Prioritize contact fields from contactInfo if available
      if (contactInfo) {
        combinedVariables.first_name = contactInfo.first_name || '';
        combinedVariables.last_name = contactInfo.last_name || '';
        combinedVariables.email = contactInfo.email || '';
        combinedVariables.company_name = contactInfo.company_name || '';
        combinedVariables.job_title = contactInfo.job_title || '';
      }
      
      // Create updated metadata preserving all existing metadata and adding our updates
      const updatedMetadata = {
        ...originalMetadata,
        variables: combinedVariables,
        variant_label: currentSequence ? selectedVariant : originalMetadata.variant_label,
        last_edited_at: new Date().toISOString(),
        is_html: bodyRichMode || subjectRichMode || Object.values(variableRichModes).some(mode => mode),
        subject_is_html: subjectRichMode,
        body_is_html: bodyRichMode,
        variable_rich_modes: variableRichModes
      };
      
      await onMessageEdit(editingMessageId, subjectContent, bodyContent, updatedMetadata, sync);
      toast.success(sync ? 'Message updated and synced successfully' : 'Message updated successfully');
      cancelEditMessage();
    } catch (error) {
      console.error('Error in saveEditMessage:', error);
      toast.error(sync ? 'Failed to update and sync message' : 'Failed to update message');
    } finally {
      setEditSaving(false);
    }
  };

  // functions for variable extraction and handling
  const extractVariables = (template: string): string[] => {
    if (!template) return [];
    
    const matches = template.match(/{{\s*([^}]+)\s*}}/g) || [];
    return matches
      .map(match => match.replace(/^{{\s*|\s*}}$/g, ''))
      .filter((value, index, self) => self.indexOf(value) === index); // Unique values only
  };

  // Get variable value using the same logic as CampaignSequenceSection
  const getVariableValue = (
    variable: string, 
    contact: Contact | undefined | null,
    msgData: Message | {subject: string, body: string} = {subject: '', body: ''}
  ): string => {
    
    // Handle simple subject and body variables (these come from message data, not contact)
    if (variable === 'subject') {
      return 'subject' in msgData ? msgData.subject || '' : '';
    }
    
    if (variable === 'body') {
      return 'body' in msgData ? msgData.body || '' : '';
    }
    
    // Handle email template variables that end with _subject or _body
    if (variable.endsWith('_subject')) {
      // Return the actual subject that will be used
      return 'subject' in msgData ? msgData.subject || '' : '';
    }
    
    if (variable.endsWith('_body')) {
      // Return the actual body that will be used
      return 'body' in msgData ? msgData.body || '' : '';
    }
    
    // First check if the variable exists in message metadata
    if (msgData && 'metadata' in msgData && msgData.metadata?.variables && 
          msgData.metadata.variables[variable] !== undefined) {
        return msgData.metadata.variables[variable];
      }
    
    // Handle standard contact variables (these require contact data)
    if (!contact) return '';
    
    const variableMap: Record<string, string> = {
      first_name: contact?.first_name || '',
      last_name: contact?.last_name || '',
      email: contact?.email || '',
      company_name: contact?.company_name || '',
      job_title: contact?.job_title || '',
      phone_number: contact?.phone_number || '',
      company_website: contact?.company_website || '',
      industry: contact?.industry || ''
    };
    
    return variableMap[variable] || '';
  };

  // Group variables by type (contact vs custom)
  const groupVariables = (variables: string[]): { contactVars: string[], customVars: string[] } => {
    const contactVarNames = ['first_name', 'last_name', 'email', 'company_name', 'job_title', 'phone_number', 'company_website', 'industry'];
    const contactVars: string[] = [];
    const customVars: string[] = [];
    
    variables.forEach(variable => {
      if (contactVarNames.includes(variable) || 
          variable === 'subject' || variable === 'body') {
        contactVars.push(variable);
      } else {
        customVars.push(variable);
      }
    });
    
    return { contactVars, customVars };
  };

  // Get the preview content with variables replaced
  const getPreviewContent = (
    template: string,
    variables: Record<string, string> = {}
  ): string => {
    if (!template) return '';
    
    // Replace all variables in the template
    return template.replace(/{{\s*([^}]+)\s*}}/g, (match, variable) => {
      // Handle special variables first
      if (variable === 'body') {
        return editMessageBody || '';
      }
      if (variable === 'subject') {
        return editMessageSubject || '';
      }
      
      // Use provided variable value or fallback to placeholder
      return variables[variable] !== undefined ? variables[variable] : `[${variable}]`;
    });
  };

  // Handle variable change
  const handleVariableChange = (variable: string, value: string) => {
    setEditVariables(prev => {
      const updated = {
        ...prev,
        [variable]: value
      };
      return updated;
    });
  };

  // Toggle variable rich mode
  const toggleVariableRichMode = (variableKey: string, richMode: boolean) => {
    setVariableRichModes(prev => ({
      ...prev,
      [variableKey]: richMode
    }));
  };

  // Handle rich text change for variables
  const handleVariableRichChange = (variableKey: string, content: string) => {
    setEditVariables(prev => ({
      ...prev,
      [variableKey]: content
    }));
  };

  // Handle message deletion
  const handleDeleteMessage = async (messageId: string) => {
    if (!confirm('Are you sure you want to delete this message?')) return;
    
    try {
      await onMessageDelete(messageId);
    } catch (error) {
      console.error('Error in handleDeleteMessage:', error);
    }
  };

  // Reset all filters
  const resetFilters = () => {
    if (onDirectionChange) onDirectionChange('all');
    if (onSortChange) onSortChange('newest');
  };

  // Render filter summary
  const renderFilterSummary = () => {
    const filters = [];
    
    if (selectedCampaignId && selectedCampaignId !== 'all') {
      filters.push(`Campaign: ${campaignName || selectedCampaignId}`);
    }
    
    if (messageDirection !== 'all') {
      filters.push(`Direction: ${messageDirection === 'outbound' ? 'Sent' : 'Received'}`);
    }
    
    if (filters.length === 0) return null;
    
    return (
      <div className="flex flex-wrap gap-2 items-center mb-4">
        <Badge variant="outline" className="bg-blue-50 text-blue-700 flex items-center gap-1">
          <Filter size={12} />
          Filters:
        </Badge>
        {filters.map((filter, i) => (
          <Badge key={i} variant="outline" className="bg-gray-100">
            {filter}
          </Badge>
        ))}
        <Button 
          size="sm" 
          variant="outline"
          className="h-7 text-xs"
          onClick={resetFilters}
        >
          Clear All
        </Button>
      </div>
    );
  };

  // Add a new variable
  const addNewVariable = () => {
    if (!newVariableKey.trim()) {
      toast.error('Variable key is required');
      return;
    }
    
    if (editVariables[newVariableKey]) {
      toast.error('Variable already exists');
      return;
    }
    
    // Get content from rich editor if in rich mode
    let variableValue = newVariableValue;
    if (newVariableRichMode && newVariableEditorRef.current) {
      try {
        variableValue = newVariableEditorRef.current.getContent();
      } catch (error) {
        console.error('Error getting new variable editor content:', error);
        variableValue = newVariableValue;
      }
    }
    
    setEditVariables(prev => ({
      ...prev,
      [newVariableKey]: variableValue
    }));
    
    // Set rich mode for the new variable
    if (newVariableRichMode) {
      setVariableRichModes(prev => ({
        ...prev,
        [newVariableKey]: true
      }));
    }
    
    setNewVariableKey('');
    setNewVariableValue('');
    setNewVariableRichMode(false);
    toast.success('Variable added');
  };

  // Remove a variable
  const removeVariable = (key: string) => {
    setEditVariables(prev => {
      const updated = { ...prev };
      delete updated[key];
      return updated;
    });
    
    // Also remove rich mode state for this variable
    setVariableRichModes(prev => {
      const updated = { ...prev };
      delete updated[key];
      return updated;
    });
    
    // Remove editor ref if exists
    if (variableEditorsRef.current[key]) {
      delete variableEditorsRef.current[key];
    }
    
    toast.success('Variable removed');
  };

  // Update iframe content with proper error handling
  const updateIframeContent = (iframe: HTMLIFrameElement, message: Message) => {
    if (!iframe || !iframe.contentDocument) return;
    
    // Show loading state
    const loadingElement = document.getElementById(`loading-${message.message_id}`);
    if (loadingElement) {
      loadingElement.style.display = 'flex';
    }
    
    const doc = iframe.contentDocument;
    const processedContent = replaceVariables(message.body, contactInfo || null, message.metadata?.variables || {});
    
    // Create comprehensive HTML document
    const content = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            padding: 1rem;
            margin: 0;
            color: #333;
            background: white;
            word-wrap: break-word;
            overflow-wrap: break-word;
          }
          p { margin: 0 0 1em 0; }
          ul, ol { margin: 0 0 1em 1.5em; padding: 0; }
          li { margin-bottom: 0.5em; }
          img { max-width: 100%; height: auto; }
          a { color: #0070f3; text-decoration: none; }
          a:hover { text-decoration: underline; }
          
          /* Ensure proper list rendering */
          ul > li, ol > li {
            list-style-position: outside;
          }
          
          /* Handle nested content properly */
          li > p {
            margin: 0;
            display: inline;
          }
          
          /* Text alignment classes */
          .text-left { text-align: left; }
          .text-center { text-align: center; }
          .text-right { text-align: right; }
          .text-justify { text-align: justify; }
        </style>
      </head>
      <body>
        <div class="content-wrapper">
          ${processedContent || '<p style="color: #999; font-style: italic;">No content to display</p>'}
        </div>
      </body>
      </html>
    `;
    
    try {
      doc.open();
      doc.write(content);
      doc.close();
      
      // Hide loading state after content is loaded
      setTimeout(() => {
        if (loadingElement) {
          loadingElement.style.display = 'none';
        }
        
        // Ensure iframe content is properly sized
        if (iframe && iframe.contentDocument) {
          const body = iframe.contentDocument.body;
          if (body) {
            body.style.margin = '0';
            body.style.padding = '16px';
          }
        }
      }, 100);
      
    } catch (error) {
      console.error('Error updating iframe:', error);
      
      // Hide loading state on error
      if (loadingElement) {
        loadingElement.style.display = 'none';
      }
      
      // Fallback: write simple content
      try {
        doc.open();
        doc.write(`
          <html>
          <body style="font-family: sans-serif; padding: 16px; margin: 0; color: #333;">
            ${processedContent || '<p style="color: #999; font-style: italic;">Content unavailable</p>'}
          </body>
          </html>
        `);
        doc.close();
      } catch (fallbackError) {
        console.error('Fallback iframe update failed:', fallbackError);
      }
    }
  };

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg font-medium">Messages</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        {renderFilterSummary()}
        
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <RefreshCw className="h-10 w-10 text-gray-400 animate-spin mb-4" />
            <p className="text-gray-500">Loading messages...</p>
          </div>
        ) : messages.length > 0 ? (
          <div className="space-y-4">
            {messages.map(message => (
              <div 
                key={message.message_id}
                id={`message-${message.message_id}`}
                className={`p-4 rounded-lg ${
                  message.direction === 'outbound' ? 'bg-blue-50 ml-12' : 'bg-gray-50 mr-12'
                } group relative`}
              >
                <div className="flex justify-between items-start mb-2">
                  <div className="flex items-center">
                    <Avatar className={`h-8 w-8 ${message.direction === 'outbound' ? 'bg-blue-500' : 'bg-gray-500'} text-white`}>
                      <AvatarFallback>
                        {message.direction === 'outbound' ? 'A' : message.from_email?.charAt(0)?.toUpperCase() || '?'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="ml-2">
                      <div className="font-medium text-sm">
                        {message.direction === 'outbound' ? 'ANAX' : message.from_email || 'Unknown'}
                      </div>
                      <div className="text-xs text-gray-500">
                        To: {message.to_email || 'Unknown'}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <div className="text-xs text-gray-500">
                      {new Date(message.created_at).toLocaleString()}
                    </div>
                    {message.metadata?.campaign_template && (
                      <Badge variant="outline" className="mt-1 text-xs bg-green-50 text-green-700">
                        Template: {message.metadata.template_name || message.metadata.campaign_template}
                      </Badge>
                    )}
                    {message.smartlead_campaign_id && (
                      <Badge variant="outline" className="mt-1 text-xs bg-indigo-50 text-indigo-700">
                        Campaign: { campaignName || message.smartlead_campaign_id}
                      </Badge>
                    )}
                    {message.metadata?.lead_id && (
                      <Badge variant="outline" className="mt-1 text-xs bg-green-50 text-green-700">
                        Lead ID: {message.metadata.lead_id}
                      </Badge>
                    )}

                    {message.metadata?.variables && Object.keys(message.metadata.variables).length > 0 && (
                      <Badge variant="outline" className="mt-1 text-xs bg-purple-50 text-purple-700">
                        Variables: {Object.keys(message.metadata.variables).length}
                      </Badge>
                    )}
                  </div>
                </div>
                
                {editingMessageId === message.message_id ? (
                  <div className="space-y-6 mt-4">
                    {/* Campaign sequence indicator */}
                    {message.smartlead_campaign_id && (
                      <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                        <div>
                          <h3 className="text-sm font-medium">Campaign Variables</h3>
                          <p className="text-xs text-gray-500">
                            {currentSequence && Object.keys(sequenceVariables).length > 0
                              ? `${Object.keys(sequenceVariables).length} variables available from campaign`
                              : loadingSequence 
                                ? 'Loading campaign variables...'
                                : 'No campaign variables found'
                            }
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={importSequenceVariables}
                          disabled={Object.keys(sequenceVariables).length === 0 || loadingSequence}
                          className="text-xs h-8"
                        >
                          <ArrowDownToLine className="h-3.5 w-3.5 mr-1" />
                          Import Variables
                        </Button>
                      </div>
                    )}

                    {/* Variables editing */}
                    <div className="space-y-4">
                      {/* Subject Editing Section */}
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium text-gray-700">Subject</h4>
                          <div className="flex items-center space-x-1">
                            <Button
                              variant={subjectRichMode ? "default" : "outline"}
                              size="sm"
                              onClick={() => setSubjectRichMode(true)}
                              className="h-6 px-2 text-[10px]"
                            >
                              <Code2 className="h-2.5 w-2.5 mr-1" />
                              Rich
                            </Button>
                            <Button
                              variant={!subjectRichMode ? "default" : "outline"}
                              size="sm"
                              onClick={() => setSubjectRichMode(false)}
                              className="h-6 px-2 text-[10px]"
                            >
                              <Type className="h-2.5 w-2.5 mr-1" />
                              Text
                            </Button>
                          </div>
                        </div>
                        
                        {subjectRichMode ? (
                          <RichTextEditor
                            ref={subjectEditorRef}
                            value={editMessageSubject}
                            onChange={setEditMessageSubject}
                            height={80}
                            minimalControls={true}
                          />
                        ) : (
                          <Input
                            value={editMessageSubject}
                            onChange={(e) => setEditMessageSubject(e.target.value)}
                            className="text-sm"
                            placeholder="Subject"
                          />
                        )}
                      </div>

                      {/* Body Editing Section */}
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium text-gray-700">Message Body</h4>
                          <div className="flex items-center space-x-1">
                            <Button
                              variant={bodyRichMode ? "default" : "outline"}
                              size="sm"
                              onClick={() => setBodyRichMode(true)}
                              className="h-6 px-2 text-[10px]"
                            >
                              <Code2 className="h-2.5 w-2.5 mr-1" />
                              Rich
                            </Button>
                            <Button
                              variant={!bodyRichMode ? "default" : "outline"}
                              size="sm"
                              onClick={() => setBodyRichMode(false)}
                              className="h-6 px-2 text-[10px]"
                            >
                              <Type className="h-2.5 w-2.5 mr-1" />
                              Text
                            </Button>
                          </div>
                        </div>
                        
                        {bodyRichMode ? (
                          <RichTextEditor
                            ref={editEditorRef}
                            value={editMessageBody}
                            onChange={setEditMessageBody}
                            height={300}
                          />
                        ) : (
                          <textarea
                            value={editMessageBody}
                            onChange={(e) => setEditMessageBody(e.target.value)}
                            className="w-full p-3 border border-gray-200 rounded-md resize-vertical text-sm"
                            rows={10}
                            placeholder="Message body"
                          />
                        )}
                      </div>

                      {/* Extract variables from current templates */}
                      {(() => {
                        const subjectVars = extractVariables(editMessageSubject);
                        const bodyVars = extractVariables(editMessageBody);
                        const templateVariables = [...new Set([...subjectVars, ...bodyVars])];
                        
                        // Get all variables (from editVariables + any found in templates + sequence variables)
                        const allVariableKeys = [...new Set([
                          ...Object.keys(editVariables), 
                          ...templateVariables,
                          ...Object.keys(sequenceVariables)
                        ])];
                        const { contactVars, customVars } = groupVariables(allVariableKeys);
                        
                        return (
                          <>
                            {/* Header */}
                            <div className="flex items-center justify-between">
                              <div>
                                <h3 className="text-sm font-medium text-gray-900">Variables</h3>
                                <p className="text-xs text-gray-500 mt-1">{customVars.length} variables</p>
                              </div>
                              {contactVars.length > 0 && (
                                <div className="text-xs text-gray-500">
                                  Contact Variables (Read-only)
                                  <br />
                                  <span className="text-green-600">{contactVars.length} variables</span>
                                </div>
                              )}
                            </div>
                            
                            {/* Contact Variables - Simplified */}
                            {contactVars.length > 0 && (
                              <div className="bg-green-50/50 rounded-lg p-3 border border-green-100">
                                <div className="grid grid-cols-1 gap-2">
                                  {contactVars.map(key => (
                                    <div key={key} className="flex items-center justify-between text-xs">
                                      <span className="font-mono text-gray-700">{key}</span>
                                      <span className="text-gray-600 bg-white px-2 py-1 rounded border">
                                        {getVariableValue(key, contactInfo, { subject: editMessageSubject, body: editMessageBody })}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            {/* Custom Variables - Main Focus */}
                            <div className="space-y-3">
                              <h4 className="text-sm font-medium text-gray-700">Custom Variables (Editable)</h4>
                              
                              {customVars.length > 0 ? (
                                <div className="space-y-3">
                                  {customVars.map(key => {
                                    const currentValue = editVariables[key] || sequenceVariables[key] || getVariableValue(key, contactInfo, messages.find(m => m.message_id === editingMessageId));
                                    const isRichMode = variableRichModes[key] || false;
                                    const isInSubject = subjectVars.includes(key);
                                    const isInBody = bodyVars.includes(key);
                                    const isUsed = isInSubject || isInBody;
                                    
                                    return (
                                      <div key={key} className="space-y-2">
                                        {/* Variable header */}
                                        <div className="flex items-center justify-between">
                                          <div className="flex items-center space-x-2">
                                            <span className="font-mono text-sm text-gray-800">{key}</span>
                                            <div className="flex items-center space-x-1">
                                              <Badge className="text-[10px] px-1.5 py-0.5 h-4 bg-blue-50 text-blue-600">
                                                Custom
                                              </Badge>
                                              {isUsed ? (
                                                <Badge className="text-[10px] px-1.5 py-0.5 h-4 bg-green-50 text-green-600">
                                                  Used
                                                </Badge>
                                              ) : (
                                                <Badge className="text-[10px] px-1.5 py-0.5 h-4 bg-gray-50 text-gray-500">
                                                  Unused
                                                </Badge>
                                              )}
                                            </div>
                                          </div>
                                          
                                          <div className="flex items-center space-x-1">
                                            <Button
                                              variant={isRichMode ? "default" : "outline"}
                                              size="sm"
                                              onClick={() => toggleVariableRichMode(key, true)}
                                              className="h-6 px-2 text-[10px]"
                                            >
                                              <Code2 className="h-2.5 w-2.5 mr-1" />
                                              Rich
                                            </Button>
                                            <Button
                                              variant={!isRichMode ? "default" : "outline"}
                                              size="sm"
                                              onClick={() => toggleVariableRichMode(key, false)}
                                              className="h-6 px-2 text-[10px]"
                                            >
                                              <Type className="h-2.5 w-2.5 mr-1" />
                                              Text
                                            </Button>
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => removeVariable(key)}
                                              className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                                            >
                                              <X className="h-3 w-3" />
                                            </Button>
                                          </div>
                                        </div>
                                        
                                        {/* Variable value input */}
                                        {isRichMode ? (
                                          <RichTextEditor
                                            ref={(ref) => {
                                              if (ref) variableEditorsRef.current[key] = ref;
                                            }}
                                            value={currentValue}
                                            onChange={(content: string) => handleVariableRichChange(key, content)}
                                            height={120}
                                          />
                                        ) : (
                                          <Input
                                            value={currentValue}
                                            onChange={(e) => handleVariableChange(key, e.target.value)}
                                            className="text-sm h-9"
                                            placeholder={`Value for ${key}`}
                                          />
                                        )}
                                      </div>
                                    );
                                  })}
                                </div>
                              ) : (
                                <div className="text-center py-6 text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
                                  <Variable className="h-6 w-6 mx-auto text-gray-300 mb-2" />
                                  <div className="text-sm">No custom variables found</div>
                                </div>
                              )}
                            </div>
                          </>
                        );
                      })()}
                    </div>
                    
                    {/* Preview Section */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-gray-700">Preview</h4>
                      <div className="border rounded bg-white h-[300px] overflow-hidden">
                        <iframe 
                          ref={iframeRef}
                          className="w-full h-full border-none" 
                          title="Message Preview"
                        />
                      </div>
                      <p className="text-xs text-gray-500">
                        Preview of your message with variables replaced
                      </p>
                    </div>
                    
                    {/* Action buttons */}
                    <div className="flex gap-2 pt-4 border-t">
                      <Button size="sm" onClick={() => saveEditMessage(false)} disabled={editSaving} className="bg-blue-600 hover:bg-blue-700 text-white">
                        {editSaving ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <CheckCircle className="h-4 w-4 mr-2" />}
                        Save
                      </Button>
                      <Button 
                        size="sm" 
                        onClick={() => saveEditMessage(true)} 
                        disabled={editSaving}
                        className="bg-green-600 hover:bg-green-700 text-white"
                      >
                        <Send className="h-4 w-4 mr-2" />
                        Save & Sync
                      </Button>
                      <Button size="sm" variant="outline" onClick={cancelEditMessage} disabled={editSaving}>
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <>
                    {/* Email Subject */}
                    <div className="px-2 pt-2 pb-2">
                      <h3 className="text-lg font-medium text-gray-800">
                        {replaceVariables(message.subject, contactInfo || null, message.metadata?.variables || {})}
                      </h3>
                    </div>
                    
                    {/* Email Body Content */}
                    <div className="border rounded bg-white h-[300px] overflow-hidden relative">
                      <iframe 
                        key={`message-${message.message_id}`}
                        className="w-full h-full border-none" 
                        title={`Message Content - ${message.message_id}`}
                        onLoad={(e) => {
                          const iframe = e.target as HTMLIFrameElement;
                          updateIframeContent(iframe, message);
                        }}
                      />
                      <div className="absolute inset-0 bg-gray-100 animate-pulse flex items-center justify-center text-gray-500 text-sm"
                           style={{ display: 'none' }}
                           id={`loading-${message.message_id}`}>
                        Loading message content...
                      </div>
                    </div>
                  </>
                )}
                
                <div className="flex gap-2 absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      startEditMessage(message);
                    }}
                    className="text-blue-600"
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      handleDeleteMessage(message.message_id);
                    }}
                    className="text-red-600"
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Delete
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <MessageCircle className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            {selectedCampaignId ? (
              <>
                <p className="text-lg font-medium text-gray-600 mb-2">No messages found</p>
                <p className="text-gray-500 max-w-md mx-auto">
                  There are no messages available for the selected campaign.
                </p>
              </>
            ) : (
              <>
                <p className="text-lg font-medium text-gray-600 mb-2">No messages available</p>
                <p className="text-gray-500 max-w-md mx-auto">
                  Select a campaign to view messages or try adjusting your filters.
                </p>
              </>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MessagesSection; 