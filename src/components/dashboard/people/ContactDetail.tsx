import React, { useEffect, useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, Ta<PERSON>Content } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Building2,
  Phone,
  Mail,
  MapPin,
  Calendar,
  MessageSquare,
  Briefcase,
  BarChart,
  Clock,
  Send,
  ArrowLeft,
  Link,
  ExternalLink,
  CheckCircle,
  FileText,
  Users,
  Globe,
  Activity,
  DollarSign,
  Linkedin as LinkedinIcon,
  Database,
  FileSearch,
  MessageCircle,
  AlertCircle,
  RefreshCw,
  CheckCircle2,
  Trash2,
  Edit,
  SaveIcon,
  X,
  Target,
  Newspaper,
  Sparkles,
} from "lucide-react";
import { toast } from "sonner";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import TrainingDataTab from "./TrainingDataTab";
import RichTextEditor, {
  RichTextEditorRef,
} from "@/components/common/RichTextEditor";
import { Contact } from "./shared/types";
import ContactDetailHeader from "./detail-components/ContactDetailHeader";
import ContactOverviewTab from "./detail-components/ContactOverviewTab";
import ContactOverviewV2Tab from "./detail-components/ContactOverviewV2Tab";
import ContactCompanyTab from "./detail-components/ContactCompanyTab";
import InvestmentCriteriaSliderV2 from "../investment-criteria/InvestmentCriteriaSliderV2";
import ContactEditTab from "./detail-components/ContactEditTab";
import ContactLinkedDealsTab from "./detail-components/ContactLinkedDealsTab";

// Remove React Query import
// import { useQuery } from '@tanstack/react-query';
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import ContactCampaignTag from "./ContactCampaignTag";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";

interface ContactDetailProps {
  contactId: number | string;
  onBack: () => void;
}

interface CompanyData {
  company_id?: number;
  name?: string;
  company_name?: string;
  company_linkedin?: string;
  summary?: string;
  founded_year?: number;
  contact_count?: string | number;
  risk_factors?: string[];
  recent_developments?: string[];
  engagement_opportunities?: string[];
  has_investment_criteria?: boolean;
  investment_criteria_count?: number;
  overview?: {
    structure_history?: string;
    executive_contacts?: ExecutiveContact[];
    investment_program?: InvestmentProgram;
    lending_program?: LendingProgram;
    capital_commitments?: CapitalCommitments;
    investment_strategy?: string[];
    recent_transactions?: Transaction[];
    hold_horizon?: string;
  };
  scraped_data?: ScrapedData;
  scraped_contacts?: ScrapedContact[];
}

interface ScrapedData {
  companyProfile?: {
    companyName?: string;
    companyType?: string;
    companyWebsite?: string;
    businessModel?: string;
    fundSize?: string;
    aum?: string;
    numberOfProperties?: number;
    headquarters?: string;
    numberOfOffices?: number;
    officeLocations?: string[];
    foundedYear?: number;
    numberOfEmployees?: string;
    investmentFocus?: string[];
    geographicFocus?: string[];
  };
  executiveTeam?: Array<{
    first_name: string;
    last_name: string;
    full_name: string;
    title: string;
    headline?: string;
    seniority?: string;
    email?: string;
    personal_email?: string;
    email_status?: string;
    linkedin_url?: string;
    contact_city?: string;
    contact_state?: string;
    contact_country?: string;
    phone?: string;
    bio?: string;
    category?: string;
  }>;
  recentDeals?: Array<{
    property?: string;
    location?: string;
    dealType?: string;
    amount?: string;
    date?: string;
    propertyType?: string;
    squareFeet?: string;
    units?: number;
  }>;
  investmentStrategy?: {
    mission?: string;
    approach?: string;
    targetReturn?: string;
    propertyTypes?: string[];
    strategies?: string[];
    assetClasses?: string[];
    valueCreation?: string[];
  };
  investmentCriteria?: {
    targetMarkets?: string[];
    dealSize?: string;
    minimumDealSize?: string;
    maximumDealSize?: string;
    holdPeriod?: string;
    riskProfile?: string;
    propertyTypes?: string[];
    propertySubcategories?: string[];
    assetTypes?: string[];
    loanTypes?: string[];
  };
  capitalSources?: string[];
  financialProducts?: Array<{
    productType?: string;
    terms?: string[];
    typicalAmount?: string;
    description?: string;
  }>;
  trackRecord?: {
    totalTransactions?: string;
    totalSquareFeet?: string;
    totalUnits?: string;
    historicalReturns?: string;
    portfolioValue?: string;
  };
  partnerships?: Array<{
    partnerName?: string;
    relationshipType?: string;
    description?: string;
  }>;
  contactInfo?: {
    website?: string;
    mainPhone?: string;
    mainEmail?: string;
    socialMedia?: {
      linkedin?: string;
      twitter?: string;
      facebook?: string;
      instagram?: string;
    };
  };

}

interface ScrapedContact {
  first_name?: string;
  last_name?: string;
  full_name?: string;
  title?: string;
  seniority?: string;
  category?: string;
  phone?: string;
  email?: string;
  personal_email?: string;
  email_status?: string;
  contact_city?: string;
  contact_state?: string;
  contact_country?: string;
  linkedin_url?: string;
  headline?: string;
  bio?: string;
}

interface ActivityProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  time: string;
}

interface Overview {
  executive_contacts?: ExecutiveContact[];
  investment_program?: InvestmentProgram;
  lending_program?: LendingProgram;
  capital_commitments?: CapitalCommitments;
  investment_strategy?: string[];
  recent_transactions?: Transaction[];
  structure_history?: string;
  recent_developments?: string[];
  contact_count?: number;
  company_linkedin?: string;
}

interface ExecutiveContact {
  name: string;
  title: string;
  email?: string;
  phone?: string;
}

interface InvestmentProgram {
  equity_size_range?: string;
  asset_type?: string[];
  deal_structure?: string[];
  property_types?: string[];
  geography?: string;
  hold_period?: string;
}

interface LendingProgram {
  program_name?: string;
  loan_size_range?: string;
  loan_types?: string[];
  geographic_focus?: string;
  property_types?: string[];
}

interface CapitalCommitments {
  debt_range?: string;
  equity_range?: string;
}

interface Transaction {
  asset_type: string;
  close_date?: string;
  location: string;
  capital_deployed?: string;
}

function normalizeScrapedData(data: any): ScrapedData {
  if (!data) return {};
  return {
    companyProfile: {
      companyName: data.companyname,
      companyType: data.companytype,
      companyWebsite: data.website,
      businessModel: data.businessmodel,
      fundSize: data.fundsize,
      aum: data.aum,
      numberOfProperties: data.numberofproperties,
      headquarters: data.headquarters,
      numberOfOffices: data.numberofoffices,
      foundedYear: data.foundedyear,
      numberOfEmployees: data.numberofemployees,
      investmentFocus: data.investmentfocus,
      geographicFocus: data.geographicfocus,
    },
    investmentStrategy: {
      mission: data.mission,
      approach: data.approach,
      targetReturn: data.targetreturn,
      propertyTypes: data.propertytypes,
      strategies: data.strategies,
      assetClasses: data.assetclasses,
      valueCreation: data.valuecreation,
    },
    investmentCriteria: {
      targetMarkets: data.targetmarkets,
      dealSize: data.dealsize,
      minimumDealSize: data.minimumdealsize,
      maximumDealSize: data.maximumdealsize,
      holdPeriod: data.holdperiod,
      riskProfile: data.riskprofile,
      propertyTypes:
        data.investment_criteria_property_types || data.propertytypes,
      propertySubcategories:
        data.investment_criteria_property_subcategories ||
        data.propertysubcategories,
      assetTypes: data.investment_criteria_asset_types || data.assettypes,
      loanTypes: data.investment_criteria_loan_types || data.loantypes,
    },
    contactInfo: {
      website: data.website,
      mainPhone: data.mainphone,
      mainEmail: data.mainemail,
      socialMedia: data.socialmedia,
    },
    financialProducts: data.financialproducts,
    capitalSources: data.capitalsources,
    partnerships: data.partnerships,
    recentDeals: data.recentdeals,
    trackRecord: {
      totalTransactions: data.totaltransactions,
      totalSquareFeet: data.totalsquarefeet,
      totalUnits: data.totalunits,
      historicalReturns: data.historicalreturns,
      portfolioValue: data.portfoliovalue,
    },
  };
}

// Add new interfaces for threads and messages
export interface Thread {
  thread_id: string;
  subject: string;
  status: string;
  created_at: string;
  updated_at: string;
  metadata?: {
    source?: string;
    campaign_id?: string;
  };
  messages: Message[];
}

export interface Message {
  message_id: string;
  thread_id: string;
  from_email: string;
  to_email: string;
  subject: string;
  direction: string;
  role: string;
  body: string;
  created_at: string;
  sent_at: string;
  metadata?: {
    lead_id?: string;
    campaign_id?: string;
    source?: string;
    smartlead_response?: any;
  };
}

// Add SmartleadData interface
interface SmartleadData {
  contact: {
    contact_id: number;
    email: string;
    smartlead_lead_id?: string;
    smartlead_status?: string;
    last_email_sent_at?: string;
  };
  threads: any[];
  campaigns: any[];
  hasSmartleadData: boolean;
}

// Types for Fireflies transcripts
interface FirefliesTranscript {
  id: string;
  provider_transcript_id: string;
  title: string;
  meeting_date: string;
  duration: number;
  participants: string[];
  transcript_text: string;
  sentences: Array<{
    text: string;
    raw_text: string;
    speaker_name: string;
    speaker_id: string;
    start_time: string;
    end_time: string;
    index?: number;
    ai_filters?: any;
  }>;
  account_name: string;
  created_at: string;
  updated_at: string;
}

interface EmailDraft {
  subject: string;
  body: string;
}

// Augment the main Contact interface with nested data structures
export interface DetailedContact extends Contact {
  company_data?: CompanyData;
  recent_activities?: {
    interaction_type: string;
    interaction_date: string;
    notes: string;
  }[];
}

const ContactDetail: React.FC<ContactDetailProps> = ({ contactId, onBack }) => {
  const router = useRouter();
  const [contact, setContact] = useState<DetailedContact | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>("overview-v2");
  const [company, setCompany] = useState<any | null>(null);
  const [showImage, setShowImage] = useState<boolean>(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<boolean>(false);
  const [threads, setThreads] = useState<Thread[]>([]);
  const [smartleadData, setSmartleadData] = useState<SmartleadData | null>(
    null
  );
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editedContact, setEditedContact] = useState<DetailedContact | null>(
    null
  );
  const [expandedDealId, setExpandedDealId] = useState<string | number | null>(
    null
  );
  const [isMatching, setIsMatching] = useState(false);

  // Add state for threads and messages
  const [loadingThreads, setLoadingThreads] = useState(true);
  const [selectedThreadId, setSelectedThreadId] = useState<string | null>(null);
  const [messagesSort, setMessagesSort] = useState("newest");

  // Smartlead states
  const [smartleadLoading, setSmartleadLoading] = useState(false);
  const [smartleadSyncing, setSmartleadSyncing] = useState(false);
  const [smartleadError, setSmartleadError] = useState<string | null>(null);
  const [sortOption, setSortOption] = useState("updated_at");
  const [sortOrder, setSortOrder] = useState("desc");

  // Add state for message deletion
  const [messageToDelete, setMessageToDelete] = useState<string | null>(null);

  // Add state for email editor
  const [emailDraft, setEmailDraft] = useState<EmailDraft>({
    subject: "",
    body: "",
  });
  const [draftSaving, setDraftSaving] = useState(false);
  const [draftSaved, setDraftSaved] = useState(false);

  // Add state for editing messages
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editMessageSubject, setEditMessageSubject] = useState("");
  const [editMessageBody, setEditMessageBody] = useState("");
  const [editSaving, setEditSaving] = useState(false);
  const editTextareaRef = useRef<HTMLTextAreaElement>(null);
  // Add state for message editing
  const [messageContent, setMessageContent] = useState<string>("");
  const messageEditorRef = useRef<RichTextEditorRef>(null);

  // Add after other useState declarations
  const [gmailAccounts, setGmailAccounts] = useState<any[]>([]);
  const [gmailThreads, setGmailThreads] = useState<any[]>([]);
  const [expandedGmailThread, setExpandedGmailThread] = useState<string | null>(
    null
  );
  const [gmailThreadMessages, setGmailThreadMessages] = useState<{
    [threadId: string]: any[];
  }>({});
  const [loadingGmailThreads, setLoadingGmailThreads] = useState(false);
  const [loadingMoreThreads, setLoadingMoreThreads] = useState(false);
  const [threadsPage, setThreadsPage] = useState(1);
  const [hasMoreThreads, setHasMoreThreads] = useState(false);

  // Restore state
  const [matchingDeals, setMatchingDeals] = useState<any[]>([]);
  const [matchingCriteria, setMatchingCriteria] = useState<any>(null);
  const [isLoadingMatchingDeals, setIsLoadingMatchingDeals] = useState(false);
  const [matchingDealsFiltering, setMatchingDealsFiltering] = useState<any>(null);
  const [showAllDealMatches, setShowAllDealMatches] = useState(false);

  // Add state for news matching
  const [matchingNews, setMatchingNews] = useState<any[]>([]);
  const [matchingNewsLoading, setMatchingNewsLoading] = useState(false);
  const [matchingNewsError, setMatchingNewsError] = useState<string | null>(null);
  const [expandedNewsId, setExpandedNewsId] = useState<string | number | null>(null);
  const [fetchedNewsForContact, setFetchedNewsForContact] = useState<string | null>(null);

  // Add state for message sort order
  const [messageSortOrder, setMessageSortOrder] = useState<'asc' | 'desc'>('desc');

  // Add state for contact processing
  type ContactProcessingStage = 'email_validation' | 'contact_enrichment_v2' | 'contact_investment_criteria' | 'email_generation';
  
  const [processingJobs, setProcessingJobs] = useState([
    { stage: 'email_validation' as ContactProcessingStage, isExecuting: false },
    { stage: 'contact_enrichment_v2' as ContactProcessingStage, isExecuting: false },
    { stage: 'contact_investment_criteria' as ContactProcessingStage, isExecuting: false },
    { stage: 'email_generation' as ContactProcessingStage, isExecuting: false },
  ]);

  // Add state for categorized criteria
  const [categorizedCriteria, setCategorizedCriteria] = useState<any>(null);
  const [isLoadingCategorizedCriteria, setIsLoadingCategorizedCriteria] = useState(false);
  
  // Add state for CRM mode
  const [isCrmMode, setIsCrmMode] = useState(false);

  // Contact transcripts state
  const [contactTranscripts, setContactTranscripts] = useState<FirefliesTranscript[]>([]);
  const [isLoadingTranscripts, setIsLoadingTranscripts] = useState(false);

  // Contact processing function
  const executeContactProcessing = async (stage: ContactProcessingStage) => {
    if (!contactId) return;

    const contactIdNumber = typeof contactId === 'string' ? parseInt(contactId) : contactId;
    
    // Update processing state
    setProcessingJobs(prev => 
      prev.map(job => 
        job.stage === stage 
          ? { ...job, isExecuting: true }
          : job
      )
    );

    try {
      const response = await fetch('/api/processing/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'execute_manual',
          stage,
          options: {
            multiIds: [contactIdNumber],
            filters: {}
          }
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.success(`Successfully triggered ${getStageConfig(stage).title} for this contact`);
      } else {
        toast.error(`Failed to trigger ${getStageConfig(stage).title}: ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Failed to execute contact processing:', error);
      toast.error(`Failed to trigger ${getStageConfig(stage).title}: ${(error as Error).message}`);
    } finally {
      setProcessingJobs(prev => 
        prev.map(job => 
          job.stage === stage 
            ? { ...job, isExecuting: false }
            : job
        )
      );
    }
  };

  const getStageConfig = (stage: ContactProcessingStage) => {
    switch (stage) {
      case 'email_validation':
        return { title: 'Email Validation', description: 'Validate contact email address' };

      case 'contact_enrichment_v2':
        return { title: 'Contact Enrichment V2', description: 'Enhanced contact enrichment with additional fields' };
      case 'contact_investment_criteria':
        return { title: 'Investment Criteria', description: 'Extract personalized investment criteria based on role and company data' };
      case 'email_generation':
        return { title: 'Email Generation', description: 'Generate email content for contact' };
      default:
        return { title: 'Unknown', description: 'Unknown processing stage' };
    }
  };

  const fetchContactDetails = async () => {
      try {
        setLoading(true);
        // Ensure contactId is properly formatted whether it's a number or string
        const formattedContactId =
          typeof contactId === "number" ? contactId : contactId;
        console.log("Fetching contact details for ID:", formattedContactId);

        const response = await fetch(`/api/contacts/${formattedContactId}`);

        if (!response.ok) {
          const errorData = await response.text();
          console.error("Server response:", response.status, errorData);
          throw new Error(`Server returned ${response.status}: ${errorData}`);
        }

        const data = await response.json();
        if (!data.recent_activities) {
          data.recent_activities = [];
        }

        // console.log("Fetched contact details:", data);
        setContact(data);

        // If the contact has a company_id, fetch company data directly
        if (data.company_id) {
          await fetchCompanyById(data.company_id);
        }
        // Otherwise, try to fetch by company name if available
        else if (data.company_name) {
          await fetchCompanyByName(data.company_name);
        } else {
          setLoading(false);
        }
      } catch (error) {
        console.error("Failed to fetch contact details:", error);
        setLoading(false);
      }
    };

    const fetchCompanyById = async (companyId: number) => {
      try {
        setLoading(true);
        console.log("Fetching company data for ID:", companyId);

        const response = await fetch(`/api/companies/${companyId}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch company data: ${response.status}`);
        }

        const companyData = await response.json();

        // console.log("Fetched company data by ID:", companyData);

        // Update the contact with company data
        setContact((prevContact) => {
          if (!prevContact) return null;
          return {
            ...prevContact,
            company_data: companyData,
          };
        });

        // Also set in the company state for backward compatibility
        setCompany(companyData);
      } catch (error) {
        console.error("Failed to fetch company data by ID:", error);
      } finally {
        setLoading(false);
      }
    };

    const fetchCompanyByName = async (companyName: string) => {
      try {
        setLoading(true);
        console.log("Searching company by name:", companyName);

        // First try to find company by name
        const response = await fetch(
          `/api/companies/search?q=${encodeURIComponent(companyName)}`
        );

        if (!response.ok) {
          throw new Error(`Failed to fetch company data: ${response.status}`);
        }

        const companies = await response.json();

        if (companies && companies.length > 0) {
          // Get full company details for the first match
          await fetchCompanyById(companies[0].company_id);
        } else {
          console.log("No companies found with name:", companyName);
          setLoading(false);
        }
      } catch (error) {
        console.error("Failed to fetch company by name:", error);
        setLoading(false);
      }
    };

  useEffect(() => {
    if (contactId) {
      fetchContactDetails();
      // Reset news data when contact changes
      setMatchingNews([]);
      setFetchedNewsForContact(null);
    }
  }, [contactId]);

  // Add new useEffect for fetching messages
  useEffect(() => {
    const fetchThreadsAndMessages = async () => {
      if (!contactId) return;

      try {
        setLoadingThreads(true);
        // Use the same formatting for contactId
        const formattedContactId =
          typeof contactId === "number" ? contactId : contactId;
        console.log(
          "Fetching threads and messages for contact:",
          formattedContactId
        );

        const response = await fetch(
          `/api/contacts/${formattedContactId}/messages`
        );

        if (!response.ok) {
          const errorData = await response.text();
          console.error("Server response:", response.status, errorData);
          throw new Error(`Server returned ${response.status}: ${errorData}`);
        }

        const data = await response.json();
        // console.log("Fetched threads and messages:", data);
        setThreads(data.threads || []);

        // Select the first thread by default if available
        if (data.threads && data.threads.length > 0) {
          setSelectedThreadId(data.threads[0].thread_id);
        }
      } catch (error) {
        console.error("Failed to fetch threads and messages:", error);
      } finally {
        setLoadingThreads(false);
      }
    };

    fetchThreadsAndMessages();
  }, [contactId]);

  // Add new function to fetch Smartlead data
  const fetchSmartleadData = async () => {
    if (!contactId) return;

    try {
      setSmartleadLoading(true);
      setSmartleadError(null);

      // Format contactId consistently
      const formattedContactId =
        typeof contactId === "number" ? contactId : contactId;
      const response = await fetch(
        `/api/smartlead/contacts/${formattedContactId}/sync?sort=${sortOption}&order=${sortOrder}`
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Server response error:", response.status, errorText);
        setSmartleadError(
          `Failed to fetch Smartlead data: Server returned ${response.status}`
        );
        return;
      }

      const data = await response.json();
      // console.log("Fetched Smartlead data:", data);
      setSmartleadData(data);
    } catch (error) {
      console.error("Failed to fetch Smartlead data:", error);
      setSmartleadError(
        `Failed to fetch Smartlead data: ${(error as Error).message}`
      );
    } finally {
      setSmartleadLoading(false);
    }
  };

  const fetchContactTranscripts = async () => {
    if (!contactId) return;
    setIsLoadingTranscripts(true);
    try {
      const response = await fetch(`/api/contacts/${contactId}/transcripts`);
      if (response.ok) {
        const data = await response.json();
        setContactTranscripts(data.transcripts || []);
      }
    } catch (error) {
      console.error("Error fetching contact transcripts:", error);
    } finally {
      setIsLoadingTranscripts(false);
    }
  };

  // Add function to trigger Smartlead sync
  const triggerSmartleadSync = async (
    campaignId?: string,
    subject?: string,
    body?: string
  ) => {
    if (!contactId) return;

    try {
      setSmartleadSyncing(true);
      setSmartleadError(null);

      // Build payload based on provided parameters
      const payload: any = {};
      if (campaignId) payload.campaign_id = campaignId;
      if (subject && body) {
        payload.subject = subject;
        payload.body = body;
      }

      // Format contactId consistently
      const formattedContactId =
        typeof contactId === "number" ? contactId : contactId;
      const response = await fetch(
        `/api/smartlead/contacts/${formattedContactId}/sync`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Server response error:", response.status, errorData);
        setSmartleadError(
          errorData.error ||
            `Failed to sync with Smartlead: Server returned ${response.status}`
        );
        return;
      }

      const data = await response.json();
      console.log("Smartlead sync result:", data);

      // Refresh Smartlead data after sync
      await fetchSmartleadData();

      // Update contact data with new Smartlead info
      if (contact && data.contact) {
        setContact({
          ...contact,
          smartlead_lead_id: data.contact.smartlead_lead_id,
          smartlead_status: data.contact.smartlead_status,
        });
      }
    } catch (error) {
      console.error("Failed to sync with Smartlead:", error);
      setSmartleadError(
        `Failed to sync with Smartlead: ${(error as Error).message}`
      );
    } finally {
      setSmartleadSyncing(false);
    }
  };

  // Add useEffect to fetch Smartlead data
  useEffect(() => {
    if (contactId && !loading) {
      fetchSmartleadData();
    }
  }, [contactId, loading, sortOption, sortOrder]);

  // Add delete message function
  const deleteMessage = async () => {
    if (!messageToDelete) return;

    try {
      setLoadingThreads(true);

      const response = await fetch(`/api/messages/${messageToDelete}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `Failed to delete message: ${response.status} - ${errorText}`
        );
      }

      // Update the threads data by removing the deleted message
      setThreads(
        threads.map((thread) => {
          if (
            thread.messages.some((msg) => msg.message_id === messageToDelete)
          ) {
            return {
              ...thread,
              messages: thread.messages.filter(
                (msg) => msg.message_id !== messageToDelete
              ),
            };
          }
          return thread;
        })
      );

      toast.success("Message deleted successfully");
    } catch (error) {
      console.error("Error deleting message:", error);
      toast.error(`Failed to delete message: ${(error as Error).message}`);
    } finally {
      setMessageToDelete(null);
      setLoadingThreads(false);
    }
  };

  // Start editing a message
  const startEditMessage = (message: Message) => {
    setEditingMessageId(message.message_id);
    setEditMessageSubject(message.subject || "");
    setEditMessageBody(message.body || "");
    setTimeout(() => {
      editTextareaRef.current?.focus();
    }, 100);
  };

  // Cancel editing
  const cancelEditMessage = () => {
    setEditingMessageId(null);
    setEditMessageSubject("");
    setEditMessageBody("");
  };

  // Save edited message
  const saveEditMessage = async (syncToSmartlead = false) => {
    if (!editingMessageId) return;
    setEditSaving(true);
    try {
      const response = await fetch(`/api/messages/${editingMessageId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          subject: editMessageSubject,
          body: editMessageBody,
        }),
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `Failed to update message: ${response.status} - ${errorText}`
        );
      }
      const data = await response.json();
      // Update the threads state with the edited message
      setThreads(
        threads.map((thread) => {
          if (
            thread.messages.some((msg) => msg.message_id === editingMessageId)
          ) {
            return {
              ...thread,
              messages: thread.messages.map((msg) =>
                msg.message_id === editingMessageId
                  ? {
                      ...msg,
                      subject: editMessageSubject,
                      body: editMessageBody,
                    }
                  : msg
              ),
            };
          }
          return thread;
        })
      );
      toast.success("Message updated successfully");

      // Optionally sync to Smartlead after saving
      if (syncToSmartlead) {
        await triggerSmartleadSync(
          undefined,
          editMessageSubject,
          editMessageBody
        );
        toast.success("Message synced to Smartlead");
      }

      cancelEditMessage();
    } catch (error) {
      console.error("Error updating message:", error);
      toast.error(`Failed to update message: ${(error as Error).message}`);
    } finally {
      setEditSaving(false);
    }
  };

  // Add a function to sync a specific message to Smartlead
  const syncMessageToSmartlead = async (message: Message) => {
    if (!message.subject || !message.body) {
      toast.error("Message must have both subject and body to sync");
      return;
    }

    try {
      await triggerSmartleadSync(undefined, message.subject, message.body);
      toast.success("Message synced to Smartlead successfully");
    } catch (error) {
      console.error("Failed to sync message to Smartlead:", error);
      toast.error(`Failed to sync message: ${(error as Error).message}`);
    }
  };

  // Add this function to handle message editing
  const handleEditMessage = (message: any) => {
    setEditingMessageId(message.message_id);
    setMessageContent(message.body || "");
  };

  const handleSaveMessage = async (messageId: string) => {
    if (!messageEditorRef.current) return;

    const content = messageEditorRef.current.getContent();

    try {
      const response = await fetch(`/api/messages/${messageId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          body: content,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update message: ${response.status}`);
      }

      // Update local state
      setThreads(
        threads.map((thread) => {
          if (thread.thread_id === selectedThreadId) {
            return {
              ...thread,
              messages: thread.messages.map((msg: any) =>
                msg.message_id === messageId ? { ...msg, body: content } : msg
              ),
            };
          }
          return thread;
        })
      );

      setEditingMessageId(null);
      setMessageContent("");

      toast.success("Message updated successfully");
    } catch (error) {
      console.error("Error updating message:", error);
      toast.error(`Failed to update message: ${(error as Error).message}`);
    }
  };

  // Add a new function to handle contact editing (for tab navigation)
  const handleEditContact = () => {
    setEditedContact({ ...(contact as DetailedContact) });
    setIsEditing(true);
  };

  // Add a function to save contact edits
  const saveContactEdits = async (updatedContact: Contact) => {
    try {
      // Remove source field as requested
      const { source, ...contactData } = updatedContact;
      
      const response = await fetch(`/api/contacts/${contactId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(contactData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update contact");
      }

      const result = await response.json();
      console.log('Contact update result:', result);
      
      toast.success("Contact updated successfully");
      return result;
    } catch (error) {
      console.error("Error updating contact:", error);
      toast.error("Failed to update contact");
      throw error;
    }
  };

  // Add a function to handle email sending
  const handleSendEmail = () => {
    if (!contact?.email) return;

    const mailToLink = `mailto:${contact.email}?subject=ANAX Capital Introduction`;
    window.open(mailToLink, "_blank");
  };

  // Add this function to trigger matching
  const handleFindMatchingDeals = async () => {
    if (!contact) return;
    
    setIsLoadingMatchingDeals(true);
    try {
      const response = await fetch(`/api/matching/news-for-contact/${contact.contact_id}`);
      const data = await response.json();
      
      if (response.ok) {
        setMatchingDeals(data.matches || []);
        setMatchingCriteria(data.matching_criteria);
      } else {
        console.error('Failed to fetch matching deals:', data.error);
      }
    } catch (error) {
      console.error('Error fetching matching deals:', error);
    } finally {
      setIsLoadingMatchingDeals(false);
    }
  };

  // Toggle handler for showing all deal matches
  const handleToggleShowAllDealMatches = () => {
    setShowAllDealMatches(!showAllDealMatches);
  };

  // Function to fetch Gmail threads with pagination
  const fetchGmailThreads = async (page: number = 1, append: boolean = false) => {
    if (!contact?.email) return;

    if (page === 1) {
      setLoadingGmailThreads(true);
      setThreadsPage(1);
      setHasMoreThreads(true);
    } else {
      setLoadingMoreThreads(true);
    }

    try {
      const response = await fetch(
        `/api/dashboard/configuration/gmail/threads-by-email?email=${encodeURIComponent(
          contact.email
        )}&page=${page}&limit=50`
      );
      const result = await response.json();
      
      if (append) {
        setGmailThreads(prev => [...prev, ...(result.data || [])]);
      } else {
        setGmailThreads(result.data || []);
      }
      

      setHasMoreThreads(result.pagination?.hasNextPage || false);
      setThreadsPage(page);
    } catch (error) {
      console.error('Error fetching Gmail threads:', error);
    } finally {
      setLoadingGmailThreads(false);
      setLoadingMoreThreads(false);
    }
  };

  // Fetch Gmail threads for this contact's email
  useEffect(() => {
    if (contact && contact.email) {
      fetchGmailThreads(1, false);
    }
  }, [contact?.email]);

  // Load more threads
  const loadMoreThreads = () => {
    if (!loadingMoreThreads && hasMoreThreads) {
      fetchGmailThreads(threadsPage + 1, true);
    }
  };

  const handleExpandGmailThread = async (threadId: string) => {
    if (expandedGmailThread === threadId) {
      setExpandedGmailThread(null);
      return;
    }
    setExpandedGmailThread(threadId);
    if (!gmailThreadMessages[threadId]) {
      const res = await fetch(
        `/api/dashboard/configuration/gmail/messages?threadId=${threadId}`
      );
      const messages = await res.json();
      setGmailThreadMessages((prev) => ({ ...prev, [threadId]: messages }));
    }
  };

  // Add useEffect to fetch deals when Deal Matching tab is active
  useEffect(() => {
    if (activeTab === 'deal-matching' && contactId) {
      setIsLoadingMatchingDeals(true);
      const crmModeParam = isCrmMode ? '&crm_mode=true' : '';
      const showAllParam = showAllDealMatches ? '&show_all=true' : '';
      fetch(`/api/matching/deals-for-contact/${contactId}?add_ic_data=true${crmModeParam}${showAllParam}`)
        .then(res => res.json())
        .then(data => {
          if (data.matches && data.matches.length > 0) {
            setMatchingDeals(data.matches);
          } else {
            setMatchingDeals([]);
          }
          setMatchingDealsFiltering(data.filtering || null);
        })
        .catch(err => {
          console.error('Error loading matching deals:', err);
        })
        .finally(() => {
          setIsLoadingMatchingDeals(false);
        });

      // Also fetch categorized criteria
      setIsLoadingCategorizedCriteria(true);
      fetch(`/api/deals/contact-email-support/categorization?contactId=${contactId}`)
        .then(res => res.json())
        .then(data => {
          if (data.success) {
            setCategorizedCriteria(data.data);
          }
        })
        .catch(err => {
          console.error('Error loading categorized criteria:', err);
        })
        .finally(() => {
          setIsLoadingCategorizedCriteria(false);
        });
    }
  }, [activeTab, contactId, isCrmMode, showAllDealMatches]);

  // Add useEffect to fetch news when News Matchings tab is active
  useEffect(() => {
    if (activeTab === 'news-matchings' && contactId && fetchedNewsForContact !== contactId.toString()) {
      setMatchingNewsLoading(true);
      setMatchingNewsError(null);
      fetch(`/api/matching/news-for-contact/${contactId}`)
        .then(res => res.json())
        .then(data => {
          if (data.matches && data.matches.length > 0) {
            setMatchingNews(data.matches);
          } else {
            setMatchingNews([]);
          }
          // Store matching criteria for display - include top-level fields
          if (data.matching_criteria) {
            setMatchingCriteria({
              ...data.matching_criteria,
              criteria_source: data.criteria_source,
              criteria_description: data.criteria_description
            });
          }
          setFetchedNewsForContact(contactId.toString());
        })
        .catch(err => {
          setMatchingNewsError('Error loading matching news.');
        })
        .finally(() => {
          setMatchingNewsLoading(false);
        });
    }
  }, [activeTab, contactId, fetchedNewsForContact]);

  // Add useEffect to fetch transcripts when Transcripts tab is active
  useEffect(() => {
    if (activeTab === 'transcripts' && contactId) {
      fetchContactTranscripts();
    }
  }, [activeTab, contactId]);

  const renderMatchingCriteria = () => {
    if (!matchingCriteria) return null;

    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Matching Criteria
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Default Location Warning */}
            {matchingCriteria.using_default_location && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Using Default Location</AlertTitle>
                <AlertDescription>
                  {matchingCriteria.default_location_reason}
                </AlertDescription>
              </Alert>
            )}

            {/* Investment Criteria Summary */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">Contact Criteria</h4>
                <div className="space-y-1 text-sm">
                  {matchingCriteria.contact_criteria.property_type && (
                    <div>Property Type: {matchingCriteria.contact_criteria.property_type}</div>
                  )}
                  {matchingCriteria.contact_criteria.state && (
                    <div>State: {matchingCriteria.contact_criteria.state}</div>
                  )}
                  {matchingCriteria.contact_criteria.city && (
                    <div>City: {matchingCriteria.contact_criteria.city}</div>
                  )}
                  {matchingCriteria.contact_criteria.country && (
                    <div>Country: {matchingCriteria.contact_criteria.country}</div>
                  )}
                  {matchingCriteria.contact_criteria.loan_type && (
                    <div>Loan Type: {matchingCriteria.contact_criteria.loan_type}</div>
                  )}
                  {matchingCriteria.contact_criteria.deal_size && (
                    <div>Deal Size: {matchingCriteria.contact_criteria.deal_size}</div>
                  )}
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Aggregated Criteria</h4>
                <div className="space-y-1 text-sm">
                  {matchingCriteria.aggregated_criteria.property_types?.length > 0 && (
                    <div>Property Types: {matchingCriteria.aggregated_criteria.property_types.join(', ')}</div>
                  )}
                  {matchingCriteria.aggregated_criteria.property_sub_categories?.length > 0 && (
                    <div>Sub Categories: {matchingCriteria.aggregated_criteria.property_sub_categories.join(', ')}</div>
                  )}
                  {matchingCriteria.aggregated_criteria.states?.length > 0 && (
                    <div>States: {matchingCriteria.aggregated_criteria.states.join(', ')}</div>
                  )}
                  {matchingCriteria.aggregated_criteria.cities?.length > 0 && (
                    <div>Cities: {matchingCriteria.aggregated_criteria.cities.join(', ')}</div>
                  )}
                  {matchingCriteria.aggregated_criteria.strategies?.length > 0 && (
                    <div>Strategies: {matchingCriteria.aggregated_criteria.strategies.join(', ')}</div>
                  )}
                  {matchingCriteria.aggregated_criteria.deal_size_range?.min || matchingCriteria.aggregated_criteria.deal_size_range?.max ? (
                    <div>
                      Deal Size: ${matchingCriteria.aggregated_criteria.deal_size_range.min || 0}M - ${matchingCriteria.aggregated_criteria.deal_size_range.max || '∞'}M
                    </div>
                  ) : null} 
                </div>
              </div>
            </div>

            {/* Detailed Criteria */}
            {matchingCriteria.detailed_criteria?.length > 0 && (
              <div>
                <h4 className="font-semibold mb-2">Detailed Investment Criteria</h4>
                <div className="space-y-2">
                  {matchingCriteria.detailed_criteria.map((criteria: any, index: number) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="font-medium text-sm">{criteria.entity_name || criteria.entity_type}</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {criteria.property_types?.length > 0 && (
                          <div>Property Types: {criteria.property_types.join(', ')}</div>
                        )}
                        {criteria.strategies?.length > 0 && (
                          <div>Strategies: {criteria.strategies.join(', ')}</div>
                        )}
                        {criteria.state?.length > 0 && (
                          <div>States: {criteria.state.join(', ')}</div>
                        )}
                        {criteria.city?.length > 0 && (
                          <div>Cities: {criteria.city.join(', ')}</div>
                        )}
                        {(criteria.minimum_deal_size || criteria.maximum_deal_size) && (
                          <div>
                            Deal Size: ${criteria.minimum_deal_size || 0}M - ${criteria.maximum_deal_size || '∞'}M
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };



  if (loading || !contact) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-10 h-10 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <p className="text-gray-600">Loading contact information...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-10 h-10 border-4 border-red-600 border-t-transparent rounded-full animate-spin"></div>
          <p className="text-red-600">Failed to load contact information</p>
        </div>
      </div>
    );
  }

  const fullName = `${contact.first_name || ''} ${contact.last_name || ''}`;
  // Remove dealSize references as property doesn't exist on DetailedContact type
  const normalizedScrapedData = normalizeScrapedData(company?.scraped_data);

  // Add the new Smartlead tab content
  const renderSmartleadContent = () => {
    if (smartleadLoading) {
      return (
        <div className="text-center py-8">
          <div className="flex flex-col items-center space-y-4">
            <div className="w-10 h-10 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <p className="text-gray-600">Loading Smartlead data...</p>
          </div>
        </div>
      );
    }

    if (smartleadError) {
      return (
        <Card className="bg-white shadow-sm">
          <CardContent className="pt-6">
            <div className="text-center py-6">
              <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Error Loading Smartlead Data
              </h3>
              <p className="text-red-500 mb-4">{smartleadError}</p>
              <Button
                onClick={fetchSmartleadData}
                variant="outline"
                className="mx-auto"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    }

    if (!smartleadData || !smartleadData.hasSmartleadData) {
      return (
        <Card className="bg-white shadow-sm">
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Database className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-700 mb-2">
                Not Synced with Smartlead
              </h3>
              <p className="text-gray-500 max-w-md mx-auto mb-6">
                This contact hasn't been synced to Smartlead yet. Sync to create
                a lead in Smartlead and track email interactions.
              </p>
              {/* Only show sync button if contact doesn't have a Smartlead lead ID */}
              {!contact.smartlead_lead_id ? (
                <Button
                  onClick={() => triggerSmartleadSync()}
                  disabled={
                    smartleadSyncing ||
                    !contact?.email ||
                    contact?.email_verification_status !== 'completed'
                  }
                  className="mx-auto"
                >
                  {smartleadSyncing ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Syncing...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Sync to Smartlead
                    </>
                  )}
                </Button>
              ) : (
                <div className="flex justify-center">
                  <Badge
                    variant="outline"
                    className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1 py-2 px-3"
                  >
                    <CheckCircle2 size={16} />
                    Already synced to Smartlead
                  </Badge>
                </div>
              )}
              {!contact?.email && (
                <p className="text-red-500 text-sm mt-4">
                  Contact has no email address
                </p>
              )}
              {contact?.email && contact?.email_verification_status !== 'completed' && (
                <p className="text-red-500 text-sm mt-4">
                  Contact email has not been validated
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      );
    }

    // Contact has Smartlead data
    return (
      <div className="space-y-6">
        {/* Smartlead Status Card */}
        <Card className="bg-white shadow-sm">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg font-medium">
                Smartlead Status
              </CardTitle>
              <div className="flex items-center gap-4">
                <div className="flex items-center space-x-2">
                  <label
                    htmlFor="sort-option"
                    className="text-sm text-gray-500"
                  >
                    Sort by:
                  </label>
                  <select
                    id="sort-option"
                    className="text-sm border rounded p-1"
                    value={sortOption}
                    onChange={(e) => setSortOption(e.target.value)}
                  >
                    <option value="created_at">Created Date</option>
                    <option value="updated_at">Updated Date</option>
                    <option value="subject">Subject</option>
                    <option value="status">Status</option>
                  </select>
                  <select
                    id="sort-order"
                    className="text-sm border rounded p-1"
                    value={sortOrder}
                    onChange={(e) => setSortOrder(e.target.value)}
                  >
                    <option value="desc">Newest First</option>
                    <option value="asc">Oldest First</option>
                  </select>
                </div>
                <Button
                  onClick={() => triggerSmartleadSync()}
                  disabled={
                    smartleadSyncing ||
                    (contact.smartlead_lead_id ? true : false)
                  }
                  size="sm"
                  variant="outline"
                >
                  {smartleadSyncing ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Syncing...
                    </>
                  ) : contact.smartlead_lead_id ? (
                    <>
                      <CheckCircle2 className="h-4 w-4 mr-2" />
                      Already Synced
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Sync to Smartlead
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-500 mb-1">Lead ID</div>
                <div className="font-medium">
                  {smartleadData.contact.smartlead_lead_id || "Not assigned"}
                </div>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-500 mb-1">Status</div>
                <div className="flex items-center">
                  <Badge
                    className={`
                    ${
                      smartleadData.contact.smartlead_status === "SENT"
                        ? "bg-blue-100 text-blue-800"
                        : smartleadData.contact.smartlead_status === "DELIVERED"
                        ? "bg-green-100 text-green-800"
                        : smartleadData.contact.smartlead_status === "OPENED"
                        ? "bg-yellow-100 text-yellow-800"
                        : smartleadData.contact.smartlead_status === "REPLIED"
                        ? "bg-purple-100 text-purple-800"
                        : smartleadData.contact.smartlead_status === "BOUNCED"
                        ? "bg-red-100 text-red-800"
                        : smartleadData.contact.smartlead_status ===
                          "UNSUBSCRIBED"
                        ? "bg-gray-100 text-gray-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {smartleadData.contact.smartlead_status || "Unknown"}
                  </Badge>
                </div>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-500 mb-1">
                  Last Email Sent
                </div>
                <div className="font-medium">
                  {smartleadData.contact.last_email_sent_at
                    ? new Date(
                        smartleadData.contact.last_email_sent_at
                      ).toLocaleString()
                    : "No emails sent"}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Email Threads Card */}
        {smartleadData.threads.length > 0 ? (
          <Card className="bg-white shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-medium">
                Email Threads
              </CardTitle>
              <CardDescription>
                {smartleadData.threads.length} thread
                {smartleadData.threads.length !== 1 ? "s" : ""} found for this
                contact
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {smartleadData.threads.map((thread) => (
                  <div
                    key={thread.thread_id}
                    className="border rounded-lg overflow-hidden"
                  >
                    <div className="bg-gray-50 p-4 border-b">
                      <div className="flex justify-between">
                        <h3 className="font-medium">
                          {thread.subject || "No Subject"}
                        </h3>
                        <Badge variant="outline" className="bg-white">
                          {thread.status}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-500 mt-1">
                        Created: {new Date(thread.created_at).toLocaleString()}
                        {thread.metadata?.campaign_id && (
                          <span className="ml-4">
                            Campaign ID: {thread.metadata.campaign_id}
                          </span>
                        )}
                      </div>
                    </div>
                    {thread.messages && thread.messages.length > 0 ? (
                      <div className="divide-y">
                        {[...thread.messages]
                          .sort((a, b) => {
                            const aTime = new Date(a.sent_at || a.created_at).getTime();
                            const bTime = new Date(b.sent_at || b.created_at).getTime();
                            return messageSortOrder === 'asc' ? aTime - bTime : bTime - aTime;
                          })
                          .map((message) => {
                            const isSent = userEmails.includes((message.from_email || '').toLowerCase());
                            const senderEmail = message.from_email;
                            const recipientEmail = message.to_email;
                            return (
                              <div key={message.message_id} className={`p-4 flex flex-col sm:flex-row sm:items-start gap-4 ${isSent ? 'bg-blue-50' : 'bg-green-50'}`}>
                                <div className="flex-shrink-0 flex flex-col items-center">
                                  <Avatar className="h-8 w-8 mb-1">
                                    <AvatarFallback>{(isSent ? senderEmail : recipientEmail)?.[0]?.toUpperCase() || '?'}</AvatarFallback>
                                  </Avatar>
                                  <Badge variant={isSent ? 'default' : 'secondary'} className={isSent ? 'bg-blue-600 text-white' : 'bg-green-600 text-white'}>
                                    {isSent ? `Sent by ${senderEmail}` : `Received by ${senderEmail}`}
                                  </Badge>
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex flex-wrap gap-2 text-xs text-gray-500 mb-1">
                                    <span>From: <span className="font-medium text-gray-700">{senderEmail}</span></span>
                                    <span>To: <span className="font-medium text-gray-700">{recipientEmail}</span></span>
                                    <span>Date: <span className="font-medium text-gray-700">{new Date(message.sent_at || message.created_at).toLocaleString()}</span></span>
                                  </div>
                                  <div className="font-semibold mb-1 text-base">{message.subject || 'No Subject'}</div>
                                  <div className="text-sm mt-2 prose max-w-none" dangerouslySetInnerHTML={{ __html: message.body }} />
                                </div>
                              </div>
                            );
                          })}
                      </div>
                    ) : (
                      <div className="p-4 text-center text-gray-500">
                        No messages in this thread
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="bg-white shadow-sm">
            <CardContent className="pt-6">
              <div className="text-center py-6">
                <MessageCircle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-700 mb-2">
                  No Email Threads Found
                </h3>
                <p className="text-gray-500 max-w-md mx-auto">
                  This contact has been synced to Smartlead, but no email
                  threads have been created yet.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  const renderEditForm = () => {
    if (!editedContact) return null;

    return (
      <div className="space-y-4 bg-white p-6 rounded-lg border">
        <h2 className="text-xl font-medium mb-4 flex items-center">
          <Edit className="mr-2 h-5 w-5 text-blue-500" />
          Edit Contact Information
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="first_name">First Name</Label>
            <Input
              id="first_name"
              value={editedContact.first_name || ""}
              onChange={(e) =>
                setEditedContact({
                  ...editedContact,
                  first_name: e.target.value,
                })
              }
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="last_name">Last Name</Label>
            <Input
              id="last_name"
              value={editedContact.last_name || ""}
              onChange={(e) =>
                setEditedContact({
                  ...editedContact,
                  last_name: e.target.value,
                })
              }
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="job_title">Job Title</Label>
            <Input
              id="job_title"
              value={editedContact.title || ""}
              onChange={(e) =>
                setEditedContact({ ...editedContact, title: e.target.value })
              }
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              value={editedContact.email || ""}
              onChange={(e) =>
                setEditedContact({ ...editedContact, email: e.target.value })
              }
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone_number">Phone Number</Label>
            <Input
              id="phone_number"
              value={editedContact.phone_number || ""}
              onChange={(e) =>
                setEditedContact({
                  ...editedContact,
                  phone_number: e.target.value,
                })
              }
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="company_website">LinkedIn URL</Label>
            <Input
              id="linkedin_url"
              value={editedContact.linkedin_url || ""}
              onChange={(e) =>
                setEditedContact({
                  ...editedContact,
                  linkedin_url: e.target.value,
                })
              }
            />
          </div>


        </div>

        <div className="space-y-2 mt-4">
          <Label htmlFor="person_summary">Summary</Label>
          <Textarea
            id="person_summary"
            value={editedContact.notes || ""}
            onChange={(e) =>
              setEditedContact({ ...editedContact, notes: e.target.value })
            }
            rows={3}
          />
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={() => setIsEditing(false)}
            className="flex items-center"
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button onClick={() => editedContact && saveContactEdits(editedContact)} className="flex items-center">
            <SaveIcon className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </div>
    );
  };

  // Available tabs
  const availableTabs = [
    {
      id: "overview-v2",
      label: "Contact Info",
      icon: <Sparkles className="h-4 w-4 mr-1.5" />,
    },
    {
      id: "edit",
      label: "Edit Contact",
      icon: <Edit className="h-4 w-4 mr-1.5" />,
    },
    {
      id: "company",
      label: "Company",
      icon: <Building2 className="h-4 w-4 mr-1.5" />,
    },
    {
      id: "investment-criteria-v2",
      label: "Investment Criteria",
      icon: <Target className="h-4 w-4 mr-1.5" />,
    },
    {
      id: "linked-deals",
      label: "Linked Deals",
      icon: <Building2 className="h-4 w-4 mr-1.5" />,
    },
    {
      id: "campaign-emails",
      label: "Campaign Emails",
      icon: <FileSearch className="h-4 w-4 mr-1.5" />,
    },
    {
      id: "deal-matching",
      label: "Deal Matching",
      icon: <Target className="h-4 w-4 mr-1.5" />,
    },
    {
      id: "news-matchings",
      label: "News Matchings",
      icon: <FileText className="h-4 w-4 mr-1.5" />,
    },
    {
      id: "email-threads",
      label: "Email Threads",
      icon: <Mail className="h-4 w-4 mr-1.5" />,
    },
    {
      id: "transcripts",
      label: "Fireflies Transcripts",
      icon: <FileText className="h-4 w-4 mr-1.5" />,
    },
  ];

  const isDealMatchingTab = activeTab === "deal-matching";
  // Remove React Query useQuery
  // const {
  //   data: matchingDealsData,
  
  //   refetch: refetchMatchingDeals,
  // } = useQuery(
  //   ['deals-for-contact', contactId],
  //   () => fetch(`/api/matching/deals-for-contact/${contactId}`).then(res => res.json()),
  //   {
  //     enabled: isDealMatchingTab && !!contactId,
  //     staleTime: 0,
  //     refetchOnWindowFocus: false,
  //   }
  // );

  const userEmails = contact && contact.email ? [contact.email.toLowerCase()] : [];

  return (
    <div className="bg-white rounded-lg border shadow-sm p-6">
      <div className="flex flex-col space-y-6">
        {/* Header/Navigation */}
        <div className="flex justify-between items-center border-b pb-4">
          <Button
            variant="ghost"
            onClick={onBack}
            className="flex items-center p-0 h-auto text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            <span>Back</span>
          </Button>
          <div className="flex space-x-2">
            {!isEditing && contact.email && (
              <Button
                onClick={handleSendEmail}
                className="flex items-center bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Mail className="h-4 w-4 mr-2" />
                Wire Email
              </Button>
            )}
          </div>
        </div>
       {/* Contact Profile */}
        {isEditing ? (
          renderEditForm()
        ) : (
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <ContactDetailHeader contact={contact} />
            </div>
            
            {/* Contact Processing Buttons */}
            <div className="flex items-center gap-2 ml-4">
              {processingJobs.map((job) => {
                const config = getStageConfig(job.stage);
                return (
                  <Button
                    key={job.stage}
                    onClick={() => executeContactProcessing(job.stage)}
                    disabled={job.isExecuting}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    {job.isExecuting ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="h-4 w-4" />
                    )}
                    {job.isExecuting ? 'Processing...' : config.title}
                  </Button>
                );
              })}
            </div>
          </div>
        )}

        {/* Tabs - if not in editing mode */}
        {!isEditing && (
          <div className="mt-6">
            <Tabs
              defaultValue="overview-v2"
              value={activeTab}
              onValueChange={setActiveTab}
            >
              <TabsList className="w-full border-b bg-transparent">
                {availableTabs.map((tab) => (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className="flex-1 py-3 rounded-none data-[state=active]:border-b-2 data-[state=active]:border-blue-500 data-[state=active]:text-blue-700 font-medium"
                  >
                    <span className="flex items-center">
                      {tab.icon}
                      {tab.label}
                    </span>
                  </TabsTrigger>
                ))}
              </TabsList>

              {/* Content for tabs */}
                <TabsContent value="overview-v2">
                  <ContactOverviewV2Tab contact={contact} />
                </TabsContent>

                <TabsContent value="edit">
                  <ContactEditTab 
                    contact={contact}
                    onSave={saveContactEdits}
                    onCancel={() => setActiveTab("overview-v2")}
                  />
                </TabsContent>

                <TabsContent value="company">
                  <ContactCompanyTab contact={contact} />
                </TabsContent>
              <TabsContent value="investment-criteria-v2">
                <InvestmentCriteriaSliderV2
                  entityType="contact"
                  entityId={contactId}
                  entityEmail={contact?.email}
                  entityName={`${contact?.first_name || ''} ${contact?.last_name || ''}`.trim()}
                />
              </TabsContent>
              <TabsContent value="linked-deals">
                <ContactLinkedDealsTab contactId={contactId} />
              </TabsContent>
              <TabsContent value="processing">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <RefreshCw className="h-5 w-5" />
                      Contact Processing
                    </CardTitle>
                    <CardDescription>
                      Trigger processing tasks for this contact
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {processingJobs.map((job) => {
                        const config = getStageConfig(job.stage);
                        return (
                          <div key={job.stage} className="border rounded-lg p-4">
                            <h3 className="font-semibold text-lg mb-2">{config.title}</h3>
                            <p className="text-sm text-gray-600 mb-4">{config.description}</p>
                            <Button
                              onClick={() => executeContactProcessing(job.stage)}
                              disabled={job.isExecuting}
                              className="w-full"
                              variant={job.isExecuting ? "secondary" : "default"}
                            >
                              {job.isExecuting ? (
                                <>
                                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                  Processing...
                                </>
                              ) : (
                                <>
                                  <RefreshCw className="h-4 w-4 mr-2" />
                                  Start {config.title}
                                </>
                              )}
                            </Button>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              <TabsContent value="campaign-emails">
                <ContactCampaignTag 
                  contactId={contactId}
                  contact={{
                    contact_id: contact.contact_id,
                    first_name: contact.first_name || '',
                    last_name: contact.last_name || '',
                    email: contact.email || '',
                    title: contact.title || '',
                    company_name: contact.company_name || '',
                    recent_activities: contact.recent_activities || [],
                  } as any}
                  normalizedScrapedData={normalizedScrapedData}
                />
              </TabsContent>
              <TabsContent value="deal-matching">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="h-5 w-5" />
                      Matching Deals
                      {matchingDeals.length > 0 && (
                        <Badge variant="secondary" className="ml-2">
                          {matchingDeals.length} matches found
                        </Badge>
                      )}
                      {matchingDeals.some((deal: any) => deal.fallback_used) && (
                        <Badge variant="outline" className="ml-2 bg-orange-50 text-orange-700 border-orange-200">
                          Company fallback used
                        </Badge>
                      )}
                      {matchingDeals.some((deal: any) => deal.location_fallback_used) && (
                        <Badge variant="outline" className="ml-2 bg-purple-50 text-purple-700 border-purple-200">
                          Location-based picks (City/State)
                        </Badge>
                      )}
                    </CardTitle>
                    <div className="flex items-center gap-4 mt-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="crm-mode"
                          checked={isCrmMode}
                          onCheckedChange={(checked) => setIsCrmMode(checked as boolean)}
                        />
                        <label htmlFor="crm-mode" className="text-sm font-medium text-gray-700">
                          CRM Mode
                        </label>
                      </div>
                      {isCrmMode && (
                        <div className="text-xs text-gray-600 bg-blue-50 px-2 py-1 rounded">
                          Filters: Non-internal deals only, within 1 year, requested criteria only
                        </div>
                      )}
                    </div>

                    {/* Filtering Toggle and Info */}
                    <div className="flex items-center justify-between mt-4">
                      <div className="flex items-center gap-2">
                        <Checkbox
                          id="show-all-deal-matches"
                          checked={showAllDealMatches}
                          onCheckedChange={handleToggleShowAllDealMatches}
                        />
                        <label htmlFor="show-all-deal-matches" className="text-sm font-medium text-gray-700">
                          Show all matches (including &lt; 50% score)
                        </label>
                      </div>
                      
                      {matchingDealsFiltering && !showAllDealMatches && (
                        <div className="text-sm text-gray-600">
                          Showing only matches ≥ 50% score
                          {matchingDealsFiltering.filtered_out_count > 0 && (
                            <span className="ml-2 text-orange-600">
                              ({matchingDealsFiltering.filtered_out_count} low-quality matches hidden)
                            </span>
                          )}
                        </div>
                      )}
                      
                      {matchingDealsFiltering && showAllDealMatches && (
                        <div className="text-sm text-gray-600">
                          Showing all matches (including low-quality)
                        </div>
                      )}
                    </div>
                    {/* Show field weights info if available */}
                    {matchingDeals.length > 0 && matchingDeals[0]?.scoring_method === "normalized_weights" && (
                      <div className="text-sm text-gray-600 mt-2">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          Using normalized field weights (always sum to 1.0)
                        </div>
                        <div className="text-xs mt-1">
                          All deals must have matching capital position to be included
                        </div>
                      </div>
                    )}
                  </CardHeader>
                  <CardContent>
                    {isLoadingMatchingDeals ? (
                      <div className="text-gray-500">Loading matching deals...</div>
                    ) : matchingDeals.length === 0 ? (
                      <div className="text-center py-8">
                        <Target className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <div className="text-gray-500">No matching deals found for this contact.</div>
                        <div className="text-sm text-gray-400 mt-2">
                          Deals must have matching capital position and other criteria
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                          Location-based recommendations (no capital position required) will be shown if available
                        </div>
                      </div>
                    ) : (
                      <>
                        {/* Summary Stats */}
                        <div className="mb-6 grid grid-cols-1 sm:grid-cols-4 gap-4">
                          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                            <div className="text-sm font-medium text-blue-900">Total Matches</div>
                            <div className="text-2xl font-bold text-blue-700">{matchingDeals.length}</div>
                          </div>
                          <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                            <div className="text-sm font-medium text-green-900">Avg Score</div>
                            <div className="text-2xl font-bold text-green-700">
                              {Math.round(matchingDeals.reduce((sum: number, deal: any) => sum + (deal.score || 0), 0) / matchingDeals.length)}
                            </div>
                          </div>
                          <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
                            <div className="text-sm font-medium text-purple-900">High Quality</div>
                            <div className="text-2xl font-bold text-purple-700">
                              {matchingDeals.filter((deal: any) => (deal.score || 0) >= 70).length}
                            </div>
                          </div>
                          <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
                            <div className="text-sm font-medium text-orange-900">Capital Match</div>
                            <div className="text-2xl font-bold text-orange-700">
                              {matchingDeals.some((deal: any) => deal.location_fallback_used) ? 'N/A' : '100%'}
                            </div>
                            <div className="text-xs text-orange-600">
                              {matchingDeals.some((deal: any) => deal.location_fallback_used) ? 'Not required for location picks' : 'Required'}
                            </div>
                          </div>
                        </div>
                        
                        {/* Location Fallback Info */}
                        {matchingDeals.some((deal: any) => deal.location_fallback_used) && (
                          <div className="mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg">
                            <div className="flex items-center gap-2 mb-2">
                              <MapPin className="h-4 w-4 text-purple-600" />
                              <div className="text-sm font-medium text-purple-900">Location-Based Recommendations</div>
                            </div>
                            <div className="text-sm text-purple-700">
                              No exact matches found based on investment criteria. Showing deals that match your location preferences (city or state).
                            </div>
                            <div className="text-xs text-purple-600 mt-1">
                              Deals are prioritized by location specificity: City → State
                            </div>
                            <div className="text-xs text-purple-600">
                              Location-based picks don't require capital position matching
                            </div>
                          </div>
                        )}

                        {/* Deal Cards */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 min-w-0">
                          {matchingDeals.map((deal: any) => {
                            const isExpanded = expandedDealId === deal.deal_id;
                            const scoreColor = (deal.score || 0) >= 80 ? 'bg-green-600' : 
                                              (deal.score || 0) >= 60 ? 'bg-blue-600' :
                                              (deal.score || 0) >= 40 ? 'bg-yellow-600' : 'bg-orange-600';

                            return (
                              <div key={deal.deal_id} className="relative group min-w-0 overflow-visible">
                                <Card className="border shadow-sm hover:shadow-md transition-shadow">
                                  <CardHeader className="pb-3">
                                    <div className="flex items-center justify-between">
                                      <CardTitle className="text-lg font-semibold text-gray-900 truncate">
                                        {deal.deal_name || `Deal #${deal.deal_id}`}
                                      </CardTitle>
                                      <div className="flex flex-col items-end gap-1">
                                        <div className={`${scoreColor} text-white text-sm font-bold px-3 py-1 rounded-full shadow`}>
                                          {deal.score}%
                                        </div>
                                        {/* New: Show criteria count and best score */}
                                        {deal.matching_criteria_count > 1 && (
                                          <div className="flex items-center gap-1">
                                            <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                              {deal.matching_criteria_count} criteria
                                            </Badge>
                                            {deal.best_score > deal.score && (
                                              <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                                                Best: {deal.best_score}%
                                              </Badge>
                                            )}
                                          </div>
                                        )}
                                        {/* Show fallback indicator */}
                                        {deal.fallback_used && (
                                          <div className="flex items-center gap-1 mt-1">
                                            <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700 border-orange-200">
                                              Company Criteria
                                            </Badge>
                                          </div>
                                        )}
                                        {/* Show location fallback indicator */}
                                        {deal.location_fallback_used && (
                                          <div className="flex items-center gap-1 mt-1">
                                            <Badge variant="outline" className="text-xs bg-purple-50 text-purple-700 border-purple-200">
                                              {deal.location_match_type === 'city' ? 'City Pick' :
                                               deal.location_match_type === 'state' ? 'State Pick' :
                                               'Location Pick'}
                                            </Badge>
                                          </div>
                                        )}
                                      </div>
                                    </div>

                                    {/* Capital Positions Matched */}
                                    {deal.all_criteria_matches && deal.all_criteria_matches.length > 0 && (
                                      <div className="mt-2">
                                        <div className="text-xs font-medium text-gray-600 mb-1">Matched Capital Positions:</div>
                                        <div className="flex flex-wrap gap-1">
                                          {deal.all_criteria_matches.map((criteria: any, criteriaIdx: number) => {
                                            const capitalPositions = criteria.investment_criteria?.capital_position;
                                            if (!capitalPositions || capitalPositions.length === 0) return null;
                                            
                                            return capitalPositions.map((position: string, posIdx: number) => (
                                              <Badge 
                                                key={`${criteriaIdx}-${posIdx}`} 
                                                variant="outline" 
                                                className="text-xs bg-blue-50 text-blue-700 border-blue-200"
                                              >
                                                {position}
                                              </Badge>
                                            ));
                                          }).flat().filter(Boolean)}
                                        </div>
                                      </div>
                                    )}

                                    {/* Quick match indicators - Show All */}
                                    <div className="flex flex-wrap gap-1 mt-2 max-h-20 overflow-y-auto">
                                      {deal.breakdown?.filter((b: any) => b.score > 0).map((b: any, idx: number) => (
                                        <Badge key={idx} variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                                          {b.field.replace(/_/g, ' ')}: {b.score}%
                                        </Badge>
                                      ))}
                                      {(!deal.breakdown || deal.breakdown.filter((b: any) => b.score > 0).length === 0) && (
                                        <Badge variant="outline" className="text-xs text-gray-500">
                                          Capital position match only
                                        </Badge>
                                      )}
                                    </div>
                                  </CardHeader>
                                  
                                  <CardContent className="pt-0">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="w-full"
                                      onClick={() => setExpandedDealId(isExpanded ? null : deal.deal_id)}
                                    >
                                      {isExpanded ? "Hide Details" : "Show Details"}
                                    </Button>

                                    {/* Quick Summary */}
                                    {!isExpanded && deal.reasons && deal.reasons.length > 0 && (
                                      <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded">
                                        <div className="font-semibold text-xs text-blue-800 mb-1">Top Matches:</div>
                                        <div className="text-xs text-blue-700">
                                          {deal.reasons.slice(0, 2).join(" • ")}
                                          {deal.reasons.length > 2 && "..."}
                                        </div>
                                      </div>
                                    )}

                                    {isExpanded && (
                                      <div className="mt-3 space-y-3">
                                        {/* Capital Position Match */}
                                        <div className="bg-green-100 border border-green-300 rounded-lg p-2">
                                          <div className="flex items-center gap-2">
                                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                            <span className="font-semibold text-green-800 text-sm">Capital Position Match</span>
                                            <Badge className="bg-green-600 text-white">Required ✓</Badge>
                                          </div>
                                          <div className="text-xs text-green-700 mt-1">
                                            This deal meets your capital position requirements
                                          </div>
                                          
                                          {/* Show matched capital positions */}
                                          {deal.all_criteria_matches && deal.all_criteria_matches.length > 0 && (
                                            <div className="mt-2">
                                              <div className="text-xs font-medium text-green-700 mb-1">Matched Positions:</div>
                                              <div className="flex flex-wrap gap-1">
                                                {deal.all_criteria_matches.map((criteria: any, criteriaIdx: number) => {
                                                  const capitalPositions = criteria.investment_criteria?.capital_position;
                                                  if (!capitalPositions || capitalPositions.length === 0) return null;
                                                  
                                                  return capitalPositions.map((position: string, posIdx: number) => (
                                                    <Badge 
                                                      key={`${criteriaIdx}-${posIdx}`} 
                                                      className="text-xs bg-green-200 text-green-800 border-green-300"
                                                    >
                                                      {position}
                                                    </Badge>
                                                  ));
                                                }).flat().filter(Boolean)}
                                              </div>
                                            </div>
                                          )}
                                        </div>

                                        {/* Multiple Criteria Summary */}
                                        {deal.matching_criteria_count > 1 && (
                                          <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-3">
                                            <div className="flex items-center justify-between mb-2">
                                              <span className="font-semibold text-sm text-blue-900">Multiple Criteria Match</span>
                                              <Badge className="bg-blue-600 text-white text-xs">
                                                {deal.matching_criteria_count} criteria
                                              </Badge>
                                            </div>
                                            <div className="grid grid-cols-2 gap-2 text-xs">
                                              <div>
                                                <span className="text-blue-700 font-medium">Average Score:</span>
                                                <span className="ml-1 font-semibold">{deal.score}%</span>
                                              </div>
                                              <div>
                                                <span className="text-blue-700 font-medium">Best Match:</span>
                                                <span className="ml-1 font-semibold">{deal.best_score}%</span>
                                              </div>
                                            </div>
                                          </div>
                                        )}

                                        {/* Individual Criteria Breakdowns */}
                                        {deal.all_criteria_matches && deal.all_criteria_matches.length > 0 && (
                                          <div className="space-y-2">
                                            <div className="flex items-center justify-between">
                                              <span className="font-semibold text-sm">Criteria Breakdown</span>
                                              <Badge variant="outline" className="text-xs">
                                                {deal.all_criteria_matches.length} matches
                                              </Badge>
                                            </div>
                                            
                                            {deal.all_criteria_matches.map((criteria: any, criteriaIdx: number) => (
                                              <div key={criteria.criteria_id || criteriaIdx} className="border rounded-lg p-3 bg-white">
                                                <div className="flex items-center justify-between mb-2">
                                                  <span className="font-medium text-sm text-gray-700">
                                                    Criteria #{criteriaIdx + 1}
                                                  </span>
                                                  <div className="flex items-center gap-2">
                                                    <span className="text-sm font-semibold">{criteria.score}%</span>
                                                    {criteria.score === deal.best_score && (
                                                      <Badge className="bg-green-600 text-white text-xs">Best</Badge>
                                                    )}
                                                  </div>
                                                </div>
                                                
                                                {/* Field-by-field breakdown for this criteria - Show All Fields */}
                                                <div className="grid grid-cols-1 gap-1 max-h-40 overflow-y-auto">
                                                  {criteria.breakdown?.map((b: any, idx: number) => (
                                                    <div key={idx} className="flex items-center justify-between py-1 px-2 bg-gray-50 rounded text-xs">
                                                      <span className="capitalize font-medium">{b.field.replace(/_/g, ' ')}</span>
                                                      <div className="flex items-center gap-2">
                                                        <span className={`font-semibold ${b.score >= 50 ? 'text-green-600' : b.score > 0 ? 'text-blue-600' : 'text-gray-500'}`}>
                                                          {b.score}%
                                                        </span>
                                                        {b.weight && (
                                                          <span className="text-gray-400">({(b.weight * 100).toFixed(0)}%)</span>
                                                        )}
                                                      </div>
                                                    </div>
                                                  ))}
                                                </div>
                                                
                                                {/* Criteria-specific reasons - Show All */}
                                                {criteria.reasons && criteria.reasons.length > 0 && (
                                                  <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded max-h-24 overflow-y-auto">
                                                    <div className="text-xs text-blue-700">
                                                      {criteria.reasons.join(" • ")}
                                                    </div>
                                                  </div>
                                                )}
                                              </div>
                                            ))}
                                          </div>
                                        )}

                                        {/* Fallback: Original Scoring Breakdown (for backward compatibility) */}
                                        {(!deal.all_criteria_matches || deal.all_criteria_matches.length === 0) && (
                                          <div className="space-y-2">
                                            <div className="flex items-center justify-between">
                                              <span className="font-semibold text-sm">Scoring Breakdown</span>
                                              <Badge variant="outline" className="text-xs">
                                                Method: {deal.scoring_method || 'weighted'}
                                              </Badge>
                                            </div>
                                            
                                            {deal.breakdown?.map((b: any, idx: number) => (
                                              <div key={idx} className="bg-white border rounded p-2">
                                                <div className="flex items-center justify-between mb-1">
                                                  <span className="font-medium text-sm capitalize">{b.field.replace('_', ' ')}</span>
                                                  <div className="flex items-center gap-2">
                                                    <span className="text-sm font-semibold">{b.score}%</span>
                                                    {b.weight && (
                                                      <Badge variant="outline" className="text-xs">
                                                        Weight: {(b.weight * 100).toFixed(1)}%
                                                      </Badge>
                                                    )}
                                                  </div>
                                                </div>
                                                <div className="w-full bg-gray-200 rounded-full h-2">
                                                  <div
                                                    className={`h-2 rounded-full ${
                                                      b.score >= 80 ? 'bg-green-500' :
                                                      b.score >= 60 ? 'bg-blue-500' :
                                                      b.score >= 40 ? 'bg-yellow-500' :
                                                      b.score > 0 ? 'bg-orange-500' : 'bg-gray-300'
                                                    }`}
                                                    style={{ width: `${b.score}%` }}
                                                  ></div>
                                                </div>
                                                <div className="text-xs text-gray-600 mt-1">{b.reason}</div>
                                              </div>
                                            ))}
                                          </div>
                                        )}

                                        {/* All Reasons Summary */}
                                        {deal.all_reasons && deal.all_reasons.length > 0 && (
                                          <div className="bg-blue-50 border border-blue-200 rounded p-2">
                                            <div className="font-semibold text-sm text-blue-800 mb-1">All Key Matches:</div>
                                            <ul className="text-xs text-blue-700 space-y-0.5">
                                              {deal.all_reasons.map((reason: string, idx: number) => (
                                                <li key={idx} className="flex items-center gap-1">
                                                  <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                                                  {reason}
                                                </li>
                                              ))}
                                            </ul>
                                          </div>
                                        )}

                                        {/* Weight Information */}
                                        {deal.weightSum && (
                                          <div className="text-xs text-gray-500 pt-2 border-t">
                                            Primary criteria weight: {(deal.weightSum * 100).toFixed(1)}%
                                            {deal.weightSum < 0.99 && " (some criteria not applicable)"}
                                          </div>
                                        )}
                                      </div>
                                    )}
                                  </CardContent>
                                </Card>
                              </div>
                            );
                          })}
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
              <TabsContent value="news-matchings">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      News Matchings
                      {matchingNews.length > 0 && (
                        <Badge variant="secondary" className="ml-2">
                          {matchingNews.length} matches found
                        </Badge>
                      )}
                      {matchingCriteria?.criteria_source && matchingCriteria.criteria_source !== 'none' ? (
                        <Badge 
                          variant="outline" 
                          className={`ml-2 ${
                            matchingCriteria.criteria_source === 'contact' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                            matchingCriteria.criteria_source === 'company' ? 'bg-green-50 text-green-700 border-green-200' :
                            matchingCriteria.criteria_source === 'location' ? 'bg-purple-50 text-purple-700 border-purple-200' :
                            'bg-gray-50 text-gray-700 border-gray-200'
                          }`}
                        >
                          {matchingCriteria.criteria_source === 'contact' ? 'Contact Criteria' :
                           matchingCriteria.criteria_source === 'company' ? 'Company Criteria' :
                           matchingCriteria.criteria_source === 'location' ? 'Location Fallback' :
                           'No Criteria'}
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="ml-2 bg-gray-50 text-gray-700 border-gray-200">
                          No Criteria Available
                        </Badge>
                      )}
                    </CardTitle>
                    <CardDescription>
                      Real estate news and deals matching your investment criteria, sorted by deal size
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {matchingNewsLoading ? (
                      <div className="text-center py-8">
                        <div className="flex flex-col items-center space-y-4">
                          <div className="w-10 h-10 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                          <p className="text-gray-600">Loading matching news...</p>
                        </div>
                      </div>
                    ) : matchingNewsError ? (
                      <div className="text-center py-8">
                        <AlertCircle className="h-12 w-12 text-red-300 mx-auto mb-4" />
                        <div className="text-red-600">{matchingNewsError}</div>
                      </div>
                    ) : matchingNews.length === 0 ? (
                      <div className="text-center py-8">
                        <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <div className="text-gray-500">No matching news found for this contact.</div>
                        <div className="text-sm text-gray-400 mt-2">
                          News must match your investment criteria to appear here
                        </div>
                      </div>
                    ) : (
                      <>
                        {/* Summary Stats */}
                        <div className="mb-6 grid grid-cols-1 sm:grid-cols-4 gap-4">
                          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                            <div className="text-sm font-medium text-blue-900">Total Matches</div>
                            <div className="text-2xl font-bold text-blue-700">{matchingNews.length}</div>
                          </div>
                          <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                            <div className="text-sm font-medium text-green-900">Avg Score</div>
                            <div className="text-2xl font-bold text-green-700">
                              {Math.round(matchingNews.reduce((sum: number, news: any) => sum + (news.score || 0), 0) / matchingNews.length)}
                            </div>
                          </div>
                          <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
                            <div className="text-sm font-medium text-purple-900">High Quality</div>
                            <div className="text-2xl font-bold text-purple-700">
                              {matchingNews.filter((news: any) => (news.score || 0) >= 70).length}
                            </div>
                          </div>
                          <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
                            <div className="text-sm font-medium text-orange-900">Avg Deal Size</div>
                            <div className="text-2xl font-bold text-orange-700">
                              {(() => {
                                const avgDealSize = matchingNews.filter((news: any) => news.deal_size).reduce((sum: number, news: any) => sum + (news.deal_size || 0), 0) / matchingNews.filter((news: any) => news.deal_size).length;
                                if (avgDealSize >= 1000) {
                                  return `$${(avgDealSize / 1000).toFixed(1)}B`;
                                }
                                return `$${Math.round(avgDealSize)}M`;
                              })()}
                            </div>
                          </div>
                        </div>

                        {/* New Hierarchical Criteria Display */}
                        {matchingCriteria && (
                          <div className="mb-6 space-y-4">
                            {/* Criteria Source Info */}
                            <div className="bg-gray-50 p-4 rounded-lg border">
                              <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                                <Target className="h-5 w-5" />
                                Matching Criteria Information
                              </h3>
                              
                              {/* Criteria Source Badge */}
                              <div className="mb-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <span className="text-sm font-medium text-gray-700">Criteria Source:</span>
                                  <Badge 
                                    className={`${
                                      matchingCriteria.criteria_source === 'contact' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                                      matchingCriteria.criteria_source === 'company' ? 'bg-green-100 text-green-800 border-green-200' :
                                      matchingCriteria.criteria_source === 'location' ? 'bg-purple-100 text-purple-800 border-purple-200' :
                                      'bg-gray-100 text-gray-800 border-gray-200'
                                    }`}
                                  >
                                    {matchingCriteria.criteria_source === 'contact' ? 'Contact Investment Criteria' :
                                     matchingCriteria.criteria_source === 'company' ? 'Company Investment Criteria' :
                                     matchingCriteria.criteria_source === 'location' ? 'Location Fallback' :
                                     'No Criteria Available'}
                                  </Badge>
                                </div>
                                <p className="text-sm text-gray-600">{matchingCriteria.criteria_description}</p>
                              </div>

                              {/* Used Criteria Display */}
                              {matchingCriteria.used_criteria && Object.keys(matchingCriteria.used_criteria).length > 0 && (
                                <div className="mb-4">
                                  <h4 className="text-md font-semibold text-gray-800 mb-2">Used Criteria:</h4>
                                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                    {/* Property Types */}
                                    {matchingCriteria.used_criteria.property_types?.length > 0 && (
                                      <div className="bg-white p-3 rounded border">
                                        <div className="text-sm font-medium text-gray-700 mb-1">Property Types</div>
                                        <div className="flex flex-wrap gap-1">
                                          {matchingCriteria.used_criteria.property_types.map((type: string, idx: number) => (
                                            <Badge key={idx} variant="secondary" className="text-xs">
                                              {type}
                                            </Badge>
                                          ))}
                                        </div>
                                      </div>
                                    )}

                                    {/* Sub Property Categories */}
                                    {matchingCriteria.used_criteria.property_sub_categories?.length > 0 && (
                                      <div className="bg-white p-3 rounded border">
                                        <div className="text-sm font-medium text-gray-700 mb-1">Sub Categories</div>
                                        <div className="flex flex-wrap gap-1">
                                          {matchingCriteria.used_criteria.property_sub_categories.map((category: string, idx: number) => (
                                            <Badge key={idx} variant="secondary" className="text-xs">
                                              {category}
                                            </Badge>
                                          ))}
                                        </div>
                                      </div>
                                    )}

                                    {/* Strategies */}
                                    {matchingCriteria.used_criteria.strategies?.length > 0 && (
                                      <div className="bg-white p-3 rounded border">
                                        <div className="text-sm font-medium text-gray-700 mb-1">Strategies</div>
                                        <div className="flex flex-wrap gap-1">
                                          {matchingCriteria.used_criteria.strategies.map((strategy: string, idx: number) => (
                                            <Badge key={idx} variant="secondary" className="text-xs">
                                              {strategy}
                                            </Badge>
                                          ))}
                                        </div>
                                      </div>
                                    )}

                                    {/* Loan Types */}
                                    {matchingCriteria.used_criteria.loan_types?.length > 0 && (
                                      <div className="bg-white p-3 rounded border">
                                        <div className="text-sm font-medium text-gray-700 mb-1">Loan Types</div>
                                        <div className="flex flex-wrap gap-1">
                                          {matchingCriteria.used_criteria.loan_types.map((type: string, idx: number) => (
                                            <Badge key={idx} variant="secondary" className="text-xs">
                                              {type}
                                            </Badge>
                                          ))}
                                        </div>
                                      </div>
                                    )}

                                    {/* Capital Positions */}
                                    {matchingCriteria.used_criteria.capital_positions?.length > 0 && (
                                      <div className="bg-white p-3 rounded border">
                                        <div className="text-sm font-medium text-gray-700 mb-1">Capital Positions</div>
                                        <div className="flex flex-wrap gap-1">
                                          {matchingCriteria.used_criteria.capital_positions.map((position: string, idx: number) => (
                                            <Badge key={idx} variant="secondary" className="text-xs">
                                              {position}
                                            </Badge>
                                          ))}
                                        </div>
                                      </div>
                                    )}

                                    {/* Deal Size Range */}
                                    {(matchingCriteria.used_criteria.min_deal_size || matchingCriteria.used_criteria.max_deal_size) && (
                                      <div className="bg-white p-3 rounded border">
                                        <div className="text-sm font-medium text-gray-700 mb-1">Deal Size Range</div>
                                        <div className="text-sm text-gray-600">
                                          ${matchingCriteria.used_criteria.min_deal_size || 0}M - ${matchingCriteria.used_criteria.max_deal_size || '∞'}M
                                        </div>
                                      </div>
                                    )}

                                    {/* Geographic Criteria */}
                                    {(matchingCriteria.used_criteria.states?.length > 0 || 
                                      matchingCriteria.used_criteria.cities?.length > 0 || 
                                      matchingCriteria.used_criteria.countries?.length > 0) && (
                                      <div className="bg-white p-3 rounded border">
                                        <div className="text-sm font-medium text-gray-700 mb-1">Geographic Focus</div>
                                        <div className="text-xs text-gray-600">
                                          {matchingCriteria.used_criteria.states?.length > 0 && (
                                            <div>States: {matchingCriteria.used_criteria.states.join(', ')}</div>
                                          )}
                                          {matchingCriteria.used_criteria.cities?.length > 0 && (
                                            <div>Cities: {matchingCriteria.used_criteria.cities.join(', ')}</div>
                                          )}
                                          {matchingCriteria.used_criteria.countries?.length > 0 && (
                                            <div>Countries: {matchingCriteria.used_criteria.countries.join(', ')}</div>
                                          )}
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}

                              {/* All Available Criteria Levels */}
                              <div className="border-t pt-4">
                                <h4 className="text-md font-semibold text-gray-800 mb-3">All Available Criteria Levels:</h4>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                  
                                  {/* Contact Criteria */}
                                  <div className="bg-blue-50 p-3 rounded border border-blue-200">
                                    <div className="flex items-center gap-2 mb-2">
                                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                      <span className="text-sm font-medium text-blue-900">Contact Criteria</span>
                                    </div>
                                    <div className="text-xs text-blue-700 space-y-1">
                                      {matchingCriteria.contact_criteria?.property_types?.length > 0 && (
                                        <div>Property Types: {matchingCriteria.contact_criteria.property_types.join(', ')}</div>
                                      )}
                                      {matchingCriteria.contact_criteria?.strategies?.length > 0 && (
                                        <div>Strategies: {matchingCriteria.contact_criteria.strategies.join(', ')}</div>
                                      )}
                                      {matchingCriteria.contact_criteria?.states?.length > 0 && (
                                        <div>States: {matchingCriteria.contact_criteria.states.join(', ')}</div>
                                      )}
                                      {matchingCriteria.contact_criteria?.cities?.length > 0 && (
                                        <div>Cities: {matchingCriteria.contact_criteria.cities.join(', ')}</div>
                                      )}
                                      {matchingCriteria.contact_criteria?.countries?.length > 0 && (
                                        <div>Countries: {matchingCriteria.contact_criteria.countries.join(', ')}</div>
                                      )}
                                      {matchingCriteria.contact_criteria?.loan_types?.length > 0 && (
                                        <div>Loan Types: {matchingCriteria.contact_criteria.loan_types.join(', ')}</div>
                                      )}
                                      {(matchingCriteria.contact_criteria?.min_deal_size || matchingCriteria.contact_criteria?.max_deal_size) && (
                                        <div>
                                          Deal Size: ${matchingCriteria.contact_criteria.min_deal_size || 0}M - ${matchingCriteria.contact_criteria.max_deal_size || '∞'}M
                                        </div>
                                      )}
                                      {(!matchingCriteria.contact_criteria?.property_types?.length && 
                                        !matchingCriteria.contact_criteria?.strategies?.length && 
                                        !matchingCriteria.contact_criteria?.states?.length && 
                                        !matchingCriteria.contact_criteria?.cities?.length && 
                                        !matchingCriteria.contact_criteria?.countries?.length && 
                                        !matchingCriteria.contact_criteria?.loan_types?.length && 
                                        !matchingCriteria.contact_criteria?.min_deal_size && 
                                        !matchingCriteria.contact_criteria?.max_deal_size) && (
                                        <div className="text-gray-500">No contact criteria available</div>
                                      )}
                                    </div>
                                  </div>

                                  {/* Company Criteria */}
                                  <div className="bg-green-50 p-3 rounded border border-green-200">
                                    <div className="flex items-center gap-2 mb-2">
                                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                      <span className="text-sm font-medium text-green-900">Company Criteria</span>
                                    </div>
                                    <div className="text-xs text-green-700 space-y-1">
                                      {matchingCriteria.company_criteria?.property_types?.length > 0 && (
                                        <div>Property Types: {matchingCriteria.company_criteria.property_types.join(', ')}</div>
                                      )}
                                      {matchingCriteria.company_criteria?.strategies?.length > 0 && (
                                        <div>Strategies: {matchingCriteria.company_criteria.strategies.join(', ')}</div>
                                      )}
                                      {matchingCriteria.company_criteria?.states?.length > 0 && (
                                        <div>States: {matchingCriteria.company_criteria.states.join(', ')}</div>
                                      )}
                                      {matchingCriteria.company_criteria?.cities?.length > 0 && (
                                        <div>Cities: {matchingCriteria.company_criteria.cities.join(', ')}</div>
                                      )}
                                      {matchingCriteria.company_criteria?.countries?.length > 0 && (
                                        <div>Countries: {matchingCriteria.company_criteria.countries.join(', ')}</div>
                                      )}
                                      {matchingCriteria.company_criteria?.loan_types?.length > 0 && (
                                        <div>Loan Types: {matchingCriteria.company_criteria.loan_types.join(', ')}</div>
                                      )}
                                      {(matchingCriteria.company_criteria?.min_deal_size || matchingCriteria.company_criteria?.max_deal_size) && (
                                        <div>
                                          Deal Size: ${matchingCriteria.company_criteria.min_deal_size || 0}M - ${matchingCriteria.company_criteria.max_deal_size || '∞'}M
                                        </div>
                                      )}
                                      {(!matchingCriteria.company_criteria?.property_types?.length && 
                                        !matchingCriteria.company_criteria?.strategies?.length && 
                                        !matchingCriteria.company_criteria?.states?.length && 
                                        !matchingCriteria.company_criteria?.cities?.length && 
                                        !matchingCriteria.company_criteria?.countries?.length && 
                                        !matchingCriteria.company_criteria?.loan_types?.length && 
                                        !matchingCriteria.company_criteria?.min_deal_size && 
                                        !matchingCriteria.company_criteria?.max_deal_size) && (
                                        <div className="text-gray-500">No company criteria available</div>
                                      )}
                                    </div>
                                  </div>

                                  {/* Location Criteria */}
                                  <div className="bg-purple-50 p-3 rounded border border-purple-200">
                                    <div className="flex items-center gap-2 mb-2">
                                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                      <span className="text-sm font-medium text-purple-900">Location Criteria</span>
                                    </div>
                                    <div className="text-xs text-purple-700 space-y-1">
                                      {matchingCriteria.location_criteria?.states?.length > 0 && (
                                        <div>States: {matchingCriteria.location_criteria.states.join(', ')}</div>
                                      )}
                                      {matchingCriteria.location_criteria?.cities?.length > 0 && (
                                        <div>Cities: {matchingCriteria.location_criteria.cities.join(', ')}</div>
                                      )}
                                      {matchingCriteria.location_criteria?.countries?.length > 0 && (
                                        <div>Countries: {matchingCriteria.location_criteria.countries.join(', ')}</div>
                                      )}
                                      {(!matchingCriteria.location_criteria?.states?.length && 
                                        !matchingCriteria.location_criteria?.cities?.length && 
                                        !matchingCriteria.location_criteria?.countries?.length) && (
                                        <div className="text-gray-500">No location criteria available</div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* News Cards */}
                        <div className="space-y-4">
                          {matchingNews.map((newsItem: any) => {
                            const isExpanded = expandedNewsId === newsItem.news_id;
                            const scoreColor = (newsItem.score || 0) >= 80 ? 'bg-green-600' : 
                                              (newsItem.score || 0) >= 60 ? 'bg-blue-600' :
                                              (newsItem.score || 0) >= 40 ? 'bg-yellow-600' : 'bg-orange-600';

                            return (
                              <Card 
                                key={newsItem.news_id} 
                                className="border shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                                onClick={() => setExpandedNewsId(isExpanded ? null : newsItem.news_id)}
                              >
                                <CardHeader className="pb-3">
                                  <div className="flex justify-between items-start">
                                    <div className="flex-1 min-w-0">
                                      <div className="flex items-center gap-2 mb-2">
                                        <div className="flex-1">
                                          <h3 className="text-lg font-medium line-clamp-2">
                                            <span className="text-blue-700 hover:underline text-left hover:text-blue-800 transition-colors">
                                              {newsItem.headline || 'Untitled News'}
                                            </span>
                                          </h3>
                                        </div>
                                        {newsItem.source_url && (
                                          <a
                                            href={newsItem.source_url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-gray-400 hover:text-gray-600 transition-colors"
                                            title="View original article"
                                            onClick={(e) => e.stopPropagation()}
                                          >
                                            <ExternalLink className="h-4 w-4" />
                                          </a>
                                        )}
                                      </div>
                                      
                                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                                        <div className="flex items-center gap-1">
                                          <DollarSign className="h-4 w-4" />
                                          <span className="font-semibold">
                                            {newsItem.deal_size ? (() => {
                                              if (newsItem.deal_size >= 1000) {
                                                return `$${(newsItem.deal_size / 1000).toFixed(1)}B`;
                                              }
                                              return `$${newsItem.deal_size}M`;
                                            })() : 'N/A'}
                                          </span>
                                        </div>
                                        <div className="flex items-center gap-1">
                                          <Calendar className="h-4 w-4" />
                                          <span>
                                            {newsItem.publication_date 
                                              ? new Date(newsItem.publication_date).toLocaleDateString() 
                                              : 'N/A'}
                                          </span>
                                        </div>
                                        <div className="flex items-center gap-1">
                                          <Globe className="h-4 w-4" />
                                          <span>{newsItem.source_name || 'Unknown Source'}</span>
                                        </div>

                                      </div>

                                      {/* Property and Location Details */}
                                      <div className="flex flex-wrap gap-1 mb-2">
                                        {newsItem.property_type && (
                                          <Badge variant="outline" className="text-xs">
                                            {Array.isArray(newsItem.property_type) 
                                              ? newsItem.property_type.join(', ') 
                                              : newsItem.property_type}
                                          </Badge>
                                        )}
                                        {newsItem.location_city && (
                                          <Badge variant="outline" className="text-xs">
                                            📍 {Array.isArray(newsItem.location_city) 
                                              ? newsItem.location_city.join(', ') 
                                              : newsItem.location_city}
                                          </Badge>
                                        )}
                                        {newsItem.location_state && (
                                          <Badge variant="outline" className="text-xs">
                                            {Array.isArray(newsItem.location_state) 
                                              ? newsItem.location_state.join(', ') 
                                              : newsItem.location_state}
                                          </Badge>
                                        )}
                                      </div>

                                      {/* Summary */}
                                      {newsItem.summary && (
                                        <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                                          {newsItem.summary}
                                        </p>
                                      )}
                                    </div>

                                    {/* Score Badge */}
                                    <div className="ml-4 flex flex-col items-end">
                                      <div className={`${scoreColor} text-white text-xs font-semibold px-2 py-1 rounded shadow`}>
                                        {newsItem.score}%
                                      </div>
                                      <div className="text-xs text-gray-500 mt-1">Match Score</div>
                                    </div>
                                  </div>

                                  {/* Top Match Indicators */}
                                  {newsItem.breakdown && newsItem.breakdown.length > 0 && (
                                    <div className="flex flex-wrap gap-1 mt-2">
                                      {newsItem.breakdown.slice(0, 3).map((b: any, idx: number) => (
                                        b.score > 0 && (
                                          <Badge key={idx} variant="outline" className="text-xs">
                                            {b.field.replace('_', ' ')}: {b.score}%
                                          </Badge>
                                        )
                                      ))}
                                    </div>
                                  )}
                                </CardHeader>
                                
                                <CardContent className="pt-0">
                                  <div className="flex gap-2 mb-3">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="flex-1"
                                      onClick={() => setExpandedNewsId(isExpanded ? null : newsItem.news_id)}
                                    >
                                      {isExpanded ? "Hide Details" : "Show Details"}
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => window.open(`/dashboard/dealnews/extracted-deals/${newsItem.enrichment_id}`, '_blank')}
                                      className="flex items-center gap-1"
                                    >
                                      <ExternalLink className="h-4 w-4" />
                                      View Full
                                    </Button>
                                  </div>

                                  {isExpanded && (
                                    <div className="space-y-4 border-t pt-4">
                                      {/* Deal Information */}
                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                          <h4 className="font-semibold text-sm">Deal Information</h4>
                                          {newsItem.deal_type && (
                                            <div className="text-sm">
                                              <span className="font-medium">Type:</span> {
                                                Array.isArray(newsItem.deal_type) 
                                                  ? newsItem.deal_type.join(', ') 
                                                  : newsItem.deal_type
                                              }
                                            </div>
                                          )}
                                          {newsItem.deal_status && (
                                            <div className="text-sm">
                                              <span className="font-medium">Status:</span> {newsItem.deal_status}
                                            </div>
                                          )}
                                          {newsItem.square_footage && (
                                            <div className="text-sm">
                                              <span className="font-medium">Size:</span> {newsItem.square_footage.toLocaleString()} sq ft
                                            </div>
                                          )}
                                          {newsItem.deal_size && (
                                            <div className="text-sm">
                                              <span className="font-medium">Deal Size:</span> {(() => {
                                                if (newsItem.deal_size >= 1000) {
                                                  return `$${(newsItem.deal_size / 1000).toFixed(1)}B`;
                                                }
                                                return `$${newsItem.deal_size}M`;
                                              })()}
                                            </div>
                                          )}
                                          {newsItem.unit_count && (
                                            <div className="text-sm">
                                              <span className="font-medium">Units:</span> {newsItem.unit_count}
                                            </div>
                                          )}
                                        </div>

                                        <div className="space-y-2">
                                          <h4 className="font-semibold text-sm">Parties Involved</h4>
                                          {newsItem.buyer_name && (
                                            <div className="text-sm">
                                              <span className="font-medium">Buyer:</span> {
                                                Array.isArray(newsItem.buyer_name) 
                                                  ? newsItem.buyer_name.join(', ') 
                                                  : newsItem.buyer_name
                                              }
                                            </div>
                                          )}
                                          {newsItem.seller_name && (
                                            <div className="text-sm">
                                              <span className="font-medium">Seller:</span> {
                                                Array.isArray(newsItem.seller_name) 
                                                  ? newsItem.seller_name.join(', ') 
                                                  : newsItem.seller_name
                                              }
                                            </div>
                                          )}
                                          {newsItem.lender_name && (
                                            <div className="text-sm">
                                              <span className="font-medium">Lender:</span> {
                                                Array.isArray(newsItem.lender_name) 
                                                  ? newsItem.lender_name.join(', ') 
                                                  : newsItem.lender_name
                                              }
                                            </div>
                                          )}
                                        </div>
                                      </div>

                                      {/* Scoring Breakdown */}
                                      {newsItem.breakdown && newsItem.breakdown.length > 0 && (
                                        <div className="space-y-2">
                                          <h4 className="font-semibold text-sm">Matching Breakdown</h4>
                                          {newsItem.breakdown.map((b: any, idx: number) => (
                                            <div key={idx} className="bg-white border rounded p-2">
                                              <div className="flex items-center justify-between mb-1">
                                                <span className="font-medium text-sm capitalize">{b.field.replace('_', ' ')}</span>
                                                <div className="flex items-center gap-2">
                                                  <span className="text-sm font-semibold">{b.score}%</span>
                                                  {b.weight && (
                                                    <Badge variant="outline" className="text-xs">
                                                      Weight: {(b.weight * 100).toFixed(1)}%
                                                    </Badge>
                                                  )}
                                                </div>
                                              </div>
                                              <div className="w-full bg-gray-200 rounded-full h-2">
                                                <div
                                                  className={`h-2 rounded-full ${
                                                    b.score >= 80 ? 'bg-green-500' :
                                                    b.score >= 60 ? 'bg-blue-500' :
                                                    b.score >= 40 ? 'bg-yellow-500' :
                                                    b.score > 0 ? 'bg-orange-500' : 'bg-gray-300'
                                                  }`}
                                                  style={{ width: `${b.score}%` }}
                                                ></div>
                                              </div>
                                              <div className="text-xs text-gray-600 mt-1">{b.reason}</div>
                                            </div>
                                          ))}
                                        </div>
                                      )}

                                      {/* Summary Reasons */}
                                      {newsItem.reasons && newsItem.reasons.length > 0 && (
                                        <div className="bg-blue-50 border border-blue-200 rounded p-2">
                                          <div className="font-semibold text-sm text-blue-800 mb-1">Key Matches:</div>
                                          <ul className="text-xs text-blue-700 space-y-0.5">
                                            {newsItem.reasons.map((reason: string, idx: number) => (
                                              <li key={idx} className="flex items-center gap-1">
                                                <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                                                {reason}
                                              </li>
                                            ))}
                                          </ul>
                                        </div>
                                      )}
                                    </div>
                                  )}
                                </CardContent>
                              </Card>
                            );
                          })}
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
              <TabsContent value="email-threads">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Mail className="h-5 w-5" />
                      Email Threads
                      {gmailThreads.length > 0 && (
                        <Badge variant="secondary" className="ml-2">
                          {gmailThreads.length} threads found
                        </Badge>
                      )}
                    </CardTitle>
                    <CardDescription>
                      Email conversations and threads for this contact (sorted by newest first)
                    </CardDescription>
                    <div className="flex items-center gap-2 mt-2">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => setMessageSortOrder(messageSortOrder === 'asc' ? 'desc' : 'asc')}
                        className="flex items-center gap-1"
                      >
                        <Clock className="h-4 w-4" />
                        Sort: {messageSortOrder === 'asc' ? 'Oldest First' : 'Newest First'}
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {!contact?.email ? (
                      <div className="text-center py-8">
                        <Mail className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <div className="text-gray-500">No email address available for this contact.</div>
                      </div>
                    ) : loadingGmailThreads ? (
                      <div className="text-center py-8">
                        <div className="flex flex-col items-center space-y-4">
                          <div className="w-10 h-10 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                          <p className="text-gray-600">Loading email threads...</p>
                        </div>
                      </div>
                    ) : gmailThreads.length === 0 ? (
                      <div className="text-center py-8">
                        <Mail className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <div className="text-gray-500">No email threads found for this contact.</div>
                        <div className="text-sm text-gray-400 mt-2">
                          Email: {contact.email}
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {gmailThreads.map((thread) => {
                          // Build a set of your Gmail account emails (lowercased)
                          const accountEmails = (gmailAccounts as any[]).map((acc: any) => acc.email.toLowerCase());
                          const senderEmail = thread.sender;
                          const recipientList = Array.isArray(thread.recipients) ? thread.recipients : (thread.recipients ? [thread.recipients] : []);
                          const senderIsAccount = senderEmail && accountEmails.includes(senderEmail.toLowerCase());
                          const recipientAccount = recipientList.find((r: any) => r && accountEmails.includes(r.toLowerCase()));
                          let badgeText = '';
                          let isSent = false;
                          if (senderIsAccount) {
                            badgeText = `Sent by ${senderEmail}`;
                            isSent = true;
                          } else if (recipientAccount) {
                            badgeText = `Received by ${recipientAccount}`;
                            isSent = false;
                          }
                          
                          return (
                            <Card key={thread.thread_id} className="border hover:shadow-md transition-shadow">
                              <CardHeader className="pb-3">
                                <div className="flex items-center justify-between">
                                  <div 
                                    className="flex items-center gap-2 cursor-pointer flex-1"
                                    onClick={() => handleExpandGmailThread(thread.thread_id)}
                                  >
                                    <div className="text-blue-600">
                                      {expandedGmailThread === thread.thread_id ? "▼" : "▶"}
                                    </div>
                                    <div className="flex-1">
                                      <h3 className="font-medium text-blue-700 hover:text-blue-800">
                                        {thread.subject || "(No Subject)"}
                                      </h3>
                                      <div className="text-sm text-gray-500 mt-1">
                                        {new Date(thread.created_at).toLocaleString()}
                                      </div>
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    {badgeText && (
                                      <Badge className={isSent ? 'bg-blue-600 text-white' : 'bg-green-600 text-white'}>
                                        {badgeText}
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </CardHeader>
                              
                              {expandedGmailThread === thread.thread_id && (
                                <CardContent className="pt-0">
                                  <div className="border-t pt-4">
                                    <h4 className="font-semibold mb-3 text-sm flex items-center gap-2">
                                      <MessageSquare className="h-4 w-4" />
                                      Messages ({gmailThreadMessages[thread.thread_id]?.length || 0})
                                    </h4>
                                    <div className="space-y-3">
                                      {[...(gmailThreadMessages[thread.thread_id] || [])]
                                        .sort((a, b) => {
                                          const aTime = new Date(a.sent_at || a.created_at).getTime();
                                          const bTime = new Date(b.sent_at || b.created_at).getTime();
                                          return messageSortOrder === 'asc' ? aTime - bTime : bTime - aTime;
                                        })
                                        .map((msg) => {
                                          const isSent = (msg.sender && msg.sender.toLowerCase && userEmails.includes(msg.sender.toLowerCase()));
                                          const senderEmail = msg.sender;
                                          const recipientEmails = (msg.recipients || []).join(', ');
                                          const badgeText = isSent ? `Sent by ${senderEmail}` : `Received by ${userEmails[0] || recipientEmails}`;
                                          
                                          return (
                                            <div key={msg.id} className={`p-4 border rounded-lg ${isSent ? 'bg-blue-50 border-blue-200' : 'bg-green-50 border-green-200'}`}>
                                              <div className="flex flex-col sm:flex-row gap-4">
                                                <div className="flex-shrink-0 flex flex-col items-center">
                                                  <Avatar className="h-8 w-8 mb-1">
                                                    <AvatarFallback>
                                                      {(isSent ? senderEmail : userEmails[0] || recipientEmails)?.[0]?.toUpperCase() || '?'}
                                                    </AvatarFallback>
                                                  </Avatar>
                                                  <Badge className={`text-xs ${isSent ? 'bg-blue-600 text-white' : 'bg-green-600 text-white'}`}>
                                                    {isSent ? 'Sent' : 'Received'}
                                                  </Badge>
                                                </div>
                                                <div className="flex-1 min-w-0">
                                                  <div className="flex flex-wrap gap-4 text-xs text-gray-500 mb-2">
                                                    <span><strong>From:</strong> {senderEmail}</span>
                                                    <span><strong>To:</strong> {recipientEmails}</span>
                                                    <span><strong>Date:</strong> {new Date(msg.sent_at || msg.created_at).toLocaleString()}</span>
                                                  </div>
                                                  <div className="font-semibold mb-2 text-base">{msg.subject}</div>
                                                  <div className="text-sm prose max-w-none" dangerouslySetInnerHTML={{ __html: msg.body }} />
                                                </div>
                                              </div>
                                            </div>
                                          );
                                        })}
                                    </div>
                                  </div>
                                </CardContent>
                              )}
                            </Card>
                          );
                        })}
                        

                        
                        {/* Load More Button */}
                        {hasMoreThreads && (
                          <div className="text-center mt-6 p-4 border-2 border-dashed border-blue-300 bg-blue-50 rounded-lg">
                            <div className="mb-2 text-sm text-blue-600">
                              Showing {gmailThreads.length} of {gmailThreads.length + (threadsPage * 50)} threads
                            </div>
                            <Button 
                              onClick={loadMoreThreads}
                              disabled={loadingMoreThreads}
                              variant="default"
                              className="w-full max-w-xs"
                            >
                              {loadingMoreThreads ? (
                                <>
                                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                  Loading more threads...
                                </>
                              ) : (
                                <>
                                  <Mail className="h-4 w-4 mr-2" />
                                  Load More Threads ({threadsPage + 1})
                                </>
                              )}
                            </Button>
                          </div>
                        )}
                        
                        {/* Show message when no more threads */}
                        {!hasMoreThreads && gmailThreads.length > 0 && (
                          <div className="text-center mt-6 p-4 text-gray-500">
                            <Mail className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                            All {gmailThreads.length} threads loaded
                          </div>
                        )}
                        
                        {/* Loading indicator for more threads */}
                        {loadingMoreThreads && (
                          <div className="text-center text-gray-500 mt-4">
                            <div className="flex items-center justify-center gap-2">
                              <RefreshCw className="h-4 w-4 animate-spin" />
                              Loading more email threads...
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
              <TabsContent value="transcripts">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Fireflies Meeting Transcripts
                      {contactTranscripts.length > 0 && (
                        <Badge variant="secondary" className="ml-2">
                          {contactTranscripts.length} meetings found
                        </Badge>
                      )}
                    </CardTitle>
                    <CardDescription>
                      Fireflies meeting transcripts where this contact participated
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {isLoadingTranscripts ? (
                      <div className="text-center py-8">
                        <div className="flex flex-col items-center space-y-4">
                          <div className="w-10 h-10 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                          <p className="text-gray-600">Loading transcripts...</p>
                        </div>
                      </div>
                    ) : contactTranscripts.length === 0 ? (
                      <div className="text-center py-8">
                        <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <div className="text-gray-500">No meeting transcripts found for this contact.</div>
                        <div className="text-sm text-gray-400 mt-2">
                          This contact hasn't participated in any recorded meetings yet.
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {contactTranscripts.map((transcript) => (
                          <Card key={transcript.id} className="border hover:shadow-md transition-shadow">
                            <CardHeader className="pb-3">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <h3 className="font-semibold text-lg mb-1">{transcript.title}</h3>
                                  <div className="flex flex-wrap gap-2 text-xs text-gray-500 mb-2">
                                    <span>Date: <span className="font-medium text-gray-700">
                                      {new Date(transcript.meeting_date).toLocaleString()}
                                    </span></span>
                                    <span>Duration: <span className="font-medium text-gray-700">
                                      {transcript.duration}m
                                    </span></span>
                                    <span>Account: <span className="font-medium text-gray-700">
                                      {transcript.account_name}
                                    </span></span>
                                  </div>
                                  <div className="text-sm text-gray-600 mb-2">
                                    <strong>Participants:</strong>
                                    <div className="flex flex-wrap gap-1 mt-1">
                                      {transcript.participants.map((participant: string, index: number) => (
                                        <span 
                                          key={index} 
                                          className={`inline-block px-2 py-1 text-xs rounded-full ${
                                            participant.toLowerCase() === contact?.email?.toLowerCase()
                                              ? 'bg-blue-600 text-white font-medium'
                                              : 'bg-blue-100 text-blue-800'
                                          }`}
                                        >
                                          {participant}
                                        </span>
                                      ))}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </CardHeader>
                            <CardContent>
                              <div className="text-sm text-gray-700 max-h-64 overflow-y-auto border border-gray-200 rounded p-3 bg-white">
                                <div className="flex items-center justify-between mb-2">
                                  <strong>Transcript:</strong>
                                  <span className="text-xs text-gray-500">
                                    {transcript.transcript_text ? 
                                      `${Math.ceil(transcript.transcript_text.length / 1000)}k characters` : 
                                      'No content'
                                    }
                                  </span>
                                </div>
                                <div 
                                  className="mt-1 whitespace-pre-wrap text-xs leading-relaxed font-mono bg-gray-50 p-2 rounded border"
                                  style={{ 
                                    maxHeight: '300px',
                                    overflowY: 'auto'
                                  }}
                                >
                                  {transcript.sentences && transcript.sentences.length > 0 ? (
                                    transcript.sentences.map((sentence: any, index: number) => (
                                      <div key={index} className="mb-2 p-2 bg-white rounded border-l-4 border-blue-200">
                                        <div className="font-semibold text-blue-700 mb-1">
                                          {sentence.speaker_name || 'Unknown Speaker'}:
                                        </div>
                                        <div className="text-gray-800">
                                          {sentence.text || sentence.raw_text || ''}
                                        </div>
                                      </div>
                                    ))
                                  ) : transcript.transcript_text ? (
                                    transcript.transcript_text.split('\n\n').map((paragraph: string, index: number) => (
                                      <div key={index} className="mb-2 p-2 bg-white rounded border-l-4 border-blue-200">
                                        {paragraph}
                                      </div>
                                    ))
                                  ) : (
                                    <span className="text-gray-500 italic">No transcript content available</span>
                                  )}
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContactDetail;
