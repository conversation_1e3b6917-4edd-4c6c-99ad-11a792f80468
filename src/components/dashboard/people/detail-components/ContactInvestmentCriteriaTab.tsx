'use client'

import React, { useState } from 'react'
import { Contact } from '../shared/types'
import InvestmentCriteriaSlider from '../../investment-criteria/InvestmentCriteriaSlider'
import InvestmentCriteriaAddModal from '../../investment-criteria/InvestmentCriteriaAddModal'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'

interface ContactInvestmentCriteriaTabProps {
  contact: Contact
}

export default function ContactInvestmentCriteriaTab({ contact }: ContactInvestmentCriteriaTabProps) {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)

  const handleAddSuccess = () => {
    // Refresh the investment criteria slider by updating the key
    setRefreshKey(prev => prev + 1)
  }

  return (
    <div className="space-y-6">
      {/* Header with Add Button */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Investment Criteria</h3>
          <p className="text-sm text-gray-600">Manage investment criteria for this contact</p>
        </div>
        <Button
          onClick={() => setIsAddModalOpen(true)}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Investment Criteria
        </Button>
      </div>

      {/* Investment Criteria Slider */}
      <InvestmentCriteriaSlider
        key={refreshKey}
        entityType="contact"
        entityId={contact.contact_id}
        entityEmail={contact.email}
        entityName={`${contact.first_name} ${contact.last_name}`}
      />

      {/* Add Investment Criteria Modal */}
      <InvestmentCriteriaAddModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        entityType="contact"
        entityId={contact.contact_id}
        entityName={`${contact.first_name} ${contact.last_name}`}
        onSuccess={handleAddSuccess}
      />
    </div>
  )
} 