'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Thread } from "../ContactDetail"
import { But<PERSON> } from "@/components/ui/button"

interface ContactMessagingTabProps {
  threads: Thread[];
  onEditMessage: (message: any) => void;
  onDeleteMessage: (messageId: string) => void;
  onSyncMessage: (message: any) => void;
}

export default function ContactMessagingTab({ threads, onEditMessage, onDeleteMessage, onSyncMessage }: ContactMessagingTabProps) {

  if (threads.length === 0) {
    return (
      <div className="mt-6 text-center text-gray-500">
        No messaging history found for this contact.
      </div>
    )
  }

  return (
    <div className="space-y-6 mt-6">
      {threads.map((thread) => (
        <Card key={thread.thread_id}>
          <CardHeader>
            <div className="flex justify-between items-center">
                <CardTitle>{thread.subject || "No Subject"}</CardTitle>
                <Badge variant="outline">{thread.status}</Badge>
            </div>
            <CardDescription>
              {thread.messages.length} message{thread.messages.length !== 1 ? 's' : ''} in this thread.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {thread.messages.map(message => (
              <div key={message.message_id} className="p-4 border rounded-lg bg-gray-50">
                <div className="flex justify-between items-start mb-2">
                    <div>
                        <div className="text-sm font-medium">
                            {message.direction === 'outbound' ? `To: ${message.to_email}` : `From: ${message.from_email}`}
                        </div>
                        <div className="text-xs text-gray-500">
                            {new Date(message.sent_at || message.created_at).toLocaleString()}
                        </div>
                    </div>
                    <Badge variant={message.direction === 'outbound' ? "default" : "secondary"}>
                        {message.direction}
                    </Badge>
                </div>
                <div className="text-sm prose max-w-none" dangerouslySetInnerHTML={{ __html: message.body }} />
                <div className="flex justify-end space-x-2 mt-4">
                    <Button variant="ghost" size="sm" onClick={() => onEditMessage(message)}>Edit</Button>
                    <Button variant="ghost" size="sm" onClick={() => onSyncMessage(message)}>Sync</Button>
                    <Button 
                      variant="destructive" 
                      size="sm" 
                      onClick={() => {
                        if (confirm(`Are you sure you want to delete this message? This action cannot be undone.`)) {
                          onDeleteMessage(message.message_id);
                        }
                      }}
                    >
                      Delete
                    </Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      ))}
    </div>
  )
} 