'use client'

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, MapPin, Building, Users, DollarSign, Home, Hash, Map, Search, Brain, FileText, CheckCircle, AlertCircle, Linkedin, Twitter, Facebook, Instagram, Youtube, Edit, Save, X, Plus, Trash2 } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import { Contact } from '../shared/types';

interface EnrichmentData {
  executive_summary?: string;
  career_timeline?: any[];
  notable_activities?: any[];
  education?: any[];
  personal_tidbits?: any[];
  company_type?: string;
  capital_positions?: any[];
  status?: string;
  completed_at?: string;
}

interface ContactOverviewTabProps {
  contact: Contact;
}

export default function ContactOverviewTab({ contact }: ContactOverviewTabProps) {
  const [enrichmentData, setEnrichmentData] = useState<EnrichmentData | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchEnrichmentData = async () => {
      if (!contact.contact_id) return;
      
      setLoading(true);
      try {
        const response = await fetch(`/api/contacts/${contact.contact_id}/extracted-data`);
        if (response.ok) {
          const data = await response.json();
          setEnrichmentData(data);
        } else {
          console.error('Failed to fetch enrichment data');
        }
      } catch (error) {
        console.error('Error fetching enrichment data:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchEnrichmentData();
  }, [contact.contact_id]);



  const investmentCriteria = [
    { title: "Deal Size", value: contact.investment_criteria_deal_size, icon: <DollarSign className="h-5 w-5" /> },
    { title: "Property Types", value: contact.investment_criteria_property_type, icon: <Home className="h-5 w-5" /> },
    { title: "Asset Types", value: contact.investment_criteria_asset_type, icon: <Building className="h-5 w-5" /> },
    { title: "Loan Types", value: contact.investment_criteria_loan_type, icon: <Hash className="h-5 w-5" /> },
    { title: "Countries", value: contact.investment_criteria_country, icon: <Map className="h-5 w-5" /> },
    { title: "States", value: contact.investment_criteria_state, icon: <Map className="h-5 w-5" /> },
    { title: "Cities", value: contact.investment_criteria_city, icon: <Map className="h-5 w-5" /> },
  ].filter(item => item.value && (!Array.isArray(item.value) || item.value.length > 0));

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'failed': return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'processing': return <Search className="h-4 w-4 text-blue-600 animate-spin" />
      default: return <Search className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200'
      case 'failed': return 'bg-red-100 text-red-800 border-red-200'
      case 'processing': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const renderArrayField = (data: any[] | undefined, title: string, icon: React.ReactNode) => {
    if (!data || data.length === 0) return null;
    
    return (
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 mt-1">
          {icon}
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900">{title}</p>
          <div className="mt-1 flex flex-wrap gap-1">
            {data.map((item, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {typeof item === 'string' ? item : JSON.stringify(item)}
              </Badge>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderSocialMediaLink = (url: string | undefined, platform: string, icon: React.ReactNode) => {
    if (!url) return null;
    
    return (
      <a
        href={url}
        target="_blank"
        rel="noopener noreferrer"
        className="flex items-center space-x-2 text-blue-600 hover:text-blue-800 transition-colors"
      >
        {icon}
        <span className="text-sm">{platform}</span>
      </a>
    );
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-1/3" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Contact Information Card */}
      <Card>
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Personal Details */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Personal Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Name</Label>
                <p className="text-sm text-gray-600">
                  {contact.first_name && contact.last_name 
                    ? `${contact.first_name} ${contact.last_name}`
                    : contact.first_name || contact.last_name || 'Not provided'
                  }
                </p>
              </div>
              
              <div className="space-y-2">
                <Label>Job Title</Label>
                <p className="text-sm text-gray-600">{contact.title || 'Not provided'}</p>
              </div>

              <div className="space-y-2">
                <Label>Email</Label>
                <p className="text-sm text-gray-600">{contact.email || 'Not provided'}</p>
              </div>

              <div className="space-y-2">
                <Label>Phone Number</Label>
                <p className="text-sm text-gray-600">{contact.phone_number || 'Not provided'}</p>
              </div>

              <div className="space-y-2">
                <Label>Company Name</Label>
                <p className="text-sm text-gray-600">{contact.company_name || 'Not provided'}</p>
              </div>

              <div className="space-y-2">
                <Label>Contact City</Label>
                <p className="text-sm text-gray-600">{contact.contact_city || 'Not provided'}</p>
              </div>

              <div className="space-y-2">
                <Label>Contact State</Label>
                <p className="text-sm text-gray-600">{contact.contact_state || 'Not provided'}</p>
              </div>

              <div className="space-y-2">
                <Label>Contact Country</Label>
                <p className="text-sm text-gray-600">{contact.contact_country || 'Not provided'}</p>
              </div>
            </div>
          </div>

          <Separator />

          {/* Social Media Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Social Media</h3>
            <div className="flex flex-wrap gap-4">
              {renderSocialMediaLink(contact.linkedin_url, 'LinkedIn', <Linkedin className="h-4 w-4" />)}
              {renderSocialMediaLink(contact.twitter, 'Twitter', <Twitter className="h-4 w-4" />)}
              {renderSocialMediaLink(contact.facebook, 'Facebook', <Facebook className="h-4 w-4" />)}
              {renderSocialMediaLink(contact.instagram, 'Instagram', <Instagram className="h-4 w-4" />)}
              {renderSocialMediaLink(contact.youtube, 'YouTube', <Youtube className="h-4 w-4" />)}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enrichment Data Card */}
      {enrichmentData && (
        <Card>
          <CardHeader>
            <CardTitle>Enrichment Data</CardTitle>
          </CardHeader>
          

          <CardContent className="space-y-4">

          {/* Company Type and Capital Positions in same row */}
          {(enrichmentData.company_type || (enrichmentData.capital_positions && enrichmentData.capital_positions.length > 0)) && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {enrichmentData.company_type && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Company Type</h4>
                  <p className="text-sm text-gray-600">{enrichmentData.company_type}</p>
                </div>
              )}
              {enrichmentData.capital_positions && enrichmentData.capital_positions.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Capital Positions</h4>
                  <div className="flex flex-wrap gap-1">
                    {enrichmentData.capital_positions.map((item, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {typeof item === 'string' ? item : JSON.stringify(item)}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
            {enrichmentData.executive_summary && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Executive Summary</h4>
                <p className="text-sm text-gray-600">{enrichmentData.executive_summary}</p>
              </div>
            )}

            {enrichmentData.career_timeline && enrichmentData.career_timeline.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Career Timeline</h4>
                <div className="space-y-2">
                  {enrichmentData.career_timeline.map((item, index) => (
                    <div key={index} className="text-sm text-gray-600">
                      {typeof item === 'string' ? item : JSON.stringify(item)}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {enrichmentData.education && enrichmentData.education.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Education</h4>
                <div className="space-y-2">
                  {enrichmentData.education.map((item, index) => (
                    <div key={index} className="text-sm text-gray-600">
                      {typeof item === 'string' ? item : JSON.stringify(item)}
                    </div>
                  ))}
                </div>
              </div>
            )}



            {enrichmentData.status && (
              <div className="flex items-center space-x-2">
                {getStatusIcon(enrichmentData.status)}
                <Badge className={getStatusColor(enrichmentData.status)}>
                  {enrichmentData.status}
                </Badge>
                {enrichmentData.completed_at && (
                  <span className="text-sm text-gray-500">
                    Completed: {new Date(enrichmentData.completed_at).toLocaleDateString()}
                  </span>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
} 