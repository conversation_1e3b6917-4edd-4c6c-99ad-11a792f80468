'use client'

import React from 'react';
import OriginalTrainingDataTab from '../TrainingDataTab';
import { Contact } from "../shared/types";

interface TrainingDataTabProps {
  contactId: string | number;
  contact: Contact;
  normalizedScrapedData?: any;
}

export default function TrainingDataTab({ contactId, contact, normalizedScrapedData }: TrainingDataTabProps) {
  // Add the missing recent_activities property required by the original component
  const contactWithActivities = {
    ...contact,
    recent_activities: [] // Provide an empty array as default
  };

  return (
    <OriginalTrainingDataTab
      contactId={contactId}
      contact={contactWithActivities}
      normalizedScrapedData={normalizedScrapedData}
    />
  );
} 