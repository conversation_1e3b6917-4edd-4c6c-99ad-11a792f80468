'use client'

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Save, X, Edit, User, Building, Globe, Mail, Phone, Linkedin, Twitter, Facebook, Instagram, Youtube, Plus, Trash2, Target, Brain, Briefcase, ChevronDown, ExternalLink, AlertTriangle, Building2, MapPin, Search, Loader2, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';
import { debounce } from 'lodash';
import Link from 'next/link';
import { Contact, CompanySuggestion } from '../shared/types';
import { contactFieldGroups, validateField, contactDbToForm, contactFormToDb } from '../shared/contactFields';
import { FilterFieldWithAdd } from '../components/FilterFieldWithAdd';

interface ContactEditTabProps {
  contact: Contact;
  onSave: (updatedContact: Contact) => Promise<void>;
  onCancel: () => void;
}

export default function ContactEditTab({ contact, onSave, onCancel }: ContactEditTabProps) {
  // Convert database contact to form format for editing
  const [editedContact, setEditedContact] = useState<Contact>(contact);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [arrayInputValues, setArrayInputValues] = useState<Record<string, string>>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [changedFields, setChangedFields] = useState<Set<string>>(new Set());
  
  // Form options for dropdowns
  const [formOptions, setFormOptions] = useState<{
    companyTypes: string[]
    capitalPositions: string[]
    jobTiers: string[]
    contactTypes: string[]
    decisionMakingRoles: string[]
  }>({
    companyTypes: [],
    capitalPositions: [],
    jobTiers: [],
    contactTypes: [],
    decisionMakingRoles: []
  });
  const [isLoadingFormOptions, setIsLoadingFormOptions] = useState(true);

  // Search and suggestion state
  const [emailSuggestions, setEmailSuggestions] = useState<any[]>([]);
  const [showEmailSuggestions, setShowEmailSuggestions] = useState(false);
  const [selectedEmailSuggestionIndex, setSelectedEmailSuggestionIndex] = useState(-1);
  const [isSearchingEmail, setIsSearchingEmail] = useState(false);
  const [activeEmailField, setActiveEmailField] = useState<'email' | 'additional_email' | 'name_search' | null>(null);
  const emailSuggestionsRef = useRef<HTMLDivElement>(null);

  // Company search and selection state
  const [isSearching, setIsSearching] = useState(false);
  const [companySuggestions, setCompanySuggestions] = useState<CompanySuggestion[]>([]);
  const [showCompanySuggestions, setShowCompanySuggestions] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<CompanySuggestion | null>(null);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const [isLoadingCompanyData, setIsLoadingCompanyData] = useState(false);
  const companyInputRef = useRef<HTMLInputElement>(null);
  const companySuggestionsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Check if there are any changes based on changedFields
    setHasChanges(changedFields.size > 0);
  }, [changedFields]);

  // Fetch form options from database mappings
  useEffect(() => {
    const fetchFormOptions = async () => {
      setIsLoadingFormOptions(true);
      try {
        const [mappingResponse, jobTiersRes, contactTypesRes, decisionRolesRes] = await Promise.all([
          fetch('/api/mapping-tables/form-options'),
          fetch('/api/contacts/filter-options?type=job_tiers'),
          fetch('/api/contacts/filter-options?type=contact_types'),
          fetch('/api/contacts/filter-options?type=decision_making_roles')
        ]);

        const mappingResult = await mappingResponse.json();
        const jobTiersData = await jobTiersRes.json();
        const contactTypesData = await contactTypesRes.json();
        const decisionRolesData = await decisionRolesRes.json();

        setFormOptions({
          companyTypes: mappingResult.success ? mappingResult.data.companyTypes || [] : [],
          capitalPositions: mappingResult.success ? mappingResult.data.capitalPositions || [] : [],
          jobTiers: jobTiersData.success ? jobTiersData.data.map((item: any) => item.value) : [],
          contactTypes: contactTypesData.success ? contactTypesData.data.map((item: any) => item.value) : [],
          decisionMakingRoles: decisionRolesData.success ? decisionRolesData.data.map((item: any) => item.value) : []
        });
      } catch (error) {
        console.error('Error fetching form options:', error);
      } finally {
        setIsLoadingFormOptions(false);
      }
    };

    fetchFormOptions();
  }, []);

  // Click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Handle email suggestions
      if (emailSuggestionsRef.current && !emailSuggestionsRef.current.contains(event.target as Node)) {
        const emailSearchResults = document.querySelector('[data-email-search-results]');
        if (emailSearchResults && emailSearchResults.contains(event.target as Node)) {
          return;
        }
        setShowEmailSuggestions(false);
        setSelectedEmailSuggestionIndex(-1);
      }
      
      // Handle company suggestions
      if (companyInputRef.current && !companyInputRef.current.contains(event.target as Node)) {
        const companySearchResults = document.querySelector('[data-company-search-results]');
        if (companySearchResults && companySearchResults.contains(event.target as Node)) {
          return;
        }
        setShowCompanySuggestions(false);
        setSelectedSuggestionIndex(-1);
      }
    };

      document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Debounced name search function
  const debouncedSearchByName = useCallback(
    debounce(async (firstName: string, lastName: string) => {
      console.log('Name search called with:', firstName, lastName);
      
      if (!firstName || !lastName || (firstName.length + lastName.length < 4)) {
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
        setIsSearchingEmail(false);
        return;
      }

      setIsSearchingEmail(true);
      setActiveEmailField('name_search');
      
      try {
        const fullName = `${firstName} ${lastName}`;
        const response = await fetch(`/api/contacts?search=${encodeURIComponent(fullName)}&limit=5`);
        
        if (response.ok) {
          const data = await response.json();
          console.log('Name search results:', data);
          setEmailSuggestions(data.contacts || []);
          setShowEmailSuggestions(data.contacts && data.contacts.length > 0);
        } else {
          setEmailSuggestions([]);
          setShowEmailSuggestions(false);
        }
      } catch (error) {
        console.error('Error during name search:', error);
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
      } finally {
        setIsSearchingEmail(false);
      }
    }, 500),
    []
  );

  // Debounced email search function
  const debouncedSearchEmails = useCallback(
    debounce(async (email: string, fieldName: 'email' | 'additional_email') => {
      console.log('Email search called with:', email, 'Field:', fieldName);
      
      if (email.length < 3) {
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
        setIsSearchingEmail(false);
        return;
      }

      setIsSearchingEmail(true);
      setActiveEmailField(fieldName);
      
      try {
        const response = await fetch(`/api/contacts/search-by-email?email=${encodeURIComponent(email)}`);
        
        if (response.ok) {
          const data = await response.json();
          console.log('Email search results:', data);
          setEmailSuggestions(data.contacts || []);
          setShowEmailSuggestions(data.contacts && data.contacts.length > 0);
        } else {
          setEmailSuggestions([]);
          setShowEmailSuggestions(false);
        }
      } catch (error) {
        console.error('Error during email search:', error);
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
      } finally {
        setIsSearchingEmail(false);
      }
    }, 500),
    []
  );

  // Email tracing function
  const traceEmailChanges = (fieldName: string, oldValue: string, newValue: string) => {
    if (oldValue !== newValue && newValue) {
      console.log(`📧 Email ${fieldName} changed:`, {
        from: oldValue,
        to: newValue,
        timestamp: new Date().toISOString(),
        contactName: `${editedContact.first_name} ${editedContact.last_name}`.trim(),
        contactId: editedContact.contact_id,
        company: editedContact.company_name
      });
      
      // You can add additional logging or analytics here
      // For example, send to analytics service, log to database, etc.
    }
  };

  // Debounced company search function
  const debouncedSearchCompanies = useCallback(
    debounce(async (searchTerm: string) => {
      console.log('Company search called with:', searchTerm);
      
      if (searchTerm.length < 2) {
        setCompanySuggestions([]);
        setShowCompanySuggestions(false);
        setIsSearching(false);
        return;
      }

      setIsSearching(true);
      try {
        const response = await fetch(`/api/companies/search?q=${encodeURIComponent(searchTerm)}`);
        
        if (response.ok) {
          const suggestions = await response.json();
          console.log('Company search results:', suggestions);
          
          // Transform the search results to ensure extracted_data is properly structured
          const transformedSuggestions = suggestions.map((suggestion: any) => ({
            ...suggestion,
            extracted_data: suggestion.extracted_data
          }));
          
          setCompanySuggestions(transformedSuggestions);
          setShowCompanySuggestions(transformedSuggestions.length > 0);
        } else {
          console.error('Company search failed:', response.statusText);
          setCompanySuggestions([]);
          setShowCompanySuggestions(false);
        }
      } catch (error) {
        console.error('Error searching companies:', error);
        setCompanySuggestions([]);
        setShowCompanySuggestions(false);
      } finally {
        setIsSearching(false);
      }
    }, 300),
    []
  );

  // Fetch company data when company_id is provided
  const fetchCompanyData = async (companyId: number) => {
    setIsLoadingCompanyData(true);
    try {
      console.log('Fetching company data for ID:', companyId);
      const response = await fetch(`/api/companies/${companyId}`);
      if (response.ok) {
        const companyData = await response.json();
        console.log('Company data received:', companyData);
        
        // Transform the company data to match our CompanySuggestion interface
        const transformedCompany: CompanySuggestion = {
          company_id: companyData.company_id,
          company_name: companyData.company_name || '',
          company_website: companyData.company_website || '',
          industry: companyData.industry || '',
          company_address: companyData.company_address || '',
          company_city: companyData.company_city || '',
          company_state: companyData.company_state || '',
          company_country: companyData.company_country || '',
          company_zip: companyData.company_zip || '',
          extracted_data: companyData.scraped_data ? {
            companytype: companyData.scraped_data.companytype || undefined,
            businessmodel: companyData.scraped_data.businessmodel || undefined,
            fundsize: companyData.scraped_data.fundsize || companyData.scraped_data.aum || undefined,
            aum: companyData.scraped_data.aum || undefined,
            headquarters: companyData.scraped_data.headquarters || undefined,
            foundedyear: companyData.scraped_data.foundedyear || companyData.founded_year || undefined,
            numberofemployees: companyData.scraped_data.numberofemployees || undefined,
            investmentfocus: companyData.scraped_data.investmentfocus || undefined,
            geographicfocus: companyData.scraped_data.geographicfocus || undefined,
            dealsize: companyData.scraped_data.dealsize || undefined,
            minimumdealsize: companyData.scraped_data.minimumdealsize || undefined,
            maximumdealsize: companyData.scraped_data.maximumdealsize || undefined,
            investment_criteria_property_types: companyData.scraped_data.investment_criteria_property_types || companyData.scraped_data.propertytypes || undefined,
            investment_criteria_asset_types: companyData.scraped_data.investment_criteria_asset_types || companyData.scraped_data.assetclasses || undefined,
            investment_criteria_loan_types: companyData.scraped_data.investment_criteria_loan_types || undefined,
            investment_criteria_property_subcategories: companyData.scraped_data.investment_criteria_property_subcategories || undefined,
            riskprofile: companyData.scraped_data.riskprofile || undefined,
            targetmarkets: companyData.scraped_data.targetmarkets || undefined,
            strategies: companyData.scraped_data.strategies || undefined,
            propertytypes: companyData.scraped_data.propertytypes || undefined,
            assetclasses: companyData.scraped_data.assetclasses || undefined,
            valuecreation: companyData.scraped_data.valuecreation || undefined,
            holdperiod: companyData.scraped_data.holdperiod || undefined,
            targetreturn: companyData.scraped_data.targetreturn || undefined,
            approach: companyData.scraped_data.approach || undefined,
          } : undefined
        };

        console.log('Transformed company:', transformedCompany);
        return transformedCompany;
      } else {
        console.error('Failed to fetch company data:', response.statusText);
        return null;
      }
    } catch (error) {
      console.error('Error fetching company data:', error);
      toast.error('Failed to load company data');
      return null;
    } finally {
      setIsLoadingCompanyData(false);
    }
  };

  // Load company data on component mount if contact has company_id
  useEffect(() => {
    const loadCompanyData = async () => {
      if (contact.company_id) {
        const companyData = await fetchCompanyData(contact.company_id);
        if (companyData) {
          setSelectedCompany(companyData);
        }
      }
    };

    loadCompanyData();
  }, [contact.company_id]);

  // Handle email contact selection
  const handleEmailContactSelect = (contact: any) => {
    // Show duplicate warning and ask user what they want to do
    const message = `A contact with email "${contact.email}" already exists:\n\n` +
      `Name: ${contact.first_name} ${contact.last_name}\n` +
      `Company: ${contact.company_name || 'N/A'}\n` +
      `Title: ${contact.title || 'N/A'}\n\n` +
      `Would you like to:\n` +
      `- Edit the existing contact instead\n` +
      `- Continue editing current contact`;

    if (confirm(message)) {
      // User wants to edit existing contact - redirect to contact detail page
      window.open(`/dashboard/people/${contact.contact_id}`, '_blank');
    }
    
    // Clear email search suggestions
    setShowEmailSuggestions(false);
    setEmailSuggestions([]);
    setSelectedEmailSuggestionIndex(-1);
  };

  // Keyboard navigation for email suggestions
  const handleEmailKeyDown = (e: React.KeyboardEvent) => {
    if (!showEmailSuggestions || emailSuggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedEmailSuggestionIndex((prev) => 
          prev < emailSuggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedEmailSuggestionIndex((prev) => 
          prev > 0 ? prev - 1 : emailSuggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedEmailSuggestionIndex >= 0) {
          handleEmailContactSelect(emailSuggestions[selectedEmailSuggestionIndex]);
        }
        break;
      case 'Escape':
        setShowEmailSuggestions(false);
        setSelectedEmailSuggestionIndex(-1);
        break;
    }
  };

  // Keyboard navigation for company suggestions
  const handleCompanyKeyDown = (e: React.KeyboardEvent) => {
    if (!showCompanySuggestions || companySuggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex((prev) => 
          prev < companySuggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex((prev) => 
          prev > 0 ? prev - 1 : companySuggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0) {
          handleCompanySelect(companySuggestions[selectedSuggestionIndex]);
        }
        break;
      case 'Escape':
        setShowCompanySuggestions(false);
        setSelectedSuggestionIndex(-1);
        break;
    }
  };

  // Handle company selection
  const handleCompanySelect = (company: CompanySuggestion) => {
    setSelectedCompany(company);
    setShowCompanySuggestions(false);
    setSelectedSuggestionIndex(-1);
    
    // Update the contact's company information
    setEditedContact(prev => ({
      ...prev,
      company_id: company.company_id,
      company_name: company.company_name
    }));
    
    toast.success('Company reassigned successfully', {
      description: `Contact is now assigned to ${company.company_name}`,
      duration: 3000,
    });
  };

  const handleInputChange = (field: keyof Contact, value: string | string[] | boolean) => {
    const oldValue = editedContact[field] as string;
    const originalValue = contact[field];
    
    // Check if the value has actually changed
    const hasChanged = JSON.stringify(value) !== JSON.stringify(originalValue);
    
    setEditedContact(prev => ({
      ...prev,
      [field]: value
    }));

    // Track changed fields
    setChangedFields(prev => {
      const newSet = new Set(prev);
      if (hasChanged) {
        newSet.add(field as string);
      } else {
        newSet.delete(field as string);
      }
      return newSet;
    });

    // Email tracing for email fields
    if (field === 'email' || field === 'additional_email') {
      traceEmailChanges(field, oldValue, value as string);
    }

    // Clear any validation errors for this field
    if (validationErrors[field as string]) {
      setValidationErrors(prev => ({
        ...prev,
        [field as string]: ''
      }));
    }

    // Perform real-time validation only if field has changed
    if (hasChanged && typeof value === 'string' && value !== '') {
      const error = validateField(field as string, value);
      if (error) {
        setValidationErrors(prev => ({
          ...prev,
          [field as string]: error
        }));
      }
    }

    // Trigger searches for specific fields
    if (field === 'email' || field === 'additional_email') {
      debouncedSearchEmails(value as string, field);
    } else if (field === 'first_name' || field === 'last_name') {
      const firstName = field === 'first_name' ? value as string : editedContact.first_name;
      const lastName = field === 'last_name' ? value as string : editedContact.last_name;
      
      if (firstName && lastName) {
        debouncedSearchByName(firstName, lastName);
      }
    } else if (field === 'company_name') {
      // Trigger company search when company_name changes
      const companyName = value as string;
      console.log('Company name changed:', companyName);
      
      // Clear selected company if user is typing something different
      if (selectedCompany && companyName !== selectedCompany.company_name) {
        console.log('Clearing selected company - user typing different name');
        setSelectedCompany(null);
      }
      
      setSelectedSuggestionIndex(-1);
      if (companyName.trim() && companyName.trim().length >= 2) {
        console.log('Triggering company search for:', companyName.trim());
        debouncedSearchCompanies(companyName);
      } else if (companyName.trim().length < 2) {
        console.log('Company search term too short, clearing suggestions');
        setShowCompanySuggestions(false);
        setCompanySuggestions([]);
      }
    }
  };

  const handleArrayChange = (field: keyof Contact, value: string) => {
    const currentArray = (editedContact[field] as string[]) || [];
    const newArray = value.split(',').map(item => item.trim()).filter(item => item.length > 0);
    handleInputChange(field, newArray);
  };

  const addArrayItem = (field: keyof Contact, value: string) => {
    if (!value.trim()) return;
    const currentArray = (editedContact[field] as string[]) || [];
    const newArray = [...currentArray, value.trim()];
    handleInputChange(field, newArray);
  };

  const removeArrayItem = (field: keyof Contact, index: number) => {
    const currentArray = (editedContact[field] as string[]) || [];
    const newArray = currentArray.filter((_, i) => i !== index);
    handleInputChange(field, newArray);
  };

  const renderArrayField = (field: keyof Contact, label: string, placeholder: string) => {
    const array = (editedContact[field] as string[]) || [];
    const inputValue = arrayInputValues[field] || '';

    // Get suggestions based on field type  
    const getSuggestions = () => {
      switch (field) {
        case 'capital_position':
          return formOptions.capitalPositions;
        case 'honorable_achievements':
        case 'hobbies':
          return [];
        default:
          return [];
      }
    };

    const suggestions = getSuggestions();

    return (
      <div className="space-y-2">
        <Label htmlFor={field as string}>{label}</Label>
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <Input
              value={inputValue}
              onChange={(e) => setArrayInputValues(prev => ({ ...prev, [field]: e.target.value }))}
              placeholder={isLoadingFormOptions ? "Loading options..." : placeholder}
              disabled={isLoadingFormOptions}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addArrayItem(field, inputValue);
                  setArrayInputValues(prev => ({ ...prev, [field]: '' }));
                }
              }}
            />
            {suggestions.length > 0 && inputValue && (
              <div className="absolute top-full left-0 right-0 z-10 bg-white border border-gray-200 rounded-md shadow-lg max-h-40 overflow-y-auto">
                {suggestions
                  .filter(suggestion => 
                    suggestion.toLowerCase().includes(inputValue.toLowerCase()) &&
                    !array.includes(suggestion)
                  )
                  .slice(0, 5)
                  .map((suggestion, index) => (
                    <button
                      key={index}
                      type="button"
                      className="w-full text-left px-3 py-2 hover:bg-gray-50 text-sm"
                      onClick={() => {
                        addArrayItem(field, suggestion);
                        setArrayInputValues(prev => ({ ...prev, [field]: '' }));
                      }}
                    >
                      {suggestion}
                    </button>
                  ))
                }
              </div>
            )}
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            disabled={!inputValue.trim() || isLoadingFormOptions}
            onClick={() => {
              addArrayItem(field, inputValue);
              setArrayInputValues(prev => ({ ...prev, [field]: '' }));
            }}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        {array.length > 0 && (
          <div className="space-y-2">
            {array.map((item, index) => (
              <div key={index} className="flex items-center gap-2 p-2 bg-slate-50 rounded-lg">
                <span className="flex-1 text-sm">{item}</span>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeArrayItem(field, index)}
                >
                  <Trash2 className="h-4 w-4 text-red-500" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  const handleSave = async () => {
    // Validate only changed fields before saving
    const errors: Record<string, string> = {};
    changedFields.forEach(fieldKey => {
      const field = fieldKey as keyof Contact;
      const value = editedContact[field];
      if (value !== null && value !== undefined) {
        const error = validateField(fieldKey, value);
        if (error) {
          errors[fieldKey] = error;
        }
      }
    });

    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      toast.error('Please fix validation errors before saving');
      return;
    }

    setIsSaving(true);
    try {
      // Convert form data to database format before saving
      const dbContact = contactFormToDb(editedContact);
      
      // Ensure company_id is included in the save data
      const contactToSave = {
        ...editedContact,
        ...dbContact,
        company_id: selectedCompany?.company_id || editedContact.company_id
      };
      
      await onSave(contactToSave);
      setChangedFields(new Set());
      setValidationErrors({});
      toast.success('Contact updated successfully', {
        description: `${editedContact.first_name} ${editedContact.last_name}'s information has been saved`,
        duration: 4000,
      });
    } catch (error) {
      console.error('Error saving contact:', error);
      toast.error('Failed to update contact', {
        description: 'Please check your connection and try again',
        duration: 5000,
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      if (confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        setChangedFields(new Set());
        setValidationErrors({});
        onCancel();
      }
    } else {
      onCancel();
    }
  };

  // Function to render a field based on its configuration
  const renderField = (fieldConfig: any, groupKey: string) => {
    const fieldName = fieldConfig.name as keyof Contact;
    const fieldValue = editedContact[fieldName];
    const hasError = validationErrors[fieldName as string];

    const commonInputProps = {
      id: fieldName as string,
      className: `${hasError ? 'border-red-300 focus:border-red-500' : ''}`
    };

    switch (fieldConfig.type) {
      case 'text':
      case 'email':
      case 'tel':
        // Special handling for company_name field with search functionality
        if (fieldName === 'company_name') {
  return (
            <div key={fieldName as string} className="space-y-2">
              <Label htmlFor={fieldName as string} className="flex items-center">
                {fieldConfig.label}
                {fieldConfig.required && <span className="text-red-500 ml-1">*</span>}
                {selectedCompany && (
                  <Badge variant="outline" className="ml-2 bg-green-50 text-green-700 border-green-200">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Assigned
              </Badge>
            )}
              </Label>
              <div className="relative">
                <Input
                  ref={companyInputRef}
                  {...commonInputProps}
                  type={fieldConfig.type}
                  value={fieldValue as string || ''}
                  onChange={(e) => handleInputChange(fieldName, e.target.value)}
                  onKeyDown={handleCompanyKeyDown}
                  placeholder={selectedCompany ? "Company assigned - type to change" : "Start typing company name..."}
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  {isSearching ? (
                    <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                  ) : selectedCompany ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <Search className="h-4 w-4 text-gray-400" />
                  )}
              </div>
              </div>

              {/* Company Search Results - Right after input */}
              {showCompanySuggestions && (
              <div className="space-y-2">
                  {companySuggestions.length > 0 ? (
                    <>
                      <Label className="text-sm font-medium text-slate-700">Search Results ({companySuggestions.length} found)</Label>
                      <div className="space-y-2 max-h-80 overflow-y-auto border rounded-xl p-2 bg-slate-50" data-company-search-results>
                        {companySuggestions.map((company, index) => (
                          <div
                            key={index}
                            className={`p-3 rounded-lg cursor-pointer border transition-all duration-200 ${
                              index === selectedSuggestionIndex 
                                ? 'bg-emerald-50 border-emerald-300 shadow-sm' 
                                : 'bg-white border-slate-200 hover:bg-slate-50 hover:border-slate-300'
                            }`}
                            onClick={() => handleCompanySelect(company)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="font-medium text-gray-900 text-sm flex items-center">
                                  {company.company_name}
                                  <Link 
                                    href={`/dashboard/companies/${company.company_id}`}
                                    className="ml-2 p-1 rounded-full hover:bg-blue-100 transition-colors"
                                    onClick={(e) => e.stopPropagation()}
                                    title="View company details"
                                  >
                                    <ExternalLink className="h-3 w-3 text-blue-600" />
                                  </Link>
              </div>
                                {company.extracted_data && (
                                  <div className="text-xs text-emerald-600 font-medium mt-1 flex items-center">
                                    <CheckCircle className="h-3 w-3 mr-1" />
                                    Enhanced Data
              </div>
                                )}
                                {(company.industry || company.extracted_data?.companytype) && (
                                  <div className="text-xs text-gray-600 mt-1">
                                    {company.extracted_data?.companytype || company.industry}
              </div>
                                )}
                                {(company.company_city && company.company_state) || company.extracted_data?.headquarters ? (
                                  <div className="text-xs text-gray-500 mt-1 flex items-center">
                                    <MapPin className="h-3 w-3 mr-1" />
                                    {company.extracted_data?.headquarters || `${company.company_city}, ${company.company_state}`}
              </div>
                                ) : null}
              </div>
                              <div className="ml-3">
                                {company.extracted_data?.fundsize && (
                                  <div className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                                    {company.extracted_data.fundsize}
            </div>
                                )}
          </div>
              </div>
              </div>
                        ))}
              </div>
                    </>
                  ) : isSearching ? (
                    <div className="p-4 text-center border rounded-xl bg-slate-50">
                      <div className="text-sm text-gray-600">
                        <Loader2 className="h-4 w-4 mx-auto mb-2 animate-spin text-gray-400" />
                        Searching for companies...
              </div>
              </div>
                  ) : (
                    <div className="p-4 text-center border rounded-xl bg-slate-50">
                      <div className="text-sm text-gray-600">
                        <Search className="h-4 w-4 mx-auto mb-2 text-gray-400" />
                        No companies found matching "{editedContact.company_name}"
            </div>
                      <div className="text-xs text-gray-500 mt-1">
                        Try a different search term or add the company manually
          </div>
              </div>
                  )}
              </div>
              )}

              {selectedCompany && (
                <div className="flex items-center justify-between mt-2">
                  <span className="text-xs text-green-600 flex items-center">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Currently assigned to {selectedCompany.company_name}
                  </span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedCompany(null);
                      setEditedContact(prev => ({
                        ...prev,
                        company_id: undefined,
                        company_name: ''
                      }));
                    }}
                    className="text-xs text-slate-500 hover:text-slate-700"
                  >
                    <X className="h-3 w-3 mr-1" />
                    Remove Assignment
                  </Button>
              </div>
              )}
              {hasError && <p className="text-red-600 text-xs">{hasError}</p>}
              </div>
          );
        }

        return (
          <div key={fieldName as string} className="space-y-2">
            <Label htmlFor={fieldName as string} className="flex items-center">
              {fieldConfig.label}
              {fieldConfig.required && <span className="text-red-500 ml-1">*</span>}
                </Label>
            <div className="relative">
                <Input
                {...commonInputProps}
                type={fieldConfig.type}
                value={fieldValue as string || ''}
                onChange={(e) => handleInputChange(fieldName, e.target.value)}
                onKeyDown={fieldName === 'email' || fieldName === 'additional_email' ? handleEmailKeyDown : undefined}
                placeholder={fieldConfig.placeholder}
              />
              {(fieldName === 'email' || fieldName === 'additional_email') && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  {isSearchingEmail ? (
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600" />
                  ) : fieldValue && !isSearchingEmail ? (
                    <div className="h-4 w-4 rounded-full bg-blue-100" />
                  ) : null}
              </div>
              )}
              </div>
            {hasError && <p className="text-red-600 text-xs">{hasError}</p>}
            
            {/* Email Search Results - Show under email fields when searching by email */}
            {(fieldName === 'email' || fieldName === 'additional_email') && showEmailSuggestions && activeEmailField === fieldName && (
              <div className="space-y-2" ref={emailSuggestionsRef}>
                <Label className="text-sm font-medium text-slate-700">
                  Existing Contacts Found ({emailSuggestions.length})
                  <span className="text-xs text-slate-500 ml-2">
                    {fieldName === 'email' ? '(for primary email)' : '(for additional email)'}
                  </span>
                </Label>
                <div className="space-y-2 max-h-60 overflow-y-auto border rounded-xl p-2 bg-orange-50" data-email-search-results>
                  {emailSuggestions.map((contact, index) => (
                    <div
                      key={contact.contact_id}
                      className={`p-3 rounded-lg cursor-pointer border transition-all duration-200 ${
                        index === selectedEmailSuggestionIndex 
                          ? 'bg-orange-100 border-orange-300 shadow-sm' 
                          : 'bg-white border-slate-200 hover:bg-slate-50 hover:border-slate-300'
                      }`}
                      onClick={() => handleEmailContactSelect(contact)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="font-medium text-gray-900 text-sm flex items-center">
                            {contact.first_name} {contact.last_name}
                            <Link 
                              href={`/dashboard/people/${contact.contact_id}`}
                              className="ml-2 p-1 rounded-full hover:bg-blue-100 transition-colors"
                              onClick={(e) => e.stopPropagation()}
                              title="View contact details"
                            >
                              <ExternalLink className="h-3 w-3 text-blue-600" />
                            </Link>
                          </div>
                          <div className="text-xs text-slate-600 mt-1">
                            {contact.email} {contact.personal_email && contact.personal_email !== contact.email && `• ${contact.personal_email}`}
                          </div>
                          {contact.title && (
                            <div className="text-xs text-slate-500">
                              {contact.title} {contact.company_name && `at ${contact.company_name}`}
                            </div>
                          )}
                          <div className="flex items-center text-xs text-orange-600 mt-2">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            <span>Contact already exists</span>
                          </div>
                        </div>
                        <div className="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded">
                          ID: {contact.contact_id}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
              </div>
        );
      
      case 'textarea':
        return (
          <div key={fieldName as string} className="space-y-2">
            <Label htmlFor={fieldName as string}>{fieldConfig.label}</Label>
            <Textarea
              {...commonInputProps}
              value={fieldValue as string || ''}
              onChange={(e) => handleInputChange(fieldName, e.target.value)}
              placeholder={fieldConfig.placeholder}
              rows={3}
            />
            {hasError && <p className="text-red-600 text-xs">{hasError}</p>}
              </div>
        );
      
      case 'select':
        // Use FilterFieldWithAdd for specific fields that need dynamic options
        if (fieldName === 'job_tier' || fieldName === 'contact_type' || fieldName === 'role_in_decision_making') {
          const options = fieldName === 'job_tier' ? formOptions.jobTiers :
                         fieldName === 'contact_type' ? formOptions.contactTypes :
                         fieldName === 'role_in_decision_making' ? formOptions.decisionMakingRoles : [];
          
          return (
            <div key={fieldName as string} className="space-y-2">
              <FilterFieldWithAdd
                label={fieldConfig.label}
                value={fieldValue as string || ''}
                options={options}
                onChange={(value) => handleInputChange(fieldName, value)}
                placeholder={fieldConfig.placeholder || `Select ${fieldConfig.label.toLowerCase()}`}
              />
              {hasError && <p className="text-red-600 text-xs">{hasError}</p>}
              </div>
          );
        }
        
        return (
          <div key={fieldName as string} className="space-y-2">
            <Label htmlFor={fieldName as string}>{fieldConfig.label}</Label>
                <Select 
              value={fieldValue as string || ''}
              onValueChange={(value) => handleInputChange(fieldName, value)}
                >
              <SelectTrigger {...commonInputProps}>
                <SelectValue placeholder={fieldConfig.placeholder || `Select ${fieldConfig.label.toLowerCase()}`} />
                  </SelectTrigger>
                  <SelectContent>
                {fieldConfig.options?.map((option: string) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
            {hasError && <p className="text-red-600 text-xs">{hasError}</p>}
              </div>
        );
      
      case 'checkbox':
        return (
          <div key={fieldName as string} className="space-y-2 flex items-center">
            <input
              type="checkbox"
              id={fieldName as string}
              checked={fieldValue as boolean || false}
              onChange={(e) => handleInputChange(fieldName, e.target.checked)}
              className="mr-2"
            />
            <Label htmlFor={fieldName as string}>{fieldConfig.label}</Label>
            {hasError && <p className="text-red-600 text-xs">{hasError}</p>}
            </div>
        );
      
      case 'date':
        return (
          <div key={fieldName as string} className="space-y-2">
            <Label htmlFor={fieldName as string}>{fieldConfig.label}</Label>
            <Input
              {...commonInputProps}
              type="date"
              value={fieldValue as string || ''}
              onChange={(e) => handleInputChange(fieldName, e.target.value)}
            />
            {hasError && <p className="text-red-600 text-xs">{hasError}</p>}
          </div>
        );

      case 'array':
        return renderArrayField(fieldName, fieldConfig.label, fieldConfig.placeholder || '');
      
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Edit Contact Information
            {hasChanges && (
              <Badge variant="outline" className="ml-2 bg-yellow-50 text-yellow-700 border-yellow-200">
                Unsaved Changes
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Render form sections using shared field groups */}
          {Object.entries(contactFieldGroups).map(([groupKey, groupConfig]) => (
            <div key={groupKey}>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                {/* Add icons based on group type */}
                {groupKey === 'personalInfo' && <User className="h-4 w-4" />}
                {groupKey === 'contactInfo' && <Mail className="h-4 w-4" />}
                {groupKey === 'socialMedia' && <Globe className="h-4 w-4" />}
                {groupKey === 'education' && <User className="h-4 w-4" />}
                {groupKey === 'personalDetails' && <User className="h-4 w-4" />}
                {groupConfig.title}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {groupConfig.fields.map(fieldConfig => 
                  renderField(fieldConfig, groupKey)
                )}
                
                {/* Name Search Results - Show in right column of Personal Information */}
                {groupKey === 'personalInfo' && showEmailSuggestions && activeEmailField === 'name_search' && (
                  <div className="space-y-2" ref={emailSuggestionsRef}>
                    <Label className="text-sm font-medium text-slate-700">
                      Existing Contacts Found ({emailSuggestions.length})
                      <span className="text-xs text-slate-500 ml-2">(by name search)</span>
                    </Label>
                    <div className="space-y-2 max-h-60 overflow-y-auto border rounded-xl p-2 bg-orange-50" data-email-search-results>
                      {emailSuggestions.map((contact, index) => (
                        <div
                          key={contact.contact_id}
                          className={`p-3 rounded-lg cursor-pointer border transition-all duration-200 ${
                            index === selectedEmailSuggestionIndex 
                              ? 'bg-orange-100 border-orange-300 shadow-sm' 
                              : 'bg-white border-slate-200 hover:bg-slate-50 hover:border-slate-300'
                          }`}
                          onClick={() => handleEmailContactSelect(contact)}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="font-medium text-gray-900 text-sm flex items-center">
                                {contact.first_name} {contact.last_name}
                                <Link 
                                  href={`/dashboard/people/${contact.contact_id}`}
                                  className="ml-2 p-1 rounded-full hover:bg-blue-100 transition-colors"
                                  onClick={(e) => e.stopPropagation()}
                                  title="View contact details"
                                >
                                  <ExternalLink className="h-3 w-3 text-blue-600" />
                                </Link>
                              </div>
                              <div className="text-xs text-slate-600 mt-1">
                                {contact.email} {contact.personal_email && contact.personal_email !== contact.email && `• ${contact.personal_email}`}
                              </div>
                              {contact.title && (
                                <div className="text-xs text-slate-500">
                                  {contact.title} {contact.company_name && `at ${contact.company_name}`}
                                </div>
                              )}
                              <div className="flex items-center text-xs text-orange-600 mt-2">
                                <AlertTriangle className="h-3 w-3 mr-1" />
                                <span>Contact already exists</span>
                              </div>
                            </div>
                            <div className="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded">
                              ID: {contact.contact_id}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              {groupKey !== 'personalDetails' && <Separator />}
              </div>
          ))}



          {/* Additional Information */}
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Additional Information
            </h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={editedContact.notes || ''}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Enter additional notes"
                  rows={3}
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="flex items-center"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={!hasChanges || isSaving}
              className="flex items-center"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 