'use client'

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Building, 
  Globe, 
  GraduationCap, 
  Award, 
  Heart, 
  Calendar, 
  UserCheck, 
  Shield, 
  Users, 
  FileText,
  Linkedin,
  Twitter,
  Facebook,
  Instagram,
  Youtube,
  ChevronDown,
  ChevronUp,
  Sparkles,
  Briefcase
} from 'lucide-react';
import { Contact } from '../shared/types';
import { contactFieldGroups, hasFieldValue, getFieldDisplayValue } from '../shared/contactFields';

interface ContactOverviewV2TabProps {
  contact: Contact;
}

export default function ContactOverviewV2Tab({ contact }: ContactOverviewV2TabProps) {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    'personal-information': true, // Personal Information always expanded
    'contact-information': true, // Contact Information always expanded
  });

  if (!contact) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-gray-500">No contact data available</div>
      </div>
    );
  }

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
    [sectionId]: !prev[sectionId]
    }));
  };

  const renderArrayField = (values: string[] | null | undefined, colorClass: string = 'blue', maxDisplay: number = 5) => {
    if (!values || values.length === 0) return null;
    
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-700 border-blue-200',
      green: 'bg-green-50 text-green-700 border-green-200',
      purple: 'bg-purple-50 text-purple-700 border-purple-200',
      orange: 'bg-orange-50 text-orange-700 border-orange-200',
      red: 'bg-red-50 text-red-700 border-red-200',
      yellow: 'bg-yellow-50 text-yellow-700 border-yellow-200',
      gray: 'bg-gray-50 text-gray-700 border-gray-200',
      indigo: 'bg-indigo-50 text-indigo-700 border-indigo-200',
      emerald: 'bg-emerald-50 text-emerald-700 border-emerald-200',
      pink: 'bg-pink-50 text-pink-700 border-pink-200',
      cyan: 'bg-cyan-50 text-cyan-700 border-cyan-200'
    };

    return (
      <div className="flex flex-wrap gap-1 mt-1">
        {values.slice(0, maxDisplay).map((value: string, index: number) => (
          <Badge key={index} className={`${colorClasses[colorClass as keyof typeof colorClasses]} text-xs border`}>
            {value}
          </Badge>
        ))}
        {values.length > maxDisplay && (
          <Badge className="bg-gray-50 text-gray-600 border-gray-200 text-xs">
            +{values.length - maxDisplay} more
          </Badge>
        )}
      </div>
    );
  };

  const renderInfoField = (label: string, value: any, icon: React.ReactNode, colorClass: string = 'blue', isLink: boolean = false) => {
    if (!value) return null;

    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600',
      green: 'bg-green-50 text-green-600',
      purple: 'bg-purple-50 text-purple-600',
      orange: 'bg-orange-50 text-orange-600',
      red: 'bg-red-50 text-red-600',
      yellow: 'bg-yellow-50 text-yellow-600',
      gray: 'bg-gray-50 text-gray-600',
      indigo: 'bg-indigo-50 text-indigo-600',
      emerald: 'bg-emerald-50 text-emerald-600',
      pink: 'bg-pink-50 text-pink-600',
      cyan: 'bg-cyan-50 text-cyan-600'
    };

    return (
      <div className="flex items-start gap-3 p-3 rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
        <div className={`p-2 rounded-md ${colorClasses[colorClass as keyof typeof colorClasses]}`}>
          {icon}
        </div>
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-gray-500 mb-1">{label}</div>
          <div className="font-medium text-gray-900">
            {Array.isArray(value) ? renderArrayField(value, colorClass) : (
              isLink ? (
                <a href={value} target="_blank" rel="noopener noreferrer" 
                   className="text-blue-600 hover:text-blue-800 flex items-center gap-1">
                  {value}
                </a>
              ) : value
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderExpandableSection = (title: string, icon: React.ReactNode, children: React.ReactNode, sectionId: string, defaultExpanded: boolean = false) => {
    const isExpanded = expandedSections[sectionId] ?? defaultExpanded;
    
    return (
      <Card className="border border-gray-200 shadow-sm">
        <CardHeader 
          className="cursor-pointer hover:bg-gray-50 transition-colors"
          onClick={() => toggleSection(sectionId)}
        >
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {icon}
              {title}
            </div>
            {isExpanded ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </CardTitle>
        </CardHeader>
        {isExpanded && (
          <CardContent>
            {children}
          </CardContent>
        )}
      </Card>
    );
  };

  // Function to get icon for a field group
  const getGroupIcon = (groupKey: string) => {
    switch (groupKey) {
      case 'personalInfo': return <User className="h-5 w-5" />;
      case 'contactInfo': return <Mail className="h-5 w-5" />;
      case 'socialMedia': return <Globe className="h-5 w-5" />;
      case 'education': return <GraduationCap className="h-5 w-5" />;
      case 'personalDetails': return <Heart className="h-5 w-5" />;
      default: return <User className="h-5 w-5" />;
    }
  };

  // Function to get color scheme for a field group
  const getGroupColorScheme = (groupKey: string) => {
    switch (groupKey) {
      case 'personalInfo': return { border: 'border-blue-200', bg: 'from-blue-50 to-white', text: 'text-blue-900', color: 'blue' };
      case 'contactInfo': return { border: 'border-green-200', bg: 'from-green-50 to-white', text: 'text-green-900', color: 'green' };
      case 'socialMedia': return { border: 'border-cyan-200', bg: 'from-cyan-50 to-white', text: 'text-cyan-900', color: 'cyan' };
      case 'education': return { border: 'border-purple-200', bg: 'from-purple-50 to-white', text: 'text-purple-900', color: 'purple' };
      case 'personalDetails': return { border: 'border-pink-200', bg: 'from-pink-50 to-white', text: 'text-pink-900', color: 'pink' };
      default: return { border: 'border-blue-200', bg: 'from-blue-50 to-white', text: 'text-blue-900', color: 'blue' };
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}

      {/* Render sections dynamically based on field groups */}
      {Object.entries(contactFieldGroups).map(([groupKey, groupConfig]) => {
        // Check if this group has any data
        const hasGroupData = groupConfig.fields.some(field => hasFieldValue(contact, field.name));
        if (!hasGroupData) return null;

        const colorScheme = getGroupColorScheme(groupKey);
        const alwaysExpanded = groupKey === 'personalInfo' || groupKey === 'contactInfo';

        return alwaysExpanded ? (
          // Always visible sections
          <Card key={groupKey} className={`border-2 ${colorScheme.border} bg-gradient-to-br ${colorScheme.bg}`}>
            <CardHeader>
              <CardTitle className={`flex items-center gap-2 ${colorScheme.text}`}>
                {getGroupIcon(groupKey)}
                {groupConfig.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {groupConfig.fields.map(field => {
                  if (!hasFieldValue(contact, field.name)) return null;
                  
                  const fieldValue = getFieldDisplayValue(contact, field.name);
                  const isLink = field.type === 'text' && (field.name === 'linkedin_url' || field.name === 'twitter' || field.name === 'facebook' || field.name === 'instagram' || field.name === 'youtube');
                  
                  return renderInfoField(
                    field.label,
                    fieldValue,
                    getGroupIcon(groupKey),
                    colorScheme.color,
                    isLink
                  );
                })}
              </div>
            </CardContent>
          </Card>
        ) : (
          // Collapsible sections
          renderExpandableSection(
            groupConfig.title,
            getGroupIcon(groupKey),
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {groupConfig.fields.map(field => {
                if (!hasFieldValue(contact, field.name)) return null;
                
                const fieldValue = getFieldDisplayValue(contact, field.name);
                const isLink = field.type === 'text' && (field.name === 'linkedin_url' || field.name === 'twitter' || field.name === 'facebook' || field.name === 'instagram' || field.name === 'youtube');
                
                return renderInfoField(
                  field.label,
                  fieldValue,
                  getGroupIcon(groupKey),
                  colorScheme.color,
                  isLink
                );
              })}
            </div>,
            groupKey
          )
        );
      })}


    </div>
  );
}
