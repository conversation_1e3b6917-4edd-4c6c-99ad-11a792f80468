import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink, Building2, DollarSign, MapPin, Calendar } from 'lucide-react';

interface ContactLinkedDealsTabProps {
  contactId: number | string;
}

interface Deal {
  deal_id: string;
  deal_name: string;
  deal_type: string;
  deal_size: number;
  deal_stage: string;
  created_at: string;
  updated_at: string;
  deal_description?: string;
  deal_location?: string;
}

const ContactLinkedDealsTab: React.FC<ContactLinkedDealsTabProps> = ({ contactId }) => {
  const router = useRouter();
  const [deals, setDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchLinkedDeals = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/contacts/${contactId}/deals`);
        if (!response.ok) {
          throw new Error('Failed to fetch linked deals');
        }
        const data = await response.json();
        setDeals(data.deals || []);
      } catch (err) {
        console.error('Error fetching linked deals:', err);
        setError('Failed to load linked deals');
      } finally {
        setLoading(false);
      }
    };

    if (contactId) {
      fetchLinkedDeals();
    }
  }, [contactId]);

  const formatCurrency = (amount: number | string | null | undefined): string => {
    if (amount === null || amount === undefined) return "N/A";
    const num = typeof amount === "string" ? parseFloat(amount) : amount;
    if (isNaN(num)) return "N/A";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(num * 1000000); // Convert millions to actual amount
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Linked Deals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="text-gray-500 mt-2">Loading linked deals...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Linked Deals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-500">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Linked Deals
          {deals.length > 0 && (
            <Badge variant="secondary" className="ml-2">
              {deals.length} deal{deals.length !== 1 ? 's' : ''}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {deals.length === 0 ? (
          <div className="text-center py-8">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No linked deals found</p>
            <p className="text-sm text-gray-400 mt-2">
              This contact is not associated with any deals yet.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {deals.map((deal) => (
              <div
                key={deal.deal_id}
                className="border rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer"
                onClick={() => router.push(`/dashboard/deals/${deal.deal_id}`)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold text-gray-900">
                        {deal.deal_name}
                      </h3>
                      <Badge variant="outline" className="text-xs">
                        {deal.deal_type}
                      </Badge>
                      {deal.deal_stage && (
                        <Badge variant="secondary" className="text-xs">
                          {deal.deal_stage}
                        </Badge>
                      )}
                    </div>
                    
                    {deal.deal_description && (
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {deal.deal_description}
                      </p>
                    )}
                    
                    <div className="flex items-center gap-6 text-sm text-gray-500">
                      {deal.deal_size && (
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          <span>{formatCurrency(deal.deal_size)}</span>
                        </div>
                      )}
                      
                      {deal.deal_location && (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          <span>{deal.deal_location}</span>
                        </div>
                      )}
                      
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span>Created {formatDate(deal.created_at)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      router.push(`/dashboard/deals/${deal.deal_id}`);
                    }}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ContactLinkedDealsTab; 