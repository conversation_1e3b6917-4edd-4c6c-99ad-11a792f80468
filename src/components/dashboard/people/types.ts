export interface Message {
  message_id: string;
  thread_id: string;
  from_email?: string;
  to_email?: string;
  subject: string;
  body: string;
  direction: string;
  role?: string;
  sent_at?: string;
  created_at: string;
  smartlead_campaign_id?: string;
  metadata?: {
    lead_id?: string;
    campaign_id?: string;
    campaign_template?: string;
    template_name?: string;
    [key: string]: any;
  };
}

export interface Campaign {
  id: string;
  name: string;
}

export interface CampaignTemplate {
  id: string;
  name: string;
  smartlead_id: string;
  subject: string;
  body: string;
}

export interface CampaignSequenceVariant {
  id: number;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
  subject: string;
  email_body: string;
  email_campaign_seq_id: number;
  variant_label: string;
}

// Add a type for template variables
export interface TemplateVariable {
  name: string;
  default_value?: string;
  description?: string;
}

export interface CampaignSequence {
  id: number;
  created_at: string;
  updated_at: string;
  email_campaign_id: number;
  seq_number: number;
  subject: string;
  email_body: string;
  sequence_variants: CampaignSequenceVariant[];
  variables?: TemplateVariable[]; // Add variables array to the type
}

export interface Contact {
  person_id?: number;
  contact_id: number;
  company_id?: number;
  first_name: string;
  last_name: string;
  email?: string;
  phone_number?: string;
  job_title?: string;
  company_name?: string;
  industry?: string;
  capital_type?: string;
  company_address?: string;
  company_city?: string;
  company_state?: string;
  company_website?: string;
  company_country?: string;
  source?: string;
  investment_criteria?: {
    asset_types?: string[];
    deal_size?: {
      min?: number;
      max?: number;
    };
    markets?: string[];
  };
  person_summary?: string;
  key_interests?: string[];
  recent_activities: {
    interaction_type: string;
    interaction_date: string;
    notes: string;
  }[];
  executive_summary?: string;
  career_timeline?: any[];
  notable_activities?: any[];
  personal_tidbits?: any[];
  conversation_hooks?: any[];
  outreach_draft?: any;
  sources?: any[];
  company_data?: CompanyData;
  // Search data fields
  searched?: boolean;
  searched_profile?: string;
  searched_input_data?: any;
  searched_tokens_used?: number;
  searched_date?: string;
  // Email validation fields
  email_generated?: boolean;
  email_validated_date?: string;
  email_status?: string;
  smartlead_lead_id?: string;
  smartlead_status?: string;
  last_email_sent_at?: string;
  extracted?: boolean;
}

export interface CompanyData {
  company_id?: number;
  name?: string;
  company_name?: string;
  company_linkedin?: string;
  summary?: string;
  founded_year?: number;
  contact_count?: string | number;
  risk_factors?: string[];
  recent_developments?: string[];
  engagement_opportunities?: string[];
  overview?: {
    structure_history?: string;
    executive_contacts?: any[];
    investment_program?: any;
    lending_program?: any;
    capital_commitments?: any;
    investment_strategy?: string[];
    recent_transactions?: any[];
    hold_horizon?: string;
  };
  scraped_data?: any;
  scraped_contacts?: any[];
}

export interface ScrapedData {
  companyProfile?: {
    companyName?: string;
    companyType?: string;
    companyWebsite?: string;
    businessModel?: string;
    fundSize?: string;
    aum?: string;
    numberOfProperties?: number;
    headquarters?: string;
    numberOfOffices?: number;
    officeLocations?: string[];
    foundedYear?: number;
    numberOfEmployees?: string;
    investmentFocus?: string[];
    geographicFocus?: string[];
  };
  executiveTeam?: Array<{
    first_name: string;
    last_name: string;
    full_name: string;
    title: string;
    headline?: string;
    seniority?: string;
    email?: string;
    personal_email?: string;
    email_status?: string;
    linkedin_url?: string;
    contact_city?: string;
    contact_state?: string;
    contact_country?: string;
    phone?: string;
    bio?: string;
    category?: string;
  }>;
  recentDeals?: Array<{
    property?: string;
    location?: string;
    dealType?: string;
    amount?: string;
    date?: string;
    propertyType?: string;
    squareFeet?: string;
    units?: number;
  }>;
  investmentStrategy?: {
    mission?: string;
    approach?: string;
    targetReturn?: string;
    propertyTypes?: string[];
    strategies?: string[];
    assetClasses?: string[];
    valueCreation?: string[];
  };
  investmentCriteria?: {
    targetMarkets?: string[];
    dealSize?: string;
    minimumDealSize?: string;
    maximumDealSize?: string;
    holdPeriod?: string;
    riskProfile?: string;
    propertyTypes?: string[];
    propertySubcategories?: string[];
    assetTypes?: string[];
    loanTypes?: string[];
  };
  capitalSources?: string[];
  financialProducts?: Array<{
    productType?: string;
    terms?: string[];
    typicalAmount?: string;
    description?: string;
  }>;
  trackRecord?: {
    totalTransactions?: string;
    totalSquareFeet?: string;
    totalUnits?: string;
    historicalReturns?: string;
    portfolioValue?: string;
  };
  partnerships?: Array<{
    partnerName?: string;
    relationshipType?: string;
    description?: string;
  }>;
  contactInfo?: {
    website?: string;
    mainPhone?: string;
    mainEmail?: string;
    socialMedia?: {
      linkedin?: string;
      twitter?: string;
      facebook?: string;
      instagram?: string;
    };
  };
}

export interface TrainingDataTabProps {
  contactId: string | number;
  contact?: Contact;
  normalizedScrapedData?: ScrapedData;
} 
export interface ContactCampaignTagProps {
  contactId: string | number;
  contact?: Contact;
  normalizedScrapedData?: ScrapedData;
}