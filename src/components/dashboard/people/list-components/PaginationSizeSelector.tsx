'use client'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface PaginationSizeSelectorProps {
  currentSize: number
  onSizeChange: (size: number) => void
  options?: number[]
}

export default function PaginationSizeSelector({
  currentSize,
  onSizeChange,
  options = [10, 25, 50, 100, 500]
}: PaginationSizeSelectorProps) {
  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-gray-600">Show:</span>
      <Select value={currentSize.toString()} onValueChange={(value) => onSizeChange(parseInt(value))}>
        <SelectTrigger className="w-20 h-8">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {options.map((size) => (
            <SelectItem key={size} value={size.toString()}>
              {size}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <span className="text-sm text-gray-600">per page</span>
    </div>
  )
} 