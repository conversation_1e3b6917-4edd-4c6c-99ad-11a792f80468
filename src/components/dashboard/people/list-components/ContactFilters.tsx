'use client'

import React from 'react';
import { Button } from '@/components/ui/button';
import { Source } from '../shared/types';
import { X } from 'lucide-react';

interface ContactFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  sources: Source[];
  selectedSource: string;
  onSourceChange: (value: string) => void;
  contactType: string;
  onContactTypeChange: (value: string) => void;
  processingStatus: string;
  onProcessingStatusChange: (value: string) => void;
  sortField: string;
  onSortFieldChange: (value: string) => void;
  sortDirection: 'asc' | 'desc';
  onSortDirectionChange: (value: 'asc' | 'desc') => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
}

const ContactFilters: React.FC<ContactFiltersProps> = ({
  sources,
  selectedSource,
  onSourceChange,
  contactType,
  onContactTypeChange,
  processingStatus,
  onProcessingStatusChange,
  sortField,
  onSortFieldChange,
  sortDirection,
  onSortDirectionChange,
  onClearFilters,
  hasActiveFilters,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Contact Type</label>
        <select
          value={contactType}
          onChange={(e) => onContactTypeChange(e.target.value)}
          className="w-full rounded-md border border-gray-300 p-2 text-sm"
        >
          <option value="all">All Types</option>
          <option value="Sponsor">Sponsor</option>
          <option value="Capital Source">Capital Source</option>
          <option value="Third-Party">Third-Party</option>
          <option value="Unknown">Unknown</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Source</label>
        <select
          value={selectedSource}
          onChange={(e) => onSourceChange(e.target.value)}
          className="w-full rounded-md border border-gray-300 p-2 text-sm"
        >
          <option value="all">All Sources</option>
          {sources.map((source) => (
            <option key={source.source} value={source.source}>
              {source.source} ({source.count})
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Processing Status</label>
        <select
          value={processingStatus}
          onChange={(e) => onProcessingStatusChange(e.target.value)}
          className="w-full rounded-md border border-gray-300 p-2 text-sm"
        >
          <option value="all">All Statuses</option>
          <option value="email_verification_status">Email Verified</option>
          <option value="contact_enrichment_status">Enrichment Completed</option>
          <option value="email_generation_status">Email Generated</option>
          <option value="smartlead_synced">Smartlead Synced</option>
          <option value="has_error">Has Processing Error</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
        <select
          value={sortField}
          onChange={(e) => onSortFieldChange(e.target.value)}
          className="w-full rounded-md border border-gray-300 p-2 text-sm"
        >
          <option value="updated_at">Last Updated</option>
          <option value="created_at">Created Date</option>
          <option value="last_name">Last Name</option>
          <option value="first_name">First Name</option>
          <option value="title">Job Title</option>
          <option value="company_name">Company</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Sort Direction</label>
        <select
          value={sortDirection}
          onChange={(e) => onSortDirectionChange(e.target.value as 'asc' | 'desc')}
          className="w-full rounded-md border border-gray-300 p-2 text-sm"
        >
          <option value="desc">Descending</option>
          <option value="asc">Ascending</option>
        </select>
      </div>

      <div className="flex items-end">
        <Button 
          onClick={onClearFilters} 
          variant="outline"
          className="flex items-center gap-1"
          disabled={!hasActiveFilters}
        >
          <X className="h-4 w-4" />
          Clear All Filters
        </Button>
      </div>
    </div>
  );
};

export default ContactFilters; 