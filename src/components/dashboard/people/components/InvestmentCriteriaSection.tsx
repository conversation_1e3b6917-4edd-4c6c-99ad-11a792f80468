import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Target, Plus, Trash2, Copy } from 'lucide-react';
import { InvestmentCriteria } from '../shared/types';
import { capitalTypes, recourseOptions } from '../constants/AddContactConstants';

interface InvestmentCriteriaSectionProps {
  investmentCriteria: InvestmentCriteria[];
  savedInvestmentCriteria?: InvestmentCriteria[];
  contactsAdded?: number;
  onAdd: () => void;
  onRemove: (id: string) => void;
  onUpdate: (id: string, field: keyof InvestmentCriteria, value: string) => void;
  onCopyPrevious: () => void;
}

export const InvestmentCriteriaSection: React.FC<InvestmentCriteriaSectionProps> = ({
  investmentCriteria,
  savedInvestmentCriteria = [],
  contactsAdded = 0,
  onAdd,
  onRemove,
  onUpdate,
  onCopyPrevious
}: InvestmentCriteriaSectionProps) => {
  // Check if capital type is debt-related
  const isDebtCapitalType = (capitalType: string) => {
    return ['Senior Debt', 'Mezzanine', 'Stretch Senior'].includes(capitalType);
  };

  // Check if capital type is equity-related
  const isEquityCapitalType = (capitalType: string) => {
    return ['Preferred Equity', 'Common Equity', 'Co-GP', 'GP', 'JV', 'LP'].includes(capitalType);
  };

  return (
    <Card className="bg-white shadow-xl border-0 rounded-2xl overflow-hidden sticky top-6">
      <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 border-b border-purple-100">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-xl mr-3">
              <Target className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <div className="text-lg font-semibold text-slate-900">Investment Criteria</div>
              <div className="text-sm text-slate-600">Define preferences for this contact</div>
            </div>
          </div>
          <div className="flex space-x-2">
            {savedInvestmentCriteria.length > 0 && contactsAdded > 0 && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={onCopyPrevious}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Previous
              </Button>
            )}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={onAdd}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-6 space-y-6 bg-gradient-to-b from-white to-slate-50 max-h-96 overflow-y-auto">
        {investmentCriteria.map((criteria, index) => (
          <div key={criteria.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-medium text-gray-900 text-sm">
                Criteria {index + 1}
              </h4>
              {investmentCriteria.length > 1 && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => onRemove(criteria.id)}
                  className="text-red-600 hover:text-red-800"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="space-y-4">
              <div>
                <Label className="text-xs">Capital Type *</Label>
                <Select
                  value={criteria.capital_type}
                  onValueChange={(value) => onUpdate(criteria.id, 'capital_type', value)}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    {capitalTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-xs">Geographic Focus</Label>
                <Input
                  className="h-9"
                  value={criteria.investment_criteria_country}
                  onChange={(e) => onUpdate(criteria.id, 'investment_criteria_country', e.target.value)}
                  placeholder="United States, California"
                />
              </div>

              <div>
                <Label className="text-xs">Property Type</Label>
                <Input
                  className="h-9"
                  value={criteria.investment_criteria_property_type}
                  onChange={(e) => onUpdate(criteria.id, 'investment_criteria_property_type', e.target.value)}
                  placeholder="Multifamily, Office"
                />
              </div>
              
              <div>
                <Label className="text-xs">Deal Size</Label>
                <Input
                  className="h-9"
                  value={criteria.investment_criteria_deal_size}
                  onChange={(e) => onUpdate(criteria.id, 'investment_criteria_deal_size', e.target.value)}
                  placeholder="$10M - $50M"
                />
              </div>

              {/* Conditional fields based on capital type */}
              {isDebtCapitalType(criteria.capital_type) && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg space-y-3">
                  <h5 className="font-medium text-blue-900 text-xs">Debt Specific</h5>
                  <div>
                    <Label className="text-xs">Loan Type</Label>
                    <Input
                      className="h-8"
                      value={criteria.investment_criteria_loan_type}
                      onChange={(e) => onUpdate(criteria.id, 'investment_criteria_loan_type', e.target.value)}
                      placeholder="Construction, Bridge"
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Term (Months)</Label>
                    <Input
                      className="h-8"
                      type="number"
                      value={criteria.loan_term_months}
                      onChange={(e) => onUpdate(criteria.id, 'loan_term_months', e.target.value)}
                      placeholder="24"
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Recourse</Label>
                    <Select
                      value={criteria.recourse}
                      onValueChange={(value) => onUpdate(criteria.id, 'recourse', value)}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue placeholder="Select" />
                      </SelectTrigger>
                      <SelectContent>
                        {recourseOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}

              {isEquityCapitalType(criteria.capital_type) && (
                <div className="p-3 bg-green-50 border border-green-200 rounded-lg space-y-3">
                  <h5 className="font-medium text-green-900 text-xs">Equity Specific</h5>
                  <div>
                    <Label className="text-xs">Hold Period (Years)</Label>
                    <Input
                      className="h-8"
                      type="number"
                      value={criteria.hold_period_years}
                      onChange={(e) => onUpdate(criteria.id, 'hold_period_years', e.target.value)}
                      placeholder="5"
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Expected IRR (%)</Label>
                    <Input
                      className="h-8"
                      type="number"
                      step="0.1"
                      value={criteria.expected_irr_percent}
                      onChange={(e) => onUpdate(criteria.id, 'expected_irr_percent', e.target.value)}
                      placeholder="15.0"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}; 