import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Target, 
  Building2, 
  MapPin, 
  DollarSign, 
  TrendingUp, 
  Calculator,
  Loader2,
  AlertCircle,
  Plus,
  RefreshCw,
  FileText
} from 'lucide-react';
import { Alert, AlertDescription } from "@/components/ui/alert";

interface InvestmentCriteriaData {
  investment_criteria_id: number;
  entity_type: string;
  entity_id: number;
  entity_name?: string;
  entity_website?: string;
  entity_industry?: string;
  entity_location?: string;
  capital_position: string;
  minimum_deal_size?: number;
  maximum_deal_size?: number;
  country?: string[];
  region?: string[];
  state?: string[];
  city?: string[];
  property_types?: string[];
  property_sub_categories?: string[];
  strategies?: string[];
  decision_making_process?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  
  // Relationship IDs to debt/equity records
  investment_criteria_debt_id?: number;
  investment_criteria_equity_id?: number;
  
  // Debt-specific fields (from joined debt table)
  debt_loan_type?: string;
  debt_loan_program?: string;
  debt_min_loan_term?: number;
  debt_max_loan_term?: number;
  debt_interest_rate?: number;
  debt_loan_interest_rate_based_off_sofr?: number;
  debt_loan_interest_rate_based_off_wsj?: number;
  debt_loan_interest_rate_based_off_prime?: number;
  debt_loan_interest_rate_based_off_3yt?: number;
  debt_loan_interest_rate_based_off_5yt?: number;
  debt_loan_interest_rate_based_off_10yt?: number;
  debt_loan_interest_rate_based_off_30yt?: number;
  debt_loan_to_value_min?: number;
  debt_loan_to_value_max?: number;
  debt_loan_to_cost_min?: number;
  debt_loan_to_cost_max?: number;
  debt_loan_origination_min_fee?: number;
  debt_loan_origination_max_fee?: number;
  debt_loan_exit_min_fee?: number;
  debt_loan_exit_max_fee?: number;
  debt_min_loan_dscr?: number;
  debt_max_loan_dscr?: number;
  debt_structured_loan_tranche?: string;
  debt_recourse_loan?: string;
  debt_closing_time?: number;
  debt_program_overview?: string;
  debt_lien_position?: string;
  debt_loan_min_debt_yield?: string;
  debt_prepayment?: string;
  debt_yield_maintenance?: string;
  debt_amortization?: string;
  debt_application_deposit?: number;
  debt_good_faith_deposit?: number;
  debt_future_facilities?: string;
  debt_eligible_borrower?: string;
  debt_occupancy_requirements?: string;
  debt_rate_lock?: string;
  debt_rate_type?: string;
  debt_loan_type_normalized?: string;
  debt_notes?: string;
  
  // Equity-specific fields (from joined equity table)
  equity_target_return?: number;
  equity_minimum_internal_rate_of_return?: number;
  equity_min_hold_period_years?: number;
  equity_max_hold_period_years?: number;
  equity_minimum_yield_on_cost?: number;
  equity_minimum_equity_multiple?: number;
  equity_ownership_requirement?: string;
  equity_program_overview?: string;
  equity_target_cash_on_cash_min?: number;
  equity_attachment_point?: number;
  equity_max_leverage_tolerance?: number;
  equity_typical_closing_timeline_days?: number;
  equity_proof_of_funds_requirement?: boolean;
  equity_occupancy_requirements?: string;
  equity_notes?: string;
}

interface InvestmentCriteriaSectionV2Props {
  entityType: 'Company' | 'Contact' | 'Deal';
  entityId: string | number;
  onAdd?: () => void;
  onEdit?: (criteria: InvestmentCriteriaData) => void;
  onRefresh?: () => void;
  className?: string;
}

export const InvestmentCriteriaSectionV2: React.FC<InvestmentCriteriaSectionV2Props> = ({
  entityType,
  entityId,
  onAdd,
  onEdit,
  onRefresh,
  className = ''
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [criteriaList, setCriteriaList] = useState<InvestmentCriteriaData[]>([]);

  const fetchInvestmentCriteria = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/investment-criteria/entity/${entityType}/${entityId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch investment criteria: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Flatten the grouped criteria into a single array
        const allCriteria: InvestmentCriteriaData[] = [];
        Object.values(result.data.groupedCriteria || {}).forEach((group: any) => {
          allCriteria.push(...group);
        });
        setCriteriaList(allCriteria);
      } else {
        throw new Error(result.error || 'Unknown error occurred');
      }
    } catch (err) {
      console.error('Error fetching investment criteria:', err);
      setError(err instanceof Error ? err.message : 'Failed to load investment criteria');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInvestmentCriteria();
  }, [entityType, entityId]);

  const formatCurrency = (value?: number) => {
    if (!value) return null;
    return `$${value.toLocaleString()}M`;
  };

  const formatPercentage = (value?: number) => {
    if (!value) return null;
    return `${value}%`;
  };

  const formatArray = (arr?: string[]) => {
    if (!arr || arr.length === 0) return null;
    return (
      <div className="flex flex-wrap gap-1">
        {arr.map((item, index) => (
          <Badge key={index} variant="outline" className="text-xs">
            {item}
          </Badge>
        ))}
      </div>
    );
  };

  const getCapitalPositionColor = (position: string) => {
    switch (position.toLowerCase()) {
      case 'debt':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'equity':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'mezzanine':
        return 'bg-purple-50 text-purple-700 border-purple-200';
      case 'preferred equity':
        return 'bg-emerald-50 text-emerald-700 border-emerald-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  const renderCriteriaCard = (criteria: InvestmentCriteriaData, index: number) => (
    <Card key={criteria.investment_criteria_id} className="border border-gray-200">
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Header with deal size */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-green-600" />
              <span className="font-medium text-sm">
                Deal Size: {
                  criteria.minimum_deal_size && criteria.maximum_deal_size
                    ? `${formatCurrency(criteria.minimum_deal_size)} - ${formatCurrency(criteria.maximum_deal_size)}`
                    : criteria.minimum_deal_size
                    ? `${formatCurrency(criteria.minimum_deal_size)}+`
                    : criteria.maximum_deal_size
                    ? `Up to ${formatCurrency(criteria.maximum_deal_size)}`
                    : 'Not specified'
                }
              </span>
            </div>
            {onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit(criteria)}
                className="h-8 px-2"
              >
                Edit
              </Button>
            )}
          </div>

          {/* Geography */}
          {(criteria.country || criteria.region || criteria.state || criteria.city) && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-red-600" />
                <span className="font-medium text-sm">Geographic Focus</span>
              </div>
              <div className="ml-6 space-y-1">
                {criteria.country && (
                  <div>
                    <span className="text-xs text-gray-600">Countries: </span>
                    {formatArray(criteria.country)}
                  </div>
                )}
                {criteria.region && (
                  <div>
                    <span className="text-xs text-gray-600">Regions: </span>
                    {formatArray(criteria.region)}
                  </div>
                )}
                {criteria.state && (
                  <div>
                    <span className="text-xs text-gray-600">States: </span>
                    {formatArray(criteria.state)}
                  </div>
                )}
                {criteria.city && (
                  <div>
                    <span className="text-xs text-gray-600">Cities: </span>
                    {formatArray(criteria.city)}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Property Types & Strategies */}
          {(criteria.property_types || criteria.property_sub_categories || criteria.strategies) && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Building2 className="h-4 w-4 text-orange-600" />
                <span className="font-medium text-sm">Investment Focus</span>
              </div>
              <div className="ml-6 space-y-1">
                {criteria.property_types && (
                  <div>
                    <span className="text-xs text-gray-600">Property Types: </span>
                    {formatArray(criteria.property_types)}
                  </div>
                )}
                {criteria.property_sub_categories && (
                  <div>
                    <span className="text-xs text-gray-600">Subcategories: </span>
                    {formatArray(criteria.property_sub_categories)}
                  </div>
                )}
                {criteria.strategies && (
                  <div>
                    <span className="text-xs text-gray-600">Strategies: </span>
                    {formatArray(criteria.strategies)}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Debt-specific details */}
          {criteria.investment_criteria_debt_id && (
            <div className="space-y-3 p-4 bg-blue-50/70 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2 pb-2 border-b border-blue-200">
                <Calculator className="h-5 w-5 text-blue-600" />
                <span className="font-semibold text-blue-800">Debt Information</span>
              </div>
              
              {/* Loan Terms */}
              {(criteria.debt_min_loan_term || criteria.debt_max_loan_term) && (
                <div className="space-y-2">
                  <span className="text-sm font-medium text-blue-700">Loan Terms</span>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {criteria.debt_min_loan_term && (
                      <div className="bg-white p-2 rounded border border-blue-100">
                        <span className="text-xs text-blue-600">Minimum Term</span>
                        <p className="font-medium text-sm">{criteria.debt_min_loan_term} months</p>
                      </div>
                    )}
                    {criteria.debt_max_loan_term && (
                      <div className="bg-white p-2 rounded border border-blue-100">
                        <span className="text-xs text-blue-600">Maximum Term</span>
                        <p className="font-medium text-sm">{criteria.debt_max_loan_term} months</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Interest Rate */}
              {criteria.debt_interest_rate && (
                <div className="space-y-2">
                  <span className="text-sm font-medium text-blue-700">Interest Rate</span>
                  <div className="bg-white p-2 rounded border border-blue-100">
                    <p className="font-medium text-sm">{formatPercentage(criteria.debt_interest_rate)}</p>
                  </div>
                </div>
              )}

              {/* Loan Ratios */}
              {(criteria.debt_loan_to_value_min || criteria.debt_loan_to_value_max) && (
                <div className="space-y-2">
                  <span className="text-sm font-medium text-blue-700">Loan-to-Value Ratio</span>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {criteria.debt_loan_to_value_min && (
                      <div className="bg-white p-2 rounded border border-blue-100">
                        <span className="text-xs text-blue-600">Minimum LTV</span>
                        <p className="font-medium text-sm">{formatPercentage(criteria.debt_loan_to_value_min)}</p>
                      </div>
                    )}
                    {criteria.debt_loan_to_value_max && (
                      <div className="bg-white p-2 rounded border border-blue-100">
                        <span className="text-xs text-blue-600">Maximum LTV</span>
                        <p className="font-medium text-sm">{formatPercentage(criteria.debt_loan_to_value_max)}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* DSCR */}
              {(criteria.debt_min_loan_dscr || criteria.debt_max_loan_dscr) && (
                <div className="space-y-2">
                  <span className="text-sm font-medium text-blue-700">Debt Service Coverage Ratio</span>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {criteria.debt_min_loan_dscr && (
                      <div className="bg-white p-2 rounded border border-blue-100">
                        <span className="text-xs text-blue-600">Minimum DSCR</span>
                        <p className="font-medium text-sm">{criteria.debt_min_loan_dscr}x</p>
                      </div>
                    )}
                    {criteria.debt_max_loan_dscr && (
                      <div className="bg-white p-2 rounded border border-blue-100">
                        <span className="text-xs text-blue-600">Maximum DSCR</span>
                        <p className="font-medium text-sm">{criteria.debt_max_loan_dscr}x</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Additional Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {criteria.debt_loan_type && (
                  <div className="bg-white p-2 rounded border border-blue-100">
                    <span className="text-xs text-blue-600">Loan Type</span>
                    <p className="text-sm font-medium">{criteria.debt_loan_type}</p>
                  </div>
                )}
                {criteria.debt_loan_program && (
                  <div className="bg-white p-2 rounded border border-blue-100">
                    <span className="text-xs text-blue-600">Loan Program</span>
                    <p className="text-sm font-medium">{criteria.debt_loan_program}</p>
                  </div>
                )}
              </div>

              {/* Additional Debt Fields */}
              {(criteria.debt_closing_time || criteria.debt_lien_position || criteria.debt_recourse_loan) && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {criteria.debt_closing_time && (
                    <div className="bg-white p-2 rounded border border-blue-100">
                      <span className="text-xs text-blue-600">Closing Time</span>
                      <p className="text-sm font-medium">{criteria.debt_closing_time} days</p>
                    </div>
                  )}
                  {criteria.debt_lien_position && (
                    <div className="bg-white p-2 rounded border border-blue-100">
                      <span className="text-xs text-blue-600">Lien Position</span>
                      <p className="text-sm font-medium">{criteria.debt_lien_position}</p>
                    </div>
                  )}
                  {criteria.debt_recourse_loan && (
                    <div className="bg-white p-2 rounded border border-blue-100">
                      <span className="text-xs text-blue-600">Recourse</span>
                      <p className="text-sm font-medium">{criteria.debt_recourse_loan}</p>
                    </div>
                  )}
                </div>
              )}

              {/* Program Overview */}
              {criteria.debt_program_overview && (
                <div className="bg-white p-3 rounded border border-blue-100">
                  <span className="text-xs text-blue-600">Program Overview</span>
                  <p className="text-sm mt-1">{criteria.debt_program_overview}</p>
                </div>
              )}
            </div>
          )}

          {/* Equity-specific details */}
          {criteria.investment_criteria_equity_id && (
            <div className="space-y-3 p-4 bg-green-50/70 rounded-lg border border-green-200">
              <div className="flex items-center space-x-2 pb-2 border-b border-green-200">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <span className="font-semibold text-green-800">Equity Information</span>
              </div>

              {/* Performance Targets */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {criteria.equity_target_return && (
                  <div className="bg-white p-3 rounded border border-green-100">
                    <span className="text-xs text-green-600">Target Return</span>
                    <p className="font-medium text-lg">{formatPercentage(criteria.equity_target_return)}</p>
                  </div>
                )}
                {criteria.equity_minimum_internal_rate_of_return && (
                  <div className="bg-white p-3 rounded border border-green-100">
                    <span className="text-xs text-green-600">Minimum IRR</span>
                    <p className="font-medium text-lg">{formatPercentage(criteria.equity_minimum_internal_rate_of_return)}</p>
                  </div>
                )}
              </div>

              {/* Hold Periods */}
              {(criteria.equity_min_hold_period_years || criteria.equity_max_hold_period_years) && (
                <div className="space-y-2">
                  <span className="text-sm font-medium text-green-700">Hold Period</span>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {criteria.equity_min_hold_period_years && (
                      <div className="bg-white p-3 rounded border border-green-100">
                        <span className="text-xs text-green-600">Minimum</span>
                        <p className="font-medium">{criteria.equity_min_hold_period_years} years</p>
                      </div>
                    )}
                    {criteria.equity_max_hold_period_years && (
                      <div className="bg-white p-3 rounded border border-green-100">
                        <span className="text-xs text-green-600">Maximum</span>
                        <p className="font-medium">{criteria.equity_max_hold_period_years} years</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Additional Returns */}
              {criteria.equity_minimum_yield_on_cost && (
                <div className="space-y-2">
                  <span className="text-sm font-medium text-green-700">Minimum Yield on Cost</span>
                  <div className="bg-white p-3 rounded border border-green-100">
                    <p className="font-medium">{formatPercentage(criteria.equity_minimum_yield_on_cost)}</p>
                  </div>
                </div>
              )}

              {/* Additional Equity Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {criteria.equity_minimum_equity_multiple && (
                  <div className="bg-white p-3 rounded border border-green-100">
                    <span className="text-xs text-green-600">Min Equity Multiple</span>
                    <p className="font-medium">{criteria.equity_minimum_equity_multiple}x</p>
                  </div>
                )}
                {criteria.equity_target_cash_on_cash_min && (
                  <div className="bg-white p-3 rounded border border-green-100">
                    <span className="text-xs text-green-600">Target Cash on Cash</span>
                    <p className="font-medium">{formatPercentage(criteria.equity_target_cash_on_cash_min)}</p>
                  </div>
                )}
              </div>

              {/* Additional Fields */}
              {(criteria.equity_attachment_point || criteria.equity_max_leverage_tolerance || criteria.equity_typical_closing_timeline_days) && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {criteria.equity_attachment_point && (
                    <div className="bg-white p-2 rounded border border-green-100">
                      <span className="text-xs text-green-600">Attachment Point</span>
                      <p className="text-sm font-medium">{criteria.equity_attachment_point}%</p>
                    </div>
                  )}
                  {criteria.equity_max_leverage_tolerance && (
                    <div className="bg-white p-2 rounded border border-green-100">
                      <span className="text-xs text-green-600">Max Leverage</span>
                      <p className="text-sm font-medium">{criteria.equity_max_leverage_tolerance}x</p>
                    </div>
                  )}
                  {criteria.equity_typical_closing_timeline_days && (
                    <div className="bg-white p-2 rounded border border-green-100">
                      <span className="text-xs text-green-600">Closing Timeline</span>
                      <p className="text-sm font-medium">{criteria.equity_typical_closing_timeline_days} days</p>
                    </div>
                  )}
                </div>
              )}

              {/* Program Overview */}
              {criteria.equity_program_overview && (
                <div className="bg-white p-3 rounded border border-green-100">
                  <span className="text-xs text-green-600">Program Overview</span>
                  <p className="text-sm mt-1">{criteria.equity_program_overview}</p>
                </div>
              )}
            </div>
          )}

          {/* Notes */}
          {criteria.notes && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <FileText className="h-4 w-4 text-gray-600" />
                <span className="font-medium text-sm">Notes</span>
              </div>
              <div className="ml-6 text-sm text-gray-700 bg-gray-50 p-2 rounded">
                {criteria.notes}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Card className={`bg-white shadow-sm border rounded-lg ${className}`}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
            <span className="text-sm text-gray-600">Loading investment criteria...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`bg-white shadow-sm border rounded-lg ${className}`}>
        <CardContent className="p-6">
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              {error}
            </AlertDescription>
          </Alert>
          <div className="mt-4 flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={fetchInvestmentCriteria}
              className="flex items-center space-x-2"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Retry</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (criteriaList.length === 0) {
    return (
      <Card className={`bg-white shadow-sm border rounded-lg ${className}`}>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-purple-600" />
              <span className="text-lg font-semibold">Investment Criteria</span>
            </div>
            <div className="flex space-x-2">
              {onRefresh && (
                <Button variant="ghost" size="sm" onClick={onRefresh}>
                  <RefreshCw className="h-4 w-4" />
                </Button>
              )}
              {onAdd && (
                <Button variant="outline" size="sm" onClick={onAdd}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Criteria
                </Button>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Investment Criteria</h3>
            <p className="text-gray-600 mb-4">
              No investment criteria have been defined for this {entityType.toLowerCase()}.
            </p>
            {onAdd && (
              <Button onClick={onAdd} className="flex items-center space-x-2">
                <Plus className="h-4 w-4" />
                <span>Add Investment Criteria</span>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`bg-white shadow-sm border rounded-lg ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Target className="h-5 w-5 text-purple-600" />
            <div>
              <span className="text-lg font-semibold">Investment Criteria</span>
              <div className="text-sm text-gray-600">{criteriaList.length} criteria defined</div>
            </div>
          </div>
          <div className="flex space-x-2">
            {onRefresh && (
              <Button variant="ghost" size="sm" onClick={onRefresh}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            )}
            {onAdd && (
              <Button variant="outline" size="sm" onClick={onAdd}>
                <Plus className="h-4 w-4 mr-2" />
                Add Criteria
              </Button>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {criteriaList.map((criteria, index) => renderCriteriaCard(criteria, index))}
      </CardContent>
    </Card>
  );
};
