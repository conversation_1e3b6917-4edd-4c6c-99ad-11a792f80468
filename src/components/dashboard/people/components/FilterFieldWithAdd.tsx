import React, { useState, useRef, useEffect } from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, X } from "lucide-react";

interface FilterFieldWithAddProps {
  label: string;
  value: string;
  options: string[];
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export const FilterFieldWithAdd: React.FC<FilterFieldWithAddProps> = ({
  label,
  value,
  options,
  onChange,
  placeholder = "Select or type to add new...",
  className = ""
}) => {
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newOption, setNewOption] = useState("");
  const [showAddButton, setShowAddButton] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Check if current value is not in options (user is typing a new value)
  useEffect(() => {
    if (value && !options.includes(value)) {
      setShowAddButton(true);
    } else {
      setShowAddButton(false);
    }
  }, [value, options]);

  const handleSelectChange = (selectedValue: string) => {
    if (selectedValue === "add_new") {
      setIsAddingNew(true);
      setNewOption(value || "");
      setTimeout(() => inputRef.current?.focus(), 100);
    } else {
      onChange(selectedValue);
      setIsAddingNew(false);
      setNewOption("");
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    setNewOption(inputValue);
    onChange(inputValue);
  };

  const handleAddNew = () => {
    if (newOption.trim()) {
      onChange(newOption.trim());
      setIsAddingNew(false);
      setNewOption("");
      setShowAddButton(false);
    }
  };

  const handleCancelAdd = () => {
    setIsAddingNew(false);
    setNewOption("");
    onChange("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddNew();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelAdd();
    }
  };

  if (isAddingNew) {
    return (
      <div className={`space-y-2 ${className}`}>
        <Label className="text-sm font-medium text-slate-700">{label}</Label>
        <div className="flex gap-2">
          <Input
            ref={inputRef}
            value={newOption}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Type new option..."
            className="flex-1"
          />
          <Button
            type="button"
            size="sm"
            onClick={handleAddNew}
            className="px-3"
          >
            <Plus className="h-4 w-4" />
          </Button>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleCancelAdd}
            className="px-3"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <Label className="text-sm font-medium text-slate-700">{label}</Label>
      <div className="relative">
        <Select value={value} onValueChange={handleSelectChange}>
          <SelectTrigger className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300">
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {options.map((option) => (
              <SelectItem key={option} value={option}>
                {option}
              </SelectItem>
            ))}
            <SelectItem value="add_new" className="text-blue-600 font-medium">
              <Plus className="h-4 w-4 mr-2 inline" />
              Add new option...
            </SelectItem>
          </SelectContent>
        </Select>
        {showAddButton && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setIsAddingNew(true)}
              className="h-6 w-6 p-0 text-blue-600 hover:text-blue-700"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
