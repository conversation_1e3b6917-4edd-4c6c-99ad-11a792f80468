import React from 'react';
import { Dialog, DialogContent, DialogDescription, Di<PERSON><PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertTriangle, ExternalLink } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { ExistingContact } from '../shared/types';

interface DuplicateContactDialogProps {
  isOpen: boolean;
  onClose: () => void;
  duplicateContacts: ExistingContact[];
  onAddAnyway: () => void;
}

export const DuplicateContactDialog: React.FC<DuplicateContactDialogProps> = ({
  isOpen,
  onClose,
  duplicateContacts,
  onAddAnyway
}) => {
  const router = useRouter();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
            Potential Duplicate Contacts Found
          </DialogTitle>
          <DialogDescription>
            We found existing contacts that might be duplicates. Please choose how to proceed.
          </DialogDescription>
        </DialogHeader>
        
        <div className="max-h-96 overflow-y-auto">
          {duplicateContacts.map((contact) => (
            <div key={contact.contact_id} className="border border-gray-200 rounded-lg p-4 mb-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">{contact.first_name} {contact.last_name}</h4>
                  <p className="text-sm text-gray-600">{contact.title}</p>
                  <p className="text-sm text-gray-600">{contact.company_name}</p>
                  {contact.email && <p className="text-sm text-blue-600">{contact.email}</p>}
                  {contact.linkedin_url && (
                    <a href={contact.linkedin_url} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-600 hover:underline">
                      LinkedIn Profile
                    </a>
                  )}
                </div>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      // Navigate to existing contact
                      router.push(`/dashboard/people/${contact.contact_id}`);
                    }}
                  >
                    <ExternalLink className="h-4 w-4 mr-1" />
                    View
                  </Button>
                </div>
              </div>

            </div>
          ))}
        </div>
        
        <DialogFooter className="flex justify-between">
          <div className="flex space-x-2">
            <Button variant="outline" onClick={onClose}>
              Update Existing
            </Button>
            <Button variant="outline" onClick={onClose}>
              Merge Contacts
            </Button>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={onAddAnyway}>
              Add Anyway
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 