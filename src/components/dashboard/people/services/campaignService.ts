import { Campaign, CampaignSequence } from '../types';

/**
 * Fetch all available campaigns from Smartlead
 * @returns Array of campaigns
 */
export async function fetchCampaigns(): Promise<Campaign[]> {
  try {
    const response = await fetch('/api/smartlead/campaigns');
    
    if (!response.ok) {
      throw new Error(`Failed to fetch campaigns: ${response.status}`);
    }
    
    const data = await response.json();
    if (data && data.campaigns && Array.isArray(data.campaigns)) {
      return data.campaigns.map((campaign: any) => ({
        id: campaign.id,
        name: campaign.name || `Campaign #${campaign.id}`,
      }));
    }
    
    return [];
  } catch (error) {
    console.error('Error fetching campaigns:', error);
    throw error;
  }
}

/**
 * Fetch campaign sequence for a specific campaign
 * @param campaignId The campaign ID
 * @returns Campaign sequence data or null if not found
 */
export async function fetchCampaignSequence(campaignId: string): Promise<CampaignSequence | null> {
  if (!campaignId || campaignId === 'all') {
    return null;
  }
  
  try {
    const response = await fetch(`/api/smartlead/campaigns/${campaignId}/sequence`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch campaign sequence: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error fetching sequence for campaign ${campaignId}:`, error);
    return null;
  }
}

/**
 * Fetch messages for a specific campaign and contact
 * @param campaignId The campaign ID
 * @param contactId The contact ID
 * @returns Array of messages
 */
export async function fetchCampaignMessages(campaignId: string, contactId: string | number) {
  try {
    // Always use local messages API to get stored messages with variables
    const url = `/api/messages?contact_id=${contactId}`;
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch messages: ${response.status}`);
    }
    
    const data = await response.json();
    const messages = Array.isArray(data) ? data.map((msg: any) => ({
      ...msg,
      direction: msg.direction || 'outbound',
      smartlead_campaign_id: msg.smartlead_campaign_id || null
    })) : [];
    
    // Filter by campaign if a specific campaign is selected
    if (campaignId && campaignId !== 'all') {
      return messages.filter(msg => 
        msg.smartlead_campaign_id === campaignId || 
        msg.smartlead_campaign_id === parseInt(campaignId)
      );
    }
    
    return messages;
  } catch (error) {
    console.error('Error fetching messages:', error);
    throw error;
  }
}

/**
 * Sync campaign data to Smartlead
 * @param contactId The contact ID
 * @param campaignId The Smartlead campaign ID
 * @param subject Message subject
 * @param body Message body
 * @param customFields Additional custom fields
 * @returns Response data from the sync operation
 */
export async function syncToSmartlead(
  contactId: string | number,
  campaignId: string,
  subject: string,
  body: string,
  customFields: Record<string, string> = {}
) {
  try {
    const response = await fetch(`/api/smartlead/contacts/${contactId}/sync`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        campaignId,
        subject,
        body,
        custom_fields: {
          subject,
          html_body: body,
          ...customFields
        }
      }),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(errorText || `Failed to sync with Smartlead: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error syncing to Smartlead:', error);
    throw error;
  }
} 