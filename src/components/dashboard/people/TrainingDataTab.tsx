"use client"

import React, { useState, useEffect } from 'react';
import { toast } from "sonner";

// Import types
import { TrainingDataTabProps, Message } from './types';

// Import sections
import ContactInfoSection from './sections/ContactInfoSection';
import CampaignSelector from './sections/CampaignSelector';
import CampaignSequenceSection from './sections/CampaignSequenceSection';
import MessagesSection from './sections/MessagesSection';
import ConversationHooksSection from './sections/ConversationHooksSection';
import SearchedDataSection from './sections/SearchedDataSection';
import ExtractedDataSection from './sections/ExtractedDataSection';
import ExecutiveSummarySection from './sections/ExecutiveSummarySection';
import NotableActivitiesSection from './sections/NotableActivitiesSection';
import PersonalTidbitsSection from './sections/PersonalTidbitsSection';

// Import services
import { fetchCampaignMessages } from './services/campaignService';

// Import utilities
import { replaceVariables } from './utils';

// Add highlight animation style
const highlightStyles = `
  @keyframes pulse-highlight {
    0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
    100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
  }
  
  .highlight-message {
    animation: pulse-highlight 2s infinite;
    border: 2px solid #3b82f6 !important;
  }
  
  /* Styles for email content rendering */
  .email-content {
    line-height: 1.5;
  }
  
  .email-content p {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }
  
  .email-content ul {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
    padding-left: 2em;
  }
  
  .email-content li {
    margin-bottom: 0.25em;
  }
  
  .email-content a {
    color: #3b82f6;
    text-decoration: underline;
  }
  
  .email-content * {
    max-width: 100%;
  }
`;

const TrainingDataTab: React.FC<TrainingDataTabProps> = ({ contactId, contact, normalizedScrapedData }) => {
  // State for messages and filtering
  const [messages, setMessages] = useState<Message[]>([]);
  const [filteredMessages, setFilteredMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>('all');
  const [messageDirection, setMessageDirection] = useState<string>('all');
  const [messagesSort, setMessagesSort] = useState<string>('newest');
  
  // Fetch messages when campaign changes
  useEffect(() => {
    const loadMessages = async () => {
      if (!contactId) {
        toast.error('Contact ID is required to fetch messages');
        return;
      }
      
      setIsLoading(true);
      try {
        const messagesData = await fetchCampaignMessages(selectedCampaignId, contactId);
        setMessages(messagesData);
      } catch (error) {
        console.error('Error fetching messages:', error);
        toast.error(`Failed to load messages: ${(error as Error).message}`);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadMessages();
  }, [selectedCampaignId, contactId]);
  
  // Apply filters and sorting
  useEffect(() => {
    let filtered = [...messages];
    
    // Apply direction filter
    if (messageDirection !== 'all') {
      filtered = filtered.filter(msg => msg.direction === messageDirection);
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      const dateA = new Date(a.created_at).getTime();
      const dateB = new Date(b.created_at).getTime();
      return messagesSort === 'newest' ? dateB - dateA : dateA - dateB;
    });
    
    setFilteredMessages(filtered);
  }, [messages, messageDirection, messagesSort]);
  
  // Message edit handler
  const handleMessageEdit = async (messageId: string, subject: string, body: string, metadata: any = {}, syncToSmartlead: boolean = false) => {
    try {
      const message = messages.find(msg => msg.message_id === messageId);
      if (!message) {
        throw new Error('Original message not found');
      }
      
      // Sanitize HTML to keep only simple tags - do this first for both database and Smartlead
      const sanitizedHtml = sanitizeHtmlForSmartlead(body);
      
      // Ensure metadata is always an object, even if undefined is passed
      const metadataToUse = metadata || {};
      
      // Combine existing metadata with new metadata
      const updatedMetadata = {
        ...(message.metadata || {}),
        ...metadataToUse
      };
      
      console.log('Updating message with metadata:', updatedMetadata);
      
      const response = await fetch(`/api/messages/${messageId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subject,
          body: sanitizedHtml, // Save sanitized HTML to database
          smartlead_campaign_id: message.smartlead_campaign_id || undefined,
          metadata: updatedMetadata
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update message: ${response.status}`);
      }
      
      // Update the message in the local state with sanitized HTML and updated metadata
      setMessages(messages.map(msg => 
        msg.message_id === messageId
          ? { ...msg, subject, body: sanitizedHtml, metadata: updatedMetadata }
          : msg
      ));
      
      toast.success('Message updated successfully');
      
      // If syncToSmartlead is true, sync the contact to Smartlead
      if (syncToSmartlead && contactId && message.smartlead_campaign_id) {
        try {
          console.log(`Syncing message to Smartlead with campaign ID: ${message.smartlead_campaign_id}`);
          
          // CRITICAL FIX: Replace variables in both subject and body before sending to Smartlead
          const variablesToUse = {
            ...(updatedMetadata.variables || {}),
            // Always prioritize contact fields for personalization
            ...(contact?.first_name && { first_name: contact.first_name }),
            ...(contact?.last_name && { last_name: contact.last_name }),
            ...(contact?.email && { email: contact.email }),
            ...(contact?.company_name && { company_name: contact.company_name }),
            ...(contact?.job_title && { job_title: contact.job_title }),
            ...(contact?.phone_number && { phone_number: contact.phone_number }),
            ...(contact?.company_website && { company_website: contact.company_website }),
            ...(contact?.industry && { industry: contact.industry })
          };
          
          // Replace variables in subject and body for Smartlead
          const processedSubject = replaceVariables(subject, contact || null, variablesToUse);
          const processedBody = replaceVariables(sanitizedHtml, contact || null, variablesToUse);
          
          // CONSISTENCY FIX: Sanitize all custom field variables that contain HTML
          const sanitizedCustomFields: Record<string, string> = {};
          Object.entries(variablesToUse).forEach(([key, value]) => {
            if (typeof value === 'string' && value.includes('<')) {
              // If the value contains HTML, sanitize it for consistency
              sanitizedCustomFields[key] = sanitizeHtmlForSmartlead(value);
            } else {
              // If it's plain text, use as-is
              sanitizedCustomFields[key] = String(value);
            }
          });
          
          console.log('processedSubject', processedSubject);
          console.log('processedBody', processedBody);
          console.log('sanitizedCustomFields', sanitizedCustomFields);
          // Use the variables from metadata for Smartlead
          const syncResponse = await fetch(`/api/smartlead/contacts/${contactId}/sync`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              campaignId: message.smartlead_campaign_id,
              subject: processedSubject,
              body: processedBody,
              is_html: true,
              // Pass all variables as custom fields for reference
              custom_fields: sanitizedCustomFields
            }),
          });
          
          if (!syncResponse.ok) {
            const syncErrorText = await syncResponse.text();
            console.error(`Error syncing to Smartlead (Status ${syncResponse.status}):`, syncErrorText);
            toast.warning('Message updated but sync to Smartlead failed');
          } else {
            const syncData = await syncResponse.json();
            
            // Update contact data if available
            if (syncData.contact && syncData.contact.smartlead_lead_id && contact) {
              contact.smartlead_lead_id = syncData.contact.smartlead_lead_id;
              contact.smartlead_status = syncData.contact.smartlead_status;
              contact.last_email_sent_at = new Date().toISOString();
            }
            
            toast.success('Message synced to Smartlead with variables replaced');
          }
        } catch (syncError) {
          console.error('Exception during Smartlead sync:', syncError);
          toast.warning('Message updated but sync to Smartlead failed');
        }
      }
      
    } catch (error) {
      console.error('Error updating message:', error);
      toast.error(`Failed to update message: ${(error as Error).message}`);
      throw error;
    }
  };
  
  // Function to sanitize HTML for Smartlead, removing CSS and complex HTML
  const sanitizeHtmlForSmartlead = (html: string): string => {
    if (!html) return '';
    
    try {
      // First, normalize newlines in the original HTML to avoid excessive line breaks
      let normalizedHtml = html.replace(/\\n+/g, ' ')
      //remove consecutive <br> tags
      normalizedHtml = normalizedHtml.replace(/<br\s*\/?>\s*<br\s*\/?>/gi, '<br>');
      
      // Create a temporary DOM element to parse the HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = normalizedHtml;
      
      // Remove all style tags
      const styleTags = tempDiv.querySelectorAll('style');
      styleTags.forEach(tag => tag.remove());
      
      // Remove all inline styles
      const elementsWithStyle = tempDiv.querySelectorAll('[style]');
      elementsWithStyle.forEach(el => el.removeAttribute('style'));
      
      // Remove all class attributes
      const elementsWithClass = tempDiv.querySelectorAll('[class]');
      elementsWithClass.forEach(el => el.removeAttribute('class'));
      
      // Keep only allowed simple tags (whitelist approach)
      const whitelistedTags = ['p', 'a', 'br', 'b', 'i', 'strong', 'em', 'u', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
      
      // Helper function to sanitize node
      const sanitizeNode = (node: HTMLElement): void => {
        // Process all child nodes
        Array.from(node.childNodes).forEach(child => {
          if (child.nodeType === Node.ELEMENT_NODE) {
            const childElement = child as HTMLElement;
            const tagName = childElement.tagName.toLowerCase();
            
            // If it's not a whitelisted tag, replace with its content
            if (!whitelistedTags.includes(tagName)) {
              // Create a document fragment with the child's content
              const fragment = document.createDocumentFragment();
              Array.from(childElement.childNodes).forEach(grandchild => {
                fragment.appendChild(grandchild.cloneNode(true));
              });
              
              // Replace the child with its content
              node.replaceChild(fragment, child);
            } else {
              // Recursively process this child's children
              sanitizeNode(childElement);
            }
          }
        });
      };
      
      // Start sanitizing from the root
      sanitizeNode(tempDiv);
      
      // Get the sanitized HTML
      let result = tempDiv.innerHTML;
      
      // Remove all newline characters that might be in the HTML content as text nodes
      result = result.replace(/\n/g, '');
      
      // Clean up consecutive <br> tags
      result = result.replace(/<br\s*\/?>\s*<br\s*\/?>/gi, '<br>');
      
      return result;
    } catch (error) {
      console.error('Error sanitizing HTML:', error);
      // If sanitization fails, strip all HTML tags as a fallback
      return html.replace(/<[^>]*>/g, '');
    }
  };
  
  // Message delete handler
  const handleMessageDelete = async (messageId: string) => {
    try {
      const response = await fetch(`/api/messages/${messageId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete message: ${response.status}`);
      }
      
      // Remove the message from the local state
      setMessages(messages.filter(msg => msg.message_id !== messageId));
      
      toast.success('Message deleted successfully');
      
    } catch (error) {
      console.error('Error deleting message:', error);
      toast.error(`Failed to delete message: ${(error as Error).message}`);
      throw error;
    }
  };
  
  // Handle new message saved (from CampaignSequenceSection)
  const handleMessageSaved = (newMessage: Message) => {
    setMessages([newMessage, ...messages]);
  };

  // Get the campaign name for the selected campaign
  const getCampaignName = () => {
    const campaign = messages.find(msg => msg.smartlead_campaign_id === selectedCampaignId);
    return campaign?.metadata?.campaign_name || `Campaign ${selectedCampaignId}`;
  };
  
  return (
    <div className="space-y-6">
      {/* Add style for message highlighting */}
      <style dangerouslySetInnerHTML={{ __html: highlightStyles }} />
      
      {/* Contact Information */}
      {/* {contact && <ContactInfoSection contact={contact} />} */}
      {/* Searched Data Section */}
      {/* {contact && <SearchedDataSection contact={contact} />} */}
      {/* Executive Summary Section */}
      {/* {contact && <ExecutiveSummarySection contact={contact} />} */}
      {/* Notable Activities Section */}
      {/* {contact && <NotableActivitiesSection contact={contact} />} */}
      {/* Personal Tidbits Section */}
      {/* {contact && <PersonalTidbitsSection contact={contact} />} */}
      {/* Extracted Data Section */}
      {/* <ExtractedDataSection normalizedScrapedData={normalizedScrapedData} /> */}
      {/* Conversation Hooks */}
      {/* {contact && <ConversationHooksSection contact={contact} contactId={contactId} />} */}
      
      {/* Campaign Selector */}
      <CampaignSelector 
        selectedCampaignId={selectedCampaignId}
        onCampaignSelect={setSelectedCampaignId}
      />
      
      {/* Only show campaign sequence section if a campaign is selected */}
      {selectedCampaignId !== 'all' && (
        <CampaignSequenceSection
          contactId={contactId}
          contact={contact}
          campaignId={selectedCampaignId}
          campaignName={getCampaignName()}
          messageData={messages.find(msg => msg.smartlead_campaign_id === selectedCampaignId) || messages[0]}
          onMessageSaved={handleMessageSaved}
        />
      )}
      
      {/* Messages Section */}
      <MessagesSection
        messages={filteredMessages}
        isLoading={isLoading}
        selectedCampaignId={selectedCampaignId}
        campaignName={getCampaignName()}
        onMessageDelete={handleMessageDelete}
        onMessageEdit={handleMessageEdit}
        messageDirection={messageDirection}
        onDirectionChange={setMessageDirection}
        messagesSort={messagesSort}
        onSortChange={setMessagesSort}
        contactInfo={contact}
      />
    </div>
  );
};

export default TrainingDataTab;