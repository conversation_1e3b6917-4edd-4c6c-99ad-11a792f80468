"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ArrowLeft, Users, Building2 } from "lucide-react"
import { CompanySuggestion } from './shared/types'
import AddContactSimplified from './addcontact/AddContactSimplified'
import AddContactWithCompany from './addcontact/AddContactWithCompany'

// Props interface for the AddContact component
interface AddContactProps {
  onBack: () => void;
  companyId?: string;
  preSelectedCompany?: CompanySuggestion;
  onSuccess?: (contactId: number) => void;
}

const AddContact: React.FC<AddContactProps> = ({ onBack, companyId, preSelectedCompany, onSuccess }) => {
  const [useCombinedForm, setUseCombinedForm] = useState(false);
  const [showForm, setShowForm] = useState(false);

  // If user wants to create both contact and company, use the combined form
  if (useCombinedForm) {
    return (
      <AddContactWithCompany
        onBack={onBack}
        companyId={companyId}
        preSelectedCompany={preSelectedCompany}
        onSuccess={(contactId, companyId) => {
          if (onSuccess) {
            onSuccess(contactId);
          }
        }}
      />
    );
  }

  // If user chooses "Add Contact Only", show the simplified form
  if (showForm) {
    return (
      <AddContactSimplified
        onBack={onBack}
        companyId={companyId}
        preSelectedCompany={preSelectedCompany}
        onSuccess={onSuccess}
        onSwitchToCombinedForm={() => setUseCombinedForm(true)}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="w-full p-6">
        <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={onBack} className="hover:bg-slate-100 rounded-xl">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                  Add New Contact
                </h1>
                <p className="text-slate-600 mt-1">Choose how you want to add a contact</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid gap-6 grid-cols-1 md:grid-cols-2">
          <Card className="bg-white shadow-xl border-0 rounded-2xl cursor-pointer hover:shadow-2xl transition-all duration-200" onClick={() => setShowForm(true)}>
            <CardContent className="p-6">
              <div className="text-center">
                <Users className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Add Contact Only</h3>
                <p className="text-gray-600">Create a new contact and link to an existing company</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-xl border-0 rounded-2xl cursor-pointer hover:shadow-2xl transition-all duration-200" onClick={() => setUseCombinedForm(true)}>
            <CardContent className="p-6">
              <div className="text-center">
                <Building2 className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Add Contact & Company</h3>
                <p className="text-gray-600">Create both a new contact and a new company together</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AddContact;
