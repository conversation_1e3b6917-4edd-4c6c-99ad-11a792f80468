import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Search, 
  MessageSquare, 
  SortAsc, 
  ArrowDown, 
  ArrowUp, 
  Trash2, 
  Users, 
  Calendar, 
  Mail, 
  ChevronLeft,
  Send,
  Filter,
  SlidersHorizontal
} from 'lucide-react';
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import ContactDetail from './ContactDetail';

// Simple debounce implementation
const debounce = (fn: Function, ms = 300) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return function (this: any, ...args: any[]) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(this, args), ms);
  };
};

// Add proper typing for threads
interface Thread {
  thread_id: string;
  subject: string;
  status: string;
  created_at: string;
  updated_at: string;
  metadata: any;
  participant_count: number;
  message_count: number;
  participants?: ThreadParticipant[];
  messages?: Message[];
  participant_emails?: string[];
}

interface ThreadParticipant {
  thread_id: string;
  contact_id: number;
  user_id?: string;
  participant_type: string;
  email: string;
  first_name?: string;
  last_name?: string;
  company_name?: string;
}

interface Message {
  message_id: string;
  thread_id: string;
  from_email: string | null;
  to_email: string;
  subject: string;
  body: string;
  direction: string;
  role: string;
  sent_at: string;
  created_at: string;
  metadata: any;
}

interface Contact {
  contact_id: number;
  first_name: string;
  last_name: string;
  email: string;
  company_name?: string;
}

const ThreadManagementView = () => {
  const [threads, setThreads] = useState<Thread[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedThread, setSelectedThread] = useState<string | null>(null);
  const [threadDetail, setThreadDetail] = useState<Thread | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [pageSize, setPageSize] = useState(20);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [contactFilter, setContactFilter] = useState<number | null>(null);
  const [contactEmailFilter, setContactEmailFilter] = useState<string>('');
  
  // Add state for confirm dialogs
  const [threadToDelete, setThreadToDelete] = useState<string | null>(null);
  const [messageToDelete, setMessageToDelete] = useState<string | null>(null);
  
  // Add sort state
  const [sortField, setSortField] = useState<string>('updated_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  
  // Add state for status filter
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Add state for contact detail view
  const [selectedContactId, setSelectedContactId] = useState<number | null>(null);
  
  // Add state for mobile sidebar visibility
  const [showMobileSidebar, setShowMobileSidebar] = useState(false);
  
  // Add reference for dialog container
  const dialogContainerRef = useRef<HTMLDivElement>(null);

  const debouncedSearch = useCallback(
    debounce((term: string) => {
      setDebouncedSearchTerm(term);
    }, 300),
    []
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    debouncedSearch(value);
  };

  // Add a debugging function
  const logCurrentFilters = () => {
    console.log('Current filters:', {
      search: debouncedSearchTerm,
      contactId: contactFilter,
      contactEmail: contactEmailFilter,
      status: statusFilter,
      sort: sortField,
      direction: sortDirection,
      page: currentPage
    });
  };

  // Better handling for contact filter
  const clearContactFilter = () => {
    setContactFilter(null);
    setCurrentPage(1);
    console.log('Cleared contact filter');
  };

  const clearContactEmailFilter = () => {
    setContactEmailFilter('');
    setCurrentPage(1);
    console.log('Cleared contact email filter');
  };

  // Fetch threads
  useEffect(() => {
    setLoading(true);
    
    // Build the query parameters
    const params = new URLSearchParams();
    params.append('page', currentPage.toString());
    params.append('limit', pageSize.toString());
    
    if (debouncedSearchTerm) {
      params.append('search', debouncedSearchTerm);
    }
    
    if (contactFilter) {
      params.append('contact_id', contactFilter.toString());
    }
    
    if (contactEmailFilter) {
      params.append('contact_email', contactEmailFilter);
    }
    
    if (statusFilter && statusFilter !== 'all') {
      params.append('status', statusFilter);
    }
    
    params.append('sort', sortField);
    params.append('direction', sortDirection);
    
    // Log the request we're about to make
    console.log('Fetching threads with params:', params.toString());
    
    // Use a try-catch block for better error handling
    try {
      fetch(`/api/threads?${params.toString()}`)
        .then(res => {
          if (!res.ok) {
            return res.text().then(text => {
              throw new Error(`Failed to fetch threads: ${res.status} - ${text}`);
            });
          }
          return res.json();
        })
        .then(data => {
          console.log('Fetched threads:', data.threads?.length || 0, 'total:', data.totalCount);
          setThreads(Array.isArray(data.threads) ? data.threads : []);
          setTotalPages(data.totalPages || 1);
          setTotalCount(data.totalCount || 0);
          setLoading(false);
        })
        .catch(err => {
          console.error('Error fetching threads:', err);
          setThreads([]);
          setLoading(false);
          toast.error("Failed to fetch threads: " + err.message);
        });
    } catch (error) {
      console.error('Error in thread filtering:', error);
      setLoading(false);
      toast.error("Failed to apply filters");
    }
  }, [currentPage, pageSize, debouncedSearchTerm, contactFilter, contactEmailFilter, statusFilter, sortField, sortDirection]);

  // Fetch thread detail when a thread is selected
  useEffect(() => {
    if (selectedThread) {
      setLoading(true);
      fetch(`/api/threads/${selectedThread}`)
        .then(res => {
          if (!res.ok) {
            throw new Error(`Failed to fetch thread detail: ${res.status}`);
          }
          return res.json();
        })
        .then(data => {
          console.log('Fetched thread detail:', data);
          setThreadDetail(data);
          setLoading(false);
        })
        .catch(err => {
          console.error('Error fetching thread detail:', err);
          setThreadDetail(null);
          setLoading(false);
          toast.error("Failed to fetch thread details");
        });
    }
  }, [selectedThread]);

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Handle sort change
  const handleSortChange = (field: string) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // New field, set default direction
      setSortField(field);
      setSortDirection('asc');
    }
    // Reset to first page when sort changes
    setCurrentPage(1);
  };

  // Delete a thread
  const deleteThread = async () => {
    if (!threadToDelete) return;
    
    try {
      const response = await fetch(`/api/threads/${threadToDelete}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete thread: ${response.status}`);
      }
      
      // If we were viewing this thread, go back to the thread list
      if (selectedThread === threadToDelete) {
        setSelectedThread(null);
        setThreadDetail(null);
      }
      
      // Remove the thread from the list
      setThreads(threads.filter(thread => thread.thread_id !== threadToDelete));
      
      toast.success("Thread deleted successfully");
    } catch (error) {
      console.error('Error deleting thread:', error);
      toast.error("Failed to delete thread");
    } finally {
      setThreadToDelete(null);
    }
  };

  // Delete a message
  const deleteMessage = async () => {
    if (!messageToDelete || !selectedThread) return;
    
    try {
      console.log(`Deleting message with ID: ${messageToDelete}`);
      const response = await fetch(`/api/messages/${messageToDelete}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete message: ${response.status} - ${errorText}`);
      }
      
      // Remove the message from the thread detail
      if (threadDetail && threadDetail.messages) {
        const deletedMessageData = await response.json();
        console.log('Delete message response:', deletedMessageData);
        
        setThreadDetail({
          ...threadDetail,
          messages: threadDetail.messages.filter(message => message.message_id !== messageToDelete),
          message_count: threadDetail.message_count - 1
        });
      }
      
      toast.success("Message deleted successfully");
    } catch (error) {
      console.error('Error deleting message:', error);
      toast.error(`Failed to delete message: ${(error as Error).message}`);
    } finally {
      setMessageToDelete(null);
    }
  };

  // Function to get initials from name
  const getInitials = (firstName: string = '', lastName: string = '') => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  // Function to get email initials safely
  const getEmailInitials = (email: string | null | undefined) => {
    if (!email) return 'UN';
    // Try to get first two chars, or just the first if only one char
    return email.substring(0, Math.min(2, email.length)).toUpperCase();
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Function to handle participant click
  const handleParticipantClick = (contactId: number) => {
    if (contactId) {
      console.log('Opening contact detail for contact ID:', contactId);
      setSelectedContactId(contactId);
    } else {
      toast.error("Cannot open contact details. Invalid contact ID.");
    }
  };

  // Function to go back from contact detail view
  const handleBackFromContact = () => {
    setSelectedContactId(null);
  };

  // Contact Detail View
  if (selectedContactId !== null) {
    return (
      <ContactDetail 
        contactId={selectedContactId} 
        onBack={handleBackFromContact} 
      />
    );
  }

  // Thread Detail View
  if (selectedThread && threadDetail) {
    return (
      <div className="flex flex-col h-screen relative" ref={dialogContainerRef}>
        {/* Thread Header */}
        <div className="bg-white dark:bg-gray-900 dark:text-white text-gray-900 p-4 flex items-center justify-between shadow-sm">
          <div className="flex items-center">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => {
                setSelectedThread(null);
                setThreadDetail(null);
              }}
              className="mr-2 text-gray-600 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-800"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <div>
              <h2 className="text-lg font-semibold">{threadDetail.subject || 'No Subject'}</h2>
              <div className="text-sm text-gray-500 dark:text-gray-300 flex flex-wrap gap-2 items-center">
                <span className="flex items-center"><Users className="h-3 w-3 mr-1" />{threadDetail.participants?.length || 0} participants</span>
                <span className="flex items-center"><MessageSquare className="h-3 w-3 mr-1" />{threadDetail.messages?.length || 0} messages</span>
                <span className="flex items-center">Status: {threadDetail.status}</span>
              </div>
            </div>
          </div>
          <div>
            <Button 
              variant="destructive" 
              size="sm"
              onClick={() => setThreadToDelete(threadDetail.thread_id)}
              className="flex items-center bg-red-600 hover:bg-red-700"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Thread
            </Button>
          </div>
        </div>

        {/* Participant List */}
        <div className="bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-sm font-medium mb-2 flex items-center text-gray-600 dark:text-gray-300">
            <Users className="h-4 w-4 mr-1" />
            Participants
          </h3>
          <div className="flex flex-wrap gap-2">
            {threadDetail.participants && threadDetail.participants.map((participant) => (
              <Badge 
                key={`${participant.thread_id}-${participant.contact_id || participant.email}`} 
                className="bg-gray-200 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600 cursor-pointer transition-colors"
                onClick={() => participant.contact_id ? handleParticipantClick(participant.contact_id) : null}
              >
                {participant.first_name && participant.last_name 
                  ? `${participant.first_name} ${participant.last_name}` 
                  : participant.email}
                {participant.company_name && ` (${participant.company_name})`}
              </Badge>
            ))}
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-auto p-4 bg-gray-50">
          {loading ? (
            <div className="flex justify-center items-center h-full">
              <div className="animate-pulse text-gray-500">Loading messages...</div>
            </div>
          ) : threadDetail.messages && threadDetail.messages.length > 0 ? (
            <div className="space-y-8 max-w-4xl mx-auto py-4">
              {threadDetail.messages.map((message) => {
                const isOutbound = message.direction === 'outbound';
                
                return (
                  <div 
                    key={message.message_id} 
                    className="relative border-b border-gray-100 pb-6 last:border-0"
                  >
                    <div className="flex items-start mb-2">
                      <Avatar className={`h-8 w-8 ${isOutbound ? 'bg-blue-600' : 'bg-gray-600'} text-white`}>
                        <AvatarFallback>
                          {isOutbound ? 'A' : (message.from_email ? message.from_email.charAt(0).toUpperCase() : '?')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="ml-3 flex-1">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="font-medium text-sm">
                              {isOutbound ? 'ANAX' : (message.from_email || 'Unknown Sender')}
                            </div>
                            <div className="text-xs text-gray-400 dark:text-gray-300 flex items-center gap-2">
                              <span>To: {message.to_email || 'Unknown Recipient'}</span>
                              <span className="text-gray-400 dark:text-gray-300">•</span>
                              <span>{formatDate(message.sent_at || message.created_at)}</span>
                            </div>
                          </div>
                          <div className="flex items-center">
                            {message.metadata?.lead_id && (
                              <Badge variant="outline" className="mr-2 text-xs bg-green-50 text-green-700">
                                Lead ID: {message.metadata.lead_id}
                              </Badge>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-gray-400 hover:text-red-600 h-7 w-7 p-0 rounded-full"
                              onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                                e.stopPropagation();
                                setMessageToDelete(message.message_id);
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        
                        {message.subject && (
                          <div className="font-medium text-gray-800 mt-3 mb-1">
                            Subject: {message.subject}
                          </div>
                        )}
                        
                        <div className="text-gray-700 mt-3 leading-relaxed whitespace-pre-wrap">
                          {message.body}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-10 text-gray-500">
              No messages in this thread
            </div>
          )}
        </div>

        {/* Delete Thread Confirmation Dialog */}
        <AlertDialog open={Boolean(threadToDelete)} onOpenChange={() => setThreadToDelete(null)}>
          <AlertDialogContent className="max-w-md shadow-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white absolute z-50 left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
            <AlertDialogHeader>
              <AlertDialogTitle className="text-red-400 flex items-center">
                <Trash2 className="h-5 w-5 mr-2" />
                Delete Thread
              </AlertDialogTitle>
              <AlertDialogDescription className="text-gray-600 dark:text-gray-300">
                Are you sure you want to delete this thread? This action will permanently remove the thread, all its messages, and participant information. This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel className="border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={deleteThread} className="bg-red-600 hover:bg-red-700 text-white">Delete</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Delete Message Confirmation Dialog */}
        <AlertDialog open={Boolean(messageToDelete)} onOpenChange={() => setMessageToDelete(null)}>
          <AlertDialogContent className="max-w-md shadow-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white absolute z-50 left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
            <AlertDialogHeader>
              <AlertDialogTitle className="text-red-400 flex items-center">
                <Trash2 className="h-5 w-5 mr-2" />
                Delete Message
              </AlertDialogTitle>
              <AlertDialogDescription className="text-gray-600 dark:text-gray-300">
                Are you sure you want to delete this message? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel className="border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={deleteMessage} className="bg-red-600 hover:bg-red-700 text-white">Delete</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    );
  }

  return (
    <div className="flex flex-col md:flex-row h-screen w-full relative" ref={dialogContainerRef}>
      {/* Mobile Sidebar Toggle */}
      <Button 
        variant="outline" 
        size="sm" 
        className="md:hidden m-2 fixed top-2 right-2 z-10"
        onClick={() => setShowMobileSidebar(!showMobileSidebar)}
      >
        <SlidersHorizontal className="h-4 w-4 mr-2" />
        {showMobileSidebar ? 'Hide Filters' : 'Show Filters'}
      </Button>
      
      {/* Left Sidebar */}
      <div className={`
        ${showMobileSidebar ? 'block' : 'hidden'} 
        md:block 
        md:w-72 
        w-full 
        border-r 
        bg-white 
        p-6 
        md:h-screen 
        overflow-auto
        ${showMobileSidebar ? 'fixed md:relative inset-0 z-50 pb-20' : ''}
      `}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-medium">Search & Filter</h2>
        </div>

        {/* Search */}
        {/* <div className="relative mb-6">
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
          <Input 
            placeholder="Search threads..." 
            value={searchTerm}
            onChange={handleSearchChange}
            className="pl-10 h-9"
          />
        </div> */}

        {/* Contact Filter */}
        <div className="mb-6">
          {/* Contact Email Filter */}
          <div className="mb-6">
            <h3 className="text-sm font-medium mb-3 flex items-center">
              <Mail className="h-4 w-4 mr-1" />
              Filter by Contact Email
            </h3>
            <div className="flex gap-2">
              <Input
                type="email"
                placeholder="Enter Email"
                value={contactEmailFilter}
                onChange={(e: React.ChangeEvent<HTMLInputElement> ) => {
                  setContactEmailFilter(e.target.value);
                }}
                className="h-9 flex-1"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={clearContactEmailFilter}
              >
                Clear
              </Button>
            </div>
          </div>
        </div>

        {/* Add Sorting Options */}
        <div className="mb-6">
          <h3 className="text-sm font-medium mb-3 flex items-center">
            <SortAsc className="h-4 w-4 mr-1" />
            Sort By
          </h3>
          <Select 
            value={sortField} 
            onValueChange={(value) => {
              setSortField(value);
              setCurrentPage(1);
            }}
          >
            <SelectTrigger className="w-full h-9">
              <SelectValue placeholder="Sort by..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="updated_at">Last Updated</SelectItem>
              <SelectItem value="created_at">Creation Date</SelectItem>
              <SelectItem value="subject">Subject</SelectItem>
              <SelectItem value="status">Status</SelectItem>
              <SelectItem value="message_count">Message Count</SelectItem>
              <SelectItem value="participant_count">Participant Count</SelectItem>
            </SelectContent>
          </Select>
          
          <div className="flex items-center mt-2 gap-2">
            <Button 
              variant={sortDirection === 'asc' ? 'default' : 'outline'} 
              size="sm"
              className="flex-1"
              onClick={() => setSortDirection('asc')}
            >
              <ArrowUp className="h-4 w-4 mr-1" />
              Ascending
            </Button>
            <Button 
              variant={sortDirection === 'desc' ? 'default' : 'outline'} 
              size="sm"
              className="flex-1"
              onClick={() => setSortDirection('desc')}
            >
              <ArrowDown className="h-4 w-4 mr-1" />
              Descending
            </Button>
          </div>
        </div>

        <div className="text-sm text-gray-500 mt-6 pt-4 border-t">
          Showing {threads.length > 0 ? (currentPage - 1) * pageSize + 1 : 0}-{Math.min(currentPage * pageSize, totalCount)} of {totalCount} threads
        </div>

        {/* Only show on mobile */}
        <div className="md:hidden mt-6">
          <Button
            variant="default"
            className="w-full"
            onClick={() => setShowMobileSidebar(false)}
          >
            Apply Filters
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 bg-gray-50 dark:bg-gray-900 p-4 md:p-6 overflow-auto">
        <div className="flex justify-between items-center mb-4 sticky top-0 bg-white dark:bg-gray-900 p-2 z-10 rounded-lg">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Thread Management</h2>
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300 hidden md:flex">
            <span>Sorted by:</span>
            <span className="font-medium">
              {sortField === 'updated_at' ? 'Last Updated' : 
               sortField === 'created_at' ? 'Creation Date' : 
               sortField === 'subject' ? 'Subject' : 
               sortField === 'status' ? 'Status' :
               sortField === 'message_count' ? 'Message Count' :
               'Participant Count'}
            </span>
            <span>{sortDirection === 'asc' ? '(A-Z)' : '(Z-A)'}</span>
          </div>
        </div>

        {loading ? (
          <div className="text-center py-10 flex justify-center items-center h-64">
            <div className="animate-pulse text-gray-500 dark:text-gray-300">Loading threads...</div>
          </div>
        ) : threads.length === 0 ? (
          <div className="text-center py-10 text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-lg shadow-sm p-8">
            <MessageSquare className="h-12 w-12 mx-auto text-gray-500 mb-4" />
            <p className="font-medium">No threads found matching your filters</p>
            <p className="text-sm mt-2">Try adjusting your search criteria or filters</p>
          </div>
        ) : (
          <div className="space-y-4">
            {threads.map((thread) => (
              <Card 
                key={thread.thread_id} 
                className="border border-gray-200 hover:border-blue-500 shadow-none transition-colors cursor-pointer bg-white text-gray-900 dark:bg-gray-800 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-750"
                onClick={() => setSelectedThread(thread.thread_id)}
              >
                <CardContent className="p-4 md:p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3 flex-1">
                      <MessageSquare className="h-5 w-5 text-blue-400" />
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-gray-100">{thread.subject || 'No Subject'}</h3>
                        {Array.isArray(thread.participant_emails) && thread.participant_emails.length > 0 && (
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate max-w-xs" title={thread.participant_emails.join(', ')}>
                            {thread.participant_emails.slice(0, 3).join(', ')}
                            {thread.participant_emails.length > 3 && `, +${thread.participant_emails.length - 3} more`}
                          </div>
                        )}
                        <div className="flex flex-wrap items-center gap-x-3 mt-1 text-xs text-gray-500 dark:text-gray-400">
                          <div className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            <span>Created: {formatDate(thread.created_at)}</span>
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            <span>Updated: {formatDate(thread.updated_at)}</span>
                          </div>
                        </div>
                        <div className="flex flex-wrap gap-2 mt-2">
                          <Badge className="bg-gray-200 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600">
                            <Users className="h-3 w-3 mr-1" />
                            {thread.participant_count || 0} participants
                          </Badge>
                          <Badge className="bg-gray-200 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600">
                            <MessageSquare className="h-3 w-3 mr-1" />
                            {thread.message_count || 0} messages
                          </Badge>
                          <Badge className={`
                            ${thread.status === 'active' ? 'bg-green-100 text-green-700 dark:bg-green-800 dark:text-green-100' : 
                              thread.status === 'archived' ? 'bg-amber-100 text-amber-700 dark:bg-amber-800 dark:text-amber-100' : 
                              'bg-blue-100 text-blue-700 dark:bg-blue-800 dark:text-blue-100'} 
                            hover:bg-opacity-80
                          `}>
                            {thread.status || 'Unknown'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      className="text-red-400 hover:text-red-300 hover:bg-gray-700"
                      onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                        e.stopPropagation();
                        setThreadToDelete(thread.thread_id);
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-center space-x-2 mt-6 bg-white dark:bg-gray-800 p-2 rounded-lg shadow-sm text-gray-700 dark:text-white border border-gray-200 dark:border-gray-700">
            <Button
              variant="outline"
              onClick={() => handlePageChange(1)}
              disabled={currentPage === 1}
              size="sm"
              className="hidden sm:flex border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              First
            </Button>
            <Button
              variant="outline"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              size="sm"
              className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Previous
            </Button>
            <div className="text-sm text-gray-700 px-4">
              Page {currentPage} of {totalPages}
            </div>
            <Button
              variant="outline"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              size="sm"
              className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Next
            </Button>
            <Button
              variant="outline"
              onClick={() => handlePageChange(totalPages)}
              disabled={currentPage === totalPages}
              size="sm"
              className="hidden sm:flex border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Last
            </Button>
          </div>
        )}
      </div>

      {/* Delete Thread Confirmation Dialog */}
      <AlertDialog open={Boolean(threadToDelete)} onOpenChange={() => setThreadToDelete(null)}>
        <AlertDialogContent className="max-w-md shadow-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white absolute z-50 left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-400 flex items-center">
              <Trash2 className="h-5 w-5 mr-2" />
              Delete Thread
            </AlertDialogTitle>
            <AlertDialogDescription className="text-gray-600 dark:text-gray-300">
              Are you sure you want to delete this thread? This action will permanently remove the thread, all its messages, and participant information. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={deleteThread} className="bg-red-600 hover:bg-red-700 text-white">Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Message Confirmation Dialog */}
      <AlertDialog open={Boolean(messageToDelete)} onOpenChange={() => setMessageToDelete(null)}>
        <AlertDialogContent className="max-w-md shadow-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white absolute z-50 left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-400 flex items-center">
              <Trash2 className="h-5 w-5 mr-2" />
              Delete Message
            </AlertDialogTitle>
            <AlertDialogDescription className="text-gray-600 dark:text-gray-300">
              Are you sure you want to delete this message? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={deleteMessage} className="bg-red-600 hover:bg-red-700 text-white">Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ThreadManagementView; 