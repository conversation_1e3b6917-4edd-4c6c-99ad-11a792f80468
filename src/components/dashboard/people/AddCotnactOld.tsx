"use client"

import React, { useState, useEffect, use<PERSON><PERSON>back, useRef } from 'react'
import { Card, CardContent, CardHeader, Card<PERSON>itle, CardFooter } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import { ArrowLeft, Plus, Trash2, Search, Loader2, CheckCircle, X, ExternalLink, Building2, MapPin, Users, Calendar, DollarSign, Target, Eye, EyeOff, AlertTriangle, GraduationCap, Heart } from "lucide-react"
import { debounce } from 'lodash'
import Link from 'next/link'
import { Contact, ContactFormData, InvestmentCriteria, ExistingContact, QueuedContact, CompanySuggestion, FieldValidationStates } from './shared/types'
import { contactFormToDb, validateField, determineCapitalType } from './shared/contactFields'

import { capitalTypes, interestRateIndexes, recourseOptions } from './constants/AddContactConstants'
import { InvestmentCriteriaSection } from './components/InvestmentCriteriaSection'
import { DuplicateContactDialog } from './components/DuplicateContactDialog'
import { FilterFieldWithAdd } from './components/FilterFieldWithAdd'

// Props interface for the AddContact component
interface AddContactProps {
  onBack: () => void;
  companyId?: string;
  preSelectedCompany?: CompanySuggestion;
}

const AddContactOld: React.FC<AddContactProps> = ({ onBack, companyId, preSelectedCompany }) => {
  const [formData, setFormData] = useState<ContactFormData>({
    // Personal Information
    first_name: '',
    last_name: '',
    title: '', // Changed from job_title to title to match db schema
    job_tier: '',
    company_name: preSelectedCompany?.company_name || '',
    
    // Contact Information
    email: '',
    additional_email: '',
    phone_number: '', // Changed from phone to phone_number to match db schema
    phone_number_secondary: '', // Changed from additional_phone to match db schema
    
    // Social Media
    linkedin_url: '', // Changed from linkedin to linkedin_url to match db schema
    twitter: '',
    facebook: '',
    instagram: '',
    youtube: '',
    
    // Personal Information (continued)
    executive_summary: '',
    career_timeline: [],
    
    // Education
    education_college: '',
    education_college_year_graduated: '',
    education_high_school: '',
    education_high_school_year_graduated: '',
    
    // Personal Details
    honorable_achievements: [], // Fixed typo from honorable_achievments
    hobbies: [],
    age: '',
    
    // Contact Location (updated field names to match db schema)
    contact_address: '', // Changed from address
    contact_city: '', // Changed from city
    contact_state: '', // Changed from state
    contact_zip_code: '', // Changed from zipcode
    contact_country: '', // Changed from country
    contact_type: '',
    relationship_owner: '',
    role_in_decision_making: '',
    last_contact_date: '',
    source_of_introduction: '',
    accredited_investor_status: false,
    kyc_status: '',
    
    // Company Information (for selection)
    company_website: preSelectedCompany?.company_website || '',
    industry: preSelectedCompany?.industry || '',
    company_address: preSelectedCompany?.company_address || '',
    company_city: preSelectedCompany?.company_city || '',
    company_state: preSelectedCompany?.company_state || '',
    company_country: preSelectedCompany?.company_country || '',
    company_zip: '',
    
    // Legacy field
    capital_type: ''
  });

  const [investmentCriteria, setInvestmentCriteria] = useState<InvestmentCriteria[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [duplicateContacts, setDuplicateContacts] = useState<ExistingContact[]>([]);
  const [showDuplicateDialog, setShowDuplicateDialog] = useState(false);
  const [queuedContact, setQueuedContact] = useState<QueuedContact | null>(null);
  
  // Enhanced UI state
  const [isSearching, setIsSearching] = useState(false);
  const [companySuggestions, setCompanySuggestions] = useState<CompanySuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<CompanySuggestion | null>(null);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [showInvestmentCriteria, setShowInvestmentCriteria] = useState(false);
  const [isLoadingCompanyData, setIsLoadingCompanyData] = useState(false);
  
  // Uniqueness validation state
  const [fieldValidationStates, setFieldValidationStates] = useState<FieldValidationStates>({
    email: { isValidating: false, isDuplicate: false },
    additional_email: { isValidating: false, isDuplicate: false },
    linkedin_url: { isValidating: false, isDuplicate: false },
    full_name: { isValidating: false, isDuplicate: false }
  });
  
  // State for managing email search suggestions
  const [emailSuggestions, setEmailSuggestions] = useState<ExistingContact[]>([]);
  const [showEmailSuggestions, setShowEmailSuggestions] = useState(false);
  const [selectedEmailSuggestionIndex, setSelectedEmailSuggestionIndex] = useState(-1);
  const [isSearchingEmail, setIsSearchingEmail] = useState(false);
  const [activeEmailField, setActiveEmailField] = useState<'email' | 'additional_email' | 'name_search' | null>(null);
  
  // State for filter options
  const [filterOptions, setFilterOptions] = useState<{
    jobTiers: string[];
    contactTypes: string[];
    decisionMakingRoles: string[];
  }>({
    jobTiers: [],
    contactTypes: [],
    decisionMakingRoles: []
  });

  // Fetch filter options on component mount
  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        const [jobTiersRes, contactTypesRes, decisionRolesRes] = await Promise.all([
          fetch('/api/contacts/filter-options?type=job_tiers'),
          fetch('/api/contacts/filter-options?type=contact_types'),
          fetch('/api/contacts/filter-options?type=decision_making_roles')
        ]);

        const jobTiersData = await jobTiersRes.json();
        const contactTypesData = await contactTypesRes.json();
        const decisionRolesData = await decisionRolesRes.json();

        setFilterOptions({
          jobTiers: jobTiersData.success ? jobTiersData.data.map((item: any) => item.value) : [],
          contactTypes: contactTypesData.success ? contactTypesData.data.map((item: any) => item.value) : [],
          decisionMakingRoles: decisionRolesData.success ? decisionRolesData.data.map((item: any) => item.value) : []
        });
      } catch (error) {
        console.error('Error fetching filter options:', error);
      }
    };

    fetchFilterOptions();
  }, []);
  
  const companyInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const emailSuggestionsRef = useRef<HTMLDivElement>(null);

  // Fetch full company data when companyId is provided
  const fetchCompanyData = async (companyId: string) => {
    setIsLoadingCompanyData(true);
    try {
      console.log('Fetching company data for ID:', companyId);
      const response = await fetch(`/api/companies/${companyId}`);
      if (response.ok) {
        const companyData = await response.json();
        console.log('Company data received:', companyData);
        
        // Transform the company data to match our CompanySuggestion interface
        const transformedCompany: CompanySuggestion = {
          company_id: companyData.company_id,
          company_name: companyData.company_name || '',
          company_website: companyData.company_website || '',
          industry: companyData.industry || '',
          company_address: companyData.company_address || '',
          company_city: companyData.company_city || '',
          company_state: companyData.company_state || '',
          company_country: companyData.company_country || '',
          company_zip: companyData.company_zip || '',
          extracted_data: companyData.scraped_data ? {
            companytype: companyData.scraped_data.companytype || undefined,
            businessmodel: companyData.scraped_data.businessmodel || undefined,
            fundsize: companyData.scraped_data.fundsize || companyData.scraped_data.aum || undefined,
            aum: companyData.scraped_data.aum || undefined,
            headquarters: companyData.scraped_data.headquarters || undefined,
            foundedyear: companyData.scraped_data.foundedyear || companyData.founded_year || undefined,
            numberofemployees: companyData.scraped_data.numberofemployees || undefined,
            investmentfocus: companyData.scraped_data.investmentfocus || undefined,
            geographicfocus: companyData.scraped_data.geographicfocus || undefined,
            dealsize: companyData.scraped_data.dealsize || undefined,
            minimumdealsize: companyData.scraped_data.minimumdealsize || undefined,
            maximumdealsize: companyData.scraped_data.maximumdealsize || undefined,
            investment_criteria_property_types: companyData.scraped_data.investment_criteria_property_types || companyData.scraped_data.propertytypes || undefined,
            investment_criteria_asset_types: companyData.scraped_data.investment_criteria_asset_types || companyData.scraped_data.assetclasses || undefined,
            investment_criteria_loan_types: companyData.scraped_data.investment_criteria_loan_types || undefined,
            investment_criteria_property_subcategories: companyData.scraped_data.investment_criteria_property_subcategories || undefined,
            riskprofile: companyData.scraped_data.riskprofile || undefined,
            targetmarkets: companyData.scraped_data.targetmarkets || undefined,
            strategies: companyData.scraped_data.strategies || undefined,
            propertytypes: companyData.scraped_data.propertytypes || undefined,
            assetclasses: companyData.scraped_data.assetclasses || undefined,
            valuecreation: companyData.scraped_data.valuecreation || undefined,
            holdperiod: companyData.scraped_data.holdperiod || undefined,
            targetreturn: companyData.scraped_data.targetreturn || undefined,
            approach: companyData.scraped_data.approach || undefined,
          } : undefined
        };

        console.log('Transformed company:', transformedCompany);
        return transformedCompany;
      } else {
        console.error('Failed to fetch company data:', response.statusText);
        return null;
      }
    } catch (error) {
      console.error('Error fetching company data:', error);
      toast.error('Failed to load company data');
      return null;
    } finally {
      setIsLoadingCompanyData(false);
    }
  };

  // Auto-fill from preSelectedCompany extracted data or fetch from API
  useEffect(() => {
    const loadCompanyData = async () => {
      let companyToUse: CompanySuggestion | null = null;

      // If we have a companyId but no preSelectedCompany, fetch the data
      if (companyId && !preSelectedCompany) {
        companyToUse = await fetchCompanyData(companyId);
      } else if (preSelectedCompany) {
        companyToUse = preSelectedCompany as CompanySuggestion;
      }

      if (companyToUse) {
        // Set the selected company state
        setSelectedCompany(companyToUse);
        
        const extractedData = companyToUse.extracted_data;
        
        // Auto-populate form data with company information
        setFormData(prev => ({
          ...prev,
          company_name: companyToUse.company_name,
          company_website: companyToUse.company_website || '',
          industry: companyToUse.industry || extractedData?.companytype || '',
          company_address: companyToUse.company_address || extractedData?.headquarters || '',
          company_city: companyToUse.company_city || '',
          company_state: companyToUse.company_state || '',
          company_country: companyToUse.company_country || '',
          company_zip: companyToUse.company_zip || '',
          // Set default capital type based on company data
          capital_type: extractedData ? determineCapitalType(extractedData.companytype || '') : ''
        }));
        
        // Create initial investment criteria based on extracted data
        if (extractedData) {
          const initialCriteria: InvestmentCriteria = {
            id: '1',
            capital_type: determineCapitalType(extractedData.companytype || ''),
            investment_criteria_country: [...(extractedData.geographicfocus || []), ...(extractedData.targetmarkets || [])].join(', '),
            investment_criteria_state: '',
            investment_criteria_city: '',
            investment_criteria_property_type: (extractedData.investment_criteria_property_types || extractedData.propertytypes || []).join(', '),
            investment_criteria_asset_type: (extractedData.investment_criteria_asset_types || extractedData.assetclasses || []).join(', '),
            investment_criteria_deal_size: extractedData.dealsize || extractedData.minimumdealsize || extractedData.aum || '',
            investment_criteria_loan_type: (extractedData.investment_criteria_loan_types || []).join(', '),
            loan_term_months: '',
            interest_rate_index: '',
            origination_fee_percent: '',
            exit_fee_percent: '',
            recourse: '',
            closing_time_weeks: '',
            hold_period_years: extractedData.holdperiod || '',
            expected_irr_percent: extractedData.targetreturn || '',
            expected_equity_multiple: ''
          };

          setInvestmentCriteria([initialCriteria]);
          // Auto-show investment criteria when coming from company section
          setShowInvestmentCriteria(true);
          
          toast.success(`Auto-populated company data and investment criteria from ${companyToUse.company_name}`);
        }
      }
    };

    loadCompanyData();
  }, [companyId, preSelectedCompany]);

  // Debounced uniqueness validation functions
  const debouncedValidateEmail = useCallback(
    debounce(async (email: string, field: 'email' | 'additional_email') => {
      if (!email || email.length < 3) return;
      
      setFieldValidationStates(prev => ({
        ...prev,
        [field]: { ...prev[field], isValidating: true, isDuplicate: false }
      }));

      try {
        const response = await fetch(`/api/contacts/search-by-email?email=${encodeURIComponent(email)}`);
        if (response.ok) {
          const result = await response.json();
          const isDuplicate = result?.contact || (result?.contacts && result.contacts.length > 0);
          const duplicateMessage = isDuplicate ? `Email already exists for ${result?.contact?.first_name || 'another contact'}` : undefined;
          
          setFieldValidationStates(prev => ({
            ...prev,
            [field]: { isValidating: false, isDuplicate, duplicateMessage }
          }));
        }
      } catch (error) {
        console.error('Email validation error:', error);
        setFieldValidationStates(prev => ({
          ...prev,
          [field]: { ...prev[field], isValidating: false }
        }));
      }
    }, 500),
    []
  );

  const debouncedValidateLinkedIn = useCallback(
    debounce(async (linkedin: string) => {
      if (!linkedin || linkedin.length < 10) return;
      
      setFieldValidationStates(prev => ({
        ...prev,
        linkedin_url: { ...prev.linkedin_url, isValidating: true, isDuplicate: false }
      }));

      try {
        const response = await fetch(`/api/contacts?linkedin_url=${encodeURIComponent(linkedin)}&limit=1`);
        if (response.ok) {
          const result = await response.json();
          const isDuplicate = result?.contacts && result.contacts.length > 0;
          const duplicateMessage = isDuplicate ? `LinkedIn URL already exists for ${result.contacts[0]?.first_name || 'another contact'}` : undefined;
          
          setFieldValidationStates(prev => ({
            ...prev,
            linkedin_url: { isValidating: false, isDuplicate, duplicateMessage }
          }));
        }
      } catch (error) {
        console.error('LinkedIn validation error:', error);
        setFieldValidationStates(prev => ({
          ...prev,
          linkedin_url: { ...prev.linkedin_url, isValidating: false }
        }));
      }
    }, 500),
    []
  );

  const debouncedValidateFullName = useCallback(
    debounce(async (firstName: string, lastName: string) => {
      if (!firstName || !lastName || (firstName.length + lastName.length < 4)) return;
      
      // Only validate if we have a selected company
      if (!selectedCompany?.company_id && !companyId) return;
      
      setFieldValidationStates(prev => ({
        ...prev,
        full_name: { ...prev.full_name, isValidating: true, isDuplicate: false }
      }));

      try {
        const companyIdToUse = companyId ? parseInt(companyId) : selectedCompany?.company_id;
        const fullName = `${firstName} ${lastName}`.trim();
        const response = await fetch(`/api/contacts?search=${encodeURIComponent(fullName)}&company_id=${companyIdToUse}&limit=1`);
        
        if (response.ok) {
          const result = await response.json();
          const isDuplicate = result?.contacts && result.contacts.length > 0;
          const duplicateMessage = isDuplicate ? `Contact already exists in this company` : undefined;
          
          setFieldValidationStates(prev => ({
            ...prev,
            full_name: { isValidating: false, isDuplicate, duplicateMessage }
          }));
        }
      } catch (error) {
        console.error('Name validation error:', error);
        setFieldValidationStates(prev => ({
          ...prev,
          full_name: { ...prev.full_name, isValidating: false }
        }));
      }
    }, 700),
    [selectedCompany, companyId]
  );

  // Debounced company search function
  const debouncedSearchCompanies = useCallback(
    debounce(async (searchTerm: string) => {
      console.log('Debounced search called with:', searchTerm, 'Length:', searchTerm.length);
      
      if (searchTerm.length < 2) {
        console.log('Search term too short, clearing suggestions');
        setCompanySuggestions([]);
        setShowSuggestions(false);
        setIsSearching(false);
        return;
      }

      setIsSearching(true);
      try {
        console.log('Making API call for:', searchTerm);
        const url = `/api/companies/search?q=${encodeURIComponent(searchTerm)}`;
        console.log('Full URL:', url);
        
        const response = await fetch(url);
        console.log('Response status:', response.status, response.ok);
        
        if (response.ok) {
          const suggestions = await response.json();
          console.log('Search results received - count:', suggestions.length);
          console.log('First few results:', suggestions.slice(0, 3));
          
          // Transform the search results to ensure extracted_data is properly structured
          const transformedSuggestions = suggestions.map((suggestion: any) => ({
            ...suggestion,
            // The API returns extracted_data as a JSON object already
            extracted_data: suggestion.extracted_data
          }));
          
          console.log('Setting suggestions:', transformedSuggestions.length);
          setCompanySuggestions(transformedSuggestions);
          setShowSuggestions(transformedSuggestions.length > 0);
          console.log('Show suggestions set to:', transformedSuggestions.length > 0);
        } else {
          console.error('API response not ok:', response.status, response.statusText);
          toast.error(`Search failed: ${response.status} ${response.statusText}`);
          setCompanySuggestions([]);
          setShowSuggestions(false);
        }
      } catch (error) {
        console.error('Error searching companies:', error);
        toast.error('Failed to search companies. Please check your connection and try again.');
        setCompanySuggestions([]);
        setShowSuggestions(false);
      } finally {
        setIsSearching(false);
      }
    }, 300),
    []
  );

  // Debounced name search function
  const debouncedSearchByName = useCallback(
    debounce(async (firstName: string, lastName: string) => {
      console.log('Name search called with:', firstName, lastName);
      
      if (!firstName || !lastName || (firstName.length + lastName.length < 4)) {
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
        setIsSearchingEmail(false);
        return;
      }

      setIsSearchingEmail(true);
      setActiveEmailField('name_search');
      
      try {
        const fullName = `${firstName} ${lastName}`;
        const response = await fetch(`/api/contacts?search=${encodeURIComponent(fullName)}&limit=5`);
        
        if (response.ok) {
          const data = await response.json();
          console.log('Name search results:', data);
          setEmailSuggestions(data.contacts || []);
          setShowEmailSuggestions(data.contacts && data.contacts.length > 0);
        } else {
          setEmailSuggestions([]);
          setShowEmailSuggestions(false);
        }
      } catch (error) {
        console.error('Error during name search:', error);
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
      } finally {
        setIsSearchingEmail(false);
      }
    }, 500),
    []
  );

  // Debounced email search function
  const debouncedSearchEmails = useCallback(
    debounce(async (email: string, fieldName: 'email' | 'additional_email') => {
      console.log('Email search called with:', email, 'Field:', fieldName);
      
      if (email.length < 3) {
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
        setIsSearchingEmail(false);
        return;
      }

      setIsSearchingEmail(true);
      setActiveEmailField(fieldName);
      
      try {
        const response = await fetch(`/api/contacts/search-by-email?email=${encodeURIComponent(email)}`);
        
        if (response.ok) {
          const data = await response.json();
          console.log('Email search results:', data);
          setEmailSuggestions(data.contacts || []);
          setShowEmailSuggestions(data.contacts && data.contacts.length > 0);
        } else {
          setEmailSuggestions([]);
          setShowEmailSuggestions(false);
        }
      } catch (error) {
        console.error('Error during email search:', error);
        setEmailSuggestions([]);
        setShowEmailSuggestions(false);
      } finally {
        setIsSearchingEmail(false);
      }
    }, 500),
    []
  );

  // Click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Handle company suggestions
      if (companyInputRef.current && !companyInputRef.current.contains(event.target as Node)) {
        const searchResults = document.querySelector('[data-search-results]');
        if (searchResults && searchResults.contains(event.target as Node)) {
          return;
        }
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
      }
      
      // Handle email suggestions
      if (emailSuggestionsRef.current && !emailSuggestionsRef.current.contains(event.target as Node)) {
        const emailSearchResults = document.querySelector('[data-email-search-results]');
        if (emailSearchResults && emailSearchResults.contains(event.target as Node)) {
          return;
        }
        setShowEmailSuggestions(false);
        setSelectedEmailSuggestionIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    const oldValue = formData[name as keyof ContactFormData] as string;
    
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Email tracing for email fields
    if (name === 'email' || name === 'additional_email') {
      traceEmailChanges(name, oldValue, value);
    }

    // Clear validation error for the field
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Clear field validation state when user starts typing
    if (fieldValidationStates[name]) {
      setFieldValidationStates(prev => ({
        ...prev,
        [name]: { ...prev[name], isDuplicate: false, duplicateMessage: undefined }
      }));
    }

    // Trigger uniqueness validations and email search
    if (name === 'email' || name === 'additional_email') {
      debouncedValidateEmail(value, name as 'email' | 'additional_email');
      // Also trigger email search to show existing contacts
      if (value.trim().length >= 3) {
        debouncedSearchEmails(value, name as 'email' | 'additional_email');
      } else {
        setShowEmailSuggestions(false);
        setEmailSuggestions([]);
      }
    } else if (name === 'linkedin_url') {
      debouncedValidateLinkedIn(value);
    } else if (name === 'first_name' || name === 'last_name') {
      // Trigger name validation when both names are available
      const otherName = name === 'first_name' ? formData.last_name : formData.first_name;
      const firstName = name === 'first_name' ? value : formData.first_name;
      const lastName = name === 'last_name' ? value : formData.last_name;
      
      if (firstName && lastName) {
        debouncedValidateFullName(firstName, lastName);
        // Also trigger name search to show existing contacts
        debouncedSearchByName(firstName, lastName);
      }
    }

    // Trigger company search when company_name changes
    if (name === 'company_name') {
      console.log('Company name changed:', value);
      
      // Clear selected company if user is typing something different
      if (selectedCompany && value !== selectedCompany.company_name) {
        console.log('Clearing selected company - user typing different name');
        setSelectedCompany(null);
      }
      
      setSelectedSuggestionIndex(-1);
      if (value.trim() && value.trim().length >= 2) {
        console.log('Triggering search for:', value.trim());
        debouncedSearchCompanies(value);
      } else if (value.trim().length < 2) {
        console.log('Search term too short, clearing suggestions');
        setShowSuggestions(false);
        setCompanySuggestions([]);
      }
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const addInvestmentCriteria = () => {
    const newCriteria: InvestmentCriteria = {
      id: Date.now().toString(),
      capital_type: '',
      investment_criteria_country: '',
      investment_criteria_state: '',
      investment_criteria_city: '',
      investment_criteria_property_type: '',
      investment_criteria_asset_type: '',
      investment_criteria_deal_size: '',
      investment_criteria_loan_type: '',
      loan_term_months: '',
      interest_rate_index: '',
      origination_fee_percent: '',
      exit_fee_percent: '',
      recourse: '',
      closing_time_weeks: '',
      hold_period_years: '',
      expected_irr_percent: '',
      expected_equity_multiple: ''
    };
    setInvestmentCriteria(prev => [...prev, newCriteria]);
  };

  const removeInvestmentCriteria = (id: string) => {
    setInvestmentCriteria(prev => prev.filter(criteria => criteria.id !== id));
  };

  const updateInvestmentCriteria = (id: string, field: keyof InvestmentCriteria, value: string) => {
    setInvestmentCriteria(prev => prev.map(criteria =>
      criteria.id === id ? { ...criteria, [field]: value } : criteria
    ));
  };

  const checkForDuplicates = async (): Promise<ExistingContact[]> => {
    try {
              console.log('🔍 Checking for duplicates with form data:', {
        email: formData.email,
        additional_email: formData.additional_email,
        linkedin_url: formData.linkedin_url,
        first_name: formData.first_name,
        last_name: formData.last_name,
        companyId,
        selectedCompanyId: selectedCompany?.company_id
      });
      
      const checkPromises: Promise<any>[] = [];
      
      // Check by email (exact match)
      if (formData.email) {
        console.log('📧 Checking email:', formData.email);
        checkPromises.push(
          fetch(`/api/contacts/search-by-email?email=${encodeURIComponent(formData.email)}`)
            .then(res => res.ok ? res.json() : null)
        );
      }
      
      // Check by additional email (exact match)
      if (formData.additional_email) {
        checkPromises.push(
          fetch(`/api/contacts/search-by-email?email=${encodeURIComponent(formData.additional_email)}`)
            .then(res => res.ok ? res.json() : null)
        );
      }

      // Check by LinkedIn URL (exact match)
      if (formData.linkedin_url) {
        checkPromises.push(
          fetch(`/api/contacts?linkedin_url=${encodeURIComponent(formData.linkedin_url)}&limit=5`)
            .then(res => res.ok ? res.json() : null)
        );
      }

      // Check by name + company (exact match)
      if (formData.first_name && formData.last_name && (companyId || selectedCompany?.company_id)) {
        const companyIdToUse = companyId ? parseInt(companyId) : selectedCompany?.company_id;
        const fullName = `${formData.first_name} ${formData.last_name}`.trim();
        checkPromises.push(
          fetch(`/api/contacts?search=${encodeURIComponent(fullName)}&company_id=${companyIdToUse}&limit=5`)
            .then(res => res.ok ? res.json() : null)
        );
      }

      if (checkPromises.length === 0) {
        return [];
      }

      const results = await Promise.all(checkPromises);
      console.log('📋 API Results:', results);
      
      const duplicates: ExistingContact[] = [];
      
      results.forEach((result, index) => {
        console.log(`📄 Result ${index}:`, result);
        if (result?.contacts) {
          duplicates.push(...result.contacts);
        } else if (result?.contact) {
          duplicates.push(result.contact);
        } else if (Array.isArray(result)) {
          duplicates.push(...result);
        }
      });

      // Remove duplicates by contact_id
      const uniqueDuplicates = duplicates.filter((contact, index, self) =>
        index === self.findIndex(c => c.contact_id === contact.contact_id)
      );

      console.log('🔍 All duplicates found:', uniqueDuplicates);
      
      // Only return exact matches - filter out false positives
      const exactMatches = uniqueDuplicates.filter(contact => {
        console.log('🔍 Checking contact:', contact);
        
        // Email match
        if (formData.email && contact.email && 
            contact.email.toLowerCase() === formData.email.toLowerCase()) {
          console.log('✅ Email match found:', contact.email);
          return true;
        }
        
        // Additional email match (checking personal_email from API)
        if (formData.additional_email && contact.personal_email && 
            contact.personal_email.trim() !== '' &&
            contact.personal_email.toLowerCase() === formData.additional_email.toLowerCase()) {
          console.log('✅ Additional email match found:', contact.personal_email);
          return true;
        }
        
        // LinkedIn URL match (checking linkedin_url from API)
        if (formData.linkedin_url && contact.linkedin_url && 
            contact.linkedin_url.toLowerCase() === formData.linkedin_url.toLowerCase()) {
          console.log('✅ LinkedIn URL match found:', contact.linkedin_url);
          return true;
        }
        
        // Name + company match (only if we have both name and company)
        if (formData.first_name && formData.last_name && contact.first_name && contact.last_name) {
          const formFullName = `${formData.first_name} ${formData.last_name}`.toLowerCase().trim();
          const contactFullName = `${contact.first_name} ${contact.last_name}`.toLowerCase().trim();
          
          if (formFullName === contactFullName && (companyId || selectedCompany?.company_id)) {
            console.log('✅ Name + company match found:', contactFullName);
            return true;
          }
        }
        
        console.log('❌ No match found for contact');
        return false;
      });

      console.log('🎯 Final exact matches:', exactMatches);
      return exactMatches;
    } catch (error) {
      console.error('Error checking for duplicates:', error);
      return [];
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Check for validation errors before submitting
    const hasValidationErrors = Object.values(fieldValidationStates).some(state => state.isDuplicate);
    if (hasValidationErrors) {
      toast.error('Please resolve validation errors before submitting');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Check for duplicates first
      const duplicates = await checkForDuplicates();
      
      if (duplicates.length > 0) {
        setDuplicateContacts(duplicates);
        setQueuedContact({
          id: Date.now().toString(),
          formData,
          investmentCriteria,
          status: 'pending'
        });
        setShowDuplicateDialog(true);
        setIsSubmitting(false);
        return;
      }

      await saveContact();
    } catch (error) {
      console.error('Error in handleSubmit:', error);
      toast.error('Failed to add contact');
      setIsSubmitting(false);
    }
  };

  const saveContact = async () => {
    try {
      console.log('Saving contact with data:', {
        formData,
        investmentCriteria,
        companyId: companyId ? parseInt(companyId) : selectedCompany?.company_id
      });
      
      const contactData = {
        ...contactFormToDb(formData),
        company_id: companyId ? parseInt(companyId) : selectedCompany?.company_id,
        investmentCriteria: investmentCriteria
      };

      const response = await fetch('/api/contacts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contactData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API Error:', errorData);
        throw new Error(errorData.error || 'Failed to add contact');
      }
      
      const data = await response.json();
      console.log('Contact saved successfully:', data);
      toast.success('Contact added successfully', {
        description: `${formData.first_name} ${formData.last_name} has been added to your contacts`,
        duration: 4000,
      });
      onBack();
    } catch (error) {
      console.error('Error saving contact:', error);
      toast.error('Failed to add contact', {
        description: 'Please check your information and try again',
        duration: 5000,
      });
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleProceedWithDuplicate = async () => {
    setShowDuplicateDialog(false);
    setIsSubmitting(true);
    try {
      await saveContact();
    } catch (error) {
      // Error handling is done in saveContact
    }
  };

  const handleCancelDuplicate = () => {
    setShowDuplicateDialog(false);
    setQueuedContact(null);
    setDuplicateContacts([]);
  };

  // Handle company selection
  const handleCompanySelect = (company: CompanySuggestion) => {
    setSelectedCompany(company);
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);
    
    const extracted = company.extracted_data;
    
    // Auto-populate company-related fields
    const newFormData = {
      ...formData,
      company_name: company.company_name,
      company_website: company.company_website || '',
      industry: company.industry || (extracted?.companytype || ''),
      company_address: company.company_address || (extracted?.headquarters || ''),
      company_city: company.company_city || '',
      company_state: company.company_state || '',
      company_country: company.company_country || '',
      company_zip: company.company_zip || '',
      // Auto-set capital type from company data - removed as requested
      // capital_type: formData.capital_type || determineCapitalType(extracted?.companytype || '')
    };

    // Auto-populate investment criteria from extracted data if available
    if (extracted && investmentCriteria.length === 0) {
      const initialCriteria: InvestmentCriteria = {
        id: '1',
        capital_type: formData.capital_type || determineCapitalType(extracted.companytype || ''),
        investment_criteria_country: [...(extracted.geographicfocus || []), ...(extracted.targetmarkets || [])].join(', '),
        investment_criteria_state: '',
        investment_criteria_city: '',
        investment_criteria_property_type: (extracted.investment_criteria_property_types || extracted.propertytypes || []).join(', '),
        investment_criteria_asset_type: (extracted.investment_criteria_asset_types || extracted.assetclasses || []).join(', '),
        investment_criteria_deal_size: extracted.dealsize || extracted.minimumdealsize || extracted.aum || '',
        investment_criteria_loan_type: (extracted.investment_criteria_loan_types || []).join(', '),
        loan_term_months: '',
        interest_rate_index: '',
        origination_fee_percent: '',
        exit_fee_percent: '',
        recourse: '',
        closing_time_weeks: '',
        hold_period_years: extracted.holdperiod || '',
        expected_irr_percent: extracted.targetreturn || '',
        expected_equity_multiple: ''
      };

      setInvestmentCriteria([initialCriteria]);
      // Auto-show investment criteria when company is selected
      setShowInvestmentCriteria(true);
    }

    setFormData(newFormData);
    toast.success('Company selected successfully', {
      description: `Auto-populated fields from ${company.company_name}`,
      duration: 3000,
    });
  };

  // Handle email contact selection
  const handleEmailContactSelect = (contact: ExistingContact) => {
    // Show duplicate warning and ask user what they want to do
    const message = `A contact with email "${contact.email}" already exists:\n\n` +
      `Name: ${contact.first_name} ${contact.last_name}\n` +
      `Company: ${contact.company_name || 'N/A'}\n` +
      `Title: ${contact.title || 'N/A'}\n\n` +
      `Would you like to:\n` +
      `- Edit the existing contact instead\n` +
      `- Continue creating a new contact`;

    if (confirm(message)) {
      // User wants to edit existing contact - redirect to contact detail page
      window.open(`/dashboard/people/${contact.contact_id}`, '_blank');
    }
    
    // Clear email search suggestions
    setShowEmailSuggestions(false);
    setEmailSuggestions([]);
    setSelectedEmailSuggestionIndex(-1);
  };

  // Email tracing function
  const traceEmailChanges = (fieldName: string, oldValue: string, newValue: string) => {
    if (oldValue !== newValue && newValue) {
      console.log(`📧 Email ${fieldName} changed:`, {
        from: oldValue,
        to: newValue,
        timestamp: new Date().toISOString(),
        contactName: `${formData.first_name} ${formData.last_name}`.trim(),
        company: formData.company_name
      });
      
      // You can add additional logging or analytics here
      // For example, send to analytics service, log to database, etc.
    }
  };

  // Keyboard navigation for suggestions
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || companySuggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex((prev) => 
          prev < companySuggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex((prev) => 
          prev > 0 ? prev - 1 : companySuggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0) {
          handleCompanySelect(companySuggestions[selectedSuggestionIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
        break;
    }
  };

  // Keyboard navigation for email suggestions
  const handleEmailKeyDown = (e: React.KeyboardEvent) => {
    if (!showEmailSuggestions || emailSuggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedEmailSuggestionIndex((prev) => 
          prev < emailSuggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedEmailSuggestionIndex((prev) => 
          prev > 0 ? prev - 1 : emailSuggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedEmailSuggestionIndex >= 0) {
          handleEmailContactSelect(emailSuggestions[selectedEmailSuggestionIndex]);
        }
        break;
      case 'Escape':
        setShowEmailSuggestions(false);
        setSelectedEmailSuggestionIndex(-1);
        break;
    }
  };

  // Helper functions
  const determineCapitalType = (companyType: string): string => {
    const type = companyType.toLowerCase();
    if (type.includes('debt') || type.includes('lending')) return 'Debt';
    if (type.includes('equity') || type.includes('investment')) return 'Equity';
    if (type.includes('mezzanine')) return 'Mezzanine';
    return '';
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="max-w-7xl mx-auto p-6 space-y-8">
        {/* Header Section */}
        <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                onClick={onBack} 
                className="hover:bg-slate-100 rounded-xl transition-all duration-200"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                  Add New Contact
                </h1>
                <p className="text-slate-600 mt-1">
                  {companyId ? `Create contact for ${selectedCompany?.company_name || 'selected company'}` : 'Create a comprehensive contact profile with company insights'}
                  {isLoadingCompanyData && (
                    <span className="ml-2 inline-flex items-center">
                      <Loader2 className="h-4 w-4 animate-spin mr-1" />
                      Loading company data...
                    </span>
                  )}
                </p>
              </div>
            </div>
            
            {/* Top Action Buttons */}
            <div className="flex items-center space-x-3">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onBack}
                className="h-11 px-6 rounded-xl border-2 border-slate-200 hover:bg-slate-50 transition-all duration-200 font-medium"
                disabled={isLoadingCompanyData}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                onClick={handleSubmit}
                disabled={isSubmitting || isLoadingCompanyData}
                className="h-11 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 min-w-[140px] disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Saving...
                  </>
                ) : isLoadingCompanyData ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Save Contact
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      
      <form onSubmit={handleSubmit}>
        <div className="grid gap-8 transition-all duration-300 grid-cols-1 lg:grid-cols-2">
          {/* Left Column: Contact Information */}
          <div className="space-y-6">
            
            {/* Merged Personal Information, Contact Location, and Categories Card */}
            <Card className="bg-white shadow-xl border-0 rounded-2xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
                <CardTitle className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-xl mr-3">
                    <Users className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-slate-900">Contact Information</div>
                    <div className="text-sm text-slate-600">(Required fields marked with *)</div>
                  </div>
                </CardTitle>
                             </CardHeader>
              <CardContent className="p-6 space-y-6 bg-gradient-to-b from-white to-slate-50">
                {/* Personal Information Section */}
                <div className="space-y-4">
                  <h3 className="text-md font-medium text-slate-700 border-b pb-2">Personal Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="first_name" className="text-sm font-medium text-slate-700">
                        First Name <span className="text-red-500">*</span>
                      </Label>
                      <div className="relative">
                        <Input 
                          id="first_name" 
                          name="first_name" 
                          value={formData.first_name} 
                          onChange={handleChange}
                          className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 hover:border-slate-300 ${
                            fieldValidationStates.full_name.isDuplicate 
                              ? 'border-red-300 focus:border-red-500' 
                              : 'border-slate-200 focus:border-blue-500'
                          }`}
                          placeholder="Enter first name"
                          required
                        />
                        {(selectedCompany || companyId) && (
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            {fieldValidationStates.full_name.isValidating ? (
                              <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                            ) : fieldValidationStates.full_name.isDuplicate ? (
                              <X className="h-4 w-4 text-red-600" />
                            ) : formData.first_name && formData.last_name && !fieldValidationStates.full_name.isValidating ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : null}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="last_name" className="text-sm font-medium text-slate-700">
                        Last Name <span className="text-red-500">*</span>
                      </Label>
                      <div className="relative">
                        <Input 
                          id="last_name" 
                          name="last_name" 
                          value={formData.last_name} 
                          onChange={handleChange}
                          className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 hover:border-slate-300 ${
                            fieldValidationStates.full_name.isDuplicate 
                              ? 'border-red-300 focus:border-red-500' 
                              : 'border-slate-200 focus:border-blue-500'
                          }`}
                          placeholder="Enter last name"
                          required
                        />
                        {(selectedCompany || companyId) && (
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            {fieldValidationStates.full_name.isValidating ? (
                              <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                            ) : fieldValidationStates.full_name.isDuplicate ? (
                              <X className="h-4 w-4 text-red-600" />
                            ) : formData.first_name && formData.last_name && !fieldValidationStates.full_name.isValidating ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : null}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  {fieldValidationStates.full_name.isDuplicate && fieldValidationStates.full_name.duplicateMessage && (
                    <div className="col-span-2">
                      <p className="text-red-600 text-xs">{fieldValidationStates.full_name.duplicateMessage}</p>
                    </div>
                  )}
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="title" className="text-sm font-medium text-slate-700">Job Title</Label>
                      <Input 
                        id="title" 
                        name="title" 
                        value={formData.title} 
                        onChange={handleChange}
                        className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                        placeholder="e.g., Managing Director (Optional)"
                      />
                    </div>
                    <div className="space-y-2">
                      <FilterFieldWithAdd
                        label="Job Tier"
                        value={formData.job_tier}
                        options={filterOptions.jobTiers}
                        onChange={(value) => handleSelectChange('job_tier', value)}
                        placeholder="Select job tier or add new..."
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-sm font-medium text-slate-700">Primary Email</Label>
                      <div className="relative">
                        <Input 
                          id="email" 
                          name="email" 
                          type="email" 
                          value={formData.email} 
                          onChange={handleChange}
                          onKeyDown={handleEmailKeyDown}
                          className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 hover:border-slate-300 ${
                            fieldValidationStates.email.isDuplicate 
                              ? 'border-red-300 focus:border-red-500' 
                              : 'border-slate-200 focus:border-blue-500'
                          }`}
                          placeholder="<EMAIL> (Optional)"
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          {fieldValidationStates.email.isValidating ? (
                            <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                          ) : fieldValidationStates.email.isDuplicate ? (
                            <X className="h-4 w-4 text-red-600" />
                          ) : formData.email && !fieldValidationStates.email.isValidating ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : null}
                        </div>
                      </div>
                      {fieldValidationStates.email.isDuplicate && fieldValidationStates.email.duplicateMessage && (
                        <p className="text-red-600 text-xs">{fieldValidationStates.email.duplicateMessage}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="additional_email" className="text-sm font-medium text-slate-700">Additional Email</Label>
                      <div className="relative">
                        <Input 
                          id="additional_email" 
                          name="additional_email" 
                          type="email" 
                          value={formData.additional_email} 
                          onChange={handleChange}
                          onKeyDown={handleEmailKeyDown}
                          className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 hover:border-slate-300 ${
                            fieldValidationStates.additional_email.isDuplicate 
                              ? 'border-red-300 focus:border-red-500' 
                              : 'border-slate-200 focus:border-blue-500'
                          }`}
                          placeholder="<EMAIL> (Optional)"
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          {fieldValidationStates.additional_email.isValidating ? (
                            <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                          ) : fieldValidationStates.additional_email.isDuplicate ? (
                            <X className="h-4 w-4 text-red-600" />
                          ) : formData.additional_email && !fieldValidationStates.additional_email.isValidating ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : null}
                        </div>
                      </div>
                      {fieldValidationStates.additional_email.isDuplicate && fieldValidationStates.additional_email.duplicateMessage && (
                        <p className="text-red-600 text-xs">{fieldValidationStates.additional_email.duplicateMessage}</p>
                      )}
                    </div>
                  </div>

                  {/* Email/Name Search Results */}
                  {showEmailSuggestions && (
                    <div className="space-y-2" ref={emailSuggestionsRef}>
                      <Label className="text-sm font-medium text-slate-700">
                        Existing Contacts Found ({emailSuggestions.length})
                        {activeEmailField && (
                          <span className="text-xs text-slate-500 ml-2">
                            {activeEmailField === 'email' ? '(for primary email)' : 
                             activeEmailField === 'additional_email' ? '(for additional email)' : 
                             '(by name search)'}
                          </span>
                        )}
                      </Label>
                      <div className="space-y-2 max-h-60 overflow-y-auto border rounded-xl p-2 bg-orange-50" data-email-search-results>
                        {emailSuggestions.map((contact, index) => (
                          <div
                            key={contact.contact_id}
                            className={`p-3 rounded-lg cursor-pointer border transition-all duration-200 ${
                              index === selectedEmailSuggestionIndex 
                                ? 'bg-orange-100 border-orange-300 shadow-sm' 
                                : 'bg-white border-slate-200 hover:bg-slate-50 hover:border-slate-300'
                            }`}
                            onClick={() => handleEmailContactSelect(contact)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="font-medium text-gray-900 text-sm flex items-center">
                                  {contact.first_name} {contact.last_name}
                                  <Link 
                                    href={`/dashboard/people/${contact.contact_id}`}
                                    className="ml-2 p-1 rounded-full hover:bg-blue-100 transition-colors"
                                    onClick={(e) => e.stopPropagation()}
                                    title="View contact details"
                                  >
                                    <ExternalLink className="h-3 w-3 text-blue-600" />
                                  </Link>
                                </div>
                                <div className="text-xs text-slate-600 mt-1">
                                  {contact.email} {contact.personal_email && contact.personal_email !== contact.email && `• ${contact.personal_email}`}
                                </div>
                                {contact.title && (
                                  <div className="text-xs text-slate-500">
                                    {contact.title} {contact.company_name && `at ${contact.company_name}`}
                                  </div>
                                )}
                                <div className="flex items-center text-xs text-orange-600 mt-2">
                                  <AlertTriangle className="h-3 w-3 mr-1" />
                                  <span>Contact already exists</span>
                                </div>
                              </div>
                              <div className="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded">
                                ID: {contact.contact_id}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="phone_number" className="text-sm font-medium text-slate-700">Primary Phone</Label>
                      <Input 
                        id="phone_number" 
                        name="phone_number" 
                        value={formData.phone_number} 
                        onChange={handleChange}
                        className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                        placeholder="e.g., +**************** (Optional)"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone_number_secondary" className="text-sm font-medium text-slate-700">Additional Phone</Label>
                      <Input 
                        id="phone_number_secondary" 
                        name="phone_number_secondary" 
                        value={formData.phone_number_secondary} 
                        onChange={handleChange}
                        className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                        placeholder="e.g., +**************** (Optional)"
                      />
                    </div>
                  </div>
                  
                  {/* Social Media Links */}
                  <div className="space-y-4 border-t pt-4">
                    <h4 className="text-sm font-medium text-slate-700">Social Media Links</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="linkedin_url" className="text-sm font-medium text-slate-700">LinkedIn Profile</Label>
                        <div className="relative">
                          <Input 
                            id="linkedin_url" 
                            name="linkedin_url" 
                            value={formData.linkedin_url} 
                            onChange={handleChange}
                            className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 hover:border-slate-300 ${
                              fieldValidationStates.linkedin_url.isDuplicate 
                                ? 'border-red-300 focus:border-red-500' 
                                : 'border-slate-200 focus:border-blue-500'
                            }`}
                            placeholder="https://linkedin.com/in/username (Optional)"
                          />
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            {fieldValidationStates.linkedin_url.isValidating ? (
                              <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                            ) : fieldValidationStates.linkedin_url.isDuplicate ? (
                              <X className="h-4 w-4 text-red-600" />
                            ) : formData.linkedin_url && !fieldValidationStates.linkedin_url.isValidating ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : null}
                          </div>
                        </div>
                        {fieldValidationStates.linkedin_url.isDuplicate && fieldValidationStates.linkedin_url.duplicateMessage && (
                          <p className="text-red-600 text-xs">{fieldValidationStates.linkedin_url.duplicateMessage}</p>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="twitter" className="text-sm font-medium text-slate-700">Twitter Profile</Label>
                        <Input 
                          id="twitter" 
                          name="twitter" 
                          value={formData.twitter} 
                          onChange={handleChange}
                          className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                          placeholder="https://twitter.com/username (Optional)"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="facebook" className="text-sm font-medium text-slate-700">Facebook</Label>
                        <Input 
                          id="facebook" 
                          name="facebook" 
                          value={formData.facebook} 
                          onChange={handleChange}
                          className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                          placeholder="Facebook URL (Optional)"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="instagram" className="text-sm font-medium text-slate-700">Instagram</Label>
                        <Input 
                          id="instagram" 
                          name="instagram" 
                          value={formData.instagram} 
                          onChange={handleChange}
                          className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                          placeholder="Instagram URL (Optional)"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="youtube" className="text-sm font-medium text-slate-700">YouTube</Label>
                        <Input 
                          id="youtube" 
                          name="youtube" 
                          value={formData.youtube} 
                          onChange={handleChange}
                          className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                          placeholder="YouTube URL (Optional)"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Contact Location Section */}
                <div className="space-y-4 border-t pt-6">
                  <h3 className="text-md font-medium text-slate-700 border-b pb-2 flex items-center">
                    <MapPin className="h-4 w-4 mr-2" />
                    Contact Location
                  </h3>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="contact_address" className="text-sm font-medium text-slate-700">Address</Label>
                      <Input 
                        id="contact_address" 
                        name="contact_address" 
                        value={formData.contact_address} 
                        onChange={handleChange}
                        className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                        placeholder="e.g., 123 Main Street (Optional)"
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="contact_city" className="text-sm font-medium text-slate-700">City</Label>
                        <Input 
                          id="contact_city" 
                          name="contact_city" 
                          value={formData.contact_city} 
                          onChange={handleChange}
                          className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                          placeholder="e.g., New York (Optional)"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="contact_state" className="text-sm font-medium text-slate-700">State/Province</Label>
                        <Input 
                          id="contact_state" 
                          name="contact_state" 
                          value={formData.contact_state} 
                          onChange={handleChange}
                          className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                          placeholder="e.g., New York (Optional)"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="contact_zip_code" className="text-sm font-medium text-slate-700">Zip Code</Label>
                        <Input 
                          id="contact_zip_code" 
                          name="contact_zip_code" 
                          value={formData.contact_zip_code} 
                          onChange={handleChange}
                          className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                          placeholder="e.g., 10001 (Optional)"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="contact_country" className="text-sm font-medium text-slate-700">Country</Label>
                        <Input 
                          id="contact_country" 
                          name="contact_country" 
                          value={formData.contact_country} 
                          onChange={handleChange}
                          className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                          placeholder="e.g., United States (Optional)"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Professional Details Section */}
                <div className="space-y-4 border-t pt-6">
                  <h3 className="text-md font-medium text-slate-700 border-b pb-2 flex items-center">
                    <DollarSign className="h-4 w-4 mr-2" />
                    Professional Details
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <FilterFieldWithAdd
                        label="Contact Type"
                        value={formData.contact_type}
                        options={filterOptions.contactTypes}
                        onChange={(value) => handleSelectChange('contact_type', value)}
                        placeholder="Select contact type or add new..."
                      />
                    </div>
                    <div className="space-y-2">
                      <FilterFieldWithAdd
                        label="Decision Making Role"
                        value={formData.role_in_decision_making}
                        options={filterOptions.decisionMakingRoles}
                        onChange={(value) => handleSelectChange('role_in_decision_making', value)}
                        placeholder="Select role or add new..."
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="relationship_owner" className="text-sm font-medium text-slate-700">Relationship Owner</Label>
                      <Input 
                        id="relationship_owner" 
                        name="relationship_owner" 
                        value={formData.relationship_owner} 
                        onChange={handleChange}
                        className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                        placeholder="e.g., John Smith (Optional)"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="source_of_introduction" className="text-sm font-medium text-slate-700">Source of Introduction</Label>
                      <Input 
                        id="source_of_introduction" 
                        name="source_of_introduction" 
                        value={formData.source_of_introduction} 
                        onChange={handleChange}
                        className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                        placeholder="e.g., LinkedIn, Referral (Optional)"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="kyc_status" className="text-sm font-medium text-slate-700">KYC Status</Label>
                      <Select 
                        value={formData.kyc_status} 
                        onValueChange={(value) => handleSelectChange('kyc_status', value)}
                      >
                        <SelectTrigger className="h-12 rounded-xl border-2 border-slate-200 focus:border-blue-500 hover:border-slate-300 focus:ring-4 focus:ring-blue-100">
                          <SelectValue placeholder="Select KYC status (Optional)" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Pending">Pending</SelectItem>
                          <SelectItem value="In Progress">In Progress</SelectItem>
                          <SelectItem value="Completed">Completed</SelectItem>
                          <SelectItem value="Not Required">Not Required</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="capital_type" className="text-sm font-medium text-slate-700">Capital Type (Legacy)</Label>
                      <Select 
                        value={formData.capital_type || ''} 
                        onValueChange={(value) => handleSelectChange('capital_type', value)}
                      >
                        <SelectTrigger className="h-12 rounded-xl border-2 border-slate-200 focus:border-blue-500 hover:border-slate-300 focus:ring-4 focus:ring-blue-100">
                          <SelectValue placeholder="Select capital type (Optional)" />
                        </SelectTrigger>
                        <SelectContent>
                          {capitalTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2 flex items-center">
                      <input 
                        type="checkbox"
                        id="accredited_investor_status"
                        checked={formData.accredited_investor_status}
                        onChange={(e) => setFormData(prev => ({ ...prev, accredited_investor_status: e.target.checked }))}
                        className="mr-2"
                      />
                      <Label htmlFor="accredited_investor_status" className="text-sm font-medium text-slate-700">
                        Accredited Investor
                      </Label>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="age" className="text-sm font-medium text-slate-700">Age</Label>
                      <Input 
                        id="age" 
                        name="age" 
                        value={formData.age} 
                        onChange={handleChange}
                        className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                        placeholder="e.g., 45 (Optional)"
                      />
                    </div>
                  </div>
                </div>

                {/* Education Section */}
                <div className="space-y-4 border-t pt-6">
                  <h3 className="text-md font-medium text-slate-700 border-b pb-2 flex items-center">
                    <GraduationCap className="h-4 w-4 mr-2" />
                    Education
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="education_college" className="text-sm font-medium text-slate-700">College/University</Label>
                      <Input 
                        id="education_college" 
                        name="education_college" 
                        value={formData.education_college} 
                        onChange={handleChange}
                        className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                        placeholder="Enter college or university name (Optional)"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="education_college_year_graduated" className="text-sm font-medium text-slate-700">College Graduation Year</Label>
                      <Input 
                        id="education_college_year_graduated" 
                        name="education_college_year_graduated" 
                        value={formData.education_college_year_graduated} 
                        onChange={handleChange}
                        className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                        placeholder="e.g., 2020 (Optional)"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="education_high_school" className="text-sm font-medium text-slate-700">High School</Label>
                      <Input 
                        id="education_high_school" 
                        name="education_high_school" 
                        value={formData.education_high_school} 
                        onChange={handleChange}
                        className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                        placeholder="Enter high school name (Optional)"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="education_high_school_year_graduated" className="text-sm font-medium text-slate-700">High School Graduation Year</Label>
                      <Input 
                        id="education_high_school_year_graduated" 
                        name="education_high_school_year_graduated" 
                        value={formData.education_high_school_year_graduated} 
                        onChange={handleChange}
                        className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                        placeholder="e.g., 2016 (Optional)"
                      />
                    </div>
                  </div>
                </div>

                {/* Personal Details Section */}
                <div className="space-y-4 border-t pt-6">
                  <h3 className="text-md font-medium text-slate-700 border-b pb-2 flex items-center">
                    <Heart className="h-4 w-4 mr-2" />
                    Personal Details
                  </h3>
                  
                  {/* Honorable Achievements */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-slate-700">Honorable Achievements</Label>
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        <Input 
                          placeholder="Enter achievement and press Enter"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              const value = (e.target as HTMLInputElement).value.trim();
                              if (value) {
                                setFormData(prev => ({
                                  ...prev,
                                  honorable_achievements: [...prev.honorable_achievements, value]
                                }));
                                (e.target as HTMLInputElement).value = '';
                              }
                            }
                          }}
                          className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                        />
                      </div>
                      {formData.honorable_achievements.length > 0 && (
                        <div className="space-y-2">
                          {formData.honorable_achievements.map((achievement, index) => (
                            <div key={index} className="flex items-center gap-2 p-2 bg-slate-50 rounded-lg">
                              <span className="flex-1 text-sm">{achievement}</span>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setFormData(prev => ({
                                    ...prev,
                                    honorable_achievements: prev.honorable_achievements.filter((_, i) => i !== index)
                                  }));
                                }}
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Hobbies */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-slate-700">Hobbies</Label>
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        <Input 
                          placeholder="Enter hobby and press Enter"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              const value = (e.target as HTMLInputElement).value.trim();
                              if (value) {
                                setFormData(prev => ({
                                  ...prev,
                                  hobbies: [...prev.hobbies, value]
                                }));
                                (e.target as HTMLInputElement).value = '';
                              }
                            }
                          }}
                          className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
                        />
                      </div>
                      {formData.hobbies.length > 0 && (
                        <div className="space-y-2">
                          {formData.hobbies.map((hobby, index) => (
                            <div key={index} className="flex items-center gap-2 p-2 bg-slate-50 rounded-lg">
                              <span className="flex-1 text-sm">{hobby}</span>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setFormData(prev => ({
                                    ...prev,
                                    hobbies: prev.hobbies.filter((_, i) => i !== index)
                                  }));
                                }}
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Investment Criteria Toggle/Edit Section */}
            <Card className="bg-white shadow-xl border-0 rounded-2xl overflow-hidden">
              <CardContent className="p-6">
                {!selectedCompany ? (
                  /* No Company Selected - Show Toggle Banner */
                  <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center">
                    <Target className="h-8 w-8 text-gray-400 mx-auto mb-3" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Investment Criteria</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      Add investment criteria to specify this contact's investment preferences
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setShowInvestmentCriteria(!showInvestmentCriteria);
                        if (!showInvestmentCriteria && investmentCriteria.length === 0) {
                          addInvestmentCriteria();
                        }
                      }}
                      className="flex items-center"
                    >
                      {showInvestmentCriteria ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
                      {showInvestmentCriteria ? 'Hide Investment Criteria' : 'Add Investment Criteria'}
                    </Button>
                  </div>
                ) : (
                  /* Company Selected - Show Company Criteria Banner */
                  <div className="bg-emerald-50 border border-emerald-200 rounded-xl p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center mb-3">
                          <CheckCircle className="h-5 w-5 text-emerald-600 mr-2" />
                          <h3 className="text-lg font-medium text-emerald-900">
                            {investmentCriteria.length > 0 ? 'Investment Criteria Available' : 'Company Investment Criteria Available'}
                          </h3>
                        </div>
                        <p className="text-sm text-emerald-700 mb-4">
                          {investmentCriteria.length > 0 
                            ? `Investment criteria from ${selectedCompany.company_name} have been loaded. You can customize these for this specific contact.`
                            : `We can pre-populate investment criteria from ${selectedCompany.company_name}'s profile. You can customize these for this specific contact.`
                          }
                        </p>
                        <div className="flex items-center space-x-4">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setShowInvestmentCriteria(!showInvestmentCriteria);
                              // If showing criteria but none exist, create from company data
                              if (!showInvestmentCriteria && investmentCriteria.length === 0 && selectedCompany.extracted_data) {
                                const extracted = selectedCompany.extracted_data;
                                const initialCriteria: InvestmentCriteria = {
                                  id: '1',
                                  capital_type: determineCapitalType(extracted.companytype || ''),
                                  investment_criteria_country: [...(extracted.geographicfocus || []), ...(extracted.targetmarkets || [])].join(', '),
                                  investment_criteria_state: '',
                                  investment_criteria_city: '',
                                  investment_criteria_property_type: (extracted.investment_criteria_property_types || extracted.propertytypes || []).join(', '),
                                  investment_criteria_asset_type: (extracted.investment_criteria_asset_types || extracted.assetclasses || []).join(', '),
                                  investment_criteria_deal_size: extracted.dealsize || extracted.minimumdealsize || extracted.aum || '',
                                  investment_criteria_loan_type: (extracted.investment_criteria_loan_types || []).join(', '),
                                  loan_term_months: '',
                                  interest_rate_index: '',
                                  origination_fee_percent: '',
                                  exit_fee_percent: '',
                                  recourse: '',
                                  closing_time_weeks: '',
                                  hold_period_years: extracted.holdperiod || '',
                                  expected_irr_percent: extracted.targetreturn || '',
                                  expected_equity_multiple: ''
                                };
                                setInvestmentCriteria([initialCriteria]);
                              }
                            }}
                            className="border-emerald-300 text-emerald-700 hover:bg-emerald-100"
                          >
                            {showInvestmentCriteria ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
                            {showInvestmentCriteria ? 'Hide Criteria' : 'Edit Investment Criteria'}
                          </Button>
                          {investmentCriteria.length > 0 && (
                            <span className="text-xs text-emerald-600 bg-emerald-100 px-2 py-1 rounded-full">
                              {investmentCriteria.length} criteria set
                            </span>
                          )}
                          {investmentCriteria.length === 0 && selectedCompany.extracted_data && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const extracted = selectedCompany.extracted_data!;
                                const initialCriteria: InvestmentCriteria = {
                                  id: '1',
                                  capital_type: formData.capital_type || determineCapitalType(extracted.companytype || ''),
                                  investment_criteria_country: [...(extracted.geographicfocus || []), ...(extracted.targetmarkets || [])].join(', '),
                                  investment_criteria_state: '',
                                  investment_criteria_city: '',
                                  investment_criteria_property_type: (extracted.investment_criteria_property_types || extracted.propertytypes || []).join(', '),
                                  investment_criteria_asset_type: (extracted.investment_criteria_asset_types || extracted.assetclasses || []).join(', '),
                                  investment_criteria_deal_size: extracted.dealsize || extracted.minimumdealsize || extracted.aum || '',
                                  investment_criteria_loan_type: (extracted.investment_criteria_loan_types || []).join(', '),
                                  loan_term_months: '',
                                  interest_rate_index: '',
                                  origination_fee_percent: '',
                                  exit_fee_percent: '',
                                  recourse: '',
                                  closing_time_weeks: '',
                                  hold_period_years: extracted.holdperiod || '',
                                  expected_irr_percent: extracted.targetreturn || '',
                                  expected_equity_multiple: ''
                                };
                                setInvestmentCriteria([initialCriteria]);
                                setShowInvestmentCriteria(true);
                                toast.success('Investment criteria loaded from company profile');
                              }}
                              className="border-blue-300 text-blue-700 hover:bg-blue-100"
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Load Company Criteria
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Investment Criteria Section - Conditionally Rendered */}
                {showInvestmentCriteria && (
                  <div className="mt-6 border-t pt-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-md font-medium text-slate-700">Investment Criteria Details</h4>
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm"
                        onClick={addInvestmentCriteria}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add Criteria
                      </Button>
                    </div>
                    <InvestmentCriteriaSection
                      investmentCriteria={investmentCriteria}
                      savedInvestmentCriteria={[]}
                      contactsAdded={0}
                      onAdd={addInvestmentCriteria}
                      onRemove={removeInvestmentCriteria}
                      onUpdate={updateInvestmentCriteria}
                      onCopyPrevious={() => {}}
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Submit Button */}
            <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
              <div className="flex justify-between items-center">
                <div className="flex items-center space-x-2 text-sm text-slate-600">
                  <div className={`w-2 h-2 rounded-full ${isLoadingCompanyData ? 'bg-blue-400' : 'bg-green-400'}`}></div>
                  <span>
                    {isLoadingCompanyData ? 'Loading company data...' : 'Ready to save contact'}
                  </span>
                  {investmentCriteria.length > 0 && !isLoadingCompanyData && (
                    <>
                      <div className="w-1 h-1 bg-slate-300 rounded-full mx-2"></div>
                      <span>{investmentCriteria.length} investment criteria added</span>
                    </>
                  )}
                </div>
                <div className="flex space-x-4">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={onBack}
                    className="h-12 px-6 rounded-xl border-2 border-slate-200 hover:bg-slate-50 transition-all duration-200"
                    disabled={isLoadingCompanyData}
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={isSubmitting || isLoadingCompanyData}
                    className="h-12 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 min-w-[140px] disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Adding...
                      </>
                    ) : isLoadingCompanyData ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Loading...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Add Contact
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column: Company Selection and Information */}
          <div className="space-y-6">
            {/* Company Selection Card */}
            <Card className="bg-white shadow-xl border-0 rounded-2xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-teal-50 border-b border-emerald-100">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="p-2 bg-emerald-100 rounded-xl mr-3">
                      <Building2 className="h-5 w-5 text-emerald-600" />
                    </div>
                    <div>
                      <div className="text-lg font-semibold text-slate-900">Company Selection</div>
                      <div className="text-sm text-slate-600">
                        {isLoadingCompanyData ? 'Loading company data...' : 'Search and select the company'}
                      </div>
                    </div>
                  </div>
                  {selectedCompany && !isLoadingCompanyData && (
                    <div className="flex items-center bg-green-100 px-3 py-1 rounded-full">
                      <CheckCircle className="h-4 w-4 text-green-600 mr-1" />
                      <span className="text-xs font-medium text-green-700">Selected</span>
                    </div>
                  )}
                  {isLoadingCompanyData && (
                    <div className="flex items-center bg-blue-100 px-3 py-1 rounded-full">
                      <Loader2 className="h-4 w-4 animate-spin text-blue-600 mr-1" />
                      <span className="text-xs font-medium text-blue-700">Loading</span>
                    </div>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-6 bg-gradient-to-b from-white to-slate-50">
                {isLoadingCompanyData ? (
                  /* Loading State */
                  <div className="space-y-4">
                    <div className="flex items-center justify-center p-8">
                      <div className="text-center">
                        <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-3" />
                        <p className="text-sm text-slate-600">Loading company information and investment criteria...</p>
                        <p className="text-xs text-slate-500 mt-1">This may take a few seconds</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="space-y-2 relative">
                      <Label htmlFor="company_name" className="text-sm font-medium text-slate-700">
                        Company Name <span className="text-red-500">*</span>
                      </Label>
                      <div className="relative">
                        <Input 
                          ref={companyInputRef}
                          id="company_name" 
                          name="company_name" 
                          value={formData.company_name} 
                          onChange={handleChange}
                          onKeyDown={handleKeyDown}
                          placeholder={selectedCompany ? "Company selected - type to change" : "Start typing company name..."}
                          className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                          required
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          {isSearching ? (
                            <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                          ) : selectedCompany ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <Search className="h-4 w-4 text-gray-400" />
                          )}
                        </div>
                      </div>
                      {selectedCompany && (
                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs text-green-600 flex items-center">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Company auto-selected from {companyId ? 'company page' : 'search'}
                          </span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedCompany(null);
                              setFormData(prev => ({
                                ...prev,
                                company_name: '',
                                company_website: '',
                                industry: '',
                                company_address: '',
                                company_city: '',
                                company_state: '',
                                company_country: ''
                              }));
                            }}
                            className="text-xs text-slate-500 hover:text-slate-700"
                          >
                            <X className="h-3 w-3 mr-1" />
                            Change Company
                          </Button>
                        </div>
                      )}
                    </div>

                    {/* Company Search Results */}
                    {showSuggestions && (
                      <div className="space-y-2">
                        {companySuggestions.length > 0 ? (
                          <>
                            <Label className="text-sm font-medium text-slate-700">Search Results ({companySuggestions.length} found)</Label>
                        <div className="space-y-2 max-h-80 overflow-y-auto border rounded-xl p-2 bg-slate-50" data-search-results>
                          {companySuggestions.map((company, index) => (
                            <div
                              key={index}
                              className={`p-3 rounded-lg cursor-pointer border transition-all duration-200 ${
                                index === selectedSuggestionIndex 
                                  ? 'bg-emerald-50 border-emerald-300 shadow-sm' 
                                  : 'bg-white border-slate-200 hover:bg-slate-50 hover:border-slate-300'
                              }`}
                              onClick={() => handleCompanySelect(company)}
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <div className="font-medium text-gray-900 text-sm flex items-center">
                                    {company.company_name}
                                    <Link 
                                      href={`/dashboard/companies/${company.company_id}`}
                                      className="ml-2 p-1 rounded-full hover:bg-blue-100 transition-colors"
                                      onClick={(e) => e.stopPropagation()}
                                      title="View company details"
                                    >
                                      <ExternalLink className="h-3 w-3 text-blue-600" />
                                    </Link>
                                  </div>
                                  {company.extracted_data && (
                                    <div className="text-xs text-emerald-600 font-medium mt-1 flex items-center">
                                      <CheckCircle className="h-3 w-3 mr-1" />
                                      Enhanced Data
                                    </div>
                                  )}
                                  {(company.industry || company.extracted_data?.companytype) && (
                                    <div className="text-xs text-gray-600 mt-1">
                                      {company.extracted_data?.companytype || company.industry}
                                    </div>
                                  )}
                                  {(company.company_city && company.company_state) || company.extracted_data?.headquarters ? (
                                    <div className="text-xs text-gray-500 mt-1 flex items-center">
                                      <MapPin className="h-3 w-3 mr-1" />
                                      {company.extracted_data?.headquarters || `${company.company_city}, ${company.company_state}`}
                                    </div>
                                  ) : null}
                                </div>
                                <div className="ml-3">
                                  {company.extracted_data?.fundsize && (
                                    <div className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                                      {company.extracted_data.fundsize}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                          </>
                        ) : isSearching ? (
                          <div className="p-4 text-center border rounded-xl bg-slate-50">
                            <div className="text-sm text-gray-600">
                              <Loader2 className="h-4 w-4 mx-auto mb-2 animate-spin text-gray-400" />
                              Searching for companies...
                            </div>
                          </div>
                        ) : (
                          <div className="p-4 text-center border rounded-xl bg-slate-50">
                            <div className="text-sm text-gray-600">
                              <Search className="h-4 w-4 mx-auto mb-2 text-gray-400" />
                              No companies found matching "{formData.company_name}"
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              Try a different search term or add the company manually below
                            </div>
                            <div className="mt-3">
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setShowSuggestions(false);
                                  setSelectedCompany(null);
                                }}
                                className="text-xs"
                              >
                                <Plus className="h-3 w-3 mr-1" />
                                Add as new company
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Manual Company Entry Fields (when no company selected) */}
                    {!selectedCompany && (
                      <div className="space-y-4 border-t pt-4">
                        <div className="space-y-2">
                          <Label htmlFor="company_website" className="text-sm font-medium text-slate-700">
                            Website
                          </Label>
                          <Input 
                            id="company_website" 
                            name="company_website" 
                            type="url"
                            value={formData.company_website} 
                            onChange={handleChange}
                            placeholder="https://example.com"
                            className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 border-slate-200 focus:border-emerald-500 hover:border-slate-300"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="industry" className="text-sm font-medium text-slate-700">Industry</Label>
                          <Select 
                            value={formData.industry} 
                            onValueChange={(value) => handleSelectChange('industry', value)}
                          >
                            <SelectTrigger className="h-12 rounded-xl border-2 border-slate-200 focus:border-emerald-500 hover:border-slate-300">
                              <SelectValue placeholder="Select industry" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="real_estate">Real Estate</SelectItem>
                              <SelectItem value="finance">Finance</SelectItem>
                              <SelectItem value="technology">Technology</SelectItem>
                              <SelectItem value="banking">Banking</SelectItem>
                              <SelectItem value="insurance">Insurance</SelectItem>
                              <SelectItem value="investment">Investment</SelectItem>
                              <SelectItem value="construction">Construction</SelectItem>
                              <SelectItem value="property_management">Property Management</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="company_address" className="text-sm font-medium text-slate-700">Company Address</Label>
                          <Input 
                            id="company_address" 
                            name="company_address" 
                            value={formData.company_address} 
                            onChange={handleChange}
                            className="h-12 rounded-xl border-2 border-slate-200 focus:border-emerald-500 hover:border-slate-300 focus:ring-4 focus:ring-emerald-100"
                            placeholder="Enter company address"
                          />
                        </div>
                        
                        <div className="grid grid-cols-1 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="company_city" className="text-sm font-medium text-slate-700">City</Label>
                            <Input 
                              id="company_city" 
                              name="company_city" 
                              value={formData.company_city} 
                              onChange={handleChange}
                              className="h-12 rounded-xl border-2 border-slate-200 focus:border-emerald-500 hover:border-slate-300 focus:ring-4 focus:ring-emerald-100"
                              placeholder="City"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="company_state" className="text-sm font-medium text-slate-700">State/Province</Label>
                            <Input 
                              id="company_state" 
                              name="company_state" 
                              value={formData.company_state} 
                              onChange={handleChange}
                              className="h-12 rounded-xl border-2 border-slate-200 focus:border-emerald-500 hover:border-slate-300 focus:ring-4 focus:ring-emerald-100"
                              placeholder="State/Province"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="company_zip" className="text-sm font-medium text-slate-700">Zip Code</Label>
                            <Input 
                              id="company_zip" 
                              name="company_zip" 
                              value={formData.company_zip} 
                              onChange={handleChange}
                              className="h-12 rounded-xl border-2 border-slate-200 focus:border-emerald-500 hover:border-slate-300 focus:ring-4 focus:ring-emerald-100"
                              placeholder="Zip Code"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="company_country" className="text-sm font-medium text-slate-700">Country</Label>
                            <Input 
                              id="company_country" 
                              name="company_country" 
                              value={formData.company_country} 
                              onChange={handleChange}
                              className="h-12 rounded-xl border-2 border-slate-200 focus:border-emerald-500 hover:border-slate-300 focus:ring-4 focus:ring-emerald-100"
                              placeholder="Country"
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>

            {/* Company Profile - Show when company is selected */}
            {selectedCompany && (
              <Card className="sticky top-4">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Building2 className="h-5 w-5 mr-2 text-blue-600" />
                      Company Profile
                    </div>
                    {selectedCompany.company_id && (
                      <Link 
                        href={`/dashboard/companies/${selectedCompany.company_id}`} 
                        className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                      >
                        View Details
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </Link>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Company Basic Info */}
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="font-semibold text-lg text-gray-900">{selectedCompany.company_name}</h3>
                        {selectedCompany.company_website && (
                          <a 
                            href={selectedCompany.company_website} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:underline flex items-center mt-1"
                          >
                            {selectedCompany.company_website}
                            <ExternalLink className="h-3 w-3 ml-1" />
                          </a>
                        )}
                      </div>
                    </div>

                    {(selectedCompany.industry || selectedCompany.extracted_data?.companytype) && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Target className="h-4 w-4 mr-2" />
                        {selectedCompany.extracted_data?.companytype || selectedCompany.industry}
                      </div>
                    )}

                    {((selectedCompany.company_city && selectedCompany.company_state) || selectedCompany.extracted_data?.headquarters) && (
                      <div className="flex items-center text-sm text-gray-600">
                        <MapPin className="h-4 w-4 mr-2" />
                        {selectedCompany.extracted_data?.headquarters || `${selectedCompany.company_city}, ${selectedCompany.company_state}`}
                      </div>
                    )}
                  </div>

                  {/* Enhanced Company Data */}
                  {selectedCompany.extracted_data && (
                    <>
                      <div className="border-t pt-4">
                        <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                          <DollarSign className="h-4 w-4 mr-2" />
                          Financial Information
                        </h4>
                        <div className="grid grid-cols-1 gap-3">
                          {(selectedCompany.extracted_data.fundsize || selectedCompany.extracted_data.aum) && (
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">{selectedCompany.extracted_data.fundsize ? 'Fund Size:' : 'AUM:'}</span>
                              <Badge variant="secondary">{selectedCompany.extracted_data.fundsize || selectedCompany.extracted_data.aum}</Badge>
                            </div>
                          )}
                          {selectedCompany.extracted_data.foundedyear && (
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Founded:</span>
                              <span className="text-sm font-medium">{selectedCompany.extracted_data.foundedyear}</span>
                            </div>
                          )}
                          {selectedCompany.extracted_data.numberofemployees && (
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Employees:</span>
                              <span className="text-sm font-medium">{selectedCompany.extracted_data.numberofemployees}</span>
                            </div>
                          )}
                          {selectedCompany.extracted_data.targetreturn && (
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Target Return:</span>
                              <span className="text-sm font-medium">{selectedCompany.extracted_data.targetreturn}</span>
                            </div>
                          )}
                          {selectedCompany.extracted_data.holdperiod && (
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Hold Period:</span>
                              <span className="text-sm font-medium">{selectedCompany.extracted_data.holdperiod}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Investment Criteria */}
                      <div className="border-t pt-4">
                        <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                          <Target className="h-4 w-4 mr-2" />
                          Investment Focus
                        </h4>
                        <div className="space-y-3">
                          {/* Geographic Focus - combine geographicfocus and targetmarkets */}
                          {((selectedCompany.extracted_data?.geographicfocus?.length || 0) > 0 || 
                            (selectedCompany.extracted_data?.targetmarkets?.length || 0) > 0) && (
                            <div>
                              <span className="text-xs text-gray-500 uppercase tracking-wide">Geographic Focus</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {[
                                  ...(selectedCompany.extracted_data?.geographicfocus || []),
                                  ...(selectedCompany.extracted_data?.targetmarkets || []).filter(market => 
                                    !(selectedCompany.extracted_data?.geographicfocus || []).includes(market)
                                  )
                                ].map((region: string, idx: number) => (
                                  <Badge key={idx} variant="outline" className="text-xs">{region}</Badge>
                                ))}
                              </div>
                            </div>
                          )}
                          
                          {/* Property Types - prefer investment_criteria_property_types but fall back to propertytypes */}
                          {((selectedCompany.extracted_data?.investment_criteria_property_types?.length || 0) > 0 || 
                            (selectedCompany.extracted_data?.propertytypes?.length || 0) > 0) && (
                            <div>
                              <span className="text-xs text-gray-500 uppercase tracking-wide">Property Types</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {((selectedCompany.extracted_data?.investment_criteria_property_types?.length || 0) > 0 ? 
                                  selectedCompany.extracted_data?.investment_criteria_property_types : 
                                  selectedCompany.extracted_data?.propertytypes
                                )?.map((type: string, idx: number) => (
                                  <Badge key={idx} variant="outline" className="text-xs">{type}</Badge>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Asset Types - prefer investment_criteria_asset_types but fall back to assetclasses */}
                          {((selectedCompany.extracted_data?.investment_criteria_asset_types?.length || 0) > 0 || 
                            (selectedCompany.extracted_data?.assetclasses?.length || 0) > 0) && (
                            <div>
                              <span className="text-xs text-gray-500 uppercase tracking-wide">Asset Types</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {((selectedCompany.extracted_data?.investment_criteria_asset_types?.length || 0) > 0 ? 
                                  selectedCompany.extracted_data?.investment_criteria_asset_types : 
                                  selectedCompany.extracted_data?.assetclasses
                                )?.map((type: string, idx: number) => (
                                  <Badge key={idx} variant="outline" className="text-xs">{type}</Badge>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Loan Types */}
                          {(selectedCompany.extracted_data?.investment_criteria_loan_types?.length || 0) > 0 && (
                            <div>
                              <span className="text-xs text-gray-500 uppercase tracking-wide">Loan Types</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {selectedCompany.extracted_data.investment_criteria_loan_types?.map((type: string, idx: number) => (
                                  <Badge key={idx} variant="outline" className="text-xs">{type}</Badge>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Deal Size */}
                          {(selectedCompany.extracted_data.dealsize || 
                            selectedCompany.extracted_data.minimumdealsize ||
                            selectedCompany.extracted_data.aum) && (
                            <div>
                              <span className="text-xs text-gray-500 uppercase tracking-wide">Deal Size Range</span>
                              <div className="mt-1">
                                <Badge variant="secondary" className="text-xs">
                                  {selectedCompany.extracted_data.dealsize || 
                                   (selectedCompany.extracted_data.minimumdealsize && selectedCompany.extracted_data.maximumdealsize 
                                     ? `${selectedCompany.extracted_data.minimumdealsize} - ${selectedCompany.extracted_data.maximumdealsize}`
                                     : selectedCompany.extracted_data.minimumdealsize 
                                       ? `${selectedCompany.extracted_data.minimumdealsize}+`
                                       : selectedCompany.extracted_data.aum)}
                                </Badge>
                              </div>
                            </div>
                          )}

                          {/* Investment Strategies */}
                          {(selectedCompany.extracted_data?.strategies?.length || 0) > 0 && (
                            <div>
                              <span className="text-xs text-gray-500 uppercase tracking-wide">Investment Strategies</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {selectedCompany.extracted_data?.strategies?.map((strategy: string, idx: number) => (
                                  <Badge key={idx} variant="outline" className="text-xs">{strategy}</Badge>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Value Creation */}
                          {(selectedCompany.extracted_data?.valuecreation?.length || 0) > 0 && (
                            <div>
                              <span className="text-xs text-gray-500 uppercase tracking-wide">Value Creation Approach</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {selectedCompany.extracted_data?.valuecreation?.map((approach: string, idx: number) => (
                                  <Badge key={idx} variant="outline" className="text-xs">{approach}</Badge>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Risk Profile */}
                          {selectedCompany.extracted_data.riskprofile && (
                            <div>
                              <span className="text-xs text-gray-500 uppercase tracking-wide">Risk Profile</span>
                              <div className="mt-1">
                                <Badge variant="outline" className="text-xs">{selectedCompany.extracted_data.riskprofile}</Badge>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </form>
      </div>

      {/* Duplicate Contact Dialog */}
      {showDuplicateDialog && queuedContact && (
        <DuplicateContactDialog
          isOpen={showDuplicateDialog}
          onClose={handleCancelDuplicate}
          onAddAnyway={handleProceedWithDuplicate}
          duplicateContacts={duplicateContacts}
        />
      )}
    </div>
  );
};

export default AddContactOld; 