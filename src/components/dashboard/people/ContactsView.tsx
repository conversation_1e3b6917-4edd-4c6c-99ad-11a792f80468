"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { UserPlus, CheckSquare, Square, Play, RefreshCw, Sparkles, Mail, PenTool, AlertTriangle, CheckCircle, XCircle, Send, Settings2 } from "lucide-react";
import ContactCard from "./list-components/ContactCard";
import Pagination from "./list-components/Pagination";
import PaginationSizeSelector from "./list-components/PaginationSizeSelector";
import ContactUnifiedFiltersV2Component from "./ContactUnifiedFiltersV2";
import { UnifiedContactData } from "./shared/types";
import type { ContactUnifiedFiltersV2 } from "../../../types/unified-filters-v2";
import { useRouter, useSearchParams } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, <PERSON>I<PERSON>, <PERSON>Trigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";




// Processing stages for contacts
type ContactProcessingStage = 'email_validation' | 'contact_enrichment' | 'contact_enrichment_v2' | 'contact_investment_criteria' | 'email_generation' | 'smartlead_sync';

interface ProcessingJob {
  stage: ContactProcessingStage;
  isExecuting: boolean;
}

export default function ContactsView() {
  const [contacts, setContacts] = useState<UnifiedContactData[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalContacts, setTotalContacts] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [mappings, setMappings] = useState<any>(null);

  
  // V2 filters state (V2 only - no legacy support)
  const [filters, setFilters] = useState<ContactUnifiedFiltersV2>({
    page: 1,
    limit: 25,
    sortBy: 'updated_at',
    sortOrder: 'desc'
  });

  // Selection state
  const [selectedContacts, setSelectedContacts] = useState<Set<number>>(new Set());
  const [selectAll, setSelectAll] = useState(false);
  
  // Processing state
  const [processingJobs, setProcessingJobs] = useState<ProcessingJob[]>([
    { stage: 'email_validation', isExecuting: false },
    { stage: 'contact_enrichment_v2', isExecuting: false },
    { stage: 'contact_investment_criteria', isExecuting: false },
    { stage: 'email_generation', isExecuting: false },
    { stage: 'smartlead_sync', isExecuting: false }
  ]);
  
  // Smartlead campaign state
  const [campaigns, setCampaigns] = useState<Array<{id: string, name: string}>>([]);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>('');
  const [loadingCampaigns, setLoadingCampaigns] = useState(false);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();

  // Handle adding new contact
  const handleAddContact = () => {
    router.push('/dashboard/contacts/add');
  };



  // Initialize V2 filters from URL parameters on component mount
  useEffect(() => {
    if (!searchParams) return;
    
    const urlFilters: ContactUnifiedFiltersV2 = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '25'),
      sortBy: searchParams.get('sortBy') || 'updated_at',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      searchTerm: searchParams.get('searchTerm') || undefined,
      

      
      // Investment criteria filters
      capitalPosition: searchParams.get('capitalPosition')?.split(',').filter(Boolean),
      propertyTypes: searchParams.get('propertyTypes')?.split(',').filter(Boolean),
      propertySubcategories: searchParams.get('propertySubcategories')?.split(',').filter(Boolean),
      strategies: searchParams.get('strategies')?.split(',').filter(Boolean),
      loanTypes: searchParams.get('loanTypes')?.split(',').filter(Boolean),
      loanProgram: searchParams.get('loanProgram')?.split(',').filter(Boolean),
      structuredLoanTranche: searchParams.get('structuredLoanTranche')?.split(',').filter(Boolean),
      recourseLoan: searchParams.get('recourseLoan')?.split(',').filter(Boolean),
      
      // Deal size and return filters
      dealSizeMin: searchParams.get('dealSizeMin') ? parseFloat(searchParams.get('dealSizeMin')!) : undefined,
      dealSizeMax: searchParams.get('dealSizeMax') ? parseFloat(searchParams.get('dealSizeMax')!) : undefined,
      targetReturnMin: searchParams.get('targetReturnMin') ? parseFloat(searchParams.get('targetReturnMin')!) : undefined,
      targetReturnMax: searchParams.get('targetReturnMax') ? parseFloat(searchParams.get('targetReturnMax')!) : undefined,
      minLoanTermMin: searchParams.get('minLoanTermMin') ? parseInt(searchParams.get('minLoanTermMin')!) : undefined,
      maxLoanTermMax: searchParams.get('maxLoanTermMax') ? parseInt(searchParams.get('maxLoanTermMax')!) : undefined,
      
      // V2 Interest rate and LTV filters with proper naming
      loanInterestRateSofrMin: searchParams.get('loanInterestRateSofrMin') ? parseFloat(searchParams.get('loanInterestRateSofrMin')!) : undefined,
      loanInterestRateSofrMax: searchParams.get('loanInterestRateSofrMax') ? parseFloat(searchParams.get('loanInterestRateSofrMax')!) : undefined,
      loanToValueMinMin: searchParams.get('loanToValueMinMin') ? parseFloat(searchParams.get('loanToValueMinMin')!) : undefined,
      loanToValueMaxMax: searchParams.get('loanToValueMaxMax') ? parseFloat(searchParams.get('loanToValueMaxMax')!) : undefined,
      loanToCostMinMin: searchParams.get('loanToCostMinMin') ? parseFloat(searchParams.get('loanToCostMinMin')!) : undefined,
      loanToCostMaxMax: searchParams.get('loanToCostMaxMax') ? parseFloat(searchParams.get('loanToCostMaxMax')!) : undefined,
      loanOriginationMaxFeeMin: searchParams.get('loanOriginationMaxFeeMin') ? parseFloat(searchParams.get('loanOriginationMaxFeeMin')!) : undefined,
      loanOriginationMaxFeeMax: searchParams.get('loanOriginationMaxFeeMax') ? parseFloat(searchParams.get('loanOriginationMaxFeeMax')!) : undefined,
      
      // V2 DSCR filters
      minLoanDscrMin: searchParams.get('minLoanDscrMin') ? parseFloat(searchParams.get('minLoanDscrMin')!) : undefined,
      maxLoanDscrMax: searchParams.get('maxLoanDscrMax') ? parseFloat(searchParams.get('maxLoanDscrMax')!) : undefined,
      
      // V2 Closing time
      closingTimeMin: searchParams.get('closingTimeMin') ? parseInt(searchParams.get('closingTimeMin')!) : undefined,
      closingTimeMax: searchParams.get('closingTimeMax') ? parseInt(searchParams.get('closingTimeMax')!) : undefined,
      
      // Contact filters
      source: searchParams.get('source')?.split(',').filter(Boolean),
      emailStatus: searchParams.get('emailStatus')?.split(',').filter(Boolean),
      jobTier: searchParams.get('jobTier')?.split(',').filter(Boolean),
      
      // Geographic filters (Contact location)
      contactCountries: searchParams.get('contactCountries')?.split(',').filter(Boolean),
      contactStates: searchParams.get('contactStates')?.split(',').filter(Boolean),
      contactCities: searchParams.get('contactCities')?.split(',').filter(Boolean),
      
      // Geographic filters (Investment Criteria)
      regions: searchParams.get('regions')?.split(',').filter(Boolean),
      states: searchParams.get('states')?.split(',').filter(Boolean),
      cities: searchParams.get('cities')?.split(',').filter(Boolean),
      countries: searchParams.get('countries')?.split(',').filter(Boolean),
      
      // Processing status filters
      emailVerificationStatus: searchParams.get('emailVerificationStatus')?.split(',').filter(Boolean),
      contactEnrichmentV2Status: searchParams.get('contactEnrichmentV2Status')?.split(',').filter(Boolean),
      emailGenerationStatus: searchParams.get('emailGenerationStatus')?.split(',').filter(Boolean),
      emailSendingStatus: searchParams.get('emailSendingStatus')?.split(',').filter(Boolean),
      
      // Company processing status filters (from company table)
      companyWebsiteScrapingStatus: searchParams.get('companyWebsiteScrapingStatus')?.split(',').filter(Boolean),
      companyOverviewV2Status: searchParams.get('companyOverviewV2Status')?.split(',').filter(Boolean),
      companyInvestmentCriteriaStatus: searchParams.get('companyInvestmentCriteriaStatus')?.split(',').filter(Boolean),
      
      // Boolean flags
      extracted: searchParams.get('extracted') === 'true' ? true : searchParams.get('extracted') === 'false' ? false : undefined,
      searched: searchParams.get('searched') === 'true' ? true : searchParams.get('searched') === 'false' ? false : undefined,
      emailGenerated: searchParams.get('emailGenerated') === 'true' ? true : searchParams.get('emailGenerated') === 'false' ? false : undefined,
      enriched: searchParams.get('enriched') === 'true' ? true : searchParams.get('enriched') === 'false' ? false : undefined,
      hasSmartleadId: searchParams.get('hasSmartleadId') === 'true' ? true : searchParams.get('hasSmartleadId') === 'false' ? false : undefined,
      
      // Email outreach filters
      hasBeenReachedOut: searchParams.get('hasBeenReachedOut') === 'true' ? true : searchParams.get('hasBeenReachedOut') === 'false' ? false : undefined,
      

      
      // NOT filters
      notCapitalPosition: searchParams.get('notCapitalPosition')?.split(',').filter(Boolean),
      notPropertyTypes: searchParams.get('notPropertyTypes')?.split(',').filter(Boolean),
      notStrategies: searchParams.get('notStrategies')?.split(',').filter(Boolean),
      notLoanTypes: searchParams.get('notLoanTypes')?.split(',').filter(Boolean),
      notStructuredLoanTranche: searchParams.get('notStructuredLoanTranche')?.split(',').filter(Boolean),
      notLoanProgram: searchParams.get('notLoanProgram')?.split(',').filter(Boolean),
      notRecourseLoan: searchParams.get('notRecourseLoan')?.split(',').filter(Boolean),
      notSource: searchParams.get('notSource')?.split(',').filter(Boolean),
      notEmailStatus: searchParams.get('notEmailStatus')?.split(',').filter(Boolean),
      
      // NOT filters for company processing status
      notCompanyWebsiteScrapingStatus: searchParams.get('notCompanyWebsiteScrapingStatus')?.split(',').filter(Boolean),
      notCompanyOverviewV2Status: searchParams.get('notCompanyOverviewV2Status')?.split(',').filter(Boolean),
      notCompanyInvestmentCriteriaStatus: searchParams.get('notCompanyInvestmentCriteriaStatus')?.split(',').filter(Boolean),
    };
    
    console.log('ContactsView: Initializing V2 filters from URL:', urlFilters);
    setFilters(urlFilters);
    // Fetch contacts immediately with the V2 filters
    fetchContacts(urlFilters);
  }, [searchParams]);

  // Load mappings from investment criteria mappings API
  useEffect(() => {
    async function fetchMappings() {
      try {
        const response = await fetch('/api/investment-criteria/mappings');
        if (response.ok) {
          const data = await response.json();
          setMappings(data || {});
        } else {
          console.error("Failed to fetch mappings:", await response.text());
        }
      } catch (error) {
        console.error("Error fetching mappings:", error);
      }
    }

    fetchMappings();
  }, []);

  // Load campaigns from Smartlead
  useEffect(() => {
    const loadCampaigns = async () => {
      setLoadingCampaigns(true);
      try {
        const response = await fetch('/api/smartlead/campaigns');
        if (!response.ok) throw new Error('Failed to fetch campaigns');
        
        const data = await response.json();
        const campaignsList = Array.isArray(data.campaigns) ? data.campaigns : (Array.isArray(data) ? data : []);
        setCampaigns(campaignsList.map((campaign: any, index: number) => {
          // Safely convert id to string
          let campaignId = '';
          if (campaign.id !== null && campaign.id !== undefined && typeof campaign.id !== 'object') {
            campaignId = String(campaign.id);
          } else {
            campaignId = `campaign-${index}`;
          }
          
          return {
            id: campaignId,
            name: (typeof campaign.name === 'string' || typeof campaign.name === 'number') ? String(campaign.name) : `Campaign ${index + 1}`
          };
        }));
      } catch (error) {
        console.error('Error loading campaigns:', error);
        toast({
          title: "Campaign Loading Failed",
          description: "Failed to load Smartlead campaigns",
          variant: "destructive",
        });
      } finally {
        setLoadingCampaigns(false);
      }
    };

    loadCampaigns();
  }, [toast]);

    // Load contacts with V2 filters
  const fetchContacts = async (currentFilters: ContactUnifiedFiltersV2) => {
    console.log('ContactsView: Starting to fetch contacts with V2 filters:', currentFilters);
    setLoading(true);
    try {
      // Build query string with all V2 filter parameters
      const params = new URLSearchParams();
      
      // Add all V2 filter parameters
      Object.entries(currentFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value) && value.length > 0) {
            params.append(key, value.join(','));
          } else if (typeof value === 'boolean') {
            params.append(key, value.toString());
          } else if (typeof value === 'number' || typeof value === 'string') {
            params.append(key, value.toString());
          }
        }
      });

      const apiUrl = `/api/contacts/unified-filters-v2?${params.toString()}`;
      console.log('ContactsView: Making V2 API call to:', apiUrl);
      
      const response = await fetch(apiUrl);
      if (response.ok) {
        const data = await response.json();
        console.log('ContactsView: V2 API response received:', { 
          contactCount: data.data?.length || 0, 
          total: data.pagination?.total || 0 
        });
        setContacts(data.data || []);
        setTotalContacts(data.pagination?.total || 0);
        setTotalPages(data.pagination?.totalPages || 0);
      } else {
        console.error("ContactsView: Failed to fetch contacts with V2 filters:", await response.text());
        setContacts([]);
        setTotalContacts(0);
        setTotalPages(0);
      }
    } catch (error) {
      console.error("ContactsView: Error fetching contacts with V2 filters:", error);
      setContacts([]);
      setTotalContacts(0);
      setTotalPages(0);
    } finally {
      setLoading(false);
    }
  };

  // Handle V2 filter changes
  const handleFiltersChange = (newFilters: ContactUnifiedFiltersV2) => {
    console.log('ContactsView: handleFiltersChange called with V2 filters:', newFilters);
    setFilters(newFilters);
    
    // Fetch contacts with new V2 filters immediately
    fetchContacts(newFilters);
    
    // Update URL with new V2 filters
    const params = new URLSearchParams();
    
    // Add all V2 filter parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','));
        } else if (typeof value === 'boolean') {
          params.set(key, value.toString());
        } else if (typeof value === 'number' || typeof value === 'string') {
          params.set(key, value.toString());
        }
      }
    });
    
    // Preserve tab parameter and add filter parameters
    params.set('tab', 'contacts');
    
    // Update URL without triggering a page reload
    const newUrl = `/dashboard/entity?${params.toString()}`;
    console.log('ContactsView: Updating URL to (V2):', newUrl);
    router.replace(newUrl, { scroll: false });
  };



  

  // Handle clear filters (V2 only)
  const handleClearFilters = () => {
    const clearedFilters: ContactUnifiedFiltersV2 = {
      page: 1,
      limit: 25,
      sortBy: 'updated_at',
      sortOrder: 'desc'
    };
    setFilters(clearedFilters);
    
    // Fetch contacts with cleared V2 filters immediately
    fetchContacts(clearedFilters);
    
    // Update URL to remove all filter parameters but keep tab
    router.replace('/dashboard/entity?tab=contacts', { scroll: false });
  };

  // Handle pagination (V2)
  const handlePageChange = (page: number) => {
    const newFilters = { ...filters, page };
    setFilters(newFilters);
    
    // Fetch contacts with new page immediately using V2
    fetchContacts(newFilters);
    
    // Update URL with new page
    const params = new URLSearchParams();
    
    // Add all V2 filter parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','));
        } else if (typeof value === 'boolean') {
          params.set(key, value.toString());
        } else if (typeof value === 'number' || typeof value === 'string') {
          params.set(key, value.toString());
        }
      }
    });
    
    // Preserve tab parameter and add filter parameters
    params.set('tab', 'contacts');
    
    // Update URL without triggering a page reload
    const newUrl = `/dashboard/entity?${params.toString()}`;
    router.replace(newUrl, { scroll: false });
  };

  // Handle pagination size change (V2)
  const handlePaginationSizeChange = (newLimit: number) => {
    const newFilters = { ...filters, limit: newLimit, page: 1 }; // Reset to page 1 when changing size
    setFilters(newFilters);
    
    // Fetch contacts with new limit immediately using V2
    fetchContacts(newFilters);
    
    // Update URL with new limit
    const params = new URLSearchParams();
    
    // Add all V2 filter parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','));
        } else if (typeof value === 'boolean') {
          params.set(key, value.toString());
        } else if (typeof value === 'number' || typeof value === 'string') {
          params.set(key, value.toString());
        }
      }
    });
    
    // Preserve tab parameter and add filter parameters
    params.set('tab', 'contacts');
    
    // Update URL without triggering a page reload
    const newUrl = `/dashboard/entity?${params.toString()}`;
    router.replace(newUrl, { scroll: false });
  };

  // Handle contact selection
  const handleSelectContact = (contactId: number) => {
    router.push(`/dashboard/people/${contactId}`);
  };

  // Handle contact selection toggle
  const handleToggleSelection = (contactId: number, event: React.MouseEvent | React.ChangeEvent) => {
    event.stopPropagation();
    setSelectedContacts(prev => {
      const newSet = new Set(prev);
      if (newSet.has(contactId)) {
        newSet.delete(contactId);
      } else {
        newSet.add(contactId);
      }
      return newSet;
    });
  };

  // Handle select all toggle
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedContacts(new Set());
      setSelectAll(false);
    } else {
      const allContactIds = contacts.map(contact => contact.contact_id).filter((id): id is number => id !== undefined);
      setSelectedContacts(new Set(allContactIds));
      setSelectAll(true);
    }
  };

  // Handle contact deletion
  const handleDeleteContact = async (contactId: number) => {
    try {
      const response = await fetch(`/api/contacts?contactId=${contactId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Remove from contacts list
        setContacts(prev => prev.filter(contact => contact.contact_id !== contactId));
        // Remove from selected contacts
        setSelectedContacts(prev => {
          const newSelected = new Set(prev);
          newSelected.delete(contactId);
          return newSelected;
        });
        // Update total count
        setTotalContacts(prev => prev - 1);
        toast({
          title: "Contact deleted",
          description: "The contact has been successfully deleted.",
        });
      } else {
        throw new Error('Failed to delete contact');
      }
    } catch (error) {
      console.error('Error deleting contact:', error);
      toast({
        title: "Error",
        description: "Failed to delete contact. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Update selectAll state based on current selection
  useEffect(() => {
    if (contacts.length === 0) {
      setSelectAll(false);
      return;
    }
    
    const allContactIds = contacts.map(contact => contact.contact_id).filter((id): id is number => id !== undefined);
    const allSelected = allContactIds.every(id => selectedContacts.has(id));
    setSelectAll(allSelected);
  }, [selectedContacts, contacts]);

  // Handle processing operations
  const executeProcessingJob = async (stage: ContactProcessingStage) => {
    if (selectedContacts.size === 0) return;

    const contactIds = Array.from(selectedContacts);
    
    // Update processing state
    setProcessingJobs(prev => 
      prev.map(job => 
        job.stage === stage 
          ? { ...job, isExecuting: true }
          : job
      )
    );

    try {
      // Handle Smartlead sync differently from other processing stages
      if (stage === 'smartlead_sync') {
        if (!selectedCampaignId) {
          toast({
            title: "Campaign Required",
            description: "Please select a campaign for Smartlead sync",
            variant: "destructive",
          });
          return;
        }

        const response = await fetch('/api/smartlead/contacts/batch-sync', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            contactIds,
            campaignId: selectedCampaignId
          })
        });

        const data = await response.json();
        if (data.success) {
          toast({
            title: "Smartlead Sync Started",
            description: `Successfully synced ${data.syncedCount || contactIds.length} contact${contactIds.length !== 1 ? 's' : ''} to Smartlead campaign`,
          });
          // Refresh contacts after a short delay to show updated status
          setTimeout(() => {
            const fetchContacts = async () => {
              try {
                const params = new URLSearchParams();
                Object.entries(filters).forEach(([key, value]) => {
                  if (value !== undefined && value !== null && value !== '') {
                    if (Array.isArray(value) && value.length > 0) {
                        params.append(key, value.join(','));
                    } else if (typeof value === 'boolean') {
                      params.append(key, value.toString());
                    } else if (typeof value === 'number' || typeof value === 'string') {
                      params.append(key, value.toString());
                    }
                  }
                });
                const apiUrl = `/api/contacts/unified-filters-v2?${params.toString()}`;
                const response = await fetch(apiUrl);
                if (response.ok) {
                  const data = await response.json();
                  setContacts(data.data || []);
                }
              } catch (error) {
                console.error("Error refreshing contacts:", error);
              }
            };
            fetchContacts();
          }, 2000);
        } else {
          console.error(`Failed to sync to Smartlead:`, data.error);
          toast({
            title: "Smartlead Sync Failed",
            description: `Failed to sync to Smartlead: ${data.error || 'Unknown error'}`,
            variant: "destructive",
          });
        }
      } else {
        // Handle other processing stages through the processing trigger API
        const jobOptions: any = {
          multiIds: contactIds,
          filters: buildFilters()
        }

        // Add campaign ID for email generation stage
        if (stage === 'email_generation' && selectedCampaignId) {
          jobOptions.campaignId = selectedCampaignId
        }

        const response = await fetch('/api/processing/trigger', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'execute_manual',
            stage,
            options: jobOptions
          })
        });

        const data = await response.json();
        if (data.success) {
          // Refresh contacts to show updated status
          toast({
            title: "Processing Started",
            description: `Successfully triggered ${getStageConfig(stage).title} for ${contactIds.length} contact${contactIds.length !== 1 ? 's' : ''}`,
          });
          // Refresh contacts after a short delay to show updated status
          setTimeout(() => {
            const fetchContacts = async () => {
              try {
                const params = new URLSearchParams();
                Object.entries(filters).forEach(([key, value]) => {
                  if (value !== undefined && value !== null && value !== '') {
                    if (Array.isArray(value) && value.length > 0) {
                        params.append(key, value.join(','));
                    } else if (typeof value === 'boolean') {
                      params.append(key, value.toString());
                    } else if (typeof value === 'number' || typeof value === 'string') {
                      params.append(key, value.toString());
                    }
                  }
                });
                const apiUrl = `/api/contacts/unified-filters-v2?${params.toString()}`;
                const response = await fetch(apiUrl);
                if (response.ok) {
                  const data = await response.json();
                  setContacts(data.data || []);
                }
              } catch (error) {
                console.error("Error refreshing contacts:", error);
              }
            };
            fetchContacts();
          }, 2000); // Wait 2 seconds for processing to start
        } else {
          console.error(`Failed to trigger ${stage}:`, data.error);
          toast({
            title: "Processing Failed",
            description: `Failed to trigger ${getStageConfig(stage).title}: ${data.error}`,
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error(`Error executing ${stage}:`, error);
      toast({
        title: "Processing Error",
        description: `Error executing ${getStageConfig(stage).title}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      // Reset processing state
      setProcessingJobs(prev => 
        prev.map(job => 
          job.stage === stage 
            ? { ...job, isExecuting: false }
            : job
        )
      );
    }
  };

  // Build filters for processing
  const buildFilters = () => {
    const filterObject: any = {};
    
    // Basic filters
    if (filters.source && filters.source.length > 0) filterObject.source = filters.source;
    
    // Contact processing status filters
    if (filters.emailVerificationStatus && filters.emailVerificationStatus.length > 0) filterObject.contact_email_verification_status = filters.emailVerificationStatus;
    if (filters.emailGenerationStatus && filters.emailGenerationStatus.length > 0) filterObject.email_generation_status = filters.emailGenerationStatus;
    if (filters.emailSendingStatus && filters.emailSendingStatus.length > 0) filterObject.email_sending_status = filters.emailSendingStatus;
    
    // Contact-specific filters
    if (filters.emailStatus && filters.emailStatus.length > 0) filterObject.emailStatus = filters.emailStatus;
    if (filters.jobTier && filters.jobTier.length > 0) filterObject.jobTier = filters.jobTier;
    
    // Geographic filters
    if (filters.contactCountries && filters.contactCountries.length > 0) filterObject.contactCountries = filters.contactCountries;
    if (filters.contactStates && filters.contactStates.length > 0) filterObject.contactStates = filters.contactStates;
    if (filters.contactCities && filters.contactCities.length > 0) filterObject.contactCities = filters.contactCities;
    if (filters.regions && filters.regions.length > 0) filterObject.regions = filters.regions;
    if (filters.states && filters.states.length > 0) filterObject.states = filters.states;
    if (filters.cities && filters.cities.length > 0) filterObject.cities = filters.cities;
    if (filters.countries && filters.countries.length > 0) filterObject.countries = filters.countries;
    
    // Investment criteria filters
    if (filters.capitalPosition && filters.capitalPosition.length > 0) filterObject.capitalPosition = filters.capitalPosition;
    if (filters.propertyTypes && filters.propertyTypes.length > 0) filterObject.propertyTypes = filters.propertyTypes;
    if (filters.strategies && filters.strategies.length > 0) filterObject.strategies = filters.strategies;
    if (filters.dealSizeMin !== undefined) filterObject.dealSizeMin = filters.dealSizeMin;
    if (filters.dealSizeMax !== undefined) filterObject.dealSizeMax = filters.dealSizeMax;
    if (filters.targetReturnMin !== undefined) filterObject.targetReturnMin = filters.targetReturnMin;
    if (filters.targetReturnMax !== undefined) filterObject.targetReturnMax = filters.targetReturnMax;
    
    // Boolean flags
    if (filters.extracted !== undefined) filterObject.extracted = filters.extracted;
    if (filters.searched !== undefined) filterObject.searched = filters.searched;
    if (filters.emailGenerated !== undefined) filterObject.emailGenerated = filters.emailGenerated;
    if (filters.enriched !== undefined) filterObject.enriched = filters.enriched;
    if (filters.hasSmartleadId !== undefined) filterObject.hasSmartleadId = filters.hasSmartleadId;
    if (filters.hasBeenReachedOut !== undefined) filterObject.hasBeenReachedOut = filters.hasBeenReachedOut;
    
    return filterObject;
  };

  // Get stage configuration
  const getStageConfig = (stage: ContactProcessingStage) => {
    const configs = {
      email_validation: {
        title: 'Email Validation',
        icon: Mail,
        color: 'bg-blue-500',
        description: 'Verify email addresses'
      },
      contact_enrichment: {
        title: 'Contact Enrichment',
        icon: Sparkles,
        color: 'bg-purple-500',
        description: 'Enrich contact data'
      },
      contact_enrichment_v2: {
        title: 'Contact Enrichment V2',
        icon: Sparkles,
        color: 'bg-purple-600',
        description: 'Enhanced contact enrichment with additional fields'
      },
      contact_investment_criteria: {
        title: 'Investment Criteria',
        icon: AlertTriangle,
        color: 'bg-rose-500',
        description: 'Extract personalized investment criteria'
      },
      email_generation: {
        title: 'Email Generation',
        icon: PenTool,
        color: 'bg-pink-500',
        description: 'Generate personalized emails'
      },
      smartlead_sync: {
        title: 'Smartlead Sync',
        icon: Send,
        color: 'bg-green-500',
        description: 'Sync Smartlead IDs'
      }
    };
    return configs[stage];
  };

  // Get processing status for a contact
  const getContactProcessingStatus = (contact: UnifiedContactData, stage: ContactProcessingStage) => {
    switch (stage) {
      case 'email_validation':
        return contact.email_verification_status;
      case 'contact_enrichment_v2':
        return contact.contact_enrichment_v2_status || 'pending';
      case 'contact_investment_criteria':
        return contact.contact_investment_criteria_status || 'pending';
      case 'email_generation':
        return contact.email_generation_status;
      case 'smartlead_sync':
        // For Smartlead sync, we check if the contact has a smartlead_lead_id
        return contact.smartlead_lead_id ? 'completed' : 'pending';
      default:
        return null;
    }
  };

  // Get status summary for selected contacts
  const getSelectedContactsStatusSummary = (stage: ContactProcessingStage) => {
    if (selectedContacts.size === 0) return null;

    const statusCounts: { [key: string]: number } = {};
    let total = 0;

    contacts.forEach(contact => {
      if (contact.contact_id && selectedContacts.has(contact.contact_id)) {
        const status = getContactProcessingStatus(contact, stage);
        if (status) {
          statusCounts[status] = (statusCounts[status] || 0) + 1;
        }
        total++;
      }
    });

    return { statusCounts, total };
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="w-full px-6 py-6">
        <header className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Contacts</h1>
            <p className="text-sm text-gray-500 mt-1">
              Manage and explore your contact database
            </p>
          </div>
          <Button
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white"
            onClick={handleAddContact}
          >
            <UserPlus className="h-4 w-4" />
            Add Contact
          </Button>
        </header>

        {/* V2 Contact Filters Only */}
        <ContactUnifiedFiltersV2Component
          filters={filters}
          mappings={mappings}
          onFiltersChange={handleFiltersChange}
          onClearFilters={handleClearFilters}
          isLoading={loading}
        />

        {/* Selection and Processing Controls */}
        {contacts.length > 0 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={() => handleSelectAll()}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium">
                      {selectedContacts.size > 0 
                        ? `${selectedContacts.size} contact${selectedContacts.size !== 1 ? 's' : ''} selected`
                        : 'Select contacts for processing'
                      }
                    </span>
                  </div>
                  {selectedContacts.size > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedContacts(new Set())}
                    >
                      Clear Selection
                    </Button>
                  )}
                </div>
              </CardTitle>
            </CardHeader>
            
            {selectedContacts.size > 0 && (
              <CardContent>
                <div className="space-y-4">
                  <div className="text-sm text-gray-600">
                    Processing operations for {selectedContacts.size} selected contact{selectedContacts.size !== 1 ? 's' : ''}
                  </div>
                  
                  {/* Campaign Selection for Smartlead Sync and Email Generation */}
                  <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Send className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-800">Campaign Selection</span>
                    </div>
                    <Select
                      value={selectedCampaignId}
                      onValueChange={setSelectedCampaignId}
                      disabled={loadingCampaigns}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder={loadingCampaigns ? "Loading campaigns..." : "Select campaign for Smartlead sync and Email Generation"} />
                      </SelectTrigger>
                      <SelectContent>
                        {campaigns.map(campaign => (
                          <SelectItem key={campaign.id} value={campaign.id}>
                            {campaign.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {selectedCampaignId && (
                      <p className="text-xs text-green-600 mt-1">
                        Campaign selected: {campaigns.find(c => c.id === selectedCampaignId)?.name}
                      </p>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-5 gap-4">
                    {processingJobs.map((job) => {
                      const config = getStageConfig(job.stage);
                      const IconComponent = config.icon;
                      const statusSummary = getSelectedContactsStatusSummary(job.stage);
                      
                      return (
                        <Card key={job.stage} className="border-l-4" style={{ borderLeftColor: config.color.replace('bg-', '') }}>
                          <CardContent className="p-4">
                            <div className="flex items-center gap-2 mb-3">
                              <IconComponent className="h-4 w-4" />
                              <h3 className="font-medium">{config.title}</h3>
                            </div>
                            
                            {statusSummary && job.stage !== 'smartlead_sync' && (
                              <div className="mb-3 text-xs text-gray-600">
                                <div className="flex flex-wrap gap-1 mb-2">
                                  {Object.entries(statusSummary.statusCounts).map(([status, count]) => (
                                    <Badge key={status} variant="outline" className="text-xs">
                                      {status}: {count}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            {job.stage === 'smartlead_sync' && (
                              <div className="mb-3 text-xs text-gray-600">
                                <p>Sync contacts to Smartlead campaign</p>
                                {!selectedCampaignId && (
                                  <p className="text-amber-600 mt-1">⚠️ Select campaign above</p>
                                )}
                              </div>
                            )}
                            
                            {job.stage === 'email_generation' && (
                              <div className="mb-3 text-xs text-gray-600">
                                <p>Generate emails and sync to Smartlead campaign</p>
                                {!selectedCampaignId && (
                                  <p className="text-amber-600 mt-1">⚠️ Select campaign above</p>
                                )}
                              </div>
                            )}
                            
                            <Button
                              onClick={() => executeProcessingJob(job.stage)}
                              disabled={job.isExecuting || ((job.stage === 'smartlead_sync' || job.stage === 'email_generation') && !selectedCampaignId)}
                              className="w-full"
                              size="sm"
                            >
                              {job.isExecuting ? (
                                <>
                                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                  Processing...
                                </>
                              ) : (
                                <>
                                  <Play className="h-4 w-4 mr-2" />
                                  Process {selectedContacts.size} Contact{selectedContacts.size !== 1 ? 's' : ''}
                                </>
                              )}
                            </Button>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        )}

        {/* Results Summary */}
          <div className="mb-4 flex items-center justify-between">
          <div className="text-sm text-gray-600 flex items-center">
            {loading ? (
              <span>Loading contacts...</span>
            ) : (
              <span>
                Showing {contacts.length} of {totalContacts.toLocaleString()} contacts
                {filters.searchTerm && ` matching "${filters.searchTerm}"`}
              </span>
            )}
          </div>
          <div className="flex items-center gap-4">
            <PaginationSizeSelector
              currentSize={filters.limit || 25}
              onSizeChange={handlePaginationSizeChange}
            />
            {totalPages > 1 && (
              <Pagination
                currentPage={filters.page || 1}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            )}
          </div>
        </div>

        {/* Contacts Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
          {loading ? (
            // Loading skeletons
            Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="h-12 w-12 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            ))
          ) : contacts.length === 0 ? (
            // No results
            <div className="col-span-full text-center py-12">
              <img 
                src="/api/placeholder/200/200" 
                alt="No contacts found" 
                className="mx-auto mb-4 opacity-50"
              />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No contacts found</h3>
              <p className="text-gray-500 mb-4">
                Try adjusting your filters or search terms to find contacts.
              </p>
              <Button
                onClick={handleClearFilters}
                variant="outline"
                className="mx-auto"
              >
                Clear all filters
              </Button>
            </div>
          ) : (
            // Contact cards
            contacts.map((contact) => (
              <ContactCard 
                key={contact.contact_id} 
                contact={contact} 
                onSelectContact={handleSelectContact}
                isSelected={contact.contact_id ? selectedContacts.has(contact.contact_id) : false}
                onToggleSelection={handleToggleSelection}
                onDeleteContact={handleDeleteContact}
                showDeleteButton={true}
              />
            ))
          )}
        </div>


      </main>
    </div>
  );
}
