"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "sonner"
import { ArrowLeft, CheckCircle, Loader2, Building2, Users, Target } from "lucide-react"
import { ContactFormData, CompanySuggestion } from '../shared/types'
import { ContactSubmissionService } from './ContactSubmissionService'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import ContactFormFields from './ContactFormFields'
import InvestmentAdditionSection from './InvestmentAdditionSection'

interface AddContactWithCompanyProps {
  onBack: () => void;
  companyId?: string;
  preSelectedCompany?: CompanySuggestion;
  onSuccess?: (contactId: number, companyId?: number) => void;
}

interface CompanyFormData {
  company_name: string;
  company_type: string;
  industry: string;
  website: string;
  main_phone: string;
  main_email: string;
  linkedin: string;
  twitter: string;
  facebook: string;
  instagram: string;
  youtube: string;
  headquarters_address: string;
  headquarters_city: string;
  headquarters_state: string;
  headquarters_zipcode: string;
  headquarters_country: string;
}

const AddContactWithCompany: React.FC<AddContactWithCompanyProps> = ({ 
  onBack, 
  companyId, 
  preSelectedCompany,
  onSuccess
}) => {
  const [formData, setFormData] = useState<ContactFormData>(
    ContactSubmissionService.getInitialFormData(preSelectedCompany)
  );

  const [companyFormData, setCompanyFormData] = useState<CompanyFormData>({
    company_name: "",
    company_type: "",
    industry: "",
    website: "",
    main_phone: "",
    main_email: "",
    linkedin: "",
    twitter: "",
    facebook: "",
    instagram: "",
    youtube: "",
    headquarters_address: "",
    headquarters_city: "",
    headquarters_state: "",
    headquarters_zipcode: "",
    headquarters_country: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [investmentCriteria, setInvestmentCriteria] = useState<any[]>([]);
  const [createNewCompany, setCreateNewCompany] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<CompanySuggestion | null>(preSelectedCompany || null);

  const [filterOptions, setFilterOptions] = useState<{
    jobTiers: string[];
  }>({
    jobTiers: []
  });

  useEffect(() => {
    const fetchFilterOptions = async () => {
      const options = await ContactSubmissionService.fetchFilterOptions();
      setFilterOptions(options);
    };
    fetchFilterOptions();
  }, []);

  useEffect(() => {
    if (selectedCompany && !createNewCompany) {
      setCompanyFormData(prev => ({
        ...prev,
        company_name: selectedCompany.company_name,
        industry: selectedCompany.industry || '',
        website: selectedCompany.company_website || '',
        headquarters_address: selectedCompany.company_address || '',
        headquarters_city: selectedCompany.company_city || '',
        headquarters_state: selectedCompany.company_state || '',
        headquarters_country: selectedCompany.company_country || '',
        headquarters_zipcode: selectedCompany.company_zip || '',
      }));
    }
  }, [selectedCompany, createNewCompany]);

  const handleCompanyFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCompanyFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCompanyFormSelectChange = (name: string, value: string) => {
    setCompanyFormData(prev => ({ ...prev, [name]: value }));
  };

  const createCompany = async (): Promise<number | null> => {
    if (!createNewCompany || !companyFormData.company_name.trim()) {
      return null;
    }

    try {
      const response = await fetch('/api/companies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          company_name: companyFormData.company_name,
          company_type: companyFormData.company_type,
          industry: companyFormData.industry,
          company_website: companyFormData.website,
          company_phone: companyFormData.main_phone,
          main_email: companyFormData.main_email,
          company_linkedin: companyFormData.linkedin,
          twitter: companyFormData.twitter,
          facebook: companyFormData.facebook,
          instagram: companyFormData.instagram,
          youtube: companyFormData.youtube,
          company_address: companyFormData.headquarters_address,
          company_city: companyFormData.headquarters_city,
          company_state: companyFormData.headquarters_state,
          company_zip: companyFormData.headquarters_zipcode,
          company_country: companyFormData.headquarters_country,
        }),
      });

      if (!response.ok) throw new Error('Failed to create company');

      const data = await response.json();
      toast.success('Company created successfully');
      return data.company_id;
    } catch (error) {
      console.error('Error creating company:', error);
      toast.error('Failed to create company');
      return null;
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      let finalCompanyId = companyId ? parseInt(companyId) : null;
      
      if (createNewCompany) {
        const createdCompanyId = await createCompany();
        if (createdCompanyId) {
          finalCompanyId = createdCompanyId;
        } else {
          setIsSubmitting(false);
          return;
        }
      }

      const contactData = { ...formData, company_id: finalCompanyId };
      const response = await fetch('/api/contacts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(contactData),
      });

      if (!response.ok) throw new Error('Failed to create contact');

      const data = await response.json();
      const contactId = data.contact_id;

      if (investmentCriteria.length > 0) {
        for (const criteria of investmentCriteria) {
          await fetch('/api/investment-criteria', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              ...criteria,
              contact_id: contactId,
              company_id: finalCompanyId,
            }),
          });
        }
      }

      toast.success('Contact and company created successfully');
      if (onSuccess) onSuccess(contactId, finalCompanyId || undefined);
    } catch (error) {
      console.error('Error creating contact:', error);
      toast.error('Failed to create contact');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCompanyNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, company_name: value }));
    setCompanyFormData(prev => ({ ...prev, company_name: value }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="w-full p-6 space-y-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={onBack} className="hover:bg-slate-100 rounded-xl">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                  Add Contact & Company
                </h1>
                <p className="text-slate-600 mt-1">Create a new contact and optionally create a new company</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Button type="button" variant="outline" onClick={onBack} className="h-11 px-6 rounded-xl">
                Cancel
              </Button>
              <Button
                type="submit"
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="h-11 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Save Contact & Company
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        <form>
          <div className="grid gap-8 grid-cols-1 lg:grid-cols-2">
            {/* Contact Information */}
            <div className="space-y-6">
              <Card className="bg-white shadow-xl border-0 rounded-2xl">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
                  <CardTitle className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-xl mr-3">
                      <Users className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <div className="text-lg font-semibold text-slate-900">Contact Information</div>
                      <div className="text-sm text-slate-600">Personal and contact details</div>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <ContactFormFields
                    formData={formData}
                    onChange={(e) => {
                      const { name, value } = e.target;
                      setFormData(prev => ({ ...prev, [name]: value }));
                    }}
                    onSelectChange={(name, value) => {
                      setFormData(prev => ({ ...prev, [name]: value }));
                    }}
                    validationStates={{
                      email: { isValidating: false, isDuplicate: false },
                      additional_email: { isValidating: false, isDuplicate: false },
                      linkedin_url: { isValidating: false, isDuplicate: false },
                      full_name: { isValidating: false, isDuplicate: false }
                    }}
                    filterOptions={filterOptions}
                    companyId={companyId}
                    selectedCompany={selectedCompany}
                    showEmailSuggestions={false}
                    emailSuggestions={[]}
                    selectedEmailSuggestionIndex={-1}
                    isSearchingEmail={false}
                    activeEmailField={null}
                    onEmailKeyDown={() => {}}
                    onEmailContactSelect={() => {}}
                    emailSuggestionsRef={React.useRef<HTMLDivElement>(null)}
                  />
                </CardContent>
              </Card>
            </div>

            {/* Company Information */}
            <div className="space-y-6">
              <Card className="bg-white shadow-xl border-0 rounded-2xl">
                <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-100">
                  <CardTitle className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-xl mr-3">
                      <Building2 className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <div className="text-lg font-semibold text-slate-900">Company Information</div>
                      <div className="text-sm text-slate-600">Select existing or create new company</div>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="company_name" className="text-sm font-medium text-gray-700">
                        Company Name *
                      </Label>
                      <Input
                        id="company_name"
                        name="company_name"
                        value={formData.company_name}
                        onChange={handleCompanyNameChange}
                        placeholder="Enter company name"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        required
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="company_type" className="text-sm font-medium text-gray-700">
                          Company Type
                        </Label>
                        <Input
                          id="company_type"
                          name="company_type"
                          value={companyFormData.company_type}
                          onChange={handleCompanyFormChange}
                          placeholder="Enter company type"
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="industry" className="text-sm font-medium text-gray-700">
                          Industry
                        </Label>
                        <Input
                          id="industry"
                          name="industry"
                          value={companyFormData.industry}
                          onChange={handleCompanyFormChange}
                          placeholder="e.g., Technology, Real Estate"
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        id="create_new_company"
                        type="checkbox"
                        checked={createNewCompany}
                        onChange={(e) => setCreateNewCompany(e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <Label htmlFor="create_new_company" className="text-sm font-medium text-gray-700">
                        Create new company with additional details
                      </Label>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Company Contact Information */}
              {createNewCompany && (
                <Card className="bg-white shadow-xl border-0 rounded-2xl">
                  <CardHeader className="bg-gradient-to-r from-purple-50 to-violet-50 border-b border-purple-100">
                    <CardTitle className="flex items-center">
                      <div className="p-2 bg-purple-100 rounded-xl mr-3">
                        <Building2 className="h-5 w-5 text-purple-600" />
                      </div>
                      <div>
                        <div className="text-lg font-semibold text-slate-900">Company Contact Information</div>
                        <div className="text-sm text-slate-600">Contact details for the new company</div>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="website" className="text-sm font-medium text-gray-700">Website</Label>
                        <Input
                          id="website"
                          name="website"
                          value={companyFormData.website}
                          onChange={handleCompanyFormChange}
                          placeholder="https://example.com"
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="main_phone" className="text-sm font-medium text-gray-700">Main Phone</Label>
                        <Input
                          id="main_phone"
                          name="main_phone"
                          value={companyFormData.main_phone}
                          onChange={handleCompanyFormChange}
                          placeholder="+****************"
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="main_email" className="text-sm font-medium text-gray-700">Main Email</Label>
                        <Input
                          id="main_email"
                          name="main_email"
                          type="email"
                          value={companyFormData.main_email}
                          onChange={handleCompanyFormChange}
                          placeholder="<EMAIL>"
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="linkedin" className="text-sm font-medium text-gray-700">LinkedIn</Label>
                        <Input
                          id="linkedin"
                          name="linkedin"
                          value={companyFormData.linkedin}
                          onChange={handleCompanyFormChange}
                          placeholder="https://linkedin.com/company/..."
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="twitter" className="text-sm font-medium text-gray-700">Twitter</Label>
                        <Input
                          id="twitter"
                          name="twitter"
                          value={companyFormData.twitter}
                          onChange={handleCompanyFormChange}
                          placeholder="https://twitter.com/..."
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="facebook" className="text-sm font-medium text-gray-700">Facebook</Label>
                        <Input
                          id="facebook"
                          name="facebook"
                          value={companyFormData.facebook}
                          onChange={handleCompanyFormChange}
                          placeholder="https://facebook.com/..."
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Company Location */}
              {createNewCompany && (
                <Card className="bg-white shadow-xl border-0 rounded-2xl">
                  <CardHeader className="bg-gradient-to-r from-orange-50 to-red-50 border-b border-orange-100">
                    <CardTitle className="flex items-center">
                      <div className="p-2 bg-orange-100 rounded-xl mr-3">
                        <Building2 className="h-5 w-5 text-orange-600" />
                      </div>
                      <div>
                        <div className="text-lg font-semibold text-slate-900">Company Location</div>
                        <div className="text-sm text-slate-600">Headquarters address</div>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="headquarters_address" className="text-sm font-medium text-gray-700">Address</Label>
                        <Input
                          id="headquarters_address"
                          name="headquarters_address"
                          value={companyFormData.headquarters_address}
                          onChange={handleCompanyFormChange}
                          placeholder="123 Main Street"
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="headquarters_city" className="text-sm font-medium text-gray-700">City</Label>
                        <Input
                          id="headquarters_city"
                          name="headquarters_city"
                          value={companyFormData.headquarters_city}
                          onChange={handleCompanyFormChange}
                          placeholder="San Francisco"
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="headquarters_state" className="text-sm font-medium text-gray-700">State</Label>
                        <Input
                          id="headquarters_state"
                          name="headquarters_state"
                          value={companyFormData.headquarters_state}
                          onChange={handleCompanyFormChange}
                          placeholder="CA"
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="headquarters_zipcode" className="text-sm font-medium text-gray-700">ZIP Code</Label>
                        <Input
                          id="headquarters_zipcode"
                          name="headquarters_zipcode"
                          value={companyFormData.headquarters_zipcode}
                          onChange={handleCompanyFormChange}
                          placeholder="94105"
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="headquarters_country" className="text-sm font-medium text-gray-700">Country</Label>
                        <Input
                          id="headquarters_country"
                          name="headquarters_country"
                          value={companyFormData.headquarters_country}
                          onChange={handleCompanyFormChange}
                          placeholder="United States"
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>

          {/* Investment Criteria Section */}
          <div className="mt-8">
            <Card className="bg-white shadow-xl border-0 rounded-2xl">
              <CardHeader className="bg-gradient-to-r from-indigo-50 to-purple-50 border-b border-indigo-100">
                <CardTitle className="flex items-center">
                  <div className="p-2 bg-indigo-100 rounded-xl mr-3">
                    <Target className="h-5 w-5 text-indigo-600" />
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-slate-900">Investment Criteria</div>
                    <div className="text-sm text-slate-600">Define investment criteria for contact and company</div>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <InvestmentAdditionSection
                  onInvestmentCriteriaChange={setInvestmentCriteria}
                  selectedCompanyIC={[]}
                />
              </CardContent>
            </Card>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddContactWithCompany;
