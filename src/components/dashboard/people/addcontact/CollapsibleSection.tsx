"use client"

import React from 'react';
import { ChevronDown, ChevronRight } from "lucide-react";

interface CollapsibleSectionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  isOpen: boolean;
  onToggle: () => void;
  children: React.ReactNode;
  bgColor?: string;
  iconColor?: string;
}

const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  description,
  icon,
  isOpen,
  onToggle,
  children,
  bgColor = "bg-blue-100",
  iconColor = "text-blue-600"
}) => {
  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100">
      <button
        type="button"
        onClick={onToggle}
        className="w-full p-6 flex items-center justify-between hover:bg-gray-50 transition-colors"
      >
        <div className="flex items-center">
          <div className={`w-10 h-10 ${bgColor} rounded-full flex items-center justify-center mr-4`}>
            <div className={iconColor}>
              {icon}
            </div>
          </div>
          <div className="text-left">
            <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
            <p className="text-sm text-gray-500">{description}</p>
          </div>
        </div>
        {isOpen ? (
          <ChevronDown className="h-6 w-6 text-gray-400" />
        ) : (
          <ChevronRight className="h-6 w-6 text-gray-400" />
        )}
      </button>
      {isOpen && (
        <div className="px-6 pb-6">
          {children}
        </div>
      )}
    </div>
  );
};

export default CollapsibleSection;
