"use client"

import React from 'react';

// Import types
import { ContactCampaignTagProps } from './types';

// Import sections
import UnifiedCampaignSequenceSectionWithDeals from './sections/UnifiedCampaignSequenceSection';

// Add highlight animation style
const highlightStyles = `
  @keyframes pulse-highlight {
    0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
    100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
  }
  
  .highlight-message {
    animation: pulse-highlight 2s infinite;
    border: 2px solid #3b82f6 !important;
  }
  
  /* Styles for email content rendering */
  .email-content {
    line-height: 1.5;
  }
  
  .email-content p {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }
  
  .email-content ul {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
    padding-left: 2em;
  }
  
  .email-content li {
    margin-bottom: 0.25em;
  }
  
  .email-content a {
    color: #3b82f6;
    text-decoration: underline;
  }
  
  .email-content * {
    max-width: 100%;
  }
`;

const ContactCampaignTag: React.FC<ContactCampaignTagProps> = ({ contactId, contact, normalizedScrapedData }) => {
  return (
    <div className="space-y-6">
      {/* Include styles */}
      <style dangerouslySetInnerHTML={{ __html: highlightStyles }} />
      
      {/* Unified Campaign Sequence & Messages Section */}
      <UnifiedCampaignSequenceSectionWithDeals
        contactId={contactId}
        contact={contact}
        onContactUpdate={(updatedContact) => {
          // Handle contact updates if needed
          console.log('Contact updated:', updatedContact);
        }}
      />
    </div>
  );
};

export default ContactCampaignTag;