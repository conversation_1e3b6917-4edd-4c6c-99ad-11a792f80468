import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Search, Loader2, CheckCircle, X, ExternalLink, Mail, User, Building2 } from "lucide-react";
import { debounce } from 'lodash';
import Link from 'next/link';
import { Contact } from './types';
import { toast } from 'sonner';

interface EmailContactSearchProps {
  onContactSelect?: (contact: Contact) => void;
  placeholder?: string;
  showResults?: boolean;
  className?: string;
  label?: string;
  required?: boolean;
}

interface ContactSearchResult extends Contact {
  match_type?: 'email' | 'partial_email' | 'name' | 'company';
  match_score?: number;
}

export const EmailContactSearch: React.FC<EmailContactSearchProps> = ({
  onContactSelect,
  placeholder = "Search by email address...",
  showResults = true,
  className = "",
  label = "Email Search",
  required = false
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<ContactSearchResult[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  const searchInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Debounced search function
  const debouncedSearchContacts = useCallback(
    debounce(async (query: string) => {
      if (query.length < 2) {
        setSearchResults([]);
        setShowSuggestions(false);
        setIsSearching(false);
        return;
      }

      setIsSearching(true);
      try {
        // First try exact email search
        if (query.includes('@')) {
          const exactEmailResponse = await fetch(`/api/contacts/search-by-email?email=${encodeURIComponent(query)}`);
          if (exactEmailResponse.ok) {
            const exactResult = await exactEmailResponse.json();
            if (exactResult.contacts && exactResult.contacts.length > 0) {
              const contactsWithMatchType = exactResult.contacts.map((contact: Contact) => ({
                ...contact,
                match_type: 'email' as const,
                match_score: 1.0
              }));
              setSearchResults(contactsWithMatchType);
              setShowSuggestions(true);
              setIsSearching(false);
              return;
            }
          }
        }

        // If no exact match, try general search
        const generalResponse = await fetch(`/api/contacts/search-deals?q=${encodeURIComponent(query)}&limit=10`);
        if (generalResponse.ok) {
          const generalResults = await generalResponse.json();
          
          // Score and categorize results
          const scoredResults = generalResults.map((contact: Contact) => {
            let matchType: 'email' | 'partial_email' | 'name' | 'company' = 'name';
            let matchScore = 0.5;

            const queryLower = query.toLowerCase();
            const emailLower = (contact.email || '').toLowerCase();
            const additionalEmailLower = (contact.additional_email || '').toLowerCase();
            const fullName = `${contact.first_name || ''} ${contact.last_name || ''}`.toLowerCase();
            const companyName = (contact.company_name || '').toLowerCase();

            if (emailLower.includes(queryLower) || additionalEmailLower.includes(queryLower)) {
              matchType = 'partial_email';
              matchScore = 0.8;
            } else if (fullName.includes(queryLower)) {
              matchType = 'name';
              matchScore = 0.6;
            } else if (companyName.includes(queryLower)) {
              matchType = 'company';
              matchScore = 0.4;
            }

            return {
              ...contact,
              match_type: matchType,
              match_score: matchScore
            };
          });

          // Sort by match score
          scoredResults.sort((a: ContactSearchResult, b: ContactSearchResult) => (b.match_score || 0) - (a.match_score || 0));
          
          setSearchResults(scoredResults);
          setShowSuggestions(scoredResults.length > 0);
        }
      } catch (error) {
        console.error('Error searching contacts:', error);
        toast.error('Failed to search contacts. Please try again.');
        setSearchResults([]);
        setShowSuggestions(false);
      } finally {
        setIsSearching(false);
      }
    }, 300),
    []
  );

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    setSelectedIndex(-1);
    
    if (value.trim()) {
      debouncedSearchContacts(value);
    } else {
      setSearchResults([]);
      setShowSuggestions(false);
    }
  };

  // Handle contact selection
  const handleContactSelect = (contact: ContactSearchResult) => {
    setSearchTerm(`${contact.first_name || ''} ${contact.last_name || ''}`.trim() || contact.email || '');
    setShowSuggestions(false);
    setSelectedIndex(-1);
    
    if (onContactSelect) {
      onContactSelect(contact);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || searchResults.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex((prev) => 
          prev < searchResults.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex((prev) => 
          prev > 0 ? prev - 1 : searchResults.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          handleContactSelect(searchResults[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  // Click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchInputRef.current && !searchInputRef.current.contains(event.target as Node)) {
        const suggestions = document.querySelector('[data-contact-search-results]');
        if (suggestions && suggestions.contains(event.target as Node)) {
          return;
        }
        setShowSuggestions(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Get match type display info
  const getMatchTypeInfo = (matchType: string) => {
    switch (matchType) {
      case 'email':
        return { icon: <Mail className="h-3 w-3" />, color: 'text-green-600', label: 'Email match' };
      case 'partial_email':
        return { icon: <Mail className="h-3 w-3" />, color: 'text-blue-600', label: 'Email contains' };
      case 'name':
        return { icon: <User className="h-3 w-3" />, color: 'text-purple-600', label: 'Name match' };
      case 'company':
        return { icon: <Building2 className="h-3 w-3" />, color: 'text-orange-600', label: 'Company match' };
      default:
        return { icon: <Search className="h-3 w-3" />, color: 'text-gray-600', label: 'Match' };
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <Label htmlFor="email-contact-search" className="text-sm font-medium text-slate-700">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
      )}
      
      <div className="relative">
        <Input
          ref={searchInputRef}
          id="email-contact-search"
          type="text"
          value={searchTerm}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 border-slate-200 focus:border-blue-500 hover:border-slate-300"
        />
        
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          {isSearching ? (
            <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
          ) : (
            <Search className="h-4 w-4 text-gray-400" />
          )}
        </div>
      </div>

      {/* Search Results */}
      {showSuggestions && showResults && (
        <div className="space-y-2">
          <Label className="text-sm font-medium text-slate-700">
            Search Results ({searchResults.length} found)
          </Label>
          <div className="space-y-2 max-h-80 overflow-y-auto border rounded-xl p-2 bg-slate-50" data-contact-search-results>
            {searchResults.map((contact, index) => {
              const matchInfo = getMatchTypeInfo(contact.match_type || 'name');
              
              return (
                <div
                  key={contact.contact_id}
                  className={`p-3 rounded-lg cursor-pointer border transition-all duration-200 ${
                    index === selectedIndex 
                      ? 'bg-blue-50 border-blue-300 shadow-sm' 
                      : 'bg-white border-slate-200 hover:bg-slate-50 hover:border-slate-300'
                  }`}
                  onClick={() => handleContactSelect(contact)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 text-sm flex items-center">
                        {contact.first_name} {contact.last_name}
                        {contact.contact_id && (
                          <Link 
                            href={`/dashboard/people/${contact.contact_id}`}
                            className="ml-2 p-1 rounded-full hover:bg-blue-100 transition-colors"
                            onClick={(e) => e.stopPropagation()}
                            title="View contact details"
                          >
                            <ExternalLink className="h-3 w-3 text-blue-600" />
                          </Link>
                        )}
                      </div>
                      
                      {contact.title && (
                        <div className="text-xs text-gray-600 mt-1">{contact.title}</div>
                      )}
                      
                      {contact.company_name && (
                        <div className="text-xs text-gray-600 flex items-center mt-1">
                          <Building2 className="h-3 w-3 mr-1" />
                          {contact.company_name}
                        </div>
                      )}
                      
                      {contact.email && (
                        <div className="text-xs text-blue-600 flex items-center mt-1">
                          <Mail className="h-3 w-3 mr-1" />
                          {contact.email}
                        </div>
                      )}
                    </div>
                    
                    <div className="ml-3 flex flex-col items-end">
                      <div className={`text-xs px-2 py-1 rounded-full bg-gray-100 flex items-center ${matchInfo.color}`}>
                        {matchInfo.icon}
                        <span className="ml-1">{matchInfo.label}</span>
                      </div>
                      
                      {contact.match_score && (
                        <div className="text-xs text-gray-500 mt-1">
                          {Math.round(contact.match_score * 100)}% match
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
            
            {searchResults.length === 0 && !isSearching && searchTerm.length >= 2 && (
              <div className="p-4 text-center border rounded-xl bg-slate-50">
                <div className="text-sm text-gray-600">
                  <Search className="h-4 w-4 mx-auto mb-2 text-gray-400" />
                  No contacts found matching "{searchTerm}"
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Try searching by email address, name, or company
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
