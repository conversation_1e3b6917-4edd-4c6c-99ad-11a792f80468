// Shared types for contact components
// Based on the actual database schema from contacts table

export interface Contact {
  // Core identifiers
  contact_id?: number;
  company_id?: number;
  
  // Personal Information
  first_name?: string;
  last_name?: string;
  full_name?: string;
  title?: string; // job title
  job_tier?: string;
  headline?: string;
  seniority?: string;
  executive_summary?: string;
  career_timeline?: any[]; // JSON array
  
  // Contact Information  
  email?: string;
  additional_email?: string;
  personal_email?: string;
  email_status?: string;
  phone_number?: string;
  phone_number_secondary?: string;
  
  // Social Media
  linkedin_url?: string;
  twitter?: string;
  facebook?: string;
  instagram?: string;
  youtube?: string;
  
  // Education
  education_college?: string;
  education_college_year_graduated?: string;
  education_high_school?: string;
  education_high_school_year_graduated?: string;
  
  // Personal Details
  age?: string;
  honorable_achievements?: string[]; // JSON array
  hobbies?: string[]; // JSON array
  
  // Contact Location
  contact_address?: string;
  contact_city?: string;
  contact_state?: string;
  contact_zip_code?: string;
  contact_country?: string;
  region?: string;
  
  // Professional Details
  contact_type?: string;
  relationship_owner?: string;
  role_in_decision_making?: string;
  last_contact_date?: string;
  source_of_introduction?: string;
  accredited_investor_status?: boolean;
  kyc_status?: string;
  
  // Company Information
  company_name?: string;
  company_type?: string;
  
  // Investment Preferences
  capital_type?: string;
  capital_position?: string[];
  investment_criteria_country?: string;
  investment_criteria_state?: string;
  investment_criteria_city?: string;
  investment_criteria_property_type?: string;
  investment_criteria_asset_type?: string;
  investment_criteria_loan_type?: string;
  investment_criteria_deal_size?: string;
  
  // Processing and Status Fields
  processing_state?: string;
  email_verification_status?: string;
  email_verification_date?: string;
  email_verification_error?: string;
  osint_status?: string;
  osint_date?: string;
  osint_error?: string;
  overview_extraction_status?: string;
  overview_extraction_date?: string;
  overview_extraction_error?: string;
  classification_status?: string;
  classification_date?: string;
  classification_error?: string;
  email_generation_status?: string;
  email_generation_date?: string;
  email_generation_error?: string;
  email_sending_status?: string;
  email_sending_date?: string;
  email_sending_error?: string;
  contact_enrichment_status?: string;
  contact_enrichment_date?: string;
  contact_enrichment_error?: string;
  contact_enrichment_v2_status?: string;
  contact_enrichment_v2_date?: string;
  contact_enrichment_v2_error?: string;
  last_processed_stage?: string;
  last_processed_at?: string;
  processing_error_count?: number;
  processing_attempts?: any;
  
  // Campaign and Email Management
  smartlead_lead_id?: string;
  smartlead_status?: string;
  last_email_sent_at?: string;
  email_batch_identifier?: string;
  email_generated?: boolean;
  email_sent_error?: string;
  email_sent_date?: string;
  
  // Conflicts
  conflicts?: any;
  conflict_status?: string;
  conflict_created_at?: string;
  conflict_resolved_at?: string;
  conflict_source?: string;
  
  // Metadata
  category?: string;
  source?: string;
  notes?: string;
  extra_attrs?: any; // JSONB
  classification_confidence?: number;
  classification_reasoning?: string;
  enriched?: boolean;
  extracted?: boolean;
  searched?: boolean;
  created_at?: string;
  updated_at?: string;
  email_validated_date?: string;
}

export interface InvestmentCriteria {
  id: string;
  capital_type: string;
  investment_criteria_country: string;
  investment_criteria_state: string;
  investment_criteria_city: string;
  investment_criteria_property_type: string;
  investment_criteria_asset_type: string;
  investment_criteria_deal_size: string;
  investment_criteria_loan_type: string;
  loan_term_months: string;
  interest_rate_index: string;
  origination_fee_percent: string;
  exit_fee_percent: string;
  recourse: string;
  closing_time_weeks: string;
  hold_period_years: string;
  expected_irr_percent: string;
  expected_equity_multiple: string;
}

export interface CompanySuggestion {
  company_id?: number;
  company_name: string;
  company_website?: string;
  industry?: string;
  company_address?: string;
  company_city?: string;
  company_state?: string;
  company_country?: string;
  company_zip?: string;
  company_phone?: string;
  company_email?: string;
  employee_count?: string;
  revenue_range?: string;
  investment_criteria_count?: number;
  // Extracted data for enhanced auto-fill
  extracted_data?: {
    companytype?: string;
    businessmodel?: string;
    fundsize?: string;
    aum?: string;
    headquarters?: string;
    foundedyear?: number;
    numberofemployees?: string;
    investmentfocus?: string[];
    geographicfocus?: string[];
    dealsize?: string;
    minimumdealsize?: string;
    maximumdealsize?: string;
    investment_criteria_property_types?: string[];
    investment_criteria_asset_types?: string[];
    investment_criteria_loan_types?: string[];
    investment_criteria_property_subcategories?: string[];
    riskprofile?: string;
    targetmarkets?: string[];
    strategies?: string[];
    propertytypes?: string[];
    assetclasses?: string[];
    valuecreation?: string[];
    holdperiod?: string;
    targetreturn?: string;
    approach?: string;
  };
}

export interface ExistingContact extends Contact {
  match_type?: 'email' | 'linkedin' | 'name_company' | 'additional_email';
  match_field?: string;
  match_value?: string;
}

export interface QueuedContact {
  id: string;
  formData: ContactFormData;
  investmentCriteria: InvestmentCriteria[];
  status: 'pending' | 'processed' | 'cancelled';
}

export interface ContactFormData {
  // Personal Information
  first_name: string;
  last_name: string;
  title: string; // job title (renamed from job_title for consistency)
  job_tier: string;

  // Contact Information
  email: string;
  additional_email: string;
  phone_number: string; // renamed from phone for consistency
  phone_number_secondary: string; // renamed from additional_phone for consistency

  // Social Media
  linkedin_url: string; // renamed from linkedin for consistency
  twitter: string;
  facebook: string;
  instagram: string;
  youtube: string;

  // Contact Location
  contact_address: string; // renamed from address for consistency
  contact_city: string; // renamed from city for consistency
  contact_state: string; // renamed from state for consistency
  contact_zip_code: string; // renamed from zipcode for consistency
  contact_country: string; // renamed from country for consistency
  last_contact_date: string;
  source_of_introduction: string;

  // Company Information (for selection, not stored in contact table)
  company_name: string;
  company_website?: string;
  industry?: string;
  company_address?: string;
  company_city?: string;
  company_state?: string;
  company_country?: string;
  company_zip?: string;
}

export interface ValidationState {
  isValidating: boolean;
  isDuplicate: boolean;
  duplicateMessage?: string;
}

export interface FieldValidationStates {
  email: ValidationState;
  additional_email: ValidationState;
  linkedin_url: ValidationState;
  full_name: ValidationState;
}

// Simplified field groups for the new contact form structure
export interface ContactFieldGroups {
  personalInfo: {
    title: string;
    icon: string;
    fields: Array<{
      name: keyof ContactFormData;
      label: string;
      type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'date' | 'array';
      required?: boolean;
      placeholder?: string;
      options?: string[];
      validation?: string;
      data_origin?: string;
    }>;
  };
  contactInfo: {
    title: string;
    icon: string;
    fields: Array<{
      name: keyof ContactFormData;
      label: string;
      type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'date' | 'array';
      required?: boolean;
      placeholder?: string;
      options?: string[];
      validation?: string;
      data_origin?: string;
    }>;
  };
  socialMedia: {
    title: string;
    icon: string;
    fields: Array<{
      name: keyof ContactFormData;
      label: string;
      type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'date' | 'array';
      required?: boolean;
      placeholder?: string;
      options?: string[];
      validation?: string;
      data_origin?: string;
    }>;
  };
}

// Define the simplified contact field groups based on the user requirements
export const CONTACT_FIELD_GROUPS: ContactFieldGroups = {
  personalInfo: {
    title: "Personal Information",
    icon: "Users",
    fields: [
      {
        name: "first_name",
        label: "First Name",
        type: "text",
        required: true,
        placeholder: "Enter first name",
        data_origin: "user_input"
      },
      {
        name: "last_name",
        label: "Last Name",
        type: "text",
        required: true,
        placeholder: "Enter last name",
        data_origin: "user_input"
      },
      {
        name: "title",
        label: "Job Title",
        type: "text",
        placeholder: "Enter job title",
        data_origin: "user_input"
      },
      {
        name: "job_tier",
        label: "Job Tier",
        type: "select",
        placeholder: "Select job tier",
        data_origin: "user_input"
      }
    ]
  },
  contactInfo: {
    title: "Contact Information",
    icon: "Phone",
    fields: [
      {
        name: "email",
        label: "Main Email",
        type: "email",
        placeholder: "Enter main email",
        validation: "max 255 chars",
        data_origin: "user_input"
      },
      {
        name: "additional_email",
        label: "Additional Email",
        type: "email",
        placeholder: "Enter additional email",
        validation: "max 255 chars",
        data_origin: "user_input/public_source"
      },
      {
        name: "phone_number",
        label: "Main Phone",
        type: "tel",
        placeholder: "Enter main phone",
        validation: "max 100 chars",
        data_origin: "user_input/public_source"
      },
      {
        name: "phone_number_secondary",
        label: "Additional Phone",
        type: "tel",
        placeholder: "Enter additional phone",
        validation: "max 100 chars",
        data_origin: "user_input/public_source"
      },
      {
        name: "contact_address",
        label: "Address",
        type: "text",
        placeholder: "Enter address",
        data_origin: "user_input/public_source"
      },
      {
        name: "contact_city",
        label: "City",
        type: "text",
        placeholder: "Enter city",
        data_origin: "user_input/public_source"
      },
      {
        name: "contact_state",
        label: "State",
        type: "text",
        placeholder: "Enter state",
        data_origin: "user_input/public_source"
      },
      {
        name: "contact_zip_code",
        label: "Zip Code",
        type: "text",
        placeholder: "Enter zip code",
        data_origin: "user_input/public_source"
      },
      {
        name: "contact_country",
        label: "Country",
        type: "text",
        placeholder: "Enter country",
        data_origin: "user_input/public_source"
      },
      {
        name: "last_contact_date",
        label: "Last Contact Date",
        type: "date",
        placeholder: "Select last contact date",
        data_origin: "user_input"
      },
      {
        name: "source_of_introduction",
        label: "Source of Introduction",
        type: "text",
        placeholder: "Enter source of introduction",
        data_origin: "user_input"
      }
    ]
  },
  socialMedia: {
    title: "Social Media",
    icon: "Globe",
    fields: [
      {
        name: "linkedin_url",
        label: "LinkedIn URL",
        type: "text",
        placeholder: "Enter LinkedIn URL",
        data_origin: "user_input/public_source"
      },
      {
        name: "twitter",
        label: "Twitter URL",
        type: "text",
        placeholder: "Enter Twitter URL",
        data_origin: "user_input/public_source"
      },
      {
        name: "facebook",
        label: "Facebook URL",
        type: "text",
        placeholder: "Enter Facebook URL",
        data_origin: "user_input/public_source"
      },
      {
        name: "instagram",
        label: "Instagram URL",
        type: "text",
        placeholder: "Enter Instagram URL",
        data_origin: "user_input/public_source"
      },
      {
        name: "youtube",
        label: "YouTube URL",
        type: "text",
        placeholder: "Enter YouTube URL",
        data_origin: "user_input/public_source"
      }
    ]
  }
};

// Form validation rules
export interface ContactValidationRules {
  [key: string]: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    validator?: (value: any) => boolean;
    errorMessage?: string;
  };
}

// Search result for contacts
export interface ContactSearchResult {
  contacts: Contact[];
  total: number;
  hasMore: boolean;
}

// Contact search filters
export interface ContactSearchFilters {
  search?: string;
  email?: string;
  linkedin_url?: string;
  company_id?: number;
  contact_type?: string;
  limit?: number;
  offset?: number;
}

// Unified contact data type for the unified filters API response
export interface UnifiedContactData extends Contact {
  // Additional fields from the API response
  contact_enrichment_v2_status?: string;
  contact_enrichment_v2_error?: string;
  contact_investment_criteria_status?: string;
  contact_investment_criteria_error?: string;
  criteria_id?: string | number;
} 