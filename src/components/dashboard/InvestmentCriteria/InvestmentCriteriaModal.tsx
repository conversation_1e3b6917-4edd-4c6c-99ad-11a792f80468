"use client"

import React from 'react'
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { InvestmentCriteria } from '@/types/investment-criteria'
import InvestmentCriteriaForm from './InvestmentCriteriaForm'

interface InvestmentCriteriaModalProps {
  isOpen: boolean;
  onClose: () => void;
  entityType: 'Contact' | 'Company' | 'Deal';
  entityId: string;
  entityName?: string;
  initialData?: Partial<InvestmentCriteria>;
  onSave: (criteria: InvestmentCriteria) => void;
  isEditing?: boolean;
}

const InvestmentCriteriaModal: React.FC<InvestmentCriteriaModalProps> = ({
  isOpen,
  onClose,
  entityType,
  entityId,
  entityName,
  initialData,
  onSave,
  isEditing = false
}) => {
  const handleSave = (criteria: InvestmentCriteria) => {
    onSave(criteria);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto p-0">
        <InvestmentCriteriaForm
          entityType={entityType}
          entityId={entityId}
          entityName={entityName}
          initialData={initialData}
          onSave={handleSave}
          onCancel={onClose}
          isEditing={isEditing}
        />
      </DialogContent>
    </Dialog>
  );
};

export default InvestmentCriteriaModal;
