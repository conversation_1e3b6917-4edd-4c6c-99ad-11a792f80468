"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import { Target, Edit, Trash2, DollarSign, MapPin, Building2, Plus, Loader2 } from "lucide-react"
import { InvestmentCriteria } from '@/types/investment-criteria'
import InvestmentCriteriaModal from './InvestmentCriteriaModal'

interface InvestmentCriteriaListProps {
  entityType: 'Contact' | 'Company' | 'Deal';
  entityId: string;
  entityName?: string;
  onCriteriaChange?: () => void;
}

const InvestmentCriteriaList: React.FC<InvestmentCriteriaListProps> = ({
  entityType,
  entityId,
  entityName,
  onCriteriaChange
}) => {
  const [criteria, setCriteria] = useState<InvestmentCriteria[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingCriteria, setEditingCriteria] = useState<InvestmentCriteria | null>(null);

  const fetchCriteria = async () => {
    try {
      setLoading(true);
      const endpoint = entityType === 'Contact' 
        ? `/api/investment-criteria/by-contact/${entityId}`
        : `/api/investment-criteria/by-company/${entityId}`;
      
      const response = await fetch(endpoint);
      if (response.ok) {
        const data = await response.json();
        setCriteria(Array.isArray(data) ? data : []);
      } else {
        setCriteria([]);
      }
    } catch (error) {
      console.error('Error fetching investment criteria:', error);
      setCriteria([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCriteria();
  }, [entityType, entityId]);

  const handleAddCriteria = () => {
    setEditingCriteria(null);
    setShowModal(true);
  };

  const handleEditCriteria = (criteriaItem: InvestmentCriteria) => {
    setEditingCriteria(criteriaItem);
    setShowModal(true);
  };

  const handleSaveCriteria = async (savedCriteria: InvestmentCriteria) => {
    await fetchCriteria(); // Refresh the list
    onCriteriaChange?.();
    toast.success(editingCriteria ? 'Investment criteria updated' : 'Investment criteria added');
  };

  const handleDeleteCriteria = async (criteriaId: number) => {
    if (!confirm('Are you sure you want to delete this investment criteria?')) {
      return;
    }

    try {
      const response = await fetch(`/api/investment-criteria/entity/${entityType}/${entityId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ criteria_id: criteriaId }),
      });

      if (response.ok) {
        await fetchCriteria(); // Refresh the list
        onCriteriaChange?.();
        toast.success('Investment criteria deleted');
      } else {
        throw new Error('Failed to delete criteria');
      }
    } catch (error) {
      console.error('Error deleting investment criteria:', error);
      toast.error('Failed to delete investment criteria');
    }
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value?: number) => {
    if (!value) return 'N/A';
    return `${value}%`;
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
          <p className="text-slate-600">Loading investment criteria...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-slate-900 flex items-center">
          <Target className="h-5 w-5 mr-2 text-blue-600" />
          Investment Criteria
        </h3>
        <Button
          onClick={handleAddCriteria}
          size="sm"
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Criteria
        </Button>
      </div>

      {criteria.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <Target className="h-12 w-12 mx-auto mb-4 text-slate-400" />
            <h4 className="text-lg font-medium text-slate-900 mb-2">No Investment Criteria</h4>
            <p className="text-slate-600 mb-4">
              Add investment criteria to track preferences and requirements for this {entityType.toLowerCase()}.
            </p>
            <Button onClick={handleAddCriteria} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Add First Criteria
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {criteria.map((criteriaItem) => (
            <Card key={criteriaItem.criteria_id} className="border border-slate-200">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base font-medium text-slate-900">
                    Investment Criteria #{criteriaItem.criteria_id}
                  </CardTitle>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditCriteria(criteriaItem)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteCriteria(criteriaItem.criteria_id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {/* Capital Position */}
                  {criteriaItem.capital_position && criteriaItem.capital_position.length > 0 && (
                    <div className="space-y-2">
                      <div className="flex items-center text-sm font-medium text-slate-700">
                        <DollarSign className="h-4 w-4 mr-1 text-blue-600" />
                        Capital Position
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {criteriaItem.capital_position.map((position) => (
                          <Badge key={position} variant="secondary" className="text-xs">
                            {position}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Deal Size */}
                  {(criteriaItem.minimum_deal_size || criteriaItem.maximum_deal_size) && (
                    <div className="space-y-2">
                      <div className="flex items-center text-sm font-medium text-slate-700">
                        <DollarSign className="h-4 w-4 mr-1 text-blue-600" />
                        Deal Size
                      </div>
                      <div className="text-sm text-slate-600">
                        {formatCurrency(criteriaItem.minimum_deal_size)} - {formatCurrency(criteriaItem.maximum_deal_size)}
                      </div>
                    </div>
                  )}

                  {/* Property Types */}
                  {criteriaItem.property_types && criteriaItem.property_types.length > 0 && (
                    <div className="space-y-2">
                      <div className="flex items-center text-sm font-medium text-slate-700">
                        <Building2 className="h-4 w-4 mr-1 text-blue-600" />
                        Property Types
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {criteriaItem.property_types.map((type) => (
                          <Badge key={type} variant="secondary" className="text-xs">
                            {type}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Target Return */}
                  {criteriaItem.target_return && (
                    <div className="space-y-2">
                      <div className="flex items-center text-sm font-medium text-slate-700">
                        <Target className="h-4 w-4 mr-1 text-blue-600" />
                        Target Return
                      </div>
                      <div className="text-sm text-slate-600">
                        {formatPercentage(criteriaItem.target_return)}
                      </div>
                    </div>
                  )}

                  {/* Geographic Focus */}
                  {(criteriaItem.country || criteriaItem.state) && (
                    <div className="space-y-2">
                      <div className="flex items-center text-sm font-medium text-slate-700">
                        <MapPin className="h-4 w-4 mr-1 text-blue-600" />
                        Geographic Focus
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {criteriaItem.country?.map((country) => (
                          <Badge key={country} variant="secondary" className="text-xs">
                            {country}
                          </Badge>
                        ))}
                        {criteriaItem.state?.map((state) => (
                          <Badge key={state} variant="secondary" className="text-xs">
                            {state}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Notes */}
                  {criteriaItem.notes && (
                    <div className="space-y-2 md:col-span-2 lg:col-span-3">
                      <div className="text-sm font-medium text-slate-700">Notes</div>
                      <div className="text-sm text-slate-600 bg-slate-50 p-3 rounded-lg">
                        {criteriaItem.notes}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Investment Criteria Modal */}
      <InvestmentCriteriaModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        entityType={entityType}
        entityId={entityId}
        entityName={entityName}
        initialData={editingCriteria || undefined}
        onSave={handleSaveCriteria}
        isEditing={!!editingCriteria}
      />
    </div>
  );
};

export default InvestmentCriteriaList;
