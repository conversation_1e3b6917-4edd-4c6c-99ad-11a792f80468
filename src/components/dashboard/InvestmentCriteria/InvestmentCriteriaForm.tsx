"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import { Target, DollarSign, MapPin, Building2, Calendar, Percent, X, Plus } from "lucide-react"
import { InvestmentCriteria } from '@/types/investment-criteria'

interface InvestmentCriteriaFormProps {
  entityType: 'Contact' | 'Company' | 'Deal';
  entityId: string;
  entityName?: string;
  initialData?: Partial<InvestmentCriteria>;
  onSave: (criteria: InvestmentCriteria) => void;
  onCancel: () => void;
  isEditing?: boolean;
}

const InvestmentCriteriaForm: React.FC<InvestmentCriteriaFormProps> = ({
  entityType,
  entityId,
  entityName,
  initialData,
  onSave,
  onCancel,
  isEditing = false
}) => {
  const [formData, setFormData] = useState<Partial<InvestmentCriteria>>({
    entity_type: entityType,
    entity_id: entityId,
    entity_name: entityName,
    capital_position: [],
    property_types: [],
    property_sub_categories: [],
    strategies: [],
    country: [],
    region: [],
    state: [],
    city: [],
    loan_type: [],
    recourse_loan: [],
    ...initialData
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Capital position options
  const capitalPositions = ['Debt', 'Equity', 'Mezzanine', 'Preferred Equity', 'Joint Venture'];
  
  // Property type options
  const propertyTypes = [
    'Multifamily', 'Office', 'Retail', 'Industrial', 'Hotel', 'Mixed-Use', 
    'Land', 'Self Storage', 'Senior Housing', 'Student Housing', 'Medical'
  ];

  // Strategy options
  const strategies = [
    'Core', 'Core Plus', 'Value Add', 'Opportunistic', 'Development', 
    'Redevelopment', 'Stabilized', 'Lease-Up', 'Distressed'
  ];

  // Geographic options (simplified for demo)
  const countries = ['United States', 'Canada', 'Mexico'];
  const usStates = [
    'California', 'Texas', 'Florida', 'New York', 'Illinois', 'Pennsylvania',
    'Ohio', 'Georgia', 'North Carolina', 'Michigan', 'New Jersey', 'Virginia'
  ];

  const handleInputChange = (field: keyof InvestmentCriteria, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayFieldChange = (field: keyof InvestmentCriteria, values: string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: values
    }));
  };

  const addArrayValue = (field: keyof InvestmentCriteria, value: string) => {
    if (!value.trim()) return;
    
    const currentValues = (formData[field] as string[]) || [];
    if (!currentValues.includes(value)) {
      handleArrayFieldChange(field, [...currentValues, value]);
    }
  };

  const removeArrayValue = (field: keyof InvestmentCriteria, value: string) => {
    const currentValues = (formData[field] as string[]) || [];
    handleArrayFieldChange(field, currentValues.filter(v => v !== value));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const endpoint = isEditing 
        ? `/api/investment-criteria/entity/${entityType}/${entityId}`
        : `/api/investment-criteria`;
      
      const method = isEditing ? 'PUT' : 'POST';
      const body = isEditing 
        ? { criteria_id: initialData?.criteria_id, updates: formData }
        : formData;

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save investment criteria');
      }

      const result = await response.json();
      toast.success(isEditing ? 'Investment criteria updated successfully' : 'Investment criteria created successfully');
      
      onSave({
        ...formData,
        criteria_id: result.criteria_id || initialData?.criteria_id,
      } as InvestmentCriteria);
    } catch (error) {
      console.error('Error saving investment criteria:', error);
      toast.error('Failed to save investment criteria');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderArrayField = (
    field: keyof InvestmentCriteria,
    label: string,
    options: string[],
    icon: React.ReactNode
  ) => {
    const values = (formData[field] as string[]) || [];
    
    return (
      <div className="space-y-3">
        <Label className="text-sm font-medium text-slate-700 flex items-center">
          {icon}
          <span className="ml-2">{label}</span>
        </Label>
        
        <Select onValueChange={(value) => addArrayValue(field, value)}>
          <SelectTrigger className="h-10">
            <SelectValue placeholder={`Select ${label.toLowerCase()}`} />
          </SelectTrigger>
          <SelectContent>
            {options.map((option) => (
              <SelectItem key={option} value={option}>
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        {values.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {values.map((value) => (
              <Badge key={value} variant="secondary" className="flex items-center gap-1">
                {value}
                <X 
                  className="h-3 w-3 cursor-pointer hover:text-red-600" 
                  onClick={() => removeArrayValue(field, value)}
                />
              </Badge>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
        <CardTitle className="flex items-center">
          <div className="p-2 bg-blue-100 rounded-xl mr-3">
            <Target className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <div className="text-lg font-semibold text-slate-900">
              {isEditing ? 'Edit' : 'Add'} Investment Criteria
            </div>
            <div className="text-sm text-slate-600">
              {entityType}: {entityName || entityId}
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Capital Position */}
          {renderArrayField('capital_position', 'Capital Position', capitalPositions, 
            <DollarSign className="h-4 w-4 text-blue-600" />)}

          {/* Deal Size */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-slate-700">Minimum Deal Size ($)</Label>
              <Input
                type="number"
                value={formData.minimum_deal_size || ''}
                onChange={(e) => handleInputChange('minimum_deal_size', parseFloat(e.target.value) || undefined)}
                placeholder="e.g., 1000000"
                className="h-10"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium text-slate-700">Maximum Deal Size ($)</Label>
              <Input
                type="number"
                value={formData.maximum_deal_size || ''}
                onChange={(e) => handleInputChange('maximum_deal_size', parseFloat(e.target.value) || undefined)}
                placeholder="e.g., 50000000"
                className="h-10"
              />
            </div>
          </div>

          {/* Property Types */}
          {renderArrayField('property_types', 'Property Types', propertyTypes, 
            <Building2 className="h-4 w-4 text-blue-600" />)}

          {/* Strategies */}
          {renderArrayField('strategies', 'Investment Strategies', strategies, 
            <Target className="h-4 w-4 text-blue-600" />)}

          {/* Geographic Focus */}
          <div className="space-y-4">
            <h3 className="text-md font-medium text-slate-700 border-b pb-2">Geographic Focus</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {renderArrayField('country', 'Countries', countries, 
                <MapPin className="h-4 w-4 text-blue-600" />)}
              {renderArrayField('state', 'States', usStates, 
                <MapPin className="h-4 w-4 text-blue-600" />)}
            </div>
          </div>

          {/* Returns */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-slate-700">Target Return (%)</Label>
              <Input
                type="number"
                step="0.01"
                value={formData.target_return || ''}
                onChange={(e) => handleInputChange('target_return', parseFloat(e.target.value) || undefined)}
                placeholder="e.g., 15.5"
                className="h-10"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium text-slate-700">Hold Period (years)</Label>
              <Input
                type="number"
                value={formData.min_hold_period || ''}
                onChange={(e) => handleInputChange('min_hold_period', parseInt(e.target.value) || undefined)}
                placeholder="e.g., 5"
                className="h-10"
              />
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-slate-700">Notes</Label>
            <Textarea
              value={formData.notes || ''}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes about investment criteria..."
              className="min-h-[100px]"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? 'Saving...' : (isEditing ? 'Update' : 'Create')} Investment Criteria
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default InvestmentCriteriaForm;
