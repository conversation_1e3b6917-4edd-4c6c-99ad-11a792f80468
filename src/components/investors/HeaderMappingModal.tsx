'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON>alog, DialogContent, <PERSON>alogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { CheckCircle, AlertTriangle, Brain, ArrowRight, Zap, Database, Users, Building2, X, RefreshCw } from 'lucide-react'



interface HeaderMappingModalProps {
  isOpen: boolean
  onClose: () => void
  headers: string[]
  sampleData?: Record<string, any> // First row of CSV data
  onConfirm: (mappings: Record<string, string>) => void
}

interface DatabaseField {
  value: string
  label: string
  table: 'companies' | 'contacts'
}

interface MappingResponse {
  success: boolean
  company_mappings?: Record<string, string[]>
  contact_mappings?: Record<string, string[]>
  unmapped_headers?: string[]
  suggestions?: {
    missing_recommended_fields: string[]
    data_quality_notes: string[]
  }
  error?: string
}

interface StoredMapping {
  header: string
  suggestedField: string
  table: 'companies' | 'contacts'
  alternatives: string[]
  userSelected?: string
}

// Helper function to format field names for display
const formatFieldLabel = (fieldName: string): string => {
  return fieldName
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

export default function HeaderMappingModal({ isOpen, onClose, headers, sampleData, onConfirm }: HeaderMappingModalProps) {
  const [loading, setLoading] = useState(false)
  const [mappingData, setMappingData] = useState<MappingResponse | null>(null)
  const [storedMappings, setStoredMappings] = useState<StoredMapping[]>([])
  const [userMappings, setUserMappings] = useState<Record<string, string>>({})
  const [error, setError] = useState<string | null>(null)
  const [databaseFields, setDatabaseFields] = useState<DatabaseField[]>([])
  const [fetchingFields, setFetchingFields] = useState(false)
  const [hasInitialMapping, setHasInitialMapping] = useState(false)

  useEffect(() => {
    if (isOpen && headers.length > 0) {
      fetchDatabaseFields()
      // Only fetch mappings if we don't have them yet
      if (!hasInitialMapping) {
        fetchHeaderMappings()
      }
    }
  }, [isOpen, headers, hasInitialMapping])

  const fetchDatabaseFields = async () => {
    setFetchingFields(true)
    try {
      const response = await fetch('/api/investors/get-database-fields')
      const data = await response.json()
      
      if (data.success) {
        const fields: DatabaseField[] = [
          ...data.companies.map((field: string) => ({
            value: field,
            label: formatFieldLabel(field),
            table: 'companies' as const
          })),
          ...data.contacts.map((field: string) => ({
            value: field,
            label: formatFieldLabel(field),
            table: 'contacts' as const
          })),
          ...data.investment_criteria.map((field: string) => ({
            value: field,
            label: formatFieldLabel(field),
            table: 'investment_criteria' as const
          }))
        ]
        setDatabaseFields(fields)
      }
    } catch (error) {
      console.error('Failed to fetch database fields:', error)
    } finally {
      setFetchingFields(false)
    }
  }

  const fetchHeaderMappings = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/investors/map-headers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          headers,
          context: sampleData 
            ? `Equity investor data with companies and contacts. Sample data: ${JSON.stringify(sampleData)}`
            : 'Equity investor data with companies and contacts'
        })
      })

      const data: MappingResponse = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to map headers')
      }

      setMappingData(data)
      
      // Store AI mappings in structured format for editing
      const mappings: StoredMapping[] = []
      const initialMappings: Record<string, string> = {}
      
      // Process company mappings
      if (data.company_mappings) {
        Object.entries(data.company_mappings).forEach(([dbField, csvHeaders]) => {
          if (csvHeaders && csvHeaders.length > 0) {
            const primaryHeader = csvHeaders[0]
            mappings.push({
              header: primaryHeader,
              suggestedField: dbField,
              table: 'companies',
              alternatives: csvHeaders.slice(1) // Other suggested headers for this field
            })
            initialMappings[primaryHeader] = dbField
          }
        })
      }
      
      // Process contact mappings
      if (data.contact_mappings) {
        Object.entries(data.contact_mappings).forEach(([dbField, csvHeaders]) => {
          if (csvHeaders && csvHeaders.length > 0) {
            const primaryHeader = csvHeaders[0]
            mappings.push({
              header: primaryHeader,
              suggestedField: dbField,
              table: 'contacts',
              alternatives: csvHeaders.slice(1) // Other suggested headers for this field
            })
            initialMappings[primaryHeader] = dbField
          }
        })
      }
      
      // Add unmapped headers
      if (data.unmapped_headers) {
        data.unmapped_headers.forEach(header => {
          mappings.push({
            header,
            suggestedField: '',
            table: 'contacts', // Default table
            alternatives: []
          })
        })
      }
      
      setStoredMappings(mappings)
      setUserMappings(initialMappings)
      setHasInitialMapping(true) // Mark that we have initial AI mappings
    } catch (error) {
      console.error('Header mapping error:', error)
      setError(error instanceof Error ? error.message : 'Unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleMappingChange = (header: string, field: string) => {
    setUserMappings(prev => ({
      ...prev,
      [header]: field === '__skip__' ? '' : field
    }))
    
    // Update stored mappings to track user changes
    setStoredMappings(prev => prev.map(mapping => 
      mapping.header === header 
        ? { ...mapping, userSelected: field === '__skip__' ? '' : field }
        : mapping
    ))
  }

  const handleConfirm = () => {
    onConfirm(userMappings)
    onClose()
  }

  const reAnalyzeMappings = async () => {
    // Force re-analysis by resetting the flag and calling API again
    setHasInitialMapping(false)
    await fetchHeaderMappings()
  }



  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="pb-4 border-b">
          <DialogTitle className="text-2xl flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-blue-100 to-purple-100 rounded-lg">
              <Brain className="h-6 w-6 text-blue-600" />
            </div>
            Smart Header Mapping
            {hasInitialMapping && !loading && (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                AI Analysis Cached
              </Badge>
            )}
          </DialogTitle>
          <p className="text-gray-600 mt-2">
            AI-powered mapping of your CSV headers to database fields. Review and adjust the suggestions below.
            {hasInitialMapping && !loading && (
              <span className="text-green-600 font-medium"> Mappings are saved - no additional API calls needed for edits.</span>
            )}
          </p>
        </DialogHeader>

        <div className="flex-1 overflow-auto py-4">
          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="inline-flex items-center gap-3 bg-white p-6 rounded-xl shadow-sm border">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <div className="text-left">
                    <p className="font-medium text-gray-900">Analyzing headers...</p>
                    <p className="text-sm text-gray-500">AI is mapping your CSV headers to database fields</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <span className="font-semibold">Mapping Failed:</span> {error}
              </AlertDescription>
            </Alert>
          )}

          {mappingData && !loading && (
            <div className="space-y-6">
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-green-700">Mapped Headers</p>
                                                  <p className="text-2xl font-bold text-green-900">
                            {Object.keys(userMappings).filter(k => userMappings[k]).length}
                          </p>
                      </div>
                      <CheckCircle className="h-8 w-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="bg-gradient-to-br from-orange-50 to-red-50 border-orange-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-orange-700">Unmapped</p>
                        <p className="text-2xl font-bold text-orange-900">
                          {mappingData.unmapped_headers?.length || 0}
                        </p>
                      </div>
                      <X className="h-8 w-8 text-orange-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-blue-700">Total Headers</p>
                        <p className="text-2xl font-bold text-blue-900">{headers.length}</p>
                      </div>
                      <Database className="h-8 w-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Header Mappings */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Zap className="h-5 w-5 text-blue-600" />
                    Header Mappings
                  </CardTitle>
                                      <p className="text-sm text-gray-600">
                      Map your CSV headers to database fields. AI suggestions have been applied automatically.
                    </p>
                </CardHeader>
                                  <CardContent className="space-y-4">
                    {storedMappings.map((mapping) => (
                      <div key={mapping.header} className="p-4 border rounded-lg bg-gray-50">
                        <div className="flex items-start justify-between gap-4">
                          <div className="flex-1 min-w-0">
                            <div className="space-y-2 mb-3">
                              <div className="flex items-center gap-3">
                                <h4 className="font-medium text-gray-900">{mapping.header}</h4>
                                {mapping.suggestedField && (
                                  <>
                                    <ArrowRight className="h-4 w-4 text-gray-400" />
                                    <Badge className={mapping.table === 'companies' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'}>
                                      {mapping.table === 'companies' ? <Building2 className="h-3 w-3 mr-1" /> : <Users className="h-3 w-3 mr-1" />}
                                      {mapping.table}
                                    </Badge>
                                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                      AI Suggested: {formatFieldLabel(mapping.suggestedField)}
                                    </Badge>
                                  </>
                                )}
                              </div>
                              
                              {/* Sample Data Display */}
                              {sampleData && sampleData[mapping.header] && (
                                <div className="bg-gray-100 px-3 py-2 rounded-md border">
                                  <div className="flex items-center gap-2">
                                    <span className="text-xs font-medium text-gray-600">Sample:</span>
                                    <span className="text-sm text-gray-800 font-mono bg-white px-2 py-1 rounded border">
                                      {String(sampleData[mapping.header]).length > 50 
                                        ? `${String(sampleData[mapping.header]).substring(0, 50)}...`
                                        : String(sampleData[mapping.header])
                                      }
                                    </span>
                                  </div>
                                </div>
                              )}
                            </div>
                            
                            <div className="space-y-3">
                              <Select
                                value={mapping.userSelected || mapping.suggestedField || ''}
                                onValueChange={(value) => handleMappingChange(mapping.header, value)}
                              >
                                <SelectTrigger className="w-full">
                                  <SelectValue placeholder="Select database field or skip" />
                                </SelectTrigger>
                                <SelectContent className="max-h-80">
                                  <SelectItem value="__skip__">
                                    <div className="flex items-center gap-2">
                                      <X className="h-4 w-4 text-gray-400" />
                                      <span>Skip this field</span>
                                    </div>
                                  </SelectItem>
                                  
                                  {/* Companies Section */}
                                  <div className="px-2 py-1.5 text-xs font-semibold text-gray-500 bg-blue-50 border-b">
                                    <div className="flex items-center gap-1">
                                      <Building2 className="h-3 w-3" />
                                      COMPANIES
                                    </div>
                                  </div>
                                  {databaseFields
                                    .filter((field: DatabaseField) => field.table === 'companies')
                                    .map((field: DatabaseField) => (
                                    <SelectItem key={field.value} value={field.value}>
                                      <div className="flex items-center gap-2 w-full">
                                        <span className="flex-1">{field.label}</span>
                                        {field.value === mapping.suggestedField && (
                                          <Badge variant="secondary" className="text-xs bg-green-100 text-green-700 border-green-200">
                                            AI Pick
                                          </Badge>
                                        )}
                                      </div>
                                    </SelectItem>
                                  ))}
                                  
                                  {/* Contacts Section */}
                                  <div className="px-2 py-1.5 text-xs font-semibold text-gray-500 bg-purple-50 border-b border-t">
                                    <div className="flex items-center gap-1">
                                      <Users className="h-3 w-3" />
                                      CONTACTS
                                    </div>
                                  </div>
                                  {databaseFields
                                    .filter((field: DatabaseField) => field.table === 'contacts')
                                    .map((field: DatabaseField) => (
                                    <SelectItem key={field.value} value={field.value}>
                                      <div className="flex items-center gap-2 w-full">
                                        <span className="flex-1">{field.label}</span>
                                        {field.value === mapping.suggestedField && (
                                          <Badge variant="secondary" className="text-xs bg-green-100 text-green-700 border-green-200">
                                            AI Pick
                                          </Badge>
                                        )}
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              
                              {mapping.userSelected && mapping.userSelected !== mapping.suggestedField && (
                                <div className="text-sm text-orange-600 bg-orange-50 p-2 rounded border border-orange-200">
                                  <span className="font-medium">User Override:</span> You've changed this from the AI suggestion
                                </div>
                              )}
                              
                              {!mapping.suggestedField && (
                                <div className="text-sm text-gray-600 bg-yellow-50 p-2 rounded border border-yellow-200">
                                  <span className="font-medium">Unmapped:</span> AI couldn't find a suitable match for this header
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </CardContent>
              </Card>



              {/* Suggestions */}
              {mappingData.suggestions && (
                <Card className="border-blue-200 bg-blue-50">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2 text-blue-800">
                      <Brain className="h-5 w-5" />
                      AI Suggestions
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {mappingData.suggestions.missing_recommended_fields.length > 0 && (
                      <div>
                        <p className="text-sm font-medium text-blue-800 mb-2">Missing Recommended Fields:</p>
                        <div className="flex flex-wrap gap-2">
                          {mappingData.suggestions.missing_recommended_fields.map((field) => (
                            <Badge key={field} variant="outline" className="text-blue-700 border-blue-300">
                              {databaseFields.find((f: DatabaseField) => f.value === field)?.label || field}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {mappingData.suggestions.data_quality_notes.length > 0 && (
                      <div>
                        <p className="text-sm font-medium text-blue-800 mb-2">Data Quality Notes:</p>
                        <ul className="text-sm text-blue-700 space-y-1">
                          {mappingData.suggestions.data_quality_notes.map((note, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="text-blue-500 mt-1">•</span>
                              {note}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </div>

        <DialogFooter className="border-t pt-4">
          <div className="flex items-center justify-between w-full">
            <Button
              variant="outline"
              onClick={reAnalyzeMappings}
              disabled={loading}
              className="flex items-center gap-2 border-purple-200 text-purple-700 hover:bg-purple-50"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              {hasInitialMapping ? 'Re-analyze with AI' : 'Analyze with AI'}
            </Button>
            
            <div className="flex gap-3">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button 
                onClick={handleConfirm}
                disabled={loading || !mappingData}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Confirm Mapping
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 