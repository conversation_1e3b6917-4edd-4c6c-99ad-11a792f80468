import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileUpload } from "@/components/ui/file-upload";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Upload, CheckCircle, AlertCircle, User, Mail } from "lucide-react";
import { Badge } from "@/components/ui/badge";

const DealCSVUpload: React.FC = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [contactEmail, setContactEmail] = useState<string>("");
  const [uploading, setUploading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [dbStatus, setDbStatus] = useState<{
    saved: boolean;
    error?: string;
  } | null>(null);
  const [processingResult, setProcessingResult] = useState<any>(null);

  const handleUpload = async () => {
    if (files.length === 0) return;
    setUploading(true);
    setMessage(null);
    setError(null);
    setDbStatus(null);
    setProcessingResult(null);

    try {
      const formData = new FormData();

      // Add all files with descriptive names
      files.forEach((file, index) => {
        const fileType = getFileType(file.name);
        formData.append(`file_${index}`, file);
        formData.append(`file_type_${index}`, fileType);
      });

      // Add contact email if provided
      if (contactEmail.trim()) {
        formData.append("contact_email", contactEmail.trim());
      }

      formData.append("mode", "file"); // Use Gemini 2.0 Flash by default
      formData.append("file_count", files.length.toString());

      const res = await fetch("/api/deals/upload-simplified", {
        method: "POST",
        body: formData,
      });
      const data = await res.json();

      if (res.ok) {
        setMessage(data.message || "Upload successful!");
        setProcessingResult(data);

        // Handle database status
        if (data.databaseSaved) {
          setDbStatus({ saved: true });
        } else if (data.databaseError) {
          setDbStatus({ saved: false, error: data.databaseError });
        }
      } else {
        setError(data.error || "Upload failed.");
      }
    } catch (err: any) {
      setError(err.message || "Upload failed.");
    } finally {
      setUploading(false);
    }
  };

  const getFileType = (fileName: string): string => {
    const name = fileName.toLowerCase();
    if (name.includes("memorandum") || name.includes("mem"))
      return "memorandum";
    if (name.includes("underwriting") || name.includes("uw"))
      return "underwriting";
    if (name.includes("term") || name.includes("sheet")) return "term_sheet";
    if (name.includes("proforma") || name.includes("model")) return "proforma";
    if (name.includes("budget") || name.includes("construction"))
      return "budget";
    if (name.includes("analysis") || name.includes("market")) return "analysis";
    return "document";
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">
          Deal Document Upload
        </h1>
        <p className="text-gray-600">
          Upload deal documents using the simplified table structure with
          contact linking
        </p>
        <div className="flex justify-center gap-2">
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 border-blue-200"
          >
            <Upload className="h-3 w-3 mr-1" />
            Simplified Schema
          </Badge>
          <Badge
            variant="outline"
            className="bg-purple-50 text-purple-700 border-purple-200"
          >
            <User className="h-3 w-3 mr-1" />
            Contact Linking
          </Badge>
        </div>
      </div>

      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl flex items-center gap-2">
            <Upload className="h-6 w-6 text-blue-600" />
            Upload Deal Documents
          </CardTitle>
          <p className="text-gray-600">
            Upload memorandum, underwriting, term sheets, pro forma models, and
            other deal documents. The system will extract data into the
            simplified deals table structure.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Contact Email Input */}
          <div className="space-y-2">
            <Label htmlFor="contact_email" className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-gray-600" />
              Contact Email (Optional)
            </Label>
            <Input
              id="contact_email"
              type="email"
              placeholder="Enter contact email to link with deal"
              value={contactEmail}
              onChange={(e) => setContactEmail(e.target.value)}
              className="max-w-md"
            />
            <p className="text-sm text-gray-500">
              Link this deal to a contact by email. The system will find the
              contact in the persons table and associate the deal.
            </p>
          </div>

          <FileUpload
            onFilesSelected={setFiles}
            acceptedFileTypes={[
              ".pdf",
              ".csv",
              ".xlsx",
              ".xls",
              ".doc",
              ".docx",
            ]}
            maxFiles={10}
            maxFileSize={30 * 1024 * 1024} // 30MB
            placeholder="Drop your deal documents here"
            description="Upload memorandum, underwriting, term sheets, pro forma models, and other deal documents. Multiple files supported."
            disabled={uploading}
          />

          <Button
            className="w-full"
            onClick={handleUpload}
            disabled={!files.length || uploading}
            size="lg"
          >
            {uploading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Processing Documents...
              </>
            ) : (
              <>
                <Upload className="h-5 w-5 mr-2" />
                Process {files.length} Document{files.length !== 1 ? "s" : ""}
              </>
            )}
          </Button>

          {message && (
            <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-green-700 font-medium">{message}</p>
                {dbStatus && (
                  <div className="mt-1">
                    {dbStatus.saved ? (
                      <span className="text-sm text-green-600">
                        Data saved to simplified deals table successfully
                      </span>
                    ) : (
                      <span className="text-sm text-orange-600">
                        Database save failed: {dbStatus.error}
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <p className="text-red-700">{error}</p>
            </div>
          )}

          {/* Results Display */}
          {processingResult && (
            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Processing Results
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <div className="flex items-center gap-2 mb-2">
                      <Upload className="h-4 w-4 text-blue-600" />
                      <span className="font-medium text-blue-900">
                        Files Processed
                      </span>
                    </div>
                    <p className="text-2xl font-bold text-blue-700">
                      {processingResult.fileCount}
                    </p>
                  </div>

                  <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="font-medium text-green-900">
                        Database Status
                      </span>
                    </div>
                    <p className="text-lg font-semibold text-green-700">
                      {processingResult.databaseSaved ? "Saved" : "Failed"}
                    </p>
                  </div>

                  <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                    <div className="flex items-center gap-2 mb-2">
                      <User className="h-4 w-4 text-purple-600" />
                      <span className="font-medium text-purple-900">
                        Contact
                      </span>
                    </div>
                    <p className="text-lg font-semibold text-purple-700">
                      {processingResult.contactEmail || "None"}
                    </p>
                  </div>
                </div>

                {processingResult.extractedData && (
                  <div className="bg-gray-50 p-4 rounded-lg border">
                    <h4 className="font-medium text-gray-900 mb-2">
                      Extracted Data Preview
                    </h4>
                    <div className="bg-white p-3 rounded border text-sm font-mono overflow-auto max-h-40">
                      <pre>
                        {JSON.stringify(
                          processingResult.extractedData,
                          null,
                          2
                        )}
                      </pre>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default DealCSVUpload;
