"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>ircle, AlertTriangle, XCircle, Zap } from "lucide-react";
import { ConflictPreviewRow } from "@/types/conflict";

interface ConflictBadgeProps {
  row: ConflictPreviewRow;
  className?: string;
}

export default function ConflictBadge({ row, className = "" }: ConflictBadgeProps) {
  const getConflictInfo = () => {
    const hasCompanyConflict = row.company_conflict_status && row.company_conflict_status !== 'none';
    const hasContactConflict = row.contact_conflict_status && row.contact_conflict_status !== 'none';
    
    if (hasCompanyConflict && hasContactConflict) {
      return {
        icon: XCircle,
        text: "Multiple",
        variant: "destructive" as const,
        bgClass: "bg-red-50 text-red-700 border-red-200"
      };
    } else if (hasCompanyConflict || hasContactConflict) {
      return {
        icon: AlertTriangle,
        text: hasCompanyConflict ? "Company" : "Contact",
        variant: "secondary" as const,
        bgClass: "bg-yellow-50 text-yellow-700 border-yellow-200"
      };
    } else if (row.company_conflict_status === 'none' || row.contact_conflict_status === 'none') {
      return {
        icon: CheckCircle,
        text: "Clean",
        variant: "default" as const,
        bgClass: "bg-green-50 text-green-700 border-green-200"
      };
    } else {
      return {
        icon: Zap,
        text: "New",
        variant: "outline" as const,
        bgClass: "bg-blue-50 text-blue-700 border-blue-200"
      };
    }
  };

  const { icon: Icon, text, bgClass } = getConflictInfo();

  return (
    <Badge 
      className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium border ${bgClass} ${className}`}
    >
      <Icon className="h-3 w-3" />
      {text}
    </Badge>
  );
} 