"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  XCircle,
  Upload,
  AlertTriangle,
  Eye,
  Building2,
  Users,
  TrendingUp,
  Activity
} from "lucide-react";
import { ConflictUploadResult } from "@/types/conflict";

interface UploadResultsProps {
  result: ConflictUploadResult;
  selectedCount: number;
  onReset: () => void;
  onViewConflicts: () => void;
  onViewData: () => void;
  className?: string;
}

export default function UploadResults({
  result,
  selectedCount,
  onReset,
  onViewConflicts,
  onViewData,
  className = ""
}: UploadResultsProps) {
  const conflictCount = result.conflicts.companies.length + result.conflicts.contacts.length;
  const stats = result.stats;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Success/Error Alert */}
      {result.success ? (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-5 w-5 text-green-600" />
          <AlertDescription className="text-green-800">
            <span className="font-semibold">Success!</span>{" "}
            Processed {selectedCount} rows successfully.
            {conflictCount > 0 &&
              ` Found ${conflictCount} conflicts that need resolution.`}
          </AlertDescription>
        </Alert>
      ) : (
        <Alert variant="destructive">
          <XCircle className="h-5 w-5" />
          <AlertDescription>
            <span className="font-semibold">Upload Failed:</span>{" "}
            {result.error || "Unknown error occurred"}
          </AlertDescription>
        </Alert>
      )}

      {/* Processing Statistics */}
      {result.success && stats && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Companies Stats */}
          <Card className="shadow-lg">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
              <CardTitle className="text-lg flex items-center gap-2">
                <Building2 className="h-5 w-5 text-blue-600" />
                Companies Processing Results
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.companies.total}
                  </p>
                  <p className="text-sm text-gray-600">
                    Total Processed
                  </p>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">
                    {stats.companies.added}
                  </p>
                  <p className="text-sm text-green-700">
                    New Added
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-orange-50 rounded-lg">
                  <p className="text-2xl font-bold text-orange-600">
                    {stats.companies.updated_with_conflicts}
                  </p>
                  <p className="text-sm text-orange-700">
                    Updated/Conflicts
                  </p>
                </div>
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <p className="text-2xl font-bold text-yellow-600">
                    {stats.companies.skipped}
                  </p>
                  <p className="text-sm text-yellow-700">
                    Skipped
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contacts Stats */}
          <Card className="shadow-lg">
            <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 border-b">
              <CardTitle className="text-lg flex items-center gap-2">
                <Users className="h-5 w-5 text-purple-600" />
                Contacts Processing Results
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.contacts.total}
                  </p>
                  <p className="text-sm text-gray-600">
                    Total Processed
                  </p>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">
                    {stats.contacts.added}
                  </p>
                  <p className="text-sm text-green-700">
                    New Added
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-orange-50 rounded-lg">
                  <p className="text-2xl font-bold text-orange-600">
                    {stats.contacts.updated_with_conflicts}
                  </p>
                  <p className="text-sm text-orange-700">
                    Updated/Conflicts
                  </p>
                </div>
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <p className="text-2xl font-bold text-yellow-600">
                    {stats.contacts.skipped}
                  </p>
                  <p className="text-sm text-yellow-700">
                    Skipped
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Investment Criteria Stats (if available) */}
      {result.success && stats && (stats as any).investment_criteria && (
        <Card className="shadow-lg">
          <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b">
            <CardTitle className="text-lg flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              Investment Criteria Processing Results
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <p className="text-2xl font-bold text-gray-900">
                  {(stats as any).investment_criteria.total}
                </p>
                <p className="text-sm text-gray-600">
                  Total Records
                </p>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <p className="text-2xl font-bold text-green-600">
                  {(stats as any).investment_criteria.added}
                </p>
                <p className="text-sm text-green-700">
                  Successfully Added
                </p>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <p className="text-2xl font-bold text-red-600">
                  {(stats as any).investment_criteria.errors}
                </p>
                <p className="text-sm text-red-700">
                  Errors
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3">
        <Button onClick={onReset} variant="outline" size="lg">
          <Upload className="h-4 w-4 mr-2" />
          Upload Another File
        </Button>
        
        {conflictCount > 0 && (
          <Button
            onClick={onViewConflicts}
            className="bg-orange-600 hover:bg-orange-700"
            size="lg"
          >
            <AlertTriangle className="h-4 w-4 mr-2" />
            Resolve {conflictCount} Conflicts
          </Button>
        )}
        
        <Button
          onClick={onViewData}
          variant="outline"
          size="lg"
        >
          <Eye className="h-4 w-4 mr-2" />
          View Updated Data
        </Button>
      </div>

      {/* Processing Summary */}
      {result.success && (
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Activity className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-blue-900">
                  Processing Complete
                </h3>
                <p className="text-sm text-blue-700 mt-1">
                  Your data has been successfully processed and added to the system. 
                  {conflictCount > 0 && " Please review and resolve any conflicts to ensure data accuracy."}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 