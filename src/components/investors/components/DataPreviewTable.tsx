"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Eye, 
  Database, 
  Activity, 
  ArrowUpDown, 
  ArrowUp, 
  ArrowDown,
  CheckCircle,
  XCircle,
  AlertTriangle
} from "lucide-react";
import { ConflictPreviewRow } from "@/types/conflict";

interface DataPreviewTableProps {
  data: ConflictPreviewRow[];
  selectedRows: Set<number>;
  headerMappings: Record<string, string>;
  checking: boolean;
  uploading: boolean;
  progress: number;
  onRowSelect: (index: number) => void;
  onSelectAll: () => void;
  onCheckConflicts: () => void;
  onUpload: () => void;
  getDisplayValue: (row: any, dbField: string) => string;
  getConflictBadge: (row: ConflictPreviewRow) => JSX.Element;
  fileInfo?: {
    name: string;
    size: number;
    sizeFormatted: string;
  };
  className?: string;
}

export default function DataPreviewTable({
  data,
  selectedRows,
  headerMappings,
  checking,
  uploading,
  progress,
  onRowSelect,
  onSelectAll,
  onCheckConflicts,
  onUpload,
  getDisplayValue,
  getConflictBadge,
  fileInfo,
  className = ""
}: DataPreviewTableProps) {
  const selectedCount = selectedRows.size;
  const isAllSelected = selectedCount === data.length && data.length > 0;
  const isPartiallySelected = selectedCount > 0 && selectedCount < data.length;

  const displayData = data.slice(0, 10); // Show first 10 rows
  const mappedHeaders = Object.values(headerMappings).filter(v => v);

  if (data.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* File Info Card */}
      {fileInfo && (
        <Card className="bg-gradient-to-r from-gray-50 to-slate-50 border border-gray-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <Database className="h-8 w-8 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">
                    {fileInfo.name}
                  </h3>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                    <span className="flex items-center gap-1">
                      <Database className="h-4 w-4" />
                      {data.length} rows
                    </span>
                    <span className="flex items-center gap-1">
                      <Activity className="h-4 w-4" />
                      {fileInfo.sizeFormatted}
                    </span>
                    <span className="flex items-center gap-1 text-green-600">
                      <CheckCircle className="h-4 w-4" />
                      Headers Mapped
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Badge variant="outline" className="px-3 py-1">
                  <Eye className="h-4 w-4 mr-2" />
                  Preview Mode
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Data Preview Table */}
      <Card className="shadow-lg">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Database className="h-5 w-5 text-blue-600" />
              Data Preview & Selection
            </CardTitle>
            <div className="flex items-center gap-3">
              <span className="text-sm text-gray-600">
                {selectedCount} of {data.length} rows selected
              </span>
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={isAllSelected}
                  ref={(el) => {
                    if (el) {
                      const input = el.querySelector('input[type="checkbox"]') as HTMLInputElement;
                      if (input) input.indeterminate = isPartiallySelected;
                    }
                  }}
                  onCheckedChange={onSelectAll}
                />
                <span className="text-sm font-medium">Select All</span>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {checking && (
            <div className="p-6 text-center">
              <div className="inline-flex items-center gap-3 bg-white p-6 rounded-xl shadow-sm">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <div className="text-left">
                  <p className="font-medium text-gray-900">
                    Checking for conflicts...
                  </p>
                  <p className="text-sm text-gray-500">
                    Analyzing {data.length} rows
                  </p>
                </div>
              </div>
            </div>
          )}

          {uploading && (
            <div className="p-6 text-center">
              <div className="inline-flex items-center gap-3 bg-white p-6 rounded-xl shadow-sm">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
                <div className="text-left">
                  <p className="font-medium text-gray-900">
                    Uploading data...
                  </p>
                  <p className="text-sm text-gray-500">
                    Processing {selectedCount} rows
                  </p>
                  <Progress value={progress} className="w-32 mt-2" />
                </div>
              </div>
            </div>
          )}

          {!checking && !uploading && (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                      Select
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Status
                    </th>
                    {mappedHeaders.map((header) => (
                      <th
                        key={header}
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        <div className="flex items-center gap-1">
                          {header}
                          <ArrowUpDown className="h-3 w-3" />
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {displayData.map((row, index) => (
                    <tr
                      key={index}
                      className={`hover:bg-gray-50 transition-colors ${
                        selectedRows.has(index) ? "bg-blue-50" : ""
                      }`}
                    >
                      <td className="px-4 py-3">
                        <Checkbox
                          checked={selectedRows.has(index)}
                          onCheckedChange={() => onRowSelect(index)}
                        />
                      </td>
                      <td className="px-4 py-3">
                        {getConflictBadge(row)}
                      </td>
                      {mappedHeaders.map((header) => (
                        <td
                          key={header}
                          className="px-4 py-3 text-sm text-gray-900 max-w-48 truncate"
                          title={getDisplayValue(row, header)}
                        >
                          {getDisplayValue(row, header)}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
              {data.length > 10 && (
                <div className="p-4 bg-gray-50 border-t text-center">
                  <p className="text-sm text-gray-600">
                    Showing first 10 rows of {data.length} total rows
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          {!checking && !uploading && (
            <div className="p-6 bg-gray-50 border-t">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  Ready to process {selectedCount} selected rows
                </div>
                <div className="flex gap-3">
                  <Button
                    onClick={onCheckConflicts}
                    disabled={selectedCount === 0}
                    variant="outline"
                    size="lg"
                  >
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    Check for Conflicts
                  </Button>
                  <Button
                    onClick={onUpload}
                    disabled={selectedCount === 0}
                    className="bg-green-600 hover:bg-green-700"
                    size="lg"
                  >
                    <Database className="h-4 w-4 mr-2" />
                    Upload {selectedCount} Rows
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 