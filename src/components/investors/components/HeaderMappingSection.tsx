"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { X, RefreshCw, Brain, Building2, Users, TrendingUp, CheckCircle, ChevronUp, ChevronDown, XCircle } from "lucide-react";
import { HeaderMappingData } from "@/lib/services/UploadService";

interface HeaderMappingSectionProps {
  csvHeaders: string[];
  headerMappings: Record<string, string[]>;
  mappingData: HeaderMappingData | null;
  showMappingSection: boolean;
  loading: boolean;
  loadingFields: boolean;
  mappingConfirmed: boolean;
  onHeaderMappingChange: (csvHeader: string, dbField: string) => void;
  onRemoveSpecificMapping: (csvHeader: string, dbField: string) => void;
  onConfirmMapping: () => void;
  onToggleMappingSection: () => void;
  onReAnalyzeMappings: () => void;
  className?: string;
}

const CategoryIcon = ({ category }: { category: string }) => {
  switch (category) {
    case 'company':
      return <Building2 className="h-4 w-4 text-blue-600" />;
    case 'contact':
      return <Users className="h-4 w-4 text-purple-600" />;
    case 'investment_criteria':
      return <TrendingUp className="h-4 w-4 text-green-600" />;
    default:
      return <CheckCircle className="h-4 w-4 text-gray-600" />;
  }
};

const CategoryBadge = ({ category }: { category: string }) => {
  const configs = {
    company: { label: "Company", className: "bg-blue-50 text-blue-700 border-blue-200" },
    contact: { label: "Contact", className: "bg-purple-50 text-purple-700 border-purple-200" },
    investment_criteria: { label: "Investment", className: "bg-green-50 text-green-700 border-green-200" },
  };
  
  const config = configs[category as keyof typeof configs] || { label: "Other", className: "bg-gray-50 text-gray-700 border-gray-200" };
  
  return (
    <Badge variant="outline" className={`text-xs ${config.className}`}>
      <CategoryIcon category={category} />
      <span className="ml-1">{config.label}</span>
    </Badge>
  );
};

export default function HeaderMappingSection({
  csvHeaders,
  headerMappings,
  mappingData,
  showMappingSection,
  loading,
  loadingFields,
  mappingConfirmed,
  onHeaderMappingChange,
  onRemoveSpecificMapping,
  onConfirmMapping,
  onToggleMappingSection,
  onReAnalyzeMappings,
  className = ""
}: HeaderMappingSectionProps) {

  // Get ALL available database fields from the map-headers API response
  const getDatabaseFieldsByCategory = () => {
    // Use database_fields from mappingData (comes from map-headers API)
    if (mappingData?.database_fields) {
      console.log('Using ALL available database fields from mappingData.database_fields:', mappingData.database_fields);
      return {
        company: mappingData.database_fields.companies || [],
        contact: mappingData.database_fields.contacts || [],
        investment_criteria: mappingData.database_fields.investment_criteria || []
      };
    }
    
    // Empty state - no database fields available
    console.log('No database fields available from mappingData - returning empty structure');
    return {
      company: [],
      contact: [],
      investment_criteria: []
    };
  };

  const dbFieldsByCategory = getDatabaseFieldsByCategory();

  // Debug logging
  console.log('=== HeaderMappingSection Debug ===');
  console.log('1. mappingData available:', !!mappingData);
  console.log('2. mappingData.database_fields:', mappingData?.database_fields);
  console.log('3. dbFieldsByCategory (FINAL RESULT):', dbFieldsByCategory);
  console.log('4. Field counts - Company:', dbFieldsByCategory.company.length, 'Contact:', dbFieldsByCategory.contact.length, 'Investment:', dbFieldsByCategory.investment_criteria.length);
  console.log('5. csvHeaders count:', csvHeaders.length);
  console.log('6. headerMappings:', headerMappings);
  console.log('7. Structured mappings from API:');
  if (mappingData) {
    console.log('   - company_mappings keys:', Object.keys(mappingData.company_mappings || {}));
    console.log('   - contact_mappings keys:', Object.keys(mappingData.contact_mappings || {}));
    console.log('   - investment_criteria_mappings keys:', Object.keys(mappingData.investment_criteria_mappings || {}));
  }
  console.log('==========================================');

  // Get mapping statistics using structured mappings from API response
  const getMappingStats = () => {
    if (mappingData) {
      // Use structured mappings for accurate counts
      const companyMappings = mappingData.company_mappings || {};
      const contactMappings = mappingData.contact_mappings || {};
      const investmentMappings = mappingData.investment_criteria_mappings || {};
      
      // Count total mapped headers from structured mappings
      const allMappedHeaders = new Set<string>();
      
      Object.values(companyMappings).forEach(csvHeaders => {
        if (Array.isArray(csvHeaders)) {
          csvHeaders.forEach(header => allMappedHeaders.add(header));
        }
      });
      
      Object.values(contactMappings).forEach(csvHeaders => {
        if (Array.isArray(csvHeaders)) {
          csvHeaders.forEach(header => allMappedHeaders.add(header));
        }
      });
      
      Object.values(investmentMappings).forEach(csvHeaders => {
        if (Array.isArray(csvHeaders)) {
          csvHeaders.forEach(header => allMappedHeaders.add(header));
        }
      });
      
      const totalMapped = allMappedHeaders.size;
      const unmappedCount = mappingData.unmapped_headers?.length || 0;
      
      return {
        totalMapped,
        unmapped: unmappedCount,
        mappedPercentage: csvHeaders.length > 0 ? Math.round((totalMapped / csvHeaders.length) * 100) : 0,
        categoryStats: {
          company: Object.keys(companyMappings).length,
          contact: Object.keys(contactMappings).length,
          investment_criteria: Object.keys(investmentMappings).length
        }
      };
    }
    
    // Fallback to legacy calculation
    const mappedHeaders = Object.keys(headerMappings).filter(h => headerMappings[h] && headerMappings[h].length > 0);
    const unmappedHeaders = csvHeaders.filter(h => !headerMappings[h] || headerMappings[h].length === 0);
    
    // Count mappings per category
    const categoryStats = {
      company: 0,
      contact: 0,
      investment_criteria: 0
    };

    Object.values(headerMappings).forEach(dbFields => {
      dbFields.forEach(dbField => {
        if (dbFieldsByCategory.company.includes(dbField)) categoryStats.company++;
        else if (dbFieldsByCategory.contact.includes(dbField)) categoryStats.contact++;
        else if (dbFieldsByCategory.investment_criteria.includes(dbField)) categoryStats.investment_criteria++;
      });
    });

    return {
      totalMapped: mappedHeaders.length,
      unmapped: unmappedHeaders.length,
      mappedPercentage: Math.round((mappedHeaders.length / csvHeaders.length) * 100),
      categoryStats
    };
  };

  const stats = getMappingStats();

  // Apply all AI suggestions from structured mappings
  const applyAllAISuggestions = () => {
    if (!mappingData) return;

    // Apply structured mappings - we need to convert to the format expected by headerMappings
    const allMappings = {
      ...mappingData.company_mappings,
      ...mappingData.contact_mappings,
      ...mappingData.investment_criteria_mappings
    };

    Object.entries(allMappings || {}).forEach(([dbField, csvHeaders]) => {
      if (Array.isArray(csvHeaders)) {
        csvHeaders.forEach(csvHeader => {
          onHeaderMappingChange(csvHeader, dbField);
        });
      }
    });
  };

  // Render fields for a specific category
  const renderCategoryFields = (categoryName: string, fields: string[]) => {
    if (!fields || fields.length === 0) return null;

    return (
      <div className="space-y-3">
        <h4 className="font-medium text-sm text-gray-700 capitalize flex items-center gap-2">
          <CategoryIcon category={categoryName} />
          {categoryName === 'investment_criteria' ? 'Investment Criteria' : categoryName} Fields ({fields.length} available)
        </h4>
        
        <div className="space-y-2">
          {fields.map((fieldName) => {
            // Find CSV headers mapped to this DB field
            const mappedHeaders = Object.keys(headerMappings).filter(csvHeader => 
              headerMappings[csvHeader].includes(fieldName)
            );
            const isMapped = mappedHeaders.length > 0;

            return (
              <div 
                key={fieldName}
                className={`p-3 border rounded-lg transition-colors ${
                  isMapped 
                    ? 'border-green-200 bg-green-50' 
                    : 'border-gray-200 bg-gray-50 hover:bg-gray-100'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 flex-1">
                    <span className="font-medium text-sm break-words" title={fieldName}>{fieldName}</span>
                    {isMapped && (
                      <Badge variant="secondary" className="text-xs shrink-0">
                        Mapped
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {/* Show currently mapped headers */}
                    {mappedHeaders.length > 0 && (
                      <div className="flex items-center gap-1 mr-2 flex-wrap">
                        {mappedHeaders.map((header) => (
                          <div key={header} className="flex items-center gap-1">
                            <span className="text-xs text-green-700 bg-green-100 px-2 py-1 rounded max-w-100" title={header}>
                              {header}
                            </span>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => onRemoveSpecificMapping(header, fieldName)}
                              className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                    
                    {/* Dropdown to add/change mapping */}
                    <Select 
                      value="" 
                      onValueChange={(value) => {
                        if (value && value !== "__no_selection__") {
                          onHeaderMappingChange(value, fieldName);
                        }
                      }}
                    >
                      <SelectTrigger className="w-48 h-8">
                        <SelectValue placeholder={isMapped ? "Add more..." : "Select header..."} />
                      </SelectTrigger>
                      <SelectContent className="max-h-80 overflow-y-auto w-96">
                        <SelectItem value="__no_selection__">Select header...</SelectItem>
                        {csvHeaders.map((header) => {
                          const isCurrentlyMapped = mappedHeaders.includes(header);
                          return (
                            <SelectItem key={header} value={header} disabled={isCurrentlyMapped}>
                              <div className="flex items-center gap-2 w-full">
                                <span className="flex-1 text-left" title={header}>{header}</span>
                                {isCurrentlyMapped && (
                                  <Badge variant="outline" className="text-xs shrink-0">Current</Badge>
                                )}
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Empty state for unmapped fields */}
                {!isMapped && (
                  <div className="text-xs text-gray-500 italic mt-1">
                    No CSV header mapped to this field
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <Card className={`border-0 shadow-lg bg-white/90 backdrop-blur-sm ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <CardTitle className="text-lg">Header Mapping</CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                {stats.totalMapped} of {csvHeaders.length} CSV headers mapped to database fields
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Badge className="bg-green-50 text-green-700 border-green-200 px-3 py-1">
              {stats.mappedPercentage}% Mapped
            </Badge>
            <Button
              onClick={onToggleMappingSection}
              disabled={loadingFields}
              variant="outline"
              size="sm"
              className="text-purple-600 border-purple-200"
            >
              {loadingFields && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
              {showMappingSection ? (
                <>
                  Hide Mapping <ChevronUp className="h-4 w-4 ml-1" />
                </>
              ) : (
                <>
                  Show Mapping <ChevronDown className="h-4 w-4 ml-1" />
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 mt-4">
          <div className="bg-blue-50 border border-blue-100 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">Company</span>
            </div>
            <p className="text-lg font-bold text-blue-900 mt-1">
              {stats.categoryStats.company}
            </p>
          </div>
          
          <div className="bg-purple-50 border border-purple-100 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-900">Contact</span>
            </div>
            <p className="text-lg font-bold text-purple-900 mt-1">
              {stats.categoryStats.contact}
            </p>
          </div>
          
          <div className="bg-green-50 border border-green-100 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-900">Investment</span>
            </div>
            <p className="text-lg font-bold text-green-900 mt-1">
              {stats.categoryStats.investment_criteria}
            </p>
          </div>
          
          <div className="bg-orange-50 border border-orange-100 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <XCircle className="h-4 w-4 text-orange-600" />
              <span className="text-sm font-medium text-orange-900">Unmapped</span>
            </div>
            <p className="text-lg font-bold text-orange-900 mt-1">{stats.unmapped}</p>
          </div>
        </div>
      </CardHeader>

      {showMappingSection && (
        <CardContent>
          <div className="space-y-6">
            {/* Action Bar */}
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-100">
              <div className="flex items-center gap-3">
                <Brain className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="font-medium text-purple-900">
                    Map CSV Headers to Database Fields
                  </p>
                  <p className="text-sm text-purple-700">
                    All available database fields are shown below. Select any CSV header to map to any database field.
                  </p>
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={onReAnalyzeMappings}
                  disabled={loading}
                  variant="outline"
                  size="sm"
                  className="text-purple-600 border-purple-200"
                >
                  {loading && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'hidden' : ''}`} />
                  Re-analyze
                </Button>

                {/* Apply All AI Suggestions Button */}
                {mappingData && (mappingData.company_mappings || mappingData.contact_mappings || mappingData.investment_criteria_mappings) && (
                  <Button
                    onClick={applyAllAISuggestions}
                    variant="outline"
                    size="sm"
                    className="text-blue-600 border-blue-200 hover:bg-blue-50"
                  >
                    <Brain className="h-4 w-4 mr-2" />
                    Apply All AI Suggestions
                  </Button>
                )}
                
                {stats.totalMapped > 0 && (
                  <Button
                    onClick={onConfirmMapping}
                    disabled={mappingConfirmed}
                    size="sm"
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    {mappingConfirmed ? (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Confirmed
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Confirm Mapping
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>


            {/* Categorized Field Mapping using API response structure */}
            <Tabs defaultValue="company" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="company" className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  Company ({dbFieldsByCategory.company.length})
                </TabsTrigger>
                <TabsTrigger value="contact" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Contact ({dbFieldsByCategory.contact.length})
                </TabsTrigger>
                <TabsTrigger value="investment_criteria" className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Investment ({dbFieldsByCategory.investment_criteria.length})
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="company" className="mt-4">
                {renderCategoryFields('company', dbFieldsByCategory.company)}
              </TabsContent>
              
              <TabsContent value="contact" className="mt-4">
                {renderCategoryFields('contact', dbFieldsByCategory.contact)}
              </TabsContent>
              
              <TabsContent value="investment_criteria" className="mt-4">
                {renderCategoryFields('investment_criteria', dbFieldsByCategory.investment_criteria)}
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      )}
    </Card>
  );
} 