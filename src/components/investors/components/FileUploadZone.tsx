"use client";

import React, { useCallback, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, FileText } from "lucide-react";

interface FileUploadZoneProps {
  onFileSelect: (file: File) => void;
  accept?: string;
  maxSize?: number; // in MB
  className?: string;
}

export default function FileUploadZone({
  onFileSelect,
  accept = ".csv,.xlsx,text/csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  maxSize,
  className = ""
}: FileUploadZoneProps) {
  const [dragActive, setDragActive] = useState(false);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    const validFile = files.find(
      (file) =>
        file.type === "text/csv" ||
        file.name.endsWith(".csv") ||
        file.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.name.endsWith(".xlsx")
    );

      if (validFile) {
    // Check file size (only if maxSize is specified and > 0)
    if (maxSize && maxSize > 0 && validFile.size > maxSize * 1024 * 1024) {
      alert(`File size exceeds ${maxSize}MB limit`);
      return;
    }
    onFileSelect(validFile);
  } else {
      alert("Please select a valid CSV or XLSX file");
    }
  }, [onFileSelect, maxSize]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Check file size (only if maxSize is specified and > 0)
      if (maxSize && maxSize > 0 && selectedFile.size > maxSize * 1024 * 1024) {
        alert(`File size exceeds ${maxSize}MB limit`);
        return;
      }
      onFileSelect(selectedFile);
    }
  };

  return (
    <div
      className={`border-2 border-dashed rounded-xl p-12 text-center transition-all duration-200 ${
        dragActive
          ? "border-blue-500 bg-blue-50/50 scale-[1.02]"
          : "border-gray-300 hover:border-blue-400 hover:bg-gray-50/50"
      } ${className}`}
      onDragEnter={handleDrag}
      onDragLeave={handleDrag}
      onDragOver={handleDrag}
      onDrop={handleDrop}
    >
      <div className="flex flex-col items-center">
        <div className="p-4 bg-blue-100 rounded-full mb-4">
          <FileText className="h-12 w-12 text-blue-600" />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Drop your files here
        </h3>
        <p className="text-gray-500 mb-6 max-w-sm">
          Support for CSV and XLSX files{maxSize && maxSize > 0 ? ` up to ${maxSize}MB` : ''}. Drag and drop or click to browse.
        </p>
        <input
          type="file"
          accept={accept}
          onChange={handleFileChange}
          className="hidden"
          id="csv-file-input"
        />
        <Button
          asChild
          size="lg"
          className="bg-blue-600 hover:bg-blue-700"
        >
          <label
            htmlFor="csv-file-input"
            className="cursor-pointer"
          >
            <Upload className="h-5 w-5 mr-2" />
            Browse Files
          </label>
        </Button>
      </div>
    </div>
  );
} 