"use client";

import React, { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  Upload,
  Database,
  BarChart3,
  Activity,
  FileText,
  CheckCircle,
  XCircle,  
  Users,
  Building2,
  RefreshCw,
  Brain,
  TrendingUp,
  AlertTriangle,
} from "lucide-react";

// Import new modular components
import FileUploadZone from "./components/FileUploadZone";
import HeaderMappingSection from "./components/HeaderMappingSection";
import { UploadProgress } from "@/components/ui/upload-progress";

// Import custom hooks
import { useFileUpload } from "@/hooks/useFileUpload";
import { useHeaderMapping } from "@/hooks/useHeaderMapping";
import { useConflictDetection } from "@/hooks/useConflictDetection";
import { useUploadProgress } from "@/hooks/useUploadProgress";

// Import existing components
import UploadStatusDashboard from "../dashboard/uploads/UploadStatusDashboard";
import ConflictDashboard from "../conflicts/ConflictDashboard";

export default function EnhancedCSVUploader() {
  const [activeTab, setActiveTab] = useState("upload");
  
  // File upload hook
  const [fileUploadState, fileUploadActions] = useFileUpload();

  // Header mapping hook
  const [mappingState, mappingActions] = useHeaderMapping(
    fileUploadState.parsedData?.headers || [],
    fileUploadState.parsedData?.sampleRow || null,
  );
  
  // Conflict detection hook
  const [conflictState, conflictActions] = useConflictDetection();
  
  // Upload progress tracking hook
  const [uploadProgressState, uploadProgressActions] = useUploadProgress();

  const handleFileSelect = useCallback(async (file: File) => {
    fileUploadActions.setFile(file);
    await fileUploadActions.parseFile(file);
    
    // Reset other states when new file is selected
    mappingActions.reset();
    conflictActions.reset();
  }, [fileUploadActions, mappingActions, conflictActions]);

  const handleHeaderMappingChange = useCallback((csvHeader: string, dbField: string) => {
    if (dbField === "") {
      // This is a removal - remove all mappings for this header
      const newMappings = { ...mappingState.headerMappings };
      delete newMappings[csvHeader];
      mappingActions.setHeaderMappings(newMappings);
    } else {
      // Add mapping using the addMapping function
      mappingActions.addMapping(csvHeader, dbField);
    }
  }, [mappingState.headerMappings, mappingActions]);

  const handleRemoveSpecificMapping = useCallback((csvHeader: string, dbField: string) => {
    mappingActions.removeMapping(csvHeader, dbField);
  }, [mappingActions]);

  // Helper function to convert array mappings to string mappings for backward compatibility
  const getStringMappings = useCallback((arrayMappings: Record<string, string[]>): Record<string, string> => {
    const stringMappings: Record<string, string> = {};
    Object.entries(arrayMappings).forEach(([csvHeader, dbFields]) => {
      if (dbFields.length > 0) {
        // For backward compatibility, use the first mapping
        stringMappings[csvHeader] = dbFields[0];
      }
    });
    return stringMappings;
  }, []);

  const handleConfirmMapping = useCallback(() => {
    mappingActions.confirmMapping(mappingState.headerMappings);
    
    // Process investment criteria when mapping is confirmed
    if (fileUploadState.parsedData) {
      // Create structured mappings from mappingData if available
      const structuredMappings = mappingState.mappingData ? {
        company_mappings: mappingState.mappingData.company_mappings || {},
        contact_mappings: mappingState.mappingData.contact_mappings || {},
        investment_criteria_mappings: mappingState.mappingData.investment_criteria_mappings || {},
        unmapped_headers: mappingState.mappingData.unmapped_headers || [],
        database_fields: mappingState.mappingData.database_fields || { companies: [], contacts: [], investment_criteria: [] }
      } : undefined;
      
      fileUploadActions.processInvestmentCriteria(
        getStringMappings(mappingState.headerMappings),
        structuredMappings
      );
    }
  }, [mappingState.headerMappings, mappingState.mappingData, mappingActions, fileUploadState.parsedData, fileUploadActions, getStringMappings]);

  const handleReAnalyzeMappings = useCallback(async () => {
    if (fileUploadState.parsedData) {
      await mappingActions.reAnalyzeMappings(
        fileUploadState.parsedData.headers,
        fileUploadState.parsedData.sampleRow || {}
      );
    }
  }, [fileUploadState.parsedData, mappingActions]);

  const handleUpload = useCallback(async () => {
    if (!fileUploadState.parsedData?.data || !mappingState.mappingConfirmed || !fileUploadState.file) return;

    // Start upload
    uploadProgressActions.startUpload();

    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', fileUploadState.file!);
      formData.append('headerMappings', JSON.stringify(mappingState.headerMappings));
      
      // Add structured mappings if available
      if (mappingState.mappingData) {
        const structuredMappings = {
          company_mappings: mappingState.mappingData.company_mappings,
          contact_mappings: mappingState.mappingData.contact_mappings,
          investment_criteria_mappings: mappingState.mappingData.investment_criteria_mappings,
          unmapped_headers: mappingState.mappingData.unmapped_headers,
          database_fields: mappingState.mappingData.database_fields,
          suggestions: mappingState.mappingData.suggestions
        };
        formData.append('structuredMappings', JSON.stringify(structuredMappings));
      }

      // Add LLM metadata if available from the mapping process
      if (mappingState.mappingData?.llm_metadata) {
        const llmMetadata = {
          llmUsed: mappingState.mappingData.llm_metadata.model || 'unknown',
          prompt: mappingState.mappingData.llm_metadata.prompt_used || '',
          input: mappingState.mappingData.llm_metadata.input_provided || '',
          output: mappingState.mappingData.llm_metadata.output_received || '',
          tokensUsed: mappingState.mappingData.llm_metadata.tokens_consumed || 0
        };
        formData.append('llmMetadata', JSON.stringify(llmMetadata));
      }
      
      formData.append('source', `Data Upload ${new Date().toISOString().split('T')[0]}`);
      formData.append('uploadedBy', 'web_user');

      const response = await fetch('/api/investors/upload-async', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success && result.upload_id) {
        const rowCount = fileUploadState.parsedData?.data.length || 0;
        
        // Mark upload as completed and ready for processing
        uploadProgressActions.completeUpload(result);
        
        // Store the upload ID for manual triggering
        fileUploadActions.setUploadId(result.upload_id);
        
        console.log(`✅ Upload successful: ${result.upload_id} with ${rowCount} rows ready for batch processing`);
      } else {
        throw new Error(result.error || "Upload failed");
      }

    } catch (error) {
      console.error('Upload error:', error);
      uploadProgressActions.failUpload(
        error instanceof Error ? error.message : 'Upload failed. Please try again.'
      );
    }
  }, [
    fileUploadState.parsedData,
    fileUploadState.file,
    mappingState.mappingConfirmed,
    mappingState.headerMappings,
    uploadProgressActions,
    fileUploadActions
  ]);

  const handleReset = useCallback(() => {
    fileUploadActions.resetUpload();
    mappingActions.reset();
    conflictActions.reset();
    uploadProgressActions.reset();
  }, [fileUploadActions, mappingActions, conflictActions, uploadProgressActions]);

  // Calculate statistics
  const totalCount = fileUploadState.parsedData?.data.length || 0;
  
  const showPreview = fileUploadState.file && fileUploadState.parsedData && !fileUploadState.loading;

  // Reusable mapping button component
  const MappingButton = ({
    onClick,
    disabled,
    loading,
    children,
    variant = "outline",
    className = "",
  }: {
    onClick: () => void;
    disabled?: boolean;
    loading?: boolean;
    children: React.ReactNode;
    variant?: "outline" | "default";
    className?: string;
  }) => (
    <Button
      variant={variant}
      onClick={onClick}
      disabled={disabled || loading}
      className={`${className} ${loading ? "cursor-not-allowed" : ""}`}
    >
      {loading ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
          Loading...
        </>
      ) : (
        children
      )}
    </Button>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Data Upload Center
            </h1>
            <p className="text-gray-600 mt-1">
              Upload and manage investor data with intelligent conflict resolution and investment criteria processing
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="px-3 py-1">
              <Activity className="h-4 w-4 mr-2" />
              Smart Processing
            </Badge>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 h-12 bg-white shadow-sm">
            <TabsTrigger
              value="upload"
              className="flex items-center gap-2 text-base font-medium"
            >
              <Upload className="h-5 w-5" />
              Contact & Company Data Upload
            </TabsTrigger>
            <TabsTrigger
              value="dashboard"
              className="flex items-center gap-2 text-base font-medium"
            >
              <BarChart3 className="h-5 w-5" />
              Upload Dashboard
            </TabsTrigger>
            <TabsTrigger
              value="conflicts"
              className="flex items-center gap-2 text-base font-medium"
            >
              <AlertTriangle className="h-5 w-5" />
              Conflict Resolution
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-6 mt-6">
            {/* Upload Section */}
            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl flex items-center gap-2">
                  <Database className="h-6 w-6 text-blue-600" />
                  Upload Contact & Company Data
                </CardTitle>
                <p className="text-gray-600">
                  Upload CSV or XLSX files containing equity investor companies and contacts data. 
                  Our intelligent system automatically detects conflicts and processes investment criteria.
                </p>
              </CardHeader>
              <CardContent>
                {/* File Upload Zone */}
                {!fileUploadState.file && (
                  <FileUploadZone
                    onFileSelect={handleFileSelect}
                  />
                )}

                {/* Loading State - replaced with detailed progress */}
                {fileUploadState.loading && fileUploadState.uploadProgress.phase !== 'idle' && (
                  <div className="py-6">
                    <UploadProgress
                      phase={fileUploadState.uploadProgress.phase}
                      currentRow={fileUploadState.uploadProgress.currentRow}
                      totalRows={fileUploadState.uploadProgress.totalRows}
                      percentage={fileUploadState.uploadProgress.percentage}
                      statusMessage={fileUploadState.uploadProgress.statusMessage}
                    />
                  </div>
                )}

                {/* Simple loading for file parsing */}
                {fileUploadState.loading && fileUploadState.uploadProgress.phase === 'idle' && (
                  <div className="text-center py-12">
                    <div className="inline-flex items-center gap-3 bg-white p-6 rounded-xl shadow-sm">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <div className="text-left">
                        <p className="font-medium text-gray-900">
                          Processing file...
                        </p>
                        <p className="text-sm text-gray-500">
                          Analyzing data structure
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Error State */}
                {fileUploadState.error && (
                  <div className="text-center py-12">
                    <div className="bg-red-50 border border-red-200 p-6 rounded-xl">
                      <p className="text-red-800 font-medium">Error Processing File</p>
                      <p className="text-red-600 text-sm mt-1">{fileUploadState.error}</p>
                      <Button 
                        onClick={handleReset} 
                        variant="outline" 
                        className="mt-4"
                      >
                        Try Again
                      </Button>
                    </div>
                  </div>
                )}

                {/* File Info and Status */}
                {showPreview && (
                  <div className="space-y-6">
                    {/* File Info Card */}
                    <Card className="bg-gradient-to-r from-gray-50 to-slate-50 border border-gray-200">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className="p-3 bg-blue-100 rounded-lg">
                              <FileText className="h-8 w-8 text-blue-600" />
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900">
                                {fileUploadState.file?.name}
                              </h3>
                              <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                                <span className="flex items-center gap-1">
                                  <Database className="h-4 w-4" />
                                  {totalCount} rows
                                </span>
                                <span className="flex items-center gap-1">
                                  <Activity className="h-4 w-4" />
                                  {fileUploadState.file ? fileUploadActions.getFileInfo(fileUploadState.file).sizeFormatted : ''}
                                </span>
                                {mappingState.mappingConfirmed && (
                                  <span className="flex items-center gap-1 text-green-600">
                                    <Brain className="h-4 w-4" />
                                    Headers Mapped
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-3">
                            <Button variant="outline" onClick={handleReset}>
                              Remove
                            </Button>

                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Header Mapping Section */}
                    <HeaderMappingSection
                      csvHeaders={fileUploadState.parsedData?.headers || []}
                      headerMappings={mappingState.headerMappings}
                      mappingData={mappingState.mappingData}
                      showMappingSection={mappingState.showMappingSection}
                      loading={mappingState.loading}
                      loadingFields={mappingState.loadingFields}
                      mappingConfirmed={mappingState.mappingConfirmed}
                      onHeaderMappingChange={handleHeaderMappingChange}
                      onRemoveSpecificMapping={handleRemoveSpecificMapping}
                      onConfirmMapping={handleConfirmMapping}
                      onToggleMappingSection={mappingActions.toggleMappingSection}
                      onReAnalyzeMappings={handleReAnalyzeMappings}
                    />

                    {/* Upload Progress Completion */}
                    {!fileUploadState.loading && fileUploadState.uploadProgress.phase === 'completed' && (
                      <div className="py-4">
                        <UploadProgress
                          phase={fileUploadState.uploadProgress.phase}
                          currentRow={fileUploadState.uploadProgress.currentRow}
                          totalRows={fileUploadState.uploadProgress.totalRows}
                          percentage={fileUploadState.uploadProgress.percentage}
                          statusMessage={fileUploadState.uploadProgress.statusMessage}
                        />
                      </div>
                    )}

                    {/* Batch Processing Trigger - Only show when upload is complete and ready for processing */}
                    {fileUploadState.uploadProgress.phase === 'waiting_for_trigger' && fileUploadState.uploadId && (
                      <Card className="border-blue-200 bg-blue-50">
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="p-2 bg-blue-100 rounded-lg">
                                <Database className="h-6 w-6 text-blue-600" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-blue-900">
                                  Ready for Batch Processing
                                </h3>
                                <p className="text-sm text-blue-700 mt-1">
                                  {fileUploadState.uploadProgress.statusMessage}
                                </p>
                                <p className="text-sm text-blue-600 mt-1">
                                  Click "Start Batch Processing" to begin processing {totalCount} rows in batches.
                                </p>
                                <p className="text-xs text-blue-500 mt-1">
                                  ⚡ The system will process data in batches of 50 rows for optimal performance.
                                </p>
                              </div>
                            </div>
                            <div className="flex gap-2">
                              <Button
                                onClick={() => fileUploadActions.triggerWorkerManually(fileUploadState.uploadId!)}
                                disabled={fileUploadState.loading}
                                className="bg-blue-600 hover:bg-blue-700 text-white"
                              >
                                {fileUploadState.loading ? (
                                  <>
                                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                    Starting...
                                  </>
                                ) : (
                                  <>
                                    <Brain className="h-4 w-4 mr-2" />
                                    Start Batch Processing
                                  </>
                                )}
                              </Button>
                              <Button
                                onClick={() => fileUploadActions.checkUploadStatus(fileUploadState.uploadId!)}
                                disabled={fileUploadState.loading}
                                variant="outline"
                                className="border-blue-300 text-blue-700 hover:bg-blue-50"
                              >
                                <RefreshCw className="h-4 w-4 mr-2" />
                                Check Status
                              </Button>
                            </div>
                          </div>
                          <div className="mt-4 p-3 bg-blue-100 rounded-lg">
                            <p className="text-sm text-blue-800">
                              <strong>Upload ID:</strong> {fileUploadState.uploadId} | 
                              <strong> Total Rows:</strong> {totalCount} | 
                              <strong> Batch Size:</strong> 50 rows | 
                              <strong> Status:</strong> Ready for batch processing
                            </p>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Batch Processing Progress */}
                    {fileUploadState.loading && fileUploadState.uploadProgress.phase === 'processing' && (
                      <div className="py-4 space-y-3">
                        <Card className="border-purple-200 bg-purple-50">
                          <CardContent className="p-4">
                            <div className="flex items-center gap-3 mb-3">
                              <div className="p-2 bg-purple-100 rounded-lg">
                                <RefreshCw className="h-5 w-5 text-purple-600 animate-spin" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-purple-900">Batch Processing in Progress</h3>
                                <p className="text-sm text-purple-700">
                                  Processing {fileUploadState.uploadProgress.totalRows} rows in batches of 50
                                </p>
                              </div>
                            </div>
                            <UploadProgress
                              phase={fileUploadState.uploadProgress.phase}
                              currentRow={fileUploadState.uploadProgress.currentRow}
                              totalRows={fileUploadState.uploadProgress.totalRows}
                              percentage={fileUploadState.uploadProgress.percentage}
                              statusMessage={fileUploadState.uploadProgress.statusMessage}
                            />
                          </CardContent>
                        </Card>
                        {fileUploadState.uploadId && (
                          <div className="flex justify-center">
                            <Button
                              onClick={() => fileUploadActions.checkUploadStatus(fileUploadState.uploadId!)}
                              variant="outline"
                              size="sm"
                              className="border-purple-300 text-purple-700 hover:bg-purple-50"
                            >
                              <RefreshCw className="h-4 w-4 mr-2" />
                              Refresh Status
                            </Button>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Investment Criteria Processing Results */}
                    {fileUploadState.investmentCriteriaRecords.length > 0 && (
                      <Card className="border-green-200 bg-green-50">
                        <CardContent className="p-4">
                          <div className="flex items-center gap-3">
                            <TrendingUp className="h-5 w-5 text-green-600" />
                            <div>
                              <h3 className="font-semibold text-green-900">
                                Investment Criteria Processing Complete
                              </h3>
                              <p className="text-sm text-green-700">
                                Generated {fileUploadState.investmentCriteriaRecords.length} investment criteria records
                                {fileUploadState.processingErrors.length > 0 && ` with ${fileUploadState.processingErrors.length} errors`}
                              </p>
                            </div>
                          </div>
                          {fileUploadState.processingErrors.length > 0 && (
                            <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                              <p className="text-sm font-medium text-red-800">Processing Errors:</p>
                              <ul className="text-sm text-red-700 mt-1 space-y-1">
                                {fileUploadState.processingErrors.slice(0, 5).map((error, index) => (
                                  <li key={index}>• {error}</li>
                                ))}
                                {fileUploadState.processingErrors.length > 5 && (
                                  <li>• ... and {fileUploadState.processingErrors.length - 5} more</li>
                                )}
                              </ul>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* Simple Upload Status */}
                    {uploadProgressState.uploading && (
                      <Card className={`border-2 ${
                        uploadProgressState.status === 'failed' ? 'bg-red-50 border-red-200' :
                        uploadProgressState.status === 'completed' ? 'bg-green-50 border-green-200' :
                        'bg-blue-50 border-blue-200'
                      }`}>
                        <CardContent className="p-6 text-center">
                          {uploadProgressState.status === 'failed' ? (
                            <div className="space-y-4">
                              <XCircle className="h-12 w-12 text-red-600 mx-auto" />
                              <div>
                                <h3 className="font-semibold text-red-900 text-lg">Upload Failed</h3>
                                <p className="text-red-700 mt-1">{uploadProgressState.error}</p>
                              </div>
                              <Button
                                onClick={handleReset}
                                variant="outline"
                                className="border-red-300 text-red-700 hover:bg-red-50"
                              >
                                <RefreshCw className="h-4 w-4 mr-2" />
                                Try Again
                              </Button>
                            </div>
                          ) : uploadProgressState.status === 'completed' ? (
                            <div className="space-y-4">
                              <CheckCircle className="h-12 w-12 text-green-600 mx-auto" />
                              <div>
                                <h3 className="font-semibold text-green-900 text-lg">Upload Successful!</h3>
                                <p className="text-green-700 mt-1">Your data has been uploaded and is ready for batch processing</p>
                                <p className="text-green-600 text-sm mt-1">
                                  Ready to process {totalCount} rows in optimized batches
                                </p>
                              </div>
                              <div className="flex gap-3 justify-center">
                                <Button
                                  onClick={() => setActiveTab("dashboard")}
                                  className="bg-green-600 hover:bg-green-700"
                                >
                                  View Dashboard
                                </Button>
                                <Button
                                  onClick={() => setActiveTab("conflicts")}
                                  variant="outline"
                                  className="border-orange-300 text-orange-700 hover:bg-orange-50"
                                >
                                  <AlertTriangle className="h-4 w-4 mr-2" />
                                  Check Conflicts
                                </Button>
                                <Button
                                  onClick={handleReset}
                                  variant="outline"
                                >
                                  Upload Another File
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div className="space-y-4">
                              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                              <div>
                                <h3 className="font-semibold text-blue-900 text-lg">Uploading...</h3>
                                <p className="text-blue-700 mt-1">Please wait while we prepare your file for batch processing</p>
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* Enhanced Preview Table - Show immediately after file upload */}
                    {showPreview && (
                      <Card className="overflow-hidden shadow-lg">
                        <CardHeader className="bg-gray-50 border-b">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-lg">
                              Data Preview
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              <Badge
                                variant="outline"
                                className="bg-blue-50 text-blue-700 border-blue-200"
                              >
                                {fileUploadState.parsedData?.headers.length || 0} columns
                              </Badge>
                              <Badge
                                variant="outline"
                                className="bg-green-50 text-green-700 border-green-200"
                              >
                                {totalCount} rows
                              </Badge>
                              {!mappingState.mappingConfirmed && (
                                <Badge
                                  variant="outline"
                                  className="bg-yellow-50 text-yellow-700 border-yellow-200"
                                >
                                  Headers Not Mapped
                                </Badge>
                              )}
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="p-0">
                          <div className="max-h-96 overflow-auto">
                            <table className="w-full text-sm">
                              <thead className="bg-gray-100 sticky top-0">
                                <tr>
                                  {fileUploadState.parsedData?.headers.map((header) => (
                                    <th
                                      key={header}
                                      className="p-3 text-left font-medium text-gray-700 min-w-32"
                                    >
                                      <div className="flex flex-col gap-1">
                                        <span className="truncate" title={header}>
                                          {header}
                                        </span>
                                        {mappingState.headerMappings[header] && (
                                          <div className="flex items-center gap-1">
                                                                        {mappingState.headerMappings[header].some(field => field.startsWith("company_") || field === "industry") ? (
                                              <Badge
                                                variant="outline"
                                                className="bg-blue-50 text-blue-600 border-blue-200 text-xs"
                                              >
                                                <Building2 className="h-2 w-2 mr-1" />
                                                Co
                                              </Badge>
                                            ) : (
                                              <Badge
                                                variant="outline"
                                                className="bg-purple-50 text-purple-600 border-purple-200 text-xs"
                                              >
                                                <Users className="h-2 w-2 mr-1" />
                                                Ct
                                              </Badge>
                                            )}
                                            {mappingActions.isAISuggestion(header) && (
                                              <Badge
                                                variant="outline"
                                                className="bg-green-50 text-green-600 border-green-200 text-xs"
                                              >
                                                AI
                                              </Badge>
                                            )}
                                          </div>
                                        )}
                                        {!mappingState.mappingConfirmed && (
                                          <Badge
                                            variant="outline"
                                            className="bg-gray-50 text-gray-600 border-gray-200 text-xs"
                                          >
                                            Raw
                                          </Badge>
                                        )}
                                      </div>
                                    </th>
                                  ))}
                                </tr>
                              </thead>
                              <tbody>
                                {fileUploadState.parsedData?.data.slice(0, 10).map((row, index) => (
                                  <tr
                                    key={index}
                                    className="border-t hover:bg-gray-50 transition-colors"
                                  >
                                    {fileUploadState.parsedData?.headers.map((header) => (
                                       <td key={header} className="p-3">
                                         <div
                                           className="max-w-32 truncate text-gray-700"
                                           title={(row as any)[header] || ""}
                                         >
                                           {(row as any)[header] || "-"}
                                         </div>
                                       </td>
                                     ))}
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                            {totalCount > 10 && (
                              <div className="p-4 bg-gray-50 border-t text-center">
                                <p className="text-sm text-gray-600">
                                  Showing first 10 rows of {totalCount} total rows
                                  {!mappingState.mappingConfirmed && (
                                    <span className="text-yellow-600 ml-2">
                                      • Use Header Mapping below to map columns to database fields
                                    </span>
                                  )}
                                </p>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Simple Upload Option */}
                    {mappingState.mappingConfirmed && !uploadProgressState.uploading && (
                      <Card className="border-green-200 bg-green-50">
                        <CardContent className="p-6 text-center">
                          <div className="flex items-center justify-center gap-3 mb-4">
                            <Database className="h-8 w-8 text-green-600" />
                            <div>
                              <h3 className="font-semibold text-green-900">
                                Ready for Upload
                              </h3>
                              <p className="text-sm text-green-700">
                                {totalCount} rows mapped and ready to upload
                              </p>
                            </div>
                          </div>
                          <Button
                            onClick={handleUpload}
                            className="bg-green-600 hover:bg-green-700"
                            size="lg"
                            disabled={uploadProgressState.uploading}
                          >
                            <Upload className="h-4 w-4 mr-2" />
                            Upload Data
                          </Button>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="dashboard" className="mt-6">
            <UploadStatusDashboard />
          </TabsContent>



          <TabsContent value="conflicts" className="mt-6">
            <ConflictDashboard 
              onNavigateToUpload={() => setActiveTab("upload")}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
