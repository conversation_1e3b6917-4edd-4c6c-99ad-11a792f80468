/**
 * Example: How to test if schedulers are working
 */

import { processorScheduler } from '../lib/scheduler/ProcessorScheduler'

// Example 1: Check if scheduler is running and get job status
async function checkSchedulerStatus() {
  console.log('=== Scheduler Status Check ===')
  
  // Get all scheduled jobs
  const jobs = processorScheduler.getJobs()
  console.log(`Total jobs configured: ${jobs.length}`)
  
  // Show enabled jobs
  const enabledJobs = jobs.filter(job => job.enabled)
  console.log(`Enabled jobs: ${enabledJobs.length}`)
  
  enabledJobs.forEach(job => {
    console.log(`- ${job.id}: ${job.stage} (${job.schedule})`)
    console.log(`  Status: ${job.isRunning ? 'Running' : 'Idle'}`)
    console.log(`  Last run: ${job.lastRun ? new Date(job.lastRun).toLocaleString() : 'Never'}`)
    console.log(`  Next run: ${job.nextRun ? new Date(job.nextRun).toLocaleString() : 'Unknown'}`)
  })
  
  // Get currently running jobs
  const runningJobs = processorScheduler.getRunningJobs()
  console.log(`Currently running jobs: ${runningJobs.length}`)
  
  runningJobs.forEach(job => {
    console.log(`- ${job.jobId}: ${job.stage}`)
    console.log(`  Started: ${job.startTime.toLocaleString()}`)
    console.log(`  Duration: ${job.endTime ? `${Math.round((job.endTime.getTime() - job.startTime.getTime()) / 1000)}s` : 'Still running'}`)
  })
}

// Example 2: Test manual job execution
async function testManualJobExecution() {
  console.log('\n=== Testing Manual Job Execution ===')
  
  try {
    // Test email validation with small limit
    console.log('Testing email validation with limit 5...')
    const result = await processorScheduler.executeManualJob('email_validation', {
      limit: 5
    })
    
    if (result) {
      console.log('✅ Email validation test successful:')
      console.log(`  Processed: ${result.processed}`)
      console.log(`  Successful: ${result.successful}`)
      console.log(`  Failed: ${result.failed}`)
    } else {
      console.log('❌ Email validation test failed')
    }
  } catch (error) {
    console.error('❌ Error testing manual job:', error)
  }
}

// Example 3: Test scheduled job execution
async function testScheduledJobExecution() {
  console.log('\n=== Testing Scheduled Job Execution ===')
  
  try {
    // Get email validation job
    const emailJob = processorScheduler.getJobStatus('email_validation_auto')
    
    if (emailJob) {
      console.log(`Testing scheduled job: ${emailJob.id}`)
      console.log(`Current status: ${emailJob.isRunning ? 'Running' : 'Idle'}`)
      
      if (!emailJob.isRunning) {
        console.log('Executing scheduled job...')
        const result = await processorScheduler.executeJob(emailJob.id)
        
        if (result) {
          console.log('✅ Scheduled job execution successful:')
          console.log(`  Processed: ${result.processed}`)
          console.log(`  Successful: ${result.successful}`)
          console.log(`  Failed: ${result.failed}`)
        } else {
          console.log('❌ Scheduled job execution failed')
        }
      } else {
        console.log('⚠️ Job is already running, skipping test')
      }
    } else {
      console.log('❌ Email validation job not found')
    }
  } catch (error) {
    console.error('❌ Error testing scheduled job:', error)
  }
}

// Example 4: Monitor job execution in real-time
async function monitorJobExecution() {
  console.log('\n=== Real-time Job Monitoring ===')
  
  const checkInterval = setInterval(async () => {
    const runningJobs = processorScheduler.getRunningJobs()
    
    if (runningJobs.length > 0) {
      console.log(`\n[${new Date().toLocaleTimeString()}] Running jobs: ${runningJobs.length}`)
      
      runningJobs.forEach(job => {
        const duration = Math.round((Date.now() - job.startTime.getTime()) / 1000)
        console.log(`  - ${job.jobId}: ${job.stage} (${duration}s)`)
      })
    } else {
      console.log(`\n[${new Date().toLocaleTimeString()}] No jobs running`)
      clearInterval(checkInterval)
    }
  }, 5000) // Check every 5 seconds
  
  // Stop monitoring after 2 minutes
  setTimeout(() => {
    clearInterval(checkInterval)
    console.log('\nMonitoring stopped')
  }, 120000)
}

// Example 5: Test job limits and configuration
async function testJobLimits() {
  console.log('\n=== Testing Job Limits ===')
  
  try {
    // Test with different limits
    const limits = [10, 50, 100]
    
    for (const limit of limits) {
      console.log(`\nTesting with limit: ${limit}`)
      
      const result = await processorScheduler.executeManualJob('email_validation', {
        limit: limit
      })
      
      if (result) {
        console.log(`  ✅ Processed ${result.processed} records`)
        console.log(`  ✅ Success rate: ${result.successful}/${result.processed} (${Math.round((result.successful / result.processed) * 100)}%)`)
      } else {
        console.log(`  ❌ Failed to process with limit ${limit}`)
      }
      
      // Wait a bit between tests
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
  } catch (error) {
    console.error('❌ Error testing job limits:', error)
  }
}

// Example 6: Clean up stuck jobs
function cleanupStuckJobs() {
  console.log('\n=== Cleaning Up Stuck Jobs ===')
  
  processorScheduler.cleanupStuckJobs()
  console.log('✅ Cleanup completed')
}

// Example 7: Comprehensive scheduler test
async function comprehensiveSchedulerTest() {
  console.log('🚀 Starting Comprehensive Scheduler Test')
  
  // Step 1: Check initial status
  await checkSchedulerStatus()
  
  // Step 2: Test manual execution
  await testManualJobExecution()
  
  // Step 3: Test scheduled job execution
  await testScheduledJobExecution()
  
  // Step 4: Test job limits
  await testJobLimits()
  
  // Step 5: Clean up
  cleanupStuckJobs()
  
  // Step 6: Final status check
  await checkSchedulerStatus()
  
  console.log('\n✅ Comprehensive test completed!')
}

// Export functions for use
export {
  checkSchedulerStatus,
  testManualJobExecution,
  testScheduledJobExecution,
  monitorJobExecution,
  testJobLimits,
  cleanupStuckJobs,
  comprehensiveSchedulerTest
}

// Example usage:
// import { comprehensiveSchedulerTest } from './scheduler-testing'
// comprehensiveSchedulerTest()
