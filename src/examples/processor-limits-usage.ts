/**
 * Example usage of the new processor limits configuration system
 */

import { processorScheduler } from '../lib/scheduler/ProcessorScheduler'
import { getProcessorLimit, validateProcessorLimit } from '../config/processor-limits'

// Example 1: Check current limits for email validator
async function checkEmailValidatorLimits() {
  const limits = getProcessorLimit('email_validator')
  console.log('Email Validator Limits:', limits)
  // Output: { defaultLimit: 100, maxLimit: 1000, batchSize: 20 }
}

// Example 2: Validate a custom limit
function validateCustomLimit() {
  const requestedLimit = 1500
  const validatedLimit = validateProcessorLimit('email_validator', requestedLimit)
  console.log(`Requested: ${requestedLimit}, Validated: ${validatedLimit}`)
  // Output: Requested: 1500, Validated: 1000 (capped at max limit)
}

// Example 3: Run manual job with default limits
async function runManualJobWithDefaults() {
  const result = await processorScheduler.executeManualJob('email_validation')
  console.log('Manual job result:', result)
  // Will use default limit of 100 records
}

// Example 4: Run manual job with custom limits
async function runManualJobWithCustomLimits() {
  const result = await processorScheduler.executeManualJob('email_validation', {
    limit: 200,        // Process 200 records
    batchSize: 25      // Process in batches of 25
  })
  console.log('Custom manual job result:', result)
}

// Example 5: Run manual job with validated limits
async function runManualJobWithValidatedLimits() {
  const requestedLimit = 1500
  const validatedLimit = validateProcessorLimit('email_validator', requestedLimit)
  
  const result = await processorScheduler.executeManualJob('email_validation', {
    limit: validatedLimit
  })
  console.log('Validated manual job result:', result)
}

// Example 6: Check all processor limits
async function checkAllProcessorLimits() {
  const processors = [
    'email_validator',
    'company_web_crawler',
    'company_overview',
    'company_overview_v2',
    'company_investment_criteria',
    'contact_enrichment',
    'contact_enrichment_v2',
    'contact_investment_criteria',
    'email_generation',
    'news_html_fetcher',
    'news_enrichment'
  ]

  for (const processor of processors) {
    const limits = getProcessorLimit(processor as any)
    console.log(`${processor}:`, limits)
  }
}

// Example 7: Environment variable configuration
function showEnvironmentVariableExample() {
  console.log(`
To configure processor limits via environment variables, add these to your .env file:

# Email Validator - Process 150 records per run, max 1200, batch size 25
EMAIL_VALIDATOR_DEFAULT_LIMIT=150
EMAIL_VALIDATOR_MAX_LIMIT=1200
EMAIL_VALIDATOR_BATCH_SIZE=25

# Company Web Crawler - Process 75 records per run, max 600, batch size 20
COMPANY_WEB_CRAWLER_DEFAULT_LIMIT=75
COMPANY_WEB_CRAWLER_MAX_LIMIT=600
COMPANY_WEB_CRAWLER_BATCH_SIZE=20

# Contact Enrichment V2 - Process 125 records per run, max 1500, batch size 30
CONTACT_ENRICHMENT_V2_DEFAULT_LIMIT=125
CONTACT_ENRICHMENT_V2_MAX_LIMIT=1500
CONTACT_ENRICHMENT_V2_BATCH_SIZE=30
  `)
}

// Example 8: API usage
async function showAPIUsage() {
  console.log(`
API Usage Examples:

1. Get current limits:
   GET /api/processor-limits

2. Update email validator limits:
   POST /api/processor-limits
   {
     "processorName": "email_validator",
     "limit": 150,
     "maxLimit": 1200,
     "batchSize": 25
   }

3. Update only the default limit:
   POST /api/processor-limits
   {
     "processorName": "company_web_crawler",
     "limit": 75
   }
  `)
}

// Example 9: Scheduled job behavior
function explainScheduledJobBehavior() {
  console.log(`
Scheduled Job Behavior:

1. Email Validator (every 30 minutes):
   - Will process 100 records (default limit)
   - In batches of 20 records
   - With 10 concurrent jobs per batch

2. Company Web Crawler (every hour):
   - Will process 50 records (default limit)
   - In batches of 15 records
   - With 3 concurrent jobs per batch

3. Company Overview V2 (every 2 hours):
   - Will process 50 records (default limit)
   - In batches of 15 records
   - With 10 concurrent jobs per batch
   - With 1.5 second delay between requests
  `)
}

// Example 10: Performance monitoring
async function monitorJobPerformance() {
  console.log(`
Performance Monitoring:

1. Check job status:
   const jobs = processorScheduler.getJobs()
   const runningJobs = processorScheduler.getRunningJobs()

2. Monitor specific job:
   const jobStatus = processorScheduler.getJobStatus('email_validation_auto')

3. Clean up stuck jobs:
   processorScheduler.cleanupStuckJobs()
  `)
}

// Export examples for use
export {
  checkEmailValidatorLimits,
  validateCustomLimit,
  runManualJobWithDefaults,
  runManualJobWithCustomLimits,
  runManualJobWithValidatedLimits,
  checkAllProcessorLimits,
  showEnvironmentVariableExample,
  showAPIUsage,
  explainScheduledJobBehavior,
  monitorJobPerformance
}
