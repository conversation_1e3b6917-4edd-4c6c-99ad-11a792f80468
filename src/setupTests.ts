// Setup file for Jest tests

// Mock global fetch for Node.js environment
global.fetch = jest.fn();

// Mock console methods to avoid noise in tests
const originalError = console.error;
const originalWarn = console.warn;
const originalLog = console.log;

beforeEach(() => {
  // Reset all mocks before each test
  jest.resetAllMocks();
  
  // Mock console methods to avoid noise during tests
  console.error = jest.fn();
  console.warn = jest.fn();
  console.log = jest.fn();
});

afterEach(() => {
  // Restore console methods after each test
  console.error = originalError;
  console.warn = originalWarn;
  console.log = originalLog;
});

// Mock the pool from db.ts to avoid database connections in tests
jest.mock('@/lib/db', () => ({
  pool: {
    connect: jest.fn().mockResolvedValue({
      query: jest.fn(),
      release: jest.fn(),
    }),
  },
}));

// Mock the AsyncUploadService to avoid file system operations
jest.mock('@/lib/services/AsyncUploadService', () => ({
  AsyncUploadService: {
    markUploadAsProcessing: jest.fn(),
    getUploadStatus: jest.fn(),
    getDataForProcessing: jest.fn(),
    markDataRowAsProcessed: jest.fn(),
    updateProgress: jest.fn(),
    markUploadAsCompleted: jest.fn(),
    markUploadAsFailed: jest.fn(),
    getPendingUploads: jest.fn(),
  },
}));

// Mock dealConflictDetector to avoid complex conflict detection in tests
jest.mock('@/lib/utils/dealConflictDetector', () => ({
  findPotentialDealDuplicates: jest.fn().mockResolvedValue([]),
}));

// Set longer timeout for integration tests
jest.setTimeout(30000); 