import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

interface ColumnGroup {
  name: string
  description: string
  columns: string[]
}

interface SimpleV2Metrics {
  totalRows: number
  completedV2Rows: number
  pendingV2Rows: number
  failedV2Rows: number
  overallPercentage: number
  columnGroups: Array<{
    name: string
    description: string
    columns: Array<{
      column_name: string
      populated_count: number
      populated_percentage: number
    }>
  }>
  hourlyProcessingStats: Array<{
    hour: string
    processed_count: number
    completed_count: number
    failed_count: number
  }>
  mappingValidation: {
    total_records: number
    company_type_validation: {
      valid_count: number
      invalid_count: number
      invalid_values: string[]
    }
    capital_position_validation: {
      valid_count: number
      invalid_count: number
      invalid_values: string[]
    }
    investment_focus_validation: {
      valid_count: number
      invalid_count: number
      invalid_values: string[]
    }
    overall_mapping_validation_percentage: number
  }
}

// Define column groups based on the CompanyOverviewProcessorV2 structure
const COLUMN_GROUPS: ColumnGroup[] = [
  {
    name: 'Core Company Information',
    description: 'Basic company details and identification',
    columns: ['company_type', 'business_model']
  },
  {
    name: 'Investment & Strategy',
    description: 'Investment focus and strategic approach',
    columns: ['capital_position', 'investment_focus', 'investment_strategy_mission', 'investment_strategy_approach']
  },
  {
    name: 'Contact Information',
    description: 'Company contact details and social media',
    columns: ['secondary_phone', 'main_email', 'secondary_email', 'twitter', 'facebook', 'instagram', 'youtube']
  },
  {
    name: 'Address Information',
    description: 'Location and office details',
    columns: ['additional_address', 'additional_city', 'additional_state', 'additional_zipcode', 'additional_country', 'office_locations']
  },
  {
    name: 'Company Metrics',
    description: 'Key business metrics and financial data',
    columns: ['fund_size', 'aum', 'number_of_properties', 'number_of_offices', 'number_of_employees', 'annual_revenue', 'net_income', 'ebitda', 'profit_margin', 'market_capitalization', 'market_share_percentage']
  },
  {
    name: 'Financial Information',
    description: 'Financial strength and funding details',
    columns: ['balance_sheet_strength', 'funding_sources', 'recent_capital_raises', 'typical_debt_to_equity_ratio', 'development_fee_structure', 'credit_rating', 'dry_powder', 'annual_deployment_target']
  },
  {
    name: 'Investment & Fund Information',
    description: 'Fund details and investment vehicle information',
    columns: ['investment_vehicle_type', 'active_fund_name_series', 'fund_size_active_fund', 'fundraising_status', 'lender_type', 'annual_loan_volume', 'portfolio_health']
  },
  {
    name: 'Partnership & Leadership',
    description: 'Partnerships, board, and executive information',
    columns: ['partnerships', 'key_equity_partners', 'key_debt_partners', 'board_of_directors', 'key_executives', 'founder_background']
  },
  {
    name: 'Market Positioning & Strategy',
    description: 'Market strategy and positioning details',
    columns: ['market_cycle_positioning', 'urban_vs_suburban_preference', 'sustainability_esg_focus', 'technology_proptech_adoption', 'adaptive_reuse_experience', 'regulatory_zoning_expertise']
  },
  {
    name: 'Corporate Structure',
    description: 'Corporate organization and structure',
    columns: ['corporate_structure', 'parent_company', 'subsidiaries', 'stock_ticker_symbol', 'stock_exchange']
  },
  {
    name: 'Business Information',
    description: 'Products, services, and business details',
    columns: ['products_services_description', 'target_customer_profile', 'major_competitors', 'unique_selling_proposition', 'industry_awards_recognitions', 'company_history']
  },
  {
    name: 'Transaction & Portfolio Data',
    description: 'Deal and portfolio metrics',
    columns: ['transactions_completed_last_12m', 'total_transaction_volume_ytd', 'deal_count_ytd', 'average_deal_size', 'portfolio_size_sqft', 'portfolio_asset_count', 'role_in_previous_deal']
  },
  {
    name: 'Relationship & Pipeline Data',
    description: 'Relationship management and pipeline status',
    columns: ['internal_relationship_manager', 'pipeline_status', 'recent_news_sentiment']
  },
  {
    name: 'Data Quality & Processing',
    description: 'Data source and quality metrics',
    columns: ['data_source', 'data_confidence_score', 'quarterly_earnings_link']
  }
]

// Define data types for proper SQL handling
const ARRAY_COLUMNS = [
  'capital_position', 'investment_focus', 'office_locations', 'funding_sources', 
  'partnerships', 'key_equity_partners', 'key_debt_partners', 'board_of_directors', 
  'key_executives', 'subsidiaries', 'major_competitors', 'industry_awards_recognitions'
]

const NUMERIC_COLUMNS = [
  'fund_size', 'aum', 'annual_revenue', 'net_income', 'ebitda', 'market_capitalization',
  'dry_powder', 'annual_deployment_target', 'fund_size_active_fund', 'annual_loan_volume',
  'total_transaction_volume_ytd', 'average_deal_size'
]

const INTEGER_COLUMNS = [
  'number_of_properties', 'number_of_offices', 'number_of_employees',
  'transactions_completed_last_12m', 'deal_count_ytd', 'portfolio_size_sqft', 'portfolio_asset_count'
]

const BOOLEAN_COLUMNS = [
  'sustainability_esg_focus', 'technology_proptech_adoption', 'adaptive_reuse_experience', 'regulatory_zoning_expertise'
]

const DATE_COLUMNS = [
  'last_contact_date'
]

const DOUBLE_COLUMNS = [
  'profit_margin', 'market_share_percentage', 'typical_debt_to_equity_ratio', 'data_confidence_score'
]

async function getSimpleV2Metrics(): Promise<SimpleV2Metrics> {
  const client = await pool.connect()
  try {
    console.log('Fetching simple company overview V2 metrics')
    
    // Build one big optimized query
    const allColumns = COLUMN_GROUPS.flatMap(group => group.columns)
    
    // Create CASE statements for each column based on its data type
    const caseStatements = allColumns.map(col => {
      if (ARRAY_COLUMNS.includes(col)) {
        return `COUNT(CASE WHEN COALESCE(overview_v2_status, 'pending') = 'completed' AND ${col} IS NOT NULL AND array_length(${col}, 1) > 0 THEN 1 END) as ${col}_count`
      } else if (NUMERIC_COLUMNS.includes(col) || INTEGER_COLUMNS.includes(col) || DOUBLE_COLUMNS.includes(col)) {
        return `COUNT(CASE WHEN COALESCE(overview_v2_status, 'pending') = 'completed' AND ${col} IS NOT NULL THEN 1 END) as ${col}_count`
      } else if (BOOLEAN_COLUMNS.includes(col)) {
        return `COUNT(CASE WHEN COALESCE(overview_v2_status, 'pending') = 'completed' AND ${col} IS NOT NULL THEN 1 END) as ${col}_count`
      } else if (DATE_COLUMNS.includes(col)) {
        return `COUNT(CASE WHEN COALESCE(overview_v2_status, 'pending') = 'completed' AND ${col} IS NOT NULL THEN 1 END) as ${col}_count`
      } else {
        return `COUNT(CASE WHEN COALESCE(overview_v2_status, 'pending') = 'completed' AND ${col} IS NOT NULL AND ${col} != '' THEN 1 END) as ${col}_count`
      }
    }).join(',\n    ')

    // Run parallel queries for main metrics, hourly stats, and mapping validation
    const [mainResult, hourlyResult, mappingResult] = await Promise.all([
      // Main query
      client.query(`
        SELECT 
          COUNT(*) as total_rows,
          COUNT(CASE WHEN COALESCE(overview_v2_status, 'pending') = 'completed' THEN 1 END) as completed_v2_rows,
          COUNT(CASE WHEN COALESCE(overview_v2_status, 'pending') = 'pending' THEN 1 END) as pending_v2_rows,
          COUNT(CASE WHEN COALESCE(overview_v2_status, 'pending') = 'failed' THEN 1 END) as failed_v2_rows,
          ${caseStatements}
        FROM companies
      `),
      
      // Hourly processing stats (last 24 hours)
      client.query(`
        SELECT 
          DATE_TRUNC('hour', overview_v2_date) as hour,
          COUNT(*) as processed_count,
          COUNT(CASE WHEN overview_v2_status = 'completed' THEN 1 END) as completed_count,
          COUNT(CASE WHEN overview_v2_status = 'failed' THEN 1 END) as failed_count
        FROM companies 
        WHERE overview_v2_date >= NOW() - INTERVAL '24 hours'
          AND overview_v2_date IS NOT NULL
        GROUP BY DATE_TRUNC('hour', overview_v2_date)
        ORDER BY hour DESC
        LIMIT 24
      `),
      
      // Mapping validation query
      client.query(`
        WITH completed_companies AS (
          SELECT *
          FROM companies 
          WHERE COALESCE(overview_v2_status, 'pending') = 'completed'
        )
        SELECT 
          COUNT(*) as total_records,
          
          -- Company Type Validation
          COUNT(CASE WHEN company_type IS NOT NULL 
                     AND EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Company Type' AND (value_1 = company_type OR value_2 = company_type) AND is_active = true) 
                THEN 1 END) as company_type_valid,
          COUNT(CASE WHEN company_type IS NOT NULL 
                     AND NOT EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Company Type' AND (value_1 = company_type OR value_2 = company_type) AND is_active = true) 
                THEN 1 END) as company_type_invalid,
          
          -- Capital Position Validation (handling array fields)
          COUNT(CASE WHEN capital_position IS NOT NULL 
                     AND array_length(capital_position, 1) > 0
                     AND (SELECT bool_and(EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Capital Position' AND (value_1 = unnest_val OR value_2 = unnest_val) AND is_active = true))
                          FROM unnest(capital_position) as unnest_val) 
                THEN 1 END) as capital_position_valid,
          COUNT(CASE WHEN capital_position IS NOT NULL 
                     AND array_length(capital_position, 1) > 0
                     AND NOT (SELECT bool_and(EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Capital Position' AND (value_1 = unnest_val OR value_2 = unnest_val) AND is_active = true))
                              FROM unnest(capital_position) as unnest_val) 
                THEN 1 END) as capital_position_invalid,
          
          -- Investment Focus Validation (handling array fields - should be Property Type, not Strategies)
          COUNT(CASE WHEN investment_focus IS NOT NULL 
                     AND array_length(investment_focus, 1) > 0
                     AND (SELECT bool_and(EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Property Type' AND (value_1 = unnest_val OR value_2 = unnest_val) AND is_active = true))
                          FROM unnest(investment_focus) as unnest_val) 
                THEN 1 END) as investment_focus_valid,
          COUNT(CASE WHEN investment_focus IS NOT NULL 
                     AND array_length(investment_focus, 1) > 0
                     AND NOT (SELECT bool_and(EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Property Type' AND (value_1 = unnest_val OR value_2 = unnest_val) AND is_active = true))
                              FROM unnest(investment_focus) as unnest_val) 
                THEN 1 END) as investment_focus_invalid
                              
        FROM completed_companies
      `)
    ])

    const row = mainResult.rows[0]
    const totalRows = parseInt(row.total_rows)
    const completedV2Rows = parseInt(row.completed_v2_rows)
    const pendingV2Rows = parseInt(row.pending_v2_rows)
    const failedV2Rows = parseInt(row.failed_v2_rows)

    // Process column groups
    const columnGroups = COLUMN_GROUPS.map(group => ({
      name: group.name,
      description: group.description,
      columns: group.columns.map(col => {
        const count = parseInt(row[`${col}_count`] || '0')
        return {
          column_name: col,
          populated_count: count,
          populated_percentage: completedV2Rows > 0 ? Math.round((count / completedV2Rows) * 100) : 0
        }
      })
    }))

    // Calculate overall percentage based on all columns across all groups
    const allColumnData = columnGroups.flatMap(group => group.columns)
    const totalFields = allColumnData.length
    const totalPopulatedFields = allColumnData.reduce((sum, col) => sum + col.populated_percentage, 0)
    const overallPercentage = totalFields > 0 ? Math.round(totalPopulatedFields / totalFields) : 0

    // Process hourly stats
    const hourlyProcessingStats = hourlyResult.rows.map(row => ({
      hour: row.hour,
      processed_count: parseInt(row.processed_count || '0'),
      completed_count: parseInt(row.completed_count || '0'),
      failed_count: parseInt(row.failed_count || '0')
    }))

    // Process mapping validation
    const mappingRow = mappingResult.rows[0] || {}
    const mappingValidation = {
      total_records: parseInt(mappingRow.total_records || '0'),
      company_type_validation: {
        valid_count: parseInt(mappingRow.company_type_valid || '0'),
        invalid_count: parseInt(mappingRow.company_type_invalid || '0'),
        invalid_values: [] // Would need additional query to get specific invalid values
      },
      capital_position_validation: {
        valid_count: parseInt(mappingRow.capital_position_valid || '0'),
        invalid_count: parseInt(mappingRow.capital_position_invalid || '0'),
        invalid_values: []
      },
      investment_focus_validation: {
        valid_count: parseInt(mappingRow.investment_focus_valid || '0'),
        invalid_count: parseInt(mappingRow.investment_focus_invalid || '0'),
        invalid_values: []
      },
      overall_mapping_validation_percentage: 0
    }

    // Calculate overall mapping validation percentage
    const totalValidations = mappingValidation.company_type_validation.valid_count + 
                           mappingValidation.capital_position_validation.valid_count + 
                           mappingValidation.investment_focus_validation.valid_count
    const totalInvalidations = mappingValidation.company_type_validation.invalid_count + 
                             mappingValidation.capital_position_validation.invalid_count + 
                             mappingValidation.investment_focus_validation.invalid_count
    const totalMappingChecks = totalValidations + totalInvalidations
    mappingValidation.overall_mapping_validation_percentage = totalMappingChecks > 0 
      ? Math.round((totalValidations / totalMappingChecks) * 100) 
      : 0

    return {
      totalRows,
      completedV2Rows,
      pendingV2Rows,
      failedV2Rows,
      overallPercentage,
      columnGroups,
      hourlyProcessingStats,
      mappingValidation
    }
  } finally {
    client.release()
  }
}

export async function GET(request: NextRequest) {
  try {
    const data = await getSimpleV2Metrics()
    
    return NextResponse.json({
      success: true,
      data
    })
  } catch (error) {
    console.error('Error fetching V2 metrics:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch V2 metrics'
    }, { status: 500 })
  }
}