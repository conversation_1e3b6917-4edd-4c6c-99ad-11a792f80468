import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const entityType = searchParams.get('entityType') || 'all'; // 'contact', 'company', 'deal', 'all'
    const capitalPosition = searchParams.get('capitalPosition'); // Optional filter by capital position

    console.log('🔍 Fetching V2 matching data quality metrics:', { entityType, capitalPosition });

    // Get comprehensive data quality metrics for V2 matching system
               const [centralMetrics, debtMetrics, equityMetrics, nsfMetrics, positionMetrics, dealsMetrics] = await Promise.all([
             getCentralInvestmentCriteriaMetrics(entityType, capitalPosition),
             getDebtInvestmentCriteriaMetrics(entityType, capitalPosition),
             getEquityInvestmentCriteriaMetrics(entityType, capitalPosition),
             getNSFFieldsMetrics(entityType, capitalPosition),
             getCapitalPositionMetrics(entityType, capitalPosition),
             getDealsV2Metrics(entityType, capitalPosition)
           ]);

               return NextResponse.json({
             success: true,
             data: {
               central_investment_criteria: centralMetrics,
               debt_investment_criteria: debtMetrics,
               equity_investment_criteria: equityMetrics,
               nsf_fields: nsfMetrics,
               capital_positions: positionMetrics,
               deals_v2: dealsMetrics,
               summary: generateSummaryMetrics(centralMetrics, debtMetrics, equityMetrics, nsfMetrics, positionMetrics, dealsMetrics)
             },
      metadata: {
        generated_at: new Date().toISOString(),
        entity_type: entityType,
        capital_position: capitalPosition,
        description: 'V2 matching system data quality metrics across all tables and fields'
      }
    });

  } catch (error) {
    console.error('Error fetching V2 matching data quality metrics:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch V2 matching data quality metrics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function getCentralInvestmentCriteriaMetrics(entityType: string, capitalPosition?: string | null) {
  const whereClause = buildWhereClause(entityType, capitalPosition);
  
  const query = `
    SELECT 
      COUNT(*) as total_records,
      COUNT(CASE WHEN capital_position IS NOT NULL THEN 1 END) as capital_position_populated,
      COUNT(CASE WHEN minimum_deal_size IS NOT NULL THEN 1 END) as minimum_deal_size_populated,
      COUNT(CASE WHEN maximum_deal_size IS NOT NULL THEN 1 END) as maximum_deal_size_populated,
      COUNT(CASE WHEN property_types IS NOT NULL AND property_types != '{}' AND array_length(property_types, 1) > 0 THEN 1 END) as property_types_populated,
      COUNT(CASE WHEN strategies IS NOT NULL AND strategies != '{}' AND array_length(strategies, 1) > 0 THEN 1 END) as strategies_populated,
      COUNT(CASE WHEN region IS NOT NULL AND region != '{}' AND array_length(region, 1) > 0 THEN 1 END) as region_populated,
      COUNT(CASE WHEN state IS NOT NULL AND state != '{}' AND array_length(state, 1) > 0 THEN 1 END) as state_populated,
      COUNT(CASE WHEN city IS NOT NULL AND city != '{}' AND array_length(city, 1) > 0 THEN 1 END) as city_populated,
      COUNT(CASE WHEN country IS NOT NULL AND country != '{}' AND array_length(country, 1) > 0 THEN 1 END) as country_populated,
      COUNT(CASE WHEN entity_type IS NOT NULL THEN 1 END) as entity_type_populated,
      COUNT(CASE WHEN entity_id IS NOT NULL THEN 1 END) as entity_id_populated
    FROM investment_criteria_central
    ${whereClause}
  `;

  const result = await pool.query(query);
  const data = result.rows[0];
  const totalRecords = parseInt(data.total_records) || 0;

  const fields = [
    'capital_position', 'minimum_deal_size', 'maximum_deal_size', 'property_types', 
    'strategies', 'region', 'state', 'city', 'country', 'entity_type', 'entity_id'
  ];

  const fieldMetrics = fields.map(field => ({
    field_name: field,
    display_name: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    populated_count: parseInt(data[`${field}_populated`]) || 0,
    total_count: totalRecords,
    quality_percentage: totalRecords > 0 ? Math.round((parseInt(data[`${field}_populated`]) / totalRecords) * 100) : 0
  }));

  return {
    total_records: totalRecords,
    fields: fieldMetrics,
    overall_quality: totalRecords > 0 ? Math.round(fieldMetrics.reduce((sum, field) => sum + field.quality_percentage, 0) / fieldMetrics.length) : 0
  };
}

async function getDebtInvestmentCriteriaMetrics(entityType: string, capitalPosition?: string | null) {
  const whereClause = buildWhereClause(entityType, capitalPosition, 'debt');
  
  const query = `
    SELECT 
      COUNT(*) as total_records,
      COUNT(CASE WHEN loan_type IS NOT NULL THEN 1 END) as loan_type_populated,
      COUNT(CASE WHEN loan_program IS NOT NULL THEN 1 END) as loan_program_populated,
      COUNT(CASE WHEN min_loan_term IS NOT NULL THEN 1 END) as min_loan_term_populated,
      COUNT(CASE WHEN max_loan_term IS NOT NULL THEN 1 END) as max_loan_term_populated,
      COUNT(CASE WHEN loan_interest_rate IS NOT NULL THEN 1 END) as loan_interest_rate_populated,
      COUNT(CASE WHEN loan_to_value_min IS NOT NULL THEN 1 END) as loan_to_value_min_populated,
      COUNT(CASE WHEN loan_to_value_max IS NOT NULL THEN 1 END) as loan_to_value_max_populated,
      COUNT(CASE WHEN loan_to_cost_min IS NOT NULL THEN 1 END) as loan_to_cost_min_populated,
      COUNT(CASE WHEN loan_to_cost_max IS NOT NULL THEN 1 END) as loan_to_cost_max_populated,
      COUNT(CASE WHEN min_loan_dscr IS NOT NULL THEN 1 END) as min_loan_dscr_populated,
      COUNT(CASE WHEN max_loan_dscr IS NOT NULL THEN 1 END) as max_loan_dscr_populated,
      COUNT(CASE WHEN loan_origination_min_fee IS NOT NULL THEN 1 END) as loan_origination_min_fee_populated,
      COUNT(CASE WHEN loan_origination_max_fee IS NOT NULL THEN 1 END) as loan_origination_max_fee_populated,
      COUNT(CASE WHEN loan_exit_min_fee IS NOT NULL THEN 1 END) as loan_exit_min_fee_populated,
      COUNT(CASE WHEN loan_exit_max_fee IS NOT NULL THEN 1 END) as loan_exit_max_fee_populated,
      COUNT(CASE WHEN recourse_loan IS NOT NULL THEN 1 END) as recourse_loan_populated,
      COUNT(CASE WHEN amortization IS NOT NULL THEN 1 END) as amortization_populated,
      COUNT(CASE WHEN closing_time IS NOT NULL THEN 1 END) as closing_time_populated,
      COUNT(CASE WHEN lien_position IS NOT NULL THEN 1 END) as lien_position_populated,
      COUNT(CASE WHEN prepayment IS NOT NULL THEN 1 END) as prepayment_populated,
      COUNT(CASE WHEN yield_maintenance IS NOT NULL THEN 1 END) as yield_maintenance_populated,
      COUNT(CASE WHEN application_deposit IS NOT NULL THEN 1 END) as application_deposit_populated,
      COUNT(CASE WHEN good_faith_deposit IS NOT NULL THEN 1 END) as good_faith_deposit_populated,
      COUNT(CASE WHEN future_facilities IS NOT NULL THEN 1 END) as future_facilities_populated,
      COUNT(CASE WHEN eligible_borrower IS NOT NULL THEN 1 END) as eligible_borrower_populated,
      COUNT(CASE WHEN occupancy_requirements IS NOT NULL THEN 1 END) as occupancy_requirements_populated,
      COUNT(CASE WHEN debt_program_overview IS NOT NULL THEN 1 END) as debt_program_overview_populated,
      COUNT(CASE WHEN structured_loan_tranche IS NOT NULL THEN 1 END) as structured_loan_tranche_populated,
      COUNT(CASE WHEN loan_type_normalized IS NOT NULL THEN 1 END) as loan_type_normalized_populated,
      COUNT(CASE WHEN rate_lock IS NOT NULL THEN 1 END) as rate_lock_populated,
      COUNT(CASE WHEN rate_type IS NOT NULL THEN 1 END) as rate_type_populated,
      COUNT(CASE WHEN loan_min_debt_yield IS NOT NULL THEN 1 END) as loan_min_debt_yield_populated
    FROM investment_criteria_debt icd
    INNER JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
    ${whereClause}
  `;

  const result = await pool.query(query);
  const data = result.rows[0];
  const totalRecords = parseInt(data.total_records) || 0;

  const fields = [
    'loan_type', 'loan_program', 'min_loan_term', 'max_loan_term', 'loan_interest_rate',
    'loan_to_value_min', 'loan_to_value_max', 'loan_to_cost_min', 'loan_to_cost_max',
    'min_loan_dscr', 'max_loan_dscr', 'loan_origination_min_fee', 'loan_origination_max_fee',
    'loan_exit_min_fee', 'loan_exit_max_fee', 'recourse_loan', 'amortization', 'closing_time',
    'lien_position', 'prepayment', 'yield_maintenance', 'application_deposit', 'good_faith_deposit',
    'future_facilities', 'eligible_borrower', 'occupancy_requirements', 'debt_program_overview',
    'structured_loan_tranche', 'loan_type_normalized', 'rate_lock', 'rate_type', 'loan_min_debt_yield'
  ];

  const fieldMetrics = fields.map(field => ({
    field_name: field,
    display_name: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    populated_count: parseInt(data[`${field}_populated`]) || 0,
    total_count: totalRecords,
    quality_percentage: totalRecords > 0 ? Math.round((parseInt(data[`${field}_populated`]) / totalRecords) * 100) : 0
  }));

  return {
    total_records: totalRecords,
    fields: fieldMetrics,
    overall_quality: totalRecords > 0 ? Math.round(fieldMetrics.reduce((sum, field) => sum + field.quality_percentage, 0) / fieldMetrics.length) : 0
  };
}

async function getEquityInvestmentCriteriaMetrics(entityType: string, capitalPosition?: string | null) {
  const whereClause = buildWhereClause(entityType, capitalPosition, 'equity');
  
  const query = `
    SELECT 
      COUNT(*) as total_records,
      COUNT(CASE WHEN target_return IS NOT NULL THEN 1 END) as target_return_populated,
      COUNT(CASE WHEN minimum_internal_rate_of_return IS NOT NULL THEN 1 END) as minimum_internal_rate_of_return_populated,
      COUNT(CASE WHEN minimum_yield_on_cost IS NOT NULL THEN 1 END) as minimum_yield_on_cost_populated,
      COUNT(CASE WHEN minimum_equity_multiple IS NOT NULL THEN 1 END) as minimum_equity_multiple_populated,
      COUNT(CASE WHEN target_cash_on_cash_min IS NOT NULL THEN 1 END) as target_cash_on_cash_min_populated,
      COUNT(CASE WHEN min_hold_period_years IS NOT NULL THEN 1 END) as min_hold_period_years_populated,
      COUNT(CASE WHEN max_hold_period_years IS NOT NULL THEN 1 END) as max_hold_period_years_populated,
      COUNT(CASE WHEN ownership_requirement IS NOT NULL THEN 1 END) as ownership_requirement_populated,
      COUNT(CASE WHEN attachment_point IS NOT NULL THEN 1 END) as attachment_point_populated,
      COUNT(CASE WHEN max_leverage_tolerance IS NOT NULL THEN 1 END) as max_leverage_tolerance_populated,
      COUNT(CASE WHEN typical_closing_timeline_days IS NOT NULL THEN 1 END) as typical_closing_timeline_days_populated,
      COUNT(CASE WHEN proof_of_funds_requirement IS NOT NULL THEN 1 END) as proof_of_funds_requirement_populated,
      COUNT(CASE WHEN equity_program_overview IS NOT NULL THEN 1 END) as equity_program_overview_populated,
      COUNT(CASE WHEN occupancy_requirements IS NOT NULL THEN 1 END) as occupancy_requirements_populated,
      COUNT(CASE WHEN yield_on_cost IS NOT NULL THEN 1 END) as yield_on_cost_populated,
      COUNT(CASE WHEN target_return_irr_on_equity IS NOT NULL THEN 1 END) as target_return_irr_on_equity_populated,
      COUNT(CASE WHEN equity_multiple IS NOT NULL THEN 1 END) as equity_multiple_populated,
      COUNT(CASE WHEN position_specific_irr IS NOT NULL THEN 1 END) as position_specific_irr_populated,
      COUNT(CASE WHEN position_specific_equity_multiple IS NOT NULL THEN 1 END) as position_specific_equity_multiple_populated
    FROM investment_criteria_equity ice
    INNER JOIN investment_criteria_central icc ON ice.investment_criteria_id = icc.investment_criteria_id
    ${whereClause}
  `;

  const result = await pool.query(query);
  const data = result.rows[0];
  const totalRecords = parseInt(data.total_records) || 0;

  const fields = [
    'target_return', 'minimum_internal_rate_of_return', 'minimum_yield_on_cost', 'minimum_equity_multiple',
    'target_cash_on_cash_min', 'min_hold_period_years', 'max_hold_period_years', 'ownership_requirement',
    'attachment_point', 'max_leverage_tolerance', 'typical_closing_timeline_days', 'proof_of_funds_requirement',
    'equity_program_overview', 'occupancy_requirements', 'yield_on_cost', 'target_return_irr_on_equity',
    'equity_multiple', 'position_specific_irr', 'position_specific_equity_multiple'
  ];

  const fieldMetrics = fields.map(field => ({
    field_name: field,
    display_name: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    populated_count: parseInt(data[`${field}_populated`]) || 0,
    total_count: totalRecords,
    quality_percentage: totalRecords > 0 ? Math.round((parseInt(data[`${field}_populated`]) / totalRecords) * 100) : 0
  }));

  return {
    total_records: totalRecords,
    fields: fieldMetrics,
    overall_quality: totalRecords > 0 ? Math.round(fieldMetrics.reduce((sum, field) => sum + field.quality_percentage, 0) / fieldMetrics.length) : 0
  };
}

async function getNSFFieldsMetrics(entityType: string, capitalPosition?: string | null) {
  const whereClause = buildWhereClause(entityType, capitalPosition, 'nsf');
  
  const query = `
    SELECT 
      COUNT(*) as total_records,
      COUNT(CASE WHEN capital_position IS NOT NULL THEN 1 END) as capital_position_populated,
      COUNT(CASE WHEN source_type IS NOT NULL THEN 1 END) as source_type_populated,
      COUNT(CASE WHEN amount IS NOT NULL THEN 1 END) as amount_populated,
      COUNT(CASE WHEN use_type IS NOT NULL THEN 1 END) as use_type_populated,
      COUNT(CASE WHEN is_required IS NOT NULL THEN 1 END) as is_required_populated
    FROM deal_nsf_fields
    ${whereClause}
  `;

  const result = await pool.query(query);
  const data = result.rows[0];
  const totalRecords = parseInt(data.total_records) || 0;

  const fields = [
    'capital_position', 'source_type', 'amount', 'use_type', 'is_required'
  ];

  const fieldMetrics = fields.map(field => ({
    field_name: field,
    display_name: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    populated_count: parseInt(data[`${field}_populated`]) || 0,
    total_count: totalRecords,
    quality_percentage: totalRecords > 0 ? Math.round((parseInt(data[`${field}_populated`]) / totalRecords) * 100) : 0
  }));

  return {
    total_records: totalRecords,
    fields: fieldMetrics,
    overall_quality: totalRecords > 0 ? Math.round(fieldMetrics.reduce((sum, field) => sum + field.quality_percentage, 0) / fieldMetrics.length) : 0
  };
}

async function getCapitalPositionMetrics(entityType: string, capitalPosition?: string | null) {
  const whereClause = buildWhereClause(entityType, capitalPosition, 'position');
  
  const query = `
    SELECT 
      COUNT(*) as total_records,
      COUNT(CASE WHEN field_name IS NOT NULL THEN 1 END) as field_name_populated,
      COUNT(CASE WHEN weight IS NOT NULL THEN 1 END) as weight_populated,
      COUNT(CASE WHEN is_active IS NOT NULL THEN 1 END) as is_active_populated
    FROM capital_position_field_weights
    ${whereClause}
  `;

  const result = await pool.query(query);
  const data = result.rows[0];
  const totalRecords = parseInt(data.total_records) || 0;

  const fields = [
    'field_name', 'weight', 'is_active'
  ];

  const fieldMetrics = fields.map(field => ({
    field_name: field,
    display_name: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    populated_count: parseInt(data[`${field}_populated`]) || 0,
    total_count: totalRecords,
    quality_percentage: totalRecords > 0 ? Math.round((parseInt(data[`${field}_populated`]) / totalRecords) * 100) : 0
  }));

  return {
    total_records: totalRecords,
    fields: fieldMetrics,
    overall_quality: totalRecords > 0 ? Math.round(fieldMetrics.reduce((sum, field) => sum + field.quality_percentage, 0) / fieldMetrics.length) : 0
  };
}

async function getDealsV2Metrics(entityType: string, capitalPosition?: string | null) {
  const whereClause = buildWhereClause(entityType, capitalPosition, 'deals');
  
  const query = `
    SELECT 
      COUNT(*) as total_records,
      COUNT(CASE WHEN d.deal_name IS NOT NULL AND d.deal_name != '' THEN 1 END) as deal_name_populated,
      COUNT(CASE WHEN d.loan_amount IS NOT NULL AND d.loan_amount > 0 THEN 1 END) as loan_amount_populated,
      COUNT(CASE WHEN EXISTS (
        SELECT 1 FROM deal_nsf_fields dnf 
        WHERE dnf.deal_id = d.deal_id 
        AND dnf.nsf_context = 'sources' 
        AND dnf.is_required = true
      ) THEN 1 END) as ask_amount_populated,
      COUNT(CASE WHEN d.property_id IS NOT NULL THEN 1 END) as property_id_populated,
      COUNT(CASE WHEN d.deal_status IS NOT NULL THEN 1 END) as deal_status_populated,
      COUNT(CASE WHEN d.deal_type IS NOT NULL THEN 1 END) as deal_type_populated,
      COUNT(CASE WHEN EXISTS (
        SELECT 1 FROM deal_nsf_fields dnf 
        WHERE dnf.deal_id = d.deal_id 
        AND dnf.capital_position IS NOT NULL
      ) THEN 1 END) as capital_position_populated
    FROM dealsv2 d
    ${whereClause}
  `;

  const result = await pool.query(query);
  const data = result.rows[0];
  const totalRecords = parseInt(data.total_records) || 0;

  const fields = [
    'deal_name', 'loan_amount', 'ask_amount', 'property_id', 'deal_status', 'deal_type', 'capital_position'
  ];

  const fieldMetrics = fields.map(field => ({
    field_name: field,
    display_name: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    populated_count: parseInt(data[`${field}_populated`]) || 0,
    total_count: totalRecords,
    quality_percentage: totalRecords > 0 ? Math.round((parseInt(data[`${field}_populated`]) / totalRecords) * 100) : 0
  }));

  return {
    total_records: totalRecords,
    fields: fieldMetrics,
    overall_quality: totalRecords > 0 ? Math.round(fieldMetrics.reduce((sum, field) => sum + field.quality_percentage, 0) / fieldMetrics.length) : 0
  };
}

function buildWhereClause(entityType: string, capitalPosition?: string | null, tableType?: string): string {
  let whereConditions: string[] = [];
  
  if (entityType !== 'all') {
    whereConditions.push(`entity_type = '${entityType}'`);
  }
  
  if (capitalPosition) {
    if (tableType === 'debt' || tableType === 'equity') {
      whereConditions.push(`icc.capital_position = '${capitalPosition}'`);
    } else if (tableType === 'nsf') {
      whereConditions.push(`capital_position = '${capitalPosition}'`);
    } else if (tableType === 'position') {
      whereConditions.push(`capital_position = '${capitalPosition}'`);
    } else if (tableType === 'deals') {
      // For deals table, filter by capital position through NSF fields
      whereConditions.push(`EXISTS (
        SELECT 1 FROM deal_nsf_fields dnf 
        WHERE dnf.deal_id = d.deal_id 
        AND dnf.capital_position = '${capitalPosition}'
      )`);
    } else {
      whereConditions.push(`capital_position = '${capitalPosition}'`);
    }
  }
  
  return whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
}

function generateSummaryMetrics(centralMetrics: any, debtMetrics: any, equityMetrics: any, nsfMetrics: any, positionMetrics: any, dealsMetrics: any) {
  const allMetrics = [
    { name: 'Central Investment Criteria', metrics: centralMetrics },
    { name: 'Debt Investment Criteria', metrics: debtMetrics },
    { name: 'Equity Investment Criteria', metrics: equityMetrics },
    { name: 'NSF Fields', metrics: nsfMetrics },
    { name: 'Capital Position Weights', metrics: positionMetrics },
    { name: 'DealsV2', metrics: dealsMetrics }
  ];

  const totalRecords = allMetrics.reduce((sum, metric) => sum + metric.metrics.total_records, 0);
  const overallQuality = allMetrics.reduce((sum, metric) => sum + metric.metrics.overall_quality, 0) / allMetrics.length;

  return {
    total_records: totalRecords,
    overall_quality: Math.round(overallQuality),
    table_breakdown: allMetrics.map(metric => ({
      table_name: metric.name,
      total_records: metric.metrics.total_records,
      overall_quality: metric.metrics.overall_quality
    }))
  };
}
