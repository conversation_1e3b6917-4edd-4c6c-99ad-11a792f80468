import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const entityType = searchParams.get('entityType') || 'all' // 'contact', 'company', or 'all'

    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'startDate and endDate are required' },
        { status: 400 }
      )
    }

    console.log('Fetching mapping validation metrics:', { startDate, endDate, entityType })

    const validationMetrics: any = {}

    if (entityType === 'contact' || entityType === 'all') {
      validationMetrics.contactEnrichment = await getContactEnrichmentValidationMetrics(startDate, endDate)
    }

    if (entityType === 'company' || entityType === 'all') {
      validationMetrics.companyOverview = await getCompanyOverviewValidationMetrics(startDate, endDate)
    }

    if (entityType === 'news' || entityType === 'all') {
      validationMetrics.newsEnrichment = await getNewsEnrichmentValidationMetrics(startDate, endDate)
    }

    const response = {
      success: true,
      data: {
        validationMetrics
      },
      timeRange: {
        startDate,
        endDate
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error in mapping validation API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function getContactEnrichmentValidationMetrics(startDate: string, endDate: string) {
  const client = await pool.connect()
  
  try {
    // Get company type validation metrics
    const companyTypeValidation = await client.query(`
      WITH valid_company_types AS (
        SELECT DISTINCT value_1 as company_type
        FROM central_mapping 
        WHERE type = 'Company Type' 
        AND is_active = true
      ),
      contact_company_types AS (
        SELECT 
          ce.company_type,
          COUNT(*) as total_count,
          COUNT(CASE WHEN vct.company_type IS NOT NULL THEN 1 END) as valid_count,
          COUNT(CASE WHEN vct.company_type IS NULL AND ce.company_type IS NOT NULL THEN 1 END) as invalid_count
        FROM contact_enrichment ce
        LEFT JOIN valid_company_types vct ON ce.company_type = vct.company_type
        WHERE ce.updated_at >= $1 AND ce.updated_at <= $2
        AND ce.company_type IS NOT NULL
        GROUP BY ce.company_type
      )
      SELECT 
        company_type,
        total_count,
        valid_count,
        invalid_count,
        CASE 
          WHEN total_count > 0 THEN ROUND((valid_count::decimal / total_count) * 100, 2)
          ELSE 0
        END as validation_percentage
      FROM contact_company_types
      ORDER BY total_count DESC
    `, [startDate, endDate])

    // Get capital position validation metrics
    const capitalPositionValidation = await client.query(`
      WITH valid_capital_positions AS (
        SELECT DISTINCT value_1 as capital_position
        FROM central_mapping 
        WHERE type = 'Capital Position' 
        AND is_active = true
      ),
      contact_capital_positions AS (
        SELECT 
          jsonb_array_elements_text(ce.capital_positions) as capital_position,
          COUNT(*) as total_count
        FROM contact_enrichment ce
        WHERE ce.updated_at >= $1 AND ce.updated_at <= $2
        AND ce.capital_positions IS NOT NULL
        AND jsonb_array_length(ce.capital_positions) > 0
        GROUP BY jsonb_array_elements_text(ce.capital_positions)
      )
      SELECT 
        ccp.capital_position,
        ccp.total_count,
        CASE 
          WHEN vcp.capital_position IS NOT NULL THEN ccp.total_count
          ELSE 0
        END as valid_count,
        CASE 
          WHEN vcp.capital_position IS NULL THEN ccp.total_count
          ELSE 0
        END as invalid_count,
        CASE 
          WHEN vcp.capital_position IS NOT NULL THEN 100.0
          ELSE 0.0
        END as validation_percentage
      FROM contact_capital_positions ccp
      LEFT JOIN valid_capital_positions vcp ON ccp.capital_position = vcp.capital_position
      ORDER BY ccp.total_count DESC
    `, [startDate, endDate])

    // Get overall validation summary
    const overallValidation = await client.query(`
      WITH valid_company_types AS (
        SELECT DISTINCT value_1 as company_type
        FROM central_mapping 
        WHERE type = 'Company Type' 
        AND is_active = true
      ),
      valid_capital_positions AS (
        SELECT DISTINCT value_1 as capital_position
        FROM central_mapping 
        WHERE type = 'Capital Position' 
        AND is_active = true
      ),
      validation_summary AS (
        SELECT 
          COUNT(*) as total_records,
          
          -- Company type validation
          COUNT(CASE WHEN ce.company_type IS NOT NULL THEN 1 END) as records_with_company_type,
          COUNT(CASE WHEN ce.company_type IS NOT NULL AND vct.company_type IS NOT NULL THEN 1 END) as valid_company_types,
          COUNT(CASE WHEN ce.company_type IS NOT NULL AND vct.company_type IS NULL THEN 1 END) as invalid_company_types,
          
          -- Capital position validation
          COUNT(CASE WHEN ce.capital_positions IS NOT NULL AND jsonb_array_length(ce.capital_positions) > 0 THEN 1 END) as records_with_capital_positions,
          
          -- Records with valid mapping data
          COUNT(CASE WHEN (ce.company_type IS NOT NULL AND vct.company_type IS NOT NULL) 
                      OR (ce.capital_positions IS NOT NULL AND jsonb_array_length(ce.capital_positions) > 0) 
                      THEN 1 END) as records_with_valid_mapping_data
          
        FROM contact_enrichment ce
        LEFT JOIN valid_company_types vct ON ce.company_type = vct.company_type
        WHERE ce.updated_at >= $1 AND ce.updated_at <= $2
      )
      SELECT 
        *,
        CASE 
          WHEN records_with_company_type > 0 THEN ROUND((valid_company_types::decimal / records_with_company_type) * 100, 2)
          ELSE 0
        END as company_type_validation_percentage,
        CASE 
          WHEN total_records > 0 THEN ROUND((records_with_valid_mapping_data::decimal / total_records) * 100, 2)
          ELSE 0
        END as overall_mapping_validation_percentage
      FROM validation_summary
    `, [startDate, endDate])

    return {
      companyTypeValidation: companyTypeValidation.rows,
      capitalPositionValidation: capitalPositionValidation.rows,
      overallValidation: overallValidation.rows[0] || {},
      validMappingCounts: {
        totalCompanyTypes: await getValidMappingCount('Company Type'),
        totalCapitalPositions: await getValidMappingCount('Capital Position')
      }
    }
  } finally {
    client.release()
  }
}

async function getCompanyOverviewValidationMetrics(startDate: string, endDate: string) {
  const client = await pool.connect()
  
  try {
    // Get property types validation metrics
    const propertyTypesValidation = await client.query(`
      WITH valid_property_types AS (
        SELECT DISTINCT value_1 as property_type
        FROM central_mapping 
        WHERE type = 'Property Type' 
        AND is_active = true
      ),
      investment_property_types AS (
        SELECT 
          unnest(ic.property_types) as property_type,
          COUNT(*) as total_count
        FROM investment_criteria ic
        WHERE ic.entity_type = 'Company Overview'
        AND ic.updated_at >= $1 AND ic.updated_at <= $2
        AND ic.property_types IS NOT NULL
        AND array_length(ic.property_types, 1) > 0
        GROUP BY unnest(ic.property_types)
      )
      SELECT 
        ipt.property_type,
        ipt.total_count,
        CASE 
          WHEN vpt.property_type IS NOT NULL THEN ipt.total_count
          ELSE 0
        END as valid_count,
        CASE 
          WHEN vpt.property_type IS NULL THEN ipt.total_count
          ELSE 0
        END as invalid_count,
        CASE 
          WHEN vpt.property_type IS NOT NULL THEN 100.0
          ELSE 0.0
        END as validation_percentage
      FROM investment_property_types ipt
      LEFT JOIN valid_property_types vpt ON ipt.property_type = vpt.property_type
      ORDER BY ipt.total_count DESC
    `, [startDate, endDate])

    // Get strategies validation metrics  
    const strategiesValidation = await client.query(`
      WITH valid_strategies AS (
        SELECT DISTINCT value_1 as strategy
        FROM central_mapping 
        WHERE type = 'Strategies' 
        AND is_active = true
      ),
      investment_strategies AS (
        SELECT 
          unnest(ic.strategies) as strategy,
          COUNT(*) as total_count
        FROM investment_criteria ic
        WHERE ic.entity_type = 'Company Overview'
        AND ic.updated_at >= $1 AND ic.updated_at <= $2
        AND ic.strategies IS NOT NULL
        AND array_length(ic.strategies, 1) > 0
        GROUP BY unnest(ic.strategies)
      )
      SELECT 
        ist.strategy,
        ist.total_count,
        CASE 
          WHEN vs.strategy IS NOT NULL THEN ist.total_count
          ELSE 0
        END as valid_count,
        CASE 
          WHEN vs.strategy IS NULL THEN ist.total_count
          ELSE 0
        END as invalid_count,
        CASE 
          WHEN vs.strategy IS NOT NULL THEN 100.0
          ELSE 0.0
        END as validation_percentage
      FROM investment_strategies ist
      LEFT JOIN valid_strategies vs ON ist.strategy = vs.strategy
      ORDER BY ist.total_count DESC
    `, [startDate, endDate])

    // Get capital position validation metrics
    const capitalPositionValidation = await client.query(`
      WITH valid_capital_positions AS (
        SELECT DISTINCT value_1 as capital_position
        FROM central_mapping 
        WHERE type = 'Capital Position' 
        AND is_active = true
      ),
      investment_capital_positions AS (
        SELECT 
          unnest(ic.capital_position) as capital_position,
          COUNT(*) as total_count
        FROM investment_criteria ic
        WHERE ic.entity_type = 'Company Overview'
        AND ic.updated_at >= $1 AND ic.updated_at <= $2
        AND ic.capital_position IS NOT NULL
        AND array_length(ic.capital_position, 1) > 0
        GROUP BY unnest(ic.capital_position)
      )
      SELECT 
        icp.capital_position,
        icp.total_count,
        CASE 
          WHEN vcp.capital_position IS NOT NULL THEN icp.total_count
          ELSE 0
        END as valid_count,
        CASE 
          WHEN vcp.capital_position IS NULL THEN icp.total_count
          ELSE 0
        END as invalid_count,
        CASE 
          WHEN vcp.capital_position IS NOT NULL THEN 100.0
          ELSE 0.0
        END as validation_percentage
      FROM investment_capital_positions icp
      LEFT JOIN valid_capital_positions vcp ON icp.capital_position = vcp.capital_position
      ORDER BY icp.total_count DESC
    `, [startDate, endDate])

    // Get loan program validation metrics
    const loanProgramValidation = await client.query(`
      WITH valid_loan_programs AS (
        SELECT DISTINCT value_1 as loan_program
        FROM central_mapping 
        WHERE type = 'Loan Program' 
        AND is_active = true
      ),
      investment_loan_programs AS (
        SELECT 
          unnest(ic.loan_program) as loan_program,
          COUNT(*) as total_count
        FROM investment_criteria ic
        WHERE ic.entity_type = 'Company Overview'
        AND ic.updated_at >= $1 AND ic.updated_at <= $2
        AND ic.loan_program IS NOT NULL
        AND array_length(ic.loan_program, 1) > 0
        GROUP BY unnest(ic.loan_program)
      )
      SELECT 
        ilp.loan_program,
        ilp.total_count,
        CASE 
          WHEN vlp.loan_program IS NOT NULL THEN ilp.total_count
          ELSE 0
        END as valid_count,
        CASE 
          WHEN vlp.loan_program IS NULL THEN ilp.total_count
          ELSE 0
        END as invalid_count,
        CASE 
          WHEN vlp.loan_program IS NOT NULL THEN 100.0
          ELSE 0.0
        END as validation_percentage
      FROM investment_loan_programs ilp
      LEFT JOIN valid_loan_programs vlp ON ilp.loan_program = vlp.loan_program
      ORDER BY ilp.total_count DESC
    `, [startDate, endDate])

    // Get US regions validation metrics
    const regionsValidation = await client.query(`
      WITH valid_regions AS (
        SELECT DISTINCT value_1 as region
        FROM central_mapping 
        WHERE type = 'U.S Regions' 
        AND is_active = true
      ),
      investment_regions AS (
        SELECT 
          unnest(ic.region) as region,
          COUNT(*) as total_count
        FROM investment_criteria ic
        WHERE ic.entity_type = 'Company Overview'
        AND ic.updated_at >= $1 AND ic.updated_at <= $2
        AND ic.region IS NOT NULL
        AND array_length(ic.region, 1) > 0
        GROUP BY unnest(ic.region)
      )
      SELECT 
        ir.region,
        ir.total_count,
        CASE 
          WHEN vr.region IS NOT NULL THEN ir.total_count
          ELSE 0
        END as valid_count,
        CASE 
          WHEN vr.region IS NULL THEN ir.total_count
          ELSE 0
        END as invalid_count,
        CASE 
          WHEN vr.region IS NOT NULL THEN 100.0
          ELSE 0.0
        END as validation_percentage
      FROM investment_regions ir
      LEFT JOIN valid_regions vr ON ir.region = vr.region
      ORDER BY ir.total_count DESC
    `, [startDate, endDate])

    // Get overall validation summary for company overview
    const overallValidation = await client.query(`
      WITH valid_property_types AS (
        SELECT DISTINCT value_1 as property_type FROM central_mapping WHERE type = 'Property Type' AND is_active = true
      ),
      valid_strategies AS (
        SELECT DISTINCT value_1 as strategy FROM central_mapping WHERE type = 'Strategies' AND is_active = true
      ),
      valid_capital_positions AS (
        SELECT DISTINCT value_1 as capital_position FROM central_mapping WHERE type = 'Capital Position' AND is_active = true
      ),
      valid_loan_programs AS (
        SELECT DISTINCT value_1 as loan_program FROM central_mapping WHERE type = 'Loan Program' AND is_active = true
      ),
      valid_regions AS (
        SELECT DISTINCT value_1 as region FROM central_mapping WHERE type = 'U.S Regions' AND is_active = true
      ),
      validation_summary AS (
        SELECT 
          COUNT(*) as total_records,
          
          -- Property types validation
          COUNT(CASE WHEN ic.property_types IS NOT NULL AND array_length(ic.property_types, 1) > 0 THEN 1 END) as records_with_property_types,
          
          -- Strategies validation
          COUNT(CASE WHEN ic.strategies IS NOT NULL AND array_length(ic.strategies, 1) > 0 THEN 1 END) as records_with_strategies,
          
          -- Capital position validation
          COUNT(CASE WHEN ic.capital_position IS NOT NULL AND array_length(ic.capital_position, 1) > 0 THEN 1 END) as records_with_capital_positions,
          
          -- Loan program validation
          COUNT(CASE WHEN ic.loan_program IS NOT NULL AND array_length(ic.loan_program, 1) > 0 THEN 1 END) as records_with_loan_programs,
          
          -- Regions validation
          COUNT(CASE WHEN ic.region IS NOT NULL AND array_length(ic.region, 1) > 0 THEN 1 END) as records_with_regions,
          
          -- Records with any valid mapping data
          COUNT(CASE WHEN (ic.property_types IS NOT NULL AND array_length(ic.property_types, 1) > 0) 
                      OR (ic.strategies IS NOT NULL AND array_length(ic.strategies, 1) > 0)
                      OR (ic.capital_position IS NOT NULL AND array_length(ic.capital_position, 1) > 0)
                      OR (ic.loan_program IS NOT NULL AND array_length(ic.loan_program, 1) > 0)
                      OR (ic.region IS NOT NULL AND array_length(ic.region, 1) > 0)
                      THEN 1 END) as records_with_mapping_data
          
        FROM investment_criteria ic
        WHERE ic.entity_type = 'Company Overview'
        AND ic.updated_at >= $1 AND ic.updated_at <= $2
      )
      SELECT 
        *,
        CASE 
          WHEN total_records > 0 THEN ROUND((records_with_mapping_data::decimal / total_records) * 100, 2)
          ELSE 0
        END as overall_mapping_validation_percentage
      FROM validation_summary
    `, [startDate, endDate])

    return {
      propertyTypesValidation: propertyTypesValidation.rows,
      strategiesValidation: strategiesValidation.rows,
      capitalPositionValidation: capitalPositionValidation.rows,
      loanProgramValidation: loanProgramValidation.rows,
      regionsValidation: regionsValidation.rows,
      overallValidation: overallValidation.rows[0] || {},
      validMappingCounts: {
        totalPropertyTypes: await getValidMappingCount('Property Type'),
        totalStrategies: await getValidMappingCount('Strategies'),
        totalCapitalPositions: await getValidMappingCount('Capital Position'),
        totalLoanPrograms: await getValidMappingCount('Loan Program'),
        totalRegions: await getValidMappingCount('U.S Regions')
      }
    }
  } finally {
    client.release()
  }
}

async function getNewsEnrichmentValidationMetrics(startDate: string, endDate: string) {
  const client = await pool.connect()
  
  try {
    // Get deal type validation metrics from news_enrichment
    const dealTypesValidation = await client.query(`
      WITH valid_deal_types AS (
        SELECT DISTINCT value_1 as deal_type
        FROM central_mapping 
        WHERE type = 'Deal Type' 
        AND is_active = true
      ),
      enrichment_deal_types AS (
        SELECT 
          jsonb_array_elements_text(ne.deal_type) as deal_type,
          COUNT(*) as total_count
        FROM news_enrichment ne
        WHERE ne.created_at >= $1 AND ne.created_at <= $2
        AND ne.is_deal_specific = true
        AND ne.deal_type IS NOT NULL
        AND ne.deal_type != '[]'
        GROUP BY jsonb_array_elements_text(ne.deal_type)
      )
      SELECT 
        ndt.deal_type,
        ndt.total_count,
        CASE WHEN vdt.deal_type IS NOT NULL THEN true ELSE false END as is_valid,
        CASE WHEN vdt.deal_type IS NOT NULL THEN 1 ELSE 0 END * ndt.total_count as valid_count
      FROM enrichment_deal_types ndt
      LEFT JOIN valid_deal_types vdt ON ndt.deal_type = vdt.deal_type
      ORDER BY ndt.total_count DESC
    `, [startDate, endDate])

    // Get capital position validation metrics from news_enrichment
    const capitalPositionsValidation = await client.query(`
      WITH valid_capital_positions AS (
        SELECT DISTINCT value_1 as capital_position
        FROM central_mapping 
        WHERE type = 'Capital Position' 
        AND is_active = true
      ),
      enrichment_capital_positions AS (
        SELECT 
          -- For now, we'll extract from the strategies field since capital_position isn't mapped yet
          jsonb_array_elements_text(ne.strategies) as capital_position,
          COUNT(*) as total_count
        FROM news_enrichment ne
        WHERE ne.created_at >= $1 AND ne.created_at <= $2
        AND ne.is_deal_specific = true
        AND ne.strategies IS NOT NULL
        AND ne.strategies != '[]'
        GROUP BY jsonb_array_elements_text(ne.strategies)
      )
      SELECT 
        ncp.capital_position,
        ncp.total_count,
        CASE WHEN vcp.capital_position IS NOT NULL THEN true ELSE false END as is_valid,
        CASE WHEN vcp.capital_position IS NOT NULL THEN 1 ELSE 0 END * ncp.total_count as valid_count
      FROM enrichment_capital_positions ncp
      LEFT JOIN valid_capital_positions vcp ON ncp.capital_position = vcp.capital_position
      ORDER BY ncp.total_count DESC
      LIMIT 20
    `, [startDate, endDate])

    // Get strategies validation metrics from news_enrichment
    const strategiesValidation = await client.query(`
      WITH valid_strategies AS (
        SELECT DISTINCT value_1 as strategy
        FROM central_mapping 
        WHERE type = 'Strategy' 
        AND is_active = true
      ),
      enrichment_strategies AS (
        SELECT 
          jsonb_array_elements_text(ne.strategies) as strategy,
          COUNT(*) as total_count
        FROM news_enrichment ne
        WHERE ne.created_at >= $1 AND ne.created_at <= $2
        AND ne.is_deal_specific = true
        AND ne.strategies IS NOT NULL
        AND ne.strategies != '[]'
        GROUP BY jsonb_array_elements_text(ne.strategies)
      )
      SELECT 
        ns.strategy,
        ns.total_count,
        CASE WHEN vs.strategy IS NOT NULL THEN true ELSE false END as is_valid,
        CASE WHEN vs.strategy IS NOT NULL THEN 1 ELSE 0 END * ns.total_count as valid_count
      FROM enrichment_strategies ns
      LEFT JOIN valid_strategies vs ON ns.strategy = vs.strategy
      ORDER BY ns.total_count DESC
      LIMIT 20
    `, [startDate, endDate])

    // Get property types validation metrics from news_enrichment
    const propertyTypesValidation = await client.query(`
      WITH valid_property_types AS (
        SELECT DISTINCT value_1 as property_type
        FROM central_mapping 
        WHERE type = 'Property Type' 
        AND is_active = true
      ),
      enrichment_property_types AS (
        SELECT 
          ne.property_type,
          COUNT(*) as total_count
        FROM news_enrichment ne
        WHERE ne.created_at >= $1 AND ne.created_at <= $2
        AND ne.is_deal_specific = true
        AND ne.property_type IS NOT NULL
        GROUP BY ne.property_type
      )
      SELECT 
        npt.property_type,
        npt.total_count,
        CASE WHEN vpt.property_type IS NOT NULL THEN true ELSE false END as is_valid,
        CASE WHEN vpt.property_type IS NOT NULL THEN 1 ELSE 0 END * npt.total_count as valid_count
      FROM enrichment_property_types npt
      LEFT JOIN valid_property_types vpt ON npt.property_type = vpt.property_type
      ORDER BY npt.total_count DESC
    `, [startDate, endDate])

    // Calculate overall validation percentages
    const overallValidation = await client.query(`
      WITH deal_type_stats AS (
        SELECT 
          COUNT(DISTINCT ne.id) as total_records,
          COUNT(DISTINCT CASE WHEN ne.deal_type IS NOT NULL AND ne.deal_type != '[]' THEN ne.id END) as records_with_deal_types
        FROM news_enrichment ne
        WHERE ne.created_at >= $1 AND ne.created_at <= $2
        AND ne.is_deal_specific = true
      ),
      property_type_stats AS (
        SELECT 
          COUNT(DISTINCT ne.id) as records_with_property_types
        FROM news_enrichment ne
        WHERE ne.created_at >= $1 AND ne.created_at <= $2
        AND ne.is_deal_specific = true
        AND ne.property_type IS NOT NULL
      ),
      strategy_stats AS (
        SELECT 
          COUNT(DISTINCT ne.id) as records_with_strategies
        FROM news_enrichment ne
        WHERE ne.created_at >= $1 AND ne.created_at <= $2
        AND ne.is_deal_specific = true
        AND ne.strategies IS NOT NULL
        AND ne.strategies != '[]'
      )
      SELECT 
        dt.total_records,
        dt.records_with_deal_types,
        pt.records_with_property_types,
        st.records_with_strategies,
        ROUND(100.0 * dt.records_with_deal_types / NULLIF(dt.total_records, 0), 2) as deal_types_percentage,
        ROUND(100.0 * pt.records_with_property_types / NULLIF(dt.total_records, 0), 2) as property_types_percentage,
        ROUND(100.0 * st.records_with_strategies / NULLIF(dt.total_records, 0), 2) as strategies_percentage
      FROM deal_type_stats dt
      CROSS JOIN property_type_stats pt
      CROSS JOIN strategy_stats st
    `, [startDate, endDate])

    return {
      dealTypesValidation: dealTypesValidation.rows,
      capitalPositionValidation: capitalPositionsValidation.rows,
      strategiesValidation: strategiesValidation.rows,
      propertyTypesValidation: propertyTypesValidation.rows,
      overallValidation: overallValidation.rows[0] || {},
      validMappingCounts: {
        totalDealTypes: await getValidMappingCount('Deal Type'),
        totalCapitalPositions: await getValidMappingCount('Capital Position'),
        totalStrategies: await getValidMappingCount('Strategy'),
        totalPropertyTypes: await getValidMappingCount('Property Type')
      }
    }
  } finally {
    client.release()
  }
}

async function getValidMappingCount(mappingType: string) {
  const client = await pool.connect()
  try {
    const result = await client.query(`
      SELECT COUNT(DISTINCT value_1) as count
      FROM central_mapping 
      WHERE type = $1 AND is_active = true
    `, [mappingType])
    return result.rows[0]?.count || 0
  } finally {
    client.release()
  }
} 