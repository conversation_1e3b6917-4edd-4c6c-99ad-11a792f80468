import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

interface ColumnGroup {
  name: string
  description: string
  table: 'central' | 'debt' | 'equity'
  columns: string[]
}

interface SimpleICMetrics {
  totalRows: number
  completedICRows: number
  pendingICRows: number
  failedICRows: number
  overallPercentage: number
  tableTotals: {
    central: number
    debt: number
    equity: number
  }
  // Add total debt and equity counts
  totalDebtCount: number
  totalEquityCount: number
  recordsWithBothDebtAndEquity: number
  columnGroups: Array<{
    name: string
    description: string
    table: 'central' | 'debt' | 'equity'
    totalRecords: number
    columns: Array<{
      column_name: string
      populated_count: number
      populated_percentage: number
    }>
  }>
  hourlyProcessingStats: Array<{
    hour: string
    processed_count: number
    completed_count: number
    failed_count: number
  }>
  mappingValidation: {
    total_records: number
    property_types_validation: {
      valid_count: number
      invalid_count: number
      invalid_values: string[]
    }
    strategies_validation: {
      valid_count: number
      invalid_count: number
      invalid_values: string[]
    }
    capital_position_validation: {
      valid_count: number
      invalid_count: number
      invalid_values: string[]
    }
    loan_programs_validation: {
      valid_count: number
      invalid_count: number
      invalid_values: string[]
    }
    overall_mapping_validation_percentage: number
  }
}

// Helper function to get column groups data for company IC (central, debt, equity tables)
async function getColumnGroupsData(client: any, completedICRows: number) {
  if (completedICRows === 0) return []

  try {
    const columnGroupsData: Array<{
      name: string
      description: string
      table: 'central' | 'debt' | 'equity'
      totalRecords: number
      columns: Array<{
        column_name: string
        populated_count: number
        populated_percentage: number
      }>
    }> = []

    // Central table fields
    const centralColumns = [
      'capital_position',
      'minimum_deal_size', 
      'maximum_deal_size',
      'country',
      'region',
      'state',
      'city',
      'property_types',
      'property_subcategories',
      'strategies',
      'decision_making_process'
    ]

    const centralColumnsData: Array<{
      column_name: string
      populated_count: number
      populated_percentage: number
    }> = []

    // Get actual populated counts and calculate real percentages
    for (const columnName of centralColumns) {
      try {
        const columnResult = await client.query(`
          SELECT 
            COUNT(CASE WHEN ${columnName} IS NOT NULL AND ${columnName} != '' AND ${columnName} != '{}' THEN 1 END) as populated_count
          FROM investment_criteria_central icc
          JOIN companies c ON c.company_id = icc.entity_id
          WHERE c.investment_criteria_status = 'completed'
            AND icc.entity_type = 'company'
        `)
        
        const populatedCount = parseInt(columnResult.rows[0].populated_count || 0)
        const populatedPercentage = completedICRows > 0 ? Math.round((populatedCount / completedICRows) * 100) : 0
        
        centralColumnsData.push({
          column_name: columnName,
          populated_count: populatedCount,
          populated_percentage: populatedPercentage
        })
      } catch (error) {
        console.error(`Error getting data for column ${columnName}:`, error)
        // Fallback to 0 if there's an error
        centralColumnsData.push({
          column_name: columnName,
          populated_count: 0,
          populated_percentage: 0
        })
      }
    }

    columnGroupsData.push({
      name: 'Central Investment Criteria',
      description: 'Core investment criteria fields from the central table',
      table: 'central',
      totalRecords: completedICRows,
      columns: centralColumnsData
    })

    // Debt table fields - Use central table as main table and join with debt table
    const debtColumns = [
      'closing_time',
      'future_facilities',
      'eligible_borrower',
      'occupancy_requirements',
      'lien_position',
      'min_loan_dscr',
      'max_loan_dscr',
      'recourse_loan',
      'loan_min_debt_yield',
      'prepayment',
      'yield_maintenance',
      'application_deposit',
      'good_faith_deposit',
      'loan_origination_max_fee',
      'loan_origination_min_fee',
      'loan_exit_min_fee',
      'loan_exit_max_fee',
      'loan_interest_rate',
      'loan_interest_rate_based_off_sofr',
      'loan_interest_rate_based_off_wsj',
      'loan_interest_rate_based_off_prime',
      'loan_interest_rate_based_off_3yt',
      'loan_interest_rate_based_off_5yt',
      'loan_interest_rate_based_off_10yt',
      'loan_interest_rate_based_off_30yt',
      'rate_lock',
      'rate_type',
      'loan_to_value_max',
      'loan_to_value_min',
      'loan_to_cost_min',
      'loan_to_cost_max',
      'debt_program_overview',
      'loan_type',
      'loan_type_normalized',
      'structured_loan_tranche',
      'loan_program',
      'min_loan_term',
      'max_loan_term',
      'amortization'
    ]

    const debtColumnsData: Array<{
      column_name: string
      populated_count: number
      populated_percentage: number
    }> = []

    // Get debt table total records by joining central table with debt table
    const debtTotalResult = await client.query(`
      SELECT COUNT(d.investment_criteria_debt_id) as total_debt_records
      FROM investment_criteria_central icc
      JOIN companies c ON c.company_id = icc.entity_id
      JOIN investment_criteria_debt d ON icc.investment_criteria_id = d.investment_criteria_id
      WHERE c.investment_criteria_status = 'completed'
        AND icc.entity_type = 'company'
    `)
    const debtTotalRecords = parseInt(debtTotalResult.rows[0].total_debt_records || 0)
    
    // Get actual populated counts for debt columns
    for (const columnName of debtColumns) {
      try {
        const columnResult = await client.query(`
          SELECT 
            COUNT(CASE WHEN d.${columnName} IS NOT NULL AND d.${columnName} != '' THEN 1 END) as populated_count
          FROM investment_criteria_central icc
          JOIN companies c ON c.company_id = icc.entity_id
          JOIN investment_criteria_debt d ON icc.investment_criteria_id = d.investment_criteria_id
          WHERE c.investment_criteria_status = 'completed'
            AND icc.entity_type = 'company'
        `)
        
        const populatedCount = parseInt(columnResult.rows[0].populated_count || 0)
        const populatedPercentage = debtTotalRecords > 0 ? Math.round((populatedCount / debtTotalRecords) * 100) : 0
        
        debtColumnsData.push({
          column_name: columnName,
          populated_count: populatedCount,
          populated_percentage: populatedPercentage
        })
      } catch (error) {
        console.error(`Error getting debt data for column ${columnName}:`, error)
        debtColumnsData.push({
          column_name: columnName,
          populated_count: 0,
          populated_percentage: 0
        })
      }
    }

    columnGroupsData.push({
      name: 'Debt Investment Criteria',
      description: 'Debt-specific investment criteria fields',
      table: 'debt',
      totalRecords: debtTotalRecords,
      columns: debtColumnsData
    })

    // Equity table fields - Use central table as main table and join with equity table
    const equityColumns = [
      'target_return',
      'minimum_internal_rate_of_return',
      'minimum_yield_on_cost',
      'minimum_equity_multiple',
      'target_cash_on_cash_min',
      'min_hold_period_years',
      'max_hold_period_years',
      'ownership_requirement',
      'attachment_point',
      'max_leverage_tolerance',
      'typical_closing_timeline_days',
      'proof_of_funds_requirement',
      'equity_program_overview',
      'occupancy_requirements'
    ]

    const equityColumnsData: Array<{
      column_name: string
      populated_count: number
      populated_percentage: number
    }> = []

    // Get equity table total records by joining central table with equity table
    const equityTotalResult = await client.query(`
      SELECT COUNT(e.investment_criteria_equity_id) as total_equity_records
      FROM investment_criteria_central icc
      JOIN companies c ON c.company_id = icc.entity_id
      JOIN investment_criteria_equity e ON icc.investment_criteria_id = e.investment_criteria_id
      WHERE c.investment_criteria_status = 'completed'
        AND icc.entity_type = 'company'
    `)
    const equityTotalRecords = parseInt(equityTotalResult.rows[0].total_equity_records || 0)
    
    // Get actual populated counts for equity columns
    for (const columnName of equityColumns) {
      try {
        const columnResult = await client.query(`
          SELECT 
            COUNT(CASE WHEN e.${columnName} IS NOT NULL AND e.${columnName} != '' THEN 1 END) as populated_count
          FROM investment_criteria_central icc
          JOIN companies c ON c.company_id = icc.entity_id
          JOIN investment_criteria_equity e ON icc.investment_criteria_id = e.investment_criteria_id
          WHERE c.investment_criteria_status = 'completed'
            AND icc.entity_type = 'company'
        `)
        
        const populatedCount = parseInt(columnResult.rows[0].populated_count || 0)
        const populatedPercentage = equityTotalRecords > 0 ? Math.round((populatedCount / equityTotalRecords) * 100) : 0
        
        equityColumnsData.push({
          column_name: columnName,
          populated_count: populatedCount,
          populated_percentage: populatedPercentage
        })
      } catch (error) {
        console.error(`Error getting equity data for column ${columnName}:`, error)
        equityColumnsData.push({
          column_name: columnName,
          populated_count: 0,
          populated_percentage: 0
        })
      }
    }

    columnGroupsData.push({
      name: 'Equity Investment Criteria',
      description: 'Equity-specific investment criteria fields',
      table: 'equity',
      totalRecords: equityTotalRecords,
      columns: equityColumnsData
    })

    return columnGroupsData
  } catch (error) {
    console.error('Error getting column groups data:', error)
    return []
  }
}

// Define column groups based on the investment criteria schema
const COLUMN_GROUPS: ColumnGroup[] = [
  {
    name: 'Deal Scope',
    description: 'Deal size and capital position information',
    table: 'central',
    columns: ['capital_position', 'minimum_deal_size', 'maximum_deal_size']
  },
  {
    name: 'Geography',
    description: 'Geographic investment focus and preferences',
    table: 'central',
    columns: ['country', 'region', 'state', 'city']
  },
  {
    name: 'Asset Strategy',
    description: 'Property types, strategies, and investment approach',
    table: 'central',
    columns: ['property_types', 'property_subcategories', 'strategies', 'decision_making_process']
  },
  {
    name: 'Debt Terms - Interest & Pricing',
    description: 'Interest rates, pricing, and rate structures for debt products',
    table: 'debt',
    columns: [
      'loan_interest_rate', 'loan_interest_rate_based_off_sofr', 'loan_interest_rate_based_off_wsj',
      'loan_interest_rate_based_off_prime', 'loan_interest_rate_based_off_3yt', 'loan_interest_rate_based_off_5yt',
      'loan_interest_rate_based_off_10yt', 'loan_interest_rate_based_off_30yt', 'rate_lock', 'rate_type'
    ]
  },
  {
    name: 'Debt Terms - Loan Sizing',
    description: 'Loan-to-value, loan-to-cost ratios and sizing parameters',
    table: 'debt',
    columns: ['loan_to_value_max', 'loan_to_value_min', 'loan_to_cost_min', 'loan_to_cost_max']
  },
  {
    name: 'Debt Terms - Fees & Costs',
    description: 'Origination fees, exit fees, and associated costs',
    table: 'debt',
    columns: [
      'loan_origination_max_fee', 'loan_origination_min_fee', 'loan_exit_min_fee', 'loan_exit_max_fee',
      'application_deposit', 'good_faith_deposit'
    ]
  },
  {
    name: 'Debt Terms - Covenants & Structure',
    description: 'Loan covenants, DSCR requirements, and structural terms',
    table: 'debt',
    columns: [
      'lien_position', 'min_loan_dscr', 'max_loan_dscr', 'recourse_loan', 'loan_min_debt_yield',
      'prepayment', 'yield_maintenance'
    ]
  },
  {
    name: 'Debt Terms - Program & Duration',
    description: 'Loan programs, types, and term structures',
    table: 'debt',
    columns: [
      'debt_program_overview', 'loan_type', 'loan_type_normalized', 'structured_loan_tranche',
      'loan_program', 'min_loan_term', 'max_loan_term', 'amortization'
    ]
  },
  {
    name: 'Equity Terms - Returns',
    description: 'Target returns, IRR, and equity multiple requirements',
    table: 'equity',
    columns: [
      'target_return', 'minimum_internal_rate_of_return', 'minimum_yield_on_cost',
      'minimum_equity_multiple', 'target_cash_on_cash_min'
    ]
  },
  {
    name: 'Equity Terms - Hold Period & Control',
    description: 'Investment timeline, ownership, and control requirements',
    table: 'equity',
    columns: [
      'min_hold_period_years', 'max_hold_period_years', 'ownership_requirement',
      'attachment_point', 'max_leverage_tolerance'
    ]
  },
  {
    name: 'Equity Terms - Process & Requirements',
    description: 'Closing timelines, due diligence, and process requirements',
    table: 'equity',
    columns: [
      'typical_closing_timeline_days', 'proof_of_funds_requirement', 'equity_program_overview'
    ]
  },
  {
    name: 'General Requirements (Debt)',
    description: 'Debt-specific borrower requirements and terms',
    table: 'debt',
    columns: ['closing_time', 'future_facilities', 'eligible_borrower']
  }
]

// Define data types for proper SQL handling
const ARRAY_COLUMNS = [
  'capital_position', 'country', 'region', 'state', 'city', 'property_types', 'property_subcategories', 'strategies'
]

const NUMERIC_COLUMNS = [
  'minimum_deal_size', 'maximum_deal_size', 'closing_time', 'min_loan_dscr', 'max_loan_dscr',
  'application_deposit', 'good_faith_deposit', 'loan_origination_max_fee', 'loan_origination_min_fee',
  'loan_exit_min_fee', 'loan_exit_max_fee', 'loan_interest_rate', 'loan_interest_rate_based_off_sofr',
  'loan_interest_rate_based_off_wsj', 'loan_interest_rate_based_off_prime', 'loan_interest_rate_based_off_3yt',
  'loan_interest_rate_based_off_5yt', 'loan_interest_rate_based_off_10yt', 'loan_interest_rate_based_off_30yt',
  'loan_to_value_max', 'loan_to_value_min', 'loan_to_cost_min', 'loan_to_cost_max',
  'min_loan_term', 'max_loan_term', 'target_return', 'minimum_internal_rate_of_return',
  'minimum_yield_on_cost', 'minimum_equity_multiple', 'target_cash_on_cash_min',
  'attachment_point', 'max_leverage_tolerance'
]

const INTEGER_COLUMNS = [
  'min_hold_period_years', 'max_hold_period_years', 'typical_closing_timeline_days'
]

const BOOLEAN_COLUMNS = [
  'proof_of_funds_requirement'
]

// Define which table each column belongs to
const getColumnTable = (columnName: string): 'central' | 'debt' | 'equity' => {
  // Central table columns (based on actual schema)
  const centralColumns = [
    'capital_position', 'minimum_deal_size', 'maximum_deal_size',
    'country', 'region', 'state', 'city',
    'property_types', 'property_subcategories', 'strategies', 'decision_making_process'
  ]
  
  // Debt table columns (based on actual schema)
  const debtColumns = [
    'closing_time', 'future_facilities', 'eligible_borrower',
    'lien_position', 'min_loan_dscr', 'max_loan_dscr', 'recourse_loan', 'loan_min_debt_yield',
    'prepayment', 'yield_maintenance', 'application_deposit', 'good_faith_deposit',
    'loan_origination_max_fee', 'loan_origination_min_fee', 'loan_exit_min_fee', 'loan_exit_max_fee',
    'loan_interest_rate', 'loan_interest_rate_based_off_sofr', 'loan_interest_rate_based_off_wsj',
    'loan_interest_rate_based_off_prime', 'loan_interest_rate_based_off_3yt', 'loan_interest_rate_based_off_5yt',
    'loan_interest_rate_based_off_10yt', 'loan_interest_rate_based_off_30yt', 'rate_lock', 'rate_type',
    'loan_to_value_max', 'loan_to_value_min', 'loan_to_cost_min', 'loan_to_cost_max',
    'debt_program_overview', 'loan_type', 'loan_type_normalized', 'structured_loan_tranche',
    'loan_program', 'min_loan_term', 'max_loan_term', 'amortization'
  ]
  
  // Equity table columns (based on actual schema)
  const equityColumns = [
    'target_return', 'minimum_internal_rate_of_return', 'minimum_yield_on_cost',
    'minimum_equity_multiple', 'target_cash_on_cash_min',
    'min_hold_period_years', 'max_hold_period_years', 'ownership_requirement',
    'attachment_point', 'max_leverage_tolerance',
    'typical_closing_timeline_days', 'proof_of_funds_requirement', 'equity_program_overview'
  ]
  
  if (centralColumns.includes(columnName)) return 'central'
  if (debtColumns.includes(columnName)) return 'debt'
  if (equityColumns.includes(columnName)) return 'equity'
  
  // Default to central if not found
  return 'central'
}

async function getSimpleICMetrics(): Promise<SimpleICMetrics> {
  const client = await pool.connect()
  try {
    console.log('Fetching simple company investment criteria metrics')
    
    // Get overall status counts
    const statusQuery = `
      SELECT 
        COUNT(*) as total_rows,
        COUNT(CASE WHEN COALESCE(investment_criteria_status, 'pending') = 'completed' THEN 1 END) as completed_ic_rows,
        COUNT(CASE WHEN COALESCE(investment_criteria_status, 'pending') = 'pending' THEN 1 END) as pending_ic_rows,
        COUNT(CASE WHEN COALESCE(investment_criteria_status, 'pending') = 'failed' THEN 1 END) as failed_ic_rows
      FROM companies
      WHERE company_overview_status = 'completed'
    `
    
    const statusResult = await client.query(statusQuery)
    const statusData = statusResult.rows[0]
    
    const totalRows = parseInt(statusData.total_rows) || 0
    const completedICRows = parseInt(statusData.completed_ic_rows) || 0
    const pendingICRows = parseInt(statusData.pending_ic_rows) || 0
    const failedICRows = parseInt(statusData.failed_ic_rows) || 0

    // Get counts for each table type using central table as main table
    const tableCountsQuery = `
      SELECT 
        COUNT(icc.investment_criteria_id) as central_count,
        COUNT(icd.investment_criteria_debt_id) as debt_count,
        COUNT(ice.investment_criteria_equity_id) as equity_count
      FROM companies c
      LEFT JOIN investment_criteria_central icc ON c.company_id = icc.entity_id AND icc.entity_type = 'company'
      LEFT JOIN investment_criteria_debt icd ON icc.investment_criteria_id = icd.investment_criteria_id
      LEFT JOIN investment_criteria_equity ice ON icc.investment_criteria_id = ice.investment_criteria_id
      WHERE c.investment_criteria_status = 'completed'
    `
    
    const tableCountsResult = await client.query(tableCountsQuery)
    const tableCounts = tableCountsResult.rows[0]
    const centralCount = parseInt(tableCounts.central_count) || 0
    const debtCount = parseInt(tableCounts.debt_count) || 0
    const equityCount = parseInt(tableCounts.equity_count) || 0

    // Get total debt and equity counts for completed company IC records
    const totalCountsQuery = `
      SELECT 
        COUNT(d.investment_criteria_debt_id) as total_debt_count,
        COUNT(e.investment_criteria_equity_id) as total_equity_count,
        COUNT(CASE WHEN d.investment_criteria_debt_id IS NOT NULL AND e.investment_criteria_equity_id IS NOT NULL THEN 1 END) as records_with_both
      FROM investment_criteria_central icc
      JOIN companies c ON c.company_id = icc.entity_id
      LEFT JOIN investment_criteria_debt d ON icc.investment_criteria_id = d.investment_criteria_id
      LEFT JOIN investment_criteria_equity e ON icc.investment_criteria_id = e.investment_criteria_id
      WHERE c.investment_criteria_status = 'completed'
        AND icc.entity_type = 'company'
    `
    
    const totalCountsResult = await client.query(totalCountsQuery)
    const totalCounts = totalCountsResult.rows[0]
    const totalDebtCount = parseInt(totalCounts.total_debt_count) || 0
    const totalEquityCount = parseInt(totalCounts.total_equity_count) || 0
    const recordsWithBothDebtAndEquity = parseInt(totalCounts.records_with_both) || 0

    // Process each column group separately by table
    const columnGroups: Array<{
      name: string
      description: string
      table: 'central' | 'debt' | 'equity'
      totalRecords: number
      columns: Array<{
        column_name: string
        populated_count: number
        populated_percentage: number
      }>
    }> = []
    
    for (const group of COLUMN_GROUPS) {
      const tableName = group.table === 'central' ? 'icc' : group.table === 'debt' ? 'icd' : 'ice'
      const totalCount = group.table === 'central' ? centralCount : group.table === 'debt' ? debtCount : equityCount
      
      // Build query for this specific table and columns
      const caseStatements = group.columns.map(col => {
        let condition = ''
        if (ARRAY_COLUMNS.includes(col)) {
          condition = `${tableName}.${col} IS NOT NULL AND ${tableName}.${col}::text != ''`
        } else if (NUMERIC_COLUMNS.includes(col) || INTEGER_COLUMNS.includes(col)) {
          condition = `${tableName}.${col} IS NOT NULL`
        } else if (BOOLEAN_COLUMNS.includes(col)) {
          condition = `${tableName}.${col} IS NOT NULL`
        } else {
          condition = `${tableName}.${col} IS NOT NULL AND TRIM(${tableName}.${col}) != ''`
        }
        
        return `COUNT(CASE WHEN ${condition} THEN 1 END) as ${col}_count`
      }).join(',\n        ')

      const query = `
        SELECT 
          ${caseStatements}
        FROM companies c
        LEFT JOIN investment_criteria_central icc ON c.company_id = icc.entity_id AND icc.entity_type = 'company'
        LEFT JOIN investment_criteria_debt icd ON icc.investment_criteria_id = icd.investment_criteria_id
        LEFT JOIN investment_criteria_equity ice ON icc.investment_criteria_id = ice.investment_criteria_id
        WHERE c.investment_criteria_status = 'completed'
      `

      const result = await client.query(query)
      const row = result.rows[0]

      const columns = group.columns.map(col => {
        const count = parseInt(row[`${col}_count`] || '0')
        return {
          column_name: col,
          populated_count: count,
          populated_percentage: totalCount > 0 ? Math.round((count / totalCount) * 100) : 0
        }
      })

      columnGroups.push({
        name: group.name,
        description: group.description,
        table: group.table,
        totalRecords: totalCount,
        columns
      })
    }

    // Calculate overall percentage based on all columns across all groups
    const allColumnData = columnGroups.flatMap(group => group.columns)
    const totalFields = allColumnData.length
    const totalPopulatedFields = allColumnData.reduce((sum, col) => sum + col.populated_percentage, 0)
    const overallPercentage = totalFields > 0 ? Math.round(totalPopulatedFields / totalFields) : 0

    // Add hourly processing stats and mapping validation
    const [hourlyResult, mappingResult] = await Promise.all([
      // Hourly processing stats (last 24 hours)
      client.query(`
        SELECT 
          DATE_TRUNC('hour', investment_criteria_date) as hour,
          COUNT(*) as processed_count,
          COUNT(CASE WHEN investment_criteria_status = 'completed' THEN 1 END) as completed_count,
          COUNT(CASE WHEN investment_criteria_status = 'failed' THEN 1 END) as failed_count
        FROM companies 
        WHERE investment_criteria_date >= NOW() - INTERVAL '24 hours'
          AND investment_criteria_date IS NOT NULL
        GROUP BY DATE_TRUNC('hour', investment_criteria_date)
        ORDER BY hour DESC
        LIMIT 24
      `),
      
      // Mapping validation query
      client.query(`
        WITH completed_ic AS (
          SELECT ic.*
          FROM investment_criteria_central ic
          JOIN companies c ON ic.entity_id = c.company_id AND ic.entity_type = 'company'
          WHERE COALESCE(c.investment_criteria_status, 'pending') = 'completed'
        )
        SELECT 
          COUNT(*) as total_records,
          
          -- Property Types Validation (handling array fields)
          COUNT(CASE WHEN property_types IS NOT NULL 
                     AND array_length(property_types, 1) > 0
                     AND (SELECT bool_and(EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Property Type' AND (value_1 = unnest_val OR value_2 = unnest_val) AND is_active = true))
                          FROM unnest(property_types) as unnest_val) 
                THEN 1 END) as property_types_valid,
          COUNT(CASE WHEN property_types IS NOT NULL 
                     AND array_length(property_types, 1) > 0
                     AND NOT (SELECT bool_and(EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Property Type' AND (value_1 = unnest_val OR value_2 = unnest_val) AND is_active = true))
                              FROM unnest(property_types) as unnest_val) 
                THEN 1 END) as property_types_invalid,
          
          -- Strategies Validation (handling array fields)
          COUNT(CASE WHEN strategies IS NOT NULL 
                     AND array_length(strategies, 1) > 0
                     AND (SELECT bool_and(EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Strategies' AND (value_1 = unnest_val OR value_2 = unnest_val) AND is_active = true))
                          FROM unnest(strategies) as unnest_val) 
                THEN 1 END) as strategies_valid,
          COUNT(CASE WHEN strategies IS NOT NULL 
                     AND array_length(strategies, 1) > 0
                     AND NOT (SELECT bool_and(EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Strategies' AND (value_1 = unnest_val OR value_2 = unnest_val) AND is_active = true))
                              FROM unnest(strategies) as unnest_val) 
                THEN 1 END) as strategies_invalid,
          
          -- Capital Position Validation
          COUNT(CASE WHEN capital_position IS NOT NULL 
                     AND EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Capital Position' AND (value_1 = capital_position OR value_2 = capital_position) AND is_active = true) 
                THEN 1 END) as capital_position_valid,
          COUNT(CASE WHEN capital_position IS NOT NULL 
                     AND NOT EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Capital Position' AND (value_1 = capital_position OR value_2 = capital_position) AND is_active = true) 
                THEN 1 END) as capital_position_invalid
                              
        FROM completed_ic
      `)
    ])

    // Process hourly stats
    const hourlyProcessingStats = hourlyResult.rows.map(row => ({
      hour: row.hour,
      processed_count: parseInt(row.processed_count || '0'),
      completed_count: parseInt(row.completed_count || '0'),
      failed_count: parseInt(row.failed_count || '0')
    }))

    // Process mapping validation
    const mappingRow = mappingResult.rows[0] || {}
    const mappingValidation = {
      total_records: parseInt(mappingRow.total_records || '0'),
      property_types_validation: {
        valid_count: parseInt(mappingRow.property_types_valid || '0'),
        invalid_count: parseInt(mappingRow.property_types_invalid || '0'),
        invalid_values: []
      },
      strategies_validation: {
        valid_count: parseInt(mappingRow.strategies_valid || '0'),
        invalid_count: parseInt(mappingRow.strategies_invalid || '0'),
        invalid_values: []
      },
      capital_position_validation: {
        valid_count: parseInt(mappingRow.capital_position_valid || '0'),
        invalid_count: parseInt(mappingRow.capital_position_invalid || '0'),
        invalid_values: []
      },
      loan_programs_validation: {
        valid_count: 0, // Would need additional query for loan programs
        invalid_count: 0,
        invalid_values: []
      },
      overall_mapping_validation_percentage: 0
    }

    // Calculate overall mapping validation percentage
    const totalValidations = mappingValidation.property_types_validation.valid_count + 
                           mappingValidation.strategies_validation.valid_count + 
                           mappingValidation.capital_position_validation.valid_count
    const totalInvalidations = mappingValidation.property_types_validation.invalid_count + 
                             mappingValidation.strategies_validation.invalid_count + 
                             mappingValidation.capital_position_validation.invalid_count
    const totalMappingChecks = totalValidations + totalInvalidations
    mappingValidation.overall_mapping_validation_percentage = totalMappingChecks > 0 
      ? Math.round((totalValidations / totalMappingChecks) * 100) 
      : 0

    return {
      totalRows,
      completedICRows,
      pendingICRows,
      failedICRows,
      overallPercentage,
      tableTotals: {
        central: centralCount,
        debt: debtCount,
        equity: equityCount
      },
      totalDebtCount,
      totalEquityCount,
      recordsWithBothDebtAndEquity,
      columnGroups,
      hourlyProcessingStats,
      mappingValidation
    }
  } finally {
    client.release()
  }
}

export async function GET(request: NextRequest) {
  try {
    const metrics = await getSimpleICMetrics()
    return NextResponse.json({
      success: true,
      data: metrics
    })
  } catch (error) {
    console.error('Error fetching IC metrics:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch IC metrics',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
