import { NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET() {
  try {
    const client = await pool.connect();

    try {
      // Get intuitive pipeline health overview
      const pipelineHealth = await getPipelineHealthOverview(client);

      // Get stage-wise quality metrics
      const stageQuality = await getStageQualityMetrics(client);

      // Get data completeness scores
      const completenessScores = await getDataCompletenessScores(client);

      // Get actionable insights
      const insights = await getActionableInsights(client);

      // Get quality trends
      const trends = await getQualityTrends(client);

      return NextResponse.json({
        success: true,
        data: {
          pipeline_health: pipelineHealth,
          stage_quality: stageQuality,
          completeness_scores: completenessScores,
          insights,
          trends
        },
        metadata: {
          generated_at: new Date().toISOString(),
          data_freshness: 'real-time',
          business_context: 'Lead pipeline quality and performance analysis'
        }
      });
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Data quality API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function getPipelineHealthOverview(client: any) {
  const healthQuery = `
    WITH pipeline_metrics AS (
      SELECT
        COUNT(*) as total_leads,
        COUNT(CASE WHEN email_verification_status = 'completed' THEN 1 END) as email_validated,
        COUNT(CASE WHEN osint_status = 'completed' THEN 1 END) as research_completed,
        COUNT(CASE WHEN classification_status = 'completed' THEN 1 END) as leads_scored,
        COUNT(CASE WHEN email_generation_status = 'completed' THEN 1 END) as content_ready,
        COUNT(CASE WHEN email_sending_status = 'completed' THEN 1 END) as outreach_sent,
        COUNT(CASE WHEN processing_error_count > 0 THEN 1 END) as leads_with_errors,
        COUNT(CASE WHEN created_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as new_leads_24h,
        COUNT(CASE WHEN email_sending_date >= NOW() - INTERVAL '24 hours' THEN 1 END) as outreach_sent_24h
      FROM contacts
      WHERE created_at >= NOW() - INTERVAL '30 days'
    )
    SELECT
      *,
      ROUND(email_validated * 100.0 / NULLIF(total_leads, 0), 2) as email_validation_rate,
      ROUND(outreach_sent * 100.0 / NULLIF(total_leads, 0), 2) as overall_conversion_rate,
      ROUND(leads_with_errors * 100.0 / NULLIF(total_leads, 0), 2) as error_rate,
      CASE
        WHEN outreach_sent * 100.0 / NULLIF(total_leads, 0) >= 80 THEN 'excellent'
        WHEN outreach_sent * 100.0 / NULLIF(total_leads, 0) >= 60 THEN 'good'
        WHEN outreach_sent * 100.0 / NULLIF(total_leads, 0) >= 30 THEN 'needs_improvement'
        ELSE 'critical'
      END as health_status
    FROM pipeline_metrics
  `;

  const result = await client.query(healthQuery);
  const metrics = result.rows[0];

  return {
    total_leads: parseInt(metrics.total_leads || 0),
    pipeline_stages: {
      email_validated: {
        count: parseInt(metrics.email_validated || 0),
        rate: parseFloat(metrics.email_validation_rate || 0),
        status: getStageStatus(metrics.email_validation_rate)
      },
      research_completed: {
        count: parseInt(metrics.research_completed || 0),
        rate: parseFloat((metrics.research_completed / metrics.email_validated * 100).toFixed(2)),
        status: getStageStatus((metrics.research_completed / metrics.email_validated * 100))
      },
      leads_scored: {
        count: parseInt(metrics.leads_scored || 0),
        rate: parseFloat((metrics.leads_scored / metrics.research_completed * 100).toFixed(2)),
        status: getStageStatus((metrics.leads_scored / metrics.research_completed * 100))
      },
      content_ready: {
        count: parseInt(metrics.content_ready || 0),
        rate: parseFloat((metrics.content_ready / metrics.leads_scored * 100).toFixed(2)),
        status: getStageStatus((metrics.content_ready / metrics.leads_scored * 100))
      },
      outreach_sent: {
        count: parseInt(metrics.outreach_sent || 0),
        rate: parseFloat(metrics.overall_conversion_rate || 0),
        status: getStageStatus(metrics.overall_conversion_rate)
      }
    },
    quality_indicators: {
      overall_health: metrics.health_status,
      error_rate: parseFloat(metrics.error_rate || 0),
      daily_throughput: parseInt(metrics.outreach_sent_24h || 0),
      new_leads_today: parseInt(metrics.new_leads_24h || 0)
    }
  };
}

function getStageStatus(rate: number): 'excellent' | 'good' | 'warning' | 'critical' {
  if (rate >= 90) return 'excellent';
  if (rate >= 75) return 'good';
  if (rate >= 50) return 'warning';
  return 'critical';
}

async function getStageQualityMetrics(client: any) {
  const stageMetricsQuery = `
    WITH stage_quality AS (
      SELECT
        'Email Validation' as stage_name,
        'email_verification' as stage_key,
        COUNT(CASE WHEN email IS NOT NULL AND email != '' THEN 1 END) as eligible_leads,
        COUNT(CASE WHEN email_verification_status = 'completed' THEN 1 END) as successful,
        COUNT(CASE WHEN email_verification_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN email_verification_status = 'error' THEN 1 END) as errors,
        ROUND(AVG(CASE WHEN email_verification_status = 'completed'
          THEN EXTRACT(EPOCH FROM (email_verification_date - created_at)) / 3600 END), 2) as avg_processing_hours
      FROM contacts
      WHERE created_at >= NOW() - INTERVAL '30 days'

      UNION ALL

      SELECT
        'Lead Research' as stage_name,
        'osint' as stage_key,
        COUNT(CASE WHEN email_verification_status = 'completed' THEN 1 END) as eligible_leads,
        COUNT(CASE WHEN osint_status = 'completed' THEN 1 END) as successful,
        COUNT(CASE WHEN osint_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN osint_status = 'error' THEN 1 END) as errors,
        ROUND(AVG(CASE WHEN osint_status = 'completed'
          THEN EXTRACT(EPOCH FROM (osint_date - email_verification_date)) / 3600 END), 2) as avg_processing_hours
      FROM contacts
      WHERE created_at >= NOW() - INTERVAL '30 days'
      AND email_verification_status = 'completed'

      UNION ALL

      SELECT
        'Lead Scoring' as stage_name,
        'classification' as stage_key,
        COUNT(CASE WHEN overview_extraction_status = 'completed' THEN 1 END) as eligible_leads,
        COUNT(CASE WHEN classification_status = 'completed' THEN 1 END) as successful,
        COUNT(CASE WHEN classification_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN classification_status = 'error' THEN 1 END) as errors,
        ROUND(AVG(CASE WHEN classification_status = 'completed'
          THEN EXTRACT(EPOCH FROM (classification_date - overview_extraction_date)) / 3600 END), 2) as avg_processing_hours
      FROM contacts
      WHERE created_at >= NOW() - INTERVAL '30 days'
      AND overview_extraction_status = 'completed'

      UNION ALL

      SELECT
        'Content Creation' as stage_name,
        'email_generation' as stage_key,
        COUNT(CASE WHEN classification_status = 'completed' THEN 1 END) as eligible_leads,
        COUNT(CASE WHEN email_generation_status = 'completed' THEN 1 END) as successful,
        COUNT(CASE WHEN email_generation_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN email_generation_status = 'error' THEN 1 END) as errors,
        ROUND(AVG(CASE WHEN email_generation_status = 'completed'
          THEN EXTRACT(EPOCH FROM (email_generation_date - classification_date)) / 3600 END), 2) as avg_processing_hours
      FROM contacts
      WHERE created_at >= NOW() - INTERVAL '30 days'
      AND classification_status = 'completed'

      UNION ALL

      SELECT
        'Outreach Delivery' as stage_name,
        'email_sending' as stage_key,
        COUNT(CASE WHEN email_generation_status = 'completed' THEN 1 END) as eligible_leads,
        COUNT(CASE WHEN email_sending_status = 'completed' THEN 1 END) as successful,
        COUNT(CASE WHEN email_sending_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN email_sending_status = 'error' THEN 1 END) as errors,
        ROUND(AVG(CASE WHEN email_sending_status = 'completed'
          THEN EXTRACT(EPOCH FROM (email_sending_date - email_generation_date)) / 3600 END), 2) as avg_processing_hours
      FROM contacts
      WHERE created_at >= NOW() - INTERVAL '30 days'
      AND email_generation_status = 'completed'
    )
    SELECT
      *,
      ROUND(successful * 100.0 / NULLIF(eligible_leads, 0), 2) as success_rate,
      ROUND(failed * 100.0 / NULLIF(eligible_leads, 0), 2) as failure_rate,
      ROUND(errors * 100.0 / NULLIF(eligible_leads, 0), 2) as error_rate
    FROM stage_quality
    ORDER BY
      CASE stage_key
        WHEN 'email_verification' THEN 1
        WHEN 'osint' THEN 2
        WHEN 'overview_extraction' THEN 3
        WHEN 'classification' THEN 4
        WHEN 'email_generation' THEN 5
        WHEN 'email_sending' THEN 6
      END
  `;

  const result = await client.query(stageMetricsQuery);
  return result.rows.map((row: any) => ({
    stage_name: row.stage_name,
    stage_key: row.stage_key,
    metrics: {
      eligible_leads: parseInt(row.eligible_leads || 0),
      successful: parseInt(row.successful || 0),
      failed: parseInt(row.failed || 0),
      errors: parseInt(row.errors || 0),
      success_rate: parseFloat(row.success_rate || 0),
      failure_rate: parseFloat(row.failure_rate || 0),
      error_rate: parseFloat(row.error_rate || 0),
      avg_processing_hours: parseFloat(row.avg_processing_hours || 0)
    },
    quality_status: getQualityStatus(row.success_rate, row.error_rate),
    recommendations: getStageRecommendations(row.stage_key, row.success_rate, row.error_rate)
  }));
}

function getQualityStatus(successRate: number, errorRate: number): 'excellent' | 'good' | 'warning' | 'critical' {
  if (successRate >= 95 && errorRate <= 2) return 'excellent';
  if (successRate >= 85 && errorRate <= 5) return 'good';
  if (successRate >= 70 && errorRate <= 10) return 'warning';
  return 'critical';
}

function getStageRecommendations(stageKey: string, successRate: number, errorRate: number): string[] {
  const recommendations: string[] = [];

  if (successRate < 80) {
    recommendations.push(`Improve ${stageKey} success rate (currently ${successRate}%)`);
  }

  if (errorRate > 5) {
    recommendations.push(`Reduce ${stageKey} error rate (currently ${errorRate}%)`);
  }

  const stageSpecific: Record<string, string[]> = {
    email_verification: [
      'Review email validation service configuration',
      'Update email validation rules',
      'Check for data source quality issues'
    ],
    osint: [
      'Optimize OSINT search parameters',
      'Review data source availability',
      'Implement better error handling'
    ],
    classification: [
      'Review classification criteria',
      'Update scoring algorithms',
      'Improve data quality inputs'
    ],
    email_generation: [
      'Review content templates',
      'Optimize personalization logic',
      'Check AI service performance'
    ],
    email_sending: [
      'Review email delivery settings',
      'Check sender reputation',
      'Optimize sending schedules'
    ]
  };

  if (successRate < 90 || errorRate > 3) {
    recommendations.push(...(stageSpecific[stageKey] || []));
  }

  return recommendations;
}

async function getDataCompletenessScores(client: any) {
  const completenessQuery = `
    WITH completeness_metrics AS (
      SELECT
        COUNT(*) as total_contacts,
        COUNT(CASE WHEN email IS NOT NULL AND email != '' THEN 1 END) as contacts_with_email,
        COUNT(CASE WHEN first_name IS NOT NULL AND first_name != '' THEN 1 END) as contacts_with_first_name,
        COUNT(CASE WHEN last_name IS NOT NULL AND last_name != '' THEN 1 END) as contacts_with_last_name,
        COUNT(CASE WHEN title IS NOT NULL AND title != '' THEN 1 END) as contacts_with_title,
        COUNT(CASE WHEN company_id IS NOT NULL THEN 1 END) as contacts_with_company,
        COUNT(CASE WHEN linkedin_url IS NOT NULL AND linkedin_url != '' THEN 1 END) as contacts_with_linkedin,
        COUNT(CASE WHEN phone_number IS NOT NULL AND phone_number != '' THEN 1 END) as contacts_with_phone
      FROM contacts
      WHERE created_at >= NOW() - INTERVAL '30 days'
    )
    SELECT
      *,
      ROUND(contacts_with_email * 100.0 / NULLIF(total_contacts, 0), 2) as email_completeness,
      ROUND(contacts_with_first_name * 100.0 / NULLIF(total_contacts, 0), 2) as first_name_completeness,
      ROUND(contacts_with_last_name * 100.0 / NULLIF(total_contacts, 0), 2) as last_name_completeness,
      ROUND(contacts_with_title * 100.0 / NULLIF(total_contacts, 0), 2) as title_completeness,
      ROUND(contacts_with_company * 100.0 / NULLIF(total_contacts, 0), 2) as company_completeness,
      ROUND(contacts_with_linkedin * 100.0 / NULLIF(total_contacts, 0), 2) as linkedin_completeness,
      ROUND(contacts_with_phone * 100.0 / NULLIF(total_contacts, 0), 2) as phone_completeness
    FROM completeness_metrics
  `;

  const result = await client.query(completenessQuery);
  const metrics = result.rows[0];

  const fields = [
    { name: 'Email Address', key: 'email_completeness', critical: true },
    { name: 'First Name', key: 'first_name_completeness', critical: true },
    { name: 'Last Name', key: 'last_name_completeness', critical: true },
    { name: 'Job Title', key: 'title_completeness', critical: false },
    { name: 'Company', key: 'company_completeness', critical: true },
    { name: 'LinkedIn Profile', key: 'linkedin_completeness', critical: false },
    { name: 'Phone Number', key: 'phone_completeness', critical: false }
  ];

  const fieldScores = fields.map(field => ({
    field_name: field.name,
    completeness_rate: parseFloat(metrics[field.key] || 0),
    is_critical: field.critical,
    status: getCompletenessStatus(metrics[field.key], field.critical),
    improvement_potential: 100 - parseFloat(metrics[field.key] || 0)
  }));

  // Calculate overall completeness score
  const criticalFields = fieldScores.filter(f => f.is_critical);
  const overallScore = criticalFields.reduce((sum, field) => sum + field.completeness_rate, 0) / criticalFields.length;

  return {
    overall_score: parseFloat(overallScore.toFixed(2)),
    overall_status: getCompletenessStatus(overallScore, true),
    field_scores: fieldScores,
    recommendations: getCompletenessRecommendations(fieldScores)
  };
}

function getCompletenessStatus(rate: number, isCritical: boolean): 'excellent' | 'good' | 'warning' | 'critical' {
  const thresholds = isCritical ? [95, 85, 70] : [90, 75, 60];

  if (rate >= thresholds[0]) return 'excellent';
  if (rate >= thresholds[1]) return 'good';
  if (rate >= thresholds[2]) return 'warning';
  return 'critical';
}

function getCompletenessRecommendations(fieldScores: any[]): string[] {
  const recommendations: string[] = [];

  fieldScores.forEach(field => {
    if (field.is_critical && field.completeness_rate < 90) {
      recommendations.push(`Improve ${field.field_name} data collection (${field.completeness_rate}% complete)`);
    }
    if (field.completeness_rate < 50) {
      recommendations.push(`Critical: ${field.field_name} field needs immediate attention`);
    }
  });

  if (recommendations.length === 0) {
    recommendations.push('Data completeness is excellent across all fields');
  }

  return recommendations;
}

async function getActionableInsights(client: any) {
  const insightsQuery = `
    WITH pipeline_bottlenecks AS (
      SELECT
        'email_verification' as stage,
        COUNT(CASE WHEN email_verification_status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN email_verification_status = 'failed' THEN 1 END) as failed_count,
        COUNT(CASE WHEN email_verification_status = 'error' THEN 1 END) as error_count
      FROM contacts
      WHERE created_at >= NOW() - INTERVAL '7 days'

      UNION ALL

      SELECT
        'osint' as stage,
        COUNT(CASE WHEN osint_status = 'pending' AND email_verification_status = 'completed' THEN 1 END) as pending_count,
        COUNT(CASE WHEN osint_status = 'failed' THEN 1 END) as failed_count,
        COUNT(CASE WHEN osint_status = 'error' THEN 1 END) as error_count
      FROM contacts
      WHERE created_at >= NOW() - INTERVAL '7 days'

      UNION ALL

      SELECT
        'classification' as stage,
        COUNT(CASE WHEN classification_status = 'pending' AND overview_extraction_status = 'completed' THEN 1 END) as pending_count,
        COUNT(CASE WHEN classification_status = 'failed' THEN 1 END) as failed_count,
        COUNT(CASE WHEN classification_status = 'error' THEN 1 END) as error_count
      FROM contacts
      WHERE created_at >= NOW() - INTERVAL '7 days'
    )
    SELECT
      stage,
      pending_count,
      failed_count,
      error_count,
      (pending_count + failed_count + error_count) as total_issues
    FROM pipeline_bottlenecks
    ORDER BY total_issues DESC
  `;

  const result = await client.query(insightsQuery);

  const insights = result.rows.map((row: any) => {
    const stage = row.stage;
    const totalIssues = parseInt(row.total_issues || 0);
    const pendingCount = parseInt(row.pending_count || 0);
    const failedCount = parseInt(row.failed_count || 0);
    const errorCount = parseInt(row.error_count || 0);

    let priority: 'high' | 'medium' | 'low' = 'low';
    const actionItems: string[] = [];

    if (totalIssues > 100) priority = 'high';
    else if (totalIssues > 50) priority = 'medium';

    if (pendingCount > 50) {
      actionItems.push(`${pendingCount} leads pending in ${stage} - consider increasing processing capacity`);
    }
    if (failedCount > 20) {
      actionItems.push(`${failedCount} leads failed in ${stage} - review failure patterns`);
    }
    if (errorCount > 10) {
      actionItems.push(`${errorCount} leads with errors in ${stage} - investigate error causes`);
    }

    return {
      stage,
      priority,
      total_issues: totalIssues,
      breakdown: {
        pending: pendingCount,
        failed: failedCount,
        errors: errorCount
      },
      action_items: actionItems
    };
  });

  return insights.filter((insight: any) => insight.total_issues > 0);
}

async function getQualityTrends(client: any) {
  const trendsQuery = `
    WITH daily_quality AS (
      SELECT
        DATE(created_at) as date,
        COUNT(*) as total_leads,
        COUNT(CASE WHEN email_verification_status = 'completed' THEN 1 END) as email_validated,
        COUNT(CASE WHEN email_sending_status = 'completed' THEN 1 END) as outreach_completed,
        COUNT(CASE WHEN processing_error_count > 0 THEN 1 END) as leads_with_errors
      FROM contacts
      WHERE created_at >= NOW() - INTERVAL '14 days'
      GROUP BY DATE(created_at)
      ORDER BY DATE(created_at)
    )
    SELECT
      date,
      total_leads,
      email_validated,
      outreach_completed,
      leads_with_errors,
      ROUND(email_validated * 100.0 / NULLIF(total_leads, 0), 2) as validation_rate,
      ROUND(outreach_completed * 100.0 / NULLIF(total_leads, 0), 2) as completion_rate,
      ROUND(leads_with_errors * 100.0 / NULLIF(total_leads, 0), 2) as error_rate
    FROM daily_quality
  `;

  const result = await client.query(trendsQuery);

  return {
    daily_metrics: result.rows.map((row: any) => ({
      date: row.date,
      total_leads: parseInt(row.total_leads || 0),
      validation_rate: parseFloat(row.validation_rate || 0),
      completion_rate: parseFloat(row.completion_rate || 0),
      error_rate: parseFloat(row.error_rate || 0)
    })),
    trend_analysis: analyzeTrends(result.rows)
  };
}

function analyzeTrends(data: any[]) {
  if (data.length < 2) return { validation: 'stable', completion: 'stable', errors: 'stable' };

  const recent = data.slice(-3);
  const earlier = data.slice(0, 3);

  const avgRecentValidation = recent.reduce((sum, d) => sum + parseFloat(d.validation_rate || 0), 0) / recent.length;
  const avgEarlierValidation = earlier.reduce((sum, d) => sum + parseFloat(d.validation_rate || 0), 0) / earlier.length;

  const avgRecentCompletion = recent.reduce((sum, d) => sum + parseFloat(d.completion_rate || 0), 0) / recent.length;
  const avgEarlierCompletion = earlier.reduce((sum, d) => sum + parseFloat(d.completion_rate || 0), 0) / earlier.length;

  const avgRecentErrors = recent.reduce((sum, d) => sum + parseFloat(d.error_rate || 0), 0) / recent.length;
  const avgEarlierErrors = earlier.reduce((sum, d) => sum + parseFloat(d.error_rate || 0), 0) / earlier.length;

  return {
    validation: avgRecentValidation > avgEarlierValidation + 2 ? 'improving' :
                avgRecentValidation < avgEarlierValidation - 2 ? 'declining' : 'stable',
    completion: avgRecentCompletion > avgEarlierCompletion + 2 ? 'improving' :
                avgRecentCompletion < avgEarlierCompletion - 2 ? 'declining' : 'stable',
    errors: avgRecentErrors < avgEarlierErrors - 1 ? 'improving' :
            avgRecentErrors > avgEarlierErrors + 1 ? 'worsening' : 'stable'
  };
}