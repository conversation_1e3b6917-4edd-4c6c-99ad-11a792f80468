import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30d'
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    // Calculate date range
    const { start, end } = calculateDateRange(timeRange, startDate, endDate)

    // Fetch data from different perspectives
    const [newsData, dealsData, companiesData, personsData, mappingValidation] = await Promise.all([
      getNewsEnrichmentMetrics(start, end),
      getDealsEnrichmentMetrics(start, end),
      getCompaniesEnrichmentMetrics(start, end),
      getPersonsEnrichmentMetrics(start, end),
      getMappingValidationMetrics(start, end)
    ])

    return NextResponse.json({
      newsData,
      dealsData,
      companiesData,
      personsData,
      mappingValidation,
      timeRange: { startDate: start, endDate: end }
    })
  } catch (error) {
    console.error('Error fetching news enrichment metrics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch news enrichment metrics' },
      { status: 500 }
    )
  }
}

function calculateDateRange(timeRange: string, startDate?: string | null, endDate?: string | null) {
  const end = endDate ? new Date(endDate) : new Date()
  let start: Date

  if (startDate) {
    start = new Date(startDate)
  } else {
    start = new Date(end)
    switch (timeRange) {
      case '7d':
        start.setDate(start.getDate() - 7)
        break
      case '30d':
        start.setDate(start.getDate() - 30)
        break
      case '90d':
        start.setDate(start.getDate() - 90)
        break
      case '1y':
        start.setFullYear(start.getFullYear() - 1)
        break
      default:
        start.setDate(start.getDate() - 30)
    }
  }

  return {
    start: start.toISOString().split('T')[0],
    end: end.toISOString().split('T')[0]
  }
}

async function getNewsEnrichmentMetrics(startDate: string, endDate: string) {
  const client = await pool.connect()
  try {
    // Get overall news enrichment statistics
    const overallStats = await client.query(`
      SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT news_id) as unique_articles,
        SUM(CASE WHEN is_deal_specific = true THEN 1 ELSE 0 END) as deal_records,
        SUM(CASE WHEN is_deal_specific = false THEN 1 ELSE 0 END) as article_records,
        AVG(source_confidence) as avg_confidence,
        COUNT(DISTINCT source_name) as unique_sources
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
    `, [startDate, endDate])

    // Get throughput metrics (daily processing)
    const throughputMetrics = await client.query(`
      SELECT 
        DATE(ne.created_at) as date,
        COUNT(*) as records_processed,
        COUNT(DISTINCT ne.news_id) as articles_processed
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      GROUP BY DATE(ne.created_at)
      ORDER BY date
    `, [startDate, endDate])

    // Get recent activity (last 24 hours)
    const recentActivity = await client.query(`
      SELECT 
        COUNT(*) as last_24_hours,
        COUNT(DISTINCT news_id) as unique_articles_24h
      FROM news_enrichment ne
      WHERE ne.created_at >= NOW() - INTERVAL '24 hours'
    `)

    // Get nullability metrics for key fields
    const nullabilityMetrics = await client.query(`
      SELECT 
        'headline' as field_name,
        COUNT(*) as total_records,
        COUNT(headline) as non_null_records,
        ROUND(100.0 * COUNT(headline) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      UNION ALL
      SELECT 
        'deal_type' as field_name,
        COUNT(*) as total_records,
        COUNT(deal_type) as non_null_records,
        ROUND(100.0 * COUNT(deal_type) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      UNION ALL
      SELECT 
        'property_type' as field_name,
        COUNT(*) as total_records,
        COUNT(property_type) as non_null_records,
        ROUND(100.0 * COUNT(property_type) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      UNION ALL
      SELECT 
        'deal_size' as field_name,
        COUNT(*) as total_records,
        COUNT(deal_size) as non_null_records,
        ROUND(100.0 * COUNT(deal_size) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      UNION ALL
      SELECT 
        'square_footage' as field_name,
        COUNT(*) as total_records,
        COUNT(square_footage) as non_null_records,
        ROUND(100.0 * COUNT(square_footage) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      UNION ALL
      SELECT 
        'cap_rate' as field_name,
        COUNT(*) as total_records,
        COUNT(cap_rate) as non_null_records,
        ROUND(100.0 * COUNT(cap_rate) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      UNION ALL
      SELECT 
        'location_city' as field_name,
        COUNT(*) as total_records,
        COUNT(location_city) as non_null_records,
        ROUND(100.0 * COUNT(location_city) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      UNION ALL
      SELECT 
        'source_confidence' as field_name,
        COUNT(*) as total_records,
        COUNT(source_confidence) as non_null_records,
        ROUND(100.0 * COUNT(source_confidence) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
    `)

    // Transform nullability metrics
    const nullabilityData = nullabilityMetrics.rows.reduce((acc, row) => {
      acc[row.field_name] = {
        totalRecords: parseInt(row.total_records),
        nonNullRecords: parseInt(row.non_null_records),
        completenessPercentage: parseFloat(row.completeness_percentage),
        nullPercentage: 100 - parseFloat(row.completeness_percentage)
      }
      return acc
    }, {} as Record<string, any>)

    return {
      totalRecords: parseInt(overallStats.rows[0]?.total_records || '0'),
      uniqueArticles: parseInt(overallStats.rows[0]?.unique_articles || '0'),
      dealRecords: parseInt(overallStats.rows[0]?.deal_records || '0'),
      articleRecords: parseInt(overallStats.rows[0]?.article_records || '0'),
      avgConfidence: parseFloat(overallStats.rows[0]?.avg_confidence || '0'),
      uniqueSources: parseInt(overallStats.rows[0]?.unique_sources || '0'),
      throughputMetrics: {
        daily: throughputMetrics.rows.map(row => ({
          date: row.date,
          records: parseInt(row.records_processed),
          articles: parseInt(row.articles_processed)
        }))
      },
      recentActivity: {
        last24Hours: parseInt(recentActivity.rows[0]?.last_24_hours || '0'),
        uniqueArticles24h: parseInt(recentActivity.rows[0]?.unique_articles_24h || '0')
      },
      nullabilityMetrics: nullabilityData
    }
  } finally {
    client.release()
  }
}

async function getDealsEnrichmentMetrics(startDate: string, endDate: string) {
  const client = await pool.connect()
  try {
    // Get deal-specific metrics
    const dealStats = await client.query(`
      SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT news_id) as unique_articles,
        COUNT(deal_type) as deals_with_type,
        COUNT(deal_size) as deals_with_size,
        COUNT(property_type) as deals_with_property_type,
        COUNT(square_footage) as deals_with_square_footage,
        COUNT(cap_rate) as deals_with_cap_rate,
        AVG(deal_size) as avg_deal_size,
        AVG(cap_rate) as avg_cap_rate
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      AND ne.is_deal_specific = true
    `, [startDate, endDate])

    // Get deal type distribution
    const dealTypeDistribution = await client.query(`
      SELECT 
        jsonb_array_elements_text(deal_type) as deal_type,
        COUNT(*) as count,
        ROUND(100.0 * COUNT(*) / (SELECT COUNT(*) FROM news_enrichment WHERE is_deal_specific = true AND created_at BETWEEN $1 AND $2), 2) as percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      AND ne.is_deal_specific = true
      AND deal_type IS NOT NULL
      AND deal_type != '[]'
      GROUP BY jsonb_array_elements_text(deal_type)
      ORDER BY count DESC
      LIMIT 10
    `, [startDate, endDate])

    // Get nullability metrics for deal-specific fields
    const dealNullabilityMetrics = await client.query(`
      SELECT 
        'deal_type' as field_name,
        COUNT(*) as total_records,
        COUNT(CASE WHEN deal_type IS NOT NULL AND deal_type != '[]' THEN 1 END) as non_null_records,
        ROUND(100.0 * COUNT(CASE WHEN deal_type IS NOT NULL AND deal_type != '[]' THEN 1 END) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      AND ne.is_deal_specific = true
      UNION ALL
      SELECT 
        'deal_size' as field_name,
        COUNT(*) as total_records,
        COUNT(deal_size) as non_null_records,
        ROUND(100.0 * COUNT(deal_size) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      AND ne.is_deal_specific = true
      UNION ALL
      SELECT 
        'property_type' as field_name,
        COUNT(*) as total_records,
        COUNT(property_type) as non_null_records,
        ROUND(100.0 * COUNT(property_type) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      AND ne.is_deal_specific = true
      UNION ALL
      SELECT 
        'cap_rate' as field_name,
        COUNT(*) as total_records,
        COUNT(cap_rate) as non_null_records,
        ROUND(100.0 * COUNT(cap_rate) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      AND ne.is_deal_specific = true
    `)

    // Transform nullability metrics
    const nullabilityData = dealNullabilityMetrics.rows.reduce((acc, row) => {
      acc[row.field_name] = {
        totalRecords: parseInt(row.total_records),
        nonNullRecords: parseInt(row.non_null_records),
        completenessPercentage: parseFloat(row.completeness_percentage),
        nullPercentage: 100 - parseFloat(row.completeness_percentage)
      }
      return acc
    }, {} as Record<string, any>)

    return {
      totalRecords: parseInt(dealStats.rows[0]?.total_records || '0'),
      uniqueArticles: parseInt(dealStats.rows[0]?.unique_articles || '0'),
      dealsWithType: parseInt(dealStats.rows[0]?.deals_with_type || '0'),
      dealsWithSize: parseInt(dealStats.rows[0]?.deals_with_size || '0'),
      dealsWithPropertyType: parseInt(dealStats.rows[0]?.deals_with_property_type || '0'),
      avgDealSize: parseFloat(dealStats.rows[0]?.avg_deal_size || '0'),
      avgCapRate: parseFloat(dealStats.rows[0]?.avg_cap_rate || '0'),
      dealTypeDistribution: dealTypeDistribution.rows,
      nullabilityMetrics: nullabilityData
    }
  } finally {
    client.release()
  }
}

async function getCompaniesEnrichmentMetrics(startDate: string, endDate: string) {
  const client = await pool.connect()
  try {
    // Get company-related metrics
    const companyStats = await client.query(`
      SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT news_id) as unique_articles,
        COUNT(CASE WHEN company_name::text != '[]' THEN 1 END) as articles_with_companies,
        COUNT(CASE WHEN buyer_name::text != '[]' THEN 1 END) as articles_with_buyers,
        COUNT(CASE WHEN seller_name::text != '[]' THEN 1 END) as articles_with_sellers,
        COUNT(CASE WHEN lender_name::text != '[]' THEN 1 END) as articles_with_lenders,
        COUNT(CASE WHEN broker_name::text != '[]' THEN 1 END) as articles_with_brokers
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
    `, [startDate, endDate])

    // Get company nullability metrics
    const companyNullabilityMetrics = await client.query(`
      SELECT 
        'company_name' as field_name,
        COUNT(*) as total_records,
        COUNT(CASE WHEN company_name::text != '[]' THEN 1 END) as non_null_records,
        ROUND(100.0 * COUNT(CASE WHEN company_name::text != '[]' THEN 1 END) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      UNION ALL
      SELECT 
        'buyer_name' as field_name,
        COUNT(*) as total_records,
        COUNT(CASE WHEN buyer_name::text != '[]' THEN 1 END) as non_null_records,
        ROUND(100.0 * COUNT(CASE WHEN buyer_name::text != '[]' THEN 1 END) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      UNION ALL
      SELECT 
        'seller_name' as field_name,
        COUNT(*) as total_records,
        COUNT(CASE WHEN seller_name::text != '[]' THEN 1 END) as non_null_records,
        ROUND(100.0 * COUNT(CASE WHEN seller_name::text != '[]' THEN 1 END) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      UNION ALL
      SELECT 
        'lender_name' as field_name,
        COUNT(*) as total_records,
        COUNT(CASE WHEN lender_name::text != '[]' THEN 1 END) as non_null_records,
        ROUND(100.0 * COUNT(CASE WHEN lender_name::text != '[]' THEN 1 END) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
    `)

    // Transform nullability metrics
    const nullabilityData = companyNullabilityMetrics.rows.reduce((acc, row) => {
      acc[row.field_name] = {
        totalRecords: parseInt(row.total_records),
        nonNullRecords: parseInt(row.non_null_records),
        completenessPercentage: parseFloat(row.completeness_percentage),
        nullPercentage: 100 - parseFloat(row.completeness_percentage)
      }
      return acc
    }, {} as Record<string, any>)

    return {
      totalRecords: parseInt(companyStats.rows[0]?.total_records || '0'),
      uniqueArticles: parseInt(companyStats.rows[0]?.unique_articles || '0'),
      articlesWithCompanies: parseInt(companyStats.rows[0]?.articles_with_companies || '0'),
      articlesWithBuyers: parseInt(companyStats.rows[0]?.articles_with_buyers || '0'),
      articlesWithSellers: parseInt(companyStats.rows[0]?.articles_with_sellers || '0'),
      articlesWithLenders: parseInt(companyStats.rows[0]?.articles_with_lenders || '0'),
      articlesWithBrokers: parseInt(companyStats.rows[0]?.articles_with_brokers || '0'),
      nullabilityMetrics: nullabilityData
    }
  } finally {
    client.release()
  }
}

async function getPersonsEnrichmentMetrics(startDate: string, endDate: string) {
  const client = await pool.connect()
  try {
    // Get person-related metrics
    const personStats = await client.query(`
      SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT news_id) as unique_articles,
        COUNT(CASE WHEN report_author::text != '[]' THEN 1 END) as articles_with_authors,
        COUNT(CASE WHEN linked_entities::text != '[]' THEN 1 END) as articles_with_linked_entities
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
    `, [startDate, endDate])

    // Get person nullability metrics
    const personNullabilityMetrics = await client.query(`
      SELECT 
        'report_author' as field_name,
        COUNT(*) as total_records,
        COUNT(CASE WHEN report_author::text != '[]' THEN 1 END) as non_null_records,
        ROUND(100.0 * COUNT(CASE WHEN report_author::text != '[]' THEN 1 END) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      UNION ALL
      SELECT 
        'linked_entities' as field_name,
        COUNT(*) as total_records,
        COUNT(CASE WHEN linked_entities::text != '[]' THEN 1 END) as non_null_records,
        ROUND(100.0 * COUNT(CASE WHEN linked_entities::text != '[]' THEN 1 END) / COUNT(*), 2) as completeness_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
    `)

    // Transform nullability metrics
    const nullabilityData = personNullabilityMetrics.rows.reduce((acc, row) => {
      acc[row.field_name] = {
        totalRecords: parseInt(row.total_records),
        nonNullRecords: parseInt(row.non_null_records),
        completenessPercentage: parseFloat(row.completeness_percentage),
        nullPercentage: 100 - parseFloat(row.completeness_percentage)
      }
      return acc
    }, {} as Record<string, any>)

    return {
      totalRecords: parseInt(personStats.rows[0]?.total_records || '0'),
      uniqueArticles: parseInt(personStats.rows[0]?.unique_articles || '0'),
      articlesWithAuthors: <AUTHORS>
      articlesWithLinkedEntities: parseInt(personStats.rows[0]?.articles_with_linked_entities || '0'),
      nullabilityMetrics: nullabilityData
    }
  } finally {
    client.release()
  }
}

async function getMappingValidationMetrics(startDate: string, endDate: string) {
  const client = await pool.connect()
  try {
    // Get mapping validation metrics
    const mappingStats = await client.query(`
      SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT news_id) as unique_articles,
        COUNT(CASE WHEN deal_type IS NOT NULL AND deal_type != '[]' THEN 1 END) as records_with_deal_types,
        COUNT(property_type) as records_with_property_types,
        COUNT(CASE WHEN strategies::text != '[]' THEN 1 END) as records_with_strategies,
        ROUND(100.0 * COUNT(CASE WHEN deal_type IS NOT NULL AND deal_type != '[]' THEN 1 END) / COUNT(*), 2) as deal_type_validation_percentage,
        ROUND(100.0 * COUNT(property_type) / COUNT(*), 2) as property_type_validation_percentage,
        ROUND(100.0 * COUNT(CASE WHEN strategies::text != '[]' THEN 1 END) / COUNT(*), 2) as strategies_validation_percentage
      FROM news_enrichment ne
      WHERE ne.created_at BETWEEN $1 AND $2
      AND ne.is_deal_specific = true
    `, [startDate, endDate])

    // Check mapping validation against central_mapping table
    const mappingValidation = await client.query(`
      WITH deal_type_expanded AS (
        SELECT 
          ne.news_id,
          jsonb_array_elements_text(ne.deal_type) as deal_type_value,
          ne.property_type
        FROM news_enrichment ne
        WHERE ne.created_at BETWEEN $1 AND $2
        AND ne.is_deal_specific = true
        AND ne.deal_type IS NOT NULL
        AND ne.deal_type != '[]'
      ),
      mapping_check AS (
        SELECT 
          dte.news_id,
          dte.deal_type_value,
          dte.property_type,
          CASE WHEN cm_deal.value_1 IS NOT NULL THEN 1 ELSE 0 END as deal_type_valid,
          CASE WHEN cm_prop.value_1 IS NOT NULL THEN 1 ELSE 0 END as property_type_valid
        FROM deal_type_expanded dte
        LEFT JOIN central_mapping cm_deal ON cm_deal.type = 'Deal Type' AND cm_deal.value_1 = dte.deal_type_value AND cm_deal.is_active = true
        LEFT JOIN central_mapping cm_prop ON cm_prop.type = 'Property Type' AND cm_prop.value_1 = dte.property_type AND cm_prop.is_active = true
      )
      SELECT 
        COUNT(*) as total_records,
        SUM(deal_type_valid) as valid_deal_types,
        SUM(property_type_valid) as valid_property_types,
        ROUND(100.0 * SUM(deal_type_valid) / COUNT(*), 2) as deal_type_validation_rate,
        ROUND(100.0 * SUM(property_type_valid) / COUNT(*), 2) as property_type_validation_rate
      FROM mapping_check
    `, [startDate, endDate])

    const overallValidation = mappingStats.rows[0]
    const validationCheck = mappingValidation.rows[0]

    return {
      overallValidation: {
        total_records: parseInt(overallValidation?.total_records || '0'),
        unique_articles: parseInt(overallValidation?.unique_articles || '0'),
        records_with_deal_types: parseInt(overallValidation?.records_with_deal_types || '0'),
        records_with_property_types: parseInt(overallValidation?.records_with_property_types || '0'),
        records_with_strategies: parseInt(overallValidation?.records_with_strategies || '0'),
        records_with_capital_positions: 0, // Would need to implement capital position counting
        deal_type_validation_percentage: parseFloat(overallValidation?.deal_type_validation_percentage || '0'),
        property_type_validation_percentage: parseFloat(overallValidation?.property_type_validation_percentage || '0'),
        strategies_validation_percentage: parseFloat(overallValidation?.strategies_validation_percentage || '0'),
        overall_mapping_validation_percentage: parseFloat(validationCheck?.deal_type_validation_rate || '0')
      },
      validationDetails: {
        totalRecords: parseInt(validationCheck?.total_records || '0'),
        validDealTypes: parseInt(validationCheck?.valid_deal_types || '0'),
        validPropertyTypes: parseInt(validationCheck?.valid_property_types || '0'),
        dealTypeValidationRate: parseFloat(validationCheck?.deal_type_validation_rate || '0'),
        propertyTypeValidationRate: parseFloat(validationCheck?.property_type_validation_rate || '0')
      }
    }
  } finally {
    client.release()
  }
} 