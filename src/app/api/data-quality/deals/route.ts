import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

interface DataQualityMetrics {
  tableName: string
  totalRecords: number
  nullabilityMetrics: Record<string, {
    nullCount: number
    nullPercentage: number
    totalRecords: number
  }>
  throughputMetrics: {
    hourly: Array<{
      hour: string
      count: number
    }>
    daily: Array<{
      date: string
      count: number
    }>
  }
  recentActivity: {
    last24Hours: number
    last7Days: number
    last30Days: number
  }
  dateRangeActivity?: {
    totalInRange: number
    last24HoursInRange: number
    last7DaysInRange: number
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate') || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString() // Default to 7 days ago
    const endDate = searchParams.get('endDate') || new Date().toISOString()

    // Get metrics for overview, debt, and equity sections
    const [overview, debt, equity] = await Promise.all([
      getOverviewMetrics(startDate, endDate),
      getDebtMetrics(startDate, endDate),
      getEquityMetrics(startDate, endDate)
    ])

    return NextResponse.json({
      success: true,
      data: {
        overview,
        debt,
        equity
      },
      timeRange: {
        startDate,
        endDate
      }
    })
  } catch (error) {
    console.error('Error fetching deals data quality metrics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch deals data quality metrics' },
      { status: 500 }
    )
  }
}

async function getJobRecentActivityCounts() {
  // Query the jobs table for completed and failed deal-processing jobs in the last 24h, 7d, 30d
  const result = await pool.query(`
    SELECT
      COUNT(CASE WHEN status = 'completed' AND completed_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as completed_24h,
      COUNT(CASE WHEN status = 'completed' AND completed_at >= NOW() - INTERVAL '7 days' THEN 1 END) as completed_7d,
      COUNT(CASE WHEN status = 'completed' AND completed_at >= NOW() - INTERVAL '30 days' THEN 1 END) as completed_30d,
      COUNT(CASE WHEN status = 'failed' AND failed_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as failed_24h,
      COUNT(CASE WHEN status = 'failed' AND failed_at >= NOW() - INTERVAL '7 days' THEN 1 END) as failed_7d,
      COUNT(CASE WHEN status = 'failed' AND failed_at >= NOW() - INTERVAL '30 days' THEN 1 END) as failed_30d
    FROM jobs
    WHERE queue_name = 'deal-processing'
  `);
  return {
    completed: {
      last24Hours: parseInt(result.rows[0]?.completed_24h?.toString()) || 0,
      last7Days: parseInt(result.rows[0]?.completed_7d?.toString()) || 0,
      last30Days: parseInt(result.rows[0]?.completed_30d?.toString()) || 0,
    },
    failed: {
      last24Hours: parseInt(result.rows[0]?.failed_24h?.toString()) || 0,
      last7Days: parseInt(result.rows[0]?.failed_7d?.toString()) || 0,
      last30Days: parseInt(result.rows[0]?.failed_30d?.toString()) || 0,
    }
  };
}

async function getOverviewMetrics(startDate: string, endDate: string): Promise<DataQualityMetrics & { jobRecentActivity?: any }> {
  // Add or adjust fields as needed for your deals table
  const comprehensiveMetricsResult = await pool.query(`
    SELECT 
      COUNT(*) as total_records,
      COUNT(CASE WHEN deal_name IS NULL OR deal_name = '' THEN 1 END) as deal_name_null_count,
      COUNT(CASE WHEN sponsor_name IS NULL OR sponsor_name = '' THEN 1 END) as sponsor_name_null_count,
      COUNT(CASE WHEN status IS NULL OR status = '' THEN 1 END) as status_null_count,
      COUNT(CASE WHEN deal_stage IS NULL OR deal_stage = '' THEN 1 END) as deal_stage_null_count,
      COUNT(CASE WHEN priority IS NULL OR priority = '' THEN 1 END) as priority_null_count,
      COUNT(CASE WHEN zip_code IS NULL OR zip_code = '' THEN 1 END) as zip_code_null_count,
      COUNT(CASE WHEN property_description IS NULL OR property_description = '' THEN 1 END) as property_description_null_count,
      COUNT(CASE WHEN lot_area IS NULL THEN 1 END) as lot_area_null_count,
      COUNT(CASE WHEN floor_area_ratio IS NULL THEN 1 END) as floor_area_ratio_null_count,
      COUNT(CASE WHEN zoning_square_footage IS NULL THEN 1 END) as zoning_square_footage_null_count,
      COUNT(CASE WHEN yield_on_cost IS NULL THEN 1 END) as yield_on_cost_null_count,
      COUNT(CASE WHEN projected_gp_equity_multiple IS NULL THEN 1 END) as projected_gp_equity_multiple_null_count,
      COUNT(CASE WHEN projected_gp_irr IS NULL THEN 1 END) as projected_gp_irr_null_count,
      COUNT(CASE WHEN projected_lp_equity_multiple IS NULL THEN 1 END) as projected_lp_equity_multiple_null_count,
      COUNT(CASE WHEN projected_lp_irr IS NULL THEN 1 END) as projected_lp_irr_null_count,
      COUNT(CASE WHEN projected_total_equity_multiple IS NULL THEN 1 END) as projected_total_equity_multiple_null_count,
      COUNT(CASE WHEN projected_total_irr IS NULL THEN 1 END) as projected_total_irr_null_count
    FROM deals
    WHERE updated_at >= $1 AND updated_at <= $2
  `, [startDate, endDate])

  const totalRecords = parseInt(comprehensiveMetricsResult.rows[0]?.total_records?.toString()) || 0
  const metrics = comprehensiveMetricsResult.rows[0]
  const allFields = [
    'deal_name', 'sponsor_name', 'status', 'deal_stage', 'priority',
    'zip_code', 'property_description', 'lot_area', 'floor_area_ratio', 'zoning_square_footage',
    'yield_on_cost', 'projected_gp_equity_multiple', 'projected_gp_irr',
    'projected_lp_equity_multiple', 'projected_lp_irr', 'projected_total_equity_multiple', 'projected_total_irr'
  ]
  
  const nullabilityMetrics: Record<string, any> = {}
  for (const field of allFields) {
    const nullCount = parseInt(metrics[`${field}_null_count`]?.toString()) || 0
    const nullPercentage = totalRecords > 0 ? (nullCount / totalRecords) * 100 : 0
    nullabilityMetrics[field] = {
      nullCount,
      nullPercentage: Math.round(nullPercentage * 100) / 100,
      totalRecords
    }
  }
  
  // Throughput metrics
  const hourlyThroughputResult = await pool.query(`
    SELECT 
      DATE_TRUNC('hour', updated_at) as hour,
      COUNT(*) as count
    FROM deals
    WHERE updated_at >= $1 AND updated_at <= $2
    GROUP BY DATE_TRUNC('hour', updated_at)
    ORDER BY hour DESC
    LIMIT 48
  `, [startDate, endDate])
  const dailyThroughputResult = await pool.query(`
    SELECT 
      DATE_TRUNC('day', updated_at) as date,
      COUNT(*) as count
    FROM deals
    WHERE updated_at >= $1 AND updated_at <= $2
    GROUP BY DATE_TRUNC('day', updated_at)
    ORDER BY date DESC
    LIMIT 30
  `, [startDate, endDate])
  const recentActivityResult = await pool.query(`
    SELECT
      COUNT(CASE WHEN updated_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as last_24_hours,
      COUNT(CASE WHEN updated_at >= NOW() - INTERVAL '7 days' THEN 1 END) as last_7_days,
      COUNT(CASE WHEN updated_at >= NOW() - INTERVAL '30 days' THEN 1 END) as last_30_days
    FROM deals
  `)
  // Add job-based recent activity
  const jobRecentActivity = await getJobRecentActivityCounts();
  return {
    tableName: 'deals',
    totalRecords,
    nullabilityMetrics,
    throughputMetrics: {
      hourly: hourlyThroughputResult.rows.map(row => ({
        hour: row.hour as string,
        count: parseInt(row.count?.toString()) || 0
      })),
      daily: dailyThroughputResult.rows.map(row => ({
        date: row.date as string,
        count: parseInt(row.count?.toString()) || 0
      }))
    },
    recentActivity: {
      last24Hours: parseInt(recentActivityResult.rows[0]?.last_24_hours?.toString()) || 0,
      last7Days: parseInt(recentActivityResult.rows[0]?.last_7_days?.toString()) || 0,
      last30Days: parseInt(recentActivityResult.rows[0]?.last_30_days?.toString()) || 0
    },
    jobRecentActivity
  }
}

async function getDebtMetrics(startDate: string, endDate: string): Promise<DataQualityMetrics> {
  // Debt-specific fields for investment criteria
  const comprehensiveMetricsResult = await pool.query(`
    SELECT 
      COUNT(*) as total_records,
      COUNT(CASE WHEN target_return IS NULL THEN 1 END) as target_return_null_count,
      COUNT(CASE WHEN minimum_deal_size IS NULL THEN 1 END) as minimum_deal_size_null_count,
      COUNT(CASE WHEN maximum_deal_size IS NULL THEN 1 END) as maximum_deal_size_null_count,
      COUNT(CASE WHEN interest_rate IS NULL THEN 1 END) as interest_rate_null_count,
      COUNT(CASE WHEN loan_to_value_max IS NULL THEN 1 END) as loan_to_value_max_null_count,
      COUNT(CASE WHEN loan_to_cost_max IS NULL THEN 1 END) as loan_to_cost_max_null_count,
      COUNT(CASE WHEN min_loan_term IS NULL THEN 1 END) as min_loan_term_null_count,
      COUNT(CASE WHEN max_loan_term IS NULL THEN 1 END) as max_loan_term_null_count,
      COUNT(CASE WHEN closing_time_weeks IS NULL THEN 1 END) as closing_time_weeks_null_count,
      COUNT(CASE WHEN (loan_program IS NULL OR array_length(loan_program, 1) IS NULL) THEN 1 END) as loan_program_null_count,
      COUNT(CASE WHEN (loan_type IS NULL OR array_length(loan_type, 1) IS NULL) THEN 1 END) as loan_type_null_count
    FROM investment_criteria
    WHERE updated_at >= $1 AND updated_at <= $2 
      AND entity_type = 'Deal'
      AND capital_position && ARRAY['Senior Debt', 'Mezzanine Debt', 'Stretch Senior Debt']
  `, [startDate, endDate])

  const totalRecords = parseInt(comprehensiveMetricsResult.rows[0]?.total_records?.toString()) || 0
  const metrics = comprehensiveMetricsResult.rows[0]
  const allFields = [
    'target_return', 'minimum_deal_size', 'maximum_deal_size', 'interest_rate', 
    'loan_to_value_max', 'loan_to_cost_max', 'min_loan_term', 'max_loan_term', 
    'closing_time_weeks', 'loan_program', 'loan_type'
  ]

  const nullabilityMetrics: Record<string, any> = {}
  for (const field of allFields) {
    const nullCount = parseInt(metrics[`${field}_null_count`]?.toString()) || 0
    const nullPercentage = totalRecords > 0 ? (nullCount / totalRecords) * 100 : 0
    nullabilityMetrics[field] = {
      nullCount,
      nullPercentage: Math.round(nullPercentage * 100) / 100,
      totalRecords
    }
  }
  
  // Throughput metrics
  const hourlyThroughputResult = await pool.query(`
    SELECT 
      DATE_TRUNC('hour', updated_at) as hour,
      COUNT(*) as count
    FROM investment_criteria
    WHERE updated_at >= $1 AND updated_at <= $2 
      AND entity_type = 'Deal'
      AND capital_position && ARRAY['Senior Debt', 'Mezzanine Debt', 'Stretch Senior Debt']
    GROUP BY DATE_TRUNC('hour', updated_at)
    ORDER BY hour DESC
    LIMIT 48
  `, [startDate, endDate])
  const dailyThroughputResult = await pool.query(`
    SELECT 
      DATE_TRUNC('day', updated_at) as date,
      COUNT(*) as count
    FROM investment_criteria
    WHERE updated_at >= $1 AND updated_at <= $2 
      AND entity_type = 'Deal'
      AND capital_position && ARRAY['Senior Debt', 'Mezzanine Debt', 'Stretch Senior Debt']
    GROUP BY DATE_TRUNC('day', updated_at)
    ORDER BY date DESC
    LIMIT 30
  `, [startDate, endDate])
  const recentActivityResult = await pool.query(`
    SELECT
      COUNT(CASE WHEN updated_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as last_24_hours,
      COUNT(CASE WHEN updated_at >= NOW() - INTERVAL '7 days' THEN 1 END) as last_7_days,
      COUNT(CASE WHEN updated_at >= NOW() - INTERVAL '30 days' THEN 1 END) as last_30_days
    FROM investment_criteria
    WHERE entity_type = 'Deal'
      AND capital_position && ARRAY['Senior Debt', 'Mezzanine Debt', 'Stretch Senior Debt']
  `)
  return {
    tableName: 'debt_criteria',
    totalRecords,
    nullabilityMetrics,
    throughputMetrics: {
      hourly: hourlyThroughputResult.rows.map(row => ({
        hour: row.hour as string,
        count: parseInt(row.count?.toString()) || 0
      })),
      daily: dailyThroughputResult.rows.map(row => ({
        date: row.date as string,
        count: parseInt(row.count?.toString()) || 0
      }))
    },
    recentActivity: {
      last24Hours: parseInt(recentActivityResult.rows[0]?.last_24_hours?.toString()) || 0,
      last7Days: parseInt(recentActivityResult.rows[0]?.last_7_days?.toString()) || 0,
      last30Days: parseInt(recentActivityResult.rows[0]?.last_30_days?.toString()) || 0
    }
  }
}

async function getEquityMetrics(startDate: string, endDate: string): Promise<DataQualityMetrics> {
  // Equity-specific fields for investment criteria
  const comprehensiveMetricsResult = await pool.query(`
    SELECT 
      COUNT(*) as total_records,
      COUNT(CASE WHEN target_return IS NULL THEN 1 END) as target_return_null_count,
      COUNT(CASE WHEN minimum_deal_size IS NULL THEN 1 END) as minimum_deal_size_null_count,
      COUNT(CASE WHEN maximum_deal_size IS NULL THEN 1 END) as maximum_deal_size_null_count,
      COUNT(CASE WHEN historical_irr IS NULL THEN 1 END) as historical_irr_null_count,
      COUNT(CASE WHEN historical_em IS NULL THEN 1 END) as historical_em_null_count,
      COUNT(CASE WHEN min_hold_period IS NULL THEN 1 END) as min_hold_period_null_count,
      COUNT(CASE WHEN max_hold_period IS NULL THEN 1 END) as max_hold_period_null_count
    FROM investment_criteria
    WHERE updated_at >= $1 AND updated_at <= $2 
      AND entity_type = 'Deal'
      AND capital_position && ARRAY['Common Equity', 'Co-GP Equity', 'GP Equity', 'JV Equity', 'LP Equity', 'Preferred Equity']
  `, [startDate, endDate])

  const totalRecords = parseInt(comprehensiveMetricsResult.rows[0]?.total_records?.toString()) || 0
  const metrics = comprehensiveMetricsResult.rows[0]
  const allFields = [
    'target_return', 'minimum_deal_size', 'maximum_deal_size', 'historical_irr', 
    'historical_em', 'min_hold_period', 'max_hold_period'
  ]

  const nullabilityMetrics: Record<string, any> = {}
  for (const field of allFields) {
    const nullCount = parseInt(metrics[`${field}_null_count`]?.toString()) || 0
    const nullPercentage = totalRecords > 0 ? (nullCount / totalRecords) * 100 : 0
    nullabilityMetrics[field] = {
      nullCount,
      nullPercentage: Math.round(nullPercentage * 100) / 100,
      totalRecords
    }
  }
  
  // Throughput metrics
  const hourlyThroughputResult = await pool.query(`
    SELECT 
      DATE_TRUNC('hour', updated_at) as hour,
      COUNT(*) as count
    FROM investment_criteria
    WHERE updated_at >= $1 AND updated_at <= $2 
      AND entity_type = 'Deal'
      AND capital_position && ARRAY['Common Equity', 'Co-GP Equity', 'GP Equity', 'JV Equity', 'LP Equity', 'Preferred Equity']
    GROUP BY DATE_TRUNC('hour', updated_at)
    ORDER BY hour DESC
    LIMIT 48
  `, [startDate, endDate])
  const dailyThroughputResult = await pool.query(`
    SELECT 
      DATE_TRUNC('day', updated_at) as date,
      COUNT(*) as count
    FROM investment_criteria
    WHERE updated_at >= $1 AND updated_at <= $2 
      AND entity_type = 'Deal'
      AND capital_position && ARRAY['Common Equity', 'Co-GP Equity', 'GP Equity', 'JV Equity', 'LP Equity', 'Preferred Equity']
    GROUP BY DATE_TRUNC('day', updated_at)
    ORDER BY date DESC
    LIMIT 30
  `, [startDate, endDate])
  const recentActivityResult = await pool.query(`
    SELECT
      COUNT(CASE WHEN updated_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as last_24_hours,
      COUNT(CASE WHEN updated_at >= NOW() - INTERVAL '7 days' THEN 1 END) as last_7_days,
      COUNT(CASE WHEN updated_at >= NOW() - INTERVAL '30 days' THEN 1 END) as last_30_days
    FROM investment_criteria
    WHERE entity_type = 'Deal'
      AND capital_position && ARRAY['Common Equity', 'Co-GP Equity', 'GP Equity', 'JV Equity', 'LP Equity', 'Preferred Equity']
  `)
  return {
    tableName: 'equity_criteria',
    totalRecords,
    nullabilityMetrics,
    throughputMetrics: {
      hourly: hourlyThroughputResult.rows.map(row => ({
        hour: row.hour as string,
        count: parseInt(row.count?.toString()) || 0
      })),
      daily: dailyThroughputResult.rows.map(row => ({
        date: row.date as string,
        count: parseInt(row.count?.toString()) || 0
      }))
    },
    recentActivity: {
      last24Hours: parseInt(recentActivityResult.rows[0]?.last_24_hours?.toString()) || 0,
      last7Days: parseInt(recentActivityResult.rows[0]?.last_7_days?.toString()) || 0,
      last30Days: parseInt(recentActivityResult.rows[0]?.last_30_days?.toString()) || 0
    }
  }
} 