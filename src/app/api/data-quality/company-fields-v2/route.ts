import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    // Get comprehensive field metrics for all AI-generated columns
    const sql = `
      WITH field_stats AS (
        SELECT 
          'company_name' as field_name,
          'text' as data_type,
          COUNT(*) as total_records,
          COUNT(company_name) as populated_records,
          COUNT(*) - COUNT(company_name) as null_records,
          ROUND((COUNT(company_name)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(company_name))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'company_type' as field_name,
          'varchar' as data_type,
          COUNT(*) as total_records,
          COUNT(company_type) as populated_records,
          COUNT(*) - COUNT(company_type) as null_records,
          ROUND((COUNT(company_type)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(company_type))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'industry' as field_name,
          'text' as data_type,
          COUNT(*) as total_records,
          COUNT(industry) as populated_records,
          COUNT(*) - COUNT(industry) as null_records,
          ROUND((COUNT(industry)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(industry))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'business_model' as field_name,
          'text' as data_type,
          COUNT(*) as total_records,
          COUNT(business_model) as populated_records,
          COUNT(*) - COUNT(business_model) as null_records,
          ROUND((COUNT(business_model)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(business_model))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'summary' as field_name,
          'text' as data_type,
          COUNT(*) as total_records,
          COUNT(summary) as populated_records,
          COUNT(*) - COUNT(summary) as null_records,
          ROUND((COUNT(summary)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(summary))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'founded_year' as field_name,
          'integer' as data_type,
          COUNT(*) as total_records,
          COUNT(founded_year) as populated_records,
          COUNT(*) - COUNT(founded_year) as null_records,
          ROUND((COUNT(founded_year)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(founded_year))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'capital_position' as field_name,
          'array' as data_type,
          COUNT(*) as total_records,
          COUNT(capital_position) as populated_records,
          COUNT(*) - COUNT(capital_position) as null_records,
          ROUND((COUNT(capital_position)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(capital_position))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'investment_focus' as field_name,
          'array' as data_type,
          COUNT(*) as total_records,
          COUNT(investment_focus) as populated_records,
          COUNT(*) - COUNT(investment_focus) as null_records,
          ROUND((COUNT(investment_focus)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(investment_focus))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'investment_strategy_mission' as field_name,
          'text' as data_type,
          COUNT(*) as total_records,
          COUNT(investment_strategy_mission) as populated_records,
          COUNT(*) - COUNT(investment_strategy_mission) as null_records,
          ROUND((COUNT(investment_strategy_mission)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(investment_strategy_mission))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'investment_strategy_approach' as field_name,
          'text' as data_type,
          COUNT(*) as total_records,
          COUNT(investment_strategy_approach) as populated_records,
          COUNT(*) - COUNT(investment_strategy_approach) as null_records,
          ROUND((COUNT(investment_strategy_approach)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(investment_strategy_approach))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'secondary_phone' as field_name,
          'varchar' as data_type,
          COUNT(*) as total_records,
          COUNT(secondary_phone) as populated_records,
          COUNT(*) - COUNT(secondary_phone) as null_records,
          ROUND((COUNT(secondary_phone)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(secondary_phone))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'main_email' as field_name,
          'varchar' as data_type,
          COUNT(*) as total_records,
          COUNT(main_email) as populated_records,
          COUNT(*) - COUNT(main_email) as null_records,
          ROUND((COUNT(main_email)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(main_email))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'secondary_email' as field_name,
          'varchar' as data_type,
          COUNT(*) as total_records,
          COUNT(secondary_email) as populated_records,
          COUNT(*) - COUNT(secondary_email) as null_records,
          ROUND((COUNT(secondary_email)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(secondary_email))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'twitter' as field_name,
          'varchar' as data_type,
          COUNT(*) as total_records,
          COUNT(twitter) as populated_records,
          COUNT(*) - COUNT(twitter) as null_records,
          ROUND((COUNT(twitter)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(twitter))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'facebook' as field_name,
          'varchar' as data_type,
          COUNT(*) as total_records,
          COUNT(facebook) as populated_records,
          COUNT(*) - COUNT(facebook) as null_records,
          ROUND((COUNT(facebook)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(facebook))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'instagram' as field_name,
          'varchar' as data_type,
          COUNT(*) as total_records,
          COUNT(instagram) as populated_records,
          COUNT(*) - COUNT(instagram) as null_records,
          ROUND((COUNT(instagram)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(instagram))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'youtube' as field_name,
          'varchar' as data_type,
          COUNT(*) as total_records,
          COUNT(youtube) as populated_records,
          COUNT(*) - COUNT(youtube) as null_records,
          ROUND((COUNT(youtube)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(youtube))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'office_locations' as field_name,
          'array' as data_type,
          COUNT(*) as total_records,
          COUNT(office_locations) as populated_records,
          COUNT(*) - COUNT(office_locations) as null_records,
          ROUND((COUNT(office_locations)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(office_locations))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'fund_size' as field_name,
          'integer' as data_type,
          COUNT(*) as total_records,
          COUNT(fund_size) as populated_records,
          COUNT(*) - COUNT(fund_size) as null_records,
          ROUND((COUNT(fund_size)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(fund_size))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'aum' as field_name,
          'integer' as data_type,
          COUNT(*) as total_records,
          COUNT(aum) as populated_records,
          COUNT(*) - COUNT(aum) as null_records,
          ROUND((COUNT(aum)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(aum))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'number_of_properties' as field_name,
          'integer' as data_type,
          COUNT(*) as total_records,
          COUNT(number_of_properties) as populated_records,
          COUNT(*) - COUNT(number_of_properties) as null_records,
          ROUND((COUNT(number_of_properties)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(number_of_properties))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'number_of_offices' as field_name,
          'integer' as data_type,
          COUNT(*) as total_records,
          COUNT(number_of_offices) as populated_records,
          COUNT(*) - COUNT(number_of_offices) as null_records,
          ROUND((COUNT(number_of_offices)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(number_of_offices))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'number_of_employees' as field_name,
          'integer' as data_type,
          COUNT(*) as total_records,
          COUNT(number_of_employees) as populated_records,
          COUNT(*) - COUNT(number_of_employees) as null_records,
          ROUND((COUNT(number_of_employees)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(number_of_employees))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'annual_revenue' as field_name,
          'integer' as data_type,
          COUNT(*) as total_records,
          COUNT(annual_revenue) as populated_records,
          COUNT(*) - COUNT(annual_revenue) as null_records,
          ROUND((COUNT(annual_revenue)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(annual_revenue))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'partnerships' as field_name,
          'array' as data_type,
          COUNT(*) as total_records,
          COUNT(partnerships) as populated_records,
          COUNT(*) - COUNT(partnerships) as null_records,
          ROUND((COUNT(partnerships)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(partnerships))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'key_executives' as field_name,
          'array' as data_type,
          COUNT(*) as total_records,
          COUNT(key_executives) as populated_records,
          COUNT(*) - COUNT(key_executives) as null_records,
          ROUND((COUNT(key_executives)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(key_executives))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'sustainability_esg_focus' as field_name,
          'boolean' as data_type,
          COUNT(*) as total_records,
          COUNT(sustainability_esg_focus) as populated_records,
          COUNT(*) - COUNT(sustainability_esg_focus) as null_records,
          ROUND((COUNT(sustainability_esg_focus)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(sustainability_esg_focus))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'technology_proptech_adoption' as field_name,
          'boolean' as data_type,
          COUNT(*) as total_records,
          COUNT(technology_proptech_adoption) as populated_records,
          COUNT(*) - COUNT(technology_proptech_adoption) as null_records,
          ROUND((COUNT(technology_proptech_adoption)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(technology_proptech_adoption))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'major_competitors' as field_name,
          'array' as data_type,
          COUNT(*) as total_records,
          COUNT(major_competitors) as populated_records,
          COUNT(*) - COUNT(major_competitors) as null_records,
          ROUND((COUNT(major_competitors)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(major_competitors))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'data_confidence_score' as field_name,
          'double_precision' as data_type,
          COUNT(*) as total_records,
          COUNT(data_confidence_score) as populated_records,
          COUNT(*) - COUNT(data_confidence_score) as null_records,
          ROUND((COUNT(data_confidence_score)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(data_confidence_score))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
        
        UNION ALL
        
        SELECT 
          'llm_used' as field_name,
          'text' as data_type,
          COUNT(*) as total_records,
          COUNT(llm_used) as populated_records,
          COUNT(*) - COUNT(llm_used) as null_records,
          ROUND((COUNT(llm_used)::numeric / COUNT(*)) * 100, 1) as populated_percentage,
          ROUND(((COUNT(*) - COUNT(llm_used))::numeric / COUNT(*)) * 100, 1) as null_percentage
        FROM companies
      )
      SELECT 
        field_name,
        data_type,
        total_records,
        populated_records,
        null_records,
        populated_percentage,
        null_percentage
      FROM field_stats
      ORDER BY populated_percentage DESC, field_name ASC
    `

    const result = await pool.query(sql)

    return NextResponse.json({
      success: true,
      data: result.rows,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error fetching company field metrics V2:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch field metrics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
