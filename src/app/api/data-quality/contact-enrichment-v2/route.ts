import { NextResponse } from 'next/server'
import { pool } from '../../../../lib/db'

export async function GET() {
  try {
    // Get overall V2 processing status counts
    const statusQuery = `
      SELECT 
        COUNT(*) as total_rows,
        COUNT(CASE WHEN contact_enrichment_v2_status = 'completed' THEN 1 END) as completed_v2_rows,
        COUNT(CASE WHEN COALESCE(contact_enrichment_v2_status, 'pending') = 'pending' THEN 1 END) as pending_v2_rows,
        COUNT(CASE WHEN contact_enrichment_v2_status = 'failed' THEN 1 END) as failed_v2_rows
      FROM contacts
      WHERE email_verification_status = 'completed'
    `
    
    const statusResult = await pool.query(statusQuery)
    const statusData = statusResult.rows[0]

    // Define V2 column groups based on the schema
    const columnGroups = [
      {
        name: 'Contact Information',
        description: 'Additional contact details and communication channels',
        columns: ['additional_email', 'phone_number_secondary']
      },
      {
        name: 'Social Media',
        description: 'Social media profiles and online presence',
        columns: ['twitter', 'facebook', 'instagram', 'youtube']
      },
      {
        name: 'Education',
        description: 'Educational background and academic history',
        columns: ['education_college', 'education_college_year_graduated']
      },
      {
        name: 'Personal Details',
        description: 'Personal information for relationship building',
        columns: ['honorable_achievements', 'hobbies', 'age']
      },
      {
        name: 'Address Information',
        description: 'Physical location and address details',
        columns: ['contact_address', 'contact_zip_code']
      },
      {
        name: 'Professional Context',
        description: 'Role classification and business relationship context',
        columns: ['contact_type', 'relationship_owner', 'role_in_decision_making']
      },
      {
        name: 'Interaction History',
        description: 'Contact engagement and communication history',
        columns: ['source_of_introduction']
      },
      {
        name: 'Compliance Information',
        description: 'Regulatory and compliance status tracking',
        columns: ['accredited_investor_status']
      },
      {
        name: 'Enhanced Profile Data',
        description: 'Enriched executive summary and career information',
        columns: ['executive_summary']
      }
    ]

    // Get column completeness data for contacts with completed V2 enrichment
    const columnGroupsWithData = await Promise.all(
      columnGroups.map(async (group) => {
        const columnsWithData = await Promise.all(
          group.columns.map(async (columnName) => {
            let query = ''
            let countCondition = ''
            
            // Handle different data types appropriately
            if (['honorable_achievements', 'hobbies'].includes(columnName)) {
              // JSONB array columns
              countCondition = `${columnName} IS NOT NULL AND jsonb_array_length(${columnName}) > 0`
            } else if (columnName === 'accredited_investor_status') {
              // Boolean column
              countCondition = `${columnName} IS NOT NULL`
            } else {
              // Text columns
              countCondition = `${columnName} IS NOT NULL AND TRIM(${columnName}) != ''`
            }
            
            query = `
              SELECT 
                COUNT(CASE WHEN ${countCondition} THEN 1 END) as populated_count,
                COUNT(*) as total_count
              FROM contacts 
              WHERE contact_enrichment_v2_status = 'completed'
            `

            const result = await pool.query(query)
            const { populated_count, total_count } = result.rows[0]
            
            return {
              column_name: columnName,
              populated_count: parseInt(populated_count) || 0,
              populated_percentage: total_count > 0 ? Math.round((populated_count / total_count) * 100) : 0
            }
          })
        )
        
        return {
          name: group.name,
          description: group.description,
          columns: columnsWithData
        }
      })
    )

    // Calculate overall percentage based on all columns across all groups
    const allColumnData = columnGroupsWithData.flatMap(group => group.columns)
    const totalFields = allColumnData.length
    const totalPopulatedFields = allColumnData.reduce((sum, col) => sum + col.populated_percentage, 0)
    const overallPercentage = totalFields > 0 ? Math.round(totalPopulatedFields / totalFields) : 0

    // Add hourly processing stats and mapping validation
    const [hourlyResult, mappingResult] = await Promise.all([
      // Hourly processing stats (last 24 hours)
      pool.query(`
        SELECT 
          DATE_TRUNC('hour', contact_enrichment_v2_date) as hour,
          COUNT(*) as processed_count,
          COUNT(CASE WHEN contact_enrichment_v2_status = 'completed' THEN 1 END) as completed_count,
          COUNT(CASE WHEN contact_enrichment_v2_status = 'failed' THEN 1 END) as failed_count
        FROM contacts 
        WHERE contact_enrichment_v2_date >= NOW() - INTERVAL '24 hours'
          AND contact_enrichment_v2_date IS NOT NULL
        GROUP BY DATE_TRUNC('hour', contact_enrichment_v2_date)
        ORDER BY hour DESC
        LIMIT 24
      `),
      
      // Mapping validation query
      pool.query(`
        WITH completed_contacts AS (
          SELECT *
          FROM contacts 
          WHERE contact_enrichment_v2_status = 'completed'
        )
        SELECT 
          COUNT(*) as total_records,
          
          -- Contact Type Validation
          COUNT(CASE WHEN contact_type IS NOT NULL 
                     AND EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Contact Type' AND (value_1 = contact_type OR value_2 = contact_type) AND is_active = true) 
                THEN 1 END) as contact_type_valid,
          COUNT(CASE WHEN contact_type IS NOT NULL 
                     AND NOT EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Contact Type' AND (value_1 = contact_type OR value_2 = contact_type) AND is_active = true) 
                THEN 1 END) as contact_type_invalid,
          
          -- Role in Decision Making Validation
          COUNT(CASE WHEN role_in_decision_making IS NOT NULL 
                     AND EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Decision Making Role' AND (value_1 = role_in_decision_making OR value_2 = role_in_decision_making) AND is_active = true) 
                THEN 1 END) as role_in_decision_making_valid,
          COUNT(CASE WHEN role_in_decision_making IS NOT NULL 
                     AND NOT EXISTS (SELECT 1 FROM central_mapping WHERE type = 'Decision Making Role' AND (value_1 = role_in_decision_making OR value_2 = role_in_decision_making) AND is_active = true) 
                THEN 1 END) as role_in_decision_making_invalid
                              
        FROM completed_contacts
      `)
    ])

    // Process hourly stats
    const hourlyProcessingStats = hourlyResult.rows.map(row => ({
      hour: row.hour,
      processed_count: parseInt(row.processed_count || '0'),
      completed_count: parseInt(row.completed_count || '0'),
      failed_count: parseInt(row.failed_count || '0')
    }))

    // Process mapping validation
    const mappingData = mappingResult.rows[0]
    const mappingValidation = {
      total_records: parseInt(mappingData.total_records || '0'),
      contact_type_validation: {
        valid_count: parseInt(mappingData.contact_type_valid || '0'),
        invalid_count: parseInt(mappingData.contact_type_invalid || '0'),
        invalid_values: [] // Could be populated with a separate query if needed
      },
      role_in_decision_making_validation: {
        valid_count: parseInt(mappingData.role_in_decision_making_valid || '0'),
        invalid_count: parseInt(mappingData.role_in_decision_making_invalid || '0'),
        invalid_values: [] // Could be populated with a separate query if needed
      },
      overall_mapping_validation_percentage: 0
    }

    // Calculate overall mapping validation percentage
    const totalMappingChecks = mappingValidation.contact_type_validation.valid_count + 
                              mappingValidation.contact_type_validation.invalid_count +
                              mappingValidation.role_in_decision_making_validation.valid_count + 
                              mappingValidation.role_in_decision_making_validation.invalid_count
    
    const totalValidMappings = mappingValidation.contact_type_validation.valid_count + 
                              mappingValidation.role_in_decision_making_validation.valid_count

    mappingValidation.overall_mapping_validation_percentage = totalMappingChecks > 0 
      ? Math.round((totalValidMappings / totalMappingChecks) * 100) 
      : 0

    const response = {
      totalRows: parseInt(statusData.total_rows) || 0,
      completedV2Rows: parseInt(statusData.completed_v2_rows) || 0,
      pendingV2Rows: parseInt(statusData.pending_v2_rows) || 0,
      failedV2Rows: parseInt(statusData.failed_v2_rows) || 0,
      overallPercentage,
      columnGroups: columnGroupsWithData,
      hourlyProcessingStats,
      mappingValidation
    }

    return NextResponse.json({
      success: true,
      data: response
    })

  } catch (error) {
    console.error('Error fetching contact enrichment V2 data quality metrics:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch data quality metrics' 
      },
      { status: 500 }
    )
  }
}
