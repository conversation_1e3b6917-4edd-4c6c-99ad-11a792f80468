import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Investment criteria filters
    const searchTerm = searchParams.get('searchTerm')
    const entityType = searchParams.get('entityType')
    const entityId = searchParams.get('entityId')
    const entityName = searchParams.get('entityName')
    const criteriaId = searchParams.get('criteriaId')
    
    // Capital & Financing
    const capitalPosition = searchParams.get('capitalPosition')?.split(',').filter(Boolean)
    const loanTypes = searchParams.get('loanTypes')?.split(',').filter(Boolean)
    const loanProgram = searchParams.get('loanProgram')?.split(',').filter(Boolean)
    const structuredLoanTranche = searchParams.get('structuredLoanTranche')?.split(',').filter(Bo<PERSON>an)
    const recourseLoan = searchParams.get('recourseLoan')?.split(',').filter(Boolean)
    
    // Deal size
    const dealSizeMin = searchParams.get('dealSizeMin') ? parseFloat(searchParams.get('dealSizeMin')!) : null
    const dealSizeMax = searchParams.get('dealSizeMax') ? parseFloat(searchParams.get('dealSizeMax')!) : null
    
    // Returns
    const targetReturnMin = searchParams.get('targetReturnMin') ? parseFloat(searchParams.get('targetReturnMin')!) : null
    const targetReturnMax = searchParams.get('targetReturnMax') ? parseFloat(searchParams.get('targetReturnMax')!) : null
    const historicalIrrMin = searchParams.get('historicalIrrMin') ? parseFloat(searchParams.get('historicalIrrMin')!) : null
    const historicalIrrMax = searchParams.get('historicalIrrMax') ? parseFloat(searchParams.get('historicalIrrMax')!) : null
    const historicalEmMin = searchParams.get('historicalEmMin') ? parseFloat(searchParams.get('historicalEmMin')!) : null
    const historicalEmMax = searchParams.get('historicalEmMax') ? parseFloat(searchParams.get('historicalEmMax')!) : null
    
    // Hold periods
    const minHoldPeriod = searchParams.get('minHoldPeriod') ? parseInt(searchParams.get('minHoldPeriod')!) : null
    const maxHoldPeriod = searchParams.get('maxHoldPeriod') ? parseInt(searchParams.get('maxHoldPeriod')!) : null
    
    // Loan terms
    const minLoanTerm = searchParams.get('minLoanTerm') ? parseInt(searchParams.get('minLoanTerm')!) : null
    const maxLoanTerm = searchParams.get('maxLoanTerm') ? parseInt(searchParams.get('maxLoanTerm')!) : null
    const interestRateMin = searchParams.get('interestRateMin') ? parseFloat(searchParams.get('interestRateMin')!) : null
    const interestRateMax = searchParams.get('interestRateMax') ? parseFloat(searchParams.get('interestRateMax')!) : null
    
    // LTV/LTC
    const loanToValueMin = searchParams.get('loanToValueMin') ? parseFloat(searchParams.get('loanToValueMin')!) : null
    const loanToValueMax = searchParams.get('loanToValueMax') ? parseFloat(searchParams.get('loanToValueMax')!) : null
    const loanToCostMin = searchParams.get('loanToCostMin') ? parseFloat(searchParams.get('loanToCostMin')!) : null
    const loanToCostMax = searchParams.get('loanToCostMax') ? parseFloat(searchParams.get('loanToCostMax')!) : null
    
    // DSCR
    const minLoanDscr = searchParams.get('minLoanDscr') ? parseFloat(searchParams.get('minLoanDscr')!) : null
    const maxLoanDscr = searchParams.get('maxLoanDscr') ? parseFloat(searchParams.get('maxLoanDscr')!) : null
    
    // Property & Geographic
    const propertyTypes = searchParams.get('propertyTypes')?.split(',').filter(Boolean)
    const propertySubcategories = searchParams.get('propertySubcategories')?.split(',').filter(Boolean)
    const strategies = searchParams.get('strategies')?.split(',').filter(Boolean)
    const financialProducts = searchParams.get('financialProducts')?.split(',').filter(Boolean)
    const regions = searchParams.get('regions')?.split(',').filter(Boolean)
    const states = searchParams.get('states')?.split(',').filter(Boolean)
    const cities = searchParams.get('cities')?.split(',').filter(Boolean)
    const countries = searchParams.get('countries')?.split(',').filter(Boolean)
    
    // Contact-specific filters
    const source = searchParams.get('source')?.split(',').filter(Boolean)
    const emailStatus = searchParams.get('emailStatus')?.split(',').filter(Boolean)
    const contactCompanyType = searchParams.get('contactCompanyType')?.split(',').filter(Boolean)
    const contactCapitalPosition = searchParams.get('contactCapitalPosition')?.split(',').filter(Boolean)
    const jobTier = searchParams.get('jobTier')?.split(',').filter(Boolean)
    
    // Contact location filters
    const contactCountries = searchParams.get('contactCountries')?.split(',').filter(Boolean)
    const contactStates = searchParams.get('contactStates')?.split(',').filter(Boolean)
    const contactCities = searchParams.get('contactCities')?.split(',').filter(Boolean)
    
    // Processing status filters
    const emailVerificationStatus = searchParams.get('emailVerificationStatus')?.split(',').filter(Boolean)
    const contactEnrichmentStatus = searchParams.get('contactEnrichmentStatus')?.split(',').filter(Boolean)
    const contactEnrichmentV2Status = searchParams.get('contactEnrichmentV2Status')?.split(',').filter(Boolean)
    const emailGenerationStatus = searchParams.get('emailGenerationStatus')?.split(',').filter(Boolean)
    const emailSendingStatus = searchParams.get('emailSendingStatus')?.split(',').filter(Boolean)
    
    // Company processing status filters (from company table)
    const companyWebsiteScrapingStatus = searchParams.get('companyWebsiteScrapingStatus')?.split(',').filter(Boolean)
    const companyOverviewStatus = searchParams.get('companyOverviewStatus')?.split(',').filter(Boolean)
    const companyOverviewV2Status = searchParams.get('companyOverviewV2Status')?.split(',').filter(Boolean)
    const companyInvestmentCriteriaStatus = searchParams.get('companyInvestmentCriteriaStatus')?.split(',').filter(Boolean)
    
    // Contact Enrichment filters (NEW)
    const enrichmentCompanyType = searchParams.get('enrichmentCompanyType')?.split(',').filter(Boolean)
    const enrichmentCapitalPositions = searchParams.get('enrichmentCapitalPositions')?.split(',').filter(Boolean)
    const enrichmentStatus = searchParams.get('enrichmentStatus')?.split(',').filter(Boolean)
    
    // Boolean flags
    const extracted = searchParams.get('extracted') === 'true' ? true : searchParams.get('extracted') === 'false' ? false : null
    const searched = searchParams.get('searched') === 'true' ? true : searchParams.get('searched') === 'false' ? false : null
    const emailGenerated = searchParams.get('emailGenerated') === 'true' ? true : searchParams.get('emailGenerated') === 'false' ? false : null
    const enriched = searchParams.get('enriched') === 'true' ? true : searchParams.get('enriched') === 'false' ? false : null
    const hasSmartleadId = searchParams.get('hasSmartleadId') === 'true' ? true : searchParams.get('hasSmartleadId') === 'false' ? false : null
    
    // Gmail outreach filters (IMPROVED)
    const hasBeenReachedOut = searchParams.get('hasBeenReachedOut') === 'true' ? true : searchParams.get('hasBeenReachedOut') === 'false' ? false : null
    
    // NOT filters (NEW)
    const notFilters = {
      notCapitalPosition: searchParams.get('notCapitalPosition')?.split(',').filter(Boolean),
      notPropertyTypes: searchParams.get('notPropertyTypes')?.split(',').filter(Boolean),
      notStrategies: searchParams.get('notStrategies')?.split(',').filter(Boolean),
      notSource: searchParams.get('notSource')?.split(',').filter(Boolean),
      notEmailStatus: searchParams.get('notEmailStatus')?.split(',').filter(Boolean),
      notEnrichmentCompanyType: searchParams.get('notEnrichmentCompanyType')?.split(',').filter(Boolean),
      notEnrichmentCapitalPositions: searchParams.get('notEnrichmentCapitalPositions')?.split(',').filter(Boolean),
      notCompanyWebsiteScrapingStatus: searchParams.get('notCompanyWebsiteScrapingStatus')?.split(',').filter(Boolean),
      notCompanyOverviewStatus: searchParams.get('notCompanyOverviewStatus')?.split(',').filter(Boolean),
      notCompanyOverviewV2Status: searchParams.get('notCompanyOverviewV2Status')?.split(',').filter(Boolean),
      notCompanyInvestmentCriteriaStatus: searchParams.get('notCompanyInvestmentCriteriaStatus')?.split(',').filter(Boolean)
    }
    
    // Status
    const isActive = searchParams.get('isActive') === 'true' ? true : searchParams.get('isActive') === 'false' ? false : null
    
    // Pagination
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = (page - 1) * limit
    
    // Sorting
    const sortBy = searchParams.get('sortBy') || 'updated_at'
    const sortOrder = searchParams.get('sortOrder') || 'desc'
    
    // Build WHERE conditions
    const whereConditions: string[] = []
    const queryParams: any[] = []
    let paramIndex = 1
    
    // Determine which tables we need to join based on filters
    const hasInvestmentCriteriaFilters = !!(
      capitalPosition?.length || loanTypes?.length || loanProgram?.length ||
      structuredLoanTranche?.length || recourseLoan?.length ||
      dealSizeMin !== null || dealSizeMax !== null ||
      targetReturnMin !== null || targetReturnMax !== null ||
      historicalIrrMin !== null || historicalIrrMax !== null ||
      historicalEmMin !== null || historicalEmMax !== null ||
      minHoldPeriod !== null || maxHoldPeriod !== null ||
      minLoanTerm !== null || maxLoanTerm !== null ||
      interestRateMin !== null || interestRateMax !== null ||
      loanToValueMin !== null || loanToValueMax !== null ||
      loanToCostMin !== null || loanToCostMax !== null ||
      minLoanDscr !== null || maxLoanDscr !== null ||
      propertyTypes?.length || propertySubcategories?.length ||
      strategies?.length || financialProducts?.length ||
      regions?.length || states?.length || cities?.length || countries?.length ||
      isActive !== null || entityId || entityName || criteriaId ||
      notFilters.notCapitalPosition?.length || notFilters.notPropertyTypes?.length || notFilters.notStrategies?.length
    )
    
    const hasContactEnrichmentFilters = !!(
      enrichmentCompanyType?.length || enrichmentCapitalPositions?.length || enrichmentStatus?.length ||
      notFilters.notEnrichmentCompanyType?.length || notFilters.notEnrichmentCapitalPositions?.length
    )
    
    const hasGmailFilters = hasBeenReachedOut !== null
    
    // Only filter for contacts with investment criteria when IC filters are applied
    if (hasInvestmentCriteriaFilters) {
      whereConditions.push(`ic.entity_type = 'Contact'`)
    }
    
    // Enhanced global search on contact names, emails, company, and title
    if (searchTerm) {
      whereConditions.push(`(
        c.first_name ILIKE $${paramIndex} OR 
        c.last_name ILIKE $${paramIndex} OR
        c.full_name ILIKE $${paramIndex} OR
        CONCAT(c.first_name, ' ', c.last_name) ILIKE $${paramIndex} OR
        c.email ILIKE $${paramIndex} OR
        c.personal_email ILIKE $${paramIndex} OR
        c.additional_email ILIKE $${paramIndex} OR
        c.title ILIKE $${paramIndex} OR
        co.company_name ILIKE $${paramIndex}
      )`)
      queryParams.push(`%${searchTerm}%`)
      paramIndex++
    }
    
    
    
    // Investment Criteria Filters
    if (entityId) {
      whereConditions.push(`ic.entity_id = $${paramIndex}`)
      queryParams.push(entityId)
      paramIndex++
    }
    
    if (entityName) {
      whereConditions.push(`ic.entity_name ILIKE $${paramIndex}`)
      queryParams.push(`%${entityName}%`)
      paramIndex++
    }
    
    if (criteriaId) {
      whereConditions.push(`ic.id = $${paramIndex}`)
      queryParams.push(parseInt(criteriaId))
      paramIndex++
    }
    
    if (capitalPosition?.length) {
      // EXACT MATCHING: Include contacts with ANY of the selected capital positions
      whereConditions.push(`ic.capital_position && $${paramIndex}::text[]`)
      queryParams.push(capitalPosition)
      paramIndex++
    }
    
    if (loanTypes?.length) {
      whereConditions.push(`ic.loan_type && $${paramIndex}::text[]`)
      queryParams.push(loanTypes)
      paramIndex++
    }
    
    if (loanProgram?.length) {
      whereConditions.push(`ic.loan_program && $${paramIndex}::text[]`)
      queryParams.push(loanProgram)
      paramIndex++
    }
    
    if (structuredLoanTranche?.length) {
      whereConditions.push(`ic.structured_loan_tranche && $${paramIndex}::text[]`)
      queryParams.push(structuredLoanTranche)
      paramIndex++
    }
    
    if (recourseLoan?.length) {
      whereConditions.push(`ic.recourse_loan && $${paramIndex}::text[]`)
      queryParams.push(recourseLoan)
      paramIndex++
    }
    
    // Deal size filters
    if (dealSizeMin !== null) {
      whereConditions.push(`ic.minimum_deal_size >= $${paramIndex}`)
      queryParams.push(dealSizeMin)
      paramIndex++
    }
    
    if (dealSizeMax !== null) {
      whereConditions.push(`ic.maximum_deal_size <= $${paramIndex}`)
      queryParams.push(dealSizeMax)
      paramIndex++
    }
    
    // Return filters
    if (targetReturnMin !== null) {
      whereConditions.push(`ic.target_return >= $${paramIndex}`)
      queryParams.push(targetReturnMin)
      paramIndex++
    }
    
    if (targetReturnMax !== null) {
      whereConditions.push(`ic.target_return <= $${paramIndex}`)
      queryParams.push(targetReturnMax)
      paramIndex++
    }
    
    if (historicalIrrMin !== null) {
      whereConditions.push(`ic.historical_irr >= $${paramIndex}`)
      queryParams.push(historicalIrrMin)
      paramIndex++
    }
    
    if (historicalIrrMax !== null) {
      whereConditions.push(`ic.historical_irr <= $${paramIndex}`)
      queryParams.push(historicalIrrMax)
      paramIndex++
    }
    
    if (historicalEmMin !== null) {
      whereConditions.push(`ic.historical_em >= $${paramIndex}`)
      queryParams.push(historicalEmMin)
      paramIndex++
    }
    
    if (historicalEmMax !== null) {
      whereConditions.push(`ic.historical_em <= $${paramIndex}`)
      queryParams.push(historicalEmMax)
      paramIndex++
    }
    
    // Hold period filters
    if (minHoldPeriod !== null) {
      whereConditions.push(`ic.min_hold_period >= $${paramIndex}`)
      queryParams.push(minHoldPeriod)
      paramIndex++
    }
    
    if (maxHoldPeriod !== null) {
      whereConditions.push(`ic.max_hold_period <= $${paramIndex}`)
      queryParams.push(maxHoldPeriod)
      paramIndex++
    }
    
    // Loan term filters
    if (minLoanTerm !== null) {
      whereConditions.push(`ic.min_loan_term >= $${paramIndex}`)
      queryParams.push(minLoanTerm)
      paramIndex++
    }
    
    if (maxLoanTerm !== null) {
      whereConditions.push(`ic.max_loan_term <= $${paramIndex}`)
      queryParams.push(maxLoanTerm)
      paramIndex++
    }
    
    // Interest rate filters
    if (interestRateMin !== null) {
      whereConditions.push(`ic.interest_rate_min >= $${paramIndex}`)
      queryParams.push(interestRateMin)
      paramIndex++
    }
    
    if (interestRateMax !== null) {
      whereConditions.push(`ic.interest_rate_max <= $${paramIndex}`)
      queryParams.push(interestRateMax)
      paramIndex++
    }
    
    // LTV filters
    if (loanToValueMin !== null) {
      whereConditions.push(`ic.loan_to_value_min >= $${paramIndex}`)
      queryParams.push(loanToValueMin)
      paramIndex++
    }
    
    if (loanToValueMax !== null) {
      whereConditions.push(`ic.loan_to_value_max <= $${paramIndex}`)
      queryParams.push(loanToValueMax)
      paramIndex++
    }
    
    // LTC filters
    if (loanToCostMin !== null) {
      whereConditions.push(`ic.loan_to_cost_min >= $${paramIndex}`)
      queryParams.push(loanToCostMin)
      paramIndex++
    }
    
    if (loanToCostMax !== null) {
      whereConditions.push(`ic.loan_to_cost_max <= $${paramIndex}`)
      queryParams.push(loanToCostMax)
      paramIndex++
    }
    
    // DSCR filters
    if (minLoanDscr !== null) {
      whereConditions.push(`ic.min_loan_dscr >= $${paramIndex}`)
      queryParams.push(minLoanDscr)
      paramIndex++
    }
    
    if (maxLoanDscr !== null) {
      whereConditions.push(`ic.max_loan_dscr <= $${paramIndex}`)
      queryParams.push(maxLoanDscr)
      paramIndex++
    }
    
    // Property type filters
    if (propertyTypes?.length) {
      whereConditions.push(`ic.property_types && $${paramIndex}::text[]`)
      queryParams.push(propertyTypes)
      paramIndex++
    }
    
    if (propertySubcategories?.length) {
      whereConditions.push(`ic.property_sub_categories && $${paramIndex}::text[]`)
      queryParams.push(propertySubcategories)
      paramIndex++
    }
    
    if (strategies?.length) {
      whereConditions.push(`ic.strategies && $${paramIndex}::text[]`)
      queryParams.push(strategies)
      paramIndex++
    }
    
    if (financialProducts?.length) {
      whereConditions.push(`ic.financial_products && $${paramIndex}::jsonb`)
      queryParams.push(financialProducts)
      paramIndex++
    }
    
    // Geographic filters from investment criteria
    if (regions?.length) {
      whereConditions.push(`ic.region && $${paramIndex}::text[]`)
      queryParams.push(regions)
      paramIndex++
    }
    
    if (states?.length) {
      whereConditions.push(`ic.state && $${paramIndex}::text[]`)
      queryParams.push(states)
      paramIndex++
    }
    
    if (cities?.length) {
      whereConditions.push(`ic.city && $${paramIndex}::text[]`)
      queryParams.push(cities)
      paramIndex++
    }
    
    if (countries?.length) {
      whereConditions.push(`ic.country && $${paramIndex}::text[]`)
      queryParams.push(countries)
      paramIndex++
    }
    
    // Contact-specific filters
    if (source?.length) {
      // OR logic: Include contacts with ANY of the selected sources
      whereConditions.push(`c.source = ANY($${paramIndex}::text[])`)
      queryParams.push(source)
      paramIndex++
    }
    
    if (emailStatus?.length) {
      whereConditions.push(`c.email_status = ANY($${paramIndex}::text[])`)
      queryParams.push(emailStatus)
      paramIndex++
    }
    
    if (contactCompanyType?.length) {
      // EXACT MATCHING: Include contacts with ANY of the selected company types
      whereConditions.push(`c.company_type = ANY($${paramIndex}::text[])`)
      queryParams.push(contactCompanyType)
      paramIndex++
    }
    
    if (contactCapitalPosition?.length) {
      // EXACT MATCHING: Include contacts with ANY of the selected capital positions
      whereConditions.push(`c.capital_position && $${paramIndex}::text[]`)
      queryParams.push(contactCapitalPosition)
      paramIndex++
    }
    
    if (jobTier?.length) {
      whereConditions.push(`c.company_type = ANY($${paramIndex}::text[])`)
      queryParams.push(jobTier)
      paramIndex++
    }
    
    // Contact location filters - EXACT MATCHING
    if (contactCountries?.length) {
      whereConditions.push(`c.contact_country = ANY($${paramIndex}::text[])`)
      queryParams.push(contactCountries)
      paramIndex++
    }
    
    if (contactStates?.length) {
      whereConditions.push(`c.contact_state = ANY($${paramIndex}::text[])`)
      queryParams.push(contactStates)
      paramIndex++
    }
    
    if (contactCities?.length) {
      whereConditions.push(`c.contact_city = ANY($${paramIndex}::text[])`)
      queryParams.push(contactCities)
      paramIndex++
    }
    
    // Processing status filters
    if (emailVerificationStatus?.length) {
      // EXACT MATCHING: Include contacts with ANY of the selected verification statuses
      whereConditions.push(`c.email_verification_status = ANY($${paramIndex}::text[])`)
      queryParams.push(emailVerificationStatus)
      paramIndex++
    }
    
    if (contactEnrichmentStatus?.length) {
      // EXACT MATCHING: Include contacts with ANY of the selected enrichment statuses
      whereConditions.push(`c.contact_enrichment_status = ANY($${paramIndex}::text[])`)
      queryParams.push(contactEnrichmentStatus)
      paramIndex++
    }
    
    if (contactEnrichmentV2Status?.length) {
      // EXACT MATCHING: Include contacts with ANY of the selected V2 enrichment statuses
      whereConditions.push(`c.contact_enrichment_v2_status = ANY($${paramIndex}::text[])`)
      queryParams.push(contactEnrichmentV2Status)
      paramIndex++
    }
    
    if (emailGenerationStatus?.length) {
      // Handle email generation status filter with support for 'not_started' (NULL values)
      const processedStatuses = emailGenerationStatus.map(status => 
        status === 'not_started' ? null : status
      );
      
      // Build condition that handles both NULL and non-NULL values
      const nullConditions: string[] = [];
      const nonNullConditions: string[] = [];
      
      processedStatuses.forEach((status, index) => {
        if (status === null) {
          nullConditions.push(`c.email_generation_status IS NULL`);
        } else {
          nonNullConditions.push(`c.email_generation_status = $${paramIndex + index}`);
        }
      });
      
      const allConditions = [...nullConditions, ...nonNullConditions];
      if (allConditions.length > 0) {
        whereConditions.push(`(${allConditions.join(' OR ')})`);
        // Add non-null values to params
        processedStatuses.filter(status => status !== null).forEach(status => {
          queryParams.push(status);
        });
        paramIndex += processedStatuses.filter(status => status !== null).length;
      }
    }
    
    if (emailSendingStatus?.length) {
      // EXACT MATCHING: Include contacts with ANY of the selected sending statuses
      whereConditions.push(`c.email_sending_status = ANY($${paramIndex}::text[])`)
      queryParams.push(emailSendingStatus)
      paramIndex++
    }

    // Company processing status filters (from company table)
    if (companyWebsiteScrapingStatus?.length) {
      whereConditions.push(`co.website_scraping_status = ANY($${paramIndex}::text[])`)
      queryParams.push(companyWebsiteScrapingStatus)
      paramIndex++
    }

    if (companyOverviewStatus?.length) {
      whereConditions.push(`co.overview_status = ANY($${paramIndex}::text[])`)
      queryParams.push(companyOverviewStatus)
      paramIndex++
    }

    if (companyOverviewV2Status?.length) {
      whereConditions.push(`co.overview_v2_status = ANY($${paramIndex}::text[])`)
      queryParams.push(companyOverviewV2Status)
      paramIndex++
    }

    if (companyInvestmentCriteriaStatus?.length) {
      whereConditions.push(`co.investment_criteria_status = ANY($${paramIndex}::text[])`)
      queryParams.push(companyInvestmentCriteriaStatus)
      paramIndex++
    }
    
    // Contact Enrichment filters (NEW)
    if (enrichmentCompanyType?.length) {
      // EXACT MATCHING: Include contacts with ANY of the selected company types
      whereConditions.push(`ce.company_type = ANY($${paramIndex}::text[])`)
      queryParams.push(enrichmentCompanyType)
      paramIndex++
    }
    
    if (enrichmentCapitalPositions?.length) {
      // OR logic: Include contacts with ANY of the selected capital positions
      whereConditions.push(`ce.capital_positions ?| $${paramIndex}::text[]`)
      queryParams.push(enrichmentCapitalPositions)
      paramIndex++
    }
    
    if (enrichmentStatus?.length) {
      // EXACT MATCHING: Include contacts with ANY of the selected enrichment statuses
      whereConditions.push(`ce.status = ANY($${paramIndex}::text[])`)
      queryParams.push(enrichmentStatus)
      paramIndex++
    }
    
    // Boolean flags
    if (extracted !== null) {
      whereConditions.push(`c.extracted = $${paramIndex}`)
      queryParams.push(extracted)
      paramIndex++
    }
    
    if (searched !== null) {
      whereConditions.push(`c.searched = $${paramIndex}`)
      queryParams.push(searched)
      paramIndex++
    }
    
    if (emailGenerated !== null) {
      whereConditions.push(`c.email_generated = $${paramIndex}`)
      queryParams.push(emailGenerated)
      paramIndex++
    }
    
    if (enriched !== null) {
      whereConditions.push(`c.enriched = $${paramIndex}`)
      queryParams.push(enriched)
      paramIndex++
    }
    
    if (hasSmartleadId !== null) {
      if (hasSmartleadId) {
        whereConditions.push(`c.smartlead_lead_id IS NOT NULL`)
      } else {
        whereConditions.push(`c.smartlead_lead_id IS NULL`)
      }
    }
    
    // Gmail outreach filters (IMPROVED)
    if (hasBeenReachedOut !== null) {
      if (hasBeenReachedOut) {
        whereConditions.push(`gm.contact_id IS NOT NULL`)
      } else {
        whereConditions.push(`gm.contact_id IS NULL`)
      }
    }
    
    // NOT filters (NEW) - Fixed to exclude NULL values
    if (notFilters.notCapitalPosition?.length) {
      // Exclude contacts with ANY of the selected capital positions AND ensure field is not NULL
      whereConditions.push(`(ic.capital_position IS NOT NULL AND NOT (ic.capital_position && $${paramIndex}::text[]))`)
      queryParams.push(notFilters.notCapitalPosition)
      paramIndex++
    }
    
    if (notFilters.notPropertyTypes?.length) {
      // Exclude contacts with ANY of the selected property types AND ensure field is not NULL
      whereConditions.push(`(ic.property_types IS NOT NULL AND NOT (ic.property_types && $${paramIndex}::text[]))`)
      queryParams.push(notFilters.notPropertyTypes)
      paramIndex++
    }
    
    if (notFilters.notStrategies?.length) {
      // Exclude contacts with ANY of the selected strategies AND ensure field is not NULL
      whereConditions.push(`(ic.strategies IS NOT NULL AND NOT (ic.strategies && $${paramIndex}::text[]))`)
      queryParams.push(notFilters.notStrategies)
      paramIndex++
    }
    
    if (notFilters.notSource?.length) {
      // Exclude contacts with ANY of the selected sources AND ensure field is not NULL
      whereConditions.push(`(c.source IS NOT NULL AND NOT (c.source = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notSource)
      paramIndex++
    }
    
    if (notFilters.notEmailStatus?.length) {
      // Exclude contacts with ANY of the selected email statuses AND ensure field is not NULL
      whereConditions.push(`(c.email_status IS NOT NULL AND NOT (c.email_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notEmailStatus)
      paramIndex++
    }
    
    if (notFilters.notEnrichmentCompanyType?.length) {
      // Exclude contacts with ANY of the selected company types AND ensure field is not NULL
      whereConditions.push(`(ce.company_type IS NOT NULL AND NOT (ce.company_type = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notEnrichmentCompanyType)
      paramIndex++
    }
    
    if (notFilters.notEnrichmentCapitalPositions?.length) {
      // Exclude contacts with ANY of the selected capital positions AND ensure field is not NULL
        whereConditions.push(`(ce.capital_positions IS NULL OR NOT (ce.capital_positions ?| $${paramIndex}::text[]))`)
        queryParams.push(notFilters.notEnrichmentCapitalPositions)
      paramIndex++
    }

    if (notFilters.notCompanyWebsiteScrapingStatus?.length) {
      whereConditions.push(`(co.website_scraping_status IS NOT NULL AND NOT (co.website_scraping_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notCompanyWebsiteScrapingStatus)
      paramIndex++
    }

    if (notFilters.notCompanyOverviewStatus?.length) {
      whereConditions.push(`(co.overview_status IS NOT NULL AND NOT (co.overview_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notCompanyOverviewStatus)
      paramIndex++
    }

    if (notFilters.notCompanyOverviewV2Status?.length) {
      whereConditions.push(`(co.overview_v2_status IS NOT NULL AND NOT (co.overview_v2_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notCompanyOverviewV2Status)
      paramIndex++
    }

    if (notFilters.notCompanyInvestmentCriteriaStatus?.length) {
      whereConditions.push(`(co.investment_criteria_status IS NOT NULL AND NOT (co.investment_criteria_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notCompanyInvestmentCriteriaStatus)
      paramIndex++
    }
    
    // Status filters
    if (isActive !== null) {
      whereConditions.push(`ic.is_active = $${paramIndex}`)
      queryParams.push(isActive)
      paramIndex++
    }

    // Build conditional JOINs - OPTIMIZED to only join what we need
    let joinClauses = `
      FROM contacts c
      LEFT JOIN companies co ON c.company_id = co.company_id
    `
    
    // Only join investment criteria if we need to filter by it OR if we need the criteria_id for display
    if (hasInvestmentCriteriaFilters) {
      joinClauses += `
        INNER JOIN (
          SELECT DISTINCT ON (entity_id) *
          FROM investment_criteria 
          WHERE entity_type LIKE 'Contact%'
          ORDER BY entity_id, updated_at DESC
        ) ic ON c.contact_id::text = ic.entity_id
      `
    } else {
      // Light LEFT JOIN to get just the criteria_id for "Has IC" badge
      joinClauses += `
        LEFT JOIN (
          SELECT DISTINCT ON (entity_id) entity_id, criteria_id
          FROM investment_criteria 
          WHERE entity_type LIKE 'Contact%'
          ORDER BY entity_id, updated_at DESC
        ) ic ON c.contact_id::text = ic.entity_id
      `
    }
    
    // Only join contact enrichment if we have enrichment filters
    if (hasContactEnrichmentFilters) {
      joinClauses += `
        INNER JOIN contact_enrichment ce ON c.contact_id = ce.contact_id
      `
    }
    
    // Only join gmail messages if we have gmail filters - HIGHLY OPTIMIZED
    if (hasGmailFilters) {
      joinClauses += `
        LEFT JOIN (
          SELECT DISTINCT contact_id
          FROM (
            SELECT unnest(recipients) as email
            FROM gmail_messages
          ) gm_emails
          INNER JOIN contacts c_inner ON (
            gm_emails.email = c_inner.email
          )
          WHERE c_inner.email IS NOT NULL
        ) gm ON c.contact_id = gm.contact_id
      `
    }
    
    // Build the final WHERE clause
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''
    
    // Simplified sorting - only essential fields (always use contact fields for consistency with SELECT)
    const validSortFields = {
      'updated_at': 'c.updated_at',
      'created_at': 'c.created_at',
      'first_name': 'c.first_name',
      'last_name': 'c.last_name',
      'email': 'c.email',
      'title': 'c.title',
      'company_name': 'co.company_name',
      'email_verification_status': 'c.email_verification_status',
      'contact_enrichment_status': 'c.contact_enrichment_status',
      'email_generation_status': 'c.email_generation_status',
      'processing_error_count': 'c.processing_error_count'
    }
    
    const defaultSortField = 'c.updated_at'
    const sortField = validSortFields[sortBy as keyof typeof validSortFields] || defaultSortField
    const orderClause = `ORDER BY ${sortField} ${sortOrder.toUpperCase()} NULLS LAST`
    
    // Count query - OPTIMIZED with hints
    const countQuery = `
      SELECT COUNT(DISTINCT c.contact_id) as total
      ${joinClauses}
      ${whereClause}
    `
    
    // OPTIMIZED SELECT - Only fields needed for ContactCard display
    const selectFields = `
      c.contact_id,
      c.first_name,
      c.last_name,
      c.full_name,
      c.title,
      c.email,
      c.contact_city,
      c.contact_state,
      c.contact_country,
      c.email_status,
      c.smartlead_lead_id,
      c.processing_error_count,
      c.conflict_status,
      c.email_verification_status,
      c.email_verification_error,
      c.contact_enrichment_status,
      c.contact_enrichment_error,
      c.email_generation_status,
      c.email_generation_error,
      c.created_at,
      c.updated_at,
      
      -- Company data (minimal)
      co.company_name,
      
      -- Investment criteria indicator (just the ID for "Has IC" badge)
      ic.criteria_id
    `
    
    // Main query with only essential fields
    const query = `
      SELECT DISTINCT
        ${selectFields}
        
      ${joinClauses}
      ${whereClause}
      ${orderClause}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    
    queryParams.push(limit, offset)
    
    // Execute queries
    const [countResult, dataResult] = await Promise.all([
      pool.query(countQuery, queryParams.slice(0, -2)), // Remove limit and offset for count
      pool.query(query, queryParams)
    ])
    
    const total = parseInt(countResult.rows[0].total)
    const totalPages = Math.ceil(total / limit)
    
    return NextResponse.json({
      success: true,
      data: dataResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      meta: {
        hasInvestmentCriteriaFilters,
        hasContactEnrichmentFilters,
        hasGmailFilters,
        joinedTables: {
          investment_criteria: hasInvestmentCriteriaFilters,
          contact_enrichment: hasContactEnrichmentFilters,
          gmail_messages: hasGmailFilters
        },
        optimized: true, // Flag to indicate this is the optimized version
        fieldsReturned: 'minimal_for_card_display',
        performance: {
          gmailJoinOptimized: true, // UNNEST + INNER JOIN instead of CROSS JOIN
          conditionalJoins: true, // Only join tables when filters are applied
          minimalSelect: true, // Only fetch fields needed for ContactCard
          recommendedIndexes: [
            'idx_investment_criteria_capital_position_gin',
            'idx_contact_enrichment_capital_positions_gin',
            'idx_contacts_source',
            'idx_contacts_email_status'
          ]
        }
      }
    })
    
  } catch (error) {
    console.error('Error in unified contacts filters API:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch unified contact data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 