import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ contactId: string }> }
) {
  try {
    const { contactId } = await params;
    
    const query = `
      SELECT DISTINCT d.*
      FROM deals d
      INNER JOIN deal_contacts dc ON d.deal_id = dc.deal_id
      WHERE dc.contact_id = $1
      ORDER BY d.created_at DESC
    `;
    
    const result = await pool.query(query, [contactId]);
    
    return NextResponse.json({
      deals: result.rows,
      total: result.rows.length
    });
  } catch (error) {
    console.error('Error fetching deals for contact:', error);
    return NextResponse.json(
      { error: 'Failed to fetch deals for contact' },
      { status: 500 }
    );
  }
} 