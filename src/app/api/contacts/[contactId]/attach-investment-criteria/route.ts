import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ contactId: string }> }
) {
  try {
    const { contactId } = await params
    const body = await request.json()
    
    const { investment_criteria_ids } = body
    
    if (!investment_criteria_ids || !Array.isArray(investment_criteria_ids) || investment_criteria_ids.length === 0) {
      return NextResponse.json(
        { error: 'investment_criteria_ids array is required' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    
    try {
      await client.query('BEGIN')
      
      // Verify contact exists
      const contactCheck = await client.query(
        'SELECT contact_id FROM contacts WHERE contact_id = $1',
        [contactId]
      )
      
      if (contactCheck.rows.length === 0) {
        await client.query('ROLLBACK')
        return NextResponse.json(
          { error: 'Contact not found' },
          { status: 404 }
        )
      }
      
      // Verify all investment criteria exist
      const criteriaCheck = await client.query(
        'SELECT investment_criteria_id FROM investment_criteria_central WHERE investment_criteria_id = ANY($1)',
        [investment_criteria_ids]
      )
      
      if (criteriaCheck.rows.length !== investment_criteria_ids.length) {
        await client.query('ROLLBACK')
        return NextResponse.json(
          { error: 'One or more investment criteria not found' },
          { status: 404 }
        )
      }
      
      const attachedCriteria = []
      
      // For each investment criteria, create a copy for the contact
      for (const criteriaId of investment_criteria_ids) {
        // Get the original criteria data
        const originalCriteriaQuery = `
          SELECT 
            icc.*,
            icd.loan_type as debt_loan_type,
            icd.loan_program as debt_loan_program,
            icd.min_loan_term as debt_min_loan_term,
            icd.max_loan_term as debt_max_loan_term,
            icd.loan_interest_rate as debt_interest_rate,
            icd.loan_to_value_min as debt_loan_to_value_min,
            icd.loan_to_value_max as debt_loan_to_value_max,
            icd.loan_to_cost_min as debt_loan_to_cost_min,
            icd.loan_to_cost_max as debt_loan_to_cost_max,
            icd.min_loan_dscr as debt_min_loan_dscr,
            icd.max_loan_dscr as debt_max_loan_dscr,
            icd.structured_loan_tranche as debt_structured_loan_tranche,
            icd.recourse_loan as debt_recourse_loan,
            icd.closing_time as debt_closing_time,
            icd.debt_program_overview,
            ice.target_return as equity_target_return,
            ice.minimum_internal_rate_of_return as equity_minimum_internal_rate_of_return,
            ice.min_hold_period_years as equity_min_hold_period_years,
            ice.max_hold_period_years as equity_max_hold_period_years,
            ice.minimum_yield_on_cost as equity_minimum_yield_on_cost,
            ice.minimum_equity_multiple as equity_minimum_equity_multiple,
            ice.ownership_requirement as equity_ownership_requirement,
            ice.equity_program_overview
          FROM investment_criteria_central icc
          LEFT JOIN investment_criteria_debt icd ON icc.investment_criteria_debt_id = icd.investment_criteria_debt_id
          LEFT JOIN investment_criteria_equity ice ON icc.investment_criteria_equity_id = ice.investment_criteria_equity_id
          WHERE icc.investment_criteria_id = $1
        `
        
        const originalResult = await client.query(originalCriteriaQuery, [criteriaId])
        
        if (originalResult.rows.length === 0) {
          continue // Skip if criteria not found
        }
        
        const original = originalResult.rows[0]
        
        // Create new debt record if original has debt data
        let newDebtId = null
        if (original.investment_criteria_debt_id) {
          const debtInsertQuery = `
            INSERT INTO investment_criteria_debt (
              investment_criteria_id, loan_type, loan_program, min_loan_term, max_loan_term,
              loan_interest_rate, loan_to_value_min, loan_to_value_max, loan_to_cost_min, loan_to_cost_max,
              min_loan_dscr, max_loan_dscr, structured_loan_tranche, recourse_loan, closing_time,
              debt_program_overview, created_at, updated_at
            ) VALUES (
              $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, NOW(), NOW()
            ) RETURNING investment_criteria_debt_id
          `
          
          const debtResult = await client.query(debtInsertQuery, [
            null, // Will be updated after central record is created
            original.debt_loan_type,
            original.debt_loan_program,
            original.debt_min_loan_term,
            original.debt_max_loan_term,
            original.debt_interest_rate,
            original.debt_loan_to_value_min,
            original.debt_loan_to_value_max,
            original.debt_loan_to_cost_min,
            original.debt_loan_to_cost_max,
            original.debt_min_loan_dscr,
            original.debt_max_loan_dscr,
            original.debt_structured_loan_tranche,
            original.debt_recourse_loan,
            original.debt_closing_time,
            original.debt_program_overview
          ])
          
          newDebtId = debtResult.rows[0].investment_criteria_debt_id
        }
        
        // Create new equity record if original has equity data
        let newEquityId = null
        if (original.investment_criteria_equity_id) {
          const equityInsertQuery = `
            INSERT INTO investment_criteria_equity (
              investment_criteria_id, target_return, minimum_internal_rate_of_return,
              min_hold_period_years, max_hold_period_years, minimum_yield_on_cost,
              minimum_equity_multiple, ownership_requirement, equity_program_overview,
              created_at, updated_at
            ) VALUES (
              $1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW()
            ) RETURNING investment_criteria_equity_id
          `
          
          const equityResult = await client.query(equityInsertQuery, [
            null, // Will be updated after central record is created
            original.equity_target_return,
            original.equity_minimum_internal_rate_of_return,
            original.equity_min_hold_period_years,
            original.equity_max_hold_period_years,
            original.equity_minimum_yield_on_cost,
            original.equity_minimum_equity_multiple,
            original.equity_ownership_requirement,
            original.equity_program_overview
          ])
          
          newEquityId = equityResult.rows[0].investment_criteria_equity_id
        }
        
        // Create new central record for the contact
        const centralInsertQuery = `
          INSERT INTO investment_criteria_central (
            entity_id, entity_type, investment_criteria_debt_id, investment_criteria_equity_id,
            capital_position, minimum_deal_size, maximum_deal_size, country, region, state, city,
            property_types, property_subcategories, strategies, decision_making_process, notes,
            created_at, updated_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, NOW(), NOW()
          ) RETURNING investment_criteria_id
        `
        
        const centralResult = await client.query(centralInsertQuery, [
          contactId,
          'contact',
          newDebtId,
          newEquityId,
          original.capital_position,
          original.minimum_deal_size,
          original.maximum_deal_size,
          original.country,
          original.region,
          original.state,
          original.city,
          original.property_types,
          original.property_subcategories,
          original.strategies,
          original.decision_making_process,
          original.notes
        ])
        
        const newCriteriaId = centralResult.rows[0].investment_criteria_id
        
        // Update debt record with the new criteria ID
        if (newDebtId) {
          await client.query(
            'UPDATE investment_criteria_debt SET investment_criteria_id = $1 WHERE investment_criteria_debt_id = $2',
            [newCriteriaId, newDebtId]
          )
        }
        
        // Update equity record with the new criteria ID
        if (newEquityId) {
          await client.query(
            'UPDATE investment_criteria_equity SET investment_criteria_id = $1 WHERE investment_criteria_equity_id = $2',
            [newCriteriaId, newEquityId]
          )
        }
        
        attachedCriteria.push({
          original_criteria_id: criteriaId,
          new_criteria_id: newCriteriaId,
          capital_position: original.capital_position
        })
      }
      
      await client.query('COMMIT')
      
      return NextResponse.json({
        success: true,
        message: `Successfully attached ${attachedCriteria.length} investment criteria to contact`,
        attached_criteria: attachedCriteria
      })
      
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
    
  } catch (error) {
    console.error('Error attaching investment criteria to contact:', error)
    return NextResponse.json(
      { error: 'Failed to attach investment criteria to contact' },
      { status: 500 }
    )
  }
}
