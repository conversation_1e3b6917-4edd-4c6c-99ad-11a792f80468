import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ contactId: string }> }
) {
  try {
    const { contactId } = await params

    // Query to fetch enrichment data for the contact from the new unified table
    const query = `
      SELECT 
        executive_summary,
        career_timeline,
        notable_activities,
        education,
        personal_tidbits,
        conversation_hooks,
        sources,
        osint_profile,
        company_type,
        capital_positions,
        confidence as classification_confidence,
        reasoning as classification_reasoning,
        status,
        completed_at,
        tokens_used,
        llm_model,
        llm_usage
      FROM 
        contact_enrichment
      WHERE 
        contact_id = $1
      LIMIT 1
    `

    const result = await pool.query(query, [contactId])
    
    if (result.rows.length === 0) {
      // Return empty object with default values instead of 404
      return NextResponse.json({
        executive_summary: null,
        career_timeline: [],
        notable_activities: [],
        education: [],
        personal_tidbits: [],
        conversation_hooks: [],
        sources: [],
        osint_profile: null,
        company_type: null,
        capital_positions: [],
        classification_confidence: null,
        classification_reasoning: null,
        status: null,
        completed_at: null,
        tokens_used: null,
        llm_model: null,
        llm_usage: null
      })
    }

    // Parse JSON fields if they're stored as strings
    const enrichmentData = result.rows[0]
    const jsonFields = [
      'career_timeline',
      'notable_activities', 
      'education',
      'personal_tidbits',
      'conversation_hooks',
      'sources',
      'capital_positions',
      'llm_usage'
    ] as const;

    jsonFields.forEach((field) => {
      const value = enrichmentData[field];
      if (typeof value === 'string') {
        try {
          enrichmentData[field] = JSON.parse(value);
        } catch (parseError) {
          console.error(`Error parsing JSON for field ${field}:`, parseError);
          // keep as-is if not valid JSON
        }
      }
    });

    return NextResponse.json(enrichmentData)
  } catch (error) {
    console.error('Error fetching contact enrichment data:', error)
    return NextResponse.json({ error: 'Failed to fetch contact enrichment data' }, { status: 500 })
  }
} 