import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

// GET: Fetch transcripts for a specific contact by searching for their email in participants
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ contactId: string }> }
) {
  const { contactId } = await params;
  
  if (!contactId) {
    return NextResponse.json({ error: "Contact ID is required" }, { status: 400 });
  }

  try {
    // First get the contact's email
    const contactResult = await pool.query(
      "SELECT email FROM contacts WHERE contact_id = $1",
      [contactId]
    );

    if (contactResult.rows.length === 0) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }

    const contactEmail = contactResult.rows[0].email;
    
    if (!contactEmail) {
      return NextResponse.json({ 
        transcripts: [], 
        pagination: { page: 1, limit: 50, total: 0, totalPages: 0 } 
      });
    }

    // Search for transcripts where this contact's email appears in participants
    const query = `
      SELECT 
        ft.id,
        ft.provider_transcript_id,
        ft.title,
        ft.meeting_date,
        ft.duration,
        ft.participants,
        ft.transcript_text,
        ft.sentences,
        ft.created_at,
        ft.updated_at,
        fa.name as account_name
      FROM fireflies_transcripts ft
      JOIN fireflies_accounts fa ON ft.account_id = fa.id
      WHERE $1 = ANY(ft.participants)
      ORDER BY ft.meeting_date DESC
    `;

    const result = await pool.query(query, [contactEmail.toLowerCase()]);

    const transcripts = result.rows.map((row: any) => ({
      id: row.id,
      provider_transcript_id: row.provider_transcript_id,
      title: row.title,
      meeting_date: row.meeting_date,
      duration: row.duration,
      participants: row.participants || [],
      transcript_text: row.transcript_text || 'No transcript content available',
      sentences: row.sentences || [],
      account_name: row.account_name,
      created_at: row.created_at,
      updated_at: row.updated_at,
    }));

    return NextResponse.json({
      transcripts,
      pagination: {
        page: 1,
        limit: transcripts.length,
        total: transcripts.length,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      },
    });
  } catch (error) {
    console.error("Error fetching contact transcripts:", error);
    return NextResponse.json(
      { error: "Failed to fetch transcripts" },
      { status: 500 }
    );
  }
} 