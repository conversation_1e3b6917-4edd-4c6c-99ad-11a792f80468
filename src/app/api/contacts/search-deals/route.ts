import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q") || "";
    const limit = parseInt(searchParams.get("limit") || "20");

    if (!query || query.length < 2) {
      return NextResponse.json([]);
    }

    const sql = `
      SELECT 
        c.contact_id,
        c.first_name,
        c.last_name,
        c.full_name,
        c.email,
        c.title,
        c.company_name,
        c.contact_city,
        c.contact_state,
        c.contact_country,
        c.linkedin_url,
        c.phone_number,
        c.created_at,
        c.updated_at
      FROM contacts c
      WHERE 
        c.first_name ILIKE $1 OR 
        c.last_name ILIKE $1 OR 
        c.full_name ILIKE $1 OR 
        c.email ILIKE $1 OR 
        c.title ILIKE $1 OR 
        c.company_name ILIKE $1
      ORDER BY 
        CASE 
          WHEN c.full_name ILIKE $1 THEN 1
          WHEN c.first_name ILIKE $1 OR c.last_name ILIKE $1 THEN 2
          WHEN c.email ILIKE $1 THEN 3
          ELSE 4
        END,
        c.full_name,
        c.created_at DESC
      LIMIT $2
    `;

    const result = await pool.query(sql, [`%${query}%`, limit]);

    return NextResponse.json(result.rows);
  } catch (error) {
    console.error("Error searching contacts:", error);
    return NextResponse.json(
      { error: "Failed to search contacts" },
      { status: 500 }
    );
  }
} 