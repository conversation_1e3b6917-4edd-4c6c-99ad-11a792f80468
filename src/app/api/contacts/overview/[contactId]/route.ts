import { NextRequest, NextResponse } from 'next/server'
import { pool } from '../../../../../lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ contactId: string }> }
) {
  try {
    const resolvedParams = await params
    const contactId = parseInt(resolvedParams.contactId)
    
    if (isNaN(contactId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid contact ID' },
        { status: 400 }
      )
    }

    // Get contact basic info
    const contactSql = `
      SELECT 
        c.contact_id,
        c.full_name,
        c.first_name,
        c.last_name,
        c.title,
        c.email,
        c.linkedin_url,
        c.company_id,
        co.company_name,
        co.company_website,
        co.industry,
        c.contact_country,
        c.email_status,
        c.searched,
        c.extracted,
        c.email_generated
      FROM contacts c
      LEFT JOIN companies co ON c.company_id = co.company_id
      WHERE c.contact_id = $1
    `

    const contactResult = await pool.query(contactSql, [contactId])
    
    if (contactResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Contact not found' },
        { status: 404 }
      )
    }

    const contact = contactResult.rows[0]

    // Get extracted data if available
    let extractedData = null
    if (contact.extracted) {
      const extractedSql = `
        SELECT 
          contact_id,
          executive_summary,
          career_timeline,
          notable_activities,
          personal_tidbits,
          conversation_hooks,
          outreach_draft,
          sources,
          created_at,
          updated_at
        FROM contact_extracted_data
        WHERE contact_id = $1
      `

      const extractedResult = await pool.query(extractedSql, [contactId])
      
      if (extractedResult.rows.length > 0) {
        extractedData = extractedResult.rows[0]
        
        // Parse JSON fields
        try {
          if (extractedData.career_timeline && typeof extractedData.career_timeline === 'string') {
            extractedData.career_timeline = JSON.parse(extractedData.career_timeline)
          }
          if (extractedData.notable_activities && typeof extractedData.notable_activities === 'string') {
            extractedData.notable_activities = JSON.parse(extractedData.notable_activities)
          }
          if (extractedData.personal_tidbits && typeof extractedData.personal_tidbits === 'string') {
            extractedData.personal_tidbits = JSON.parse(extractedData.personal_tidbits)
          }
          if (extractedData.conversation_hooks && typeof extractedData.conversation_hooks === 'string') {
            extractedData.conversation_hooks = JSON.parse(extractedData.conversation_hooks)
          }
          if (extractedData.sources && typeof extractedData.sources === 'string') {
            extractedData.sources = JSON.parse(extractedData.sources)
          }
        } catch (parseError) {
          console.warn('Error parsing JSON fields for contact', contactId, parseError)
        }
      }
    }

    // Get searched data if available
    let searchedData = null
    if (contact.searched) {
      const searchedSql = `
        SELECT 
          contact_id,
          profile,
          input_data,
          prompt_content,
          tokens_used,
          created_at,
          updated_at
        FROM contact_searched_data
        WHERE contact_id = $1
      `

      const searchedResult = await pool.query(searchedSql, [contactId])
      
      if (searchedResult.rows.length > 0) {
        searchedData = searchedResult.rows[0]
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        contact,
        extracted_data: extractedData,
        searched_data: searchedData
      }
    })

  } catch (error) {
    console.error('Error fetching contact overview data:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
} 