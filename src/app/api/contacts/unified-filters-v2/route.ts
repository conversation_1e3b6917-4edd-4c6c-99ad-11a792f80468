import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Stats mode: support stats=true|false|only and keep backward compat with statsOnly=true
    const statsParam = searchParams.get('stats')
    const legacyStatsOnly = searchParams.get('statsOnly') === 'true'
    type StatsMode = 'true' | 'false' | 'only'
    const statsMode: StatsMode = statsParam === 'only' ? 'only' : statsParam === 'false' ? 'false' : statsParam === 'true' ? 'true' : (legacyStatsOnly ? 'only' : 'false')
    const shouldComputeStats = statsMode === 'only' || statsMode === 'true'
    const shouldReturnData = statsMode !== 'only'
    

    
    // Investment criteria filters
    const searchTerm = searchParams.get('searchTerm')
    const entityType = searchParams.get('entityType')
    const entityId = searchParams.get('entityId')
    const entityName = searchParams.get('entityName')
    const criteriaId = searchParams.get('criteriaId')
    
    
    // Capital & Financing
    const capitalPosition = searchParams.get('capitalPosition')?.split(',').filter(Boolean)
    const loanTypes = searchParams.get('loanTypes')?.split(',').filter(Boolean)
    const loanProgram = searchParams.get('loanProgram')?.split(',').filter(Boolean)
    const structuredLoanTranche = searchParams.get('structuredLoanTranche')?.split(',').filter(Boolean)
    const recourseLoan = searchParams.get('recourseLoan')?.split(',').filter(Boolean)
    
    // Deal size
    const dealSizeMin = searchParams.get('dealSizeMin') ? parseFloat(searchParams.get('dealSizeMin')!) : null
    const dealSizeMax = searchParams.get('dealSizeMax') ? parseFloat(searchParams.get('dealSizeMax')!) : null
    
    // Returns
    const targetReturnMin = searchParams.get('targetReturnMin') ? parseFloat(searchParams.get('targetReturnMin')!) : null
    const targetReturnMax = searchParams.get('targetReturnMax') ? parseFloat(searchParams.get('targetReturnMax')!) : null
    const historicalIrrMin = searchParams.get('historicalIrrMin') ? parseFloat(searchParams.get('historicalIrrMin')!) : null
    const historicalIrrMax = searchParams.get('historicalIrrMax') ? parseFloat(searchParams.get('historicalIrrMax')!) : null
    const historicalEmMin = searchParams.get('historicalEmMin') ? parseFloat(searchParams.get('historicalEmMin')!) : null
    const historicalEmMax = searchParams.get('historicalEmMax') ? parseFloat(searchParams.get('historicalEmMax')!) : null
    
    // Hold periods
    const minHoldPeriod = searchParams.get('minHoldPeriod') ? parseInt(searchParams.get('minHoldPeriod')!) : null
    const maxHoldPeriod = searchParams.get('maxHoldPeriod') ? parseInt(searchParams.get('maxHoldPeriod')!) : null
    
    // Loan terms
    const minLoanTerm = searchParams.get('minLoanTerm') ? parseInt(searchParams.get('minLoanTerm')!) : null
    const maxLoanTerm = searchParams.get('maxLoanTerm') ? parseInt(searchParams.get('maxLoanTerm')!) : null
    const interestRateMin = searchParams.get('interestRateMin') ? parseFloat(searchParams.get('interestRateMin')!) : null
    const interestRateMax = searchParams.get('interestRateMax') ? parseFloat(searchParams.get('interestRateMax')!) : null
    
    // LTV/LTC
    const loanToValueMin = searchParams.get('loanToValueMin') ? parseFloat(searchParams.get('loanToValueMin')!) : null
    const loanToValueMax = searchParams.get('loanToValueMax') ? parseFloat(searchParams.get('loanToValueMax')!) : null
    const loanToCostMin = searchParams.get('loanToCostMin') ? parseFloat(searchParams.get('loanToCostMin')!) : null
    const loanToCostMax = searchParams.get('loanToCostMax') ? parseFloat(searchParams.get('loanToCostMax')!) : null
    
    // DSCR
    const minLoanDscr = searchParams.get('minLoanDscr') ? parseFloat(searchParams.get('minLoanDscr')!) : null
    const maxLoanDscr = searchParams.get('maxLoanDscr') ? parseFloat(searchParams.get('maxLoanDscr')!) : null
    
    // Property & Geographic
    const propertyTypes = searchParams.get('propertyTypes')?.split(',').filter(Boolean)
    const propertySubcategories = searchParams.get('propertySubcategories')?.split(',').filter(Boolean)
    const strategies = searchParams.get('strategies')?.split(',').filter(Boolean)
    const financialProducts = searchParams.get('financialProducts')?.split(',').filter(Boolean)
    const regions = searchParams.get('regions')?.split(',').filter(Boolean)
    const states = searchParams.get('states')?.split(',').filter(Boolean)
    const cities = searchParams.get('cities')?.split(',').filter(Boolean)
    const countries = searchParams.get('countries')?.split(',').filter(Boolean)
    
    // Contact-specific filters
    const source = searchParams.get('source')?.split(',').filter(Boolean)
    const emailStatus = searchParams.get('emailStatus')?.split(',').filter(Boolean)
    const contactCompanyType = searchParams.get('contactCompanyType')?.split(',').filter(Boolean)
    const contactCapitalPosition = searchParams.get('contactCapitalPosition')?.split(',').filter(Boolean)
    const jobTier = searchParams.get('jobTier')?.split(',').filter(Boolean)
    const contactIds = searchParams.get('contactIds')?.split(',')
    const contactEmails = searchParams.get('contactEmails')?.split(',')
    const notEmptyEmail = searchParams.get('notEmptyEmail') === 'true'
    
    // Contact location filters
    const contactCountries = searchParams.get('contactCountries')?.split(',').filter(Boolean)
    const contactStates = searchParams.get('contactStates')?.split(',').filter(Boolean)
    const contactCities = searchParams.get('contactCities')?.split(',').filter(Boolean)
    
    // Processing status filters
    const emailVerificationStatus = searchParams.get('emailVerificationStatus')?.split(',').filter(Boolean)
    const contactEnrichmentV2Status = searchParams.get('contactEnrichmentV2Status')?.split(',').filter(Boolean)
    const contactInvestmentCriteriaStatus = searchParams.get('contactInvestmentCriteriaStatus')?.split(',').filter(Boolean)
    const emailGenerationStatus = searchParams.get('emailGenerationStatus')?.split(',').filter(Boolean)
    const emailSendingStatus = searchParams.get('emailSendingStatus')?.split(',').filter(Boolean)
    
    // Company processing status filters (from company table)
    const companyWebsiteScrapingStatus = searchParams.get('companyWebsiteScrapingStatus')?.split(',').filter(Boolean)
    const companyOverviewV2Status = searchParams.get('companyOverviewV2Status')?.split(',').filter(Boolean)
    const companyInvestmentCriteriaStatus = searchParams.get('companyInvestmentCriteriaStatus')?.split(',').filter(Boolean)
    
    // Company investment criteria existence filter
    const companyHasInvestmentCriteriaParam = searchParams.get('companyHasInvestmentCriteria')
    const companyHasInvestmentCriteria = companyHasInvestmentCriteriaParam === 'true' ? true : companyHasInvestmentCriteriaParam === 'false' ? false : undefined
    
    // === V2 ENRICHMENT FILTERS (NEW) ===
    const contactType = searchParams.get('contactType')?.split(',').filter(Boolean)
    const relationshipOwner = searchParams.get('relationshipOwner')?.split(',').filter(Boolean)
    const roleInDecisionMaking = searchParams.get('roleInDecisionMaking')?.split(',').filter(Boolean)
    const sourceOfIntroduction = searchParams.get('sourceOfIntroduction')?.split(',').filter(Boolean)
    
    // V2 Education filters
    const educationCollege = searchParams.get('educationCollege')?.split(',').filter(Boolean)
    const educationCollegeYearGraduated = searchParams.get('educationCollegeYearGraduated')?.split(',').filter(Boolean)
    
    // V2 Age range filter
    const ageRange = searchParams.get('ageRange')?.split(',').filter(Boolean)
    
    // V2 Boolean flags
    const accreditedInvestorStatus = searchParams.get('accreditedInvestorStatus') === 'true' ? true : searchParams.get('accreditedInvestorStatus') === 'false' ? false : null
    const hasExecutiveSummary = searchParams.get('hasExecutiveSummary') === 'true' ? true : searchParams.get('hasExecutiveSummary') === 'false' ? false : null
    const hasCareerTimeline = searchParams.get('hasCareerTimeline') === 'true' ? true : searchParams.get('hasCareerTimeline') === 'false' ? false : null
    const hasAdditionalEmail = searchParams.get('hasAdditionalEmail') === 'true' ? true : searchParams.get('hasAdditionalEmail') === 'false' ? false : null
    const hasSecondaryPhone = searchParams.get('hasSecondaryPhone') === 'true' ? true : searchParams.get('hasSecondaryPhone') === 'false' ? false : null
    
    // V2 Social media filters
    const hasTwitter = searchParams.get('hasTwitter') === 'true' ? true : searchParams.get('hasTwitter') === 'false' ? false : null
    const hasFacebook = searchParams.get('hasFacebook') === 'true' ? true : searchParams.get('hasFacebook') === 'false' ? false : null
    const hasInstagram = searchParams.get('hasInstagram') === 'true' ? true : searchParams.get('hasInstagram') === 'false' ? false : null
    const hasYoutube = searchParams.get('hasYoutube') === 'true' ? true : searchParams.get('hasYoutube') === 'false' ? false : null
    
    // V2 Personal details
    const hasHonorableAchievements = searchParams.get('hasHonorableAchievements') === 'true' ? true : searchParams.get('hasHonorableAchievements') === 'false' ? false : null
    const hasHobbies = searchParams.get('hasHobbies') === 'true' ? true : searchParams.get('hasHobbies') === 'false' ? false : null
    const hasContactAddress = searchParams.get('hasContactAddress') === 'true' ? true : searchParams.get('hasContactAddress') === 'false' ? false : null
    const hasContactZipCode = searchParams.get('hasContactZipCode') === 'true' ? true : searchParams.get('hasContactZipCode') === 'false' ? false : null
    
    // Boolean flags (legacy)
    const extracted = searchParams.get('extracted') === 'true' ? true : searchParams.get('extracted') === 'false' ? false : null
    const searched = searchParams.get('searched') === 'true' ? true : searchParams.get('searched') === 'false' ? false : null
    const emailGenerated = searchParams.get('emailGenerated') === 'true' ? true : searchParams.get('emailGenerated') === 'false' ? false : null
    const enriched = searchParams.get('enriched') === 'true' ? true : searchParams.get('enriched') === 'false' ? false : null
    const hasSmartleadId = searchParams.get('hasSmartleadId') === 'true' ? true : searchParams.get('hasSmartleadId') === 'false' ? false : null
    
    // Gmail outreach filters
    const hasBeenReachedOut = searchParams.get('hasBeenReachedOut') === 'true' ? true : searchParams.get('hasBeenReachedOut') === 'false' ? false : null
    
    // === NEW DEBT & EQUITY FIELDS ===
    // Enhanced Debt Fields
    const eligibleBorrower = searchParams.get('eligibleBorrower')?.split(',').filter(Boolean)
    const lienPosition = searchParams.get('lienPosition')?.split(',').filter(Boolean)
    const rateLock = searchParams.get('rateLock')?.split(',').filter(Boolean)
    const rateType = searchParams.get('rateType')?.split(',').filter(Boolean)
    const amortization = searchParams.get('amortization')?.split(',').filter(Boolean)
    const loanTypeNormalized = searchParams.get('loanTypeNormalized')?.split(',').filter(Boolean)
    
    // Additional Debt Fields from CSV
    const loanMinDebtYield = searchParams.get('loanMinDebtYield')?.split(',').filter(Boolean)
    const closingTimeMin = searchParams.get('closingTimeMin') ? parseFloat(searchParams.get('closingTimeMin')!) : null
    const closingTimeMax = searchParams.get('closingTimeMax') ? parseFloat(searchParams.get('closingTimeMax')!) : null
    
    // Additional Debt Fields from CSV - Missing fields
    const futureFacilities = searchParams.get('futureFacilities')?.split(',').filter(Boolean)
    const occupancyRequirements = searchParams.get('occupancyRequirements')?.split(',').filter(Boolean)
    const prepayment = searchParams.get('prepayment')?.split(',').filter(Boolean)
    const yieldMaintenance = searchParams.get('yieldMaintenance')?.split(',').filter(Boolean)
    
    // Loan Fee Fields
    const loanOriginationMaxFeeMin = searchParams.get('loanOriginationMaxFeeMin') ? parseFloat(searchParams.get('loanOriginationMaxFeeMin')!) : null
    const loanOriginationMaxFeeMax = searchParams.get('loanOriginationMaxFeeMax') ? parseFloat(searchParams.get('loanOriginationMaxFeeMax')!) : null
    const loanOriginationMinFeeMin = searchParams.get('loanOriginationMinFeeMin') ? parseFloat(searchParams.get('loanOriginationMinFeeMin')!) : null
    const loanOriginationMinFeeMax = searchParams.get('loanOriginationMinFeeMax') ? parseFloat(searchParams.get('loanOriginationMinFeeMax')!) : null
    const loanExitMinFeeMin = searchParams.get('loanExitMinFeeMin') ? parseFloat(searchParams.get('loanExitMinFeeMin')!) : null
    const loanExitMinFeeMax = searchParams.get('loanExitMinFeeMax') ? parseFloat(searchParams.get('loanExitMinFeeMax')!) : null
    const loanExitMaxFeeMin = searchParams.get('loanExitMaxFeeMin') ? parseFloat(searchParams.get('loanExitMaxFeeMin')!) : null
    const loanExitMaxFeeMax = searchParams.get('loanExitMaxFeeMax') ? parseFloat(searchParams.get('loanExitMaxFeeMax')!) : null
    
    // Loan Interest Rate Fields
    const loanInterestRateSofrMin = searchParams.get('loanInterestRateSofrMin') ? parseFloat(searchParams.get('loanInterestRateSofrMin')!) : null
    const loanInterestRateSofrMax = searchParams.get('loanInterestRateSofrMax') ? parseFloat(searchParams.get('loanInterestRateSofrMax')!) : null
    const loanInterestRateWsjMin = searchParams.get('loanInterestRateWsjMin') ? parseFloat(searchParams.get('loanInterestRateWsjMin')!) : null
    const loanInterestRateWsjMax = searchParams.get('loanInterestRateWsjMax') ? parseFloat(searchParams.get('loanInterestRateWsjMax')!) : null
    const loanInterestRatePrimeMin = searchParams.get('loanInterestRatePrimeMin') ? parseFloat(searchParams.get('loanInterestRatePrimeMin')!) : null
    const loanInterestRatePrimeMax = searchParams.get('loanInterestRatePrimeMax') ? parseFloat(searchParams.get('loanInterestRatePrimeMax')!) : null
    const loanInterestRate3ytMin = searchParams.get('loanInterestRate3ytMin') ? parseFloat(searchParams.get('loanInterestRate3ytMin')!) : null
    const loanInterestRate3ytMax = searchParams.get('loanInterestRate3ytMax') ? parseFloat(searchParams.get('loanInterestRate3ytMax')!) : null
    const loanInterestRate5ytMin = searchParams.get('loanInterestRate5ytMin') ? parseFloat(searchParams.get('loanInterestRate5ytMin')!) : null
    const loanInterestRate5ytMax = searchParams.get('loanInterestRate5ytMax') ? parseFloat(searchParams.get('loanInterestRate5ytMax')!) : null
    const loanInterestRate10ytMin = searchParams.get('loanInterestRate10ytMin') ? parseFloat(searchParams.get('loanInterestRate10ytMin')!) : null
    const loanInterestRate10ytMax = searchParams.get('loanInterestRate10ytMax') ? parseFloat(searchParams.get('loanInterestRate10ytMax')!) : null
    const loanInterestRate30ytMin = searchParams.get('loanInterestRate30ytMin') ? parseFloat(searchParams.get('loanInterestRate30ytMin')!) : null
    const loanInterestRate30ytMax = searchParams.get('loanInterestRate30ytMax') ? parseFloat(searchParams.get('loanInterestRate30ytMax')!) : null
    
    // Loan Sizing Fields
    const loanToValueMinMin = searchParams.get('loanToValueMinMin') ? parseFloat(searchParams.get('loanToValueMinMin')!) : null
    const loanToValueMinMax = searchParams.get('loanToValueMinMax') ? parseFloat(searchParams.get('loanToValueMinMax')!) : null
    const loanToValueMaxMin = searchParams.get('loanToValueMaxMin') ? parseFloat(searchParams.get('loanToValueMaxMin')!) : null
    const loanToValueMaxMax = searchParams.get('loanToValueMaxMax') ? parseFloat(searchParams.get('loanToValueMaxMax')!) : null
    const loanToCostMinMin = searchParams.get('loanToCostMinMin') ? parseFloat(searchParams.get('loanToCostMinMin')!) : null
    const loanToCostMinMax = searchParams.get('loanToCostMinMax') ? parseFloat(searchParams.get('loanToCostMinMax')!) : null
    const loanToCostMaxMin = searchParams.get('loanToCostMaxMin') ? parseFloat(searchParams.get('loanToCostMaxMin')!) : null
    const loanToCostMaxMax = searchParams.get('loanToCostMaxMax') ? parseFloat(searchParams.get('loanToCostMaxMax')!) : null
    
    // Loan Term Fields
    const minLoanTermMin = searchParams.get('minLoanTermMin') ? parseFloat(searchParams.get('minLoanTermMin')!) : null
    const minLoanTermMax = searchParams.get('minLoanTermMax') ? parseFloat(searchParams.get('minLoanTermMax')!) : null
    const maxLoanTermMin = searchParams.get('maxLoanTermMin') ? parseFloat(searchParams.get('maxLoanTermMin')!) : null
    const maxLoanTermMax = searchParams.get('maxLoanTermMax') ? parseFloat(searchParams.get('maxLoanTermMax')!) : null
    
    // Enhanced Equity Fields
    const ownershipRequirement = searchParams.get('ownershipRequirement')?.split(',').filter(Boolean)
    const minimumYieldOnCostMin = searchParams.get('minimumYieldOnCostMin') ? parseFloat(searchParams.get('minimumYieldOnCostMin')!) : null
    const minimumYieldOnCostMax = searchParams.get('minimumYieldOnCostMax') ? parseFloat(searchParams.get('minimumYieldOnCostMax')!) : null
    const maxLeverageToleranceMin = searchParams.get('maxLeverageToleranceMin') ? parseFloat(searchParams.get('maxLeverageToleranceMin')!) : null
    const maxLeverageToleranceMax = searchParams.get('maxLeverageToleranceMax') ? parseFloat(searchParams.get('maxLeverageToleranceMax')!) : null
    
    // NOT filters (V2 Enhanced)
    const notFilters = {
      notCapitalPosition: searchParams.get('notCapitalPosition')?.split(',').filter(Boolean),
      notPropertyTypes: searchParams.get('notPropertyTypes')?.split(',').filter(Boolean),
      notStrategies: searchParams.get('notStrategies')?.split(',').filter(Boolean),
      notSource: searchParams.get('notSource')?.split(',').filter(Boolean),
      notEmailStatus: searchParams.get('notEmailStatus')?.split(',').filter(Boolean),
      notEmailVerificationStatus: searchParams.get('notEmailVerificationStatus')?.split(',').filter(Boolean),

      notContactEnrichmentV2Status: searchParams.get('notContactEnrichmentV2Status')?.split(',').filter(Boolean),
      notContactInvestmentCriteriaStatus: searchParams.get('notContactInvestmentCriteriaStatus')?.split(',').filter(Boolean),
      notContactType: searchParams.get('notContactType')?.split(',').filter(Boolean),
      notCompanyWebsiteScrapingStatus: searchParams.get('notCompanyWebsiteScrapingStatus')?.split(',').filter(Boolean),
      notCompanyOverviewV2Status: searchParams.get('notCompanyOverviewV2Status')?.split(',').filter(Boolean),
      notCompanyInvestmentCriteriaStatus: searchParams.get('notCompanyInvestmentCriteriaStatus')?.split(',').filter(Boolean),
      // Additional NOT filters for investment criteria
      notLoanTypes: searchParams.get('notLoanTypes')?.split(',').filter(Boolean),
      notStructuredLoanTranche: searchParams.get('notStructuredLoanTranche')?.split(',').filter(Boolean),
      notLoanProgram: searchParams.get('notLoanProgram')?.split(',').filter(Boolean),
      notRecourseLoan: searchParams.get('notRecourseLoan')?.split(',').filter(Boolean),
      notEligibleBorrower: searchParams.get('notEligibleBorrower')?.split(',').filter(Boolean),
      notLienPosition: searchParams.get('notLienPosition')?.split(',').filter(Boolean),
      notOwnershipRequirement: searchParams.get('notOwnershipRequirement')?.split(',').filter(Boolean),
      notRateType: searchParams.get('notRateType')?.split(',').filter(Boolean),
      notAmortization: searchParams.get('notAmortization')?.split(',').filter(Boolean)
    }
    
    // Status
    const isActive = searchParams.get('isActive') === 'true' ? true : searchParams.get('isActive') === 'false' ? false : null
    
    // Pagination with safety limit
    const page = parseInt(searchParams.get('page') || '1')
    const requestedLimit = parseInt(searchParams.get('limit') || '50')
    const limit = Math.min(requestedLimit, 500) // Safety limit of 500
    const offset = (page - 1) * limit
    
    // Sorting
    const sortBy = searchParams.get('sortBy') || 'updated_at'
    const sortOrder = searchParams.get('sortOrder') || 'desc'
    
    // Build WHERE conditions
    const whereConditions: string[] = []
    const queryParams: any[] = []
    let paramIndex = 1

    // Log incoming query parameters for debugging
    console.log('[Contacts Unified API] Query params:', {
      searchTerm,
      emailVerificationStatus,
      contactEnrichmentV2Status,
      contactInvestmentCriteriaStatus,
      emailGenerationStatus,
      source,
      jobTier,
      limit,
      offset,
      statsMode
    })
    
    // Determine which tables we need to join based on filters
    const hasInvestmentCriteriaFilters = !!(
      // Investment criteria table column filters (should trigger IC JOIN)
      capitalPosition?.length || loanTypes?.length || loanProgram?.length ||
      structuredLoanTranche?.length || recourseLoan?.length ||
      dealSizeMin !== null || dealSizeMax !== null ||
      targetReturnMin !== null || targetReturnMax !== null ||
      historicalIrrMin !== null || historicalIrrMax !== null ||
      historicalEmMin !== null || historicalEmMax !== null ||
      minHoldPeriod !== null || maxHoldPeriod !== null ||
      minLoanTerm !== null || maxLoanTerm !== null ||
      interestRateMin !== null || interestRateMax !== null ||
      loanToValueMin !== null || loanToValueMax !== null ||
      loanToCostMin !== null || loanToCostMax !== null ||
      minLoanDscr !== null || maxLoanDscr !== null ||
      propertyTypes?.length || propertySubcategories?.length ||
      strategies?.length || financialProducts?.length ||
      regions?.length || states?.length || cities?.length || countries?.length ||
      isActive !== null || entityId || entityName || criteriaId ||
      // Investment criteria NOT filters (should trigger IC JOIN)
      notFilters.notCapitalPosition?.length || notFilters.notPropertyTypes?.length || notFilters.notStrategies?.length ||
      notFilters.notLoanTypes?.length || notFilters.notStructuredLoanTranche?.length || notFilters.notLoanProgram?.length ||
      notFilters.notRecourseLoan?.length || notFilters.notEligibleBorrower?.length || notFilters.notLienPosition?.length ||
      notFilters.notOwnershipRequirement?.length || notFilters.notRateType?.length || notFilters.notAmortization?.length ||
      eligibleBorrower?.length || lienPosition?.length || rateLock?.length ||
      rateType?.length || amortization?.length || loanTypeNormalized?.length ||
      ownershipRequirement?.length ||
      futureFacilities?.length || occupancyRequirements?.length || prepayment?.length || yieldMaintenance?.length
    )
    
    const hasContactEnrichmentV2Filters = !!(
      contactType?.length || relationshipOwner?.length || roleInDecisionMaking?.length ||
      sourceOfIntroduction?.length || educationCollege?.length || educationCollegeYearGraduated?.length ||
      ageRange?.length ||
      accreditedInvestorStatus !== null || hasExecutiveSummary !== null || hasCareerTimeline !== null ||
      hasAdditionalEmail !== null || hasSecondaryPhone !== null || hasTwitter !== null || hasFacebook !== null ||
      hasInstagram !== null || hasYoutube !== null || hasHonorableAchievements !== null || hasHobbies !== null ||
      hasContactAddress !== null || hasContactZipCode !== null || notFilters.notContactType?.length
    )
    
    const hasGmailFilters = hasBeenReachedOut !== null
    
    // Only filter for contacts with investment criteria when IC filters are applied
    if (hasInvestmentCriteriaFilters) {
      whereConditions.push(`ic.entity_type = 'contact'`)
    }
    
    // Enhanced global search on contact names, emails, company, and title + V2 enrichment data
    if (searchTerm) {
      whereConditions.push(`(
        c.first_name ILIKE $${paramIndex} OR 
        c.last_name ILIKE $${paramIndex} OR
        c.full_name ILIKE $${paramIndex} OR
        CONCAT(c.first_name, ' ', c.last_name) ILIKE $${paramIndex} OR
        c.email ILIKE $${paramIndex} OR
        c.personal_email ILIKE $${paramIndex} OR
        c.additional_email ILIKE $${paramIndex} OR
        c.title ILIKE $${paramIndex} OR
        co.company_name ILIKE $${paramIndex} OR
        c.contact_type ILIKE $${paramIndex} OR
        c.relationship_owner ILIKE $${paramIndex}
      )`)
      queryParams.push(`%${searchTerm}%`)
      paramIndex++
    }
    
    // Contact Filters
    if (contactIds?.length) {
      whereConditions.push(`c.contact_id = ANY($${paramIndex}::bigint[])`)
      queryParams.push(contactIds)
      paramIndex++
    }

    if (contactEmails?.length) {
      whereConditions.push(`c.email = ANY($${paramIndex}::text[])`)
      queryParams.push(contactEmails)
      paramIndex++
    }

    if (notEmptyEmail) {
      whereConditions.push(`COALESCE(c.email, '') <> ''`)
    }
    
    // Investment Criteria Filters (same as legacy)
    if (entityId) {
      whereConditions.push(`ic.entity_id = $${paramIndex}::bigint`)
      queryParams.push(entityId)
      paramIndex++
    }
    
    if (entityName) {
      whereConditions.push(`ic.entity_name ILIKE $${paramIndex}`)
      queryParams.push(`%${entityName}%`)
      paramIndex++
    }
    
    if (criteriaId) {
      whereConditions.push(`ic.id = $${paramIndex}::bigint`)
      queryParams.push(criteriaId) // Keep as string, let PostgreSQL handle the conversion
      paramIndex++
    }
    
    if (capitalPosition?.length) {
      whereConditions.push(`ic.capital_position = ANY($${paramIndex}::text[])`)
      queryParams.push(capitalPosition)
      paramIndex++
    }
    
    if (loanTypes?.length) {
      whereConditions.push(`icd.loan_type = ANY($${paramIndex}::text[])`)
      queryParams.push(loanTypes)
      paramIndex++
    }
    
    if (loanProgram?.length) {
      whereConditions.push(`icd.loan_program = ANY($${paramIndex}::text[])`)
      queryParams.push(loanProgram)
      paramIndex++
    }
    
    if (structuredLoanTranche?.length) {
      whereConditions.push(`icd.structured_loan_tranche = ANY($${paramIndex}::text[])`)
      queryParams.push(structuredLoanTranche)
      paramIndex++
    }
    
    if (recourseLoan?.length) {
      whereConditions.push(`icd.recourse_loan = ANY($${paramIndex}::text[])`)
      queryParams.push(recourseLoan)
      paramIndex++
    }
    
    // Deal size filters
    if (dealSizeMin !== null) {
      whereConditions.push(`ic.minimum_deal_size >= $${paramIndex}`)
      queryParams.push(dealSizeMin)
      paramIndex++
    }
    
    if (dealSizeMax !== null) {
      whereConditions.push(`ic.maximum_deal_size <= $${paramIndex}`)
      queryParams.push(dealSizeMax)
      paramIndex++
    }
    
    // Return filters (equity table)
    if (targetReturnMin !== null) {
      whereConditions.push(`ice.target_return >= $${paramIndex}`)
      queryParams.push(targetReturnMin)
      paramIndex++
    }
    
    if (targetReturnMax !== null) {
      whereConditions.push(`ice.target_return <= $${paramIndex}`)
      queryParams.push(targetReturnMax)
      paramIndex++
    }
    
    if (historicalIrrMin !== null) {
      whereConditions.push(`ice.minimum_internal_rate_of_return >= $${paramIndex}`)
      queryParams.push(historicalIrrMin)
      paramIndex++
    }
    
    if (historicalIrrMax !== null) {
      whereConditions.push(`ice.minimum_internal_rate_of_return <= $${paramIndex}`)
      queryParams.push(historicalIrrMax)
      paramIndex++
    }
    
    if (historicalEmMin !== null) {
      whereConditions.push(`ice.minimum_equity_multiple >= $${paramIndex}`)
      queryParams.push(historicalEmMin)
      paramIndex++
    }
    
    if (historicalEmMax !== null) {
      whereConditions.push(`ice.minimum_equity_multiple <= $${paramIndex}`)
      queryParams.push(historicalEmMax)
      paramIndex++
    }
    
    // Hold period filters (equity table)
    if (minHoldPeriod !== null) {
      whereConditions.push(`ice.min_hold_period_years >= $${paramIndex}`)
      queryParams.push(minHoldPeriod)
      paramIndex++
    }
    
    if (maxHoldPeriod !== null) {
      whereConditions.push(`ice.max_hold_period_years <= $${paramIndex}`)
      queryParams.push(maxHoldPeriod)
      paramIndex++
    }
    
    // Loan term filters (debt table)
    if (minLoanTerm !== null) {
      whereConditions.push(`icd.min_loan_term >= $${paramIndex}`)
      queryParams.push(minLoanTerm)
      paramIndex++
    }
    
    if (maxLoanTerm !== null) {
      whereConditions.push(`icd.max_loan_term <= $${paramIndex}`)
      queryParams.push(maxLoanTerm)
      paramIndex++
    }
    
    // Interest rate filters (debt table)
    if (interestRateMin !== null) {
      whereConditions.push(`icd.loan_interest_rate >= $${paramIndex}`)
      queryParams.push(interestRateMin)
      paramIndex++
    }
    
    if (interestRateMax !== null) {
      whereConditions.push(`icd.loan_interest_rate <= $${paramIndex}`)
      queryParams.push(interestRateMax)
      paramIndex++
    }
    
    // LTV filters (debt table)
    if (loanToValueMin !== null) {
      whereConditions.push(`icd.loan_to_value_min >= $${paramIndex}`)
      queryParams.push(loanToValueMin)
      paramIndex++
    }
    
    if (loanToValueMax !== null) {
      whereConditions.push(`icd.loan_to_value_max <= $${paramIndex}`)
      queryParams.push(loanToValueMax)
      paramIndex++
    }
    
    // LTC filters (debt table)
    if (loanToCostMin !== null) {
      whereConditions.push(`icd.loan_to_cost_min >= $${paramIndex}`)
      queryParams.push(loanToCostMin)
      paramIndex++
    }
    
    if (loanToCostMax !== null) {
      whereConditions.push(`icd.loan_to_cost_max <= $${paramIndex}`)
      queryParams.push(loanToCostMax)
      paramIndex++
    }
    
    // DSCR filters (debt table)
    if (minLoanDscr !== null) {
      whereConditions.push(`icd.min_loan_dscr >= $${paramIndex}`)
      queryParams.push(minLoanDscr)
      paramIndex++
    }
    
    if (maxLoanDscr !== null) {
      whereConditions.push(`icd.max_loan_dscr <= $${paramIndex}`)
      queryParams.push(maxLoanDscr)
      paramIndex++
    }
    
    // === NEW ENHANCED DEBT & EQUITY FILTERS ===
    // Enhanced Debt Fields
    if (eligibleBorrower?.length) {
      whereConditions.push(`icd.eligible_borrower = ANY($${paramIndex}::text[])`)
      queryParams.push(eligibleBorrower)
      paramIndex++
    }
    
    if (lienPosition?.length) {
      whereConditions.push(`icd.lien_position = ANY($${paramIndex}::text[])`)
      queryParams.push(lienPosition)
      paramIndex++
    }
    
    if (rateLock?.length) {
      whereConditions.push(`icd.rate_lock = ANY($${paramIndex}::text[])`)
      queryParams.push(rateLock)
      paramIndex++
    }
    
    if (rateType?.length) {
      whereConditions.push(`icd.rate_type = ANY($${paramIndex}::text[])`)
      queryParams.push(rateType)
      paramIndex++
    }
    
    if (amortization?.length) {
      whereConditions.push(`icd.amortization = ANY($${paramIndex}::text[])`)
      queryParams.push(amortization)
      paramIndex++
    }
    
    if (loanTypeNormalized?.length) {
      whereConditions.push(`icd.loan_type_normalized = ANY($${paramIndex}::text[])`)
      queryParams.push(loanTypeNormalized)
      paramIndex++
    }
    
    // Enhanced Equity Fields
    if (ownershipRequirement?.length) {
      whereConditions.push(`ice.ownership_requirement = ANY($${paramIndex}::text[])`)
      queryParams.push(ownershipRequirement)
      paramIndex++
    }
    
    // Additional Debt Fields from CSV
    if (loanMinDebtYield?.length) {
      whereConditions.push(`icd.loan_min_debt_yield = ANY($${paramIndex}::text[])`)
      queryParams.push(loanMinDebtYield)
      paramIndex++
    }
    
    if (closingTimeMin !== null) {
      whereConditions.push(`icd.closing_time >= $${paramIndex}`)
      queryParams.push(closingTimeMin)
      paramIndex++
    }
    
    if (closingTimeMax !== null) {
      whereConditions.push(`icd.closing_time <= $${paramIndex}`)
      queryParams.push(closingTimeMax)
      paramIndex++
    }
    
    // Additional Debt Fields from CSV - Missing fields
    if (futureFacilities?.length) {
      whereConditions.push(`icd.future_facilities = ANY($${paramIndex}::text[])`)
      queryParams.push(futureFacilities)
      paramIndex++
    }
    
    if (occupancyRequirements?.length) {
      whereConditions.push(`icd.occupancy_requirements = ANY($${paramIndex}::text[])`)
      queryParams.push(occupancyRequirements)
      paramIndex++
    }
    
    if (prepayment?.length) {
      whereConditions.push(`icd.prepayment = ANY($${paramIndex}::text[])`)
      queryParams.push(prepayment)
      paramIndex++
    }
    
    if (yieldMaintenance?.length) {
      whereConditions.push(`icd.yield_maintenance = ANY($${paramIndex}::text[])`)
      queryParams.push(yieldMaintenance)
      paramIndex++
    }
    
    // Loan Fee Filters
    if (loanOriginationMaxFeeMin !== null) {
      whereConditions.push(`icd.loan_origination_max_fee >= $${paramIndex}`)
      queryParams.push(loanOriginationMaxFeeMin)
      paramIndex++
    }
    
    if (loanOriginationMaxFeeMax !== null) {
      whereConditions.push(`icd.loan_origination_max_fee <= $${paramIndex}`)
      queryParams.push(loanOriginationMaxFeeMax)
      paramIndex++
    }
    
    if (loanOriginationMinFeeMin !== null) {
      whereConditions.push(`icd.loan_origination_min_fee >= $${paramIndex}`)
      queryParams.push(loanOriginationMinFeeMin)
      paramIndex++
    }
    
    if (loanOriginationMinFeeMax !== null) {
      whereConditions.push(`icd.loan_origination_min_fee <= $${paramIndex}`)
      queryParams.push(loanOriginationMinFeeMax)
      paramIndex++
    }
    
    if (loanExitMinFeeMin !== null) {
      whereConditions.push(`icd.loan_exit_min_fee >= $${paramIndex}`)
      queryParams.push(loanExitMinFeeMin)
      paramIndex++
    }
    
    if (loanExitMinFeeMax !== null) {
      whereConditions.push(`icd.loan_exit_min_fee <= $${paramIndex}`)
      queryParams.push(loanExitMinFeeMax)
      paramIndex++
    }
    
    if (loanExitMaxFeeMin !== null) {
      whereConditions.push(`icd.loan_exit_max_fee >= $${paramIndex}`)
      queryParams.push(loanExitMaxFeeMin)
      paramIndex++
    }
    
    if (loanExitMaxFeeMax !== null) {
      whereConditions.push(`icd.loan_exit_max_fee <= $${paramIndex}`)
      queryParams.push(loanExitMaxFeeMax)
      paramIndex++
    }
    
    // Loan Interest Rate Filters
    if (loanInterestRateSofrMin !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_sofr >= $${paramIndex}`)
      queryParams.push(loanInterestRateSofrMin)
      paramIndex++
    }
    
    if (loanInterestRateSofrMax !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_sofr <= $${paramIndex}`)
      queryParams.push(loanInterestRateSofrMax)
      paramIndex++
    }
    
    if (loanInterestRateWsjMin !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_wsj >= $${paramIndex}`)
      queryParams.push(loanInterestRateWsjMin)
      paramIndex++
    }
    
    if (loanInterestRateWsjMax !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_wsj <= $${paramIndex}`)
      queryParams.push(loanInterestRateWsjMax)
      paramIndex++
    }
    
    if (loanInterestRatePrimeMin !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_prime >= $${paramIndex}`)
      queryParams.push(loanInterestRatePrimeMin)
      paramIndex++
    }
    
    if (loanInterestRatePrimeMax !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_prime <= $${paramIndex}`)
      queryParams.push(loanInterestRatePrimeMax)
      paramIndex++
    }
    
    if (loanInterestRate3ytMin !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_3yt >= $${paramIndex}`)
      queryParams.push(loanInterestRate3ytMin)
      paramIndex++
    }
    
    if (loanInterestRate3ytMax !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_3yt <= $${paramIndex}`)
      queryParams.push(loanInterestRate3ytMax)
      paramIndex++
    }
    
    if (loanInterestRate5ytMin !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_5yt >= $${paramIndex}`)
      queryParams.push(loanInterestRate5ytMin)
      paramIndex++
    }
    
    if (loanInterestRate5ytMax !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_5yt <= $${paramIndex}`)
      queryParams.push(loanInterestRate5ytMax)
      paramIndex++
    }
    
    if (loanInterestRate10ytMin !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_10yt >= $${paramIndex}`)
      queryParams.push(loanInterestRate10ytMin)
      paramIndex++
    }
    
    if (loanInterestRate10ytMax !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_10yt <= $${paramIndex}`)
      queryParams.push(loanInterestRate10ytMax)
      paramIndex++
    }
    
    if (loanInterestRate30ytMin !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_30yt >= $${paramIndex}`)
      queryParams.push(loanInterestRate30ytMin)
      paramIndex++
    }
    
    if (loanInterestRate30ytMax !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_30yt <= $${paramIndex}`)
      queryParams.push(loanInterestRate30ytMax)
      paramIndex++
    }
    
    // Loan Sizing Filters
    if (loanToValueMinMin !== null) {
      whereConditions.push(`icd.loan_to_value_min >= $${paramIndex}`)
      queryParams.push(loanToValueMinMin)
      paramIndex++
    }
    
    if (loanToValueMinMax !== null) {
      whereConditions.push(`icd.loan_to_value_min <= $${paramIndex}`)
      queryParams.push(loanToValueMinMax)
      paramIndex++
    }
    
    if (loanToValueMaxMin !== null) {
      whereConditions.push(`icd.loan_to_value_max >= $${paramIndex}`)
      queryParams.push(loanToValueMaxMin)
      paramIndex++
    }
    
    if (loanToValueMaxMax !== null) {
      whereConditions.push(`icd.loan_to_value_max <= $${paramIndex}`)
      queryParams.push(loanToValueMaxMax)
      paramIndex++
    }
    
    if (loanToCostMinMin !== null) {
      whereConditions.push(`icd.loan_to_cost_min >= $${paramIndex}`)
      queryParams.push(loanToCostMinMin)
      paramIndex++
    }
    
    if (loanToCostMinMax !== null) {
      whereConditions.push(`icd.loan_to_cost_min <= $${paramIndex}`)
      queryParams.push(loanToCostMinMax)
      paramIndex++
    }
    
    if (loanToCostMaxMin !== null) {
      whereConditions.push(`icd.loan_to_cost_max >= $${paramIndex}`)
      queryParams.push(loanToCostMaxMin)
      paramIndex++
    }
    
    if (loanToCostMaxMax !== null) {
      whereConditions.push(`icd.loan_to_cost_max <= $${paramIndex}`)
      queryParams.push(loanToCostMaxMax)
      paramIndex++
    }
    
    // Loan Term Filters
    if (minLoanTermMin !== null) {
      whereConditions.push(`icd.min_loan_term >= $${paramIndex}`)
      queryParams.push(minLoanTermMin)
      paramIndex++
    }
    
    if (minLoanTermMax !== null) {
      whereConditions.push(`icd.min_loan_term <= $${paramIndex}`)
      queryParams.push(minLoanTermMax)
      paramIndex++
    }
    
    if (maxLoanTermMin !== null) {
      whereConditions.push(`icd.max_loan_term >= $${paramIndex}`)
      queryParams.push(maxLoanTermMin)
      paramIndex++
    }
    
    if (maxLoanTermMax !== null) {
      whereConditions.push(`icd.max_loan_term <= $${paramIndex}`)
      queryParams.push(maxLoanTermMax)
      paramIndex++
    }
    
    // Additional Equity Fields
    if (minimumYieldOnCostMin !== null) {
      whereConditions.push(`ice.minimum_yield_on_cost >= $${paramIndex}`)
      queryParams.push(minimumYieldOnCostMin)
      paramIndex++
    }
    
    if (minimumYieldOnCostMax !== null) {
      whereConditions.push(`ice.minimum_yield_on_cost <= $${paramIndex}`)
      queryParams.push(minimumYieldOnCostMax)
      paramIndex++
    }
    
    if (maxLeverageToleranceMin !== null) {
      whereConditions.push(`ice.max_leverage_tolerance >= $${paramIndex}`)
      queryParams.push(maxLeverageToleranceMin)
      paramIndex++
    }
    
    if (maxLeverageToleranceMax !== null) {
      whereConditions.push(`ice.max_leverage_tolerance <= $${paramIndex}`)
      queryParams.push(maxLeverageToleranceMax)
      paramIndex++
    }
    
    // Property type filters
    if (propertyTypes?.length) {
      whereConditions.push(`ic.property_types && $${paramIndex}::text[]`)
      queryParams.push(propertyTypes)
      paramIndex++
    }
    
    if (propertySubcategories?.length) {
      whereConditions.push(`ic.property_sub_categories && $${paramIndex}::text[]`)
      queryParams.push(propertySubcategories)
      paramIndex++
    }
    
    if (strategies?.length) {
      whereConditions.push(`ic.strategies && $${paramIndex}::text[]`)
      queryParams.push(strategies)
      paramIndex++
    }
    
    if (financialProducts?.length) {
      whereConditions.push(`ic.financial_products && $${paramIndex}::jsonb`)
      queryParams.push(financialProducts)
      paramIndex++
    }
    
    // Geographic filters from investment criteria
    if (regions?.length) {
      whereConditions.push(`ic.region && $${paramIndex}::text[]`)
      queryParams.push(regions)
      paramIndex++
    }
    
    if (states?.length) {
      whereConditions.push(`ic.state && $${paramIndex}::text[]`)
      queryParams.push(states)
      paramIndex++
    }
    
    if (cities?.length) {
      whereConditions.push(`ic.city && $${paramIndex}::text[]`)
      queryParams.push(cities)
      paramIndex++
    }
    
    if (countries?.length) {
      whereConditions.push(`ic.country && $${paramIndex}::text[]`)
      queryParams.push(countries)
      paramIndex++
    }
    
    // Contact-specific filters (same as legacy)
    if (source?.length) {
      whereConditions.push(`c.source = ANY($${paramIndex}::text[])`)
      queryParams.push(source)
      paramIndex++
    }
    
    if (emailStatus?.length) {
      whereConditions.push(`c.email_status = ANY($${paramIndex}::text[])`)
      queryParams.push(emailStatus)
      paramIndex++
    }
    
    if (contactCompanyType?.length) {
      whereConditions.push(`c.company_type = ANY($${paramIndex}::text[])`)
      queryParams.push(contactCompanyType)
      paramIndex++
    }
    
    if (contactCapitalPosition?.length) {
      whereConditions.push(`c.capital_position && $${paramIndex}::text[]`)
      queryParams.push(contactCapitalPosition)
      paramIndex++
    }
    
    if (jobTier?.length) {
      whereConditions.push(`c.company_type = ANY($${paramIndex}::text[])`)
      queryParams.push(jobTier)
      paramIndex++
    }
    
    // Contact location filters
    if (contactCountries?.length) {
      whereConditions.push(`c.contact_country = ANY($${paramIndex}::text[])`)
      queryParams.push(contactCountries)
      paramIndex++
    }
    
    if (contactStates?.length) {
      whereConditions.push(`c.contact_state = ANY($${paramIndex}::text[])`)
      queryParams.push(contactStates)
      paramIndex++
    }
    
    if (contactCities?.length) {
      whereConditions.push(`c.contact_city = ANY($${paramIndex}::text[])`)
      queryParams.push(contactCities)
      paramIndex++
    }
    
    // Processing status filters (same as legacy)
    if (emailVerificationStatus?.length) {
      whereConditions.push(`c.email_verification_status = ANY($${paramIndex}::text[])`)
      queryParams.push(emailVerificationStatus)
      paramIndex++
    }
    

    
    if (contactEnrichmentV2Status?.length) {
      whereConditions.push(`c.contact_enrichment_v2_status = ANY($${paramIndex}::text[])`)
      queryParams.push(contactEnrichmentV2Status)
      paramIndex++
    }
    
    if (contactInvestmentCriteriaStatus?.length) {
      whereConditions.push(`c.contact_investment_criteria_status = ANY($${paramIndex}::text[])`)
      queryParams.push(contactInvestmentCriteriaStatus)
      paramIndex++
    }
    
    if (emailGenerationStatus?.length) {
      // Handle email generation status filter with support for 'not_started' (NULL values)
      const processedStatuses = emailGenerationStatus.map(status => 
        status === 'not_started' ? null : status
      );
      
      // Build condition that handles both NULL and non-NULL values
      const nullConditions: string[] = [];
      const nonNullConditions: string[] = [];
      
      processedStatuses.forEach((status, index) => {
        if (status === null) {
          nullConditions.push(`c.email_generation_status IS NULL`);
        } else {
          nonNullConditions.push(`c.email_generation_status = $${paramIndex + index}`);
        }
      });
      
      const allConditions = [...nullConditions, ...nonNullConditions];
      if (allConditions.length > 0) {
        whereConditions.push(`(${allConditions.join(' OR ')})`);
        // Add non-null values to params
        processedStatuses.filter(status => status !== null).forEach(status => {
          queryParams.push(status);
        });
        paramIndex += processedStatuses.filter(status => status !== null).length;
      }
    }
    
    if (emailSendingStatus?.length) {
      whereConditions.push(`c.email_sending_status = ANY($${paramIndex}::text[])`)
      queryParams.push(emailSendingStatus)
      paramIndex++
    }

    // Company processing status filters (from company table)
    if (companyWebsiteScrapingStatus?.length) {
      whereConditions.push(`co.website_scraping_status = ANY($${paramIndex}::text[])`)
      queryParams.push(companyWebsiteScrapingStatus)
      paramIndex++
    }

    if (companyOverviewV2Status?.length) {
      whereConditions.push(`co.overview_v2_status = ANY($${paramIndex}::text[])`)
      queryParams.push(companyOverviewV2Status)
      paramIndex++
    }

    if (companyInvestmentCriteriaStatus?.length) {
      whereConditions.push(`co.investment_criteria_status = ANY($${paramIndex}::text[])`)
      queryParams.push(companyInvestmentCriteriaStatus)
      paramIndex++
    }
    
    // Company investment criteria existence filter
    if (companyHasInvestmentCriteria !== undefined) {
      if (companyHasInvestmentCriteria) {
        whereConditions.push(`EXISTS (SELECT 1 FROM investment_criteria_central icc WHERE icc.entity_id = co.company_id AND icc.entity_type LIKE 'company%')`)
      } else {
        whereConditions.push(`NOT EXISTS (SELECT 1 FROM investment_criteria_central icc WHERE icc.entity_id = co.company_id AND icc.entity_type LIKE 'company%')`)
      }
    }
    
    // === V2 CONTACT ENRICHMENT FILTERS ===
    if (contactType?.length) {
      whereConditions.push(`c.contact_type = ANY($${paramIndex}::text[])`)
      queryParams.push(contactType)
      paramIndex++
    }
    
    if (relationshipOwner?.length) {
      whereConditions.push(`c.relationship_owner = ANY($${paramIndex}::text[])`)
      queryParams.push(relationshipOwner)
      paramIndex++
    }
    
    if (roleInDecisionMaking?.length) {
      whereConditions.push(`c.role_in_decision_making = ANY($${paramIndex}::text[])`)
      queryParams.push(roleInDecisionMaking)
      paramIndex++
    }
    
    if (sourceOfIntroduction?.length) {
      whereConditions.push(`c.source_of_introduction = ANY($${paramIndex}::text[])`)
      queryParams.push(sourceOfIntroduction)
      paramIndex++
    }
    
    // V2 Education filters
    if (educationCollege?.length) {
      whereConditions.push(`c.education_college = ANY($${paramIndex}::text[])`)
      queryParams.push(educationCollege)
      paramIndex++
    }
    
    if (educationCollegeYearGraduated?.length) {
      whereConditions.push(`c.education_college_year_graduated = ANY($${paramIndex}::text[])`)
      queryParams.push(educationCollegeYearGraduated)
      paramIndex++
    }
    

    
    // V2 Age range filter
    if (ageRange?.length) {
      whereConditions.push(`c.age = ANY($${paramIndex}::text[])`)
      queryParams.push(ageRange)
      paramIndex++
    }
    
    // V2 Boolean flags
    if (accreditedInvestorStatus !== null) {
      whereConditions.push(`c.accredited_investor_status = $${paramIndex}`)
      queryParams.push(accreditedInvestorStatus)
      paramIndex++
    }
    
    if (hasExecutiveSummary !== null) {
      if (hasExecutiveSummary) {
        whereConditions.push(`c.executive_summary IS NOT NULL AND c.executive_summary != ''`)
      } else {
        whereConditions.push(`(c.executive_summary IS NULL OR c.executive_summary = '')`)
      }
    }
    
    if (hasCareerTimeline !== null) {
      if (hasCareerTimeline) {
        whereConditions.push(`c.career_timeline IS NOT NULL AND c.career_timeline != ''`)
      } else {
        whereConditions.push(`(c.career_timeline IS NULL OR c.career_timeline = '')`)
      }
    }
    
    if (hasAdditionalEmail !== null) {
      if (hasAdditionalEmail) {
        whereConditions.push(`c.additional_email IS NOT NULL AND c.additional_email != ''`)
      } else {
        whereConditions.push(`(c.additional_email IS NULL OR c.additional_email = '')`)
      }
    }
    
    if (hasSecondaryPhone !== null) {
      if (hasSecondaryPhone) {
        whereConditions.push(`c.phone_number_secondary IS NOT NULL AND c.phone_number_secondary != ''`)
      } else {
        whereConditions.push(`(c.phone_number_secondary IS NULL OR c.phone_number_secondary = '')`)
      }
    }
    
    // V2 Social media filters
    if (hasTwitter !== null) {
      if (hasTwitter) {
        whereConditions.push(`c.twitter IS NOT NULL AND c.twitter != ''`)
      } else {
        whereConditions.push(`(c.twitter IS NULL OR c.twitter = '')`)
      }
    }
    
    if (hasFacebook !== null) {
      if (hasFacebook) {
        whereConditions.push(`c.facebook IS NOT NULL AND c.facebook != ''`)
      } else {
        whereConditions.push(`(c.facebook IS NULL OR c.facebook = '')`)
      }
    }
    
    if (hasInstagram !== null) {
      if (hasInstagram) {
        whereConditions.push(`c.instagram IS NOT NULL AND c.instagram != ''`)
      } else {
        whereConditions.push(`(c.instagram IS NULL OR c.instagram = '')`)
      }
    }
    
    if (hasYoutube !== null) {
      if (hasYoutube) {
        whereConditions.push(`c.youtube IS NOT NULL AND c.youtube != ''`)
      } else {
        whereConditions.push(`(c.youtube IS NULL OR c.youtube = '')`)
      }
    }
    
    // V2 Personal details
    if (hasHonorableAchievements !== null) {
      if (hasHonorableAchievements) {
        whereConditions.push(`c.honorable_achievements IS NOT NULL AND c.honorable_achievements != ''`)
      } else {
        whereConditions.push(`(c.honorable_achievements IS NULL OR c.honorable_achievements = '')`)
      }
    }
    
    if (hasHobbies !== null) {
      if (hasHobbies) {
        whereConditions.push(`c.hobbies IS NOT NULL AND c.hobbies != ''`)
      } else {
        whereConditions.push(`(c.hobbies IS NULL OR c.hobbies = '')`)
      }
    }
    
    if (hasContactAddress !== null) {
      if (hasContactAddress) {
        whereConditions.push(`c.contact_address IS NOT NULL AND c.contact_address != ''`)
      } else {
        whereConditions.push(`(c.contact_address IS NULL OR c.contact_address = '')`)
      }
    }
    
    if (hasContactZipCode !== null) {
      if (hasContactZipCode) {
        whereConditions.push(`c.contact_zip_code IS NOT NULL AND c.contact_zip_code != ''`)
      } else {
        whereConditions.push(`(c.contact_zip_code IS NULL OR c.contact_zip_code = '')`)
      }
    }
    
    // Boolean flags (legacy)
    if (extracted !== null) {
      whereConditions.push(`c.extracted = $${paramIndex}`)
      queryParams.push(extracted)
      paramIndex++
    }
    
    if (searched !== null) {
      whereConditions.push(`c.searched = $${paramIndex}`)
      queryParams.push(searched)
      paramIndex++
    }
    
    if (emailGenerated !== null) {
      whereConditions.push(`c.email_generated = $${paramIndex}`)
      queryParams.push(emailGenerated)
      paramIndex++
    }
    
    if (enriched !== null) {
      whereConditions.push(`c.enriched = $${paramIndex}`)
      queryParams.push(enriched)
      paramIndex++
    }
    
    if (hasSmartleadId !== null) {
      if (hasSmartleadId) {
        whereConditions.push(`c.smartlead_lead_id IS NOT NULL`)
      } else {
        whereConditions.push(`c.smartlead_lead_id IS NULL`)
      }
    }
    
    // Gmail outreach filters
    if (hasBeenReachedOut !== null) {
      if (hasBeenReachedOut) {
        whereConditions.push(`gm.contact_id IS NOT NULL`)
      } else {
        whereConditions.push(`gm.contact_id IS NULL`)
      }
    }
    
    // NOT filters (V2 Enhanced) - Fixed to exclude NULL values
    if (notFilters.notCapitalPosition?.length) {
      whereConditions.push(`(ic.capital_position IS NOT NULL AND NOT (ic.capital_position = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notCapitalPosition)
      paramIndex++
    }
    
    if (notFilters.notPropertyTypes?.length) {
      whereConditions.push(`(ic.property_types IS NOT NULL AND NOT (ic.property_types && $${paramIndex}::text[]))`)
      queryParams.push(notFilters.notPropertyTypes)
      paramIndex++
    }
    
    if (notFilters.notStrategies?.length) {
      whereConditions.push(`(ic.strategies IS NOT NULL AND NOT (ic.strategies && $${paramIndex}::text[]))`)
      queryParams.push(notFilters.notStrategies)
      paramIndex++
    }
    
    if (notFilters.notSource?.length) {
      whereConditions.push(`(c.source IS NOT NULL AND NOT (c.source = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notSource)
      paramIndex++
    }
    
    if (notFilters.notEmailStatus?.length) {
      whereConditions.push(`(c.email_status IS NOT NULL AND NOT (c.email_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notEmailStatus)
      paramIndex++
    }
    
    // V2 NOT filters
    if (notFilters.notContactType?.length) {
      whereConditions.push(`(c.contact_type IS NOT NULL AND NOT (c.contact_type = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notContactType)
      paramIndex++
    }
    
    if (notFilters.notEmailVerificationStatus?.length) {
      whereConditions.push(`(c.email_verification_status IS NOT NULL AND NOT (c.email_verification_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notEmailVerificationStatus)
      paramIndex++
    }
    

    
    if (notFilters.notContactEnrichmentV2Status?.length) {
      whereConditions.push(`(c.contact_enrichment_v2_status IS NOT NULL AND NOT (c.contact_enrichment_v2_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notContactEnrichmentV2Status)
      paramIndex++
    }
    
    if (notFilters.notContactInvestmentCriteriaStatus?.length) {
      whereConditions.push(`(c.contact_investment_criteria_status IS NOT NULL AND NOT (c.contact_investment_criteria_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notContactInvestmentCriteriaStatus)
      paramIndex++
    }

    if (notFilters.notCompanyWebsiteScrapingStatus?.length) {
      whereConditions.push(`(co.website_scraping_status IS NOT NULL AND NOT (co.website_scraping_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notCompanyWebsiteScrapingStatus)
      paramIndex++
    }

    if (notFilters.notCompanyOverviewV2Status?.length) {
      whereConditions.push(`(co.overview_v2_status IS NOT NULL AND NOT (co.overview_v2_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notCompanyOverviewV2Status)
      paramIndex++
    }

    if (notFilters.notCompanyInvestmentCriteriaStatus?.length) {
      whereConditions.push(`(co.investment_criteria_status IS NOT NULL AND NOT (co.investment_criteria_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notCompanyInvestmentCriteriaStatus)
      paramIndex++
    }
    
    // Additional NOT filters for investment criteria fields
    if (notFilters.notLoanTypes?.length) {
      whereConditions.push(`(icd.loan_type IS NOT NULL AND NOT (icd.loan_type = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notLoanTypes)
      paramIndex++
    }
    
    if (notFilters.notStructuredLoanTranche?.length) {
      whereConditions.push(`(icd.structured_loan_tranche IS NOT NULL AND NOT (icd.structured_loan_tranche = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notStructuredLoanTranche)
      paramIndex++
    }
    
    if (notFilters.notLoanProgram?.length) {
      whereConditions.push(`(icd.loan_program IS NOT NULL AND NOT (icd.loan_program = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notLoanProgram)
      paramIndex++
    }
    
    if (notFilters.notRecourseLoan?.length) {
      whereConditions.push(`(icd.recourse_loan IS NOT NULL AND NOT (icd.recourse_loan = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notRecourseLoan)
      paramIndex++
    }
    
    if (notFilters.notEligibleBorrower?.length) {
      whereConditions.push(`(icd.eligible_borrower IS NOT NULL AND NOT (icd.eligible_borrower = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notEligibleBorrower)
      paramIndex++
    }
    
    if (notFilters.notLienPosition?.length) {
      whereConditions.push(`(icd.lien_position IS NOT NULL AND NOT (icd.lien_position = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notLienPosition)
      paramIndex++
    }
    
    if (notFilters.notOwnershipRequirement?.length) {
      whereConditions.push(`(ice.ownership_requirement IS NOT NULL AND NOT (ice.ownership_requirement = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notOwnershipRequirement)
      paramIndex++
    }
    
    if (notFilters.notRateType?.length) {
      whereConditions.push(`(icd.rate_type IS NOT NULL AND NOT (icd.rate_type = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notRateType)
      paramIndex++
    }
    
    if (notFilters.notAmortization?.length) {
      whereConditions.push(`(icd.amortization IS NOT NULL AND NOT (icd.amortization = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notAmortization)
      paramIndex++
    }
    
    // Status filters
    if (isActive !== null) {
      whereConditions.push(`ic.is_active = $${paramIndex}`)
      queryParams.push(isActive)
      paramIndex++
    }

    // Build conditional JOINs - OPTIMIZED to only join what we need
    let joinClauses = `
      FROM contacts c
      LEFT JOIN companies co ON c.company_id = co.company_id
    `
    
    // Determine which debt/equity tables we need based on filters
    const hasDebtFilters = !!(
      loanTypes?.length || loanProgram?.length || structuredLoanTranche?.length || 
      recourseLoan?.length || minLoanTerm !== null || maxLoanTerm !== null ||
      interestRateMin !== null || interestRateMax !== null ||
      loanToValueMin !== null || loanToValueMax !== null ||
      loanToCostMin !== null || loanToCostMax !== null ||
      minLoanDscr !== null || maxLoanDscr !== null ||
      eligibleBorrower?.length || lienPosition?.length || rateLock?.length ||
      rateType?.length || amortization?.length || loanTypeNormalized?.length ||
      loanMinDebtYield?.length || closingTimeMin !== null || closingTimeMax !== null ||
      loanOriginationMaxFeeMin !== null || loanOriginationMaxFeeMax !== null ||
      loanOriginationMinFeeMin !== null || loanOriginationMinFeeMax !== null ||
      loanExitMinFeeMin !== null || loanExitMinFeeMax !== null ||
      loanExitMaxFeeMin !== null || loanExitMaxFeeMax !== null ||
      loanInterestRateSofrMin !== null || loanInterestRateSofrMax !== null ||
      loanInterestRateWsjMin !== null || loanInterestRateWsjMax !== null ||
      loanInterestRatePrimeMin !== null || loanInterestRatePrimeMax !== null ||
      loanInterestRate3ytMin !== null || loanInterestRate3ytMax !== null ||
      loanInterestRate5ytMin !== null || loanInterestRate5ytMax !== null ||
      loanInterestRate10ytMin !== null || loanInterestRate10ytMax !== null ||
      loanInterestRate30ytMin !== null || loanInterestRate30ytMax !== null ||
      loanToValueMinMin !== null || loanToValueMinMax !== null ||
      loanToValueMaxMin !== null || loanToValueMaxMax !== null ||
      loanToCostMinMin !== null || loanToCostMinMax !== null ||
      loanToCostMaxMin !== null || loanToCostMaxMax !== null ||
      minLoanTermMin !== null || minLoanTermMax !== null ||
      maxLoanTermMin !== null || maxLoanTermMax !== null ||
      notFilters.notLoanTypes?.length || notFilters.notStructuredLoanTranche?.length || 
      notFilters.notLoanProgram?.length || notFilters.notRecourseLoan?.length ||
      notFilters.notEligibleBorrower?.length || notFilters.notLienPosition?.length ||
      notFilters.notRateType?.length || notFilters.notAmortization?.length
    )
    
    const hasEquityFilters = !!(
      targetReturnMin !== null || targetReturnMax !== null ||
      historicalIrrMin !== null || historicalIrrMax !== null ||
      historicalEmMin !== null || historicalEmMax !== null ||
      minHoldPeriod !== null || maxHoldPeriod !== null ||
      ownershipRequirement?.length ||
      minimumYieldOnCostMin !== null || minimumYieldOnCostMax !== null ||
      maxLeverageToleranceMin !== null || maxLeverageToleranceMax !== null ||
      notFilters.notOwnershipRequirement?.length
    )
    
    // Only join investment criteria if we need to filter by it OR if we need the criteria_id for display
    if (hasInvestmentCriteriaFilters) {
      joinClauses += `
        INNER JOIN (
          SELECT DISTINCT ON (entity_id) *
          FROM investment_criteria_central 
          WHERE entity_type = 'contact'
          ORDER BY entity_id, updated_at DESC
        ) ic ON c.contact_id = ic.entity_id
      `
      
      // Join debt table if needed
      if (hasDebtFilters) {
        joinClauses += `
          LEFT JOIN investment_criteria_debt icd ON ic.investment_criteria_id = icd.investment_criteria_id
        `
      }
      
      // Join equity table if needed  
      if (hasEquityFilters) {
        joinClauses += `
          LEFT JOIN investment_criteria_equity ice ON ic.investment_criteria_id = ice.investment_criteria_id
        `
      }
    } else {
      // Light LEFT JOIN to get just the investment_criteria_id for "Has IC" badge
      joinClauses += `
        LEFT JOIN (
          SELECT DISTINCT ON (entity_id) entity_id, investment_criteria_id
          FROM investment_criteria_central 
          WHERE entity_type = 'contact'
          ORDER BY entity_id, updated_at DESC
        ) ic ON c.contact_id = ic.entity_id
      `
    }
    
    // V2 enrichment data is stored directly in the contacts table, no JOIN needed
    
    // Only join gmail messages if we have gmail filters - HIGHLY OPTIMIZED
    if (hasGmailFilters) {
      joinClauses += `
        LEFT JOIN (
          SELECT DISTINCT contact_id
          FROM (
            SELECT unnest(recipients) as email
            FROM gmail_messages
          ) gm_emails
          INNER JOIN contacts c_inner ON (
            gm_emails.email = c_inner.email
          )
          WHERE c_inner.email IS NOT NULL
        ) gm ON c.contact_id = gm.contact_id
      `
    }
    
    // Build the final WHERE clause
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''
    
    // V2 Enhanced sorting - includes V2 enrichment fields
    const validSortFields = {
      'updated_at': 'c.updated_at',
      'created_at': 'c.created_at',
      'first_name': 'c.first_name',
      'last_name': 'c.last_name',
      'email': 'c.email',
      'title': 'c.title',
      'company_name': 'co.company_name',
      'email_verification_status': 'c.email_verification_status',
      'contact_enrichment_status': 'c.contact_enrichment_status',
      'contact_enrichment_v2_status': 'c.contact_enrichment_v2_status',
      'email_generation_status': 'c.email_generation_status',
      'processing_error_count': 'c.processing_error_count',
      'contact_type': 'c.contact_type',
      'relationship_owner': 'c.relationship_owner'
    }
    
    const defaultSortField = 'c.updated_at'
    const sortField = validSortFields[sortBy as keyof typeof validSortFields] || defaultSortField
    const orderClause = `ORDER BY ${sortField} ${sortOrder.toUpperCase()} NULLS LAST`
    
    // Build LIMIT/OFFSET clause - always add parameters if they exist (needed for count query)
    let limitString = ''
    let countQueryParams = [...queryParams] // Create a copy for count query
    let mainQueryParams = [...queryParams] // Create a copy for main query
    
    if (limit) {
      limitString = `LIMIT $${paramIndex} `
      mainQueryParams.push(limit)
      countQueryParams.push(limit)
      paramIndex++
      if (offset) {
        limitString += `OFFSET $${paramIndex}`
        mainQueryParams.push(offset)
        countQueryParams.push(offset)
        paramIndex++
      }
    }
    

    
    // Single unified query - no separate count needed since we have window functions
    
    // V2 ENHANCED SELECT - Includes V2 enrichment fields + Stats calculations
    const selectFields = `
      c.contact_id,
      c.first_name,
      c.last_name,
      c.full_name,
      c.title,
      c.email,
      c.contact_city,
      c.contact_state,
      c.contact_country,
      c.email_status,
      c.smartlead_lead_id,
      c.processing_error_count,
      c.conflict_status,
      c.email_verification_status,
      c.email_verification_error,
      c.contact_enrichment_status,
      c.contact_enrichment_error,
      c.contact_enrichment_v2_status,
      c.contact_investment_criteria_status,
      c.email_generation_status,
      c.email_generation_error,
      c.created_at,
      c.updated_at,
      
      -- Company data (minimal)
      c.company_id,
      co.company_name,
      
      -- Investment criteria indicator (just the ID for "Has IC" badge)
      ic.investment_criteria_id,
      
      -- V2 Enrichment data (enhanced)
      c.contact_type,
      c.relationship_owner,
      c.role_in_decision_making,
      c.source_of_introduction,
      c.accredited_investor_status,
      c.education_college,
      c.education_high_school,
      c.age,
      c.executive_summary,
      c.career_timeline,
      c.additional_email,
      c.phone_number_secondary,
      c.twitter,
      c.facebook,
      c.instagram,
      c.youtube,
      c.honorable_achievements,
      c.hobbies,
      c.contact_address,
      c.contact_zip_code
    `

    // Simple stats query - just count totals and statuses
    const statsQuery = `
      SELECT 
        COUNT(*) as total_contacts,
        COUNT(CASE WHEN c.email IS NOT NULL AND c.email != ' ' AND c.email != '' AND (COALESCE(c.email_verification_status, 'pending') = 'pending') THEN 1 END) as email_validation_pending,
        COUNT(CASE WHEN c.email_verification_status = 'running' THEN 1 END) as email_validation_running,
        COUNT(CASE WHEN c.email_verification_status = 'completed' THEN 1 END) as email_validation_completed,
        COUNT(CASE WHEN c.email_verification_status = 'failed' THEN 1 END) as email_validation_failed,
        COUNT(CASE WHEN c.email_verification_status = 'error' THEN 1 END) as email_validation_error,
        
        -- Contact Enrichment V2 Stage
        COUNT(CASE WHEN c.email_verification_status = 'completed' AND COALESCE(c.contact_enrichment_v2_status, 'pending') = 'pending' THEN 1 END) as contact_enrichment_v2_pending,
        COUNT(CASE WHEN c.email_verification_status = 'completed' AND c.contact_enrichment_v2_status = 'running' THEN 1 END) as contact_enrichment_v2_running,
        COUNT(CASE WHEN c.contact_enrichment_v2_status = 'completed' THEN 1 END) as contact_enrichment_v2_completed,
        COUNT(CASE WHEN c.contact_enrichment_v2_status = 'failed' THEN 1 END) as contact_enrichment_v2_failed,
        COUNT(CASE WHEN c.contact_enrichment_v2_status = 'error' THEN 1 END) as contact_enrichment_v2_error,
        
        -- Contact Investment Criteria Stage
        COUNT(CASE WHEN c.contact_enrichment_v2_status = 'completed' AND COALESCE(c.contact_investment_criteria_status, 'pending') = 'pending' THEN 1 END) as contact_investment_criteria_pending,
        COUNT(CASE WHEN c.contact_enrichment_v2_status = 'completed' AND c.contact_investment_criteria_status = 'running' THEN 1 END) as contact_investment_criteria_running,
        COUNT(CASE WHEN c.contact_enrichment_v2_status = 'completed' AND c.contact_investment_criteria_status = 'completed' THEN 1 END) as contact_investment_criteria_completed,
        COUNT(CASE WHEN c.contact_enrichment_v2_status = 'completed' AND c.contact_investment_criteria_status = 'failed' THEN 1 END) as contact_investment_criteria_failed,
        COUNT(CASE WHEN c.contact_enrichment_v2_status = 'completed' AND c.contact_investment_criteria_status = 'error' THEN 1 END) as contact_investment_criteria_error,
        
        -- Email Generation Stage
        COUNT(CASE WHEN c.contact_enrichment_v2_status = 'completed' AND COALESCE(c.email_generation_status, 'pending') = 'pending' THEN 1 END) as email_generation_pending,
        COUNT(CASE WHEN c.contact_enrichment_v2_status = 'completed' AND c.email_generation_status = 'running' THEN 1 END) as email_generation_running,
        COUNT(CASE WHEN c.contact_enrichment_v2_status = 'completed' AND c.email_generation_status = 'completed' THEN 1 END) as email_generation_completed,
        COUNT(CASE WHEN c.contact_enrichment_v2_status = 'completed' AND c.email_generation_status = 'failed' THEN 1 END) as email_generation_failed,
        COUNT(CASE WHEN c.contact_enrichment_v2_status = 'completed' AND c.email_generation_status = 'error' THEN 1 END) as email_generation_error
      ${joinClauses}
      ${whereClause}
    `

    // Main query with V2 enhanced fields
    const query = `
      SELECT DISTINCT
        ${selectFields}
        
      ${joinClauses}
      ${whereClause}
      ${orderClause}
      ${shouldReturnData ? limitString : ''}
    `

    console.log('[Contacts Unified API] Stats query:', statsQuery)
    console.log('[Contacts Unified API] Stats query params:', mainQueryParams)

    // Build final query parameters - use the appropriate parameter arrays
    const finalQueryParams = shouldReturnData ? mainQueryParams : queryParams
    
    // Log the final query for debugging
    console.log('[Contacts Unified API] Final query:', query)
    console.log('[Contacts Unified API] Query params:', finalQueryParams)
    
    // Execute queries based on stats mode
    let dataResult: any = null
    let statsResult: any = null
    
    if (shouldReturnData) {
      // Get both data and stats
      dataResult = await pool.query(query, mainQueryParams)
      statsResult = await pool.query(statsQuery, queryParams)
    } else {
      // Stats only mode
      statsResult = await pool.query(statsQuery, queryParams)
    }

    // Extract stats from first row
    const stats = statsResult && statsResult.rows.length > 0 ? {
      total_contacts: parseInt(statsResult.rows[0].total_contacts || '0'),
      email_validation_pending: parseInt(statsResult.rows[0].email_validation_pending || '0'),
      email_validation_running: parseInt(statsResult.rows[0].email_validation_running || '0'),
      email_validation_completed: parseInt(statsResult.rows[0].email_validation_completed || '0'),
      email_validation_failed: parseInt(statsResult.rows[0].email_validation_failed || '0'),
      email_validation_error: parseInt(statsResult.rows[0].email_validation_error || '0'),
      contact_enrichment_v2_pending: parseInt(statsResult.rows[0].contact_enrichment_v2_pending || '0'),
      contact_enrichment_v2_running: parseInt(statsResult.rows[0].contact_enrichment_v2_running || '0'),
      contact_enrichment_v2_completed: parseInt(statsResult.rows[0].contact_enrichment_v2_completed || '0'),
      contact_enrichment_v2_failed: parseInt(statsResult.rows[0].contact_enrichment_v2_failed || '0'),
      contact_enrichment_v2_error: parseInt(statsResult.rows[0].contact_enrichment_v2_error || '0'),
      contact_investment_criteria_pending: parseInt(statsResult.rows[0].contact_investment_criteria_pending || '0'),
      contact_investment_criteria_running: parseInt(statsResult.rows[0].contact_investment_criteria_running || '0'),
      contact_investment_criteria_completed: parseInt(statsResult.rows[0].contact_investment_criteria_completed || '0'),
      contact_investment_criteria_failed: parseInt(statsResult.rows[0].contact_investment_criteria_failed || '0'),
      contact_investment_criteria_error: parseInt(statsResult.rows[0].contact_investment_criteria_error || '0'),
      email_generation_pending: parseInt(statsResult.rows[0].email_generation_pending || '0'),
      email_generation_running: parseInt(statsResult.rows[0].email_generation_running || '0'),
      email_generation_completed: parseInt(statsResult.rows[0].email_generation_completed || '0'),
      email_generation_failed: parseInt(statsResult.rows[0].email_generation_failed || '0'),
      email_generation_error: parseInt(statsResult.rows[0].email_generation_error || '0')
    } : null
    
    const total = stats?.total_contacts || 0
    const totalPages = Math.ceil(total / limit)
    
    // Clean data rows (only if we have data)
    const cleanDataRows = shouldReturnData && dataResult ? dataResult.rows : null

    return NextResponse.json({
      success: true,
      data: cleanDataRows,
      stats: stats, // Always include stats when computed
      pagination: shouldReturnData ? {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      } : null,
      meta: {
        hasInvestmentCriteriaFilters,
        hasContactEnrichmentV2Filters,
        hasGmailFilters,
        joinedTables: {
          investment_criteria: hasInvestmentCriteriaFilters,
          contact_enrichment_v2: hasContactEnrichmentV2Filters,
          gmail_messages: hasGmailFilters
        },
        optimized: true, // Flag to indicate this is the optimized version
        fieldsReturned: 'v2_enhanced_for_card_display',
        version: 'v2',
        performance: {
          gmailJoinOptimized: true, // UNNEST + INNER JOIN instead of CROSS JOIN
          conditionalJoins: true, // Only join tables when filters are applied
          minimalSelect: true, // Only fetch fields needed for ContactCard
          v2EnrichmentSupport: true, // V2 enrichment data included
          recommendedIndexes: [
            'idx_investment_criteria_capital_position_gin',
            'idx_contacts_contact_type',
            'idx_contacts_relationship_owner',
            'idx_contacts_source',
            'idx_contacts_email_status',
            'idx_contacts_contact_enrichment_v2_status'
          ]
        }
      }
    })
    
  } catch (error) {
    console.error('Error in V2 unified contacts filters API:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch V2 unified contact data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
