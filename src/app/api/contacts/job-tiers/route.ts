import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

interface JobTierRow {
  job_tier: string;
  count: string;
}
export async function GET(request: NextRequest) {
  try {
    const query = `
      SELECT 
        company_type as job_tier,
        COUNT(*) as count
      FROM contacts 
      WHERE company_type IS NOT NULL 
      GROUP BY company_type 
      ORDER BY 
        CASE company_type
          WHEN 'Tier 1 - C-Suite / Founders' THEN 1
          WHEN 'Tier 2 - Senior Executives' THEN 2
          WHEN 'Tier 3 - Mid-Level Executives' THEN 3
          WHEN 'Tier 4 - Investment / Portfolio Managers' THEN 4
          WHEN 'Tier 5 - Analysts & Associates' THEN 5
          ELSE 6
        END
    `;

    const result = await pool.query(query);
    
    const jobTiers = result.rows.map((row: JobTierRow) => ({
      job_tier: row.job_tier,
      count: parseInt(row.count)
    }));

    return NextResponse.json(jobTiers);
  } catch (error) {
    console.error('Error fetching job tiers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch job tiers' },
      { status: 500 }
    );
  }
} 