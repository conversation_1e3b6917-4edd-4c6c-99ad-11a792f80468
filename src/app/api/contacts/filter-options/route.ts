import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const filterType = searchParams.get('type');
    
    console.log('🔍 Filter options API called with type:', filterType);
    
    let query = '';
    const params: any[] = [];
    
    switch (filterType) {
      case 'sources':
        query = 'SELECT DISTINCT source FROM contacts WHERE source IS NOT NULL ORDER BY source';
        break;
        
      case 'company_types':
        query = 'SELECT DISTINCT company_type FROM contacts WHERE company_type IS NOT NULL ORDER BY company_type';
        break;
        
      case 'contact_countries':
        query = 'SELECT DISTINCT contact_country FROM contacts WHERE contact_country IS NOT NULL ORDER BY contact_country';
        break;
        
      case 'contact_states':
        query = 'SELECT DISTINCT contact_state FROM contacts WHERE contact_state IS NOT NULL ORDER BY contact_state';
        break;
        
      case 'contact_cities':
        query = 'SELECT DISTINCT contact_city FROM contacts WHERE contact_city IS NOT NULL ORDER BY contact_city';
        break;
        
      case 'enrichment_company_types':
        query = 'SELECT DISTINCT company_type FROM contact_enrichment WHERE company_type IS NOT NULL ORDER BY company_type';
        break;
        
      case 'enrichment_capital_positions':
        query = `
          SELECT DISTINCT jsonb_array_elements_text(capital_positions) as capital_position 
          FROM contact_enrichment 
          WHERE capital_positions IS NOT NULL 
          AND capital_positions != '[]'
          ORDER BY capital_position
        `;
        break;
        
      case 'enrichment_statuses':
        query = 'SELECT DISTINCT status FROM contact_enrichment WHERE status IS NOT NULL ORDER BY status';
        break;
        
      case 'email_generation_statuses':
        query = `
          SELECT DISTINCT 
            CASE 
              WHEN email_generation_status IS NULL THEN 'not_started'
              ELSE email_generation_status 
            END as email_generation_status
          FROM contacts 
          ORDER BY email_generation_status
        `;
        break;
        
      case 'email_sending_statuses':
        query = 'SELECT DISTINCT email_sending_status FROM contacts WHERE email_sending_status IS NOT NULL ORDER BY email_sending_status';
        break;
        
      case 'email_verification_statuses':
        query = 'SELECT DISTINCT email_verification_status FROM contacts WHERE email_verification_status IS NOT NULL ORDER BY email_verification_status';
        break;
        
      case 'contact_enrichment_statuses':
        query = 'SELECT DISTINCT contact_enrichment_status FROM contacts WHERE contact_enrichment_status IS NOT NULL ORDER BY contact_enrichment_status';
        break;
        
      case 'job_tiers':
        query = 'SELECT DISTINCT job_tier FROM contacts WHERE job_tier IS NOT NULL AND job_tier != \'\' ORDER BY job_tier';
        break;
        
      case 'contact_types':
        query = 'SELECT DISTINCT contact_type FROM contacts WHERE contact_type IS NOT NULL AND contact_type != \'\' ORDER BY contact_type';
        break;
        
      case 'decision_making_roles':
        query = 'SELECT DISTINCT role_in_decision_making FROM contacts WHERE role_in_decision_making IS NOT NULL AND role_in_decision_making != \'\' ORDER BY role_in_decision_making';
        break;
        
      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid filter type. Supported types: sources, company_types, contact_countries, contact_states, contact_cities, enrichment_company_types, enrichment_capital_positions, enrichment_statuses, email_generation_statuses, email_sending_statuses, email_verification_statuses, contact_enrichment_statuses, job_tiers, contact_types, decision_making_roles'
        }, { status: 400 });
    }
    
    const result = await pool.query(query, params);
    
    return NextResponse.json({
      success: true,
      data: result.rows.map(row => {
        const value = Object.values(row)[0];
        return { value, label: value };
      })
    });
    
  } catch (error) {
    console.error('Error fetching filter options:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch filter options',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 