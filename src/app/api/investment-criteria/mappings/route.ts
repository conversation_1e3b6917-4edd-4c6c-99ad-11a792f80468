import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

interface HierarchicalMapping {
  [parent: string]: string[]  // parent -> array of children
}

interface MappingStructure {
  [type: string]: {
    parents: string[]     // All unique value_1 entries
    children: string[]    // All unique value_2 entries
    hierarchical: HierarchicalMapping  // parent -> children mapping
  }
}

export async function GET() {
  const client = await pool.connect()
  
  try {
    // Get all mappings from central_mapping table
    const mappingsQuery = `
      SELECT 
        type,
        level_1,
        level_2,
        value_1,
        value_2
      FROM central_mapping
      WHERE is_active = true
      ORDER BY type, value_1, value_2
    `
    
    const result = await client.query(mappingsQuery)
    
    // Process mappings into hierarchical structure
    const mappings: MappingStructure = {}
    
    result.rows.forEach(row => {
      const { type, level_1, level_2, value_1, value_2 } = row
      
      // Initialize structure for this type if it doesn't exist
      if (!mappings[type]) {
        mappings[type] = {
          parents: [],
          children: [],
          hierarchical: {}
        }
      }
      
      // Add value_1 (parents) to parents array
      if (value_1 && !mappings[type].parents.includes(value_1)) {
        mappings[type].parents.push(value_1)
      }
      
      // Add value_2 (children) to children array if it exists
      if (value_2 && !mappings[type].children.includes(value_2)) {
        mappings[type].children.push(value_2)
      }
      
      // Build hierarchical structure (parent -> children mapping)
      if (value_1) {
        // Initialize parent in hierarchical structure
        if (!mappings[type].hierarchical[value_1]) {
          mappings[type].hierarchical[value_1] = []
        }
        
        // Add child if it exists and isn't already there
        if (value_2 && !mappings[type].hierarchical[value_1].includes(value_2)) {
          mappings[type].hierarchical[value_1].push(value_2)
        }
      }
    })
    
    // Sort all arrays
    Object.keys(mappings).forEach(type => {
      mappings[type].parents.sort()
      mappings[type].children.sort()
      Object.keys(mappings[type].hierarchical).forEach(parent => {
        mappings[type].hierarchical[parent].sort()
      })
    })
    
    return NextResponse.json(mappings)
    
  } catch (error) {
    console.error('Error fetching investment criteria mappings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch mappings' },
      { status: 500 }
    )
  } finally {
    client.release()
  }
} 