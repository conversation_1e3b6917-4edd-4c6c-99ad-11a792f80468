import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse filters from query parameters
    const filters = {
      // Search functionality
      searchTerm: searchParams.get('searchTerm'),
      entityName: searchParams.get('entityName'),
      entityType: searchParams.get('entityType'),
      entityId: searchParams.get('entityId'),
      email: searchParams.get('email'),
      
      // Deal size
      dealSizeMin: searchParams.get('dealSizeMin'),
      dealSizeMax: searchParams.get('dealSizeMax'),
      
      // Hold periods - NEW CORRECTED PARAMETERS
      minHoldPeriod: searchParams.get('minHoldPeriod'),
      maxHoldPeriod: searchParams.get('maxHoldPeriod'),
      
      // Loan terms - NEW CORRECTED PARAMETERS  
      minLoanTerm: searchParams.get('minLoanTerm'),
      maxLoanTerm: searchParams.get('maxLoanTerm'),
      
      // DSCR - NEW CORRECTED PARAMETERS
      minLoanDscr: searchParams.get('minLoanDscr'),
      maxLoanDscr: searchParams.get('maxLoanDscr'),
      
      // Closing time - NEW CORRECTED PARAMETER
      closingTimeWeeks: searchParams.get('closingTimeWeeks'),
      
      // LTV/LTC - CORRECTED PARAMETER NAMES
      loanToValueMin: searchParams.get('loanToValueMin'),
      loanToValueMax: searchParams.get('loanToValueMax'),
      loanToCostMin: searchParams.get('loanToCostMin'),
      loanToCostMax: searchParams.get('loanToCostMax'),
      
      // Capital and loans
      capitalTypes: searchParams.get('capitalTypes')?.split(',').filter(Boolean) || [],
      loanTypes: searchParams.get('loanTypes')?.split(',').filter(Boolean) || [],
      
      // Property and geography
      propertyTypes: searchParams.get('propertyTypes')?.split(',').filter(Boolean) || [],
      propertySubcategories: searchParams.get('propertySubcategories')?.split(',').filter(Boolean) || [],
      countries: searchParams.get('countries')?.split(',').filter(Boolean) || [],
      regions: searchParams.get('regions')?.split(',').filter(Boolean) || [],
      states: searchParams.get('states')?.split(',').filter(Boolean) || [],
      cities: searchParams.get('cities')?.split(',').filter(Boolean) || [],
      
      // Pagination and sorting
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '25'),
      sortBy: searchParams.get('sortBy') || 'updated_at',
      sortOrder: searchParams.get('sortOrder') || 'desc'
    }

    // Build WHERE clause dynamically
    const whereConditions = ['ic.is_active = true']
    const queryParams: any[] = []
    let paramIndex = 1

    // Search functionality
    if (filters.searchTerm) {
      whereConditions.push(`(
        LOWER(CASE 
          WHEN ic.entity_type LIKE 'Company%' THEN c.company_name
          WHEN ic.entity_type = 'Contact' THEN CONCAT(p.first_name, ' ', p.last_name)
          ELSE ic.entity_id
        END) LIKE LOWER($${paramIndex}) 
        OR LOWER(ic.entity_type) LIKE LOWER($${paramIndex})
        OR LOWER(ic.entity_id) LIKE LOWER($${paramIndex})
      )`)
      queryParams.push(`%${filters.searchTerm}%`)
      paramIndex++
    }

    if (filters.entityName) {
      whereConditions.push(`(
        LOWER(CASE 
          WHEN ic.entity_type LIKE 'Company%' THEN c.company_name
          WHEN ic.entity_type = 'Contact' THEN CONCAT(p.first_name, ' ', p.last_name)
          ELSE ic.entity_id
        END) LIKE LOWER($${paramIndex})
      )`)
      queryParams.push(`%${filters.entityName}%`)
      paramIndex++
    }

    if (filters.entityType) {
      whereConditions.push(`LOWER(ic.entity_type) = LOWER($${paramIndex})`)
      queryParams.push(filters.entityType)
      paramIndex++
    }

    if (filters.entityId) {
      whereConditions.push(`LOWER(ic.entity_id) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.entityId}%`)
      paramIndex++
    }

    if (filters.email) {
      whereConditions.push(`(
        LOWER(ic.entity_name) LIKE LOWER($${paramIndex})
        OR LOWER(ic.extra_fields->>'email') = LOWER($${paramIndex + 1})
        OR LOWER(p.email) = LOWER($${paramIndex + 1})
      )`)
      queryParams.push(`%${filters.email}%`)
      queryParams.push(filters.email)
      paramIndex += 2
    }

    // Deal size filters
    if (filters.dealSizeMin) {
      whereConditions.push(`ic.minimum_deal_size >= $${paramIndex}`)
      queryParams.push(parseFloat(filters.dealSizeMin))
      paramIndex++
    }
    if (filters.dealSizeMax) {
      whereConditions.push(`ic.maximum_deal_size <= $${paramIndex}`)
      queryParams.push(parseFloat(filters.dealSizeMax))
      paramIndex++
    }

    // Capital types (mapped to capital_position in database)
    if (filters.capitalTypes.length > 0) {
      whereConditions.push(`ic.capital_position && $${paramIndex}::text[]`)
      queryParams.push(filters.capitalTypes)
      paramIndex++
    }

    // Loan types
    if (filters.loanTypes.length > 0) {
      whereConditions.push(`(ic.loan_type && $${paramIndex}::text[] OR ic.loan_type_normalized && $${paramIndex}::text[])`)
      queryParams.push(filters.loanTypes)
      paramIndex++
    }

    // Hold period filters - CORRECTED LOGIC
    if (filters.minHoldPeriod) {
      whereConditions.push(`ic.min_hold_period >= $${paramIndex}`)
      queryParams.push(parseInt(filters.minHoldPeriod))
      paramIndex++
    }
    if (filters.maxHoldPeriod) {
      whereConditions.push(`ic.max_hold_period <= $${paramIndex}`)
      queryParams.push(parseInt(filters.maxHoldPeriod))
      paramIndex++
    }

    // Loan term filters - CORRECTED LOGIC
    if (filters.minLoanTerm) {
      whereConditions.push(`ic.min_loan_term >= $${paramIndex}`)
      queryParams.push(parseInt(filters.minLoanTerm))
      paramIndex++
    }
    if (filters.maxLoanTerm) {
      whereConditions.push(`ic.max_loan_term <= $${paramIndex}`)
      queryParams.push(parseInt(filters.maxLoanTerm))
      paramIndex++
    }

    // DSCR filters - CORRECTED LOGIC
    if (filters.minLoanDscr) {
      whereConditions.push(`ic.min_loan_dscr >= $${paramIndex}`)
      queryParams.push(parseFloat(filters.minLoanDscr))
      paramIndex++
    }
    if (filters.maxLoanDscr) {
      whereConditions.push(`ic.max_loan_dscr <= $${paramIndex}`)
      queryParams.push(parseFloat(filters.maxLoanDscr))
      paramIndex++
    }



    // LTV filters - CORRECTED PARAMETER NAMES
    if (filters.loanToValueMin) {
      whereConditions.push(`ic.loan_to_value_min >= $${paramIndex}`)
      queryParams.push(parseFloat(filters.loanToValueMin))
      paramIndex++
    }
    if (filters.loanToValueMax) {
      whereConditions.push(`ic.loan_to_value_max <= $${paramIndex}`)
      queryParams.push(parseFloat(filters.loanToValueMax))
      paramIndex++
    }

    // LTC filters - CORRECTED PARAMETER NAMES
    if (filters.loanToCostMin) {
      whereConditions.push(`ic.loan_to_cost_min >= $${paramIndex}`)
      queryParams.push(parseFloat(filters.loanToCostMin))
      paramIndex++
    }
    if (filters.loanToCostMax) {
      whereConditions.push(`ic.loan_to_cost_max <= $${paramIndex}`)
      queryParams.push(parseFloat(filters.loanToCostMax))
      paramIndex++
    }

    // Property types
    if (filters.propertyTypes.length > 0) {
      whereConditions.push(`ic.property_types && $${paramIndex}::text[]`)
      queryParams.push(filters.propertyTypes)
      paramIndex++
    }

    // Property subcategories
    if (filters.propertySubcategories.length > 0) {
      whereConditions.push(`ic.property_sub_categories && $${paramIndex}::text[]`)
      queryParams.push(filters.propertySubcategories)
      paramIndex++
    }

    // Geographic filters
    if (filters.countries.length > 0) {
      whereConditions.push(`ic.country && $${paramIndex}::text[]`)
      queryParams.push(filters.countries)
      paramIndex++
    }
    if (filters.regions.length > 0) {
      whereConditions.push(`ic.region && $${paramIndex}::text[]`)
      queryParams.push(filters.regions)
      paramIndex++
    }
    if (filters.states.length > 0) {
      whereConditions.push(`ic.state && $${paramIndex}::text[]`)
      queryParams.push(filters.states)
      paramIndex++
    }
    if (filters.cities.length > 0) {
      whereConditions.push(`ic.city && $${paramIndex}::text[]`)
      queryParams.push(filters.cities)
      paramIndex++
    }

    // Build ORDER BY clause - comprehensive list of sortable fields
    const validSortFields = [
      // Date fields
       'updated_at',
      
      // Financial metrics
      'target_return', 'historical_irr', 'historical_em',
      
      // Deal size
      'minimum_deal_size', 'maximum_deal_size',
      
      // Loan terms
      'interest_rate', 'interest_rate_sofr', 'interest_rate_wsj', 'interest_rate_prime',
      'min_loan_term', 'max_loan_term',
      
      // LTV/LTC ratios
      'loan_to_value_min', 'loan_to_value_max', 'loan_to_cost_min', 'loan_to_cost_max',
      
      // Hold periods
      'min_hold_period', 'max_hold_period',
      
      // DSCR
      'min_loan_dscr', 'max_loan_dscr',
      
      // Fees
      'loan_origination_fee_min', 'loan_origination_fee_max',
      'loan_exit_fee_min', 'loan_exit_fee_max',
      
      // Timeline
      'closing_time_weeks'
    ]
    
    const sortField = validSortFields.includes(filters.sortBy) ? filters.sortBy : 'created_at'
    const sortDirection = filters.sortOrder === 'asc' ? 'ASC' : 'DESC'
    
    // Create ORDER BY clause that prioritizes non-null values first, then null values
    const orderByClause = `ORDER BY ic.${sortField} IS NULL, ic.${sortField} ${sortDirection}`

    // Calculate offset
    const offset = (filters.page - 1) * filters.limit

    // Main query with joins to get entity details
    const mainQuery = `
      SELECT 
        ic.*,
        CASE 
          WHEN ic.entity_type LIKE 'Company%' THEN c.company_name
          WHEN ic.entity_type = 'Contact' THEN CONCAT(p.first_name, ' ', p.last_name)
          ELSE ic.entity_id
        END as entity_name,
        CASE 
          WHEN ic.entity_type LIKE 'Company%' THEN c.company_city
          WHEN ic.entity_type = 'Contact' THEN CONCAT(p.contact_city, ', ', p.contact_state)
          ELSE NULL
        END as entity_location
      FROM investment_criteria ic
      LEFT JOIN companies c ON ic.entity_type LIKE 'Company%' AND ic.entity_id = c.company_id::text
      LEFT JOIN contacts p ON ic.entity_type = 'Contact' AND ic.entity_id = p.contact_id::text
      WHERE ${whereConditions.join(' AND ')}
      ${orderByClause}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    queryParams.push(filters.limit, offset)

    // Count query for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM investment_criteria ic
      LEFT JOIN companies c ON ic.entity_type LIKE 'Company%' AND ic.entity_id = c.company_id::text
      LEFT JOIN contacts p ON ic.entity_type = 'Contact' AND ic.entity_id = p.contact_id::text
      WHERE ${whereConditions.join(' AND ')}
    `

    const [dataResult, countResult] = await Promise.all([
      pool.query(mainQuery, queryParams),
      pool.query(countQuery, queryParams.slice(0, -2)) // Remove limit and offset for count
    ])

    const total = parseInt(countResult.rows[0].total)
    const totalPages = Math.ceil(total / filters.limit)

    return NextResponse.json({
      data: dataResult.rows,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total,
        totalPages,
        hasNext: filters.page < totalPages,
        hasPrev: filters.page > 1
      },
      filters: filters // Return applied filters for UI state
    })

  } catch (error) {
    console.error('Error fetching investment criteria:', error)
    return NextResponse.json(
      { error: 'Failed to fetch investment criteria' },
      { status: 500 }
    )
  }
} 

export async function POST(request: Request) {
  try {
    const body = await request.json()
    
    // Extract required fields
    const {
      entity_type,
      entity_id,
      entity_name,
      target_return,
      property_types,
      property_sub_categories,
      strategies,
      minimum_deal_size,
      maximum_deal_size,
      min_hold_period,
      max_hold_period,
      financial_products,
      historical_irr,
      historical_em,
      country,
      region,
      state,
      city,
      loan_program,
      loan_type,
      capital_position,
      capital_source,
      structured_loan_tranche,
      min_loan_term,
      max_loan_term,
      interest_rate,
      interest_rate_sofr,
      interest_rate_wsj,
      interest_rate_prime,
      loan_to_value_min,
      loan_to_value_max,
      loan_to_cost_min,
      loan_to_cost_max,
      loan_origination_fee_min,
      loan_origination_fee_max,
      loan_exit_fee_min,
      loan_exit_fee_max,
      min_loan_dscr,
      max_loan_dscr,
      recourse_loan,
      closing_time_weeks,
      notes,
      extra_fields
    } = body

    // Validate required fields
    if (!entity_type || !entity_id) {
      return NextResponse.json(
        { error: 'entity_type and entity_id are required' },
        { status: 400 }
      )
    }

    // Helper function to convert array to PostgreSQL array format
    const arrayToPostgresArray = (arr: string[] | null | undefined): string | null => {
      if (!arr || arr.length === 0) return null
      return `{${arr.map(item => `"${item.replace(/"/g, '\\"')}"`).join(',')}}`
    }

    // Build ARRAY constructor for financial_products (jsonb[] field) 
    const financialProductsArray = financial_products && financial_products.length > 0
      ? `{${financial_products.map((value: any) => `"${JSON.stringify(value).replace(/"/g, '\\"')}"`).join(',')}}`
      : null

    // Insert new investment criteria
    const insertQuery = `
      INSERT INTO investment_criteria (
        entity_type, entity_id, entity_name, target_return, property_types, property_sub_categories, strategies,
        minimum_deal_size, maximum_deal_size, min_hold_period, max_hold_period,
        financial_products, historical_irr, historical_em, country, region, state, city,
        loan_program, loan_type, capital_source, structured_loan_tranche,
        min_loan_term, max_loan_term, interest_rate, interest_rate_sofr, interest_rate_wsj,
        interest_rate_prime, loan_to_value_max, loan_to_cost_max, loan_origination_fee_max, loan_exit_fee_max,
        min_loan_dscr, max_loan_dscr, recourse_loan, extra_fields,
        capital_position, loan_type_normalized, loan_to_value_min, loan_to_cost_min, 
        loan_origination_fee_min, loan_exit_fee_min, closing_time_weeks, notes
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18,
        $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32,
        $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43, $44
      ) RETURNING criteria_id
    `

    const params = [
      entity_type, // entity_type
      entity_id.toString(), // entity_id
      entity_name, // entity_name
      target_return || null, // target_return
      arrayToPostgresArray(property_types), // property_types (PostgreSQL array)
      arrayToPostgresArray(property_sub_categories), // property_sub_categories (PostgreSQL array)
      arrayToPostgresArray(strategies), // strategies (PostgreSQL array)
      minimum_deal_size || null, // minimum_deal_size
      maximum_deal_size || null, // maximum_deal_size
      min_hold_period || null, // min_hold_period
      max_hold_period || null, // max_hold_period
      financialProductsArray, // financial_products (PostgreSQL array)
      historical_irr || null, // historical_irr
      historical_em || null, // historical_em
      arrayToPostgresArray(country), // country (PostgreSQL array)
      arrayToPostgresArray(region), // region (PostgreSQL array)
      arrayToPostgresArray(state), // state (PostgreSQL array)
      arrayToPostgresArray(city), // city (PostgreSQL array)
      arrayToPostgresArray(loan_program), // loan_program (PostgreSQL array)
      arrayToPostgresArray(loan_type), // loan_type (PostgreSQL array)
      capital_source || null, // capital_source (text field)
      arrayToPostgresArray(structured_loan_tranche), // structured_loan_tranche (PostgreSQL array)
      min_loan_term || null, // min_loan_term
      max_loan_term || null, // max_loan_term
      interest_rate || null, // interest_rate
      interest_rate_sofr || null, // interest_rate_sofr
      interest_rate_wsj || null, // interest_rate_wsj
      interest_rate_prime || null, // interest_rate_prime
      loan_to_value_max || null, // loan_to_value_max
      loan_to_cost_max || null, // loan_to_cost_max
      loan_origination_fee_max || null, // loan_origination_fee_max
      loan_exit_fee_max || null, // loan_exit_fee_max
      min_loan_dscr || null, // min_loan_dscr
      max_loan_dscr || null, // max_loan_dscr
      arrayToPostgresArray(recourse_loan), // recourse_loan (PostgreSQL array)
      extra_fields ? JSON.stringify(extra_fields) : null, // extra_fields (JSONB field)
      arrayToPostgresArray(capital_position), // capital_position (PostgreSQL array)
      arrayToPostgresArray(loan_type), // loan_type_normalized (using loan_type as normalized)
      loan_to_value_min || null, // loan_to_value_min
      loan_to_cost_min || null, // loan_to_cost_min
      loan_origination_fee_min || null, // loan_origination_fee_min
      loan_exit_fee_min || null, // loan_exit_fee_min
      closing_time_weeks || null, // closing_time_weeks
      notes || null // notes
    ]

    const result = await pool.query(insertQuery, params)
    const criteriaId = result.rows[0].criteria_id

    return NextResponse.json({
      success: true,
      criteria_id: criteriaId,
      message: 'Investment criteria created successfully'
    })

  } catch (error) {
    console.error('Error creating investment criteria:', error)
    return NextResponse.json(
      { error: 'Failed to create investment criteria' },
      { status: 500 }
    )
  }
} 