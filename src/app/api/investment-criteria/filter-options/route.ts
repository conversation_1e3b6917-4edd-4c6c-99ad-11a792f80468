import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const entityType = searchParams.get('entityType') || 'Contact'; // default to Contact (capitalized)
    
    const client = await pool.connect();
    
    try {
      // Query only the investment_criteria table based on entity_type
      const queries = await Promise.all([
        // Capital Positions from investment_criteria table
        client.query(`
          SELECT DISTINCT unnest(capital_position) as value
          FROM investment_criteria 
          WHERE entity_type = $1 
            AND capital_position IS NOT NULL 
            AND array_length(capital_position, 1) > 0
          ORDER BY value
        `, [entityType]),
        
        // Property Types from investment_criteria table
        client.query(`
          SELECT DISTINCT unnest(property_types) as value
          FROM investment_criteria 
          WHERE entity_type = $1 
            AND property_types IS NOT NULL 
            AND array_length(property_types, 1) > 0
          ORDER BY value
        `, [entityType]),
        
        // Property Subcategories from investment_criteria table
        client.query(`
          SELECT DISTINCT unnest(property_sub_categories) as value
          FROM investment_criteria 
          WHERE entity_type = $1 
            AND property_sub_categories IS NOT NULL 
            AND array_length(property_sub_categories, 1) > 0
          ORDER BY value
        `, [entityType]),
        
        // Strategies from investment_criteria table
        client.query(`
          SELECT DISTINCT unnest(strategies) as value
          FROM investment_criteria 
          WHERE entity_type = $1 
            AND strategies IS NOT NULL 
            AND array_length(strategies, 1) > 0
          ORDER BY value
        `, [entityType]),
        
        // Loan Types from investment_criteria table
        client.query(`
          SELECT DISTINCT unnest(loan_type) as value
          FROM investment_criteria 
          WHERE entity_type = $1 
            AND loan_type IS NOT NULL 
            AND array_length(loan_type, 1) > 0
          ORDER BY value
        `, [entityType]),
        
        // Structured Loan Tranches from investment_criteria table
        client.query(`
          SELECT DISTINCT unnest(structured_loan_tranche) as value
          FROM investment_criteria 
          WHERE entity_type = $1 
            AND structured_loan_tranche IS NOT NULL 
            AND array_length(structured_loan_tranche, 1) > 0
          ORDER BY value
        `, [entityType]),
        
        // Loan Programs from investment_criteria table
        client.query(`
          SELECT DISTINCT unnest(loan_program) as value
          FROM investment_criteria 
          WHERE entity_type = $1 
            AND loan_program IS NOT NULL 
            AND array_length(loan_program, 1) > 0
          ORDER BY value
        `, [entityType]),
        
        // Recourse Loans from investment_criteria table
        client.query(`
          SELECT DISTINCT unnest(recourse_loan) as value
          FROM investment_criteria 
          WHERE entity_type = $1 
            AND recourse_loan IS NOT NULL 
            AND array_length(recourse_loan, 1) > 0
          ORDER BY value
        `, [entityType]),
        
        // Countries from investment_criteria table
        client.query(`
          SELECT DISTINCT unnest(country) as value
          FROM investment_criteria 
          WHERE entity_type = $1 
            AND country IS NOT NULL 
            AND array_length(country, 1) > 0
          ORDER BY value
        `, [entityType]),
        
        // Regions from investment_criteria table
        client.query(`
          SELECT DISTINCT unnest(region) as value
          FROM investment_criteria 
          WHERE entity_type = $1 
            AND region IS NOT NULL 
            AND array_length(region, 1) > 0
          ORDER BY value
        `, [entityType]),
        
        // States from investment_criteria table
        client.query(`
          SELECT DISTINCT unnest(state) as value
          FROM investment_criteria 
          WHERE entity_type = $1 
            AND state IS NOT NULL 
            AND array_length(state, 1) > 0
          ORDER BY value
        `, [entityType]),
        
        // Cities from investment_criteria table
        client.query(`
          SELECT DISTINCT unnest(city) as value
          FROM investment_criteria 
          WHERE entity_type = $1 
            AND city IS NOT NULL 
            AND array_length(city, 1) > 0
          ORDER BY value
        `, [entityType])
      ]);

      const [
        capitalPositions,
        propertyTypes,
        propertySubcategories,
        strategies,
        loanTypes,
        structuredLoanTranches,
        loanPrograms,
        recourseLoans,
        countries,
        regions,
        states,
        cities
      ] = queries;

      return NextResponse.json({
        capitalPositions: capitalPositions.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        propertyTypes: propertyTypes.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        propertySubcategories: propertySubcategories.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        strategies: strategies.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        loanTypes: loanTypes.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        structuredLoanTranches: structuredLoanTranches.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        loanPrograms: loanPrograms.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        recourseLoans: recourseLoans.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        countries: countries.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        regions: regions.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        states: states.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        cities: cities.rows.map(row => ({
          value: row.value,
          label: row.value
        }))
      });
      
    } finally {
      client.release();
    }
    
  } catch (error) {
    console.error('Error fetching investment criteria filter options:', error);
    return NextResponse.json(
      { error: 'Failed to fetch investment criteria filter options' },
      { status: 500 }
    );
  }
}