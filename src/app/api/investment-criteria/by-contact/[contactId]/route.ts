import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ contactId: string }> }
) {
  try {
    const { contactId } = await params

    if (!contactId) {
      return NextResponse.json({ error: 'Contact ID is required' }, { status: 400 })
    }

    // First, try to find investment criteria directly linked to the contact
    const directQuery = `
      SELECT 
        ic.*,
        CASE 
          WHEN ic.entity_type = 'contact' THEN CONCAT(p.first_name, ' ', p.last_name)
          WHEN ic.entity_type = 'company' THEN c.company_name
          ELSE ic.entity_id::text
        END as entity_name,
        CASE 
          WHEN ic.entity_type = 'contact' THEN CONCAT(p.contact_city, ', ', p.contact_state)
          WHEN ic.entity_type = 'company' THEN CONCAT(c.company_city, ', ', c.company_state)
          ELSE NULL
        END as entity_location,
        -- Debt fields
        icd.loan_type,
        icd.loan_program,
        icd.min_loan_term,
        icd.max_loan_term,
        icd.loan_interest_rate,
        icd.loan_interest_rate_based_off_sofr,
        icd.loan_interest_rate_based_off_wsj,
        icd.loan_interest_rate_based_off_prime,
        icd.loan_interest_rate_based_off_3yt,
        icd.loan_interest_rate_based_off_5yt,
        icd.loan_interest_rate_based_off_10yt,
        icd.loan_interest_rate_based_off_30yt,
        icd.loan_to_value_min,
        icd.loan_to_value_max,
        icd.loan_to_cost_min,
        icd.loan_to_cost_max,
        icd.loan_origination_min_fee,
        icd.loan_origination_max_fee,
        icd.loan_exit_min_fee,
        icd.loan_exit_max_fee,
        icd.min_loan_dscr,
        icd.max_loan_dscr,
        icd.structured_loan_tranche,
        icd.recourse_loan,
        icd.closing_time,
        icd.debt_program_overview,
        icd.lien_position,
        icd.loan_min_debt_yield,
        icd.prepayment,
        icd.yield_maintenance,
        icd.amortization,
        icd.application_deposit,
        icd.good_faith_deposit,
        icd.future_facilities,
        icd.eligible_borrower,
        icd.occupancy_requirements,
        icd.rate_lock,
        icd.rate_type,
        icd.loan_type_normalized,
        icd.notes as debt_notes,
        -- Equity fields
        ice.target_return,
        ice.minimum_internal_rate_of_return,
        ice.min_hold_period_years,
        ice.max_hold_period_years,
        ice.minimum_yield_on_cost,
        ice.minimum_equity_multiple,
        ice.ownership_requirement,
        ice.equity_program_overview,
        ice.target_cash_on_cash_min,
        ice.attachment_point,
        ice.max_leverage_tolerance,
        ice.typical_closing_timeline_days,
        ice.proof_of_funds_requirement,
        ice.occupancy_requirements as equity_occupancy_requirements,
        ice.notes as equity_notes,
        ice.yield_on_cost,
        ice.target_return_irr_on_equity,
        ice.equity_multiple,
        ice.position_specific_irr,
        ice.position_specific_equity_multiple
      FROM investment_criteria_central ic
      LEFT JOIN contacts p ON ic.entity_type = 'contact' AND ic.entity_id = p.contact_id
      LEFT JOIN companies c ON ic.entity_type = 'company' AND ic.entity_id = c.company_id
      LEFT JOIN investment_criteria_debt icd ON ic.investment_criteria_debt_id = icd.investment_criteria_debt_id
      LEFT JOIN investment_criteria_equity ice ON ic.investment_criteria_equity_id = ice.investment_criteria_equity_id
      WHERE ic.entity_type = 'contact' AND ic.entity_id = $1
      ORDER BY ic.created_at DESC
    `
    
    const result = await pool.query(directQuery, [contactId])

    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Error fetching investment criteria by contact:', error)
    return NextResponse.json(
      { error: 'Failed to fetch investment criteria' },
      { status: 500 }
    )
  }
} 