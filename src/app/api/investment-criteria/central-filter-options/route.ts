import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const type = searchParams.get('type');
  
  try {
    console.log('🔍 Investment Criteria Central API called with type:', type);

    let data = {};

    switch (type) {
      case 'central':
        data = await fetchCentralOptions();
        break;
      case 'debt':
        data = await fetchDebtOptions();
        break;
      case 'equity':
        data = await fetchEquityOptions();
        break;
      case 'all':
        const [central, debt, equity] = await Promise.all([
          fetchCentralOptions(),
          fetchDebtOptions(),
          fetchEquityOptions()
        ]);
        data = { central, debt, equity };
        break;
      default:
        return NextResponse.json({ error: 'Invalid type parameter. Use: central, debt, equity, or all' }, { status: 400 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching investment criteria options:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch investment criteria options',
        details: error instanceof Error ? error.message : 'Unknown error',
        type: type
      },
      { status: 500 }
    );
  }
}

async function fetchCentralOptions() {
  const client = await pool.connect();
  try {
    console.log('🔍 fetchCentralOptions: Starting to fetch central investment criteria options');
    
    // Capital Positions
    const capitalPositionQuery = `
      SELECT DISTINCT capital_position as value, capital_position as label, COUNT(*) as count
      FROM investment_criteria_central 
      WHERE capital_position IS NOT NULL AND capital_position != ''
      GROUP BY capital_position
      ORDER BY count DESC, capital_position ASC
      LIMIT 50
    `;
    const capitalPositionResult = await client.query(capitalPositionQuery);
    console.log('🔍 fetchCentralOptions: Capital position query completed, rows:', capitalPositionResult.rows.length);

    // Countries (from array fields)
    const countriesQuery = `
      SELECT DISTINCT unnest(country) as value, unnest(country) as label, COUNT(*) as count
      FROM investment_criteria_central 
      WHERE country IS NOT NULL AND array_length(country, 1) > 0
      GROUP BY unnest(country)
      ORDER BY count DESC, value ASC
      LIMIT 100
    `;
    const countriesResult = await client.query(countriesQuery);
    console.log('🔍 fetchCentralOptions: Countries query completed, rows:', countriesResult.rows.length);

    // Regions (from array fields)
    const regionsQuery = `
      SELECT DISTINCT unnest(region) as value, unnest(region) as label, COUNT(*) as count
      FROM investment_criteria_central 
      WHERE region IS NOT NULL AND array_length(region, 1) > 0
      GROUP BY unnest(region)
      ORDER BY count DESC, value ASC
      LIMIT 100
    `;
    const regionsResult = await client.query(regionsQuery);

    // States (from array fields)
    const statesQuery = `
      SELECT DISTINCT unnest(state) as value, unnest(state) as label, COUNT(*) as count
      FROM investment_criteria_central 
      WHERE state IS NOT NULL AND array_length(state, 1) > 0
      GROUP BY unnest(state)
      ORDER BY count DESC, value ASC
      LIMIT 100
    `;
    const statesResult = await client.query(statesQuery);

    // Cities (from array fields)
    const citiesQuery = `
      SELECT DISTINCT unnest(city) as value, unnest(city) as label, COUNT(*) as count
      FROM investment_criteria_central 
      WHERE city IS NOT NULL AND array_length(city, 1) > 0
      GROUP BY unnest(city)
      ORDER BY count DESC, value ASC
      LIMIT 200
    `;
    const citiesResult = await client.query(citiesQuery);

    // Property Types (from array fields)
    const propertyTypesQuery = `
      SELECT DISTINCT unnest(property_types) as value, unnest(property_types) as label, COUNT(*) as count
      FROM investment_criteria_central 
      WHERE property_types IS NOT NULL AND array_length(property_types, 1) > 0
      GROUP BY unnest(property_types)
      ORDER BY count DESC, value ASC
      LIMIT 100
    `;
    const propertyTypesResult = await client.query(propertyTypesQuery);

    // Property Subcategories (from array fields)
    const propertySubcategoriesQuery = `
      SELECT DISTINCT unnest(property_subcategories) as value, unnest(property_subcategories) as label, COUNT(*) as count
      FROM investment_criteria_central 
      WHERE property_subcategories IS NOT NULL AND array_length(property_subcategories, 1) > 0
      GROUP BY unnest(property_subcategories)
      ORDER BY count DESC, value ASC
      LIMIT 200
    `;
    const propertySubcategoriesResult = await client.query(propertySubcategoriesQuery);

    // Strategies (from array fields)
    const strategiesQuery = `
      SELECT DISTINCT unnest(strategies) as value, unnest(strategies) as label, COUNT(*) as count
      FROM investment_criteria_central 
      WHERE strategies IS NOT NULL AND array_length(strategies, 1) > 0
      GROUP BY unnest(strategies)
      ORDER BY count DESC, value ASC
      LIMIT 100
    `;
    const strategiesResult = await client.query(strategiesQuery);

    // Entity Types
    const entityTypesQuery = `
      SELECT DISTINCT entity_type as value, entity_type as label, COUNT(*) as count
      FROM investment_criteria_central 
      WHERE entity_type IS NOT NULL AND entity_type != ''
      GROUP BY entity_type
      ORDER BY count DESC, entity_type ASC
    `;
    const entityTypesResult = await client.query(entityTypesQuery);

    // Deal Size Ranges (min/max statistics)
    const dealSizeStatsQuery = `
      SELECT 
        MIN(minimum_deal_size) as min_deal_size,
        MAX(maximum_deal_size) as max_deal_size,
        AVG(minimum_deal_size) as avg_min_deal_size,
        AVG(maximum_deal_size) as avg_max_deal_size,
        COUNT(*) as total_records
      FROM investment_criteria_central 
      WHERE minimum_deal_size IS NOT NULL OR maximum_deal_size IS NOT NULL
    `;
    const dealSizeStatsResult = await client.query(dealSizeStatsQuery);

    return {
      capitalPosition: capitalPositionResult.rows,
      countries: countriesResult.rows,
      regions: regionsResult.rows,
      states: statesResult.rows,
      cities: citiesResult.rows,
      propertyTypes: propertyTypesResult.rows,
      propertySubcategories: propertySubcategoriesResult.rows,
      strategies: strategiesResult.rows,
      entityTypes: entityTypesResult.rows,
      dealSizeStats: dealSizeStatsResult.rows[0] || {},
      totalRecords: await getTotalCentralRecords(client)
    };

  } catch (error) {
    console.error('🔍 fetchCentralOptions: Error:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function fetchDebtOptions() {
  const client = await pool.connect();
  try {
    console.log('🔍 fetchDebtOptions: Starting to fetch debt criteria options');
    
    // Loan Types
    const loanTypesQuery = `
      SELECT DISTINCT loan_type as value, loan_type as label, COUNT(*) as count
      FROM investment_criteria_debt 
      WHERE loan_type IS NOT NULL AND loan_type != ''
      GROUP BY loan_type
      ORDER BY count DESC, loan_type ASC
      LIMIT 50
    `;
    const loanTypesResult = await client.query(loanTypesQuery);

    // Loan Programs
    const loanProgramsQuery = `
      SELECT DISTINCT loan_program as value, loan_program as label, COUNT(*) as count
      FROM investment_criteria_debt 
      WHERE loan_program IS NOT NULL AND loan_program != ''
      GROUP BY loan_program
      ORDER BY count DESC, loan_program ASC
      LIMIT 50
    `;
    const loanProgramsResult = await client.query(loanProgramsQuery);

    // Structured Loan Tranches
    const structuredLoanTranchesQuery = `
      SELECT DISTINCT structured_loan_tranche as value, structured_loan_tranche as label, COUNT(*) as count
      FROM investment_criteria_debt 
      WHERE structured_loan_tranche IS NOT NULL AND structured_loan_tranche != ''
      GROUP BY structured_loan_tranche
      ORDER BY count DESC, structured_loan_tranche ASC
      LIMIT 20
    `;
    const structuredLoanTranchesResult = await client.query(structuredLoanTranchesQuery);

    // Recourse Loan Types
    const recourseLoanQuery = `
      SELECT DISTINCT recourse_loan as value, recourse_loan as label, COUNT(*) as count
      FROM investment_criteria_debt 
      WHERE recourse_loan IS NOT NULL AND recourse_loan != ''
      GROUP BY recourse_loan
      ORDER BY count DESC, recourse_loan ASC
      LIMIT 10
    `;
    const recourseLoanResult = await client.query(recourseLoanQuery);

    // Rate Types
    const rateTypesQuery = `
      SELECT DISTINCT rate_type as value, rate_type as label, COUNT(*) as count
      FROM investment_criteria_debt 
      WHERE rate_type IS NOT NULL AND rate_type != ''
      GROUP BY rate_type
      ORDER BY count DESC, rate_type ASC
      LIMIT 20
    `;
    const rateTypesResult = await client.query(rateTypesQuery);

    // Eligible Borrower Types
    const eligibleBorrowerQuery = `
      SELECT DISTINCT eligible_borrower as value, eligible_borrower as label, COUNT(*) as count
      FROM investment_criteria_debt 
      WHERE eligible_borrower IS NOT NULL AND eligible_borrower != ''
      GROUP BY eligible_borrower
      ORDER BY count DESC, eligible_borrower ASC
      LIMIT 30
    `;
    const eligibleBorrowerResult = await client.query(eligibleBorrowerQuery);

    // Occupancy Requirements
    const occupancyRequirementsQuery = `
      SELECT DISTINCT occupancy_requirements as value, occupancy_requirements as label, COUNT(*) as count
      FROM investment_criteria_debt 
      WHERE occupancy_requirements IS NOT NULL AND occupancy_requirements != ''
      GROUP BY occupancy_requirements
      ORDER BY count DESC, occupancy_requirements ASC
      LIMIT 20
    `;
    const occupancyRequirementsResult = await client.query(occupancyRequirementsQuery);

    // Lien Position
    const lienPositionQuery = `
      SELECT DISTINCT lien_position as value, lien_position as label, COUNT(*) as count
      FROM investment_criteria_debt 
      WHERE lien_position IS NOT NULL AND lien_position != ''
      GROUP BY lien_position
      ORDER BY count DESC, lien_position ASC
      LIMIT 10
    `;
    const lienPositionResult = await client.query(lienPositionQuery);

    // Numerical ranges stats
    const rangeStatsQuery = `
      SELECT 
        MIN(closing_time) as min_closing_time,
        MAX(closing_time) as max_closing_time,
        MIN(min_loan_term) as min_loan_term,
        MAX(max_loan_term) as max_loan_term,
        MIN(loan_to_value_min) as min_ltv,
        MAX(loan_to_value_max) as max_ltv,
        MIN(loan_to_cost_min) as min_ltc,
        MAX(loan_to_cost_max) as max_ltc,
        MIN(loan_interest_rate) as min_interest_rate,
        MAX(loan_interest_rate) as max_interest_rate,
        MIN(min_loan_dscr) as min_dscr,
        MAX(max_loan_dscr) as max_dscr,
        COUNT(*) as total_records
      FROM investment_criteria_debt
    `;
    const rangeStatsResult = await client.query(rangeStatsQuery);

    return {
      loanTypes: loanTypesResult.rows,
      loanPrograms: loanProgramsResult.rows,
      structuredLoanTranches: structuredLoanTranchesResult.rows,
      recourseLoans: recourseLoanResult.rows,
      rateTypes: rateTypesResult.rows,
      eligibleBorrowers: eligibleBorrowerResult.rows,
      occupancyRequirements: occupancyRequirementsResult.rows,
      lienPositions: lienPositionResult.rows,
      rangeStats: rangeStatsResult.rows[0] || {},
      totalRecords: await getTotalDebtRecords(client)
    };

  } catch (error) {
    console.error('🔍 fetchDebtOptions: Error:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function fetchEquityOptions() {
  const client = await pool.connect();
  try {
    console.log('🔍 fetchEquityOptions: Starting to fetch equity criteria options');
    
    // Ownership Requirements
    const ownershipRequirementsQuery = `
      SELECT DISTINCT ownership_requirement as value, ownership_requirement as label, COUNT(*) as count
      FROM investment_criteria_equity 
      WHERE ownership_requirement IS NOT NULL AND ownership_requirement != ''
      GROUP BY ownership_requirement
      ORDER BY count DESC, ownership_requirement ASC
      LIMIT 30
    `;
    const ownershipRequirementsResult = await client.query(ownershipRequirementsQuery);

    // Occupancy Requirements
    const occupancyRequirementsQuery = `
      SELECT DISTINCT occupancy_requirements as value, occupancy_requirements as label, COUNT(*) as count
      FROM investment_criteria_equity 
      WHERE occupancy_requirements IS NOT NULL AND occupancy_requirements != ''
      GROUP BY occupancy_requirements
      ORDER BY count DESC, occupancy_requirements ASC
      LIMIT 20
    `;
    const occupancyRequirementsResult = await client.query(occupancyRequirementsQuery);

    // Proof of Funds Requirements
    const proofOfFundsQuery = `
      SELECT 
        proof_of_funds_requirement,
        COUNT(*) as count
      FROM investment_criteria_equity 
      WHERE proof_of_funds_requirement IS NOT NULL
      GROUP BY proof_of_funds_requirement
      ORDER BY count DESC
    `;
    const proofOfFundsResult = await client.query(proofOfFundsQuery);

    // Numerical ranges stats
    const rangeStatsQuery = `
      SELECT 
        MIN(target_return) as min_target_return,
        MAX(target_return) as max_target_return,
        MIN(minimum_internal_rate_of_return) as min_irr,
        MAX(minimum_internal_rate_of_return) as max_irr,
        MIN(minimum_yield_on_cost) as min_yield_on_cost,
        MAX(minimum_yield_on_cost) as max_yield_on_cost,
        MIN(minimum_equity_multiple) as min_equity_multiple,
        MAX(minimum_equity_multiple) as max_equity_multiple,
        MIN(target_cash_on_cash_min) as min_cash_on_cash,
        MAX(target_cash_on_cash_min) as max_cash_on_cash,
        MIN(min_hold_period_years) as min_hold_period,
        MAX(max_hold_period_years) as max_hold_period,
        MIN(attachment_point) as min_attachment_point,
        MAX(attachment_point) as max_attachment_point,
        MIN(max_leverage_tolerance) as min_leverage_tolerance,
        MAX(max_leverage_tolerance) as max_leverage_tolerance,
        MIN(typical_closing_timeline_days) as min_closing_timeline,
        MAX(typical_closing_timeline_days) as max_closing_timeline,
        COUNT(*) as total_records
      FROM investment_criteria_equity
    `;
    const rangeStatsResult = await client.query(rangeStatsQuery);

    return {
      ownershipRequirements: ownershipRequirementsResult.rows,
      occupancyRequirements: occupancyRequirementsResult.rows,
      proofOfFundsRequirements: proofOfFundsResult.rows.map(row => ({
        value: row.proof_of_funds_requirement ? 'required' : 'not_required',
        label: row.proof_of_funds_requirement ? 'Required' : 'Not Required',
        count: row.count
      })),
      rangeStats: rangeStatsResult.rows[0] || {},
      totalRecords: await getTotalEquityRecords(client)
    };

  } catch (error) {
    console.error('🔍 fetchEquityOptions: Error:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function getTotalCentralRecords(client: any): Promise<number> {
  const result = await client.query('SELECT COUNT(*) as count FROM investment_criteria_central');
  return parseInt(result.rows[0].count);
}

async function getTotalDebtRecords(client: any): Promise<number> {
  const result = await client.query('SELECT COUNT(*) as count FROM investment_criteria_debt');
  return parseInt(result.rows[0].count);
}

async function getTotalEquityRecords(client: any): Promise<number> {
  const result = await client.query('SELECT COUNT(*) as count FROM investment_criteria_equity');
  return parseInt(result.rows[0].count);
}
