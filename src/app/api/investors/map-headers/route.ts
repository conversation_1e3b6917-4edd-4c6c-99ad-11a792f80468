import { NextRequest, NextResponse } from 'next/server'
import { PerplexityProvider } from '@/lib/llm/PerplexityProvider'
import { HEADER_MAPPING_SYSTEM_PROMPT, HEADER_MAPPING_USER_TEMPLATE } from '@/lib/prompts'
import { pool } from '@/lib/db'

interface HeaderMappingRequest {
  headers: string[]
  context?: string
}

interface HeaderMappingResponse {
  success: boolean
  company_mappings?: Record<string, string[]>
  contact_mappings?: Record<string, string[]>
  investment_criteria_central_mappings?: Record<string, string[]>
  investment_criteria_debt_mappings?: Record<string, string[]>
  investment_criteria_equity_mappings?: Record<string, string[]>
  unmapped_headers?: string[]
  suggestions?: {
    missing_recommended_fields: string[]
    data_quality_notes: string[]
    special_mappings_applied?: string[]
  }
  error?: string
}

/**
 * Custom parser for header mapping AI responses
 * Handles the specific structure and validation requirements for our header mapping use case
 */
function parseHeaderMappingResponse(content: string): any | null {
  try {
    console.log('[DEBUG] Header Mapping Parser: Starting to parse response')
    console.log('[DEBUG] Header Mapping Parser: Raw content length:', content.length)
    
    // Clean the content - remove any markdown formatting, code blocks, or extra text
    let cleanContent = content.trim()
    
    // Remove markdown code blocks if present
    cleanContent = cleanContent.replace(/^```json\s*/i, '').replace(/\s*```$/, '')
    cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '')
    
    // Find the JSON object - look for the first { and last }
    const firstBrace = cleanContent.indexOf('{')
    const lastBrace = cleanContent.lastIndexOf('}')
    
    if (firstBrace === -1 || lastBrace === -1 || firstBrace >= lastBrace) {
      console.error('[ERROR] Header Mapping Parser: No valid JSON object found in response')
      return null
    }
    
    // Extract just the JSON portion
    const jsonContent = cleanContent.substring(firstBrace, lastBrace + 1)
    console.log('[DEBUG] Header Mapping Parser: Extracted JSON content length:', jsonContent.length)
    
    // Remove any comments or extra text that might break JSON parsing
    const sanitizedContent = jsonContent
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove /* */ comments
      .replace(/\/\/.*$/gm, '') // Remove // comments
      .replace(/,(\s*[}\]])/g, '$1') // Remove trailing commas
    
    console.log('[DEBUG] Header Mapping Parser: Sanitized content length:', sanitizedContent.length)
    
    // Parse the JSON
    const parsed = JSON.parse(sanitizedContent)
    console.log('[DEBUG] Header Mapping Parser: Successfully parsed JSON')
    
    // Validate the structure
    if (!parsed || typeof parsed !== 'object') {
      console.error('[ERROR] Header Mapping Parser: Parsed result is not an object')
      return null
    }
    
    // Check for required top-level structure
    const hasValidStructure = (
      parsed.company_mappings !== undefined ||
      parsed.contact_mappings !== undefined ||
      parsed.investment_criteria_central_mappings !== undefined ||
      parsed.investment_criteria_debt_mappings !== undefined ||
      parsed.investment_criteria_equity_mappings !== undefined
    )
    
    if (!hasValidStructure) {
      console.error('[ERROR] Header Mapping Parser: Missing required mapping sections')
      console.error('[ERROR] Header Mapping Parser: Available keys:', Object.keys(parsed))
      return null
    }
    
    // Validate mapping structures
    const mappingSections = ['company_mappings', 'contact_mappings', 'investment_criteria_central_mappings', 'investment_criteria_debt_mappings', 'investment_criteria_equity_mappings']
    for (const section of mappingSections) {
      if (parsed[section] !== undefined) {
        if (typeof parsed[section] !== 'object' || Array.isArray(parsed[section])) {
          console.error(`[ERROR] Header Mapping Parser: ${section} is not a valid object`)
          return null
        }
        
        // Validate each mapping in the section
        for (const [dbField, headers] of Object.entries(parsed[section])) {
          if (!Array.isArray(headers)) {
            console.error(`[ERROR] Header Mapping Parser: ${section}.${dbField} headers is not an array:`, headers)
            return null
          }
          
          // Check that all headers are strings
          if (!headers.every(h => typeof h === 'string')) {
            console.error(`[ERROR] Header Mapping Parser: ${section}.${dbField} contains non-string headers:`, headers)
            return null
          }
        }
      }
    }
    
    // Validate unmapped_headers if present
    if (parsed.unmapped_headers !== undefined && !Array.isArray(parsed.unmapped_headers)) {
      console.error('[ERROR] Header Mapping Parser: unmapped_headers is not an array')
      return null
    }
    
    // Validate suggestions structure if present
    if (parsed.suggestions !== undefined) {
      if (typeof parsed.suggestions !== 'object' || Array.isArray(parsed.suggestions)) {
        console.error('[ERROR] Header Mapping Parser: suggestions is not a valid object')
        return null
      }
    }
    
    console.log('[DEBUG] Header Mapping Parser: All validations passed')
    
    // Log mapping statistics
    const companyCount = Object.keys(parsed.company_mappings || {}).length
    const contactCount = Object.keys(parsed.contact_mappings || {}).length
    const criteriaCentralCount = Object.keys(parsed.investment_criteria_central_mappings || {}).length
    const criteriaDebtCount = Object.keys(parsed.investment_criteria_debt_mappings || {}).length
    const criteriaEquityCount = Object.keys(parsed.investment_criteria_equity_mappings || {}).length
    const unmappedCount = (parsed.unmapped_headers || []).length
    
    console.log(`[INFO] Header Mapping Parser: Parsed ${companyCount} company, ${contactCount} contact, ${criteriaCentralCount} central criteria, ${criteriaDebtCount} debt criteria, ${criteriaEquityCount} equity criteria mappings with ${unmappedCount} unmapped headers`)
    
    return parsed
    
  } catch (error) {
    console.error('[ERROR] Header Mapping Parser: JSON parsing failed:', error)
    console.error('[ERROR] Header Mapping Parser: Attempted to parse:', content.substring(0, 500) + '...')
    return null
  }
}

export async function POST(request: NextRequest): Promise<NextResponse<HeaderMappingResponse>> {
  try {
    const body: HeaderMappingRequest = await request.json()
    const { headers, context } = body

    if (!headers || !Array.isArray(headers) || headers.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Headers array is required and cannot be empty'
      }, { status: 400 })
    }

    // Initialize Perplexity provider
    const llmProvider = new PerplexityProvider(
      {
        log: (level: string, message: string) => {
          console.log(`[${level.toUpperCase()}] Header Mapping: ${message}`)
        },
      },
      process.env.PERPLEXITY_API_KEY,
      {
        model: 'sonar', // Perplexity's latest model
        temperature: 0.3, // Lower temperature for consistent mapping
        maxTokens: 3000
      },
      true // Enable tracing
    )

    // Fetch the companies and contacts table columns
    const fieldsResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3030'}/api/investors/get-database-fields`)
    const fieldsData = await fieldsResponse.json()
    console.log('Fields Data:', fieldsData)
    
    if (!fieldsData.success) {
      throw new Error('Failed to fetch database fields')
    }

    // Prepare the prompt with dynamic fields
    const systemPrompt = HEADER_MAPPING_SYSTEM_PROMPT
      .replace('{companies_table_fields}', fieldsData.companies.map((field: string) => `- ${field}`).join('\n'))
      .replace('{contacts_table_fields}', fieldsData.contacts.map((field: string) => `- ${field}`).join('\n'))
      .replace('{investment_criteria_central_table_fields}', fieldsData.investment_criteria_central.map((field: string) => `- ${field}`).join('\n'))
      .replace('{investment_criteria_debt_table_fields}', fieldsData.investment_criteria_debt.map((field: string) => `- ${field}`).join('\n'))
      .replace('{investment_criteria_equity_table_fields}', fieldsData.investment_criteria_equity.map((field: string) => `- ${field}`).join('\n'))

    // Extract sample data from context if provided
    let sampleData = 'No sample data provided'
    let cleanContext = context || ''
    
    if (context && context.includes('Sample data:')) {
      const sampleMatch = context.match(/Sample data: (.+)$/)
      if (sampleMatch) {
        try {
          const parsedSample = JSON.parse(sampleMatch[1])
          sampleData = JSON.stringify(parsedSample, null, 2)
          cleanContext = context.replace(/Sample data: .+$/, '').trim()
        } catch (e) {
          sampleData = sampleMatch[1]
        }
      }
    }

    const userPrompt = HEADER_MAPPING_USER_TEMPLATE
      .replace('{headers}', JSON.stringify(headers, null, 2))
      .replace('{sample_data}', sampleData)

    // Add additional context if provided
    const finalPrompt = cleanContext 
      ? `${userPrompt}\n\n### Additional Context:\n${cleanContext}`
      : userPrompt

    console.log('Sending header mapping request to Perplexity...')
    console.log('Headers to map:', headers)

    // Call Perplexity with enhanced reasoning capabilities
    const response = await llmProvider.callLLM([
      { role: 'system', content: systemPrompt },
      { role: 'user', content: finalPrompt }
    ], {
      model: 'sonar', // Use Perplexity's reasoning model
      temperature: 0.3, // Low temperature for consistent mapping
      maxTokens: 3000
    })

    console.log('Perplexity Response:', response.content)

    // Parse the JSON response using our custom parser
    const mappingResult = parseHeaderMappingResponse(response.content)
    
    if (!mappingResult) {
      console.error('Failed to parse Perplexity response as valid header mapping JSON')
      console.error('Raw response:', response.content)
      return NextResponse.json({
        success: false,
        error: 'Failed to parse mapping response from AI. The response format was invalid. Please try again.'
      }, { status: 500 })
    }
    
    console.log('Mapping Result:', mappingResult)

    // Capture LLM metadata for storage
    const llmMetadata = {
      model: response.model || 'sonar',
      prompt_used: systemPrompt + '\n\n' + finalPrompt,
      input_provided: JSON.stringify(headers),
      output_received: response.content,
      tokens_consumed: response.usage?.totalTokens || 0,
      response_timestamp: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      database_fields: fieldsData, // Include the structured database fields
      headers: headers,
      company_mappings: mappingResult.company_mappings || {},
      contact_mappings: mappingResult.contact_mappings || {},
      investment_criteria_central_mappings: mappingResult.investment_criteria_central_mappings || {},
      investment_criteria_debt_mappings: mappingResult.investment_criteria_debt_mappings || {},
      investment_criteria_equity_mappings: mappingResult.investment_criteria_equity_mappings || {},
      unmapped_headers: mappingResult.unmapped_headers || [],
      suggestions: mappingResult.suggestions || {
        missing_recommended_fields: [],
        data_quality_notes: [],
        special_mappings_applied: []
      },
      llm_metadata: llmMetadata
    })

  } catch (error) {
    console.error('Header mapping error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 })
  }
}

export async function GET(): Promise<NextResponse> {
  return NextResponse.json({
    message: 'Header mapping endpoint. Use POST with headers array.',
    example: {
      headers: ['Company Name', 'Website', 'First Name', 'Last Name', 'Email'],
      context: 'Optional additional context about the data'
    }
  })
} 