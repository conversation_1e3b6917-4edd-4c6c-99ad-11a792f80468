import { NextRequest, NextResponse } from 'next/server';
import { AsyncUploadService } from '@/lib/services/AsyncUploadService';
import { pool } from '@/lib/db';

// GET /api/investors/upload-results/[uploadId] - Get upload results
export async function GET(
  request: NextRequest,
  { params }: { params: { uploadId: string } }
) {
  try {
    const uploadId = params.uploadId;

    if (!uploadId) {
      return NextResponse.json(
        { success: false, error: 'Upload ID is required' },
        { status: 400 }
      );
    }

    const uploadStatus = await AsyncUploadService.getUploadStatus(parseInt(uploadId));
    
    if (!uploadStatus) {
      return NextResponse.json(
        { success: false, error: 'Upload not found' },
        { status: 404 }
      );
    }

    // Get processing results from the database
    const processingResults = await getProcessingResults(parseInt(uploadId));

    // Get created records
    const createdRecords = await getCreatedRecords(parseInt(uploadId));

    // Get processing stats
    const processingStats = await getProcessingStats(parseInt(uploadId));

    const results = {
      upload: uploadStatus,
      records: createdRecords.investmentCriteria || [],
      errors: processingResults.errors || [],
      warnings: processingResults.warnings || [],
      stats: {
        companiesCreated: processingStats.companiesCreated,
        contactsCreated: processingStats.contactsCreated,
        investmentCriteriaCreated: processingStats.investmentCriteriaCreated,
        totalRowsProcessed: processingStats.totalRowsProcessed,
        errorRows: processingStats.errorRows,
        processingTimeMs: processingStats.processingTimeMs
      }
    };

    return NextResponse.json({
      success: true,
      ...results
    });

  } catch (error) {
    console.error('Error getting upload results:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get upload results',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper functions to get detailed results
async function getProcessingResults(uploadId: number): Promise<{
  errors: string[];
  warnings: string[];
}> {
  const client = await pool.connect();
  
  try {
    // Get processing logs/errors from upload_data_log or processing logs
    const logsQuery = `
      SELECT processing_result, error_message, warnings
      FROM upload_data_log 
      WHERE upload_log_id = $1
      AND (error_message IS NOT NULL OR warnings IS NOT NULL)
      ORDER BY row_number
    `;
    
    const result = await client.query(logsQuery, [uploadId]);
    
    const errors: string[] = [];
    const warnings: string[] = [];
    
    result.rows.forEach(row => {
      if (row.error_message) {
        errors.push(row.error_message);
      }
      if (row.warnings) {
        // Warnings might be JSON or string
        try {
          const warningArray = Array.isArray(row.warnings) ? row.warnings : JSON.parse(row.warnings);
          warnings.push(...warningArray);
        } catch {
          if (typeof row.warnings === 'string') {
            warnings.push(row.warnings);
          }
        }
      }
    });
    
    return { errors, warnings };
    
  } catch (error) {
    console.error('Error getting processing results:', error);
    return { errors: [], warnings: [] };
  } finally {
    client.release();
  }
}

async function getCreatedRecords(uploadId: number): Promise<{
  companies: any[];
  contacts: any[];
  investmentCriteria: any[];
}> {
  const client = await pool.connect();
  
  try {
    // Get recently created records that might be related to this upload
    // Since we don't have a direct link, we'll get records created around the upload time
    const uploadInfo = await client.query(
      'SELECT created_at, processing_started_at, processing_completed_at FROM upload_logs WHERE upload_id = $1',
      [uploadId]
    );
    
    if (uploadInfo.rows.length === 0) {
      return { companies: [], contacts: [], investmentCriteria: [] };
    }
    
    const uploadTime = uploadInfo.rows[0].processing_started_at || uploadInfo.rows[0].created_at;
    const completedTime = uploadInfo.rows[0].processing_completed_at || new Date();
    
    // Get investment criteria created during processing timeframe
    const icQuery = `
      SELECT criteria_id, entity_type, entity_id, capital_position, 
             minimum_deal_size, maximum_deal_size, property_types,
             country, region, state, city, created_at
      FROM investment_criteria_dummy 
      WHERE created_at BETWEEN $1 AND $2
      ORDER BY created_at DESC
      LIMIT 1000
    `;
    
    const icResult = await client.query(icQuery, [uploadTime, completedTime]);
    
    // Transform to legacy format for compatibility
    const investmentCriteria = icResult.rows.map(row => ({
      id: row.criteria_id.toString(),
      capitalPosition: Array.isArray(row.capital_position) ? row.capital_position.join(', ') : row.capital_position,
      dealSize: `${row.minimum_deal_size || ''} - ${row.maximum_deal_size || ''}`.trim().replace(/^-\s*$/, ''),
      recordType: 'investment_criteria' as const,
      location: {
        country: row.country,
        region: row.region,
        state: row.state,
        city: row.city
      },
      propertyType: {
        propertyTypes: row.property_types
      },
      dealSizeInfo: {
        minimumDealSize: row.minimum_deal_size,
        maximumDealSize: row.maximum_deal_size
      },
      notes: `Created from upload ${uploadId} on ${new Date(row.created_at).toLocaleString()}`
    }));
    
    return {
      companies: [], // Could implement similar logic for companies if needed
      contacts: [], // Could implement similar logic for contacts if needed
      investmentCriteria
    };
    
  } catch (error) {
    console.error('Error getting created records:', error);
    return { companies: [], contacts: [], investmentCriteria: [] };
  } finally {
    client.release();
  }
}

async function getProcessingStats(uploadId: number): Promise<{
  companiesCreated: number;
  contactsCreated: number;
  investmentCriteriaCreated: number;
  totalRowsProcessed: number;
  errorRows: number;
  processingTimeMs: number;
}> {
  const client = await pool.connect();
  
  try {
    // Get stats from upload progress or calculate from logs
    const uploadQuery = `
      SELECT total_rows, progress_percentage, processing_started_at, 
             processing_completed_at, progress
      FROM upload_logs 
      WHERE upload_id = $1
    `;
    
    const uploadResult = await client.query(uploadQuery, [uploadId]);
    
    if (uploadResult.rows.length === 0) {
      return {
        companiesCreated: 0,
        contactsCreated: 0, 
        investmentCriteriaCreated: 0,
        totalRowsProcessed: 0,
        errorRows: 0,
        processingTimeMs: 0
      };
    }
    
    const upload = uploadResult.rows[0];
    const progress = upload.progress || {};
    
    // Calculate processing time
    let processingTimeMs = 0;
    if (upload.processing_started_at && upload.processing_completed_at) {
      processingTimeMs = new Date(upload.processing_completed_at).getTime() - 
                        new Date(upload.processing_started_at).getTime();
    }
    
    // Count error rows
    const errorQuery = `
      SELECT COUNT(*) as error_count
      FROM upload_data_log 
      WHERE upload_log_id = $1 AND error_message IS NOT NULL
    `;
    
    const errorResult = await client.query(errorQuery, [uploadId]);
    const errorRows = parseInt(errorResult.rows[0]?.error_count || '0');
    
    return {
      companiesCreated: progress.companiesProcessed || 0,
      contactsCreated: progress.contactsProcessed || 0,
      investmentCriteriaCreated: progress.investmentCriteriaProcessed || 0,
      totalRowsProcessed: Math.floor((upload.total_rows * (upload.progress_percentage || 0)) / 100),
      errorRows,
      processingTimeMs
    };
    
  } catch (error) {
    console.error('Error getting processing stats:', error);
    return {
      companiesCreated: 0,
      contactsCreated: 0,
      investmentCriteriaCreated: 0,
      totalRowsProcessed: 0,
      errorRows: 0,
      processingTimeMs: 0
    };
  } finally {
    client.release();
  }
} 