import { NextRequest, NextResponse } from 'next/server';
import { AsyncUploadService } from '@/lib/services/AsyncUploadService';

// GET /api/investors/upload-status/[uploadId] - Check upload status
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ uploadId: string }> }
) {
  try {
    const { uploadId } = await params;

    if (!uploadId) {
      return NextResponse.json(
        { success: false, error: 'Upload ID is required' },
        { status: 400 }
      );
    }

    const uploadStatus = await AsyncUploadService.getUploadStatus(parseInt(uploadId));
    
    if (!uploadStatus) {
      return NextResponse.json(
        { success: false, error: 'Upload not found' },
        { status: 404 }
      );
    }

    // Enhanced status response
    return NextResponse.json({
      success: true,
      status: uploadStatus.status,
      progress: {
        percentage: uploadStatus.progress_percentage || 0,
        processedRows: uploadStatus.processed_until || 0,
        totalRows: uploadStatus.total_rows || 0,
        currentPhase: uploadStatus.status
      },
      upload: {
        id: uploadStatus.upload_id,
        fileName: uploadStatus.file_name,
        createdAt: uploadStatus.created_at,
        processingStartedAt: uploadStatus.processing_started_at,
        processingCompletedAt: uploadStatus.processing_completed_at
      },
      stats: {
        companiesProcessed: uploadStatus.companies_processed || 0,
        contactsProcessed: uploadStatus.contacts_processed || 0,
        conflictsDetected: uploadStatus.conflicts_detected || 0
      },
      errorMessage: uploadStatus.error_message
    });

  } catch (error) {
    console.error('Error checking upload status:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to check upload status' },
      { status: 500 }
    );
  }
} 