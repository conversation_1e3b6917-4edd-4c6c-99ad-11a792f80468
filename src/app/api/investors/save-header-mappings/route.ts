import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

interface SaveHeaderMappingsRequest {
  upload_id: number
  header_mappings: {
    companies: Record<string, string[]>
    contacts: Record<string, string[]>
    investment_criteria_central: Record<string, string[]>
    investment_criteria_debt: Record<string, string[]>
    investment_criteria_equity: Record<string, string[]>
  }
  structured_mappings?: {
    company_mappings: Record<string, string[]>
    contact_mappings: Record<string, string[]>
    investment_criteria_central_mappings: Record<string, string[]>
    investment_criteria_debt_mappings: Record<string, string[]>
    investment_criteria_equity_mappings: Record<string, string[]>
    unmapped_headers: string[]
    database_fields: any
    suggestions?: any
  }
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body: SaveHeaderMappingsRequest = await request.json()
    const { upload_id, header_mappings, structured_mappings } = body

    if (!upload_id) {
      return NextResponse.json({
        success: false,
        error: 'Upload ID is required'
      }, { status: 400 })
    }

    if (!header_mappings) {
      return NextResponse.json({
        success: false,
        error: 'Header mappings are required'
      }, { status: 400 })
    }

    const client = await pool.connect()

    try {
      // Check if upload exists
      const uploadCheck = await client.query(
        'SELECT upload_id FROM upload_logs WHERE upload_id = $1',
        [upload_id]
      )

      if (uploadCheck.rows.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'Upload not found'
        }, { status: 404 })
      }

      // Prepare the data to save
      const dataToSave = {
        header_mappings,
        structured_mappings: structured_mappings || null,
        updated_at: new Date().toISOString()
      }

      // Update the upload_logs table with the new header mappings
      const result = await client.query(
        `UPDATE upload_logs 
         SET header_mappings = $1, 
             structured_mappings = $2,
             updated_at = $3
         WHERE upload_id = $4
         RETURNING upload_id`,
        [JSON.stringify(dataToSave.header_mappings), 
         structured_mappings ? JSON.stringify(dataToSave.structured_mappings) : null,
         dataToSave.updated_at,
         upload_id]
      )

      if (result.rows.length === 0) {
        throw new Error('Failed to update header mappings')
      }

      console.log(`[INFO] Header mappings saved for upload ${upload_id}`)

      return NextResponse.json({
        success: true,
        message: 'Header mappings saved successfully',
        upload_id: result.rows[0].upload_id
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('[ERROR] Failed to save header mappings:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 })
  }
}
