import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const uploadId = searchParams.get('uploadId');

    if (!uploadId) {
      return NextResponse.json(
        { success: false, error: 'Upload ID is required' },
        { status: 400 }
      );
    }

    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');

      // First check if upload exists
      const uploadCheck = await client.query(
        'SELECT upload_id, file_name, status FROM upload_logs WHERE upload_id = $1',
        [parseInt(uploadId)]
      );

      if (uploadCheck.rows.length === 0) {
        await client.query('ROLLBACK');
        return NextResponse.json(
          { success: false, error: 'Upload not found' },
          { status: 404 }
        );
      }

      const upload = uploadCheck.rows[0];

      // Don't allow deletion of currently processing uploads
      if (upload.status === 'processing') {
        await client.query('ROLLBACK');
        return NextResponse.json(
          { 
            success: false, 
            error: 'Cannot delete upload that is currently being processed' 
          },
          { status: 400 }
        );
      }

      // Delete associated data rows first (foreign key constraint)
      const dataDeleteResult = await client.query(
        'DELETE FROM upload_data_log WHERE upload_log_id = $1',
        [parseInt(uploadId)]
      );

      // Delete the upload record
      const uploadDeleteResult = await client.query(
        'DELETE FROM upload_logs WHERE upload_id = $1',
        [parseInt(uploadId)]
      );

      await client.query('COMMIT');

      return NextResponse.json({
        success: true,
        message: `Successfully deleted upload "${upload.file_name}"`,
        deletedRows: {
          upload: uploadDeleteResult.rowCount,
          data: dataDeleteResult.rowCount
        }
      });

    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Error deleting upload:', error);
      
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to delete upload: ' + (error instanceof Error ? error.message : 'Unknown error')
        },
        { status: 500 }
      );
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error in delete upload API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { uploadIds } = body;

    if (!uploadIds || !Array.isArray(uploadIds) || uploadIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Upload IDs array is required' },
        { status: 400 }
      );
    }

    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');

      let deletedCount = 0;
      const errors: string[] = [];

      for (const uploadId of uploadIds) {
        try {
          // Check if upload exists and is not processing
          const uploadCheck = await client.query(
            'SELECT upload_id, file_name, status FROM upload_logs WHERE upload_id = $1',
            [parseInt(uploadId)]
          );

          if (uploadCheck.rows.length === 0) {
            errors.push(`Upload ${uploadId}: Not found`);
            continue;
          }

          const upload = uploadCheck.rows[0];

          if (upload.status === 'processing') {
            errors.push(`Upload ${uploadId}: Cannot delete while processing`);
            continue;
          }

          // Delete associated data rows first
          await client.query(
            'DELETE FROM upload_data_log WHERE upload_log_id = $1',
            [parseInt(uploadId)]
          );

          // Delete the upload record
          await client.query(
            'DELETE FROM upload_logs WHERE upload_id = $1',
            [parseInt(uploadId)]
          );

          deletedCount++;

        } catch (error) {
          errors.push(`Upload ${uploadId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      await client.query('COMMIT');

      return NextResponse.json({
        success: true,
        message: `Successfully deleted ${deletedCount} upload(s)`,
        deletedCount,
        errors: errors.length > 0 ? errors : undefined
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error in bulk delete upload API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
} 