import { pool } from '@/lib/db'
import { NextRequest, NextResponse } from 'next/server'
import { ConflictData, ConflictUploadResult } from '@/types/conflict'
import { extractDomain, formatWebsite } from '@/lib/utils/domain'

interface CSVRow {
  'Company': string
  'Company Website': string
  'First Name': string
  'Last Name': string
  'Job Title': string
  'Email': string
  'LinkedIn URL': string
  'Company LinkedIn': string
  'Company Address': string
  'City': string
  'State': string
  'Zip': string
  'Country': string
  'Company Phone': string
  'Industry': string
  'Capital Type': string
  'Phone Number': string
  'Linked-In Profile': string
  [key: string]: string
}



function getMappedValue(row: any, dbField: string, headerMappings: Record<string, string>): string {
  const csvHeader = Object.keys(headerMappings).find(header => headerMappings[header] === dbField)
  return csvHeader && row[csvHeader] ? String(row[csvHeader]).trim() : ''
}

// Helper function to check if a field is an investment criteria field
function isInvestmentCriteriaField(fieldName: string): boolean {
  const investmentCriteriaFields = [
    // Location fields
    'country', 'region', 'state', 'city',
    
    // Deal size fields
    'minimum_deal_size', 'maximum_deal_size', 'target_return',
    
    // Property fields
    'property_types', 'property_sub_categories', 'strategies',
    
    // Hold period fields
    'min_hold_period', 'max_hold_period',
    
    // Financial products and metrics
    'financial_products', 'historical_irr', 'historical_em', 'capital_source',
    
    // Loan fields
    'loan_program', 'loan_type', 'structured_loan_tranche',
    'min_loan_term', 'max_loan_term',
    
    // Interest rate fields
    'interest_rate', 'interest_rate_sofr', 'interest_rate_wsj', 'interest_rate_prime',
    'interest_rate_libor', 'interest_rate_5yt', 'interest_rate_10yt',
    
    // Loan-to-value and loan-to-cost
    'loan_to_value_min', 'loan_to_value_max', 'loan_to_cost_min', 'loan_to_cost_max',
    
    // Fees
    'loan_origination_fee_min', 'loan_origination_fee_max',
    'loan_exit_fee_min', 'loan_exit_fee_max',
    
    // DSCR (Debt Service Coverage Ratio)
    'min_loan_dscr', 'max_loan_dscr',
    
    // Other loan terms
    'recourse_loan', 'closing_time_weeks',
    
    // Capital position
    'capital_position',
    
    // Extra fields
    'extra_fields',
    
    // Normalized fields
    'loan_type_normalized'
  ];
  
  return investmentCriteriaFields.includes(fieldName);
}

function detectCompanyConflicts(
  existingCompany: any,
  newData: CSVRow,
  source: string,
  headerMappings: Record<string, string>,
  matchReason?: string
): ConflictData {
  const conflicts: ConflictData = {}
  const timestamp = new Date().toISOString()

  // Get all company fields that have mappings, excluding investment criteria fields
  const companyFields = Object.values(headerMappings).filter(dbField => 
    (dbField.startsWith('company_') || dbField === 'industry') && !isInvestmentCriteriaField(dbField)
  )

  companyFields.forEach(dbField => {
    const existingValue = existingCompany[dbField]
    const newValue = getMappedValue(newData, dbField, headerMappings)
    
    const existingStr = existingValue ? String(existingValue).trim() : ''
    const newStr = newValue ? String(newValue).trim() : ''

    // Only create conflict if both values are non-empty AND different
    if (existingStr && newStr && existingStr !== newStr) {
      conflicts[dbField] = {
        existing_value: existingStr,
        new_value: newStr,
        source,
        created_at: timestamp,
        field_type: 'string',
        match_reason: matchReason || (
          dbField.includes('name') ? 'name' :
          dbField.includes('website') ? 'domain' :
          dbField.includes('phone') ? 'phone' :
          dbField.includes('address') ? 'address' :
          'field_comparison'
        )
      }
    }
  })

  return conflicts
}

function detectContactConflicts(
  existingContact: any,
  newData: CSVRow,
  source: string,
  headerMappings: Record<string, string>,
  matchReason?: string
): ConflictData {
  const conflicts: ConflictData = {}
  const timestamp = new Date().toISOString()

  // Get all contact fields that have mappings, excluding company and investment criteria fields
  const contactFields = Object.values(headerMappings).filter(dbField => 
    !dbField.startsWith('company_') && !['industry'].includes(dbField) && !isInvestmentCriteriaField(dbField)
  )

  contactFields.forEach(dbField => {
    const existingValue = existingContact[dbField]
    const newValue = getMappedValue(newData, dbField, headerMappings)
    
    const existingStr = existingValue ? String(existingValue).trim() : ''
    const newStr = newValue ? String(newValue).trim() : ''

    // NEW CONFLICT LOGIC:
    // - If DB is empty and CSV has value -> NOT a conflict (fill empty field)
    // - If CSV is empty and DB has value -> NOT a conflict (keep existing)
    // - If both are empty -> NOT a conflict
    // - If both have values and they're the same -> NOT a conflict
    // - If both have values and they're different -> CONFLICT
    
    // Only create conflict if both values are non-empty AND different
    if (existingStr && newStr && existingStr !== newStr) {
      conflicts[dbField] = {
        existing_value: existingStr,
        new_value: newStr,
        source,
        created_at: timestamp,
        field_type: 'string',
        match_reason: matchReason || (
          dbField.includes('email') ? 'email' :
          dbField.includes('linkedin') ? 'linkedin' :
          dbField.includes('name') ? 'name' :
          dbField.includes('phone') ? 'phone' :
          'field_comparison'
        )
      }
    }
  })

  return conflicts
}

// Add utility function for finding companies by domain
async function findCompaniesByDomain(client: any, domain: string): Promise<any[]> {
  if (!domain) return []
  
  const result = await client.query(`
    SELECT * FROM companies 
    WHERE company_website IS NOT NULL 
    AND company_website != ''
    AND (
      lower(company_website) LIKE $1 
      OR lower(company_website) LIKE $2
      OR lower(company_website) LIKE $3
      OR lower(company_website) LIKE $4
    )
  `, [
    `%${domain.toLowerCase()}%`,
    `%${domain.toLowerCase()}/%`,
    `https://${domain.toLowerCase()}%`,
    `http://${domain.toLowerCase()}%`
  ])
  
  // Filter results to exact domain matches
  return result.rows.filter((company: any) => {
    const companyDomain = extractDomain(company.company_website)
    return companyDomain.toLowerCase() === domain.toLowerCase()
  })
}

async function findCompanyByNameAndWebsite(client: any, companyName: string, website: string): Promise<any[]> {
  const results: any[] = []
  
  // 1. First try exact name match
  const nameResult = await client.query(
    'SELECT * FROM companies WHERE lower(company_name) = lower($1)',
    [companyName]
  )
  results.push(...nameResult.rows)
  
  // 2. If we have a website, try domain matching
  if (website) {
    const domain = extractDomain(website)
    if (domain) {
      const domainResults = await findCompaniesByDomain(client, domain)
      // Add domain matches that aren't already in results
      domainResults.forEach((company: any) => {
        if (!results.find((r: any) => r.company_id === company.company_id)) {
          results.push(company)
        }
      })
    }
  }
  
  return results
}

export async function POST(request: NextRequest) {
  try {
    const { data, headerMappings, source: requestSource } = await request.json()
    
    if (!data || !Array.isArray(data)) {
      return NextResponse.json(
        { error: 'Invalid data array provided' },
        { status: 400 }
      )
    }
    
    const csvData: CSVRow[] = data
    const source = requestSource || `Equity Investors Import ${new Date().toISOString().split('T')[0]}`
    const logs: string[] = []

    const stats = {
      companies: {
        total: 0,
        added: 0,
        updated_with_conflicts: 0,
        skipped: 0,
        error: 0
      },
      contacts: {
        total: 0,
        added: 0,
        updated_with_conflicts: 0,
        skipped: 0,
        error: 0
      }
    }

    const conflicts = {
      companies: [] as any[],
      contacts: [] as any[]
    }

    // Process each row
    for (const row of csvData) {
      const client = await pool.connect()
      
      try {
        await client.query('BEGIN')

        // Extract company data using mappings
        const companyName = getMappedValue(row, 'company_name', headerMappings) || ''
        const companyWebsite = getMappedValue(row, 'company_website', headerMappings) || ''
        
        if (!companyName) {
          logs.push('Warning: Skipping row with missing company name')
          await client.query('COMMIT')
          continue
        }

        stats.companies.total++

        // Find potential matching companies by name and domain
        const potentialMatches = await findCompanyByNameAndWebsite(client, companyName, companyWebsite)

        let companyId: number

        if (potentialMatches.length === 1) {
          // Single match found - proceed with conflict detection
          const existingCompany = potentialMatches[0]
          companyId = existingCompany.company_id

          // Determine how this company was matched
          const matchReason = existingCompany.company_name.toLowerCase() === companyName.toLowerCase() ? 'name' : 'domain'

          // Detect conflicts with match reason
          const companyConflicts = detectCompanyConflicts(existingCompany, row, source, headerMappings, matchReason)

          if (Object.keys(companyConflicts).length > 0) {
            // Update company with conflicts, including match_reason in extra_attrs
            const extraAttrs = existingCompany.extra_attrs || {}
            extraAttrs.match_reason = matchReason
            
            await client.query(`
              UPDATE companies 
              SET conflicts = $1::jsonb,
                  conflict_status = 'pending',
                  conflict_created_at = CURRENT_TIMESTAMP,
                  conflict_source = $2,
                  extra_attrs = $4::jsonb,
                  updated_at = CURRENT_TIMESTAMP
              WHERE company_id = $3
            `, [JSON.stringify(companyConflicts), source, companyId, JSON.stringify(extraAttrs)])

            conflicts.companies.push({
              company_id: companyId,
              company_name: existingCompany.company_name,
              conflicts: companyConflicts,
              type: 'company',
              match_reason: matchReason,
              company_website: existingCompany.company_website
            })

            stats.companies.updated_with_conflicts++
            logs.push(`Updated company '${existingCompany.company_name}' with ${Object.keys(companyConflicts).length} conflicts`)
          } else {
            // No conflicts - check if we can fill empty fields
            const fieldsToUpdate: string[] = []
            const valuesToUpdate: any[] = []
            let updateQuery = 'UPDATE companies SET '
            let paramIndex = 1

            // Get all company fields that have mappings
            const companyFields = Object.values(headerMappings).filter((dbField): dbField is string => 
              typeof dbField === 'string' && (dbField.startsWith('company_') || ['industry'].includes(dbField))
            )

            companyFields.forEach((dbField: string) => {
              const existingValue = existingCompany[dbField]
              const newValue = getMappedValue(row, dbField, headerMappings)
              
              // Special formatting for website
              const formattedNewValue = dbField === 'company_website' ? formatWebsite(newValue) : newValue

              const existingStr = existingValue ? String(existingValue).trim() : ''
              const newStr = formattedNewValue ? String(formattedNewValue).trim() : ''

              // Special handling for company website
              if (dbField === 'company_website') {
                // Fill empty field if CSV has value and DB is empty
                if (!existingStr && newStr) {
                  fieldsToUpdate.push(`${dbField} = $${paramIndex}`)
                  valuesToUpdate.push(newStr)
                  paramIndex++
                }
                // If both have values, check if we should update to a better format
                else if (existingStr && newStr) {
                  const existingDomain = extractDomain(existingStr)
                  const newDomain = extractDomain(newStr)
                  
                  // If same domain but new value is better formatted (has https, etc.)
                  if (existingDomain === newDomain && newStr.startsWith('https://') && !existingStr.startsWith('https://')) {
                    fieldsToUpdate.push(`${dbField} = $${paramIndex}`)
                    valuesToUpdate.push(newStr)
                    paramIndex++
                  }
                }
              } else {
                // Fill empty field if CSV has value and DB is empty
                if (!existingStr && newStr) {
                  fieldsToUpdate.push(`${dbField} = $${paramIndex}`)
                  valuesToUpdate.push(newStr)
                  paramIndex++
                }
              }
            })

            if (fieldsToUpdate.length > 0) {
              updateQuery += fieldsToUpdate.join(', ')
              updateQuery += `, updated_at = CURRENT_TIMESTAMP WHERE company_id = $${paramIndex}`
              valuesToUpdate.push(companyId)

              await client.query(updateQuery, valuesToUpdate)
              logs.push(`Updated company '${existingCompany.company_name}' - filled ${fieldsToUpdate.length} empty fields`)
              stats.companies.updated_with_conflicts++ // Using this counter for "updated" companies
            } else {
              stats.companies.skipped++
              logs.push(`Company '${existingCompany.company_name}' exists with no conflicts and no empty fields to fill - skipped`)
            }
          }
        } else if (potentialMatches.length > 1) {
          // Multiple matches found - create conflict for user to choose
          const conflictData = {
            multiple_company_matches: {
              existing_value: potentialMatches.map(company => ({
                company_id: company.company_id,
                company_name: company.company_name,
                company_website: company.company_website,
                match_reason: company.company_name.toLowerCase() === companyName.toLowerCase() ? 'name' : 'domain'
              })),
              new_value: {
                company_name: companyName,
                company_website: companyWebsite
              },
              source,
              created_at: new Date().toISOString(),
              field_type: 'company_selection'
            }
          }

          // Use the first match as the primary company for now, but mark it with conflicts
          const primaryCompany = potentialMatches[0]
          companyId = primaryCompany.company_id

          await client.query(`
            UPDATE companies 
            SET conflicts = $1::jsonb,
                conflict_status = 'pending',
                conflict_created_at = CURRENT_TIMESTAMP,
                conflict_source = $2,
                updated_at = CURRENT_TIMESTAMP
            WHERE company_id = $3
          `, [JSON.stringify(conflictData), source, companyId])

          conflicts.companies.push({
            company_id: companyId,
            company_name: primaryCompany.company_name,
            conflicts: conflictData,
            type: 'multiple_matches',
            potential_matches: potentialMatches
          })

          stats.companies.updated_with_conflicts++
          logs.push(`Multiple company matches found for '${companyName}' - created conflict for user selection (${potentialMatches.length} matches)`)
        } else {
          // No matches found - insert new company
          const companyData: any = {
            company_name: companyName,
            source,
            conflict_status: 'none'
          }
          
          // Get all company fields that have mappings and populate data
          const companyFields = Object.values(headerMappings).filter((dbField): dbField is string => 
            typeof dbField === 'string' && (dbField.startsWith('company_') || ['industry'].includes(dbField))
          )
          
          companyFields.forEach((dbField: string) => {
            const value = getMappedValue(row, dbField, headerMappings)
            if (value) {
              companyData[dbField] = dbField === 'company_website' ? formatWebsite(value) : value
            }
          })
          
          // Build dynamic insert query
          const fields = Object.keys(companyData)
          const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ')
          const values = fields.map(field => companyData[field])
          
          try {
            const insertResult = await client.query(`
              INSERT INTO companies (${fields.join(', ')}, created_at, updated_at)
              VALUES (${placeholders}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
              RETURNING company_id
            `, values)
            
            companyId = insertResult.rows[0].company_id
            stats.companies.added++
            logs.push(`Added new company '${companyName}' with ID ${companyId}`)
          } catch (error: any) {
            // Handle canonical handle constraint violations
            if (error.code === '23505' && error.constraint === 'companies_canonical_handle_uniq') {
              // Find the conflicting company by canonical handle
              const canonicalHandle = companyData.company_website ? 
                companyData.company_website.toLowerCase() : 
                companyName.toLowerCase()
              
              const existingCompanyResult = await client.query(`
                SELECT * FROM companies 
                WHERE canonical_handle = $1
              `, [canonicalHandle])
              
              if (existingCompanyResult.rows.length > 0) {
                const existingCompany = existingCompanyResult.rows[0]
                companyId = existingCompany.company_id
                
                // Determine match reason for canonical handle conflict
                const matchReason = companyData.company_website ? 'domain' : 'name'
                
                // Detect conflicts with the existing company
                const companyConflicts = detectCompanyConflicts(existingCompany, row, source, headerMappings, matchReason)
                
                if (Object.keys(companyConflicts).length > 0) {
                  // Update existing company with conflicts, including match_reason in extra_attrs
                  const extraAttrs = existingCompany.extra_attrs || {}
                  extraAttrs.match_reason = matchReason
                  
                  await client.query(`
                    UPDATE companies 
                    SET conflicts = $1::jsonb,
                        conflict_status = 'pending',
                        conflict_created_at = CURRENT_TIMESTAMP,
                        conflict_source = $2,
                        extra_attrs = $4::jsonb,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE company_id = $3
                  `, [JSON.stringify(companyConflicts), source, companyId, JSON.stringify(extraAttrs)])

                  conflicts.companies.push({
                    company_id: companyId,
                    company_name: existingCompany.company_name,
                    conflicts: companyConflicts,
                    type: 'company',
                    match_reason: matchReason,
                    company_website: existingCompany.company_website
                  })

                  stats.companies.updated_with_conflicts++
                  logs.push(`Canonical handle conflict - updated existing company '${existingCompany.company_name}' with ${Object.keys(companyConflicts).length} conflicts`)
                } else {
                  stats.companies.skipped++
                  logs.push(`Canonical handle conflict - company '${existingCompany.company_name}' already exists with same canonical handle`)
                }
              } else {
                throw new Error(`Canonical handle constraint violation but could not find conflicting company`)
              }
            } else {
              throw error // Re-throw other errors
            }
          }
        }

        // Process contact using dynamic mappings
        const firstName = getMappedValue(row, 'first_name', headerMappings) || ''
        const lastName = getMappedValue(row, 'last_name', headerMappings) || ''
        const fullName = `${firstName} ${lastName}`.trim()
        const email = getMappedValue(row, 'email', headerMappings) || ''
        const linkedinUrl = getMappedValue(row, 'linkedin_url', headerMappings) || ''
        
        if (fullName) {
          stats.contacts.total++

          // Enhanced contact matching: check by name, email, or LinkedIn URL
          let contactResult: any
          let matchReason: string = ''
          
          // First try to find by email (most reliable)
          if (email && email.includes('@')) {
            contactResult = await client.query(`
              SELECT * FROM contacts 
              WHERE email = $1
            `, [email])
            if (contactResult.rows.length > 0) {
              matchReason = 'email'
            }
          }
          
          // If no email match, try LinkedIn URL
          if ((!contactResult || contactResult.rows.length === 0) && linkedinUrl && linkedinUrl.includes('linkedin.com')) {
            contactResult = await client.query(`
              SELECT * FROM contacts 
              WHERE linkedin_url = $1
            `, [linkedinUrl])
            if (contactResult.rows.length > 0) {
              matchReason = 'linkedin'
            }
          }
          
          // If no email or LinkedIn match, try name + company
          if ((!contactResult || contactResult.rows.length === 0) && fullName) {
            contactResult = await client.query(`
              SELECT * FROM contacts 
              WHERE full_name = $1 AND company_id = $2
            `, [fullName, companyId])
            if (contactResult.rows.length > 0) {
              matchReason = 'name'
            }
          }

          if (contactResult.rows.length > 0) {
            const existingContact = contactResult.rows[0]
            
            // Detect conflicts with match reason
            const contactConflicts = detectContactConflicts(existingContact, row, source, headerMappings, matchReason)

            if (Object.keys(contactConflicts).length > 0) {
              // Update contact with conflicts, including match_reason in extra_attrs
              const extraAttrs = existingContact.extra_attrs || {}
              extraAttrs.match_reason = matchReason
              
              await client.query(`
                UPDATE contacts 
                SET conflicts = $1::jsonb,
                    conflict_status = 'pending',
                    conflict_created_at = CURRENT_TIMESTAMP,
                    conflict_source = $2,
                    extra_attrs = $4::jsonb,
                    updated_at = CURRENT_TIMESTAMP
                WHERE contact_id = $3
              `, [JSON.stringify(contactConflicts), source, existingContact.contact_id, JSON.stringify(extraAttrs)])

              conflicts.contacts.push({
                contact_id: existingContact.contact_id,
                full_name: fullName,
                company_name: companyName,
                conflicts: contactConflicts,
                type: 'contact',
                match_reason: matchReason,
                email: email || existingContact.email,
                linkedin_url: linkedinUrl || existingContact.linkedin_url
              })

              stats.contacts.updated_with_conflicts++
              logs.push(`Updated contact '${fullName}' with ${Object.keys(contactConflicts).length} conflicts`)
            } else {
              // No conflicts - check if we can fill empty fields
              const fieldsToUpdate: string[] = []
              const valuesToUpdate: any[] = []
              let updateQuery = 'UPDATE contacts SET '
              let paramIndex = 1

              // Get all contact fields that have mappings
              const contactFields = Object.values(headerMappings).filter((dbField): dbField is string => 
                typeof dbField === 'string' && (!dbField.startsWith('company_') && !['industry'].includes(dbField))
              )

              contactFields.forEach((dbField: string) => {
                const existingValue = existingContact[dbField]
                const newValue = getMappedValue(row, dbField, headerMappings)
                
                const existingStr = existingValue ? String(existingValue).trim() : ''
                const newStr = newValue ? String(newValue).trim() : ''

                // Fill empty field if CSV has value and DB is empty
                if (!existingStr && newStr) {
                  fieldsToUpdate.push(`${dbField} = $${paramIndex}`)
                  valuesToUpdate.push(newStr)
                  paramIndex++
                }
              })

              if (fieldsToUpdate.length > 0) {
                updateQuery += fieldsToUpdate.join(', ')
                updateQuery += `, updated_at = CURRENT_TIMESTAMP WHERE contact_id = $${paramIndex}`
                valuesToUpdate.push(existingContact.contact_id)

                await client.query(updateQuery, valuesToUpdate)
                logs.push(`Updated contact '${fullName}' - filled ${fieldsToUpdate.length} empty fields`)
                stats.contacts.updated_with_conflicts++ // Using this counter for "updated" contacts
            } else {
              stats.contacts.skipped++
                logs.push(`Contact '${fullName}' exists with no conflicts and no empty fields to fill - skipped`)
              }
            }
          } else {
            // Insert new contact using dynamic mappings
            const contactData: any = {
              company_id: companyId,
              first_name: firstName,
              last_name: lastName,
              full_name: fullName,
              source,
              conflict_status: 'none'
            }
            
            // Get all contact fields that have mappings and populate data
            const contactFields = Object.values(headerMappings).filter((dbField): dbField is string => 
              typeof dbField === 'string' && (!dbField.startsWith('company_') && !['industry'].includes(dbField))
            )
            
            contactFields.forEach((dbField: string) => {
              const value = getMappedValue(row, dbField, headerMappings)
              if (value && !['first_name', 'last_name'].includes(dbField)) { // Skip first/last name as already set
                contactData[dbField] = value
              }
            })
            
            // Build dynamic insert query
            const fields = Object.keys(contactData)
            const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ')
            const values = fields.map(field => contactData[field])
            
            try {
              await client.query(`
                INSERT INTO contacts (${fields.join(', ')}, created_at, updated_at)
                VALUES (${placeholders}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
              `, values)
            
            stats.contacts.added++
            logs.push(`Added new contact '${fullName}'`)
            } catch (contactError: any) {
              // Handle contact insertion errors gracefully
              console.error(`Error inserting contact '${fullName}':`, contactError)
              
              if (contactError.code === '23505') {
                // Unique constraint violation
                logs.push(`Contact insertion failed due to unique constraint: '${fullName}' - ${contactError.detail}`)
                stats.contacts.error++
              } else {
                // Other database errors
                logs.push(`Contact insertion failed: '${fullName}' - ${contactError.message}`)
                stats.contacts.error++
              }
            }
          }
        }

        await client.query('COMMIT')

      } catch (error) {
        await client.query('ROLLBACK')
        console.error(`Transaction error for company '${row['Company']}':`, error)
        logs.push(`Transaction error for company '${row['Company']}': ${error}`)
        stats.companies.error++
      } finally {
        client.release()
      }
    }

    const result: ConflictUploadResult = {
      success: true,
      stats,
      conflicts,
      logs: logs.slice(-50)
    }

    return NextResponse.json(result)

  } catch (error) {
    console.error('Upload processing error:', error)
    return NextResponse.json(
      { error: 'Failed to process CSV upload', details: error },
      { status: 500 }
    )
  }
} 