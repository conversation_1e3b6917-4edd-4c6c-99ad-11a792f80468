import { NextRequest, NextResponse } from 'next/server'
import AsyncUploadService from '@/lib/services/AsyncUploadService'

// Helper functions for enhanced status responses
function getStatusDescription(status: string): string {
  const descriptions: Record<string, string> = {
    'pending': 'Upload queued for processing',
    'processing': 'Currently processing your data',
    'completed': 'Upload completed successfully',
    'failed': 'Upload processing failed',
    'paused': 'Processing temporarily paused'
  }
  return descriptions[status] || 'Unknown status'
}

function getEstimatedTimeRemaining(uploadStatus: any): string | null {
  if (uploadStatus.status === 'completed' || uploadStatus.status === 'failed') {
    return null
  }
  
  const progress = uploadStatus.progress_percentage || 0
  if (progress === 0) return '2-5 minutes'
  if (progress < 25) return '3-4 minutes'
  if (progress < 50) return '2-3 minutes'
  if (progress < 75) return '1-2 minutes'
  if (progress < 95) return 'Almost done'
  return 'Finishing up...'
}

function getNextAction(uploadStatus: any): string {
  if (uploadStatus.status === 'completed') {
    return 'View processed data in dashboard'
  }
  if (uploadStatus.status === 'failed') {
    return 'Check error details and retry if needed'
  }
  if (uploadStatus.status === 'processing') {
    return 'Processing in progress - no action needed'
  }
  return 'Upload is being processed'
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const headerMappingsStr = formData.get('headerMappings') as string
    const structuredMappingsStr = formData.get('structuredMappings') as string | undefined
    const source = formData.get('source') as string | undefined
    const uploadedBy = formData.get('uploadedBy') as string | undefined
    console.log('file', file)
    console.log('headerMappingsStr', headerMappingsStr)
    console.log('structuredMappingsStr', structuredMappingsStr)
    console.log('source', source)
    console.log('uploadedBy', uploadedBy)
    // Parse LLM metadata if provided
    let llmMetadata
    try {
      const llmMetadataStr = formData.get('llmMetadata') as string
      if (llmMetadataStr) {
        llmMetadata = JSON.parse(llmMetadataStr)
      }
    } catch (error) {
      console.warn('Failed to parse LLM metadata:', error)
    }

    // Parse structured mappings if provided
    let structuredMappings
    try {
      if (structuredMappingsStr) {
        const parsed = JSON.parse(structuredMappingsStr)
        // Defensive: If the parsed object has a 'success' property, extract the mapping
        if (parsed && typeof parsed === 'object' && 'success' in parsed) {
          // Try to extract the actual mapping object from a known property
          structuredMappings = parsed.structuredMappings || parsed.data || null
        } else {
          structuredMappings = parsed
        }
      }
    } catch (error) {
      console.warn('Failed to parse structured mappings:', error)
    }

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    if (!headerMappingsStr) {
      return NextResponse.json(
        { error: 'No header mappings provided' },
        { status: 400 }
      )
    }

    let headerMappings: Record<string, string[]>
    try {
      headerMappings = JSON.parse(headerMappingsStr)
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid header mappings format' },
        { status: 400 }
      )
    }

    // Store upload for background processing
    const result = await AsyncUploadService.storeUploadForProcessing({
      file,
      headerMappings,
      structuredMappings,
      source,
      uploadedBy,
      llmMetadata
    })

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to store upload' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      upload_id: result.upload_id,
      message: result.message,
      status: 'pending',
      file_info: {
        name: result.storedFile?.originalName,
        size: result.storedFile?.size,
        type: result.storedFile?.fileType,
        path: result.storedFile?.filePath
      },
      processing: {
        estimated_processing_time: '2-5 minutes',
        next_step: 'File queued for background processing',
        status_check_url: `/api/investors/upload-async?upload_id=${result.upload_id}`
      },
      timestamps: {
        uploaded_at: new Date().toISOString(),
        estimated_completion: new Date(Date.now() + 5 * 60 * 1000).toISOString()
      }
    })

  } catch (error) {
    console.error('Upload processing error:', error)
    return NextResponse.json(
      { error: 'Failed to process upload', details: error },
      { status: 500 }
    )
  }
}

// GET endpoint to check upload status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const uploadId = searchParams.get('upload_id')

    if (!uploadId) {
      // Return all uploads if no specific ID requested
      const uploads = await AsyncUploadService.getAllUploads(50, 0)
      return NextResponse.json({ uploads })
    }

    const uploadStatus = await AsyncUploadService.getUploadStatus(parseInt(uploadId))
    
    if (!uploadStatus) {
      return NextResponse.json(
        { error: 'Upload not found' },
        { status: 404 }
      )
    }

    // Enhanced status response with more detailed information
    const enhancedStatus = {
      ...uploadStatus,
      progress_percentage: Math.min(100, Math.max(0, uploadStatus.progress_percentage || 0)),
      status_description: getStatusDescription(uploadStatus.status),
      is_complete: uploadStatus.status === 'completed',
      has_errors: uploadStatus.status === 'failed' || !!uploadStatus.error_message,
      estimated_time_remaining: getEstimatedTimeRemaining(uploadStatus),
      next_action: getNextAction(uploadStatus)
    }

    return NextResponse.json({ 
      success: true,
      upload: enhancedStatus 
    })

  } catch (error) {
    console.error('Error getting upload status:', error)
    return NextResponse.json(
      { error: 'Failed to get upload status' },
      { status: 500 }
    )
  }
} 