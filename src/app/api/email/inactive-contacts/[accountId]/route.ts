import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";
import { parse } from "json2csv";

// Common noreply patterns
const NOREPLY_PATTERNS = [
  /noreply/i,
  /no-reply/i,
  /donotreply/i,
  /do-not-reply/i,
  /no_reply/i,
  /auto@/i,
  /automated/i,
  /mailer-daemon/i,
  /bounce/i,
];

function isNoreply(email: string | null | undefined): boolean {
  if (!email) return true;
  return NOREPLY_PATTERNS.some((pat) => pat.test(email));
}

function monthsAgo(date: Date, months: number) {
  const d = new Date(date);
  d.setMonth(d.getMonth() - months);
  return d;
}

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ accountId: string }> }
) {
  try {
    const { accountId } = await context.params;
    const { searchParams } = new URL(req.url);
    const mode = searchParams.get("mode") || "all"; // all, sent, received
    const threeMonthsAgo = monthsAgo(new Date(), 3);

    // Fetch all messages for this account
    const { rows } = await pool.query(
      `SELECT sender, recipients, sent_at FROM gmail_messages WHERE account_id = $1 AND sent_at IS NOT NULL`,
      [accountId]
    );

    // Build communication map
    const contactMap: Record<string, { lastSent?: Date; lastReceived?: Date }> =
      {};
    for (const row of rows) {
      const sender = row.sender?.toLowerCase();
      const recipients: string[] = row.recipients || [];
      const sentAt = row.sent_at ? new Date(row.sent_at) : null;
      if (!sentAt) continue;
      // Sent: account is sender
      if (sender && !isNoreply(sender)) {
        if (!contactMap[sender]) contactMap[sender] = {};
        contactMap[sender].lastSent = contactMap[sender].lastSent
          ? new Date(
              Math.max(contactMap[sender].lastSent.getTime(), sentAt.getTime())
            )
          : sentAt;
      }
      // Received: account is recipient
      for (const rec of recipients) {
        const recLower = rec?.toLowerCase();
        if (!recLower || isNoreply(recLower)) continue;
        if (!contactMap[recLower]) contactMap[recLower] = {};
        contactMap[recLower].lastReceived = contactMap[recLower].lastReceived
          ? new Date(
              Math.max(
                contactMap[recLower].lastReceived.getTime(),
                sentAt.getTime()
              )
            )
          : sentAt;
      }
    }

    // Remove the account's own email from contacts
    const acctRes = await pool.query(
      `SELECT email FROM gmail_accounts WHERE id = $1`,
      [accountId]
    );
    const myEmail = acctRes.rows[0]?.email?.toLowerCase();
    if (myEmail) delete contactMap[myEmail];

    // Filter contacts based on mode
    const now = new Date();
    const results: {
      contact: string;
      lastSent?: string;
      lastReceived?: string;
    }[] = [];
    for (const [contact, info] of Object.entries(contactMap)) {
      if (isNoreply(contact)) continue;
      if (mode === "all") {
        const lastComm = [info.lastSent, info.lastReceived]
          .filter(Boolean)
          .sort((a, b) => (b as Date).getTime() - (a as Date).getTime())[0];
        if (!lastComm || lastComm < threeMonthsAgo) {
          results.push({
            contact,
            lastSent: info.lastSent?.toISOString() || "",
            lastReceived: info.lastReceived?.toISOString() || "",
          });
        }
      } else if (mode === "sent") {
        if (!info.lastSent || info.lastSent < threeMonthsAgo) {
          results.push({
            contact,
            lastSent: info.lastSent?.toISOString() || "",
            lastReceived: info.lastReceived?.toISOString() || "",
          });
        }
      } else if (mode === "received") {
        if (!info.lastReceived || info.lastReceived < threeMonthsAgo) {
          results.push({
            contact,
            lastSent: info.lastSent?.toISOString() || "",
            lastReceived: info.lastReceived?.toISOString() || "",
          });
        }
      }
    }

    // Generate CSV
    const csv = parse(results, {
      fields: ["contact", "lastSent", "lastReceived"],
    });
    return new NextResponse(csv, {
      status: 200,
      headers: {
        "Content-Type": "text/csv",
        "Content-Disposition": `attachment; filename=inactive_contacts_${mode}.csv`,
      },
    });
  } catch (error) {
    console.error("Error generating inactive contacts CSV:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
