import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';
import { 
  DashboardResponse, 
  ContactPipelineData, 
  CompanyPipelineData,
  StageDetails,
  StageMetrics,
  DashboardFilters,
  CONTACT_STAGE_MAPPING,
  COMPANY_STAGE_MAPPING
} from '@/types/dashboard';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse filters
    const filters: DashboardFilters = {
      dateRange: (searchParams.get('dateRange') as any) || '7d',
      source: searchParams.get('source') || undefined,
      region: searchParams.get('region') || undefined,
      capital_type: searchParams.get('capital_type') || undefined,
      stage: searchParams.get('stage') || undefined,
    };

    const client = await pool.connect();

    try {
      // Get date range in days
      const days = getDaysFromRange(filters.dateRange);
      
      // Build filter conditions
      const { contactFilters, companyFilters } = buildFilterConditions(filters);

      // Get contact pipeline data
      const contactData = await getContactPipelineData(client, contactFilters, days);
      
      // Get company pipeline data  
      const companyData = await getCompanyPipelineData(client, companyFilters, days);

      // Get performance metrics
      const performance = await getPerformanceMetrics(client, contactFilters, companyFilters, days);

      // Get error analysis
      const errors = await getErrorAnalysis(client, contactFilters, companyFilters, days);

      // Get timeline data
      const timeline = await getTimelineData(client, contactFilters, companyFilters, days);

      const response: DashboardResponse = {
        success: true,
        data: {
          contacts: contactData,
          companies: companyData,
          performance,
          errors,
          timeline
        },
        metadata: {
          generated_at: new Date().toISOString(),
          filters_applied: filters,
          data_freshness: 'real-time',
          next_refresh_at: new Date(Date.now() + 5 * 60 * 1000).toISOString() // 5 minutes
        }
      };

      return NextResponse.json(response);

    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Pipeline dashboard error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

function getDaysFromRange(range: string): number {
  switch (range) {
    case '1d': return 1;
    case '7d': return 7;
    case '30d': return 30;
    case '90d': return 90;
    default: return 7;
  }
}

function buildFilterConditions(filters: DashboardFilters) {
  const contactConditions: string[] = [];
  const companyConditions: string[] = [];
  const contactParams: any[] = [];
  const companyParams: any[] = [];
  let contactParamIndex = 1;
  let companyParamIndex = 1;

  // Source filter
  if (filters.source) {
    contactConditions.push(`c.source = $${contactParamIndex}`);
    contactParams.push(filters.source);
    contactParamIndex++;
    
    companyConditions.push(`c.source = $${companyParamIndex}`);
    companyParams.push(filters.source);
    companyParamIndex++;
  }

  // Region filter
  if (filters.region) {
    contactConditions.push(`c.region = $${contactParamIndex}`);
    contactParams.push(filters.region);
    contactParamIndex++;
  }

  // Capital type filter
  if (filters.capital_type) {
    contactConditions.push(`c.capital_type = $${contactParamIndex}`);
    contactParams.push(filters.capital_type);
    contactParamIndex++;
  }

  return {
    contactFilters: {
      whereClause: contactConditions.length > 0 ? 'AND ' + contactConditions.join(' AND ') : '',
      params: contactParams
    },
    companyFilters: {
      whereClause: companyConditions.length > 0 ? 'AND ' + companyConditions.join(' AND ') : '',
      params: companyParams
    }
  };
}

async function getContactPipelineData(client: any, filters: any, days: number): Promise<ContactPipelineData> {
  // Get overall contact metrics
  const overviewQuery = `
    SELECT 
      COUNT(*) as total_leads,
      COUNT(CASE WHEN processing_state != 'completed' AND processing_state != 'failed' THEN 1 END) as active_leads,
      COUNT(CASE WHEN processing_state = 'completed' THEN 1 END) as completed_leads,
      COUNT(CASE WHEN processing_state = 'failed' THEN 1 END) as failed_leads,
      CAST(
        COUNT(CASE WHEN processing_state = 'completed' THEN 1 END) * 100.0 /
        NULLIF(COUNT(*), 0) AS DECIMAL(10,2)
      ) as overall_conversion_rate,
      CAST(
        AVG(EXTRACT(EPOCH FROM (COALESCE(email_sending_date, NOW()) - created_at)) / 86400) AS DECIMAL(10,2)
      ) as avg_pipeline_time_days,
      COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '1 day' THEN 1 END) as daily_throughput
    FROM contacts c
    WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
    ${filters.whereClause}
  `;

  const overviewResult = await client.query(overviewQuery, filters.params);
  const overview = overviewResult.rows[0];

  // Get stage-wise metrics
  const stages = await getContactStageMetrics(client, filters, days);

  // Get funnel data
  const funnelData = await getContactFunnelData(client, filters, days);

  // Get recent activity
  const recentActivity = await getContactRecentActivity(client, filters);

  return {
    overview: {
      total_leads: parseInt(overview.total_leads || 0),
      active_leads: parseInt(overview.active_leads || 0),
      completed_leads: parseInt(overview.completed_leads || 0),
      failed_leads: parseInt(overview.failed_leads || 0),
      overall_conversion_rate: parseFloat(overview.overall_conversion_rate || 0),
      avg_pipeline_time_days: parseFloat(overview.avg_pipeline_time_days || 0),
      daily_throughput: parseInt(overview.daily_throughput || 0),
      health_score: calculateHealthScore(overview)
    },
    stages,
    funnel_data: funnelData,
    recent_activity: recentActivity
  };
}

function calculateHealthScore(overview: any): number {
  const conversionRate = parseFloat(overview.overall_conversion_rate || 0);
  const pipelineTime = parseFloat(overview.avg_pipeline_time_days || 0);
  const throughput = parseInt(overview.daily_throughput || 0);
  
  // Simple health score calculation (0-100)
  let score = 0;
  
  // Conversion rate component (40% weight)
  if (conversionRate >= 80) score += 40;
  else if (conversionRate >= 60) score += 30;
  else if (conversionRate >= 40) score += 20;
  else if (conversionRate >= 20) score += 10;
  
  // Pipeline time component (30% weight) - lower is better
  if (pipelineTime <= 1) score += 30;
  else if (pipelineTime <= 3) score += 25;
  else if (pipelineTime <= 7) score += 20;
  else if (pipelineTime <= 14) score += 10;
  
  // Throughput component (30% weight)
  if (throughput >= 100) score += 30;
  else if (throughput >= 50) score += 25;
  else if (throughput >= 20) score += 20;
  else if (throughput >= 10) score += 10;
  
  return Math.min(100, score);
}

async function getContactStageMetrics(client: any, filters: any, days: number): Promise<StageDetails[]> {
  const stageQueries = [
    {
      key: 'email_verification',
      query: `
        SELECT
          COUNT(CASE WHEN c.email IS NOT NULL AND c.email != '' THEN 1 END) as total,
          COUNT(CASE WHEN c.email_verification_status = 'pending' THEN 1 END) as pending,
          COUNT(CASE WHEN c.email_verification_status = 'running' THEN 1 END) as running,
          COUNT(CASE WHEN c.email_verification_status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN c.email_verification_status = 'failed' THEN 1 END) as failed,
          COUNT(CASE WHEN c.email_verification_status = 'error' THEN 1 END) as error,
          COUNT(CASE WHEN c.email_verification_date >= CURRENT_DATE - INTERVAL '1 day' THEN 1 END) as last_24h_completed,
          CAST(AVG(CASE WHEN c.email_verification_date IS NOT NULL
            THEN EXTRACT(EPOCH FROM (c.email_verification_date - c.created_at)) / 3600 END) AS DECIMAL(10,2)) as avg_processing_time_hours
        FROM contacts c
        WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
        AND c.email IS NOT NULL AND c.email != ''
        ${filters.whereClause}
      `
    },
    {
      key: 'osint',
      query: `
        SELECT
          COUNT(CASE WHEN c.email_verification_status = 'completed' THEN 1 END) as total,
          COUNT(CASE WHEN c.osint_status = 'pending' AND c.email_verification_status = 'completed' THEN 1 END) as pending,
          COUNT(CASE WHEN c.osint_status = 'running' THEN 1 END) as running,
          COUNT(CASE WHEN c.osint_status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN c.osint_status = 'failed' THEN 1 END) as failed,
          COUNT(CASE WHEN c.osint_status = 'error' THEN 1 END) as error,
          COUNT(CASE WHEN c.osint_date >= CURRENT_DATE - INTERVAL '1 day' THEN 1 END) as last_24h_completed,
          ROUND(AVG(CASE WHEN c.osint_date IS NOT NULL
            THEN EXTRACT(EPOCH FROM (c.osint_date - c.email_verification_date)) / 3600 END), 2) as avg_processing_time_hours
        FROM contacts c
        WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
        AND c.email_verification_status = 'completed'
        ${filters.whereClause}
      `
    },
    {
      key: 'overview_extraction',
      query: `
        SELECT
          COUNT(CASE WHEN c.osint_status = 'completed' THEN 1 END) as total,
          COUNT(CASE WHEN c.company_overview_status = 'pending' AND c.osint_status = 'completed' THEN 1 END) as pending,
          COUNT(CASE WHEN c.company_overview_status = 'running' THEN 1 END) as running,
          COUNT(CASE WHEN c.company_overview_status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN c.company_overview_status = 'failed' THEN 1 END) as failed,
          COUNT(CASE WHEN c.company_overview_status = 'error' THEN 1 END) as error,
          COUNT(CASE WHEN c.overview_extraction_date >= CURRENT_DATE - INTERVAL '1 day' THEN 1 END) as last_24h_completed,
          ROUND(AVG(CASE WHEN c.overview_extraction_date IS NOT NULL
            THEN EXTRACT(EPOCH FROM (c.overview_extraction_date - c.osint_date)) / 3600 END), 2) as avg_processing_time_hours
        FROM contacts c
        WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
        AND c.osint_status = 'completed'
        ${filters.whereClause}
      `
    },
    {
      key: 'classification',
      query: `
        SELECT
          COUNT(CASE WHEN c.company_overview_status = 'completed' THEN 1 END) as total,
          COUNT(CASE WHEN c.classification_status = 'pending' AND c.company_overview_status = 'completed' THEN 1 END) as pending,
          COUNT(CASE WHEN c.classification_status = 'running' THEN 1 END) as running,
          COUNT(CASE WHEN c.classification_status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN c.classification_status = 'failed' THEN 1 END) as failed,
          COUNT(CASE WHEN c.classification_status = 'error' THEN 1 END) as error,
          COUNT(CASE WHEN c.classification_date >= CURRENT_DATE - INTERVAL '1 day' THEN 1 END) as last_24h_completed,
          ROUND(AVG(CASE WHEN c.classification_date IS NOT NULL
            THEN EXTRACT(EPOCH FROM (c.classification_date - c.overview_extraction_date)) / 3600 END), 2) as avg_processing_time_hours
        FROM contacts c
        WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
        AND c.company_overview_status = 'completed'
        ${filters.whereClause}
      `
    },
    {
      key: 'email_generation',
      query: `
        SELECT
          COUNT(CASE WHEN c.classification_status = 'completed' THEN 1 END) as total,
          COUNT(CASE WHEN c.email_generation_status = 'pending' AND c.classification_status = 'completed' THEN 1 END) as pending,
          COUNT(CASE WHEN c.email_generation_status = 'running' THEN 1 END) as running,
          COUNT(CASE WHEN c.email_generation_status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN c.email_generation_status = 'failed' THEN 1 END) as failed,
          COUNT(CASE WHEN c.email_generation_status = 'error' THEN 1 END) as error,
          COUNT(CASE WHEN c.email_generation_date >= CURRENT_DATE - INTERVAL '1 day' THEN 1 END) as last_24h_completed,
          ROUND(AVG(CASE WHEN c.email_generation_date IS NOT NULL
            THEN EXTRACT(EPOCH FROM (c.email_generation_date - c.classification_date)) / 3600 END), 2) as avg_processing_time_hours
        FROM contacts c
        WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
        AND c.classification_status = 'completed'
        ${filters.whereClause}
      `
    },
    {
      key: 'email_sending',
      query: `
        SELECT
          COUNT(CASE WHEN c.email_generation_status = 'completed' THEN 1 END) as total,
          COUNT(CASE WHEN c.email_sending_status = 'pending' AND c.email_generation_status = 'completed' THEN 1 END) as pending,
          COUNT(CASE WHEN c.email_sending_status = 'running' THEN 1 END) as running,
          COUNT(CASE WHEN c.email_sending_status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN c.email_sending_status = 'failed' THEN 1 END) as failed,
          COUNT(CASE WHEN c.email_sending_status = 'error' THEN 1 END) as error,
          COUNT(CASE WHEN c.email_sending_date >= CURRENT_DATE - INTERVAL '1 day' THEN 1 END) as last_24h_completed,
          ROUND(AVG(CASE WHEN c.email_sending_date IS NOT NULL
            THEN EXTRACT(EPOCH FROM (c.email_sending_date - c.email_generation_date)) / 3600 END), 2) as avg_processing_time_hours
        FROM contacts c
        WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
        AND c.email_generation_status = 'completed'
        ${filters.whereClause}
      `
    }
  ];

  const stages: StageDetails[] = [];

  for (const stageQuery of stageQueries) {
    const result = await client.query(stageQuery.query, filters.params);
    const row = result.rows[0];

    const total = parseInt(row.total || 0);
    const completed = parseInt(row.completed || 0);
    const failed = parseInt(row.failed || 0);
    const error = parseInt(row.error || 0);

    const successRate = total > 0 ? ((completed / total) * 100) : 0;
    const conversionRate = total > 0 ? (((completed) / total) * 100) : 0;

    const stageMapping = CONTACT_STAGE_MAPPING[stageQuery.key as keyof typeof CONTACT_STAGE_MAPPING];

    stages.push({
      stage_name: stageQuery.key,
      stage_key: stageQuery.key,
      display_name: stageMapping.display_name,
      description: stageMapping.description,
      business_impact: stageMapping.business_impact,
      metrics: {
        total,
        pending: parseInt(row.pending || 0),
        running: parseInt(row.running || 0),
        completed,
        failed: parseInt(row.failed || 0),
        error: parseInt(row.error || 0),
        success_rate: parseFloat(successRate.toFixed(2)),
        avg_processing_time_hours: parseFloat(row.avg_processing_time_hours || 0),
        last_24h_completed: parseInt(row.last_24h_completed || 0),
        conversion_rate: parseFloat(conversionRate.toFixed(2))
      },
      prerequisites: getStagePrerequisites(stageQuery.key),
      next_stage: getNextStage(stageQuery.key),
      is_final_stage: stageQuery.key === 'email_sending'
    });
  }

  return stages;
}

function getStagePrerequisites(stageKey: string): string[] {
  const prerequisites: Record<string, string[]> = {
    email_verification: ['Valid email address'],
    osint: ['Verified email'],
    overview_extraction: ['OSINT research completed'],
    classification: ['Profile data extracted'],
    email_generation: ['Lead classified and scored'],
    email_sending: ['Personalized content generated']
  };
  return prerequisites[stageKey] || [];
}

function getNextStage(stageKey: string): string | undefined {
  const nextStages: Record<string, string> = {
    email_verification: 'osint',
    osint: 'overview_extraction',
    overview_extraction: 'classification',
    classification: 'email_generation',
    email_generation: 'email_sending'
  };
  return nextStages[stageKey];
}

async function getContactFunnelData(client: any, filters: any, days: number) {
  const funnelQuery = `
    WITH stage_counts AS (
      SELECT
        'Email Validation' as stage_name, 1 as stage_order,
        COUNT(CASE WHEN c.email IS NOT NULL AND c.email != '' THEN 1 END) as count
      FROM contacts c
      WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days' ${filters.whereClause}

      UNION ALL

      SELECT
        'Lead Research' as stage_name, 2 as stage_order,
        COUNT(CASE WHEN c.email_verification_status = 'completed' THEN 1 END) as count
      FROM contacts c
      WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days' ${filters.whereClause}

      UNION ALL

      SELECT
        'Profile Building' as stage_name, 3 as stage_order,
        COUNT(CASE WHEN c.osint_status = 'completed' THEN 1 END) as count
      FROM contacts c
      WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days' ${filters.whereClause}

      UNION ALL

      SELECT
        'Lead Scoring' as stage_name, 4 as stage_order,
        COUNT(CASE WHEN c.company_overview_status = 'completed' THEN 1 END) as count
      FROM contacts c
      WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days' ${filters.whereClause}

      UNION ALL

      SELECT
        'Content Creation' as stage_name, 5 as stage_order,
        COUNT(CASE WHEN c.classification_status = 'completed' THEN 1 END) as count
      FROM contacts c
      WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days' ${filters.whereClause}

      UNION ALL

      SELECT
        'Outreach Delivery' as stage_name, 6 as stage_order,
        COUNT(CASE WHEN c.email_generation_status = 'completed' THEN 1 END) as count
      FROM contacts c
      WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days' ${filters.whereClause}
    ),
    funnel_with_rates AS (
      SELECT
        stage_name,
        stage_order,
        count,
        LAG(count) OVER (ORDER BY stage_order) as prev_count,
        CASE
          WHEN LAG(count) OVER (ORDER BY stage_order) > 0
          THEN ROUND((count * 100.0 / LAG(count) OVER (ORDER BY stage_order)), 2)
          ELSE 100.0
        END as conversion_rate,
        CASE
          WHEN LAG(count) OVER (ORDER BY stage_order) > 0
          THEN ROUND(((LAG(count) OVER (ORDER BY stage_order) - count) * 100.0 / LAG(count) OVER (ORDER BY stage_order)), 2)
          ELSE 0.0
        END as drop_off_rate
      FROM stage_counts
    )
    SELECT stage_name, count, conversion_rate, drop_off_rate
    FROM funnel_with_rates
    ORDER BY stage_order
  `;

  const result = await client.query(funnelQuery, [...filters.params, ...filters.params, ...filters.params, ...filters.params, ...filters.params, ...filters.params]);
  return result.rows.map((row: any) => ({
    stage_name: row.stage_name,
    count: parseInt(row.count || 0),
    conversion_rate: parseFloat(row.conversion_rate || 100),
    drop_off_rate: parseFloat(row.drop_off_rate || 0)
  }));
}

async function getContactRecentActivity(client: any, filters: any) {
  const activityQuery = `
    SELECT
      'Email Validation' as stage,
      COUNT(CASE WHEN c.email_verification_date >= NOW() - INTERVAL '1 hour' THEN 1 END) as count,
      NOW() as timestamp,
      CASE
        WHEN COUNT(CASE WHEN c.email_verification_date >= NOW() - INTERVAL '1 hour' THEN 1 END) >
             COUNT(CASE WHEN c.email_verification_date >= NOW() - INTERVAL '2 hour' AND c.email_verification_date < NOW() - INTERVAL '1 hour' THEN 1 END)
        THEN 'up'
        WHEN COUNT(CASE WHEN c.email_verification_date >= NOW() - INTERVAL '1 hour' THEN 1 END) <
             COUNT(CASE WHEN c.email_verification_date >= NOW() - INTERVAL '2 hour' AND c.email_verification_date < NOW() - INTERVAL '1 hour' THEN 1 END)
        THEN 'down'
        ELSE 'stable'
      END as trend
    FROM contacts c
    WHERE c.created_at >= CURRENT_DATE - INTERVAL '7 days' ${filters.whereClause}

    UNION ALL

    SELECT
      'Lead Research' as stage,
      COUNT(CASE WHEN c.osint_date >= NOW() - INTERVAL '1 hour' THEN 1 END) as count,
      NOW() as timestamp,
      CASE
        WHEN COUNT(CASE WHEN c.osint_date >= NOW() - INTERVAL '1 hour' THEN 1 END) >
             COUNT(CASE WHEN c.osint_date >= NOW() - INTERVAL '2 hour' AND c.osint_date < NOW() - INTERVAL '1 hour' THEN 1 END)
        THEN 'up'
        WHEN COUNT(CASE WHEN c.osint_date >= NOW() - INTERVAL '1 hour' THEN 1 END) <
             COUNT(CASE WHEN c.osint_date >= NOW() - INTERVAL '2 hour' AND c.osint_date < NOW() - INTERVAL '1 hour' THEN 1 END)
        THEN 'down'
        ELSE 'stable'
      END as trend
    FROM contacts c
    WHERE c.created_at >= CURRENT_DATE - INTERVAL '7 days' ${filters.whereClause}

    UNION ALL

    SELECT
      'Content Creation' as stage,
      COUNT(CASE WHEN c.email_generation_date >= NOW() - INTERVAL '1 hour' THEN 1 END) as count,
      NOW() as timestamp,
      CASE
        WHEN COUNT(CASE WHEN c.email_generation_date >= NOW() - INTERVAL '1 hour' THEN 1 END) >
             COUNT(CASE WHEN c.email_generation_date >= NOW() - INTERVAL '2 hour' AND c.email_generation_date < NOW() - INTERVAL '1 hour' THEN 1 END)
        THEN 'up'
        WHEN COUNT(CASE WHEN c.email_generation_date >= NOW() - INTERVAL '1 hour' THEN 1 END) <
             COUNT(CASE WHEN c.email_generation_date >= NOW() - INTERVAL '2 hour' AND c.email_generation_date < NOW() - INTERVAL '1 hour' THEN 1 END)
        THEN 'down'
        ELSE 'stable'
      END as trend
    FROM contacts c
    WHERE c.created_at >= CURRENT_DATE - INTERVAL '7 days' ${filters.whereClause}
  `;

  const result = await client.query(activityQuery, [...filters.params, ...filters.params, ...filters.params]);
  return result.rows.map((row: any) => ({
    stage: row.stage,
    count: parseInt(row.count || 0),
    timestamp: row.timestamp,
    trend: row.trend as 'up' | 'down' | 'stable'
  }));
}

async function getCompanyPipelineData(client: any, filters: any, days: number): Promise<CompanyPipelineData> {
  // Get company overview metrics
  const overviewQuery = `
    SELECT
      COUNT(*) as total_companies,
      COUNT(CASE WHEN processing_state != 'completed' AND processing_state != 'failed' THEN 1 END) as active_companies,
      COUNT(CASE WHEN processing_state = 'completed' THEN 1 END) as completed_companies,
      COUNT(CASE WHEN processing_state = 'failed' THEN 1 END) as failed_companies,
      ROUND(
        COUNT(CASE WHEN processing_state = 'completed' THEN 1 END) * 100.0 /
        NULLIF(COUNT(*), 0), 2
      ) as overall_conversion_rate
    FROM companies c
    WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
    ${filters.whereClause}
  `;

  const overviewResult = await client.query(overviewQuery, filters.params);
  const overview = overviewResult.rows[0];

  // Get company stage metrics
  const stageQueries = [
    {
      key: 'website_scraping',
      query: `
        SELECT
          COUNT(CASE WHEN c.company_website IS NOT NULL AND c.company_website != '' THEN 1 END) as total,
          COUNT(CASE WHEN c.website_scraping_status = 'pending' THEN 1 END) as pending,
          COUNT(CASE WHEN c.website_scraping_status = 'running' THEN 1 END) as running,
          COUNT(CASE WHEN c.website_scraping_status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN c.website_scraping_status = 'failed' THEN 1 END) as failed,
          COUNT(CASE WHEN c.website_scraping_status = 'error' THEN 1 END) as error,
          COUNT(CASE WHEN c.website_scraping_date >= CURRENT_DATE - INTERVAL '1 day' THEN 1 END) as last_24h_completed,
          ROUND(AVG(CASE WHEN c.website_scraping_date IS NOT NULL
            THEN EXTRACT(EPOCH FROM (c.website_scraping_date - c.created_at)) / 3600 END), 2) as avg_processing_time_hours
        FROM companies c
        WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
        AND c.company_website IS NOT NULL AND c.company_website != ''
        ${filters.whereClause}
      `
    },
    {
      key: 'company_overview',
      query: `
        SELECT
          COUNT(CASE WHEN c.website_scraping_status = 'completed' THEN 1 END) as total,
          COUNT(CASE WHEN c.company_overview_status = 'pending' AND c.website_scraping_status = 'completed' THEN 1 END) as pending,
          COUNT(CASE WHEN c.company_overview_status = 'running' THEN 1 END) as running,
          COUNT(CASE WHEN c.company_overview_status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN c.company_overview_status = 'failed' THEN 1 END) as failed,
          COUNT(CASE WHEN c.company_overview_status = 'error' THEN 1 END) as error,
          COUNT(CASE WHEN c.company_overview_date >= CURRENT_DATE - INTERVAL '1 day' THEN 1 END) as last_24h_completed,
          ROUND(AVG(CASE WHEN c.company_overview_date IS NOT NULL
            THEN EXTRACT(EPOCH FROM (c.company_overview_date - c.website_scraping_date)) / 3600 END), 2) as avg_processing_time_hours
        FROM companies c
        WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
        AND c.website_scraping_status = 'completed'
        ${filters.whereClause}
      `
    }
  ];

  const stages: StageDetails[] = [];

  for (const stageQuery of stageQueries) {
    const result = await client.query(stageQuery.query, filters.params);
    const row = result.rows[0];

    const total = parseInt(row.total || 0);
    const completed = parseInt(row.completed || 0);
    const successRate = total > 0 ? ((completed / total) * 100) : 0;

    const stageMapping = COMPANY_STAGE_MAPPING[stageQuery.key as keyof typeof COMPANY_STAGE_MAPPING];

    stages.push({
      stage_name: stageQuery.key,
      stage_key: stageQuery.key,
      display_name: stageMapping.display_name,
      description: stageMapping.description,
      business_impact: stageMapping.business_impact,
      metrics: {
        total,
        pending: parseInt(row.pending || 0),
        running: parseInt(row.running || 0),
        completed,
        failed: parseInt(row.failed || 0),
        error: parseInt(row.error || 0),
        success_rate: parseFloat(successRate.toFixed(2)),
        avg_processing_time_hours: parseFloat(row.avg_processing_time_hours || 0),
        last_24h_completed: parseInt(row.last_24h_completed || 0)
      },
      prerequisites: stageQuery.key === 'website_scraping' ? ['Company website URL'] : ['Website data scraped'],
      next_stage: stageQuery.key === 'website_scraping' ? 'company_overview' : undefined,
      is_final_stage: stageQuery.key === 'company_overview'
    });
  }

  // Get company funnel data
  const funnelQuery = `
    WITH stage_counts AS (
      SELECT
        'Company Research' as stage_name, 1 as stage_order,
        COUNT(CASE WHEN c.company_website IS NOT NULL AND c.company_website != '' THEN 1 END) as count
      FROM companies c
      WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days' ${filters.whereClause}

      UNION ALL

      SELECT
        'Company Profiling' as stage_name, 2 as stage_order,
        COUNT(CASE WHEN c.website_scraping_status = 'completed' THEN 1 END) as count
      FROM companies c
      WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days' ${filters.whereClause}
    ),
    funnel_with_rates AS (
      SELECT
        stage_name,
        stage_order,
        count,
        LAG(count) OVER (ORDER BY stage_order) as prev_count,
        CASE
          WHEN LAG(count) OVER (ORDER BY stage_order) > 0
          THEN ROUND((count * 100.0 / LAG(count) OVER (ORDER BY stage_order)), 2)
          ELSE 100.0
        END as conversion_rate
      FROM stage_counts
    )
    SELECT stage_name, count, conversion_rate
    FROM funnel_with_rates
    ORDER BY stage_order
  `;

  const funnelResult = await client.query(funnelQuery, [...filters.params, ...filters.params]);
  const funnelData = funnelResult.rows.map((row: any) => ({
    stage_name: row.stage_name,
    count: parseInt(row.count || 0),
    conversion_rate: parseFloat(row.conversion_rate || 100)
  }));

  return {
    overview: {
      total_companies: parseInt(overview.total_companies || 0),
      active_companies: parseInt(overview.active_companies || 0),
      completed_companies: parseInt(overview.completed_companies || 0),
      failed_companies: parseInt(overview.failed_companies || 0),
      overall_conversion_rate: parseFloat(overview.overall_conversion_rate || 0)
    },
    stages,
    funnel_data: funnelData
  };
}

async function getPerformanceMetrics(client: any, contactFilters: any, companyFilters: any, days: number) {
  // Processing speed metrics
  const speedQuery = `
    SELECT
      'Email Validation' as stage,
      ROUND(AVG(EXTRACT(EPOCH FROM (email_verification_date - created_at)) / 60), 2) as avg_time_minutes,
      ROUND(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY EXTRACT(EPOCH FROM (email_verification_date - created_at)) / 60), 2) as median_time_minutes,
      ROUND(PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY EXTRACT(EPOCH FROM (email_verification_date - created_at)) / 60), 2) as p95_time_minutes
    FROM contacts
    WHERE email_verification_date IS NOT NULL
    AND created_at >= CURRENT_DATE - INTERVAL '${days} days'
    ${contactFilters.whereClause}

    UNION ALL

    SELECT
      'Lead Research' as stage,
      ROUND(AVG(EXTRACT(EPOCH FROM (osint_date - email_verification_date)) / 60), 2) as avg_time_minutes,
      ROUND(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY EXTRACT(EPOCH FROM (osint_date - email_verification_date)) / 60), 2) as median_time_minutes,
      ROUND(PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY EXTRACT(EPOCH FROM (osint_date - email_verification_date)) / 60), 2) as p95_time_minutes
    FROM contacts
    WHERE osint_date IS NOT NULL AND email_verification_date IS NOT NULL
    AND created_at >= CURRENT_DATE - INTERVAL '${days} days'
    ${contactFilters.whereClause}

    UNION ALL

    SELECT
      'Content Creation' as stage,
      ROUND(AVG(EXTRACT(EPOCH FROM (email_generation_date - classification_date)) / 60), 2) as avg_time_minutes,
      ROUND(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY EXTRACT(EPOCH FROM (email_generation_date - classification_date)) / 60), 2) as median_time_minutes,
      ROUND(PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY EXTRACT(EPOCH FROM (email_generation_date - classification_date)) / 60), 2) as p95_time_minutes
    FROM contacts
    WHERE email_generation_date IS NOT NULL AND classification_date IS NOT NULL
    AND created_at >= CURRENT_DATE - INTERVAL '${days} days'
    ${contactFilters.whereClause}
  `;

  const speedResult = await client.query(speedQuery, [...contactFilters.params, ...contactFilters.params, ...contactFilters.params]);

  // Throughput metrics
  const throughputQuery = `
    SELECT
      'Email Validation' as stage,
      ROUND(COUNT(CASE WHEN email_verification_date >= NOW() - INTERVAL '1 hour' THEN 1 END), 0) as per_hour,
      ROUND(COUNT(CASE WHEN email_verification_date >= NOW() - INTERVAL '1 day' THEN 1 END), 0) as per_day,
      'stable' as trend
    FROM contacts
    WHERE created_at >= CURRENT_DATE - INTERVAL '${days} days'
    ${contactFilters.whereClause}

    UNION ALL

    SELECT
      'Lead Research' as stage,
      ROUND(COUNT(CASE WHEN osint_date >= NOW() - INTERVAL '1 hour' THEN 1 END), 0) as per_hour,
      ROUND(COUNT(CASE WHEN osint_date >= NOW() - INTERVAL '1 day' THEN 1 END), 0) as per_day,
      'stable' as trend
    FROM contacts
    WHERE created_at >= CURRENT_DATE - INTERVAL '${days} days'
    ${contactFilters.whereClause}

    UNION ALL

    SELECT
      'Content Creation' as stage,
      ROUND(COUNT(CASE WHEN email_generation_date >= NOW() - INTERVAL '1 hour' THEN 1 END), 0) as per_hour,
      ROUND(COUNT(CASE WHEN email_generation_date >= NOW() - INTERVAL '1 day' THEN 1 END), 0) as per_day,
      'stable' as trend
    FROM contacts
    WHERE created_at >= CURRENT_DATE - INTERVAL '${days} days'
    ${contactFilters.whereClause}
  `;

  const throughputResult = await client.query(throughputQuery, [...contactFilters.params, ...contactFilters.params, ...contactFilters.params]);

  // Error rates
  const errorRateQuery = `
    SELECT
      'Email Validation' as stage,
      ROUND(
        COUNT(CASE WHEN email_verification_status IN ('failed', 'error') THEN 1 END) * 100.0 /
        NULLIF(COUNT(CASE WHEN email IS NOT NULL AND email != '' THEN 1 END), 0), 2
      ) as error_rate_percentage,
      'stable' as trend
    FROM contacts
    WHERE created_at >= CURRENT_DATE - INTERVAL '${days} days'
    ${contactFilters.whereClause}

    UNION ALL

    SELECT
      'Lead Research' as stage,
      ROUND(
        COUNT(CASE WHEN osint_status IN ('failed', 'error') THEN 1 END) * 100.0 /
        NULLIF(COUNT(CASE WHEN email_verification_status = 'completed' THEN 1 END), 0), 2
      ) as error_rate_percentage,
      'stable' as trend
    FROM contacts
    WHERE created_at >= CURRENT_DATE - INTERVAL '${days} days'
    ${contactFilters.whereClause}

    UNION ALL

    SELECT
      'Content Creation' as stage,
      ROUND(
        COUNT(CASE WHEN email_generation_status IN ('failed', 'error') THEN 1 END) * 100.0 /
        NULLIF(COUNT(CASE WHEN classification_status = 'completed' THEN 1 END), 0), 2
      ) as error_rate_percentage,
      'stable' as trend
    FROM contacts
    WHERE created_at >= CURRENT_DATE - INTERVAL '${days} days'
    ${contactFilters.whereClause}
  `;

  const errorRateResult = await client.query(errorRateQuery, [...contactFilters.params, ...contactFilters.params, ...contactFilters.params]);

  return {
    processing_speed: speedResult.rows.map((row: any) => ({
      stage: row.stage,
      avg_time_minutes: parseFloat(row.avg_time_minutes || 0),
      median_time_minutes: parseFloat(row.median_time_minutes || 0),
      p95_time_minutes: parseFloat(row.p95_time_minutes || 0)
    })),
    throughput: throughputResult.rows.map((row: any) => ({
      stage: row.stage,
      per_hour: parseInt(row.per_hour || 0),
      per_day: parseInt(row.per_day || 0),
      trend: row.trend as 'increasing' | 'decreasing' | 'stable'
    })),
    error_rates: errorRateResult.rows.map((row: any) => ({
      stage: row.stage,
      error_rate_percentage: parseFloat(row.error_rate_percentage || 0),
      trend: row.trend as 'improving' | 'worsening' | 'stable'
    }))
  };
}

async function getErrorAnalysis(client: any, contactFilters: any, companyFilters: any, days: number) {
  const errorQuery = `
    WITH contact_errors AS (
      SELECT
        CASE
          WHEN email_verification_error ILIKE '%timeout%' OR email_verification_error ILIKE '%rate%' THEN 'Rate Limited'
          WHEN email_verification_error ILIKE '%invalid%' OR email_verification_error ILIKE '%bounce%' THEN 'Invalid Email'
          WHEN email_verification_error ILIKE '%network%' OR email_verification_error ILIKE '%connection%' THEN 'Network Issues'
          WHEN osint_error ILIKE '%timeout%' OR osint_error ILIKE '%rate%' THEN 'Rate Limited'
          WHEN osint_error ILIKE '%not found%' OR osint_error ILIKE '%no data%' THEN 'Data Not Found'
          WHEN classification_error ILIKE '%timeout%' OR classification_error ILIKE '%rate%' THEN 'Rate Limited'
          WHEN email_generation_error ILIKE '%timeout%' OR email_generation_error ILIKE '%rate%' THEN 'Rate Limited'
          ELSE 'Other'
        END as error_type,
        contact_id as entity_id,
        'contact' as entity_type,
        CASE
          WHEN email_verification_error IS NOT NULL THEN 'email_verification'
          WHEN osint_error IS NOT NULL THEN 'osint'
          WHEN overview_extraction_error IS NOT NULL THEN 'overview_extraction'
          WHEN classification_error IS NOT NULL THEN 'classification'
          WHEN email_generation_error IS NOT NULL THEN 'email_generation'
          WHEN email_sending_error IS NOT NULL THEN 'email_sending'
        END as stage,
        CASE
          WHEN email_verification_error IS NOT NULL THEN email_verification_error
          WHEN osint_error IS NOT NULL THEN osint_error
          WHEN overview_extraction_error IS NOT NULL THEN overview_extraction_error
          WHEN classification_error IS NOT NULL THEN classification_error
          WHEN email_generation_error IS NOT NULL THEN email_generation_error
          WHEN email_sending_error IS NOT NULL THEN email_sending_error
        END as error_message,
        updated_at as occurred_at,
        processing_error_count as retry_count
      FROM contacts
      WHERE created_at >= CURRENT_DATE - INTERVAL '${days} days'
      AND (
        email_verification_error IS NOT NULL OR
        osint_error IS NOT NULL OR
        overview_extraction_error IS NOT NULL OR
        classification_error IS NOT NULL OR
        email_generation_error IS NOT NULL OR
        email_sending_error IS NOT NULL
      )
      ${contactFilters.whereClause}

      UNION ALL

      SELECT
        CASE
          WHEN website_scraping_error ILIKE '%timeout%' OR website_scraping_error ILIKE '%rate%' THEN 'Rate Limited'
          WHEN website_scraping_error ILIKE '%not found%' OR website_scraping_error ILIKE '%404%' THEN 'Website Not Found'
          WHEN website_scraping_error ILIKE '%blocked%' OR website_scraping_error ILIKE '%forbidden%' THEN 'Access Blocked'
          WHEN company_overview_error ILIKE '%timeout%' OR company_overview_error ILIKE '%rate%' THEN 'Rate Limited'
          ELSE 'Other'
        END as error_type,
        company_id as entity_id,
        'company' as entity_type,
        CASE
          WHEN website_scraping_error IS NOT NULL THEN 'website_scraping'
          WHEN company_overview_error IS NOT NULL THEN 'company_overview'
        END as stage,
        CASE
          WHEN website_scraping_error IS NOT NULL THEN website_scraping_error
          WHEN company_overview_error IS NOT NULL THEN company_overview_error
        END as error_message,
        updated_at as occurred_at,
        0 as retry_count
      FROM companies
      WHERE created_at >= CURRENT_DATE - INTERVAL '${days} days'
      AND (
        website_scraping_error IS NOT NULL OR
        company_overview_error IS NOT NULL
      )
      ${companyFilters.whereClause}
    ),
    error_summary AS (
      SELECT
        error_type,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM contact_errors), 2) as percentage
      FROM contact_errors
      GROUP BY error_type
    ),
    recent_examples AS (
      SELECT
        error_type,
        entity_id,
        entity_type,
        stage,
        error_message,
        occurred_at,
        retry_count,
        ROW_NUMBER() OVER (PARTITION BY error_type ORDER BY occurred_at DESC) as rn
      FROM contact_errors
    )
    SELECT
      es.error_type,
      es.count,
      es.percentage,
      COALESCE(
        JSON_AGG(
          JSON_BUILD_OBJECT(
            'entity_id', re.entity_id,
            'entity_type', re.entity_type,
            'stage', re.stage,
            'error_message', re.error_message,
            'occurred_at', re.occurred_at,
            'retry_count', re.retry_count
          ) ORDER BY re.occurred_at DESC
        ) FILTER (WHERE re.rn <= 3),
        '[]'::json
      ) as recent_examples
    FROM error_summary es
    LEFT JOIN recent_examples re ON es.error_type = re.error_type AND re.rn <= 3
    GROUP BY es.error_type, es.count, es.percentage
    ORDER BY es.count DESC
  `;

  const errorResult = await client.query(errorQuery, [...contactFilters.params, ...companyFilters.params]);

  return errorResult.rows.map((row: any) => ({
    error_type: row.error_type,
    count: parseInt(row.count || 0),
    percentage: parseFloat(row.percentage || 0),
    recent_examples: row.recent_examples || [],
    suggested_actions: getSuggestedActions(row.error_type)
  }));
}

function getSuggestedActions(errorType: string): string[] {
  const actions: Record<string, string[]> = {
    'Rate Limited': [
      'Implement exponential backoff',
      'Reduce processing rate',
      'Add more API keys/accounts',
      'Distribute load across time'
    ],
    'Invalid Email': [
      'Improve email validation rules',
      'Update email verification service',
      'Review data sources quality'
    ],
    'Network Issues': [
      'Check network connectivity',
      'Implement retry mechanisms',
      'Monitor service health'
    ],
    'Data Not Found': [
      'Review search criteria',
      'Try alternative data sources',
      'Update search algorithms'
    ],
    'Website Not Found': [
      'Validate website URLs',
      'Check for redirects',
      'Update company data sources'
    ],
    'Access Blocked': [
      'Rotate IP addresses',
      'Use different user agents',
      'Implement delays between requests'
    ]
  };
  return actions[errorType] || ['Review error logs', 'Contact technical support'];
}

async function getTimelineData(client: any, contactFilters: any, companyFilters: any, days: number) {
  const timelineQuery = `
    WITH date_series AS (
      SELECT generate_series(
        CURRENT_DATE - INTERVAL '${days} days',
        CURRENT_DATE,
        '1 day'::interval
      )::date as date
    ),
    contact_timeline AS (
      SELECT
        DATE(email_verification_date) as date,
        'Email Validation' as stage,
        COUNT(*) as completed,
        0 as failed,
        0 as pending,
        0 as running
      FROM contacts
      WHERE email_verification_date >= CURRENT_DATE - INTERVAL '${days} days'
      AND email_verification_status = 'completed'
      ${contactFilters.whereClause}
      GROUP BY DATE(email_verification_date)

      UNION ALL

      SELECT
        DATE(osint_date) as date,
        'Lead Research' as stage,
        COUNT(*) as completed,
        0 as failed,
        0 as pending,
        0 as running
      FROM contacts
      WHERE osint_date >= CURRENT_DATE - INTERVAL '${days} days'
      AND osint_status = 'completed'
      ${contactFilters.whereClause}
      GROUP BY DATE(osint_date)

      UNION ALL

      SELECT
        DATE(email_generation_date) as date,
        'Content Creation' as stage,
        COUNT(*) as completed,
        0 as failed,
        0 as pending,
        0 as running
      FROM contacts
      WHERE email_generation_date >= CURRENT_DATE - INTERVAL '${days} days'
      AND email_generation_status = 'completed'
      ${contactFilters.whereClause}
      GROUP BY DATE(email_generation_date)
    )
    SELECT
      ds.date::text,
      COALESCE(ct.stage, 'Email Validation') as stage,
      COALESCE(ct.completed, 0) as completed,
      COALESCE(ct.failed, 0) as failed,
      COALESCE(ct.pending, 0) as pending,
      COALESCE(ct.running, 0) as running
    FROM date_series ds
    LEFT JOIN contact_timeline ct ON ds.date = ct.date
    ORDER BY ds.date, ct.stage
  `;

  const timelineResult = await client.query(timelineQuery, [...contactFilters.params, ...contactFilters.params, ...contactFilters.params]);

  return timelineResult.rows.map((row: any) => ({
    date: row.date,
    stage: row.stage,
    completed: parseInt(row.completed || 0),
    failed: parseInt(row.failed || 0),
    pending: parseInt(row.pending || 0),
    running: parseInt(row.running || 0)
  }));
}
