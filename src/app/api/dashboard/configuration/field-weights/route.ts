import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

// GET: Return all field weights
export async function GET() {
  try {
    const result = await pool.query(
      `SELECT field_name, weight, description FROM field_weights ORDER BY field_name`
    );
    return NextResponse.json({ field_weights: result.rows });
  } catch (error) {
    console.error("Error fetching field weights:", error);
    return NextResponse.json(
      { error: "Failed to fetch field weights" },
      { status: 500 }
    );
  }
}

// POST: Update a field weight
export async function POST(req: NextRequest) {
  try {
    const { field_name, weight } = await req.json();
    if (!field_name || typeof weight !== "number" || weight < 0 || weight > 1) {
      return NextResponse.json({ error: "Invalid input" }, { status: 400 });
    }
    const result = await pool.query(
      `UPDATE field_weights SET weight = $1 WHERE field_name = $2 RETURNING field_name, weight, description`,
      [weight, field_name]
    );
    if (result.rowCount === 0) {
      return NextResponse.json({ error: "Field not found" }, { status: 404 });
    }
    return NextResponse.json({ field_weight: result.rows[0] });
  } catch (error) {
    console.error("Error updating field weight:", error);
    return NextResponse.json(
      { error: "Failed to update field weight" },
      { status: 500 }
    );
  }
}
