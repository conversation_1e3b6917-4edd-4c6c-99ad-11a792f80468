import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

// GET: List all Fireflies accounts
export async function GET() {
  const result = await pool.query(
    "SELECT id, name, api_key, status, last_sync_at, error_count, last_error, updated_at FROM fireflies_accounts ORDER BY name ASC"
  );
  return NextResponse.json(result.rows);
}

// POST: Add a new Fireflies account
export async function POST(req: NextRequest) {
  const { name, apiKey } = await req.json();
  
  if (!name || typeof name !== "string") {
    return NextResponse.json({ error: "Name is required" }, { status: 400 });
  }
  
  if (!apiKey || typeof apiKey !== "string") {
    return NextResponse.json({ error: "API key is required" }, { status: 400 });
  }

  // First check if account with this name already exists
  const existingAccount = await pool.query(
    "SELECT id FROM fireflies_accounts WHERE name = $1",
    [name.trim()]
  );

  if (existingAccount.rows.length > 0) {
    // Update existing account
    const result = await pool.query(
      `UPDATE fireflies_accounts 
       SET api_key = $2, updated_at = NOW(), status = 'active', error_count = 0, last_error = NULL
       WHERE name = $1
       RETURNING id, name, status, last_sync_at, error_count, last_error, updated_at`,
      [name.trim(), apiKey.trim()]
    );
    return NextResponse.json(result.rows[0]);
  } else {
    // Insert new account
    const result = await pool.query(
      `INSERT INTO fireflies_accounts (name, api_key, created_at, updated_at)
       VALUES ($1, $2, NOW(), NOW())
       RETURNING id, name, status, last_sync_at, error_count, last_error, updated_at`,
      [name.trim(), apiKey.trim()]
    );
    return NextResponse.json(result.rows[0]);
  }
}

// DELETE: Remove a Fireflies account
export async function DELETE(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get("id");
  
  if (!id) {
    return NextResponse.json({ error: "Account ID is required" }, { status: 400 });
  }

  const result = await pool.query(
    "DELETE FROM fireflies_accounts WHERE id = $1 RETURNING id, name",
    [id]
  );

  if (result.rows.length === 0) {
    return NextResponse.json({ error: "Account not found" }, { status: 404 });
  }

  return NextResponse.json({ message: "Account deleted successfully", account: result.rows[0] });
} 