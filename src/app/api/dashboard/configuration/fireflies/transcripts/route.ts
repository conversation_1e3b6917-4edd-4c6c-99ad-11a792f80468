import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

// GET: Fetch transcripts with pagination and optional account filtering
export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const accountId = searchParams.get("accountId");
  const page = parseInt(searchParams.get("page") || "1");
  const limit = parseInt(searchParams.get("limit") || "50");
  const offset = (page - 1) * limit;

  try {
    let query = `
      SELECT 
        ft.id,
        ft.provider_transcript_id,
        ft.title,
        ft.meeting_date,
        ft.duration,
        ft.participants,
        ft.transcript_text,
        ft.sentences,
        ft.created_at,
        ft.updated_at,
        fa.name as account_name
      FROM fireflies_transcripts ft
      JOIN fireflies_accounts fa ON ft.account_id = fa.id
    `;

    const queryParams: any[] = [];
    let paramCount = 0;

    if (accountId) {
      query += ` WHERE ft.account_id = $${++paramCount}`;
      queryParams.push(accountId);
    }

    query += ` ORDER BY ft.meeting_date DESC`;

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM fireflies_transcripts ft
      JOIN fireflies_accounts fa ON ft.account_id = fa.id
    `;
    
    if (accountId) {
      countQuery += ` WHERE ft.account_id = $1`;
    }

    const countResult = await pool.query(countQuery, accountId ? [accountId] : []);
    const total = parseInt(countResult.rows[0].total);

    // Get paginated results
    query += ` LIMIT $${++paramCount} OFFSET $${++paramCount}`;
    queryParams.push(limit, offset);

    const result = await pool.query(query, queryParams);

    const transcripts = result.rows.map((row: any) => ({
      id: row.id,
      provider_transcript_id: row.provider_transcript_id,
      title: row.title,
      meeting_date: row.meeting_date,
      duration: row.duration,
      participants: row.participants || [],
      transcript_text: row.transcript_text || 'No transcript content available',
      sentences: row.sentences || [],
      account_name: row.account_name,
      created_at: row.created_at,
      updated_at: row.updated_at,
    }));

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      transcripts,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error("Error fetching Fireflies transcripts:", error);
    return NextResponse.json(
      { error: "Failed to fetch transcripts" },
      { status: 500 }
    );
  }
} 