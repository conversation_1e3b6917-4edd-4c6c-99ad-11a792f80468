import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

// GET: Fetch all capital position field weights
export async function GET() {
  try {
    const query = `
      SELECT 
        capital_position,
        field_name,
        weight,
        description,
        created_at,
        updated_at
      FROM capital_position_field_weights
      ORDER BY capital_position, field_name
    `;
    
    const result = await pool.query(query);
    
    return NextResponse.json(result.rows);
  } catch (error) {
    console.error('Error fetching capital position field weights:', error);
    return NextResponse.json(
      { error: 'Failed to fetch capital position field weights' },
      { status: 500 }
    );
  }
}

// POST: Create or update capital position field weights
export async function POST(request: NextRequest) {
  try {
    const { capitalPosition, fieldName, weight, description } = await request.json();
    
    if (!capitalPosition || !fieldName || weight === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: capitalPosition, fieldName, weight' },
        { status: 400 }
      );
    }
    
    // Check if record exists
    const checkQuery = `
      SELECT weight FROM capital_position_field_weights 
      WHERE capital_position = $1 AND field_name = $2
    `;
    const checkResult = await pool.query(checkQuery, [capitalPosition, fieldName]);
    
    if (checkResult.rows.length > 0) {
      // Update existing record
      const updateQuery = `
        UPDATE capital_position_field_weights 
        SET weight = $3, description = $4, updated_at = NOW()
        WHERE capital_position = $1 AND field_name = $2
        RETURNING *
      `;
      const updateResult = await pool.query(updateQuery, [capitalPosition, fieldName, weight, description]);
      return NextResponse.json(updateResult.rows[0]);
    } else {
      // Insert new record
      const insertQuery = `
        INSERT INTO capital_position_field_weights 
        (capital_position, field_name, weight, description, created_at, updated_at)
        VALUES ($1, $2, $3, $4, NOW(), NOW())
        RETURNING *
      `;
      const insertResult = await pool.query(insertQuery, [capitalPosition, fieldName, weight, description]);
      return NextResponse.json(insertResult.rows[0]);
    }
  } catch (error) {
    console.error('Error creating/updating capital position field weight:', error);
    return NextResponse.json(
      { error: 'Failed to create/update capital position field weight' },
      { status: 500 }
    );
  }
}

// DELETE: Remove a capital position field weight
export async function DELETE(request: NextRequest) {
  try {
    const { capitalPosition, fieldName } = await request.json();
    
    if (!capitalPosition || !fieldName) {
      return NextResponse.json(
        { error: 'Missing required fields: capitalPosition, fieldName' },
        { status: 400 }
      );
    }
    
    const query = `
      DELETE FROM capital_position_field_weights 
      WHERE capital_position = $1 AND field_name = $2
      RETURNING *
    `;
    
    const result = await pool.query(query, [capitalPosition, fieldName]);
    
    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Capital position field weight not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ message: 'Capital position field weight deleted successfully' });
  } catch (error) {
    console.error('Error deleting capital position field weight:', error);
    return NextResponse.json(
      { error: 'Failed to delete capital position field weight' },
      { status: 500 }
    );
  }
}

// PUT: Bulk update capital position field weights
export async function PUT(request: NextRequest) {
  try {
    const { weights } = await request.json();
    
    if (!Array.isArray(weights)) {
      return NextResponse.json(
        { error: 'Weights must be an array' },
        { status: 400 }
      );
    }
    
    // Start a transaction
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      for (const weight of weights) {
        const { capitalPosition, fieldName, weight: weightValue, description } = weight;
        
        if (!capitalPosition || !fieldName || weightValue === undefined) {
          throw new Error('Missing required fields in weight object');
        }
        
        const upsertQuery = `
          INSERT INTO capital_position_field_weights 
          (capital_position, field_name, weight, description, created_at, updated_at)
          VALUES ($1, $2, $3, $4, NOW(), NOW())
          ON CONFLICT (capital_position, field_name) 
          DO UPDATE SET 
            weight = EXCLUDED.weight,
            description = EXCLUDED.description,
            updated_at = NOW()
        `;
        
        await client.query(upsertQuery, [capitalPosition, fieldName, weightValue, description]);
      }
      
      await client.query('COMMIT');
      
      return NextResponse.json({ message: 'Capital position field weights updated successfully' });
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error bulk updating capital position field weights:', error);
    return NextResponse.json(
      { error: 'Failed to bulk update capital position field weights' },
      { status: 500 }
    );
  }
}
