import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

// GET: List all Gmail accounts
export async function GET() {
  const result = await pool.query(
    "SELECT id, email, last_history_id, updated_at FROM gmail_accounts ORDER BY email ASC"
  );
  return NextResponse.json(result.rows);
}

// POST: Add a new Gmail account
export async function POST(req: NextRequest) {
  const { email } = await req.json();
  if (!email || typeof email !== "string") {
    return NextResponse.json({ error: "Email is required" }, { status: 400 });
  }
  const result = await pool.query(
    `INSERT INTO gmail_accounts (email, created_at, updated_at)
     VALUES ($1, NOW(), NOW())
     ON CONFLICT (email) DO UPDATE SET updated_at = NOW()
     RETURNING id, email, last_history_id, updated_at`,
    [email.trim()]
  );
  return NextResponse.json(result.rows[0]);
}

// DELETE: Remove a Gmail account by id
export async function DELETE(req: NextRequest) {
  const { id } = await req.json();
  if (!id || typeof id !== "string") {
    return NextResponse.json({ error: "ID is required" }, { status: 400 });
  }
  await pool.query("DELETE FROM gmail_accounts WHERE id = $1", [id]);
  return NextResponse.json({ success: true });
}
