import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url!);
  const email = searchParams.get("email");
  if (!email) {
    return NextResponse.json({ error: "email is required" }, { status: 400 });
  }

  // Pagination parameters
  const page = parseInt(searchParams.get("page") || "1");
  const limit = parseInt(searchParams.get("limit") || "50");
  const offset = (page - 1) * limit;

  // Get total count for pagination
  const countResult = await pool.query(
    `
    SELECT COUNT(DISTINCT t.id) as total
    FROM gmail_threads t
    JOIN gmail_messages m ON m.thread_id = t.id
    WHERE LOWER(m.sender) = LOWER($1) OR EXISTS (
      SELECT 1 FROM UNNEST(m.recipients) AS r WHERE LOWER(r) = LOWER($1)
    )
    `,
    [email]
  );
  const total = parseInt(countResult.rows[0].total);
  const totalPages = Math.ceil(total / limit);

  // Case-insensitive match for sender and recipients
  const result = await pool.query(
    `
    WITH first_msg AS (
      SELECT DISTINCT ON (m.thread_id) m.thread_id, m.sender, m.recipients, m.sent_at
      FROM gmail_messages m
      WHERE m.thread_id IN (SELECT t.id FROM gmail_threads t)
      ORDER BY m.thread_id, m.sent_at ASC
    )
    SELECT DISTINCT t.id as thread_id, t.subject, t.created_at,
      fm.sender, fm.recipients,
      CASE
        WHEN EXISTS (
          SELECT 1 FROM gmail_messages m2
          WHERE m2.thread_id = t.id AND LOWER(m2.sender) = LOWER($1)
        ) THEN 'sent'
        ELSE 'received'
      END as direction
    FROM gmail_threads t
    JOIN gmail_messages m ON m.thread_id = t.id
    LEFT JOIN first_msg fm ON fm.thread_id = t.id
    WHERE LOWER(m.sender) = LOWER($1) OR EXISTS (
      SELECT 1 FROM UNNEST(m.recipients) AS r WHERE LOWER(r) = LOWER($1)
    )
    ORDER BY t.created_at DESC
    LIMIT $2 OFFSET $3
  `,
    [email, limit, offset]
  );
  
  return NextResponse.json({
    data: result.rows,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  });
}
