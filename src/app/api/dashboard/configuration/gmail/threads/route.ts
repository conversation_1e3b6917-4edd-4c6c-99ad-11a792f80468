import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

// Simple in-memory rate limiter (per accountId)
const rateLimitMap: {
  [accountId: string]: { count: number; lastReset: number };
} = {};
const RATE_LIMIT = 10; // max requests
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url!);
  const accountId = searchParams.get("accountId");
  if (!accountId) {
    return NextResponse.json(
      { error: "accountId is required" },
      { status: 400 }
    );
  }

  // Pagination parameters
  const page = parseInt(searchParams.get("page") || "1");
  const limit = parseInt(searchParams.get("limit") || "50");
  const offset = (page - 1) * limit;

  // Rate limiting logic
  const now = Date.now();
  if (
    !rateLimitMap[accountId] ||
    now - rateLimitMap[accountId].lastReset > RATE_LIMIT_WINDOW
  ) {
    rateLimitMap[accountId] = { count: 1, lastReset: now };
  } else {
    rateLimitMap[accountId].count++;
    if (rateLimitMap[accountId].count > RATE_LIMIT) {
      return NextResponse.json(
        { error: "Rate limit exceeded. Try again later." },
        { status: 429 }
      );
    }
  }

  // Get total count for pagination
  const countResult = await pool.query(
    `SELECT COUNT(*) as total FROM gmail_threads WHERE account_id = $1`,
    [accountId]
  );
  const total = parseInt(countResult.rows[0].total);
  const totalPages = Math.ceil(total / limit);

  const result = await pool.query(
    `
    WITH first_msg AS (
      SELECT DISTINCT ON (m.thread_id) m.thread_id, m.sender, m.recipients, m.sent_at
      FROM gmail_messages m
      WHERE m.thread_id IN (SELECT id FROM gmail_threads WHERE account_id = $1)
      ORDER BY m.thread_id, m.sent_at ASC
    )
    SELECT t.id, t.provider_thread_id, t.subject, t.created_at,
      fm.sender, fm.recipients
    FROM gmail_threads t
    LEFT JOIN first_msg fm ON fm.thread_id = t.id
    WHERE t.account_id = $1
    ORDER BY t.created_at DESC
    LIMIT $2 OFFSET $3
  `,
    [accountId, limit, offset]
  );
  
  return NextResponse.json({
    data: result.rows,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  });
}
