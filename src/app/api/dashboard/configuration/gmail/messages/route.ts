import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url!);
  const threadId = searchParams.get("threadId");
  if (!threadId) {
    return NextResponse.json(
      { error: "threadId is required" },
      { status: 400 }
    );
  }
  const result = await pool.query(
    "SELECT id, sender, recipients, subject, sent_at, body FROM gmail_messages WHERE thread_id = $1 ORDER BY sent_at ASC",
    [threadId]
  );
  return NextResponse.json(result.rows);
}
