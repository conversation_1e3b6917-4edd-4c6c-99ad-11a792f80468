import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

// Simple pipeline dashboard API without complex ROUND functions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get('dateRange') || '7d';
    const source = searchParams.get('source') || 'all';

    const client = await pool.connect();

    try {
      const days = getDaysFromRange(dateRange);
      const filters = buildFilters(source);

      // Get simple pipeline overview
      const overview = await getPipelineOverview(client, filters, days);
      
      // Get stage metrics
      const stages = await getStageMetrics(client, filters, days);
      
      // Get recent activity
      const recentActivity = await getRecentActivity(client, filters);

      return NextResponse.json({
        success: true,
        data: {
          overview,
          stages,
          recent_activity: recentActivity
        },
        metadata: {
          generated_at: new Date().toISOString(),
          filters: { dateRange, source },
          business_context: getBusinessContext(dateRange)
        }
      });

    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Simple pipeline error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

function getDaysFromRange(range: string): number {
  switch (range) {
    case '1d': return 1;
    case '7d': return 7;
    case '30d': return 30;
    case '90d': return 90;
    default: return 7;
  }
}

function buildFilters(source: string) {
  const conditions: string[] = [];
  const params: any[] = [];
  let paramIndex = 1;

  if (source !== 'all') {
    conditions.push(`c.source = $${paramIndex}`);
    params.push(source);
    paramIndex++;
  }

  return {
    whereClause: conditions.length > 0 ? 'AND ' + conditions.join(' AND ') : '',
    params
  };
}

async function getPipelineOverview(client: any, filters: any, days: number) {
  const overviewQuery = `
    SELECT 
      COUNT(*) as total_leads,
      COUNT(CASE WHEN email_verification_status = 'completed' THEN 1 END) as email_validated,
      COUNT(CASE WHEN osint_status = 'completed' THEN 1 END) as research_completed,
      COUNT(CASE WHEN classification_status = 'completed' THEN 1 END) as leads_scored,
      COUNT(CASE WHEN email_generation_status = 'completed' THEN 1 END) as content_ready,
      COUNT(CASE WHEN email_sending_status = 'completed' THEN 1 END) as outreach_sent,
      COUNT(CASE WHEN processing_error_count > 0 THEN 1 END) as leads_with_errors,
      COUNT(CASE WHEN created_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as new_leads_24h,
      COUNT(CASE WHEN email_sending_date >= NOW() - INTERVAL '24 hours' THEN 1 END) as outreach_sent_24h
    FROM contacts c
    WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
    ${filters.whereClause}
  `;

  const result = await client.query(overviewQuery, filters.params);
  const metrics = result.rows[0];

  const totalLeads = parseInt(metrics.total_leads || 0);
  const emailValidated = parseInt(metrics.email_validated || 0);
  const outreachSent = parseInt(metrics.outreach_sent || 0);
  const leadsWithErrors = parseInt(metrics.leads_with_errors || 0);

  return {
    total_leads: totalLeads,
    email_validated: emailValidated,
    research_completed: parseInt(metrics.research_completed || 0),
    leads_scored: parseInt(metrics.leads_scored || 0),
    content_ready: parseInt(metrics.content_ready || 0),
    outreach_sent: outreachSent,
    leads_with_errors: leadsWithErrors,
    rates: {
      validation_rate: totalLeads > 0 ? Math.round((emailValidated / totalLeads) * 100 * 100) / 100 : 0,
      completion_rate: totalLeads > 0 ? Math.round((outreachSent / totalLeads) * 100 * 100) / 100 : 0,
      error_rate: totalLeads > 0 ? Math.round((leadsWithErrors / totalLeads) * 100 * 100) / 100 : 0
    },
    velocity: {
      new_leads_24h: parseInt(metrics.new_leads_24h || 0),
      outreach_sent_24h: parseInt(metrics.outreach_sent_24h || 0)
    },
    health_status: getHealthStatus(totalLeads, outreachSent, leadsWithErrors)
  };
}

function getHealthStatus(totalLeads: number, outreachSent: number, leadsWithErrors: number): string {
  if (totalLeads === 0) return 'no_data';
  
  const completionRate = (outreachSent / totalLeads) * 100;
  const errorRate = (leadsWithErrors / totalLeads) * 100;
  
  if (completionRate >= 80 && errorRate <= 5) return 'excellent';
  if (completionRate >= 60 && errorRate <= 10) return 'good';
  if (completionRate >= 30 && errorRate <= 20) return 'warning';
  return 'critical';
}

async function getStageMetrics(client: any, filters: any, days: number) {
  const stageQueries = [
    {
      name: 'Email Validation',
      key: 'email_verification',
      query: `
        SELECT 
          COUNT(CASE WHEN c.email IS NOT NULL AND c.email != '' THEN 1 END) as eligible,
          COUNT(CASE WHEN c.email_verification_status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN c.email_verification_status = 'failed' THEN 1 END) as failed,
          COUNT(CASE WHEN c.email_verification_status = 'error' THEN 1 END) as errors,
          COUNT(CASE WHEN c.email_verification_status = 'running' THEN 1 END) as running,
          COUNT(CASE WHEN c.email_verification_status = 'pending' AND c.email IS NOT NULL AND c.email != '' THEN 1 END) as pending
        FROM contacts c
        WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
        ${filters.whereClause}
      `
    },
    {
      name: 'Lead Research',
      key: 'osint',
      query: `
        SELECT 
          COUNT(CASE WHEN c.email_verification_status = 'completed' THEN 1 END) as eligible,
          COUNT(CASE WHEN c.osint_status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN c.osint_status = 'failed' THEN 1 END) as failed,
          COUNT(CASE WHEN c.osint_status = 'error' THEN 1 END) as errors,
          COUNT(CASE WHEN c.osint_status = 'running' THEN 1 END) as running,
          COUNT(CASE WHEN c.osint_status = 'pending' AND c.email_verification_status = 'completed' THEN 1 END) as pending
        FROM contacts c
        WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
        ${filters.whereClause}
      `
    },
    {
      name: 'Lead Scoring',
      key: 'classification',
      query: `
        SELECT 
          COUNT(CASE WHEN c.company_overview_status = 'completed' THEN 1 END) as eligible,
          COUNT(CASE WHEN c.classification_status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN c.classification_status = 'failed' THEN 1 END) as failed,
          COUNT(CASE WHEN c.classification_status = 'error' THEN 1 END) as errors,
          COUNT(CASE WHEN c.classification_status = 'running' THEN 1 END) as running,
          COUNT(CASE WHEN c.classification_status = 'pending' AND c.company_overview_status = 'completed' THEN 1 END) as pending
        FROM contacts c
        WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
        ${filters.whereClause}
      `
    },
    {
      name: 'Content Creation',
      key: 'email_generation',
      query: `
        SELECT 
          COUNT(CASE WHEN c.classification_status = 'completed' THEN 1 END) as eligible,
          COUNT(CASE WHEN c.email_generation_status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN c.email_generation_status = 'failed' THEN 1 END) as failed,
          COUNT(CASE WHEN c.email_generation_status = 'error' THEN 1 END) as errors,
          COUNT(CASE WHEN c.email_generation_status = 'running' THEN 1 END) as running,
          COUNT(CASE WHEN c.email_generation_status = 'pending' AND c.classification_status = 'completed' THEN 1 END) as pending
        FROM contacts c
        WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
        ${filters.whereClause}
      `
    },
    {
      name: 'Outreach Delivery',
      key: 'email_sending',
      query: `
        SELECT 
          COUNT(CASE WHEN c.email_generation_status = 'completed' THEN 1 END) as eligible,
          COUNT(CASE WHEN c.email_sending_status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN c.email_sending_status = 'failed' THEN 1 END) as failed,
          COUNT(CASE WHEN c.email_sending_status = 'error' THEN 1 END) as errors,
          COUNT(CASE WHEN c.email_sending_status = 'running' THEN 1 END) as running,
          COUNT(CASE WHEN c.email_sending_status = 'pending' AND c.email_generation_status = 'completed' THEN 1 END) as pending
        FROM contacts c
        WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
        ${filters.whereClause}
      `
    }
  ];

  const stages = [];

  for (const stage of stageQueries) {
    const result = await client.query(stage.query, filters.params);
    const metrics = result.rows[0];

    const eligible = parseInt(metrics.eligible || 0);
    const completed = parseInt(metrics.completed || 0);
    const failed = parseInt(metrics.failed || 0);
    const errors = parseInt(metrics.errors || 0);

    stages.push({
      stage_name: stage.name,
      stage_key: stage.key,
      metrics: {
        eligible,
        completed,
        failed,
        errors,
        running: parseInt(metrics.running || 0),
        pending: parseInt(metrics.pending || 0)
      },
      rates: {
        success_rate: eligible > 0 ? Math.round((completed / eligible) * 100 * 100) / 100 : 0,
        failure_rate: eligible > 0 ? Math.round((failed / eligible) * 100 * 100) / 100 : 0,
        error_rate: eligible > 0 ? Math.round((errors / eligible) * 100 * 100) / 100 : 0
      },
      status: getStageStatus(completed, failed, errors, eligible)
    });
  }

  return stages;
}

function getStageStatus(completed: number, failed: number, errors: number, eligible: number): string {
  if (eligible === 0) return 'no_data';
  
  const successRate = (completed / eligible) * 100;
  const errorRate = ((failed + errors) / eligible) * 100;
  
  if (successRate >= 95 && errorRate <= 2) return 'excellent';
  if (successRate >= 85 && errorRate <= 5) return 'good';
  if (successRate >= 70 && errorRate <= 10) return 'warning';
  return 'critical';
}

async function getRecentActivity(client: any, filters: any) {
  const activityQuery = `
    SELECT 
      COUNT(CASE WHEN email_verification_date >= NOW() - INTERVAL '1 hour' THEN 1 END) as email_validations_1h,
      COUNT(CASE WHEN osint_date >= NOW() - INTERVAL '1 hour' THEN 1 END) as research_completed_1h,
      COUNT(CASE WHEN email_generation_date >= NOW() - INTERVAL '1 hour' THEN 1 END) as content_created_1h,
      COUNT(CASE WHEN email_sending_date >= NOW() - INTERVAL '1 hour' THEN 1 END) as outreach_sent_1h
    FROM contacts c
    WHERE c.created_at >= CURRENT_DATE - INTERVAL '7 days'
    ${filters.whereClause}
  `;

  const result = await client.query(activityQuery, filters.params);
  const activity = result.rows[0];

  return {
    last_hour: {
      email_validations: parseInt(activity.email_validations_1h || 0),
      research_completed: parseInt(activity.research_completed_1h || 0),
      content_created: parseInt(activity.content_created_1h || 0),
      outreach_sent: parseInt(activity.outreach_sent_1h || 0)
    }
  };
}

function getBusinessContext(dateRange: string): string {
  const contexts = {
    '1d': 'Real-time daily performance snapshot',
    '7d': 'Weekly pipeline performance overview',
    '30d': 'Monthly trend analysis and optimization insights',
    '90d': 'Quarterly strategic performance review'
  };
  return contexts[dateRange as keyof typeof contexts] || 'Pipeline performance analysis';
}
