import { NextRequest, NextResponse } from 'next/server'
import UploadProcessor from '@/lib/workers/UploadProcessor'
import { AsyncUploadService } from '@/lib/services/AsyncUploadService'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { upload_id, trigger_all } = body

    if (upload_id) {
      // Process specific upload
      console.log(`🚀 API: Processing specific upload: ${upload_id}`)
      
      const processor = new UploadProcessor()
      
      try {
        const result = await processor.processUpload(upload_id.toString())
        
        console.log(`✅ API: Upload ${upload_id} processing completed`);
        console.log(`📊 API: Final Stats - Success: ${result.successfulRows}/${result.totalRows}, Conflicts: ${result.conflictRows}, Companies: ${result.companiesCreated}, Contacts: ${result.contactsCreated}`);
        
        return NextResponse.json({
          success: result.successfulRows > 0,
          message: result.successfulRows > 0
            ? `Upload ${upload_id} processed successfully: ${result.successfulRows}/${result.totalRows} rows (${result.conflictRows} conflicts)`
            : `Failed to process upload ${upload_id}: ${result.errors.join(', ')}`,
          upload_id,
          stats: result,
          processing_summary: {
            total_rows: result.totalRows,
            processed_rows: result.processedRows,
            successful_rows: result.successfulRows,
            failed_rows: result.failedRows,
            conflict_rows: result.conflictRows,
            companies_created: result.companiesCreated,
            contacts_created: result.contactsCreated,
            investment_criteria_created: result.investmentCriteriaCreated,
            processing_time_seconds: (result.processingTimeMs / 1000).toFixed(2),
            rows_per_second: result.processedRows > 0 ? (result.processedRows / (result.processingTimeMs / 1000)).toFixed(1) : '0'
          }
        })
      } catch (processingError) {
        console.error(`❌ API: Upload ${upload_id} processing failed:`, processingError);
        
        return NextResponse.json({
          success: false,
          message: `Upload ${upload_id} processing failed: ${processingError instanceof Error ? processingError.message : 'Unknown error'}`,
          upload_id,
          error: processingError instanceof Error ? processingError.message : 'Unknown error'
        }, { status: 500 })
      }
    } else if (trigger_all) {
      // Process all pending uploads
      console.log('🚀 API: Processing all pending uploads')
      const pendingUploads = await AsyncUploadService.getPendingUploads()
      console.log(`📊 API: Found ${pendingUploads.length} pending uploads`)
      
      const processor = new UploadProcessor()
      const results = []
      
      for (let i = 0; i < pendingUploads.length; i++) {
        const upload = pendingUploads[i]
        console.log(`\n🔄 API: Processing upload ${i + 1}/${pendingUploads.length}: ${upload.upload_id}`)
        
        try {
          const result = await processor.processUpload(upload.upload_id.toString())
          console.log(`✅ API: Upload ${upload.upload_id} completed - ${result.successfulRows}/${result.totalRows} rows successful`)
          
          results.push({
            upload_id: upload.upload_id,
            success: result.successfulRows > 0,
            stats: result,
            processing_summary: {
              successful_rows: result.successfulRows,
              total_rows: result.totalRows,
              conflict_rows: result.conflictRows,
              processing_time_seconds: (result.processingTimeMs / 1000).toFixed(2)
            }
          })
        } catch (error) {
          console.error(`❌ API: Upload ${upload.upload_id} failed:`, error)
          
          results.push({
            upload_id: upload.upload_id,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      const totalSuccessful = results.filter(r => r.success).length
      console.log(`\n🎉 API: Batch processing completed - ${totalSuccessful}/${pendingUploads.length} uploads successful`)
      
      return NextResponse.json({
        success: true,
        message: `Processed ${pendingUploads.length} uploads (${totalSuccessful} successful)`,
        processed_count: pendingUploads.length,
        successful_count: totalSuccessful,
        results
      })
    } else {
      return NextResponse.json({
        success: false,
        message: 'Either upload_id or trigger_all must be provided'
      }, { status: 400 })
    }

  } catch (error) {
    console.error('❌ API: Process uploads error:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to process uploads',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('📊 API: Fetching pending uploads status')
    
    const pendingUploads = await AsyncUploadService.getPendingUploads()
    const allUploads = await AsyncUploadService.getAllUploads(50, 0)
    const processingUploads = allUploads.filter((upload: any) => upload.status === 'processing')
    
    console.log(`📊 API: Found ${pendingUploads.length} pending, ${processingUploads.length} processing`)
    
    // Get statistics for all uploads
    const completedUploads = allUploads.filter((upload: any) => upload.status === 'completed')
    const failedUploads = allUploads.filter((upload: any) => upload.status === 'failed')
    
    return NextResponse.json({
      success: true,
      worker_status: {
        pending_uploads: pendingUploads.length,
        processing_uploads: processingUploads.length,
        total_uploads: allUploads.length
      },
      pending_uploads: pendingUploads.map((upload: any) => ({
        upload_id: upload.upload_id,
        file_name: upload.file_name,
        total_rows: upload.total_rows,
        processed_until: upload.processed_until || 0,
        progress_percentage: upload.total_rows > 0 ? Math.round(((upload.processed_until || 0) / upload.total_rows) * 100) : 0,
        created_at: upload.created_at,
        processing_started_at: upload.processing_started_at,
        processing_completed_at: upload.processing_completed_at,
        status: upload.status,
        error_message: upload.error_message,
        companies_processed: upload.companies_processed,
        contacts_processed: upload.contacts_processed,
        conflicts_detected: upload.conflicts_detected
      })),
      recent_uploads: allUploads.slice(0, 20).map((upload: any) => ({
        upload_id: upload.upload_id,
        file_name: upload.file_name,
        total_rows: upload.total_rows,
        processed_until: upload.processed_until || 0,
        progress_percentage: upload.total_rows > 0 ? Math.round(((upload.processed_until || 0) / upload.total_rows) * 100) : 0,
        created_at: upload.created_at,
        processing_started_at: upload.processing_started_at,
        processing_completed_at: upload.processing_completed_at,
        status: upload.status,
        error_message: upload.error_message,
        companies_processed: upload.companies_processed,
        contacts_processed: upload.contacts_processed,
        conflicts_detected: upload.conflicts_detected
      })),
      statistics: {
        completed: completedUploads.length,
        failed: failedUploads.length,
        processing: processingUploads.length,
        pending: pendingUploads.length
      }
    })
  } catch (error) {
    console.error('❌ API: Get status error:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to get upload status',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
} 