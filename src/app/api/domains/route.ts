import { NextRequest, NextResponse } from 'next/server';
import { 
  fetchAllCompanyWebsites, 
  checkMatchingDomains, 
  getCompaniesWithMatchingDomains,
  getUniqueDomainsWithCompanies 
} from '@/lib/utils/domain';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    switch (action) {
      case 'websites':
        // Fetch all company websites
        const companies = await fetchAllCompanyWebsites();
        const paginatedCompanies = companies.slice(offset, offset + limit);
        
        return NextResponse.json({
          success: true,
          data: {
            companies: paginatedCompanies,
            total: companies.length,
            limit,
            offset,
            hasMore: offset + limit < companies.length
          }
        });

      case 'matching':
        // Get companies with matching domains
        const matchingResults = await getCompaniesWithMatchingDomains();
        const paginatedMatches = matchingResults.slice(offset, offset + limit);
        
        return NextResponse.json({
          success: true,
          data: {
            companies: paginatedMatches,
            total: matchingResults.length,
            limit,
            offset,
            hasMore: offset + limit < matchingResults.length
          }
        });

      case 'unique':
        // Get unique domains with companies
        const domainMap = await getUniqueDomainsWithCompanies();
        const domainsArray = Array.from(domainMap.entries()).map(([domain, companies]) => ({
          domain,
          companies,
          count: companies.length
        }));
        
        const paginatedDomains = domainsArray.slice(offset, offset + limit);
        
        return NextResponse.json({
          success: true,
          data: {
            domains: paginatedDomains,
            total: domainsArray.length,
            limit,
            offset,
            hasMore: offset + limit < domainsArray.length
          }
        });

      case 'check':
        // Check all companies for matching domains
        const allResults = await checkMatchingDomains();
        const paginatedResults = allResults.slice(offset, offset + limit);
        
        return NextResponse.json({
          success: true,
          data: {
            results: paginatedResults,
            total: allResults.length,
            limit,
            offset,
            hasMore: offset + limit < allResults.length
          }
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use: websites, matching, unique, or check',
          availableActions: [
            'websites - Fetch all company websites with extracted domains',
            'matching - Get companies with matching domains (duplicates)',
            'unique - Get unique domains with their associated companies',
            'check - Check all companies for matching domains'
          ]
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in domains API:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { urls } = body;

    if (!urls || !Array.isArray(urls)) {
      return NextResponse.json({
        success: false,
        error: 'Please provide an array of URLs in the request body'
      }, { status: 400 });
    }

    // Extract domains from provided URLs
    const { extractDomain } = await import('@/lib/utils/domain');
    const results = urls.map(url => ({
      url,
      extracted_domain: extractDomain(url)
    }));

    return NextResponse.json({
      success: true,
      data: results
    });
  } catch (error) {
    console.error('Error in domains API POST:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
