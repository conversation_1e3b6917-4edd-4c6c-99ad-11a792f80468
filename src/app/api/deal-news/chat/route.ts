import { NextResponse } from 'next/server';
import { pool } from '@/lib/db';
import OpenAI from 'openai';

export const dynamic = 'force-dynamic';
export const maxDuration = 60; // 60 seconds timeout

export async function POST(request: Request) {
  try {
    const { query, messages } = await request.json();

    if (!query) {
      return NextResponse.json({ error: 'Query is required' }, { status: 400 });
    }

    console.log('Chat query received:', query);

    // Initialize OpenAI client
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    // Get recent deals data
    const { dealsData, queryInfo } = await getRelevantDealsData(query);

    console.log(`Found ${dealsData.length} deals matching the query criteria`);
    
    // Format system message with deals data and query context
    const systemMessage = `
You are a helpful assistant specialized in real estate deal information. You have access to a database of real estate deals.
The following deals match the user's query: "${query}"

Query interpretation:
${JSON.stringify(queryInfo, null, 2)}

DEAL DATA (${dealsData.length} results):
${JSON.stringify(dealsData, null, 2)}

Instructions:
1. If the deals data has relevant information, use it to provide a detailed, informative response.
2. Format monetary values appropriately (e.g., "$10M" or "$1.5B").
3. Group similar deals together in your response when appropriate.
4. If date information is available, mention when deals occurred.
5. If no deals match the specific criteria, acknowledge this and suggest broadening the search.
6. IMPORTANT: Your answers must be based strictly on the provided deal data. Do not make up or assume information not present in the data.
7. If the data is empty or insufficient, be honest about the limitations of your knowledge.
`;

    const conversationHistory = messages.map((msg: any) => ({
      role: msg.role,
      content: msg.content,
    }));

    // Add system message at the beginning
    conversationHistory.unshift({
      role: 'system',
      content: systemMessage,
    });

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o', // Use the best available model, can be upgraded to 4.1 when available
      messages: conversationHistory,
      temperature: 0.3, // Lower temperature for more factual responses
      max_tokens: 1000,
    });

    const response = completion.choices[0].message.content || 'Sorry, I could not find an answer.';

    // Return response along with debug info in development
    return NextResponse.json({ 
      response,
      debug: process.env.NODE_ENV === 'development' ? {
        queryInfo,
        dealsCount: dealsData.length,
        // Only return a sample of the data to keep response size reasonable
        sampleData: dealsData.slice(0, 3)
      } : undefined
    });
  } catch (error: any) {
    console.error('Chat API error:', error);
    return NextResponse.json(
      { error: 'Failed to process request', message: error.message },
      { status: 500 }
    );
  }
}

async function getRelevantDealsData(query: string) {
  const cleanedQuery = query.toLowerCase();
  let sqlQuery = `
    SELECT 
      n.id,
      n.title,
      n.news_date,
      n.source_url,
      n.news_text
    FROM 
      news n
    WHERE 
      n.is_relevant = true
  `;

  const params: any[] = [];
  const queryInfo = {
    timeframe: null as string | null,
    location: null as string | null,
    propertyType: null as string | null,
    dealSize: null as string | null,
    sortBy: 'date' as string,
    sortDirection: 'desc' as string
  };
  
  // Add more sophisticated time period filtering
  if (cleanedQuery.includes('last week') || cleanedQuery.includes('past week') || cleanedQuery.includes('recent')) {
    sqlQuery += ` AND d.deal_date > NOW() - INTERVAL '7 days'`;
    queryInfo.timeframe = 'last 7 days';
  } else if (cleanedQuery.includes('last month') || cleanedQuery.includes('past month')) {
    sqlQuery += ` AND d.deal_date > NOW() - INTERVAL '30 days'`;
    queryInfo.timeframe = 'last 30 days';
  } else if (cleanedQuery.includes('this year') || cleanedQuery.includes('year to date')) {
    sqlQuery += ` AND d.deal_date > DATE_TRUNC('year', CURRENT_DATE)`;
    queryInfo.timeframe = 'year to date';
  } else if (cleanedQuery.includes('Q1') || cleanedQuery.includes('first quarter')) {
    const year = new Date().getFullYear();
    sqlQuery += ` AND d.deal_date BETWEEN '${year}-01-01' AND '${year}-03-31'`;
    queryInfo.timeframe = `Q1 ${year}`;
  } else if (cleanedQuery.includes('Q2') || cleanedQuery.includes('second quarter')) {
    const year = new Date().getFullYear();
    sqlQuery += ` AND d.deal_date BETWEEN '${year}-04-01' AND '${year}-06-30'`;
    queryInfo.timeframe = `Q2 ${year}`;
  } else if (cleanedQuery.includes('Q3') || cleanedQuery.includes('third quarter')) {
    const year = new Date().getFullYear();
    sqlQuery += ` AND d.deal_date BETWEEN '${year}-07-01' AND '${year}-09-30'`;
    queryInfo.timeframe = `Q3 ${year}`;
  } else if (cleanedQuery.includes('Q4') || cleanedQuery.includes('fourth quarter')) {
    const year = new Date().getFullYear();
    sqlQuery += ` AND d.deal_date BETWEEN '${year}-10-01' AND '${year}-12-31'`;
    queryInfo.timeframe = `Q4 ${year}`;
  }

  // Enhanced location filtering - support for states and regions too
  const locations = [
    // Major cities
    'new york', 'los angeles', 'chicago', 'houston', 'phoenix', 'philadelphia',
    'san antonio', 'san diego', 'dallas', 'san jose', 'austin', 'atlanta', 'miami',
    'boston', 'seattle', 'denver', 'washington dc', 'nashville', 'portland', 'las vegas',
    
    // States
    'california', 'texas', 'florida', 'new york state', 'illinois', 'pennsylvania',
    'ohio', 'georgia', 'north carolina', 'michigan', 'new jersey', 'virginia',
    'washington', 'arizona', 'massachusetts', 'tennessee', 'indiana', 'missouri',
    'maryland', 'wisconsin', 'minnesota', 'colorado', 'alabama', 'south carolina',
    'louisiana', 'kentucky', 'oregon', 'oklahoma', 'connecticut', 'iowa', 'mississippi',
    'arkansas', 'utah', 'nevada', 'new mexico', 'west virginia', 'nebraska',
    'idaho', 'hawaii', 'maine', 'new hampshire', 'rhode island', 'montana',
    'delaware', 'south dakota', 'north dakota', 'alaska', 'vermont', 'wyoming',
    
    // Regions
    'northeast', 'midwest', 'south', 'west', 'southwest', 'southeast', 'northwest', 'northeast'
  ];
  
  for (const location of locations) {
    if (cleanedQuery.includes(location)) {
      sqlQuery += ` AND (
        LOWER(d.location) LIKE $${params.length + 1}
        OR LOWER(d.title) LIKE $${params.length + 1}
        OR LOWER(COALESCE(d.summary, '')) LIKE $${params.length + 1}
      )`;
      params.push(`%${location}%`);
      queryInfo.location = location;
      break;
    }
  }

  // Enhanced property type filtering
  const propertyTypes = [
    'office', 'retail', 'industrial', 'multifamily', 'residential', 'hotel', 'hospitality',
    'mixed-use', 'mixed use', 'commercial', 'warehouse', 'distribution', 'self-storage',
    'self storage', 'student housing', 'affordable housing', 'senior housing', 'medical office', 
    'healthcare', 'life science', 'data center', 'shopping center', 'mall'
  ];
  
  for (const type of propertyTypes) {
    if (cleanedQuery.includes(type)) {
      sqlQuery += ` AND (
        LOWER(d.property_type) LIKE $${params.length + 1}
        OR LOWER(d.title) LIKE $${params.length + 1}
        OR LOWER(COALESCE(d.summary, '')) LIKE $${params.length + 1}
      )`;
      params.push(`%${type}%`);
      queryInfo.propertyType = type;
      break;
    }
  }

  // Better deal size parsing
  const amountRegex = /(\$?\d+(\.\d+)?)\s?(million|billion|m|b|mm)/i;
  const match = cleanedQuery.match(amountRegex);
  if (match) {
    const amount = parseFloat(match[1]);
    const unit = match[3].toLowerCase();
    let multiplier = 1;
    
    if (unit === 'million' || unit === 'm' || unit === 'mm') {
      multiplier = 1000000;
    } else if (unit === 'billion' || unit === 'b') {
      multiplier = **********;
    }
    
    const value = amount * multiplier;
    sqlQuery += ` AND d.deal_amount >= ${value}`;
    queryInfo.dealSize = `$${amount}${unit.charAt(0).toUpperCase()}+`;
  }
  
  // More intelligent ordering based on query
  if (cleanedQuery.includes('largest') || cleanedQuery.includes('biggest') || 
      cleanedQuery.includes('highest') || cleanedQuery.includes('most expensive')) {
    sqlQuery += ` ORDER BY d.deal_amount DESC NULLS LAST`;
    queryInfo.sortBy = 'amount';
    queryInfo.sortDirection = 'desc';
  } else if (cleanedQuery.includes('smallest') || cleanedQuery.includes('least expensive')) {
    sqlQuery += ` ORDER BY d.deal_amount ASC NULLS LAST`;
    queryInfo.sortBy = 'amount';
    queryInfo.sortDirection = 'asc';
  } else if (cleanedQuery.includes('newest') || cleanedQuery.includes('latest') || 
            cleanedQuery.includes('recent')) {
    sqlQuery += ` ORDER BY d.deal_date DESC NULLS LAST`;
    queryInfo.sortBy = 'date';
    queryInfo.sortDirection = 'desc';
  } else if (cleanedQuery.includes('oldest')) {
    sqlQuery += ` ORDER BY d.deal_date ASC NULLS LAST`;
    queryInfo.sortBy = 'date';
    queryInfo.sortDirection = 'asc';
  } 
  // Default ordering 
  else if (!sqlQuery.includes('ORDER BY')) {
    sqlQuery += ` ORDER BY d.deal_date DESC NULLS LAST`;
  }
  
  // If we're looking for "top deals", limit to top 10
  let limit = 20;
  if (cleanedQuery.includes('top')) {
    if (cleanedQuery.match(/top\s+(\d+)/i)) {
      const topN = parseInt(cleanedQuery.match(/top\s+(\d+)/i)![1]);
      limit = Math.min(Math.max(topN, 1), 50); // Between 1 and 50
    } else {
      limit = 10; // Default for "top deals"
    }
  }
  
  sqlQuery += ` LIMIT ${limit}`;
  
  try {
    console.log('Executing SQL query:', sqlQuery, params);
    const { rows } = await pool.query(sqlQuery, params);
    console.log(`Query returned ${rows.length} results`);
    
    // Format the data to be more useful
    const formattedData = rows.map(row => {
      // Try to parse the extracted_details JSON if it exists
      let extractedDetails = {};
      if (row.extracted_details) {
        try {
          if (typeof row.extracted_details === 'string') {
            extractedDetails = JSON.parse(row.extracted_details);
          } else {
            extractedDetails = row.extracted_details;
          }
        } catch (e) {
          console.log('Failed to parse extracted_details:', e);
        }
      }
      
      // Format deal amount for display
      let formattedAmount = 'Not specified';
      if (row.deal_amount) {
        const amount = parseFloat(row.deal_amount);
        if (amount >= **********) {
          formattedAmount = `$${(amount / **********).toFixed(2)}B`;
        } else if (amount >= 1000000) {
          formattedAmount = `$${(amount / 1000000).toFixed(2)}M`;
        } else {
          formattedAmount = `$${amount.toLocaleString()}`;
        }
      }
      
      return {
        ...row,
        deal_amount_formatted: formattedAmount,
        extracted_details: extractedDetails
      };
    });
    
    return { dealsData: formattedData, queryInfo };
  } catch (error) {
    console.error('Database query error:', error);
    return { dealsData: [], queryInfo };
  }
} 