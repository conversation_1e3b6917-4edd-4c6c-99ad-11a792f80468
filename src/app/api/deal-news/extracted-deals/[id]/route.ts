import { NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export const dynamic = 'force-dynamic';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
    const parameters = await params;
    const enrichmentId = parameters.id;
  
  if (!enrichmentId || isNaN(Number(enrichmentId))) {
    return NextResponse.json({ error: 'Invalid enrichment ID' }, { status: 400 });
  }

  try {
    const client = await pool.connect();
    try {
      // Get the enriched news data
      const enrichmentQuery = `
        SELECT 
          ne.id,
          ne.news_id,
          ne.deal_type,
          ne.deal_status,
          ne.publication_date as deal_date,
          ne.deal_size as deal_value,
          'USD' as currency,
          ne.cap_rate,
          null as ltv, -- not in current schema
          ne.source_confidence as confidence_score,
          ne.property_type,
          ne.square_footage as property_size,
          'sqft' as property_size_unit,
          CASE 
            WHEN ne.address IS NOT NULL AND jsonb_typeof(ne.address) = 'array' AND jsonb_array_length(ne.address) > 0 
            THEN (SELECT string_agg(value::text, ', ') FROM jsonb_array_elements_text(ne.address))
            WHEN ne.address IS NOT NULL AND jsonb_typeof(ne.address) = 'string'
            THEN ne.address::text
            ELSE null
          END as property_address,
          json_build_object(
            'propertyName', CASE 
              WHEN ne.project_name IS NOT NULL AND jsonb_typeof(ne.project_name) = 'array' AND jsonb_array_length(ne.project_name) > 0
              THEN (SELECT value FROM jsonb_array_elements_text(ne.project_name) LIMIT 1)
              WHEN ne.project_name IS NOT NULL AND jsonb_typeof(ne.project_name) = 'string'
              THEN ne.project_name::text
              ELSE null
            END,
            'strategies', COALESCE(ne.strategies, '[]'::jsonb),
            'subPropertyType', ne.sub_property_type,
            'unitCount', ne.unit_count,
            'constructionType', ne.construction_type,
            'projectTimeline', ne.project_timeline,
            'jobCreation', ne.job_creation,
            'subsidyInfo', ne.subsidy_info
          ) as deal_details,
          ne.created_at,
          ne.headline as news_title,
          ne.publication_date as news_date,
          ne.body_text as news_text,
          ne.source_url as news_url,
          
          -- Entity information as structured data
          ne.buyer_name,
          ne.seller_name,
          ne.company_name,
          ne.lender_name,
          ne.broker_name,
          ne.equity_partner,
          ne.developer_name,
          ne.tenant_name,
          ne.report_author,
          ne.linked_entities,
          
          -- Market and financial information
          ne.financing_type,
          ne.capital_stack_notes,
          ne.fund_name,
          ne.fund_type,
          ne.fund_size,
          ne.fund_strategy,
          ne.distress_flag,
          ne.loan_performance,
          
          -- Location details
          ne.location_city,
          ne.location_state,
          ne.location_neighborhood,
          ne.zip_code,
          
          -- Market context
          ne.market_name,
          ne.submarket_name,
          ne.sentiment_summary,
          ne.forecast_summary,
          ne.macroeconomic_commentary,
          
          -- System fields
          ne.llm_tags,
          ne.quotes_llm_tags,
          ne.extraction_notes
        FROM news_enrichment ne
        WHERE ne.id = $1 AND ne.is_deal_specific = true
      `;

      const enrichmentResult = await client.query(enrichmentQuery, [enrichmentId]);
      
      if (enrichmentResult.rows.length === 0) {
        return NextResponse.json({ error: 'Deal not found' }, { status: 404 });
      }
      
      const enrichment = enrichmentResult.rows[0];
      
      // Transform JSONB company data into structured format matching the old API
      const transformCompaniesFromJsonb = (jsonbArray: any[], role: string) => {
        if (!jsonbArray || !Array.isArray(jsonbArray)) return [];
        
        return jsonbArray.map((name, index) => ({
          id: `${role.toLowerCase()}-${index}`,
          company_name: name,
          role: role,
          confidence_score: enrichment.confidence_score || 0.8,
          participation_details: null,
          created_at: enrichment.created_at
        }));
      };
      
      // Extract companies from JSONB arrays
      const companies = [
        ...transformCompaniesFromJsonb(enrichment.buyer_name, 'Buyer'),
        ...transformCompaniesFromJsonb(enrichment.seller_name, 'Seller'),
        ...transformCompaniesFromJsonb(enrichment.company_name, 'Company'),
        ...transformCompaniesFromJsonb(enrichment.lender_name, 'Lender'),
        ...transformCompaniesFromJsonb(enrichment.broker_name, 'Broker'),
        ...transformCompaniesFromJsonb(enrichment.equity_partner, 'Equity Partner'),
        ...transformCompaniesFromJsonb(enrichment.developer_name, 'Developer'),
        ...transformCompaniesFromJsonb(enrichment.tenant_name, 'Tenant')
      ];
      
      // Transform linked entities into persons format
      const persons = (enrichment.linked_entities && Array.isArray(enrichment.linked_entities) 
        ? enrichment.linked_entities.map((entity: any, index: number) => ({
            id: `person-${index}`,
            person_name: entity.name || 'Unknown',
            title: entity.role || null,
            role: entity.role || 'Mentioned',
            confidence_score: entity.confidenceScore || enrichment.confidence_score || 0.8,
            involvement_details: { context: entity.context || null },
            company_id: null,
            created_at: enrichment.created_at
          }))
        : []);
      
      // Also extract from report_author
      if (enrichment.report_author && Array.isArray(enrichment.report_author)) {
        enrichment.report_author.forEach((author: string, index: number) => {
          persons.push({
            id: `author-${index}`,
            person_name: author,
            title: 'Report Author',
            role: 'Author',
            confidence_score: enrichment.confidence_score || 0.9,
            involvement_details: null,
            company_id: null,
            created_at: enrichment.created_at
          });
        });
      }
      
      // Transform deal_value from millions to dollars for display
      const transformedEnrichment = {
        ...enrichment,
        deal_value: enrichment.deal_value ? enrichment.deal_value * 1000000 : 0,
        companies,
        persons,
        
        // Add additional context from the enrichment
        market_context: {
          marketName: enrichment.market_name,
          submarketName: enrichment.submarket_name,
          sentimentSummary: enrichment.sentiment_summary,
          forecastSummary: enrichment.forecast_summary,
          macroeconomicCommentary: enrichment.macroeconomic_commentary
        },
        
        financial_context: {
          financingType: enrichment.financing_type,
          capitalStackNotes: enrichment.capital_stack_notes,
          fundName: enrichment.fund_name,
          fundType: enrichment.fund_type,
          fundSize: enrichment.fund_size ? enrichment.fund_size * 1000000 : null,
          fundStrategy: enrichment.fund_strategy,
          distressFlag: enrichment.distress_flag,
          loanPerformance: enrichment.loan_performance
        },
        
        extraction_metadata: {
          llmTags: enrichment.llm_tags,
          quotesLlmTags: enrichment.quotes_llm_tags,
          extractionNotes: enrichment.extraction_notes,
          sourceConfidence: enrichment.confidence_score
        }
      };

      return NextResponse.json(transformedEnrichment);
    } finally {
      client.release();
    }
  } catch (error) {
    console.error(`Error fetching enrichment ${enrichmentId}:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch deal details' },
      { status: 500 }
    );
  }
} 