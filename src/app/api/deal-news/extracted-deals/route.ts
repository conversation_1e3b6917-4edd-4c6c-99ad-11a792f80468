import { NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1');
  const pageSize = parseInt(searchParams.get('pageSize') || '20');
  const search = searchParams.get('search') || '';
  const sort = searchParams.get('sort') || 'date_desc';
  const source = searchParams.get('source') || '';
  
  const offset = (page - 1) * pageSize;
  
  try {
    const client = await pool.connect();
    try {
      // Build the WHERE clause for searching - exclude bad URLs
      let whereClause = 'WHERE ne.is_deal_specific = true AND EXISTS (SELECT 1 FROM news n WHERE n.id = ne.news_id AND (n.bad_url IS NULL OR n.bad_url = false))';
      const searchParams: any[] = [];
      let paramCount = 0;
      
      if (search) {
        paramCount++;
        whereClause += `
          AND (
            ne.deal_type::text ILIKE $${paramCount} OR
            ne.deal_status ILIKE $${paramCount} OR
            ne.property_type::text ILIKE $${paramCount} OR
            ne.address::text ILIKE $${paramCount} OR
            ne.company_name::text ILIKE $${paramCount} OR
            ne.buyer_name::text ILIKE $${paramCount} OR
            ne.seller_name::text ILIKE $${paramCount} OR
            ne.headline ILIKE $${paramCount}
          )
        `;
        searchParams.push(`%${search}%`);
      }
      
      // Add source filter if provided
      if (source) {
        paramCount++;
        whereClause += ` AND ne.source_name = $${paramCount}`;
        searchParams.push(source);
      }
      
      // Determine the ORDER BY clause based on sort parameter
      let orderByClause = '';
      switch (sort) {
        case 'date_asc':
          orderByClause = 'ORDER BY ne.created_at ASC NULLS LAST, ne.id DESC';
          break;
        case 'date_desc':
          orderByClause = 'ORDER BY ne.created_at DESC NULLS LAST, ne.id DESC';
          break;
        case 'value_asc':
          orderByClause = 'ORDER BY ne.deal_size ASC NULLS LAST, ne.id DESC';
          break;
        case 'value_desc':
          orderByClause = 'ORDER BY ne.deal_size DESC NULLS LAST, ne.id DESC';
          break;
        default:
          orderByClause = 'ORDER BY ne.publication_date DESC NULLS LAST, ne.created_at DESC NULLS LAST, ne.id DESC';
      }
      
      // Count total deals matching search criteria
      const countQuery = `
        SELECT COUNT(*) as total
        FROM news_enrichment ne
        ${whereClause}
      `;
      
      const countResult = await client.query(countQuery, searchParams);
      const total = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(total / pageSize);
      
      // Fetch deals from news_enrichment - restructured to avoid CASE with set-returning functions
      const dealsQuery = `
        SELECT 
          ne.id,
          ne.news_id,
          ne.deal_type,
          ne.deal_status,
          ne.publication_date as deal_date,
          ne.deal_size as deal_value,
          'USD' as currency,
          ne.cap_rate,
          null as ltv,
          ne.source_confidence as confidence_score,
          ne.property_type,
          ne.square_footage as property_size,
          'sqft' as property_size_unit,
          -- Handle address properly with array_to_string
          CASE 
            WHEN jsonb_typeof(COALESCE(ne.address, '[]'::jsonb)) = 'array' AND jsonb_array_length(COALESCE(ne.address, '[]'::jsonb)) > 0 
            THEN array_to_string(ARRAY(SELECT jsonb_array_elements_text(ne.address)), ', ')
            ELSE null
          END as property_address,
          ne.created_at,
          ne.headline as news_title,
          ne.publication_date as news_date,
          ne.source_name as news_source,
          -- Build companies object with proper JSONB handling
          json_build_object(
            'buyers', COALESCE(ne.buyer_name, '[]'::jsonb),
            'sellers', COALESCE(ne.seller_name, '[]'::jsonb),
            'companies', COALESCE(ne.company_name, '[]'::jsonb),
            'lenders', COALESCE(ne.lender_name, '[]'::jsonb),
            'brokers', COALESCE(ne.broker_name, '[]'::jsonb)
          ) as companies
        FROM news_enrichment ne
        ${whereClause}
        ${orderByClause}
        LIMIT $${searchParams.length + 1} OFFSET $${searchParams.length + 2}
      `;
      
      const dealsResult = await client.query(
        dealsQuery, 
        [...searchParams, pageSize, offset]
      );
      
      // Transform the results to match the expected format
      const transformedDeals = dealsResult.rows.map(deal => {
        // Convert deal_value from millions to dollars if needed
        const dealValue = deal.deal_value ? deal.deal_value * 1000000 : 0;
        
        // Extract companies from JSONB arrays safely
        const companies: Array<{id: number, company_name: string, role: string}> = [];
        if (deal.companies) {
          const companiesObj = deal.companies;
          
          // Helper function to safely extract array from JSONB
          const extractArray = (jsonbArray: any): string[] => {
            if (!jsonbArray) return [];
            if (Array.isArray(jsonbArray)) return jsonbArray;
            try {
              return JSON.parse(jsonbArray);
            } catch {
              return [];
            }
          };
          
          // Add each company type
          extractArray(companiesObj.buyers).forEach((name: string) => {
            companies.push({ id: Math.random(), company_name: name, role: 'Buyer' });
          });
          extractArray(companiesObj.sellers).forEach((name: string) => {
            companies.push({ id: Math.random(), company_name: name, role: 'Seller' });
          });
          extractArray(companiesObj.companies).forEach((name: string) => {
            companies.push({ id: Math.random(), company_name: name, role: 'Company' });
          });
          extractArray(companiesObj.lenders).forEach((name: string) => {
            companies.push({ id: Math.random(), company_name: name, role: 'Lender' });
          });
          extractArray(companiesObj.brokers).forEach((name: string) => {
            companies.push({ id: Math.random(), company_name: name, role: 'Broker' });
          });
        }
        
        return {
          ...deal,
          deal_value: dealValue,
          companies: companies
        };
      });
      
      // Get source statistics
      const sourceStatsQuery = `
        SELECT 
          ne.source_name as news_source,
          COUNT(ne.id) as deal_count,
          COUNT(CASE WHEN DATE(ne.created_at AT TIME ZONE 'UTC') >= DATE(NOW()) THEN ne.id END) as today_count
        FROM 
          news_enrichment ne
        WHERE
          ne.is_deal_specific = true
        GROUP BY 
          ne.source_name
        ORDER BY 
          deal_count DESC
      `;
      
      const sourceStatsResult = await client.query(sourceStatsQuery);
      
      // Get daily statistics for last 7 days
      const dailyStatsQuery = `
        SELECT 
          ne.source_name as news_source,
          DATE(ne.created_at AT TIME ZONE 'UTC') as date,
          COUNT(ne.id) as count
        FROM 
          news_enrichment ne
        WHERE 
          ne.is_deal_specific = true
          AND DATE(ne.created_at AT TIME ZONE 'UTC') >= DATE(NOW()) - INTERVAL '7 days'
        GROUP BY 
          ne.source_name, DATE(ne.created_at AT TIME ZONE 'UTC')
        ORDER BY 
          date ASC, count DESC
      `;
      
      const dailyStatsResult = await client.query(dailyStatsQuery);
      
      return NextResponse.json({
        deals: transformedDeals,
        total,
        page,
        totalPages,
        pageSize,
        sources: sourceStatsResult.rows,
        dailyStats: dailyStatsResult.rows
      });
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error fetching deals:', error);
    return NextResponse.json(
      { error: 'Failed to fetch deals' },
      { status: 500 }
    );
  }
} 