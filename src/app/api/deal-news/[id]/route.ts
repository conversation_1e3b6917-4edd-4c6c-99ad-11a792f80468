import { NextResponse } from 'next/server';
import { pool } from '@/lib/db';

function isError(error: unknown): error is Error {
  return error instanceof Error;
}

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
  ) {
    const resolvedParams = await params
    const newsId = resolvedParams.id;

  if (!newsId || isNaN(Number(newsId))) {
    return NextResponse.json({ error: 'Valid ID is required' }, { status: 400 });
  }

  let client;

  try {
    client = await pool.connect();

    // Get the news item with all available columns
    const newsResult = await client.query(`
      SELECT
        id,
        news_source,
        url,
        news_text,
        news_date,
        created_at,
        updated_at,
        news_title,
        raw_html,
        is_relevant,
        enriched,
        fetched,
        fetch_error,
        enrichment_status,
        enrichment_error,
        bad_url
      FROM news
      WHERE id = $1
    `, [newsId]);

    if (newsResult.rows.length === 0) {
      return NextResponse.json({ error: 'News item not found' }, { status: 404 });
    }

    const newsItem = newsResult.rows[0];

    // Get deals related to this news item from news_enrichment
    const dealsResult = await client.query(`
      SELECT
        id,
        news_id,
        deal_type,
        deal_status,
        publication_date as deal_date,
        deal_size as deal_value,
        'USD' as currency,
        cap_rate,
        null as ltv,
        source_confidence as confidence_score,
        property_type,
        square_footage as property_size,
        'sqft' as property_size_unit,
        CASE 
          WHEN jsonb_array_length(COALESCE(address, '[]'::jsonb)) > 0 
          THEN (SELECT string_agg(value::text, ', ') FROM jsonb_array_elements_text(address))
          ELSE null
        END as property_address,
        json_build_object(
          'propertyName', COALESCE((SELECT value FROM jsonb_array_elements_text(project_name) LIMIT 1), null),
          'strategies', COALESCE(strategies, '[]'::jsonb),
          'subPropertyType', sub_property_type,
          'unitCount', unit_count,
          'squareFootage', square_footage,
          'constructionType', construction_type,
          'projectTimeline', project_timeline,
          'jobCreation', job_creation,
          'subsidyInfo', subsidy_info,
          'financing', json_build_object(
            'financingType', financing_type,
            'capitalStackNotes', capital_stack_notes,
            'fundName', fund_name,
            'fundType', fund_type,
            'fundSize', fund_size,
            'fundStrategy', fund_strategy
          )
        ) as deal_details,
        created_at,
        CASE 
          WHEN jsonb_array_length(COALESCE(project_name, '[]'::jsonb)) > 0 
          THEN (SELECT value FROM jsonb_array_elements_text(project_name) LIMIT 1)
          ELSE null
        END as property_name,
        json_build_object(
          'city', location_city,
          'state', location_state,
          'neighborhood', location_neighborhood,
          'zipCode', zip_code
        ) as location_details,
        deal_type as deal_type_normalized,
        null as capital_position, -- not directly mapped in new schema
        strategies,
        json_build_array(property_type) as property_types,
        json_build_array(sub_property_type) as property_subcategories,
        square_footage as square_feet,
        unit_count as units,
        json_build_object(
          'buyers', buyer_name,
          'sellers', seller_name,
          'companies', company_name,
          'lenders', lender_name,
          'brokers', broker_name,
          'equityPartners', equity_partner,
          'developers', developer_name,
          'tenants', tenant_name
        ) as participant_companies,
        null as deal_type_normalized_specialty
      FROM news_enrichment
      WHERE news_id = $1 AND is_deal_specific = true
      ORDER BY created_at DESC
    `, [newsId]);

    // Transform deals to match expected format (convert millions to dollars)
    const transformedDeals = dealsResult.rows.map(deal => ({
      ...deal,
      deal_value: deal.deal_value ? deal.deal_value * 1000000 : 0
    }));

    // Get companies from enrichment data - flatten JSONB arrays into individual records
    const companiesResult = await client.query(`
      WITH company_data AS (
        SELECT 
          ne.id as enrichment_id,
          ne.news_id,
          ne.created_at,
          ne.source_confidence,
          'Buyer' as role,
          jsonb_array_elements_text(ne.buyer_name) as company_name
        FROM news_enrichment ne 
        WHERE ne.news_id = $1 AND jsonb_array_length(COALESCE(ne.buyer_name, '[]'::jsonb)) > 0
        
        UNION ALL
        
        SELECT 
          ne.id as enrichment_id,
          ne.news_id,
          ne.created_at,
          ne.source_confidence,
          'Seller' as role,
          jsonb_array_elements_text(ne.seller_name) as company_name
        FROM news_enrichment ne 
        WHERE ne.news_id = $1 AND jsonb_array_length(COALESCE(ne.seller_name, '[]'::jsonb)) > 0
        
        UNION ALL
        
        SELECT 
          ne.id as enrichment_id,
          ne.news_id,
          ne.created_at,
          ne.source_confidence,
          'Company' as role,
          jsonb_array_elements_text(ne.company_name) as company_name
        FROM news_enrichment ne 
        WHERE ne.news_id = $1 AND jsonb_array_length(COALESCE(ne.company_name, '[]'::jsonb)) > 0
        
        UNION ALL
        
        SELECT 
          ne.id as enrichment_id,
          ne.news_id,
          ne.created_at,
          ne.source_confidence,
          'Lender' as role,
          jsonb_array_elements_text(ne.lender_name) as company_name
        FROM news_enrichment ne 
        WHERE ne.news_id = $1 AND jsonb_array_length(COALESCE(ne.lender_name, '[]'::jsonb)) > 0
        
        UNION ALL
        
        SELECT 
          ne.id as enrichment_id,
          ne.news_id,
          ne.created_at,
          ne.source_confidence,
          'Broker' as role,
          jsonb_array_elements_text(ne.broker_name) as company_name
        FROM news_enrichment ne 
        WHERE ne.news_id = $1 AND jsonb_array_length(COALESCE(ne.broker_name, '[]'::jsonb)) > 0
        
        UNION ALL
        
        SELECT 
          ne.id as enrichment_id,
          ne.news_id,
          ne.created_at,
          ne.source_confidence,
          'Equity Partner' as role,
          jsonb_array_elements_text(ne.equity_partner) as company_name
        FROM news_enrichment ne 
        WHERE ne.news_id = $1 AND jsonb_array_length(COALESCE(ne.equity_partner, '[]'::jsonb)) > 0
        
        UNION ALL
        
        SELECT 
          ne.id as enrichment_id,
          ne.news_id,
          ne.created_at,
          ne.source_confidence,
          'Developer' as role,
          jsonb_array_elements_text(ne.developer_name) as company_name
        FROM news_enrichment ne 
        WHERE ne.news_id = $1 AND jsonb_array_length(COALESCE(ne.developer_name, '[]'::jsonb)) > 0
        
        UNION ALL
        
        SELECT 
          ne.id as enrichment_id,
          ne.news_id,
          ne.created_at,
          ne.source_confidence,
          'Tenant' as role,
          jsonb_array_elements_text(ne.tenant_name) as company_name
        FROM news_enrichment ne 
        WHERE ne.news_id = $1 AND jsonb_array_length(COALESCE(ne.tenant_name, '[]'::jsonb)) > 0
      )
      SELECT 
        ROW_NUMBER() OVER (ORDER BY created_at DESC) as id,
        news_id,
        null as deal_id,
        company_name,
        role,
        source_confidence as confidence_score,
        null as participation_details,
        created_at,
        null as company_context
      FROM company_data
      ORDER BY source_confidence DESC, created_at DESC
    `, [newsId]);

    // Get persons from enrichment data - extract from linked_entities and report_author
    const personsResult = await client.query(`
      WITH person_data AS (
        -- Extract from linked_entities JSONB
        SELECT 
          ne.news_id,
          ne.created_at,
          (entity->>'name')::text as person_name,
          (entity->>'role')::text as title,
          (entity->>'role')::text as role,
          COALESCE((entity->>'confidenceScore')::decimal, ne.source_confidence, 0.8) as confidence_score,
          json_build_object('context', entity->>'context') as involvement_details,
          null as company_id,
          entity->>'context' as person_context,
          null as company_name
        FROM news_enrichment ne,
             jsonb_array_elements(COALESCE(ne.linked_entities, '[]'::jsonb)) as entity
        WHERE ne.news_id = $1
        
        UNION ALL
        
        -- Extract from report_author
        SELECT 
          ne.news_id,
          ne.created_at,
          author as person_name,
          'Report Author' as title,
          'Author' as role,
          ne.source_confidence as confidence_score,
          null as involvement_details,
          null as company_id,
          'Article author' as person_context,
          null as company_name
        FROM news_enrichment ne,
             jsonb_array_elements_text(COALESCE(ne.report_author, '[]'::jsonb)) as author
        WHERE ne.news_id = $1
      )
      SELECT 
        ROW_NUMBER() OVER (ORDER BY confidence_score DESC, created_at DESC) as id,
        news_id,
        null as deal_id,
        null as company_id,
        person_name,
        title,
        role,
        confidence_score,
        involvement_details,
        created_at,
        person_context,
        company_name
      FROM person_data
      ORDER BY confidence_score DESC, created_at DESC
    `, [newsId]);

    // Combine all data
    const responseData = {
      ...newsItem,
      deals: transformedDeals,
      companies: companiesResult.rows,
      persons: personsResult.rows,
    };

    return NextResponse.json(responseData);
  } catch (error: unknown) {
    console.error('Database Error:', error);
    const errorMessage = isError(error) ? error.message : 'Unknown database error';
    return NextResponse.json(
      { error: 'Database error', details: errorMessage },
      { status: 500 }
    );
  } finally {
    if (client) {
      client.release();
    }
  }
} 

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const newsId = parseInt(id);
    
    if (isNaN(newsId)) {
      return NextResponse.json(
        { error: 'Invalid news ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { action } = body;

    if (action !== 'mark_bad_url') {
      return NextResponse.json(
        { error: 'Invalid action' },
        { status: 400 }
      );
    }

    const client = await pool.connect();
    
    try {
      // Update the news item to mark as bad URL
      const result = await client.query(
        `UPDATE news 
         SET bad_url = true, 
             updated_at = CURRENT_TIMESTAMP 
         WHERE id = $1 
         RETURNING id, url, bad_url`,
        [newsId]
      );

      if (result.rows.length === 0) {
        return NextResponse.json(
          { error: 'News item not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'URL marked as bad successfully',
        data: result.rows[0]
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error marking URL as bad:', error);
    return NextResponse.json(
      { error: 'Failed to mark URL as bad' },
      { status: 500 }
    );
  }
} 