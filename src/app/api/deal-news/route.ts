import { NextResponse } from 'next/server';
import { pool } from '@/lib/db';

const ITEMS_PER_PAGE = 50;

function isError(error: unknown): error is Error {
  return error instanceof Error;
}

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const source = searchParams.get('source');
  const page = parseInt(searchParams.get('page') || '1');
  const lastId = searchParams.get('lastId');
  const onlyFetched = searchParams.get('onlyFetched') === 'true';
  const onlyExtracted = searchParams.get('onlyExtracted') === 'true';
  const titleSearch = searchParams.get('titleSearch') || '';
  const orderBy = searchParams.get('orderBy') || 'id'; // 'id' or 'updated_at'
  
  if (!source) {
    return NextResponse.json({ error: 'Source is required' }, { status: 400 });
  }

  let client;
  
  try {
    client = await pool.connect();

    // If no source specified, return stats
    if (source === 'stats') {
      const result = await client.query(`
        WITH source_updates AS (
          SELECT
            LOWER(news_source) as news_source,
            MAX(created_at) as last_checked
          FROM news
          WHERE (bad_url IS NULL OR bad_url = false)
          GROUP BY LOWER(news_source)
        )
        SELECT
          (SELECT COUNT(url) FROM news) as total_urls,
          (SELECT COUNT(*) FROM news WHERE news_text IS NOT NULL AND fetched = true) as processed_items,
          (SELECT COUNT(DISTINCT LOWER(news_source)) FROM news) as active_sources,
          (SELECT COUNT(*) FROM news_enrichment WHERE is_deal_specific = true) as deals_captured,
          (SELECT MAX(created_at) FROM news) as last_updated,
          (SELECT COUNT(*) FROM news WHERE DATE(created_at) = CURRENT_DATE) as today_total,
          (SELECT COUNT(DISTINCT company_name) FROM (
            SELECT jsonb_array_elements_text(buyer_name) as company_name FROM news_enrichment WHERE buyer_name IS NOT NULL AND buyer_name != '[]'
            UNION
            SELECT jsonb_array_elements_text(seller_name) FROM news_enrichment WHERE seller_name IS NOT NULL AND seller_name != '[]'
            UNION  
            SELECT jsonb_array_elements_text(company_name) FROM news_enrichment WHERE company_name IS NOT NULL AND company_name != '[]'
            UNION
            SELECT jsonb_array_elements_text(lender_name) FROM news_enrichment WHERE lender_name IS NOT NULL AND lender_name != '[]'
            UNION
            SELECT jsonb_array_elements_text(broker_name) FROM news_enrichment WHERE broker_name IS NOT NULL AND broker_name != '[]'
            UNION
            SELECT jsonb_array_elements_text(equity_partner) FROM news_enrichment WHERE equity_partner IS NOT NULL AND equity_partner != '[]'
            UNION
            SELECT jsonb_array_elements_text(developer_name) FROM news_enrichment WHERE developer_name IS NOT NULL AND developer_name != '[]'
            UNION
            SELECT jsonb_array_elements_text(tenant_name) FROM news_enrichment WHERE tenant_name IS NOT NULL AND tenant_name != '[]'
          ) companies) as companies_captured,
          (SELECT COUNT(DISTINCT person_name) FROM (
            SELECT (entity->>'name')::text as person_name
            FROM news_enrichment, jsonb_array_elements(COALESCE(linked_entities, '[]'::jsonb)) as entity
            WHERE linked_entities IS NOT NULL AND linked_entities != '[]'
            UNION
            SELECT jsonb_array_elements_text(report_author)::text 
            FROM news_enrichment 
            WHERE report_author IS NOT NULL AND report_author != '[]'
          ) people) as people_captured,
          (SELECT COUNT(*) FROM news_enrichment WHERE is_deal_specific = true AND DATE(created_at) = CURRENT_DATE) as today_deals,
          (SELECT COUNT(DISTINCT company_name) FROM (
            SELECT jsonb_array_elements_text(buyer_name) as company_name FROM news_enrichment WHERE buyer_name IS NOT NULL AND buyer_name != '[]' AND DATE(created_at) = CURRENT_DATE
            UNION
            SELECT jsonb_array_elements_text(seller_name) FROM news_enrichment WHERE seller_name IS NOT NULL AND seller_name != '[]' AND DATE(created_at) = CURRENT_DATE
            UNION  
            SELECT jsonb_array_elements_text(company_name) FROM news_enrichment WHERE company_name IS NOT NULL AND company_name != '[]' AND DATE(created_at) = CURRENT_DATE
            UNION
            SELECT jsonb_array_elements_text(lender_name) FROM news_enrichment WHERE lender_name IS NOT NULL AND lender_name != '[]' AND DATE(created_at) = CURRENT_DATE
            UNION
            SELECT jsonb_array_elements_text(broker_name) FROM news_enrichment WHERE broker_name IS NOT NULL AND broker_name != '[]' AND DATE(created_at) = CURRENT_DATE
            UNION
            SELECT jsonb_array_elements_text(equity_partner) FROM news_enrichment WHERE equity_partner IS NOT NULL AND equity_partner != '[]' AND DATE(created_at) = CURRENT_DATE
            UNION
            SELECT jsonb_array_elements_text(developer_name) FROM news_enrichment WHERE developer_name IS NOT NULL AND developer_name != '[]' AND DATE(created_at) = CURRENT_DATE
            UNION
            SELECT jsonb_array_elements_text(tenant_name) FROM news_enrichment WHERE tenant_name IS NOT NULL AND tenant_name != '[]' AND DATE(created_at) = CURRENT_DATE
          ) companies_today) as today_companies,
          (SELECT COUNT(DISTINCT person_name) FROM (
            SELECT (entity->>'name')::text as person_name
            FROM news_enrichment, jsonb_array_elements(COALESCE(linked_entities, '[]'::jsonb)) as entity
            WHERE linked_entities IS NOT NULL AND linked_entities != '[]' AND DATE(created_at) = CURRENT_DATE
            UNION
            SELECT jsonb_array_elements_text(report_author)::text 
            FROM news_enrichment 
            WHERE report_author IS NOT NULL AND report_author != '[]' AND DATE(created_at) = CURRENT_DATE
          ) people_today) as today_persons
        FROM source_updates su
      `);

      return NextResponse.json({
        totalUrls: parseInt(result.rows[0].total_urls),
        processedItems: parseInt(result.rows[0].processed_items),
        activeSources: parseInt(result.rows[0].active_sources),
        dealsCaptured: parseInt(result.rows[0].deals_captured),
        todayCompanies: parseInt(result.rows[0].today_companies),
        todayPersons: parseInt(result.rows[0].today_persons),
        todayDeals: parseInt(result.rows[0].today_deals),
        todayTotal: parseInt(result.rows[0].today_total),
        lastUpdated: result.rows[0].last_updated,
        sourcesLastUpdated: result.rows[0].sources_last_updated
      });
    }

    // Build the WHERE clause with all filters
    let whereClause = `AND LOWER(news_source) = LOWER($1)`;
    const countParams = [source];
    let paramIndex = 2;
    
    if (onlyFetched) {
      whereClause += ` AND fetched = true AND news_text IS NOT NULL`;
    }
    
    if (onlyExtracted) {
      whereClause += ` AND enriched = true`;
    }
    
    if (titleSearch.trim()) {
      whereClause += ` AND news_title ILIKE $${paramIndex}`;
      countParams.push(`%${titleSearch.trim()}%`);
      paramIndex++;
    }
    
    // Get total count for the source with all filters
    const countQuery = `SELECT COUNT(*) FROM news WHERE (bad_url IS NULL OR bad_url = false) ${whereClause}`;
    const countResult = await client.query(countQuery, countParams);
    const totalCount = parseInt(countResult.rows[0].count);

    // Build pagination query with parameterized queries and proper ordering
    let query = '';
    let queryParams = [];

    // Build base SELECT clause
    const selectClause = `
      SELECT 
        id,
        news_title,
        news_date,
        news_text,
        raw_html,
        url,
        created_at,
        updated_at,
        enriched,
        fetched,
        fetch_error,
        enrichment_status,
        enrichment_error
      FROM news
      WHERE (bad_url IS NULL OR bad_url = false)
      `;

    // Build WHERE clause with same conditions as count query
    let mainWhereClause = `AND LOWER(news_source) = LOWER($1)`;
    queryParams = [source];
    let paramCount = 1;
    
    if (onlyFetched) {
      mainWhereClause += ` AND fetched = true AND news_text IS NOT NULL`;
    }
    
    if (onlyExtracted) {
      mainWhereClause += ` AND enriched = true`;
    }
    
    if (titleSearch.trim()) {
      paramCount++;
      mainWhereClause += ` AND news_title ILIKE $${paramCount}`;
      queryParams.push(`%${titleSearch.trim()}%`);
    }

    // Build ORDER BY clause
    const orderClause = orderBy === 'updated_at' 
      ? `ORDER BY updated_at DESC, id DESC` 
      : `ORDER BY id DESC`;

    if (lastId) {
      // Cursor-based pagination - get items older than lastId
      if (orderBy === 'updated_at') {
        // For updated_at ordering, we need to get the lastId's updated_at value
        const lastItemResult = await client.query(`SELECT updated_at FROM news WHERE id = $1`, [parseInt(lastId)]);
        
        if (lastItemResult.rows.length > 0) {
          const lastUpdatedAt = lastItemResult.rows[0].updated_at;
          paramCount++;
          mainWhereClause += ` AND (updated_at < $${paramCount} OR (updated_at = $${paramCount} AND id < $${paramCount + 1}))`;
          queryParams.push(lastUpdatedAt);
          paramCount++;
          queryParams.push(lastId);
        } else {
          // Fallback to ID-based pagination
          paramCount++;
          mainWhereClause += ` AND id < $${paramCount}`;
          queryParams.push(lastId);
        }
      } else {
        // ID-based pagination
        paramCount++;
        mainWhereClause += ` AND id < $${paramCount}`;
        queryParams.push(lastId);
      }
    }

    // Add LIMIT
    paramCount++;
    queryParams.push(ITEMS_PER_PAGE.toString());
    query = `${selectClause} ${mainWhereClause} ${orderClause} LIMIT $${paramCount}`;

    const result = await client.query(query, queryParams);

    const lastItemId = result.rows.length > 0 
      ? result.rows[result.rows.length - 1].id 
      : null;

    // Calculate if there are more items
    const hasMore = result.rows.length === ITEMS_PER_PAGE;

    return NextResponse.json({
      news: result.rows,
      totalCount,
      hasMore,
      currentPage: page,
      lastId: lastItemId,
      itemsPerPage: ITEMS_PER_PAGE,
      filters: {
        source,
        onlyFetched,
        onlyExtracted,
        titleSearch,
        orderBy
      }
    });

  } catch (error: unknown) {
    console.error('Database Error:', error);
    const errorMessage = isError(error) ? error.message : 'Unknown database error';
    return NextResponse.json(
      { error: 'Database error', details: errorMessage },
      { status: 500 }
    );
  } finally {
    if (client) {
      client.release();
    }
  }
}

export async function PATCH(request: Request) {
  const { id, is_relevant } = await request.json();
  let client;

  try {
    client = await pool.connect();
    
    await client.query(`
      UPDATE news
      SET is_relevant = $1
      WHERE id = $2
    `, [is_relevant, id]);
    
    return NextResponse.json({ success: true });
    
  } catch (error: unknown) {
    console.error('Database Error:', error);
    const errorMessage = isError(error) ? error.message : 'Unknown database error';
    return NextResponse.json(
      { error: 'Database error', details: errorMessage },
      { status: 500 }
    );
  } finally {
    if (client) {
      client.release();
    }
  }
} 