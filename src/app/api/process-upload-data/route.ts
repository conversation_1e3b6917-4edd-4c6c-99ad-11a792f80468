import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';
import { UploadProcessor } from '@/lib/workers/UploadProcessor';

export async function POST(request: NextRequest) {
  let uploadId: string | number | undefined;
  let rowNumber: string | number | undefined;
  
  try {
    const body = await request.json();
    ({ rowNumber, uploadId } = body);

    if (!rowNumber || !uploadId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Row number and upload ID are required' 
        },
        { status: 400 }
      );
    }

    console.log(`🧪 [MINI TEST] Processing upload ${uploadId}, row ${rowNumber} using UploadProcessor`);

    const client = await pool.connect();
    
    try {
      // Get the row data from upload_data_log
      const dataQuery = `
        SELECT * FROM upload_data_log 
        WHERE upload_log_id = $1 AND row_number = $2
        LIMIT 1
      `;
      
      const dataResult = await client.query(dataQuery, [uploadId, rowNumber]);
      
      if (dataResult.rows.length === 0) {
        return NextResponse.json(
          { 
            success: false, 
            error: `No data found for upload ${uploadId}, row ${rowNumber}` 
          },
          { status: 404 }
        );
      }

      const dataRow = dataResult.rows[0];
      console.log(`📊 Found data row with ${Object.keys(dataRow).length} fields`);
      
      // Get header mappings from upload_logs
      const headerQuery = `
        SELECT header_mappings FROM upload_logs 
        WHERE upload_id = $1
      `;
      
      const headerResult = await client.query(headerQuery, [uploadId]);
      
      if (headerResult.rows.length === 0) {
        return NextResponse.json(
          { 
            success: false, 
            error: `No header mappings found for upload ${uploadId}` 
          },
          { status: 404 }
        );
      }

      const rawHeaderMappings = headerResult.rows[0].header_mappings;
      console.log(`🗺️ Found header mappings with ${Object.keys(rawHeaderMappings).length} table sections`);

      // Create flat header mappings for UploadProcessor (csvHeader -> dbField)
      const structuredHeaderMappings: Record<string, string> = {};
      const tables = ['companies', 'contacts', 'investment_criteria'];
      
      tables.forEach(tableKey => {
        const tableMappings = rawHeaderMappings[tableKey] || {};
        for (const [dbField, csvHeaders] of Object.entries(tableMappings)) {
          if (Array.isArray(csvHeaders) && csvHeaders.length > 0) {
            csvHeaders.forEach((csvHeader: string) => {
              if (csvHeader && csvHeader.trim()) {
                structuredHeaderMappings[csvHeader.trim()] = dbField;
              }
            });
          } else if (typeof csvHeaders === 'string' && csvHeaders.trim()) {
            structuredHeaderMappings[csvHeaders.trim()] = dbField;
          }
        }
      });

      console.log(`📋 Created ${Object.keys(structuredHeaderMappings).length} header mappings for processing`);

      // Reconstruct CSV row data from key1-key50 columns
      const csvRowData: Record<string, any> = {};
      const headersMap = dataRow.headers_map || {};
      
      for (let i = 1; i <= 50; i++) {
        const keyName = `key${i}`;
        const originalHeader = headersMap[keyName];
        const value = dataRow[keyName];

        if (originalHeader && value !== null && value !== undefined && value !== '') {
          csvRowData[originalHeader] = value;
        }
      }

      console.log(`🔄 Reconstructed CSV row: ${JSON.stringify(csvRowData, null, 2)}`);

      // CREATE UPLOADPROCESSOR INSTANCE AND PROCESS THE ROW
      const processor = new UploadProcessor();
      
      console.log(`\n🚀 === STARTING MINI TEST PROCESSING ===`);
      console.log(`📋 Upload ID: ${uploadId}`);
      console.log(`📊 Row Number: ${rowNumber}`);
      console.log(`📦 Data Fields: ${Object.keys(csvRowData).length}`);
      console.log(`🗺️ Header Mappings: ${Object.keys(structuredHeaderMappings).length}`);
      
      // Add file logging for debugging
      const fs = require('fs');
      const debugLog = {
        timestamp: new Date().toISOString(),
        uploadId,
        rowNumber,
        csvRowDataKeys: Object.keys(csvRowData),
        csvRowDataSample: Object.fromEntries(Object.entries(csvRowData).slice(0, 5)),
        structuredHeaderMappingsKeys: Object.keys(structuredHeaderMappings),
        structuredHeaderMappingsSample: Object.fromEntries(Object.entries(structuredHeaderMappings).slice(0, 5))
      };
      fs.appendFileSync('debug-processing.log', JSON.stringify(debugLog, null, 2) + '\n---\n');

      // Use UploadProcessor's processRow method to actually process and save the data
      const startTime = Date.now();
      const processingResult = await (processor as any).processRow(
        csvRowData,
        structuredHeaderMappings,
        uploadId.toString(),
        rowNumber
      );
      const endTime = Date.now();
      const processingTimeMs = endTime - startTime;

      console.log(`\n✅ === MINI TEST COMPLETED ===`);
      console.log(`⏱️ Processing Time: ${processingTimeMs}ms`);
      console.log(`📊 Result:`, processingResult);

      // Return comprehensive test results
      return NextResponse.json({
        success: true,
        testInfo: {
          uploadId,
          rowNumber,
          processingTimeMs,
          timestamp: new Date().toISOString()
        },
        processingResult: {
          success: processingResult.success,
          error: processingResult.error,
          companiesCreated: processingResult.companiesCreated || 0,
          contactsCreated: processingResult.contactsCreated || 0,
          investmentCriteriaCreated: processingResult.investmentCriteriaCreated || 0,
          hasConflicts: processingResult.hasConflicts || false
        },
        inputData: {
          originalData: csvRowData,
          headerMappingsCount: Object.keys(structuredHeaderMappings).length,
          dataFieldsCount: Object.keys(csvRowData).length
        },
        debug: {
          sampleHeaderMappings: Object.fromEntries(
            Object.entries(structuredHeaderMappings).slice(0, 10)
          ),
          sampleInputData: Object.fromEntries(
            Object.entries(csvRowData).slice(0, 10)
          ),
          rawHeaderStructure: {
            companies: Object.keys(rawHeaderMappings.companies || {}).length,
            contacts: Object.keys(rawHeaderMappings.contacts || {}).length,
            investment_criteria: Object.keys(rawHeaderMappings.investment_criteria || {}).length
          }
        }
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('❌ [MINI TEST ERROR] Processing failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        details: {
          uploadId,
          rowNumber: rowNumber || 'unknown',
          timestamp: new Date().toISOString()
        }
      },
      { status: 500 }
    );
  }
} 