import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const level = searchParams.get('level');

    if (type) {
      // Get hierarchical data for a specific type
      const hierarchyData = await getHierarchicalDataForType(type, level);
      return NextResponse.json({
        success: true,
        data: hierarchyData
      });
    } else {
      // Get all types with counts
      const typesQuery = `
        SELECT type, COUNT(*) as count
        FROM central_mapping 
        WHERE is_active = true
        GROUP BY type
        ORDER BY type
      `;
      
      const result = await pool.query(typesQuery);
      
      return NextResponse.json({
        success: true,
        data: result.rows
      });
    }
  } catch (error) {
    console.error('Error fetching mapping types:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch mapping types' },
      { status: 500 }
    );
  }
}

async function getHierarchicalDataForType(type: string, level?: string | null) {
  // Build query based on level parameter
  let query = `
    SELECT * FROM central_mapping 
    WHERE type = $1 AND is_active = true
  `;
  const params = [type];
  
  // Add level filtering if specified
  if (level) {
    if (level === '1') {
      query += ` AND level_1 IS NOT NULL AND value_1 IS NOT NULL AND value_1 != ''`;
    } else if (level === '2') {
      query += ` AND level_2 IS NOT NULL AND value_2 IS NOT NULL AND value_2 != ''`;
    } else if (level === '3') {
      query += ` AND level_3 IS NOT NULL AND value_3 IS NOT NULL AND value_3 != ''`;
    }
  }
  
  query += ` ORDER BY value_1, value_2, value_3`;
  
  const result = await pool.query(query, params);
  const mappings = result.rows;

  if (mappings.length === 0) {
    return {
      type,
      levels: [],
      hierarchyRows: []
    };
  }

  // Determine the actual levels used in this type by examining the data
  const levelSet = new Set();
  mappings.forEach(mapping => {
    if (mapping.level_1 && mapping.level_1.trim()) levelSet.add(mapping.level_1.trim());
    if (mapping.level_2 && mapping.level_2.trim()) levelSet.add(mapping.level_2.trim());
    if (mapping.level_3 && mapping.level_3.trim()) levelSet.add(mapping.level_3.trim());
  });

  // Get unique levels from first mapping to maintain order
  const firstMapping: any = mappings[0];
  const levels: string[] = [];
  if (firstMapping.level_1 && firstMapping.level_1.trim()) levels.push(firstMapping.level_1.trim());
  if (firstMapping.level_2 && firstMapping.level_2.trim()) levels.push(firstMapping.level_2.trim());
  if (firstMapping.level_3 && firstMapping.level_3.trim()) levels.push(firstMapping.level_3.trim());

  // If level is specified, return only that level's unique values
  if (level) {
    const valueMap = new Map();
    mappings.forEach(mapping => {
      let value = '';
      if (level === '1' && mapping.value_1) {
        value = mapping.value_1.trim();
      } else if (level === '2' && mapping.value_2) {
        value = mapping.value_2.trim();
      } else if (level === '3' && mapping.value_3) {
        value = mapping.value_3.trim();
      }
      
      if (value && !valueMap.has(value)) {
        valueMap.set(value, {
          id: mapping.id,
          values: [value],
          fullMapping: mapping
        });
      }
    });
    
    return {
      type,
      levels: [level],
      hierarchyRows: Array.from(valueMap.values())
    };
  }

  // If we only have one level, create a simple single-column structure
  if (levels.length === 1) {
    // Group by value_1 to get unique entries
    const valueMap = new Map();
    mappings.forEach(mapping => {
      if (mapping.value_1 && mapping.value_1.trim()) {
        const key = mapping.value_1.trim();
        if (!valueMap.has(key)) {
          valueMap.set(key, {
            id: mapping.id,
            values: [mapping.value_1.trim()],
            fullMapping: mapping
          });
        }
      }
    });
    
    return {
      type,
      levels,
      hierarchyRows: Array.from(valueMap.values())
    };
  }

  // For multiple levels, create hierarchical structure
  const hierarchyMap = new Map();
  
  mappings.forEach(mapping => {
    const values: string[] = [];
    if (mapping.value_1 && mapping.value_1.trim()) values.push(mapping.value_1.trim());
    if (mapping.value_2 && mapping.value_2.trim()) values.push(mapping.value_2.trim());
    if (mapping.value_3 && mapping.value_3.trim()) values.push(mapping.value_3.trim());
    
    // Create a key based on the combination of values
    const key = values.join('|');
    
    if (!hierarchyMap.has(key) && values.length > 0) {
      hierarchyMap.set(key, {
        id: mapping.id,
        values: values,
        fullMapping: mapping
      });
    }
  });

  const hierarchyRows = Array.from(hierarchyMap.values());

  return {
    type,
    levels,
    hierarchyRows
  };
} 