import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { operation, data } = body;

    if (!operation || !data) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: operation, data' },
        { status: 400 }
      );
    }

    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');

      let result;
      switch (operation) {
        case 'create':
          result = await bulkCreate(client, data);
          break;
        case 'update':
          result = await bulkUpdate(client, data);
          break;
        case 'delete':
          result = await bulkDelete(client, data);
          break;
        default:
          throw new Error('Invalid operation. Supported: create, update, delete');
      }

      await client.query('COMMIT');

      return NextResponse.json({
        success: true,
        message: `Bulk ${operation} completed successfully`,
        result
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error in bulk operation:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to execute bulk operation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function bulkCreate(client: any, mappings: any[]) {
  let insertedCount = 0;
  let skippedCount = 0;
  const errors: string[] = [];

  for (const mapping of mappings) {
    try {
      const { type, level_1, value_1, level_2, value_2, level_3, value_3, is_active = true } = mapping;

      if (!type || !level_1 || !value_1) {
        skippedCount++;
        errors.push(`Skipped mapping: missing required fields (type, level_1, value_1)`);
        continue;
      }

      const insertQuery = `
        INSERT INTO central_mapping (type, level_1, value_1, level_2, value_2, level_3, value_3, is_active)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT ON CONSTRAINT unique_central_mapping_idx DO NOTHING
        RETURNING id
      `;

      const result = await client.query(insertQuery, [
        type, level_1, value_1, level_2 || null, value_2 || null, level_3 || null, value_3 || null, is_active
      ]);

      if (result.rowCount && result.rowCount > 0) {
        insertedCount++;
      } else {
        skippedCount++;
        errors.push(`Skipped duplicate mapping: ${type} - ${value_1}`);
      }

    } catch (error) {
      skippedCount++;
      errors.push(`Error inserting mapping: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return { insertedCount, skippedCount, errors };
}

async function bulkUpdate(client: any, mappings: any[]) {
  let updatedCount = 0;
  let skippedCount = 0;
  const errors: string[] = [];

  for (const mapping of mappings) {
    try {
      const { id, type, level_1, value_1, level_2, value_2, level_3, value_3, is_active = true } = mapping;

      if (!id) {
        skippedCount++;
        errors.push(`Skipped mapping: missing id`);
        continue;
      }

      const updateQuery = `
        UPDATE central_mapping 
        SET type = $2, level_1 = $3, value_1 = $4, level_2 = $5, value_2 = $6, 
            level_3 = $7, value_3 = $8, is_active = $9, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
        RETURNING id
      `;

      const result = await client.query(updateQuery, [
        id, type, level_1, value_1, level_2 || null, value_2 || null, level_3 || null, value_3 || null, is_active
      ]);

      if (result.rowCount && result.rowCount > 0) {
        updatedCount++;
      } else {
        skippedCount++;
        errors.push(`Mapping not found for id: ${id}`);
      }

    } catch (error) {
      skippedCount++;
      errors.push(`Error updating mapping: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return { updatedCount, skippedCount, errors };
}

async function bulkDelete(client: any, ids: number[]) {
  let deletedCount = 0;
  let skippedCount = 0;
  const errors: string[] = [];

  for (const id of ids) {
    try {
      if (!id) {
        skippedCount++;
        errors.push(`Skipped: invalid id`);
        continue;
      }

      const deleteQuery = 'DELETE FROM central_mapping WHERE id = $1 RETURNING id';
      const result = await client.query(deleteQuery, [id]);

      if (result.rowCount && result.rowCount > 0) {
        deletedCount++;
      } else {
        skippedCount++;
        errors.push(`Mapping not found for id: ${id}`);
      }

    } catch (error) {
      skippedCount++;
      errors.push(`Error deleting mapping: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return { deletedCount, skippedCount, errors };
} 