import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    let query = `
      SELECT DISTINCT type, level_1, level_2, level_3,
             array_agg(DISTINCT value_1) FILTER (WHERE value_1 IS NOT NULL) as level_1_values,
             array_agg(DISTINCT value_2) FILTER (WHERE value_2 IS NOT NULL) as level_2_values,
             array_agg(DISTINCT value_3) FILTER (WHERE value_3 IS NOT NULL) as level_3_values
      FROM central_mapping 
      WHERE is_active = true
    `;
    
    const params: any[] = [];

    if (type) {
      query += ` AND type = $1`;
      params.push(type);
    }

    query += ` GROUP BY type, level_1, level_2, level_3 ORDER BY type, level_1, level_2, level_3`;

    const result = await pool.query(query, params);

    // Transform data into hierarchical structure
    const hierarchicalData = transformToHierarchical(result.rows);

    return NextResponse.json({
      success: true,
      data: hierarchicalData
    });
  } catch (error) {
    console.error('Error fetching hierarchical mapping data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch hierarchical mapping data' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, hierarchyData } = body;

    if (!type || !hierarchyData) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: type, hierarchyData' },
        { status: 400 }
      );
    }

    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');

      let insertedCount = 0;

      // Process hierarchical data
      for (const item of hierarchyData) {
        const { level_1, level_2, level_3, values } = item;
        
        for (const valueSet of values) {
          const query = `
            INSERT INTO central_mapping (type, level_1, value_1, level_2, value_2, level_3, value_3, is_active)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            ON CONFLICT ON CONSTRAINT unique_central_mapping_idx DO NOTHING
            RETURNING id
          `;

          const result = await client.query(query, [
            type,
            level_1,
            valueSet.value_1 || '',
            level_2 || null,
            valueSet.value_2 || null,
            level_3 || null,
            valueSet.value_3 || null,
            true
          ]);

          if (result.rowCount && result.rowCount > 0) {
            insertedCount++;
          }
        }
      }

      await client.query('COMMIT');

      return NextResponse.json({
        success: true,
        message: 'Hierarchical data created successfully',
        insertedCount
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error creating hierarchical mapping data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create hierarchical mapping data' },
      { status: 500 }
    );
  }
}

function transformToHierarchical(rows: any[]) {
  const typeGroups: { [key: string]: any } = {};

  rows.forEach(row => {
    if (!typeGroups[row.type]) {
      typeGroups[row.type] = {
        type: row.type,
        levels: [],
        hierarchies: []
      };
    }

    // Build unique levels structure
    const levels = [];
    if (row.level_1) levels.push(row.level_1);
    if (row.level_2) levels.push(row.level_2);
    if (row.level_3) levels.push(row.level_3);
    
    typeGroups[row.type].levels = levels;

    // Build hierarchical values
    const hierarchy = {
      level_1: row.level_1,
      level_2: row.level_2,
      level_3: row.level_3,
      values: {
        level_1_values: row.level_1_values || [],
        level_2_values: row.level_2_values || [],
        level_3_values: row.level_3_values || []
      }
    };

    typeGroups[row.type].hierarchies.push(hierarchy);
  });

  return Object.values(typeGroups);
} 