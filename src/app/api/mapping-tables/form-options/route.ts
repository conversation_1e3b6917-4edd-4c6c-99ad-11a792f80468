import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const client = await pool.connect();
    
    try {
      // Get all distinct values for different mapping types
      const queries = await Promise.all([
        // Company Types
        client.query(`
          SELECT DISTINCT value_1 as value FROM central_mapping 
          WHERE type = 'Company Type' AND is_active = true 
          ORDER BY value_1
        `),
        
        // Property Types  
        client.query(`
          SELECT DISTINCT value_1 as value FROM central_mapping 
          WHERE type = 'Property Type' AND is_active = true 
          ORDER BY value_1
        `),
        
        // Investment Strategies
        client.query(`
          SELECT DISTINCT value_1 as value FROM central_mapping 
          WHERE type = 'Strategies' AND is_active = true 
          ORDER BY value_1
        `),
        
        // Geographic Regions (U.S Regions)
        client.query(`
          SELECT DISTINCT value_1 as value FROM central_mapping 
          WHERE type = 'U.S Regions' AND is_active = true 
          ORDER BY value_1
        `),
        
        // Capital Positions (simple list)
        client.query(`
          SELECT DISTINCT value_1 as value FROM central_mapping 
          WHERE type = 'Capital Position' AND is_active = true 
          ORDER BY value_1
        `),
        
        // Capital Position to Loan Type mappings (hierarchical)
        client.query(`
          SELECT value_1 as capital_position, value_2 as loan_type 
          FROM central_mapping 
          WHERE type = 'Capital Position' 
          AND value_2 IS NOT NULL 
          AND is_active = true
          ORDER BY value_1, value_2
        `),
        
        // Deal Types
        client.query(`
          SELECT DISTINCT value_1 as value FROM central_mapping 
          WHERE type = 'Deal Type' AND is_active = true 
          ORDER BY value_1
        `),
        
        // Loan Programs
        client.query(`
          SELECT DISTINCT value_1 as value FROM central_mapping 
          WHERE type = 'Loan Program' AND is_active = true 
          ORDER BY value_1
        `),
        
        // Structured Loan Tranches
        client.query(`
          SELECT DISTINCT value_1 as value FROM central_mapping 
          WHERE type = 'Structured Loan Tranches' AND is_active = true 
          ORDER BY value_1
        `),
        
        // Recourse Loan options
        client.query(`
          SELECT DISTINCT value_1 as value FROM central_mapping 
          WHERE type = 'Recourse Loan' AND is_active = true 
          ORDER BY value_1
        `)
      ]);

      const [
        companyTypes,
        propertyTypes,
        investmentStrategies,
        geographicRegions,
        capitalPositions,
        capitalPositionMappingsData,
        dealTypes,
        loanPrograms,
        structuredLoanTranches,
        recourseLoans
      ] = queries;

      // Process capital position mappings into hierarchical structure
      const capitalPositionMappings: Record<string, string[]> = {};
      capitalPositionMappingsData.rows.forEach(row => {
        const capitalPos = row.capital_position;
        const loanType = row.loan_type;
        
        if (!capitalPositionMappings[capitalPos]) {
          capitalPositionMappings[capitalPos] = [];
        }
        capitalPositionMappings[capitalPos].push(loanType);
      });

      // Define additional form options that don't exist in central_mapping
      const investmentVehicleTypes = [
        'Private Equity Fund',
        'REIT',
        'Family Office',
        'Pension Fund',
        'Hedge Fund',
        'Investment Bank',
        'Insurance Company',
        'Sovereign Wealth Fund'
      ];

      const lenderTypes = [
        'Commercial Bank',
        'Debt Fund',
        'CMBS',
        'Agency',
        'Life Co',
        'Credit Union',
        'Private Lender',
        'Bridge Lender'
      ];

      const stockExchanges = [
        'NASDAQ',
        'NYSE',
        'LSE',
        'TSE',
        'HKEX',
        'Euronext',
        'ASX'
      ];

      const creditRatings = [
        'AAA',
        'AA+',
        'AA',
        'AA-',
        'A+',
        'A',
        'A-',
        'BBB+',
        'BBB',
        'BBB-',
        'BB+',
        'BB',
        'BB-',
        'B+',
        'B',
        'B-',
        'CCC+',
        'CCC',
        'CCC-',
        'CC',
        'C',
        'D'
      ];

      const corporateStructures = [
        'LLC',
        'C-Corp',
        'S-Corp',
        'Partnership',
        'Limited Partnership',
        'LLP',
        'Sole Proprietorship',
        'Public Company'
      ];

      const pipelineStatuses = [
        'Prospect',
        'Approached',
        'Warming',
        'Active Mandate',
        'Closed Deal',
        'Dormant'
      ];

      const roleInPreviousDeals = [
        'General Partner',
        'Limited Partner',
        'Co-GP',
        'Lead Investor',
        'Co-Investor',
        'Advisor',
        'Lender',
        'Broker'
      ];

      const recentNewsSentiments = [
        'Positive',
        'Negative',
        'Neutral',
        'Mixed'
      ];

      const fundingStatuses = [
        'In Market',
        'Closed',
        'Oversubscribed',
        'Final Close',
        'First Close',
        'Soft Circling'
      ];

      return NextResponse.json({
        success: true,
        data: {
          companyTypes: companyTypes.rows.map(row => row.value),
          industries: [], // Will be populated from existing data or form submissions
          investmentStrategies: investmentStrategies.rows.map(row => row.value),
          investmentVehicleTypes,
          lenderTypes,
          stockExchanges,
          creditRatings,
          corporateStructures,
          pipelineStatuses,
          roleInPreviousDeals,
          recentNewsSentiments,
          fundingStatuses,
          // Legacy fields for backward compatibility
          propertyTypes: propertyTypes.rows.map(row => row.value),
          geographicRegions: geographicRegions.rows.map(row => row.value),
          capitalPositions: capitalPositions.rows.map(row => row.value),
          capitalPositionMappings: capitalPositionMappings,
          dealTypes: dealTypes.rows.map(row => row.value),
          loanPrograms: loanPrograms.rows.map(row => row.value),
          structuredLoanTranches: structuredLoanTranches.rows.map(row => row.value),
          recourseLoans: recourseLoans.rows.map(row => row.value)
        }
      });
      
    } finally {
      client.release();
    }
    
  } catch (error) {
    console.error('Error fetching form options:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch form options' },
      { status: 500 }
    );
  }
}