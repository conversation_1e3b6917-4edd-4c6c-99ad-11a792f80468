import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const client = await pool.connect();
    
    try {
      const result = await client.query(`
        SELECT *
        FROM central_mapping 
        WHERE type = 'Capital Position' 
        AND is_active = true
        ORDER BY value_1, value_2
      `);

      // Process into Map format for easier use
      const capitalPositionMap: Record<string, string[]> = {};
      
      if (result.rows) {
        result.rows.forEach((mapping: any) => {
          if (mapping.value_1 && mapping.value_2) {
            const capitalPos = mapping.value_1;
            const loanType = mapping.value_2;
            
            if (!capitalPositionMap[capitalPos]) {
              capitalPositionMap[capitalPos] = [];
            }
            capitalPositionMap[capitalPos].push(loanType);
          }
        });
      }

      return NextResponse.json({
        success: true,
        data: result.rows,
        mappings: capitalPositionMap,
        count: result.rows.length
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error fetching capital position mappings:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch capital position mappings',
        data: [],
        mappings: {}
      },
      { status: 500 }
    );
  }
} 