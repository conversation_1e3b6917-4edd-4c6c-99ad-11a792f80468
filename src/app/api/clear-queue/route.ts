import { NextRequest, NextResponse } from "next/server";
import { Queue } from "bullmq";

export async function POST(request: NextRequest) {
  try {
    const queue = new Queue('deal-processing', {
      connection: {
        host: 'localhost',
        port: 6379,
      }
    });

    // Get all jobs
    const jobs = await queue.getJobs(['waiting', 'active', 'delayed', 'failed']);
    console.log(`Found ${jobs.length} total jobs`);

    // Find requirement extraction jobs
    const requirementJobs = jobs.filter(job => job.name === 'requirement-extraction');
    console.log(`Found ${requirementJobs.length} requirement extraction jobs`);

    // Remove all requirement extraction jobs
    for (const job of requirementJobs) {
      console.log(`Removing job ${job.id} (${job.name})`);
      await job.remove();
    }

    await queue.close();

    return NextResponse.json({
      success: true,
      message: `Cleared ${requirementJobs.length} requirement extraction jobs`,
      totalJobs: jobs.length,
      clearedJobs: requirementJobs.length
    });

  } catch (error) {
    console.error("Error clearing queue:", error);
    return NextResponse.json(
      { 
        error: "Failed to clear queue",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
} 