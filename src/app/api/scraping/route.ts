import { NextRequest, NextResponse } from 'next/server';
import { ScrapingProcessor, NewsFetchingProcessor } from '@/lib/scrapers/ScrapingProcessor';
import { ScraperManager } from '@/lib/scrapers/ScraperManager';

// Store active processors (in production, you'd use a proper queue/job system)
const activeProcessors = new Map<string, {
  processor: ScrapingProcessor | NewsFetchingProcessor | ScraperManager;
  startTime: Date;
  type: 'scraping' | 'fetching' | 'manager';
}>();

// GET /api/scraping - Get status of scraping operations
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'status') {
      const status = Array.from(activeProcessors.entries()).map(([id, info]) => ({
        id,
        type: info.type,
        startTime: info.startTime,
        duration: Date.now() - info.startTime.getTime()
      }));

      return NextResponse.json({
        success: true,
        activeProcessors: status,
        totalActive: status.length
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Scraping API is running',
      endpoints: {
        'GET /api/scraping?action=status': 'Get status of active scraping operations',
        'POST /api/scraping': 'Start scraping operation',
        'DELETE /api/scraping?id=<id>': 'Stop scraping operation'
      },
      supportedTypes: ['scraping', 'fetching', 'manager'],
      supportedSites: ['bisnow', 'therealdeal', 'globest', 'pincus']
    });
  } catch (error) {
    console.error('Error in scraping API GET:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// POST /api/scraping - Start scraping operation
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      type = 'scraping',
      siteName,
      maxPages = 30,
      tryLogin = true 
    } = body;

    // Generate unique ID for this operation
    const operationId = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    if (type === 'scraping') {
      const processor = new ScrapingProcessor(`ScrapingProcessor_${operationId}`);
      
      // Store the processor
      activeProcessors.set(operationId, {
        processor,
        startTime: new Date(),
        type: 'scraping'
      });

      // Start processing (don't await - let it run in background)
      processor.process().then((result) => {
        console.log(`Scraping operation ${operationId} completed:`, result);
        activeProcessors.delete(operationId);
      }).catch((error) => {
        console.error(`Scraping operation ${operationId} failed:`, error);
        activeProcessors.delete(operationId);
      });

      return NextResponse.json({
        success: true,
        operationId,
        message: 'Scraping operation started',
        type: 'scraping'
      });

    } else if (type === 'fetching') {
      const processor = new NewsFetchingProcessor(`NewsFetchingProcessor_${operationId}`);
      
      // Store the processor
      activeProcessors.set(operationId, {
        processor,
        startTime: new Date(),
        type: 'fetching'
      });

      // Start processing (don't await - let it run in background)
      processor.process().then((result) => {
        console.log(`News fetching operation ${operationId} completed:`, result);
        activeProcessors.delete(operationId);
      }).catch((error) => {
        console.error(`News fetching operation ${operationId} failed:`, error);
        activeProcessors.delete(operationId);
      });

      return NextResponse.json({
        success: true,
        operationId,
        message: 'News fetching operation started',
        type: 'fetching'
      });

    } else if (type === 'manager') {
      const manager = new ScraperManager({
        headless: true,
        maxConcurrentScrapers: 1,
        scraperTimeout: 30 * 60 * 1000, // 30 minutes
        waitTimeBetweenSites: 5000
      });

      // Store the manager
      activeProcessors.set(operationId, {
        processor: manager,
        startTime: new Date(),
        type: 'manager'
      });

      // Initialize and start scraping
      (async () => {
        try {
          await manager.initialize();
          
          if (siteName) {
            // Run single scraper
            const result = await manager.runSingleScraper(siteName, tryLogin, maxPages);
            console.log(`Single scraper operation ${operationId} completed for ${siteName}:`, result);
          } else {
            // Run all scrapers
            const result = await manager.runAllScrapers(tryLogin, maxPages);
            console.log(`All scrapers operation ${operationId} completed:`, result);
          }
        } catch (error) {
          console.error(`Scraper manager operation ${operationId} failed:`, error);
        } finally {
          await manager.cleanup();
          activeProcessors.delete(operationId);
        }
      })();

      return NextResponse.json({
        success: true,
        operationId,
        message: siteName 
          ? `Scraper manager started for site: ${siteName}` 
          : 'Scraper manager started for all sites',
        type: 'manager',
        siteName: siteName || 'all'
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid operation type. Use "scraping", "fetching", or "manager"'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in scraping API POST:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// DELETE /api/scraping - Stop scraping operation
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const operationId = searchParams.get('id');

    if (!operationId) {
      return NextResponse.json({
        success: false,
        error: 'Operation ID is required'
      }, { status: 400 });
    }

    const operation = activeProcessors.get(operationId);
    if (!operation) {
      return NextResponse.json({
        success: false,
        error: 'Operation not found or already completed'
      }, { status: 404 });
    }

    // Remove from active processors
    activeProcessors.delete(operationId);

    return NextResponse.json({
      success: true,
      message: `Operation ${operationId} has been stopped`,
      operationId
    });

  } catch (error) {
    console.error('Error in scraping API DELETE:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
} 