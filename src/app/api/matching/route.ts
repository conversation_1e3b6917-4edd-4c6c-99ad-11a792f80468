import { pool } from '@/lib/db'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const query = `
      SELECT m.*, 
             scv.value as suggested_clean_value,
             ccv.value as current_clean_value
      FROM criteria_value_matches m
      LEFT JOIN clean_criteria_values scv ON m.suggested_clean_value_id = scv.id
      LEFT JOIN clean_criteria_values ccv ON m.current_clean_value_id = ccv.id
      ORDER BY m.field, m.raw_value
    `
    const result = await pool.query(query)
    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: 'Database error' }, { status: 500 })
  }
} 