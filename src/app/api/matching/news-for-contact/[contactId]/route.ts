import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

interface MatchingCriteria {
  property_types: string[];
  property_sub_categories: string[];
  strategies: string[];
  countries: string[];
  states: string[];
  cities: string[];
  loan_types: string[];
  capital_positions: string[];
  min_deal_size: number | null;
  max_deal_size: number | null;
}

interface NewsMatch {
  news_id: string;
  headline: string;
  summary: string;
  publication_date: string;
  source_name: string;
  source_url: string;
  property_type: any;
  sub_property_type: any;
  location_city: any;
  location_state: any;
  address: string;
  deal_type: any;
  deal_size: number;
  deal_status: string;
  buyer_name: string;
  seller_name: string;
  lender_name: string;
  broker_name: string;
  company_name: string;
  project_name: string;
  strategies: any;
  loan_type: any;
  equity_type: any;
  financing_type: any;
  square_footage: number;
  unit_count: number;
  cap_rate: number;
  price_per_sf: number;
  created_at: string;
  updated_at: string;
  enrichment_id: number;
  score: number;
  reasons: string[];
  breakdown: any[];
  scoring_method: string;
  max_possible_score: number;
  actual_score: number;
  deal_size_rank: boolean;
}

interface CriteriaSource {
  source: 'contact' | 'company' | 'location';
  description: string;
  criteria: MatchingCriteria;
  hasInvestmentCriteria: boolean;
}

/**
 * Reusable function to find news matches based on criteria
 */
async function findNewsMatches(
  criteria: MatchingCriteria, 
  hasInvestmentCriteria: boolean,
  isLocationFallback: boolean = false
): Promise<NewsMatch[]> {
  const whereConditions: string[] = [];
  const queryParams: any[] = [];
  let paramCount = 0;

  // Property Type Matching (only if we have investment criteria)
  if (hasInvestmentCriteria && criteria.property_types.length > 0) {
    paramCount++;
    whereConditions.push(`
      (ne.property_type::jsonb ?| $${paramCount}::text[] OR
       EXISTS (
         SELECT 1 FROM jsonb_array_elements_text(ne.property_type) AS pt
         WHERE pt = ANY($${paramCount}::text[])
       ))
    `);
    queryParams.push(criteria.property_types);
  }

  // Sub Property Type Matching (only if we have investment criteria)
  if (hasInvestmentCriteria && criteria.property_sub_categories.length > 0) {
    paramCount++;
    whereConditions.push(`
      (ne.sub_property_type::jsonb ?| $${paramCount}::text[] OR
       EXISTS (
         SELECT 1 FROM jsonb_array_elements_text(ne.sub_property_type) AS pst
         WHERE pst = ANY($${paramCount}::text[])
       ))
    `);
    queryParams.push(criteria.property_sub_categories);
  }

  // Deal Type Matching (only if we have investment criteria)
  if (hasInvestmentCriteria && criteria.strategies.length > 0) {
    paramCount++;
    whereConditions.push(`
      (ne.deal_type::jsonb ?| $${paramCount}::text[] OR
       EXISTS (
         SELECT 1 FROM jsonb_array_elements_text(ne.deal_type) AS dt
         WHERE dt = ANY($${paramCount}::text[])
       ))
    `);
    queryParams.push(criteria.strategies);
  }

  // Deal Size Matching (only if we have investment criteria)
  if (hasInvestmentCriteria && (criteria.min_deal_size || criteria.max_deal_size)) {
    const sizeConditions: string[] = [];
    
    if (criteria.min_deal_size) {
      paramCount++;
      sizeConditions.push(`ne.deal_size >= $${paramCount}`);
      queryParams.push(criteria.min_deal_size);
    }
    
    if (criteria.max_deal_size) {
      paramCount++;
      sizeConditions.push(`ne.deal_size <= $${paramCount}`);
      queryParams.push(criteria.max_deal_size);
    }
    
    if (sizeConditions.length > 0) {
      whereConditions.push(`(${sizeConditions.join(' AND ')})`);
    }
  }

  // Location matching (always applied if location criteria exist)
  if (criteria.states.length > 0) {
    paramCount++;
    whereConditions.push(`
      ((jsonb_typeof(ne.location_state) = 'array' AND (
          ne.location_state ?| $${paramCount}::text[] OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements_text(ne.location_state) AS ls
            WHERE ls = ANY($${paramCount}::text[])
          )
      )) OR
      (jsonb_typeof(ne.location_state) = 'string' AND ne.location_state @> to_jsonb($${paramCount}::text)))
    `);
    queryParams.push(criteria.states);
  }

  if (criteria.cities.length > 0) {
    paramCount++;
    whereConditions.push(`
      ((jsonb_typeof(ne.location_city) = 'array' AND (
          ne.location_city ?| $${paramCount}::text[] OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements_text(ne.location_city) AS lc
            WHERE lc = ANY($${paramCount}::text[])
          )
      )) OR
      (jsonb_typeof(ne.location_city) = 'string' AND ne.location_city @> to_jsonb($${paramCount}::text)))
    `);
    queryParams.push(criteria.cities);
  }

  if (criteria.countries.length > 0) {
    paramCount++;
    whereConditions.push(`
      ((jsonb_typeof(ne.location_state) = 'array' AND (
          ne.location_state ?| $${paramCount}::text[] OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements_text(ne.location_state) AS country_match
            WHERE country_match = ANY($${paramCount}::text[])
          )
      )) OR
      (jsonb_typeof(ne.location_state) = 'string' AND ne.location_state @> to_jsonb($${paramCount}::text)) OR
      (jsonb_typeof(ne.location_city) = 'array' AND (
          ne.location_city ?| $${paramCount}::text[] OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements_text(ne.location_city) AS country_match
            WHERE country_match = ANY($${paramCount}::text[])
          )
      )) OR
      (jsonb_typeof(ne.location_city) = 'string' AND ne.location_city @> to_jsonb($${paramCount}::text)))
    `);
    queryParams.push(criteria.countries);
  }

  // If no conditions, return empty results
  if (whereConditions.length === 0) {
    return [];
  }

  // Query news enrichment data with DISTINCT ON to get unique news_id
  const newsQuery = `
    SELECT DISTINCT ON (ne.news_id)
      ne.news_id,
      ne.headline,
      ne.summary,
      ne.publication_date,
      ne.source_name,
      ne.source_url,
      ne.property_type,
      ne.sub_property_type,
      ne.location_city,
      ne.location_state,
      ne.address,
      ne.deal_type,
      ne.deal_size,
      ne.deal_status,
      ne.buyer_name,
      ne.seller_name,
      ne.lender_name,
      ne.broker_name,
      ne.company_name,
      ne.project_name,
      ne.strategies,
      ne.loan_type,
      ne.equity_type,
      ne.financing_type,
      ne.square_footage,
      ne.unit_count,
      ne.cap_rate,
      ne.price_per_sf,
      ne.created_at,
      ne.updated_at,
      ne.id as enrichment_id
    FROM news_enrichment ne
    WHERE (${whereConditions.join(' OR ')})
    AND ne.is_deal_specific = true
    AND ne.deal_size IS NOT NULL
    AND ne.deal_size > 0
    AND ne.publication_date >= CURRENT_DATE - INTERVAL '90 days'
    ORDER BY ne.news_id, ne.deal_size DESC, ne.publication_date DESC
    LIMIT 1000
  `;

  const newsResult = await pool.query(newsQuery, queryParams);
  
  // Score and filter matches
  const matches: NewsMatch[] = [];
  
  for (const newsItem of newsResult.rows) {
    let score = 0;
    let maxScore = 0;
    const reasons: string[] = [];
    const breakdown: any[] = [];

    if (hasInvestmentCriteria) {
      // Property Type Matching (weight: 30%)
      maxScore += 30;
      if (newsItem.property_type && criteria.property_types.length > 0) {
        const newsPropertyTypes = Array.isArray(newsItem.property_type) 
          ? newsItem.property_type 
          : [newsItem.property_type];
        
        const matchingTypes = newsPropertyTypes.filter((type: any) => 
          criteria.property_types.some((criteriaType: string) => 
            criteriaType.toLowerCase().includes(type.toLowerCase()) ||
            type.toLowerCase().includes(criteriaType.toLowerCase())
          )
        );
        
        if (matchingTypes.length > 0) {
          const typeScore = Math.min(30, (matchingTypes.length / newsPropertyTypes.length) * 30);
          score += typeScore;
          reasons.push(`Property type match: ${matchingTypes.join(', ')}`);
          breakdown.push({
            field: 'property_type',
            score: Math.round((typeScore / 30) * 100),
            weight: 0.30,
            reason: `Matched ${matchingTypes.length} property types`
          });
        }
      }

      // Sub Property Type Matching (weight: 20%)
      maxScore += 20;
      if (newsItem.sub_property_type && criteria.property_sub_categories.length > 0) {
        const newsSubTypes = Array.isArray(newsItem.sub_property_type) 
          ? newsItem.sub_property_type 
          : [newsItem.sub_property_type];
        
        const matchingSubTypes = newsSubTypes.filter((subType: any) => 
          criteria.property_sub_categories.some((criteriaType: string) => 
            criteriaType.toLowerCase().includes(subType.toLowerCase()) ||
            subType.toLowerCase().includes(criteriaType.toLowerCase())
          )
        );
        
        if (matchingSubTypes.length > 0) {
          const subTypeScore = Math.min(20, (matchingSubTypes.length / newsSubTypes.length) * 20);
          score += subTypeScore;
          reasons.push(`Sub-property type match: ${matchingSubTypes.join(', ')}`);
          breakdown.push({
            field: 'sub_property_type',
            score: Math.round((subTypeScore / 20) * 100),
            weight: 0.20,
            reason: `Matched ${matchingSubTypes.length} sub-property types`
          });
        }
      }

      // Deal Type Matching (weight: 10%)
      maxScore += 10;
      if (newsItem.deal_type && criteria.strategies.length > 0) {
        const newsDealTypes = Array.isArray(newsItem.deal_type) 
          ? newsItem.deal_type 
          : [newsItem.deal_type];
        
        const matchingDealTypes = newsDealTypes.filter((dealType: any) => 
          criteria.strategies.some((criteriaType: string) => 
            criteriaType.toLowerCase().includes(dealType.toLowerCase()) ||
            dealType.toLowerCase().includes(criteriaType.toLowerCase())
          )
        );
        
        if (matchingDealTypes.length > 0) {
          const dealTypeScore = Math.min(10, (matchingDealTypes.length / newsDealTypes.length) * 10);
          score += dealTypeScore;
          reasons.push(`Deal type match: ${matchingDealTypes.join(', ')}`);
          breakdown.push({
            field: 'deal_type',
            score: Math.round((dealTypeScore / 10) * 100),
            weight: 0.10,
            reason: `Matched ${matchingDealTypes.length} deal types`
          });
        }
      }

      // Deal Size Matching (weight: 10%)
      maxScore += 10;
      if (newsItem.deal_size && (criteria.min_deal_size || criteria.max_deal_size)) {
        let sizeMatch = false;
        let sizeReason = '';
        
        if (criteria.min_deal_size && criteria.max_deal_size) {
          if (newsItem.deal_size >= criteria.min_deal_size && newsItem.deal_size <= criteria.max_deal_size) {
            sizeMatch = true;
            sizeReason = `Within deal size range ($${criteria.min_deal_size}M - $${criteria.max_deal_size}M)`;
          } else {
            if (newsItem.deal_size < criteria.min_deal_size) {
              sizeReason = `Below minimum deal size ($${criteria.min_deal_size}M)`;
            } else {
              sizeReason = `Above maximum deal size ($${criteria.max_deal_size}M)`;
            }
          }
        } else if (criteria.min_deal_size) {
          if (newsItem.deal_size >= criteria.min_deal_size) {
            sizeMatch = true;
            sizeReason = `Above minimum deal size ($${criteria.min_deal_size}M)`;
          } else {
            sizeReason = `Below minimum deal size ($${criteria.min_deal_size}M)`;
          }
        } else if (criteria.max_deal_size) {
          if (newsItem.deal_size <= criteria.max_deal_size) {
            sizeMatch = true;
            sizeReason = `Below maximum deal size ($${criteria.max_deal_size}M)`;
          } else {
            sizeReason = `Above maximum deal size ($${criteria.max_deal_size}M)`;
          }
        }
        
        if (sizeMatch) {
          score += 10;
          reasons.push(`Deal size: $${newsItem.deal_size}M ${sizeReason}`);
          breakdown.push({
            field: 'deal_size',
            score: 100,
            weight: 0.10,
            reason: sizeReason
          });
        } else {
          breakdown.push({
            field: 'deal_size',
            score: 0,
            weight: 0.10,
            reason: `Deal size: $${newsItem.deal_size}M - ${sizeReason}`
          });
        }
      }
    }

    // Geographic matching (higher weight when using location fallback)
    const geoWeight = isLocationFallback ? 0.6 : 0.15;
    
    // Location State Matching
    maxScore += geoWeight * 100;
    if (newsItem.location_state && criteria.states.length > 0) {
      const newsStates = Array.isArray(newsItem.location_state) 
        ? newsItem.location_state 
        : [newsItem.location_state];
      
      const matchingStates = newsStates.filter((state: any) => 
        criteria.states.some((criteriaState: string) => 
          criteriaState.toLowerCase().includes(state.toLowerCase()) ||
          state.toLowerCase().includes(criteriaState.toLowerCase())
        )
      );

      if (matchingStates.length > 0) {
        const stateScore = Math.min(geoWeight * 100, (matchingStates.length / newsStates.length) * geoWeight * 100);
        score += stateScore;
        reasons.push(`State match: ${matchingStates.join(', ')}`);
        breakdown.push({
          field: 'location_state',
          score: Math.round((stateScore / (geoWeight * 100)) * 100),
          weight: geoWeight,
          reason: `Matched ${matchingStates.length} states`
        });
      }
    }

    // Location City Matching
    if (newsItem.location_city && criteria.cities.length > 0) {
      const newsCities = Array.isArray(newsItem.location_city) 
        ? newsItem.location_city 
        : [newsItem.location_city];
      
      const matchingCities = newsCities.filter((city: any) => 
        criteria.cities.some((criteriaCity: string) => 
          criteriaCity.toLowerCase().includes(city.toLowerCase()) ||
          city.toLowerCase().includes(criteriaCity.toLowerCase())
        )
      );

      if (matchingCities.length > 0) {
        const cityScore = Math.min(geoWeight * 100, (matchingCities.length / newsCities.length) * geoWeight * 100);
        score += cityScore;
        reasons.push(`City match: ${matchingCities.join(', ')}`);
        breakdown.push({
          field: 'location_city',
          score: Math.round((cityScore / (geoWeight * 100)) * 100),
          weight: geoWeight,
          reason: `Matched ${matchingCities.length} cities`
        });
      }
    }

    // Calculate final score as percentage
    let finalScore = 0;
    if (isLocationFallback) {
      // When using location fallback, any geographic match gets a reasonable score
      finalScore = maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
      // Ensure minimum score for location matches when using fallback
      if (finalScore === 0 && (reasons.some(r => r.includes('State match')) || reasons.some(r => r.includes('City match')))) {
        finalScore = 50; // Give at least 50% score for location matches when using fallback
      }
    } else {
      finalScore = maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
    }
    
    // Include matches with score > 0 or when using location fallback (to ensure we show results)
    if (finalScore > 0 || (isLocationFallback && reasons.length > 0)) {
      matches.push({
        ...newsItem,
        score: finalScore,
        reasons,
        breakdown,
        scoring_method: 'weighted',
        max_possible_score: maxScore,
        actual_score: score,
        deal_size_rank: true
      });
    }
  }

  // Sort by deal size first when using location fallback, otherwise by score then deal size
  matches.sort((a, b) => {
    if (isLocationFallback) {
      // When using location fallback, prioritize deal size
      return (b.deal_size || 0) - (a.deal_size || 0);
    } else {
      // Normal scoring priority
      if (b.score !== a.score) return b.score - a.score;
      return (b.deal_size || 0) - (a.deal_size || 0);
    }
  });

  return matches;
}

/**
 * Extract contact-level investment criteria
 */
function extractContactCriteria(contact: any): MatchingCriteria {
  const criteria: MatchingCriteria = {
    property_types: [],
    property_sub_categories: [],
    strategies: [],
    countries: [],
    states: [],
    cities: [],
    loan_types: [],
    capital_positions: [],
    min_deal_size: null,
    max_deal_size: null,
  };

  if (contact.investment_criteria_property_type) {
    criteria.property_types.push(contact.investment_criteria_property_type);
  }
  if (contact.investment_criteria_state) {
    criteria.states.push(contact.investment_criteria_state);
  }
  if (contact.investment_criteria_city) {
    criteria.cities.push(contact.investment_criteria_city);
  }
  if (contact.investment_criteria_country) {
    criteria.countries.push(contact.investment_criteria_country);
  }
  if (contact.investment_criteria_loan_type) {
    criteria.loan_types.push(contact.investment_criteria_loan_type);
  }

  return criteria;
}

/**
 * Extract company-level investment criteria from investment_criteria table and extracted data
 */
function extractCompanyCriteria(contact: any, criteriaRows: any[]): MatchingCriteria {
  const criteria: MatchingCriteria = {
    property_types: [],
    property_sub_categories: [],
    strategies: [],
    countries: [],
    states: [],
    cities: [],
    loan_types: [],
    capital_positions: [],
    min_deal_size: null,
    max_deal_size: null,
  };

  // Add criteria from investment_criteria table
  const companyCriteria = criteriaRows.filter((row: any) => 
    row.entity_type === 'Company' || 
    row.entity_type === 'Company Overview' ||
    row.entity_type === 'company' || 
    row.entity_type === 'company overview'
  );

  companyCriteria.forEach((row) => {
    // Handle array fields properly
    if (row.property_types && Array.isArray(row.property_types)) {
      criteria.property_types.push(...row.property_types);
    }
    if (row.property_sub_categories && Array.isArray(row.property_sub_categories)) {
      criteria.property_sub_categories.push(...row.property_sub_categories);
    }
    if (row.strategies && Array.isArray(row.strategies)) {
      criteria.strategies.push(...row.strategies);
    }
    if (row.country && Array.isArray(row.country)) {
      criteria.countries.push(...row.country);
    }
    if (row.state && Array.isArray(row.state)) {
      criteria.states.push(...row.state);
    }
    if (row.city && Array.isArray(row.city)) {
      criteria.cities.push(...row.city);
    }
    if (row.loan_type && Array.isArray(row.loan_type)) {
      criteria.loan_types.push(...row.loan_type);
    }
    if (row.capital_position && Array.isArray(row.capital_position)) {
      criteria.capital_positions.push(...row.capital_position);
    }
    
    if (row.minimum_deal_size && (!criteria.min_deal_size || row.minimum_deal_size < criteria.min_deal_size)) {
      criteria.min_deal_size = row.minimum_deal_size;
    }
    if (row.maximum_deal_size && (!criteria.max_deal_size || row.maximum_deal_size > criteria.max_deal_size)) {
      criteria.max_deal_size = row.maximum_deal_size;
    }
  });

  // Add criteria from company extracted data
  if (contact.investment_criteria_property_types && Array.isArray(contact.investment_criteria_property_types)) {
    criteria.property_types.push(...contact.investment_criteria_property_types);
  }
  if (contact.investment_criteria_property_subcategories && Array.isArray(contact.investment_criteria_property_subcategories)) {
    criteria.property_sub_categories.push(...contact.investment_criteria_property_subcategories);
  }
  if (contact.investment_criteria_asset_types && Array.isArray(contact.investment_criteria_asset_types)) {
    criteria.strategies.push(...contact.investment_criteria_asset_types);
  }
  if (contact.investment_criteria_loan_types && Array.isArray(contact.investment_criteria_loan_types)) {
    criteria.loan_types.push(...contact.investment_criteria_loan_types);
  }
  if (contact.strategies && Array.isArray(contact.strategies)) {
    criteria.strategies.push(...contact.strategies);
  }
  if (contact.minimumdealsize) {
    criteria.min_deal_size = contact.minimumdealsize;
  }
  if (contact.maximumdealsize) {
    criteria.max_deal_size = contact.maximumdealsize;
  }

  // Remove duplicates
  (Object.keys(criteria) as Array<keyof MatchingCriteria>).forEach(key => {
    const value = criteria[key];
    if (Array.isArray(value)) {
      criteria[key] = Array.from(new Set(value)) as any;
    }
  });

  return criteria;
}

/**
 * Extract location-based criteria as fallback
 */
function extractLocationCriteria(contact: any): MatchingCriteria {
  const criteria: MatchingCriteria = {
    property_types: [],
    property_sub_categories: [],
    strategies: [],
    countries: [],
    states: [],
    cities: [],
    loan_types: [],
    capital_positions: [],
    min_deal_size: null,
    max_deal_size: null,
  };

  // Priority 1: Contact location
  if (contact.contact_state) criteria.states.push(contact.contact_state);
  if (contact.contact_city) criteria.cities.push(contact.contact_city);
  if (contact.contact_country) criteria.countries.push(contact.contact_country);
  
  // Priority 2: Company location (if no contact location)
  if (criteria.states.length === 0 && criteria.cities.length === 0 && criteria.countries.length === 0) {
    if (contact.company_state) criteria.states.push(contact.company_state);
    if (contact.company_city) criteria.cities.push(contact.company_city);
    if (contact.company_country) criteria.countries.push(contact.company_country);
    
    // Priority 3: Parse company address if no direct location fields
    if (criteria.states.length === 0 && criteria.cities.length === 0 && criteria.countries.length === 0) {
      if (contact.company_address) {
        const addressParts = contact.company_address.split(',').map((part: string) => part.trim());
        if (addressParts.length >= 2) {
          // Extract city and state from address like "Pittsburgh, Pennsylvania, United States"
          const city = addressParts[0];
          const state = addressParts[1];
          const country = addressParts[2] || 'United States';
          
          criteria.cities.push(city);
          criteria.states.push(state);
          criteria.countries.push(country);
        }
      }
      
      // Priority 4: Company extracted data locations
      if (criteria.states.length === 0 && criteria.cities.length === 0 && criteria.countries.length === 0) {
        // Extract headquarters location
        if (contact.headquarters) {
          const parts = contact.headquarters.split(',').map((part: string) => part.trim());
          if (parts.length >= 2) {
            const city = parts[0];
            const state = parts[1];
            criteria.cities.push(city);
            criteria.states.push(state);
          }
        }
        
        // Extract geographic focus areas
        if (contact.geographicfocus && Array.isArray(contact.geographicfocus)) {
          contact.geographicfocus.forEach((location: string) => {
            if (typeof location === 'string') {
              if (location.includes('United States') || location.includes('USA')) {
                criteria.countries.push(location);
              } else if (location.length === 2 && location === location.toUpperCase()) {
                criteria.states.push(location);
              } else {
                criteria.cities.push(location);
              }
            }
          });
        }
        
        // Extract target markets
        if (contact.targetmarkets && Array.isArray(contact.targetmarkets)) {
          contact.targetmarkets.forEach((market: string) => {
            if (typeof market === 'string') {
              const parts = market.split(',').map((part: string) => part.trim());
              if (parts.length >= 2) {
                const city = parts[0];
                const state = parts[1];
                criteria.cities.push(city);
                criteria.states.push(state);
              } else {
                criteria.cities.push(market);
              }
            }
          });
        }
        
        // Extract office locations
        if (contact.officelocations && Array.isArray(contact.officelocations)) {
          contact.officelocations.forEach((office: string) => {
            if (typeof office === 'string') {
              const parts = office.split(',').map((part: string) => part.trim());
              if (parts.length >= 2) {
                const city = parts[0];
                const state = parts[1];
                criteria.cities.push(city);
                criteria.states.push(state);
              } else {
                criteria.cities.push(office);
              }
            }
          });
        }
      }
    }
  }

  // Remove duplicates
  (Object.keys(criteria) as Array<keyof MatchingCriteria>).forEach(key => {
    const value = criteria[key];
    if (Array.isArray(value)) {
      criteria[key] = Array.from(new Set(value)) as any;
    }
  });

  return criteria;
}

/**
 * Check if criteria has meaningful investment criteria (not just location)
 */
function hasInvestmentCriteria(criteria: MatchingCriteria): boolean {
  return criteria.property_types.length > 0 ||
         criteria.property_sub_categories.length > 0 ||
         criteria.strategies.length > 0 ||
         criteria.loan_types.length > 0 ||
         criteria.capital_positions.length > 0 ||
         criteria.min_deal_size !== null ||
         criteria.max_deal_size !== null;
}

/**
 * Get description of location sources used
 */
function getLocationDescription(contact: any, criteria: MatchingCriteria): string {
  const sources: string[] = [];
  
  if (contact.contact_state || contact.contact_city || contact.contact_country) {
    sources.push('contact location');
  }
  if (contact.company_state || contact.company_city || contact.company_country) {
    sources.push('company location');
  }
  if (contact.headquarters || contact.geographicfocus || contact.targetmarkets || contact.officelocations) {
    sources.push('company extracted data');
  }
  
  return sources.join(' and ');
}

export async function GET(
  request: NextRequest,
  { params }: { params: { contactId: string } }
) {
  try {
    const { contactId } = await params;
    
    if (!contactId) {
      return NextResponse.json(
        { error: 'Contact ID is required' },
        { status: 400 }
      );
    }

    // Get the contact and their company's information
    const contactQuery = `
      SELECT 
        c.contact_id,
        c.company_id,
        c.investment_criteria_country,
        c.investment_criteria_state,
        c.investment_criteria_city,
        c.investment_criteria_property_type,
        c.investment_criteria_asset_type,
        c.investment_criteria_loan_type,
        c.investment_criteria_deal_size,
        c.contact_city,
        c.contact_state,
        c.contact_country,
        co.company_name,
        co.company_city,
        co.company_state,
        co.company_country,
        ced.headquarters,
        ced.geographicfocus,
        ced.officelocations,
        ced.targetmarkets,
        ced.investment_criteria_property_types,
        ced.investment_criteria_property_subcategories,
        ced.investment_criteria_asset_types,
        ced.investment_criteria_loan_types,
        ced.minimumdealsize,
        ced.maximumdealsize,
        ced.strategies,
        ced.updated_at as extracted_data_updated
      FROM contacts c
      LEFT JOIN companies co ON c.company_id = co.company_id
      LEFT JOIN company_extracted_data ced ON co.company_id = ced.company_id::integer
      WHERE c.contact_id = $1
    `;

    const contactResult = await pool.query(contactQuery, [contactId]);
    
    if (contactResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Contact not found' },
        { status: 404 }
      );
    }

    const contact = contactResult.rows[0];

    // Get detailed investment criteria from investment_criteria table
    const criteriaQuery = `
      SELECT 
        ic.property_types,
        ic.property_sub_categories,
        ic.strategies,
        ic.minimum_deal_size,
        ic.maximum_deal_size,
        ic.country,
        ic.region,
        ic.state,
        ic.city,
        ic.loan_type,
        ic.capital_position,
        ic.entity_type,
        ic.entity_id,
        ic.entity_name
      FROM investment_criteria ic
      WHERE (
        (LOWER(ic.entity_type) = 'contact' AND ic.entity_id = $1::text) OR
        (LOWER(ic.entity_type) IN ('company', 'company overview') AND ic.entity_id = $2::text)
      )
    `;

    const criteriaResult = await pool.query(criteriaQuery, [
      contactId,
      contact.company_id?.toString() || '0'
    ]);

    // Hierarchical criteria checking
    let criteriaSource: CriteriaSource | null = null;
    let matches: NewsMatch[] = [];

    // Step 1: Try contact-level investment criteria ONLY
    const contactCriteria = extractContactCriteria(contact);
    if (hasInvestmentCriteria(contactCriteria)) {
      matches = await findNewsMatches(contactCriteria, true, false);
      criteriaSource = {
        source: 'contact',
        description: 'Using contact-level investment criteria',
        criteria: contactCriteria,
        hasInvestmentCriteria: true
      };
    }

    // Step 2: If no matches, try company-level investment criteria ONLY
    if (matches.length === 0 && !criteriaSource) {
      const companyCriteria = extractCompanyCriteria(contact, criteriaResult.rows);
      if (hasInvestmentCriteria(companyCriteria)) {
        matches = await findNewsMatches(companyCriteria, true, false);
        criteriaSource = {
          source: 'company',
          description: 'Using company-level investment criteria',
          criteria: companyCriteria,
          hasInvestmentCriteria: true
        };
      }
    }

    // Step 3: If still no matches, use location fallback
  

    // If no criteria source was determined, return empty results
    if (!criteriaSource) {
      const response: any = {
        contact: {
          contact_id: contact.contact_id,
          company_id: contact.company_id,
          company_name: contact.company_name
        },
        matches: [],
        criteria_source: 'none',
        criteria_description: 'No investment criteria or location information available',
        matching_criteria: {
          used_criteria: {},
          contact_criteria: extractContactCriteria(contact),
          company_criteria: extractCompanyCriteria(contact, criteriaResult.rows),
          location_criteria: extractLocationCriteria(contact)
        },
        total_evaluated: 0,
        total_matches: 0,
        top_matches_returned: 0
      };

      return NextResponse.json(response);
    }

    // Limit to top 50 matches
    const topMatches = matches.slice(0, 50);

    return NextResponse.json({
      contact: {
        contact_id: contact.contact_id,
        company_id: contact.company_id,
        company_name: contact.company_name
      },
      matches: topMatches,
      criteria_source: criteriaSource.source,
      criteria_description: criteriaSource.description,
      matching_criteria: {
        used_criteria: criteriaSource.criteria,
        contact_criteria: extractContactCriteria(contact),
        company_criteria: extractCompanyCriteria(contact, criteriaResult.rows),
        location_criteria: extractLocationCriteria(contact)
      },
      total_evaluated: matches.length,
      total_matches: matches.length,
      top_matches_returned: topMatches.length
    });

  } catch (error) {
    console.error('Error matching news for contact:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 

