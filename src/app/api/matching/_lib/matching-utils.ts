import { pool } from "@/lib/db";

// Types for matching
export interface FieldWeights {
  [fieldName: string]: number;
}

export interface MatchResult {
  score: number;
  reason: string;
  confidence: number;
}

// Weight management functions
export async function fetchAndNormalizeWeights(): Promise<FieldWeights> {
  try {
    const result = await pool.query(`SELECT field_name, weight FROM field_weights WHERE field_name != 'capital_position'`);
    const weights: FieldWeights = {};
    
    // Convert to object and calculate total
    let totalWeight = 0;
    for (const row of result.rows) {
      const weight = Number(row.weight);
      weights[row.field_name] = weight;
      totalWeight += weight;
    }
    
    // Normalize weights to sum to 1
    if (totalWeight > 0) {
      for (const [field] of Object.entries(weights)) {
        weights[field] = weights[field] / totalWeight;
      }
    }
    
    // console.log('Raw weights from database:', result.rows);
    // console.log('Normalized weights (sum to 1):', weights);
    // console.log('Total weight check:', Object.values(weights).reduce((sum, w) => sum + w, 0));
    
    return weights;
  } catch (error) {
    console.error('Error fetching weights, using defaults:', error);
    return getDefaultNormalizedWeights();
  }
}

export function getDefaultNormalizedWeights(): FieldWeights {
  // These are already normalized to sum to 1
  return {
    location: 0.35,           // 35% - Geographic matching
    deal_size: 0.30,          // 30% - Deal size overlap  
    property_types: 0.15,     // 15% - Asset type matching
    strategies: 0.10,         // 10% - Investment strategies
    financial_products: 0.05, // 5%  - Debt/equity/hybrid products
    loan_to_value: 0.03,      // 3%  - LTV ranges
    interest_rate: 0.02       // 2%  - Interest rate requirements
  };
}

// Scoring functions
export function calculateArrayMatchScore(dealArray: any, contactArray: any, fieldName: string): MatchResult {
  // Handle null or missing arrays
  if (!dealArray || !contactArray || 
      !Array.isArray(dealArray) || !Array.isArray(contactArray) ||
      dealArray.length === 0 || contactArray.length === 0) {
    return {
      score: 0,
      reason: `No ${fieldName} match - missing data`,
      confidence: 0.9
    };
  }
  
  // Calculate intersection - binary scoring (any overlap = 100%)
  const intersection = dealArray.filter(item => contactArray.includes(item));
  const intersectionCount = intersection.length;
  
  if (intersectionCount === 0) {
    return {
      score: 0,
      reason: `No ${fieldName} overlap`,
      confidence: 0.95
    };
  }
  
  // If there's any overlap, it's a 100% match for that field
  const matchedItems = intersection.join(', ');
  
  return {
    score: 1.0,
    reason: `${fieldName} match: ${matchedItems}`,
    confidence: 0.95
  };
}

export function calculateDealSizeScore(
  dealMin: number | null,
  dealMax: number | null,
  contactMin: number | null,
  contactMax: number | null
): MatchResult {
  // Handle missing data
  if ((!dealMin && !dealMax) || (!contactMin && !contactMax)) {
    const missingInfo = [];
    if (!dealMin && !dealMax) missingInfo.push('deal sizes');
    if (!contactMin && !contactMax) missingInfo.push('contact criteria');
    return {
      score: 0,
      reason: `Missing ${missingInfo.join(' and ')} data`,
      confidence: 0.7
    };
  }
  
  // Convert nulls to reasonable defaults
  const dMin = dealMin || 0;
  const dMax = dealMax || (dealMin ? dealMin * 10 : Number.MAX_SAFE_INTEGER);
  const cMin = contactMin || 0;
  const cMax = contactMax || (contactMin ? contactMin * 10 : Number.MAX_SAFE_INTEGER);
  
  // Add validation for reasonable deal sizes
  if (dMin < 0 || dMax < 0 || cMin < 0 || cMax < 0) {
    return {
      score: 0,
      reason: 'Invalid deal size values (negative)',
      confidence: 0.9
    };
  }

  // Ensure min <= max for both ranges
  const dealMinFixed = Math.min(dMin, dMax);
  const dealMaxFixed = Math.max(dMin, dMax);
  const contactMinFixed = Math.min(cMin, cMax);
  const contactMaxFixed = Math.max(cMin, cMax);
  
  // Correct overlap check: two ranges overlap if dealMin <= contactMax AND dealMax >= contactMin
  const hasOverlap = dealMinFixed <= contactMaxFixed && dealMaxFixed >= contactMinFixed;
  
  // Format ranges for display (database values are already in millions)
  const formatRange = (min: number, max: number) => {
    if (max === Number.MAX_SAFE_INTEGER) {
      return `$${min.toFixed(1)}M+`;
    } else if (min === max) {
      return `$${min.toFixed(1)}M`;
    } else {
      return `$${min.toFixed(1)}M-$${max.toFixed(1)}M`;
    }
  };
  
  const dealRange = formatRange(dealMinFixed, dealMaxFixed);
  const contactRange = formatRange(contactMinFixed, contactMaxFixed);
  
  if (hasOverlap) {
    // Calculate the actual overlapping range
    const overlapMin = Math.max(dealMinFixed, contactMinFixed);
    const overlapMax = Math.min(dealMaxFixed, contactMaxFixed);
    const overlapSize = overlapMax - overlapMin;
    
    // Calculate range sizes for scoring (avoid infinity calculations)
    const dealRangeSize = dealMaxFixed === Number.MAX_SAFE_INTEGER ? 
      dealMinFixed * 10 : Math.max(dealMaxFixed - dealMinFixed, 1);
    const contactRangeSize = contactMaxFixed === Number.MAX_SAFE_INTEGER ? 
      contactMinFixed * 10 : Math.max(contactMaxFixed - contactMinFixed, 1);
    
    // Special case: Deal is a single value (exact amount) within contact's range
    const dealIsSingleValue = dealMinFixed === dealMaxFixed;
    const dealWithinContactRange = dealMinFixed >= contactMinFixed && dealMaxFixed <= contactMaxFixed;
    
    if (dealIsSingleValue && dealWithinContactRange) {
      // Perfect match: exact deal amount falls within contact's acceptable range
      const finalScore = 1.0;
      const overlapRange = formatRange(overlapMin, overlapMax);
      
      return {
        score: finalScore,
        reason: `PERFECT DEAL SIZE MATCH: Deal ${dealRange} falls perfectly within Contact range ${contactRange} (100% match)`,
        confidence: 0.98
      };
    }
    
    // Calculate coverage ratios for range overlaps
    const dealCoverage = overlapSize / dealRangeSize;
    const contactCoverage = overlapSize / contactRangeSize;
    
    // Weighted score based on coverage
    const coverageScore = Math.min(dealCoverage * 0.6 + contactCoverage * 0.4, 1.0);
    
    // Bonus for perfect range alignment
    const perfectMatchBonus = (dealMinFixed === contactMinFixed && dealMaxFixed === contactMaxFixed) ? 0.1 : 0;
    
    const finalScore = Math.min(coverageScore + perfectMatchBonus, 1.0);
    
    const overlapRange = formatRange(overlapMin, overlapMax);
    
    return {
      score: finalScore,
      reason: `DEAL SIZE OVERLAP: Deal ${dealRange} ∩ Contact ${contactRange} = ${overlapRange} (${Math.round(finalScore * 100)}% match)`,
      confidence: 0.95
    };
  }
  
  // No overlap - calculate gap and provide partial scoring for close misses
  let gap: number;
  let gapDescription: string;
  
  if (dealMaxFixed < contactMinFixed) {
    // Deal range is entirely below contact range
    gap = contactMinFixed - dealMaxFixed;
    gapDescription = `Deal too small by $${gap.toFixed(1)}M`;
  } else {
    // Deal range is entirely above contact range (dealMinFixed > contactMaxFixed)
    gap = dealMinFixed - contactMaxFixed;
    gapDescription = `Deal too large by $${gap.toFixed(1)}M`;
  }
  
  // Calculate relative gap for partial scoring
  const avgDealSize = (dealMinFixed + dealMaxFixed) / 2;
  const avgContactSize = (contactMinFixed + contactMaxFixed) / 2;
  const avgOverallSize = (avgDealSize + avgContactSize) / 2;
  
  const relativeGap = avgOverallSize > 0 ? gap / avgOverallSize : 1;
  
  // Partial scoring for close misses
  if (relativeGap <= 0.15) {
    const nearMissScore = Math.max(0, (0.15 - relativeGap) / 0.15) * 0.4;
    return {
      score: nearMissScore,
      reason: `NO OVERLAP but CLOSE: Deal ${dealRange} vs Contact ${contactRange} - ${gapDescription} (${Math.round(nearMissScore * 100)}% partial credit)`,
      confidence: 0.8
    };
  } else if (relativeGap <= 0.3) {
    const distantScore = Math.max(0, (0.3 - relativeGap) / 0.3) * 0.2;
    return {
      score: distantScore,
      reason: `NO OVERLAP but NEARBY: Deal ${dealRange} vs Contact ${contactRange} - ${gapDescription} (${Math.round(distantScore * 100)}% partial credit)`,
      confidence: 0.75
    };
  }
  
  // Complete mismatch
  return {
    score: 0,
    reason: `NO DEAL SIZE MATCH: Deal ${dealRange} vs Contact ${contactRange} - ${gapDescription}`,
    confidence: 0.9
  };
}

export function calculateRangeMatchScore(
  dealMin: number | null,
  dealMax: number | null,
  contactMin: number | null,
  contactMax: number | null,
  fieldName: string,
  isPercentage: boolean = false
): MatchResult {
  const unitText = isPercentage ? '%' : '';
  
  // Handle missing data
  if ((!dealMin && !dealMax) || (!contactMin && !contactMax)) {
    return {
      score: 0,
      reason: `No ${fieldName} data available`,
      confidence: 0.7
    };
  }
  
  // Convert nulls to reasonable defaults and ensure proper ranges
  const dMin = Number(dealMin ?? dealMax ?? 0);
  const dMax = Number(dealMax ?? dealMin ?? 0);
  const cMin = Number(contactMin ?? 0);
  const cMax = Number(contactMax ?? 0);
  
  // Ensure min <= max for both ranges
  const dealMinFixed = Math.min(dMin, dMax);
  const dealMaxFixed = Math.max(dMin, dMax);
  const contactMinFixed = Math.min(cMin, cMax);
  const contactMaxFixed = Math.max(cMin, cMax);
  

  

  
  // Check if deal range is completely within contact range (PERFECT MATCH)
  if (dealMinFixed >= contactMinFixed && dealMaxFixed <= contactMaxFixed) {
    return {
      score: 1.0,
      reason: `${fieldName} PERFECT MATCH: Deal range ${dealMinFixed.toFixed(1)}${unitText}-${dealMaxFixed.toFixed(1)}${unitText} is completely within Contact range ${contactMinFixed.toFixed(1)}${unitText}-${contactMaxFixed.toFixed(1)}${unitText} (100% match)`,
      confidence: 0.98
    };
  }
  
  // Check if contact range is completely within deal range (also a good match)
  if (contactMinFixed >= dealMinFixed && contactMaxFixed <= dealMaxFixed) {
    return {
      score: 1.0,
      reason: `${fieldName} FULL COVERAGE: Contact range ${contactMinFixed.toFixed(1)}${unitText}-${contactMaxFixed.toFixed(1)}${unitText} is completely covered by Deal range ${dealMinFixed.toFixed(1)}${unitText}-${dealMaxFixed.toFixed(1)}${unitText} (100% match)`,
      confidence: 0.98
    };
  }
  
  // Calculate overlap
  const overlapMin = Math.max(dealMinFixed, contactMinFixed);
  const overlapMax = Math.min(dealMaxFixed, contactMaxFixed);
  
  // Add small tolerance for floating point precision issues
  const tolerance = 1e-6; // Increased tolerance for floating point precision issues
  
  if (overlapMin <= overlapMax + tolerance) {
    // There is partial overlap
    const overlapSize = overlapMax - overlapMin;
    const dealRangeSize = Math.max(dealMaxFixed - dealMinFixed, 0.01); // Avoid division by zero
    const contactRangeSize = Math.max(contactMaxFixed - contactMinFixed, 0.01);
    

    

    
    // Calculate how much of each range is covered by the overlap
    const dealCoverage = overlapSize / dealRangeSize;
    const contactCoverage = overlapSize / contactRangeSize;
    
    // Score based on the better coverage (more generous scoring)
    const bestCoverage = Math.max(dealCoverage, contactCoverage);
    const avgCoverage = (dealCoverage + contactCoverage) / 2;
    
    // Weighted score favoring the better coverage
    const finalScore = bestCoverage * 0.7 + avgCoverage * 0.3;
    
    return {
      score: Math.min(finalScore, 1.0),
      reason: `${fieldName} OVERLAP: Deal ${dealMinFixed.toFixed(1)}${unitText}-${dealMaxFixed.toFixed(1)}${unitText} ∩ Contact ${contactMinFixed.toFixed(1)}${unitText}-${contactMaxFixed.toFixed(1)}${unitText} = ${overlapMin.toFixed(1)}${unitText}-${overlapMax.toFixed(1)}${unitText} (${Math.round(finalScore * 100)}% match)`,
      confidence: 0.9
    };
  }
  
  // No overlap - check for near misses
  // Note: If we reach here, overlapMin > overlapMax, so gap = overlapMin - overlapMax
  const gap = overlapMin - overlapMax;
  const avgDealSize = (dealMinFixed + dealMaxFixed) / 2;
  const avgContactSize = (contactMinFixed + contactMaxFixed) / 2;
  const avgOverallSize = (avgDealSize + avgContactSize) / 2;
  
  const relativeGap = avgOverallSize > 0 ? gap / avgOverallSize : 1;
  

  
  // Near miss scoring for close ranges
  if (relativeGap <= 0.10) { // Within 10% gap
    const nearMissScore = Math.max(0, (0.10 - relativeGap) / 0.10) * 0.5;
    
    return {
      score: nearMissScore,
      reason: `${fieldName} NEAR MISS: Deal ${dealMinFixed.toFixed(1)}${unitText}-${dealMaxFixed.toFixed(1)}${unitText} vs Contact ${contactMinFixed.toFixed(1)}${unitText}-${contactMaxFixed.toFixed(1)}${unitText} (${Math.round(nearMissScore * 100)}% partial match)`,
      confidence: 0.7
    };
  } else if (relativeGap <= 0.25) { // Within 25% gap
    const distantScore = Math.max(0, (0.25 - relativeGap) / 0.25) * 0.2;
    return {
      score: distantScore,
      reason: `${fieldName} DISTANT: Deal ${dealMinFixed.toFixed(1)}${unitText}-${dealMaxFixed.toFixed(1)}${unitText} vs Contact ${contactMinFixed.toFixed(1)}${unitText}-${contactMaxFixed.toFixed(1)}${unitText} (${Math.round(distantScore * 100)}% partial match)`,
      confidence: 0.6
    };
  }
  
  return {
    score: 0,
    reason: `${fieldName} NO MATCH: Deal ${dealMinFixed.toFixed(1)}${unitText}-${dealMaxFixed.toFixed(1)}${unitText} vs Contact ${contactMinFixed.toFixed(1)}${unitText}-${contactMaxFixed.toFixed(1)}${unitText}`,
    confidence: 0.9
  };
}

// New scoring functions for additional investment criteria fields

export function calculateTargetReturnScore(
  dealReturn: number | null,
  contactReturn: number | null,
  tolerance: number = 0.02 // 2% tolerance
): MatchResult {
  if (!dealReturn || !contactReturn) {
    return {
      score: 0,
      reason: 'No target return data available',
      confidence: 0.7
    };
  }
  
  const diff = Math.abs(dealReturn - contactReturn);
  const relativeDiff = diff / contactReturn;
  
  if (relativeDiff <= tolerance) {
    const score = Math.max(0, 1 - (relativeDiff / tolerance));
    return {
      score,
      reason: `Target return match: ${(dealReturn * 100).toFixed(1)}% vs ${(contactReturn * 100).toFixed(1)}% (${Math.round(score * 100)}% compatibility)`,
      confidence: 0.95
    };
  }
  
  return {
    score: 0,
    reason: `Target return mismatch: ${(dealReturn * 100).toFixed(1)}% vs ${(contactReturn * 100).toFixed(1)}%`,
    confidence: 0.9
  };
}

export function calculateTextSimilarityScore(
  dealText: string | null,
  contactText: string | null,
  fieldName: string
): MatchResult {
  if (!dealText || !contactText) {
    return {
      score: 0,
      reason: `No ${fieldName} text data available`,
      confidence: 0.7
    };
  }
  
  // Simple text similarity (can be enhanced with fuzzy matching)
  const dealWords = dealText.toLowerCase().split(/\s+/);
  const contactWords = contactText.toLowerCase().split(/\s+/);
  
  const intersection = dealWords.filter(word => 
    contactWords.some(cWord => cWord.includes(word) || word.includes(cWord))
  );
  
  const similarity = intersection.length / Math.max(dealWords.length, contactWords.length);
  
  if (similarity > 0.3) {
    return {
      score: similarity,
      reason: `${fieldName} text similarity: ${Math.round(similarity * 100)}% match`,
      confidence: 0.8
    };
  }
  
  return {
    score: 0,
    reason: `${fieldName} text mismatch`,
    confidence: 0.8
  };
}

export function calculateInterestRateScore(
  dealRate: number | null,
  contactRate: number | null,
  rateType: string = 'standard'
): MatchResult {
  if (!dealRate || !contactRate) {
    return {
      score: 0,
      reason: `No ${rateType} interest rate data available`,
      confidence: 0.7
    };
  }
  
  const rateDiff = Math.abs(dealRate - contactRate);
  const tolerance = 0.01; // 1% tolerance
  
  if (rateDiff <= tolerance) {
    const score = Math.max(0, 1 - (rateDiff / tolerance));
    return {
      score,
      reason: `${rateType} rate match: ${(dealRate * 100).toFixed(2)}% vs ${(contactRate * 100).toFixed(2)}% (${Math.round(score * 100)}% compatibility)`,
      confidence: 0.95
    };
  }
  
  return {
    score: 0,
    reason: `${rateType} rate mismatch: ${(dealRate * 100).toFixed(2)}% vs ${(contactRate * 100).toFixed(2)}%`,
    confidence: 0.9
  };
}

export function calculateClosingTimeScore(
  dealClosingTime: number | null,
  contactClosingTime: number | null
): MatchResult {
  if (!dealClosingTime || !contactClosingTime) {
    return {
      score: 0,
      reason: 'No closing time data available',
      confidence: 0.7
    };
  }
  
  // Contact requirement should be >= deal requirement
  if (contactClosingTime >= dealClosingTime) {
    const efficiency = dealClosingTime / contactClosingTime;
    return {
      score: efficiency,
      reason: `Closing time compatible: ${dealClosingTime} weeks needed, ${contactClosingTime} weeks available (${Math.round(efficiency * 100)}% efficiency)`,
      confidence: 0.9
    };
  }
  
  return {
    score: 0,
    reason: `Closing time incompatible: ${dealClosingTime} weeks needed, only ${contactClosingTime} weeks available`,
    confidence: 0.95
  };
}

// Comprehensive field scorer that handles all investment criteria fields
export function calculateFieldScore(
  dealData: any,
  contactData: any,
  fieldName: string,
  fieldWeight: number
): { field: string; score: number; weight: number; reason: string; confidence: number } | null {
  
  if (fieldWeight <= 0) {
    return null; // Skip fields with 0 weight
  }
  
  let result: MatchResult;
  
  switch (fieldName) {
    // Geographic fields (combined into location)
    case 'location':
      const regionScore = calculateArrayMatchScore(dealData.region, contactData.region, "region");
      const stateScore = calculateArrayMatchScore(dealData.state, contactData.state, "state");
      const cityScore = calculateArrayMatchScore(dealData.city, contactData.city, "city");
      
      // Use best location match (city > state > region hierarchy)
      let bestLocationScore = regionScore.score * 0.5;
      let locationReason = regionScore.reason;
      
      if (stateScore.score > regionScore.score) {
        bestLocationScore = Math.max(bestLocationScore, stateScore.score * 0.7);
        locationReason = stateScore.reason;
      }
      
      if (cityScore.score > 0) {
        bestLocationScore = Math.max(bestLocationScore, cityScore.score);
        locationReason = cityScore.reason;
      }
      
      result = {
        score: bestLocationScore,
        reason: locationReason,
        confidence: Math.max(regionScore.confidence || 0.8, stateScore.confidence || 0.8, cityScore.confidence || 0.8)
      };
      break;

    // Deal size (combined min/max)
    case 'deal_size':
      result = calculateDealSizeScore(
        dealData.minimum_deal_size,
        dealData.maximum_deal_size,
        contactData.minimum_deal_size,
        contactData.maximum_deal_size
      );
      break;

    // Array fields
    case 'property_types':
    case 'property_sub_categories':
    case 'strategies':
    case 'financial_products':
    case 'loan_type':
    case 'loan_type_normalized':
    case 'loan_program':
    case 'structured_loan_tranche':
    case 'recourse_loan':
    case 'country':
    case 'region':
    case 'state':
    case 'city':
    case 'location_focus':
      result = calculateArrayMatchScore(
        dealData[fieldName],
        contactData[fieldName],
        fieldName.replace('_', ' ')
      );
      break;

    // Range fields
    case 'loan_to_value':
      result = calculateRangeMatchScore(
        dealData.loan_to_value_min,
        dealData.loan_to_value_max,
        contactData.loan_to_value_min,
        contactData.loan_to_value_max,
        'LTV',
        true
      );
      break;

    case 'loan_to_cost':
      result = calculateRangeMatchScore(
        dealData.loan_to_cost_min,
        dealData.loan_to_cost_max,
        contactData.loan_to_cost_min,
        contactData.loan_to_cost_max,
        'LTC',
        true
      );
      break;

    case 'hold_period':
      result = calculateRangeMatchScore(
        dealData.min_hold_period,
        dealData.max_hold_period,
        contactData.min_hold_period,
        contactData.max_hold_period,
        'hold period'
      );
      break;

    case 'loan_term':
      result = calculateRangeMatchScore(
        dealData.min_loan_term,
        dealData.max_loan_term,
        contactData.min_loan_term,
        contactData.max_loan_term,
        'loan term'
      );
      break;

    case 'loan_dscr':
      result = calculateRangeMatchScore(
        dealData.min_loan_dscr,
        dealData.max_loan_dscr,
        contactData.min_loan_dscr,
        contactData.max_loan_dscr,
        'DSCR'
      );
      break;

    case 'loan_origination_fee':
      result = calculateRangeMatchScore(
        dealData.loan_origination_fee_min,
        dealData.loan_origination_fee_max,
        contactData.loan_origination_fee_min,
        contactData.loan_origination_fee_max,
        'origination fee',
        true
      );
      break;

    case 'loan_exit_fee':
      result = calculateRangeMatchScore(
        dealData.loan_exit_fee_min,
        dealData.loan_exit_fee_max,
        contactData.loan_exit_fee_min,
        contactData.loan_exit_fee_max,
        'exit fee',
        true
      );
      break;

    // Single value fields
    case 'target_return':
      result = calculateTargetReturnScore(dealData.target_return, contactData.target_return);
      break;

    case 'historical_irr':
      result = calculateTargetReturnScore(dealData.historical_irr, contactData.historical_irr);
      break;

    case 'historical_em':
      result = calculateTargetReturnScore(dealData.historical_em, contactData.historical_em, 0.1);
      break;

    case 'interest_rate':
      result = calculateInterestRateScore(dealData.interest_rate, contactData.interest_rate);
      break;

    case 'interest_rate_sofr':
      result = calculateInterestRateScore(dealData.interest_rate_sofr, contactData.interest_rate_sofr, 'SOFR');
      break;

    case 'interest_rate_wsj':
      result = calculateInterestRateScore(dealData.interest_rate_wsj, contactData.interest_rate_wsj, 'WSJ Prime');
      break;

    case 'interest_rate_prime':
      result = calculateInterestRateScore(dealData.interest_rate_prime, contactData.interest_rate_prime, 'Prime');
      break;

    case 'interest_rate_libor':
      result = calculateInterestRateScore(dealData.interest_rate_libor, contactData.interest_rate_libor, 'LIBOR');
      break;

    case 'interest_rate_5yt':
      result = calculateInterestRateScore(dealData.interest_rate_5yt, contactData.interest_rate_5yt, '5Y Treasury');
      break;

    case 'interest_rate_10yt':
      result = calculateInterestRateScore(dealData.interest_rate_10yt, contactData.interest_rate_10yt, '10Y Treasury');
      break;

    case 'closing_time_weeks':
      result = calculateClosingTimeScore(dealData.closing_time_weeks, contactData.closing_time_weeks);
      break;

    // Text fields
    case 'capital_source':
      result = calculateTextSimilarityScore(dealData.capital_source, contactData.capital_source, 'capital source');
      break;

    case 'entity_name':
      result = calculateTextSimilarityScore(dealData.entity_name, contactData.entity_name, 'entity name');
      break;

    case 'notes':
      result = calculateTextSimilarityScore(dealData.notes, contactData.notes, 'notes');
      break;

    default:
      console.warn(`Unknown field for scoring: ${fieldName}`);
      return null;
  }

  return {
    field: fieldName,
    score: Math.round(result.score * 100),
    weight: fieldWeight,
    reason: result.reason,
    confidence: result.confidence || 0.8
  };
} 