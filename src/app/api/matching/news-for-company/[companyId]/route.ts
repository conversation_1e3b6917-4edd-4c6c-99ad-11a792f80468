import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: { companyId: string } }
) {
  try {
    const { companyId } = await params;

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      );
    }



    // First, get company details including overview data and extracted data
    const companyQuery = `
      SELECT 
        c.company_id,
        c.company_name,
        c.industry,
        c.company_city,
        c.company_state,
        c.company_country,
        c.company_address,
        c.overview,
        ced.headquarters,
        ced.geographicfocus,
        ced.officelocations,
        ced.targetmarkets,
        ced.investment_criteria_property_types,
        ced.investment_criteria_property_subcategories,
        ced.investment_criteria_asset_types,
        ced.investment_criteria_loan_types,
        ced.minimumdealsize,
        ced.maximumdealsize,
        ced.strategies,
        ced.updated_at as extracted_data_updated
      FROM companies c
      LEFT JOIN company_extracted_data ced ON c.company_id = ced.company_id::integer
      WHERE c.company_id = $1
    `;

    const companyResult = await pool.query(companyQuery, [companyId]);

    if (companyResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      );
    }

    const company = companyResult.rows[0];

    // Get investment criteria for this company
    const criteriaQuery = `
      SELECT 
        ic.property_types,
        ic.property_sub_categories,
        ic.strategies,
        ic.minimum_deal_size,
        ic.maximum_deal_size,
        ic.country,
        ic.region,
        ic.state,
        ic.city,
        ic.loan_type,
        ic.capital_position,
        ic.entity_type,
        ic.entity_id,
        ic.entity_name
      FROM investment_criteria ic
      WHERE (
        (LOWER(ic.entity_type) IN ('company', 'company overview') AND ic.entity_id = $1::text)
      )
      AND ic.is_active = true
    `;

    const criteriaResult = await pool.query(criteriaQuery, [companyId]);

    // Combine all investment criteria
    const allCriteria = {
      property_types: [] as string[],
      property_sub_categories: [] as string[],
      strategies: [] as string[],
      countries: [] as string[],
      states: [] as string[],
      cities: [] as string[],
      loan_types: [] as string[],
      capital_positions: [] as string[],
      min_deal_size: null as number | null,
      max_deal_size: null as number | null,
    };

    let hasInvestmentCriteria = false;
    let usingDefaultLocation = false;

    // Collect all values from criteria
    const detailedCriteria = criteriaResult.rows;
    
    if (detailedCriteria.length > 0) {
      hasInvestmentCriteria = true;
      
      detailedCriteria.forEach(criteria => {
        // Handle array fields
        if (criteria.property_types && Array.isArray(criteria.property_types)) {
          allCriteria.property_types.push(...criteria.property_types);
        }
        if (criteria.property_sub_categories && Array.isArray(criteria.property_sub_categories)) {
          allCriteria.property_sub_categories.push(...criteria.property_sub_categories);
        }
        if (criteria.strategies && Array.isArray(criteria.strategies)) {
          allCriteria.strategies.push(...criteria.strategies);
        }
        if (criteria.country && Array.isArray(criteria.country)) {
          allCriteria.countries.push(...criteria.country);
        }
        if (criteria.state && Array.isArray(criteria.state)) {
          allCriteria.states.push(...criteria.state);
        }
        if (criteria.city && Array.isArray(criteria.city)) {
          allCriteria.cities.push(...criteria.city);
        }
        if (criteria.loan_type && Array.isArray(criteria.loan_type)) {
          allCriteria.loan_types.push(...criteria.loan_type);
        }
        if (criteria.capital_position && typeof criteria.capital_position === 'string') {
          allCriteria.capital_positions.push(criteria.capital_position);
        }

        // Handle numeric fields - take min and max across all criteria
        if (criteria.minimum_deal_size && (allCriteria.min_deal_size === null || criteria.minimum_deal_size < allCriteria.min_deal_size)) {
          allCriteria.min_deal_size = criteria.minimum_deal_size;
        }
        if (criteria.maximum_deal_size && (allCriteria.max_deal_size === null || criteria.maximum_deal_size > allCriteria.max_deal_size)) {
          allCriteria.max_deal_size = criteria.maximum_deal_size;
        }
      });
    }

    // Also check for extracted investment criteria data (from company_extracted_data)
    let hasExtractedCriteria = false;
    if (company.investment_criteria_property_types || company.investment_criteria_property_subcategories || 
        company.investment_criteria_loan_types || company.strategies || 
        company.minimumdealsize || company.maximumdealsize) {
      
      hasExtractedCriteria = true;
      hasInvestmentCriteria = true;

      // Add extracted property types
      if (company.investment_criteria_property_types && Array.isArray(company.investment_criteria_property_types)) {
        allCriteria.property_types.push(...company.investment_criteria_property_types);
      }

      // Add extracted property subcategories
      if (company.investment_criteria_property_subcategories && Array.isArray(company.investment_criteria_property_subcategories)) {
        allCriteria.property_sub_categories.push(...company.investment_criteria_property_subcategories);
      }

      // Add extracted strategies
      if (company.strategies && Array.isArray(company.strategies)) {
        allCriteria.strategies.push(...company.strategies);
      }

      // Add extracted loan types
      if (company.investment_criteria_loan_types && Array.isArray(company.investment_criteria_loan_types)) {
        allCriteria.loan_types.push(...company.investment_criteria_loan_types);
      }

      // Handle deal size from extracted data
      if (company.minimumdealsize) {
        const minSize = parseFloat(company.minimumdealsize.replace(/[^0-9.]/g, ''));
        if (!isNaN(minSize) && (allCriteria.min_deal_size === null || minSize < allCriteria.min_deal_size)) {
          allCriteria.min_deal_size = minSize;
        }
      }
      
      if (company.maximumdealsize) {
        const maxSize = parseFloat(company.maximumdealsize.replace(/[^0-9.]/g, ''));
        if (!isNaN(maxSize) && (allCriteria.max_deal_size === null || maxSize > allCriteria.max_deal_size)) {
          allCriteria.max_deal_size = maxSize;
        }
      }
    }

    // If still no investment criteria, use location fallback
    if (!hasInvestmentCriteria) {
      hasInvestmentCriteria = false;
      usingDefaultLocation = true;
      
      // Priority 1: Basic company location fields
      if (company.company_state) {
        allCriteria.states.push(company.company_state);
      }
      if (company.company_city) {
        allCriteria.cities.push(company.company_city);
      }
      if (company.company_country) {
        allCriteria.countries.push(company.company_country);
      }

      // Priority 2: Company extracted data (more structured)
      if (company.headquarters || company.geographicfocus || company.targetmarkets) {
        // Extract headquarters location
        if (company.headquarters) {
          const headquarters = company.headquarters;
          // Parse headquarters like "New York, NY" or "Los Angeles, CA"
          const parts = headquarters.split(',').map((part: string) => part.trim());
          if (parts.length >= 2) {
            const city = parts[0];
            const state = parts[1];
            if (!allCriteria.cities.includes(city)) {
              allCriteria.cities.push(city);
            }
            if (!allCriteria.states.includes(state)) {
              allCriteria.states.push(state);
            }
          }
        }
        
        // Extract geographic focus areas
        if (company.geographicfocus && Array.isArray(company.geographicfocus)) {
          company.geographicfocus.forEach((location: string) => {
            if (typeof location === 'string') {
              // Handle various formats - could be states, countries, or regions
              if (!allCriteria.cities.some(city => city.toLowerCase() === location.toLowerCase()) &&
                  !allCriteria.states.some(state => state.toLowerCase() === location.toLowerCase())) {
                // Add to appropriate category based on common patterns
                if (location.includes('United States') || location.includes('USA')) {
                  if (!allCriteria.countries.includes(location)) {
                    allCriteria.countries.push(location);
                  }
                } else if (location.length === 2 && location === location.toUpperCase()) {
                  // Likely a state abbreviation
                  allCriteria.states.push(location);
                } else {
                  // Default to cities/regions
                  allCriteria.cities.push(location);
                }
              }
            }
          });
        }
        
        // Extract target markets (these are typically more specific locations)
        if (company.targetmarkets && Array.isArray(company.targetmarkets)) {
          company.targetmarkets.forEach((market: string) => {
            if (typeof market === 'string') {
              // Parse target markets - these are usually cities or specific regions
                             const parts = market.split(',').map((part: string) => part.trim());
              if (parts.length >= 2) {
                const city = parts[0];
                const state = parts[1];
                if (!allCriteria.cities.includes(city)) {
                  allCriteria.cities.push(city);
                }
                if (!allCriteria.states.includes(state)) {
                  allCriteria.states.push(state);
                }
              } else {
                // Single location entry
                if (!allCriteria.cities.some(city => city.toLowerCase() === market.toLowerCase())) {
                  allCriteria.cities.push(market);
                }
              }
            }
          });
        }
        
        // Extract office locations if available
        if (company.officelocations && Array.isArray(company.officelocations)) {
          company.officelocations.forEach((office: string) => {
            if (typeof office === 'string') {
                             const parts = office.split(',').map((part: string) => part.trim());
              if (parts.length >= 2) {
                const city = parts[0];
                const state = parts[1];
                if (!allCriteria.cities.includes(city)) {
                  allCriteria.cities.push(city);
                }
                if (!allCriteria.states.includes(state)) {
                  allCriteria.states.push(state);
                }
              } else {
                if (!allCriteria.cities.some(city => city.toLowerCase() === office.toLowerCase())) {
                  allCriteria.cities.push(office);
                }
              }
            }
          });
        }
      }

      // Priority 3: Overview location data (legacy format)
      if (company.overview && typeof company.overview === 'object') {
        const overview = company.overview;
        
        // Extract headquarters location
        if (overview.companyProfile?.headquarters) {
          const headquarters = overview.companyProfile.headquarters;
          // Parse headquarters like "New York, NY" or "Los Angeles, CA"
          if (typeof headquarters === 'string') {
            const parts = headquarters.split(',').map((part: string) => part.trim());
            if (parts.length >= 2) {
              const city = parts[0];
              const state = parts[1];
              if (!allCriteria.cities.includes(city)) {
                allCriteria.cities.push(city);
              }
              if (!allCriteria.states.includes(state)) {
                allCriteria.states.push(state);
              }
            }
          }
        }
        
        // Extract geographic focus areas
        if (overview.companyProfile?.geographicFocus && Array.isArray(overview.companyProfile.geographicFocus)) {
          overview.companyProfile.geographicFocus.forEach((location: string) => {
            if (typeof location === 'string') {
              // Handle various formats like "New York City", "California", "United States"
              if (!allCriteria.cities.some(city => city.toLowerCase() === location.toLowerCase())) {
                allCriteria.cities.push(location);
              }
            }
          });
        }
        
        // Extract office locations
        if (overview.companyProfile?.officeLocations && Array.isArray(overview.companyProfile.officeLocations)) {
          overview.companyProfile.officeLocations.forEach((location: string) => {
            if (typeof location === 'string') {
              // Parse office locations similar to headquarters
              const parts = location.split(',').map((part: string) => part.trim());
              if (parts.length >= 2) {
                const city = parts[0];
                const state = parts[1];
                if (!allCriteria.cities.includes(city)) {
                  allCriteria.cities.push(city);
                }
                if (!allCriteria.states.includes(state)) {
                  allCriteria.states.push(state);
                }
              } else {
                // Single location entry
                if (!allCriteria.cities.some(city => city.toLowerCase() === location.toLowerCase())) {
                  allCriteria.cities.push(location);
                }
              }
            }
          });
        }
        
        // Extract target markets from investment criteria
        if (overview.investmentCriteria?.targetMarkets && Array.isArray(overview.investmentCriteria.targetMarkets)) {
          overview.investmentCriteria.targetMarkets.forEach((market: string) => {
            if (typeof market === 'string') {
              if (!allCriteria.cities.some(city => city.toLowerCase() === market.toLowerCase()) &&
                  !allCriteria.states.some(state => state.toLowerCase() === market.toLowerCase())) {
                // Try to determine if it's a city or state, default to cities
                allCriteria.cities.push(market);
              }
            }
          });
        }
      }
    }

    // Remove duplicates from arrays
    Object.keys(allCriteria).forEach(key => {
      if (Array.isArray(allCriteria[key as keyof typeof allCriteria])) {
        (allCriteria[key as keyof typeof allCriteria] as string[]) = 
          [...new Set(allCriteria[key as keyof typeof allCriteria] as string[])];
      }
    });

    // If no criteria and no location, return empty results
    if (!hasInvestmentCriteria && allCriteria.states.length === 0 && allCriteria.cities.length === 0 && allCriteria.countries.length === 0) {
      return NextResponse.json({
        company,
        matches: [],
        matching_criteria: {
          aggregated_criteria: allCriteria,
          detailed_criteria: [],
          company_criteria: false,
          using_default_location: false,
          default_location_reason: 'No investment criteria or location information available'
        }
      });
    }

    // Build the matching query for news - using news_enrichment table
    const matchingConditions: string[] = [];
    const queryParams: any[] = [];
    let paramCount = 0;

    // Property types matching (only if we have investment criteria)
    if (hasInvestmentCriteria && allCriteria.property_types.length > 0) {
      paramCount++;
      matchingConditions.push(`
        (ne.property_type::jsonb ?| $${paramCount}::text[] OR 
         EXISTS (
           SELECT 1 FROM jsonb_array_elements_text(ne.property_type) AS pt 
           WHERE pt = ANY($${paramCount}::text[])
         ))
      `);
      queryParams.push(allCriteria.property_types);
    }

    // Property sub-categories matching (only if we have investment criteria)
    if (hasInvestmentCriteria && allCriteria.property_sub_categories.length > 0) {
      paramCount++;
      matchingConditions.push(`
        (ne.sub_property_type::jsonb ?| $${paramCount}::text[] OR
         EXISTS (
           SELECT 1 FROM jsonb_array_elements_text(ne.sub_property_type) AS psc
           WHERE psc = ANY($${paramCount}::text[])
         ))
      `);
      queryParams.push(allCriteria.property_sub_categories);
    }

    // Strategies matching (only if we have investment criteria)
    if (hasInvestmentCriteria && allCriteria.strategies.length > 0) {
      paramCount++;
      matchingConditions.push(`
        (ne.deal_type::jsonb ?| $${paramCount}::text[] OR
         EXISTS (
           SELECT 1 FROM jsonb_array_elements_text(ne.deal_type) AS dt
           WHERE dt = ANY($${paramCount}::text[])
         ))
      `);
      queryParams.push(allCriteria.strategies);
    }

    // Loan types matching (only if we have investment criteria)
    if (hasInvestmentCriteria && allCriteria.loan_types.length > 0) {
      paramCount++;
      matchingConditions.push(`
        (ne.loan_type::jsonb ?| $${paramCount}::text[] OR
         EXISTS (
           SELECT 1 FROM jsonb_array_elements_text(ne.loan_type) AS lt
           WHERE lt = ANY($${paramCount}::text[])
         ))
      `);
      queryParams.push(allCriteria.loan_types);
    }

    // Deal size matching (only if we have investment criteria)
    if (hasInvestmentCriteria && (allCriteria.min_deal_size || allCriteria.max_deal_size)) {
      const sizeConditions: string[] = [];
      
      if (allCriteria.min_deal_size) {
        paramCount++;
        sizeConditions.push(`ne.deal_size >= $${paramCount}`);
        queryParams.push(allCriteria.min_deal_size);
      }
      
      if (allCriteria.max_deal_size) {
        paramCount++;
        sizeConditions.push(`ne.deal_size <= $${paramCount}`);
        queryParams.push(allCriteria.max_deal_size);
      }
      
      if (sizeConditions.length > 0) {
        matchingConditions.push(`(${sizeConditions.join(' AND ')})`);
      }
    }

    // Robust location matching: handle both arrays and scalars
    const locationStateParam = allCriteria.states.length > 0 ? allCriteria.states : [];
    const locationCityParam = allCriteria.cities.length > 0 ? allCriteria.cities : [];
    const locationCountryParam = allCriteria.countries.length > 0 ? allCriteria.countries : [];

    if (locationStateParam.length > 0) {
      paramCount++;
      matchingConditions.push(`
        ((jsonb_typeof(ne.location_state) = 'array' AND (
            ne.location_state ?| $${paramCount}::text[] OR
            EXISTS (
              SELECT 1 FROM jsonb_array_elements_text(ne.location_state) AS ls
              WHERE ls = ANY($${paramCount}::text[])
            )
        )) OR
        (jsonb_typeof(ne.location_state) = 'string' AND ne.location_state @> to_jsonb($${paramCount}::text)))
      `);
      queryParams.push(locationStateParam);
    }

    if (locationCityParam.length > 0) {
      paramCount++;
      matchingConditions.push(`
        ((jsonb_typeof(ne.location_city) = 'array' AND (
            ne.location_city ?| $${paramCount}::text[] OR
            EXISTS (
              SELECT 1 FROM jsonb_array_elements_text(ne.location_city) AS lc
              WHERE lc = ANY($${paramCount}::text[])
            )
        )) OR
        (jsonb_typeof(ne.location_city) = 'string' AND ne.location_city @> to_jsonb($${paramCount}::text)))
      `);
      queryParams.push(locationCityParam);
    }

    if (locationCountryParam.length > 0) {
      paramCount++;
      matchingConditions.push(`
        ((jsonb_typeof(ne.location_state) = 'array' AND (
            ne.location_state ?| $${paramCount}::text[] OR
            EXISTS (
              SELECT 1 FROM jsonb_array_elements_text(ne.location_state) AS country_match
              WHERE country_match = ANY($${paramCount}::text[])
            )
        )) OR
        (jsonb_typeof(ne.location_state) = 'string' AND ne.location_state @> to_jsonb($${paramCount}::text)) OR
        (jsonb_typeof(ne.location_city) = 'array' AND (
            ne.location_city ?| $${paramCount}::text[] OR
            EXISTS (
              SELECT 1 FROM jsonb_array_elements_text(ne.location_city) AS country_match
              WHERE country_match = ANY($${paramCount}::text[])
            )
        )) OR
        (jsonb_typeof(ne.location_city) = 'string' AND ne.location_city @> to_jsonb($${paramCount}::text)))
      `);
      queryParams.push(locationCountryParam);
    }

    // If no matching conditions, return empty results
    if (matchingConditions.length === 0) {
      return NextResponse.json({
        company,
        matches: [],
        matching_criteria: {
          aggregated_criteria: allCriteria,
          detailed_criteria: detailedCriteria,
          company_criteria: hasInvestmentCriteria,
          using_default_location: usingDefaultLocation,
          default_location_reason: usingDefaultLocation ? `Using company location (${company.company_city || 'N/A'}, ${company.company_state || 'N/A'}) as fallback since entity lacks specific investment criteria. Results sorted by deal size descending.` : null
        }
      });
    }

    // Build the final query using news_enrichment table
    const newsQuery = `
      SELECT 
        ne.id as enrichment_id,
        ne.news_id,
        ne.headline,
        ne.summary,
        ne.publication_date,
        ne.source_name,
        ne.source_url,
        ne.deal_size,
        ne.property_type,
        ne.sub_property_type,
        ne.deal_type,
        ne.deal_status,
        ne.location_city,
        ne.location_state,
        ne.buyer_name,
        ne.seller_name,
        ne.lender_name,
        ne.square_footage,
        ne.unit_count,
        ne.loan_type,
        ne.is_deal_specific
      FROM news_enrichment ne
      WHERE (${matchingConditions.join(' OR ')})
      AND ne.publication_date >= CURRENT_DATE - INTERVAL '90 days'
      AND ne.is_deal_specific = true
      AND ne.deal_size IS NOT NULL
      AND ne.deal_size > 0
      ORDER BY ne.deal_size DESC NULLS LAST, ne.publication_date DESC
      LIMIT 50
    `;

    const newsResult = await pool.query(newsQuery, queryParams);

    // Calculate match scores for each news item
    const scoredMatches = newsResult.rows.map(newsItem => {
      const matches = [];
      const breakdown = [];
      let totalScore = 0;
      let totalWeight = 0;

      if (hasInvestmentCriteria) {
        // Property type matching (weight: 0.25)
        if (allCriteria.property_types.length > 0 && newsItem.property_type) {
          const newsPropertyTypes = Array.isArray(newsItem.property_type) ? newsItem.property_type : [newsItem.property_type];
          const intersection = newsPropertyTypes.filter((pt: string) => 
            allCriteria.property_types.some(cpt => cpt.toLowerCase() === pt.toLowerCase())
          );
          if (intersection.length > 0) {
            const score = Math.min(100, (intersection.length / Math.max(newsPropertyTypes.length, allCriteria.property_types.length)) * 100);
            matches.push(`Property type: ${intersection.join(', ')}`);
            breakdown.push({ field: 'property_types', score: Math.round(score), weight: 0.25, reason: `Matched: ${intersection.join(', ')}` });
            totalScore += score * 0.25;
            totalWeight += 0.25;
          }
        }

        // Property sub-category matching (weight: 0.2)
        if (allCriteria.property_sub_categories.length > 0 && newsItem.sub_property_type) {
          const newsSubTypes = Array.isArray(newsItem.sub_property_type) ? newsItem.sub_property_type : [newsItem.sub_property_type];
          const intersection = newsSubTypes.filter((psc: string) => 
            allCriteria.property_sub_categories.some(cpsc => cpsc.toLowerCase() === psc.toLowerCase())
          );
          if (intersection.length > 0) {
            const score = Math.min(100, (intersection.length / Math.max(newsSubTypes.length, allCriteria.property_sub_categories.length)) * 100);
            matches.push(`Property subcategory: ${intersection.join(', ')}`);
            breakdown.push({ field: 'property_subcategories', score: Math.round(score), weight: 0.2, reason: `Matched: ${intersection.join(', ')}` });
            totalScore += score * 0.2;
            totalWeight += 0.2;
          }
        }

        // Deal type/strategy matching (weight: 0.2)
        if (allCriteria.strategies.length > 0 && newsItem.deal_type) {
          const newsDealTypes = Array.isArray(newsItem.deal_type) ? newsItem.deal_type : [newsItem.deal_type];
          const intersection = newsDealTypes.filter((dt: string) => 
            allCriteria.strategies.some(cs => cs.toLowerCase() === dt.toLowerCase())
          );
          if (intersection.length > 0) {
            const score = Math.min(100, (intersection.length / Math.max(newsDealTypes.length, allCriteria.strategies.length)) * 100);
            matches.push(`Strategy: ${intersection.join(', ')}`);
            breakdown.push({ field: 'strategies', score: Math.round(score), weight: 0.2, reason: `Matched: ${intersection.join(', ')}` });
            totalScore += score * 0.2;
            totalWeight += 0.2;
          }
        }

        // Deal size matching (weight: 0.15)
        if ((allCriteria.min_deal_size || allCriteria.max_deal_size) && newsItem.deal_size) {
          let sizeScore = 100;
          let sizeReason = 'Deal size within range';
          
          if (allCriteria.min_deal_size && newsItem.deal_size < allCriteria.min_deal_size) {
            sizeScore = Math.max(0, 100 - ((allCriteria.min_deal_size - newsItem.deal_size) / allCriteria.min_deal_size * 100));
            sizeReason = `Below minimum (${newsItem.deal_size}M vs ${allCriteria.min_deal_size}M+)`;
          }
          
          if (allCriteria.max_deal_size && newsItem.deal_size > allCriteria.max_deal_size) {
            sizeScore = Math.max(0, 100 - ((newsItem.deal_size - allCriteria.max_deal_size) / allCriteria.max_deal_size * 100));
            sizeReason = `Above maximum (${newsItem.deal_size}M vs ${allCriteria.max_deal_size}M max)`;
          }
          
          if (sizeScore > 0) {
            matches.push(`Deal size: $${newsItem.deal_size}M`);
            breakdown.push({ field: 'deal_size', score: Math.round(sizeScore), weight: 0.15, reason: sizeReason });
            totalScore += sizeScore * 0.15;
            totalWeight += 0.15;
          }
        }

        // Loan type matching (weight: 0.1)
        if (allCriteria.loan_types.length > 0 && newsItem.loan_type) {
          const newsLoanTypes = Array.isArray(newsItem.loan_type) ? newsItem.loan_type : [newsItem.loan_type];
          const intersection = newsLoanTypes.filter((lt: string) => 
            allCriteria.loan_types.some(clt => clt.toLowerCase() === lt.toLowerCase())
          );
          if (intersection.length > 0) {
            matches.push(`Loan type: ${intersection.join(', ')}`);
            breakdown.push({ field: 'loan_types', score: 100, weight: 0.1, reason: `Matched: ${intersection.join(', ')}` });
            totalScore += 100 * 0.1;
            totalWeight += 0.1;
          }
        }
      }

      // Geographic matching (higher weight when using default location)
      const geoWeight = usingDefaultLocation ? 0.5 : 0.1;
      
      if (allCriteria.states.length > 0 && newsItem.location_state) {
        const newsStates = Array.isArray(newsItem.location_state) ? newsItem.location_state : [newsItem.location_state];
        const intersection = newsStates.filter((ls: string) => 
          allCriteria.states.some(cs => cs.toLowerCase() === ls.toLowerCase())
        );
        if (intersection.length > 0) {
          matches.push(`State: ${intersection.join(', ')}`);
          breakdown.push({ field: 'states', score: 100, weight: geoWeight, reason: `Matched: ${intersection.join(', ')}` });
          totalScore += 100 * geoWeight;
          totalWeight += geoWeight;
        }
      }

      if (allCriteria.cities.length > 0 && newsItem.location_city) {
        const newsCities = Array.isArray(newsItem.location_city) ? newsItem.location_city : [newsItem.location_city];
        const intersection = newsCities.filter((lc: string) => 
          allCriteria.cities.some(cc => cc.toLowerCase() === lc.toLowerCase())
        );
        if (intersection.length > 0) {
          matches.push(`City: ${intersection.join(', ')}`);
          breakdown.push({ field: 'cities', score: 100, weight: geoWeight, reason: `Matched: ${intersection.join(', ')}` });
          totalScore += 100 * geoWeight;
          totalWeight += geoWeight;
        }
      }

      // Calculate final score (normalize by total weight to handle missing criteria)
      let finalScore = 0;
      if (usingDefaultLocation) {
        // For default location matching, prioritize deal size and geographic match
        finalScore = totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
      } else {
        finalScore = totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
      }

      return {
        ...newsItem,
        score: finalScore,
        reasons: matches,
        breakdown: breakdown,
        deal_size_rank: true // Indicates this was sorted by deal size as requested
      };
    }).filter(item => item.score > 0 || usingDefaultLocation) // Include all when using default location
    .sort((a, b) => {
      // When using default location, sort primarily by deal size
      if (usingDefaultLocation) {
        return (b.deal_size || 0) - (a.deal_size || 0);
      }
      // Otherwise sort by score first, then deal size
      if (b.score !== a.score) return b.score - a.score;
      return (b.deal_size || 0) - (a.deal_size || 0);
    });

    return NextResponse.json({
      company,
      matches: scoredMatches,
      matching_criteria: {
        aggregated_criteria: allCriteria,
        detailed_criteria: detailedCriteria,
        company_criteria: hasInvestmentCriteria,
        using_default_location: usingDefaultLocation,
        default_location_reason: usingDefaultLocation ? 
          `Using company location (${company.company_city || 'N/A'}, ${company.company_state || 'N/A'}) as fallback since entity lacks specific investment criteria. Results sorted by deal size descending.` : 
          null
      }
    });

  } catch (error) {
    console.error('Error in news-for-company API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 