import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";
import {
  fetchAndNormalizeWeights,
  calculateArrayMatchScore,
  calculateDealSizeScore,
  calculateRangeMatchScore
} from "../../_lib/matching-utils";

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ dealId: string }> }
) {
  try {
    const { dealId } = await context.params;
    const { searchParams } = new URL(req.url);
    
    // Check for CRM mode parameter
    const isCrmMode = searchParams.get('crm_mode') === 'true';
    
    // Parse filtering parameters
    const showAllMatches = searchParams.get('show_all') === 'true';
    const minScoreThreshold = showAllMatches ? 0 : 50; // Default to 50% if not showing all
    
    // Fetch and normalize field weights from database
    const fieldWeights = await fetchAndNormalizeWeights();
    
    console.log(`Fetching matching contacts for deal ${dealId} using pre-filtered SQL approach`);
    console.log("Using normalized field weights for contacts-for-deal:", fieldWeights);
    if (isCrmMode) {
      console.log("CRM Mode enabled: Filtering to only include contacts with requested investment criteria");
    }

    // Enhanced SQL to first get deal criteria, then filter contacts/companies by capital position
    const sql = `
      WITH deal_criteria AS (
        SELECT 
          ic.*
        FROM investment_criteria ic
        WHERE ic.entity_type = 'Deal' 
          AND ic.entity_id = $1::text 
      ),
      deal_capital_positions AS (
        SELECT DISTINCT unnest(capital_position) as capital_pos
        FROM deal_criteria
        WHERE capital_position IS NOT NULL
          ${isCrmMode ? 'AND is_requested = true' : ''}
      ),
      matching_contact_criteria AS (
        SELECT DISTINCT ic.*
        FROM investment_criteria ic
        INNER JOIN deal_capital_positions dcp ON (ic.capital_position && ARRAY[dcp.capital_pos])
        WHERE ic.entity_type = 'Contact' 
          AND ic.is_active = true
      ),
      matching_company_criteria AS (
        SELECT DISTINCT ic.*
        FROM investment_criteria ic
        INNER JOIN deal_capital_positions dcp ON (ic.capital_position && ARRAY[dcp.capital_pos])
        INNER JOIN contacts ct ON ic.entity_id = ct.company_id::text
        WHERE ic.entity_type LIKE 'Company%' 
          AND ic.is_active = true
      ),
      all_matching_criteria AS (
        SELECT *, 'Contact' as criteria_source FROM matching_contact_criteria
        UNION ALL
        SELECT *, 'Company' as criteria_source FROM matching_company_criteria
      )
      SELECT
        c.entity_id AS contact_id,
        c.criteria_id AS contact_criteria_id,
        c.criteria_source,
        ct.first_name,
        ct.last_name,
        ct.email,
        ct.company_id,
        ct.title AS job_title,
        ct.phone_number,
        ct.linkedin_url,
        ct.updated_at AS contact_updated_at,
        ct.created_at AS contact_created_at,
        comp.company_name,
        comp.company_city,
        comp.company_state,
        comp.company_website,
        comp.industry,
        c.minimum_deal_size,
        c.maximum_deal_size,
        c.historical_irr,
        c.historical_em,
        c.region AS contact_region,
        c.state AS contact_state,
        c.city AS contact_city,
        c.country AS contact_country,
        c.capital_position AS contact_capital_position,
        c.property_types AS contact_property_types,
        c.strategies AS contact_strategies,
        c.financial_products AS contact_financial_products,
        c.loan_type AS contact_loan_type,
        c.loan_program AS contact_loan_program,
        c.recourse_loan AS contact_recourse_loan,
        c.structured_loan_tranche AS contact_structured_loan_tranche,
        c.property_sub_categories AS contact_property_sub_categories,
        c.capital_source AS contact_capital_source,
        c.loan_type_normalized AS contact_loan_type_normalized,
        c.interest_rate_sofr AS contact_interest_rate_sofr,
        c.interest_rate_wsj AS contact_interest_rate_wsj,
        c.interest_rate_prime AS contact_interest_rate_prime,
        c.interest_rate_libor AS contact_interest_rate_libor,
        c.interest_rate_5yt AS contact_interest_rate_5yt,
        c.interest_rate_10yt AS contact_interest_rate_10yt,
        c.loan_term_string AS contact_loan_term_string,
        c.location_focus AS contact_location_focus,
        c.min_closing_time_weeks AS contact_min_closing_time_weeks,
        c.max_closing_time_weeks AS contact_max_closing_time_weeks,
        c.closing_time_weeks AS contact_closing_time_weeks,
        c.target_return AS contact_target_return,
        c.min_hold_period,
        c.max_hold_period,
        c.min_loan_term,
        c.max_loan_term,
        c.interest_rate,
        c.loan_to_value_min,
        c.loan_to_value_max,
        c.loan_to_cost_min,
        c.loan_to_cost_max,
        c.loan_origination_fee_min,
        c.loan_origination_fee_max,
        c.loan_exit_fee_min,
        c.loan_exit_fee_max,
        c.min_loan_dscr,
        c.max_loan_dscr,
        -- Deal criteria for comparison
        d.region AS deal_region,
        d.state AS deal_state,
        d.city AS deal_city,
        d.country AS deal_country,
        d.capital_position AS deal_capital_position,
        d.property_types AS deal_property_types,
        d.strategies AS deal_strategies,
        d.financial_products AS deal_financial_products,
        d.loan_type AS deal_loan_type,
        d.loan_program AS deal_loan_program,
        d.recourse_loan AS deal_recourse_loan,
        d.structured_loan_tranche AS deal_structured_loan_tranche,
        d.property_sub_categories AS deal_property_sub_categories,
        d.capital_source AS deal_capital_source,
        d.loan_type_normalized AS deal_loan_type_normalized,
        d.interest_rate_sofr AS deal_interest_rate_sofr,
        d.interest_rate_wsj AS deal_interest_rate_wsj,
        d.interest_rate_prime AS deal_interest_rate_prime,
        d.interest_rate_libor AS deal_interest_rate_libor,
        d.interest_rate_5yt AS deal_interest_rate_5yt,
        d.interest_rate_10yt AS deal_interest_rate_10yt,
        d.loan_term_string AS deal_loan_term_string,
        d.location_focus AS deal_location_focus,
        d.min_closing_time_weeks AS deal_min_closing_time_weeks,
        d.max_closing_time_weeks AS deal_max_closing_time_weeks,
        d.closing_time_weeks AS deal_closing_time_weeks,
        d.target_return AS deal_target_return,
        d.minimum_deal_size AS deal_min_deal_size,
        d.maximum_deal_size AS deal_max_deal_size,
        d.min_hold_period AS deal_min_hold_period,
        d.max_hold_period AS deal_max_hold_period,
        d.min_loan_term AS deal_min_loan_term,
        d.max_loan_term AS deal_max_loan_term,
        d.interest_rate AS deal_interest_rate,
        d.loan_to_value_min AS deal_loan_to_value_min,
        d.loan_to_value_max AS deal_loan_to_value_max,
        d.loan_to_cost_min AS deal_loan_to_cost_min,
        d.loan_to_cost_max AS deal_loan_to_cost_max,
        d.loan_origination_fee_min AS deal_loan_origination_fee_min,
        d.loan_origination_fee_max AS deal_loan_origination_fee_max,
        d.loan_exit_fee_min AS deal_loan_exit_fee_min,
        d.loan_exit_fee_max AS deal_loan_exit_fee_max,
        d.min_loan_dscr AS deal_min_loan_dscr,
        d.max_loan_dscr AS deal_max_loan_dscr,
        d.historical_irr AS deal_historical_irr,
        d.historical_em AS deal_historical_em,
        -- Capital position match (pre-filtered, so should always be 1)
        1 AS capital_position_match,
        d.criteria_id AS deal_criteria_id

      FROM all_matching_criteria c
      INNER JOIN deal_criteria d ON (d.capital_position && c.capital_position${isCrmMode ? ' AND d.is_requested = true' : ''})
      INNER JOIN contacts ct ON c.entity_id = ct.contact_id::text
      LEFT JOIN companies comp ON ct.company_id = comp.company_id
      ORDER BY c.criteria_source DESC, c.entity_id, c.criteria_id;
    `;
    
    const { rows } = await pool.query(sql, [String(dealId)]);
    
    // Group by contact_id and process each group
    const contactGroups = new Map<string, any[]>();
    
    for (const row of rows) {
      // All rows now have capital_position_match = 1 since they're pre-filtered
      if (!contactGroups.has(row.contact_id)) {
        contactGroups.set(row.contact_id, []);
      }
      contactGroups.get(row.contact_id)!.push(row);
    }
    
    // Process each contact with all its matching criteria
    const matches = Array.from(contactGroups.entries()).map(([contactId, criteriaMatches]) => {
      const firstRow = criteriaMatches[0]; // Get contact info from first row
      const allCriteriaScores: any[] = [];
      let bestOverallScore = 0;
      let bestCriteria: any = null;
      let combinedBreakdown: any[] = [];
      const allReasons: string[] = [];

      // Process each criteria match for this contact
      criteriaMatches.forEach((row) => {
        let totalWeightedScore = 0;
        const breakdown: any[] = [];

        // Location scoring - check combined location weight first, then individual geographic fields
        const locationWeight = fieldWeights.location || 0;
        if (locationWeight > 0) {
          // Use combined location approach with hierarchical weighting
          const regionScore = calculateArrayMatchScore(row.deal_region, row.contact_region, "region");
          const stateScore = calculateArrayMatchScore(row.deal_state, row.contact_state, "state");
          const cityScore = calculateArrayMatchScore(row.deal_city, row.contact_city, "city");
          
          // Use best location match (city > state > region hierarchy)
          let bestLocationScore = regionScore.score * 0.5;
          let locationReason = regionScore.reason;
          
          if (stateScore.score > regionScore.score) {
            bestLocationScore = Math.max(bestLocationScore, stateScore.score * 0.7);
            locationReason = stateScore.reason;
          }
          
          if (cityScore.score > 0) {
            bestLocationScore = Math.max(bestLocationScore, cityScore.score);
            locationReason = cityScore.reason;
          }
          
          breakdown.push({
            field: "location",
            score: Math.round(bestLocationScore * 100),
            weight: locationWeight,
            reason: locationReason,
            confidence: Math.max(regionScore.confidence || 0.8, stateScore.confidence || 0.8, cityScore.confidence || 0.8)
          });
          
          totalWeightedScore += bestLocationScore * locationWeight;
        } else {
          // Use individual geographic field weights when location weight is 0
          
          // Region scoring
          const regionWeight = fieldWeights.region || 0;
          if (regionWeight > 0) {
            const regionResult = calculateArrayMatchScore(row.deal_region, row.contact_region, "region");
            
            breakdown.push({
              field: "region",
              score: Math.round(regionResult.score * 100),
              weight: regionWeight,
              reason: regionResult.reason,
              confidence: regionResult.confidence || 0.8
            });
            
            totalWeightedScore += regionResult.score * regionWeight;
          }
          
          // State scoring
          const stateWeight = fieldWeights.state || 0;
          if (stateWeight > 0) {
            const stateResult = calculateArrayMatchScore(row.deal_state, row.contact_state, "state");
            
            breakdown.push({
              field: "state",
              score: Math.round(stateResult.score * 100),
              weight: stateWeight,
              reason: stateResult.reason,
              confidence: stateResult.confidence || 0.85
            });
            
            totalWeightedScore += stateResult.score * stateWeight;
          }
          
          // City scoring
          const cityWeight = fieldWeights.city || 0;
          if (cityWeight > 0) {
            const cityResult = calculateArrayMatchScore(row.deal_city, row.contact_city, "city");
            
            breakdown.push({
              field: "city",
              score: Math.round(cityResult.score * 100),
              weight: cityWeight,
              reason: cityResult.reason,
              confidence: cityResult.confidence || 0.9
            });
            
            totalWeightedScore += cityResult.score * cityWeight;
          }
          
          // Country scoring
          const countryWeight = fieldWeights.country || 0;
          if (countryWeight > 0) {
            const countryResult = calculateArrayMatchScore(row.deal_country, row.contact_country, "country");
            
            breakdown.push({
              field: "country",
              score: Math.round(countryResult.score * 100),
              weight: countryWeight,
              reason: countryResult.reason,
              confidence: countryResult.confidence || 0.8
            });
            
            totalWeightedScore += countryResult.score * countryWeight;
          }
        }

        // Deal size scoring
        const dealSizeWeight = fieldWeights.deal_size || 0;
        if (dealSizeWeight > 0) {
          // Convert string values to numbers (database stores in millions, matching logic expects millions)
          const dealMin = row.deal_min_deal_size ? Number(row.deal_min_deal_size) : null;
          const dealMax = row.deal_max_deal_size ? Number(row.deal_max_deal_size) : null;
          const contactMin = row.minimum_deal_size ? Number(row.minimum_deal_size) : null;
          const contactMax = row.maximum_deal_size ? Number(row.maximum_deal_size) : null;
          
          const dealSizeResult = calculateDealSizeScore(
            dealMin,
            dealMax,
            contactMin,
            contactMax
          );
          
          // Debug logging for deal size issues
          if (dealSizeResult.score === 0 && (dealMin || dealMax) && (contactMin || contactMax)) {
            // console.log(`Deal size no match - Deal: ${dealMin}-${dealMax}, Contact: ${contactMin}-${contactMax}, Reason: ${dealSizeResult.reason}`);
          }
          
          breakdown.push({
            field: "deal_size",
            score: Math.round(dealSizeResult.score * 100),
            weight: dealSizeWeight,
            reason: dealSizeResult.reason,
            confidence: dealSizeResult.confidence || 0.9
          });
          
          totalWeightedScore += dealSizeResult.score * dealSizeWeight;
        }

        // Property types scoring
        const propertyTypesWeight = fieldWeights.property_types || 0;
        if (propertyTypesWeight > 0) {
          const propertyTypesResult = calculateArrayMatchScore(
            row.deal_property_types,
            row.contact_property_types,
            "property types"
          );
          
          breakdown.push({
            field: "property_types",
            score: Math.round(propertyTypesResult.score * 100),
            weight: propertyTypesWeight,
            reason: propertyTypesResult.reason,
            confidence: propertyTypesResult.confidence || 0.85
          });
          
          totalWeightedScore += propertyTypesResult.score * propertyTypesWeight;
        }

        // Strategies scoring
        const strategiesWeight = fieldWeights.strategies || 0;
        if (strategiesWeight > 0) {
          const strategiesResult = calculateArrayMatchScore(
            row.deal_strategies,
            row.contact_strategies,
            "strategies"
          );
          
          breakdown.push({
            field: "strategies",
            score: Math.round(strategiesResult.score * 100),
            weight: strategiesWeight,
            reason: strategiesResult.reason,
            confidence: strategiesResult.confidence || 0.85
          });
          
          totalWeightedScore += strategiesResult.score * strategiesWeight;
        }

        // Financial products scoring
        const financialProductsWeight = fieldWeights.financial_products || 0;
        if (financialProductsWeight > 0) {
          const financialProductsResult = calculateArrayMatchScore(
            row.deal_financial_products,
            row.contact_financial_products,
            "financial products"
          );
          
          breakdown.push({
            field: "financial_products",
            score: Math.round(financialProductsResult.score * 100),
            weight: financialProductsWeight,
            reason: financialProductsResult.reason,
            confidence: financialProductsResult.confidence || 0.85
          });
          
          totalWeightedScore += financialProductsResult.score * financialProductsWeight;
        }

        // Loan to Value scoring
        const ltvWeight = fieldWeights.loan_to_value || 0;
        if (ltvWeight > 0) {
          const ltvResult = calculateRangeMatchScore(
            row.deal_loan_to_value_min,
            row.deal_loan_to_value_max,
            row.loan_to_value_min,
            row.loan_to_value_max,
            "LTV",
            true
          );
          
          breakdown.push({
            field: "loan_to_value",
            score: Math.round(ltvResult.score * 100),
            weight: ltvWeight,
            reason: ltvResult.reason,
            confidence: ltvResult.confidence || 0.9
          });
          
          totalWeightedScore += ltvResult.score * ltvWeight;
        }

        // Interest rate scoring
        const interestRateWeight = fieldWeights.interest_rate || 0;
        if (interestRateWeight > 0 && row.deal_interest_rate && row.interest_rate) {
          const rateMatch = row.deal_interest_rate >= row.interest_rate;
          const rateScore = rateMatch ? 1 : 0;
          const rateDiff = Math.abs(row.deal_interest_rate - row.interest_rate);
          
          breakdown.push({
            field: "interest_rate",
            score: Math.round(rateScore * 100),
            weight: interestRateWeight,
            reason: rateMatch 
              ? `Interest rate match: ${(row.deal_interest_rate * 100).toFixed(1)}% meets requirement`
              : `Interest rate gap: ${(rateDiff * 100).toFixed(1)}% difference`,
            confidence: 0.95
          });
          
          totalWeightedScore += rateScore * interestRateWeight;
        }

        // Loan to Cost scoring
        const ltcWeight = fieldWeights.loan_to_cost || 0;
        if (ltcWeight > 0) {
          const ltcResult = calculateRangeMatchScore(
            row.deal_loan_to_cost_min,
            row.deal_loan_to_cost_max,
            row.loan_to_cost_min,
            row.loan_to_cost_max,
            "LTC",
            true
          );
          
          breakdown.push({
            field: "loan_to_cost",
            score: Math.round(ltcResult.score * 100),
            weight: ltcWeight,
            reason: ltcResult.reason,
            confidence: ltcResult.confidence || 0.9
          });
          
          totalWeightedScore += ltcResult.score * ltcWeight;
        }

        // Hold period scoring
        const holdPeriodWeight = fieldWeights.hold_period || 0;
        if (holdPeriodWeight > 0) {
          const holdPeriodResult = calculateRangeMatchScore(
            row.deal_min_hold_period,
            row.deal_max_hold_period,
            row.min_hold_period,
            row.max_hold_period,
            "hold period"
          );
          
          breakdown.push({
            field: "hold_period",
            score: Math.round(holdPeriodResult.score * 100),
            weight: holdPeriodWeight,
            reason: holdPeriodResult.reason,
            confidence: holdPeriodResult.confidence || 0.9
          });
          
          totalWeightedScore += holdPeriodResult.score * holdPeriodWeight;
        }

        // Loan term scoring
        const loanTermWeight = fieldWeights.loan_term || 0;
        if (loanTermWeight > 0) {
          const loanTermResult = calculateRangeMatchScore(
            row.deal_min_loan_term,
            row.deal_max_loan_term,
            row.min_loan_term,
            row.max_loan_term,
            "loan term"
          );
          
          breakdown.push({
            field: "loan_term",
            score: Math.round(loanTermResult.score * 100),
            weight: loanTermWeight,
            reason: loanTermResult.reason,
            confidence: loanTermResult.confidence || 0.9
          });
          
          totalWeightedScore += loanTermResult.score * loanTermWeight;
        }

        // DSCR scoring
        const dscrWeight = fieldWeights.loan_dscr || 0;
        if (dscrWeight > 0) {
          const dscrResult = calculateRangeMatchScore(
            row.deal_min_loan_dscr,
            row.deal_max_loan_dscr,
            row.min_loan_dscr,
            row.max_loan_dscr,
            "DSCR"
          );
          
          breakdown.push({
            field: "loan_dscr",
            score: Math.round(dscrResult.score * 100),
            weight: dscrWeight,
            reason: dscrResult.reason,
            confidence: dscrResult.confidence || 0.9
          });
          
          totalWeightedScore += dscrResult.score * dscrWeight;
        }

        // Loan origination fee scoring
        const originationFeeWeight = fieldWeights.loan_origination_fee || 0;
        if (originationFeeWeight > 0) {
          const originationFeeResult = calculateRangeMatchScore(
            row.deal_loan_origination_fee_min,
            row.deal_loan_origination_fee_max,
            row.loan_origination_fee_min,
            row.loan_origination_fee_max,
            "origination fee",
            true
          );
          
          breakdown.push({
            field: "loan_origination_fee",
            score: Math.round(originationFeeResult.score * 100),
            weight: originationFeeWeight,
            reason: originationFeeResult.reason,
            confidence: originationFeeResult.confidence || 0.9
          });
          
          totalWeightedScore += originationFeeResult.score * originationFeeWeight;
        }

        // Loan exit fee scoring
        const exitFeeWeight = fieldWeights.loan_exit_fee || 0;
        if (exitFeeWeight > 0) {
          const exitFeeResult = calculateRangeMatchScore(
            row.deal_loan_exit_fee_min,
            row.deal_loan_exit_fee_max,
            row.loan_exit_fee_min,
            row.loan_exit_fee_max,
            "exit fee",
            true
          );
          
          breakdown.push({
            field: "loan_exit_fee",
            score: Math.round(exitFeeResult.score * 100),
            weight: exitFeeWeight,
            reason: exitFeeResult.reason,
            confidence: exitFeeResult.confidence || 0.9
          });
          
          totalWeightedScore += exitFeeResult.score * exitFeeWeight;
        }

        // Loan type scoring
        const loanTypeWeight = fieldWeights.loan_type || 0;
        if (loanTypeWeight > 0) {
          const loanTypeResult = calculateArrayMatchScore(
            row.deal_loan_type,
            row.contact_loan_type,
            "loan type"
          );
          
          breakdown.push({
            field: "loan_type",
            score: Math.round(loanTypeResult.score * 100),
            weight: loanTypeWeight,
            reason: loanTypeResult.reason,
            confidence: loanTypeResult.confidence || 0.85
          });
          
          totalWeightedScore += loanTypeResult.score * loanTypeWeight;
        }

        // Loan program scoring
        const loanProgramWeight = fieldWeights.loan_program || 0;
        if (loanProgramWeight > 0) {
          const loanProgramResult = calculateArrayMatchScore(
            row.deal_loan_program,
            row.contact_loan_program,
            "loan program"
          );
          
          breakdown.push({
            field: "loan_program",
            score: Math.round(loanProgramResult.score * 100),
            weight: loanProgramWeight,
            reason: loanProgramResult.reason,
            confidence: loanProgramResult.confidence || 0.85
          });
          
          totalWeightedScore += loanProgramResult.score * loanProgramWeight;
        }

        // Recourse loan scoring
        const recourseLoanWeight = fieldWeights.recourse_loan || 0;
        if (recourseLoanWeight > 0) {
          const recourseLoanResult = calculateArrayMatchScore(
            row.deal_recourse_loan,
            row.contact_recourse_loan,
            "recourse loan"
          );
          
          breakdown.push({
            field: "recourse_loan",
            score: Math.round(recourseLoanResult.score * 100),
            weight: recourseLoanWeight,
            reason: recourseLoanResult.reason,
            confidence: recourseLoanResult.confidence || 0.85
          });
          
          totalWeightedScore += recourseLoanResult.score * recourseLoanWeight;
        }

        // Structured loan tranche scoring
        const structuredLoanTrancheWeight = fieldWeights.structured_loan_tranche || 0;
        if (structuredLoanTrancheWeight > 0) {
          const structuredLoanTrancheResult = calculateArrayMatchScore(
            row.deal_structured_loan_tranche,
            row.contact_structured_loan_tranche,
            "structured loan tranche"
          );
          
          breakdown.push({
            field: "structured_loan_tranche",
            score: Math.round(structuredLoanTrancheResult.score * 100),
            weight: structuredLoanTrancheWeight,
            reason: structuredLoanTrancheResult.reason,
            confidence: structuredLoanTrancheResult.confidence || 0.85
          });
          
          totalWeightedScore += structuredLoanTrancheResult.score * structuredLoanTrancheWeight;
        }

        // Property sub categories scoring
        const propertySubCategoriesWeight = fieldWeights.property_sub_categories || 0;
        if (propertySubCategoriesWeight > 0) {
          const propertySubCategoriesResult = calculateArrayMatchScore(
            row.deal_property_sub_categories,
            row.contact_property_sub_categories,
            "property sub categories"
          );
          
          breakdown.push({
            field: "property_sub_categories",
            score: Math.round(propertySubCategoriesResult.score * 100),
            weight: propertySubCategoriesWeight,
            reason: propertySubCategoriesResult.reason,
            confidence: propertySubCategoriesResult.confidence || 0.85
          });
          
          totalWeightedScore += propertySubCategoriesResult.score * propertySubCategoriesWeight;
        }

        // Capital source scoring (text field)
        const capitalSourceWeight = fieldWeights.capital_source || 0;
        if (capitalSourceWeight > 0 && row.deal_capital_source && row.contact_capital_source) {
          const sourceMatch = row.deal_capital_source.toLowerCase() === row.contact_capital_source.toLowerCase();
          const sourceScore = sourceMatch ? 1 : 0;
          
          breakdown.push({
            field: "capital_source",
            score: Math.round(sourceScore * 100),
            weight: capitalSourceWeight,
            reason: sourceMatch 
              ? `Capital source match: ${row.deal_capital_source}`
              : `Capital source mismatch: ${row.deal_capital_source} vs ${row.contact_capital_source}`,
            confidence: 0.9
          });
          
          totalWeightedScore += sourceScore * capitalSourceWeight;
        }

        // Loan type normalized scoring
        const loanTypeNormalizedWeight = fieldWeights.loan_type_normalized || 0;
        if (loanTypeNormalizedWeight > 0) {
          const loanTypeNormalizedResult = calculateArrayMatchScore(
            row.deal_loan_type_normalized,
            row.contact_loan_type_normalized,
            "loan type normalized"
          );
          
          breakdown.push({
            field: "loan_type_normalized",
            score: Math.round(loanTypeNormalizedResult.score * 100),
            weight: loanTypeNormalizedWeight,
            reason: loanTypeNormalizedResult.reason,
            confidence: loanTypeNormalizedResult.confidence || 0.85
          });
          
          totalWeightedScore += loanTypeNormalizedResult.score * loanTypeNormalizedWeight;
        }

        // Location focus scoring
        const locationFocusWeight = fieldWeights.location_focus || 0;
        if (locationFocusWeight > 0) {
          const locationFocusResult = calculateArrayMatchScore(
            row.deal_location_focus,
            row.contact_location_focus,
            "location focus"
          );
          
          breakdown.push({
            field: "location_focus",
            score: Math.round(locationFocusResult.score * 100),
            weight: locationFocusWeight,
            reason: locationFocusResult.reason,
            confidence: locationFocusResult.confidence || 0.85
          });
          
          totalWeightedScore += locationFocusResult.score * locationFocusWeight;
        }

        // Additional interest rate fields scoring
        const additionalInterestRates = [
          { field: 'interest_rate_sofr', deal: row.deal_interest_rate_sofr, contact: row.contact_interest_rate_sofr },
          { field: 'interest_rate_wsj', deal: row.deal_interest_rate_wsj, contact: row.contact_interest_rate_wsj },
          { field: 'interest_rate_prime', deal: row.deal_interest_rate_prime, contact: row.contact_interest_rate_prime },
          { field: 'interest_rate_libor', deal: row.deal_interest_rate_libor, contact: row.contact_interest_rate_libor },
          { field: 'interest_rate_5yt', deal: row.deal_interest_rate_5yt, contact: row.contact_interest_rate_5yt },
          { field: 'interest_rate_10yt', deal: row.deal_interest_rate_10yt, contact: row.contact_interest_rate_10yt }
        ];

        additionalInterestRates.forEach(({ field, deal, contact }) => {
          const weight = fieldWeights[field] || 0;
          if (weight > 0 && deal && contact) {
            const rateMatch = deal >= contact;
            const rateScore = rateMatch ? 1 : 0;
            const rateDiff = Math.abs(deal - contact);
            
            breakdown.push({
              field: field,
              score: Math.round(rateScore * 100),
              weight: weight,
              reason: rateMatch 
                ? `${field.replace('_', ' ')} match: ${(deal * 100).toFixed(1)}% meets requirement`
                : `${field.replace('_', ' ')} gap: ${(rateDiff * 100).toFixed(1)}% difference`,
              confidence: 0.95
            });
            
            totalWeightedScore += rateScore * weight;
          }
        });

        // Closing time weeks scoring
        const closingTimeWeight = fieldWeights.closing_time_weeks || 0;
        if (closingTimeWeight > 0) {
          const closingTimeResult = calculateRangeMatchScore(
            row.deal_min_closing_time_weeks,
            row.deal_max_closing_time_weeks,
            row.contact_min_closing_time_weeks,
            row.contact_max_closing_time_weeks,
            "closing time weeks"
          );
          
          breakdown.push({
            field: "closing_time_weeks",
            score: Math.round(closingTimeResult.score * 100),
            weight: closingTimeWeight,
            reason: closingTimeResult.reason,
            confidence: closingTimeResult.confidence || 0.9
          });
          
          totalWeightedScore += closingTimeResult.score * closingTimeWeight;
        }

        // Target return scoring
        const targetReturnWeight = fieldWeights.target_return || 0;
        if (targetReturnWeight > 0 && row.contact_target_return) {
          const returnMatch = row.deal_target_return >= row.contact_target_return;
          const returnScore = returnMatch ? 1 : 0;
          const returnDiff = Math.abs(row.deal_target_return - row.contact_target_return);
          
          breakdown.push({
            field: "target_return",
            score: Math.round(returnScore * 100),
            weight: targetReturnWeight,
            reason: returnMatch 
              ? `Target return match: ${(row.deal_target_return * 100).toFixed(1)}% meets requirement`
              : `Target return gap: ${(returnDiff * 100).toFixed(1)}% difference`,
            confidence: 0.95
          });
          
          totalWeightedScore += returnScore * targetReturnWeight;
        }

        // Calculate criteria-specific score (direct calculation since weights sum to 1.0)
        const criteriaScore = Math.round(totalWeightedScore * 100);

        // Extract reasons for display
        const reasons = breakdown
          .filter(b => b.score > 0)
          .map(b => b.reason);

        const criteriaResult = {
          criteria_id: row.contact_criteria_id,
          criteria_source: row.criteria_source, // 'Contact' or 'Company'
          score: criteriaScore,
          breakdown,
          reasons
        };

        allCriteriaScores.push(criteriaResult);

        // Collect all reasons
        allReasons.push(...reasons);

        // Track the best scoring criteria for overall contact score
        if (criteriaScore > bestOverallScore) {
          bestOverallScore = criteriaScore;
          bestCriteria = criteriaResult;
          combinedBreakdown = breakdown; // Use best scoring criteria's breakdown for backward compatibility
        }
      });

      // Filter out criteria with 0 score
      const validCriteriaScores = allCriteriaScores.filter(c => c.score > 0);
      
      if (validCriteriaScores.length === 0) {
        return null; // No valid matches for this contact
      }

      // Prioritize contact criteria over company criteria
      const contactCriteriaScores = validCriteriaScores.filter(c => c.criteria_source === 'Contact');
      const companyCriteriaScores = validCriteriaScores.filter(c => c.criteria_source === 'Company');
      
      // Use contact criteria if available, otherwise fall back to company criteria
      const primaryCriteriaScores = contactCriteriaScores.length > 0 ? contactCriteriaScores : companyCriteriaScores;
      
      // Calculate average score across primary criteria
      const avgScore = Math.round(primaryCriteriaScores.reduce((sum, c) => sum + c.score, 0) / primaryCriteriaScores.length);

      // Remove duplicate reasons
      const uniqueReasons = [...new Set(allReasons)];

      // Determine the best scoring criteria (prioritize contact over company)
      let bestPrimaryCriteria = primaryCriteriaScores[0];
      for (const criteria of primaryCriteriaScores) {
        if (criteria.score > bestPrimaryCriteria.score) {
          bestPrimaryCriteria = criteria;
        }
      }

      return {
        contact_id: firstRow.contact_id,
        first_name: firstRow.first_name,
        last_name: firstRow.last_name,
        full_name: `${firstRow.first_name || ''} ${firstRow.last_name || ''}`.trim(),
        title: firstRow.job_title,
        email: firstRow.email,
        company_id: firstRow.company_id,
        phone_number: firstRow.phone_number,
        linkedin_url: firstRow.linkedin_url,
        contact_city: Array.isArray(firstRow.contact_city) ? firstRow.contact_city[0] : firstRow.contact_city,
        contact_state: Array.isArray(firstRow.contact_state) ? firstRow.contact_state[0] : firstRow.contact_state,
        contact_updated_at: firstRow.contact_updated_at,
        contact_created_at: firstRow.contact_created_at,
        company_name: firstRow.company_name,
        company_city: firstRow.company_city,
        company_state: firstRow.company_state,
        company_website: firstRow.company_website,
        industry: firstRow.industry,
        score: avgScore, // Average score across primary criteria
        best_score: bestPrimaryCriteria.score, // Highest scoring criteria
        criteria_source: bestPrimaryCriteria.criteria_source, // 'Contact' or 'Company'
        // Backward compatibility fields for UI
        breakdown: bestPrimaryCriteria.breakdown, // Use best scoring criteria's breakdown
        reasons: bestPrimaryCriteria.reasons || [], // Reasons from best scoring criteria
        scoring_method: "enhanced_normalized_weights_grouped_with_fallback",
        // New grouped data
        matching_criteria_count: validCriteriaScores.length,
        contact_criteria_count: contactCriteriaScores.length,
        company_criteria_count: companyCriteriaScores.length,
        all_criteria_matches: validCriteriaScores, // All matching criteria with their breakdowns
        primary_reasons: bestPrimaryCriteria.reasons || [], // Reasons from best scoring criteria
        all_reasons: uniqueReasons.slice(0, 10), // All unique reasons, limited to 10
        fallback_used: false, // No fallback logic, all matches are pre-filtered
      };
    })
    .filter(match => match !== null) // Remove contacts with no valid matches
    .filter(match => match.score >= minScoreThreshold) // Filter by minimum score threshold
    .sort((a, b) => {
      // Primary: best_score descending (highest percentage match first)
      if (b.best_score !== a.best_score) return b.best_score - a.best_score;
      // Secondary: average score descending
      if (b.score !== a.score) return b.score - a.score;
      // Tertiary: most recent contact (updated_at, fallback to created_at)
      const dateA = new Date(a.contact_updated_at || a.contact_created_at || 0).getTime();
      const dateB = new Date(b.contact_updated_at || b.contact_created_at || 0).getTime();
      return dateB - dateA;
    });

    const totalContactCriteria = matches.reduce((total, m) => total + m.contact_criteria_count, 0);
    const totalCompanyCriteria = matches.reduce((total, m) => total + m.company_criteria_count, 0);
    
    console.log(`Found ${matches.length} unique contacts with ${matches.reduce((total, m) => total + m.matching_criteria_count, 0)} total criteria matches`);
    console.log(`Contact criteria matches: ${totalContactCriteria}, Company criteria matches: ${totalCompanyCriteria}`);
    
    return NextResponse.json({ 
      matches,
      field_weights_used: fieldWeights,
      total_candidates: contactGroups.size,
      matches_found: matches.length,
      contact_criteria_matches: totalContactCriteria,
      company_criteria_matches: totalCompanyCriteria,
      fallback_used: false, // No fallback with pre-filtered approach
      crm_mode: isCrmMode,
      scoring_method: "enhanced_javascript_with_normalized_weights_pre_filtered",
      filtering: {
        min_score_threshold: minScoreThreshold,
        show_all_matches: showAllMatches,
        crm_mode: isCrmMode,
        total_before_filtering: Array.from(contactGroups.entries()).length,
        filtered_out_count: Array.from(contactGroups.entries()).length - matches.length
      }
    });
    
  } catch (error) {
    console.error("Error in contacts-for-deal matching:", error);
    return NextResponse.json(
      { error: "Failed to fetch contact matches" },
      { status: 500 }
    );
  }
} 