import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";
import {
  fetchAndNormalizeWeights,
  calculateArrayMatchScore,
  calculateDealSizeScore,
  calculateRangeMatchScore
} from "../../_lib/matching-utils";

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ contactId: string }> }
) {
  try {
    const { contactId } = await context.params;
    
    // Check for add_ic_data parameter
    const url = new URL(req.url);
    const addIcData = url.searchParams.get('add_ic_data') === 'true';
    
    // Check for CRM mode parameter
    const isCrmMode = url.searchParams.get('crm_mode') === 'true';
    
    // Parse filtering parameters
    const showAllMatches = url.searchParams.get('show_all') === 'true';
    const minScoreThreshold = showAllMatches ? 0 : 50; // Default to 50% if not showing all
    
    // Fetch and normalize field weights from database
    const fieldWeights = await fetchAndNormalizeWeights();
    
    // console.log(`Fetching matching deals for contact ${contactId} using enhanced JavaScript scoring`);
    // console.log("Using normalized field weights:", fieldWeights);

    // Enhanced SQL to include both contact and company investment criteria with fallback logic
    const sql = `
      WITH contact_data AS (
        SELECT 
          contact_id,
          company_id,
          first_name,
          last_name,
          email,
          title AS job_title,
          phone_number,
          linkedin_url,
          updated_at AS contact_updated_at,
          created_at AS contact_created_at
        FROM contacts 
        WHERE contact_id = $1
      ),
      contact_criteria AS (
        SELECT 
          ic.*,
          'Contact' as criteria_source
        FROM investment_criteria ic
        WHERE ic.entity_type = 'Contact' 
          AND ic.entity_id = $1::text 
          AND ic.is_active = true
      ),
      company_criteria AS (
        SELECT 
          ic.*,
          'Company' as criteria_source
        FROM investment_criteria ic
        INNER JOIN contact_data cd ON ic.entity_id = cd.company_id::text
        WHERE ic.entity_type LIKE 'Company%' 
          AND ic.is_active = true
      ),
      all_criteria AS (
        SELECT * FROM contact_criteria
        UNION ALL
        SELECT * FROM company_criteria
      )
      SELECT
        d.entity_id AS deal_id,
        d.criteria_id AS deal_criteria_id,
        deals.deal_name,
        d.minimum_deal_size,
        d.maximum_deal_size,
        d.historical_irr,
        d.historical_em,
        d.region AS deal_region,
        d.state AS deal_state,
        d.city AS deal_city,
        d.country AS deal_country,
        d.capital_position AS deal_capital_position,
        d.property_types AS deal_property_types,
        d.strategies AS deal_strategies,
        d.financial_products AS deal_financial_products,
        d.loan_type AS deal_loan_type,
        d.loan_program AS deal_loan_program,
        d.recourse_loan AS deal_recourse_loan,
        d.structured_loan_tranche AS deal_structured_loan_tranche,
        d.min_hold_period,
        d.max_hold_period,
        d.min_loan_term,
        d.max_loan_term,
        d.interest_rate,
        d.loan_to_value_min,
        d.loan_to_value_max,
        d.loan_to_cost_min,
        d.loan_to_cost_max,
        d.loan_origination_fee_min,
        d.loan_origination_fee_max,
        d.loan_exit_fee_min,
        d.loan_exit_fee_max,
        d.min_loan_dscr,
        d.max_loan_dscr,
        d.property_sub_categories AS deal_property_sub_categories,
        d.capital_source AS deal_capital_source,
        d.loan_type_normalized AS deal_loan_type_normalized,
        d.interest_rate_sofr AS deal_interest_rate_sofr,
        d.interest_rate_wsj AS deal_interest_rate_wsj,
        d.interest_rate_prime AS deal_interest_rate_prime,
        d.interest_rate_libor AS deal_interest_rate_libor,
        d.interest_rate_5yt AS deal_interest_rate_5yt,
        d.interest_rate_10yt AS deal_interest_rate_10yt,
        d.loan_term_string AS deal_loan_term_string,
        d.location_focus AS deal_location_focus,
        d.min_closing_time_weeks AS deal_min_closing_time_weeks,
        d.max_closing_time_weeks AS deal_max_closing_time_weeks,
        d.closing_time_weeks AS deal_closing_time_weeks,
        -- Contact/Company criteria for comparison
        c.criteria_id AS contact_criteria_id,
        c.criteria_source,
        c.target_return AS contact_target_return,
        c.region AS contact_region,
        c.state AS contact_state,
        c.city AS contact_city,
        c.country AS contact_country,
        c.capital_position AS contact_capital_position,
        c.property_types AS contact_property_types,
        c.property_sub_categories AS contact_property_sub_categories,
        c.strategies AS contact_strategies,
        c.financial_products AS contact_financial_products,
        c.loan_type AS contact_loan_type,
        c.loan_program AS contact_loan_program,
        c.recourse_loan AS contact_recourse_loan,
        c.structured_loan_tranche AS contact_structured_loan_tranche,
        c.minimum_deal_size AS contact_min_deal_size,
        c.maximum_deal_size AS contact_max_deal_size,
        c.min_hold_period AS contact_min_hold_period,
        c.max_hold_period AS contact_max_hold_period,
        c.min_loan_term AS contact_min_loan_term,
        c.max_loan_term AS contact_max_loan_term,
        c.interest_rate AS contact_interest_rate,
        c.loan_to_value_min AS contact_loan_to_value_min,
        c.loan_to_value_max AS contact_loan_to_value_max,
        c.loan_to_cost_min AS contact_loan_to_cost_min,
        c.loan_to_cost_max AS contact_loan_to_cost_max,
        c.loan_origination_fee_min AS contact_loan_origination_fee_min,
        c.loan_origination_fee_max AS contact_loan_origination_fee_max,
        c.loan_exit_fee_min AS contact_loan_exit_fee_min,
        c.loan_exit_fee_max AS contact_loan_exit_fee_max,
        c.min_loan_dscr AS contact_min_loan_dscr,
        c.max_loan_dscr AS contact_max_loan_dscr,
        c.historical_irr AS contact_historical_irr,
        c.historical_em AS contact_historical_em,
        c.property_sub_categories AS contact_property_sub_categories,
        c.capital_source AS contact_capital_source,
        c.loan_type_normalized AS contact_loan_type_normalized,
        c.interest_rate_sofr AS contact_interest_rate_sofr,
        c.interest_rate_wsj AS contact_interest_rate_wsj,
        c.interest_rate_prime AS contact_interest_rate_prime,
        c.interest_rate_libor AS contact_interest_rate_libor,
        c.interest_rate_5yt AS contact_interest_rate_5yt,
        c.interest_rate_10yt AS contact_interest_rate_10yt,
        c.loan_term_string AS contact_loan_term_string,
        c.location_focus AS contact_location_focus,
        c.min_closing_time_weeks AS contact_min_closing_time_weeks,
        c.max_closing_time_weeks AS contact_max_closing_time_weeks,
        c.closing_time_weeks AS contact_closing_time_weeks,
        -- Contact info
        cd.first_name,
        cd.last_name,
        cd.email,
        cd.company_id,
        cd.job_title,
        cd.phone_number,
        cd.linkedin_url,
        cd.contact_updated_at,
        cd.contact_created_at,
        comp.company_name,
        comp.company_city,
        comp.company_state,
        comp.company_website,
        comp.industry,
        -- Capital Position match is guaranteed by JOIN condition
        1 AS capital_position_match,
        d.criteria_id AS deal_criteria_id
      FROM investment_criteria d
      INNER JOIN all_criteria c ON (d.capital_position && c.capital_position${isCrmMode ? ' AND d.is_requested = true' : ''})
      INNER JOIN deals ON d.entity_id = deals.deal_id::text
      INNER JOIN contact_data cd ON cd.contact_id = $1
      LEFT JOIN companies comp ON cd.company_id = comp.company_id
      WHERE d.entity_type = 'Deal' AND d.is_active = true
      ${isCrmMode ? `
        -- CRM Mode Filters
        AND deals.is_internal_only = false
        AND deals.deal_date >= NOW() - INTERVAL '1 year'
      ` : ''}
      ORDER BY c.criteria_source DESC, d.entity_id, c.criteria_id
    
    `;
    
    const { rows } = await pool.query(sql, [String(contactId)]);
    
    // Group by deal_id and process each group with fallback logic
    const dealGroups = new Map<string, any[]>();
    
    for (const row of rows) {
      // All rows now have capital position matches due to JOIN condition
      if (!dealGroups.has(row.deal_id)) {
        dealGroups.set(row.deal_id, []);
      }
      dealGroups.get(row.deal_id)!.push(row);
    }
    
    // Check if we have enough contact-based matches, if not, include company-based matches
    const contactBasedMatches = Array.from(dealGroups.entries()).filter(([dealId, criteriaMatches]) => 
      criteriaMatches.some(row => row.criteria_source === 'Contact')
    );
    
    // If we have fewer than 10 contact-based matches, include company-based matches as fallback
    if (contactBasedMatches.length < 10) {
      console.log(`Only ${contactBasedMatches.length} contact-based matches found, including company-based matches as fallback`);
      
      // Re-process to include company criteria matches
      dealGroups.clear();
      for (const row of rows) {
        if (row.capital_position_match === 1) {
          if (!dealGroups.has(row.deal_id)) {
            dealGroups.set(row.deal_id, []);
          }
          dealGroups.get(row.deal_id)!.push(row);
        }
      }
    }
    
    // Process each deal with all its matching criteria
    const matches = Array.from(dealGroups.entries()).map(([dealId, criteriaMatches]) => {
      const firstRow = criteriaMatches[0]; // Get deal info from first row
      const allCriteriaScores: any[] = [];
      let bestOverallScore = 0;
      let bestCriteria: any = null;
      let combinedBreakdown: any[] = [];
      const allReasons: string[] = [];

      // Process each criteria match for this deal
      criteriaMatches.forEach((row) => {
        let totalWeightedScore = 0;
        const breakdown: any[] = [];

        // Location scoring - check combined location weight first, then individual geographic fields
        const locationWeight = fieldWeights.location || 0;
        if (locationWeight > 0) {
          // Use combined location approach with hierarchical weighting
          const regionScore = calculateArrayMatchScore(row.deal_region, row.contact_region, "region");
          const stateScore = calculateArrayMatchScore(row.deal_state, row.contact_state, "state");
          const cityScore = calculateArrayMatchScore(row.deal_city, row.contact_city, "city");
          
          // Use best location match (city > state > region hierarchy)
          let bestLocationScore = regionScore.score * 0.5;
          let locationReason = regionScore.reason;
          
          if (stateScore.score > regionScore.score) {
            bestLocationScore = Math.max(bestLocationScore, stateScore.score * 0.7);
            locationReason = stateScore.reason;
          }
          
          if (cityScore.score > 0) {
            bestLocationScore = Math.max(bestLocationScore, cityScore.score);
            locationReason = cityScore.reason;
          }
          
          breakdown.push({
            field: "location",
            score: Math.round(bestLocationScore * 100),
            weight: locationWeight,
            reason: locationReason,
            confidence: Math.max(regionScore.confidence || 0.8, stateScore.confidence || 0.8, cityScore.confidence || 0.8)
          });
          
          totalWeightedScore += bestLocationScore * locationWeight;
        } else {
          // Use individual geographic field weights when location weight is 0
          
          // Region scoring
          const regionWeight = fieldWeights.region || 0;
          if (regionWeight > 0) {
            const regionResult = calculateArrayMatchScore(row.deal_region, row.contact_region, "region");
            
            breakdown.push({
              field: "region",
              score: Math.round(regionResult.score * 100),
              weight: regionWeight,
              reason: regionResult.reason,
              confidence: regionResult.confidence || 0.8
            });
            
            totalWeightedScore += regionResult.score * regionWeight;
          }
          
          // State scoring
          const stateWeight = fieldWeights.state || 0;
          if (stateWeight > 0) {
            const stateResult = calculateArrayMatchScore(row.deal_state, row.contact_state, "state");
            
            breakdown.push({
              field: "state",
              score: Math.round(stateResult.score * 100),
              weight: stateWeight,
              reason: stateResult.reason,
              confidence: stateResult.confidence || 0.85
            });
            
            totalWeightedScore += stateResult.score * stateWeight;
          }
          
          // City scoring
          const cityWeight = fieldWeights.city || 0;
          if (cityWeight > 0) {
            const cityResult = calculateArrayMatchScore(row.deal_city, row.contact_city, "city");
            
            breakdown.push({
              field: "city",
              score: Math.round(cityResult.score * 100),
              weight: cityWeight,
              reason: cityResult.reason,
              confidence: cityResult.confidence || 0.9
            });
            
            totalWeightedScore += cityResult.score * cityWeight;
          }
          
          // Country scoring
          const countryWeight = fieldWeights.country || 0;
          if (countryWeight > 0) {
            const countryResult = calculateArrayMatchScore(row.deal_country, row.contact_country, "country");
            
            breakdown.push({
              field: "country",
              score: Math.round(countryResult.score * 100),
              weight: countryWeight,
              reason: countryResult.reason,
              confidence: countryResult.confidence || 0.8
            });
            
            totalWeightedScore += countryResult.score * countryWeight;
          }
        }

        // Deal size scoring
        const dealSizeWeight = fieldWeights.deal_size || 0;
        if (dealSizeWeight > 0) {
          // Convert string values to numbers (database stores in millions, matching logic expects millions)
          const dealMin = row.minimum_deal_size ? Number(row.minimum_deal_size) : null;
          const dealMax = row.maximum_deal_size ? Number(row.maximum_deal_size) : null;
          const contactMin = row.contact_min_deal_size ? Number(row.contact_min_deal_size) : null;
          const contactMax = row.contact_max_deal_size ? Number(row.contact_max_deal_size) : null;
          
          // Enhanced debug logging for deal "88-11 179th Place" and contact 211724
          if (row.deal_name && row.deal_name.includes("179th")) {
            console.log(`🔍 DEBUG Deal Size for "${row.deal_name}":`, {
              deal_id: row.deal_id,
              contact_criteria_id: row.contact_criteria_id,
              raw_data: {
                minimum_deal_size: row.minimum_deal_size,
                maximum_deal_size: row.maximum_deal_size,
                contact_min_deal_size: row.contact_min_deal_size,
                contact_max_deal_size: row.contact_max_deal_size
              },
              converted_values: {
                dealMin,
                dealMax,
                contactMin,
                contactMax
              },
              capital_position_match: row.capital_position_match
            });
          }
          
          const dealSizeResult = calculateDealSizeScore(
            dealMin,
            dealMax,
            contactMin,
            contactMax
          );
          
          // Enhanced debug logging
          if (row.deal_name && row.deal_name.includes("179th")) {
            console.log(`🔍 DEBUG Deal Size Result for "${row.deal_name}":`, {
              score: dealSizeResult.score,
              reason: dealSizeResult.reason,
              confidence: dealSizeResult.confidence
            });
          }
          
          // Debug logging for deal size issues
          if (dealSizeResult.score === 0 && (dealMin || dealMax) && (contactMin || contactMax)) {
            // console.log(`Deal size no match - Deal: ${dealMin}-${dealMax}, Contact: ${contactMin}-${contactMax}, Reason: ${dealSizeResult.reason}`);
          }
          
          breakdown.push({
            field: "deal_size",
            score: Math.round(dealSizeResult.score * 100),
            weight: dealSizeWeight,
            reason: dealSizeResult.reason,
            confidence: dealSizeResult.confidence || 0.9
          });
          
          totalWeightedScore += dealSizeResult.score * dealSizeWeight;
        }

        // Property types scoring
        const propertyTypesWeight = fieldWeights.property_types || 0;
        if (propertyTypesWeight > 0) {
          const propertyTypesResult = calculateArrayMatchScore(
            row.deal_property_types,
            row.contact_property_types,
            "property types"
          );
          
          breakdown.push({
            field: "property_types",
            score: Math.round(propertyTypesResult.score * 100),
            weight: propertyTypesWeight,
            reason: propertyTypesResult.reason,
            confidence: propertyTypesResult.confidence || 0.85
          });
          
          totalWeightedScore += propertyTypesResult.score * propertyTypesWeight;
        }

        // Strategies scoring
        const strategiesWeight = fieldWeights.strategies || 0;
        if (strategiesWeight > 0) {
          const strategiesResult = calculateArrayMatchScore(
            row.deal_strategies,
            row.contact_strategies,
            "strategies"
          );
          
          breakdown.push({
            field: "strategies",
            score: Math.round(strategiesResult.score * 100),
            weight: strategiesWeight,
            reason: strategiesResult.reason,
            confidence: strategiesResult.confidence || 0.85
          });
          
          totalWeightedScore += strategiesResult.score * strategiesWeight;
        }

        // Financial products scoring
        const financialProductsWeight = fieldWeights.financial_products || 0;
        if (financialProductsWeight > 0) {
          const financialProductsResult = calculateArrayMatchScore(
            row.deal_financial_products,
            row.contact_financial_products,
            "financial products"
          );
          
          breakdown.push({
            field: "financial_products",
            score: Math.round(financialProductsResult.score * 100),
            weight: financialProductsWeight,
            reason: financialProductsResult.reason,
            confidence: financialProductsResult.confidence || 0.85
          });
          
          totalWeightedScore += financialProductsResult.score * financialProductsWeight;
        }

        // Loan to Value scoring
        const ltvWeight = fieldWeights.loan_to_value || 0;
        if (ltvWeight > 0) {
          const ltvResult = calculateRangeMatchScore(
            row.loan_to_value_min,
            row.loan_to_value_max,
            row.contact_loan_to_value_min,
            row.contact_loan_to_value_max,
            "LTV",
            true
          );
          
          breakdown.push({
            field: "loan_to_value",
            score: Math.round(ltvResult.score * 100),
            weight: ltvWeight,
            reason: ltvResult.reason,
            confidence: ltvResult.confidence || 0.9
          });
          
          totalWeightedScore += ltvResult.score * ltvWeight;
        }

        // Interest rate scoring
        const interestRateWeight = fieldWeights.interest_rate || 0;
        if (interestRateWeight > 0 && row.interest_rate && row.contact_interest_rate) {
          const rateMatch = row.interest_rate >= row.contact_interest_rate;
          const rateScore = rateMatch ? 1 : 0;
          const rateDiff = Math.abs(row.interest_rate - row.contact_interest_rate);
          
          breakdown.push({
            field: "interest_rate",
            score: Math.round(rateScore * 100),
            weight: interestRateWeight,
            reason: rateMatch 
              ? `Interest rate match: ${(row.interest_rate * 100).toFixed(1)}% meets requirement`
              : `Interest rate gap: ${(rateDiff * 100).toFixed(1)}% difference`,
            confidence: 0.95
          });
          
          totalWeightedScore += rateScore * interestRateWeight;
        }

        // Loan to Cost scoring
        const ltcWeight = fieldWeights.loan_to_cost || 0;
        if (ltcWeight > 0) {
          const ltcResult = calculateRangeMatchScore(
            row.loan_to_cost_min,
            row.loan_to_cost_max,
            row.contact_loan_to_cost_min,
            row.contact_loan_to_cost_max,
            "LTC",
            true
          );
          
          breakdown.push({
            field: "loan_to_cost",
            score: Math.round(ltcResult.score * 100),
            weight: ltcWeight,
            reason: ltcResult.reason,
            confidence: ltcResult.confidence || 0.9
          });
          
          totalWeightedScore += ltcResult.score * ltcWeight;
        }

        // Hold period scoring
        const holdPeriodWeight = fieldWeights.hold_period || 0;
        if (holdPeriodWeight > 0) {
          const holdPeriodResult = calculateRangeMatchScore(
            row.min_hold_period,
            row.max_hold_period,
            row.contact_min_hold_period,
            row.contact_max_hold_period,
            "hold period"
          );
          
          breakdown.push({
            field: "hold_period",
            score: Math.round(holdPeriodResult.score * 100),
            weight: holdPeriodWeight,
            reason: holdPeriodResult.reason,
            confidence: holdPeriodResult.confidence || 0.9
          });
          
          totalWeightedScore += holdPeriodResult.score * holdPeriodWeight;
        }

        // Loan term scoring
        const loanTermWeight = fieldWeights.loan_term || 0;
        if (loanTermWeight > 0) {
          const loanTermResult = calculateRangeMatchScore(
            row.min_loan_term,
            row.max_loan_term,
            row.contact_min_loan_term,
            row.contact_max_loan_term,
            "loan term"
          );
          
          breakdown.push({
            field: "loan_term",
            score: Math.round(loanTermResult.score * 100),
            weight: loanTermWeight,
            reason: loanTermResult.reason,
            confidence: loanTermResult.confidence || 0.9
          });
          
          totalWeightedScore += loanTermResult.score * loanTermWeight;
        }

        // DSCR scoring
        const dscrWeight = fieldWeights.loan_dscr || 0;
        if (dscrWeight > 0) {
          const dscrResult = calculateRangeMatchScore(
            row.min_loan_dscr,
            row.max_loan_dscr,
            row.contact_min_loan_dscr,
            row.contact_max_loan_dscr,
            "DSCR"
          );
          
          breakdown.push({
            field: "loan_dscr",
            score: Math.round(dscrResult.score * 100),
            weight: dscrWeight,
            reason: dscrResult.reason,
            confidence: dscrResult.confidence || 0.9
          });
          
          totalWeightedScore += dscrResult.score * dscrWeight;
        }

        // Loan origination fee scoring
        const originationFeeWeight = fieldWeights.loan_origination_fee || 0;
        if (originationFeeWeight > 0) {
          const originationFeeResult = calculateRangeMatchScore(
            row.loan_origination_fee_min,
            row.loan_origination_fee_max,
            row.contact_loan_origination_fee_min,
            row.contact_loan_origination_fee_max,
            "origination fee",
            true
          );
          
          breakdown.push({
            field: "loan_origination_fee",
            score: Math.round(originationFeeResult.score * 100),
            weight: originationFeeWeight,
            reason: originationFeeResult.reason,
            confidence: originationFeeResult.confidence || 0.9
          });
          
          totalWeightedScore += originationFeeResult.score * originationFeeWeight;
        }

        // Loan exit fee scoring
        const exitFeeWeight = fieldWeights.loan_exit_fee || 0;
        if (exitFeeWeight > 0) {
          const exitFeeResult = calculateRangeMatchScore(
            row.loan_exit_fee_min,
            row.loan_exit_fee_max,
            row.contact_loan_exit_fee_min,
            row.contact_loan_exit_fee_max,
            "exit fee",
            true
          );
          
          breakdown.push({
            field: "loan_exit_fee",
            score: Math.round(exitFeeResult.score * 100),
            weight: exitFeeWeight,
            reason: exitFeeResult.reason,
            confidence: exitFeeResult.confidence || 0.9
          });
          
          totalWeightedScore += exitFeeResult.score * exitFeeWeight;
        }

        // Loan type scoring
        const loanTypeWeight = fieldWeights.loan_type || 0;
        if (loanTypeWeight > 0) {
          const loanTypeResult = calculateArrayMatchScore(
            row.deal_loan_type,
            row.contact_loan_type,
            "loan type"
          );
          
          breakdown.push({
            field: "loan_type",
            score: Math.round(loanTypeResult.score * 100),
            weight: loanTypeWeight,
            reason: loanTypeResult.reason,
            confidence: loanTypeResult.confidence || 0.85
          });
          
          totalWeightedScore += loanTypeResult.score * loanTypeWeight;
        }

        // Loan program scoring
        const loanProgramWeight = fieldWeights.loan_program || 0;
        if (loanProgramWeight > 0) {
          const loanProgramResult = calculateArrayMatchScore(
            row.deal_loan_program,
            row.contact_loan_program,
            "loan program"
          );
          
          breakdown.push({
            field: "loan_program",
            score: Math.round(loanProgramResult.score * 100),
            weight: loanProgramWeight,
            reason: loanProgramResult.reason,
            confidence: loanProgramResult.confidence || 0.85
          });
          
          totalWeightedScore += loanProgramResult.score * loanProgramWeight;
        }

        // Recourse loan scoring
        const recourseLoanWeight = fieldWeights.recourse_loan || 0;
        if (recourseLoanWeight > 0) {
          const recourseLoanResult = calculateArrayMatchScore(
            row.deal_recourse_loan,
            row.contact_recourse_loan,
            "recourse loan"
          );
          
          breakdown.push({
            field: "recourse_loan",
            score: Math.round(recourseLoanResult.score * 100),
            weight: recourseLoanWeight,
            reason: recourseLoanResult.reason,
            confidence: recourseLoanResult.confidence || 0.85
          });
          
          totalWeightedScore += recourseLoanResult.score * recourseLoanWeight;
        }

        // Structured loan tranche scoring
        const structuredLoanTrancheWeight = fieldWeights.structured_loan_tranche || 0;
        if (structuredLoanTrancheWeight > 0) {
          const structuredLoanTrancheResult = calculateArrayMatchScore(
            row.deal_structured_loan_tranche,
            row.contact_structured_loan_tranche,
            "structured loan tranche"
          );
          
          breakdown.push({
            field: "structured_loan_tranche",
            score: Math.round(structuredLoanTrancheResult.score * 100),
            weight: structuredLoanTrancheWeight,
            reason: structuredLoanTrancheResult.reason,
            confidence: structuredLoanTrancheResult.confidence || 0.85
          });
          
          totalWeightedScore += structuredLoanTrancheResult.score * structuredLoanTrancheWeight;
        }

        // Property sub categories scoring
        const propertySubCategoriesWeight = fieldWeights.property_sub_categories || 0;
        if (propertySubCategoriesWeight > 0) {
          const propertySubCategoriesResult = calculateArrayMatchScore(
            row.deal_property_sub_categories,
            row.contact_property_sub_categories,
            "property sub categories"
          );
          
          breakdown.push({
            field: "property_sub_categories",
            score: Math.round(propertySubCategoriesResult.score * 100),
            weight: propertySubCategoriesWeight,
            reason: propertySubCategoriesResult.reason,
            confidence: propertySubCategoriesResult.confidence || 0.85
          });
          
          totalWeightedScore += propertySubCategoriesResult.score * propertySubCategoriesWeight;
        }

        // Capital source scoring (text field)
        const capitalSourceWeight = fieldWeights.capital_source || 0;
        if (capitalSourceWeight > 0 && row.deal_capital_source && row.contact_capital_source) {
          const sourceMatch = row.deal_capital_source.toLowerCase() === row.contact_capital_source.toLowerCase();
          const sourceScore = sourceMatch ? 1 : 0;
          
          breakdown.push({
            field: "capital_source",
            score: Math.round(sourceScore * 100),
            weight: capitalSourceWeight,
            reason: sourceMatch 
              ? `Capital source match: ${row.deal_capital_source}`
              : `Capital source mismatch: ${row.deal_capital_source} vs ${row.contact_capital_source}`,
            confidence: 0.9
          });
          
          totalWeightedScore += sourceScore * capitalSourceWeight;
        }

        // Loan type normalized scoring
        const loanTypeNormalizedWeight = fieldWeights.loan_type_normalized || 0;
        if (loanTypeNormalizedWeight > 0) {
          const loanTypeNormalizedResult = calculateArrayMatchScore(
            row.deal_loan_type_normalized,
            row.contact_loan_type_normalized,
            "loan type normalized"
          );
          
          breakdown.push({
            field: "loan_type_normalized",
            score: Math.round(loanTypeNormalizedResult.score * 100),
            weight: loanTypeNormalizedWeight,
            reason: loanTypeNormalizedResult.reason,
            confidence: loanTypeNormalizedResult.confidence || 0.85
          });
          
          totalWeightedScore += loanTypeNormalizedResult.score * loanTypeNormalizedWeight;
        }

        // Location focus scoring
        const locationFocusWeight = fieldWeights.location_focus || 0;
        if (locationFocusWeight > 0) {
          const locationFocusResult = calculateArrayMatchScore(
            row.deal_location_focus,
            row.contact_location_focus,
            "location focus"
          );
          
          breakdown.push({
            field: "location_focus",
            score: Math.round(locationFocusResult.score * 100),
            weight: locationFocusWeight,
            reason: locationFocusResult.reason,
            confidence: locationFocusResult.confidence || 0.85
          });
          
          totalWeightedScore += locationFocusResult.score * locationFocusWeight;
        }

        // Additional interest rate fields scoring
        const additionalInterestRates = [
          { field: 'interest_rate_sofr', deal: row.deal_interest_rate_sofr, contact: row.contact_interest_rate_sofr },
          { field: 'interest_rate_wsj', deal: row.deal_interest_rate_wsj, contact: row.contact_interest_rate_wsj },
          { field: 'interest_rate_prime', deal: row.deal_interest_rate_prime, contact: row.contact_interest_rate_prime },
          { field: 'interest_rate_libor', deal: row.deal_interest_rate_libor, contact: row.contact_interest_rate_libor },
          { field: 'interest_rate_5yt', deal: row.deal_interest_rate_5yt, contact: row.contact_interest_rate_5yt },
          { field: 'interest_rate_10yt', deal: row.deal_interest_rate_10yt, contact: row.contact_interest_rate_10yt }
        ];

        additionalInterestRates.forEach(({ field, deal, contact }) => {
          const weight = fieldWeights[field] || 0;
          if (weight > 0 && deal && contact) {
            const rateMatch = deal >= contact;
            const rateScore = rateMatch ? 1 : 0;
            const rateDiff = Math.abs(deal - contact);
            
            breakdown.push({
              field: field,
              score: Math.round(rateScore * 100),
              weight: weight,
              reason: rateMatch 
                ? `${field.replace('_', ' ')} match: ${(deal * 100).toFixed(1)}% meets requirement`
                : `${field.replace('_', ' ')} gap: ${(rateDiff * 100).toFixed(1)}% difference`,
              confidence: 0.95
            });
            
            totalWeightedScore += rateScore * weight;
          }
        });

        // Closing time weeks scoring
        const closingTimeWeight = fieldWeights.closing_time_weeks || 0;
        if (closingTimeWeight > 0) {
          const closingTimeResult = calculateRangeMatchScore(
            row.deal_min_closing_time_weeks,
            row.deal_max_closing_time_weeks,
            row.contact_min_closing_time_weeks,
            row.contact_max_closing_time_weeks,
            "closing time weeks"
          );
          
          breakdown.push({
            field: "closing_time_weeks",
            score: Math.round(closingTimeResult.score * 100),
            weight: closingTimeWeight,
            reason: closingTimeResult.reason,
            confidence: closingTimeResult.confidence || 0.9
          });
          
          totalWeightedScore += closingTimeResult.score * closingTimeWeight;
        }

        // Target return scoring
        const targetReturnWeight = fieldWeights.target_return || 0;
        if (targetReturnWeight > 0 && row.contact_target_return && row.deal_target_return) {
          const returnMatch = row.deal_target_return >= row.contact_target_return;
          const returnScore = returnMatch ? 1 : 0;
          const returnDiff = Math.abs(row.deal_target_return - row.contact_target_return);
          
          breakdown.push({
            field: "target_return",
            score: Math.round(returnScore * 100),
            weight: targetReturnWeight,
            reason: returnMatch 
              ? `Target return match: ${(row.deal_target_return * 100).toFixed(1)}% meets requirement of ${(row.contact_target_return * 100).toFixed(1)}%`
              : `Target return gap: Deal ${(row.deal_target_return * 100).toFixed(1)}% vs required ${(row.contact_target_return * 100).toFixed(1)}% (${(returnDiff * 100).toFixed(1)}% difference)`,
            confidence: 0.95
          });
          
          totalWeightedScore += returnScore * targetReturnWeight;
        }

        // Calculate criteria-specific score (direct calculation since weights sum to 1.0)
        const criteriaScore = Math.round(totalWeightedScore * 100);

        // Extract reasons for display
        const reasons = breakdown
          .filter(b => b.score > 0)
          .map(b => b.reason);

        const criteriaResult = {
          criteria_id: row.deal_criteria_id, // Use deal criteria ID instead of contact criteria ID
          contact_criteria_id: row.contact_criteria_id, // Keep contact criteria ID for reference
          criteria_source: row.criteria_source, // 'Contact' or 'Company'
          score: criteriaScore,
          breakdown,
          reasons,
          // Include investment criteria data if requested
          ...(addIcData && {
            investment_criteria: {
              target_return: row.contact_target_return,
              deal_size: {
                min: row.contact_min_deal_size,
                max: row.contact_max_deal_size,
                unit: 'MM'
              },
              historical_performance: {
                irr: row.contact_historical_irr,
                equity_multiple: row.contact_historical_em
              },
              capital_position: row.contact_capital_position,
              property_types: row.contact_property_types,
              property_sub_categories: row.contact_property_sub_categories,
              strategies: row.contact_strategies,
              financial_products: row.contact_financial_products,
              loan_terms: {
                type: row.contact_loan_type,
                program: row.contact_loan_program,
                recourse: row.contact_recourse_loan,
                min_hold_period: row.contact_min_hold_period,
                max_hold_period: row.contact_max_hold_period,
                min_loan_term: row.contact_min_loan_term,
                max_loan_term: row.contact_max_loan_term
              },
              loan_metrics: {
                interest_rate: row.contact_interest_rate,
                ltv_min: row.contact_loan_to_value_min,
                ltv_max: row.contact_loan_to_value_max,
                ltc_min: row.contact_loan_to_cost_min,
                ltc_max: row.contact_loan_to_cost_max,
                origination_fee_min: row.contact_loan_origination_fee_min,
                origination_fee_max: row.contact_loan_origination_fee_max,
                exit_fee_min: row.contact_loan_exit_fee_min,
                exit_fee_max: row.contact_loan_exit_fee_max,
                dscr_min: row.contact_min_loan_dscr,
                dscr_max: row.contact_max_loan_dscr
              }
            }
          })
        };

        allCriteriaScores.push(criteriaResult);

        // Collect all reasons
        allReasons.push(...reasons);

        // Track the best scoring criteria for overall deal score
        if (criteriaScore > bestOverallScore) {
          bestOverallScore = criteriaScore;
          bestCriteria = criteriaResult;
          combinedBreakdown = breakdown; // Use best scoring criteria's breakdown for backward compatibility
        }
      });

      // Filter out criteria with 0 score
      const validCriteriaScores = allCriteriaScores.filter(c => c.score > 0);
      
      if (validCriteriaScores.length === 0) {
        return null; // No valid matches for this deal
      }

      // Prioritize contact criteria over company criteria
      const contactCriteriaScores = validCriteriaScores.filter(c => c.criteria_source === 'Contact');
      const companyCriteriaScores = validCriteriaScores.filter(c => c.criteria_source === 'Company');
      
      // Use contact criteria if available, otherwise fall back to company criteria
      const primaryCriteriaScores = contactCriteriaScores.length > 0 ? contactCriteriaScores : companyCriteriaScores;
      
      // Calculate average score across primary criteria
      const avgScore = Math.round(primaryCriteriaScores.reduce((sum, c) => sum + c.score, 0) / primaryCriteriaScores.length);

      // Remove duplicate reasons
      const uniqueReasons = [...new Set(allReasons)];

      // Determine the best scoring criteria (prioritize contact over company)
      let bestPrimaryCriteria = primaryCriteriaScores[0];
      for (const criteria of primaryCriteriaScores) {
        if (criteria.score > bestPrimaryCriteria.score) {
          bestPrimaryCriteria = criteria;
        }
      }

      return {
        deal_id: firstRow.deal_id,
        deal_name: firstRow.deal_name,
        score: avgScore, // Average score across primary criteria
        best_score: bestPrimaryCriteria.score, // Highest scoring criteria
        criteria_source: bestPrimaryCriteria.criteria_source, // 'Contact' or 'Company'
        // Backward compatibility fields for UI
        breakdown: bestPrimaryCriteria.breakdown, // Use best scoring criteria's breakdown
        reasons: bestPrimaryCriteria.reasons || [], // Reasons from best scoring criteria
        scoring_method: "enhanced_normalized_weights_grouped_with_fallback",
        // New grouped data
        matching_criteria_count: validCriteriaScores.length,
        contact_criteria_count: contactCriteriaScores.length,
        company_criteria_count: companyCriteriaScores.length,
        all_criteria_matches: validCriteriaScores, // All matching criteria with their breakdowns
        primary_reasons: bestPrimaryCriteria.reasons || [], // Reasons from best scoring criteria
        all_reasons: uniqueReasons.slice(0, 10), // All unique reasons, limited to 10
        fallback_used: contactCriteriaScores.length === 0 && companyCriteriaScores.length > 0,
        // Add matched criteria IDs for categorization API
        matched_criteria_ids: validCriteriaScores.map(c => c.criteria_id),
      };
    })
    .filter(match => match !== null) // Remove deals with no valid matches
    .filter(match => match.score >= minScoreThreshold) // Filter by minimum score threshold
    .sort((a, b) => {
      // Primary: best_score descending (highest percentage match first)
      if (b.best_score !== a.best_score) return b.best_score - a.best_score;
      // Secondary: average score descending
      if (b.score !== a.score) return b.score - a.score;
      // Tertiary: deal name alphabetical
      return (a.deal_name || '').localeCompare(b.deal_name || '');
    });

    const totalContactCriteria = matches.reduce((total, m) => total + m.contact_criteria_count, 0);
    const totalCompanyCriteria = matches.reduce((total, m) => total + m.company_criteria_count, 0);
    const fallbackUsed = matches.some(m => m.fallback_used);
    
    console.log(`Found ${matches.length} unique deals with ${matches.reduce((total, m) => total + m.matching_criteria_count, 0)} total criteria matches`);
    console.log(`Contact criteria matches: ${totalContactCriteria}, Company criteria matches: ${totalCompanyCriteria}`);
    console.log(`Total matched criteria IDs: ${matches.reduce((total, m) => total + (m.matched_criteria_ids?.length || 0), 0)}`);
    if (fallbackUsed) {
      console.log(`⚠️  Fallback to company criteria was used for some matches`);
    }

    // Location-based fallback: if no matches found, return 3 deals based on location only
    const locationFallbackDeals: any[] = [];
    const locationFallbackUsed = false;
    
    if (matches.length === 0) {
      console.log(`No matches found for contact ${contactId}, applying location-based fallback`);
      
      // try {
      //   // Get contact's location preferences from contact record, basic location, and investment criteria
      //   const contactLocationQuery = `
      //     SELECT 
      //       ct.first_name,
      //       ct.last_name,
      //       ct.company_id,
      //       ct.investment_criteria_city,
      //       ct.investment_criteria_state,
      //       ct.investment_criteria_country,
      //       ct.contact_city,
      //       ct.contact_state,
      //       ct.contact_country,
      //       comp.company_name,
      //       comp.company_city,
      //       comp.company_state,
      //       -- Also get location from investment criteria if available
      //       ic.region,
      //       ic.state,
      //       ic.city,
      //       ic.country
      //     FROM contacts ct
      //     LEFT JOIN companies comp ON ct.company_id = comp.company_id
      //     LEFT JOIN investment_criteria ic ON ic.entity_type = 'Contact' 
      //       AND ic.entity_id = ct.contact_id::text 
      //       AND ic.is_active = true
      //     WHERE ct.contact_id = $1
      //     LIMIT 1
      //   `;
        
      //   const contactLocationResult = await pool.query(contactLocationQuery, [String(contactId)]);
        
      //   if (contactLocationResult.rows.length > 0) {
      //     const contactLocation = contactLocationResult.rows[0];
          
      //     // Combine location preferences from contact record, basic location, and investment criteria
      //     const locationPreferences: { [key: string]: string[] } = {
      //       city: [
      //         ...(contactLocation.investment_criteria_city || []), 
      //         ...(contactLocation.city || []),
      //         ...(contactLocation.contact_city ? [contactLocation.contact_city] : [])
      //       ],
      //       state: [
      //         ...(contactLocation.investment_criteria_state || []), 
      //         ...(contactLocation.state || []),
      //         ...(contactLocation.contact_state ? [contactLocation.contact_state] : [])
      //       ]
      //     };
          
      //     // Filter out empty arrays and duplicates
      //     Object.keys(locationPreferences).forEach(key => {
      //       locationPreferences[key] = [...new Set(locationPreferences[key].filter((item: string) => item && item.trim() !== ''))];
      //     });
          
      //     console.log(`Location preferences for contact ${contactId}:`, locationPreferences);
          
      //     // Check if we have any location preferences
      //     let hasLocationPreferences = Object.values(locationPreferences).some(prefs => prefs.length > 0);
          
      //     // If no location preferences found, try company location as final fallback
      //     if (!hasLocationPreferences && contactLocation.company_city) {
      //       console.log(`No contact location preferences found for contact ${contactId}, using company location as fallback`);
      //       locationPreferences.city = [...locationPreferences.city, contactLocation.company_city];
      //       locationPreferences.state = [...locationPreferences.state, contactLocation.company_state].filter(Boolean);
      //       hasLocationPreferences = true;
      //     }
          
      //     if (!hasLocationPreferences) {
      //       console.log(`No location preferences found for contact ${contactId}, skipping location fallback`);
      //       // Return empty response when no location preferences exist
      //       return NextResponse.json({ 
      //         matches: [],
      //         field_weights_used: fieldWeights,
      //         total_candidates: 0,
      //         matches_found: 0,
      //         contact_criteria_matches: 0,
      //         company_criteria_matches: 0,
      //         fallback_used: false,
      //         location_fallback_used: false,
      //         scoring_method: "enhanced_javascript_with_normalized_weights_grouped_with_fallback"
      //       });
      //     }
          
      //     // Build location-based query to find deals (no investment criteria filtering)
      //   //           // Only show unique deals (one row per deal_id), even if there are multiple criteria per deal
      //   //           const locationFallbackQuery = `
      //   //   SELECT DISTINCT ON (d.entity_id)
      //   //     d.entity_id AS deal_id,
      //   //     d.criteria_id,
      //   //     deals.deal_name,
      //   //     d.minimum_deal_size,
      //   //     d.maximum_deal_size,
      //   //     d.region AS deal_region,
      //   //     d.state AS deal_state,
      //   //     d.city AS deal_city,
      //   //     d.country AS deal_country,
      //   //     d.capital_position AS deal_capital_position,
      //   //     d.property_types AS deal_property_types,
      //   //     d.strategies AS deal_strategies,
      //   //     d.financial_products AS deal_financial_products,
      //   //     d.loan_type AS deal_loan_type,
      //   //     d.interest_rate,
      //   //     d.loan_to_value_min,
      //   //     d.loan_to_value_max,
      //   //     d.loan_to_cost_min,
      //   //     d.loan_to_cost_max,
      //   //     d.min_hold_period,
      //   //     d.max_hold_period,
      //   //     d.min_loan_term,
      //   //     d.max_loan_term,
      //   //     d.min_loan_dscr,
      //   //     d.max_loan_dscr,
      //   //     d.property_sub_categories AS deal_property_sub_categories,
      //   //     d.capital_source AS deal_capital_source,
      //   //     d.loan_type_normalized AS deal_loan_type_normalized,
      //   //     d.interest_rate_sofr AS deal_interest_rate_sofr,
      //   //     d.interest_rate_wsj AS deal_interest_rate_wsj,
      //   //     d.interest_rate_prime AS deal_interest_rate_prime,
      //   //     d.interest_rate_libor AS deal_interest_rate_libor,
      //   //     d.interest_rate_5yt AS deal_interest_rate_5yt,
      //   //     d.interest_rate_10yt AS deal_interest_rate_10yt,
      //   //     d.loan_term_string AS deal_loan_term_string,
      //   //     d.location_focus AS deal_location_focus,
      //   //     d.min_closing_time_weeks AS deal_min_closing_time_weeks,
      //   //     d.max_closing_time_weeks AS deal_max_closing_time_weeks,
      //   //     d.closing_time_weeks AS deal_closing_time_weeks,
      //   //     d.target_return AS deal_target_return,
      //   //     d.historical_irr,
      //   //     d.historical_em,
      //   //     -- Add columns needed for ORDER BY
      //   //     COALESCE(d.minimum_deal_size, 0) AS deal_size_for_sort,
      //   //     deals.created_at AS deal_created_at
      //   //   FROM investment_criteria d
      //   //   INNER JOIN deals ON d.entity_id = deals.deal_id::text
      //   //   WHERE d.entity_type = 'Deal' 
      //   //     AND d.is_active = true
      //   //     AND (
      //   //       -- Match by city if available
      //   //       (d.city IS NOT NULL AND d.city && $1::text[])
      //   //       OR
      //   //       -- Match by state if available
      //   //       (d.state IS NOT NULL AND d.state && $2::text[])
      //   //     )
      //   //   ORDER BY 
      //   //     d.entity_id,
      //   //     deal_created_at DESC
      //   //   LIMIT 6
      //   // `;
      //   //   const locationParams = [
      //   //     locationPreferences.city || [],
      //   //     locationPreferences.state || []
      //   //   ];
          
      //   //   const locationFallbackResult = await pool.query(locationFallbackQuery, locationParams);
          
      //   //   if (locationFallbackResult.rows.length > 0) {
      //   //     locationFallbackDeals = locationFallbackResult.rows.slice(0, 3).map((row, index) => {
      //   //       // Create a simple location-based score (30-50 range to indicate fallback)
      //   //       const baseScore = 30 + (index * 5); // 30, 35, 40
              
      //   //       // Determine location match reason with better logic
      //   //       let locationReason = "Location-based recommendation";
      //   //       let locationType = "location";
              
      //   //       // Check for exact matches in order of specificity using combined preferences
      //   //       if (row.deal_city && locationPreferences.city.length > 0 && 
      //   //           row.deal_city.some((city: string) => locationPreferences.city.includes(city))) {
      //   //         const matchedCity = row.deal_city.find((city: string) => locationPreferences.city.includes(city));
      //   //         locationReason = `City match: ${matchedCity}`;
      //   //         locationType = "city";
      //   //       } else if (row.deal_state && locationPreferences.state.length > 0 && 
      //   //                  row.deal_state.some((state: string) => locationPreferences.state.includes(state))) {
      //   //         const matchedState = row.deal_state.find((state: string) => locationPreferences.state.includes(state));
      //   //         locationReason = `State match: ${matchedState}`;
      //   //         locationType = "state";
      //   //       }
              
      //   //       return {
      //   //         deal_id: row.deal_id,
      //   //         deal_name: row.deal_name,
      //   //         score: baseScore,
      //   //         best_score: baseScore,
      //   //         criteria_source: 'Location_Fallback',
      //   //         breakdown: [{
      //   //           field: "location_fallback",
      //   //           score: baseScore,
      //   //           weight: 1.0,
      //   //           reason: locationReason,
      //   //           confidence: 0.6
      //   //         }],
      //   //         reasons: [locationReason],
      //   //         scoring_method: "location_fallback_only",
      //   //         location_match_type: locationType,
      //   //         matching_criteria_count: 1,
      //   //         contact_criteria_count: 0,
      //   //         company_criteria_count: 0,
      //   //         all_criteria_matches: [{
      //   //           criteria_id: 'location_fallback',
      //   //           criteria_source: 'Location_Fallback',
      //   //           score: baseScore,
      //   //           breakdown: [{
      //   //             field: "location_fallback",
      //   //             score: baseScore,
      //   //             weight: 1.0,
      //   //             reason: locationReason,
      //   //             confidence: 0.6
      //   //           }],
      //   //           reasons: [locationReason],
      //   //           location_match_type: locationType
      //   //         }],
      //   //         primary_reasons: [locationReason],
      //   //         all_reasons: [locationReason],
      //   //         fallback_used: false,
      //   //         location_fallback_used: true,
      //   //         // Add matched criteria IDs for categorization API (use actual criteria_id from database)
      //   //         matched_criteria_ids: [row.criteria_id],
      //   //         // Include deal details for display
      //   //         deal_details: {
      //   //           minimum_deal_size: row.minimum_deal_size,
      //   //           maximum_deal_size: row.maximum_deal_size,
      //   //           region: row.deal_region,
      //   //           state: row.deal_state,
      //   //           city: row.deal_city,
      //   //           country: row.deal_country,
      //   //           property_types: row.deal_property_types,
      //   //           strategies: row.deal_strategies,
      //   //           loan_type: row.deal_loan_type,
      //   //           interest_rate: row.interest_rate,
      //   //           loan_to_value_min: row.loan_to_value_min,
      //   //           loan_to_value_max: row.loan_to_value_max,
      //   //           loan_to_cost_min: row.loan_to_cost_min,
      //   //           loan_to_cost_max: row.loan_to_cost_max,
      //   //           min_hold_period: row.min_hold_period,
      //   //           max_hold_period: row.max_hold_period,
      //   //           min_loan_term: row.min_loan_term,
      //   //           max_loan_term: row.max_loan_term,
      //   //           min_loan_dscr: row.min_loan_dscr,
      //   //           max_loan_dscr: row.max_loan_dscr,
      //   //           property_sub_categories: row.deal_property_sub_categories,
      //   //           capital_source: row.deal_capital_source,
      //   //           loan_type_normalized: row.deal_loan_type_normalized,
      //   //           interest_rate_sofr: row.deal_interest_rate_sofr,
      //   //           interest_rate_wsj: row.deal_interest_rate_wsj,
      //   //           interest_rate_prime: row.deal_interest_rate_prime,
      //   //           interest_rate_libor: row.deal_interest_rate_libor,
      //   //           interest_rate_5yt: row.deal_interest_rate_5yt,
      //   //           interest_rate_10yt: row.deal_interest_rate_10yt,
      //   //           loan_term_string: row.deal_loan_term_string,
      //   //           location_focus: row.deal_location_focus,
      //   //           min_closing_time_weeks: row.deal_min_closing_time_weeks,
      //   //           max_closing_time_weeks: row.deal_max_closing_time_weeks,
      //   //           closing_time_weeks: row.deal_closing_time_weeks,
      //   //           target_return: row.deal_target_return,
      //   //           historical_irr: row.historical_irr,
      //   //           historical_em: row.historical_em
      //   //         }
      //   //       };
      //   //     });
            
      //   //     locationFallbackUsed = true;
      //   //     console.log(`📍 Location fallback found ${locationFallbackDeals.length} deals for contact ${contactId}`);
      //   //   }
      //   }
      // } catch (error) {
      //   console.error(`Error in location fallback for contact ${contactId}:`, error);
      // }
    }
    
    // Combine regular matches with location fallback deals
    const allMatches = [...matches, ...locationFallbackDeals];
    
    return NextResponse.json({ 
      matches: allMatches,
      field_weights_used: fieldWeights,
      total_candidates: dealGroups.size,
      matches_found: allMatches.length,
      contact_criteria_matches: totalContactCriteria,
      company_criteria_matches: totalCompanyCriteria,
      fallback_used: fallbackUsed,
      location_fallback_used: locationFallbackUsed,
      scoring_method: locationFallbackUsed ? "location_fallback_with_enhanced_javascript" : "enhanced_javascript_with_normalized_weights_grouped_with_fallback",
      filtering: {
        min_score_threshold: minScoreThreshold,
        show_all_matches: showAllMatches,
        total_before_filtering: Array.from(dealGroups.entries()).length,
        filtered_out_count: Array.from(dealGroups.entries()).length - allMatches.length
      }
    });
    
  } catch (error) {
    console.error("Error in deals-for-contact matching:", error);
    return NextResponse.json(
      { error: "Failed to fetch deal matches" },
      { status: 500 }
    );
  }
}
