import { pool } from '@/lib/db'
import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { id, clean_value_id, is_verified } = body

    const query = `
      UPDATE criteria_value_matches
      SET current_clean_value_id = $1,
          current_clean_value = (SELECT value FROM clean_criteria_values WHERE id = $1),
          is_verified = $2
      WHERE id = $3
      RETURNING *
    `
    const result = await pool.query(query, [clean_value_id, is_verified, id])
    
    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: 'Database error' }, { status: 500 })
  }
} 