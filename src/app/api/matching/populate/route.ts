import { pool } from '@/lib/db'
import { NextResponse } from 'next/server'

export async function POST() {
  try {
    // Get all the investment criteria fields
    const criteriaFields = [
      'investment_criteria_country',
      'investment_criteria_geographic_region',
      'investment_criteria_state',
      'investment_criteria_city',
      'investment_criteria_deal_size',
      'investment_criteria_property_type',
      'investment_criteria_property_type_subcategory',
      'investment_criteria_asset_type',
      'investment_criteria_loan_type',
      'investment_criteria_loan_type_short_term',
      'investment_criteria_loan_type_long_term',
      'investment_criteria_loan_term_years',
      'investment_criteria_loan_interest_rate_basis',
      'investment_criteria_loan_interest_rate',
      'investment_criteria_loan_to_value',
      'investment_criteria_loan_to_cost',
      'investment_criteria_loan_origination_fee_pct',
      'investment_criteria_loan_exit_fee_pct',
      'investment_criteria_recourse_loan',
      'investment_criteria_loan_dscr',
      'investment_criteria_closing_time',
      'capital_type'
    ]

    // For each field, get distinct values and insert into matches table
    for (const field of criteriaFields) {
      const distinctValuesQuery = `
        SELECT DISTINCT ${field} as value
        FROM contacts
        WHERE ${field} IS NOT NULL AND ${field} != ''
      `
      const distinctValues = await pool.query(distinctValuesQuery)

      // Insert each distinct value into the matches table if it doesn't exist
      for (const row of distinctValues.rows) {
        const insertQuery = `
          INSERT INTO criteria_value_matches 
            (field, raw_value, confidence_score)
          VALUES 
            ($1, $2, 0)
          ON CONFLICT (field, raw_value) DO NOTHING
        `
        await pool.query(insertQuery, [field, row.value])
      }
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: 'Database error' }, { status: 500 })
  }
} 