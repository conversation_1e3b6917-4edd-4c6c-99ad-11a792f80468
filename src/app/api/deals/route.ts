import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "20");
    const search = searchParams.get("search") || "";
    const sortBy = searchParams.get("sortBy") || "deal_id";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    const offset = (page - 1) * pageSize;

    // Map camelCase frontend parameters to snake_case database columns
    const columnMapping: { [key: string]: string } = {
      'updatedAt': 'updated_at',
      'createdAt': 'created_at',
      'dealId': 'deal_id',
      'dealName': 'deal_name',
      'dealStage': 'deal_stage',
      'dealStatus': 'deal_status',
      'dateReceived': 'date_received',
      'dateClosed': 'date_closed',
      'dateUnderContract': 'date_under_contract',
      'priority': 'priority',
      'reviewStatus': 'review_status',
      'reviewedAt': 'reviewed_at',
      'extractionTimestamp': 'extraction_timestamp'
    };

    // Map the sortBy parameter to the correct database column
    const mappedSortBy = columnMapping[sortBy] || sortBy;

    // Build the WHERE clause for search
    let whereClause = "";
    const queryParams: any[] = [];
    let paramIndex = 1;

    if (search) {
      whereClause = `
        WHERE (
          COALESCE(deal_name, '') ILIKE $${paramIndex} OR 
          COALESCE(sponsor_name, '') ILIKE $${paramIndex} OR
          COALESCE(property_description, '') ILIKE $${paramIndex} OR
          COALESCE(zip_code, '') ILIKE $${paramIndex}
        )
      `;
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM deals ${whereClause}`;
    const countResult = await pool.query(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].count);

    // Get deals with pagination
    const dealsQuery = `
      SELECT * FROM deals 
      ${whereClause}
      ORDER BY ${mappedSortBy} ${sortOrder.toUpperCase()}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    queryParams.push(pageSize, offset);
    
    console.log(`🔍 V1 API: Sorting by ${sortBy} -> ${mappedSortBy} (${sortOrder.toUpperCase()})`);
    console.log(`📋 V1 API: Query: ${dealsQuery}`);
    
    const dealsResult = await pool.query(dealsQuery, queryParams);
    const deals = dealsResult.rows;

    // Calculate data quality metrics for each deal
    const dealsWithQuality = deals.map(deal => {
      const qualityMetrics = calculateDealDataQuality(deal);
      return {
        ...deal,
        data_quality_metrics: qualityMetrics
      };
    });

    const totalPages = Math.ceil(total / pageSize);

    return NextResponse.json({
      deals: dealsWithQuality,
      total,
      totalPages,
      currentPage: page,
      pageSize
    });
  } catch (error) {
    console.error("Error fetching deals:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

function calculateDealDataQuality(deal: any) {
  const fields = [
    'deal_name', 'sponsor_name', 'status', 'deal_stage', 'priority',
    'zip_code', 'property_description', 'lot_area', 'floor_area_ratio', 'zoning_square_footage',
    'yield_on_cost', 'projected_gp_equity_multiple', 'projected_gp_irr',
    'projected_lp_equity_multiple', 'projected_lp_irr', 'projected_total_equity_multiple', 'projected_total_irr'
  ];

  let completedFields = 0;
  const missingFields: string[] = [];

  fields.forEach(field => {
    const value = deal[field];
    const hasValue = value !== null && value !== '' && value !== undefined;
    
    if (hasValue) {
      completedFields++;
    } else {
      missingFields.push(field);
    }
  });

  const qualityScore = Math.round((completedFields / fields.length) * 100);

  return {
    qualityScore,
    completedFields,
    totalFields: fields.length,
    missingFields
  };
}
