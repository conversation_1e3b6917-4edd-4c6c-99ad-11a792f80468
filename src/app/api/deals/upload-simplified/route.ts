import { NextRequest, NextResponse } from "next/server";
import { DealProcessor } from "@/lib/processors/DealProcessor";
import { FileManager } from "@/lib/utils/fileManager";
import { pool } from "@/lib/db";
import {
  findPotentialDealDuplicates,
  type PotentialDealDuplicate,
} from "@/lib/utils/dealConflictDetector";
import {
  generateDealEmbedding,
  DealEmbeddingData,
} from "@/lib/embeddings/EmbeddingService";
import { bullMQManager } from "@/lib/queue/BullMQManager";
import { DealProcessingJobData } from "@/lib/queue/DealProcessorJob";
import { getDealsTableColumns, getInvestmentCriteriaColumns } from '@/lib/utils/dbSchema';

// Utility to convert camelCase to snake_case
function camelToSnake(str: string): string {
  return str.replace(/[A-Z]/g, (letter: string) => `_${letter.toLowerCase()}`);
}

// Recursively convert all keys in an object from camelCase to snake_case
function keysToSnake(obj: unknown): unknown {
  if (Array.isArray(obj)) {
    return obj.map(keysToSnake);
  } else if (obj && typeof obj === "object" && obj.constructor === Object) {
    const newObj: Record<string, unknown> = {};
    for (const key in obj as Record<string, unknown>) {
      if (Object.hasOwn(obj, key)) {
        newObj[camelToSnake(key)] = keysToSnake(
          (obj as Record<string, unknown>)[key]
        );
      }
    }
    return newObj;
  }
  return obj;
}

// Utility to get the actual columns in the deals table
async function getDealsTableColumns(): Promise<Set<string>> {
  const res = await pool.query(
    `SELECT column_name FROM information_schema.columns WHERE table_name = 'deals' AND table_schema = 'public'`
  );
  return new Set(
    res.rows.map((row: { column_name: string }) => row.column_name)
  );
}

// Utility to get the actual columns in the investment_criteria table
async function getInvestmentCriteriaColumns(): Promise<Set<string>> {
  const res = await pool.query(
    `SELECT column_name FROM information_schema.columns WHERE table_name = 'investment_criteria' AND table_schema = 'public'`
  );
  return new Set(
    res.rows.map((row: { column_name: string }) => row.column_name)
  );
}

// Normalize min/max fields for investment criteria
function normalizeMinMaxFields(
  criteria: Record<string, any>,
  pairs: [string, string][]
) {
  for (const [minKey, maxKey] of pairs) {
    const minVal = criteria[minKey];
    const maxVal = criteria[maxKey];
    // If either is a string with a range, split
    if (typeof minVal === "string" && minVal.includes("-")) {
      const [min, max] = minVal
        .split("-")
        .map((s) => parseFloat(s.replace(/[^0-9.]/g, "")));
      criteria[minKey] = min;
      criteria[maxKey] = max;
    } else if (typeof maxVal === "string" && maxVal.includes("-")) {
      const [min, max] = maxVal
        .split("-")
        .map((s) => parseFloat(s.replace(/[^0-9.]/g, "")));
      criteria[minKey] = min;
      criteria[maxKey] = max;
    } else if (minVal != null && (maxVal == null || maxVal === "")) {
      criteria[maxKey] = minVal;
    } else if (maxVal != null && (minVal == null || minVal === "")) {
      criteria[minKey] = maxVal;
    }
  }
}

// Function to create LLM output file and relationship
async function createLLMOutputFile(
  dealId: number,
  result: any,
  files: File[]
): Promise<void> {
  try {
    const isMultipleFiles = files.length > 1;
    const llmResponse = result.llmResponse;

    if (!llmResponse) {
      console.warn("No LLM response available for LLM output file creation");
      return;
    }

    const rawLLMOutput = {
      timestamp: new Date().toISOString(),
      dealId: dealId,
      ...(isMultipleFiles
        ? {
            fileCount: files.length,
            files: files.map((f) => ({
              fileName: f.name,
              mimeType: f.type,
              size: f.size,
            })),
          }
        : {
            fileName: files[0].name,
            mimeType: files[0].type,
          }),
      llmProvider: "gemini-2.5-flash",
      prompt: {
        system: "Deal extraction prompt",
        user: isMultipleFiles
          ? `Analysis of ${files.length} files together`
          : "File analysis request",
      },
      response: {
        raw: llmResponse.content || JSON.stringify(llmResponse),
        parsed: result.extractedData,
        parseError: null,
      },
      metadata: {
        processingDuration: result.processingDuration || 0,
        extractionMethod: "simplified",
        confidence:
          result.extractedData?.metadata?.extraction_confidence || "unknown",
      },
    };

    const jsonBuffer = Buffer.from(
      JSON.stringify(rawLLMOutput, null, 2),
      "utf-8"
    );
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const outputFileName = isMultipleFiles
      ? `deal_${dealId}_llm_output_multiple_${timestamp}.json`
      : `deal_${dealId}_llm_output_${timestamp}.json`;

    // Upload the raw LLM output as a JSON file
    const uploadRequest = {
      original_name: outputFileName,
      title: isMultipleFiles
        ? `Raw LLM Output for Deal ${dealId} (Multiple Files)`
        : `Raw LLM Output for Deal ${dealId}`,
      description: isMultipleFiles
        ? `Raw Gemini LLM output and processing details for ${files.length} files processed together`
        : `Raw Gemini LLM output and processing details for deal extraction`,
      uploaded_by: "system",
      upload_source: "deal_processor",
      access_level: "private" as const,
      metadata: {
        deal_id: dealId,
        file_type: "llm_output",
        extraction_timestamp: new Date().toISOString(),
        llm_provider: "gemini-2.5-flash",
        ...(isMultipleFiles
          ? {
              file_count: files.length,
              original_filenames: files.map((f) => f.name),
            }
          : {
              original_filename: files[0].name,
            }),
      },
      tags: [
        "llm_output",
        "deal_processing",
        "gemini",
        ...(isMultipleFiles ? ["multiple_files"] : []),
      ],
    };

    const { file: uploadedFile } = await FileManager.uploadFile(
      jsonBuffer,
      uploadRequest
    );

    // Create relationship to the deal
    const relationshipRequest = {
      target_table_name: "deals",
      target_column_name: "deal_id",
      target_row_id: dealId.toString(),
      relationship_type: "llm_output",
      relationship_title: isMultipleFiles
        ? "Raw LLM Processing Output (Multiple Files)"
        : "Raw LLM Processing Output",
      relationship_notes: isMultipleFiles
        ? `Contains the raw LLM response and processing details for ${files.length} files`
        : "Contains the raw LLM response and processing details",
      is_primary: false,
      display_order: 999, // Show at the end
    };

    await FileManager.createFileRelationship(
      uploadedFile.file_id,
      relationshipRequest
    );

    console.log(`✓ Stored raw LLM output as file: ${outputFileName}`);
  } catch (error) {
    console.warn(
      `Failed to store raw LLM output: ${error}. Continuing with deal processing.`
    );
  }
}

// Function to process and insert deal data
async function processDealData(
  result: any,
  dealsTableColumns: Set<string>,
  files: File[] // Add files parameter for metadata
): Promise<
  | number
  | null
  | {
      success: false;
      duplicates: PotentialDealDuplicate[];
      message: string;
    }
> {
  let dealId: number | null = null;

  // Insert into deals table
  if (result.extractedData) {
    // Combine coreFields and extraFields from the DealProcessor
    const extractedCoreFields = result.extractedData.coreFields || {};
    const extractedExtraFields = result.extractedData.extraFields || {};
    const extractedMetadata = result.extractedData.metadata || {};

    const coreFieldsSnake = keysToSnake(extractedCoreFields) as Record<
      string,
      any
    >;
    const extraFieldsSnake = keysToSnake(extractedExtraFields) as Record<
      string,
      any
    >;
    const allFields = {
      ...coreFieldsSnake,
      ...extraFieldsSnake,
    } as Record<string, any>;

    console.log("ALL EXTRACTED FIELDS:", JSON.stringify(allFields, null, 2));
    console.log(
      "EXTRACTED METADATA:",
      JSON.stringify(extractedMetadata, null, 2)
    );
    console.log("DEALS TABLE COLUMNS:", Array.from(dealsTableColumns));

    // Extract investment_criteria before processing other fields
    const investmentCriteriaData =
      result.extractedData.investmentCriteria ||    // ← Added this - direct from extractedData
      extractedCoreFields.investmentCriteria ||
      extractedExtraFields.investmentCriteria ||
      allFields["investment_criteria"] ||
      allFields["investmentCriteria"];
    delete allFields["investment_criteria"];
    delete allFields["investmentCriteria"];

    const coreFields: Record<string, any> = {};
    const extraFields: Record<string, any> = {};

    for (const key in allFields) {
      if (dealsTableColumns.has(key)) {
        coreFields[key] = allFields[key];
        console.log(
          `✓ MAPPED TO DEALS TABLE: ${key} = ${JSON.stringify(allFields[key])}`
        );
      } else {
        extraFields[key] = allFields[key];
        console.log(
          `→ MAPPED TO EXTRA_FIELDS: ${key} = ${JSON.stringify(allFields[key])}`
        );
      }
    }

    // Add metadata fields to coreFields if they exist in the deals table
    const isMultipleFiles = files.length > 1;
    const totalFileSize = files.reduce((total, f) => total + f.size, 0);

    const metadataFields = {
      // Processing metadata
      extraction_timestamp: new Date(),
      llm_model_used: extractedMetadata.llm_model_used || "gemini-2.5-flash",
      llm_provider: extractedMetadata.llm_provider || "gemini",
      processor_version: extractedMetadata.processor_version || "1.0",
      processing_duration_ms: result.processingDuration || 0,

      // Document metadata
      document_type: Array.isArray(extractedMetadata.document_type)
        ? extractedMetadata.document_type
        : extractedMetadata.document_type
        ? [extractedMetadata.document_type]
        : ["deal_document"],
      extraction_method: isMultipleFiles
        ? ["simplified_multiple"]
        : ["simplified"],
      document_source: files.map((f) => f.type),
      document_filename: files.map((f) => f.name),
      document_size_bytes: totalFileSize,

      // Extraction quality metadata
      extraction_confidence:
        extractedMetadata.extraction_confidence || "medium",
      processing_notes:
        extractedMetadata.processing_notes ||
        (isMultipleFiles
          ? `Processed ${files.length} files together using Gemini 2.5 Flash`
          : `Processed single file using Gemini 2.5 Flash`),
      review_status: "pending",
      status: "active",

      // Additional metadata if available
      data_quality_issues: extractedMetadata.data_quality_issues || null,
      missing_critical_fields:
        extractedMetadata.missing_critical_fields || null,
    };

    // Only add metadata fields that exist in the deals table
    for (const [key, value] of Object.entries(metadataFields)) {
      if (dealsTableColumns.has(key)) {
        coreFields[key] = value;
        console.log(
          `✓ ADDED METADATA TO DEALS TABLE: ${key} = ${JSON.stringify(value)}`
        );
      } else {
        extraFields[key] = value;
        console.log(
          `→ ADDED METADATA TO EXTRA_FIELDS: ${key} = ${JSON.stringify(value)}`
        );
      }
    }

    // Always include extra_fields
    coreFields.extra_fields = JSON.stringify(extraFields);
    // Remove deal_id if present (auto-incremented)
    delete coreFields.deal_id;

    console.log(
      "FINAL CORE FIELDS FOR INSERT:",
      JSON.stringify(coreFields, null, 2)
    );

    // === CONFLICT DETECTION ===
    console.log("🚨 === CHECKING FOR DEAL CONFLICTS ===");
    console.log("🎯 Deal data being checked for duplicates:", {
      deal_name: coreFields.deal_name,
      sponsor_name: coreFields.sponsor_name,
      city: coreFields.city,
      state: coreFields.state,
      region: coreFields.region,
      property_type: coreFields.property_type,
      all_core_fields: Object.keys(coreFields),
    });

    try {
      console.log("📞 Calling findPotentialDealDuplicates...");
      const potentialDuplicates = await findPotentialDealDuplicates(coreFields);
      console.log("📨 Received response from duplicate detection:", {
        count: potentialDuplicates.length,
        duplicates: potentialDuplicates,
      });

      if (potentialDuplicates.length > 0) {
        console.log(
          `🚨 Found ${potentialDuplicates.length} potential deal duplicates - RETURNING CONFLICT RESPONSE`
        );
        console.log(
          "🔄 Duplicates details:",
          JSON.stringify(potentialDuplicates, null, 2)
        );

        // Return the duplicates instead of creating the deal
        return {
          success: false,
          duplicates: potentialDuplicates,
          message: `Found ${potentialDuplicates.length} potential duplicate deal(s). Please review and resolve conflicts.`,
        };
      }

      console.log("✅ No conflicts detected, proceeding with deal creation");
    } catch (conflictError) {
      console.error("❌ Error during conflict detection:", conflictError);
      if (conflictError instanceof Error) {
        console.error("❌ Stack trace:", conflictError.stack);
      }
      // Continue with insertion if conflict detection fails
      console.log(
        "⚠️  Conflict detection failed, proceeding with deal creation"
      );
    }

    // Build insert
    const columns = Object.keys(coreFields);
    const values = Object.values(coreFields);
    const placeholders = columns.map((_, i) => `$${i + 1}`).join(", ");
    const sql = `INSERT INTO public.deals (${columns.join(
      ", "
    )}) VALUES (${placeholders}) RETURNING deal_id`;

    console.log("DEALS INSERT SQL:", sql);
    console.log("DEALS INSERT VALUES:", values);

    const insertResult = await pool.query(sql, values);
    dealId = insertResult.rows[0]?.deal_id || null;

    console.log("✓ INSERTED DEAL ID:", dealId);

    // Generate and store embedding for the newly created deal
    // COMMENTED OUT: deal_embedding column doesn't exist in deals table
    /*
    if (dealId) {
      try {
        console.log("🔤 Generating vector embedding for deal...");
        const embeddingData: DealEmbeddingData = {
          deal_name: coreFields.deal_name,
          sponsor_name: coreFields.sponsor_name,
          property_type: coreFields.property_type,
          city: coreFields.city,
          state: coreFields.state,
          region: coreFields.region,
          deal_summary: coreFields.deal_summary,
          description: coreFields.description,
        };

        const embedding = await generateDealEmbedding(embeddingData);
        const embeddingArray = `[${embedding.join(",")}]`;

        // Update the deal with the embedding
        const updateEmbeddingQuery = `
          UPDATE public.deals 
          SET deal_embedding = $1::vector,
              embedding_model = 'text-embedding-004',
              embedding_created_at = NOW()
          WHERE deal_id = $2
        `;

        await pool.query(updateEmbeddingQuery, [embeddingArray, dealId]);
        console.log(`✅ Generated and stored embedding for deal ${dealId}`);
      } catch (embeddingError) {
        console.error(
          `⚠️ Failed to generate embedding for deal ${dealId}:`,
          embeddingError
        );
        // Don't fail the entire upload if embedding generation fails
        console.log(
          "⚠️ Continuing with deal creation despite embedding failure"
        );
      }
    }
    */

    // --- Insert multiple investment criteria if present ---
    if (dealId && investmentCriteriaData) {
      console.log("=== DEBUGGING INVESTMENT CRITERIA INSERTION ===");
      console.log(
        "INVESTMENT CRITERIA DATA:",
        JSON.stringify(investmentCriteriaData, null, 2)
      );

      const investmentArr = Array.isArray(investmentCriteriaData)
        ? investmentCriteriaData
        : [investmentCriteriaData];

      if (investmentArr.length > 0) {
        const investmentCriteriaColumns = await getInvestmentCriteriaColumns();

        console.log(
          "INVESTMENT CRITERIA TABLE COLUMNS:",
          Array.from(investmentCriteriaColumns)
        );

        for (let criteriaObj of investmentArr) {
          console.log(
            "PROCESSING CRITERIA OBJECT:",
            JSON.stringify(criteriaObj, null, 2)
          );

          criteriaObj = keysToSnake(criteriaObj) as Record<string, any>;
          console.log(
            "AFTER SNAKE_CASE:",
            JSON.stringify(criteriaObj, null, 2)
          );

          // Map min/max fields to single database columns for investment criteria
          // For LTV: Use max value if available, otherwise min, for the main loan_ltv column
          if (
            criteriaObj.loan_to_value_max !== null &&
            criteriaObj.loan_to_value_max !== undefined
          ) {
            criteriaObj.loan_ltv = criteriaObj.loan_to_value_max;
          } else if (
            criteriaObj.loan_to_value_min !== null &&
            criteriaObj.loan_to_value_min !== undefined
          ) {
            criteriaObj.loan_ltv = criteriaObj.loan_to_value_min;
          }

          // For LTC: Use max value if available, otherwise min, for the main loan_ltc column
          if (
            criteriaObj.loan_to_cost_max !== null &&
            criteriaObj.loan_to_cost_max !== undefined
          ) {
            criteriaObj.loan_ltc = criteriaObj.loan_to_cost_max;
          } else if (
            criteriaObj.loan_to_cost_min !== null &&
            criteriaObj.loan_to_cost_min !== undefined
          ) {
            criteriaObj.loan_ltc = criteriaObj.loan_to_cost_min;
          }

          // Normalize other min/max fields that have dedicated columns
          normalizeMinMaxFields(criteriaObj, [
            ["minimum_deal_size", "maximum_deal_size"],
            ["min_hold_period", "max_hold_period"],
            ["min_loan_term", "max_loan_term"],
            ["min_loan_dscr", "max_loan_dscr"],
          ]);

          // Always set entity_type and entity_id
          criteriaObj.entity_type = "Deal";
          criteriaObj.entity_id = dealId;

          // Insert processor context fields if available
          if (result.extractedData && result.extractedData.metadata) {
            const meta = result.extractedData.metadata;
            if (meta.llm_model_used)
              criteriaObj.llm_model_used = meta.llm_model_used;
            if (meta.llm_provider) criteriaObj.llm_provider = meta.llm_provider;
            if (meta.processor_version)
              criteriaObj.processor_version = meta.processor_version;
            if (meta.extraction_method)
              criteriaObj.extraction_method = meta.extraction_method;
          }

          const core: Record<string, any> = {};
          const extra: Record<string, any> = {};

          // Always preserve min/max LTV/LTC values in extra_fields for UI display
          if (
            criteriaObj.loan_to_value_min !== null &&
            criteriaObj.loan_to_value_min !== undefined
          ) {
            extra.loan_to_value_min = criteriaObj.loan_to_value_min;
          }
          if (
            criteriaObj.loan_to_value_max !== null &&
            criteriaObj.loan_to_value_max !== undefined
          ) {
            extra.loan_to_value_max = criteriaObj.loan_to_value_max;
          }
          if (
            criteriaObj.loan_to_cost_min !== null &&
            criteriaObj.loan_to_cost_min !== undefined
          ) {
            extra.loan_to_cost_min = criteriaObj.loan_to_cost_min;
          }
          if (
            criteriaObj.loan_to_cost_max !== null &&
            criteriaObj.loan_to_cost_max !== undefined
          ) {
            extra.loan_to_cost_max = criteriaObj.loan_to_cost_max;
          }

          for (const key in criteriaObj) {
            if (investmentCriteriaColumns.has(key)) {
              core[key] = criteriaObj[key];
              console.log(
                `✓ MAPPED TO CRITERIA TABLE: ${key} = ${JSON.stringify(
                  criteriaObj[key]
                )}`
              );
            } else {
              // Skip the min/max fields since we already handled them above
              if (!key.match(/^loan_to_(value|cost)_(min|max)$/)) {
                extra[key] = criteriaObj[key];
                console.log(
                  `→ MAPPED TO CRITERIA EXTRA_FIELDS: ${key} = ${JSON.stringify(
                    criteriaObj[key]
                  )}`
                );
              }
            }
          }

          core.extra_fields = JSON.stringify(extra);
          // Remove criteria_id if present (auto-incremented)
          delete core.criteria_id;

          console.log(
            "FINAL CRITERIA CORE FIELDS:",
            JSON.stringify(core, null, 2)
          );

          const cols = Object.keys(core);
          const vals = Object.values(core);
          const phs = cols.map((_, i) => `$${i + 1}`).join(", ");
          const sql = `INSERT INTO public.investment_criteria (${cols.join(
            ", "
          )}) VALUES (${phs}) RETURNING criteria_id`;

          console.log("CRITERIA INSERT SQL:", sql);
          console.log("CRITERIA INSERT VALUES:", vals);

          const criteriaResult = await pool.query(sql, vals);
          console.log(
            "✓ INSERTED CRITERIA ID:",
            criteriaResult.rows[0]?.criteria_id
          );
        }
      }
    }
    // --- End investment criteria insert ---
  }

  return dealId;
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const files: File[] = [];
    const fileTypes: string[] = [];
    let contactId: string | null = null;
    const contactEmail: string | null = null;
    let createdBy: string | null = null;

    // Extract contact_id if provided
    const contactIdForm = formData.get("contact_id");
    if (
      contactIdForm &&
      typeof contactIdForm === "string" &&
      contactIdForm.trim()
    ) {
      contactId = contactIdForm.trim();
    }

    // Extract contact_emails (multi-select) or contact_email (single) if provided
    const contactEmailsForm = formData.get("contact_emails");
    const contactEmailForm = formData.get("contact_email");
    
    let contactEmails: string[] = [];
    
    if (contactEmailsForm && typeof contactEmailsForm === "string") {
      try {
        contactEmails = JSON.parse(contactEmailsForm);
      } catch (error) {
        console.error("Error parsing contact_emails:", error);
      }
    } else if (
      contactEmailForm &&
      typeof contactEmailForm === "string" &&
      contactEmailForm.trim()
    ) {
      // Fallback to single contact email for backward compatibility
      contactEmails = [contactEmailForm.trim()];
    }

    // Extract created_by if provided
    const createdByForm = formData.get("created_by");
    if (
      createdByForm &&
      typeof createdByForm === "string" &&
      createdByForm.trim()
    ) {
      createdBy = createdByForm.trim();
    }

    // Extract llm_model if provided
    const llmModelForm = formData.get("llm_model");
    let llmModel:
      | "gemini-flash"
      | "gemini-pro"
      | "openai-4o"
      | "openai-4o-mini"
      | "openai-o1-preview"
      | "openai-o1-mini" = "gemini-flash"; // Default to gemini-flash
    if (
      llmModelForm &&
      typeof llmModelForm === "string" &&
      (llmModelForm === "gemini-flash" ||
        llmModelForm === "gemini-pro" ||
        llmModelForm === "openai-4o" ||
        llmModelForm === "openai-4o-mini" ||
        llmModelForm === "openai-o1-preview" ||
        llmModelForm === "openai-o1-mini")
    ) {
      llmModel = llmModelForm;
    }

    // Debug logging to track model selection
    console.log("🔍 API Route - Model Selection Debug:");
    console.log("  - Received llm_model from form:", llmModelForm);
    console.log("  - Final llmModel value:", llmModel);

    // Extract files
    let index = 0;
    while (formData.has(`file_${index}`)) {
      const file = formData.get(`file_${index}`) as File;
      const fileType = formData.get(`file_type_${index}`) as string;

      if (file) {
        files.push(file);
        fileTypes.push(fileType || "document");
      }
      index++;
    }

    if (files.length === 0) {
      return NextResponse.json({ error: "No files provided" }, { status: 400 });
    }

    // Handle contact lookup by emails if provided
    const contactIds: number[] = [];
    console.log(`📧 Processing contact emails:`, contactEmails);
    
    if (contactEmails.length > 0 && !contactId) {
      try {
        console.log(`🔍 Looking up ${contactEmails.length} contacts by email`);
        
        // Look up all contacts by their emails
        for (const email of contactEmails) {
          console.log(`🔎 Searching for contact with email: ${email}`);
          const contactResult = await pool.query(
            "SELECT contact_id FROM public.contacts WHERE email = $1 OR personal_email = $1 LIMIT 1",
            [email]
          );

          if (contactResult.rows.length > 0) {
            const foundContactId = contactResult.rows[0].contact_id;
            contactIds.push(foundContactId);
            console.log(`✅ Found contact ${foundContactId} for email ${email}`);
          } else {
            console.warn(`⚠️ Contact with email ${email} not found in contacts table`);
          }
        }
        
        console.log(`📋 Resolved contact IDs:`, contactIds);
        
        // For backward compatibility, use the first contact as the primary contact
        if (contactIds.length > 0) {
          contactId = contactIds[0].toString();
          console.log(`🏷️ Set primary contact ID: ${contactId}`);
        }
      } catch (error) {
        console.error("❌ Error looking up contacts by email:", error);
        return NextResponse.json(
          { error: "Failed to look up contacts by email" },
          { status: 500 }
        );
      }
    } else if (contactEmail && !contactId) {
      // Fallback for single contact email
      try {
        const contactResult = await pool.query(
          "SELECT contact_id FROM public.contacts WHERE email = $1 OR personal_email = $1 LIMIT 1",
          [contactEmail]
        );

        if (contactResult.rows.length > 0) {
          contactId = contactResult.rows[0].contact_id;
        } else {
          return NextResponse.json(
            {
              error: `Contact with email ${contactEmail} not found in contacts table`,
            },
            { status: 400 }
          );
        }
      } catch (error) {
        console.error("Error looking up contact by email:", error);
        return NextResponse.json(
          { error: "Failed to look up contact by email" },
          { status: 500 }
        );
      }
    }

    // Validate contact_id if provided
    if (contactId) {
      const contactCheck = await pool.query(
        "SELECT contact_id FROM public.contacts WHERE contact_id = $1",
        [parseInt(contactId)]
      );

      if (contactCheck.rows.length === 0) {
        return NextResponse.json(
          { error: `Contact with ID ${contactId} not found in contacts table` },
          { status: 400 }
        );
      }
    }

    // Convert files to base64 buffers for the job
    const fileBuffers = await Promise.all(
      files.map(async (file, index) => {
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        return {
          buffer: buffer.toString("base64"),
          mimeType: file.type,
          fileName: file.name,
          fileType: fileTypes[index] || "document",
        };
      })
    );

    // Create job data
    const jobData: DealProcessingJobData = {
      files: fileBuffers,
      contactId: contactId || undefined,
      contactIds: contactIds.length > 0 ? contactIds : undefined, // Add multiple contact IDs
      contactEmail: contactEmail || undefined,
      createdBy: createdBy || undefined,
      llmModel: llmModel, // Pass the selected model
      jobMetadata: {
        uploadTimestamp: new Date().toISOString(),
        originalFileNames: files.map((f) => f.name),
        fileCount: files.length,
      },
    };
    
    console.log(`📦 Created job data:`, {
      contactId: jobData.contactId,
      contactIds: jobData.contactIds,
      contactEmail: jobData.contactEmail,
      fileCount: jobData.jobMetadata.fileCount,
      llmModel: jobData.llmModel
    });

    // Add job to queue
    const jobId = await bullMQManager.addDealProcessingJob(jobData, {
      priority: 1,
      createdBy: createdBy || "anonymous",
    });

    // Return success response with job ID
    return NextResponse.json({
      success: true,
      jobId,
      message: "Files uploaded successfully and processing started",
      fileCount: files.length,
      fileNames: files.map((f) => f.name),
      estimatedProcessingTime: files.length * 30, // seconds
    });
  } catch (error) {
    console.error("Error in upload-simplified:", error);
    
    // Provide more specific error messages
    let errorMessage = "Failed to upload files";
    let errorDetails = error instanceof Error ? error.message : "Unknown error";
    
    if (errorDetails.includes("duplicate key value violates unique constraint")) {
      errorMessage = "Duplicate job detected - please try again";
      errorDetails = "A job with the same ID already exists. This may be due to a retry or duplicate request.";
    } else if (errorDetails.includes("BullMQ Manager not initialized")) {
      errorMessage = "Job queue system is not available";
      errorDetails = "The background processing system is not properly initialized.";
    } else if (errorDetails.includes("Contact with email")) {
      errorMessage = "Contact not found";
      errorDetails = errorDetails;
    }
    
    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        details: errorDetails,
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check recent uploads
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = parseInt(searchParams.get("offset") || "0");
    const createdBy = searchParams.get("created_by") || undefined;

    // Get recent uploads
    const uploads = await bullMQManager.getRecentUploads(
      limit,
      offset,
      createdBy
    );

    return NextResponse.json({
      success: true,
      uploads,
      pagination: {
        limit,
        offset,
        count: uploads.length,
      },
    });
  } catch (error) {
    console.error("Error getting recent uploads:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to get recent uploads",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
