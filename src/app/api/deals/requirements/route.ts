import { NextRequest, NextResponse } from "next/server";
import { generateDealRequirementPromptWithText } from "@/lib/prompts/deal-requirement";
import { LLMFactory, createProcessorLoggerAdapter } from "@/lib/llm";

export async function POST(request: NextRequest) {
  try {
    const { text, model = "gemini-flash" } = await request.json();

    if (!text) {
      return NextResponse.json(
        { error: "Text content is required" },
        { status: 400 }
      );
    }

    // Create LLM provider
    const loggerAdapter = createProcessorLoggerAdapter(console.log);
    const llmProvider = LLMFactory.createProvider(model, loggerAdapter);

    // Generate prompt with the provided text
    const prompt = generateDealRequirementPromptWithText(text);

    // Create messages for LLM
    const messages = [
      {
        role: "system" as const,
        content: prompt,
      },
    ];

    // Call LLM
    const response = await llmProvider.chat(messages);

    if (!response.success) {
      return NextResponse.json(
        { error: "Failed to process with LLM", details: response.error },
        { status: 500 }
      );
    }

    // Parse the JSON response
    let parsedResponse;
    try {
      parsedResponse = JSON.parse(response.content);
    } catch (parseError) {
      return NextResponse.json(
        { 
          error: "Failed to parse LLM response as JSON", 
          rawResponse: response.content,
          parseError: parseError instanceof Error ? parseError.message : "Unknown parse error"
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: parsedResponse,
      model: model,
      prompt: prompt,
    });

  } catch (error) {
    console.error("Error in deal requirements extraction:", error);
    return NextResponse.json(
      { 
        error: "Internal server error", 
        details: error instanceof Error ? error.message : "Unknown error" 
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: "Deal Requirements API",
    description: "POST endpoint for extracting capital requirements from deal documents",
    usage: {
      method: "POST",
      body: {
        text: "string - The deal document text to analyze",
        model: "string (optional) - LLM model to use (default: gemini-flash)"
      },
      response: {
        success: "boolean",
        data: "object - Extracted capital requirements",
        model: "string - Model used for extraction",
        prompt: "string - Prompt used for extraction"
      }
    }
  });
} 