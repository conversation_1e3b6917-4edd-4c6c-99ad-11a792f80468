import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;

    // Get deals with quality metrics
    const dealsQuery = `
      SELECT 
        d.deal_id,
        d.deal_name,
        d.sponsor_name,
        d.status,
        d.deal_stage,
        d.priority,
        -- Calculate deal quality score
        ROUND(
          (COUNT(CASE WHEN d.deal_name IS NOT NULL AND d.deal_name != '' THEN 1 END) +
           COUNT(CASE WHEN d.sponsor_name IS NOT NULL AND d.sponsor_name != '' THEN 1 END) +
           COUNT(CASE WHEN d.status IS NOT NULL AND d.status != '' THEN 1 END) +
           COUNT(CASE WHEN d.deal_stage IS NOT NULL AND d.deal_stage != '' THEN 1 END) +
           COUNT(CASE WHEN d.priority IS NOT NULL AND d.priority != '' THEN 1 END) +
           COUNT(CASE WHEN d.zip_code IS NOT NULL AND d.zip_code != '' THEN 1 END) +
           COUNT(CASE WHEN d.property_description IS NOT NULL AND d.property_description != '' THEN 1 END) +
           COUNT(CASE WHEN d.lot_area IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN d.floor_area_ratio IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN d.zoning_square_footage IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN d.yield_on_cost IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN d.projected_gp_equity_multiple IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN d.projected_gp_irr IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN d.projected_lp_equity_multiple IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN d.projected_lp_irr IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN d.projected_total_equity_multiple IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN d.projected_total_irr IS NOT NULL THEN 1 END)) * 100.0 / 17
        ) as deal_quality,
        -- Count investment criteria
        COALESCE(ic.criteria_count, 0) as criteria_count,
        -- Calculate criteria quality score
        COALESCE(ic.criteria_quality, 0) as criteria_quality
      FROM deals d
      LEFT JOIN (
        SELECT 
          entity_id::int as deal_id,
          COUNT(*) as criteria_count,
          ROUND(
            (SUM(CASE WHEN target_return IS NOT NULL THEN 1 ELSE 0 END) +
             SUM(CASE WHEN minimum_deal_size IS NOT NULL THEN 1 ELSE 0 END) +
             SUM(CASE WHEN maximum_deal_size IS NOT NULL THEN 1 ELSE 0 END) +
             SUM(CASE WHEN historical_irr IS NOT NULL THEN 1 ELSE 0 END) +
             SUM(CASE WHEN historical_em IS NOT NULL THEN 1 ELSE 0 END) +
             SUM(CASE WHEN interest_rate IS NOT NULL THEN 1 ELSE 0 END) +
             SUM(CASE WHEN loan_to_value_max IS NOT NULL THEN 1 ELSE 0 END) +
             SUM(CASE WHEN loan_to_cost_max IS NOT NULL THEN 1 ELSE 0 END) +
             SUM(CASE WHEN min_hold_period IS NOT NULL THEN 1 ELSE 0 END) +
             SUM(CASE WHEN max_hold_period IS NOT NULL THEN 1 ELSE 0 END) +
             SUM(CASE WHEN min_loan_term IS NOT NULL THEN 1 ELSE 0 END) +
             SUM(CASE WHEN max_loan_term IS NOT NULL THEN 1 ELSE 0 END) +
             SUM(CASE WHEN closing_time_weeks IS NOT NULL THEN 1 ELSE 0 END) +
             SUM(CASE WHEN property_types IS NOT NULL AND array_length(property_types, 1) > 0 THEN 1 ELSE 0 END) +
             SUM(CASE WHEN property_sub_categories IS NOT NULL AND array_length(property_sub_categories, 1) > 0 THEN 1 ELSE 0 END) +
             SUM(CASE WHEN strategies IS NOT NULL AND array_length(strategies, 1) > 0 THEN 1 ELSE 0 END) +
             SUM(CASE WHEN financial_products IS NOT NULL AND array_length(financial_products, 1) > 0 THEN 1 ELSE 0 END) +
             SUM(CASE WHEN country IS NOT NULL AND array_length(country, 1) > 0 THEN 1 ELSE 0 END) +
             SUM(CASE WHEN region IS NOT NULL AND array_length(region, 1) > 0 THEN 1 ELSE 0 END) +
             SUM(CASE WHEN state IS NOT NULL AND array_length(state, 1) > 0 THEN 1 ELSE 0 END) +
             SUM(CASE WHEN city IS NOT NULL AND array_length(city, 1) > 0 THEN 1 ELSE 0 END) +
             SUM(CASE WHEN loan_program IS NOT NULL AND array_length(loan_program, 1) > 0 THEN 1 ELSE 0 END) +
             SUM(CASE WHEN loan_type IS NOT NULL AND array_length(loan_type, 1) > 0 THEN 1 ELSE 0 END)) * 100.0 / (23 * COUNT(*))
          ) as criteria_quality
        FROM investment_criteria 
        WHERE entity_type = 'Deal'
        GROUP BY entity_id
      ) ic ON d.deal_id = ic.deal_id
      GROUP BY d.deal_id, d.deal_name, d.sponsor_name, d.status, d.deal_stage, d.priority, ic.criteria_count, ic.criteria_quality
      ORDER BY d.deal_id DESC
      LIMIT $1 OFFSET $2
    `;

    // Get total count for pagination
    const countQuery = `SELECT COUNT(*) as total FROM deals`;

    const [dealsResult, countResult] = await Promise.all([
      pool.query(dealsQuery, [limit, offset]),
      pool.query(countQuery)
    ]);

    const deals = dealsResult.rows.map(deal => {
      const dealQuality = deal.deal_quality || 0;
      const criteriaQuality = deal.criteria_quality || 0;
      const overallQuality = Math.round((dealQuality + criteriaQuality) / 2);

      return {
        deal_id: deal.deal_id,
        deal_name: deal.deal_name,
        sponsor_name: deal.sponsor_name,
        status: deal.status,
        deal_stage: deal.deal_stage,
        priority: deal.priority,
        quality_metrics: {
          deal_quality: dealQuality,
          criteria_quality: criteriaQuality,
          overall_quality: overallQuality,
          criteria_count: deal.criteria_count
        }
      };
    });

    const totalCount = parseInt(countResult.rows[0]?.total || '0');
    const totalPages = Math.ceil(totalCount / limit);

    // Calculate summary statistics
    const summaryStats = deals.reduce((acc, deal) => {
      const quality = deal.quality_metrics.overall_quality;
      
      if (quality >= 90) acc.excellent++;
      else if (quality >= 80) acc.good++;
      else if (quality >= 70) acc.fair++;
      else acc.poor++;
      
      acc.totalQuality += quality;
      acc.totalCriteria += deal.quality_metrics.criteria_count;
      
      return acc;
    }, {
      excellent: 0,
      good: 0,
      fair: 0,
      poor: 0,
      totalQuality: 0,
      totalCriteria: 0
    });

    const averageQuality = deals.length > 0 ? Math.round(summaryStats.totalQuality / deals.length) : 0;

    const response = {
      deals,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      },
      summary: {
        total_deals: deals.length,
        average_quality: averageQuality,
        quality_distribution: {
          excellent: summaryStats.excellent,
          good: summaryStats.good,
          fair: summaryStats.fair,
          poor: summaryStats.poor
        },
        total_criteria: summaryStats.totalCriteria,
        average_criteria_per_deal: deals.length > 0 ? Math.round(summaryStats.totalCriteria / deals.length) : 0
      }
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching deals quality summary:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 