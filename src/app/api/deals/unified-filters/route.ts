import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

interface FilterGroup {
  name: string;
  filters: Filter[];
}

interface Filter {
  name: string;
  type: "range" | "multiselect" | "text";
  options?: string[];
  min?: number;
  max?: number;
  unit?: string;
  hierarchicalMappings?: { [key: string]: any };
}

interface CentralMappingData {
  [key: string]: {
    flat: string[];
    hierarchical?: { [key: string]: string[] };
  };
}

export async function GET(request: NextRequest) {
  try {
    console.log("Starting unified deal filters API...");

    const { searchParams } = new URL(request.url);

    // Parse pagination parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "25");
    const offset = (page - 1) * limit;
    const sortBy = searchParams.get("sortBy") || "updated_at";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    // Parse filter parameters
    const searchTerm = searchParams.get("searchTerm");
    const dealName = searchParams.get("dealName");
    const sponsorName = searchParams.get("sponsorName");
    const dealSizeMin = searchParams.get("dealSizeMin")
      ? parseFloat(searchParams.get("dealSizeMin")!)
      : null;
    const dealSizeMax = searchParams.get("dealSizeMax")
      ? parseFloat(searchParams.get("dealSizeMax")!)
      : null;
    const targetReturnMin = searchParams.get("targetReturnMin")
      ? parseFloat(searchParams.get("targetReturnMin")!) / 100
      : null;
    const targetReturnMax = searchParams.get("targetReturnMax")
      ? parseFloat(searchParams.get("targetReturnMax")!) / 100
      : null;
    const yieldOnCostMin = searchParams.get("yieldOnCostMin")
      ? parseFloat(searchParams.get("yieldOnCostMin")!) / 100
      : null;
    const yieldOnCostMax = searchParams.get("yieldOnCostMax")
      ? parseFloat(searchParams.get("yieldOnCostMax")!) / 100
      : null;
    const projectedTotalIrrMin = searchParams.get("projectedTotalIrrMin")
      ? parseFloat(searchParams.get("projectedTotalIrrMin")!) / 100
      : null;
    const projectedTotalIrrMax = searchParams.get("projectedTotalIrrMax")
      ? parseFloat(searchParams.get("projectedTotalIrrMax")!) / 100
      : null;

    // Array filters
    const matchType = searchParams
      .get("matchType")
      ?.split(",")
      .filter((v) => v);
    const status = searchParams
      .get("status")
      ?.split(",")
      .filter((v) => v);
    const dealStage = searchParams
      .get("dealStage")
      ?.split(",")
      .filter((v) => v);
    const priority = searchParams
      .get("priority")
      ?.split(",")
      .filter((v) => v);
    const propertyTypes = searchParams
      .get("propertyTypes")
      ?.split(",")
      .filter((v) => v);
    const strategies = searchParams
      .get("strategies")
      ?.split(",")
      .filter((v) => v);
    const countries = searchParams
      .get("countries")
      ?.split(",")
      .filter((v) => v);
    const regions = searchParams
      .get("regions")
      ?.split(",")
      .filter((v) => v);
    const states = searchParams
      .get("states")
      ?.split(",")
      .filter((v) => v);
    const cities = searchParams
      .get("cities")
      ?.split(",")
      .filter((v) => v);
    const capitalPositions = searchParams
      .get("capitalPositions")
      ?.split(",")
      .filter((v) => v);
    const llmProvider = searchParams
      .get("llmProvider")
      ?.split(",")
      .filter((v) => v);

    const client = await pool.connect();

    // Build WHERE conditions
    const whereConditions: string[] = ["ic.is_active = true"];
    const queryParams: any[] = [];
    let paramCounter = 1;

    // Search term filter (searches deal name, sponsor name, and capital position)
    if (searchTerm) {
      whereConditions.push(`(
        d.deal_name ILIKE $${paramCounter} OR 
        d.sponsor_name ILIKE $${paramCounter} OR
        ic.capital_position::text ILIKE $${paramCounter}
      )`);
      queryParams.push(`%${searchTerm}%`);
      paramCounter++;
    }

    // Deal name filter
    if (dealName) {
      whereConditions.push(`d.deal_name ILIKE $${paramCounter}`);
      queryParams.push(`%${dealName}%`);
      paramCounter++;
    }

    // Sponsor name filter
    if (sponsorName) {
      whereConditions.push(`d.sponsor_name ILIKE $${paramCounter}`);
      queryParams.push(`%${sponsorName}%`);
      paramCounter++;
    }

    // Deal size filters
    if (dealSizeMin !== null) {
      whereConditions.push(
        `(ic.minimum_deal_size >= $${paramCounter} OR ic.maximum_deal_size >= $${paramCounter})`
      );
      queryParams.push(dealSizeMin);
      paramCounter++;
    }
    if (dealSizeMax !== null) {
      whereConditions.push(
        `(ic.minimum_deal_size <= $${paramCounter} OR ic.maximum_deal_size <= $${paramCounter})`
      );
      queryParams.push(dealSizeMax);
      paramCounter++;
    }

    // Target return filters
    if (targetReturnMin !== null) {
      whereConditions.push(`ic.target_return >= $${paramCounter}`);
      queryParams.push(targetReturnMin);
      paramCounter++;
    }
    if (targetReturnMax !== null) {
      whereConditions.push(`ic.target_return <= $${paramCounter}`);
      queryParams.push(targetReturnMax);
      paramCounter++;
    }

    // Yield on cost filters
    if (yieldOnCostMin !== null) {
      whereConditions.push(`d.yield_on_cost >= $${paramCounter}`);
      queryParams.push(yieldOnCostMin);
      paramCounter++;
    }
    if (yieldOnCostMax !== null) {
      whereConditions.push(`d.yield_on_cost <= $${paramCounter}`);
      queryParams.push(yieldOnCostMax);
      paramCounter++;
    }

    // Projected total IRR filters
    if (projectedTotalIrrMin !== null) {
      whereConditions.push(`d.projected_total_irr >= $${paramCounter}`);
      queryParams.push(projectedTotalIrrMin);
      paramCounter++;
    }
    if (projectedTotalIrrMax !== null) {
      whereConditions.push(`d.projected_total_irr <= $${paramCounter}`);
      queryParams.push(projectedTotalIrrMax);
      paramCounter++;
    }

    // Array filters
    if (matchType && matchType.length > 0) {
      whereConditions.push(`d.match_type = ANY($${paramCounter})`);
      queryParams.push(matchType);
      paramCounter++;
    }
    if (status && status.length > 0) {
      whereConditions.push(`d.status = ANY($${paramCounter})`);
      queryParams.push(status);
      paramCounter++;
    }
    if (dealStage && dealStage.length > 0) {
      whereConditions.push(`d.deal_stage = ANY($${paramCounter})`);
      queryParams.push(dealStage);
      paramCounter++;
    }
    if (priority && priority.length > 0) {
      whereConditions.push(`d.priority = ANY($${paramCounter})`);
      queryParams.push(priority);
      paramCounter++;
    }
    if (propertyTypes && propertyTypes.length > 0) {
      whereConditions.push(`ic.property_types && $${paramCounter}`);
      queryParams.push(propertyTypes);
      paramCounter++;
    }
    if (strategies && strategies.length > 0) {
      whereConditions.push(`ic.strategies && $${paramCounter}`);
      queryParams.push(strategies);
      paramCounter++;
    }
    if (countries && countries.length > 0) {
      whereConditions.push(`ic.country && $${paramCounter}`);
      queryParams.push(countries);
      paramCounter++;
    }
    if (regions && regions.length > 0) {
      whereConditions.push(`ic.region && $${paramCounter}`);
      queryParams.push(regions);
      paramCounter++;
    }
    if (states && states.length > 0) {
      whereConditions.push(`ic.state && $${paramCounter}`);
      queryParams.push(states);
      paramCounter++;
    }
    if (cities && cities.length > 0) {
      whereConditions.push(`ic.city && $${paramCounter}`);
      queryParams.push(cities);
      paramCounter++;
    }
    if (capitalPositions && capitalPositions.length > 0) {
      whereConditions.push(`ic.capital_position && $${paramCounter}`);
      queryParams.push(capitalPositions);
      paramCounter++;
    }
    if (llmProvider && llmProvider.length > 0) {
      whereConditions.push(`d.llm_provider = ANY($${paramCounter})`);
      queryParams.push(llmProvider);
      paramCounter++;
    }

    const whereClause =
      whereConditions.length > 0
        ? `WHERE ${whereConditions.join(" AND ")}`
        : "";

    console.log("Applied filters:", {
      dealName,
      sponsorName,
      dealSizeMin,
      dealSizeMax,
      targetReturnMin,
      targetReturnMax,
    });

    // Build ORDER BY clause
    const getSortColumn = (sortBy: string): string => {
      const sortMap: { [key: string]: string } = {
        deal_name: "d.deal_name",
        sponsor_name: "d.sponsor_name",
        created_at: "d.created_at",
        updated_at: "d.updated_at",
        extraction_timestamp: "d.extraction_timestamp",
        yield_on_cost: "d.yield_on_cost",
        projected_total_irr: "d.projected_total_irr",
        deal_stage: "d.deal_stage",
        status: "d.status",
        priority: "d.priority",
        // Investment criteria sorts
        target_return: "ic.target_return",
        historical_irr: "ic.historical_irr",
        minimum_deal_size: "ic.minimum_deal_size",
        maximum_deal_size: "ic.maximum_deal_size",
      };
      return sortMap[sortBy] || "d.updated_at";
    };

    const orderClause = `ORDER BY ${getSortColumn(
      sortBy
    )} ${sortOrder.toUpperCase()}`;

    // Add pagination parameters to the end
    queryParams.push(limit, offset);
    const limitParam = paramCounter;
    const offsetParam = paramCounter + 1;

    // Count query - get total number of deals with investment criteria
    const countQuery = `
      SELECT COUNT(DISTINCT d.deal_id) as total
      FROM deals d
      INNER JOIN investment_criteria ic ON ic.entity_id = d.deal_id::text AND ic.entity_type = 'Deal'
      ${whereClause}
    `;

    // Main data query - return actual deals with investment criteria summary
    const dataQuery = `
      SELECT DISTINCT ON (d.deal_id)
        d.deal_id,
        d.deal_name,
        d.sponsor_name,
        d.contact_id,
        d.match_type,
        d.status,
        d.deal_stage,
        d.priority,
        d.zip_code,
        d.neighborhood,
        d.property_description,
        d.lot_area,
        d.floor_area_ratio,
        d.zoning_square_footage,
        d.yield_on_cost,
        d.projected_gp_equity_multiple,
        d.projected_gp_irr,
        d.projected_lp_equity_multiple,
        d.projected_lp_irr,
        d.projected_total_equity_multiple,
        d.projected_total_irr,
        d.document_type,
        d.extraction_confidence,
        d.processing_notes,
        d.extraction_timestamp,
        d.processor_version,
        d.llm_model_used,
        d.llm_provider,
        d.extraction_method,
        d.document_source,
        d.document_filename,
        d.document_size_bytes,
        d.processing_duration_ms,
        d.review_status,
        d.reviewed_by,
        d.reviewed_at,
        d.review_notes,
        d.created_at,
        d.updated_at,
        
        -- Investment criteria summary (first matching criteria)
        ic.criteria_id,
        ic.entity_type as ic_entity_type,
        ic.target_return,
        ic.minimum_deal_size,
        ic.maximum_deal_size,
        ic.historical_irr,
        ic.historical_em,
        ic.min_hold_period,
        ic.max_hold_period,
        ic.capital_source,
        ic.min_loan_term,
        ic.max_loan_term,
        ic.interest_rate,
        ic.loan_to_value_min,
        ic.loan_to_value_max,
        ic.loan_to_cost_min,
        ic.loan_to_cost_max,
        ic.min_loan_dscr,
        ic.max_loan_dscr,
        ic.is_active as ic_is_active,
        ic.created_at as ic_created_at,
        ic.updated_at as ic_updated_at
        
      FROM deals d
      INNER JOIN investment_criteria ic ON ic.entity_id = d.deal_id::text AND ic.entity_type = 'Deal'
      ${whereClause}
      ORDER BY d.deal_id, ${getSortColumn(sortBy)} ${sortOrder.toUpperCase()}
      LIMIT $${limitParam} OFFSET $${offsetParam}
    `;

    // console.log("Count query:", countQuery);
    // console.log("Data query:", dataQuery);
    // console.log("Query params:", queryParams);

    // Execute queries
    const countParams = queryParams.slice(0, -2); // Remove limit and offset for count query
    const [countResult, dataResult] = await Promise.all([
      client.query(countQuery, countParams),
      client.query(dataQuery, queryParams),
    ]);

    client.release();

    const totalCount = parseInt(countResult.rows[0].total || "0");
    const totalPages = Math.ceil(totalCount / limit);

    // Extract deal IDs for bulk quality calculation
    const dealIds = dataResult.rows.map(row => row.deal_id);

    // Optimized bulk quality calculation with better performance
    const bulkQualityQuery = `
      WITH deal_quality AS (
        SELECT 
          deal_id,
          ROUND(
            (
              -- Use boolean arithmetic for better performance
              (deal_name IS NOT NULL AND deal_name != '')::int +
              (sponsor_name IS NOT NULL AND sponsor_name != '')::int +
              (status IS NOT NULL AND status != '')::int +
              (deal_stage IS NOT NULL AND deal_stage != '')::int +
              (priority IS NOT NULL AND priority != '')::int +
              (zip_code IS NOT NULL AND zip_code != '')::int +
              (property_description IS NOT NULL AND property_description != '')::int +
              (lot_area IS NOT NULL)::int +
              (floor_area_ratio IS NOT NULL)::int +
              (zoning_square_footage IS NOT NULL)::int +
              (yield_on_cost IS NOT NULL)::int +
              (projected_gp_equity_multiple IS NOT NULL)::int +
              (projected_gp_irr IS NOT NULL)::int +
              (projected_lp_equity_multiple IS NOT NULL)::int +
              (projected_lp_irr IS NOT NULL)::int +
              (projected_total_equity_multiple IS NOT NULL)::int +
              (projected_total_irr IS NOT NULL)::int
            ) * 100.0 / 17
          ) as deal_quality
        FROM deals 
        WHERE deal_id = ANY($1)
      ),
      criteria_quality AS (
        SELECT 
          entity_id::int as deal_id,
          ROUND(
            (
              -- Use boolean arithmetic for better performance
              (target_return IS NOT NULL)::int +
              (minimum_deal_size IS NOT NULL)::int +
              (maximum_deal_size IS NOT NULL)::int +
              (historical_irr IS NOT NULL)::int +
              (historical_em IS NOT NULL)::int +
              (interest_rate IS NOT NULL)::int +
              (loan_to_value_max IS NOT NULL)::int +
              (loan_to_cost_max IS NOT NULL)::int +
              (min_hold_period IS NOT NULL)::int +
              (max_hold_period IS NOT NULL)::int +
              (min_loan_term IS NOT NULL)::int +
              (max_loan_term IS NOT NULL)::int +
              (closing_time_weeks IS NOT NULL)::int +
              (property_types IS NOT NULL AND array_length(property_types, 1) > 0)::int +
              (property_sub_categories IS NOT NULL AND array_length(property_sub_categories, 1) > 0)::int +
              (strategies IS NOT NULL AND array_length(strategies, 1) > 0)::int +
              (financial_products IS NOT NULL AND array_length(financial_products, 1) > 0)::int +
              (country IS NOT NULL AND array_length(country, 1) > 0)::int +
              (region IS NOT NULL AND array_length(region, 1) > 0)::int +
              (state IS NOT NULL AND array_length(state, 1) > 0)::int +
              (city IS NOT NULL AND array_length(city, 1) > 0)::int +
              (loan_program IS NOT NULL AND array_length(loan_program, 1) > 0)::int +
              (loan_type IS NOT NULL AND array_length(loan_type, 1) > 0)::int
            ) * 100.0 / 23
          ) as criteria_quality
        FROM investment_criteria 
        WHERE entity_type = 'Deal' AND entity_id::int = ANY($1)
      )
      SELECT 
        COALESCE(dq.deal_id, cq.deal_id) as deal_id,
        COALESCE(dq.deal_quality, 0) as deal_quality,
        COALESCE(cq.criteria_quality, 0) as criteria_quality,
        ROUND((COALESCE(dq.deal_quality, 0) + COALESCE(cq.criteria_quality, 0)) / 2) as overall_quality
      FROM deal_quality dq
      FULL OUTER JOIN criteria_quality cq ON dq.deal_id = cq.deal_id
    `;

    // Execute bulk quality query
    const qualityResult = await pool.query(bulkQualityQuery, [dealIds]);
    
    // Create a map for quick lookup
    const qualityMap = new Map();
    qualityResult.rows.forEach(row => {
      qualityMap.set(row.deal_id, {
        deal_quality: row.deal_quality,
        criteria_quality: row.criteria_quality,
        overall_quality: row.overall_quality
      });
    });

    // Since we're using DISTINCT ON, each row represents one deal with one investment criteria
    // No need for complex grouping logic
    const deals = dataResult.rows.map((row) => {
      // Create the deal object with investment criteria
      const deal = {
        deal_id: row.deal_id,
        deal_name: row.deal_name,
        sponsor_name: row.sponsor_name,
        contact_id: row.contact_id,
        match_type: row.match_type,
        status: row.status,
        deal_stage: row.deal_stage,
        priority: row.priority,
        zip_code: row.zip_code,
        neighborhood: row.neighborhood,
        property_description: row.property_description,
        lot_area: row.lot_area,
        floor_area_ratio: row.floor_area_ratio,
        zoning_square_footage: row.zoning_square_footage,
        yield_on_cost: row.yield_on_cost,
        projected_gp_equity_multiple: row.projected_gp_equity_multiple,
        projected_gp_irr: row.projected_gp_irr,
        projected_lp_equity_multiple: row.projected_lp_equity_multiple,
        projected_lp_irr: row.projected_lp_irr,
        projected_total_equity_multiple: row.projected_total_equity_multiple,
        projected_total_irr: row.projected_total_irr,
        document_type: row.document_type,
        extraction_confidence: row.extraction_confidence,
        processing_notes: row.processing_notes,
        extraction_timestamp: row.extraction_timestamp,
        processor_version: row.processor_version,
        llm_model_used: row.llm_model_used,
        llm_provider: row.llm_provider,
        extraction_method: row.extraction_method,
        document_source: row.document_source,
        document_filename: row.document_filename,
        document_size_bytes: row.document_size_bytes,
        processing_duration_ms: row.processing_duration_ms,
        review_status: row.review_status,
        reviewed_by: row.reviewed_by,
        reviewed_at: row.reviewed_at,
        review_notes: row.review_notes,
        created_at: row.created_at,
        updated_at: row.updated_at,
        investment_criteria: [{
          criteria_id: row.criteria_id,
          entity_type: row.ic_entity_type,
          target_return: row.target_return,
          minimum_deal_size: row.minimum_deal_size,
          maximum_deal_size: row.maximum_deal_size,
          historical_irr: row.historical_irr,
          historical_em: row.historical_em,
          min_hold_period: row.min_hold_period,
          max_hold_period: row.max_hold_period,
          capital_source: row.capital_source,
          min_loan_term: row.min_loan_term,
          max_loan_term: row.max_loan_term,
          interest_rate: row.interest_rate,
          loan_to_value_min: row.loan_to_value_min,
          loan_to_value_max: row.loan_to_value_max,
          loan_to_cost_min: row.loan_to_cost_min,
          loan_to_cost_max: row.loan_to_cost_max,
          min_loan_dscr: row.min_loan_dscr,
          max_loan_dscr: row.max_loan_dscr,
          is_active: row.ic_is_active,
          created_at: row.ic_created_at,
          updated_at: row.ic_updated_at,
        }]
      };

      // Get quality metrics from the map
      const qualityMetrics = qualityMap.get(deal.deal_id) || {
        deal_quality: 0,
        criteria_quality: 0,
        overall_quality: 0
      };

      // Debug logging
      console.log(`UNIFIED-FILTERS API - Deal ${deal.deal_id}:`, {
        deal_quality: qualityMetrics.deal_quality,
        criteria_quality: qualityMetrics.criteria_quality,
        overall_quality: qualityMetrics.overall_quality,
        calculation: `(${qualityMetrics.deal_quality} + ${qualityMetrics.criteria_quality}) / 2 = ${qualityMetrics.overall_quality}`
      });

      // Add quality metrics to the deal
      return {
        ...deal,
        deal_quality: qualityMetrics.deal_quality,
        overall_quality: qualityMetrics.overall_quality,
        investment_criteria: deal.investment_criteria.map((criteria: any) => ({
          ...criteria,
          quality: qualityMetrics.criteria_quality // Use the overall criteria quality for each criteria
        }))
      };
    });

    const result = {
      deals,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
      message: "Unified deal data retrieved successfully",
    };

    console.log(
      `Retrieved ${deals.length} deals out of ${totalCount} total`
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error in unified deal filters:", error);
    return NextResponse.json(
      {
        error: "Failed to retrieve unified deal data",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
