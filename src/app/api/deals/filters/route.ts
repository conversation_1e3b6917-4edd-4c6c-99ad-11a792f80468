import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

// Build hierarchical mappings from central_mapping table (same as other filters)
async function buildHierarchicalMappings() {
  try {
    const mappingsQuery = `
      SELECT type, level_1, value_1, level_2, value_2, level_3, value_3 
      FROM central_mapping 
      WHERE is_active = true 
      ORDER BY type, value_1, value_2, value_3
    `;
    const mappingsResult = await pool.query(mappingsQuery);

    const hierarchicalMappings: Record<string, any> = {};

    mappingsResult.rows.forEach((row) => {
      const { type, level_1, value_1, level_2, value_2, level_3, value_3 } =
        row;

      if (!hierarchicalMappings[type]) {
        hierarchicalMappings[type] = {
          flat: new Set(),
          hierarchical: {},
        };
      }

      if (value_1) {
        hierarchicalMappings[type].flat.add(value_1);

        if (!hierarchicalMappings[type].hierarchical[value_1]) {
          hierarchicalMappings[type].hierarchical[value_1] = {
            value: value_1,
            children: {},
          };
        }

        if (level_2 && value_2) {
          if (
            !hierarchicalMappings[type].hierarchical[value_1].children[level_2]
          ) {
            hierarchicalMappings[type].hierarchical[value_1].children[level_2] =
              new Set();
          }
          hierarchicalMappings[type].hierarchical[value_1].children[
            level_2
          ].add(value_2);
          hierarchicalMappings[type].flat.add(value_2);
        }
      }
    });

    // Convert Sets to Arrays for JSON serialization
    Object.keys(hierarchicalMappings).forEach((type) => {
      hierarchicalMappings[type].flat = Array.from(
        hierarchicalMappings[type].flat
      );

      Object.keys(hierarchicalMappings[type].hierarchical).forEach(
        (parentKey) => {
          const parent = hierarchicalMappings[type].hierarchical[parentKey];
          Object.keys(parent.children).forEach((childType) => {
            parent.children[childType] = Array.from(parent.children[childType]);
          });
        }
      );
    });

    return hierarchicalMappings;
  } catch (error) {
    console.error("Error building hierarchical mappings for deals:", error);
    return {};
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get hierarchical mappings using the same approach as other filters
    const hierarchicalMappings = await buildHierarchicalMappings();

    // Get deal-specific data that's not in central_mapping
    const dealSpecificQueries = {
      matchTypes:
        "SELECT DISTINCT match_type FROM public.deals WHERE match_type IS NOT NULL ORDER BY match_type",
      dealStages:
        "SELECT DISTINCT deal_stage FROM public.deals WHERE deal_stage IS NOT NULL ORDER BY deal_stage",
      statuses:
        "SELECT DISTINCT status FROM public.deals WHERE status IS NOT NULL ORDER BY status",
      priorities:
        "SELECT DISTINCT priority FROM public.deals WHERE priority IS NOT NULL ORDER BY priority",
      reviewStatuses:
        "SELECT DISTINCT review_status FROM public.deals WHERE review_status IS NOT NULL ORDER BY review_status",
      extractionConfidences:
        "SELECT DISTINCT extraction_confidence FROM public.deals WHERE extraction_confidence IS NOT NULL ORDER BY extraction_confidence",
      llmModels:
        "SELECT DISTINCT llm_model_used FROM public.deals WHERE llm_model_used IS NOT NULL ORDER BY llm_model_used",
      llmProviders:
        "SELECT DISTINCT llm_provider FROM public.deals WHERE llm_provider IS NOT NULL ORDER BY llm_provider",
    };

    // Execute deal-specific queries in parallel
    const dealSpecificResults = await Promise.all(
      Object.entries(dealSpecificQueries).map(async ([key, query]) => {
        const result = await pool.query(query);
        return [key, result.rows.map((row) => row[Object.keys(row)[0]])];
      })
    );

    // Convert deal-specific results to object
    const dealSpecificOptions = Object.fromEntries(dealSpecificResults);

    // Build filter options using hierarchical mappings (parent categories only) + deal-specific data
    const filterOptions = {
      // Hierarchical mappings for advanced filtering
      hierarchicalMappings,

      // Use parent categories only for hierarchical types
      propertyTypes: hierarchicalMappings["Property Type"]?.hierarchical
        ? Object.keys(hierarchicalMappings["Property Type"].hierarchical)
        : hierarchicalMappings["Property Type"]?.flat || [],
      capitalTypes: hierarchicalMappings["Capital Position"]?.hierarchical
        ? Object.keys(hierarchicalMappings["Capital Position"].hierarchical)
        : hierarchicalMappings["Capital Position"]?.flat || [],
      regions: hierarchicalMappings["U.S Regions"]?.hierarchical
        ? Object.keys(hierarchicalMappings["U.S Regions"].hierarchical)
        : hierarchicalMappings["U.S Regions"]?.flat || [],

      // Extract subcategories from hierarchical data
      loanTypes: [],
      states: [],
      cities: [],

      // Deal-specific options (from deals table)
      ...dealSpecificOptions,

      // Ranges for numerical filters (with default values)
      ranges: {
        processingDuration: { min: 0, max: 100000 },
        documentSize: { min: 0, max: 10000000 },
        lotArea: { min: 0, max: 1000000 },
        floorAreaRatio: { min: 0, max: 10 },
        zoningSquareFootage: { min: 0, max: 1000000 },
        yieldOnCost: { min: 0, max: 20 },
        projectedGpEquityMultiple: { min: 0, max: 10 },
        projectedGpIrr: { min: 0, max: 50 },
        projectedLpEquityMultiple: { min: 0, max: 10 },
        projectedLpIrr: { min: 0, max: 50 },
        projectedTotalEquityMultiple: { min: 0, max: 10 },
        projectedTotalIrr: { min: 0, max: 50 },
      },
    };

    // Extract loan types from Capital Position hierarchical data
    if (hierarchicalMappings["Capital Position"]?.hierarchical) {
      const loanTypesSet = new Set<string>();
      Object.values(
        hierarchicalMappings["Capital Position"].hierarchical
      ).forEach((parent: any) => {
        if (parent.children["Loan Type"]) {
          parent.children["Loan Type"].forEach((loanType: string) => {
            loanTypesSet.add(loanType);
          });
        }
      });
      filterOptions.loanTypes = Array.from(loanTypesSet).sort();
    }

    // Extract states from U.S Regions hierarchical data
    if (hierarchicalMappings["U.S Regions"]?.hierarchical) {
      const statesSet = new Set<string>();
      Object.values(hierarchicalMappings["U.S Regions"].hierarchical).forEach(
        (parent: any) => {
          if (parent.children["U.S Regions States"]) {
            parent.children["U.S Regions States"].forEach((state: string) => {
              statesSet.add(state);
            });
          }
        }
      );
      filterOptions.states = Array.from(statesSet).sort();
    }

    // Get cities from deals table (fallback since not in central_mapping)
    const citiesQuery =
      "SELECT DISTINCT zip_code FROM public.deals WHERE zip_code IS NOT NULL ORDER BY zip_code";
    const citiesResult = await pool.query(citiesQuery);
    filterOptions.cities = citiesResult.rows.map((row) => row.zip_code);

    // Get neighborhoods from deals table
    const neighborhoodsQuery =
      "SELECT DISTINCT neighborhood FROM public.deals WHERE neighborhood IS NOT NULL ORDER BY neighborhood";
    const neighborhoodsResult = await pool.query(neighborhoodsQuery);
    filterOptions.neighborhoods = neighborhoodsResult.rows.map(
      (row) => row.neighborhood
    );

    // Get range values for numeric filters
    const rangeQueries = {
      processingDuration:
        "SELECT MIN(processing_duration_ms) as min, MAX(processing_duration_ms) as max FROM public.deals WHERE processing_duration_ms IS NOT NULL",
      documentSize:
        "SELECT MIN(document_size_bytes) as min, MAX(document_size_bytes) as max FROM public.deals WHERE document_size_bytes IS NOT NULL",
      lotArea:
        "SELECT MIN(lot_area) as min, MAX(lot_area) as max FROM public.deals WHERE lot_area IS NOT NULL",
      floorAreaRatio:
        "SELECT MIN(floor_area_ratio) as min, MAX(floor_area_ratio) as max FROM public.deals WHERE floor_area_ratio IS NOT NULL",
      zoningSquareFootage:
        "SELECT MIN(zoning_square_footage) as min, MAX(zoning_square_footage) as max FROM public.deals WHERE zoning_square_footage IS NOT NULL",
      yieldOnCost:
        "SELECT MIN(yield_on_cost) as min, MAX(yield_on_cost) as max FROM public.deals WHERE yield_on_cost IS NOT NULL",
      projectedGpEquityMultiple:
        "SELECT MIN(projected_gp_equity_multiple) as min, MAX(projected_gp_equity_multiple) as max FROM public.deals WHERE projected_gp_equity_multiple IS NOT NULL",
      projectedGpIrr:
        "SELECT MIN(projected_gp_irr) as min, MAX(projected_gp_irr) as max FROM public.deals WHERE projected_gp_irr IS NOT NULL",
      projectedLpEquityMultiple:
        "SELECT MIN(projected_lp_equity_multiple) as min, MAX(projected_lp_equity_multiple) as max FROM public.deals WHERE projected_lp_equity_multiple IS NOT NULL",
      projectedLpIrr:
        "SELECT MIN(projected_lp_irr) as min, MAX(projected_lp_irr) as max FROM public.deals WHERE projected_lp_irr IS NOT NULL",
      projectedTotalEquityMultiple:
        "SELECT MIN(projected_total_equity_multiple) as min, MAX(projected_total_equity_multiple) as max FROM public.deals WHERE projected_total_equity_multiple IS NOT NULL",
      projectedTotalIrr:
        "SELECT MIN(projected_total_irr) as min, MAX(projected_total_irr) as max FROM public.deals WHERE projected_total_irr IS NOT NULL",
    };

    const rangeResults = await Promise.all(
      Object.entries(rangeQueries).map(async ([key, query]) => {
        const result = await pool.query(query);
        const row = result.rows[0];
        return [key, { min: row?.min || 0, max: row?.max || 100 }];
      })
    );

    const ranges = Object.fromEntries(rangeResults);

    return NextResponse.json({
      ...filterOptions,
      ranges,
    });
  } catch (error) {
    console.error("Error fetching filter options:", error);
    return NextResponse.json(
      { error: "Failed to fetch filter options" },
      { status: 500 }
    );
  }
}
