import { pool } from '@/lib/db'
import { NextRequest, NextResponse } from 'next/server'

// Interfaces for categorization
interface FormattedCriteriaPosition {
  deal_id: string
  criteria_id: number
  capital_position: string[]
  state: string[]
  deal_size: string
  property_type: string[]
  strategy: string[]
  target_return?: number
  entity_type: string
}

interface CategorizedCriteria {
  debt_positions: FormattedCriteriaPosition[]
  equity_positions: FormattedCriteriaPosition[]
}

export async function POST(request: NextRequest) {
  try {
    const { contactId } = await request.json()
    
    if (!contactId) {
      return NextResponse.json(
        { error: 'contactId is required' },
        { status: 400 }
      )
    }

    console.log(`📊 Processing categorization for contact ${contactId}...`)

    // Use the existing matching API instead of direct database access
    const matchingResponse = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3030'}/api/matching/deals-for-contact/${contactId}?add_ic_data=true&crm_mode=true`)
    
    if (!matchingResponse.ok) {
      console.error(`❌ Matching API failed with status: ${matchingResponse.status}`)
      throw new Error(`Failed to fetch matching deals: ${matchingResponse.status}`)
    }
    
    const matchingData = await matchingResponse.json()
    const matchingDeals = matchingData.matches || []
    
    if (!matchingDeals || matchingDeals.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'No matching deals found',
        data: {
          totalDeals: 0,
          categorizedCriteria: {
            debt_positions: [],
            equity_positions: []
          }
        }
      })
    }

    console.log(`📊 Found ${matchingDeals.length} matching deals`)

    // Extract all matched criteria IDs from the matching deals
    const matchedCriteriaIds = matchingDeals.flatMap((deal: any) => deal.matched_criteria_ids || [])
    // console.log('matchedCriteriaIds', matchedCriteriaIds)
    if (matchedCriteriaIds.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'No matched criteria IDs found',
        data: {
          totalDeals: matchingDeals.length,
          categorizedCriteria: {
            debt_positions: [],
            equity_positions: []
          }
        }
      })
    }

    console.log(`📊 Found ${matchedCriteriaIds.length} matched criteria IDs`)

    // Fetch all Investment Criteria for the matched criteria IDs
    const investmentCriteria = await fetchInvestmentCriteria(matchedCriteriaIds)
    console.log(`📊 Fetched ${investmentCriteria.length} investment criteria records`)
    // console.log('investmentCriteria', JSON.stringify(investmentCriteria.slice(0, 3), null, 2))
    
    // Analyze the results
    const analysis = {
      totalDeals: matchingDeals.length,
      totalMatchedCriteria: matchedCriteriaIds.length,
      totalCriteria: investmentCriteria.length,
    }

    return NextResponse.json({
      success: true,
      message: 'Categorization completed successfully',
      data: {
        analysis,
        categorizedCriteria: investmentCriteria,
      }
    })

  } catch (error) {
    console.error('❌ Error in categorization:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        data: null
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const contactId = searchParams.get('contactId')
  
  if (!contactId) {
    return NextResponse.json(
      { error: 'contactId query parameter is required' },
      { status: 400 }
    )
  }

  // Create a new request object for POST
  const postRequest = new NextRequest('http://localhost/api/deals/contact-email-support/categorization', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ contactId: parseInt(contactId) })
  })

  return POST(postRequest)
}



/**
 * Categorize investment criteria into debt and equity positions
 */
function categorizeInvestmentCriteria(criteria: FormattedCriteriaPosition[]): CategorizedCriteria {
  const debtPositions: FormattedCriteriaPosition[] = []
  const equityPositions: FormattedCriteriaPosition[] = []
  
  // Debt position types
  const debtCapitalPositions = ['Mezzanine', 'Senior Debt', 'Stretch Senior']
  
  criteria.forEach(criterion => {
    // Check if any of the capital positions in the array are debt positions
    const capitalPositions = Array.isArray(criterion.capital_position) ? criterion.capital_position : [criterion.capital_position]
    const hasDebtPosition = capitalPositions.some(pos => debtCapitalPositions.includes(pos))
    
    if (hasDebtPosition) {
      debtPositions.push(criterion)
    } else {
      equityPositions.push(criterion)
    }
  })

  return {
    debt_positions: debtPositions,
    equity_positions: equityPositions
  }
}

/**
 * Fetch investment criteria for a list of criteria IDs
 */
async function fetchInvestmentCriteria(criteriaIds: string[]): Promise<FormattedCriteriaPosition[]> {
  if (criteriaIds.length === 0) {
    return []
  }
  
  // I wannt to get non null maximum_deal_size and target_return first and then the rest
  const investmentCriterias = await pool.query(`
    SELECT 
      ic.criteria_id,
      ic.capital_position,
      ic.state,
      ic.property_types,
      ic.strategies,
      ic.maximum_deal_size as deal_size,
      ic.target_return,
      ic.entity_id as deal_id,
      ic.entity_type
    FROM investment_criteria ic
    WHERE ic.criteria_id = ANY($1)
    ORDER BY ic.maximum_deal_size is not null DESC, ic.target_return is not null DESC, ic.maximum_deal_size DESC, ic.criteria_id ASC
  `, [criteriaIds])
  
  console.log('📊 Query returned:', investmentCriterias.rows.length, 'investment criteria records')
  
  // console.log('investmentCriterias', JSON.stringify(investmentCriterias.rows.slice(0, 3), null, 2))
  const formattedCriteria = investmentCriterias.rows.map((row: any) => {
    // Format deal size
    let dealSizeStr = 'Not specified'
    if (row.deal_size) {
      dealSizeStr = `$${row.deal_size}M`
    }
    
    return {
      deal_id: row.deal_id,
      criteria_id: row.criteria_id,
      capital_position: row.capital_position,
      state: row.state || [],
      property_type: row.property_types || [],
      strategy: row.strategies || [],
      deal_size: dealSizeStr,
      target_return: row.target_return,
      entity_type: row.entity_type
    }
  })
  return removeDuplicatePositions(formattedCriteria)
}

/**
 * Remove duplicate positions with similar properties
 */
function removeDuplicatePositions(positions: FormattedCriteriaPosition[]): FormattedCriteriaPosition[] {
  const seen = new Set<string>()
  return positions.filter(position => {
    // Create a unique key based on deal_id, criteria_id, capital position, states, and property types
    const key = JSON.stringify({
      capital_position: position.capital_position,
      deal_id: position.deal_id,
    })
    
    if (seen.has(key)) {
      return false
    }
    
    seen.add(key)
    return true
  })
} 