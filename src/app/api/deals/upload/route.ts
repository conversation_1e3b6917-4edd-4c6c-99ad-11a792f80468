import { NextResponse } from "next/server";
import { DealProcessor } from "@/lib/processors/DealProcessor";
import { writeFileSync, mkdirSync, existsSync } from "fs";
import { join } from "path";
import {
  extractionMap,
  DEAL_EXTRACTION_USER_TEMPLATE_FUNCTION,
  UNIVERSAL_DEAL_EXTRACTION_USER_TEMPLATE_FUNCTION,
} from "@/lib/prompts/deal-extraction";
import { GeminiProvider, createProcessorLoggerAdapter } from "@/lib/llm";
import * as XLSX from "xlsx";
import { pool } from "@/lib/db";
// import { DealProcessor } from "@/lib/processors/DealProcessor";
// export const runtime = "nodejs";
// const dealProcessor = new DealProcessor();

// Rate limiting helper
const rateLimiter = {
  lastRequest: 0,
  minInterval: 1000, // 1 second between requests
};

// Retry helper with exponential backoff
async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error: any) {
      if (attempt === maxRetries) {
        throw error;
      }

      // Check if it's a rate limit error
      const isRateLimit =
        error.message?.includes("too many requests") ||
        error.message?.includes("rate limit") ||
        error.message?.includes("quota exceeded");

      if (isRateLimit) {
        const delay = baseDelay * Math.pow(2, attempt); // Exponential backoff
        console.log(
          `Rate limit hit, retrying in ${delay}ms (attempt ${attempt + 1}/${
            maxRetries + 1
          })`
        );
        await new Promise((resolve) => setTimeout(resolve, delay));
      } else {
        // For non-rate-limit errors, don't retry
        throw error;
      }
    }
  }
  throw new Error("Max retries exceeded");
}

// Convert XLSX to CSV (all sheets)
function convertXlsxToCsv(buffer: Buffer): {
  csvContent: string;
  csvBuffer: Buffer;
  sheetInfo: any[];
} {
  try {
    // Read the Excel file
    const workbook = XLSX.read(buffer, { type: "buffer" });

    const sheetInfo = [];
    let combinedCsvContent = "";

    // Process all sheets
    for (let i = 0; i < workbook.SheetNames.length; i++) {
      const sheetName = workbook.SheetNames[i];
      const worksheet = workbook.Sheets[sheetName];

      // Convert sheet to CSV
      const csvContent = XLSX.utils.sheet_to_csv(worksheet);

      // Add sheet info
      sheetInfo.push({
        sheetName: sheetName,
        sheetIndex: i,
        rowCount: XLSX.utils.sheet_to_json(worksheet).length,
        columnCount: Object.keys(worksheet).filter((key) => !key.includes("!"))
          .length,
        csvLength: csvContent.length,
      });

      // Add sheet header and content to combined CSV
      if (i > 0) {
        combinedCsvContent += "\n\n"; // Add spacing between sheets
      }
      combinedCsvContent += `=== SHEET: ${sheetName} ===\n`;
      combinedCsvContent += csvContent;
    }

    const csvBuffer = Buffer.from(combinedCsvContent, "utf8");

    return {
      csvContent: combinedCsvContent,
      csvBuffer,
      sheetInfo,
    };
  } catch (error) {
    throw new Error(`Failed to convert XLSX to CSV: ${error}`);
  }
}

// Convert Excel to text format for Gemini
function convertExcelToText(buffer: Buffer): {
  textContent: string;
  textBuffer: Buffer;
  sheetInfo: any[];
} {
  try {
    // Read the Excel file
    const workbook = XLSX.read(buffer, { type: "buffer" });

    const sheetInfo = [];
    let combinedTextContent = "";

    // Process all sheets
    for (let i = 0; i < workbook.SheetNames.length; i++) {
      const sheetName = workbook.SheetNames[i];
      const worksheet = workbook.Sheets[sheetName];

      // Convert sheet to JSON for better text representation
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
      }) as any[][];

      // Add sheet info
      sheetInfo.push({
        sheetName: sheetName,
        sheetIndex: i,
        rowCount: jsonData.length,
        columnCount: jsonData.length > 0 ? jsonData[0].length : 0,
      });

      // Add sheet header and content to combined text
      if (i > 0) {
        combinedTextContent += "\n\n"; // Add spacing between sheets
      }
      combinedTextContent += `=== SHEET: ${sheetName} ===\n`;

      // Convert JSON data to readable text format
      if (jsonData.length > 0) {
        // Get headers from first row
        const headers = jsonData[0] || [];
        combinedTextContent += `Headers: ${headers.join(" | ")}\n\n`;

        // Add data rows
        for (let rowIndex = 1; rowIndex < jsonData.length; rowIndex++) {
          const row = jsonData[rowIndex] || [];
          const rowText = row
            .map((cell: any) => String(cell || ""))
            .join(" | ");
          combinedTextContent += `Row ${rowIndex}: ${rowText}\n`;
        }
      }
    }

    const textBuffer = Buffer.from(combinedTextContent, "utf8");

    return {
      textContent: combinedTextContent,
      textBuffer,
      sheetInfo,
    };
  } catch (error) {
    throw new Error(`Failed to convert Excel to text: ${error}`);
  }
}

export async function POST(request: Request) {
  try {
    console.log("Upload route hit!");

    const formData = await request.formData();
    const mode = (formData.get("mode") as string) || "file";
    const fileCount = parseInt(formData.get("file_count") as string) || 1;

    // Collect all files
    const files: Array<{ file: File; type: string; index: number }> = [];

    for (let i = 0; i < fileCount; i++) {
      const file = formData.get(`file_${i}`) as File;
      const fileType = (formData.get(`file_type_${i}`) as string) || "document";

      if (file) {
        files.push({ file, type: fileType, index: i });
      }
    }

    if (files.length === 0) {
      return NextResponse.json({ error: "No files provided" }, { status: 400 });
    }

    console.log(
      `Processing ${files.length} files:`,
      files.map((f) => ({ name: f.file.name, type: f.file.type }))
    );

    // Process each file and prepare for LLM
    const processedFiles: Array<{
      buffer: Buffer;
      mimeType: string;
      fileName: string;
      fileType: string;
      sheetInfo?: any[];
    }> = [];

    for (const { file, type, index } of files) {
      // Read the file buffer
      const buffer = Buffer.from(await file.arrayBuffer());

      // Process Excel files - convert to text format for Gemini
      let processedBuffer: Buffer = buffer;
      let processedFileName = file.name;
      let processedFileType = file.type;
      let sheetInfo: any[] = [];

      if (
        file.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.name.toLowerCase().endsWith(".xlsx")
      ) {
        try {
          // Convert Excel to text format for Gemini
          const {
            textContent,
            textBuffer,
            sheetInfo: excelSheetInfo,
          } = convertExcelToText(buffer);

          processedBuffer = textBuffer;
          processedFileName = file.name.replace(/\.xlsx$/i, ".txt");
          processedFileType = "text/plain";
          sheetInfo = excelSheetInfo;

          console.log(
            `Converted Excel file ${file.name} to text format with ${sheetInfo.length} sheets:`,
            sheetInfo.map((s) => s.sheetName).join(", ")
          );
          console.log("Sheet details:", sheetInfo);
          console.log("Text content length:", textContent.length);
          console.log(
            "First 500 characters of converted text:",
            textContent.substring(0, 500)
          );

          // Log a sample of the converted text structure
          if (textContent.length > 0) {
            const lines = textContent.split("\n").slice(0, 10);
            console.log("Sample of converted text structure:");
            lines.forEach((line, index) => {
              console.log(`Line ${index + 1}: ${line}`);
            });
          }
        } catch (error) {
          console.error("Error converting Excel file to text:", error);
          // Continue with original file if conversion fails
          console.log("Falling back to original Excel file format");
          processedBuffer = buffer;
          processedFileName = file.name;
          processedFileType = file.type;
          // Try to get basic sheet info for logging
          try {
            const workbook = XLSX.read(buffer, { type: "buffer" });
            sheetInfo = workbook.SheetNames.map((sheetName, i) => ({
              sheetName: sheetName,
              sheetIndex: i,
              rowCount: 0, // We can't get this without conversion
              columnCount: 0,
            }));
            console.log(
              `Fallback: Excel file has ${workbook.SheetNames.length} sheets:`,
              workbook.SheetNames.join(", ")
            );
          } catch (fallbackError) {
            console.error("Error getting fallback sheet info:", fallbackError);
          }
        }
      } else if (
        file.type === "application/vnd.ms-excel" ||
        file.name.toLowerCase().endsWith(".xls")
      ) {
        // Handle legacy Excel format
        try {
          const {
            textContent,
            textBuffer,
            sheetInfo: excelSheetInfo,
          } = convertExcelToText(buffer);

          processedBuffer = textBuffer;
          processedFileName = file.name.replace(/\.xls$/i, ".txt");
          processedFileType = "text/plain";
          sheetInfo = excelSheetInfo;

          console.log(
            `Converted legacy Excel file ${file.name} to text format with ${sheetInfo.length} sheets:`,
            sheetInfo.map((s) => s.sheetName).join(", ")
          );
          console.log("Sheet details:", sheetInfo);
          console.log("Text content length:", textContent.length);
        } catch (error) {
          console.error("Error converting legacy Excel file to text:", error);
          // Continue with original file if conversion fails
          console.log("Falling back to original legacy Excel file format");
          processedBuffer = buffer;
          processedFileName = file.name;
          processedFileType = file.type;
          // Try to get basic sheet info for logging
          try {
            const workbook = XLSX.read(buffer, { type: "buffer" });
            sheetInfo = workbook.SheetNames.map((sheetName, i) => ({
              sheetName: sheetName,
              sheetIndex: i,
              rowCount: 0, // We can't get this without conversion
              columnCount: 0,
            }));
            console.log(
              `Fallback: Legacy Excel file has ${workbook.SheetNames.length} sheets:`,
              workbook.SheetNames.join(", ")
            );
          } catch (fallbackError) {
            console.error("Error getting fallback sheet info:", fallbackError);
          }
        }
      }

      processedFiles.push({
        buffer: processedBuffer,
        mimeType: processedFileType,
        fileName: processedFileName,
        fileType: type,
        sheetInfo: sheetInfo,
      });
    }

    // Prepare the universal prompt
    const excelFiles = processedFiles.filter(
      (f) => f.sheetInfo && f.sheetInfo.length > 0
    );
    const otherFiles = processedFiles.filter(
      (f) => !f.sheetInfo || f.sheetInfo.length === 0
    );

    let documentText = `Multiple documents will be uploaded directly to the API. `;

    if (excelFiles.length > 0) {
      documentText += `Excel files converted to text: ${excelFiles
        .map(
          (f) =>
            `${f.fileName} (${f.sheetInfo?.length || 0} sheets: ${
              f.sheetInfo?.map((s) => s.sheetName).join(", ") || "unknown"
            })`
        )
        .join(", ")}. `;
    }

    if (otherFiles.length > 0) {
      documentText += `Other files: ${otherFiles
        .map((f) => f.fileType)
        .join(", ")}. `;
    }

    const systemPrompt = UNIVERSAL_DEAL_EXTRACTION_USER_TEMPLATE_FUNCTION({
      mappingObject: extractionMap,
      documentText: documentText,
    });

    // Create Gemini provider
    const loggerAdapter = createProcessorLoggerAdapter(
      (level: string, message: string) => {
        console.log(`[${level}] ${message}`);
      }
    );

    const llmProvider = new GeminiProvider(
      loggerAdapter,
      process.env.GEMINI_API_KEY,
      { temperature: 0.1, maxTokens: 8000 }
    );

    // Rate limiting
    const now = Date.now();
    const timeSinceLastRequest = now - rateLimiter.lastRequest;
    if (timeSinceLastRequest < rateLimiter.minInterval) {
      const waitTime = rateLimiter.minInterval - timeSinceLastRequest;
      console.log(`Rate limiting: waiting ${waitTime}ms before next request`);
      await new Promise((resolve) => setTimeout(resolve, waitTime));
    }
    rateLimiter.lastRequest = Date.now();

    // Prepare messages for Gemini with multiple files
    const messages: any[] = [
      {
        role: "system",
        parts: [systemPrompt],
      },
    ];

    // Add each file as a separate user message part
    for (const processedFile of processedFiles) {
      let fileContext = `Document Type: ${processedFile.fileType}`;

      if (processedFile.sheetInfo && processedFile.sheetInfo.length > 0) {
        fileContext += `\nExcel file converted to text with ${
          processedFile.sheetInfo.length
        } sheets: ${processedFile.sheetInfo
          .map(
            (s) =>
              `${s.sheetName} (${s.rowCount} rows, ${s.columnCount} columns)`
          )
          .join(", ")}`;
      }

      messages.push({
        role: "user",
        parts: [
          fileContext,
          {
            fileBuffer: processedFile.buffer,
            mimeType: processedFile.mimeType,
          },
        ],
      });
    }

    // Call the LLM with multiple files
    const llmResponse = await retryWithBackoff(
      async () => {
        return await llmProvider.callLLMWithFiles(messages);
      },
      3,
      2000
    );

    // Parse the AI response
    let extractedData: Record<string, any> = {};
    let parseError: string | null = null;
    let jsonContent = "";

    try {
      jsonContent = llmResponse.content;

      // Handle markdown-formatted JSON responses
      if (jsonContent.includes("```json")) {
        const jsonMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonContent = jsonMatch[1].trim();
        }
      } else if (jsonContent.includes("```")) {
        const codeMatch = jsonContent.match(/```\s*([\s\S]*?)\s*```/);
        if (codeMatch) {
          jsonContent = codeMatch[1].trim();
        }
      }

      // Clean up any remaining markdown or extra text
      const firstBrace = jsonContent.indexOf("{");
      const lastBrace = jsonContent.lastIndexOf("}");

      if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
        jsonContent = jsonContent.substring(firstBrace, lastBrace + 1);
      }

      jsonContent = jsonContent
        .replace(/^\s*```\s*/, "")
        .replace(/\s*```\s*$/, "")
        .replace(/^[^{]*/, "")
        .replace(/[^}]*$/, "")
        .trim();

      console.log(
        "Attempting to parse JSON:",
        jsonContent.substring(0, 200) + "..."
      );

      extractedData = JSON.parse(jsonContent);
    } catch (err) {
      parseError = `Failed to parse LLM JSON output: ${err}. Raw response: ${llmResponse.content.substring(
        0,
        500
      )}...`;
      console.error("JSON parsing error:", err);
      console.error("Raw response:", llmResponse.content);
      console.error("Cleaned content:", jsonContent);
    }

    // Create output directory if it doesn't exist
    const outputDir = join(process.cwd(), "deal-processing-outputs");
    if (!existsSync(outputDir)) {
      mkdirSync(outputDir, { recursive: true });
    }

    // Generate timestamp for unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const baseFileName =
      files.length === 1
        ? files[0].file.name.replace(/\.[^/.]+$/, "")
        : `multi_docs_${files.length}`;
    const outputFileName = `${baseFileName}_gemini2.0_${timestamp}.json`;

    // Prepare the output data
    const outputData = {
      metadata: {
        timestamp: new Date().toISOString(),
        fileCount: files.length,
        files: files.map((f) => ({
          name: f.file.name,
          size: f.file.size,
          type: f.file.type,
          documentType: f.type,
          lastModified: new Date(f.file.lastModified).toISOString(),
        })),
        processingDuration:
          Date.now() - rateLimiter.lastRequest + rateLimiter.minInterval,
        mode: mode,
        llmProvider: "gemini-2.5-flash",
      },
      input: {
        files: processedFiles.map((f) => ({
          fileName: f.fileName,
          fileType: f.fileType,
          mimeType: f.mimeType,
          size: f.buffer.length,
          sheetInfo: f.sheetInfo,
        })),
        extractionMap: extractionMap,
      },
      output: {
        extractedData: extractedData,
        parseError: parseError,
        rawResponse: llmResponse.content,
        cleanedJson: jsonContent,
      },
    };

    // Save the output to file
    const outputPath = join(outputDir, outputFileName);
    await writeFileSync(
      outputPath,
      JSON.stringify(outputData, null, 2),
      "utf8"
    );

    console.log(`Processing complete. Output saved to: ${outputPath}`);

    // Use DealProcessor to save extracted data to database
    let dbInsertSuccess = false;
    let dbError = null;
    let dealId = null;

    try {
      // Create DealProcessor instance
      const dealProcessor = new DealProcessor();

      // Process the first file (or combine all files if needed)
      const firstFile = processedFiles[0];

      const result = await dealProcessor.processFiles([firstFile]);

      if (result.success) {
        dealId = result.dealId;
        dbInsertSuccess = true;
        console.log(`Data saved to deals table with ID: ${dealId}`);
      } else {
        dbError = result.error;
        console.error("DealProcessor error:", result.error);
      }
    } catch (dbErr) {
      console.error("Database insertion error:", dbErr);
      dbError =
        dbErr instanceof Error ? dbErr.message : "Unknown database error";
    }

    // After deal is inserted, save files and record in DB
    // REMOVED: deal_files logic (directory creation, file writing, DB insert)
    // If you want to save files, use the global FileManager system instead.

    // Return success response
    return NextResponse.json({
      message: `Successfully processed ${files.length} document${
        files.length !== 1 ? "s" : ""
      } using Gemini 2.0 Flash`,
      fileCount: files.length,
      outputFile: outputFileName,
      extractedData: extractedData,
      parseError: parseError,
      databaseSaved: dbInsertSuccess,
      databaseError: dbError,
      dealId: dealId,
    });
  } catch (error: any) {
    console.error("Error in upload route:", error);
    return NextResponse.json(
      { error: error.message || "Internal server error." },
      { status: 500 }
    );
  }
}
