import { NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET() {
  try {
    // Check if deals table exists and has data
    const countQuery = "SELECT COUNT(*) as total FROM public.deals";
    const countResult = await pool.query(countQuery);
    const totalDeals = countResult.rows[0]?.total || 0;

    // Get a few sample deals with all their data
    const sampleQuery = "SELECT * FROM public.deals LIMIT 3";
    const sampleResult = await pool.query(sampleQuery);
    const sampleDeals = sampleResult.rows;

    // Check the structure of the deals table
    const dealsColumnsQuery = `
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'deals' 
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `;
    const dealsColumnsResult = await pool.query(dealsColumnsQuery);

    return NextResponse.json({
      totalDeals: parseInt(totalDeals),
      sampleDeals,
      dealsColumns: dealsColumnsResult.rows,
      message: totalDeals > 0 ? "Deals found" : "No deals in database",
    });
  } catch (error) {
    console.error("Error testing deals table:", error);
    return NextResponse.json(
      {
        error: "Database error",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

export async function POST() {
  return NextResponse.json({ message: "Test POST route working!" });
}
