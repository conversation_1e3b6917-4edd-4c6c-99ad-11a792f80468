import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q");

    if (!query || query.length < 2) {
      return NextResponse.json([]);
    }

    const searchQuery = `
      SELECT 
        deal_id,
        deal_name,
        sponsor,
        property_type,
        capital_type,
        deal_size_min,
        deal_size_max,
        region,
        state,
        city
      FROM public.deals 
      WHERE 
        deal_name ILIKE $1 OR 
        sponsor ILIKE $1 OR 
        property_type ILIKE $1 OR
        capital_type ILIKE $1 OR
        region ILIKE $1 OR
        state ILIKE $1 OR
        city ILIKE $1
      ORDER BY 
        CASE 
          WHEN deal_name ILIKE $1 THEN 1 
          WHEN sponsor ILIKE $1 THEN 2
          WHEN property_type ILIKE $1 THEN 3
          ELSE 4 
        END,
        deal_name
      LIMIT 10
    `;

    const result = await pool.query(searchQuery, [`%${query}%`]);

    return NextResponse.json(result.rows);
  } catch (error) {
    console.error("Error searching deals:", error);
    return NextResponse.json(
      { error: "Failed to search deals" },
      { status: 500 }
    );
  }
}
