import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function POST(request: NextRequest) {
  try {
    const { action, dealData, targetDealId } = await request.json();

    console.log("=== CONFLICT RESOLUTION API ===");
    console.log("Action:", action);
    console.log("Target Deal ID:", targetDealId);
    console.log("Deal Data:", JSON.stringify(dealData, null, 2));

    switch (action) {
      case "replace":
        return handleReplaceDeal(dealData, targetDealId);
      case "create_new":
        return handleCreateNewDeal(dealData);
      default:
        return NextResponse.json(
          { success: false, error: "Invalid action" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Error in conflict resolution:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function handleReplaceDeal(
  dealData: any,
  targetDealId: number
): Promise<NextResponse> {
  const client = await pool.connect();

  try {
    await client.query("BEGIN");

    // Get existing deal to verify it exists
    const existingDealResult = await client.query(
      "SELECT * FROM public.deals WHERE deal_id = $1",
      [targetDealId]
    );

    if (existingDealResult.rows.length === 0) {
      throw new Error(`Deal with ID ${targetDealId} not found`);
    }

    const existingDeal = existingDealResult.rows[0];

    // Mark the old deal as replaced
    const markOldQuery = `
      UPDATE public.deals 
      SET 
        status = 'Replaced',
        processing_notes = COALESCE(processing_notes, '') || $1,
        updated_at = CURRENT_TIMESTAMP
      WHERE deal_id = $2
    `;

    const oldDealNote = `\n[REPLACED ${new Date().toISOString()}] This deal was replaced by a new upload. Original status: ${
      existingDeal.status
    }`;
    await client.query(markOldQuery, [oldDealNote, targetDealId]);

    // Get the deals table columns to filter valid fields
    const columnsResult = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'deals' AND table_schema = 'public'
    `);

    const dealsTableColumns = new Set(
      columnsResult.rows.map((row: { column_name: string }) => row.column_name)
    );

    // Prepare insert data for new deal
    const insertFields: Record<string, any> = {};
    const extraFields: Record<string, any> = {};

    // Separate core fields from extra fields
    Object.keys(dealData).forEach((key) => {
      if (
        key !== "deal_id" &&
        dealData[key] !== null &&
        dealData[key] !== undefined
      ) {
        if (dealsTableColumns.has(key)) {
          insertFields[key] = dealData[key];
        } else {
          extraFields[key] = dealData[key];
        }
      }
    });

    // Add extra fields as JSON
    if (Object.keys(extraFields).length > 0) {
      insertFields.extra_fields = JSON.stringify(extraFields);
    }

    // Add processing notes to indicate this replaced an old deal
    insertFields.processing_notes = JSON.stringify({
      created_at: new Date().toISOString(),
      creation_reason: "duplicate_replacement",
      replaced_deal_id: targetDealId,
      replaced_deal_name: existingDeal.deal_name,
      note: `New deal created to replace deal #${targetDealId}`,
    });

    if (Object.keys(insertFields).length === 0) {
      throw new Error("No valid fields to insert");
    }

    // Build insert query for new deal
    const columns = Object.keys(insertFields);
    const values = Object.values(insertFields);
    const placeholders = columns.map((_, i) => `$${i + 1}`).join(", ");

    const insertQuery = `
      INSERT INTO public.deals (${columns.join(", ")}) 
      VALUES (${placeholders}) 
      RETURNING deal_id
    `;

    console.log("Insert Query:", insertQuery);
    console.log("Insert Values:", values);

    const result = await client.query(insertQuery, values);
    const newDealId = result.rows[0].deal_id;

    await client.query("COMMIT");

    console.log(
      `✓ Deal replaced successfully: Old deal ${targetDealId} marked as replaced, new deal ${newDealId} created`
    );

    return NextResponse.json({
      success: true,
      message: "Deal replaced successfully",
      dealId: newDealId,
      replacedDealId: targetDealId,
      action: "replaced",
    });
  } catch (error) {
    await client.query("ROLLBACK");
    console.error("Error replacing deal:", error);
    throw error;
  } finally {
    client.release();
  }
}

async function handleCreateNewDeal(dealData: any): Promise<NextResponse> {
  const client = await pool.connect();

  try {
    await client.query("BEGIN");

    // Get the deals table columns
    const columnsResult = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'deals' AND table_schema = 'public'
    `);

    const dealsTableColumns = new Set(
      columnsResult.rows.map((row: { column_name: string }) => row.column_name)
    );

    // Prepare insert data
    const insertFields: Record<string, any> = {};
    const extraFields: Record<string, any> = {};

    // Separate core fields from extra fields
    Object.keys(dealData).forEach((key) => {
      if (
        key !== "deal_id" &&
        dealData[key] !== null &&
        dealData[key] !== undefined
      ) {
        if (dealsTableColumns.has(key)) {
          insertFields[key] = dealData[key];
        } else {
          extraFields[key] = dealData[key];
        }
      }
    });

    // Add extra fields as JSON
    if (Object.keys(extraFields).length > 0) {
      insertFields.extra_fields = JSON.stringify(extraFields);
    }

    // Add processing notes to indicate this was created despite duplicates
    insertFields.processing_notes = JSON.stringify({
      created_at: new Date().toISOString(),
      creation_reason: "duplicate_ignored",
      note: "Created despite potential duplicates detected",
    });

    if (Object.keys(insertFields).length === 0) {
      throw new Error("No valid fields to insert");
    }

    // Build insert query
    const columns = Object.keys(insertFields);
    const values = Object.values(insertFields);
    const placeholders = columns.map((_, i) => `$${i + 1}`).join(", ");

    const insertQuery = `
      INSERT INTO public.deals (${columns.join(", ")}) 
      VALUES (${placeholders}) 
      RETURNING deal_id
    `;

    console.log("Insert Query:", insertQuery);
    console.log("Insert Values:", values);

    const result = await client.query(insertQuery, values);

    await client.query("COMMIT");

    console.log("✓ New deal created successfully:", result.rows[0].deal_id);

    return NextResponse.json({
      success: true,
      message: "New deal created successfully",
      dealId: result.rows[0].deal_id,
      action: "created_new",
    });
  } catch (error) {
    await client.query("ROLLBACK");
    console.error("Error creating new deal:", error);
    throw error;
  } finally {
    client.release();
  }
}
