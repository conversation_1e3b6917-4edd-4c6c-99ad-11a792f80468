import { NextResponse } from "next/server";

export const runtime = "nodejs";

export async function POST(request: Request) {
  try {
    console.log("Simple upload route hit!");
    return NextResponse.json({
      message: "Simple upload route working!",
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    console.error("Error in simple upload route:", error);
    return NextResponse.json(
      { error: error.message || "Internal server error." },
      { status: 500 }
    );
  }
}
