import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";
import { bullMQManager } from "@/lib/queue/BullMQManager";

export async function POST(request: NextRequest) {
  try {
    const { jobId, action, targetDealId, extractedData } = await request.json();

    if (!jobId) {
      return NextResponse.json(
        { error: "Job ID is required" },
        { status: 400 }
      );
    }

    if (!action || !["replace", "create_new"].includes(action)) {
      return NextResponse.json(
        { error: "Action must be 'replace' or 'create_new'" },
        { status: 400 }
      );
    }

    // Get the job from the queue
    const job = await bullMQManager.getJobStatus(jobId);
    if (!job) {
      return NextResponse.json(
        { error: "Job not found" },
        { status: 404 }
      );
    }

    const jobData = job.data;

    if (action === "replace" && targetDealId) {
      // Replace existing deal with new data
      const client = await pool.connect();
      try {
        await client.query("BEGIN");

        // Get the deals table columns and types
        const columnsResult = await client.query(`
          SELECT column_name, data_type
          FROM information_schema.columns
          WHERE table_name = 'deals' AND table_schema = 'public'
        `);
        const dealsTableColumns: Record<string, string> = {};
        columnsResult.rows.forEach((row: { column_name: string, data_type: string }) => {
          dealsTableColumns[row.column_name] = row.data_type;
        });

        // Update the existing deal with new data
        const updateFields: Record<string, any> = {};
        const extraFields: Record<string, any> = {};

        // Separate core fields from extra fields, sanitize types
        Object.keys(extractedData).forEach((key) => {
          if (key !== "deal_id" && extractedData[key] !== null && extractedData[key] !== undefined) {
            if (dealsTableColumns[key]) {
              let value = extractedData[key];
              const type = dealsTableColumns[key];
              if (type === "integer") {
                value = parseInt(value, 10);
                if (isNaN(value)) return;
              } else if (type === "numeric" || type === "double precision" || type === "real") {
                value = parseFloat(value);
                if (isNaN(value)) return;
              } else if (type === "boolean") {
                value = Boolean(value);
              }
              updateFields[key] = value;
            } else {
              extraFields[key] = extractedData[key];
            }
          }
        });

        // Add extra fields as JSON
        if (Object.keys(extraFields).length > 0) {
          updateFields.extra_fields = JSON.stringify(extraFields);
        }

        // Add processing notes
        updateFields.processing_notes = JSON.stringify({
          updated_at: new Date().toISOString(),
          update_reason: "duplicate_replaced",
          note: "Updated with new data from duplicate resolution",
          original_job_id: jobId,
        });

        // Build update query
        const updateColumns = Object.keys(updateFields);
        const updateValues = Object.values(updateFields);
        const updatePlaceholders = updateColumns.map((_, i) => `${updateColumns[i]} = $${i + 2}`).join(", ");

        const updateQuery = `
          UPDATE public.deals 
          SET ${updatePlaceholders}, updated_at = NOW()
          WHERE deal_id = $1
          RETURNING deal_id
        `;

        const updateResult = await client.query(updateQuery, [targetDealId, ...updateValues]);

        if (updateResult.rows.length === 0) {
          throw new Error("Failed to update deal");
        }

        // Mark the job as completed
        await bullMQManager.updateJobInDatabase(jobId, {
          status: "completed",
          result: {
            success: true,
            dealId: targetDealId,
            message: "Deal updated successfully",
            action: "replace",
          },
          completed_at: new Date(),
        });

        await client.query("COMMIT");

        return NextResponse.json({
          success: true,
          dealId: targetDealId,
          message: "Deal updated successfully",
          action: "replace",
        });
      } catch (error) {
        await client.query("ROLLBACK");
        throw error;
      } finally {
        client.release();
      }
    } else if (action === "create_new") {
      // Create new deal despite conflicts
      const client = await pool.connect();
      try {
        await client.query("BEGIN");

        // Get the deals table columns and types
        const columnsResult = await client.query(`
          SELECT column_name, data_type
          FROM information_schema.columns
          WHERE table_name = 'deals' AND table_schema = 'public'
        `);
        const dealsTableColumns: Record<string, string> = {};
        columnsResult.rows.forEach((row: { column_name: string, data_type: string }) => {
          dealsTableColumns[row.column_name] = row.data_type;
        });

        // Prepare insert data
        const insertFields: Record<string, any> = {};
        const extraFields: Record<string, any> = {};

        // Separate core fields from extra fields, sanitize types
        Object.keys(extractedData).forEach((key) => {
          if (key !== "deal_id" && extractedData[key] !== null && extractedData[key] !== undefined) {
            if (dealsTableColumns[key]) {
              let value = extractedData[key];
              const type = dealsTableColumns[key];
              if (type === "integer") {
                value = parseInt(value, 10);
                if (isNaN(value)) return;
              } else if (type === "numeric" || type === "double precision" || type === "real") {
                value = parseFloat(value);
                if (isNaN(value)) return;
              } else if (type === "boolean") {
                value = Boolean(value);
              }
              insertFields[key] = value;
            } else {
              extraFields[key] = extractedData[key];
            }
          }
        });

        // Add extra fields as JSON
        if (Object.keys(extraFields).length > 0) {
          insertFields.extra_fields = JSON.stringify(extraFields);
        }

        // Add processing notes to indicate this was created despite duplicates
        insertFields.processing_notes = JSON.stringify({
          created_at: new Date().toISOString(),
          creation_reason: "duplicate_ignored",
          note: "Created despite potential duplicates detected",
          original_job_id: jobId,
        });

        if (Object.keys(insertFields).length === 0) {
          throw new Error("No valid fields to insert");
        }

        // Build insert query
        const columns = Object.keys(insertFields);
        const values = Object.values(insertFields);
        const placeholders = columns.map((_, i) => `$${i + 1}`).join(", ");

        const insertQuery = `
          INSERT INTO public.deals (${columns.join(", ")}) 
          VALUES (${placeholders}) 
          RETURNING deal_id
        `;

        const insertResult = await client.query(insertQuery, values);
        const newDealId = insertResult.rows[0]?.deal_id;

        if (!newDealId) {
          throw new Error("Failed to create new deal");
        }

        // Mark the job as completed
        await bullMQManager.updateJobInDatabase(jobId, {
          status: "completed",
          result: {
            success: true,
            dealId: newDealId,
            message: "New deal created successfully",
            action: "create_new",
          },
          completed_at: new Date(),
        });

        await client.query("COMMIT");

        return NextResponse.json({
          success: true,
          dealId: newDealId,
          message: "New deal created successfully",
          action: "create_new",
        });
      } catch (error) {
        await client.query("ROLLBACK");
        throw error;
      } finally {
        client.release();
      }
    }

    return NextResponse.json(
      { error: "Invalid action" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error resolving job conflicts:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to resolve conflicts",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get("jobId");

    if (!jobId) {
      return NextResponse.json(
        { error: "Job ID is required" },
        { status: 400 }
      );
    }

    // Get the job from the queue
    const job = await bullMQManager.getJobStatus(jobId);
    if (!job) {
      return NextResponse.json(
        { error: "Job not found" },
        { status: 404 }
      );
    }

    const jobData = job.data;
    const jobState = job.status;

    return NextResponse.json({
      success: true,
      jobId,
      jobState,
      jobData,
    });
  } catch (error) {
    console.error("Error getting job conflicts:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to get job conflicts",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
} 