import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get("status") || "pending";
    const result = await pool.query(
      `SELECT conflict_id, deal_id, conflict_data, status, created_at FROM deal_conflicts WHERE status = $1 ORDER BY created_at DESC`,
      [status]
    );
    return NextResponse.json({ success: true, conflicts: result.rows });
  } catch (error) {
    console.error("Error fetching deal conflicts:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch conflicts" },
      { status: 500 }
    );
  }
}
