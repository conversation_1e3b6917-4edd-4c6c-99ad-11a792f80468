import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function POST(request: NextRequest) {
  try {
    const { conflict_id, resolution } = await request.json();
    if (!conflict_id) {
      return NextResponse.json(
        { success: false, error: "Missing conflict_id" },
        { status: 400 }
      );
    }
    await pool.query(
      `UPDATE deal_conflicts SET status = 'resolved', resolved_at = NOW(), conflict_data = jsonb_set(conflict_data, '{resolution}', to_jsonb($1::text), true) WHERE conflict_id = $2`,
      [resolution || "resolved", conflict_id]
    );
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error resolving deal conflict:", error);
    return NextResponse.json(
      { success: false, error: "Failed to resolve conflict" },
      { status: 500 }
    );
  }
}
