import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = parseInt(id);
    console.log("Fetching deal with ID:", dealId);

    if (isNaN(dealId)) {
      console.log("Invalid deal ID:", id);
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    // Fetch the deal
    const dealQuery = `SELECT * FROM public.deals WHERE deal_id = $1`;
    const dealResult = await pool.query(dealQuery, [dealId]);
    if (dealResult.rows.length === 0) {
      console.log("No deal found with ID:", dealId);
      return NextResponse.json({ error: "Deal not found" }, { status: 404 });
    }
    const deal = dealResult.rows[0];

    // Fetch all investment criteria for this deal
    const criteriaQuery = `
      SELECT * FROM public.investment_criteria
      WHERE entity_type = 'Deal' AND entity_id::text = $1
      ORDER BY criteria_id
    `;
    const criteriaResult = await pool.query(criteriaQuery, [dealId]);
    deal.investment_criteria = criteriaResult.rows;

    // Calculate data quality metrics using SQL
    const dealQualityQuery = `
      SELECT 
        ROUND(
          (COUNT(CASE WHEN deal_name IS NOT NULL AND deal_name != '' THEN 1 END) +
           COUNT(CASE WHEN sponsor_name IS NOT NULL AND sponsor_name != '' THEN 1 END) +
           COUNT(CASE WHEN status IS NOT NULL AND status != '' THEN 1 END) +
           COUNT(CASE WHEN deal_stage IS NOT NULL AND deal_stage != '' THEN 1 END) +
           COUNT(CASE WHEN priority IS NOT NULL AND priority != '' THEN 1 END) +
           COUNT(CASE WHEN zip_code IS NOT NULL AND zip_code != '' THEN 1 END) +
           COUNT(CASE WHEN property_description IS NOT NULL AND property_description != '' THEN 1 END) +
           COUNT(CASE WHEN lot_area IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN floor_area_ratio IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN zoning_square_footage IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN yield_on_cost IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN projected_gp_equity_multiple IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN projected_gp_irr IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN projected_lp_equity_multiple IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN projected_lp_irr IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN projected_total_equity_multiple IS NOT NULL THEN 1 END) +
           COUNT(CASE WHEN projected_total_irr IS NOT NULL THEN 1 END)) * 100.0 / 17
        ) as deal_quality
      FROM deals 
      WHERE deal_id = $1
    `;

    const criteriaQualityQuery = `
      SELECT 
        ROUND(
          (SUM(CASE WHEN target_return IS NOT NULL THEN 1 ELSE 0 END) +
           SUM(CASE WHEN minimum_deal_size IS NOT NULL THEN 1 ELSE 0 END) +
           SUM(CASE WHEN maximum_deal_size IS NOT NULL THEN 1 ELSE 0 END) +
           SUM(CASE WHEN historical_irr IS NOT NULL THEN 1 ELSE 0 END) +
           SUM(CASE WHEN historical_em IS NOT NULL THEN 1 ELSE 0 END) +
           SUM(CASE WHEN interest_rate IS NOT NULL THEN 1 ELSE 0 END) +
           SUM(CASE WHEN loan_to_value_max IS NOT NULL THEN 1 ELSE 0 END) +
           SUM(CASE WHEN loan_to_cost_max IS NOT NULL THEN 1 ELSE 0 END) +
           SUM(CASE WHEN min_hold_period IS NOT NULL THEN 1 ELSE 0 END) +
           SUM(CASE WHEN max_hold_period IS NOT NULL THEN 1 ELSE 0 END) +
           SUM(CASE WHEN min_loan_term IS NOT NULL THEN 1 ELSE 0 END) +
           SUM(CASE WHEN max_loan_term IS NOT NULL THEN 1 ELSE 0 END) +
           SUM(CASE WHEN closing_time_weeks IS NOT NULL THEN 1 ELSE 0 END) +
           SUM(CASE WHEN property_types IS NOT NULL AND array_length(property_types, 1) > 0 THEN 1 ELSE 0 END) +
           SUM(CASE WHEN property_sub_categories IS NOT NULL AND array_length(property_sub_categories, 1) > 0 THEN 1 ELSE 0 END) +
           SUM(CASE WHEN strategies IS NOT NULL AND array_length(strategies, 1) > 0 THEN 1 ELSE 0 END) +
           SUM(CASE WHEN financial_products IS NOT NULL AND array_length(financial_products, 1) > 0 THEN 1 ELSE 0 END) +
           SUM(CASE WHEN country IS NOT NULL AND array_length(country, 1) > 0 THEN 1 ELSE 0 END) +
           SUM(CASE WHEN region IS NOT NULL AND array_length(region, 1) > 0 THEN 1 ELSE 0 END) +
           SUM(CASE WHEN state IS NOT NULL AND array_length(state, 1) > 0 THEN 1 ELSE 0 END) +
           SUM(CASE WHEN city IS NOT NULL AND array_length(city, 1) > 0 THEN 1 ELSE 0 END) +
           SUM(CASE WHEN loan_program IS NOT NULL AND array_length(loan_program, 1) > 0 THEN 1 ELSE 0 END) +
           SUM(CASE WHEN loan_type IS NOT NULL AND array_length(loan_type, 1) > 0 THEN 1 ELSE 0 END)) * 100.0 / (23 * COUNT(*))
        ) as criteria_quality
      FROM investment_criteria 
      WHERE entity_type = 'Deal' AND entity_id = $1
    `;

    // Execute both queries
    const [dealQualityResult, criteriaQualityResult] = await Promise.all([
      pool.query(dealQualityQuery, [dealId]),
      pool.query(criteriaQualityQuery, [dealId])
    ]);

    const dealQualityScore = dealQualityResult.rows[0]?.deal_quality || 0;
    const criteriaQualityScore = criteriaQualityResult.rows[0]?.criteria_quality || 0;
    
    // Debug raw SQL results
    console.log(`DEAL-DETAIL API - Deal ${dealId} Raw SQL Results:`, {
      dealQualityRaw: dealQualityResult.rows[0],
      criteriaQualityRaw: criteriaQualityResult.rows[0],
      dealQualityScore,
      criteriaQualityScore
    });
    const overallQuality = Math.round((Number(dealQualityScore) + Number(criteriaQualityScore)) / 2);
    
    // Debug logging
    console.log(`DEAL-DETAIL API - Deal ${dealId}:`, {
      deal_quality: dealQualityScore,
      criteria_quality: criteriaQualityScore,
      overall_quality: overallQuality,
      calculation: `(${dealQualityScore} + ${criteriaQualityScore}) / 2 = ${overallQuality}`,
      criteria_count: deal.investment_criteria.length
    });

    // Add quality metrics to the response
    deal.deal_quality = dealQualityScore;
    deal.overall_quality = overallQuality;
    
    // Fetch detailed quality metrics from the quality API
    try {
      const qualityResponse = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3000'}/api/deals/${dealId}/quality`);
      if (qualityResponse.ok) {
        const qualityData = await qualityResponse.json();
        deal.data_quality_metrics = {
          overview: qualityData.overview,
          debt: qualityData.debt,
          equity: qualityData.equity
        };
        deal.overall_quality = qualityData.overall_quality;
      } else {
        // Fallback to old structure if quality API fails
        deal.data_quality_metrics = {
          overview: {
            qualityScore: dealQualityScore,
            completedFields: Math.round((dealQualityScore / 100) * 17),
            totalFields: 17,
            missingFields: []
          },
          debt: {
            qualityScore: criteriaQualityScore,
            completedFields: Math.round((criteriaQualityScore / 100) * 11),
            totalFields: 11,
            missingFields: [],
            criteriaCount: deal.investment_criteria.length
          },
          equity: {
            qualityScore: criteriaQualityScore,
            completedFields: Math.round((criteriaQualityScore / 100) * 7),
            totalFields: 7,
            missingFields: [],
            criteriaCount: deal.investment_criteria.length
          }
        };
      }
    } catch (error) {
      console.error("Error fetching quality metrics:", error);
      // Fallback to old structure
      deal.data_quality_metrics = {
        overview: {
          qualityScore: dealQualityScore,
          completedFields: Math.round((dealQualityScore / 100) * 17),
          totalFields: 17,
          missingFields: []
        },
        debt: {
          qualityScore: criteriaQualityScore,
          completedFields: Math.round((criteriaQualityScore / 100) * 11),
          totalFields: 11,
          missingFields: [],
          criteriaCount: deal.investment_criteria.length
        },
        equity: {
          qualityScore: criteriaQualityScore,
          completedFields: Math.round((criteriaQualityScore / 100) * 7),
          totalFields: 7,
          missingFields: [],
          criteriaCount: deal.investment_criteria.length
        }
      };
    }

    // Return the deal data with investment_criteria and quality metrics
    return NextResponse.json(deal);
  } catch (error) {
    console.error("Error fetching deal:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = parseInt(id);
    const body = await request.json();

    if (isNaN(dealId)) {
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    // Start a transaction
    await pool.query("BEGIN");

    try {
      // Update deal fields
      const updateDealQuery = `
        UPDATE deals SET
          deal_name = $1,
          sponsor_name = $2,
          status = $3,
          deal_stage = $4,
          priority = $5,
          match_type = $6,
          zip_code = $7,
          neighborhood = $8,
          address = $9,
          property_description = $10,
          lot_area = $11,
          floor_area_ratio = $12,
          zoning_square_footage = $13,
          yield_on_cost = $14,
          projected_gp_equity_multiple = $15,
          projected_gp_irr = $16,
          projected_lp_equity_multiple = $17,
          projected_lp_irr = $18,
          projected_total_equity_multiple = $19,
          projected_total_irr = $20,
          deal_date = $21,
          is_distressed = $22,
          is_internal_only = $23,
          extra_fields = $24,
          updated_at = NOW()
        WHERE deal_id = $25
        RETURNING *
      `;

      const dealValues = [
        body.deal_name,
        body.sponsor_name,
        body.status,
        body.deal_stage,
        body.priority,
        body.match_type,
        body.zip_code,
        body.neighborhood,
        body.address,
        body.property_description,
        body.lot_area,
        body.floor_area_ratio,
        body.zoning_square_footage,
        body.yield_on_cost,
        body.projected_gp_equity_multiple,
        body.projected_gp_irr,
        body.projected_lp_equity_multiple,
        body.projected_lp_irr,
        body.projected_total_equity_multiple,
        body.projected_total_irr,
        body.deal_date,
        body.is_distressed || false,
        body.is_internal_only || false,
        body.extra_fields || null,
        dealId,
      ];

      const dealResult = await pool.query(updateDealQuery, dealValues);

      if (dealResult.rows.length === 0) {
        throw new Error("Deal not found");
      }

      // Handle investment criteria updates
      if (body.investment_criteria && Array.isArray(body.investment_criteria)) {
        // Helper function to ensure array fields are properly formatted
        const transformCriteriaData = (criteria: any) => {
          console.log('=== TRANSFORMING CRITERIA DATA ===');
          console.log('Original criteria:', JSON.stringify(criteria, null, 2));
          
          const transformed = {
            ...criteria,
            property_types: Array.isArray(criteria.property_types) ? criteria.property_types : 
                          (criteria.property_types ? criteria.property_types.split(',').map((s: string) => s.trim()) : []),
            property_sub_categories: Array.isArray(criteria.property_sub_categories) ? criteria.property_sub_categories : 
                                   (criteria.property_sub_categories ? criteria.property_sub_categories.split(',').map((s: string) => s.trim()) : []),
            strategies: Array.isArray(criteria.strategies) ? criteria.strategies : 
                      (criteria.strategies ? criteria.strategies.split(',').map((s: string) => s.trim()) : []),
            financial_products: (() => {
              console.log('=== PROCESSING FINANCIAL_PRODUCTS ===');
              console.log('Original criteria.financial_products:', criteria.financial_products);
              console.log('Type:', typeof criteria.financial_products);
              console.log('Is array:', Array.isArray(criteria.financial_products));
              
              let array;
              
              // Handle different input formats
              if (Array.isArray(criteria.financial_products)) {
                array = criteria.financial_products;
              } else if (typeof criteria.financial_products === 'string') {
                // Check if it's already a JSON string
                try {
                  const parsed = JSON.parse(criteria.financial_products);
                  array = Array.isArray(parsed) ? parsed : [criteria.financial_products];
                } catch {
                  // Not JSON, treat as comma-separated string
                  array = criteria.financial_products.split(',').map((s: string) => s.trim()).filter((s: string) => s.length > 0);
                }
              } else {
                array = [];
              }
              
              console.log('Processed array:', array);
              
              // Convert to PostgreSQL array format for ARRAY of JSONB
              if (array.length === 0) {
                return [];
              }
              // Each element needs to be JSON.stringify'd (matching DealProcessorJob pattern)
              const jsonbArray = array.map((item: string) => JSON.stringify(item));
              console.log('JSONB array result:', jsonbArray);
              console.log('=== END PROCESSING FINANCIAL_PRODUCTS ===');
              
              return jsonbArray;
            })(),
            country: Array.isArray(criteria.country) ? criteria.country : 
                    (criteria.country ? criteria.country.split(',').map((s: string) => s.trim()) : []),
            region: Array.isArray(criteria.region) ? criteria.region : 
                   (criteria.region ? criteria.region.split(',').map((s: string) => s.trim()) : []),
            state: Array.isArray(criteria.state) ? criteria.state : 
                  (criteria.state ? criteria.state.split(',').map((s: string) => s.trim()) : []),
            city: Array.isArray(criteria.city) ? criteria.city : 
                 (criteria.city ? criteria.city.split(',').map((s: string) => s.trim()) : []),
            loan_program: Array.isArray(criteria.loan_program) ? criteria.loan_program : 
                        (criteria.loan_program ? criteria.loan_program.split(',').map((s: string) => s.trim()) : []),
            loan_type: Array.isArray(criteria.loan_type) ? criteria.loan_type : 
                      (criteria.loan_type ? criteria.loan_type.split(',').map((s: string) => s.trim()) : []),
            capital_position: Array.isArray(criteria.capital_position) ? criteria.capital_position : 
                           (criteria.capital_position ? criteria.capital_position.split(',').map((s: string) => s.trim()) : []),
            capital_source: Array.isArray(criteria.capital_source) ? criteria.capital_source : 
                          (criteria.capital_source ? criteria.capital_source.split(',').map((s: string) => s.trim()) : []),
            structured_loan_tranche: Array.isArray(criteria.structured_loan_tranche) ? criteria.structured_loan_tranche : 
                                   (criteria.structured_loan_tranche ? criteria.structured_loan_tranche.split(',').map((s: string) => s.trim()) : []),
            extra_fields: criteria.extra_fields || {}
          };

          // Debug logging
          console.log('Original financial_products:', criteria.financial_products);
          console.log('Transformed financial_products:', transformed.financial_products);
          console.log('Type of financial_products:', typeof transformed.financial_products);

          // Final safety check - ensure all array fields are properly formatted
          const arrayFields = [
            'property_types', 'property_sub_categories', 'strategies',
            'country', 'region', 'state', 'city', 'loan_program', 'loan_type',
            'capital_position', 'capital_source', 'structured_loan_tranche'
          ];

          // Check regular array fields
          arrayFields.forEach(field => {
            if (!Array.isArray(transformed[field])) {
              console.log(`Warning: ${field} is not an array, converting to empty array. Value:`, transformed[field]);
              transformed[field] = [];
            }
          });

          // Check financial_products specifically (ARRAY of JSONB)
          if (!Array.isArray(transformed.financial_products)) {
            console.log(`Warning: financial_products is not an array, converting to empty array. Value:`, transformed.financial_products);
            transformed.financial_products = [];
          }

          console.log('Final transformed criteria:', JSON.stringify(transformed, null, 2));
          console.log('=== END TRANSFORMING CRITERIA DATA ===');

          return transformed;
        };

        // Handle deleted criteria first
        if (body.deleted_criteria_ids && Array.isArray(body.deleted_criteria_ids) && body.deleted_criteria_ids.length > 0) {
          console.log('Deleting criteria IDs:', body.deleted_criteria_ids);
          await pool.query(
            `DELETE FROM investment_criteria WHERE criteria_id = ANY($1) AND entity_type = 'Deal' AND entity_id = $2`,
            [body.deleted_criteria_ids, dealId.toString()]
          );
        }

        // Handle new criteria flag
        if (body.has_new_criteria) {
          console.log('New criteria detected - will create new records for criteria without existing IDs');
        }

        // Get existing criteria for this deal
        const existingCriteriaResult = await pool.query(
          `SELECT criteria_id FROM investment_criteria WHERE entity_type = 'Deal' AND entity_id = $1`,
          [dealId.toString()]
        );

        // Update existing criteria or insert new ones
        for (let i = 0; i < body.investment_criteria.length; i++) {
          const criteria = transformCriteriaData(body.investment_criteria[i]);
          
          // Determine if this is a new criteria or existing one
          let existingCriteriaId = existingCriteriaResult.rows[i]?.criteria_id;
          
          // If has_new_criteria flag is set and criteria_id is 0 or null, treat as new
          if (body.has_new_criteria && (!criteria.criteria_id || criteria.criteria_id === 0)) {
            existingCriteriaId = null; // Force creation of new criteria
            console.log('Treating criteria as new due to has_new_criteria flag');
          }

          if (existingCriteriaId) {
            // Update existing criteria
            const updateCriteriaQuery = `
              UPDATE investment_criteria SET
                target_return = $1,
                property_types = $2,
                property_sub_categories = $3,
                strategies = $4,
                minimum_deal_size = $5,
                maximum_deal_size = $6,
                min_hold_period = $7,
                max_hold_period = $8,
                financial_products = $9,
                historical_irr = $10,
                historical_em = $11,
                country = $12,
                region = $13,
                state = $14,
                city = $15,
                loan_program = $16,
                loan_type = $17,
                capital_position = $18,
                capital_source = $19,
                structured_loan_tranche = $20,
                min_loan_term = $21,
                max_loan_term = $22,
                interest_rate = $23,
                loan_to_value_min = $24,
                loan_to_value_max = $25,
                loan_to_cost_min = $26,
                loan_to_cost_max = $27,
                min_loan_dscr = $28,
                max_loan_dscr = $29,
                recourse_loan = $30,
                extra_fields = $31,
                is_active = $32,
                is_requested = $33,
                include_in_sources = $34,
                updated_at = NOW()
              WHERE criteria_id = $35
            `;

            const updateValues = [
              criteria.target_return,
              criteria.property_types,
              criteria.property_sub_categories,
              criteria.strategies,
              criteria.minimum_deal_size,
              criteria.maximum_deal_size,
              criteria.min_hold_period,
              criteria.max_hold_period,
              criteria.financial_products,
              criteria.historical_irr,
              criteria.historical_em,
              criteria.country,
              criteria.region,
              criteria.state,
              criteria.city,
              criteria.loan_program,
              criteria.loan_type,
              criteria.capital_position,
              criteria.capital_source,
              criteria.structured_loan_tranche,
              criteria.min_loan_term,
              criteria.max_loan_term,
              criteria.interest_rate,
              criteria.loan_to_value_min,
              criteria.loan_to_value_max,
              criteria.loan_to_cost_min,
              criteria.loan_to_cost_max,
              criteria.min_loan_dscr,
              criteria.max_loan_dscr,
              criteria.recourse_loan,
              criteria.extra_fields,
              criteria.is_active !== undefined ? criteria.is_active : true,
              criteria.is_requested !== undefined ? criteria.is_requested : false,
              criteria.include_in_sources !== undefined ? criteria.include_in_sources : true,
              existingCriteriaId
            ];

            // Debug: Log the values being sent to the database
            console.log('Update values being sent to database:');
            console.log('financial_products (param $9):', updateValues[8]);
            console.log('Type of financial_products:', typeof updateValues[8]);
            console.log('Is array:', Array.isArray(updateValues[8]));
            console.log('Raw financial_products value:', JSON.stringify(updateValues[8]));
            console.log('is_requested (param $33):', updateValues[32]);
            console.log('All update values:', updateValues);

            await pool.query(updateCriteriaQuery, updateValues);
          } else {
            // Insert new criteria
            const insertCriteriaQuery = `
              INSERT INTO investment_criteria (
                entity_type,
                entity_id,
                target_return,
                property_types,
                property_sub_categories,
                strategies,
                minimum_deal_size,
                maximum_deal_size,
                min_hold_period,
                max_hold_period,
                financial_products,
                historical_irr,
                historical_em,
                country,
                region,
                state,
                city,
                loan_program,
                loan_type,
                capital_position,
                capital_source,
                structured_loan_tranche,
                min_loan_term,
                max_loan_term,
                interest_rate,
                loan_to_value_min,
                loan_to_value_max,
                loan_to_cost_min,
                loan_to_cost_max,
                min_loan_dscr,
                max_loan_dscr,
                recourse_loan,
                extra_fields,
                is_active,
                is_requested,
                include_in_sources,
                created_at,
                updated_at
              ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20,
                $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38
              )
            `;

            const insertValues = [
              'Deal',
              dealId.toString(),
              criteria.target_return,
              criteria.property_types,
              criteria.property_sub_categories,
              criteria.strategies,
              criteria.minimum_deal_size,
              criteria.maximum_deal_size,
              criteria.min_hold_period,
              criteria.max_hold_period,
              criteria.financial_products,
              criteria.historical_irr,
              criteria.historical_em,
              criteria.country,
              criteria.region,
              criteria.state,
              criteria.city,
              criteria.loan_program,
              criteria.loan_type,
              criteria.capital_position,
              criteria.capital_source,
              criteria.structured_loan_tranche,
              criteria.min_loan_term,
              criteria.max_loan_term,
              criteria.interest_rate,
              criteria.loan_to_value_min,
              criteria.loan_to_value_max,
              criteria.loan_to_cost_min,
              criteria.loan_to_cost_max,
              criteria.min_loan_dscr,
              criteria.max_loan_dscr,
              criteria.recourse_loan,
              criteria.extra_fields,
              criteria.is_active !== undefined ? criteria.is_active : true,
              criteria.is_requested !== undefined ? criteria.is_requested : false,
              criteria.include_in_sources !== undefined ? criteria.include_in_sources : true,
              new Date().toISOString(),
              new Date().toISOString()
            ];

            // Debug: Log the values being sent to the database for insert
            console.log('Insert values being sent to database:');
            console.log('financial_products (param $11):', insertValues[10]);
            console.log('Type of financial_products:', typeof insertValues[10]);
            console.log('Is array:', Array.isArray(insertValues[10]));
            console.log('is_requested (param $36):', insertValues[35]);

            await pool.query(insertCriteriaQuery, insertValues);
          }
        }
      }

      // Commit transaction
      await pool.query("COMMIT");

      // Fetch updated deal with criteria
      const updatedDealResult = await pool.query(
        `SELECT 
          d.*,
          c.contact_id,
          c.first_name,
          c.last_name,
          c.email,
          c.title,
          c.phone_number,
          c.linkedin_url
        FROM deals d
        LEFT JOIN contacts c ON d.contact_id = c.contact_id
        WHERE d.deal_id = $1`,
        [dealId]
      );

      const updatedCriteriaResult = await pool.query(
        `SELECT * FROM investment_criteria 
        WHERE entity_type = 'Deal' AND entity_id = $1
        ORDER BY created_at DESC`,
        [dealId.toString()]
      );

      const updatedDeal = {
        ...updatedDealResult.rows[0],
        investment_criteria: updatedCriteriaResult.rows,
      };

      return NextResponse.json(updatedDeal);
    } catch (error) {
      // Rollback transaction on error
      await pool.query("ROLLBACK");
      throw error;
    }
  } catch (error) {
    console.error("Error updating deal:", error);
    return NextResponse.json(
      { error: "Failed to update deal" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const dealId = params.id;

    if (!dealId) {
      return NextResponse.json(
        { error: "Deal ID is required" },
        { status: 400 }
      );
    }

    const client = await pool.connect();

    try {
      // Start transaction
      await client.query("BEGIN");

      // Delete investment criteria first (foreign key constraint)
      const deleteCriteriaQuery = `
        DELETE FROM investment_criteria 
        WHERE entity_type = 'Deal' AND entity_id = $1
      `;
      const criteriaResult = await client.query(deleteCriteriaQuery, [dealId]);

      // Delete the deal
      const deleteDealQuery = `
        DELETE FROM deals 
        WHERE deal_id = $1
      `;
      const dealResult = await client.query(deleteDealQuery, [dealId]);

      // Check if deal was found and deleted
      if (dealResult.rowCount === 0) {
        await client.query("ROLLBACK");
        return NextResponse.json({ error: "Deal not found" }, { status: 404 });
      }

      // Commit transaction
      await client.query("COMMIT");

      console.log(
        `Deleted deal ${dealId} and ${criteriaResult.rowCount} related investment criteria`
      );

      return NextResponse.json({
        message: "Deal deleted successfully",
        deletedDeal: dealId,
        deletedCriteria: criteriaResult.rowCount,
      });
    } catch (error) {
      // Rollback on error
      await client.query("ROLLBACK");
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error("Error deleting deal:", error);
    return NextResponse.json(
      {
        error: "Failed to delete deal",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}


