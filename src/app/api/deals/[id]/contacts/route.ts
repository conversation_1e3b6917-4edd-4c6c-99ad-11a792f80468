import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

// GET: Get all contacts for a deal
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id: dealId } = await context.params;
    
    const sql = `
      SELECT 
        c.contact_id,
        c.first_name,
        c.last_name,
        c.full_name,
        c.email,
        c.personal_email,
        c.title,
        c.phone_number,
        c.linkedin_url,
        c.company_id,
        comp.company_name,
        dc.created_at as added_at
      FROM deal_contacts dc
      INNER JOIN contacts c ON dc.contact_id = c.contact_id
      LEFT JOIN companies comp ON c.company_id = comp.company_id
      WHERE dc.deal_id = $1
      ORDER BY dc.created_at DESC
    `;
    
    const result = await pool.query(sql, [dealId]);
    
    return NextResponse.json({
      contacts: result.rows,
      total: result.rows.length
    });
  } catch (error) {
    console.error("Error fetching deal contacts:", error);
    return NextResponse.json(
      { error: "Failed to fetch deal contacts" },
      { status: 500 }
    );
  }
}

// POST: Add contacts to a deal
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id: dealId } = await context.params;
    const { contactIds } = await req.json();
    
    if (!contactIds || !Array.isArray(contactIds) || contactIds.length === 0) {
      return NextResponse.json(
        { error: "contactIds array is required" },
        { status: 400 }
      );
    }
    
    // Verify the deal exists
    const dealCheck = await pool.query(
      "SELECT deal_id FROM deals WHERE deal_id = $1",
      [dealId]
    );
    
    if (dealCheck.rows.length === 0) {
      return NextResponse.json(
        { error: "Deal not found" },
        { status: 404 }
      );
    }
    
    // Verify all contacts exist
    const contactCheck = await pool.query(
      "SELECT contact_id FROM contacts WHERE contact_id = ANY($1)",
      [contactIds]
    );
    
    if (contactCheck.rows.length !== contactIds.length) {
      return NextResponse.json(
        { error: "One or more contacts not found" },
        { status: 404 }
      );
    }
    
    // Add contacts to deal
    const addedContacts = [];
    for (const contactId of contactIds) {
      try {
        await pool.query(
          "INSERT INTO deal_contacts (deal_id, contact_id) VALUES ($1, $2) ON CONFLICT (deal_id, contact_id) DO NOTHING",
          [dealId, contactId]
        );
        addedContacts.push(contactId);
      } catch (error) {
        console.error(`Error adding contact ${contactId} to deal ${dealId}:`, error);
      }
    }
    
    // Update the primary contact if this is the first contact being added
    const existingContacts = await pool.query(
      "SELECT contact_id FROM deals WHERE deal_id = $1",
      [dealId]
    );
    
    if (!existingContacts.rows[0]?.contact_id && addedContacts.length > 0) {
      await pool.query(
        "UPDATE deals SET contact_id = $1 WHERE deal_id = $2",
        [addedContacts[0], dealId]
      );
    }
    
    return NextResponse.json({
      success: true,
      addedContacts,
      totalAdded: addedContacts.length
    });
  } catch (error) {
    console.error("Error adding contacts to deal:", error);
    return NextResponse.json(
      { error: "Failed to add contacts to deal" },
      { status: 500 }
    );
  }
}

// DELETE: Remove a contact from a deal
export async function DELETE(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id: dealId } = await context.params;
    const { searchParams } = new URL(req.url);
    const contactId = searchParams.get('contactId');
    
    if (!contactId) {
      return NextResponse.json(
        { error: "contactId query parameter is required" },
        { status: 400 }
      );
    }
    
    // Remove the contact from the deal
    await pool.query(
      "DELETE FROM deal_contacts WHERE deal_id = $1 AND contact_id = $2",
      [dealId, contactId]
    );
    
    // If this was the primary contact, update the deals table
    const primaryContactCheck = await pool.query(
      "SELECT contact_id FROM deals WHERE deal_id = $1 AND contact_id = $2",
      [dealId, contactId]
    );
    
    if (primaryContactCheck.rows.length > 0) {
      // Find another contact to set as primary, or set to null
      const remainingContacts = await pool.query(
        "SELECT contact_id FROM deal_contacts WHERE deal_id = $1 ORDER BY created_at ASC LIMIT 1",
        [dealId]
      );
      
      const newPrimaryContactId = remainingContacts.rows.length > 0 
        ? remainingContacts.rows[0].contact_id 
        : null;
      
      await pool.query(
        "UPDATE deals SET contact_id = $1 WHERE deal_id = $2",
        [newPrimaryContactId, dealId]
      );
    }
    
    return NextResponse.json({
      success: true,
      removedContactId: contactId
    });
  } catch (error) {
    console.error("Error removing contact from deal:", error);
    return NextResponse.json(
      { error: "Failed to remove contact from deal" },
      { status: 500 }
    );
  }
} 