import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";
import { BullMQManager } from "@/lib/queue/BullMQManager";
import { processRequirementExtraction } from "@/lib/workers/requirement-extraction-worker";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  console.log("🚨 REQUIREMENT EXTRACTION API CALLED - THIS SHOULD APPEAR IN LOGS");
  try {
    console.log("🔍 Requirement extraction API called");
    const { id } = await params;
    const dealId = parseInt(id);
    console.log(`🔍 Processing deal ID: ${dealId}`);

    if (isNaN(dealId)) {
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    // Check if deal exists
    const dealResult = await pool.query("SELECT * FROM deals WHERE deal_id = $1", [dealId]);
    if (dealResult.rows.length === 0) {
      return NextResponse.json({ error: "Deal not found" }, { status: 404 });
    }

                    // Get all files associated with this deal using direct query
                const filesResult = await pool.query(`
                  SELECT
                    f.file_id,
                    f.file_name,
                    f.original_name,
                    f.title,
                    f.mime_type,
                    f.file_size_bytes,
                    f.metadata,
                    fr.relationship_type,
                    fr.relationship_title,
                    f.uploaded_at
                  FROM files f
                  INNER JOIN file_relationships fr ON f.file_id = fr.file_id
                  WHERE fr.target_table_name = 'deals'
                  AND fr.target_column_name = 'deal_id'
                  AND fr.target_row_id = $1
                  ORDER BY fr.is_primary DESC, fr.display_order ASC, f.uploaded_at DESC
                `, [dealId.toString()]);

    console.log(`🔍 Found ${filesResult.rows.length} files in database for deal ${dealId}`);
    filesResult.rows.forEach((file, index) => {
      console.log(`🔍 File ${index + 1}: ${file.original_name} (${file.file_name}) - ${file.mime_type} - ${file.file_size_bytes} bytes`);
    });

    if (filesResult.rows.length === 0) {
      return NextResponse.json({ 
        error: "No files found for this deal",
        message: "Please upload files to the deal before running requirement extraction"
      }, { status: 400 });
    }

    // Get file content for each file
    const { FileManager } = await import("@/lib/utils/fileManager");
    const filesWithContent = await Promise.all(filesResult.rows.map(async (file: any) => {
      // Get the actual file path from metadata
      const filePath = file.metadata?.file_path || file.file_name;
      console.log(`Fetching file from path: ${filePath} (${file.original_name})`);
      const content = await FileManager.getFileFromDisk(filePath);
      console.log(`File content length: ${content?.length || 0} bytes`);
      
      const fileData = {
        fileId: file.file_id,
        originalName: file.original_name,
        filePath: filePath, // Use the actual file path from metadata
        fileSize: file.file_size_bytes,
        relationshipType: file.relationship_type,
        title: file.title,
        buffer: content ? content.toString('base64') : null, // Pass as base64 like DealProcessor
        mimeType: file.mime_type,
        fileName: file.original_name
      };
      
      console.log(`File data prepared: ${fileData.fileName} - Buffer length: ${fileData.buffer?.length || 0}`);
      return fileData;
    }));
    
    console.log(`Total files fetched: ${filesWithContent.length}`);

    // Create a job record for tracking
    const jobResult = await pool.query(`
      INSERT INTO jobs (
        job_id,
        queue_name,
        job_name,
        status,
        data,
        metadata,
        created_at,
        updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
      RETURNING job_id
    `, [
      `req_extraction_${dealId}_${Date.now()}`, // Generate unique job ID
      'deal-processing',
      'requirement-extraction',
      'waiting',
      JSON.stringify({
        jobId: `req_extraction_${dealId}_${Date.now()}`,
        dealId,
        files: filesWithContent
      }),
      JSON.stringify({
        file_count: filesResult.rows.length,
        processor: 'DealRequirementProcessor',
        description: 'Capital requirement extraction for existing deal files',
        entity_type: 'Deal',
        entity_id: dealId.toString()
      })
    ]);

    const jobId = jobResult.rows[0].job_id;

    // Add job to the existing deal queue
    const queueManager = BullMQManager.getInstance();
    const bullJob = await queueManager.getDealQueue().add('requirement-extraction', {
      databaseJobId: jobId, // Pass the database job ID
      dealId,
      files: filesWithContent,
      timestamp: Date.now(), // Add timestamp to identify fresh jobs
      apiCallTime: new Date().toISOString() // Add API call time
    });

    // Update job record with Bull job ID
    await pool.query(`
      UPDATE jobs 
      SET 
        data = jsonb_set(data, '{bull_job_id}', $1::jsonb),
        updated_at = NOW() 
      WHERE job_id = $2
    `, [JSON.stringify(bullJob.id), jobId]);

    return NextResponse.json({
      success: true,
      job_id: jobId,
      bull_job_id: bullJob.id,
      message: 'Requirement extraction job queued successfully'
    });

  } catch (error) {
    console.error("Error queuing requirement extraction:", error);
    return NextResponse.json(
      { 
        error: "Failed to queue requirement extraction",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
} 