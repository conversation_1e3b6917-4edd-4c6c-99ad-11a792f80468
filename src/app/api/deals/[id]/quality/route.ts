import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = parseInt(id);
    console.log("Fetching deal quality details for ID:", dealId);

    if (isNaN(dealId)) {
      console.log("Invalid deal ID:", id);
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    // Check if deal exists
    const dealCheckQuery = `SELECT deal_id, deal_name FROM public.deals WHERE deal_id = $1`;
    const dealCheckResult = await pool.query(dealCheckQuery, [dealId]);
    if (dealCheckResult.rows.length === 0) {
      console.log("No deal found with ID:", dealId);
      return NextResponse.json({ error: "Deal not found" }, { status: 404 });
    }

    // Calculate detailed deal quality metrics
    const dealQualityQuery = `
      SELECT 
        deal_name,
        sponsor_name,
        status,
        deal_stage,
        priority,
        zip_code,
        property_description,
        lot_area,
        floor_area_ratio,
        zoning_square_footage,
        yield_on_cost,
        projected_gp_equity_multiple,
        projected_gp_irr,
        projected_lp_equity_multiple,
        projected_lp_irr,
        projected_total_equity_multiple,
        projected_total_irr,
        -- Calculate completion for each field
        CASE WHEN deal_name IS NOT NULL AND deal_name != '' THEN 1 ELSE 0 END as deal_name_complete,
        CASE WHEN sponsor_name IS NOT NULL AND sponsor_name != '' THEN 1 ELSE 0 END as sponsor_name_complete,
        CASE WHEN status IS NOT NULL AND status != '' THEN 1 ELSE 0 END as status_complete,
        CASE WHEN deal_stage IS NOT NULL AND deal_stage != '' THEN 1 ELSE 0 END as deal_stage_complete,
        CASE WHEN priority IS NOT NULL AND priority != '' THEN 1 ELSE 0 END as priority_complete,
        CASE WHEN zip_code IS NOT NULL AND zip_code != '' THEN 1 ELSE 0 END as zip_code_complete,
        CASE WHEN property_description IS NOT NULL AND property_description != '' THEN 1 ELSE 0 END as property_description_complete,
        CASE WHEN lot_area IS NOT NULL THEN 1 ELSE 0 END as lot_area_complete,
        CASE WHEN floor_area_ratio IS NOT NULL THEN 1 ELSE 0 END as floor_area_ratio_complete,
        CASE WHEN zoning_square_footage IS NOT NULL THEN 1 ELSE 0 END as zoning_square_footage_complete,
        CASE WHEN yield_on_cost IS NOT NULL THEN 1 ELSE 0 END as yield_on_cost_complete,
        CASE WHEN projected_gp_equity_multiple IS NOT NULL THEN 1 ELSE 0 END as projected_gp_equity_multiple_complete,
        CASE WHEN projected_gp_irr IS NOT NULL THEN 1 ELSE 0 END as projected_gp_irr_complete,
        CASE WHEN projected_lp_equity_multiple IS NOT NULL THEN 1 ELSE 0 END as projected_lp_equity_multiple_complete,
        CASE WHEN projected_lp_irr IS NOT NULL THEN 1 ELSE 0 END as projected_lp_irr_complete,
        CASE WHEN projected_total_equity_multiple IS NOT NULL THEN 1 ELSE 0 END as projected_total_equity_multiple_complete,
        CASE WHEN projected_total_irr IS NOT NULL THEN 1 ELSE 0 END as projected_total_irr_complete
      FROM deals 
      WHERE deal_id = $1
    `;

    // Calculate detailed investment criteria quality metrics
    const criteriaQualityQuery = `
      SELECT 
        criteria_id,
        capital_position,
        target_return,
        minimum_deal_size,
        maximum_deal_size,
        historical_irr,
        historical_em,
        interest_rate,
        loan_to_value_max,
        loan_to_cost_max,
        min_hold_period,
        max_hold_period,
        min_loan_term,
        max_loan_term,
        closing_time_weeks,
        property_types,
        property_sub_categories,
        strategies,
        financial_products,
        country,
        region,
        state,
        city,
        loan_program,
        loan_type,
        -- Calculate completion for each field
        CASE WHEN target_return IS NOT NULL THEN 1 ELSE 0 END as target_return_complete,
        CASE WHEN minimum_deal_size IS NOT NULL THEN 1 ELSE 0 END as minimum_deal_size_complete,
        CASE WHEN maximum_deal_size IS NOT NULL THEN 1 ELSE 0 END as maximum_deal_size_complete,
        CASE WHEN historical_irr IS NOT NULL THEN 1 ELSE 0 END as historical_irr_complete,
        CASE WHEN historical_em IS NOT NULL THEN 1 ELSE 0 END as historical_em_complete,
        CASE WHEN interest_rate IS NOT NULL THEN 1 ELSE 0 END as interest_rate_complete,
        CASE WHEN loan_to_value_max IS NOT NULL THEN 1 ELSE 0 END as loan_to_value_max_complete,
        CASE WHEN loan_to_cost_max IS NOT NULL THEN 1 ELSE 0 END as loan_to_cost_max_complete,
        CASE WHEN min_hold_period IS NOT NULL THEN 1 ELSE 0 END as min_hold_period_complete,
        CASE WHEN max_hold_period IS NOT NULL THEN 1 ELSE 0 END as max_hold_period_complete,
        CASE WHEN min_loan_term IS NOT NULL THEN 1 ELSE 0 END as min_loan_term_complete,
        CASE WHEN max_loan_term IS NOT NULL THEN 1 ELSE 0 END as max_loan_term_complete,
        CASE WHEN closing_time_weeks IS NOT NULL THEN 1 ELSE 0 END as closing_time_weeks_complete,
        CASE WHEN property_types IS NOT NULL AND array_length(property_types, 1) > 0 THEN 1 ELSE 0 END as property_types_complete,
        CASE WHEN property_sub_categories IS NOT NULL AND array_length(property_sub_categories, 1) > 0 THEN 1 ELSE 0 END as property_sub_categories_complete,
        CASE WHEN strategies IS NOT NULL AND array_length(strategies, 1) > 0 THEN 1 ELSE 0 END as strategies_complete,
        CASE WHEN financial_products IS NOT NULL AND array_length(financial_products, 1) > 0 THEN 1 ELSE 0 END as financial_products_complete,
        CASE WHEN country IS NOT NULL AND array_length(country, 1) > 0 THEN 1 ELSE 0 END as country_complete,
        CASE WHEN region IS NOT NULL AND array_length(region, 1) > 0 THEN 1 ELSE 0 END as region_complete,
        CASE WHEN state IS NOT NULL AND array_length(state, 1) > 0 THEN 1 ELSE 0 END as state_complete,
        CASE WHEN city IS NOT NULL AND array_length(city, 1) > 0 THEN 1 ELSE 0 END as city_complete,
        CASE WHEN loan_program IS NOT NULL AND array_length(loan_program, 1) > 0 THEN 1 ELSE 0 END as loan_program_complete,
        CASE WHEN loan_type IS NOT NULL AND array_length(loan_type, 1) > 0 THEN 1 ELSE 0 END as loan_type_complete
      FROM investment_criteria 
      WHERE entity_type = 'Deal' AND entity_id = $1
      ORDER BY criteria_id
    `;

    // Execute both queries
    const [dealQualityResult, criteriaQualityResult] = await Promise.all([
      pool.query(dealQualityQuery, [dealId]),
      pool.query(criteriaQualityQuery, [dealId])
    ]);

    const dealData = dealQualityResult.rows[0];
    const criteriaData = criteriaQualityResult.rows;

    if (!dealData) {
      return NextResponse.json({ error: "Deal not found" }, { status: 404 });
    }

    // Calculate deal completion percentage
    const dealFields = [
      'deal_name', 'sponsor_name', 'status', 'deal_stage', 'priority', 'zip_code',
      'property_description', 'lot_area', 'floor_area_ratio', 'zoning_square_footage',
      'yield_on_cost', 'projected_gp_equity_multiple', 'projected_gp_irr',
      'projected_lp_equity_multiple', 'projected_lp_irr', 'projected_total_equity_multiple',
      'projected_total_irr'
    ];

    const dealFieldDetails = dealFields.map(field => ({
      field,
      hasValue: dealData[`${field}_complete`] === 1,
      value: dealData[field],
      displayName: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }));

    const dealCompletedFields = dealFieldDetails.filter(f => f.hasValue).length;
    const dealQualityScore = Math.round((dealCompletedFields / dealFields.length) * 100);

    // Group criteria by capital position type (debt vs equity)
    const debtCriteria = criteriaData.filter(criteria => 
      criteria.capital_position?.some((pos: string) => 
        pos.includes("Debt") || pos.includes("Senior") || pos.includes("Mezzanine")
      )
    );

    const equityCriteria = criteriaData.filter(criteria => 
      criteria.capital_position?.some((pos: string) => 
        pos.includes("Equity") || pos.includes("Partner") || pos.includes("GP") || pos.includes("LP")
      )
    );

    // Calculate debt section quality
    const debtFields = [
      'target_return', 'minimum_deal_size', 'maximum_deal_size', 'interest_rate', 
      'loan_to_value_max', 'loan_to_cost_max', 'min_loan_term', 'max_loan_term', 
      'closing_time_weeks', 'loan_program', 'loan_type'
    ];

    const debtFieldDetails = debtFields.map(field => {
      const hasValue = debtCriteria.some(criteria => criteria[`${field}_complete`] === 1);
      return {
        field,
        hasValue,
        displayName: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      };
    });

    const debtCompletedFields = debtFieldDetails.filter(f => f.hasValue).length;
    const debtQualityScore = debtFields.length > 0 ? Math.round((debtCompletedFields / debtFields.length) * 100) : 0;

    // Calculate individual debt criteria quality
    const debtIndividualCriteria = debtCriteria.map(criteria => {
      const fieldDetails = debtFields.map(field => ({
        field,
        hasValue: criteria[`${field}_complete`] === 1,
        displayName: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      }));

      const completedFields = fieldDetails.filter(f => f.hasValue).length;
      const qualityScore = Math.round((completedFields / debtFields.length) * 100);

      return {
        criteria_id: criteria.criteria_id,
        capital_position: criteria.capital_position,
        qualityScore,
        completedFields,
        totalFields: debtFields.length,
        missingFields: fieldDetails.filter(f => !f.hasValue).map(f => f.field)
      };
    });

    // Calculate equity section quality
    const equityFields = [
      'target_return', 'minimum_deal_size', 'maximum_deal_size', 'historical_irr', 
      'historical_em', 'min_hold_period', 'max_hold_period'
    ];

    const equityFieldDetails = equityFields.map(field => {
      const hasValue = equityCriteria.some(criteria => criteria[`${field}_complete`] === 1);
      return {
        field,
        hasValue,
        displayName: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      };
    });

    const equityCompletedFields = equityFieldDetails.filter(f => f.hasValue).length;
    const equityQualityScore = equityFields.length > 0 ? Math.round((equityCompletedFields / equityFields.length) * 100) : 0;

    // Calculate individual equity criteria quality
    const equityIndividualCriteria = equityCriteria.map(criteria => {
      const fieldDetails = equityFields.map(field => ({
        field,
        hasValue: criteria[`${field}_complete`] === 1,
        displayName: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      }));

      const completedFields = fieldDetails.filter(f => f.hasValue).length;
      const qualityScore = Math.round((completedFields / equityFields.length) * 100);

      return {
        criteria_id: criteria.criteria_id,
        capital_position: criteria.capital_position,
        qualityScore,
        completedFields,
        totalFields: equityFields.length,
        missingFields: fieldDetails.filter(f => !f.hasValue).map(f => f.field)
      };
    });

    // Calculate overall quality
    const sections = [dealQualityScore, debtQualityScore, equityQualityScore].filter(score => score > 0);
    const overallQuality = sections.length > 0 ? Math.round(sections.reduce((sum, score) => sum + score, 0) / sections.length) : 0;

    const response = {
      deal_id: dealId,
      deal_name: dealData.deal_name,
      overall_quality: overallQuality,
      overview: {
        qualityScore: dealQualityScore,
        completedFields: dealCompletedFields,
        totalFields: dealFields.length,
        fieldDetails: dealFieldDetails,
        missingFields: dealFieldDetails.filter(f => !f.hasValue).map(f => f.field)
      },
      debt: {
        qualityScore: debtQualityScore,
        completedFields: debtCompletedFields,
        totalFields: debtFields.length,
        fieldDetails: debtFieldDetails,
        missingFields: debtFieldDetails.filter(f => !f.hasValue).map(f => f.field),
        criteriaCount: debtCriteria.length,
        individualCriteria: debtIndividualCriteria
      },
      equity: {
        qualityScore: equityQualityScore,
        completedFields: equityCompletedFields,
        totalFields: equityFields.length,
        fieldDetails: equityFieldDetails,
        missingFields: equityFieldDetails.filter(f => !f.hasValue).map(f => f.field),
        criteriaCount: equityCriteria.length,
        individualCriteria: equityIndividualCriteria
      }
    };

    console.log(`DEAL-QUALITY API - Deal ${dealId}:`, {
      overview_quality: dealQualityScore,
      debt_quality: debtQualityScore,
      equity_quality: equityQualityScore,
      overall_quality: overallQuality,
      debt_criteria_count: debtCriteria.length,
      equity_criteria_count: equityCriteria.length
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching deal quality details:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 