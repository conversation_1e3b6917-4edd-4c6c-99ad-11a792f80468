import { NextRequest, NextResponse } from "next/server";
import { FileManager } from "@/lib/utils/fileManager";
import {
  FileUploadRequest,
  FileRelationshipRequest,
  FileProcessingOptions,
  FileUploadResponse,
} from "@/types/file";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = id;

    // Get files for this deal
    const files = await FileManager.getTableFiles({
      table_name: "deals",
      column_name: "deal_id",
      row_id: dealId,
    });

    return NextResponse.json({
      success: true,
      deal_id: dealId,
      files: files,
      count: files.length,
    });
  } catch (error) {
    console.error("Error getting deal files:", error);
    return NextResponse.json(
      { error: "Failed to get deal files" },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = id;

    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json(
        { success: false, message: "No file provided" },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const fileBuffer = Buffer.from(arrayBuffer);

    // Extract upload request data
    const uploadRequest: FileUploadRequest = {
      original_name: file.name,
      title: (formData.get("title") as string) || undefined,
      description: (formData.get("description") as string) || undefined,
      uploaded_by: (formData.get("uploaded_by") as string) || undefined,
      upload_source: (formData.get("upload_source") as string) || "web",
      is_public: formData.get("is_public") === "true",
      access_level:
        (formData.get("access_level") as "private" | "team" | "public") ||
        "private",
      tags: formData.get("tags")
        ? JSON.parse(formData.get("tags") as string)
        : undefined,
      metadata: formData.get("metadata")
        ? JSON.parse(formData.get("metadata") as string)
        : undefined,
      custom_fields: formData.get("custom_fields")
        ? JSON.parse(formData.get("custom_fields") as string)
        : undefined,
    };

    // Extract processing options
    const options: FileProcessingOptions = {
      validate_table_column: formData.get("validate_table_column") === "true",
      allow_duplicates: formData.get("allow_duplicates") === "true",
      auto_generate_title: formData.get("auto_generate_title") === "true",
      preserve_original_name: formData.get("preserve_original_name") === "true",
    };

    // Upload file
    const { file: uploadedFile, isDuplicate } = await FileManager.uploadFile(
      fileBuffer,
      uploadRequest,
      options
    );

    // Create relationship to the deal
    const relationshipRequest: FileRelationshipRequest = {
      target_table_name: "deals",
      target_column_name: "deal_id",
      target_row_id: dealId,
      relationship_type:
        (formData.get("relationship_type") as string) || "attachment",
      relationship_title:
        (formData.get("relationship_title") as string) || undefined,
      relationship_notes:
        (formData.get("relationship_notes") as string) || undefined,
      display_order: formData.get("display_order")
        ? parseInt(formData.get("display_order") as string)
        : undefined,
      is_primary: formData.get("is_primary") === "true",
    };

    const relationship = await FileManager.createFileRelationship(
      uploadedFile.file_id,
      relationshipRequest,
      options
    );

    const response: FileUploadResponse = {
      success: true,
      file: uploadedFile,
      relationship,
      is_duplicate: isDuplicate,
      message: isDuplicate
        ? "File already exists"
        : "File uploaded successfully to deal",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error uploading file to deal:", error);
    return NextResponse.json(
      { success: false, message: "Failed to upload file to deal" },
      { status: 500 }
    );
  }
}
