import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";
import { FileManager } from "@/lib/utils/fileManager";
import { bullMQManager } from "@/lib/queue/BullMQManager";
import { DealProcessor } from "@/lib/processors/DealProcessor";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = parseInt(id);

    if (isNaN(dealId)) {
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    const body = await request.json();
    const { fileIds, selectedFiles } = body;

    // Get the deal to verify it exists
    const dealResult = await pool.query(
      "SELECT * FROM deals WHERE deal_id = $1",
      [dealId]
    );

    if (dealResult.rows.length === 0) {
      return NextResponse.json({ error: "Deal not found" }, { status: 404 });
    }

    // Get files for this deal
    const filesResult = await pool.query(`
      SELECT f.*, fr.relationship_type, fr.is_primary
      FROM files f
      JOIN file_relationships fr ON f.file_id = fr.file_id
      WHERE fr.target_table_name = 'deals' 
      AND fr.target_column_name = 'deal_id' 
      AND fr.target_row_id = $1
      AND f.metadata->>'file_type' NOT IN ('llm_output', 'json')
      ORDER BY fr.display_order, f.uploaded_at
    `, [dealId.toString()]);

    const allFiles = filesResult.rows;
    
    // Determine which files to process
    let filesToProcess = allFiles;
    
    if (selectedFiles && selectedFiles.length > 0) {
      // Process only selected files
      filesToProcess = allFiles.filter(file => 
        selectedFiles.includes(file.file_id)
      );
    } else if (fileIds && fileIds.length > 0) {
      // Process specific file IDs
      filesToProcess = allFiles.filter(file => 
        fileIds.includes(file.file_id)
      );
    }

    if (filesToProcess.length === 0) {
      return NextResponse.json({ 
        error: "No files found to process" 
      }, { status: 400 });
    }

    // Prepare files for processing
    const fileBuffers = await Promise.all(
      filesToProcess.map(async (file) => {
        // Get file content from disk
        const filePath = file.metadata?.file_path;
        if (!filePath) {
          throw new Error(`No file path found for file ${file.file_id}`);
        }

        // Read file from disk (you'll need to implement this based on your storage)
        const fs = require('fs');
        const buffer = fs.readFileSync(filePath);
        
        return {
          buffer: buffer.toString('base64'),
          mimeType: file.mime_type,
          fileName: file.original_name,
          fileId: file.file_id,
        };
      })
    );

    // Create job data
    const jobData = {
      files: fileBuffers,
      dealId: dealId,
      contactId: dealResult.rows[0].contact_id || undefined,
      createdBy: "file_processing",
      llmModel: "gemini-2.5-flash",
      jobMetadata: {
        uploadTimestamp: new Date().toISOString(),
        originalFileNames: filesToProcess.map(f => f.original_name),
        fileCount: filesToProcess.length,
        processingType: "file_reprocessing",
        selectedFiles: selectedFiles || null,
      },
    };

    // Add job to queue
    const jobId = await bullMQManager.addDealProcessingJob(jobData, {
      priority: 1,
      createdBy: "file_processing",
    });

    // Update file metadata to mark as being processed
    for (const file of filesToProcess) {
      await pool.query(`
        UPDATE files 
        SET metadata = jsonb_set(
          jsonb_set(
            jsonb_set(
              COALESCE(metadata, '{}'::jsonb),
              '{processing_status}',
              '"processing"'
            ),
            '{processing_job_id}',
            $1::jsonb
          ),
          '{processing_timestamp}',
          $2::jsonb
        )
        WHERE file_id = $3
      `, [
        JSON.stringify(jobId),
        JSON.stringify(new Date().toISOString()),
        file.file_id
      ]);
    }

    return NextResponse.json({
      success: true,
      jobId,
      message: `Processing ${filesToProcess.length} file(s) for deal ${dealId}`,
      filesProcessed: filesToProcess.length,
      fileNames: filesToProcess.map(f => f.original_name),
    });

  } catch (error) {
    console.error("Error processing deal files:", error);
    return NextResponse.json(
      { error: "Failed to process files" },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = parseInt(id);

    if (isNaN(dealId)) {
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    // Get all files for this deal with processing status
    const filesResult = await pool.query(`
      SELECT 
        f.*,
        fr.relationship_type,
        fr.is_primary,
        fr.display_order,
        f.metadata->>'processing_status' as processing_status,
        f.metadata->>'processing_job_id' as processing_job_id,
        f.metadata->>'processing_timestamp' as processing_timestamp,
        f.metadata->>'file_type' as file_type
      FROM files f
      JOIN file_relationships fr ON f.file_id = fr.file_id
      WHERE fr.target_table_name = 'deals' 
      AND fr.target_column_name = 'deal_id' 
      AND fr.target_row_id = $1
      ORDER BY fr.display_order, f.uploaded_at
    `, [dealId.toString()]);

    // Get jobs for this deal
    const jobsResult = await pool.query(`
      SELECT * FROM jobs 
      WHERE data->>'dealId' = $1 
      OR metadata->>'deal_id' = $1
      ORDER BY created_at DESC
    `, [dealId.toString()]);

    return NextResponse.json({
      files: filesResult.rows,
      jobs: jobsResult.rows,
    });

  } catch (error) {
    console.error("Error fetching deal files:", error);
    return NextResponse.json(
      { error: "Failed to fetch files" },
      { status: 500 }
    );
  }
} 