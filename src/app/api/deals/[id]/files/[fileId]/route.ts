import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

// DELETE /api/deals/[id]/files/[fileId] - Delete a file relationship from a deal
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; fileId: string }> }
) {
  try {
    const { id, fileId } = await params;
    const dealId = parseInt(id);

    if (isNaN(dealId)) {
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    // Delete the file relationship for this specific file and deal
    const result = await pool.query(
      `DELETE FROM file_relationships 
       WHERE file_id = $1 
       AND target_table_name = 'deals' 
       AND target_column_name = 'deal_id' 
       AND target_row_id = $2
       RETURNING relationship_id`,
      [fileId, dealId.toString()]
    );

    if (result.rowCount === 0) {
      return NextResponse.json(
        { success: false, message: "File relationship not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "File relationship deleted successfully",
      relationship_id: result.rows[0].relationship_id,
    });
  } catch (error) {
    console.error("Error deleting file relationship:", error);
    return NextResponse.json(
      { success: false, message: "Failed to delete file relationship" },
      { status: 500 }
    );
  }
} 