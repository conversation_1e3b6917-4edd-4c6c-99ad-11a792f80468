import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = parseInt(id);

    if (isNaN(dealId)) {
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    // Check if deal exists
    const dealResult = await pool.query("SELECT * FROM deals WHERE deal_id = $1", [dealId]);
    if (dealResult.rows.length === 0) {
      return NextResponse.json({ error: "Deal not found" }, { status: 404 });
    }

    // Get all jobs for this deal
    const jobsResult = await pool.query(`
      SELECT 
        job_id,
        queue_name,
        job_name,
        status,
        created_at,
        updated_at,
        started_at,
        completed_at,
        failed_at,
        result,
        error_message,
        metadata,
        processing_duration,
        attempts,
        max_attempts,
        data
      FROM jobs
      WHERE metadata->>'entity_type' = 'Deal' AND metadata->>'entity_id' = $1
      ORDER BY created_at DESC
      LIMIT 50
    `, [dealId.toString()]);

    const jobs = jobsResult.rows.map(job => ({
      ...job,
      job_type: job.job_name, // Map job_name to job_type for backward compatibility
      entity_type: job.metadata?.entity_type || null,
      entity_id: job.metadata?.entity_id || null,
      metadata: job.metadata || null,
      result: job.result || null,
      data: job.data || null
    }));

    return NextResponse.json({
      success: true,
      jobs: jobs,
      total: jobs.length
    });

  } catch (error) {
    console.error("Error fetching jobs:", error);
    return NextResponse.json(
      { 
        error: "Failed to fetch jobs",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
} 