import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = parseInt(id);
    console.log("Fetching deal v2 with ID:", dealId);

    if (isNaN(dealId)) {
      console.log("Invalid deal ID:", id);
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    // Fetch the deal with all enhanced fields
    const dealQuery = `
      SELECT 
        d.*,
        -- Add computed fields for better UX
        CASE 
          WHEN d.sources_total > 0 THEN 
            ROUND((d.sources_total_debt / d.sources_total * 100)::numeric, 2)
          ELSE NULL 
        END as debt_percentage,
        CASE 
          WHEN d.sources_total > 0 THEN 
            ROUND((d.sources_total_equity / d.sources_total * 100)::numeric, 2)
          ELSE NULL 
        END as equity_percentage,
        CASE 
          WHEN d.sources_total > 0 AND d.sources_senior_debt > 0 THEN 
            ROUND((d.sources_senior_debt / d.sources_total * 100)::numeric, 2)
          ELSE NULL 
        END as senior_debt_percentage,
        CASE 
          WHEN d.sources_total > 0 AND d.sources_mezzanine_debt > 0 THEN 
            ROUND((d.sources_mezzanine_debt / d.sources_total * 100)::numeric, 2)
          ELSE NULL 
        END as mezzanine_debt_percentage,
        CASE 
          WHEN d.sources_total > 0 AND d.sources_preferred_equity > 0 THEN 
            ROUND((d.sources_preferred_equity / d.sources_total * 100)::numeric, 2)
          ELSE NULL 
        END as preferred_equity_percentage,
        CASE 
          WHEN d.sources_total > 0 AND d.sources_common_equity > 0 THEN 
            ROUND((d.sources_common_equity / d.sources_total * 100)::numeric, 2)
          ELSE NULL 
        END as common_equity_percentage,
        CASE 
          WHEN d.sources_total > 0 AND d.sources_general_partner > 0 THEN 
            ROUND((d.sources_general_partner / d.sources_total * 100)::numeric, 2)
          ELSE NULL 
        END as gp_percentage,
        CASE 
          WHEN d.sources_total > 0 AND d.sources_limited_partner > 0 THEN 
            ROUND((d.sources_limited_partner / d.sources_total * 100)::numeric, 2)
          ELSE NULL 
        END as lp_percentage
      FROM public.deals d 
      WHERE d.deal_id = $1
    `;
    
    const dealResult = await pool.query(dealQuery, [dealId]);
    if (dealResult.rows.length === 0) {
      console.log("No deal found with ID:", dealId);
      return NextResponse.json({ error: "Deal not found" }, { status: 404 });
    }
    const deal = dealResult.rows[0];

    // Fetch NSF fields data
    const nsfQuery = `
      SELECT 
        id,
        deal_id,
        deal_type,
        nsf_context,
        -- Core NSF measurements moved to properties table
        -- gsf_gross_square_foot,
        -- zfa_zoning_floor_area,
        -- total_nsf_net_square_foot,
        residential_nsf_net_square_foot,
        retail_nsf_net_square_foot,
        office_nsf_net_square_foot,
        community_facility_nsf_net_square_foot,
        parking_sf,
        building_sqft,
        created_at,
        updated_at
      FROM public.deal_nsf_fields 
      WHERE deal_id = $1
      ORDER BY deal_type, nsf_context
    `;
    
    const nsfResult = await pool.query(nsfQuery, [dealId]);
    deal.nsf_fields = nsfResult.rows;

    // Fetch all investment criteria for this deal
    const criteriaQuery = `
      SELECT * FROM public.investment_criteria
      WHERE entity_type = 'deal_v2' AND entity_id::text = $1
      ORDER BY criteria_id
    `;
    const criteriaResult = await pool.query(criteriaQuery, [dealId]);
    deal.investment_criteria = criteriaResult.rows;

    // Fetch contact information if available
    if (deal.contact_id) {
      const contactQuery = `
        SELECT 
          contact_id,
          first_name,
          last_name,
          email,
          phone,
          title,
          company_id,
          created_at,
          updated_at
        FROM public.contacts 
        WHERE contact_id = $1
      `;
      const contactResult = await pool.query(contactQuery, [deal.contact_id]);
      if (contactResult.rows.length > 0) {
        deal.contact = contactResult.rows[0];
      }
    }

    // Fetch deal contacts (many-to-many relationship)
    const dealContactsQuery = `
      SELECT 
        dc.deal_id,
        dc.contact_id,
        c.first_name,
        c.last_name,
        c.email,
        c.phone,
        c.title,
        c.company_id,
        dc.created_at as association_created_at
      FROM public.deal_contacts dc
      INNER JOIN public.contacts c ON dc.contact_id = c.contact_id
      WHERE dc.deal_id = $1
      ORDER BY c.first_name, c.last_name
    `;
    const dealContactsResult = await pool.query(dealContactsQuery, [dealId]);
    deal.deal_contacts = dealContactsResult.rows;

    // Add data quality metrics
    const qualityMetrics = calculateDealDataQuality(deal);
    deal.data_quality_metrics = qualityMetrics;

    return NextResponse.json({
      success: true,
      deal: deal,
      message: "Deal v2 data retrieved successfully"
    });

  } catch (error) {
    console.error("Error fetching deal v2:", error);
    return NextResponse.json(
      { 
        error: "Failed to fetch deal v2",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// Helper function to calculate data quality metrics
function calculateDealDataQuality(deal: any) {
  const requiredFields = [
    'deal_name', 'sponsor_name', 'property_type', 'strategy',
    'sources_total', 'uses_total'
  ];
  
  const financialFields = [
    'sources_senior_debt', 'sources_mezzanine_debt', 'sources_total_equity',
    'sources_preferred_equity', 'sources_common_equity', 'sources_general_partner',
    'sources_limited_partner'
  ];
  
  const locationFields = ['address', 'city', 'state', 'zip_code', 'country'];
  const propertyFields = ['property_description', 'year_built', 'land_acres'];
  
  let completedFields = 0;
  let totalFields = 0;
  
  // Check required fields
  requiredFields.forEach(field => {
    totalFields++;
    if (deal[field] && deal[field] !== '') completedFields++;
  });
  
  // Check financial fields
  financialFields.forEach(field => {
    totalFields++;
    if (deal[field] && deal[field] > 0) completedFields++;
  });
  
  // Check location fields
  locationFields.forEach(field => {
    totalFields++;
    if (deal[field] && deal[field] !== '') completedFields++;
  });
  
  // Check property fields
  propertyFields.forEach(field => {
    totalFields++;
    if (deal[field] && deal[field] !== '') completedFields++;
  });
  
  const completionRate = Math.round((completedFields / totalFields) * 100);
  
  return {
    completion_rate: completionRate,
    completed_fields: completedFields,
    total_fields: totalFields,
    missing_fields: totalFields - completedFields,
    quality_score: completionRate >= 80 ? 'High' : completionRate >= 60 ? 'Medium' : 'Low'
  };
} 