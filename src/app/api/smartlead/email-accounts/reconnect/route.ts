import { NextRequest, NextResponse } from 'next/server';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * POST: Reconnect failed email accounts
 */
export async function POST(req: NextRequest) {
  try {
    // Build API URL
    const apiUrl = `${SMARTLEAD_BASE_URL}email-accounts/reconnect-failed-email-accounts?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error reconnecting failed email accounts: ${errorText}`);
      
      // Handle specific error cases
      if (response.status === 406) {
        return NextResponse.json(
          { error: 'Bulk reconnect API cannot be consumed more than 3 times a day' },
          { status: 406 }
        );
      }
      
      if (response.status === 404) {
        return NextResponse.json(
          { error: 'No failed email account found!' },
          { status: 404 }
        );
      }
      
      return NextResponse.json(
        { error: `Failed to reconnect email accounts: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error reconnecting failed email accounts:', error);
    return NextResponse.json(
      { error: `Error reconnecting failed email accounts: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 