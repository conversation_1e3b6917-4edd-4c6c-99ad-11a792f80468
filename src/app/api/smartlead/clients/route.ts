import { NextRequest, NextResponse } from 'next/server';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * GET: Fetch all clients
 */
export async function GET(req: NextRequest) {
  try {
    // Build API URL
    const apiUrl = `${SMARTLEAD_BASE_URL}client/?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error fetching clients: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to fetch clients: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching clients:', error);
    return NextResponse.json(
      { error: `Error fetching clients: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

/**
 * POST: Add client to system
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { name, email, permission, logo, logo_url, password } = body;
    
    // Validate required fields
    if (!name || !email || !password) {
      return NextResponse.json(
        { error: 'Missing required fields: name, email, password' },
        { status: 400 }
      );
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }
    
    // Validate permissions
    const validPermissions = ['full_access', 'reply_master_inbox'];
    if (permission && !Array.isArray(permission)) {
      return NextResponse.json(
        { error: 'Permission must be an array' },
        { status: 400 }
      );
    }
    
    if (permission) {
      for (const perm of permission) {
        if (!validPermissions.includes(perm)) {
          return NextResponse.json(
            { error: `Invalid permission: ${perm}. Must be one of: ${validPermissions.join(', ')}` },
            { status: 400 }
          );
        }
      }
    }
    
    // Build API URL
    const apiUrl = `${SMARTLEAD_BASE_URL}client/save?api_key=${SMARTLEAD_API_KEY}`;
    
    const payload = {
      name,
      email,
      permission: permission || ['reply_master_inbox'],
      logo: logo || null,
      logo_url: logo_url || null,
      password
    };
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error adding client: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to add client: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error adding client:', error);
    return NextResponse.json(
      { error: `Error adding client: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 