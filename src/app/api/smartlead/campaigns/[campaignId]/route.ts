import { NextRequest, NextResponse } from 'next/server';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * GET: Fetch campaign details by ID
 */
export async function GET(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;
    
    // Build API URL
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error fetching campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to fetch campaign: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error fetching campaign:`, error);
    return NextResponse.json(
      { error: `Error fetching campaign: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

/**
 * PUT: Update campaign settings
 */
export async function PUT(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;
    const body = await req.json();
    
    // Build API URL for settings update
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/settings?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error updating campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to update campaign: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error updating campaign:`, error);
    return NextResponse.json(
      { error: `Error updating campaign: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

/**
 * DELETE: Delete a campaign
 */
export async function DELETE(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;
    
    // Build API URL for campaign deletion
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error deleting campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to delete campaign: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error deleting campaign:`, error);
    return NextResponse.json(
      { error: `Error deleting campaign: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 