import { NextRequest, NextResponse } from 'next/server';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * GET: Fetch webhooks for a campaign
 */
export async function GET(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;
    
    // Build API URL
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/webhooks?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error fetching webhooks for campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to fetch webhooks: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error fetching webhooks:`, error);
    return NextResponse.json(
      { error: `Error fetching webhooks: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

/**
 * POST: Add or update webhook for a campaign
 */
export async function POST(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;
    const body = await req.json();
    
    // Validate required fields
    const { name, webhook_url, event_types, categories } = body;
    
    if (!name || !webhook_url || !event_types || !Array.isArray(event_types)) {
      return NextResponse.json(
        { error: 'Missing required fields: name, webhook_url, event_types' },
        { status: 400 }
      );
    }
    
    // Validate webhook URL
    try {
      new URL(webhook_url);
    } catch {
      return NextResponse.json(
        { error: 'Invalid webhook URL format' },
        { status: 400 }
      );
    }
    
    // Validate event types
    const validEventTypes = [
      'EMAIL_SENT', 'EMAIL_OPEN', 'EMAIL_LINK_CLICK', 'EMAIL_REPLY',
      'LEAD_UNSUBSCRIBED', 'LEAD_CATEGORY_UPDATED'
    ];
    
    for (const eventType of event_types) {
      if (!validEventTypes.includes(eventType)) {
        return NextResponse.json(
          { error: `Invalid event type: ${eventType}. Must be one of: ${validEventTypes.join(', ')}` },
          { status: 400 }
        );
      }
    }
    
    // Build API URL
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/webhooks?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error adding/updating webhook for campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to add/update webhook: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error adding/updating webhook:`, error);
    return NextResponse.json(
      { error: `Error adding/updating webhook: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

/**
 * DELETE: Delete webhook from a campaign
 */
export async function DELETE(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;
    const body = await req.json();
    const { id } = body;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Webhook ID is required' },
        { status: 400 }
      );
    }
    
    // Build API URL
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/webhooks?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ id }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error deleting webhook for campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to delete webhook: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error deleting webhook:`, error);
    return NextResponse.json(
      { error: `Error deleting webhook: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 