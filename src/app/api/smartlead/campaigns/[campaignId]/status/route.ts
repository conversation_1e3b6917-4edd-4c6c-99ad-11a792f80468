import { NextRequest, NextResponse } from 'next/server';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * POST: Update campaign status
 */
export async function POST(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;
    const body = await req.json();
    const { status } = body;
    
    // Validate status
    const validStatuses = ['PAUSED', 'STOPPED', 'START'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: `Invalid status. Must be one of: ${validStatuses.join(', ')}` },
        { status: 400 }
      );
    }
    
    // Build API URL for status update
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/status?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error updating campaign status ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to update campaign status: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error updating campaign status:`, error);
    return NextResponse.json(
      { error: `Error updating campaign status: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 