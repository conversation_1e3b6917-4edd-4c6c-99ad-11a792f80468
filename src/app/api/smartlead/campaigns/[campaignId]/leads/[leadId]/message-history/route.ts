import { NextRequest, NextResponse } from 'next/server';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * GET: Fetch message history for a specific lead in a campaign
 */
export async function GET(
  req: NextRequest,
  context: { params: { campaignId: string; leadId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId, leadId } = params;
    
    // Build API URL
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads/${leadId}/message-history?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error fetching message history for lead ${leadId} in campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to fetch message history: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error fetching message history:`, error);
    return NextResponse.json(
      { error: `Error fetching message history: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

/**
 * POST: Send a custom message to a lead in a campaign
 */
export async function POST(
  req: NextRequest,
  context: { params: { campaignId: string; leadId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId, leadId } = params;
    
    const body = await req.json();
    const { subject, html_body } = body;
    
    if (!subject || !html_body) {
      return NextResponse.json(
        { error: 'Subject and HTML body are required' },
        { status: 400 }
      );
    }
    
    console.log(`Sending custom message to lead ${leadId} in campaign ${campaignId}`);
    
    // First, verify if lead exists in the campaign
    const leadVerificationUrl = `${SMARTLEAD_BASE_URL}leads/${leadId}?api_key=${SMARTLEAD_API_KEY}`;
    const leadVerificationResponse = await fetch(leadVerificationUrl);
    
    if (!leadVerificationResponse.ok) {
      return NextResponse.json(
        { error: `Lead ${leadId} not found: ${leadVerificationResponse.status}` },
        { status: 404 }
      );
    }
    
    // Use the correct endpoint for sending a message
    // Note: This is the Smartlead API endpoint for sending a manual email
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads/${leadId}/manual-email?api_key=${SMARTLEAD_API_KEY}`;
    
    // Process email body to remove newline characters that might be present in HTML
    const processedHtmlBody = html_body.replace(/\n/g, '');
    
    const payload = {
      subject: subject,
      html_body: processedHtmlBody,
      is_html: true
    };
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error sending custom message: ${errorText}`);
      
      // If endpoint not found, try alternative endpoint
      if (response.status === 404) {
        // Alternative endpoint using PATCH for updating lead with custom fields
        const alternativeUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads/${leadId}?api_key=${SMARTLEAD_API_KEY}`;
        
        const alternativePayload = {
          custom_fields: {
            subject: subject,
            html_body: processedHtmlBody
          }
        };
        
        const alternativeResponse = await fetch(alternativeUrl, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(alternativePayload),
        });
        
        if (!alternativeResponse.ok) {
          const alternativeErrorText = await alternativeResponse.text();
          console.error(`Error with alternative method: ${alternativeErrorText}`);
          return NextResponse.json(
            { error: `Failed to send message using alternative method: ${alternativeResponse.status}` },
            { status: alternativeResponse.status }
          );
        }
        
        const alternativeData = await alternativeResponse.json();
        return NextResponse.json({
          success: true,
          message: 'Custom message sent successfully using alternative method',
          data: alternativeData
        });
      }
      
      return NextResponse.json(
        { error: `Failed to send custom message: ${response.status}` },
        { status: response.status }
      );
    }
    
    const data = await response.json();
    return NextResponse.json({
      success: true,
      message: 'Custom message sent successfully',
      data
    });
    
  } catch (error) {
    console.error('Error sending custom message:', error);
    return NextResponse.json(
      { error: `Error sending custom message: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 