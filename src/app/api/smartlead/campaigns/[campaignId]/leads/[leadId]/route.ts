import { NextRequest, NextResponse } from 'next/server';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * DELETE: Remove a specific lead from a campaign
 */
export async function DELETE(
  req: NextRequest,
  context: { params: { campaignId: string; leadId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId, leadId } = params;
    
    console.log(`Attempting to delete lead ${leadId} from campaign ${campaignId}`);
    
    // First, we need to get the campaign leads to find the actual lead.id
    // because our leadId is actually the campaign_lead_map_id
    const allLeads: any[] = [];
    let offset = 0;
    const limit = 100; // Smartlead API maximum limit
    let hasMore = true;
    
    // Fetch all leads with pagination
    while (hasMore) {
      const leadsResponse = await fetch(`${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads?api_key=${SMARTLEAD_API_KEY}&limit=${limit}&offset=${offset}`);
      
      if (!leadsResponse.ok) {
        const errorText = await leadsResponse.text();
        console.error(`Error fetching leads for campaign ${campaignId}: ${errorText}`);
        return NextResponse.json(
          { error: `Failed to fetch leads: ${leadsResponse.status}` },
          { status: leadsResponse.status }
        );
      }

      const leadsData = await leadsResponse.json();
      
      if (leadsData.data && leadsData.data.length > 0) {
        allLeads.push(...leadsData.data);
        offset += limit;
        hasMore = leadsData.data.length === limit; // Continue if we got a full page
      } else {
        hasMore = false;
      }
    }
    
    // Find the lead with the matching campaign_lead_map_id
    const targetLead = allLeads.find((item: any) => 
      item.campaign_lead_map_id === leadId
    );
    
    if (!targetLead) {
      console.error(`Lead with campaign_lead_map_id ${leadId} not found in campaign ${campaignId}`);
      return NextResponse.json(
        { error: `Lead not found in campaign` },
        { status: 404 }
      );
    }
    
    // Use the actual lead.id from Smartlead for the delete API
    const actualLeadId = targetLead.lead?.id;
    
    if (!actualLeadId) {
      console.error(`Lead data missing for campaign_lead_map_id ${leadId}`);
      return NextResponse.json(
        { error: `Lead data incomplete` },
        { status: 400 }
      );
    }
    
    console.log(`Found actual lead ID ${actualLeadId} for campaign_lead_map_id ${leadId}`);
    
    // Now delete using the actual lead ID
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads/${actualLeadId}?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error deleting lead ${actualLeadId} from campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to delete lead: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`Lead ${actualLeadId} (campaign_lead_map_id: ${leadId}) deleted successfully from campaign ${campaignId}:`, data);
    
    return NextResponse.json({
      success: true,
      message: 'Lead deleted successfully',
      data
    });
  } catch (error) {
    console.error(`Error deleting lead from campaign:`, error);
    return NextResponse.json(
      { error: `Error deleting lead: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 
/**
 * GET: Get a specific lead
 */
export async function GET(
  req: NextRequest,
  context: { params: { campaignId: string; leadId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId, leadId } = params;
    
    // Build API URL
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads/${leadId}?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error fetching lead ${leadId} for campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to fetch lead: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error fetching lead:`, error);
    return NextResponse.json(
      { error: `Error fetching lead: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

/**
 * PUT: Update a lead
 */
export async function PUT(
  req: NextRequest,
  context: { params: { campaignId: string; leadId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId, leadId } = params;
    const body = await req.json();
    
    // Build API URL
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads/${leadId}?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error updating lead ${leadId} for campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to update lead: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error updating lead:`, error);
    return NextResponse.json(
      { error: `Error updating lead: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 