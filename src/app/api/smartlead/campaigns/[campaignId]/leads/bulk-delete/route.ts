import { NextRequest, NextResponse } from 'next/server';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * POST: Bulk delete leads from a campaign
 */
export async function POST(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;
    const body = await req.json();
    
    // Validate required fields
    const { leadIds } = body;
    
    if (!leadIds || !Array.isArray(leadIds) || leadIds.length === 0) {
      return NextResponse.json(
        { error: 'Lead IDs array is required and must not be empty' },
        { status: 400 }
      );
    }
    
    // Build API URL
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads/bulk-delete?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ lead_ids: leadIds }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error bulk deleting leads for campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to bulk delete leads: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error bulk deleting leads:`, error);
    return NextResponse.json(
      { error: `Error bulk deleting leads: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 