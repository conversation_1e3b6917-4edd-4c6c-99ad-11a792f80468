import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * GET: Fetch comprehensive stats for a specific campaign
 */
export async function GET(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  const params = await context.params;
  const { campaignId } = params;
  
  // Get query parameters for filtering
  const { searchParams } = new URL(req.url);
  const emailSequenceNumber = searchParams.get('email_sequence_number');
  const emailStatus = searchParams.get('email_status');
  
  try {

    // Fetch campaign details
    const campaignResponse = await fetch(`${SMARTLEAD_BASE_URL}campaigns/${campaignId}?api_key=${SMARTLEAD_API_KEY}`);
    
    if (!campaignResponse.ok) {
      const errorText = await campaignResponse.text();
      console.error(`Error fetching campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to fetch campaign: ${campaignResponse.status}` },
        { status: campaignResponse.status }
      );
    }

    const campaignData = await campaignResponse.json();

    // Fetch campaign sequence
    let sequenceData = null;
    try {
      const sequenceResponse = await fetch(`${SMARTLEAD_BASE_URL}campaigns/${campaignId}/sequences?api_key=${SMARTLEAD_API_KEY}`);
      if (sequenceResponse.ok) {
        sequenceData = await sequenceResponse.json();
      }
    } catch (error) {
      console.warn(`Could not fetch sequence for campaign ${campaignId}:`, error);
    }

    // Fetch all leads for this campaign to calculate stats
    const allLeads: any[] = [];
    let offset = 0;
    const limit = 100;
    let hasMore = true;
    let totalLeads = 0;

    while (hasMore) {
      const leadsResponse = await fetch(`${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads?api_key=${SMARTLEAD_API_KEY}&limit=${limit}&offset=${offset}`);
      
      if (!leadsResponse.ok) {
        console.warn(`Error fetching leads for campaign ${campaignId} at offset ${offset}`);
        break;
      }

      const leadsData = await leadsResponse.json();
      
      if (leadsData.data && leadsData.data.length > 0) {
        allLeads.push(...leadsData.data);
        offset += limit;
        hasMore = leadsData.data.length === limit;
        
        // Get total count from first response
        if (totalLeads === 0 && leadsData.total_leads) {
          totalLeads = parseInt(leadsData.total_leads, 10);
        }
      } else {
        hasMore = false;
      }
    }

    // Calculate lead status distribution
    const statusCounts: Record<string, number> = {};
    const leadStatuses = ['STARTED', 'INPROGRESS', 'COMPLETED', 'BLOCKED'];
    
    leadStatuses.forEach(status => {
      statusCounts[status] = 0;
    });

    allLeads.forEach(leadItem => {
      const status = leadItem.status || 'UNKNOWN';
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    // Fetch campaign analytics from Smartlead API
    const analyticsResponse = await fetch(`${SMARTLEAD_BASE_URL}campaigns/${campaignId}/analytics?api_key=${SMARTLEAD_API_KEY}`);
    let analyticsData = null;
    if (analyticsResponse.ok) {
      analyticsData = await analyticsResponse.json();
    }

    // Fetch detailed campaign statistics from Smartlead API
    const allStatistics: any[] = [];
    let statsOffset = 0;
    const statsLimit = 100;
    let hasMoreStats = true;
    let totalStats = 0;

    while (hasMoreStats) {
      // Build statistics API URL with filters
      const statsParams = new URLSearchParams({
        api_key: SMARTLEAD_API_KEY,
        offset: statsOffset.toString(),
        limit: statsLimit.toString()
      });
      
      if (emailSequenceNumber) {
        statsParams.append('email_sequence_number', emailSequenceNumber);
      }
      if (emailStatus) {
        statsParams.append('email_status', emailStatus);
      }
      
      const statsUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/statistics?${statsParams.toString()}`;
      const statsResponse = await fetch(statsUrl);
      
      if (!statsResponse.ok) {
        console.warn(`Error fetching statistics for campaign ${campaignId} at offset ${statsOffset}`);
        break;
      }

      const statsData = await statsResponse.json();
      
      if (statsData.data && statsData.data.length > 0) {
        allStatistics.push(...statsData.data);
        statsOffset += statsLimit;
        hasMoreStats = statsData.data.length === statsLimit;
        
        // Get total count from first response
        if (totalStats === 0 && statsData.total_stats) {
          totalStats = parseInt(statsData.total_stats, 10);
        }
      } else {
        hasMoreStats = false;
      }
    }

    // Process statistics to calculate metrics
    const emailStats = {
      sent: allStatistics.length,
      delivered: allStatistics.filter(stat => stat.sent_time && !stat.is_bounced).length,
      opened: allStatistics.filter(stat => stat.open_time || stat.open_count > 0).length,
      replied: allStatistics.filter(stat => stat.reply_time).length,
      bounced: allStatistics.filter(stat => stat.is_bounced).length,
      unsubscribed: allStatistics.filter(stat => stat.is_unsubscribed).length,
      clicked: allStatistics.filter(stat => stat.click_time || stat.click_count > 0).length
    };

    // Calculate daily activity from statistics
    const dailyActivityMap = new Map();
    allStatistics.forEach(stat => {
      if (stat.sent_time) {
        const date = new Date(stat.sent_time).toISOString().split('T')[0];
        if (!dailyActivityMap.has(date)) {
          dailyActivityMap.set(date, { sent: 0, opened: 0, replied: 0, clicked: 0 });
        }
        const dayStats = dailyActivityMap.get(date);
        dayStats.sent++;
        if (stat.open_time || stat.open_count > 0) dayStats.opened++;
        if (stat.reply_time) dayStats.replied++;
        if (stat.click_time || stat.click_count > 0) dayStats.clicked++;
      }
    });

    const dailyActivity = Array.from(dailyActivityMap.entries())
      .map(([date, stats]) => ({ date, ...stats }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .slice(-30); // Last 30 days

    // Get recent activity from statistics (last 20 entries) and link with database contacts
    const recentStatistics = allStatistics
      .filter(stat => stat.sent_time)
      .sort((a, b) => new Date(b.sent_time).getTime() - new Date(a.sent_time).getTime())
      .slice(0, 20);

    // Get unique emails for database lookup
    const uniqueEmails = [...new Set(recentStatistics.map(stat => stat.lead_email))];
    
    // Search for contacts in database by email
    const contactsMap = new Map();
    if (uniqueEmails.length > 0) {
      try {
        const placeholders = uniqueEmails.map((_, index) => `$${index + 1}`).join(',');
        const contactQuery = `
          SELECT 
            c.contact_id,
            c.first_name as contact_first_name,
            c.last_name as contact_last_name,
            c.email,
            c.company_id,
            comp.company_name,
            comp.company_website,
            comp.industry
          FROM contacts c
          LEFT JOIN companies comp ON c.company_id = comp.company_id
          WHERE LOWER(c.email) = ANY(ARRAY[${placeholders}])
        `;
        
        const { rows: contacts } = await pool.query(contactQuery, uniqueEmails);
        
        // Create a map for quick lookup
        contacts.forEach(contact => {
          contactsMap.set(contact.email.toLowerCase(), contact);
        });
      } catch (error) {
        console.error('Error fetching contacts from database:', error);
      }
    }

    // Map recent activity with contact information
    const recentActivity = recentStatistics.map(stat => {
      const contact = contactsMap.get(stat.lead_email?.toLowerCase());
      
      return {
        contact_id: contact?.contact_id || null,
        first_name: stat.lead_name ? stat.lead_name.split(' ')[0] : '',
        last_name: stat.lead_name ? stat.lead_name.split(' ').slice(1).join(' ') : '',
        email: stat.lead_email,
        smartlead_status: stat.reply_time ? 'REPLIED' : 
                         stat.is_bounced ? 'BOUNCED' : 
                         stat.is_unsubscribed ? 'UNSUBSCRIBED' :
                         stat.open_time || stat.open_count > 0 ? 'OPENED' :
                         stat.click_time || stat.click_count > 0 ? 'CLICKED' : 'SENT',
        last_email_sent_at: stat.sent_time,
        company_name: contact?.company_name || null,
        sequence_number: stat.sequence_number,
        email_subject: stat.email_subject,
        // Additional contact info for reference
        contact_info: contact ? {
          contact_id: contact.contact_id,
          company_id: contact.company_id,
          contact_first_name: contact.contact_first_name,
          contact_last_name: contact.contact_last_name,
          company_name: contact.company_name,
          company_website: contact.company_website,
          industry: contact.industry
        } : null
      };
    });

    // Calculate performance metrics
    const totalSent = emailStats.sent;
    const totalOpened = emailStats.opened;
    const totalReplied = emailStats.replied;
    const totalBounced = emailStats.bounced;

    const openRate = totalSent > 0 ? ((totalOpened / totalSent) * 100).toFixed(2) : '0';
    const replyRate = totalSent > 0 ? ((totalReplied / totalSent) * 100).toFixed(2) : '0';
    const bounceRate = totalSent > 0 ? ((totalBounced / totalSent) * 100).toFixed(2) : '0';

    return NextResponse.json({
      campaign: {
        id: campaignData.id,
        name: campaignData.name,
        status: campaignData.status,
        created_at: campaignData.created_at,
        updated_at: campaignData.updated_at,
        track_settings: campaignData.track_settings,
        stop_lead_settings: campaignData.stop_lead_settings,
        max_leads_per_day: campaignData.max_leads_per_day,
        min_time_btwn_emails: campaignData.min_time_btwn_emails,
        follow_up_percentage: campaignData.follow_up_percentage,
        enable_ai_esp_matching: campaignData.enable_ai_esp_matching,
        send_as_plain_text: campaignData.send_as_plain_text
      },
      sequence: sequenceData,
      leadStats: {
        totalLeads: totalLeads || allLeads.length,
        statusDistribution: statusCounts
      },
      emailStats: {
        sent: totalSent,
        delivered: emailStats.delivered,
        opened: totalOpened,
        replied: totalReplied,
        bounced: totalBounced,
        unsubscribed: emailStats.unsubscribed,
        clicked: emailStats.clicked
      },
      performanceMetrics: {
        openRate: parseFloat(openRate),
        replyRate: parseFloat(replyRate),
        bounceRate: parseFloat(bounceRate)
      },
      dailyActivity,
      recentActivity
    });

  } catch (error) {
    console.error(`Error fetching campaign stats for ${campaignId}:`, error);
    return NextResponse.json(
      { error: `Error fetching campaign stats: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 