import { NextRequest, NextResponse } from 'next/server';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * POST: Create a new sequence for a campaign
 */
export async function POST(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;
    const body = await req.json();
    
    // Validate required fields
    const { subject, email_body, seq_number, delay_in_days } = body;
    
    if (!subject || !email_body || !seq_number) {
      return NextResponse.json(
        { error: 'Missing required fields: subject, email_body, seq_number' },
        { status: 400 }
      );
    }
    
    // Build API URL
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/sequences?api_key=${SMARTLEAD_API_KEY}`;
    
    const payload = {
      sequences: [{
        subject,
        email_body,
        seq_number: parseInt(seq_number),
        seq_delay_details: {
          delay_in_days: parseInt(delay_in_days) || 1
        }
      }]
    };
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error creating sequence for campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to create sequence: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error creating sequence:`, error);
    return NextResponse.json(
      { error: `Error creating sequence: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 