import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '';
const SMARTLEAD_BASE_URL = 'https://server.smartlead.ai/api/v1/';

/**
 * POST: Sync all contacts associated with a specific campaign to Smartlead
 */
export async function POST(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;
    
    if (!campaignId) {
      return NextResponse.json(
        { error: 'Campaign ID is required' },
        { status: 400 }
      );
    }
    
    console.log(`Finding messages for Smartlead campaign ID: ${campaignId}`);
    
    // Get messages with contact IDs for this campaign
    const { rows: messages } = await pool.query(`
      SELECT m.*, tp.contact_id 
      FROM messages AS m 
      JOIN thread_participants AS tp ON m.thread_id = tp.thread_id 
      WHERE tp.contact_id IS NOT NULL
      AND m.smartlead_campaign_id = $1
    `, [campaignId]);
    
    if (messages.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'No messages found for this campaign',
        syncedCount: 0,
        totalCount: 0
      });
    }
    
    console.log(`Found ${messages.length} messages for campaign ${campaignId}`);
    
    // Collect unique contact IDs
    const uniqueContactIds = [...new Set(messages.map(m => m.contact_id))];
    console.log(`Found ${uniqueContactIds.length} unique contacts to sync`);
    
    // Use the batch-sync endpoint for processing
    const baseUrl = process.env.API_BASE_URL || req.nextUrl.origin || 'http://localhost:3030';
    const batchSyncUrl = new URL(`${baseUrl}/api/smartlead/contacts/batch-sync`);
    
    // Call the batch-sync endpoint
    const response = await fetch(batchSyncUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contactIds: uniqueContactIds,
        campaignId
      }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error(`Error syncing contacts for campaign ${campaignId}:`, errorData);
      return NextResponse.json(
        { error: errorData.error || `Failed with status: ${response.status}` },
        { status: response.status }
      );
    }
    
    // Return the response from batch-sync
    const result = await response.json();
    return NextResponse.json({
      ...result,
      message: `Synced contacts for campaign ${campaignId}: ${result.syncedCount} succeeded, ${result.failedCount} failed`,
      campaign_id: campaignId
    });
    
  } catch (error) {
    console.error(`Error syncing contacts for campaign:`, error);
    return NextResponse.json(
      { error: `Error syncing contacts: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 