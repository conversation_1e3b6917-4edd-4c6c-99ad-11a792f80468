import { NextRequest, NextResponse } from 'next/server';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * POST: Update campaign schedule
 */
export async function POST(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;
    const body = await req.json();
    
    // Validate required fields
    const { timezone, days_of_the_week, start_hour, end_hour, min_time_btw_emails, max_new_leads_per_day } = body;
    
    if (!timezone || !days_of_the_week || !start_hour || !end_hour) {
      return NextResponse.json(
        { error: 'Missing required fields: timezone, days_of_the_week, start_hour, end_hour' },
        { status: 400 }
      );
    }
    
    // Validate timezone format
    const validTimezones = [
      'America/Los_Angeles', 'America/New_York', 'Europe/London', 'Asia/Tokyo',
      'Australia/Sydney', 'UTC', 'America/Chicago', 'America/Denver'
    ];
    
    if (!validTimezones.includes(timezone)) {
      return NextResponse.json(
        { error: `Invalid timezone. Must be one of: ${validTimezones.join(', ')}` },
        { status: 400 }
      );
    }
    
    // Validate days of the week (0-6, where 0 is Sunday)
    if (!Array.isArray(days_of_the_week) || days_of_the_week.some(day => day < 0 || day > 6)) {
      return NextResponse.json(
        { error: 'days_of_the_week must be an array of numbers 0-6 (0=Sunday, 6=Saturday)' },
        { status: 400 }
      );
    }
    
    // Validate time format (HH:MM)
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(start_hour) || !timeRegex.test(end_hour)) {
      return NextResponse.json(
        { error: 'start_hour and end_hour must be in HH:MM format (e.g., "09:00", "18:00")' },
        { status: 400 }
      );
    }
    
    // Build API URL for schedule update
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/schedule?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error updating campaign schedule ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to update campaign schedule: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error updating campaign schedule:`, error);
    return NextResponse.json(
      { error: `Error updating campaign schedule: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 