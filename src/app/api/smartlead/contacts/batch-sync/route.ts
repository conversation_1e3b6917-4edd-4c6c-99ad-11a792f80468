import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '';
const SMARTLEAD_BASE_URL = 'https://server.smartlead.ai/api/v1/';
const SMARTLEAD_CAMPAIGN_ID = process.env.SMARTLEAD_CAMPAIGN_ID || '1897921';

// Helper function to add delay between requests
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * POST: Batch sync multiple contacts to Smartlead
 * This endpoint will sync multiple email-validated contacts to Smartlead
 * Either provide contactIds directly or a campaignId to find contacts from messages table
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { contactIds: providedContactIds, campaignId = SMARTLEAD_CAMPAIGN_ID } = body;
    
    if (!campaignId) {
      return NextResponse.json(
        { error: 'Campaign ID is required' },
        { status: 400 }
      );
    }
    
    // If contactIds are provided directly, query for contacts
    if (providedContactIds && Array.isArray(providedContactIds) && providedContactIds.length > 0) {
      console.log(`Using provided contact IDs: ${providedContactIds.length} contacts`);
      
      // Get contact details
      const { rows: contacts } = await pool.query(`
        SELECT 
          contact_id,
          first_name,
          last_name,
          email,
          email_generated,
          smartlead_lead_id,
          smartlead_status
        FROM contacts
        WHERE contact_id = ANY($1::int[])
        AND email IS NOT NULL
        AND email_generated = true
      `, [providedContactIds]);
      
      if (contacts.length === 0) {
        return NextResponse.json({
          success: false,
          message: 'No eligible contacts found for sync',
          syncedCount: 0,
          totalCount: providedContactIds.length
        });
      }
      
      return await processContacts(contacts, campaignId, null, null, req);
    } 
    // Otherwise, find messages with the specified campaign ID and their associated contacts
    else {
      console.log(`Finding messages for Smartlead campaign ID: ${campaignId}`);
      
      // Get messages with contact IDs based on user's suggested query
      const { rows: messages } = await pool.query(`
        SELECT m.*, tp.contact_id 
        FROM messages AS m 
        JOIN thread_participants AS tp ON m.thread_id = tp.thread_id 
        WHERE tp.contact_id IS NOT NULL
        AND m.smartlead_campaign_id = $1
      `, [campaignId]);
      
      if (messages.length === 0) {
        return NextResponse.json({
          success: false,
          message: 'No messages found for this campaign',
          syncedCount: 0,
          totalCount: 0
        });
      }
      
      console.log(`Found ${messages.length} messages for campaign ${campaignId}`);
      
      // Process each message and contact pair
      const results = [];
      const baseUrl = process.env.API_BASE_URL || req.nextUrl.origin || 'http://localhost:3030';
      
      // Process messages in batches
      const batchSize = 1;
      const batches = [];
      for (let i = 0; i < messages.length; i += batchSize) {
        batches.push(messages.slice(i, i + batchSize));
      }
      
      // Process each batch with a delay between batches
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`Processing batch ${i+1}/${batches.length} (${batch.length} messages)`);
        
        // Add a delay between batches, except for the first one
        if (i > 0) {
          console.log(`Waiting 1 minute before next batch...`);
          await sleep(60000); // 1 minute delay
        }
        
        const batchResults = await Promise.allSettled(
          batch.map(async (message) => {
            try {
              // Get contact details to ensure it's valid for syncing
              const { rows: contacts } = await pool.query(`
                SELECT 
                  contact_id,
                  first_name,
                  last_name,
                  email,
                  email_generated,
                  smartlead_lead_id,
                  smartlead_status
                FROM contacts
                WHERE contact_id = $1
                AND email IS NOT NULL
                AND email_generated = true
              `, [message.contact_id]);
              
              if (contacts.length === 0) {
                return {
                  contact_id: message.contact_id,
                  message_id: message.message_id,
                  success: false,
                  error: 'Contact not found or has no valid email'
                };
              }
              
              const contact = contacts[0];
              
              // Prepare payload for the individual contact sync endpoint
              const payload = {
                campaignId,
                subject: message.subject || '',
                body: message.body || '',
              };
              
              // Construct absolute URL for the API endpoint
              const syncUrl = new URL(`${baseUrl}/api/smartlead/contacts/${contact.contact_id}/sync`);
              
              console.log(`Syncing contact ${contact.contact_id} (${contact.email}) with message ${message.message_id} to campaign ${campaignId}`);
              
              // Call the individual contact sync endpoint
              const response = await fetch(syncUrl, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload),
              });
              
              if (!response.ok) {
                const errorData = await response.json();
                console.error(`Error syncing contact ${contact.contact_id}:`, errorData);
                return {
                  contact_id: contact.contact_id,
                  email: contact.email,
                  message_id: message.message_id,
                  success: false,
                  error: errorData.error || `Failed with status: ${response.status}`
                };
              }
              
              const data = await response.json();
              return {
                contact_id: contact.contact_id,
                email: contact.email,
                message_id: message.message_id,
                success: true,
                lead_id: data.lead_id,
                campaign_id: campaignId,
                message: 'Successfully synced to Smartlead'
              };
            } catch (error) {
              console.error(`Error syncing message ${message.message_id}:`, error);
              return {
                contact_id: message.contact_id,
                message_id: message.message_id,
                success: false,
                error: (error as Error).message
              };
            }
          })
        );
        
        results.push(...batchResults);
      }
      
      // Count successes and failures
      const successResults = results.filter(r => 
        r.status === 'fulfilled' && (r.value as any).success
      );
      const successCount = successResults.length;
      const failureCount = results.length - successCount;
      
      // Get the lead IDs of successfully synced contacts
      const syncedLeadIds = successResults
        .map(r => r.status === 'fulfilled' ? (r.value as any).lead_id : null)
        .filter(Boolean);
      
      return NextResponse.json({
        success: true,
        message: `Synced ${successCount} contacts to Smartlead, ${failureCount} failed`,
        syncedCount: successCount,
        failedCount: failureCount,
        totalCount: messages.length,
        campaign: {
          id: campaignId
        },
        lead_ids: syncedLeadIds,
        results: results.map(r => {
          if (r.status === 'fulfilled') {
            return r.value;
          } else {
            return { 
              success: false, 
              error: (r as PromiseRejectedResult).reason?.message || 'Unknown error'
            };
          }
        })
      });
    }
  } catch (error) {
    console.error('Error in batch sync:', error);
    return NextResponse.json(
      { error: `Error batch syncing contacts: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

// Helper function to process contacts when contactIds are provided directly
async function processContacts(
  contacts: any[], 
  campaignId: string, 
  subject: string | null, 
  body: string | null, 
  req: NextRequest
) {
  console.log(`Processing ${contacts.length} contacts for campaign ${campaignId}`);
  
  // Process contacts in batches of 10 to avoid overwhelming the API
  const batchSize = 10;
  const batches = [];
  for (let i = 0; i < contacts.length; i += batchSize) {
    batches.push(contacts.slice(i, i + batchSize));
  }
  
  const results = [];
  const baseUrl = process.env.API_BASE_URL || req.nextUrl.origin || 'http://localhost:3030';
  
  // Process each batch with a delay between batches
  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    console.log(`Processing batch ${i+1}/${batches.length} (${batch.length} contacts)`);
    
    // Add a delay between batches, except for the first one
    if (i > 0) {
      console.log(`Waiting 30 seconds before next batch...`);
      await sleep(30000); // 30 second delay
    }
    
    const batchResults = await Promise.allSettled(
      batch.map(async (contact) => {
        try {
          // Prepare payload for the individual contact sync endpoint
          const payload = {
            campaignId,
            ...(subject ? { subject } : {}),
            ...(body ? { body } : {}),
          };
          
          // Construct absolute URL for the API endpoint
          const syncUrl = new URL(`${baseUrl}/api/smartlead/contacts/${contact.contact_id}/sync`);
          
          console.log(`Syncing contact ${contact.contact_id} (${contact.email}) to campaign ${campaignId}`);
          
          // Call the individual contact sync endpoint
          const response = await fetch(syncUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
          });
          
          if (!response.ok) {
            const errorData = await response.json();
            console.error(`Error syncing contact ${contact.contact_id}:`, errorData);
            return {
              contact_id: contact.contact_id,
              email: contact.email,
              success: false,
              error: errorData.error || `Failed with status: ${response.status}`
            };
          }
          
          const data = await response.json();
          return {
            contact_id: contact.contact_id,
            email: contact.email,
            success: true,
            lead_id: data.lead_id,
            campaign_id: campaignId,
            message: 'Successfully synced to Smartlead'
          };
        } catch (error) {
          console.error(`Error syncing contact ${contact.contact_id}:`, error);
          return {
            contact_id: contact.contact_id,
            email: contact.email || 'unknown',
            success: false,
            error: (error as Error).message
          };
        }
      })
    );
    
    results.push(...batchResults);
  }
  
  // Count successes and failures
  const successResults = results.filter(r => 
    r.status === 'fulfilled' && (r.value as any).success
  );
  const successCount = successResults.length;
  const failureCount = results.length - successCount;
  
  // Get the lead IDs of successfully synced contacts
  const syncedLeadIds = successResults
    .map(r => r.status === 'fulfilled' ? (r.value as any).lead_id : null)
    .filter(Boolean);
  
  return NextResponse.json({
    success: true,
    message: `Synced ${successCount} contacts to Smartlead, ${failureCount} failed`,
    syncedCount: successCount,
    failedCount: failureCount,
    totalCount: contacts.length,
    campaign: {
      id: campaignId
    },
    lead_ids: syncedLeadIds,
    results: results.map(r => {
      if (r.status === 'fulfilled') {
        return r.value;
      } else {
        return { 
          success: false, 
          error: (r as PromiseRejectedResult).reason?.message || 'Unknown error'
        };
      }
    })
  });
} 