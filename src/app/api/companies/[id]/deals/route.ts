import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: companyId } = await params;
    
    const query = `
      SELECT DISTINCT d.*, c.contact_id, c.first_name, c.last_name, c.email, c.title
      FROM deals d
      INNER JOIN deal_contacts dc ON d.deal_id = dc.deal_id
      INNER JOIN contacts c ON dc.contact_id = c.contact_id
      WHERE c.company_id = $1
      ORDER BY d.created_at DESC, c.first_name, c.last_name
    `;
    
    const result = await pool.query(query, [companyId]);
    
    // Group deals by contact for better organization
    const dealsByContact = result.rows.reduce((acc: any, row: any) => {
      const contactKey = `${row.contact_id}-${row.first_name}-${row.last_name}`;
      if (!acc[contactKey]) {
        acc[contact<PERSON>ey] = {
          contact: {
            contact_id: row.contact_id,
            first_name: row.first_name,
            last_name: row.last_name,
            email: row.email,
            title: row.title,
            full_name: `${row.first_name} ${row.last_name}`
          },
          deals: []
        };
      }
      
      // Add deal to this contact's list
      const deal = { ...row };
      delete deal.contact_id;
      delete deal.first_name;
      delete deal.last_name;
      delete deal.email;
      delete deal.title;
      
      acc[contactKey].deals.push(deal);
      return acc;
    }, {});
    
    return NextResponse.json({
      dealsByContact: Object.values(dealsByContact),
      total: result.rows.length
    });
  } catch (error) {
    console.error('Error fetching deals for company:', error);
    return NextResponse.json(
      { error: 'Failed to fetch deals for company' },
      { status: 500 }
    );
  }
} 