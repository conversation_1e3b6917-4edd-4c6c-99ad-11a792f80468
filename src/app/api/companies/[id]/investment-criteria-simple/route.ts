import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: companyId } = await params

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Simple query to get investment criteria
    const query = `
      SELECT 
        investment_criteria_id,
        capital_position,
        minimum_deal_size,
        maximum_deal_size,
        country,
        region,
        state,
        city,
        property_types,
        property_subcategories,
        strategies,
        decision_making_process,
        notes,
        investment_criteria_debt_id,
        investment_criteria_equity_id
      FROM investment_criteria_central 
      WHERE entity_id = $1 
        AND entity_type = 'company'
      ORDER BY created_at DESC
    `

    const result = await pool.query(query, [companyId])
    
    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Error fetching company investment criteria:', error)
    return NextResponse.json(
      { error: 'Failed to fetch investment criteria', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
