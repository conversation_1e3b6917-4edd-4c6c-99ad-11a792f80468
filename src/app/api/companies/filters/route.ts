import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse filters from query parameters
    const filters = {
      // Basic search
      searchTerm: searchParams.get('searchTerm'),
      companyName: searchParams.get('companyName'),
      companyId: searchParams.get('companyId'),
      
      // Company profile filters (from company_extracted_data table)
      companyType: searchParams.get('companyType')?.split(',').filter(Boolean) || [],
      businessModel: searchParams.get('businessModel'),
      companyIndustry: searchParams.get('companyIndustry')?.split(',').filter(Boolean) || [],
      capitalPosition: searchParams.get('capitalPosition')?.split(',').filter(Boolean) || [],
      
      // Financial metrics (from company_extracted_data table)
      fundSizeMin: searchParams.get('fundSizeMin'),
      fundSizeMax: searchParams.get('fundSizeMax'),
      aumMin: searchParams.get('aumMin'),
      aumMax: searchParams.get('aumMax'),
      
      // Company size filters (from company_extracted_data table)
      numberOfPropertiesMin: searchParams.get('numberOfPropertiesMin'),
      numberOfPropertiesMax: searchParams.get('numberOfPropertiesMax'),
      numberOfOfficesMin: searchParams.get('numberOfOfficesMin'),
      numberOfOfficesMax: searchParams.get('numberOfOfficesMax'),
      numberOfEmployeesMin: searchParams.get('numberOfEmployeesMin'),
      numberOfEmployeesMax: searchParams.get('numberOfEmployeesMax'),
      foundedYearMin: searchParams.get('foundedYearMin'),
      foundedYearMax: searchParams.get('foundedYearMax'),
      
      // Track Record Metrics (from company_extracted_data table)
      totalTransactions: searchParams.get('totalTransactions'),
      totalSquareFeet: searchParams.get('totalSquareFeet'),
      totalUnits: searchParams.get('totalUnits'),
      historicalReturns: searchParams.get('historicalReturns'),
      portfolioValue: searchParams.get('portfolioValue'),
      
      // Investment Focus & Strategy (from company_extracted_data JSONB fields)
      investmentFocus: searchParams.get('investmentFocus')?.split(',').filter(Boolean) || [],
      propertyTypes: searchParams.get('propertyTypes')?.split(',').filter(Boolean) || [],
      strategies: searchParams.get('strategies')?.split(',').filter(Boolean) || [],
      assetClasses: searchParams.get('assetClasses')?.split(',').filter(Boolean) || [],
      extractedGeographicFocus: searchParams.get('extractedGeographicFocus')?.split(',').filter(Boolean) || [],
      targetMarkets: searchParams.get('targetMarkets')?.split(',').filter(Boolean) || [],
      capitalSources: searchParams.get('capitalSources')?.split(',').filter(Boolean) || [],
      financialProducts: searchParams.get('financialProducts')?.split(',').filter(Boolean) || [],
      partnerships: searchParams.get('partnerships')?.split(',').filter(Boolean) || [],
      officeLocations: searchParams.get('officeLocations')?.split(',').filter(Boolean) || [],
      
      // Deal Size & Investment Criteria (from company_extracted_data)
      extractedDealSizeMin: searchParams.get('extractedDealSizeMin'),
      extractedDealSizeMax: searchParams.get('extractedDealSizeMax'),
      minimumDealSize: searchParams.get('minimumDealSize'),
      maximumDealSize: searchParams.get('maximumDealSize'),
      holdPeriod: searchParams.get('holdPeriod'),
      riskProfile: searchParams.get('riskProfile'),
      targetReturn: searchParams.get('targetReturn'),
      
      // Contact Information (from company_extracted_data)
      website: searchParams.get('website'),
      mainPhone: searchParams.get('mainPhone'),
      mainEmail: searchParams.get('mainEmail'),
      linkedinUrl: searchParams.get('linkedinUrl'),
      
      // Mission & Strategy (from company_extracted_data)
      mission: searchParams.get('mission'),
      approach: searchParams.get('approach'),
      
      // Geographic filters (legacy from companies table)
      headquarters: searchParams.get('headquarters')?.split(',').filter(Boolean) || [],
      companyState: searchParams.get('companyState')?.split(',').filter(Boolean) || [],
      companyCity: searchParams.get('companyCity')?.split(',').filter(Boolean) || [],
      companyCountry: searchParams.get('companyCountry')?.split(',').filter(Boolean) || [],
      
      // Legacy overview structure (from companies.overview JSONB)
      primaryIndustry: searchParams.get('primaryIndustry')?.split(',').filter(Boolean) || [],
      coreCapitalPosition: searchParams.get('coreCapitalPosition')?.split(',').filter(Boolean) || [],
      investmentStrategy: searchParams.get('investmentStrategy')?.split(',').filter(Boolean) || [],
      holdHorizon: searchParams.get('holdHorizon')?.split(',').filter(Boolean) || [],
      assetTypes: searchParams.get('assetTypes')?.split(',').filter(Boolean) || [],
      dealStructure: searchParams.get('dealStructure')?.split(',').filter(Boolean) || [],
      
      // Processing status filters
      processed: searchParams.get('processed'),
      extracted: searchParams.get('extracted'),
      processingState: searchParams.get('processingState')?.split(',').filter(Boolean) || [],
      websiteScrapingStatus: searchParams.get('websiteScrapingStatus')?.split(',').filter(Boolean) || [],
      companyOverviewStatus: searchParams.get('companyOverviewStatus')?.split(',').filter(Boolean) || [],
      
      // Investment criteria relationship filters
      hasInvestmentCriteria: searchParams.get('hasInvestmentCriteria'),
      targetReturnMin: searchParams.get('targetReturnMin'),
      targetReturnMax: searchParams.get('targetReturnMax'),
      dealSizeMin: searchParams.get('dealSizeMin'),
      dealSizeMax: searchParams.get('dealSizeMax'),
      criteriaPropertyTypes: searchParams.get('criteriaPropertyTypes')?.split(',').filter(Boolean) || [],
      criteriaRegions: searchParams.get('criteriaRegions')?.split(',').filter(Boolean) || [],
      criteriaStates: searchParams.get('criteriaStates')?.split(',').filter(Boolean) || [],
      
      // Pagination & sorting
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '25'),
      sortBy: searchParams.get('sortBy') || 'updated_at',
      sortOrder: searchParams.get('sortOrder') || 'desc'
    }

    // Build WHERE clause dynamically
    const whereConditions = ['c.company_id IS NOT NULL']
    const queryParams: any[] = []
    let paramIndex = 1

    // Search functionality (across multiple tables)
    if (filters.searchTerm) {
      whereConditions.push(`(
        LOWER(c.company_name) LIKE LOWER($${paramIndex}) 
                  OR LOWER(c.company_id::text) LIKE LOWER($${paramIndex})
        OR LOWER(c.industry) LIKE LOWER($${paramIndex})
          OR LOWER(ced.companyname) LIKE LOWER($${paramIndex})
          OR LOWER(ced.companytype) LIKE LOWER($${paramIndex})
        OR LOWER(ced.businessmodel) LIKE LOWER($${paramIndex})
      )`)
      queryParams.push(`%${filters.searchTerm}%`)
      paramIndex++
    }

    if (filters.companyName) {
      whereConditions.push(`(
        LOWER(c.company_name) LIKE LOWER($${paramIndex})
        OR LOWER(ced.companyname) LIKE LOWER($${paramIndex})
      )`)
      queryParams.push(`%${filters.companyName}%`)
      paramIndex++
    }

    if (filters.companyId) {
      whereConditions.push(`LOWER(c.company_id::text) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.companyId}%`)
      paramIndex++
    }

    // Company profile filters (from company_extracted_data table)
    if (filters.companyType.length > 0) {
      const companyTypeConditions = filters.companyType.map(() => {
        const condition = `LOWER(ced.companytype) = LOWER($${paramIndex})`
        queryParams.push(filters.companyType[paramIndex - queryParams.length - 1])
        paramIndex++
        return condition
      })
      whereConditions.push(`(${companyTypeConditions.join(' OR ')})`)
    }

    if (filters.businessModel) {
      whereConditions.push(`LOWER(ced.businessmodel) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.businessModel}%`)
      paramIndex++
    }

    if (filters.companyIndustry.length > 0) {
      const industryConditions = filters.companyIndustry.map(() => {
        const condition = `LOWER(ced.companyindustry) = LOWER($${paramIndex})`
        queryParams.push(filters.companyIndustry[paramIndex - queryParams.length - 1])
        paramIndex++
        return condition
      })
      whereConditions.push(`(${industryConditions.join(' OR ')})`)
    }

    if (filters.capitalPosition.length > 0) {
      const capitalConditions = filters.capitalPosition.map(() => {
        const condition = `LOWER(ced.capitalposition) = LOWER($${paramIndex})`
        queryParams.push(filters.capitalPosition[paramIndex - queryParams.length - 1])
        paramIndex++
        return condition
      })
      whereConditions.push(`(${capitalConditions.join(' OR ')})`)
    }

    // Financial metrics with simple numeric parsing for extracted data
    if (filters.fundSizeMin) {
      whereConditions.push(`(
        CASE 
          WHEN ced.fundsize ~* '\\$([0-9,\\.]+)([KMB])' THEN
            CASE 
              WHEN ced.fundsize ~* 'B' THEN 
                CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000000
              WHEN ced.fundsize ~* 'M' THEN 
                CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000
              WHEN ced.fundsize ~* 'K' THEN 
                CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT)
              ELSE 0
            END
          ELSE 0
        END >= $${paramIndex}
      )`)
      queryParams.push(parseFloat(filters.fundSizeMin))
      paramIndex++
    }

    if (filters.fundSizeMax) {
      whereConditions.push(`(
        CASE 
          WHEN ced.fundsize ~* '\\$([0-9,\\.]+)([KMB])' THEN
            CASE 
              WHEN ced.fundsize ~* 'B' THEN 
                CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000000
              WHEN ced.fundsize ~* 'M' THEN 
                CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000
              WHEN ced.fundsize ~* 'K' THEN 
                CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT)
              ELSE 0
            END
          ELSE 0
        END <= $${paramIndex}
      )`)
      queryParams.push(parseFloat(filters.fundSizeMax))
      paramIndex++
    }

    // Similar AUM filtering from extracted data
    if (filters.aumMin) {
      whereConditions.push(`(
        CASE 
          WHEN ced.aum ~* '\\$([0-9,\\.]+)([KMB])' THEN
            CASE 
              WHEN ced.aum ~* 'B' THEN 
                CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000000
              WHEN ced.aum ~* 'M' THEN 
                CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000
              WHEN ced.aum ~* 'K' THEN 
                CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT)
              ELSE 0
            END
          ELSE 0
        END >= $${paramIndex}
      )`)
      queryParams.push(parseFloat(filters.aumMin))
      paramIndex++
    }

    if (filters.aumMax) {
      whereConditions.push(`(
        CASE 
          WHEN ced.aum ~* '\\$([0-9,\\.]+)([KMB])' THEN
            CASE 
              WHEN ced.aum ~* 'B' THEN 
                CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000000
              WHEN ced.aum ~* 'M' THEN 
                CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000
              WHEN ced.aum ~* 'K' THEN 
                CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT)
              ELSE 0
            END
          ELSE 0
        END <= $${paramIndex}
      )`)
      queryParams.push(parseFloat(filters.aumMax))
      paramIndex++
    }

    // Company size filters (from extracted data)
    if (filters.numberOfPropertiesMin) {
      whereConditions.push(`ced.numberofproperties >= $${paramIndex}`)
      queryParams.push(parseInt(filters.numberOfPropertiesMin))
      paramIndex++
    }

    if (filters.numberOfPropertiesMax) {
      whereConditions.push(`ced.numberofproperties <= $${paramIndex}`)
      queryParams.push(parseInt(filters.numberOfPropertiesMax))
      paramIndex++
    }

    if (filters.numberOfOfficesMin) {
      whereConditions.push(`ced.numberofoffices >= $${paramIndex}`)
      queryParams.push(parseInt(filters.numberOfOfficesMin))
      paramIndex++
    }

    if (filters.numberOfOfficesMax) {
      whereConditions.push(`ced.numberofoffices <= $${paramIndex}`)
      queryParams.push(parseInt(filters.numberOfOfficesMax))
      paramIndex++
    }

    if (filters.foundedYearMin) {
      whereConditions.push(`ced.foundedyear >= $${paramIndex}`)
      queryParams.push(parseInt(filters.foundedYearMin))
      paramIndex++
    }

    if (filters.foundedYearMax) {
      whereConditions.push(`ced.foundedyear <= $${paramIndex}`)
      queryParams.push(parseInt(filters.foundedYearMax))
      paramIndex++
    }

    // Track Record Metrics (text search in extracted data)
    if (filters.totalTransactions) {
      whereConditions.push(`LOWER(ced.totaltransactions) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.totalTransactions}%`)
      paramIndex++
    }

    if (filters.totalSquareFeet) {
      whereConditions.push(`LOWER(ced.totalsquarefeet) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.totalSquareFeet}%`)
      paramIndex++
    }

    if (filters.totalUnits) {
      whereConditions.push(`LOWER(ced.totalunits) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.totalUnits}%`)
      paramIndex++
    }

    if (filters.historicalReturns) {
      whereConditions.push(`LOWER(ced.historicalreturns) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.historicalReturns}%`)
      paramIndex++
    }

    if (filters.portfolioValue) {
      whereConditions.push(`LOWER(ced.portfoliovalue) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.portfolioValue}%`)
      paramIndex++
    }

    // Investment Focus & Strategy (JSONB array filters from extracted data)
    if (filters.investmentFocus.length > 0) {
      whereConditions.push(`ced.investmentfocus ?| $${paramIndex}::text[]`)
      queryParams.push(filters.investmentFocus)
      paramIndex++
    }

    if (filters.propertyTypes.length > 0) {
      whereConditions.push(`ced.propertytypes ?| $${paramIndex}::text[]`)
      queryParams.push(filters.propertyTypes)
      paramIndex++
    }

    if (filters.strategies.length > 0) {
      whereConditions.push(`ced.strategies ?| $${paramIndex}::text[]`)
      queryParams.push(filters.strategies)
      paramIndex++
    }

    if (filters.assetClasses.length > 0) {
      whereConditions.push(`ced.assetclasses ?| $${paramIndex}::text[]`)
      queryParams.push(filters.assetClasses)
      paramIndex++
    }

    if (filters.extractedGeographicFocus.length > 0) {
      whereConditions.push(`ced.geographicfocus ?| $${paramIndex}::text[]`)
      queryParams.push(filters.extractedGeographicFocus)
      paramIndex++
    }

    if (filters.targetMarkets.length > 0) {
      whereConditions.push(`ced.targetmarkets ?| $${paramIndex}::text[]`)
      queryParams.push(filters.targetMarkets)
      paramIndex++
    }

    if (filters.capitalSources.length > 0) {
      whereConditions.push(`ced.capitalsources ?| $${paramIndex}::text[]`)
      queryParams.push(filters.capitalSources)
      paramIndex++
    }

    if (filters.financialProducts.length > 0) {
      whereConditions.push(`ced.financialproducts ?| $${paramIndex}::text[]`)
      queryParams.push(filters.financialProducts)
      paramIndex++
    }

    if (filters.partnerships.length > 0) {
      whereConditions.push(`ced.partnerships ?| $${paramIndex}::text[]`)
      queryParams.push(filters.partnerships)
      paramIndex++
    }

    if (filters.officeLocations.length > 0) {
      whereConditions.push(`ced.officelocations ?| $${paramIndex}::text[]`)
      queryParams.push(filters.officeLocations)
      paramIndex++
    }

    // Deal Size & Investment Criteria (text search in extracted data)
    if (filters.minimumDealSize) {
      whereConditions.push(`LOWER(ced.minimumdealsize) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.minimumDealSize}%`)
      paramIndex++
    }

    if (filters.maximumDealSize) {
      whereConditions.push(`LOWER(ced.maximumdealsize) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.maximumDealSize}%`)
      paramIndex++
    }

    if (filters.holdPeriod) {
      whereConditions.push(`LOWER(ced.holdperiod) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.holdPeriod}%`)
      paramIndex++
    }

    if (filters.riskProfile) {
      whereConditions.push(`LOWER(ced.riskprofile) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.riskProfile}%`)
      paramIndex++
    }

    if (filters.targetReturn) {
      whereConditions.push(`LOWER(ced.targetreturn) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.targetReturn}%`)
      paramIndex++
    }

    // Contact Information (text search in extracted data)
    if (filters.website) {
      whereConditions.push(`LOWER(ced.website) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.website}%`)
      paramIndex++
    }

    if (filters.mainPhone) {
      whereConditions.push(`LOWER(ced.mainphone) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.mainPhone}%`)
      paramIndex++
    }

    if (filters.mainEmail) {
      whereConditions.push(`LOWER(ced.mainemail) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.mainEmail}%`)
      paramIndex++
    }

    if (filters.linkedinUrl) {
      whereConditions.push(`LOWER(ced.linkedin_url) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.linkedinUrl}%`)
      paramIndex++
    }

    // Mission & Strategy (text search in extracted data)
    if (filters.mission) {
      whereConditions.push(`LOWER(ced.mission) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.mission}%`)
      paramIndex++
    }

    if (filters.approach) {
      whereConditions.push(`LOWER(ced.approach) LIKE LOWER($${paramIndex})`)
      queryParams.push(`%${filters.approach}%`)
      paramIndex++
    }

    // Geographic filters (legacy from companies table)
    if (filters.headquarters.length > 0) {
      const headquartersConditions = filters.headquarters.map(() => {
          const condition = `LOWER(ced.headquarters) = LOWER($${paramIndex})`
        queryParams.push(filters.headquarters[paramIndex - queryParams.length - 1])
        paramIndex++
        return condition
      })
      whereConditions.push(`(${headquartersConditions.join(' OR ')})`)
    }

    // Basic company fields
    if (filters.companyState.length > 0) {
      const stateConditions = filters.companyState.map(() => {
        const condition = `LOWER(c.company_state) = LOWER($${paramIndex})`
        queryParams.push(filters.companyState[paramIndex - queryParams.length - 1])
        paramIndex++
        return condition
      })
      whereConditions.push(`(${stateConditions.join(' OR ')})`)
    }

    if (filters.companyCity.length > 0) {
      const cityConditions = filters.companyCity.map(() => {
        const condition = `LOWER(c.company_city) = LOWER($${paramIndex})`
        queryParams.push(filters.companyCity[paramIndex - queryParams.length - 1])
        paramIndex++
        return condition
      })
      whereConditions.push(`(${cityConditions.join(' OR ')})`)
    }

    if (filters.companyCountry.length > 0) {
      const countryConditions = filters.companyCountry.map(() => {
        const condition = `LOWER(c.company_country) = LOWER($${paramIndex})`
        queryParams.push(filters.companyCountry[paramIndex - queryParams.length - 1])
        paramIndex++
        return condition
      })
      whereConditions.push(`(${countryConditions.join(' OR ')})`)
    }

    // Legacy overview structure filters (property types, asset types, etc.)
    if (filters.assetTypes.length > 0) {
      whereConditions.push(`c.overview->'investment_program'->>'asset_type' && $${paramIndex}::text[]`)
      queryParams.push(filters.assetTypes)
      paramIndex++
    }

    if (filters.dealStructure.length > 0) {
      whereConditions.push(`c.overview->'investment_program'->'deal_structure' && $${paramIndex}::text[]`)
      queryParams.push(filters.dealStructure)
      paramIndex++
    }

    // Processing status filters
    if (filters.processed !== null && filters.processed !== undefined) {
      whereConditions.push(`c.processed = $${paramIndex}`)
      queryParams.push(filters.processed === 'true')
      paramIndex++
    }

    if (filters.extracted !== null && filters.extracted !== undefined) {
      whereConditions.push(`c.extracted = $${paramIndex}`)
      queryParams.push(filters.extracted === 'true')
      paramIndex++
    }

    if (filters.processingState.length > 0) {
      const processingConditions = filters.processingState.map(() => {
        const condition = `c.processing_state = $${paramIndex}`
        queryParams.push(filters.processingState[paramIndex - queryParams.length - 1])
        paramIndex++
        return condition
      })
      whereConditions.push(`(${processingConditions.join(' OR ')})`)
    }

    if (filters.websiteScrapingStatus.length > 0) {
      const scrapingConditions = filters.websiteScrapingStatus.map(() => {
        const condition = `c.website_scraping_status = $${paramIndex}`
        queryParams.push(filters.websiteScrapingStatus[paramIndex - queryParams.length - 1])
        paramIndex++
        return condition
      })
      whereConditions.push(`(${scrapingConditions.join(' OR ')})`)
    }

    if (filters.companyOverviewStatus.length > 0) {
      const overviewConditions = filters.companyOverviewStatus.map(() => {
        const condition = `c.company_overview_status = $${paramIndex}`
        queryParams.push(filters.companyOverviewStatus[paramIndex - queryParams.length - 1])
        paramIndex++
        return condition
      })
      whereConditions.push(`(${overviewConditions.join(' OR ')})`)
    }

    // Investment criteria relationship filters
    if (filters.hasInvestmentCriteria !== null && filters.hasInvestmentCriteria !== undefined) {
      if (filters.hasInvestmentCriteria === 'true') {
        whereConditions.push(`ic.criteria_id IS NOT NULL`)
      } else {
        whereConditions.push(`ic.criteria_id IS NULL`)
      }
    }

    if (filters.targetReturnMin) {
      whereConditions.push(`ic.target_return >= $${paramIndex}`)
      queryParams.push(parseFloat(filters.targetReturnMin) / 100) // Convert percentage to decimal
      paramIndex++
    }

    if (filters.targetReturnMax) {
      whereConditions.push(`ic.target_return <= $${paramIndex}`)
      queryParams.push(parseFloat(filters.targetReturnMax) / 100) // Convert percentage to decimal
      paramIndex++
    }

    if (filters.dealSizeMin) {
      whereConditions.push(`ic.minimum_deal_size >= $${paramIndex}`)
      queryParams.push(parseFloat(filters.dealSizeMin))
      paramIndex++
    }

    if (filters.dealSizeMax) {
      whereConditions.push(`ic.maximum_deal_size <= $${paramIndex}`)
      queryParams.push(parseFloat(filters.dealSizeMax))
      paramIndex++
    }

    // Array filters for investment criteria
    if (filters.criteriaPropertyTypes.length > 0) {
      whereConditions.push(`ic.property_types && $${paramIndex}::text[]`)
      queryParams.push(filters.criteriaPropertyTypes)
      paramIndex++
    }

    if (filters.criteriaRegions.length > 0) {
      whereConditions.push(`ic.region && $${paramIndex}::text[]`)
      queryParams.push(filters.criteriaRegions)
      paramIndex++
    }

    if (filters.criteriaStates.length > 0) {
      whereConditions.push(`ic.state && $${paramIndex}::text[]`)
      queryParams.push(filters.criteriaStates)
      paramIndex++
    }

    // Build the main query with LEFT JOINs to company_extracted_data and investment_criteria
    const baseQuery = `
      FROM companies c
      LEFT JOIN company_extracted_data ced ON c.company_id::bigint = ced.company_id
      LEFT JOIN investment_criteria ic ON (c.company_id::text = ic.entity_id AND ic.entity_type LIKE 'Company%' AND ic.is_active = true)
      WHERE ${whereConditions.join(' AND ')}
    `

    // Get total count
    const countQuery = `SELECT COUNT(DISTINCT c.company_id) as total ${baseQuery}`
    const countResult = await pool.query(countQuery, queryParams)
    const totalItems = parseInt(countResult.rows[0].total)

    // Calculate pagination
    const totalPages = Math.ceil(totalItems / filters.limit)
    const offset = (filters.page - 1) * filters.limit

    // Build ORDER BY clause with support for extracted data fields
    const validSortFields = [
      'updated_at', 'created_at', 'company_name', 'industry',
      'fund_size_parsed', 'aum_parsed', 'founded_year', 
      'number_of_properties', 'number_of_offices', 'employees_max',
      'investment_criteria_count', 'criteria_target_return_max', 'criteria_deal_size_max',
      // New extracted data sort fields (prioritizing non-null values)
      'extracted_fund_size', 'extracted_aum', 'extracted_founded_year',
      'extracted_number_of_properties', 'extracted_number_of_offices',
      'extracted_total_transactions', 'extracted_portfolio_value', 
      'extracted_historical_returns', 'extracted_data_completeness'
    ]
    
    const sortField = validSortFields.includes(filters.sortBy) ? filters.sortBy : 'updated_at'
    const sortDirection = filters.sortOrder === 'asc' ? 'ASC' : 'DESC'

    // Main query with sorting and pagination, including extracted data (excluding JSONB fields for now)
    const mainQuery = `
      SELECT DISTINCT
        c.company_id,
        c.company_name,
        c.industry,
        c.company_state,
        c.company_city,
        c.company_country,
        c.processed,
        c.extracted,
        c.created_at,
        c.updated_at,
        c.company_linkedin,
        ced.companytype,
        ced.businessmodel,
        ced.fundsize,
        ced.aum,
        ced.numberofproperties,
        ced.headquarters as extracted_headquarters,
        ced.numberofoffices,
        ced.foundedyear,
        ced.numberofemployees,
        ced.totaltransactions,
        ced.totalsquarefeet,
        ced.totalunits,
        ced.historicalreturns,
        ced.portfoliovalue,
        ced.website as extracted_website,
        ced.mainphone,
        ced.mainemail,
        ced.linkedin_url,
        ced.mission,
        ced.approach,
        ced.targetreturn,
        ced.dealsize,
        ced.minimumdealsize,
        ced.maximumdealsize,
        ced.holdperiod,
        ced.riskprofile,
        ced.companyindustry,
        ced.capitalposition,
        COUNT(ic.criteria_id) as investment_criteria_count,
        MAX(ic.target_return) as criteria_target_return_max,
        MAX(ic.maximum_deal_size) as criteria_deal_size_max,
        -- Parsed financial metrics for sorting (using extracted data only)
        CASE 
          WHEN ced.fundsize ~* '\\$([0-9,\\.]+)([KMB])' THEN
            CASE 
              WHEN ced.fundsize ~* 'B' THEN 
                CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000000
              WHEN ced.fundsize ~* 'M' THEN 
                CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000
              WHEN ced.fundsize ~* 'K' THEN 
                CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT)
              ELSE NULL
            END
          ELSE NULL
        END as fund_size_parsed,
        CASE 
          WHEN ced.aum ~* '\\$([0-9,\\.]+)([KMB])' THEN
            CASE 
              WHEN ced.aum ~* 'B' THEN 
                CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000000
              WHEN ced.aum ~* 'M' THEN 
                CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000
              WHEN ced.aum ~* 'K' THEN 
                CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT)
              ELSE NULL
            END
          ELSE NULL
        END as aum_parsed,
        ced.foundedyear as founded_year,
        ced.numberofproperties as number_of_properties,
        ced.numberofoffices as number_of_offices,
        -- Special sort fields for prioritizing non-null extracted data
        CASE WHEN ced.fundsize IS NOT NULL THEN 
          CASE 
            WHEN ced.fundsize ~* 'B' THEN 
              CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000000
            WHEN ced.fundsize ~* 'M' THEN 
              CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000
            WHEN ced.fundsize ~* 'K' THEN 
              CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT)
            ELSE 0
          END
        ELSE NULL END as extracted_fund_size,
        CASE WHEN ced.aum IS NOT NULL THEN 
          CASE 
            WHEN ced.aum ~* 'B' THEN 
              CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000000
            WHEN ced.aum ~* 'M' THEN 
              CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000
            WHEN ced.aum ~* 'K' THEN 
              CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT)
            ELSE 0
          END
        ELSE NULL END as extracted_aum,
        CASE WHEN ced.foundedyear IS NOT NULL THEN ced.foundedyear ELSE NULL END as extracted_founded_year,
        CASE WHEN ced.numberofproperties IS NOT NULL THEN ced.numberofproperties ELSE NULL END as extracted_number_of_properties,
        CASE WHEN ced.numberofoffices IS NOT NULL THEN ced.numberofoffices ELSE NULL END as extracted_number_of_offices,
        CASE WHEN ced.totaltransactions IS NOT NULL THEN 1 ELSE 0 END as extracted_total_transactions,
        CASE WHEN ced.portfoliovalue IS NOT NULL THEN 1 ELSE 0 END as extracted_portfolio_value,
        CASE WHEN ced.historicalreturns IS NOT NULL THEN 1 ELSE 0 END as extracted_historical_returns,
        -- Data completeness score (count of non-null extracted fields)
        (
          CASE WHEN ced.companytype IS NOT NULL THEN 1 ELSE 0 END +
          CASE WHEN ced.businessmodel IS NOT NULL THEN 1 ELSE 0 END +
          CASE WHEN ced.fundsize IS NOT NULL THEN 1 ELSE 0 END +
          CASE WHEN ced.aum IS NOT NULL THEN 1 ELSE 0 END +
          CASE WHEN ced.numberofproperties IS NOT NULL THEN 1 ELSE 0 END +
          CASE WHEN ced.headquarters IS NOT NULL THEN 1 ELSE 0 END +
          CASE WHEN ced.numberofoffices IS NOT NULL THEN 1 ELSE 0 END +
          CASE WHEN ced.foundedyear IS NOT NULL THEN 1 ELSE 0 END +
          CASE WHEN ced.totaltransactions IS NOT NULL THEN 1 ELSE 0 END +
          CASE WHEN ced.portfoliovalue IS NOT NULL THEN 1 ELSE 0 END
        ) as extracted_data_completeness
      ${baseQuery}
      GROUP BY c.company_id, c.company_name, c.industry, c.company_state, c.company_city, c.company_country, 
               c.processed, c.extracted, c.created_at, c.updated_at, c.company_linkedin,
               ced.companytype, ced.businessmodel, ced.fundsize, ced.aum, ced.numberofproperties,
               ced.headquarters, ced.numberofoffices, ced.foundedyear, ced.numberofemployees,
               ced.totaltransactions, ced.totalsquarefeet, ced.totalunits, ced.historicalreturns,
               ced.portfoliovalue, ced.website, ced.mainphone, ced.mainemail,
               ced.linkedin_url, ced.mission, ced.approach, ced.targetreturn, ced.dealsize,
               ced.minimumdealsize, ced.maximumdealsize, ced.holdperiod, ced.riskprofile,
               ced.companyindustry, ced.capitalposition
      ORDER BY ${sortField} ${sortDirection} NULLS LAST
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    queryParams.push(filters.limit, offset)
    
    const result = await pool.query(mainQuery, queryParams)
    const companies = result.rows

    return NextResponse.json({
      companies,
      pagination: {
        currentPage: filters.page,
        totalPages,
        totalItems,
        itemsPerPage: filters.limit,
        hasNextPage: filters.page < totalPages,
        hasPreviousPage: filters.page > 1
      },
      filters: filters // Return applied filters for debugging
    })

  } catch (error) {
    console.error('Error fetching companies:', error)
    return NextResponse.json(
      { error: 'Failed to fetch companies', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
} 