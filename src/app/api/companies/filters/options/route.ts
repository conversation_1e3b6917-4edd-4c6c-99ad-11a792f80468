import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

// MCP-driven function to build hierarchical mappings for companies
async function buildHierarchicalMappings() {
  try {
    const mappingsQuery = `
      SELECT type, level_1, value_1, level_2, value_2, level_3, value_3 
      FROM central_mapping 
      WHERE is_active = true 
      ORDER BY type, value_1, value_2, value_3
    `
    const mappingsResult = await pool.query(mappingsQuery)
    
    const hierarchicalMappings: Record<string, any> = {}
    
    mappingsResult.rows.forEach(row => {
      const { type, level_1, value_1, level_2, value_2, level_3, value_3 } = row
      
      if (!hierarchicalMappings[type]) {
        hierarchicalMappings[type] = {
          flat: new Set(), // For simple flat lists
          hierarchical: {} // For nested structures
        }
      }
      
      // Always add to flat list for backwards compatibility
      if (value_1) {
        hierarchicalMappings[type].flat.add(value_1)
      }
      
      // Build hierarchical structure if there are sub-levels
      if (value_1) {
        if (!hierarchicalMappings[type].hierarchical[value_1]) {
          hierarchicalMappings[type].hierarchical[value_1] = {
            value: value_1,
            children: {}
          }
        }
        
        if (level_2 && value_2) {
          if (!hierarchicalMappings[type].hierarchical[value_1].children[level_2]) {
            hierarchicalMappings[type].hierarchical[value_1].children[level_2] = new Set()
          }
          hierarchicalMappings[type].hierarchical[value_1].children[level_2].add(value_2)
          
          // Also add sub-items to flat list for filtering
          hierarchicalMappings[type].flat.add(value_2)
        }
      }
    })
    
    // Convert Sets to Arrays for JSON serialization
    Object.keys(hierarchicalMappings).forEach(type => {
      hierarchicalMappings[type].flat = Array.from(hierarchicalMappings[type].flat)
      
      Object.keys(hierarchicalMappings[type].hierarchical).forEach(parentKey => {
        const parent = hierarchicalMappings[type].hierarchical[parentKey]
        Object.keys(parent.children).forEach(childType => {
          parent.children[childType] = Array.from(parent.children[childType])
        })
      })
    })
    
    return hierarchicalMappings
  } catch (error) {
    console.error('Error building hierarchical mappings:', error)
    return {}
  }
}

// Function to get actual data ranges from companies
async function getCompanyDataRanges() {
  try {
    const rangeQuery = `
      SELECT 
        MIN(
          CASE 
            WHEN ced.fundsize ~* '\\$([0-9,\\.]+)([KMB])' THEN
              CASE 
                WHEN ced.fundsize ~* 'B' THEN 
                  CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000000
                WHEN ced.fundsize ~* 'M' THEN 
                  CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000
                WHEN ced.fundsize ~* 'K' THEN 
                  CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT)
                ELSE NULL
              END
            ELSE NULL
          END
        ) as min_fund_size,
        MAX(
          CASE 
            WHEN ced.fundsize ~* '\\$([0-9,\\.]+)([KMB])' THEN
              CASE 
                WHEN ced.fundsize ~* 'B' THEN 
                  CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000000
                WHEN ced.fundsize ~* 'M' THEN 
                  CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000
                WHEN ced.fundsize ~* 'K' THEN 
                  CAST(REGEXP_REPLACE(ced.fundsize, '[^0-9\\.]', '', 'g') AS FLOAT)
                ELSE NULL
              END
            ELSE NULL
          END
        ) as max_fund_size,
        MIN(
          CASE 
            WHEN ced.aum ~* '\\$([0-9,\\.]+)([KMB])' THEN
              CASE 
                WHEN ced.aum ~* 'B' THEN 
                  CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000000
                WHEN ced.aum ~* 'M' THEN 
                  CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000
                WHEN ced.aum ~* 'K' THEN 
                  CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT)
                ELSE NULL
              END
            ELSE NULL
          END
        ) as min_aum,
        MAX(
          CASE 
            WHEN ced.aum ~* '\\$([0-9,\\.]+)([KMB])' THEN
              CASE 
                WHEN ced.aum ~* 'B' THEN 
                  CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000000
                WHEN ced.aum ~* 'M' THEN 
                  CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT) * 1000
                WHEN ced.aum ~* 'K' THEN 
                  CAST(REGEXP_REPLACE(ced.aum, '[^0-9\\.]', '', 'g') AS FLOAT)
                ELSE NULL
              END
            ELSE NULL
          END
        ) as max_aum,
        MIN(ced.numberofproperties) as min_properties,
        MAX(ced.numberofproperties) as max_properties,
        MIN(ced.numberofoffices) as min_offices,
        MAX(ced.numberofoffices) as max_offices,
        MIN(ced.foundedyear) as min_founded_year,
        MAX(ced.foundedyear) as max_founded_year,
        MIN(ced.numberofemployees) as min_employees,
        MAX(ced.numberofemployees) as max_employees
      FROM company_extracted_data ced 
      WHERE ced.company_id IS NOT NULL
    `
    
    const rangeResult = await pool.query(rangeQuery)
    const ranges = rangeResult.rows[0]
    
    return {
      fundSize: {
        min: Math.floor(ranges.min_fund_size || 1000),
        max: Math.ceil(ranges.max_fund_size || *********)
      },
      aum: {
        min: Math.floor(ranges.min_aum || 1000),
        max: Math.ceil(ranges.max_aum || *********)
      },
      numberOfProperties: {
        min: ranges.min_properties || 1,
        max: ranges.max_properties || 10000
      },
      numberOfOffices: {
        min: ranges.min_offices || 1,
        max: ranges.max_offices || 100
      },
      foundedYear: {
        min: ranges.min_founded_year || 1900,
        max: ranges.max_founded_year || new Date().getFullYear()
      },
      numberOfEmployees: {
        min: ranges.min_employees || 1,
        max: ranges.max_employees || 10000
      }
    }
  } catch (error) {
    console.error('Error getting company data ranges:', error)
    return {
      fundSize: { min: 1000, max: ********* },
      aum: { min: 1000, max: ********* },
      numberOfProperties: { min: 1, max: 10000 },
      numberOfOffices: { min: 1, max: 100 },
      foundedYear: { min: 1900, max: new Date().getFullYear() },
      numberOfEmployees: { min: 1, max: 10000 }
    }
  }
}

export async function GET() {
  try {
    // Get hierarchical mappings using MCP
    const hierarchicalMappings = await buildHierarchicalMappings()
    
    // Get actual data ranges
    const ranges = await getCompanyDataRanges()
    
    // Build filter options with both flat and hierarchical data
    const filterOptions: any = {
      // Hierarchical mappings for advanced filtering
      hierarchicalMappings,
      
      // Company profile options from central_mapping - using parent categories only for hierarchical types
      companyTypes: hierarchicalMappings['Company Type']?.flat || [],
      businessModels: hierarchicalMappings['Business Model']?.flat || [],
      companyIndustries: hierarchicalMappings['Industry']?.flat || [],
      capitalPositions: hierarchicalMappings['Capital Position']?.hierarchical ? 
        Object.keys(hierarchicalMappings['Capital Position'].hierarchical) : 
        hierarchicalMappings['Capital Position']?.flat || [],
      
      // Property & asset options from central_mapping - using parent categories only for hierarchical types
      propertyTypes: hierarchicalMappings['Property Type']?.hierarchical ? 
        Object.keys(hierarchicalMappings['Property Type'].hierarchical) : 
        hierarchicalMappings['Property Type']?.flat || [],
      propertySubcategories: [], // Will be populated from hierarchical data below
      assetTypes: hierarchicalMappings['Asset Type']?.flat || [],
      dealStructures: hierarchicalMappings['Deal Structure']?.flat || [],
      investmentFocus: hierarchicalMappings['Investment Focus']?.flat || [],
      geographicFocus: hierarchicalMappings['Geographic Focus']?.flat || [],
      
      // Geographic data - using parent categories only for hierarchical types
      countries: ['United States'], // Default, can be expanded
      regions: hierarchicalMappings['U.S Regions']?.hierarchical ? 
        Object.keys(hierarchicalMappings['U.S Regions'].hierarchical) : 
        hierarchicalMappings['U.S Regions']?.flat || [],
      states: [],
      cities: [],
      headquarters: [],
      
      // Processing status options (hardcoded common values)
      processingStates: ['pending', 'processing', 'completed', 'failed'],
      websiteScrapingStatuses: ['pending', 'completed', 'failed'],
      companyOverviewStatuses: ['pending', 'completed', 'failed'],
      
      // Investment criteria options (from investment_criteria table) - using parent categories only for hierarchical types
      criteriaPropertyTypes: hierarchicalMappings['Property Type']?.hierarchical ? 
        Object.keys(hierarchicalMappings['Property Type'].hierarchical) : 
        hierarchicalMappings['Property Type']?.flat || [],
      criteriaStrategies: hierarchicalMappings['Strategies']?.flat || [],
      criteriaRegions: hierarchicalMappings['U.S Regions']?.hierarchical ? 
        Object.keys(hierarchicalMappings['U.S Regions'].hierarchical) : 
        hierarchicalMappings['U.S Regions']?.flat || [],
      criteriaStates: [],
      criteriaCities: [],
      
      // Range options
      ranges
    }

    // Extract property subcategories from Property Type hierarchical data
    if (hierarchicalMappings['Property Type']?.hierarchical) {
      const subcategoriesSet = new Set<string>()
      Object.values(hierarchicalMappings['Property Type'].hierarchical).forEach((parent: any) => {
        if (parent.children['Property Subproperty Type']) {
          parent.children['Property Subproperty Type'].forEach((subType: string) => {
            subcategoriesSet.add(subType)
          })
        }
      })
      filterOptions.propertySubcategories = Array.from(subcategoriesSet).sort()
    }

    // Extract states from U.S Regions hierarchical data
    if (hierarchicalMappings['U.S Regions']?.hierarchical) {
      const statesSet = new Set<string>()
      Object.values(hierarchicalMappings['U.S Regions'].hierarchical).forEach((parent: any) => {
        if (parent.children['U.S Regions States']) {
          parent.children['U.S Regions States'].forEach((state: string) => {
            statesSet.add(state)
          })
        }
      })
      filterOptions.states = Array.from(statesSet).sort()
      filterOptions.criteriaStates = Array.from(statesSet).sort()
    }

    // Get additional data from companies and company_extracted_data tables
    const companiesDataQuery = `
      SELECT DISTINCT
        c.company_state as state,
        c.company_city as city,
        c.company_country as country,
        c.industry,
        ced.headquarters,
        ced.companytype as company_type,
        ced.businessmodel as business_model
      FROM companies c 
      LEFT JOIN company_extracted_data ced ON c.company_id::bigint = ced.company_id
      WHERE c.company_id IS NOT NULL 
        AND (c.company_state IS NOT NULL OR c.company_city IS NOT NULL OR c.company_country IS NOT NULL 
             OR c.industry IS NOT NULL OR ced.headquarters IS NOT NULL OR ced.companytype IS NOT NULL)
    `
    
    try {
      const companiesDataResult = await pool.query(companiesDataQuery)
      const companiesData = companiesDataResult.rows
      
      const statesFromCompanies = new Set<string>()
      const citiesFromCompanies = new Set<string>()
      const countriesFromCompanies = new Set<string>()
      const headquartersFromCompanies = new Set<string>()
      const industriesFromCompanies = new Set<string>()
      const companyTypesFromCompanies = new Set<string>()
      const businessModelsFromCompanies = new Set<string>()
      
      companiesData.forEach((row: any) => {
        if (row.state && row.state.trim()) statesFromCompanies.add(row.state.trim())
        if (row.city && row.city.trim()) citiesFromCompanies.add(row.city.trim())
        if (row.country && row.country.trim()) countriesFromCompanies.add(row.country.trim())
        if (row.headquarters && row.headquarters.trim()) headquartersFromCompanies.add(row.headquarters.trim())
        if (row.industry && row.industry.trim()) industriesFromCompanies.add(row.industry.trim())
        if (row.company_type && row.company_type.trim()) companyTypesFromCompanies.add(row.company_type.trim())
        if (row.business_model && row.business_model.trim()) businessModelsFromCompanies.add(row.business_model.trim())
      })
      
      // Merge with existing data (prioritize mapping data)
      const allStates = new Set([...filterOptions.states, ...Array.from(statesFromCompanies)])
      filterOptions.states = Array.from(allStates).sort()
      filterOptions.criteriaStates = Array.from(allStates).sort()
      
      filterOptions.cities = Array.from(citiesFromCompanies).sort()
      filterOptions.criteriaCities = Array.from(citiesFromCompanies).sort()
      
      const allCountries = new Set([...filterOptions.countries, ...Array.from(countriesFromCompanies)])
      filterOptions.countries = Array.from(allCountries).sort()
      
      filterOptions.headquarters = Array.from(headquartersFromCompanies).sort()
      
      // Merge company types and business models
      const allCompanyTypes = new Set([...filterOptions.companyTypes, ...Array.from(companyTypesFromCompanies)])
      filterOptions.companyTypes = Array.from(allCompanyTypes).sort()
      
      const allBusinessModels = new Set([...filterOptions.businessModels, ...Array.from(businessModelsFromCompanies)])
      filterOptions.businessModels = Array.from(allBusinessModels).sort()
      
      // Add industries from companies (since this isn't in central_mapping)
      const allIndustries = new Set([...filterOptions.companyIndustries, ...Array.from(industriesFromCompanies)])
      filterOptions.companyIndustries = Array.from(allIndustries).sort()
      
    } catch (error) {
      console.error('Error fetching company-specific data:', error)
      // Use defaults if query fails
    }

    // Get investment criteria ranges for companies
    const investmentCriteriaRangesQuery = `
      SELECT 
        MIN(ic.target_return) as min_target_return,
        MAX(ic.target_return) as max_target_return,
        MIN(ic.minimum_deal_size) as min_deal_size,
        MAX(ic.maximum_deal_size) as max_deal_size,
        MIN(ic.min_hold_period) as min_hold_period,
        MAX(ic.max_hold_period) as max_hold_period
      FROM investment_criteria ic 
      WHERE ic.entity_type LIKE 'Company%' AND ic.is_active = true
    `
    
    try {
      const criteriaRangesResult = await pool.query(investmentCriteriaRangesQuery)
      const criteriaRanges = criteriaRangesResult.rows[0]
      
      // Add investment criteria ranges to the response
      filterOptions.ranges.targetReturn = {
        min: Math.floor((criteriaRanges.min_target_return || 0) * 100), // Convert to percentage
        max: Math.ceil((criteriaRanges.max_target_return || 0.5) * 100)
      }
      filterOptions.ranges.dealSize = {
        min: Math.floor(criteriaRanges.min_deal_size || 1000000),
        max: Math.ceil(criteriaRanges.max_deal_size || *********)
      }
      filterOptions.ranges.holdPeriod = {
        min: criteriaRanges.min_hold_period || 0,
        max: criteriaRanges.max_hold_period || 120
      }
    } catch (error) {
      console.error('Error fetching investment criteria ranges:', error)
      // Set defaults
      filterOptions.ranges.targetReturn = { min: 0, max: 50 }
      filterOptions.ranges.dealSize = { min: 1000000, max: ********* }
      filterOptions.ranges.holdPeriod = { min: 0, max: 120 }
    }

    console.log('Company filter options generated:', {
      totalTypes: Object.keys(hierarchicalMappings).length,
      companyTypes: filterOptions.companyTypes.length,
      businessModels: filterOptions.businessModels.length,
      propertyTypes: filterOptions.propertyTypes.length,
      propertySubcategories: filterOptions.propertySubcategories.length,
      states: filterOptions.states.length,
      headquarters: filterOptions.headquarters.length
    })
    
    return NextResponse.json(filterOptions)
    
  } catch (error) {
    console.error('Error in company filter options API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch filter options' },
      { status: 500 }
    )
  }
} 