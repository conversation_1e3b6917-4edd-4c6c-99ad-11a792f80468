import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Investment criteria filters
    const searchTerm = searchParams.get('searchTerm')
    const entityType = searchParams.get('entityType')
    const entityId = searchParams.get('entityId')
    const entityName = searchParams.get('entityName')
    const criteriaId = searchParams.get('criteriaId')
    
    // Capital & Financing
    const capitalPosition = searchParams.get('capitalPosition')?.split(',').filter(Boolean)
    const loanTypes = searchParams.get('loanTypes')?.split(',').filter(Boolean)
    const loanProgram = searchParams.get('loanProgram')?.split(',').filter(Boolean)
    const structuredLoanTranche = searchParams.get('structuredLoanTranche')?.split(',').filter(Bo<PERSON>an)
    const recourseLoan = searchParams.get('recourseLoan')?.split(',').filter(Boolean)
    
    // Deal size
    const dealSizeMin = searchParams.get('dealSizeMin') ? parseFloat(searchParams.get('dealSizeMin')!) : null
    const dealSizeMax = searchParams.get('dealSizeMax') ? parseFloat(searchParams.get('dealSizeMax')!) : null
    
    // Returns
    const targetReturnMin = searchParams.get('targetReturnMin') ? parseFloat(searchParams.get('targetReturnMin')!) : null
    const targetReturnMax = searchParams.get('targetReturnMax') ? parseFloat(searchParams.get('targetReturnMax')!) : null
    const historicalIrrMin = searchParams.get('historicalIrrMin') ? parseFloat(searchParams.get('historicalIrrMin')!) : null
    const historicalIrrMax = searchParams.get('historicalIrrMax') ? parseFloat(searchParams.get('historicalIrrMax')!) : null
    const historicalEmMin = searchParams.get('historicalEmMin') ? parseFloat(searchParams.get('historicalEmMin')!) : null
    const historicalEmMax = searchParams.get('historicalEmMax') ? parseFloat(searchParams.get('historicalEmMax')!) : null
    
    // Hold periods
    const minHoldPeriod = searchParams.get('minHoldPeriod') ? parseInt(searchParams.get('minHoldPeriod')!) : null
    const maxHoldPeriod = searchParams.get('maxHoldPeriod') ? parseInt(searchParams.get('maxHoldPeriod')!) : null
    
    // Loan terms
    const minLoanTerm = searchParams.get('minLoanTerm') ? parseInt(searchParams.get('minLoanTerm')!) : null
    const maxLoanTerm = searchParams.get('maxLoanTerm') ? parseInt(searchParams.get('maxLoanTerm')!) : null
    const interestRateMin = searchParams.get('interestRateMin') ? parseFloat(searchParams.get('interestRateMin')!) : null
    const interestRateMax = searchParams.get('interestRateMax') ? parseFloat(searchParams.get('interestRateMax')!) : null
    
    // LTV/LTC
    const loanToValueMin = searchParams.get('loanToValueMin') ? parseFloat(searchParams.get('loanToValueMin')!) : null
    const loanToValueMax = searchParams.get('loanToValueMax') ? parseFloat(searchParams.get('loanToValueMax')!) : null
    const loanToCostMin = searchParams.get('loanToCostMin') ? parseFloat(searchParams.get('loanToCostMin')!) : null
    const loanToCostMax = searchParams.get('loanToCostMax') ? parseFloat(searchParams.get('loanToCostMax')!) : null
    
    // DSCR
    const minLoanDscr = searchParams.get('minLoanDscr') ? parseFloat(searchParams.get('minLoanDscr')!) : null
    const maxLoanDscr = searchParams.get('maxLoanDscr') ? parseFloat(searchParams.get('maxLoanDscr')!) : null
    
    // Property & Geographic
    const propertyTypes = searchParams.get('propertyTypes')?.split(',').filter(Boolean)
    const propertySubcategories = searchParams.get('propertySubcategories')?.split(',').filter(Boolean)
    const strategies = searchParams.get('strategies')?.split(',').filter(Boolean)
    const financialProducts = searchParams.get('financialProducts')?.split(',').filter(Boolean)
    const regions = searchParams.get('regions')?.split(',').filter(Boolean)
    const states = searchParams.get('states')?.split(',').filter(Boolean)
    const cities = searchParams.get('cities')?.split(',').filter(Boolean)
    const countries = searchParams.get('countries')?.split(',').filter(Boolean)
    
    // Company extracted data filters
    const companyType = searchParams.get('companyType')?.split(',').filter(Boolean)
    const businessModel = searchParams.get('businessModel')
    const companyIndustry = searchParams.get('companyIndustry')?.split(',').filter(Boolean)
    const headquarters = searchParams.get('headquarters')?.split(',').filter(Boolean)
    
    // Company Financial Range Filters
    const fundSizeMin = searchParams.get('fundSizeMin') ? parseFloat(searchParams.get('fundSizeMin')!) : null
    const fundSizeMax = searchParams.get('fundSizeMax') ? parseFloat(searchParams.get('fundSizeMax')!) : null
    const aumMin = searchParams.get('aumMin') ? parseFloat(searchParams.get('aumMin')!) : null
    const aumMax = searchParams.get('aumMax') ? parseFloat(searchParams.get('aumMax')!) : null
    const portfolioValueMin = searchParams.get('portfolioValueMin') ? parseFloat(searchParams.get('portfolioValueMin')!) : null
    const portfolioValueMax = searchParams.get('portfolioValueMax') ? parseFloat(searchParams.get('portfolioValueMax')!) : null
    const historicalReturnsMin = searchParams.get('historicalReturnsMin') ? parseFloat(searchParams.get('historicalReturnsMin')!) : null
    const historicalReturnsMax = searchParams.get('historicalReturnsMax') ? parseFloat(searchParams.get('historicalReturnsMax')!) : null
    
    // Company Scale Range Filters
    const foundedYearMin = searchParams.get('foundedYearMin') ? parseInt(searchParams.get('foundedYearMin')!) : null
    const foundedYearMax = searchParams.get('foundedYearMax') ? parseInt(searchParams.get('foundedYearMax')!) : null
    const numberOfPropertiesMin = searchParams.get('numberOfPropertiesMin') ? parseInt(searchParams.get('numberOfPropertiesMin')!) : null
    const numberOfPropertiesMax = searchParams.get('numberOfPropertiesMax') ? parseInt(searchParams.get('numberOfPropertiesMax')!) : null
    const numberOfOfficesMin = searchParams.get('numberOfOfficesMin') ? parseInt(searchParams.get('numberOfOfficesMin')!) : null
    const numberOfOfficesMax = searchParams.get('numberOfOfficesMax') ? parseInt(searchParams.get('numberOfOfficesMax')!) : null
    const numberOfEmployeesMin = searchParams.get('numberOfEmployeesMin') ? parseInt(searchParams.get('numberOfEmployeesMin')!) : null
    const numberOfEmployeesMax = searchParams.get('numberOfEmployeesMax') ? parseInt(searchParams.get('numberOfEmployeesMax')!) : null
    const totalTransactionsMin = searchParams.get('totalTransactionsMin') ? parseInt(searchParams.get('totalTransactionsMin')!) : null
    const totalTransactionsMax = searchParams.get('totalTransactionsMax') ? parseInt(searchParams.get('totalTransactionsMax')!) : null
    const totalSquareFeetMin = searchParams.get('totalSquareFeetMin') ? parseFloat(searchParams.get('totalSquareFeetMin')!) : null
    const totalSquareFeetMax = searchParams.get('totalSquareFeetMax') ? parseFloat(searchParams.get('totalSquareFeetMax')!) : null
    const totalUnitsMin = searchParams.get('totalUnitsMin') ? parseInt(searchParams.get('totalUnitsMin')!) : null
    const totalUnitsMax = searchParams.get('totalUnitsMax') ? parseInt(searchParams.get('totalUnitsMax')!) : null
    
    // Timeline
    const closingTimeWeeks = searchParams.get('closingTimeWeeks') ? parseInt(searchParams.get('closingTimeWeeks')!) : null
    
    // Status
    const isActive = searchParams.get('isActive') === 'true' ? true : searchParams.get('isActive') === 'false' ? false : null
    
    // Processing status filters
    const websiteScrapingStatus = searchParams.get('websiteScrapingStatus')?.split(',').filter(Boolean)
    const companyOverviewStatus = searchParams.get('companyOverviewStatus')?.split(',').filter(Boolean)
    const overviewV2Status = searchParams.get('overviewV2Status')?.split(',').filter(Boolean)
    const investmentCriteriaStatus = searchParams.get('investmentCriteriaStatus')?.split(',').filter(Boolean)
    
    // Contact Processor Filters
    const hasContacts = searchParams.get('hasContacts') === 'true' ? true : searchParams.get('hasContacts') === 'false' ? false : null
    const contactsEmailVerificationStatus = searchParams.get('contactsEmailVerificationStatus')?.split(',').filter(Boolean)
    const contactsEnrichmentStatus = searchParams.get('contactsEnrichmentStatus')?.split(',').filter(Boolean)
    const contactsEnrichmentV2Status = searchParams.get('contactsEnrichmentV2Status')?.split(',').filter(Boolean)
    const contactsEmailGenerationStatus = searchParams.get('contactsEmailGenerationStatus')?.split(',').filter(Boolean)
    const contactsEmailSendingStatus = searchParams.get('contactsEmailSendingStatus')?.split(',').filter(Boolean)
    
    // Pagination
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = (page - 1) * limit
    
    // Sorting
    const sortBy = searchParams.get('sortBy') || 'updated_at'
    const sortOrder = searchParams.get('sortOrder') || 'desc'
    
    // Build WHERE conditions
    const whereConditions: string[] = []
    const queryParams: any[] = []
    let paramIndex = 1
    
    // Determine which tables we need to join based on filters
    const hasInvestmentCriteriaFilters = !!(
      capitalPosition?.length || loanTypes?.length || loanProgram?.length ||
      structuredLoanTranche?.length || recourseLoan?.length ||
      dealSizeMin !== null || dealSizeMax !== null ||
      targetReturnMin !== null || targetReturnMax !== null ||
      historicalIrrMin !== null || historicalIrrMax !== null ||
      historicalEmMin !== null || historicalEmMax !== null ||
      minHoldPeriod !== null || maxHoldPeriod !== null ||
      minLoanTerm !== null || maxLoanTerm !== null ||
      interestRateMin !== null || interestRateMax !== null ||
      loanToValueMin !== null || loanToValueMax !== null ||
      loanToCostMin !== null || loanToCostMax !== null ||
      minLoanDscr !== null || maxLoanDscr !== null ||
      propertyTypes?.length || propertySubcategories?.length ||
      strategies?.length || financialProducts?.length ||
      regions?.length || states?.length || cities?.length || countries?.length ||
      closingTimeWeeks !== null || isActive !== null ||
      entityId || entityName || criteriaId
    )
    
    const hasCompanyExtractedDataFilters = !!(
      companyType?.length || businessModel || companyIndustry?.length || headquarters?.length ||
      fundSizeMin !== null || fundSizeMax !== null || aumMin !== null || aumMax !== null ||
      portfolioValueMin !== null || portfolioValueMax !== null || historicalReturnsMin !== null || historicalReturnsMax !== null ||
      foundedYearMin !== null || foundedYearMax !== null || numberOfPropertiesMin !== null || numberOfPropertiesMax !== null ||
      numberOfOfficesMin !== null || numberOfOfficesMax !== null || numberOfEmployeesMin !== null || numberOfEmployeesMax !== null ||
      totalTransactionsMin !== null || totalTransactionsMax !== null || totalSquareFeetMin !== null || totalSquareFeetMax !== null ||
      totalUnitsMin !== null || totalUnitsMax !== null || overviewV2Status?.length || investmentCriteriaStatus?.length
    )
    
    const hasContactProcessorFilters = !!(
      contactsEmailVerificationStatus?.length || contactsEnrichmentStatus?.length ||
      contactsEnrichmentV2Status?.length || contactsEmailGenerationStatus?.length || contactsEmailSendingStatus?.length
    )
    
    // Only filter for companies with investment criteria when IC filters are applied
    if (hasInvestmentCriteriaFilters) {
      whereConditions.push(`ic.entity_type LIKE 'Company%'`)
    }
    
    // Basic filters
    if (searchTerm) {
      whereConditions.push(`(
        c.company_name ILIKE $${paramIndex} OR 
        c.company_website ILIKE $${paramIndex} OR
        ced.companytype ILIKE $${paramIndex} OR
        ced.businessmodel ILIKE $${paramIndex}
      )`)
      queryParams.push(`%${searchTerm}%`)
      paramIndex++
    }
    
    if (entityId) {
      whereConditions.push(`ic.entity_id = $${paramIndex}`)
      queryParams.push(entityId)
      paramIndex++
    }
    
    if (entityName) {
      whereConditions.push(`ic.entity_name ILIKE $${paramIndex}`)
      queryParams.push(`%${entityName}%`)
      paramIndex++
    }
    
    if (criteriaId) {
      whereConditions.push(`ic.id = $${paramIndex}`)
      queryParams.push(parseInt(criteriaId))
      paramIndex++
    }
    
    // Capital & Financing filters - Fix array parameter handling
    if (capitalPosition?.length) {
      whereConditions.push(`ic.capital_position && $${paramIndex}::text[]`)
      queryParams.push(capitalPosition)
      paramIndex++
    }
    
    if (loanTypes?.length) {
      whereConditions.push(`ic.loan_type && $${paramIndex}::text[]`)
      queryParams.push(loanTypes)
      paramIndex++
    }
    
    if (loanProgram?.length) {
      whereConditions.push(`ic.loan_program && $${paramIndex}::text[]`)
      queryParams.push(loanProgram)
      paramIndex++
    }
    
    if (structuredLoanTranche?.length) {
      whereConditions.push(`ic.structured_loan_tranche && $${paramIndex}::text[]`)
      queryParams.push(structuredLoanTranche)
      paramIndex++
    }
    
    if (recourseLoan?.length) {
      whereConditions.push(`ic.recourse_loan && $${paramIndex}::text[]`)
      queryParams.push(recourseLoan)
      paramIndex++
    }
    
    // Deal size filters
    if (dealSizeMin !== null) {
      whereConditions.push(`ic.minimum_deal_size >= $${paramIndex}`)
      queryParams.push(dealSizeMin)
      paramIndex++
    }
    
    if (dealSizeMax !== null) {
      whereConditions.push(`ic.maximum_deal_size <= $${paramIndex}`)
      queryParams.push(dealSizeMax)
      paramIndex++
    }
    
    // Return filters
    if (targetReturnMin !== null) {
      whereConditions.push(`ic.target_return >= $${paramIndex}`)
      queryParams.push(targetReturnMin)
      paramIndex++
    }
    
    if (targetReturnMax !== null) {
      whereConditions.push(`ic.target_return <= $${paramIndex}`)
      queryParams.push(targetReturnMax)
      paramIndex++
    }
    
    if (historicalIrrMin !== null) {
      whereConditions.push(`ic.historical_irr >= $${paramIndex}`)
      queryParams.push(historicalIrrMin)
      paramIndex++
    }
    
    if (historicalIrrMax !== null) {
      whereConditions.push(`ic.historical_irr <= $${paramIndex}`)
      queryParams.push(historicalIrrMax)
      paramIndex++
    }
    
    if (historicalEmMin !== null) {
      whereConditions.push(`ic.historical_em >= $${paramIndex}`)
      queryParams.push(historicalEmMin)
      paramIndex++
    }
    
    if (historicalEmMax !== null) {
      whereConditions.push(`ic.historical_em <= $${paramIndex}`)
      queryParams.push(historicalEmMax)
      paramIndex++
    }
    
    // Hold period filters
    if (minHoldPeriod !== null) {
      whereConditions.push(`ic.min_hold_period >= $${paramIndex}`)
      queryParams.push(minHoldPeriod)
      paramIndex++
    }
    
    if (maxHoldPeriod !== null) {
      whereConditions.push(`ic.max_hold_period <= $${paramIndex}`)
      queryParams.push(maxHoldPeriod)
      paramIndex++
    }
    
    // Loan term filters
    if (minLoanTerm !== null) {
      whereConditions.push(`ic.min_loan_term >= $${paramIndex}`)
      queryParams.push(minLoanTerm)
      paramIndex++
    }
    
    if (maxLoanTerm !== null) {
      whereConditions.push(`ic.max_loan_term <= $${paramIndex}`)
      queryParams.push(maxLoanTerm)
      paramIndex++
    }
    
    // Interest rate filters
    if (interestRateMin !== null) {
      whereConditions.push(`ic.interest_rate_min >= $${paramIndex}`)
      queryParams.push(interestRateMin)
      paramIndex++
    }
    
    if (interestRateMax !== null) {
      whereConditions.push(`ic.interest_rate_max <= $${paramIndex}`)
      queryParams.push(interestRateMax)
      paramIndex++
    }
    
    // LTV filters
    if (loanToValueMin !== null) {
      whereConditions.push(`ic.loan_to_value_min >= $${paramIndex}`)
      queryParams.push(loanToValueMin)
      paramIndex++
    }
    
    if (loanToValueMax !== null) {
      whereConditions.push(`ic.loan_to_value_max <= $${paramIndex}`)
      queryParams.push(loanToValueMax)
      paramIndex++
    }
    
    // LTC filters
    if (loanToCostMin !== null) {
      whereConditions.push(`ic.loan_to_cost_min >= $${paramIndex}`)
      queryParams.push(loanToCostMin)
      paramIndex++
    }
    
    if (loanToCostMax !== null) {
      whereConditions.push(`ic.loan_to_cost_max <= $${paramIndex}`)
      queryParams.push(loanToCostMax)
      paramIndex++
    }
    
    // DSCR filters
    if (minLoanDscr !== null) {
      whereConditions.push(`ic.min_loan_dscr >= $${paramIndex}`)
      queryParams.push(minLoanDscr)
      paramIndex++
    }
    
    if (maxLoanDscr !== null) {
      whereConditions.push(`ic.max_loan_dscr <= $${paramIndex}`)
      queryParams.push(maxLoanDscr)
      paramIndex++
    }
    
    // Property type filters - Fix array parameter handling
    if (propertyTypes?.length) {
      whereConditions.push(`ic.property_types && $${paramIndex}::text[]`)
      queryParams.push(propertyTypes)
      paramIndex++
    }
    
    if (propertySubcategories?.length) {
      whereConditions.push(`ic.property_sub_categories && $${paramIndex}::text[]`)
      queryParams.push(propertySubcategories)
      paramIndex++
    }
    
    if (strategies?.length) {
      whereConditions.push(`ic.strategies && $${paramIndex}::text[]`)
      queryParams.push(strategies)
      paramIndex++
    }
    
    if (financialProducts?.length) {
      whereConditions.push(`ic.financial_products && $${paramIndex}::jsonb`)
      queryParams.push(financialProducts)
      paramIndex++
    }
    
    // Geographic filters - Fix array parameter handling
    if (regions?.length) {
      whereConditions.push(`ic.region && $${paramIndex}::text[]`)
      queryParams.push(regions)
      paramIndex++
    }
    
    if (states?.length) {
      whereConditions.push(`ic.state && $${paramIndex}::text[]`)
      queryParams.push(states)
      paramIndex++
    }
    
    if (cities?.length) {
      whereConditions.push(`ic.city && $${paramIndex}::text[]`)
      queryParams.push(cities)
      paramIndex++
    }
    
    if (countries?.length) {
      whereConditions.push(`ic.country && $${paramIndex}::text[]`)
      queryParams.push(countries)
      paramIndex++
    }
    
    // Company extracted data filters
    if (companyType?.length) {
      // Handle as text array - use ILIKE for pattern matching since it's text field
      const companyTypeConditions = companyType.map((_, index) => `ced.companytype ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${companyTypeConditions.join(' OR ')})`)
      companyType.forEach(type => {
        queryParams.push(`%${type}%`)
        paramIndex++
      })
    }
    
    if (businessModel) {
      whereConditions.push(`ced.businessmodel ILIKE $${paramIndex}`)
      queryParams.push(`%${businessModel}%`)
      paramIndex++
    }
    
    if (companyIndustry?.length) {
      // Handle as text array - use ILIKE for pattern matching since it's text field
      const industryConditions = companyIndustry.map((_, index) => `ced.companyindustry ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${industryConditions.join(' OR ')})`)
      companyIndustry.forEach(industry => {
        queryParams.push(`%${industry}%`)
        paramIndex++
      })
    }
    
    if (headquarters?.length) {
      // Handle as text array - use ILIKE for pattern matching since it's text field
      const hqConditions = headquarters.map((_, index) => `ced.headquarters ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${hqConditions.join(' OR ')})`)
      headquarters.forEach(hq => {
        queryParams.push(`%${hq}%`)
        paramIndex++
      })
    }
    
    // Company Financial Range Filters (text fields - need REGEXP_REPLACE)
    if (fundSizeMin !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.fundsize, '[^0-9.]', '', 'g'), '') AS NUMERIC) >= $${paramIndex}`)
      queryParams.push(fundSizeMin)
      paramIndex++
    }
    
    if (fundSizeMax !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.fundsize, '[^0-9.]', '', 'g'), '') AS NUMERIC) <= $${paramIndex}`)
      queryParams.push(fundSizeMax)
      paramIndex++
    }
    
    if (aumMin !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.aum, '[^0-9.]', '', 'g'), '') AS NUMERIC) >= $${paramIndex}`)
      queryParams.push(aumMin)
      paramIndex++
    }
    
    if (aumMax !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.aum, '[^0-9.]', '', 'g'), '') AS NUMERIC) <= $${paramIndex}`)
      queryParams.push(aumMax)
      paramIndex++
    }
    
    if (portfolioValueMin !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.portfoliovalue, '[^0-9.]', '', 'g'), '') AS NUMERIC) >= $${paramIndex}`)
      queryParams.push(portfolioValueMin)
      paramIndex++
    }
    
    if (portfolioValueMax !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.portfoliovalue, '[^0-9.]', '', 'g'), '') AS NUMERIC) <= $${paramIndex}`)
      queryParams.push(portfolioValueMax)
      paramIndex++
    }
    
    if (historicalReturnsMin !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.historicalreturns, '[^0-9.]', '', 'g'), '') AS NUMERIC) >= $${paramIndex}`)
      queryParams.push(historicalReturnsMin)
      paramIndex++
    }
    
    if (historicalReturnsMax !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.historicalreturns, '[^0-9.]', '', 'g'), '') AS NUMERIC) <= $${paramIndex}`)
      queryParams.push(historicalReturnsMax)
      paramIndex++
    }
    
    // Company Scale Range Filters
    // Integer fields - direct comparison (foundedyear, numberofoffices, numberofproperties)
    if (foundedYearMin !== null) {
      whereConditions.push(`ced.foundedyear >= $${paramIndex}`)
      queryParams.push(foundedYearMin)
      paramIndex++
    }
    
    if (foundedYearMax !== null) {
      whereConditions.push(`ced.foundedyear <= $${paramIndex}`)
      queryParams.push(foundedYearMax)
      paramIndex++
    }
    
    if (numberOfPropertiesMin !== null) {
      whereConditions.push(`ced.numberofproperties >= $${paramIndex}`)
      queryParams.push(numberOfPropertiesMin)
      paramIndex++
    }
    
    if (numberOfPropertiesMax !== null) {
      whereConditions.push(`ced.numberofproperties <= $${paramIndex}`)
      queryParams.push(numberOfPropertiesMax)
      paramIndex++
    }
    
    if (numberOfOfficesMin !== null) {
      whereConditions.push(`ced.numberofoffices >= $${paramIndex}`)
      queryParams.push(numberOfOfficesMin)
      paramIndex++
    }
    
    if (numberOfOfficesMax !== null) {
      whereConditions.push(`ced.numberofoffices <= $${paramIndex}`)
      queryParams.push(numberOfOfficesMax)
      paramIndex++
    }
    
    // Text fields that contain numbers - need REGEXP_REPLACE
    if (numberOfEmployeesMin !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.numberofemployees, '[^0-9]', '', 'g'), '') AS INTEGER) >= $${paramIndex}`)
      queryParams.push(numberOfEmployeesMin)
      paramIndex++
    }
    
    if (numberOfEmployeesMax !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.numberofemployees, '[^0-9]', '', 'g'), '') AS INTEGER) <= $${paramIndex}`)
      queryParams.push(numberOfEmployeesMax)
      paramIndex++
    }
    
    if (totalTransactionsMin !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.totaltransactions, '[^0-9]', '', 'g'), '') AS INTEGER) >= $${paramIndex}`)
      queryParams.push(totalTransactionsMin)
      paramIndex++
    }
    
    if (totalTransactionsMax !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.totaltransactions, '[^0-9]', '', 'g'), '') AS INTEGER) <= $${paramIndex}`)
      queryParams.push(totalTransactionsMax)
      paramIndex++
    }
    
    if (totalSquareFeetMin !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.totalsquarefeet, '[^0-9.]', '', 'g'), '') AS NUMERIC) >= $${paramIndex}`)
      queryParams.push(totalSquareFeetMin)
      paramIndex++
    }
    
    if (totalSquareFeetMax !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.totalsquarefeet, '[^0-9.]', '', 'g'), '') AS NUMERIC) <= $${paramIndex}`)
      queryParams.push(totalSquareFeetMax)
      paramIndex++
    }
    
    if (totalUnitsMin !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.totalunits, '[^0-9]', '', 'g'), '') AS INTEGER) >= $${paramIndex}`)
      queryParams.push(totalUnitsMin)
      paramIndex++
    }
    
    if (totalUnitsMax !== null) {
      whereConditions.push(`CAST(NULLIF(REGEXP_REPLACE(ced.totalunits, '[^0-9]', '', 'g'), '') AS INTEGER) <= $${paramIndex}`)
      queryParams.push(totalUnitsMax)
      paramIndex++
    }
    
    // Status filters
    if (isActive !== null) {
      whereConditions.push(`ic.is_active = $${paramIndex}`)
      queryParams.push(isActive)
      paramIndex++
    }
    
    // Processing status filters
    if (overviewV2Status?.length) {
      const statusConditions = overviewV2Status.map((_, index) => `c.overview_v2_status = $${paramIndex + index}`)
      whereConditions.push(`(${statusConditions.join(' OR ')})`)
      overviewV2Status.forEach(status => {
        queryParams.push(status)
        paramIndex++
      })
    }
    
    if (investmentCriteriaStatus?.length) {
      const statusConditions = investmentCriteriaStatus.map((_, index) => `c.investment_criteria_status = $${paramIndex + index}`)
      whereConditions.push(`(${statusConditions.join(' OR ')})`)
      investmentCriteriaStatus.forEach(status => {
        queryParams.push(status)
        paramIndex++
      })
    }
    
    // Contact Processor Filters
    if (hasContacts !== null) {
      if (hasContacts) {
        whereConditions.push(`EXISTS (SELECT 1 FROM contacts WHERE company_id = c.company_id)`)
      } else {
        whereConditions.push(`NOT EXISTS (SELECT 1 FROM contacts WHERE company_id = c.company_id)`)
      }
    }
    
    // Only add contact processor status filters if we have the cp table joined
    if (hasContactProcessorFilters) {
      if (contactsEmailVerificationStatus?.length) {
        const statusConditions = contactsEmailVerificationStatus.map((_, index) => `cp.contact_email_verification_status = $${paramIndex + index}`)
        whereConditions.push(`(${statusConditions.join(' OR ')})`)
        contactsEmailVerificationStatus.forEach(status => {
          queryParams.push(status)
          paramIndex++
        })
      }
      
      if (contactsEnrichmentStatus?.length) {
        const statusConditions = contactsEnrichmentStatus.map((_, index) => `cp.contact_enrichment_status = $${paramIndex + index}`)
        whereConditions.push(`(${statusConditions.join(' OR ')})`)
        contactsEnrichmentStatus.forEach(status => {
          queryParams.push(status)
          paramIndex++
        })
      }
      
      if (contactsEnrichmentV2Status?.length) {
        const statusConditions = contactsEnrichmentV2Status.map((_, index) => `cp.contact_enrichment_v2_status = $${paramIndex + index}`)
        whereConditions.push(`(${statusConditions.join(' OR ')})`)
        contactsEnrichmentV2Status.forEach(status => {
          queryParams.push(status)
          paramIndex++
        })
      }
      
      if (contactsEmailGenerationStatus?.length) {
        const statusConditions = contactsEmailGenerationStatus.map((_, index) => `cp.contact_email_generation_status = $${paramIndex + index}`)
        whereConditions.push(`(${statusConditions.join(' OR ')})`)
        contactsEmailGenerationStatus.forEach(status => {
          queryParams.push(status)
          paramIndex++
        })
      }
      
      if (contactsEmailSendingStatus?.length) {
        const statusConditions = contactsEmailSendingStatus.map((_, index) => `cp.contact_email_sending_status = $${paramIndex + index}`)
        whereConditions.push(`(${statusConditions.join(' OR ')})`)
        contactsEmailSendingStatus.forEach(status => {
          queryParams.push(status)
          paramIndex++
        })
      }
    }
    
    // Build conditional JOINs - OPTIMIZED to only join what we need
    let joinClauses = `
      FROM companies c
    `
    
    // Only join investment criteria if we need to filter by it OR if we need the criteria_id for display
    if (hasInvestmentCriteriaFilters) {
      joinClauses += `
        INNER JOIN (
          SELECT DISTINCT ON (entity_id) *
          FROM investment_criteria 
          WHERE entity_type LIKE 'Company%'
          ORDER BY entity_id, updated_at DESC
        ) ic ON c.company_id::text = ic.entity_id
      `
    } else {
      // Light LEFT JOIN to get just the criteria_id for "Has IC" badge
      joinClauses += `
        LEFT JOIN (
          SELECT DISTINCT ON (entity_id) entity_id, criteria_id
          FROM investment_criteria 
          WHERE entity_type LIKE 'Company%'
          ORDER BY entity_id, updated_at DESC
        ) ic ON c.company_id::text = ic.entity_id
      `
    }
    
    // Only join company extracted data if we have extracted data filters
    if (hasCompanyExtractedDataFilters) {
      joinClauses += `
        INNER JOIN company_extracted_data ced ON c.company_id::bigint = ced.company_id
      `
    } else {
      // Light LEFT JOIN to get basic extracted data
      joinClauses += `
        LEFT JOIN company_extracted_data ced ON c.company_id::bigint = ced.company_id
      `
    }
    
    // Join contacts table if we have contact processor filters
    if (hasContactProcessorFilters) {
      joinClauses += `
        LEFT JOIN (
          SELECT 
            company_id,
            COUNT(*) as contact_count,
            MAX(email_verification_status) as contact_email_verification_status,
            MAX(contact_enrichment_status) as contact_enrichment_status,
            MAX(contact_enrichment_v2_status) as contact_enrichment_v2_status,
            MAX(email_generation_status) as contact_email_generation_status,
            MAX(email_sending_status) as contact_email_sending_status
          FROM contacts 
          GROUP BY company_id
        ) cp ON c.company_id = cp.company_id
      `
    }
    
    // Build the final WHERE clause
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''
    
    // Simplified sorting - only essential fields (always use company fields for consistency with SELECT)
    const validSortFields = {
      'updated_at': 'c.updated_at',
      'created_at': 'c.created_at',
      'company_name': 'c.company_name',
      'company_website': 'c.company_website',
      'industry': 'c.industry',
      'target_return': 'ic.target_return',
      'historical_irr': 'ic.historical_irr',
      'historical_em': 'ic.historical_em',
      'minimum_deal_size': 'ic.minimum_deal_size',
      'maximum_deal_size': 'ic.maximum_deal_size',
      'totaltransactions': 'ced.totaltransactions',
      'fundsize': 'ced.fundsize',
      'aum': 'ced.aum',
      'foundedyear': 'ced.foundedyear',
      'numberofemployees': 'ced.numberofemployees'
    }
    
    const defaultSortField = 'c.updated_at'
    const sortField = validSortFields[sortBy as keyof typeof validSortFields] || defaultSortField
    const orderClause = `ORDER BY ${sortField} ${sortOrder.toUpperCase()} NULLS LAST`
    
    // Count query - OPTIMIZED with hints
    const countQuery = `
      SELECT COUNT(DISTINCT c.company_id) as total
      ${joinClauses}
      ${whereClause}
    `
    
    // OPTIMIZED SELECT - Only fields needed for CompanyCard display
    const selectFields = `
      c.company_id,
      c.company_name,
      c.company_website as website,
      c.company_phone as main_phone,
      ced.mainemail as main_email,
      c.company_linkedin as linkedin_url,
      c.company_state,
      c.company_city,
      c.company_country,
      c.industry,
      c.processed,
      c.extracted,
      c.processing_state,
      c.website_scraping_status,
      c.company_overview_status,
      c.overview_v2_status,
      c.created_at,
      c.updated_at,
      
      -- Investment criteria indicator (just the ID for "Has IC" badge)
      ic.criteria_id,
      
      -- Company extracted data (minimal)
      ced.totaltransactions,
      ced.totalsquarefeet,
      ced.totalunits,
      ced.historicalreturns,
      ced.portfoliovalue,
      ced.companytype,
      ced.businessmodel,
      ced.fundsize,
      ced.aum,
      ced.numberofproperties,
      ced.headquarters,
      ced.numberofoffices,
      ced.foundedyear,
      ced.numberofemployees,
      ced.targetreturn as extracted_target_return,
      ced.companyindustry,
      ced.capitalposition as extracted_capital_position${hasContactProcessorFilters ? `,
      
      -- Contact processor fields
      cp.contact_count,
      cp.contact_email_verification_status,
      cp.contact_enrichment_status,
      cp.contact_enrichment_v2_status,
      cp.contact_email_generation_status,
      cp.contact_email_sending_status` : ''}
    `
    
    // Main query with only essential fields
    const query = `
      SELECT DISTINCT
        ${selectFields}
        
      ${joinClauses}
      ${whereClause}
      ${orderClause}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    
    queryParams.push(limit, offset)
    
    // Execute queries
    const [countResult, dataResult] = await Promise.all([
      pool.query(countQuery, queryParams.slice(0, -2)), // Remove limit and offset for count
      pool.query(query, queryParams)
    ])
    
    const total = parseInt(countResult.rows[0].total)
    const totalPages = Math.ceil(total / limit)
    
    return NextResponse.json({
      success: true,
      data: dataResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      meta: {
        hasInvestmentCriteriaFilters,
        hasCompanyExtractedDataFilters,
        joinedTables: {
          investment_criteria: hasInvestmentCriteriaFilters,
          company_extracted_data: hasCompanyExtractedDataFilters,
          contact_processor: hasContactProcessorFilters
        },
        optimized: true, // Flag to indicate this is the optimized version
        fieldsReturned: 'minimal_for_card_display',
        performance: {
          conditionalJoins: true, // Only join tables when filters are applied
          minimalSelect: true, // Only fetch fields needed for CompanyCard
          recommendedIndexes: [
            'idx_investment_criteria_entity_type_entity_id',
            'idx_company_extracted_data_company_id',
            'idx_companies_company_id'
          ]
        }
      }
    })
    
  } catch (error) {
    console.error('Error in unified filters API:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch unified company data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 