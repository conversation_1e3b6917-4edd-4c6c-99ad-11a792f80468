import { pool } from '@/lib/db'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Query to get unique company types
    const companyTypesQuery = `
      SELECT DISTINCT companytype 
      FROM company_extracted_data 
      WHERE companytype IS NOT NULL AND companytype != ''
      ORDER BY companytype
    `
    
    // Query to get all property types (from arrays)
    const propertyTypesQuery = `
      SELECT DISTINCT jsonb_array_elements_text(investment_criteria_property_types) as property_type
      FROM company_extracted_data
      WHERE investment_criteria_property_types IS NOT NULL 
        AND jsonb_typeof(investment_criteria_property_types) = 'array'
      ORDER BY property_type
    `
    
    // Query to get all capital types
    const capitalTypesQuery = `
      SELECT DISTINCT jsonb_array_elements_text(capitalsources) as capital_type
      FROM company_extracted_data
      WHERE capitalsources IS NOT NULL 
        AND jsonb_typeof(capitalsources) = 'array'
      ORDER BY capital_type
    `
    
    // Query to get all loan types
    const loanTypesQuery = `
      SELECT DISTINCT jsonb_array_elements_text(investment_criteria_loan_types) as loan_type
      FROM company_extracted_data
      WHERE investment_criteria_loan_types IS NOT NULL 
        AND jsonb_typeof(investment_criteria_loan_types) = 'array'
      ORDER BY loan_type
    `
    
    // Query to get all asset types
    const assetTypesQuery = `
      SELECT DISTINCT jsonb_array_elements_text(investment_criteria_asset_types) as asset_type
      FROM company_extracted_data
      WHERE investment_criteria_asset_types IS NOT NULL 
        AND jsonb_typeof(investment_criteria_asset_types) = 'array'
      ORDER BY asset_type
    `
    
    // Query to get all investment focus areas
    const investmentFocusQuery = `
      SELECT DISTINCT jsonb_array_elements_text(investmentfocus) as investment_focus
      FROM company_extracted_data
      WHERE investmentfocus IS NOT NULL 
        AND jsonb_typeof(investmentfocus) = 'array'
      ORDER BY investment_focus
    `
    
    // Query to get all geographic focus areas
    const geographicFocusQuery = `
      SELECT DISTINCT jsonb_array_elements_text(geographicfocus) as geographic_focus
      FROM company_extracted_data
      WHERE geographicfocus IS NOT NULL 
        AND jsonb_typeof(geographicfocus) = 'array'
      ORDER BY geographic_focus
    `
    
    // Query to get all regions
    const regionsQuery = `
      SELECT DISTINCT jsonb_array_elements_text(targetmarkets) as region
      FROM company_extracted_data
      WHERE targetmarkets IS NOT NULL 
        AND jsonb_typeof(targetmarkets) = 'array'
      ORDER BY region
    `
    
    // Query to get all states
    const statesQuery = `
      SELECT DISTINCT jsonb_array_elements_text(targetmarkets) as state
      FROM company_extracted_data
      WHERE targetmarkets IS NOT NULL 
        AND jsonb_typeof(targetmarkets) = 'array'
      ORDER BY state
    `
    
    // Query to get all cities
    const citiesQuery = `
      SELECT DISTINCT jsonb_array_elements_text(targetmarkets) as city
      FROM company_extracted_data
      WHERE targetmarkets IS NOT NULL 
        AND jsonb_typeof(targetmarkets) = 'array'
      ORDER BY city
    `
    
    // Query to get all countries
    const countriesQuery = `
      SELECT DISTINCT jsonb_array_elements_text(targetmarkets) as country
      FROM company_extracted_data
      WHERE targetmarkets IS NOT NULL 
        AND jsonb_typeof(targetmarkets) = 'array'
      ORDER BY country
    `
    
    // Execute all queries in parallel
    const [
      companyTypesResult, 
      propertyTypesResult, 
      capitalTypesResult,
      loanTypesResult,
      assetTypesResult,
      investmentFocusResult, 
      geographicFocusResult,
      regionsResult,
      statesResult,
      citiesResult,
      countriesResult
    ] = await Promise.all([
      pool.query(companyTypesQuery),
      pool.query(propertyTypesQuery),
      pool.query(capitalTypesQuery),
      pool.query(loanTypesQuery),
      pool.query(assetTypesQuery),
      pool.query(investmentFocusQuery),
      pool.query(geographicFocusQuery),
      pool.query(regionsQuery),
      pool.query(statesQuery),
      pool.query(citiesQuery),
      pool.query(countriesQuery)
    ])
    
    // Extract the values from the results
    const companyTypes = companyTypesResult.rows.map(row => row.companytype)
    const propertyTypes = propertyTypesResult.rows.map(row => row.property_type)
    const capitalTypes = capitalTypesResult.rows.map(row => row.capital_type)
    const loanTypes = loanTypesResult.rows.map(row => row.loan_type)
    const assetTypes = assetTypesResult.rows.map(row => row.asset_type)
    const investmentFocus = investmentFocusResult.rows.map(row => row.investment_focus)
    const geographicFocus = geographicFocusResult.rows.map(row => row.geographic_focus)
    const regions = regionsResult.rows.map(row => row.region)
    const states = statesResult.rows.map(row => row.state)
    const cities = citiesResult.rows.map(row => row.city)
    const countries = countriesResult.rows.map(row => row.country)
    
    // Return all filter options
    return NextResponse.json({
      companyTypes,
      propertyTypes,
      capitalTypes,
      loanTypes,
      assetTypes,
      investmentFocus,
      geographicFocus,
      regions,
      states,
      cities,
      countries,
      // Also include numeric range boundaries for sliders
      ranges: {
        dealSize: {
          min: 0,
          max: 1000 // In millions
        },
        ltv: {
          min: 0,
          max: 100 // Percentage
        },
        ltc: {
          min: 0,
          max: 100 // Percentage
        }
      }
    })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch filter options' },
      { status: 500 }
    )
  }
} 