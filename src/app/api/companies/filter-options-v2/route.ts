import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const section = searchParams.get('section');
  
  try {
    let data = {};

    switch (section) {
      case 'core_companies':
        data = await fetchCoreCompaniesOptions();
        break;
      case 'overview_v2':
        data = await fetchOverviewV2Options();
        break;
      case 'investment_criteria':
        data = await fetchInvestmentCriteriaOptions();
        break;
      default:
        return NextResponse.json({ 
          error: 'Invalid section parameter. Use: core_companies, overview_v2, or investment_criteria' 
        }, { status: 400 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching company filter options V2:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      section: section
    });
    return NextResponse.json(
      { 
        error: 'Failed to fetch filter options',
        details: error instanceof Error ? error.message : 'Unknown error',
        section: section
      },
      { status: 500 }
    );
  }
}

async function fetchCoreCompaniesOptions() {
  console.log('🔍 fetchCoreCompaniesOptions: Starting...');
  
  const client = await pool.connect();
  try {
    console.log('🔍 fetchCoreCompaniesOptions: Connected to database');
    
    // Company Addresses
    const companyAddressesQuery = `
      SELECT DISTINCT company_address as value, company_address as label, COUNT(*) as count
      FROM companies 
      WHERE company_address IS NOT NULL AND company_address != ''
      GROUP BY company_address
      ORDER BY count DESC, company_address ASC
      LIMIT 200
    `;
    const companyAddressesResult = await client.query(companyAddressesQuery);

    // Company Cities
    const companyCitiesQuery = `
      SELECT DISTINCT company_city as value, company_city as label, COUNT(*) as count
      FROM companies 
      WHERE company_city IS NOT NULL AND company_city != ''
      GROUP BY company_city
      ORDER BY count DESC, company_city ASC
      LIMIT 200
    `;
    const companyCitiesResult = await client.query(companyCitiesQuery);

    // Company States
    const companyStatesQuery = `
      SELECT DISTINCT company_state as value, company_state as label, COUNT(*) as count
      FROM companies 
      WHERE company_state IS NOT NULL AND company_state != ''
      GROUP BY company_state
      ORDER BY count DESC, company_state ASC
    `;
    const companyStatesResult = await client.query(companyStatesQuery);

    // Company Websites
    const companyWebsitesQuery = `
      SELECT DISTINCT company_website as value, company_website as label, COUNT(*) as count
      FROM companies 
      WHERE company_website IS NOT NULL AND company_website != ''
      GROUP BY company_website
      ORDER BY count DESC, company_website ASC
      LIMIT 500
    `;
    const companyWebsitesResult = await client.query(companyWebsitesQuery);

    // Industries
    const industriesQuery = `
      SELECT DISTINCT industry as value, industry as label, COUNT(*) as count
      FROM companies 
      WHERE industry IS NOT NULL AND industry != ''
      GROUP BY industry
      ORDER BY count DESC, industry ASC
    `;
    const industriesResult = await client.query(industriesQuery);

    // Company Countries
    const companyCountriesQuery = `
      SELECT DISTINCT company_country as value, company_country as label, COUNT(*) as count
      FROM companies 
      WHERE company_country IS NOT NULL AND company_country != ''
      GROUP BY company_country
      ORDER BY count DESC, company_country ASC
    `;
    const companyCountriesResult = await client.query(companyCountriesQuery);

    // Sources
    const sourcesQuery = `
      SELECT DISTINCT source as value, source as label, COUNT(*) as count
      FROM companies 
      WHERE source IS NOT NULL AND source != ''
      GROUP BY source
      ORDER BY count DESC, source ASC
    `;
    const sourcesResult = await client.query(sourcesQuery);

    // Processing status options (static list)
    const processingStatusOptions = [
      { value: 'not_started', label: 'Not Started' },
      { value: 'pending', label: 'Pending' },
      { value: 'running', label: 'Running' },
      { value: 'completed', label: 'Completed' },
      { value: 'failed', label: 'Failed' },
      { value: 'error', label: 'Error' }
    ];

    const result = {
      companyAddresses: companyAddressesResult.rows,
      companyCities: companyCitiesResult.rows,
      companyStates: companyStatesResult.rows,
      companyWebsites: companyWebsitesResult.rows,
      industries: industriesResult.rows,
      companyCountries: companyCountriesResult.rows,
      sources: sourcesResult.rows,
      websiteScrapingStatuses: processingStatusOptions,
      companyOverviewStatuses: processingStatusOptions,
      overviewV2Statuses: processingStatusOptions
    };
    
    console.log('🔍 fetchCoreCompaniesOptions: Returning core companies result with', Object.keys(result).length, 'categories');
    return result;
    
  } finally {
    console.log('🔍 fetchCoreCompaniesOptions: Releasing database connection');
    client.release();
  }
}

async function fetchOverviewV2Options() {
  console.log('🔍 fetchOverviewV2Options: Starting...');
  
  const client = await pool.connect();
  try {
    console.log('🔍 fetchOverviewV2Options: Connected to database');
    
    // Helper function for array field extraction
    const extractArrayElements = (arrayField: string) => {
      return `
        SELECT DISTINCT unnest(${arrayField}) as value, unnest(${arrayField}) as label, COUNT(*) as count
        FROM companies 
        WHERE ${arrayField} IS NOT NULL AND array_length(${arrayField}, 1) > 0
        GROUP BY unnest(${arrayField})
        ORDER BY count DESC, unnest(${arrayField}) ASC
        LIMIT 200
      `;
    };
    
    // Company Types
    const companyTypesQuery = `
      SELECT DISTINCT company_type as value, company_type as label, COUNT(*) as count
      FROM companies 
      WHERE company_type IS NOT NULL AND company_type != ''
      GROUP BY company_type
      ORDER BY count DESC, company_type ASC
    `;
    const companyTypesResult = await client.query(companyTypesQuery);

    // Investment Focus (array field)
    const investmentFocusResult = await client.query(extractArrayElements('investment_focus'));

    // Headquarters Cities
    const headquartersCitiesQuery = `
      SELECT DISTINCT company_city as value, company_city as label, COUNT(*) as count
      FROM companies 
      WHERE company_city IS NOT NULL AND company_city != ''
      GROUP BY company_city
      ORDER BY count DESC, company_city ASC
    `;
    const headquartersCitiesResult = await client.query(headquartersCitiesQuery);

    // Headquarters States
    const headquartersStatesQuery = `
      SELECT DISTINCT company_state as value, company_state as label, COUNT(*) as count
      FROM companies 
      WHERE company_state IS NOT NULL AND company_state != ''
      GROUP BY company_state
      ORDER BY count DESC, company_state ASC
    `;
    const headquartersStatesResult = await client.query(headquartersStatesQuery);

    // Headquarters Countries
    const headquartersCountriesQuery = `
      SELECT DISTINCT company_country as value, company_country as label, COUNT(*) as count
      FROM companies 
      WHERE company_country IS NOT NULL AND company_country != ''
      GROUP BY company_country
      ORDER BY count DESC, company_country ASC
    `;
    const headquartersCountriesResult = await client.query(headquartersCountriesQuery);

    // Office Locations (array field)
    const officeLocationsResult = await client.query(extractArrayElements('office_locations'));

    // Balance Sheet Strengths
    const balanceSheetStrengthsQuery = `
      SELECT DISTINCT balance_sheet_strength as value, balance_sheet_strength as label, COUNT(*) as count
      FROM companies 
      WHERE balance_sheet_strength IS NOT NULL AND balance_sheet_strength != ''
      GROUP BY balance_sheet_strength
      ORDER BY count DESC, balance_sheet_strength ASC
    `;
    const balanceSheetStrengthsResult = await client.query(balanceSheetStrengthsQuery);

    // Funding Sources (array field)
    const fundingSourcesResult = await client.query(extractArrayElements('funding_sources'));

    // Development Fee Structures
    const developmentFeeStructuresQuery = `
      SELECT DISTINCT development_fee_structure as value, development_fee_structure as label, COUNT(*) as count
      FROM companies 
      WHERE development_fee_structure IS NOT NULL AND development_fee_structure != ''
      GROUP BY development_fee_structure
      ORDER BY count DESC, development_fee_structure ASC
    `;
    const developmentFeeStructuresResult = await client.query(developmentFeeStructuresQuery);

    // Credit Ratings
    const creditRatingsQuery = `
      SELECT DISTINCT credit_rating as value, credit_rating as label, COUNT(*) as count
      FROM companies 
      WHERE credit_rating IS NOT NULL AND credit_rating != ''
      GROUP BY credit_rating
      ORDER BY count DESC, credit_rating ASC
    `;
    const creditRatingsResult = await client.query(creditRatingsQuery);

    // Investment Vehicle Types
    const investmentVehicleTypesQuery = `
      SELECT DISTINCT investment_vehicle_type as value, investment_vehicle_type as label, COUNT(*) as count
      FROM companies 
      WHERE investment_vehicle_type IS NOT NULL AND investment_vehicle_type != ''
      GROUP BY investment_vehicle_type
      ORDER BY count DESC, investment_vehicle_type ASC
    `;
    const investmentVehicleTypesResult = await client.query(investmentVehicleTypesQuery);

    // Active Fund Name Series
    const activeFundNameSeriesQuery = `
      SELECT DISTINCT active_fund_name_series as value, active_fund_name_series as label, COUNT(*) as count
      FROM companies 
      WHERE active_fund_name_series IS NOT NULL AND active_fund_name_series != ''
      GROUP BY active_fund_name_series
      ORDER BY count DESC, active_fund_name_series ASC
    `;
    const activeFundNameSeriesResult = await client.query(activeFundNameSeriesQuery);

    // Fundraising Statuses
    const fundraisingStatusesQuery = `
      SELECT DISTINCT fundraising_status as value, fundraising_status as label, COUNT(*) as count
      FROM companies 
      WHERE fundraising_status IS NOT NULL AND fundraising_status != ''
      GROUP BY fundraising_status
      ORDER BY count DESC, fundraising_status ASC
    `;
    const fundraisingStatusesResult = await client.query(fundraisingStatusesQuery);

    // Lender Types
    const lenderTypesQuery = `
      SELECT DISTINCT lender_type as value, lender_type as label, COUNT(*) as count
      FROM companies 
      WHERE lender_type IS NOT NULL AND lender_type != ''
      GROUP BY lender_type
      ORDER BY count DESC, lender_type ASC
    `;
    const lenderTypesResult = await client.query(lenderTypesQuery);

    // Lending Origins
    const lendingOriginsQuery = `
      SELECT DISTINCT lending_origin as value, lending_origin as label, COUNT(*) as count
      FROM companies 
      WHERE lending_origin IS NOT NULL AND lending_origin != ''
      GROUP BY lending_origin
      ORDER BY count DESC, lending_origin ASC
    `;
    const lendingOriginsResult = await client.query(lendingOriginsQuery);

    // Portfolio Health
    const portfolioHealthsQuery = `
      SELECT DISTINCT portfolio_health as value, portfolio_health as label, COUNT(*) as count
      FROM companies 
      WHERE portfolio_health IS NOT NULL AND portfolio_health != ''
      GROUP BY portfolio_health
      ORDER BY count DESC, portfolio_health ASC
    `;
    const portfolioHealthsResult = await client.query(portfolioHealthsQuery);

    // Partnerships (array field)
    const partnershipsResult = await client.query(extractArrayElements('partnerships'));

    // Key Equity Partners (array field)
    const keyEquityPartnersResult = await client.query(extractArrayElements('key_equity_partners'));

    // Key Debt Partners (array field)
    const keyDebtPartnersResult = await client.query(extractArrayElements('key_debt_partners'));

    // Board of Directors (array field)
    const boardOfDirectorsResult = await client.query(extractArrayElements('board_of_directors'));

    // Key Executives (array field)
    const keyExecutivesResult = await client.query(extractArrayElements('key_executives'));

    // Founder Backgrounds
    const founderBackgroundsQuery = `
      SELECT DISTINCT founder_background as value, founder_background as label, COUNT(*) as count
      FROM companies 
      WHERE founder_background IS NOT NULL AND founder_background != ''
      GROUP BY founder_background
      ORDER BY count DESC, founder_background ASC
    `;
    const founderBackgroundsResult = await client.query(founderBackgroundsQuery);



    // Corporate Structures
    const corporateStructuresQuery = `
      SELECT DISTINCT corporate_structure as value, corporate_structure as label, COUNT(*) as count
      FROM companies 
      WHERE corporate_structure IS NOT NULL AND corporate_structure != ''
      GROUP BY corporate_structure
      ORDER BY count DESC, corporate_structure ASC
    `;
    const corporateStructuresResult = await client.query(corporateStructuresQuery);

    // Parent Companies
    const parentCompaniesQuery = `
      SELECT DISTINCT parent_company as value, parent_company as label, COUNT(*) as count
      FROM companies 
      WHERE parent_company IS NOT NULL AND parent_company != ''
      GROUP BY parent_company
      ORDER BY count DESC, parent_company ASC
    `;
    const parentCompaniesResult = await client.query(parentCompaniesQuery);

    // Subsidiaries (array field)
    const subsidiariesResult = await client.query(extractArrayElements('subsidiaries'));

    // Stock Ticker Symbols
    const stockTickerSymbolsQuery = `
      SELECT DISTINCT stock_ticker_symbol as value, stock_ticker_symbol as label, COUNT(*) as count
      FROM companies 
      WHERE stock_ticker_symbol IS NOT NULL AND stock_ticker_symbol != ''
      GROUP BY stock_ticker_symbol
      ORDER BY count DESC, stock_ticker_symbol ASC
    `;
    const stockTickerSymbolsResult = await client.query(stockTickerSymbolsQuery);

    // Stock Exchanges
    const stockExchangesQuery = `
      SELECT DISTINCT stock_exchange as value, stock_exchange as label, COUNT(*) as count
      FROM companies 
      WHERE stock_exchange IS NOT NULL AND stock_exchange != ''
      GROUP BY stock_exchange
      ORDER BY count DESC, stock_exchange ASC
    `;
    const stockExchangesResult = await client.query(stockExchangesQuery);

    // Target Customer Profiles
    const targetCustomerProfilesQuery = `
      SELECT DISTINCT target_customer_profile as value, target_customer_profile as label, COUNT(*) as count
      FROM companies 
      WHERE target_customer_profile IS NOT NULL AND target_customer_profile != ''
      GROUP BY target_customer_profile
      ORDER BY count DESC, target_customer_profile ASC
    `;
    const targetCustomerProfilesResult = await client.query(targetCustomerProfilesQuery);

    // Major Competitors (array field)
    const majorCompetitorsResult = await client.query(extractArrayElements('major_competitors'));

    // Industry Awards Recognitions (array field)
    const industryAwardsRecognitionsResult = await client.query(extractArrayElements('industry_awards_recognitions'));

    // Roles in Previous Deals
    const rolesInPreviousDealsQuery = `
      SELECT DISTINCT role_in_previous_deal as value, role_in_previous_deal as label, COUNT(*) as count
      FROM companies 
      WHERE role_in_previous_deal IS NOT NULL AND role_in_previous_deal != ''
      GROUP BY role_in_previous_deal
      ORDER BY count DESC, role_in_previous_deal ASC
    `;
    const rolesInPreviousDealsResult = await client.query(rolesInPreviousDealsQuery);

    // Internal Relationship Managers
    const internalRelationshipManagersQuery = `
      SELECT DISTINCT internal_relationship_manager as value, internal_relationship_manager as label, COUNT(*) as count
      FROM companies 
      WHERE internal_relationship_manager IS NOT NULL AND internal_relationship_manager != ''
      GROUP BY internal_relationship_manager
      ORDER BY count DESC, internal_relationship_manager ASC
    `;
    const internalRelationshipManagersResult = await client.query(internalRelationshipManagersQuery);

    // Pipeline Statuses
    const pipelineStatusesQuery = `
      SELECT DISTINCT pipeline_status as value, pipeline_status as label, COUNT(*) as count
      FROM companies 
      WHERE pipeline_status IS NOT NULL AND pipeline_status != ''
      GROUP BY pipeline_status
      ORDER BY count DESC, pipeline_status ASC
    `;
    const pipelineStatusesResult = await client.query(pipelineStatusesQuery);

    // Recent News Sentiments
    const recentNewsSentimentsQuery = `
      SELECT DISTINCT recent_news_sentiment as value, recent_news_sentiment as label, COUNT(*) as count
      FROM companies 
      WHERE recent_news_sentiment IS NOT NULL AND recent_news_sentiment != ''
      GROUP BY recent_news_sentiment
      ORDER BY count DESC, recent_news_sentiment ASC
    `;
    const recentNewsSentimentsResult = await client.query(recentNewsSentimentsQuery);

    // Data Sources
    const dataSourcesQuery = `
      SELECT DISTINCT data_source as value, data_source as label, COUNT(*) as count
      FROM companies 
      WHERE data_source IS NOT NULL AND data_source != ''
      GROUP BY data_source
      ORDER BY count DESC, data_source ASC
    `;
    const dataSourcesResult = await client.query(dataSourcesQuery);

    const result = {
      companyTypes: companyTypesResult.rows,
      investmentFocus: investmentFocusResult.rows,
      headquartersAddresses: headquartersCitiesResult.rows, // Reusing cities for addresses
      headquartersCities: headquartersCitiesResult.rows,
      headquartersStates: headquartersStatesResult.rows,
      headquartersCountries: headquartersCountriesResult.rows,
      officeLocations: officeLocationsResult.rows,
      balanceSheetStrengths: balanceSheetStrengthsResult.rows,
      fundingSources: fundingSourcesResult.rows,
      developmentFeeStructures: developmentFeeStructuresResult.rows,
      creditRatings: creditRatingsResult.rows,
      investmentVehicleTypes: investmentVehicleTypesResult.rows,
      activeFundNameSeries: activeFundNameSeriesResult.rows,
      fundraisingStatuses: fundraisingStatusesResult.rows,
      lenderTypes: lenderTypesResult.rows,
      lendingOrigins: lendingOriginsResult.rows,
      portfolioHealths: portfolioHealthsResult.rows,
      partnerships: partnershipsResult.rows,
      keyEquityPartners: keyEquityPartnersResult.rows,
      keyDebtPartners: keyDebtPartnersResult.rows,
      boardOfDirectors: boardOfDirectorsResult.rows,
      keyExecutives: keyExecutivesResult.rows,
      founderBackgrounds: founderBackgroundsResult.rows,
      corporateStructures: corporateStructuresResult.rows,
      parentCompanies: parentCompaniesResult.rows,
      subsidiaries: subsidiariesResult.rows,
      stockTickerSymbols: stockTickerSymbolsResult.rows,
      stockExchanges: stockExchangesResult.rows,
      targetCustomerProfiles: targetCustomerProfilesResult.rows,
      majorCompetitors: majorCompetitorsResult.rows,
      industryAwardsRecognitions: industryAwardsRecognitionsResult.rows,
      rolesInPreviousDeals: rolesInPreviousDealsResult.rows,
      internalRelationshipManagers: internalRelationshipManagersResult.rows,
      pipelineStatuses: pipelineStatusesResult.rows,
      recentNewsSentiments: recentNewsSentimentsResult.rows,
      dataSources: dataSourcesResult.rows
    };
    
    console.log('🔍 fetchOverviewV2Options: Returning overview V2 result with', Object.keys(result).length, 'categories');
    return result;
    
  } finally {
    console.log('🔍 fetchOverviewV2Options: Releasing database connection');
    client.release();
  }
}

async function fetchInvestmentCriteriaOptions() {
  console.log('🔍 fetchInvestmentCriteriaOptions: Starting...');
  
  const client = await pool.connect();
  try {
    console.log('🔍 fetchInvestmentCriteriaOptions: Connected to database');
    
    // Helper function to extract array elements from investment criteria central
    const extractICArrayElements = (arrayField: string) => {
      return `
        SELECT DISTINCT unnest(${arrayField}) as value, unnest(${arrayField}) as label, COUNT(*) as count
        FROM investment_criteria_central 
        WHERE ${arrayField} IS NOT NULL AND array_length(${arrayField}, 1) > 0 
        AND entity_type = 'company'
        GROUP BY unnest(${arrayField})
        ORDER BY count DESC, unnest(${arrayField}) ASC
        LIMIT 200
      `;
    };

    // Capital Position from centralized table (text field, not array)
    const capitalPositionsQuery = `
      SELECT DISTINCT capital_position as value, capital_position as label, COUNT(*) as count
      FROM investment_criteria_central 
      WHERE capital_position IS NOT NULL AND capital_position != ''
      AND entity_type = 'company'
      GROUP BY capital_position
      ORDER BY count DESC, capital_position ASC
    `;
    const capitalPositionsResult = await client.query(capitalPositionsQuery);

    // Property Types from centralized table
    const propertyTypesResult = await client.query(extractICArrayElements('property_types'));

    // Property Subcategories from centralized table
    const propertySubcategoriesResult = await client.query(extractICArrayElements('property_subcategories'));

    // Strategies from centralized table
    const strategiesResult = await client.query(extractICArrayElements('strategies'));

    // Countries from centralized table
    const countriesResult = await client.query(extractICArrayElements('country'));

    // Regions from centralized table
    const regionsResult = await client.query(extractICArrayElements('region'));

    // States from centralized table
    const statesResult = await client.query(extractICArrayElements('state'));

    // Cities from centralized table
    const citiesResult = await client.query(extractICArrayElements('city'));

    // Debt-specific fields from investment_criteria_debt table
    const structuredLoanTranchesQuery = `
      SELECT DISTINCT structured_loan_tranche as value, structured_loan_tranche as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.structured_loan_tranche IS NOT NULL AND icd.structured_loan_tranche != ''
      AND icc.entity_type = 'company'
      GROUP BY structured_loan_tranche
      ORDER BY count DESC, structured_loan_tranche ASC
    `;
    const structuredLoanTranchesResult = await client.query(structuredLoanTranchesQuery);

    const loanProgramsQuery = `
      SELECT DISTINCT loan_program as value, loan_program as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.loan_program IS NOT NULL AND icd.loan_program != ''
      AND icc.entity_type = 'company'
      GROUP BY loan_program
      ORDER BY count DESC, loan_program ASC
      LIMIT 100
    `;
    const loanProgramsResult = await client.query(loanProgramsQuery);

    const recourseLoansQuery = `
      SELECT DISTINCT recourse_loan as value, recourse_loan as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.recourse_loan IS NOT NULL AND icd.recourse_loan != ''
      AND icc.entity_type = 'company'
      GROUP BY recourse_loan
      ORDER BY count DESC, recourse_loan ASC
    `;
    const recourseLoansResult = await client.query(recourseLoansQuery);

    const loanTypesQuery = `
      SELECT DISTINCT loan_type as value, loan_type as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.loan_type IS NOT NULL AND icd.loan_type != ''
      AND icc.entity_type = 'company'
      GROUP BY loan_type
      ORDER BY count DESC, loan_type ASC
    `;
    const loanTypesResult = await client.query(loanTypesQuery);

    const loanTypesNormalizedQuery = `
      SELECT DISTINCT loan_type_normalized as value, loan_type_normalized as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.loan_type_normalized IS NOT NULL AND icd.loan_type_normalized != ''
      AND icc.entity_type = 'company'
      GROUP BY loan_type_normalized
      ORDER BY count DESC, loan_type_normalized ASC
    `;
    const loanTypesNormalizedResult = await client.query(loanTypesNormalizedQuery);

    // Additional Central table fields
    const decisionMakingProcessesQuery = `
      SELECT DISTINCT decision_making_process as value, decision_making_process as label, COUNT(*) as count
      FROM investment_criteria_central 
      WHERE decision_making_process IS NOT NULL AND decision_making_process != ''
      AND entity_type = 'company'
      GROUP BY decision_making_process
      ORDER BY count DESC, decision_making_process ASC
    `;
    const decisionMakingProcessesResult = await client.query(decisionMakingProcessesQuery);

    // Additional Debt-specific fields - Missing from CSV
    const loanMinDebtYieldQuery = `
      SELECT DISTINCT loan_min_debt_yield as value, loan_min_debt_yield as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.loan_min_debt_yield IS NOT NULL AND icd.loan_min_debt_yield != ''
      AND icc.entity_type = 'company'
      GROUP BY loan_min_debt_yield
      ORDER BY count DESC, loan_min_debt_yield ASC
    `;
    const loanMinDebtYieldResult = await client.query(loanMinDebtYieldQuery);

    // Additional Debt-specific fields
    const eligibleBorrowersQuery = `
      SELECT DISTINCT eligible_borrower as value, eligible_borrower as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.eligible_borrower IS NOT NULL AND icd.eligible_borrower != ''
      AND icc.entity_type = 'company'
      GROUP BY eligible_borrower
      ORDER BY count DESC, eligible_borrower ASC
    `;
    const eligibleBorrowersResult = await client.query(eligibleBorrowersQuery);

    const lienPositionsQuery = `
      SELECT DISTINCT lien_position as value, lien_position as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.lien_position IS NOT NULL AND icd.lien_position != ''
      AND icc.entity_type = 'company'
      GROUP BY lien_position
      ORDER BY count DESC, lien_position ASC
    `;
    const lienPositionsResult = await client.query(lienPositionsQuery);

    const rateLocksQuery = `
      SELECT DISTINCT rate_lock as value, rate_lock as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.rate_lock IS NOT NULL AND icd.rate_lock != ''
      AND icc.entity_type = 'company'
      GROUP BY rate_lock
      ORDER BY count DESC, rate_lock ASC
    `;
    const rateLocksResult = await client.query(rateLocksQuery);

    const rateTypesQuery = `
      SELECT DISTINCT rate_type as value, rate_type as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.rate_type IS NOT NULL AND icd.rate_type != ''
      AND icc.entity_type = 'company'
      GROUP BY rate_type
      ORDER BY count DESC, rate_type ASC
    `;
    const rateTypesResult = await client.query(rateTypesQuery);

    const amortizationsQuery = `
      SELECT DISTINCT amortization as value, amortization as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.amortization IS NOT NULL AND icd.amortization != ''
      AND icc.entity_type = 'company'
      GROUP BY amortization
      ORDER BY count DESC, amortization ASC
    `;
    const amortizationsResult = await client.query(amortizationsQuery);

    // Additional Debt-specific fields from CSV
    const futureFacilitiesQuery = `
      SELECT DISTINCT future_facilities as value, future_facilities as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.future_facilities IS NOT NULL AND icd.future_facilities != ''
      AND icc.entity_type = 'company'
      GROUP BY future_facilities
      ORDER BY count DESC, future_facilities ASC
    `;
    const futureFacilitiesResult = await client.query(futureFacilitiesQuery);

    const occupancyRequirementsQuery = `
      SELECT DISTINCT occupancy_requirements as value, occupancy_requirements as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.occupancy_requirements IS NOT NULL AND icd.occupancy_requirements != ''
      AND icc.entity_type = 'company'
      GROUP BY occupancy_requirements
      ORDER BY count DESC, occupancy_requirements ASC
    `;
    const occupancyRequirementsResult = await client.query(occupancyRequirementsQuery);

    const prepaymentQuery = `
      SELECT DISTINCT prepayment as value, prepayment as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.prepayment IS NOT NULL AND icd.prepayment != ''
      AND icc.entity_type = 'company'
      GROUP BY prepayment
      ORDER BY count DESC, prepayment ASC
    `;
    const prepaymentResult = await client.query(prepaymentQuery);

    const yieldMaintenanceQuery = `
      SELECT DISTINCT yield_maintenance as value, yield_maintenance as label, COUNT(*) as count
      FROM investment_criteria_debt icd
      JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
      WHERE icd.yield_maintenance IS NOT NULL AND icd.yield_maintenance != ''
      AND icc.entity_type = 'company'
      GROUP BY yield_maintenance
      ORDER BY count DESC, yield_maintenance ASC
    `;
    const yieldMaintenanceResult = await client.query(yieldMaintenanceQuery);

    // Equity-specific fields
    const ownershipRequirementsQuery = `
      SELECT DISTINCT ownership_requirement as value, ownership_requirement as label, COUNT(*) as count
      FROM investment_criteria_equity ice
      JOIN investment_criteria_central icc ON ice.investment_criteria_id = icc.investment_criteria_id
      WHERE ice.ownership_requirement IS NOT NULL AND ice.ownership_requirement != ''
      AND icc.entity_type = 'company'
      GROUP BY ownership_requirement
      ORDER BY count DESC, ownership_requirement ASC
    `;
    const ownershipRequirementsResult = await client.query(ownershipRequirementsQuery);

    const result = {
      capitalPositions: capitalPositionsResult.rows,
      propertyTypes: propertyTypesResult.rows,
      propertySubcategories: propertySubcategoriesResult.rows,
      strategies: strategiesResult.rows,
      loanTypes: loanTypesResult.rows,
      loanTypeNormalized: loanTypesNormalizedResult.rows,
      structuredLoanTranches: structuredLoanTranchesResult.rows,
      loanPrograms: loanProgramsResult.rows,
      recourseLoans: recourseLoansResult.rows,
      countries: countriesResult.rows,
      regions: regionsResult.rows,
      states: statesResult.rows,
      cities: citiesResult.rows,
      decisionMakingProcesses: decisionMakingProcessesResult.rows,
      eligibleBorrowers: eligibleBorrowersResult.rows,
      lienPositions: lienPositionsResult.rows,
      rateLocks: rateLocksResult.rows,
      rateTypes: rateTypesResult.rows,
      amortizations: amortizationsResult.rows,
      ownershipRequirements: ownershipRequirementsResult.rows,
      loanMinDebtYield: loanMinDebtYieldResult.rows,
      futureFacilities: futureFacilitiesResult.rows,
      occupancyRequirements: occupancyRequirementsResult.rows,
      prepayment: prepaymentResult.rows,
      yieldMaintenance: yieldMaintenanceResult.rows,
    };
    
    console.log('🔍 fetchInvestmentCriteriaOptions: Returning investment criteria result with', Object.keys(result).length, 'categories');
    return result;
    
  } finally {
    console.log('🔍 fetchInvestmentCriteriaOptions: Releasing database connection');
    client.release();
  }
}
