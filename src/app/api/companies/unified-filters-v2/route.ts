import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    console.log('[Companies Unified API] Search params:', searchParams)

    // Stats mode: support stats=true|false|only and keep backward compat with statsOnly=true
    const statsParam = searchParams.get('stats')
    const legacyStatsOnly = searchParams.get('statsOnly') === 'true'
    type StatsMode = 'true' | 'false' | 'only'
    const statsMode: StatsMode = statsParam === 'only' ? 'only' : statsParam === 'true' ? 'true' : statsParam === 'false' ? 'false' : (legacyStatsOnly ? 'only' : 'false')
    const shouldComputeStats = statsMode === 'only' || statsMode === 'true'
    const shouldReturnData = statsMode !== 'only'
    


    // Investment criteria filters
    const searchTerm = searchParams.get('searchTerm')
    const entityType = searchParams.get('entityType')
    const entityId = searchParams.get('entityId')
    const entityName = searchParams.get('entityName')
    const criteriaId = searchParams.get('criteriaId')

    // Core Company Table Filters
    const companyAddress = searchParams.get('companyAddress')?.split(',').filter(Boolean)
    const companyCity = searchParams.get('companyCity')?.split(',').filter(Boolean)
    const companyState = searchParams.get('companyState')?.split(',').filter(Boolean)
    const companyWebsite = searchParams.get('companyWebsite')?.split(',').filter(Boolean)
    const industry = searchParams.get('industry')?.split(',').filter(Boolean)
    const companyCountry = searchParams.get('companyCountry')?.split(',').filter(Boolean)
    const source = searchParams.get('source')?.split(',').filter(Boolean)
    const companyIds = searchParams.get('companyIds')?.split(',')
    const companyNames = searchParams.get('companyNames')?.split(',')
    const notEmptyCompanyWebsite = searchParams.get('notEmptyCompanyWebsite') === 'true'

    // Company Processing Status Filters
    const websiteScrapingStatus = searchParams.get('websiteScrapingStatus')?.split(',').filter(Boolean)
    const companyOverviewStatus = searchParams.get('companyOverviewStatus')?.split(',').filter(Boolean)
    const overviewV2Status = searchParams.get('overviewV2Status')?.split(',').filter(Boolean)
    const investmentCriteriaStatus = searchParams.get('companyInvestmentCriteriaStatus')?.split(',').filter(Boolean)
    const companyOverviewV2Status = searchParams.get('companyOverviewV2Status')?.split(',').filter(Boolean)

    // Company Overview V2 Fields
    const companyType = searchParams.get('companyType')?.split(',').filter(Boolean)
    const foundedYearMin = searchParams.get('foundedYearMin') ? parseInt(searchParams.get('foundedYearMin')!) : null
    const foundedYearMax = searchParams.get('foundedYearMax') ? parseInt(searchParams.get('foundedYearMax')!) : null

    // Investment & Strategy V2 Filters
    const investmentFocus = searchParams.get('investmentFocus')?.split(',').filter(Boolean)

    // Contact Information V2 Filters
    const hasMainPhone = searchParams.get('hasMainPhone') === 'true' ? true : searchParams.get('hasMainPhone') === 'false' ? false : null
    const hasSecondaryPhone = searchParams.get('hasSecondaryPhone') === 'true' ? true : searchParams.get('hasSecondaryPhone') === 'false' ? false : null
    const hasMainEmail = searchParams.get('hasMainEmail') === 'true' ? true : searchParams.get('hasMainEmail') === 'false' ? false : null
    const hasSecondaryEmail = searchParams.get('hasSecondaryEmail') === 'true' ? true : searchParams.get('hasSecondaryEmail') === 'false' ? false : null
    const hasCompanyLinkedin = searchParams.get('hasCompanyLinkedin') === 'true' ? true : searchParams.get('hasCompanyLinkedin') === 'false' ? false : null
    const hasTwitter = searchParams.get('hasTwitter') === 'true' ? true : searchParams.get('hasTwitter') === 'false' ? false : null
    const hasFacebook = searchParams.get('hasFacebook') === 'true' ? true : searchParams.get('hasFacebook') === 'false' ? false : null
    const hasInstagram = searchParams.get('hasInstagram') === 'true' ? true : searchParams.get('hasInstagram') === 'false' ? false : null
    const hasYoutube = searchParams.get('hasYoutube') === 'true' ? true : searchParams.get('hasYoutube') === 'false' ? false : null

    // Location V2 Filters
    const headquartersAddress = searchParams.get('headquartersAddress')?.split(',').filter(Boolean)
    const headquartersCity = searchParams.get('headquartersCity')?.split(',').filter(Boolean)
    const headquartersState = searchParams.get('headquartersState')?.split(',').filter(Boolean)
    const headquartersCountry = searchParams.get('headquartersCountry')?.split(',').filter(Boolean)
    const officeLocations = searchParams.get('officeLocations')?.split(',').filter(Boolean)

    // Financial Metrics V2 Filters
    const fundSizeMin = searchParams.get('fundSizeMin') ? parseFloat(searchParams.get('fundSizeMin')!) : null
    const fundSizeMax = searchParams.get('fundSizeMax') ? parseFloat(searchParams.get('fundSizeMax')!) : null
    const aumMin = searchParams.get('aumMin') ? parseFloat(searchParams.get('aumMin')!) : null
    const aumMax = searchParams.get('aumMax') ? parseFloat(searchParams.get('aumMax')!) : null
    const numberOfPropertiesMin = searchParams.get('numberOfPropertiesMin') ? parseInt(searchParams.get('numberOfPropertiesMin')!) : null
    const numberOfPropertiesMax = searchParams.get('numberOfPropertiesMax') ? parseInt(searchParams.get('numberOfPropertiesMax')!) : null
    const numberOfOfficesMin = searchParams.get('numberOfOfficesMin') ? parseInt(searchParams.get('numberOfOfficesMin')!) : null
    const numberOfOfficesMax = searchParams.get('numberOfOfficesMax') ? parseInt(searchParams.get('numberOfOfficesMax')!) : null
    const numberOfEmployeesMin = searchParams.get('numberOfEmployeesMin') ? parseInt(searchParams.get('numberOfEmployeesMin')!) : null
    const numberOfEmployeesMax = searchParams.get('numberOfEmployeesMax') ? parseInt(searchParams.get('numberOfEmployeesMax')!) : null
    const annualRevenueMin = searchParams.get('annualRevenueMin') ? parseFloat(searchParams.get('annualRevenueMin')!) : null
    const annualRevenueMax = searchParams.get('annualRevenueMax') ? parseFloat(searchParams.get('annualRevenueMax')!) : null

    // Financial Information V2 Filters
    const balanceSheetStrength = searchParams.get('balanceSheetStrength')?.split(',').filter(Boolean)
    const fundingSources = searchParams.get('fundingSources')?.split(',').filter(Boolean)
    const creditRating = searchParams.get('creditRating')?.split(',').filter(Boolean)
    const dryPowderMin = searchParams.get('dryPowderMin') ? parseFloat(searchParams.get('dryPowderMin')!) : null
    const dryPowderMax = searchParams.get('dryPowderMax') ? parseFloat(searchParams.get('dryPowderMax')!) : null

    // Investment & Fund Information V2 Filters
    const investmentVehicleType = searchParams.get('investmentVehicleType')?.split(',').filter(Boolean)
    const fundraisingStatus = searchParams.get('fundraisingStatus')?.split(',').filter(Boolean)
    const lenderType = searchParams.get('lenderType')?.split(',').filter(Boolean)
    const annualLoanVolumeMin = searchParams.get('annualLoanVolumeMin') ? parseFloat(searchParams.get('annualLoanVolumeMin')!) : null
    const annualLoanVolumeMax = searchParams.get('annualLoanVolumeMax') ? parseFloat(searchParams.get('annualLoanVolumeMax')!) : null
    const portfolioHealth = searchParams.get('portfolioHealth')?.split(',').filter(Boolean)

    // Partnership & Leadership V2 Filters
    const partnerships = searchParams.get('partnerships')?.split(',').filter(Boolean)
    const keyEquityPartners = searchParams.get('keyEquityPartners')?.split(',').filter(Boolean)
    const keyDebtPartners = searchParams.get('keyDebtPartners')?.split(',').filter(Boolean)
    const keyExecutives = searchParams.get('keyExecutives')?.split(',').filter(Boolean)

    // Market Positioning & Strategy V2 Filters
    const sustainabilityEsgFocus = searchParams.get('sustainabilityEsgFocus') === 'true' ? true : searchParams.get('sustainabilityEsgFocus') === 'false' ? false : null
    const technologyProptechAdoption = searchParams.get('technologyProptechAdoption') === 'true' ? true : searchParams.get('technologyProptechAdoption') === 'false' ? false : null
    const adaptiveReuseExperience = searchParams.get('adaptiveReuseExperience') === 'true' ? true : searchParams.get('adaptiveReuseExperience') === 'false' ? false : null
    const regulatoryZoningExpertise = searchParams.get('regulatoryZoningExpertise') === 'true' ? true : searchParams.get('regulatoryZoningExpertise') === 'false' ? false : null

    // Corporate Structure V2 Filters
    const corporateStructure = searchParams.get('corporateStructure')?.split(',').filter(Boolean)
    const parentCompany = searchParams.get('parentCompany')?.split(',').filter(Boolean)
    const stockTickerSymbol = searchParams.get('stockTickerSymbol')?.split(',').filter(Boolean)
    const stockExchange = searchParams.get('stockExchange')?.split(',').filter(Boolean)

    // Contact Processor Flags
    const hasContacts = searchParams.get('hasContacts') === 'true' ? true : searchParams.get('hasContacts') === 'false' ? false : null
    const contactsEmailVerificationStatus = searchParams.get('contactsEmailVerificationStatus')?.split(',').filter(Boolean)
    const contactsEnrichmentStatus = searchParams.get('contactsEnrichmentStatus')?.split(',').filter(Boolean)
    const contactsEnrichmentV2Status = searchParams.get('contactsEnrichmentV2Status')?.split(',').filter(Boolean)
    const contactsEmailGenerationStatus = searchParams.get('contactsEmailGenerationStatus')?.split(',').filter(Boolean)
    const contactsEmailSendingStatus = searchParams.get('contactsEmailSendingStatus')?.split(',').filter(Boolean)

    // Capital & Financing
    const capitalPosition = searchParams.get('capitalPosition')?.split(',').filter(Boolean)
    const loanTypes = searchParams.get('loanTypes')?.split(',').filter(Boolean)
    const loanProgram = searchParams.get('loanProgram')?.split(',').filter(Boolean)
    const structuredLoanTranche = searchParams.get('structuredLoanTranche')?.split(',').filter(Boolean)
    const recourseLoan = searchParams.get('recourseLoan')?.split(',').filter(Boolean)

    // Deal size
    const dealSizeMin = searchParams.get('dealSizeMin') ? parseFloat(searchParams.get('dealSizeMin')!) : null
    const dealSizeMax = searchParams.get('dealSizeMax') ? parseFloat(searchParams.get('dealSizeMax')!) : null

    // Returns
    const targetReturnMin = searchParams.get('targetReturnMin') ? parseFloat(searchParams.get('targetReturnMin')!) : null
    const targetReturnMax = searchParams.get('targetReturnMax') ? parseFloat(searchParams.get('targetReturnMax')!) : null
    const historicalIrrMin = searchParams.get('historicalIrrMin') ? parseFloat(searchParams.get('historicalIrrMin')!) : null
    const historicalIrrMax = searchParams.get('historicalIrrMax') ? parseFloat(searchParams.get('historicalIrrMax')!) : null
    const historicalEmMin = searchParams.get('historicalEmMin') ? parseFloat(searchParams.get('historicalEmMin')!) : null
    const historicalEmMax = searchParams.get('historicalEmMax') ? parseFloat(searchParams.get('historicalEmMax')!) : null

    // Hold periods
    const minHoldPeriod = searchParams.get('minHoldPeriod') ? parseInt(searchParams.get('minHoldPeriod')!) : null
    const maxHoldPeriod = searchParams.get('maxHoldPeriod') ? parseInt(searchParams.get('maxHoldPeriod')!) : null

    // Loan terms
    const minLoanTerm = searchParams.get('minLoanTerm') ? parseInt(searchParams.get('minLoanTerm')!) : null
    const maxLoanTerm = searchParams.get('maxLoanTerm') ? parseInt(searchParams.get('maxLoanTerm')!) : null
    const interestRateMin = searchParams.get('interestRateMin') ? parseFloat(searchParams.get('interestRateMin')!) : null
    const interestRateMax = searchParams.get('interestRateMax') ? parseFloat(searchParams.get('interestRateMax')!) : null

    // LTV/LTC
    const loanToValueMin = searchParams.get('loanToValueMin') ? parseFloat(searchParams.get('loanToValueMin')!) : null
    const loanToValueMax = searchParams.get('loanToValueMax') ? parseFloat(searchParams.get('loanToValueMax')!) : null
    const loanToCostMin = searchParams.get('loanToCostMin') ? parseFloat(searchParams.get('loanToCostMin')!) : null
    const loanToCostMax = searchParams.get('loanToCostMax') ? parseFloat(searchParams.get('loanToCostMax')!) : null

    // DSCR
    const minLoanDscr = searchParams.get('minLoanDscr') ? parseFloat(searchParams.get('minLoanDscr')!) : null
    const maxLoanDscr = searchParams.get('maxLoanDscr') ? parseFloat(searchParams.get('maxLoanDscr')!) : null

    // Property & Geographic
    const propertyTypes = searchParams.get('propertyTypes')?.split(',').filter(Boolean)
    const propertySubcategories = searchParams.get('propertySubcategories')?.split(',').filter(Boolean)
    const strategies = searchParams.get('strategies')?.split(',').filter(Boolean)
    const financialProducts = searchParams.get('financialProducts')?.split(',').filter(Boolean)
    const regions = searchParams.get('regions')?.split(',').filter(Boolean)
    const states = searchParams.get('states')?.split(',').filter(Boolean)
    const cities = searchParams.get('cities')?.split(',').filter(Boolean)
    const countries = searchParams.get('countries')?.split(',').filter(Boolean)

    // Company extracted data filters (additional)
    const companyIndustry = searchParams.get('companyIndustry')?.split(',').filter(Boolean)
    const headquarters = searchParams.get('headquarters')?.split(',').filter(Boolean)

    // Company Financial Range Filters (additional)
    const portfolioValueMin = searchParams.get('portfolioValueMin') ? parseFloat(searchParams.get('portfolioValueMin')!) : null
    const portfolioValueMax = searchParams.get('portfolioValueMax') ? parseFloat(searchParams.get('portfolioValueMax')!) : null
    const historicalReturnsMin = searchParams.get('historicalReturnsMin') ? parseFloat(searchParams.get('historicalReturnsMin')!) : null
    const historicalReturnsMax = searchParams.get('historicalReturnsMax') ? parseFloat(searchParams.get('historicalReturnsMax')!) : null

    // Company Scale Range Filters (additional)
    const totalTransactionsMin = searchParams.get('totalTransactionsMin') ? parseInt(searchParams.get('totalTransactionsMin')!) : null
    const totalTransactionsMax = searchParams.get('totalTransactionsMax') ? parseInt(searchParams.get('totalTransactionsMax')!) : null
    const totalSquareFeetMin = searchParams.get('totalSquareFeetMin') ? parseFloat(searchParams.get('totalSquareFeetMin')!) : null
    const totalSquareFeetMax = searchParams.get('totalSquareFeetMax') ? parseFloat(searchParams.get('totalSquareFeetMax')!) : null
    const totalUnitsMin = searchParams.get('totalUnitsMin') ? parseInt(searchParams.get('totalUnitsMin')!) : null
    const totalUnitsMax = searchParams.get('totalUnitsMax') ? parseInt(searchParams.get('totalUnitsMax')!) : null

    // Timeline
    const closingTimeWeeks = searchParams.get('closingTimeWeeks') ? parseInt(searchParams.get('closingTimeWeeks')!) : null

    // Status
    const isActive = searchParams.get('isActive') === 'true' ? true : searchParams.get('isActive') === 'false' ? false : null

    // === NEW DEBT & EQUITY FIELDS ===
    // Enhanced Debt Fields
    const eligibleBorrower = searchParams.get('eligibleBorrower')?.split(',').filter(Boolean)
    const lienPosition = searchParams.get('lienPosition')?.split(',').filter(Boolean)
    const rateLock = searchParams.get('rateLock')?.split(',').filter(Boolean)
    const rateType = searchParams.get('rateType')?.split(',').filter(Boolean)
    const amortization = searchParams.get('amortization')?.split(',').filter(Boolean)
    const loanTypeNormalized = searchParams.get('loanTypeNormalized')?.split(',').filter(Boolean)

    // Additional Debt Fields from CSV
    const loanMinDebtYield = searchParams.get('loanMinDebtYield')?.split(',').filter(Boolean)
    const closingTimeMin = searchParams.get('closingTimeMin') ? parseFloat(searchParams.get('closingTimeMin')!) : null
    const closingTimeMax = searchParams.get('closingTimeMax') ? parseFloat(searchParams.get('closingTimeMax')!) : null

    // Additional Debt Fields from CSV - Missing fields
    const futureFacilities = searchParams.get('futureFacilities')?.split(',').filter(Boolean)
    const occupancyRequirements = searchParams.get('occupancyRequirements')?.split(',').filter(Boolean)
    const prepayment = searchParams.get('prepayment')?.split(',').filter(Boolean)
    const yieldMaintenance = searchParams.get('yieldMaintenance')?.split(',').filter(Boolean)

    // Loan Fee Fields
    const loanOriginationMaxFeeMin = searchParams.get('loanOriginationMaxFeeMin') ? parseFloat(searchParams.get('loanOriginationMaxFeeMin')!) : null
    const loanOriginationMaxFeeMax = searchParams.get('loanOriginationMaxFeeMax') ? parseFloat(searchParams.get('loanOriginationMaxFeeMax')!) : null
    const loanOriginationMinFeeMin = searchParams.get('loanOriginationMinFeeMin') ? parseFloat(searchParams.get('loanOriginationMinFeeMin')!) : null
    const loanOriginationMinFeeMax = searchParams.get('loanOriginationMinFeeMax') ? parseFloat(searchParams.get('loanOriginationMinFeeMax')!) : null
    const loanExitMinFeeMin = searchParams.get('loanExitMinFeeMin') ? parseFloat(searchParams.get('loanExitMinFeeMin')!) : null
    const loanExitMinFeeMax = searchParams.get('loanExitMinFeeMax') ? parseFloat(searchParams.get('loanExitMinFeeMax')!) : null
    const loanExitMaxFeeMin = searchParams.get('loanExitMaxFeeMin') ? parseFloat(searchParams.get('loanExitMaxFeeMin')!) : null
    const loanExitMaxFeeMax = searchParams.get('loanExitMaxFeeMax') ? parseFloat(searchParams.get('loanExitMaxFeeMax')!) : null

    // Loan Interest Rate Fields
    const loanInterestRateSofrMin = searchParams.get('loanInterestRateSofrMin') ? parseFloat(searchParams.get('loanInterestRateSofrMin')!) : null
    const loanInterestRateSofrMax = searchParams.get('loanInterestRateSofrMax') ? parseFloat(searchParams.get('loanInterestRateSofrMax')!) : null
    const loanInterestRateWsjMin = searchParams.get('loanInterestRateWsjMin') ? parseFloat(searchParams.get('loanInterestRateWsjMin')!) : null
    const loanInterestRateWsjMax = searchParams.get('loanInterestRateWsjMax') ? parseFloat(searchParams.get('loanInterestRateWsjMax')!) : null
    const loanInterestRatePrimeMin = searchParams.get('loanInterestRatePrimeMin') ? parseFloat(searchParams.get('loanInterestRatePrimeMin')!) : null
    const loanInterestRatePrimeMax = searchParams.get('loanInterestRatePrimeMax') ? parseFloat(searchParams.get('loanInterestRatePrimeMax')!) : null
    const loanInterestRate3ytMin = searchParams.get('loanInterestRate3ytMin') ? parseFloat(searchParams.get('loanInterestRate3ytMin')!) : null
    const loanInterestRate3ytMax = searchParams.get('loanInterestRate3ytMax') ? parseFloat(searchParams.get('loanInterestRate3ytMax')!) : null
    const loanInterestRate5ytMin = searchParams.get('loanInterestRate5ytMin') ? parseFloat(searchParams.get('loanInterestRate5ytMin')!) : null
    const loanInterestRate5ytMax = searchParams.get('loanInterestRate5ytMax') ? parseFloat(searchParams.get('loanInterestRate5ytMax')!) : null
    const loanInterestRate10ytMin = searchParams.get('loanInterestRate10ytMin') ? parseFloat(searchParams.get('loanInterestRate10ytMin')!) : null
    const loanInterestRate10ytMax = searchParams.get('loanInterestRate10ytMax') ? parseFloat(searchParams.get('loanInterestRate10ytMax')!) : null
    const loanInterestRate30ytMin = searchParams.get('loanInterestRate30ytMin') ? parseFloat(searchParams.get('loanInterestRate30ytMin')!) : null
    const loanInterestRate30ytMax = searchParams.get('loanInterestRate30ytMax') ? parseFloat(searchParams.get('loanInterestRate30ytMax')!) : null

    // Loan Sizing Fields
    const loanToValueMinMin = searchParams.get('loanToValueMinMin') ? parseFloat(searchParams.get('loanToValueMinMin')!) : null
    const loanToValueMinMax = searchParams.get('loanToValueMinMax') ? parseFloat(searchParams.get('loanToValueMinMax')!) : null
    const loanToValueMaxMin = searchParams.get('loanToValueMaxMin') ? parseFloat(searchParams.get('loanToValueMaxMin')!) : null
    const loanToValueMaxMax = searchParams.get('loanToValueMaxMax') ? parseFloat(searchParams.get('loanToValueMaxMax')!) : null
    const loanToCostMinMin = searchParams.get('loanToCostMinMin') ? parseFloat(searchParams.get('loanToCostMinMin')!) : null
    const loanToCostMinMax = searchParams.get('loanToCostMinMax') ? parseFloat(searchParams.get('loanToCostMinMax')!) : null
    const loanToCostMaxMin = searchParams.get('loanToCostMaxMin') ? parseFloat(searchParams.get('loanToCostMaxMin')!) : null
    const loanToCostMaxMax = searchParams.get('loanToCostMaxMax') ? parseFloat(searchParams.get('loanToCostMaxMax')!) : null

    // Loan Term Fields
    const minLoanTermMin = searchParams.get('minLoanTermMin') ? parseFloat(searchParams.get('minLoanTermMin')!) : null
    const minLoanTermMax = searchParams.get('minLoanTermMax') ? parseFloat(searchParams.get('minLoanTermMax')!) : null
    const maxLoanTermMin = searchParams.get('maxLoanTermMin') ? parseFloat(searchParams.get('maxLoanTermMin')!) : null
    const maxLoanTermMax = searchParams.get('maxLoanTermMax') ? parseFloat(searchParams.get('maxLoanTermMax')!) : null

    // Enhanced Equity Fields
    const ownershipRequirement = searchParams.get('ownershipRequirement')?.split(',').filter(Boolean)
    const minimumYieldOnCostMin = searchParams.get('minimumYieldOnCostMin') ? parseFloat(searchParams.get('minimumYieldOnCostMin')!) : null
    const minimumYieldOnCostMax = searchParams.get('minimumYieldOnCostMax') ? parseFloat(searchParams.get('minimumYieldOnCostMax')!) : null
    const maxLeverageToleranceMin = searchParams.get('maxLeverageToleranceMin') ? parseFloat(searchParams.get('maxLeverageToleranceMin')!) : null
    const maxLeverageToleranceMax = searchParams.get('maxLeverageToleranceMax') ? parseFloat(searchParams.get('maxLeverageToleranceMax')!) : null



    // === V2 ENHANCED FILTERS ===
    // Enhanced NOT filters for V2
    const notFilters = {
      notCapitalPosition: searchParams.get('notCapitalPosition')?.split(',').filter(Boolean),
      notPropertyTypes: searchParams.get('notPropertyTypes')?.split(',').filter(Boolean),
      notStrategies: searchParams.get('notStrategies')?.split(',').filter(Boolean),
      notCompanyType: searchParams.get('notCompanyType')?.split(',').filter(Boolean),
      notCompanyIndustry: searchParams.get('notCompanyIndustry')?.split(',').filter(Boolean),
      notWebsiteScrapingStatus: searchParams.get('notWebsiteScrapingStatus')?.split(',').filter(Boolean),
      notCompanyOverviewStatus: searchParams.get('notCompanyOverviewStatus')?.split(',').filter(Boolean),
      notOverviewV2Status: searchParams.get('notOverviewV2Status')?.split(',').filter(Boolean),
      notInvestmentCriteriaStatus: searchParams.get('notInvestmentCriteriaStatus')?.split(',').filter(Boolean),
      // Contact processor NOT filters
      notContactsEmailVerificationStatus: searchParams.get('notContactsEmailVerificationStatus')?.split(',').filter(Boolean),
      notContactsEnrichmentStatus: searchParams.get('notContactsEnrichmentStatus')?.split(',').filter(Boolean),
      notContactsEnrichmentV2Status: searchParams.get('notContactsEnrichmentV2Status')?.split(',').filter(Boolean),
      notContactsEmailGenerationStatus: searchParams.get('notContactsEmailGenerationStatus')?.split(',').filter(Boolean),
      notContactsEmailSendingStatus: searchParams.get('notContactsEmailSendingStatus')?.split(',').filter(Boolean),
      // Additional investment criteria NOT filters
      notLoanTypes: searchParams.get('notLoanTypes')?.split(',').filter(Boolean),
      notStructuredLoanTranche: searchParams.get('notStructuredLoanTranche')?.split(',').filter(Boolean),
      notLoanProgram: searchParams.get('notLoanProgram')?.split(',').filter(Boolean),
      notRecourseLoan: searchParams.get('notRecourseLoan')?.split(',').filter(Boolean),
      notEligibleBorrower: searchParams.get('notEligibleBorrower')?.split(',').filter(Boolean),
      notLienPosition: searchParams.get('notLienPosition')?.split(',').filter(Boolean),
      notOwnershipRequirement: searchParams.get('notOwnershipRequirement')?.split(',').filter(Boolean),
      notRateType: searchParams.get('notRateType')?.split(',').filter(Boolean),
      notAmortization: searchParams.get('notAmortization')?.split(',').filter(Boolean),
      // Additional company filters
      notFundraisingStatus: searchParams.get('notFundraisingStatus')?.split(',').filter(Boolean),
      notLenderType: searchParams.get('notLenderType')?.split(',').filter(Boolean),
      notPartnerships: searchParams.get('notPartnerships')?.split(',').filter(Boolean),
      notInvestmentFocus: searchParams.get('notInvestmentFocus')?.split(',').filter(Boolean)
    }

    // V2 Company Overview Enhanced Filters (if you have a company_overview_v2 table)
    const companyOverviewV2Filters = {
      hasDetailed: searchParams.get('hasDetailedOverview') === 'true' ? true : searchParams.get('hasDetailedOverview') === 'false' ? false : null,
      hasKeyPersonnel: searchParams.get('hasKeyPersonnel') === 'true' ? true : searchParams.get('hasKeyPersonnel') === 'false' ? false : null,
      hasRecentNews: searchParams.get('hasRecentNews') === 'true' ? true : searchParams.get('hasRecentNews') === 'false' ? false : null,
      hasCompetitiveAnalysis: searchParams.get('hasCompetitiveAnalysis') === 'true' ? true : searchParams.get('hasCompetitiveAnalysis') === 'false' ? false : null
    }

    // Pagination with safety limit
    const page = parseInt(searchParams.get('page') || '1')
    const requestedLimit = parseInt(searchParams.get('limit') || '50')
    const limit = Math.min(requestedLimit, 500) // Safety limit of 500
    const offset = (page - 1) * limit

    // Sorting
    const sortBy = searchParams.get('sortBy') || 'updated_at'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // Build WHERE conditions
    const whereConditions: string[] = []
    const queryParams: any[] = []
    let paramIndex = 1

    // Log incoming query parameters for debugging
    console.log('[Companies Unified API] Query params:', {
      searchTerm,
      websiteScrapingStatus,
      overviewV2Status,
      investmentCriteriaStatus,
      source,
      limit,
      offset,
      statsMode
    })

    // DEBUG: Log the notFilters to understand what's happening
    console.log('[Companies Unified API] DEBUG - notFilters:', {
      notCapitalPosition: notFilters.notCapitalPosition,
      notCapitalPositionLength: notFilters.notCapitalPosition?.length,
      notPropertyTypes: notFilters.notPropertyTypes,
      notPropertyTypesLength: notFilters.notPropertyTypes?.length,
      notStrategies: notFilters.notStrategies,
      notStrategiesLength: notFilters.notStrategies?.length
    })

    // Determine which tables we need to join based on filters
    const hasInvestmentCriteriaFilters = !!(
      capitalPosition?.length || loanTypes?.length || loanProgram?.length ||
      structuredLoanTranche?.length || recourseLoan?.length ||
      dealSizeMin !== null || dealSizeMax !== null ||
      targetReturnMin !== null || targetReturnMax !== null ||
      historicalIrrMin !== null || historicalIrrMax !== null ||
      historicalEmMin !== null || historicalEmMax !== null ||
      minHoldPeriod !== null || maxHoldPeriod !== null ||
      minLoanTerm !== null || maxLoanTerm !== null ||
      interestRateMin !== null || interestRateMax !== null ||
      loanToValueMin !== null || loanToValueMax !== null ||
      loanToCostMin !== null || loanToCostMax !== null ||
      minLoanDscr !== null || maxLoanDscr !== null ||
      propertyTypes?.length || propertySubcategories?.length ||
      strategies?.length || financialProducts?.length ||
      regions?.length || states?.length || cities?.length || countries?.length ||
      closingTimeWeeks !== null || isActive !== null ||
      entityId || entityName || criteriaId ||
      notFilters.notCapitalPosition?.length || notFilters.notPropertyTypes?.length || notFilters.notStrategies?.length ||
      notFilters.notLoanTypes?.length || notFilters.notStructuredLoanTranche?.length || notFilters.notLoanProgram?.length ||
      notFilters.notRecourseLoan?.length || notFilters.notEligibleBorrower?.length || notFilters.notLienPosition?.length ||
      notFilters.notOwnershipRequirement?.length || notFilters.notRateType?.length || notFilters.notAmortization?.length ||
      eligibleBorrower?.length || lienPosition?.length || rateLock?.length ||
      rateType?.length || amortization?.length || loanTypeNormalized?.length ||
      ownershipRequirement?.length ||
      loanMinDebtYield?.length || closingTimeMin !== null || closingTimeMax !== null ||
      futureFacilities?.length || occupancyRequirements?.length || prepayment?.length || yieldMaintenance?.length ||
      loanOriginationMaxFeeMin !== null || loanOriginationMaxFeeMax !== null ||
      loanOriginationMinFeeMin !== null || loanOriginationMinFeeMax !== null ||
      loanExitMinFeeMin !== null || loanExitMinFeeMax !== null ||
      loanExitMaxFeeMin !== null || loanExitMaxFeeMax !== null ||
      loanInterestRateSofrMin !== null || loanInterestRateSofrMax !== null ||
      loanInterestRateWsjMin !== null || loanInterestRateWsjMax !== null ||
      loanInterestRatePrimeMin !== null || loanInterestRatePrimeMax !== null ||
      loanInterestRate3ytMin !== null || loanInterestRate3ytMax !== null ||
      loanInterestRate5ytMin !== null || loanInterestRate5ytMax !== null ||
      loanInterestRate10ytMin !== null || loanInterestRate10ytMax !== null ||
      loanInterestRate30ytMin !== null || loanInterestRate30ytMax !== null ||
      loanToValueMinMin !== null || loanToValueMinMax !== null ||
      loanToValueMaxMin !== null || loanToValueMaxMax !== null ||
      loanToCostMinMin !== null || loanToCostMinMax !== null ||
      loanToCostMaxMin !== null || loanToCostMaxMax !== null ||
      minLoanTermMin !== null || minLoanTermMax !== null ||
      maxLoanTermMin !== null || maxLoanTermMax !== null ||
      minimumYieldOnCostMin !== null || minimumYieldOnCostMax !== null ||
      maxLeverageToleranceMin !== null || maxLeverageToleranceMax !== null
    )

    // V2: Most company data is now in the companies table, so we rarely need company_extracted_data
    // Only join company_extracted_data if we have filters that specifically need old extracted data
    const hasCompanyExtractedDataFilters = false
    // Note: In V2, all company filters use the companies table directly
    // company_extracted_data table is no longer needed for V2 filtering

    const hasContactProcessorFilters = !!(
      contactsEmailVerificationStatus?.length || contactsEnrichmentStatus?.length ||
      contactsEnrichmentV2Status?.length || contactsEmailGenerationStatus?.length || contactsEmailSendingStatus?.length ||
      notFilters.notContactsEmailVerificationStatus?.length || notFilters.notContactsEnrichmentStatus?.length ||
      notFilters.notContactsEnrichmentV2Status?.length || notFilters.notContactsEmailGenerationStatus?.length ||
      notFilters.notContactsEmailSendingStatus?.length
    )

    const hasCompanyOverviewV2Filters = !!(
      companyOverviewV2Filters.hasDetailed !== null || companyOverviewV2Filters.hasKeyPersonnel !== null ||
      companyOverviewV2Filters.hasRecentNews !== null || companyOverviewV2Filters.hasCompetitiveAnalysis !== null
    )

    // Only filter for companies with investment criteria when IC filters are applied
    if (hasInvestmentCriteriaFilters) {
      whereConditions.push(`ic.entity_type = 'company'`)
    }

    // Enhanced global search - V2 uses companies table columns
    if (searchTerm) {
      whereConditions.push(`(
        c.company_name ILIKE $${paramIndex} OR 
        c.company_website ILIKE $${paramIndex} OR
        c.company_type ILIKE $${paramIndex} OR
        c.industry ILIKE $${paramIndex} OR
        c.company_address ILIKE $${paramIndex} OR
        array_to_string(c.key_executives, ', ') ILIKE $${paramIndex} OR
        array_to_string(c.partnerships, ', ') ILIKE $${paramIndex}
      )`)
      queryParams.push(`%${searchTerm}%`)
      paramIndex++
    }

    // Company Filters
    if (companyIds?.length) {
      whereConditions.push(`c.id = ANY($${paramIndex}::bigint[])`)
      queryParams.push(companyIds)
      paramIndex++
    }

    if (companyNames?.length) {
      whereConditions.push(`c.company_name ILIKE ANY($${paramIndex}::text[])`)
      queryParams.push(companyNames)
      paramIndex++
    }

    if (notEmptyCompanyWebsite) {
      whereConditions.push(`COALESCE(c.company_website, '') <> ''`)
    }

    // Core Company Table Filters
    if (source?.length) {
      const sourceConditions = source.map((_, index) => `c.source = $${paramIndex + index}`)
      whereConditions.push(`(${sourceConditions.join(' OR ')})`)
      source.forEach(s => {
        queryParams.push(s)
        paramIndex++
      })
    }

    if (companyAddress?.length) {
      const addressConditions = companyAddress.map((_, index) => `c.company_address ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${addressConditions.join(' OR ')})`)
      companyAddress.forEach(addr => {
        queryParams.push(`%${addr}%`)
        paramIndex++
      })
    }

    if (companyCity?.length) {
      const cityConditions = companyCity.map((_, index) => `c.company_city ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${cityConditions.join(' OR ')})`)
      companyCity.forEach(city => {
        queryParams.push(`%${city}%`)
        paramIndex++
      })
    }

    if (companyState?.length) {
      const stateConditions = companyState.map((_, index) => `c.company_state ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${stateConditions.join(' OR ')})`)
      companyState.forEach(state => {
        queryParams.push(`%${state}%`)
        paramIndex++
      })
    }

    if (companyWebsite?.length) {
      const websiteConditions = companyWebsite.map((_, index) => `c.company_website ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${websiteConditions.join(' OR ')})`)
      companyWebsite.forEach(website => {
        queryParams.push(`%${website}%`)
        paramIndex++
      })
    }

    if (industry?.length) {
      const industryConditions = industry.map((_, index) => `c.industry ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${industryConditions.join(' OR ')})`)
      industry.forEach(ind => {
        queryParams.push(`%${ind}%`)
        paramIndex++
      })
    }

    if (companyCountry?.length) {
      const countryConditions = companyCountry.map((_, index) => `c.company_country ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${countryConditions.join(' OR ')})`)
      companyCountry.forEach(country => {
        queryParams.push(`%${country}%`)
        paramIndex++
      })
    }

    // Company Processing Status Filters
    if (websiteScrapingStatus?.length) {
      const statusConditions = websiteScrapingStatus.map((_, index) => `c.website_scraping_status = $${paramIndex + index}`)
      whereConditions.push(`(${statusConditions.join(' OR ')})`)
      websiteScrapingStatus.forEach(status => {
        queryParams.push(status)
        paramIndex++
      })
    }

    if (companyOverviewStatus?.length) {
      const statusConditions = companyOverviewStatus.map((_, index) => `c.company_overview_status = $${paramIndex + index}`)
      whereConditions.push(`(${statusConditions.join(' OR ')})`)
      companyOverviewStatus.forEach(status => {
        queryParams.push(status)
        paramIndex++
      })
    }

    if (overviewV2Status?.length) {
      const statusConditions = overviewV2Status.map((_, index) => `c.overview_v2_status = $${paramIndex + index}`)
      whereConditions.push(`(${statusConditions.join(' OR ')})`)
      overviewV2Status.forEach(status => {
        queryParams.push(status)
        paramIndex++
      })
    }

    if (investmentCriteriaStatus?.length) {
      const statusConditions = investmentCriteriaStatus.map((_, index) => `c.investment_criteria_status = $${paramIndex + index}`)
      whereConditions.push(`(${statusConditions.join(' OR ')})`)
      investmentCriteriaStatus.forEach(status => {
        queryParams.push(status)
        paramIndex++
      })
    }


    if (companyOverviewV2Status?.length) {
      whereConditions.push(`c.overview_v2_status = ANY($${paramIndex}::text[])`)
      queryParams.push(companyOverviewV2Status)
      paramIndex++
    }

    // Company Overview V2 Fields
    if (companyType?.length) {
      const typeConditions = companyType.map((_, index) => `c.company_type ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${typeConditions.join(' OR ')})`)
      companyType.forEach(type => {
        queryParams.push(`%${type}%`)
        paramIndex++
      })
    }



    if (foundedYearMin !== null) {
      whereConditions.push(`c.founded_year >= $${paramIndex}`)
      queryParams.push(foundedYearMin)
      paramIndex++
    }

    if (foundedYearMax !== null) {
      whereConditions.push(`c.founded_year <= $${paramIndex}`)
      queryParams.push(foundedYearMax)
      paramIndex++
    }

    // Investment & Strategy V2 Filters
    if (investmentFocus?.length) {
      const focusConditions = investmentFocus.map((_, index) => `c.investment_focus && $${paramIndex + index}::text[]`)
      whereConditions.push(`(${focusConditions.join(' OR ')})`)
      investmentFocus.forEach(focus => {
        queryParams.push([focus])
        paramIndex++
      })
    }



    // Contact Information V2 Filters
    if (hasMainPhone !== null) {
      if (hasMainPhone) {
        whereConditions.push(`c.company_phone IS NOT NULL AND c.company_phone != ''`)
      } else {
        whereConditions.push(`(c.company_phone IS NULL OR c.company_phone = '')`)
      }
    }

    if (hasSecondaryPhone !== null) {
      if (hasSecondaryPhone) {
        whereConditions.push(`c.secondary_phone IS NOT NULL AND c.secondary_phone != ''`)
      } else {
        whereConditions.push(`(c.secondary_phone IS NULL OR c.secondary_phone = '')`)
      }
    }

    if (hasMainEmail !== null) {
      if (hasMainEmail) {
        whereConditions.push(`c.main_email IS NOT NULL AND c.main_email != ''`)
      } else {
        whereConditions.push(`(c.main_email IS NULL OR c.main_email = '')`)
      }
    }

    if (hasSecondaryEmail !== null) {
      if (hasSecondaryEmail) {
        whereConditions.push(`c.secondary_email IS NOT NULL AND c.secondary_email != ''`)
      } else {
        whereConditions.push(`(c.secondary_email IS NULL OR c.secondary_email = '')`)
      }
    }

    if (hasCompanyLinkedin !== null) {
      if (hasCompanyLinkedin) {
        whereConditions.push(`c.company_linkedin IS NOT NULL AND c.company_linkedin != ''`)
      } else {
        whereConditions.push(`(c.company_linkedin IS NULL OR c.company_linkedin = '')`)
      }
    }

    if (hasTwitter !== null) {
      if (hasTwitter) {
        whereConditions.push(`c.twitter IS NOT NULL AND c.twitter != ''`)
      } else {
        whereConditions.push(`(c.twitter IS NULL OR c.twitter = '')`)
      }
    }

    if (hasFacebook !== null) {
      if (hasFacebook) {
        whereConditions.push(`c.facebook IS NOT NULL AND c.facebook != ''`)
      } else {
        whereConditions.push(`(c.facebook IS NULL OR c.facebook = '')`)
      }
    }

    if (hasInstagram !== null) {
      if (hasInstagram) {
        whereConditions.push(`c.instagram IS NOT NULL AND c.instagram != ''`)
      } else {
        whereConditions.push(`(c.instagram IS NULL OR c.instagram = '')`)
      }
    }

    if (hasYoutube !== null) {
      if (hasYoutube) {
        whereConditions.push(`c.youtube IS NOT NULL AND c.youtube != ''`)
      } else {
        whereConditions.push(`(c.youtube IS NULL OR c.youtube = '')`)
      }
    }

    // Location V2 Filters
    if (headquartersAddress?.length) {
      const addressConditions = headquartersAddress.map((_, index) => `c.company_address ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${addressConditions.join(' OR ')})`)
      headquartersAddress.forEach(addr => {
        queryParams.push(`%${addr}%`)
        paramIndex++
      })
    }

    if (headquartersCity?.length) {
      const cityConditions = headquartersCity.map((_, index) => `c.company_city ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${cityConditions.join(' OR ')})`)
      headquartersCity.forEach(city => {
        queryParams.push(`%${city}%`)
        paramIndex++
      })
    }

    if (headquartersState?.length) {
      const stateConditions = headquartersState.map((_, index) => `c.company_state ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${stateConditions.join(' OR ')})`)
      headquartersState.forEach(state => {
        queryParams.push(`%${state}%`)
        paramIndex++
      })
    }

    if (headquartersCountry?.length) {
      const countryConditions = headquartersCountry.map((_, index) => `c.company_country ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${countryConditions.join(' OR ')})`)
      headquartersCountry.forEach(country => {
        queryParams.push(`%${country}%`)
        paramIndex++
      })
    }

    if (officeLocations?.length) {
      const locationConditions = officeLocations.map((_, index) => `c.office_locations && $${paramIndex + index}::text[]`)
      whereConditions.push(`(${locationConditions.join(' OR ')})`)
      officeLocations.forEach(location => {
        queryParams.push([location])
        paramIndex++
      })
    }

    // Financial Metrics V2 Filters
    if (fundSizeMin !== null) {
      whereConditions.push(`c.fund_size >= $${paramIndex}`)
      queryParams.push(fundSizeMin)
      paramIndex++
    }

    if (fundSizeMax !== null) {
      whereConditions.push(`c.fund_size <= $${paramIndex}`)
      queryParams.push(fundSizeMax)
      paramIndex++
    }

    if (aumMin !== null) {
      whereConditions.push(`c.aum >= $${paramIndex}`)
      queryParams.push(aumMin)
      paramIndex++
    }

    if (aumMax !== null) {
      whereConditions.push(`c.aum <= $${paramIndex}`)
      queryParams.push(aumMax)
      paramIndex++
    }

    if (numberOfPropertiesMin !== null) {
      whereConditions.push(`c.number_of_properties >= $${paramIndex}`)
      queryParams.push(numberOfPropertiesMin)
      paramIndex++
    }

    if (numberOfPropertiesMax !== null) {
      whereConditions.push(`c.number_of_properties <= $${paramIndex}`)
      queryParams.push(numberOfPropertiesMax)
      paramIndex++
    }

    if (numberOfOfficesMin !== null) {
      whereConditions.push(`c.number_of_offices >= $${paramIndex}`)
      queryParams.push(numberOfOfficesMin)
      paramIndex++
    }

    if (numberOfOfficesMax !== null) {
      whereConditions.push(`c.number_of_offices <= $${paramIndex}`)
      queryParams.push(numberOfOfficesMax)
      paramIndex++
    }

    if (numberOfEmployeesMin !== null) {
      whereConditions.push(`c.number_of_employees >= $${paramIndex}`)
      queryParams.push(numberOfEmployeesMin)
      paramIndex++
    }

    if (numberOfEmployeesMax !== null) {
      whereConditions.push(`c.number_of_employees <= $${paramIndex}`)
      queryParams.push(numberOfEmployeesMax)
      paramIndex++
    }

    if (annualRevenueMin !== null) {
      whereConditions.push(`c.annual_revenue >= $${paramIndex}`)
      queryParams.push(annualRevenueMin)
      paramIndex++
    }

    if (annualRevenueMax !== null) {
      whereConditions.push(`c.annual_revenue <= $${paramIndex}`)
      queryParams.push(annualRevenueMax)
      paramIndex++
    }

    // Financial Information V2 Filters
    if (balanceSheetStrength?.length) {
      const strengthConditions = balanceSheetStrength.map((_, index) => `c.balance_sheet_strength ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${strengthConditions.join(' OR ')})`)
      balanceSheetStrength.forEach(strength => {
        queryParams.push(`%${strength}%`)
        paramIndex++
      })
    }

    if (fundingSources?.length) {
      const sourceConditions = fundingSources.map((_, index) => `c.funding_sources && $${paramIndex + index}::text[]`)
      whereConditions.push(`(${sourceConditions.join(' OR ')})`)
      fundingSources.forEach(source => {
        queryParams.push([source])
        paramIndex++
      })
    }

    if (creditRating?.length) {
      const ratingConditions = creditRating.map((_, index) => `c.credit_rating ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${ratingConditions.join(' OR ')})`)
      creditRating.forEach(rating => {
        queryParams.push(`%${rating}%`)
        paramIndex++
      })
    }

    if (dryPowderMin !== null) {
      whereConditions.push(`c.dry_powder >= $${paramIndex}`)
      queryParams.push(dryPowderMin)
      paramIndex++
    }

    if (dryPowderMax !== null) {
      whereConditions.push(`c.dry_powder <= $${paramIndex}`)
      queryParams.push(dryPowderMax)
      paramIndex++
    }

    // Investment & Fund Information V2 Filters
    if (investmentVehicleType?.length) {
      const vehicleConditions = investmentVehicleType.map((_, index) => `c.investment_vehicle_type ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${vehicleConditions.join(' OR ')})`)
      investmentVehicleType.forEach(vehicle => {
        queryParams.push(`%${vehicle}%`)
        paramIndex++
      })
    }

    if (fundraisingStatus?.length) {
      const statusConditions = fundraisingStatus.map((_, index) => `c.fundraising_status ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${statusConditions.join(' OR ')})`)
      fundraisingStatus.forEach(status => {
        queryParams.push(`%${status}%`)
        paramIndex++
      })
    }

    if (lenderType?.length) {
      const typeConditions = lenderType.map((_, index) => `c.lender_type ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${typeConditions.join(' OR ')})`)
      lenderType.forEach(type => {
        queryParams.push(`%${type}%`)
        paramIndex++
      })
    }

    if (annualLoanVolumeMin !== null) {
      whereConditions.push(`c.annual_loan_volume >= $${paramIndex}`)
      queryParams.push(annualLoanVolumeMin)
      paramIndex++
    }

    if (annualLoanVolumeMax !== null) {
      whereConditions.push(`c.annual_loan_volume <= $${paramIndex}`)
      queryParams.push(annualLoanVolumeMax)
      paramIndex++
    }

    if (portfolioHealth?.length) {
      const healthConditions = portfolioHealth.map((_, index) => `c.portfolio_health ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${healthConditions.join(' OR ')})`)
      portfolioHealth.forEach(health => {
        queryParams.push(`%${health}%`)
        paramIndex++
      })
    }

    // Partnership & Leadership V2 Filters
    if (partnerships?.length) {
      const partnershipConditions = partnerships.map((_, index) => `c.partnerships && $${paramIndex + index}::text[]`)
      whereConditions.push(`(${partnershipConditions.join(' OR ')})`)
      partnerships.forEach(partnership => {
        queryParams.push([partnership])
        paramIndex++
      })
    }

    if (keyEquityPartners?.length) {
      const partnerConditions = keyEquityPartners.map((_, index) => `c.key_equity_partners && $${paramIndex + index}::text[]`)
      whereConditions.push(`(${partnerConditions.join(' OR ')})`)
      keyEquityPartners.forEach(partner => {
        queryParams.push([partner])
        paramIndex++
      })
    }

    if (keyDebtPartners?.length) {
      const partnerConditions = keyDebtPartners.map((_, index) => `c.key_debt_partners && $${paramIndex + index}::text[]`)
      whereConditions.push(`(${partnerConditions.join(' OR ')})`)
      keyDebtPartners.forEach(partner => {
        queryParams.push([partner])
        paramIndex++
      })
    }

    if (keyExecutives?.length) {
      const executiveConditions = keyExecutives.map((_, index) => `c.key_executives && $${paramIndex + index}::text[]`)
      whereConditions.push(`(${executiveConditions.join(' OR ')})`)
      keyExecutives.forEach(executive => {
        queryParams.push([executive])
        paramIndex++
      })
    }

    // Market Positioning & Strategy V2 Filters


    if (sustainabilityEsgFocus !== null) {
      whereConditions.push(`c.sustainability_esg_focus = $${paramIndex}`)
      queryParams.push(sustainabilityEsgFocus)
      paramIndex++
    }

    if (technologyProptechAdoption !== null) {
      whereConditions.push(`c.technology_proptech_adoption = $${paramIndex}`)
      queryParams.push(technologyProptechAdoption)
      paramIndex++
    }

    if (adaptiveReuseExperience !== null) {
      whereConditions.push(`c.adaptive_reuse_experience = $${paramIndex}`)
      queryParams.push(adaptiveReuseExperience)
      paramIndex++
    }

    if (regulatoryZoningExpertise !== null) {
      whereConditions.push(`c.regulatory_zoning_expertise = $${paramIndex}`)
      queryParams.push(regulatoryZoningExpertise)
      paramIndex++
    }

    // Corporate Structure V2 Filters
    if (corporateStructure?.length) {
      const structureConditions = corporateStructure.map((_, index) => `c.corporate_structure ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${structureConditions.join(' OR ')})`)
      corporateStructure.forEach(structure => {
        queryParams.push(`%${structure}%`)
        paramIndex++
      })
    }

    if (parentCompany?.length) {
      const companyConditions = parentCompany.map((_, index) => `c.parent_company ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${companyConditions.join(' OR ')})`)
      parentCompany.forEach(company => {
        queryParams.push(`%${company}%`)
        paramIndex++
      })
    }

    if (stockTickerSymbol?.length) {
      const symbolConditions = stockTickerSymbol.map((_, index) => `c.stock_ticker_symbol ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${symbolConditions.join(' OR ')})`)
      stockTickerSymbol.forEach(symbol => {
        queryParams.push(`%${symbol}%`)
        paramIndex++
      })
    }

    if (stockExchange?.length) {
      const exchangeConditions = stockExchange.map((_, index) => `c.stock_exchange ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${exchangeConditions.join(' OR ')})`)
      stockExchange.forEach(exchange => {
        queryParams.push(`%${exchange}%`)
        paramIndex++
      })
    }

    // Contact Processor Flags
    if (hasContacts !== null) {
      if (hasContacts) {
        whereConditions.push(`EXISTS (SELECT 1 FROM contacts WHERE company_id = c.company_id)`)
      } else {
        whereConditions.push(`NOT EXISTS (SELECT 1 FROM contacts WHERE company_id = c.company_id)`)
      }
    }

    if (contactsEmailVerificationStatus?.length) {
      const statusConditions = contactsEmailVerificationStatus.map((_, index) => `cp.contact_email_verification_status = $${paramIndex + index}`)
      whereConditions.push(`(${statusConditions.join(' OR ')})`)
      contactsEmailVerificationStatus.forEach(status => {
        queryParams.push(status)
        paramIndex++
      })
    }

    if (contactsEnrichmentStatus?.length) {
      const statusConditions = contactsEnrichmentStatus.map((_, index) => `cp.contact_enrichment_status = $${paramIndex + index}`)
      whereConditions.push(`(${statusConditions.join(' OR ')})`)
      contactsEnrichmentStatus.forEach(status => {
        queryParams.push(status)
        paramIndex++
      })
    }

    if (contactsEnrichmentV2Status?.length) {
      const statusConditions = contactsEnrichmentV2Status.map((_, index) => `cp.contact_enrichment_v2_status = $${paramIndex + index}`)
      whereConditions.push(`(${statusConditions.join(' OR ')})`)
      contactsEnrichmentV2Status.forEach(status => {
        queryParams.push(status)
        paramIndex++
      })
    }

    if (contactsEmailGenerationStatus?.length) {
      const statusConditions = contactsEmailGenerationStatus.map((_, index) => `cp.contact_email_generation_status = $${paramIndex + index}`)
      whereConditions.push(`(${statusConditions.join(' OR ')})`)
      contactsEmailGenerationStatus.forEach(status => {
        queryParams.push(status)
        paramIndex++
      })
    }

    if (contactsEmailSendingStatus?.length) {
      const statusConditions = contactsEmailSendingStatus.map((_, index) => `cp.contact_email_sending_status = $${paramIndex + index}`)
      whereConditions.push(`(${statusConditions.join(' OR ')})`)
      contactsEmailSendingStatus.forEach(status => {
        queryParams.push(status)
        paramIndex++
      })
    }

    if (entityId) {
      whereConditions.push(`ic.entity_id = $${paramIndex}::bigint`)
      queryParams.push(entityId)
      paramIndex++
    }

    if (entityName) {
      whereConditions.push(`ic.entity_name ILIKE $${paramIndex}`)
      queryParams.push(`%${entityName}%`)
      paramIndex++
    }

    if (criteriaId) {
      whereConditions.push(`ic.id = $${paramIndex}::bigint`)
      queryParams.push(criteriaId) // Keep as string, let PostgreSQL handle the conversion
      paramIndex++
    }

    // Capital & Financing filters
    if (capitalPosition?.length) {
      whereConditions.push(`ic.capital_position = ANY($${paramIndex}::text[])`)
      queryParams.push(capitalPosition)
      paramIndex++
    }

    if (loanTypes?.length) {
      whereConditions.push(`icd.loan_type = ANY($${paramIndex}::text[])`)
      queryParams.push(loanTypes)
      paramIndex++
    }

    if (loanProgram?.length) {
      whereConditions.push(`icd.loan_program = ANY($${paramIndex}::text[])`)
      queryParams.push(loanProgram)
      paramIndex++
    }

    if (structuredLoanTranche?.length) {
      whereConditions.push(`icd.structured_loan_tranche = ANY($${paramIndex}::text[])`)
      queryParams.push(structuredLoanTranche)
      paramIndex++
    }

    if (recourseLoan?.length) {
      whereConditions.push(`icd.recourse_loan = ANY($${paramIndex}::text[])`)
      queryParams.push(recourseLoan)
      paramIndex++
    }

    // Deal size filters
    if (dealSizeMin !== null) {
      whereConditions.push(`ic.minimum_deal_size >= $${paramIndex}`)
      queryParams.push(dealSizeMin)
      paramIndex++
    }

    if (dealSizeMax !== null) {
      whereConditions.push(`ic.maximum_deal_size <= $${paramIndex}`)
      queryParams.push(dealSizeMax)
      paramIndex++
    }

    // Return filters (equity table)
    if (targetReturnMin !== null) {
      whereConditions.push(`ice.target_return >= $${paramIndex}`)
      queryParams.push(targetReturnMin)
      paramIndex++
    }

    if (targetReturnMax !== null) {
      whereConditions.push(`ice.target_return <= $${paramIndex}`)
      queryParams.push(targetReturnMax)
      paramIndex++
    }

    if (historicalIrrMin !== null) {
      whereConditions.push(`ice.minimum_internal_rate_of_return >= $${paramIndex}`)
      queryParams.push(historicalIrrMin)
      paramIndex++
    }

    if (historicalIrrMax !== null) {
      whereConditions.push(`ice.minimum_internal_rate_of_return <= $${paramIndex}`)
      queryParams.push(historicalIrrMax)
      paramIndex++
    }

    if (historicalEmMin !== null) {
      whereConditions.push(`ice.minimum_equity_multiple >= $${paramIndex}`)
      queryParams.push(historicalEmMin)
      paramIndex++
    }

    if (historicalEmMax !== null) {
      whereConditions.push(`ice.minimum_equity_multiple <= $${paramIndex}`)
      queryParams.push(historicalEmMax)
      paramIndex++
    }

    // Hold period filters (equity table)
    if (minHoldPeriod !== null) {
      whereConditions.push(`ice.min_hold_period_years >= $${paramIndex}`)
      queryParams.push(minHoldPeriod)
      paramIndex++
    }

    if (maxHoldPeriod !== null) {
      whereConditions.push(`ice.max_hold_period_years <= $${paramIndex}`)
      queryParams.push(maxHoldPeriod)
      paramIndex++
    }

    // Loan term filters (debt table)
    if (minLoanTerm !== null) {
      whereConditions.push(`icd.min_loan_term >= $${paramIndex}`)
      queryParams.push(minLoanTerm)
      paramIndex++
    }

    if (maxLoanTerm !== null) {
      whereConditions.push(`icd.max_loan_term <= $${paramIndex}`)
      queryParams.push(maxLoanTerm)
      paramIndex++
    }

    // Interest rate filters (debt table)
    if (interestRateMin !== null) {
      whereConditions.push(`icd.loan_interest_rate >= $${paramIndex}`)
      queryParams.push(interestRateMin)
      paramIndex++
    }

    if (interestRateMax !== null) {
      whereConditions.push(`icd.loan_interest_rate <= $${paramIndex}`)
      queryParams.push(interestRateMax)
      paramIndex++
    }

    // LTV filters (debt table)
    if (loanToValueMin !== null) {
      whereConditions.push(`icd.loan_to_value_min >= $${paramIndex}`)
      queryParams.push(loanToValueMin)
      paramIndex++
    }

    if (loanToValueMax !== null) {
      whereConditions.push(`icd.loan_to_value_max <= $${paramIndex}`)
      queryParams.push(loanToValueMax)
      paramIndex++
    }

    // LTC filters (debt table)
    if (loanToCostMin !== null) {
      whereConditions.push(`icd.loan_to_cost_min >= $${paramIndex}`)
      queryParams.push(loanToCostMin)
      paramIndex++
    }

    if (loanToCostMax !== null) {
      whereConditions.push(`icd.loan_to_cost_max <= $${paramIndex}`)
      queryParams.push(loanToCostMax)
      paramIndex++
    }

    // DSCR filters (debt table)
    if (minLoanDscr !== null) {
      whereConditions.push(`icd.min_loan_dscr >= $${paramIndex}`)
      queryParams.push(minLoanDscr)
      paramIndex++
    }

    if (maxLoanDscr !== null) {
      whereConditions.push(`icd.max_loan_dscr <= $${paramIndex}`)
      queryParams.push(maxLoanDscr)
      paramIndex++
    }

    // === NEW ENHANCED DEBT & EQUITY FILTERS ===
    // Enhanced Debt Fields
    if (eligibleBorrower?.length) {
      whereConditions.push(`icd.eligible_borrower = ANY($${paramIndex}::text[])`)
      queryParams.push(eligibleBorrower)
      paramIndex++
    }

    if (lienPosition?.length) {
      whereConditions.push(`icd.lien_position = ANY($${paramIndex}::text[])`)
      queryParams.push(lienPosition)
      paramIndex++
    }

    if (rateLock?.length) {
      whereConditions.push(`icd.rate_lock = ANY($${paramIndex}::text[])`)
      queryParams.push(rateLock)
      paramIndex++
    }

    if (rateType?.length) {
      whereConditions.push(`icd.rate_type = ANY($${paramIndex}::text[])`)
      queryParams.push(rateType)
      paramIndex++
    }

    if (amortization?.length) {
      whereConditions.push(`icd.amortization = ANY($${paramIndex}::text[])`)
      queryParams.push(amortization)
      paramIndex++
    }

    if (loanTypeNormalized?.length) {
      whereConditions.push(`icd.loan_type_normalized = ANY($${paramIndex}::text[])`)
      queryParams.push(loanTypeNormalized)
      paramIndex++
    }

    // Enhanced Equity Fields
    if (ownershipRequirement?.length) {
      whereConditions.push(`ice.ownership_requirement = ANY($${paramIndex}::text[])`)
      queryParams.push(ownershipRequirement)
      paramIndex++
    }

    // Additional Debt Fields from CSV
    if (loanMinDebtYield?.length) {
      whereConditions.push(`icd.loan_min_debt_yield = ANY($${paramIndex}::text[])`)
      queryParams.push(loanMinDebtYield)
      paramIndex++
    }

    if (closingTimeMin !== null) {
      whereConditions.push(`icd.closing_time >= $${paramIndex}`)
      queryParams.push(closingTimeMin)
      paramIndex++
    }

    if (closingTimeMax !== null) {
      whereConditions.push(`icd.closing_time <= $${paramIndex}`)
      queryParams.push(closingTimeMax)
      paramIndex++
    }

    // Additional Debt Fields from CSV - Missing fields
    if (futureFacilities?.length) {
      whereConditions.push(`icd.future_facilities = ANY($${paramIndex}::text[])`)
      queryParams.push(futureFacilities)
      paramIndex++
    }

    if (occupancyRequirements?.length) {
      whereConditions.push(`icd.occupancy_requirements = ANY($${paramIndex}::text[])`)
      queryParams.push(occupancyRequirements)
      paramIndex++
    }

    if (prepayment?.length) {
      whereConditions.push(`icd.prepayment = ANY($${paramIndex}::text[])`)
      queryParams.push(prepayment)
      paramIndex++
    }

    if (yieldMaintenance?.length) {
      whereConditions.push(`icd.yield_maintenance = ANY($${paramIndex}::text[])`)
      queryParams.push(yieldMaintenance)
      paramIndex++
    }

    // Loan Fee Filters
    if (loanOriginationMaxFeeMin !== null) {
      whereConditions.push(`icd.loan_origination_max_fee >= $${paramIndex}`)
      queryParams.push(loanOriginationMaxFeeMin)
      paramIndex++
    }

    if (loanOriginationMaxFeeMax !== null) {
      whereConditions.push(`icd.loan_origination_max_fee <= $${paramIndex}`)
      queryParams.push(loanOriginationMaxFeeMax)
      paramIndex++
    }

    if (loanOriginationMinFeeMin !== null) {
      whereConditions.push(`icd.loan_origination_min_fee >= $${paramIndex}`)
      queryParams.push(loanOriginationMinFeeMin)
      paramIndex++
    }

    if (loanOriginationMinFeeMax !== null) {
      whereConditions.push(`icd.loan_origination_min_fee <= $${paramIndex}`)
      queryParams.push(loanOriginationMinFeeMax)
      paramIndex++
    }

    if (loanExitMinFeeMin !== null) {
      whereConditions.push(`icd.loan_exit_min_fee >= $${paramIndex}`)
      queryParams.push(loanExitMinFeeMin)
      paramIndex++
    }

    if (loanExitMinFeeMax !== null) {
      whereConditions.push(`icd.loan_exit_min_fee <= $${paramIndex}`)
      queryParams.push(loanExitMinFeeMax)
      paramIndex++
    }

    if (loanExitMaxFeeMin !== null) {
      whereConditions.push(`icd.loan_exit_max_fee >= $${paramIndex}`)
      queryParams.push(loanExitMaxFeeMin)
      paramIndex++
    }

    if (loanExitMaxFeeMax !== null) {
      whereConditions.push(`icd.loan_exit_max_fee <= $${paramIndex}`)
      queryParams.push(loanExitMaxFeeMax)
      paramIndex++
    }

    // Loan Interest Rate Filters
    if (loanInterestRateSofrMin !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_sofr >= $${paramIndex}`)
      queryParams.push(loanInterestRateSofrMin)
      paramIndex++
    }

    if (loanInterestRateSofrMax !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_sofr <= $${paramIndex}`)
      queryParams.push(loanInterestRateSofrMax)
      paramIndex++
    }

    if (loanInterestRateWsjMin !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_wsj >= $${paramIndex}`)
      queryParams.push(loanInterestRateWsjMin)
      paramIndex++
    }

    if (loanInterestRateWsjMax !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_wsj <= $${paramIndex}`)
      queryParams.push(loanInterestRateWsjMax)
      paramIndex++
    }

    if (loanInterestRatePrimeMin !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_prime >= $${paramIndex}`)
      queryParams.push(loanInterestRatePrimeMin)
      paramIndex++
    }

    if (loanInterestRatePrimeMax !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_prime <= $${paramIndex}`)
      queryParams.push(loanInterestRatePrimeMax)
      paramIndex++
    }

    if (loanInterestRate3ytMin !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_3yt >= $${paramIndex}`)
      queryParams.push(loanInterestRate3ytMin)
      paramIndex++
    }

    if (loanInterestRate3ytMax !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_3yt <= $${paramIndex}`)
      queryParams.push(loanInterestRate3ytMax)
      paramIndex++
    }

    if (loanInterestRate5ytMin !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_5yt >= $${paramIndex}`)
      queryParams.push(loanInterestRate5ytMin)
      paramIndex++
    }

    if (loanInterestRate5ytMax !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_5yt <= $${paramIndex}`)
      queryParams.push(loanInterestRate5ytMax)
      paramIndex++
    }

    if (loanInterestRate10ytMin !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_10yt >= $${paramIndex}`)
      queryParams.push(loanInterestRate10ytMin)
      paramIndex++
    }

    if (loanInterestRate10ytMax !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_10yt <= $${paramIndex}`)
      queryParams.push(loanInterestRate10ytMax)
      paramIndex++
    }

    if (loanInterestRate30ytMin !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_30yt >= $${paramIndex}`)
      queryParams.push(loanInterestRate30ytMin)
      paramIndex++
    }

    if (loanInterestRate30ytMax !== null) {
      whereConditions.push(`icd.loan_interest_rate_based_off_30yt <= $${paramIndex}`)
      queryParams.push(loanInterestRate30ytMax)
      paramIndex++
    }

    // Loan Sizing Filters
    if (loanToValueMinMin !== null) {
      whereConditions.push(`icd.loan_to_value_min >= $${paramIndex}`)
      queryParams.push(loanToValueMinMin)
      paramIndex++
    }

    if (loanToValueMinMax !== null) {
      whereConditions.push(`icd.loan_to_value_min <= $${paramIndex}`)
      queryParams.push(loanToValueMinMax)
      paramIndex++
    }

    if (loanToValueMaxMin !== null) {
      whereConditions.push(`icd.loan_to_value_max >= $${paramIndex}`)
      queryParams.push(loanToValueMaxMin)
      paramIndex++
    }

    if (loanToValueMaxMax !== null) {
      whereConditions.push(`icd.loan_to_value_max <= $${paramIndex}`)
      queryParams.push(loanToValueMaxMax)
      paramIndex++
    }

    if (loanToCostMinMin !== null) {
      whereConditions.push(`icd.loan_to_cost_min >= $${paramIndex}`)
      queryParams.push(loanToCostMinMin)
      paramIndex++
    }

    if (loanToCostMinMax !== null) {
      whereConditions.push(`icd.loan_to_cost_min <= $${paramIndex}`)
      queryParams.push(loanToCostMinMax)
      paramIndex++
    }

    if (loanToCostMaxMin !== null) {
      whereConditions.push(`icd.loan_to_cost_max >= $${paramIndex}`)
      queryParams.push(loanToCostMaxMin)
      paramIndex++
    }

    if (loanToCostMaxMax !== null) {
      whereConditions.push(`icd.loan_to_cost_max <= $${paramIndex}`)
      queryParams.push(loanToCostMaxMax)
      paramIndex++
    }

    // Loan Term Filters
    if (minLoanTermMin !== null) {
      whereConditions.push(`icd.min_loan_term >= $${paramIndex}`)
      queryParams.push(minLoanTermMin)
      paramIndex++
    }

    if (minLoanTermMax !== null) {
      whereConditions.push(`icd.min_loan_term <= $${paramIndex}`)
      queryParams.push(minLoanTermMax)
      paramIndex++
    }

    if (maxLoanTermMin !== null) {
      whereConditions.push(`icd.max_loan_term >= $${paramIndex}`)
      queryParams.push(maxLoanTermMin)
      paramIndex++
    }

    if (maxLoanTermMax !== null) {
      whereConditions.push(`icd.max_loan_term <= $${paramIndex}`)
      queryParams.push(maxLoanTermMax)
      paramIndex++
    }

    // Additional Equity Fields
    if (minimumYieldOnCostMin !== null) {
      whereConditions.push(`ice.minimum_yield_on_cost >= $${paramIndex}`)
      queryParams.push(minimumYieldOnCostMin)
      paramIndex++
    }

    if (minimumYieldOnCostMax !== null) {
      whereConditions.push(`ice.minimum_yield_on_cost <= $${paramIndex}`)
      queryParams.push(minimumYieldOnCostMax)
      paramIndex++
    }

    if (maxLeverageToleranceMin !== null) {
      whereConditions.push(`ice.max_leverage_tolerance >= $${paramIndex}`)
      queryParams.push(maxLeverageToleranceMin)
      paramIndex++
    }

    if (maxLeverageToleranceMax !== null) {
      whereConditions.push(`ice.max_leverage_tolerance <= $${paramIndex}`)
      queryParams.push(maxLeverageToleranceMax)
      paramIndex++
    }

    // Property type filters
    if (propertyTypes?.length) {
      whereConditions.push(`ic.property_types && $${paramIndex}::text[]`)
      queryParams.push(propertyTypes)
      paramIndex++
    }

    if (propertySubcategories?.length) {
      whereConditions.push(`ic.property_sub_categories && $${paramIndex}::text[]`)
      queryParams.push(propertySubcategories)
      paramIndex++
    }

    if (strategies?.length) {
      whereConditions.push(`ic.strategies && $${paramIndex}::text[]`)
      queryParams.push(strategies)
      paramIndex++
    }

    if (financialProducts?.length) {
      whereConditions.push(`ic.financial_products && $${paramIndex}::jsonb`)
      queryParams.push(financialProducts)
      paramIndex++
    }

    // Geographic filters
    if (regions?.length) {
      whereConditions.push(`ic.region && $${paramIndex}::text[]`)
      queryParams.push(regions)
      paramIndex++
    }

    if (states?.length) {
      whereConditions.push(`ic.state && $${paramIndex}::text[]`)
      queryParams.push(states)
      paramIndex++
    }

    if (cities?.length) {
      whereConditions.push(`ic.city && $${paramIndex}::text[]`)
      queryParams.push(cities)
      paramIndex++
    }

    if (countries?.length) {
      whereConditions.push(`ic.country && $${paramIndex}::text[]`)
      queryParams.push(countries)
      paramIndex++
    }

    // Company extracted data filters
    if (companyType?.length) {
      const companyTypeConditions = companyType.map((_, index) => `c.company_type ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${companyTypeConditions.join(' OR ')})`)
      companyType.forEach(type => {
        queryParams.push(`%${type}%`)
        paramIndex++
      })
    }


    if (companyIndustry?.length) {
      const industryConditions = companyIndustry.map((_, index) => `c.industry ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${industryConditions.join(' OR ')})`)
      companyIndustry.forEach(industry => {
        queryParams.push(`%${industry}%`)
        paramIndex++
      })
    }

    if (industry?.length) {
      const industryConditions = industry.map((_, index) => `c.industry ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${industryConditions.join(' OR ')})`)
      industry.forEach(ind => {
        queryParams.push(`%${ind}%`)
        paramIndex++
      })
    }

    if (companyCountry?.length) {
      const countryConditions = companyCountry.map((_, index) => `c.company_country ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${countryConditions.join(' OR ')})`)
      companyCountry.forEach(country => {
        queryParams.push(`%${country}%`)
        paramIndex++
      })
    }

    if (headquarters?.length) {
      const hqConditions = headquarters.map((_, index) => `c.company_address ILIKE $${paramIndex + index}`)
      whereConditions.push(`(${hqConditions.join(' OR ')})`)
      headquarters.forEach(hq => {
        queryParams.push(`%${hq}%`)
        paramIndex++
      })
    }

    // Company Financial Range Filters (V2 - numeric fields from companies table)
    if (fundSizeMin !== null) {
      whereConditions.push(`c.fund_size >= $${paramIndex}`)
      queryParams.push(fundSizeMin)
      paramIndex++
    }

    if (fundSizeMax !== null) {
      whereConditions.push(`c.fund_size <= $${paramIndex}`)
      queryParams.push(fundSizeMax)
      paramIndex++
    }

    if (aumMin !== null) {
      whereConditions.push(`c.aum >= $${paramIndex}`)
      queryParams.push(aumMin)
      paramIndex++
    }

    if (aumMax !== null) {
      whereConditions.push(`c.aum <= $${paramIndex}`)
      queryParams.push(aumMax)
      paramIndex++
    }

    // Note: portfoliovalue and historicalreturns are not in V2 companies table
    // These filters are removed in V2 as they're not populated by CompanyOverviewProcessorV2

    // Company Scale Range Filters
    if (foundedYearMin !== null) {
      whereConditions.push(`c.founded_year >= $${paramIndex}`)
      queryParams.push(foundedYearMin)
      paramIndex++
    }

    if (foundedYearMax !== null) {
      whereConditions.push(`c.founded_year <= $${paramIndex}`)
      queryParams.push(foundedYearMax)
      paramIndex++
    }

    if (numberOfPropertiesMin !== null) {
      whereConditions.push(`c.number_of_properties >= $${paramIndex}`)
      queryParams.push(numberOfPropertiesMin)
      paramIndex++
    }

    if (numberOfPropertiesMax !== null) {
      whereConditions.push(`c.number_of_properties <= $${paramIndex}`)
      queryParams.push(numberOfPropertiesMax)
      paramIndex++
    }

    if (numberOfOfficesMin !== null) {
      whereConditions.push(`c.number_of_offices >= $${paramIndex}`)
      queryParams.push(numberOfOfficesMin)
      paramIndex++
    }

    if (numberOfOfficesMax !== null) {
      whereConditions.push(`c.number_of_offices <= $${paramIndex}`)
      queryParams.push(numberOfOfficesMax)
      paramIndex++
    }

    // V2 - number_of_employees is already numeric in companies table
    if (numberOfEmployeesMin !== null) {
      whereConditions.push(`c.number_of_employees >= $${paramIndex}`)
      queryParams.push(numberOfEmployeesMin)
      paramIndex++
    }

    if (numberOfEmployeesMax !== null) {
      whereConditions.push(`c.number_of_employees <= $${paramIndex}`)
      queryParams.push(numberOfEmployeesMax)
      paramIndex++
    }

    // Note: totalTransactions, totalSquareFeet, totalUnits are not in V2 companies table
    // These filters are removed in V2 - replaced by portfolio_size_sqft, portfolio_asset_count, etc.

    // V2 Portfolio filters (using new V2 columns)
    if (totalTransactionsMin !== null) {
      whereConditions.push(`c.transactions_completed_last_12m >= $${paramIndex}`)
      queryParams.push(totalTransactionsMin)
      paramIndex++
    }

    if (totalTransactionsMax !== null) {
      whereConditions.push(`c.transactions_completed_last_12m <= $${paramIndex}`)
      queryParams.push(totalTransactionsMax)
      paramIndex++
    }

    if (totalSquareFeetMin !== null) {
      whereConditions.push(`c.portfolio_size_sqft >= $${paramIndex}`)
      queryParams.push(totalSquareFeetMin)
      paramIndex++
    }

    if (totalSquareFeetMax !== null) {
      whereConditions.push(`c.portfolio_size_sqft <= $${paramIndex}`)
      queryParams.push(totalSquareFeetMax)
      paramIndex++
    }

    if (totalUnitsMin !== null) {
      whereConditions.push(`c.portfolio_asset_count >= $${paramIndex}`)
      queryParams.push(totalUnitsMin)
      paramIndex++
    }

    if (totalUnitsMax !== null) {
      whereConditions.push(`c.portfolio_asset_count <= $${paramIndex}`)
      queryParams.push(totalUnitsMax)
      paramIndex++
    }

    // Status filters
    if (isActive !== null) {
      whereConditions.push(`ic.is_active = $${paramIndex}`)
      queryParams.push(isActive)
      paramIndex++
    }

    // Processing status filters (V2 Enhanced)
    if (websiteScrapingStatus?.length) {
      whereConditions.push(`c.website_scraping_status = ANY($${paramIndex}::text[])`)
      queryParams.push(websiteScrapingStatus)
      paramIndex++
    }

    if (companyOverviewStatus?.length) {
      whereConditions.push(`c.overview_status = ANY($${paramIndex}::text[])`)
      queryParams.push(companyOverviewStatus)
      paramIndex++
    }

    if (overviewV2Status?.length) {
      whereConditions.push(`c.overview_v2_status = ANY($${paramIndex}::text[])`)
      queryParams.push(overviewV2Status)
      paramIndex++
    }

    if (investmentCriteriaStatus?.length) {
      whereConditions.push(`c.investment_criteria_status = ANY($${paramIndex}::text[])`)
      queryParams.push(investmentCriteriaStatus)
      paramIndex++
    }

    // Contact Processor Filters
    if (hasContacts !== null) {
      if (hasContacts) {
        whereConditions.push(`EXISTS (SELECT 1 FROM contacts WHERE company_id = c.company_id)`)
      } else {
        whereConditions.push(`NOT EXISTS (SELECT 1 FROM contacts WHERE company_id = c.company_id)`)
      }
    }

    // Only add contact processor status filters if we have the cp table joined
    if (hasContactProcessorFilters) {
      if (contactsEmailVerificationStatus?.length) {
        whereConditions.push(`cp.contact_email_verification_status = ANY($${paramIndex}::text[])`)
        queryParams.push(contactsEmailVerificationStatus)
        paramIndex++
      }

      if (contactsEnrichmentStatus?.length) {
        whereConditions.push(`cp.contact_enrichment_status = ANY($${paramIndex}::text[])`)
        queryParams.push(contactsEnrichmentStatus)
        paramIndex++
      }

      if (contactsEnrichmentV2Status?.length) {
        whereConditions.push(`cp.contact_enrichment_v2_status = ANY($${paramIndex}::text[])`)
        queryParams.push(contactsEnrichmentV2Status)
        paramIndex++
      }

      if (contactsEmailGenerationStatus?.length) {
        whereConditions.push(`cp.contact_email_generation_status = ANY($${paramIndex}::text[])`)
        queryParams.push(contactsEmailGenerationStatus)
        paramIndex++
      }

      if (contactsEmailSendingStatus?.length) {
        whereConditions.push(`cp.contact_email_sending_status = ANY($${paramIndex}::text[])`)
        queryParams.push(contactsEmailSendingStatus)
        paramIndex++
      }

      // Contact processor NOT filters - using cp table
      if (notFilters.notContactsEmailVerificationStatus?.length) {
        whereConditions.push(`(cp.contact_email_verification_status IS NOT NULL AND NOT (cp.contact_email_verification_status = ANY($${paramIndex}::text[])))`)
        queryParams.push(notFilters.notContactsEmailVerificationStatus)
        paramIndex++
      }

      if (notFilters.notContactsEnrichmentStatus?.length) {
        whereConditions.push(`(cp.contact_enrichment_status IS NOT NULL AND NOT (cp.contact_enrichment_status = ANY($${paramIndex}::text[])))`)
        queryParams.push(notFilters.notContactsEnrichmentStatus)
        paramIndex++
      }

      if (notFilters.notContactsEnrichmentV2Status?.length) {
        whereConditions.push(`(cp.contact_enrichment_v2_status IS NOT NULL AND NOT (cp.contact_enrichment_v2_status = ANY($${paramIndex}::text[])))`)
        queryParams.push(notFilters.notContactsEnrichmentV2Status)
        paramIndex++
      }

      if (notFilters.notContactsEmailGenerationStatus?.length) {
        whereConditions.push(`(cp.contact_email_generation_status IS NOT NULL AND NOT (cp.contact_email_generation_status = ANY($${paramIndex}::text[])))`)
        queryParams.push(notFilters.notContactsEmailGenerationStatus)
        paramIndex++
      }

      if (notFilters.notContactsEmailSendingStatus?.length) {
        whereConditions.push(`(cp.contact_email_sending_status IS NOT NULL AND NOT (cp.contact_email_sending_status = ANY($${paramIndex}::text[])))`)
        queryParams.push(notFilters.notContactsEmailSendingStatus)
        paramIndex++
      }
    }

    // === V2 ENHANCED NOT FILTERS ===
    if (notFilters.notCapitalPosition?.length) {
      whereConditions.push(`(ic.capital_position IS NOT NULL AND NOT (ic.capital_position = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notCapitalPosition)
      paramIndex++
    }

    if (notFilters.notPropertyTypes?.length) {
      whereConditions.push(`(ic.property_types IS NOT NULL AND NOT (ic.property_types && $${paramIndex}::text[]))`)
      queryParams.push(notFilters.notPropertyTypes)
      paramIndex++
    }

    if (notFilters.notStrategies?.length) {
      whereConditions.push(`(ic.strategies IS NOT NULL AND NOT (ic.strategies && $${paramIndex}::text[]))`)
      queryParams.push(notFilters.notStrategies)
      paramIndex++
    }

    // Additional investment criteria NOT filters for debt/equity tables
    if (notFilters.notLoanTypes?.length) {
      whereConditions.push(`(icd.loan_type IS NOT NULL AND NOT (icd.loan_type = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notLoanTypes)
      paramIndex++
    }

    if (notFilters.notStructuredLoanTranche?.length) {
      whereConditions.push(`(icd.structured_loan_tranche IS NOT NULL AND NOT (icd.structured_loan_tranche = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notStructuredLoanTranche)
      paramIndex++
    }

    if (notFilters.notLoanProgram?.length) {
      whereConditions.push(`(icd.loan_program IS NOT NULL AND NOT (icd.loan_program = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notLoanProgram)
      paramIndex++
    }

    if (notFilters.notRecourseLoan?.length) {
      whereConditions.push(`(icd.recourse_loan IS NOT NULL AND NOT (icd.recourse_loan = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notRecourseLoan)
      paramIndex++
    }

    if (notFilters.notEligibleBorrower?.length) {
      whereConditions.push(`(icd.eligible_borrower IS NOT NULL AND NOT (icd.eligible_borrower = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notEligibleBorrower)
      paramIndex++
    }

    if (notFilters.notLienPosition?.length) {
      whereConditions.push(`(icd.lien_position IS NOT NULL AND NOT (icd.lien_position = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notLienPosition)
      paramIndex++
    }

    if (notFilters.notOwnershipRequirement?.length) {
      whereConditions.push(`(ice.ownership_requirement IS NOT NULL AND NOT (ice.ownership_requirement = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notOwnershipRequirement)
      paramIndex++
    }

    if (notFilters.notRateType?.length) {
      whereConditions.push(`(icd.rate_type IS NOT NULL AND NOT (icd.rate_type = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notRateType)
      paramIndex++
    }

    if (notFilters.notAmortization?.length) {
      whereConditions.push(`(icd.amortization IS NOT NULL AND NOT (icd.amortization = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notAmortization)
      paramIndex++
    }

    if (notFilters.notCompanyType?.length) {
      const notTypeConditions = notFilters.notCompanyType.map((_, index) => `c.company_type NOT ILIKE $${paramIndex + index}`)
      whereConditions.push(`(c.company_type IS NOT NULL AND (${notTypeConditions.join(' AND ')}))`)
      notFilters.notCompanyType.forEach(type => {
        queryParams.push(`%${type}%`)
        paramIndex++
      })
    }

    if (notFilters.notCompanyIndustry?.length) {
      const notIndustryConditions = notFilters.notCompanyIndustry.map((_, index) => `c.industry NOT ILIKE $${paramIndex + index}`)
      whereConditions.push(`(c.industry IS NOT NULL AND (${notIndustryConditions.join(' AND ')}))`)
      notFilters.notCompanyIndustry.forEach(industry => {
        queryParams.push(`%${industry}%`)
        paramIndex++
      })
    }

    if (notFilters.notWebsiteScrapingStatus?.length) {
      whereConditions.push(`(c.website_scraping_status IS NOT NULL AND NOT (c.website_scraping_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notWebsiteScrapingStatus)
      paramIndex++
    }

    if (notFilters.notCompanyOverviewStatus?.length) {
      whereConditions.push(`(c.overview_status IS NOT NULL AND NOT (c.overview_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notCompanyOverviewStatus)
      paramIndex++
    }

    if (notFilters.notOverviewV2Status?.length) {
      whereConditions.push(`(c.overview_v2_status IS NOT NULL AND NOT (c.overview_v2_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notOverviewV2Status)
      paramIndex++
    }

    if (notFilters.notInvestmentCriteriaStatus?.length) {
      whereConditions.push(`(c.investment_criteria_status IS NOT NULL AND NOT (c.investment_criteria_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notInvestmentCriteriaStatus)
      paramIndex++
    }

    // Contact processor NOT filters - handled through contacts table join
    // Note: These fields are in the contacts table, not the companies table
    // They are handled through the existing contact processor join logic

    // Additional company NOT filters
    if (notFilters.notFundraisingStatus?.length) {
      whereConditions.push(`(c.fundraising_status IS NOT NULL AND NOT (c.fundraising_status = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notFundraisingStatus)
      paramIndex++
    }

    if (notFilters.notLenderType?.length) {
      whereConditions.push(`(c.lender_type IS NOT NULL AND NOT (c.lender_type = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notLenderType)
      paramIndex++
    }

    if (notFilters.notPartnerships?.length) {
      whereConditions.push(`(c.partnerships IS NOT NULL AND NOT (c.partnerships && $${paramIndex}::text[]))`)
      queryParams.push(notFilters.notPartnerships)
      paramIndex++
    }

    if (notFilters.notInvestmentFocus?.length) {
      whereConditions.push(`(c.investment_focus IS NOT NULL AND NOT (c.investment_focus = ANY($${paramIndex}::text[])))`)
      queryParams.push(notFilters.notInvestmentFocus)
      paramIndex++
    }

    // Build conditional JOINs - V2 OPTIMIZED to only join what we need
    let joinClauses = `
      FROM companies c
    `

    // Determine which debt/equity tables we need based on filters
    const hasDebtFilters = !!(
      loanTypes?.length || loanProgram?.length || structuredLoanTranche?.length ||
      recourseLoan?.length || minLoanTerm !== null || maxLoanTerm !== null ||
      interestRateMin !== null || interestRateMax !== null ||
      loanToValueMin !== null || loanToValueMax !== null ||
      loanToCostMin !== null || loanToCostMax !== null ||
      minLoanDscr !== null || maxLoanDscr !== null ||
      eligibleBorrower?.length || lienPosition?.length || rateLock?.length ||
      rateType?.length || amortization?.length || loanTypeNormalized?.length ||
      loanMinDebtYield?.length || closingTimeMin !== null || closingTimeMax !== null ||
      loanOriginationMaxFeeMin !== null || loanOriginationMaxFeeMax !== null ||
      loanOriginationMinFeeMin !== null || loanOriginationMinFeeMax !== null ||
      loanExitMinFeeMin !== null || loanExitMinFeeMax !== null ||
      loanExitMaxFeeMin !== null || loanExitMaxFeeMax !== null ||
      loanInterestRateSofrMin !== null || loanInterestRateSofrMax !== null ||
      loanInterestRateWsjMin !== null || loanInterestRateWsjMax !== null ||
      loanInterestRatePrimeMin !== null || loanInterestRatePrimeMax !== null ||
      loanInterestRate3ytMin !== null || loanInterestRate3ytMax !== null ||
      loanInterestRate5ytMin !== null || loanInterestRate5ytMax !== null ||
      loanInterestRate10ytMin !== null || loanInterestRate10ytMax !== null ||
      loanInterestRate30ytMin !== null || loanInterestRate30ytMax !== null ||
      loanToValueMinMin !== null || loanToValueMinMax !== null ||
      loanToValueMaxMin !== null || loanToValueMaxMax !== null ||
      loanToCostMinMin !== null || loanToCostMinMax !== null ||
      loanToCostMaxMin !== null || loanToCostMaxMax !== null ||
      minLoanTermMin !== null || minLoanTermMax !== null ||
      maxLoanTermMin !== null || maxLoanTermMax !== null ||
      notFilters.notLoanTypes?.length || notFilters.notStructuredLoanTranche?.length ||
      notFilters.notLoanProgram?.length || notFilters.notRecourseLoan?.length ||
      notFilters.notEligibleBorrower?.length || notFilters.notLienPosition?.length ||
      notFilters.notRateType?.length || notFilters.notAmortization?.length
    )

    const hasEquityFilters = !!(
      targetReturnMin !== null || targetReturnMax !== null ||
      historicalIrrMin !== null || historicalIrrMax !== null ||
      historicalEmMin !== null || historicalEmMax !== null ||
      minHoldPeriod !== null || maxHoldPeriod !== null ||
      ownershipRequirement?.length ||
      minimumYieldOnCostMin !== null || minimumYieldOnCostMax !== null ||
      maxLeverageToleranceMin !== null || maxLeverageToleranceMax !== null ||
      notFilters.notOwnershipRequirement?.length
    )

    // Only join investment criteria if we need to filter by it OR if we need the investment_criteria_id for display
    if (hasInvestmentCriteriaFilters) {
      joinClauses += `
        INNER JOIN (
          SELECT DISTINCT ON (entity_id) *
          FROM investment_criteria_central 
          WHERE entity_type = 'company'
          ORDER BY entity_id, updated_at DESC
        ) ic ON c.company_id = ic.entity_id
      `

      // Join debt table if needed
      if (hasDebtFilters) {
        joinClauses += `
          LEFT JOIN investment_criteria_debt icd ON ic.investment_criteria_id = icd.investment_criteria_id
        `
      }

      // Join equity table if needed  
      if (hasEquityFilters) {
        joinClauses += `
          LEFT JOIN investment_criteria_equity ice ON ic.investment_criteria_id = ice.investment_criteria_id
        `
      }
    } else {
      // Light LEFT JOIN to get just the investment_criteria_id for "Has IC" badge
      joinClauses += `
        LEFT JOIN (
          SELECT DISTINCT ON (entity_id) entity_id, investment_criteria_id
          FROM investment_criteria_central 
          WHERE entity_type = 'company'
          ORDER BY entity_id, updated_at DESC
        ) ic ON c.company_id = ic.entity_id
      `
    }

    // V2: No longer need company_extracted_data table - all data is in companies table
    // Note: company_extracted_data join removed in V2 as all relevant data is now in companies table

    // V2: Join company_overview_v2 if we have V2 overview filters (if this table exists)
    if (hasCompanyOverviewV2Filters) {
      joinClauses += `
        INNER JOIN company_overview_v2 cov2 ON c.company_id = cov2.company_id
      `
    }

    // Join contacts table if we have contact processor filters
    if (hasContactProcessorFilters) {
      joinClauses += `
        LEFT JOIN (
          SELECT 
            company_id,
            COUNT(*) as contact_count,
            MAX(email_verification_status) as contact_email_verification_status,
            MAX(contact_enrichment_status) as contact_enrichment_status,
            MAX(contact_enrichment_v2_status) as contact_enrichment_v2_status,
            MAX(email_generation_status) as contact_email_generation_status,
            MAX(email_sending_status) as contact_email_sending_status
          FROM contacts 
          GROUP BY company_id
        ) cp ON c.company_id = cp.company_id
      `
    }

    // Build the final WHERE clause
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

    // V2 Enhanced sorting - includes extracted data fields
    const validSortFields = {
      'updated_at': 'c.updated_at',
      'created_at': 'c.created_at',
      'company_name': 'c.company_name',
      'company_website': 'c.company_website',
      'industry': 'c.industry',
      'target_return': 'ic.target_return',
      'historical_irr': 'ic.historical_irr',
      'historical_em': 'ic.historical_em',
      'minimum_deal_size': 'ic.minimum_deal_size',
      'maximum_deal_size': 'ic.maximum_deal_size',
      'transactions_completed_last_12m': 'c.transactions_completed_last_12m',
      'fund_size': 'c.fund_size',
      'aum': 'c.aum',
      'founded_year': 'c.founded_year',
      'number_of_employees': 'c.number_of_employees',
      'number_of_properties': 'c.number_of_properties',
      'company_type': 'c.company_type',
      'data_confidence_score': 'c.data_confidence_score',
      'market_capitalization': 'c.market_capitalization',
      'annual_revenue': 'c.annual_revenue',
      'websiteScrapingStatus': 'c.website_scraping_status',
      'overviewV2Status': 'c.overview_v2_status'
    }

    const defaultSortField = 'c.updated_at'
    const sortField = validSortFields[sortBy as keyof typeof validSortFields] || defaultSortField
    const orderClause = `ORDER BY ${sortField} ${sortOrder.toUpperCase()} NULLS LAST`
    // Build LIMIT/OFFSET clause - always add parameters if they exist (needed for count query)
    let limitString = ''
    let countQueryParams = [...queryParams] // Create a copy for count query
    let mainQueryParams = [...queryParams] // Create a copy for main query
    
    if (limit) {
      limitString = `LIMIT $${paramIndex} `
      mainQueryParams.push(limit)
      countQueryParams.push(limit)
      paramIndex++
      if (offset) {
        limitString += `OFFSET $${paramIndex}`
        mainQueryParams.push(offset)
        countQueryParams.push(offset)
        paramIndex++
      }
    }
    
    // V2 ENHANCED SELECT - Includes more extracted data fields and V2 features
    const selectFields = `
      c.company_id,
      c.company_name,
      c.company_website as website,
      c.company_phone as main_phone,
      c.main_email,
      c.company_linkedin as linkedin_url,
      c.company_state,
      c.company_city,
      c.company_country,
      c.industry,
      c.source,
      c.processed,
      c.extracted,
      c.processing_state,
      c.website_scraping_status,
      c.company_overview_status,
      c.overview_v2_status,
      c.investment_criteria_status,
      c.created_at,
      c.updated_at,
      
      -- Investment criteria indicator (just the ID for "Has IC" badge)
      ic.investment_criteria_id,
      
      -- V2 Company data (from companies table)
      c.transactions_completed_last_12m,
      c.portfolio_size_sqft,
      c.portfolio_asset_count,
      c.annual_revenue,
      c.market_capitalization,
      c.company_type,
      c.fund_size,
      c.aum,
      c.number_of_properties,
      c.company_address as headquarters,
      c.number_of_offices,
      c.founded_year,
      c.number_of_employees,
      c.investment_focus,
      c.partnerships,
      c.key_executives,
      c.data_confidence_score,
      c.capital_position${hasContactProcessorFilters ? `,
      
      -- Contact processor fields
      cp.contact_count,
      cp.contact_email_verification_status,
      cp.contact_enrichment_status,
      cp.contact_enrichment_v2_status,
      cp.contact_email_generation_status,
      cp.contact_email_sending_status` : ''}
    `

    // Simple stats query - just count totals and statuses
    const statsQuery = `
      SELECT 
        COUNT(*) as total_companies,
        COUNT(CASE WHEN COALESCE(c.website_scraping_status, 'pending') = 'pending' AND c.company_website IS NOT NULL THEN 1 END) as web_crawler_pending,
        COUNT(CASE WHEN c.website_scraping_status = 'running' THEN 1 END) as web_crawler_running,
        COUNT(CASE WHEN c.website_scraping_status = 'completed' THEN 1 END) as web_crawler_completed,
        COUNT(CASE WHEN c.website_scraping_status = 'failed' THEN 1 END) as web_crawler_failed,
        COUNT(CASE WHEN c.website_scraping_status = 'error' THEN 1 END) as web_crawler_error,

        -- Company Overview V2 Stage
        COUNT(CASE WHEN COALESCE(c.overview_v2_status, 'pending') = 'pending' AND c.website_scraping_status = 'completed' THEN 1 END) as company_overview_v2_pending,
        COUNT(CASE WHEN c.website_scraping_status = 'completed' AND c.overview_v2_status = 'running' THEN 1 END) as company_overview_v2_running,
        COUNT(CASE WHEN c.overview_v2_status = 'completed' THEN 1 END) as company_overview_v2_completed,
        COUNT(CASE WHEN c.overview_v2_status = 'failed' THEN 1 END) as company_overview_v2_failed,
        COUNT(CASE WHEN c.overview_v2_status = 'error' THEN 1 END) as company_overview_v2_error,

        -- Company Investment Criteria Stage
        COUNT(CASE WHEN COALESCE(c.investment_criteria_status, 'pending') = 'pending' AND c.overview_v2_status = 'completed' THEN 1 END) as company_investment_criteria_pending,
        COUNT(CASE WHEN c.overview_v2_status = 'completed' AND c.investment_criteria_status = 'running' THEN 1 END) as company_investment_criteria_running,
        COUNT(CASE WHEN c.overview_v2_status = 'completed' AND c.investment_criteria_status = 'completed' THEN 1 END) as company_investment_criteria_completed,
        COUNT(CASE WHEN c.overview_v2_status = 'completed' AND c.investment_criteria_status = 'failed' THEN 1 END) as company_investment_criteria_failed,
        COUNT(CASE WHEN c.overview_v2_status = 'completed' AND c.investment_criteria_status = 'error' THEN 1 END) as company_investment_criteria_error
      ${joinClauses}
      ${whereClause}
    `



    
    // Main query with V2 enhanced fields
    const query = `
      SELECT DISTINCT
      ${selectFields}
        
      ${joinClauses}
      ${whereClause}
      ${orderClause}
      ${shouldReturnData ? limitString : ''}
    `

    // Build final query parameters - use the appropriate parameter arrays
    const finalQueryParams = shouldReturnData ? mainQueryParams : queryParams

    // Log the final query for debugging
    console.log('[Companies Unified API] Final query:', query)
    console.log('[Companies Unified API] Query params:', finalQueryParams)

    // Execute queries based on stats mode
    let dataResult: any = null
    let statsResult: any = null
    
    if (shouldReturnData) {
      // Get both data and stats
      dataResult = await pool.query(query, mainQueryParams)
      statsResult = await pool.query(statsQuery, queryParams)
    } else {
      // Stats only mode
      statsResult = await pool.query(statsQuery, queryParams)
    }
    
    // Extract stats from first row
    const stats = statsResult && statsResult.rows.length > 0 ? {
      total_companies: parseInt(statsResult.rows[0].total_companies || '0'),
      web_crawler_pending: parseInt(statsResult.rows[0].web_crawler_pending || '0'),
      web_crawler_running: parseInt(statsResult.rows[0].web_crawler_running || '0'),
      web_crawler_completed: parseInt(statsResult.rows[0].web_crawler_completed || '0'),
      web_crawler_failed: parseInt(statsResult.rows[0].web_crawler_failed || '0'),
      web_crawler_error: parseInt(statsResult.rows[0].web_crawler_error || '0'),
      company_overview_v2_pending: parseInt(statsResult.rows[0].company_overview_v2_pending || '0'),
      company_overview_v2_running: parseInt(statsResult.rows[0].company_overview_v2_running || '0'),
      company_overview_v2_completed: parseInt(statsResult.rows[0].company_overview_v2_completed || '0'),
      company_overview_v2_failed: parseInt(statsResult.rows[0].company_overview_v2_failed || '0'),
      company_overview_v2_error: parseInt(statsResult.rows[0].company_overview_v2_error || '0'),
      company_investment_criteria_pending: parseInt(statsResult.rows[0].company_investment_criteria_pending || '0'),
      company_investment_criteria_running: parseInt(statsResult.rows[0].company_investment_criteria_running || '0'),
      company_investment_criteria_completed: parseInt(statsResult.rows[0].company_investment_criteria_completed || '0'),
      company_investment_criteria_failed: parseInt(statsResult.rows[0].company_investment_criteria_failed || '0'),
      company_investment_criteria_error: parseInt(statsResult.rows[0].company_investment_criteria_error || '0')
    } : null
    
    const total = stats?.total_companies || 0
    const totalPages = Math.ceil(total / limit)

    // Clean data rows (only if we have data)
    const cleanDataRows = shouldReturnData && dataResult ? dataResult.rows : null

    return NextResponse.json({
      success: true,
      data: cleanDataRows,
      stats: stats,
      pagination: shouldReturnData ? {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      } : null,
      meta: {
        hasInvestmentCriteriaFilters,
        hasCompanyExtractedDataFilters,
        hasContactProcessorFilters,
        hasCompanyOverviewV2Filters,
        joinedTables: {
          investment_criteria: hasInvestmentCriteriaFilters,
          company_extracted_data: hasCompanyExtractedDataFilters,
          contact_processor: hasContactProcessorFilters,
          company_overview_v2: hasCompanyOverviewV2Filters
        },
        optimized: true, // Flag to indicate this is the optimized version
        fieldsReturned: 'v2_enhanced_for_card_display',
        version: 'v2',
        performance: {
          conditionalJoins: true, // Only join tables when filters are applied
          minimalSelect: true, // Only fetch fields needed for CompanyCard
          enhancedNotFilters: true, // V2 NOT filter support
          v2ExtractedDataSupport: true, // Enhanced extracted data filtering
          recommendedIndexes: [
            'idx_investment_criteria_entity_type_entity_id',
            'idx_company_extracted_data_company_id',
            'idx_companies_company_id',
            'idx_companies_overview_v2_status',
            'idx_companies_investment_criteria_status'
          ]
        }
      }
    })

  } catch (error) {
    console.error('Error in V2 unified companies filters API:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch V2 unified company data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
