import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const client = await pool.connect();
    
    try {
      // Get all unique values from companies table and company_extracted_data table
      const queries = await Promise.all([
        // Company Address (from companies table)
        client.query(`
          SELECT DISTINCT company_address as value 
          FROM companies
          WHERE company_address IS NOT NULL AND company_address != ''
          ORDER BY value
        `),
        
        // Company City (from companies table)
        client.query(`
          SELECT DISTINCT company_city as value 
          FROM companies
          WHERE company_city IS NOT NULL AND company_city != ''
          ORDER BY value
        `),
        
        // Company State (from companies table)
        client.query(`
          SELECT DISTINCT company_state as value 
          FROM companies
          WHERE company_state IS NOT NULL AND company_state != ''
          ORDER BY value
        `),
        
        // Company Website (from companies table)
        client.query(`
          SELECT DISTINCT company_website as value 
          FROM companies
          WHERE company_website IS NOT NULL AND company_website != ''
          ORDER BY value
        `),
        
        // Industry (from companies table)
        client.query(`
          SELECT DISTINCT industry as value 
          FROM companies
          WHERE industry IS NOT NULL AND industry != ''
          ORDER BY value
        `),
        
        // Founded Year (from companies table)
        client.query(`
          SELECT DISTINCT founded_year as value 
          FROM companies
          WHERE founded_year IS NOT NULL
          ORDER BY value
        `),
        
        // Company Country (from companies table)
        client.query(`
          SELECT DISTINCT company_country as value 
          FROM companies
          WHERE company_country IS NOT NULL AND company_country != ''
          ORDER BY value
        `),
        
        // Source (from companies table)
        client.query(`
          SELECT DISTINCT source as value 
          FROM companies
          WHERE source IS NOT NULL AND source != ''
          ORDER BY value
        `),
        
        // Website Scraping Status (from companies table)
        client.query(`
          SELECT DISTINCT website_scraping_status as value 
          FROM companies
          WHERE website_scraping_status IS NOT NULL
          ORDER BY value
        `),
        
        // Company Overview Status (from companies table)
        client.query(`
          SELECT DISTINCT company_overview_status as value 
          FROM companies
          WHERE company_overview_status IS NOT NULL
          ORDER BY value
        `),
        
        // Company Overview V2 Status (from companies table)
        client.query(`
          SELECT DISTINCT overview_v2_status as value 
          FROM companies
          WHERE overview_v2_status IS NOT NULL
          ORDER BY value
        `),
        
        // Fund Size (from company_extracted_data table)
        client.query(`
          SELECT DISTINCT fundsize as value 
          FROM company_extracted_data
          WHERE fundsize IS NOT NULL AND fundsize != ''
          ORDER BY value
        `),
        
        // AUM (from company_extracted_data table)
        client.query(`
          SELECT DISTINCT aum as value 
          FROM company_extracted_data
          WHERE aum IS NOT NULL AND aum != ''
          ORDER BY value
        `),
        
        // Headquarters (from company_extracted_data table)
        client.query(`
          SELECT DISTINCT headquarters as value 
          FROM company_extracted_data
          WHERE headquarters IS NOT NULL AND headquarters != ''
          ORDER BY value
        `),
        
        // Founded Year (from company_extracted_data table)
        client.query(`
          SELECT DISTINCT foundedyear as value 
          FROM company_extracted_data
          WHERE foundedyear IS NOT NULL
          ORDER BY value
        `),
        
        // Investment Focus (from company_extracted_data table - JSONB field)
        client.query(`
          SELECT DISTINCT unnest(
            CASE 
              WHEN jsonb_typeof(investmentfocus) = 'array'
              THEN ARRAY(SELECT jsonb_array_elements_text(investmentfocus))
              ELSE ARRAY[]::text[]
            END
          ) as value
          FROM company_extracted_data
          WHERE investmentfocus IS NOT NULL
          ORDER BY value
        `),
        
        // Geographic Focus (from company_extracted_data table - JSONB field)
        client.query(`
          SELECT DISTINCT unnest(
            CASE 
              WHEN jsonb_typeof(geographicfocus) = 'array'
              THEN ARRAY(SELECT jsonb_array_elements_text(geographicfocus))
              ELSE ARRAY[]::text[]
            END
          ) as value
          FROM company_extracted_data
          WHERE geographicfocus IS NOT NULL
          ORDER BY value
        `),
        
        // Target Return (from company_extracted_data table)
        client.query(`
          SELECT DISTINCT targetreturn as value 
          FROM company_extracted_data
          WHERE targetreturn IS NOT NULL AND targetreturn != ''
          ORDER BY value
        `),
        
        // Property Types (from company_extracted_data table - JSONB field)
        client.query(`
          SELECT DISTINCT unnest(
            CASE 
              WHEN jsonb_typeof(propertytypes) = 'array'
              THEN ARRAY(SELECT jsonb_array_elements_text(propertytypes))
              ELSE ARRAY[]::text[]
            END
          ) as value
          FROM company_extracted_data
          WHERE propertytypes IS NOT NULL
          ORDER BY value
        `),
        
        // Strategies (from company_extracted_data table - JSONB field)
        client.query(`
          SELECT DISTINCT unnest(
            CASE 
              WHEN jsonb_typeof(strategies) = 'array'
              THEN ARRAY(SELECT jsonb_array_elements_text(strategies))
              ELSE ARRAY[]::text[]
            END
          ) as value
          FROM company_extracted_data
          WHERE strategies IS NOT NULL
          ORDER BY value
        `),
        
        // Company Industry (from company_extracted_data table)
        client.query(`
          SELECT DISTINCT companyindustry as value 
          FROM company_extracted_data
          WHERE companyindustry IS NOT NULL AND companyindustry != ''
          ORDER BY value
        `),
        
        // Capital Position (from company_extracted_data table)
        client.query(`
          SELECT DISTINCT capitalposition as value 
          FROM company_extracted_data
          WHERE capitalposition IS NOT NULL AND capitalposition != ''
          ORDER BY value
        `),
        
        // Office Locations (from company_extracted_data table - JSONB field)
        client.query(`
          SELECT DISTINCT unnest(
            CASE 
              WHEN jsonb_typeof(officelocations) = 'array'
              THEN ARRAY(SELECT jsonb_array_elements_text(officelocations))
              ELSE ARRAY[]::text[]
            END
          ) as value
          FROM company_extracted_data
          WHERE officelocations IS NOT NULL
          ORDER BY value
        `),
        
        // Partnerships (from company_extracted_data table - JSONB field)
        client.query(`
          SELECT DISTINCT unnest(
            CASE 
              WHEN jsonb_typeof(partnerships) = 'array'
              THEN ARRAY(SELECT jsonb_array_elements_text(partnerships))
              ELSE ARRAY[]::text[]
            END
          ) as value
          FROM company_extracted_data
          WHERE partnerships IS NOT NULL
          ORDER BY value
        `),
      ]);

      const [
        companyAddresses,
        companyCities,
        companyStates,
        companyWebsites,
        industries,
        foundedYears,
        companyCountries,
        sources,
        websiteScrapingStatuses,
        companyOverviewStatuses,
        companyOverviewV2Statuses,
        fundSizes,
        aums,
        headquarters,
        foundedYearsExtracted,
        investmentFocus,
        geographicFocus,
        targetReturns,
        propertyTypes,
        strategies,
        companyIndustries,
        capitalPositions,
        officeLocations,
        partnerships,
      ] = queries;

      const filterOptions = {
        // Company table filters
        companyAddresses: companyAddresses.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        companyCities: companyCities.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        companyStates: companyStates.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        companyWebsites: companyWebsites.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        industries: industries.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        foundedYears: foundedYears.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        companyCountries: companyCountries.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        sources: sources.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        websiteScrapingStatuses: websiteScrapingStatuses.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        companyOverviewStatuses: companyOverviewStatuses.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        companyOverviewV2Statuses: companyOverviewV2Statuses.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        overviewV2Statuses: companyOverviewV2Statuses.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        
        // Company extracted data filters
        fundSizes: fundSizes.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        aums: aums.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        headquarters: headquarters.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        foundedYearsExtracted: foundedYearsExtracted.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        investmentFocus: investmentFocus.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        geographicFocus: geographicFocus.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        targetReturns: targetReturns.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        propertyTypes: propertyTypes.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        strategies: strategies.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        investmentStrategy: strategies.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        companyIndustries: companyIndustries.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        capitalPositions: capitalPositions.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        officeLocations: officeLocations.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
        partnerships: partnerships.rows.map(row => ({
          value: row.value,
          label: row.value
        })),
      };

      return NextResponse.json(filterOptions);
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error fetching company filter options:', error);
    return NextResponse.json(
      { error: 'Failed to fetch company filter options' }, 
      { status: 500 }
    );
  }
} 