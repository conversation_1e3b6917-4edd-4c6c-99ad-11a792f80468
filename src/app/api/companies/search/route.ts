import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    // Accept either 'q' or 'name' as the search parameter
    const query = searchParams.get('q') || searchParams.get('name') || ''
    // Accept 'domain' as a search parameter for domain-based search
    const domain = searchParams.get('domain') || ''
    
    if (query.length < 2 && domain.length < 2) {
      return NextResponse.json([])
    }

    // Search companies with extracted data for enhanced auto-fill
    const searchQuery = `
      SELECT 
        c.company_id,
        c.company_name,
        c.company_website,
        c.industry,
        c.company_address,
        c.company_city,
        c.company_state,
        c.company_country,
        -- Count investment criteria from central table
        COALESCE(ic_count.count, 0) as investment_criteria_count,
        -- Include extracted data for auto-filling
        jsonb_build_object(
          'companytype', ced.companytype,
          'businessmodel', ced.businessmodel,
          'fundsize', ced.fundsize,
          'aum', ced.aum,
          'headquarters', ced.headquarters,
          'foundedyear', ced.foundedyear,
          'numberofemployees', ced.numberofemployees,
          'investmentfocus', ced.investmentfocus,
          'geographicfocus', ced.geographicfocus,
          'dealsize', ced.dealsize,
          'minimumdealsize', ced.minimumdealsize,
          'maximumdealsize', ced.maximumdealsize,
          'investment_criteria_property_types', ced.investment_criteria_property_types,
          'investment_criteria_asset_types', ced.investment_criteria_asset_types,
          'investment_criteria_loan_types', ced.investment_criteria_loan_types,
          'investment_criteria_property_subcategories', ced.investment_criteria_property_subcategories,
          'riskprofile', ced.riskprofile,
          'targetmarkets', ced.targetmarkets,
          'strategies', ced.strategies,
          'propertytypes', ced.propertytypes,
          'assetclasses', ced.assetclasses,
          'valuecreation', ced.valuecreation,
          'holdperiod', ced.holdperiod,
          'targetreturn', ced.targetreturn,
          'approach', ced.approach
        ) as extracted_data
      FROM companies c
      LEFT JOIN company_extracted_data ced ON c.company_id::bigint = ced.company_id
      LEFT JOIN (
        SELECT 
          entity_id::bigint as company_id,
          COUNT(*) as count
        FROM investment_criteria_central 
        WHERE entity_type = 'company'
        GROUP BY entity_id
      ) ic_count ON c.company_id::bigint = ic_count.company_id
      WHERE 
        (LOWER(c.company_name) LIKE LOWER($1)
        OR LOWER(c.company_name) LIKE LOWER($3)
        OR LOWER(c.company_name) LIKE LOWER($4)
        OR LOWER(c.company_name) LIKE LOWER($5))
        ${domain.length >= 2 ? `
        OR (LOWER(c.company_website) LIKE LOWER($6)
        OR LOWER(c.company_website) LIKE LOWER($7)
        OR LOWER(c.company_website) LIKE LOWER($8))` : ''}
      ORDER BY 
        -- Exact matches first
        CASE WHEN LOWER(c.company_name) = LOWER($2) THEN 0 ELSE 1 END,
        -- Domain matches for domain search
        ${domain.length >= 2 ? `CASE WHEN LOWER(c.company_website) = LOWER($9) THEN 0 ELSE 1 END,` : ''}
        -- Prioritize companies with extracted data
        CASE WHEN ced.company_id IS NOT NULL THEN 0 ELSE 1 END,
        -- Then prioritize companies with more complete investment criteria data
        CASE WHEN ced.investment_criteria_property_types IS NOT NULL OR 
                  ced.geographicfocus IS NOT NULL OR 
                  ced.dealsize IS NOT NULL THEN 0 ELSE 1 END,
        -- Then by how closely the search term matches at start
        CASE WHEN LOWER(c.company_name) LIKE LOWER($3) THEN 0 
             WHEN LOWER(c.company_name) LIKE LOWER($4) THEN 1
             WHEN LOWER(c.company_name) LIKE LOWER($5) THEN 2
             ELSE 3 END,
        -- Then by how early the search term appears
        POSITION(LOWER($2) IN LOWER(c.company_name)),
        c.company_name
      LIMIT 20
    `

    // Create different pattern matches for better search quality
    const exactTerm = query
    const startsWith = `${query}%`
    const wordsStartWith = `% ${query}%`
    const anywhere = `%${query}%`
    
    // Create domain pattern matches
    const domainExact = domain
    const domainStartsWith = `${domain}%`
    const domainAnywhere = `%${domain}%`

    const params = [
      anywhere,      // $1: match anywhere in name
      exactTerm,     // $2: exact match
      startsWith,    // $3: starts with query
      wordsStartWith,// $4: starts a word in the middle
      anywhere       // $5: anywhere pattern again for the sort
    ]

    // Add domain search parameters if domain is provided
    if (domain.length >= 2) {
      params.push(
        domainAnywhere,   // $6: domain match anywhere
        domainStartsWith, // $7: domain starts with
        domainAnywhere,   // $8: domain anywhere again
        domainExact       // $9: exact domain match
      )
    }

    const result = await pool.query(searchQuery, params)
    
    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Error searching companies:', error)
    return NextResponse.json(
      { error: 'Failed to search companies' },
      { status: 500 }
    )
  }
} 