import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit
    const search = searchParams.get('search') || ''
    const contactType = searchParams.get('contactType') || 'all' // 'all', 'investors', 'sponsors', 'thirdparty'
    
    const whereClauses = []
    const queryParams: any[] = []
    let paramIndex = 1
    
    // Add search filter
    if (search) {
      whereClauses.push(`(
        LOWER(first_name) LIKE $${paramIndex} OR
        LOWER(last_name) LIKE $${paramIndex} OR
        LOWER(company) LIKE $${paramIndex} OR 
        LOWER(investment_criteria_country) LIKE $${paramIndex} OR
        LOWER(investment_criteria_state) LIKE $${paramIndex} OR
        LOWER(investment_criteria_city) LIKE $${paramIndex}
      )`)
      queryParams.push(`%${search.toLowerCase()}%`)
      paramIndex++
    }
    
    // Add contact type filter
    if (contactType !== 'all') {
      let typeCondition = ''
      if (contactType === 'investors') typeCondition = 'investor'
      else if (contactType === 'sponsors') typeCondition = 'sponsor'
      else if (contactType === 'thirdparty') typeCondition = 'third party'
      
      if (typeCondition) {
        whereClauses.push(`LOWER(contact_category) LIKE $${paramIndex}`)
        queryParams.push(`%${typeCondition}%`)
        paramIndex++
      }
    }
    
    const whereSql = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : ''
    
    // Get total count with filters
    const countQuery = `
      SELECT COUNT(*) as total
      FROM list44
      ${whereSql}
    `
    const countResult = await pool.query(countQuery, queryParams)
    const totalCount = countResult.rows[0].total
    
    // Add pagination parameters to queryParams
    queryParams.push(limit)
    queryParams.push(offset)
    
    const dataQuery = `
      SELECT 
        uuid,
        first_name, 
        last_name,
        email,
        phone_number,
        job_title,
        company,
        capital_type,
        linkedin_profile,
        company_address,
        company_website,
        industry,
        contact_category,
        notes,
        investment_criteria_country,
        investment_criteria_geographic_region,
        investment_criteria_state,
        investment_criteria_city,
        investment_criteria_deal_size,
        investment_criteria_property_type,
        investment_criteria_property_type_subcategory,
        investment_criteria_asset_type,
        investment_criteria_loan_type,
        investment_criteria_loan_type_subcategory_short_term,
        investment_criteria_loan_type_subcategory_long_term,
        investment_criteria_loan_term_years,
        investment_criteria_loan_interest_rate_basis,
        investment_criteria_loan_interest_rate,
        investment_criteria_loan_to_value,
        investment_criteria_loan_to_cost,
        investment_criteria_loan_origination_fee_percent,
        investment_criteria_loan_exit_fee_percent,
        investment_criteria_recourse_loan,
        investment_criteria_loan_dscr,
        investment_criteria_closing_time,
        investment_criteria_tear_sheet
      FROM 
        list44
      ${whereSql}
      ORDER BY 
        last_name ASC, first_name ASC
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}
    `
    
    const result = await pool.query(dataQuery, queryParams)
    
    return NextResponse.json({
      rows: result.rows,
      pagination: {
        total: parseInt(totalCount),
        page,
        limit,
        totalPages: Math.ceil(parseInt(totalCount) / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching list44 data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch data from list44 table' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Create query based on provided fields
    const fields = Object.keys(body)
    const values = Object.values(body)
    
    const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ')
    
    const query = `
      INSERT INTO list44 (
        ${fields.join(', ')},
        uuid
      ) VALUES (
        ${placeholders},
        gen_random_uuid()
      )
      RETURNING *
    `
    
    const result = await pool.query(query, values)
    
    return NextResponse.json({
      message: 'Contact added successfully',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating list44 contact:', error)
    return NextResponse.json(
      { error: 'Failed to create contact' },
      { status: 500 }
    )
  }
} 