import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";
import { calculateV2MatchScore } from "../../_lib/matching-utils-v2";

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ contactId: string }> }
) {
  try {
    const { contactId } = await context.params;
    
    // Parse query parameters
    const url = new URL(req.url);
    const showAllMatches = url.searchParams.get('show_all') === 'true';
    const minScoreThreshold = showAllMatches ? 0 : 50; // Default to 50% if not showing all
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    console.log(`V2: Fetching matching deals for contact ${contactId} using central investment criteria tables`);

    // Get contact data and investment criteria
    const contactQuery = `
      SELECT 
        c.contact_id,
        c.first_name,
        c.last_name,
        c.email,
        c.title AS job_title,
        c.phone_number,
        c.linkedin_url,
        c.updated_at AS contact_updated_at,
        c.created_at AS contact_created_at,
        comp.company_id,
        comp.company_name,
        comp.company_city,
        comp.company_state,
        comp.company_website,
        comp.industry
      FROM contacts c
      LEFT JOIN companies comp ON c.company_id = comp.company_id
      WHERE c.contact_id = $1
    `;

    const contactResult = await pool.query(contactQuery, [contactId]);
    
    if (contactResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Contact not found' },
        { status: 404 }
      );
    }

    const contactData = contactResult.rows[0];

    // Get investment criteria for this contact (from central table and debt/equity tables)
    const criteriaQuery = `
      SELECT 
        icc.*,
        icd.*,
        ice.*
      FROM investment_criteria_central icc
      LEFT JOIN investment_criteria_debt icd ON icc.investment_criteria_id = icd.investment_criteria_id
      LEFT JOIN investment_criteria_equity ice ON icc.investment_criteria_id = ice.investment_criteria_id
      WHERE (icc.entity_id = $1 OR icc.entity_id = $2::text)
        AND icc.is_active = true
      ORDER BY icc.investment_criteria_id
    `;

    const criteriaResult = await pool.query(criteriaQuery, [contactId, contactData.company_id]);
    const contactCriteria = criteriaResult.rows.filter(row => row.investment_criteria_id);

    if (contactCriteria.length === 0) {
      return NextResponse.json({
        matches: [],
        total: 0,
        message: 'No investment criteria found for this contact'
      });
    }

    // Get all deals from dealsv2 table
    const dealsQuery = `
      SELECT 
        d.deal_id,
        d.deal_name,
        d.summary,
        d.ask_capital_position,
        d.ask_amount,
        d.strategy,
        d.deal_stage,
        d.deal_status,
        d.hold_period,
        d.yield_on_cost,
        d.common_equity_internal_rate_of_return_irr,
        d.common_equity_equity_multiple,
        d.gp_equity_multiple,
        d.gp_internal_rate_of_return_irr,
        d.lp_equity_multiple,
        d.lp_internal_rate_of_return_irr,
        d.preferred_equity_internal_rate_of_return_irr,
        d.preferred_equity_equity_multiple,
        d.total_internal_rate_of_return_irr,
        d.total_equity_multiple,
        d.loan_amount,
        d.interest_rate,
        d.loan_term,
        d.loan_to_cost_ltc,
        d.loan_to_value_ltv,
        d.loan_type,
        d.dscr,
        d.recourse,
        d.extra_fields,
        d.exit_cap_rate,
        -- Core NSF measurements moved to properties table
        -- d.total_nsf_net_square_foot,
        d.created_at,
        d.updated_at,
        -- Property data
        p.address,
        p.city,
        p.state,
        p.region,
        p.country,
        p.property_type,
        p.subproperty_type,
        p.building_sqft,
        p.number_of_units,
        -- Core NSF measurements from property table
        p.gsf_gross_square_foot,
        p.zfa_zoning_floor_area,
        p.total_nsf_net_square_foot
      FROM dealsv2 d
      LEFT JOIN properties p ON d.property_id = p.property_id
      WHERE d.deal_status != 'closed'
      ORDER BY d.created_at DESC
      LIMIT $2 OFFSET $3
    `;

    const dealsResult = await pool.query(dealsQuery, [limit, offset]);
    const deals = dealsResult.rows;

    // Calculate matches for each deal
    const matches: any[] = [];

    for (const deal of deals) {
      // For each capital position in the deal, find matching criteria
      const dealCapitalPositions = deal.ask_capital_position || [];
      
      for (const dealCapitalPosition of dealCapitalPositions) {
        // Find criteria that match this capital position
        const matchingCriteria = contactCriteria.filter(criteria => 
          criteria.capital_position && 
          criteria.capital_position.includes(dealCapitalPosition)
        );

        for (const criteria of matchingCriteria) {
          // Calculate match score using v2 matching
          const matchResult = await calculateV2MatchScore(
            deal,
            criteria,
            dealCapitalPosition
          );

          const matchScore = Math.round(matchResult.totalScore * 100);

          // Only include matches above threshold
          if (matchScore >= minScoreThreshold) {
            matches.push({
              deal_id: deal.deal_id,
              deal_name: deal.deal_name,
              capital_position: dealCapitalPosition,
              match_score: matchScore,
              match_breakdown: matchResult.breakdown,
              match_reasons: matchResult.reasons,
              criteria_source: criteria.criteria_source,
              investment_criteria_id: criteria.investment_criteria_id,
              deal_data: {
                deal_id: deal.deal_id,
                deal_name: deal.deal_name,
                summary: deal.summary,
                ask_capital_position: deal.ask_capital_position,
                ask_amount: deal.ask_amount,
                strategy: deal.strategy,
                deal_stage: deal.deal_stage,
                deal_status: deal.deal_status,
                hold_period: deal.hold_period,
                yield_on_cost: deal.yield_on_cost,
                total_internal_rate_of_return_irr: deal.total_internal_rate_of_return_irr,
                total_equity_multiple: deal.total_equity_multiple,
                exit_cap_rate: deal.exit_cap_rate,
                total_nsf_net_square_foot: deal.total_nsf_net_square_foot, // Deprecated - use property.total_nsf_net_square_foot
                loan_amount: deal.loan_amount,
                interest_rate: deal.interest_rate,
                loan_term: deal.loan_term,
                loan_to_cost_ltc: deal.loan_to_cost_ltc,
                loan_to_value_ltv: deal.loan_to_value_ltv,
                loan_type: deal.loan_type,
                dscr: deal.dscr,
                recourse: deal.recourse,
                property_address: deal.address,
                property_city: deal.city,
                property_state: deal.state,
                property_region: deal.region,
                property_type: deal.property_type,
                building_sqft: deal.building_sqft,
                number_of_units: deal.number_of_units,
                created_at: deal.created_at,
                updated_at: deal.updated_at
              },
              contact_data: {
                contact_id: contactData.contact_id,
                first_name: contactData.first_name,
                last_name: contactData.last_name,
                email: contactData.email,
                job_title: contactData.job_title,
                company_name: contactData.company_name,
                company_city: contactData.company_city,
                company_state: contactData.company_state,
                investment_criteria_id: criteria.investment_criteria_id,
                criteria_source: criteria.criteria_source,
                capital_position: criteria.capital_position,
                minimum_deal_size: criteria.minimum_deal_size,
                maximum_deal_size: criteria.maximum_deal_size,
                property_types: criteria.property_types,
                strategies: criteria.strategies,
                target_return: criteria.target_return,
                region: criteria.region,
                state: criteria.state,
                city: criteria.city,
                // Debt fields
                loan_to_value_min: criteria.loan_to_value_min,
                loan_to_value_max: criteria.loan_to_value_max,
                loan_to_cost_min: criteria.loan_to_cost_min,
                loan_to_cost_max: criteria.loan_to_cost_max,
                min_loan_term: criteria.min_loan_term,
                max_loan_term: criteria.max_loan_term,
                min_loan_dscr: criteria.min_loan_dscr,
                max_loan_dscr: criteria.max_loan_dscr,
                loan_interest_rate: criteria.loan_interest_rate,
                // Equity fields
                min_hold_period_years: criteria.min_hold_period_years,
                max_hold_period_years: criteria.max_hold_period_years,
                minimum_equity_multiple: criteria.minimum_equity_multiple
              }
            });
          }
        }
      }
    }

    // Sort matches by score (highest first)
    matches.sort((a, b) => b.match_score - a.match_score);

    // Get total count for pagination
    const totalQuery = `
      SELECT COUNT(DISTINCT d.deal_id) as total
      FROM dealsv2 d
      WHERE d.deal_status != 'closed'
    `;
    const totalResult = await pool.query(totalQuery);
    const total = parseInt(totalResult.rows[0].total);

    return NextResponse.json({
      matches,
      total,
      pagination: {
        limit,
        offset,
        hasMore: offset + limit < total
      },
      contact: {
        contact_id: contactData.contact_id,
        first_name: contactData.first_name,
        last_name: contactData.last_name,
        email: contactData.email,
        company_name: contactData.company_name,
        criteria_count: contactCriteria.length
      }
    });

  } catch (error) {
    console.error('Error in V2 deals-for-contact matching:', error);
    return NextResponse.json(
      { error: 'Failed to fetch matching deals' },
      { status: 500 }
    );
  }
}
