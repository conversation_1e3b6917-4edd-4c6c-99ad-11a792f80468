import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

// GET: Return field weights for a specific capital position
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ capitalPosition: string }> }
) {
  try {
    const { capitalPosition } = await context.params;
    
    // Decode the capital position from URL
    const decodedCapitalPosition = decodeURIComponent(capitalPosition);

    const result = await pool.query(
      `SELECT 
        capital_position,
        field_name,
        weight,
        description,
        is_active,
        created_at,
        updated_at
       FROM capital_position_field_weights 
       WHERE capital_position = $1 AND is_active = true
       ORDER BY weight DESC`,
      [decodedCapitalPosition]
    );

    // Calculate total weight for normalization
    const totalWeight = result.rows.reduce((sum, row) => sum + Number(row.weight), 0);
    
    // Add normalized weights
    const weightsWithNormalized = result.rows.map(row => ({
      ...row,
      normalized_weight: totalWeight > 0 ? Number(row.weight) / totalWeight : 0,
      normalized_percentage: totalWeight > 0 ? Math.round((Number(row.weight) / totalWeight) * 100) : 0
    }));

    return NextResponse.json({ 
      capital_position: decodedCapitalPosition,
      field_weights: weightsWithNormalized,
      total_weight: totalWeight,
      normalized_total: totalWeight > 0 ? 1 : 0
    });
  } catch (error) {
    console.error("Error fetching capital position field weights:", error);
    return NextResponse.json(
      { error: "Failed to fetch capital position field weights" },
      { status: 500 }
    );
  }
}

// PUT: Update field weights for a specific capital position
export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ capitalPosition: string }> }
) {
  try {
    const { capitalPosition } = await context.params;
    const { field_weights } = await req.json();
    
    // Decode the capital position from URL
    const decodedCapitalPosition = decodeURIComponent(capitalPosition);
    
    if (!Array.isArray(field_weights)) {
      return NextResponse.json({ 
        error: "Invalid input. 'field_weights' array is required" 
      }, { status: 400 });
    }

    const results = [];
    
    for (const fieldWeight of field_weights) {
      const { field_name, weight, description, is_active } = fieldWeight;
      
      if (!field_name || typeof weight !== "number" || weight < 0 || weight > 1) {
        return NextResponse.json({ 
          error: "Invalid field weight. field_name and weight (0-1) are required" 
        }, { status: 400 });
      }

      const result = await pool.query(
        `INSERT INTO capital_position_field_weights 
         (capital_position, field_name, weight, description, is_active, updated_at)
         VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
         ON CONFLICT (capital_position, field_name) 
         DO UPDATE SET 
           weight = EXCLUDED.weight,
           description = EXCLUDED.description,
           is_active = EXCLUDED.is_active,
           updated_at = CURRENT_TIMESTAMP
         RETURNING capital_position, field_name, weight, description, is_active`,
        [decodedCapitalPosition, field_name, weight, description || null, is_active !== false]
      );

      results.push(result.rows[0]);
    }

    return NextResponse.json({ 
      capital_position: decodedCapitalPosition,
      updated_weights: results 
    });
  } catch (error) {
    console.error("Error updating capital position field weights:", error);
    return NextResponse.json(
      { error: "Failed to update capital position field weights" },
      { status: 500 }
    );
  }
}
