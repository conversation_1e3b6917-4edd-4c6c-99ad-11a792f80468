import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

// GET: Return all capital position field weights
export async function GET() {
  try {
    const result = await pool.query(
      `SELECT 
        capital_position,
        field_name,
        weight,
        description,
        is_active,
        created_at,
        updated_at
       FROM capital_position_field_weights 
       ORDER BY capital_position, weight DESC`
    );
    
    return NextResponse.json({ 
      capital_position_weights: result.rows 
    });
  } catch (error) {
    console.error("Error fetching capital position field weights:", error);
    return NextResponse.json(
      { error: "Failed to fetch capital position field weights" },
      { status: 500 }
    );
  }
}

// POST: Update a capital position field weight
export async function POST(req: NextRequest) {
  try {
    const { capital_position, field_name, weight, description, is_active } = await req.json();
    
    if (!capital_position || !field_name || typeof weight !== "number" || weight < 0 || weight > 1) {
      return NextResponse.json({ 
        error: "Invalid input. capital_position, field_name, and weight (0-1) are required" 
      }, { status: 400 });
    }

    // Update or insert the weight
    const result = await pool.query(
      `INSERT INTO capital_position_field_weights 
       (capital_position, field_name, weight, description, is_active, updated_at)
       VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
       ON CONFLICT (capital_position, field_name) 
       DO UPDATE SET 
         weight = EXCLUDED.weight,
         description = EXCLUDED.description,
         is_active = EXCLUDED.is_active,
         updated_at = CURRENT_TIMESTAMP
       RETURNING capital_position, field_name, weight, description, is_active`,
      [capital_position, field_name, weight, description || null, is_active !== false]
    );

    return NextResponse.json({ 
      capital_position_weight: result.rows[0] 
    });
  } catch (error) {
    console.error("Error updating capital position field weight:", error);
    return NextResponse.json(
      { error: "Failed to update capital position field weight" },
      { status: 500 }
    );
  }
}

// PUT: Update multiple capital position field weights
export async function PUT(req: NextRequest) {
  try {
    const { updates } = await req.json();
    
    if (!Array.isArray(updates)) {
      return NextResponse.json({ 
        error: "Invalid input. 'updates' array is required" 
      }, { status: 400 });
    }

    const results = [];
    
    for (const update of updates) {
      const { capital_position, field_name, weight, description, is_active } = update;
      
      if (!capital_position || !field_name || typeof weight !== "number" || weight < 0 || weight > 1) {
        return NextResponse.json({ 
          error: "Invalid update. capital_position, field_name, and weight (0-1) are required" 
        }, { status: 400 });
      }

      const result = await pool.query(
        `INSERT INTO capital_position_field_weights 
         (capital_position, field_name, weight, description, is_active, updated_at)
         VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
         ON CONFLICT (capital_position, field_name) 
         DO UPDATE SET 
           weight = EXCLUDED.weight,
           description = EXCLUDED.description,
           is_active = EXCLUDED.is_active,
           updated_at = CURRENT_TIMESTAMP
         RETURNING capital_position, field_name, weight, description, is_active`,
        [capital_position, field_name, weight, description || null, is_active !== false]
      );

      results.push(result.rows[0]);
    }

    return NextResponse.json({ 
      updated_weights: results 
    });
  } catch (error) {
    console.error("Error updating capital position field weights:", error);
    return NextResponse.json(
      { error: "Failed to update capital position field weights" },
      { status: 500 }
    );
  }
}

// DELETE: Delete a capital position field weight
export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const capital_position = searchParams.get('capital_position');
    const field_name = searchParams.get('field_name');
    
    if (!capital_position || !field_name) {
      return NextResponse.json({ 
        error: "capital_position and field_name parameters are required" 
      }, { status: 400 });
    }

    const result = await pool.query(
      `DELETE FROM capital_position_field_weights 
       WHERE capital_position = $1 AND field_name = $2
       RETURNING capital_position, field_name`,
      [capital_position, field_name]
    );

    if (result.rowCount === 0) {
      return NextResponse.json({ 
        error: "Capital position field weight not found" 
      }, { status: 404 });
    }

    return NextResponse.json({ 
      message: "Capital position field weight deleted successfully",
      deleted: result.rows[0]
    });
  } catch (error) {
    console.error("Error deleting capital position field weight:", error);
    return NextResponse.json(
      { error: "Failed to delete capital position field weight" },
      { status: 500 }
    );
  }
}
