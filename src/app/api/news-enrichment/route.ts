import { NextRequest, NextResponse } from 'next/server';
import { NewsEnrichmentProcessor } from '@/lib/processors/NewsEnrichmentProcessor';
import pLimit from 'p-limit';
import { pool } from '@/lib/db';

export const dynamic = 'force-dynamic';

interface ProcessingResult {
  id: number
  title: string
  success: boolean
  error?: string
  extractedData?: any
}

interface ProcessingRequest {
  testMode?: boolean
  limit?: number
  articleIds?: number[]
  concurrencyLimit?: number
}

export async function POST(request: NextRequest) {
  let processingResults: ProcessingResult[] = [];
  let errors: any[] = [];
  
  try {
    const requestBody: ProcessingRequest = await request.json();
    const { 
      testMode = false, 
      limit = 5, 
      articleIds = [], 
      concurrencyLimit = 3 
    } = requestBody;

    console.log(`News enrichment request: testMode=${testMode}, limit=${limit}, specific articleIds=${articleIds.join(',')}`);
    
    // Validate parameters
    const validLimit = Math.min(Math.max(1, parseInt(String(limit))), 50);
    const validConcurrency = Math.min(Math.max(1, parseInt(String(concurrencyLimit))), 10);
    
    // Create processor with appropriate options
    const processorOptions = {
      limit: validLimit,
      singleId: articleIds.length === 1 ? articleIds[0] : undefined,
      testMode: testMode
    };
    
    const processor = new NewsEnrichmentProcessor(processorOptions);
    
    // Get articles to process
    let articlesToProcess;
    
    if (articleIds.length > 0) {
      // Process specific articles
      articlesToProcess = [];
      for (const articleId of articleIds) {
        const specificProcessor = new NewsEnrichmentProcessor({ singleId: articleId });
        const entities = await specificProcessor.getUnprocessedEntities();
        articlesToProcess.push(...entities);
      }
    } else {
      // Get unprocessed articles
      articlesToProcess = await processor.getUnprocessedEntities();
    }
    
    console.log(`Found ${articlesToProcess.length} articles to process`);
    
    if (articlesToProcess.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No articles found to process',
        processed: 0,
        errors: 0,
        results: []
      });
    }
    
    // Set up concurrency limit for processing
    const limit_func = pLimit(validConcurrency);
    
    // Process articles with concurrency control
    const processingPromises = articlesToProcess.map(article => 
      limit_func(async () => {
        try {
          console.log(`\nProcessing article ${article.news_id}: ${article.news_title}`);
          
          const result = await processor.processEntity(article);
          
          if (result.success) {
            await processor.updateEntityStatus(article.news_id, true);
            console.log(`Successfully processed article ${article.news_id}`);
            
            // Return enriched data in test mode
            const returnData: ProcessingResult = {
              id: article.news_id,
              title: article.news_title,
              success: true
            };
            
            if (testMode) {
              // In test mode, return the extracted data
              returnData.extractedData = {
                message: 'Test mode - data extracted but not saved to database',
                // Note: In a real implementation, you'd capture the extracted data
                // before saving and include it here
              };
            }
            
            return returnData;
          } else {
            await processor.updateEntityStatus(article.news_id, false);
            console.error(`Failed to process article ${article.news_id}: ${result.error}`);
            
            return {
              id: article.news_id,
              title: article.news_title,
              success: false,
              error: result.error
            };
          }
        } catch (error: any) {
          console.error(`Error processing article ${article.news_id}:`, error);
          
          try {
            await processor.updateEntityStatus(article.news_id, false);
          } catch (updateError) {
            console.error(`Failed to update status for article ${article.news_id}:`, updateError);
          }
          
          return {
            id: article.news_id,
            title: article.news_title,
            success: false,
            error: error.message || 'Unknown error'
          };
        }
      })
    );
    
    // Wait for all processing to complete
    const results = await Promise.all(processingPromises);
    
    // Collect results and errors
    processingResults = results.filter(r => r.success);
    errors = results.filter(r => !r.success);
    
    // Return comprehensive results
    return NextResponse.json({
      success: true,
      message: `Processed ${articlesToProcess.length} articles`,
      processed: processingResults.length,
      errors: errors.length,
      results: testMode 
        ? processingResults  // Return full data in test mode
        : processingResults.map(r => ({ id: r.id, title: r.title })), // Return summary in normal mode
      errorDetails: errors,
      processingStats: {
        totalArticles: articlesToProcess.length,
        successfullyProcessed: processingResults.length,
        failedProcessing: errors.length,
        concurrencyLimit: validConcurrency,
        testMode: testMode
      }
    });
    
  } catch (error: any) {
    console.error('Error in news enrichment API:', error);
    
    return NextResponse.json({
      success: false,
      message: error.message || 'Internal server error',
      processed: processingResults.length,
      errors: errors.length + 1,
      results: processingResults,
      errorDetails: [...errors, { error: error.message }]
    }, { status: 500 });
  }
}

/**
 * GET endpoint to retrieve enrichment status and statistics
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const newsId = searchParams.get('newsId');
    const includeStats = searchParams.get('includeStats') === 'true';
    
    const processor = new NewsEnrichmentProcessor();
    
    if (newsId) {
      // Get enrichment data for specific news article
      const sql = `
        SELECT ne.*, n.news_title, n.news_source, n.news_date
        FROM news_enrichment ne
        JOIN news n ON ne.news_id = n.id
        WHERE ne.news_id = $1
      `;
      
      const result = await pool.query(sql, [parseInt(newsId)]);
      
      if (result.rows.length === 0) {
        return NextResponse.json({
          success: false,
          message: 'No enrichment data found for this article'
        }, { status: 404 });
      }
      
      
      // Also get investment criteria for this article
      const criteriaSql = `
        SELECT * FROM investment_criteria 
        WHERE entity_type = 'News Article' AND entity_id = $1
        ORDER BY created_at DESC
      `;
      
      const criteriaResult = await pool.query(criteriaSql, [newsId]);
      
      return NextResponse.json({
        success: true,
        enrichmentData: result.rows[0],
        investmentCriteria: criteriaResult.rows
      });
    }
    
    if (includeStats) {
      // Get overall enrichment statistics
      const statsSql = `
        SELECT 
          COUNT(*) as total_articles,
          COUNT(CASE WHEN ne.news_id IS NOT NULL THEN 1 END) as enriched_articles,
          COUNT(CASE WHEN ne.news_id IS NOT NULL THEN 1 END) as completed_articles,
          0 as failed_articles,
          0 as processing_articles,
          0 as pending_articles,
          AVG(ne.source_confidence) as avg_confidence_score,
          COUNT(CASE WHEN ne.is_deal_specific = true THEN 1 END) as articles_with_deals,
          COUNT(CASE WHEN ne.is_deal_specific = true THEN 1 END) as total_deals_extracted,
          0 as articles_with_criteria,
          0 as total_criteria_extracted
        FROM news n
        LEFT JOIN news_enrichment ne ON n.id = ne.news_id
        WHERE n.enriched = true 
          AND n.news_text IS NOT NULL
          AND n.is_relevant = true
      `;
      
      const statsResult = await pool.query(statsSql, []);
      
      return NextResponse.json({
        success: true,
        statistics: statsResult.rows[0]
      });
    }
    
    // Get list of recent enrichment activities
    const recentSql = `
      SELECT 
        ne.news_id,
        n.news_title,
        n.news_source,
        n.news_date,
        CASE 
          WHEN ne.news_id IS NOT NULL THEN 'completed'
          ELSE 'pending'
        END as processing_status,
        ne.source_confidence as confidence_score,
        CASE WHEN ne.is_deal_specific = true THEN 1 ELSE 0 END as deal_count,
        0 as investment_criteria_count,
        ne.created_at as processed_at,
        ne.created_at
      FROM news_enrichment ne
      JOIN news n ON ne.news_id = n.id
      ORDER BY ne.updated_at DESC
      LIMIT 50
    `;
    
    const recentResult = await pool.query(recentSql, []);
    
    return NextResponse.json({
      success: true,
      recentEnrichments: recentResult.rows
    });
    
  } catch (error: any) {
    console.error('Error in news enrichment GET API:', error);
    
    return NextResponse.json({
      success: false,
      message: error.message || 'Internal server error'
    }, { status: 500 });
  }
} 