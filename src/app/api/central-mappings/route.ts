import { NextRequest, NextResponse } from 'next/server';
import { typeORMService } from '@/lib/typeorm/service';
import { withTypeOR<PERSON><PERSON>and<PERSON> } from '@/lib/typeorm/middleware';

async function getCentralMappingsHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const dataSource = await typeORMService.getDataSource();
    
    // Fetch all central mappings using query builder with distinct values
    const capitalPositions = await dataSource
      .createQueryBuilder()
      .select(['cm.value_1 as value', 'cm.value_1 as label', 'cm.type as category'])
      .from('central_mapping', 'cm')
      .where('cm.type = :type', { type: 'Capital Position' })
      .andWhere('cm.is_active = :isActive', { isActive: true })
      .distinct()
      .orderBy('cm.value_1', 'ASC')
      .getRawMany();

    const propertyTypes = await dataSource
      .createQueryBuilder()
      .select(['cm.value_1 as value', 'cm.value_1 as label', 'cm.type as category'])
      .from('central_mapping', 'cm')
      .where('cm.type = :type', { type: 'Property Type' })
      .andWhere('cm.is_active = :isActive', { isActive: true })
      .distinct()
      .orderBy('cm.value_1', 'ASC')
      .getRawMany();

    const subpropertyTypes = await dataSource
      .createQueryBuilder()
      .select(['cm.value_2 as value', 'cm.value_2 as label', 'cm.type as category'])
      .from('central_mapping', 'cm')
      .where('cm.type = :type', { type: 'Property Type' })
      .andWhere('cm.value_2 IS NOT NULL')
      .andWhere('cm.is_active = :isActive', { isActive: true })
      .distinct()
      .orderBy('cm.value_2', 'ASC')
      .getRawMany();

    const strategies = await dataSource
      .createQueryBuilder()
      .select(['cm.value_1 as value', 'cm.value_1 as label', 'cm.type as category'])
      .from('central_mapping', 'cm')
      .where('cm.type = :type', { type: 'Strategies' })
      .andWhere('cm.is_active = :isActive', { isActive: true })
      .distinct()
      .orderBy('cm.value_1', 'ASC')
      .getRawMany();

    const industries = await dataSource
      .createQueryBuilder()
      .select(['cm.value_1 as value', 'cm.value_1 as label', 'cm.type as category'])
      .from('central_mapping', 'cm')
      .where('cm.type = :type', { type: 'Industry' })
      .andWhere('cm.is_active = :isActive', { isActive: true })
      .distinct()
      .orderBy('cm.value_1', 'ASC')
      .getRawMany();

    return NextResponse.json({
      success: true,
      data: {
        capitalPositions,
        propertyTypes,
        subpropertyTypes,
        strategies,
        industries
      }
    });

  } catch (error) {
    console.error('Error fetching central mappings:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch central mappings',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export const GET = withTypeORMHandler(getCentralMappingsHandler);
