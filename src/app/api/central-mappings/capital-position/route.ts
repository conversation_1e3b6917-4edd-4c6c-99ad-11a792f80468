import { NextRequest, NextResponse } from 'next/server';
import { AppDataSource } from '@/lib/typeorm/config';

export async function GET(request: NextRequest) {
  try {
    // Initialize database connection if not already connected
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }

    // Query the central_mapping table for capital position options
    const capitalPositions = await AppDataSource.query(`
      SELECT DISTINCT value_1 as capital_position
      FROM central_mapping 
      WHERE type = 'Capital Position' 
        AND is_active = true 
      ORDER BY value_1
    `);

    // Extract capital position values
    const capitalPositionOptions = capitalPositions.map((row: any) => row.capital_position);

    return NextResponse.json({
      success: true,
      capitalPositions: capitalPositionOptions,
      count: capitalPositionOptions.length
    });

  } catch (error) {
    console.error('Error fetching capital position options:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch capital position options',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
