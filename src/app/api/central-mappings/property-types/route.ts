import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const client = await pool.connect();
    
    try {
      // Fetch property type hierarchy from central_mapping table
      const query = `
        SELECT value_1 as property_type, value_2 as sub_type 
        FROM central_mapping 
        WHERE type = 'Property Type' 
        AND is_active = true
        ORDER BY value_1, value_2
      `;
      
      const result = await client.query(query);
      
      // Group by property type
      const mappings: Record<string, string[]> = {};
      
      result.rows.forEach(row => {
        const propertyType = row.property_type;
        const subType = row.sub_type;
        
        if (!mappings[propertyType]) {
          mappings[propertyType] = [];
        }
        if (subType) {
          mappings[propertyType].push(subType);
        }
      });

      console.log(`[DEBUG] Fetched ${Object.keys(mappings).length} property type mappings`);
      
      return NextResponse.json({
        success: true,
        mappings,
        count: Object.keys(mappings).length
      });
      
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error fetching property type mappings:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch property type mappings',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 