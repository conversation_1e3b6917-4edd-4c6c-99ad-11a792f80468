import { NextRequest, NextResponse } from 'next/server';
import { AppDataSource } from '@/lib/typeorm/config';

export async function GET(request: NextRequest) {
  try {
    // Initialize database connection if not already connected
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }

    // Get property type from query params for filtering
    const { searchParams } = new URL(request.url);
    const propertyType = searchParams.get('propertyType');

    let query = `
      SELECT DISTINCT value_2 as subproperty_type
      FROM central_mapping 
      WHERE type = 'Property Type' 
        AND value_2 IS NOT NULL
        AND is_active = true 
    `;

    const params: any[] = [];

    // Add property type filter if provided
    if (propertyType) {
      query += ` AND value_1 = $1`;
      params.push(propertyType);
    }

    query += ` ORDER BY value_2`;

    // Execute query with parameters
    const subpropertyTypes = await AppDataSource.query(query, params);

    // Extract subproperty type values
    const subpropertyTypeOptions = subpropertyTypes.map((row: any) => row.subproperty_type);

    return NextResponse.json({
      success: true,
      subpropertyTypes: subpropertyTypeOptions,
      count: subpropertyTypeOptions.length,
      filteredBy: propertyType || 'all'
    });

  } catch (error) {
    console.error('Error fetching subproperty type options:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch subproperty type options',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
