import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const client = await pool.connect();
    
    try {
      // Fetch capital position to loan type mappings from central_mapping table
      const query = `
        SELECT value_1 as capital_position, value_2 as loan_type 
        FROM central_mapping 
        WHERE type = 'Capital Position' 
        AND value_2 IS NOT NULL 
        AND is_active = true
        ORDER BY value_1, value_2
      `;
      
      const result = await client.query(query);
      
      // Group by capital position
      const mappings: Record<string, string[]> = {};
      
      result.rows.forEach(row => {
        const capitalPos = row.capital_position;
        const loanType = row.loan_type;
        
        if (!mappings[capitalPos]) {
          mappings[capitalPos] = [];
        }
        mappings[capitalPos].push(loanType);
      });

      console.log(`[DEBUG] Fetched ${Object.keys(mappings).length} capital position mappings`);
      
      return NextResponse.json({
        success: true,
        mappings,
        count: Object.keys(mappings).length
      });
      
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error fetching capital position mappings:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch capital position mappings',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 