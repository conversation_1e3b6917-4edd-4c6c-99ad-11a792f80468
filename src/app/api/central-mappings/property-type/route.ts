import { NextRequest, NextResponse } from 'next/server';
import { AppDataSource } from '@/lib/typeorm/config';

export async function GET(request: NextRequest) {
  try {
    // Initialize database connection if not already connected
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }

    // Query the central_mapping table for property type options
    const propertyTypes = await AppDataSource.query(`
      SELECT DISTINCT value_1 as property_type
      FROM central_mapping 
      WHERE type = 'Property Type' 
        AND is_active = true 
      ORDER BY value_1
    `);

    // Extract property type values
    const propertyTypeOptions = propertyTypes.map((row: any) => row.property_type);

    return NextResponse.json({
      success: true,
      propertyTypes: propertyTypeOptions,
      count: propertyTypeOptions.length
    });

  } catch (error) {
    console.error('Error fetching property type options:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch property type options',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
