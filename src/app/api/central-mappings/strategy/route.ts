import { NextRequest, NextResponse } from 'next/server';
import { AppDataSource } from '@/lib/typeorm/config';

export async function GET(request: NextRequest) {
  try {
    // Initialize database connection if not already connected
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }

    // Query the central_mapping table for strategy options
    const strategies = await AppDataSource.query(`
      SELECT DISTINCT value_1 as strategy
      FROM central_mapping 
      WHERE type = 'Strategies' 
        AND is_active = true 
      ORDER BY value_1
    `);

    // Extract strategy values
    const strategyOptions = strategies.map((row: any) => row.strategy);

    return NextResponse.json({
      success: true,
      strategies: strategyOptions,
      count: strategyOptions.length
    });

  } catch (error) {
    console.error('Error fetching strategy options:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch strategy options',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
