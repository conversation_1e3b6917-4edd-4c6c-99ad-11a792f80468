import { NextRequest, NextResponse } from "next/server";
import { typeORMService } from "@/lib/typeorm/service";
import { withTypeOR<PERSON><PERSON>and<PERSON> } from "@/lib/typeorm/middleware";
import { ILike, FindOptionsWhere, FindManyOptions } from "typeorm";

async function getDealsHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "20");
    const search = searchParams.get("search") || "";
    const sortBy = searchParams.get("sortBy") || "updatedAt";
    const sortOrder = searchParams.get("sortOrder") || "DESC";

    // Get repositories from the service
    const dealsRepository = typeORMService.getDealsRepository();
    const nsfRepository = typeORMService.getNsfRepository();

    // Build search conditions
    const whereConditions: FindOptionsWhere<any> = {};
    if (search) {
      whereConditions.dealName = ILike(`%${search}%`);
    }

    // Add support for additional filters
    const dealStage = searchParams.get("dealStage");
    const dealStatus = searchParams.get("dealStatus");

    if (dealStage) {
      whereConditions.dealStage = dealStage;
    }
    if (dealStatus) {
      whereConditions.dealStatus = dealStatus;
    }

    // Map snake_case column names to camelCase property names
    const columnMapping: { [key: string]: string } = {
      'updated_at': 'updatedAt',
      'created_at': 'createdAt',
      'deal_id': 'dealId',
      'deal_name': 'dealName',
      'deal_stage': 'dealStage',
      'deal_status': 'dealStatus',
      'date_received': 'dateReceived',
      'date_closed': 'dateClosed',
      'date_under_contract': 'dateUnderContract',
      'priority': 'priority',
      'review_status': 'reviewStatus',
      'reviewed_at': 'reviewedAt',
      'extraction_timestamp': 'extractionTimestamp'
    };

    // Build sort options with proper property mapping
    const sortOptions: any = {};
    const mappedSortBy = columnMapping[sortBy] || sortBy;
    sortOptions[mappedSortBy] = sortOrder.toUpperCase();
    
    // Get total count
    const total = await dealsRepository.count({ where: whereConditions });

    // Get deals with pagination
    const findOptions: FindManyOptions<any> = {
      where: whereConditions,
      order: sortOptions,
      skip: (page - 1) * pageSize,
      take: pageSize,
      relations: ['property', 'property.owner']
    };

    const deals = await dealsRepository.find(findOptions);

    // Calculate data quality metrics for each deal
    const dealsWithQuality = await Promise.all(
      deals.map(async (deal) => {
        const qualityMetrics = await calculateDealDataQuality(deal, nsfRepository);
        return {
          ...deal,
          data_quality_metrics: qualityMetrics
        };
      })
    );

    const totalPages = Math.ceil(total / pageSize);

    return NextResponse.json({
      deals: dealsWithQuality,
      total,
      totalPages,
      currentPage: page,
      pageSize
    });
  } catch (error) {
    console.error("Error fetching deals v2:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function calculateDealDataQuality(deal: any, nsfRepository: any) {
  const fields = [
    'dealName', 'dealStage', 'priority', 'dealStatus',
    'yieldOnCost', 'commonEquityInternalRateOfReturnIrr', 'commonEquityEquityMultiple',
    'gpEquityMultiple', 'gpInternalRateOfReturnIrr', 'lpEquityMultiple', 'lpInternalRateOfReturnIrr',
    'preferredEquityInternalRateOfReturnIrr', 'preferredEquityEquityMultiple',
    'totalInternalRateOfReturnIrr', 'totalEquityMultiple'
  ];

  let completedFields = 0;
  const missingFields: string[] = [];

  fields.forEach(field => {
    const value = (deal as any)[field];
    const hasValue = value !== null && value !== '' && value !== undefined;
    
    if (hasValue) {
      completedFields++;
    } else {
      missingFields.push(field);
    }
  });

  // Check NSF fields
  const nsfFields = await nsfRepository.find({ where: { deal: { dealId: deal.dealId } } });
  const hasNsfData = nsfFields.length > 0;
  
  if (hasNsfData) {
    completedFields++;
  } else {
    missingFields.push('nsf_fields');
  }

  // Check property data
  if (deal.property) {
    completedFields++;
  } else {
    missingFields.push('property');
  }

  const totalFields = fields.length + 2; // +2 for nsf_fields and property
  const qualityScore = Math.round((completedFields / totalFields) * 100);

  return {
    qualityScore,
    completedFields,
    totalFields,
    missingFields
  };
}

// Export the handler wrapped with TypeORM middleware
export const GET = withTypeORMHandler(getDealsHandler); 