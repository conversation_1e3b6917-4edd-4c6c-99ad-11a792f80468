import { NextRequest, NextResponse } from 'next/server';
import { typeORMService } from '@/lib/typeorm/service';

// GET - Fetch all NSF fields for a deal
async function getNsfFieldsHandler(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = parseInt(id);
    if (isNaN(dealId)) {
      return NextResponse.json({ error: 'Invalid deal ID' }, { status: 400 });
    }

    // Initialize TypeORM service if not already initialized
    if (!typeORMService.getInitialized()) {
      await typeORMService.initialize();
    }
    
    const nsfRepository = typeORMService.getNsfRepository();
    const nsfFields = await nsfRepository.find({
      where: { dealId },
      order: { nsfContext: 'ASC', capitalPosition: 'ASC' }
    });

    return NextResponse.json({ nsfFields });
  } catch (error) {
    console.error('Error fetching NSF fields:', error);
    return NextResponse.json(
      { error: 'Failed to fetch NSF fields' },
      { status: 500 }
    );
  }
}

// POST - Create a new NSF field
async function createNsfFieldHandler(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = parseInt(id);
    if (isNaN(dealId)) {
      return NextResponse.json({ error: 'Invalid deal ID' }, { status: 400 });
    }

    const body = await request.json();
    const {
      nsfContext,
      capitalPosition,
      sourceType,
      useType,
      dealType,
      amount,
      amountPerGsf,
      amountPerNsf,
      amountPerZfa,
      percentageOfTotal,
      isRequired,
      additionalInfo
    } = body;

    // Validate required fields
    if (!nsfContext) {
      return NextResponse.json(
        { error: 'nsfContext is required' },
        { status: 400 }
      );
    }
    
    // For sources, sourceType is required; for uses, useType is required
    if (nsfContext === 'sources' && !sourceType) {
      return NextResponse.json(
        { error: 'sourceType is required for sources' },
        { status: 400 }
      );
    }
    
    if (nsfContext === 'uses_total' && !useType) {
      return NextResponse.json(
        { error: 'useType is required for uses' },
        { status: 400 }
      );
    }

    // Initialize TypeORM service if not already initialized
    if (!typeORMService.getInitialized()) {
      await typeORMService.initialize();
    }
    
    const nsfRepository = typeORMService.getNsfRepository();

    // Check for duplicates
    if (nsfContext === 'sources') {
      // Check if sourceType already exists for this deal
      const existingSource = await nsfRepository.findOne({
        where: { dealId, nsfContext: 'sources', sourceType }
      });
      
      if (existingSource) {
        return NextResponse.json(
          { error: `Source type "${sourceType}" already exists for this deal. Each source type can only be added once.` },
          { status: 400 }
        );
      }
    } else if (nsfContext === 'uses_total') {
      // Check if useType already exists for this deal
      const existingUse = await nsfRepository.findOne({
        where: { dealId, nsfContext: 'uses_total', useType }
      });
      
      if (existingUse) {
        return NextResponse.json(
          { error: `Use type "${useType}" already exists for this deal. Each use type can only be added once.` },
          { status: 400 }
        );
      }
    }

    // Start a transaction for creating NSF field
    console.log(`[DEBUG] About to create query runner for NSF field creation`);
    const queryRunner = typeORMService.getDataSource().createQueryRunner();
    console.log(`[DEBUG] Query runner created, connecting...`);
    await queryRunner.connect();
    console.log(`[DEBUG] Query runner connected, starting transaction...`);
    await queryRunner.startTransaction();
    console.log(`[DEBUG] Transaction started successfully`);

    try {
      // Create the NSF field
      const nsfField = nsfRepository.create({
        dealId,
        nsfContext,
        // Set capitalPosition based on sourceType or useType for backward compatibility
        capitalPosition: sourceType || useType || null,
        sourceType,
        useType,
        dealType: dealType || (sourceType ? 'debt' : 'equity'), // Infer deal type
        // Note: NSF measurement fields moved to properties table
        amount: amount || 0,
        amountPerGsf: amountPerGsf || 0,
        amountPerNsf: amountPerNsf || 0,
        amountPerZfa: amountPerZfa || 0,
        percentageOfTotal: percentageOfTotal || 0,
        isRequired: false, // Default to false, will be set by askCapitalPosition logic
        additionalInfo: additionalInfo || {}
      });

      const savedNsfField = await nsfRepository.save(nsfField);

      await queryRunner.commitTransaction();

      // Create investment criteria outside the main transaction to avoid conflicts
      if (nsfContext === 'sources' && sourceType) {
        try {
          console.log(`[DEBUG] Starting investment criteria creation for sourceType: ${sourceType}, dealId: ${dealId}`);
          console.log(`[DEBUG] sourceType type: ${typeof sourceType}, value: ${JSON.stringify(sourceType)}`);
          
          // Use a separate query runner for investment criteria
          const icQueryRunner = typeORMService.getDataSource().createQueryRunner();
          await icQueryRunner.connect();
          console.log(`[DEBUG] Query runner connected successfully`);
          
          await icQueryRunner.startTransaction();
          console.log(`[DEBUG] Transaction started successfully`);

          try {
            // Determine if this is debt or equity based on sourceType
            const isDebt = sourceType.toLowerCase().includes('debt') || 
                          sourceType.toLowerCase().includes('loan') || 
                          sourceType.toLowerCase().includes('mezzanine');
            
            console.log(`[DEBUG] Determined deal type: ${isDebt ? 'debt' : 'equity'}`);
            console.log(`[DEBUG] About to insert into investment_criteria_central with values:`, {
              entity_type: 'deal_v2',
              entity_id: dealId,
              capital_position: sourceType,
              dealId_type: typeof dealId,
              sourceType_type: typeof sourceType
            });
            
            // Create investment criteria central entry - ensure entity_id is integer
            const centralCriteriaResult = await icQueryRunner.query(`
              INSERT INTO investment_criteria_central 
              (entity_type, entity_id, capital_position, created_at, updated_at)
              VALUES ($1, $2, $3, NOW(), NOW())
              RETURNING investment_criteria_id
            `, ['deal_v2', dealId, sourceType]);
            
            console.log(`[DEBUG] Central criteria insert result:`, centralCriteriaResult);
            
            const investmentCriteriaId = centralCriteriaResult[0].investment_criteria_id;
            console.log(`[DEBUG] Got investment criteria ID: ${investmentCriteriaId}`);
            
            // Create the appropriate criteria type
            if (isDebt) {
              console.log(`[DEBUG] Creating debt criteria for ID: ${investmentCriteriaId}`);
              const debtResult = await icQueryRunner.query(`
                INSERT INTO investment_criteria_debt 
                (investment_criteria_id, created_at, updated_at)
                VALUES ($1, NOW(), NOW())
                RETURNING investment_criteria_debt_id
              `, [investmentCriteriaId]);
              console.log(`[DEBUG] Debt criteria created:`, debtResult);
            } else {
              console.log(`[DEBUG] Creating equity criteria for ID: ${investmentCriteriaId}`);
              const equityResult = await icQueryRunner.query(`
                INSERT INTO investment_criteria_equity 
                (investment_criteria_id, created_at, updated_at)
                VALUES ($1, NOW(), NOW())
                RETURNING investment_criteria_equity_id
              `, [investmentCriteriaId]);
              console.log(`[DEBUG] Equity criteria created:`, equityResult);
            }

            console.log(`[DEBUG] About to commit transaction`);
            await icQueryRunner.commitTransaction();
            console.log(`[DEBUG] Transaction committed successfully`);
          } catch (icError) {
            console.error(`[DEBUG] Error during investment criteria creation:`, icError);
            console.error(`[DEBUG] Error stack:`, icError instanceof Error ? icError.stack : 'No stack trace');
            await icQueryRunner.rollbackTransaction();
            console.warn('Failed to create investment criteria:', icError);
          } finally {
            console.log(`[DEBUG] Releasing query runner`);
            await icQueryRunner.release();
          }
        } catch (investmentCriteriaError) {
          // Log the error but don't fail the entire NSF field creation
          console.error(`[DEBUG] Outer catch block error:`, investmentCriteriaError);
          console.error(`[DEBUG] Outer error stack:`, investmentCriteriaError instanceof Error ? investmentCriteriaError.stack : 'No stack trace');
          console.warn('Failed to create investment criteria, continuing with NSF field:', investmentCriteriaError);
        }
      }

      return NextResponse.json({ 
        nsfField: savedNsfField,
        message: 'NSF field created successfully'
      });
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
      } catch (error) {
      console.error('Error creating NSF field:', error);
      console.error(`[DEBUG] Error type: ${typeof error}`);
      console.error(`[DEBUG] Error constructor: ${error?.constructor?.name}`);
      console.error(`[DEBUG] Error message: ${error?.message}`);
      console.error(`[DEBUG] Error name: ${error?.name}`);
      
      if (error instanceof Error && error.stack) {
        console.error(`[DEBUG] Full error stack:`, error.stack);
      }
      
      // Log additional error properties
      if (error && typeof error === 'object') {
        console.error(`[DEBUG] Error keys:`, Object.keys(error));
        console.error(`[DEBUG] Error prototype:`, Object.getPrototypeOf(error));
      }
      
      return NextResponse.json(
        { error: 'Failed to create NSF field' },
        { status: 500 }
      );
    }
}

// PUT - Update an existing NSF field
async function updateNsfFieldHandler(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = parseInt(id);
    if (isNaN(dealId)) {
      return NextResponse.json({ error: 'Invalid deal ID' }, { status: 400 });
    }

    const body = await request.json();
    const { nsfFieldId, ...updateData } = body;

    if (!nsfFieldId) {
      return NextResponse.json(
        { error: 'nsfFieldId is required' },
        { status: 400 }
      );
    }

    const nsfRepository = typeORMService.getNsfRepository();
    
    // Check if the NSF field exists and belongs to this deal
    const existingField = await nsfRepository.findOne({
      where: { id: nsfFieldId, dealId }
    });

    if (!existingField) {
      return NextResponse.json(
        { error: 'NSF field not found' },
        { status: 404 }
      );
    }

    // Update the field
    await nsfRepository.update(nsfFieldId, updateData);
    
    // Fetch the updated field
    const updatedField = await nsfRepository.findOne({
      where: { id: nsfFieldId }
    });

    return NextResponse.json({ 
      nsfField: updatedField,
      message: 'NSF field updated successfully'
    });
  } catch (error) {
    console.error('Error updating NSF field:', error);
    return NextResponse.json(
      { error: 'Failed to update NSF field' },
      { status: 500 }
    );
  }
}

// DELETE - Delete an NSF field
async function deleteNsfFieldHandler(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = parseInt(id);
    if (isNaN(dealId)) {
      return NextResponse.json({ error: 'Invalid deal ID' }, { status: 400 });
    }

    const { searchParams } = new URL(request.url);
    const nsfFieldId = searchParams.get('nsfFieldId');

    if (!nsfFieldId) {
      return NextResponse.json(
        { error: 'nsfFieldId query parameter is required' },
        { status: 400 }
      );
    }

    // Initialize TypeORM service if not already initialized
    if (!typeORMService.getInitialized()) {
      await typeORMService.initialize();
    }
    
    const nsfRepository = typeORMService.getNsfRepository();
    
    // Check if the NSF field exists and belongs to this deal
    const existingField = await nsfRepository.findOne({
      where: { id: parseInt(nsfFieldId), dealId }
    });

    if (!existingField) {
      return NextResponse.json(
        { error: 'NSF field not found' },
        { status: 404 }
      );
    }

    // Start a transaction for deleting NSF field and related investment criteria
    const queryRunner = typeORMService.getDataSource().createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Delete the NSF field
      await nsfRepository.delete(parseInt(nsfFieldId));

      // If this was a source, also delete related investment criteria
      if (existingField.nsfContext === 'sources' && existingField.sourceType) {
        // Find investment criteria central entries with the same capital position
        const centralCriteriaResult = await queryRunner.query(`
          SELECT investment_criteria_id 
          FROM investment_criteria_central 
          WHERE entity_type = 'deal_v2' 
          AND entity_id = $1 
          AND capital_position = $2
        `, [dealId, existingField.sourceType]);

        if (centralCriteriaResult.length > 0) {
          for (const criteria of centralCriteriaResult) {
            const criteriaId = criteria.investment_criteria_id;
            
            // Delete debt criteria if they exist
            await queryRunner.query(`
              DELETE FROM investment_criteria_debt 
              WHERE investment_criteria_id = $1
            `, [criteriaId]);
            
            // Delete equity criteria if they exist
            await queryRunner.query(`
              DELETE FROM investment_criteria_equity 
              WHERE investment_criteria_id = $1
            `, [criteriaId]);
            
            // Delete the central criteria entry
            await queryRunner.query(`
              DELETE FROM investment_criteria_central 
              WHERE investment_criteria_id = $1
            `, [criteriaId]);
          }
        }
      }

      await queryRunner.commitTransaction();

      return NextResponse.json({ 
        message: 'NSF field and related investment criteria deleted successfully'
      });
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  } catch (error) {
    console.error('Error deleting NSF field:', error);
    return NextResponse.json(
      { error: 'Failed to delete NSF field' },
      { status: 500 }
    );
  }
}

// Export the handlers directly without TypeORM middleware wrapper
export const GET = async (request: NextRequest) => {
  const pathParts = request.nextUrl.pathname.split('/');
  const dealId = pathParts[pathParts.length - 2]; // Get the deal ID (second to last part)
  return getNsfFieldsHandler(request, { params: Promise.resolve({ id: dealId || '' }) });
};

export const POST = async (request: NextRequest) => {
  const pathParts = request.nextUrl.pathname.split('/');
  const dealId = pathParts[pathParts.length - 2]; // Get the deal ID (second to last part)
  return createNsfFieldHandler(request, { params: Promise.resolve({ id: dealId || '' }) });
};

export const PUT = async (request: NextRequest) => {
  const pathParts = request.nextUrl.pathname.split('/');
  const dealId = pathParts[pathParts.length - 2]; // Get the deal ID (second to last part)
  return updateNsfFieldHandler(request, { params: Promise.resolve({ id: dealId || '' }) });
};

export const DELETE = async (request: NextRequest) => {
  const pathParts = request.nextUrl.pathname.split('/');
  const dealId = pathParts[pathParts.length - 2]; // Get the deal ID (second to last part)
  return deleteNsfFieldHandler(request, { params: Promise.resolve({ id: dealId || '' }) });
};
