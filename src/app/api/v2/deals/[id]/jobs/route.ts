import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = id;
    
    if (!dealId) {
      return NextResponse.json(
        { error: "Deal ID is required" },
        { status: 400 }
      );
    }

    // Query jobs related to this deal
    const result = await pool.query(
      `SELECT * FROM public.jobs 
       WHERE data->>'dealId' = $1 
       OR data->>'dealId' = $2
       ORDER BY created_at DESC`,
      [dealId, parseInt(dealId).toString()]
    );

    return NextResponse.json({
      success: true,
      jobs: result.rows || [],
    });

  } catch (error) {
    console.error("Error fetching jobs:", error);
    return NextResponse.json(
      { 
        error: "Failed to fetch jobs",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
} 