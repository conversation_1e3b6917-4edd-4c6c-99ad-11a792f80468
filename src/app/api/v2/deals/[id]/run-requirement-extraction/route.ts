import { NextRequest, NextResponse } from "next/server";
import { BullMQManager } from "@/lib/queue/BullMQManager";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { dealId } = await request.json();
    
    if (!dealId) {
      return NextResponse.json(
        { error: "Deal ID is required" },
        { status: 400 }
      );
    }

    // Get BullMQ manager instance
    const bullManager = BullMQManager.getInstance();
    
    // Create a mock file entity for reprocessing
    const jobData = {
      files: [], // Empty files for reprocessing existing deal
      dealId: dealId,
      jobMetadata: {
        originalFileNames: [],
        fileCount: 0,
        uploadTimestamp: new Date().toISOString(),
      },
    };

    // Add V2 processing job to queue
    const jobId = await bullManager.addDealV2ProcessingJob(jobData, {
      createdBy: "system",
    });

    return NextResponse.json({
      success: true,
      message: "Requirement extraction job queued successfully",
      jobId: jobId,
    });

  } catch (error) {
    console.error("Error running requirement extraction:", error);
    return NextResponse.json(
      { 
        error: "Failed to run requirement extraction",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
} 