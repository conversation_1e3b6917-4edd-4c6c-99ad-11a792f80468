import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";
import { TypeORMService } from "@/lib/typeorm/service";
import { withTypeOR<PERSON>Hand<PERSON> } from "@/lib/typeorm/middleware";

// GET: Get all contacts for a V2 deal
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id: dealId } = await context.params;
    
    // Initialize TypeORM service
    const typeORMService = TypeORMService.getInstance();
    await typeORMService.initialize();
    
    // Verify the V2 deal exists
    const dealsRepository = typeORMService.getDealsRepository();
    const deal = await dealsRepository.findOne({ where: { dealId: parseInt(dealId) } });
    
    if (!deal) {
      return NextResponse.json(
        { error: "V2 Deal not found" },
        { status: 404 }
      );
    }
    
    // Get deal contacts using TypeORM
    const dealContactRepository = typeORMService.getDealContactRepository();
    const dealContacts = await dealContactRepository.find({
      where: { dealV2Id: parseInt(dealId), dealVersion: "v2" },
      order: { createdAt: "DESC" }
    });
    
    // Get contact details for each deal contact
    const contacts = [];
    for (const dealContact of dealContacts) {
      const contactResult = await pool.query(
        `SELECT 
          c.contact_id,
          c.first_name,
          c.last_name,
          c.full_name,
          c.email,
          c.personal_email,
          c.title,
          c.phone_number,
          c.linkedin_url,
          c.company_id,
          comp.company_name
        FROM contacts c
        LEFT JOIN companies comp ON c.company_id = comp.company_id
        WHERE c.contact_id = $1`,
        [dealContact.contactId]
      );
      
      if (contactResult.rows.length > 0) {
        contacts.push({
          ...contactResult.rows[0],
          added_at: dealContact.createdAt
        });
      }
    }
    
    return NextResponse.json({
      contacts,
      total: contacts.length
    });
  } catch (error) {
    console.error("Error fetching V2 deal contacts:", error);
    return NextResponse.json(
      { error: "Failed to fetch V2 deal contacts" },
      { status: 500 }
    );
  }
}

// POST: Add contacts to a V2 deal
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id: dealId } = await context.params;
    const { contactIds } = await req.json();
    
    if (!contactIds || !Array.isArray(contactIds) || contactIds.length === 0) {
      return NextResponse.json(
        { error: "contactIds array is required" },
        { status: 400 }
      );
    }
    
    // Verify the V2 deal exists
    const dealCheck = await pool.query(
      "SELECT deal_id FROM dealsv2 WHERE deal_id = $1",
      [dealId]
    );
    
    if (dealCheck.rows.length === 0) {
      return NextResponse.json(
        { error: "V2 Deal not found" },
        { status: 404 }
      );
    }
    
    // Verify all contacts exist
    const contactCheck = await pool.query(
      "SELECT contact_id FROM contacts WHERE contact_id = ANY($1)",
      [contactIds]
    );
    
    if (contactCheck.rows.length !== contactIds.length) {
      return NextResponse.json(
        { error: "One or more contacts not found" },
        { status: 404 }
      );
    }
    
    // Initialize TypeORM service
    const typeORMService = TypeORMService.getInstance();
    await typeORMService.initialize();
    
    // Add contacts to deal using TypeORM
    const dealContactRepository = typeORMService.getDealContactRepository();
    const addedContacts = [];
    
    for (const contactId of contactIds) {
      try {
        // Check if contact already exists for this deal
        const existingContact = await dealContactRepository.findOne({
          where: { dealV2Id: parseInt(dealId), contactId, dealVersion: "v2" }
        });
        
        if (!existingContact) {
          // Create new deal contact
          const dealContact = dealContactRepository.create({
            dealV2Id: parseInt(dealId),
            contactId,
            dealVersion: "v2"
          });
          
          await dealContactRepository.save(dealContact);
          addedContacts.push(contactId);
        }
      } catch (error) {
        console.error(`Error adding contact ${contactId} to V2 deal ${dealId}:`, error);
      }
    }
    
    // Update the primary contact if this is the first contact being added
    const dealsRepository = typeORMService.getDealsRepository();
    const deal = await dealsRepository.findOne({ where: { dealId: parseInt(dealId) } });
    
    if (deal && addedContacts.length > 0) {
      // Check if dealsv2 table has a primary_contact_id column
      const tableInfo = await pool.query(
        "SELECT column_name FROM information_schema.columns WHERE table_name = 'dealsv2' AND column_name = 'primary_contact_id'"
      );
      
      if (tableInfo.rows.length > 0) {
        // Update primary contact in dealsv2 table
        await pool.query(
          "UPDATE dealsv2 SET primary_contact_id = $1 WHERE deal_id = $2",
          [addedContacts[0], dealId]
        );
      }
    }
    
    return NextResponse.json({
      success: true,
      addedContacts,
      totalAdded: addedContacts.length
    });
  } catch (error) {
    console.error("Error adding contacts to V2 deal:", error);
    return NextResponse.json(
      { error: "Failed to add contacts to V2 deal" },
      { status: 500 }
    );
  }
}

// DELETE: Remove a contact from a V2 deal
export async function DELETE(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id: dealId } = await context.params;
    const { searchParams } = new URL(req.url);
    const contactId = searchParams.get('contactId');
    
    if (!contactId) {
      return NextResponse.json(
        { error: "contactId query parameter is required" },
        { status: 400 }
      );
    }
    
    // Initialize TypeORM service
    const typeORMService = TypeORMService.getInstance();
    await typeORMService.initialize();
    
    // Remove the contact from the deal using TypeORM
    const dealContactRepository = typeORMService.getDealContactRepository();
    await dealContactRepository.delete({
      dealV2Id: parseInt(dealId),
      contactId: parseInt(contactId),
      dealVersion: "v2"
    });
    
    // Check if dealsv2 table has a primary_contact_id column
    const tableInfo = await pool.query(
      "SELECT column_name FROM information_schema.columns WHERE table_name = 'dealsv2' AND column_name = 'primary_contact_id'"
    );
    
    if (tableInfo.rows.length > 0) {
      // If this was the primary contact, update the dealsv2 table
      const primaryContactCheck = await pool.query(
        "SELECT primary_contact_id FROM dealsv2 WHERE deal_id = $1 AND primary_contact_id = $2",
        [dealId, contactId]
      );
      
      if (primaryContactCheck.rows.length > 0) {
        // Find another contact to set as primary, or set to null
        const remainingContacts = await dealContactRepository.find({
          where: { dealV2Id: parseInt(dealId), dealVersion: "v2" },
          order: { createdAt: "ASC" },
          take: 1
        });
        
        const newPrimaryContactId = remainingContacts.length > 0 
          ? remainingContacts[0].contactId 
          : null;
        
        await pool.query(
          "UPDATE dealsv2 SET primary_contact_id = $1 WHERE deal_id = $2",
          [newPrimaryContactId, dealId]
        );
      }
    }
    
    return NextResponse.json({
      success: true,
      removedContactId: contactId
    });
  } catch (error) {
    console.error("Error removing contact from V2 deal:", error);
    return NextResponse.json(
      { error: "Failed to remove contact from V2 deal" },
      { status: 500 }
    );
  }
} 