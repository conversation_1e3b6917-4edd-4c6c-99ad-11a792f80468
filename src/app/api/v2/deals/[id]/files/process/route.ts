import { NextRequest, NextResponse } from "next/server";
import { FileManager } from "@/lib/utils/fileManager";
import { bullMQManager } from "@/lib/queue/BullMQManager";

// POST /api/v2/deals/[id]/files/process
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = id;
    const { selectedFiles } = await request.json();

    if (!selectedFiles || selectedFiles.length === 0) {
      return NextResponse.json(
        { error: "No files selected for processing" },
        { status: 400 }
      );
    }

    // Get the selected files using the generic FileManager
    const tableFiles = await FileManager.getTableFiles({
      table_name: "dealsv2",
      column_name: "deal_id",
      row_id: dealId,
    });

    // Filter to only the selected files
    const files = tableFiles.filter(file => selectedFiles.includes(file.file_id));

    if (files.length === 0) {
      return NextResponse.json(
        { error: "No valid files found for processing" },
        { status: 400 }
      );
    }

    // Create V2 processing jobs for each file
    const jobs = [];
    for (const file of files) {
      // Get the actual file content from disk
      const fileContent = await FileManager.getFileFromDisk(file.storage_path);
      
      if (fileContent) {
        // Convert to base64 for processing
        const base64Content = fileContent.toString('base64');
        
        const job = await bullMQManager.addDealV2ProcessingJob({
          dealId: parseInt(dealId),
          files: [base64Content],
          fileNames: [file.original_name],
          processorVersion: "v2"
        });
        
        jobs.push(job);

        // Update file processing status using FileManager
        await FileManager.updateFile(file.file_id, {
          processing_status: "processing",
          processing_job_id: job.id,
          processing_timestamp: new Date()
        });
      }
    }

    return NextResponse.json({ 
      message: `Processing started for ${files.length} files`,
      jobs: jobs.map(job => ({ id: job.id, name: job.name }))
    });
  } catch (error) {
    console.error("Error processing V2 deal files:", error);
    return NextResponse.json(
      { error: "Failed to start processing" },
      { status: 500 }
    );
  }
} 