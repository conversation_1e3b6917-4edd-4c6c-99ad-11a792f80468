import { NextRequest, NextResponse } from "next/server";
import { FileManager } from "@/lib/utils/fileManager";

// DELETE /api/v2/deals/[id]/files/[fileId]
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; fileId: string }> }
) {
  try {
    const { id, fileId } = await params;
    const dealId = id;

    // First, get the file relationships to find the relationship ID
    const relationships = await FileManager.getFileRelationships({
      file_id: fileId
    });

    // Find the relationship for this specific deal
    const relationship = relationships.find(rel => 
      rel.target_table_name === "dealsv2" && 
      rel.target_column_name === "deal_id" && 
      rel.target_row_id === dealId
    );

    if (!relationship) {
      return NextResponse.json(
        { error: "File relationship not found" },
        { status: 404 }
      );
    }

    // Delete the file relationship using the generic FileManager
    const result = await FileManager.deleteFileRelationship(relationship.relationship_id);

    if (!result) {
      return NextResponse.json(
        { error: "Failed to delete file relationship" },
        { status: 500 }
      );
    }

    return NextResponse.json({ 
      message: "File deleted successfully" 
    });
  } catch (error) {
    console.error("Error deleting V2 deal file:", error);
    return NextResponse.json(
      { error: "Failed to delete file" },
      { status: 500 }
    );
  }
} 