import { NextRequest, NextResponse } from "next/server";
import { bullMQManager } from "@/lib/queue/BullMQManager";
import { writeFile, mkdir } from "fs/promises";
import { join } from "path";
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/typeorm/middleware";

async function uploadDeals<PERSON>and<PERSON>(request: NextRequest): Promise<NextResponse> {
  try {
    const formData = await request.formData();
    
    // Debug: Log all form data keys
    console.log("🔍 Form data keys received:", Array.from(formData.keys()));
    
    // Extract files from form data - handle both "files" and "file_0", "file_1" patterns
    let files: File[] = [];
    
    // First try the standard "files" field
    const standardFiles = formData.getAll("files") as File[];
    console.log("🔍 Standard files found:", standardFiles?.length || 0);
    
    if (standardFiles && standardFiles.length > 0) {
      files = standardFiles;
    } else {
      // Look for individual file fields like "file_0", "file_1", etc.
      let fileIndex = 0;
      while (true) {
        const file = formData.get(`file_${fileIndex}`) as File;
        if (!file) break;
        console.log(`🔍 Found file_${fileIndex}:`, file.name, file.type);
        files.push(file);
        fileIndex++;
      }
    }
    
    console.log("🔍 Total files extracted:", files.length);
    
    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: "No files provided" },
        { status: 400 }
      );
    }

    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), "uploads", "deals-v2");
    await mkdir(uploadsDir, { recursive: true });

        // Create job data for V2 processing
    const jobData = {
      files: await Promise.all(files.map(async file => ({
        buffer: Buffer.from(await file.arrayBuffer()).toString('base64'), // Base64 encode buffer
        mimeType: file.type,
        fileName: file.name
      }))),
      processorVersion: "v2",
      llmModel: "gemini-flash" as const,
      createdBy: "upload_ui",
      jobMetadata: {
        uploadTimestamp: new Date().toISOString(),
        originalFileNames: files.map(f => f.name),
        fileCount: files.length,
        processorVersion: "v2"
      }
    };

    console.log("📦 Created V2 job data:", {
      fileCount: jobData.files.length,
      processorVersion: jobData.processorVersion,
      llmModel: jobData.llmModel
    });

    // Add V2 job to queue using the V2-specific method
    const jobId = await bullMQManager.addDealV2ProcessingJob(jobData, {
      priority: 1,
      createdBy: "upload_ui"
    });

    // Return success response with job ID
    return NextResponse.json({
      success: true,
      jobId,
      message: "Files uploaded successfully and V2 processing started",
      fileCount: files.length,
      fileNames: files.map(f => f.name),
      processorVersion: "v2",
      estimatedProcessingTime: files.length * 30 // seconds
    });

  } catch (error) {
    console.error("Error uploading deals v2:", error);
    return NextResponse.json(
      { 
        error: "Failed to upload deals",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// Export the handler wrapped with TypeORM middleware
export const POST = withTypeORMHandler(uploadDealsHandler); 