import { NextRequest, NextResponse } from "next/server";
import { typeORMService } from "@/lib/typeorm/service";
import { withTypeOR<PERSON><PERSON>and<PERSON> } from "@/lib/typeorm/middleware";

async function getDealsQualityHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const dealId = searchParams.get("dealId");

    // Get repositories from the service
    const dealsRepository = typeORMService.getDealsRepository();
    const nsfRepository = typeORMService.getNsfRepository();
    const propertyRepository = typeORMService.getPropertyRepository();

    if (dealId) {
      // Get quality metrics for a specific deal
      const deal = await dealsRepository.findOne({
        where: { dealId: parseInt(dealId) },
        relations: ['property', 'property.owner']
      });

      if (!deal) {
        return NextResponse.json(
          { error: "Deal not found" },
          { status: 404 }
        );
      }

      const qualityMetrics = await calculateDetailedQualityMetrics(deal, nsfRepository);

      return NextResponse.json({
        success: true,
        dealId: parseInt(dealId),
        qualityMetrics
      });
    } else {
      // Get overall quality metrics for all deals
      const deals = await dealsRepository.find({
        relations: ['property', 'property.owner']
      });

      const overallMetrics = await calculateOverallQualityMetrics(deals, nsfRepository);

      return NextResponse.json({
        success: true,
        overallMetrics
      });
    }

  } catch (error) {
    console.error("Error getting deals quality v2:", error);
    return NextResponse.json(
      { 
        error: "Failed to get deals quality",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

async function calculateDetailedQualityMetrics(deal: DealsV2, nsfRepository: any) {
  const fieldCategories = {
    basic: [
      'dealName', 'dealStage', 'dealStatus', 'strategy', 'priority'
    ],
    financial: [
      'yieldOnCost', 'commonEquityInternalRateOfReturnIrr', 'commonEquityEquityMultiple',
      'gpEquityMultiple', 'gpInternalRateOfReturnIrr', 'lpEquityMultiple', 'lpInternalRateOfReturnIrr',
      'preferredEquityInternalRateOfReturnIrr', 'preferredEquityEquityMultiple',
      'totalInternalRateOfReturnIrr', 'totalEquityMultiple'
    ],
    unitCounts: [
      'numAffordableHousing1bedroomUnits', 'numAffordableHousing2bedroomUnits',
      'numAffordableHousing3bedroomUnits', 'numAffordableHousingStudiosUnits',
      'numMarketRate1bedroomUnits', 'numMarketRate2bedroomUnits',
      'numMarketRate3bedroomUnits', 'numMarketRateStudiosUnits'
    ],
    pricing: [
      'affordableHousingRent1bedroomUnit', 'affordableHousingRent2bedroomUnit',
      'affordableHousingRent3bedroomUnit', 'affordableHousingRentStudioUnit',
      'marketRateRent1bedroomUnit', 'marketRateRent2bedroomUnit',
      'marketRateRent3bedroomUnit', 'marketRateSale1bedroomUnit',
      'marketRateSale2bedroomUnit', 'marketRateSale3bedroomUnit',
      'marketRateSaleStudioUnit'
    ],
    nsf: [
      'residentialNsfNetSquareFoot', 'retailNsfNetSquareFoot', 'officeNsfNetSquareFoot',
      'communityFacilityNsfNetSquareFoot', 'totalNsfNetSquareFoot'
    ],
    loan: [
      'loanAmount', 'interestRate', 'loanTerm', 'loanToCostLtc', 'loanToValueLtv',
      'dscr', 'exitCapRate'
    ]
  };

  const categoryScores: any = {};
  let totalScore = 0;
  let totalFields = 0;

  for (const [category, fields] of Object.entries(fieldCategories)) {
    let completedFields = 0;
    const missingFields: string[] = [];

    fields.forEach(field => {
      const value = (deal as any)[field];
      const hasValue = value !== null && value !== '' && value !== undefined;
      
      if (hasValue) {
        completedFields++;
      } else {
        missingFields.push(field);
      }
    });

    const categoryScore = Math.round((completedFields / fields.length) * 100);
    categoryScores[category] = {
      score: categoryScore,
      completed: completedFields,
      total: fields.length,
      missing: missingFields
    };

    totalScore += completedFields;
    totalFields += fields.length;
  }

  // Check NSF fields
  const nsfFields = await nsfRepository.find({ where: { deal: { dealId: deal.dealId } } });
  const hasNsfData = nsfFields.length > 0;
  
  if (hasNsfData) {
    totalScore++;
  }
  totalFields++;

  // Check property data
  if (deal.property) {
    totalScore++;
  }
  totalFields++;

  const overallScore = Math.round((totalScore / totalFields) * 100);

  return {
    overallScore,
    categoryScores,
    nsfFieldsCount: nsfFields.length,
    hasProperty: !!deal.property,
    recommendations: generateRecommendations(categoryScores, overallScore)
  };
}

async function calculateOverallQualityMetrics(deals: DealsV2[], nsfRepository: any) {
  const totalDeals = deals.length;
  let totalScore = 0;
  const categoryAverages: any = {};
  const fieldCompleteness: any = {};

  // Initialize field tracking
  const allFields = [
    'dealName', 'dealStage', 'dealStatus', 'strategy', 'priority',
    'yieldOnCost', 'commonEquityInternalRateOfReturnIrr', 'commonEquityEquityMultiple',
    'residentialNsfNetSquareFoot', 'retailNsfNetSquareFoot', 'officeNsfNetSquareFoot',
    'totalNsfNetSquareFoot', 'loanAmount', 'interestRate', 'loanTerm'
  ];

  allFields.forEach(field => {
    fieldCompleteness[field] = { completed: 0, total: totalDeals };
  });

  for (const deal of deals) {
    const metrics = await calculateDetailedQualityMetrics(deal, nsfRepository);
    totalScore += metrics.overallScore;

    // Track field completeness
    allFields.forEach(field => {
      const value = (deal as any)[field];
      if (value !== null && value !== '' && value !== undefined) {
        fieldCompleteness[field].completed++;
      }
    });
  }

  const averageScore = Math.round(totalScore / totalDeals);

  // Calculate field completion percentages
  Object.keys(fieldCompleteness).forEach(field => {
    const { completed, total } = fieldCompleteness[field];
    fieldCompleteness[field].percentage = Math.round((completed / total) * 100);
  });

  return {
    totalDeals,
    averageScore,
    fieldCompleteness,
    qualityDistribution: {
      high: deals.filter(d => d.reviewStatus === 'approved').length,
      medium: deals.filter(d => d.reviewStatus === 'reviewed').length,
      low: deals.filter(d => d.reviewStatus === 'pending').length
    }
  };
}

function generateRecommendations(categoryScores: any, overallScore: number): string[] {
  const recommendations: string[] = [];

  if (overallScore < 50) {
    recommendations.push("Overall data quality is low. Focus on completing basic deal information first.");
  }

  Object.entries(categoryScores).forEach(([category, data]: [string, any]) => {
    if (data.score < 30) {
      recommendations.push(`${category.charAt(0).toUpperCase() + category.slice(1)} data is severely incomplete. Prioritize this category.`);
    } else if (data.score < 60) {
      recommendations.push(`${category.charAt(0).toUpperCase() + category.slice(1)} data needs improvement. Consider adding more ${category} fields.`);
    }
  });

  if (recommendations.length === 0) {
    recommendations.push("Data quality is good across all categories. Consider adding more detailed financial metrics.");
  }

  return recommendations;
}

// Export the handler wrapped with TypeORM middleware
export const GET = withTypeORMHandler(getDealsQualityHandler); 