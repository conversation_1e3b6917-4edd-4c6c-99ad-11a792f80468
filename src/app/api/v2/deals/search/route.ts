import { NextRequest, NextResponse } from "next/server";
import { typeORMService } from "@/lib/typeorm/service";
import { withTypeOR<PERSON><PERSON>and<PERSON> } from "@/lib/typeorm/middleware";
import { Like, Between, In, FindOptionsWhere, FindManyOptions } from "typeorm";

async function searchDealsHandler(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "20");
    const search = searchParams.get("search") || "";
    const sortBy = searchParams.get("sortBy") || "dealId";
    const sortOrder = searchParams.get("sortOrder") || "DESC";
    
    // Filter parameters
    const dealType = searchParams.get("dealType");
    const dealStage = searchParams.get("dealStage");
    const dealStatus = searchParams.get("dealStatus");
    const strategy = searchParams.get("strategy");
    const propertyType = searchParams.get("propertyType");
    const state = searchParams.get("state");
    const city = searchParams.get("city");
    const minAmount = searchParams.get("minAmount");
    const maxAmount = searchParams.get("maxAmount");
    const isDistressed = searchParams.get("isDistressed");
    const reviewStatus = searchParams.get("reviewStatus");

    // Get repositories from the service
    const dealsRepository = typeORMService.getDealsRepository();
    const propertyRepository = typeORMService.getPropertyRepository();
    const nsfRepository = typeORMService.getNsfRepository();

    // Build search conditions
    const whereConditions: FindOptionsWhere<DealsV2> = {};
    
    if (search) {
      whereConditions.dealName = Like(`%${search}%`);
    }
    
    if (dealType) {
      whereConditions.dealType = dealType;
    }
    
    if (dealStage) {
      whereConditions.dealStage = dealStage;
    }
    
    if (dealStatus) {
      whereConditions.dealStatus = dealStatus;
    }
    
    if (strategy) {
      whereConditions.strategy = strategy;
    }
    
    if (isDistressed !== null && isDistressed !== undefined) {
      whereConditions.isDistressed = isDistressed === "true";
    }
    
    if (reviewStatus) {
      whereConditions.reviewStatus = reviewStatus;
    }

    // Build sort options
    const sortOptions: any = {};
    sortOptions[sortBy] = sortOrder.toUpperCase();

    // Get total count
    const total = await dealsRepository.count({ where: whereConditions });

    // Build find options with relations
    const findOptions: FindManyOptions<DealsV2> = {
      where: whereConditions,
      order: sortOptions,
      skip: (page - 1) * pageSize,
      take: pageSize,
      relations: ['property', 'property.owner']
    };

    // Apply property filters if specified
    if (propertyType || state || city) {
      findOptions.relations = ['property', 'property.owner'];
      
      // We'll need to filter after fetching due to TypeORM limitations with nested relations
      let deals = await dealsRepository.find(findOptions);
      
      // Apply property filters
      if (propertyType) {
        deals = deals.filter(deal => deal.property?.propertyType === propertyType);
      }
      
      if (state) {
        deals = deals.filter(deal => deal.property?.state === state);
      }
      
      if (city) {
        deals = deals.filter(deal => deal.property?.city === city);
      }
      
      // Apply amount filters
      if (minAmount || maxAmount) {
        deals = deals.filter(deal => {
          const amount = deal.askAmount?.[0] || 0;
          if (minAmount && amount < parseFloat(minAmount)) return false;
          if (maxAmount && amount > parseFloat(maxAmount)) return false;
          return true;
        });
      }
      
      // Calculate data quality metrics
      const dealsWithQuality = await Promise.all(
        deals.map(async (deal) => {
          const qualityMetrics = await calculateDealDataQuality(deal, nsfRepository);
          return {
            ...deal,
            data_quality_metrics: qualityMetrics
          };
        })
      );

      const totalPages = Math.ceil(total / pageSize);

      return NextResponse.json({
        deals: dealsWithQuality,
        total: deals.length,
        totalPages,
        currentPage: page,
        pageSize,
        filters: {
          dealStage,
          dealStatus,
          strategy,
          propertyType,
          state,
          city,
          minAmount,
          maxAmount,
          isDistressed,
          reviewStatus
        }
      });
    } else {
      // No property filters, use standard approach
      const deals = await dealsRepository.find(findOptions);

      // Calculate data quality metrics
      const dealsWithQuality = await Promise.all(
        deals.map(async (deal) => {
          const qualityMetrics = await calculateDealDataQuality(deal, nsfRepository);
          return {
            ...deal,
            data_quality_metrics: qualityMetrics
          };
        })
      );

      const totalPages = Math.ceil(total / pageSize);

      return NextResponse.json({
        deals: dealsWithQuality,
        total,
        totalPages,
        currentPage: page,
        pageSize,
        filters: {
          dealStage,
          dealStatus,
          strategy,
          propertyType,
          state,
          city,
          minAmount,
          maxAmount,
          isDistressed,
          reviewStatus
        }
      });
    }

  } catch (error) {
    console.error("Error searching deals v2:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function calculateDealDataQuality(deal: any, nsfRepository: any) {
  const fields = [
    'dealName', 'dealStage', 'dealStatus', 'strategy',
    'yieldOnCost', 'commonEquityInternalRateOfReturnIrr', 'commonEquityEquityMultiple',
    'gpEquityMultiple', 'gpInternalRateOfReturnIrr', 'lpEquityMultiple', 'lpInternalRateOfReturnIrr',
    'preferredEquityInternalRateOfReturnIrr', 'preferredEquityEquityMultiple',
    'totalInternalRateOfReturnIrr', 'totalEquityMultiple'
  ];

  let completedFields = 0;
  const missingFields: string[] = [];

  fields.forEach(field => {
    const value = (deal as any)[field];
    const hasValue = value !== null && value !== '' && value !== undefined;
    
    if (hasValue) {
      completedFields++;
    } else {
      missingFields.push(field);
    }
  });

  // Check NSF fields
  const nsfFields = await nsfRepository.find({ where: { deal: { dealId: deal.dealId } } });
  const hasNsfData = nsfFields.length > 0;
  
  if (hasNsfData) {
    completedFields++;
  } else {
    missingFields.push('nsf_fields');
  }

  // Check property data
  if (deal.property) {
    completedFields++;
  } else {
    missingFields.push('property');
  }

  const totalFields = fields.length + 2; // +2 for nsf_fields and property
  const qualityScore = Math.round((completedFields / totalFields) * 100);

  return {
    qualityScore,
    completedFields,
    totalFields,
    missingFields
  };
}

// Export the handler wrapped with TypeORM middleware
export const GET = withTypeORMHandler(searchDealsHandler); 