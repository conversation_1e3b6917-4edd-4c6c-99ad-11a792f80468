import { NextRequest, NextResponse } from 'next/server'
import { getProcessorLimits, validateProcessorLimit, getProcessorLimit } from '../../../config/processor-limits'

export async function GET() {
  try {
    const limits = getProcessorLimits()
    
    return NextResponse.json({
      success: true,
      data: limits
    })
  } catch (error) {
    console.error('[ProcessorLimits API] Error getting limits:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get processor limits' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { processorName, limit, maxLimit, batchSize } = body

    if (!processorName) {
      return NextResponse.json(
        { success: false, error: 'Processor name is required' },
        { status: 400 }
      )
    }

    // Validate the processor name
    const validProcessors = [
      'email_validator',
      'company_web_crawler',
      'company_overview_v2',
      'company_investment_criteria',
      'contact_enrichment_v2',
      'contact_investment_criteria',
      'email_generation',
      'news_html_fetcher',
      'news_enrichment'
    ]

    if (!validProcessors.includes(processorName)) {
      return NextResponse.json(
        { success: false, error: `Invalid processor name. Must be one of: ${validProcessors.join(', ')}` },
        { status: 400 }
      )
    }

    // Get current limits
    const currentLimits = getProcessorLimit(processorName as any)
    
    // Validate new values
    if (limit !== undefined) {
      if (limit <= 0) {
        return NextResponse.json(
          { success: false, error: 'Limit must be greater than 0' },
          { status: 400 }
        )
      }
      if (maxLimit !== undefined && limit > maxLimit) {
        return NextResponse.json(
          { success: false, error: `Limit cannot exceed max limit of ${maxLimit}` },
          { status: 400 }
        )
      }
    }

    if (maxLimit !== undefined) {
      if (maxLimit <= 0) {
        return NextResponse.json(
          { success: false, error: 'Max limit must be greater than 0' },
          { status: 400 }
        )
      }
      if (limit !== undefined && limit > maxLimit) {
        return NextResponse.json(
          { success: false, error: `Limit cannot exceed max limit of ${maxLimit}` },
          { status: 400 }
        )
      }
    }

    if (batchSize !== undefined) {
      if (batchSize <= 0) {
        return NextResponse.json(
          { success: false, error: 'Batch size must be greater than 0' },
          { status: 400 }
        )
      }
    }

    // Note: In a real implementation, you would save these to a database or configuration file
    // For now, we'll just return the validated values
    const updatedLimits = {
      defaultLimit: limit !== undefined ? limit : currentLimits.defaultLimit,
      maxLimit: maxLimit !== undefined ? maxLimit : currentLimits.maxLimit,
      batchSize: batchSize !== undefined ? batchSize : currentLimits.batchSize
    }

    console.log(`[ProcessorLimits API] Updated limits for ${processorName}:`, updatedLimits)

    return NextResponse.json({
      success: true,
      data: {
        processorName,
        limits: updatedLimits,
        message: 'Limits updated successfully (Note: Changes require restart to take effect)'
      }
    })
  } catch (error) {
    console.error('[ProcessorLimits API] Error updating limits:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update processor limits' },
      { status: 500 }
    )
  }
}
