import { NextRequest, NextResponse } from "next/server";
import { bullMQManager } from "@/lib/queue/BullMQManager";

// GET /api/jobs/recent - Get recent uploads with file details
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = parseInt(searchParams.get("offset") || "0");
    const createdBy = searchParams.get("created_by") || undefined;

    // Get recent uploads
    const uploads = await bullMQManager.getRecentUploads(
      limit,
      offset,
      createdBy
    );

    // Get queue stats
    const stats = await bullMQManager.getQueueStats();

    return NextResponse.json({
      success: true,
      uploads,
      stats,
      pagination: {
        limit,
        offset,
        count: uploads.length,
        hasMore: uploads.length === limit,
      },
    });
  } catch (error) {
    console.error("Error getting recent uploads:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to get recent uploads",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
