import { NextRequest, NextResponse } from "next/server";
import { bullMQManager } from "@/lib/queue/BullMQManager";
import { pool } from "@/lib/db";

// GET /api/jobs/status - Check job queue system status
export async function GET(request: NextRequest) {
  try {
    // Check if job queue tables exist
    const tablesCheck = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('jobs', 'job_logs', 'job_files')
    `);

    const existingTables = tablesCheck.rows.map((row) => row.table_name);
    const requiredTables = ["jobs", "job_logs", "job_files"];
    const missingTables = requiredTables.filter(
      (table) => !existingTables.includes(table)
    );

    // Check BullMQ manager health
    const bullMQHealth = await bullMQManager.healthCheck();

    // Get queue stats if tables exist
    let queueStats = null;
    if (missingTables.length === 0) {
      queueStats = await bullMQManager.getQueueStats();
    }

    return NextResponse.json({
      success: true,
      status: {
        tables: {
          required: requiredTables,
          existing: existingTables,
          missing: missingTables,
          ready: missingTables.length === 0,
        },
        bullMQ: bullMQHealth,
        queue: queueStats,
      },
      message:
        missingTables.length > 0
          ? `Job queue tables missing: ${missingTables.join(
              ", "
            )}. Please run the database migration.`
          : "Job queue system is ready",
    });
  } catch (error) {
    console.error("Error checking job queue status:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to check job queue status",
        details: error instanceof Error ? error.message : "Unknown error",
        suggestion: "Please check database connection and run migrations",
      },
      { status: 500 }
    );
  }
}
