import { NextRequest, NextResponse } from "next/server";
import { bullMQManager, JobRecord } from "@/lib/queue/BullMQManager";

// GET /api/jobs - Get recent jobs or specific job status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get("job_id");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = parseInt(searchParams.get("offset") || "0");
    const createdBy = searchParams.get("created_by") || undefined;
    const includeStats = searchParams.get("include_stats") === "true";

    // If specific job ID requested, return that job
    if (jobId) {
      const job = await bullMQManager.getJobStatus(jobId);

      if (!job) {
        return NextResponse.json({ error: "Job not found" }, { status: 404 });
      }

      return NextResponse.json({
        success: true,
        job,
      });
    }

    // Otherwise return list of recent jobs
    const jobs = await bullMQManager.getRecentJobs(limit, offset, createdBy);

    let stats = null;
    if (includeStats) {
      stats = await bullMQManager.getQueueStats();
    }

    return NextResponse.json({
      success: true,
      jobs,
      pagination: {
        limit,
        offset,
        count: jobs.length,
      },
      stats,
    });
  } catch (error) {
    console.error("Error in jobs API:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch jobs",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// POST /api/jobs - Manual job operations (future use)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, jobId } = body;

    switch (action) {
      case "get_stats":
        const stats = await bullMQManager.getQueueStats();
        return NextResponse.json({
          success: true,
          stats,
        });

      case "get_job_details":
        if (!jobId) {
          return NextResponse.json(
            { error: "Job ID required" },
            { status: 400 }
          );
        }

        const job = await bullMQManager.getJobStatus(jobId);
        return NextResponse.json({
          success: true,
          job,
        });

      default:
        return NextResponse.json({ error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    console.error("Error in jobs POST API:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to process job request",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
