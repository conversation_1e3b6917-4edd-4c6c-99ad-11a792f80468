import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';
import { ProcessingFilters } from '@/types/processing';

// Helper function to build date filter conditions
function buildDateFilter(dateRange: string, tableAlias: string = ''): { condition: string; params: any[] } {
  const params: any[] = [];
  let condition = '';
  const prefix = tableAlias ? `${tableAlias}.` : '';
  
  switch (dateRange) {
    case 'today':
      condition = `${prefix}created_at >= CURRENT_DATE`;
      break;
    case 'week':
      condition = `${prefix}created_at >= CURRENT_DATE - INTERVAL '7 days'`;
      break;
    case 'month':
      condition = `${prefix}created_at >= CURRENT_DATE - INTERVAL '30 days'`;
      break;
    case 'quarter':
      condition = `${prefix}created_at >= CURRENT_DATE - INTERVAL '90 days'`;
      break;
    case 'all':
    default:
      // No date filter
      break;
  }
  
  return { condition, params };
}

// Helper function to build contact filters using ProcessingFilters interface
function buildContactFilters(filters: ProcessingFilters, tableAlias: string = ''): { whereClause: string; params: any[] } {
  const conditions: string[] = [];
  const params: any[] = [];
  let paramIndex = 1;
  
  const prefix = tableAlias ? `${tableAlias}.` : '';
  
  // Source filter
  if (filters.source && Array.isArray(filters.source) && filters.source.length > 0) {
    const placeholders = filters.source.map((_, i) => `$${paramIndex + i}`).join(',');
    conditions.push(`${prefix}source IN (${placeholders})`);
    params.push(...filters.source);
    paramIndex += filters.source.length;
  } else if (filters.source && typeof filters.source === 'string' && filters.source !== 'all') {
    conditions.push(`${prefix}source = $${paramIndex}`);
    params.push(filters.source);
    paramIndex++;
  }
  
  // Date range filter
  if (filters.dateRange && filters.dateRange !== 'all') {
    const dateFilter = buildDateFilter(filters.dateRange, tableAlias);
    if (dateFilter.condition) {
      conditions.push(dateFilter.condition);
      params.push(...dateFilter.params);
      paramIndex += dateFilter.params.length;
    }
  }
  
  // Contact enrichment V2 status filter
  if (filters.contact_enrichment_v2_status && filters.contact_enrichment_v2_status !== 'all') {
    conditions.push(`${prefix}contact_enrichment_v2_status = $${paramIndex}`);
    params.push(filters.contact_enrichment_v2_status);
    paramIndex++;
  }

  // Contact email verification status filter
  if (filters.contact_email_verification_status && filters.contact_email_verification_status !== 'all') {
    conditions.push(`${prefix}email_verification_status = $${paramIndex}`);
    params.push(filters.contact_email_verification_status);
    paramIndex++;
  }



  // Email status filter
  if (filters.emailStatus && Array.isArray(filters.emailStatus) && filters.emailStatus.length > 0) {
    const placeholders = filters.emailStatus.map((_, i) => `$${paramIndex + i}`).join(',');
    conditions.push(`${prefix}email_status IN (${placeholders})`);
    params.push(...filters.emailStatus);
    paramIndex += filters.emailStatus.length;
  }
  
  // Job tier filter
  if (filters.jobTier && Array.isArray(filters.jobTier) && filters.jobTier.length > 0) {
    const placeholders = filters.jobTier.map((_, i) => `$${paramIndex + i}`).join(',');
    conditions.push(`${prefix}company_type IN (${placeholders})`);
    params.push(...filters.jobTier);
    paramIndex += filters.jobTier.length;
  }
  
  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
  return { whereClause, params };
}

// Helper function to build company filters using ProcessingFilters interface
function buildCompanyFilters(filters: ProcessingFilters, tableAlias: string = ''): { whereClause: string; params: any[] } {
  const conditions: string[] = [];
  const params: any[] = [];
  let paramIndex = 1;
  
  const prefix = tableAlias ? `${tableAlias}.` : '';
  
  // Source filter (if applicable to companies)
  if (filters.source && Array.isArray(filters.source) && filters.source.length > 0) {
    const placeholders = filters.source.map((_, i) => `$${paramIndex + i}`).join(',');
    conditions.push(`${prefix}source IN (${placeholders})`);
    params.push(...filters.source);
    paramIndex += filters.source.length;
  } else if (filters.source && typeof filters.source === 'string' && filters.source !== 'all') {
    conditions.push(`${prefix}source = $${paramIndex}`);
    params.push(filters.source);
    paramIndex++;
  }
  
  // Company overview V2 status filter
  if (filters.company_overview_v2_status && filters.company_overview_v2_status !== 'all') {
    conditions.push(`${prefix}company_overview_v2_status = $${paramIndex}`);
    params.push(filters.company_overview_v2_status);
    paramIndex++;
  }

  // Date range filter
  if (filters.dateRange && filters.dateRange !== 'all') {
    const dateFilter = buildDateFilter(filters.dateRange, tableAlias);
    if (dateFilter.condition) {
      conditions.push(dateFilter.condition);
      params.push(...dateFilter.params);
      paramIndex += dateFilter.params.length;
    }
  }
  

  
  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
  return { whereClause, params };
}

// Helper function to build news filters (kept separate as requested)
function buildNewsFilters(filters: ProcessingFilters, tableAlias: string = ''): { whereClause: string; params: any[] } {
  const conditions: string[] = [];
  const params: any[] = [];
  let paramIndex = 1;
  
  const prefix = tableAlias ? `${tableAlias}.` : '';
  
  // Date range filter
  if (filters.dateRange && filters.dateRange !== 'all') {
    const dateFilter = buildDateFilter(filters.dateRange, tableAlias);
    if (dateFilter.condition) {
      conditions.push(dateFilter.condition);
      params.push(...dateFilter.params);
      paramIndex += dateFilter.params.length;
    }
  }
  
  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
  return { whereClause, params };
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Build ProcessingFilters from query parameters
    const sourceParam = searchParams.get('source');
    const emailStatusParam = searchParams.get('email_status');
    const jobTierParam = searchParams.get('jobTier');
    const filters: ProcessingFilters = {
      source: sourceParam ? sourceParam.split(',') : [],
      dateRange: searchParams.get('dateRange') || 'all',
      contact_enrichment_v2_status: searchParams.get('contact_enrichment_v2_status') || 'all',
      contact_email_verification_status: searchParams.get('contact_email_verification_status') || 'all',
      company_overview_v2_status: searchParams.get('company_overview_v2_status') || 'all',
      emailStatus: emailStatusParam ? emailStatusParam.split(',') : [],
      jobTier: jobTierParam ? jobTierParam.split(',') : []
    };
    
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Helper to build a date filter for arbitrary start/end
    function buildStartEndDateFilter(tableAlias: string = ''): { condition: string; params: any[] } {
      const prefix = tableAlias ? `${tableAlias}.` : '';
      if (startDate && endDate) {
        return {
          condition: `${prefix}updated_at >= $STARTDATE$ AND ${prefix}updated_at <= $ENDDATE$`,
          params: [startDate, endDate]
        };
      }
      return { condition: '', params: [] };
    }

    // Build filter conditions for unified entities and news
    let unifiedFilters: { whereClause: string; params: any[] };
    let companyFilters: { whereClause: string; params: any[] };
    let newsFilters: { whereClause: string; params: any[] };
    if (startDate && endDate) {
      // Use start/end date filtering
      const unifiedDateFilter = buildStartEndDateFilter('c');
      const companyDateFilter = buildStartEndDateFilter('co');
      const newsDateFilter = buildStartEndDateFilter('n');
      
      // Compose with other filters
      function injectDateFilter(base: ReturnType<typeof buildContactFilters>, dateFilter: { condition: string; params: any[] }) {
        let whereClause = base.whereClause;
        const params = [...base.params];
        if (dateFilter.condition) {
          if (whereClause) {
            whereClause += ` AND ${dateFilter.condition}`;
          } else {
            whereClause = `WHERE ${dateFilter.condition}`;
          }
          params.push(...dateFilter.params);
        }
        // Replace $STARTDATE$/$ENDDATE$ with correct param numbers
        const paramIdx = params.length - dateFilter.params.length + 1;
        whereClause = whereClause.replace('$STARTDATE$', `$${paramIdx}`);
        whereClause = whereClause.replace('$ENDDATE$', `$${paramIdx + 1}`);
        return { whereClause, params };
      }
      unifiedFilters = injectDateFilter(buildContactFilters(filters, 'c'), unifiedDateFilter);
      companyFilters = injectDateFilter(buildCompanyFilters(filters, 'co'), companyDateFilter);
      newsFilters = injectDateFilter(buildNewsFilters(filters, 'n'), newsDateFilter);
    } else {
      // Use legacy dateRange logic
      unifiedFilters = buildContactFilters(filters, 'c');
      companyFilters = buildCompanyFilters(filters, 'co');
      newsFilters = buildNewsFilters(filters, 'n');
    }
    
    // Execute queries in parallel
    const [
      contactStats,
      companyStats,
      newsStats,
      contactErrors,
      companyErrors,
      newsErrors
    ] = await Promise.all([
      // Contact Statistics - separate query to avoid JOIN issues with email status filters
      pool.query(`
        SELECT 
          COUNT(distinct c.contact_id) as total_contacts,
          -- Email Validation Stage
          COUNT(distinct c.contact_id) as email_validation_total,
          COUNT(distinct CASE WHEN email IS NOT NULL AND email != ' ' AND email != '' AND (COALESCE(email_verification_status, 'pending') = 'pending') THEN c.contact_id END) as email_validation_pending,
          COUNT(distinct CASE WHEN email_verification_status = 'running' THEN c.contact_id END) as email_validation_running,
          COUNT(distinct CASE WHEN email_verification_status = 'completed' THEN c.contact_id END) as email_validation_completed,
          COUNT(distinct CASE WHEN email_verification_status = 'failed' THEN c.contact_id END) as email_validation_failed,
          COUNT(distinct CASE WHEN email_verification_status = 'error' THEN c.contact_id END) as email_validation_error,
          
          -- Contact Enrichment V2 Stage
          COUNT(distinct CASE WHEN email_verification_status = 'completed' AND COALESCE(contact_enrichment_v2_status, 'pending') = 'pending' THEN c.contact_id END) as contact_enrichment_v2_pending,
          COUNT(distinct CASE WHEN email_verification_status = 'completed' AND contact_enrichment_v2_status = 'running' THEN c.contact_id END) as contact_enrichment_v2_running,
          COUNT(distinct CASE WHEN contact_enrichment_v2_status = 'completed' THEN c.contact_id END) as contact_enrichment_v2_completed,
          COUNT(distinct CASE WHEN contact_enrichment_v2_status = 'failed' THEN c.contact_id END) as contact_enrichment_v2_failed,
          COUNT(distinct CASE WHEN contact_enrichment_v2_status = 'error' THEN c.contact_id END) as contact_enrichment_v2_error,
          
          -- Contact Investment Criteria Stage
          COUNT(distinct CASE WHEN contact_enrichment_v2_status = 'completed' AND COALESCE(contact_investment_criteria_status, 'pending') = 'pending' THEN c.contact_id END) as contact_investment_criteria_pending,
          COUNT(distinct CASE WHEN contact_enrichment_v2_status = 'completed' AND contact_investment_criteria_status = 'running' THEN c.contact_id END) as contact_investment_criteria_running,
          COUNT(distinct CASE WHEN contact_enrichment_v2_status = 'completed' AND contact_investment_criteria_status = 'completed' THEN c.contact_id END) as contact_investment_criteria_completed,
          COUNT(distinct CASE WHEN contact_enrichment_v2_status = 'completed' AND contact_investment_criteria_status = 'failed' THEN c.contact_id END) as contact_investment_criteria_failed,
          COUNT(distinct CASE WHEN contact_enrichment_v2_status = 'completed' AND contact_investment_criteria_status = 'error' THEN c.contact_id END) as contact_investment_criteria_error,
          
          -- Email Generation Stage
          COUNT(distinct CASE WHEN contact_enrichment_v2_status = 'completed' AND COALESCE(email_generation_status, 'pending') = 'pending' THEN c.contact_id END) as email_generation_pending,
          COUNT(distinct CASE WHEN contact_enrichment_v2_status = 'completed' AND email_generation_status = 'running' THEN c.contact_id END) as email_generation_running,
          COUNT(distinct CASE WHEN contact_enrichment_v2_status = 'completed' AND email_generation_status = 'completed' THEN c.contact_id END) as email_generation_completed,
          COUNT(distinct CASE WHEN contact_enrichment_v2_status = 'completed' AND email_generation_status = 'failed' THEN c.contact_id END) as email_generation_failed,
          COUNT(distinct CASE WHEN contact_enrichment_v2_status = 'completed' AND email_generation_status = 'error' THEN c.contact_id END) as email_generation_error
        FROM contacts c
        ${unifiedFilters.whereClause}
      `, unifiedFilters.params),
      
      // Company Statistics - unified query to respect contact filters
      pool.query(`
        SELECT 
          COUNT(distinct co.company_id) as total_companies,
          -- Web Crawler Stage
          COUNT(distinct CASE WHEN COALESCE(co.website_scraping_status, 'pending') = 'pending' AND co.company_website IS NOT NULL THEN co.company_id END) as web_crawler_pending,
          COUNT(distinct CASE WHEN co.website_scraping_status = 'running' THEN co.company_id END) as web_crawler_running,
          COUNT(distinct CASE WHEN co.website_scraping_status = 'completed' THEN co.company_id END) as web_crawler_completed,
          COUNT(distinct CASE WHEN co.website_scraping_status = 'failed' THEN co.company_id END) as web_crawler_failed,
          COUNT(distinct CASE WHEN co.website_scraping_status = 'error' THEN co.company_id END) as web_crawler_error,
          

          
          -- Company Overview V2 Stage
          COUNT(distinct CASE WHEN COALESCE(co.overview_v2_status, 'pending') = 'pending' AND co.website_scraping_status = 'completed' THEN co.company_id END) as company_overview_v2_pending,
          COUNT(distinct CASE WHEN co.website_scraping_status = 'completed' AND co.overview_v2_status = 'running' THEN co.company_id END) as company_overview_v2_running,
          COUNT(distinct CASE WHEN co.overview_v2_status = 'completed' THEN co.company_id END) as company_overview_v2_completed,
          COUNT(distinct CASE WHEN co.overview_v2_status = 'failed' THEN co.company_id END) as company_overview_v2_failed,
          COUNT(distinct CASE WHEN co.overview_v2_status = 'error' THEN co.company_id END) as company_overview_v2_error,
          
          -- Company Investment Criteria Stage
          COUNT(distinct CASE WHEN COALESCE(co.investment_criteria_status, 'pending') = 'pending' AND co.overview_v2_status = 'completed' THEN co.company_id END) as company_investment_criteria_pending,
          COUNT(distinct CASE WHEN co.overview_v2_status = 'completed' AND co.investment_criteria_status = 'running' THEN co.company_id END) as company_investment_criteria_running,
          COUNT(distinct CASE WHEN co.overview_v2_status = 'completed' AND co.investment_criteria_status = 'completed' THEN co.company_id END) as company_investment_criteria_completed,
          COUNT(distinct CASE WHEN co.overview_v2_status = 'completed' AND co.investment_criteria_status = 'failed' THEN co.company_id END) as company_investment_criteria_failed,
          COUNT(distinct CASE WHEN co.overview_v2_status = 'completed' AND co.investment_criteria_status = 'error' THEN co.company_id END) as company_investment_criteria_error
        FROM companies co
        INNER JOIN contacts c ON co.company_id = c.company_id
        ${unifiedFilters.whereClause}
      `, unifiedFilters.params),
      
      // News Statistics - Format to match NewsStatsRaw interface
      pool.query(`
        SELECT 
          COUNT(*) as total_news,
          -- Fetch Stage (matching expected field names)
          COUNT(CASE WHEN fetched = false OR fetched IS NULL THEN 1 END) as fetch_pending,
          COUNT(CASE WHEN fetch_status = 'running' THEN 1 END) as fetch_running,
          COUNT(CASE WHEN fetched = true OR fetch_status = 'completed' THEN 1 END) as fetch_completed,
          COUNT(CASE WHEN fetched = true AND fetch_status = 'failed' THEN 1 END) as fetch_failed,
          COUNT(CASE WHEN fetched = true AND fetch_status = 'error' THEN 1 END) as fetch_error,
          
          -- Enrichment Stage (matching expected field names)
          COUNT(CASE WHEN fetched = true AND enriched = false AND news_text IS NOT NULL and length(news_text) > 10 THEN 1 END) as enrichment_pending,
          COUNT(CASE WHEN enriched = true THEN 1 END) as enrichment_completed,
          COUNT(CASE WHEN enrichment_status = 'failed' THEN 1 END) as enrichment_failed,
          COUNT(CASE WHEN enrichment_status = 'error' THEN 1 END) as enrichment_error
        FROM news n
        WHERE (bad_url = false OR bad_url IS NULL)
        ${newsFilters.whereClause ? newsFilters.whereClause.replace('WHERE', 'AND') : ''}
      `, newsFilters.params),
      
      // Contact Errors - Recent errors from all processing stages
      pool.query(`
        SELECT 
          'contact' as entity_type,
          c.contact_id as entity_id,
          CASE 
            WHEN c.email_verification_status = 'failed' THEN 'email_validation'
            WHEN c.contact_enrichment_v2_status = 'failed' THEN 'contact_enrichment_v2'
            WHEN c.contact_investment_criteria_status = 'failed' THEN 'contact_investment_criteria'
            WHEN c.email_generation_status = 'failed' THEN 'email_generation'
            ELSE 'unknown'
          END as stage,
          CASE 
            WHEN c.email_verification_status = 'failed' THEN COALESCE(c.email_verification_error, 'Email verification failed')

            WHEN c.contact_enrichment_v2_status = 'failed' THEN COALESCE(c.contact_enrichment_v2_error, 'Contact enrichment V2 failed')
            WHEN c.contact_investment_criteria_status = 'failed' THEN COALESCE(c.contact_investment_criteria_error, 'Contact investment criteria failed')
            WHEN c.email_generation_status = 'failed' THEN COALESCE(c.email_generation_error, 'Email generation failed')
            ELSE 'Unknown error'
          END as error_message,
          CASE 
            WHEN c.email_verification_status = 'failed' THEN 'Email Verification'

            WHEN c.contact_enrichment_v2_status = 'failed' THEN 'Contact Enrichment V2'
            WHEN c.contact_investment_criteria_status = 'failed' THEN 'Contact Investment Criteria'
            WHEN c.email_generation_status = 'failed' THEN 'Email Generation'
            ELSE 'Unknown Stage'
          END as error_category,
          CASE 
            WHEN c.email_verification_status = 'failed' THEN c.email_verification_date

            WHEN c.contact_enrichment_v2_status = 'failed' THEN c.contact_enrichment_v2_date
            WHEN c.contact_investment_criteria_status = 'failed' THEN c.contact_investment_criteria_date
            WHEN c.email_generation_status = 'failed' THEN c.email_generation_date
            ELSE c.updated_at
          END as occurred_at,
          COALESCE(c.processing_error_count, 0) as retry_count
        FROM contacts c
        WHERE (c.email_verification_status = 'failed' OR c.contact_enrichment_v2_status = 'failed' OR c.contact_investment_criteria_status = 'failed' OR c.email_generation_status = 'failed')
        ${unifiedFilters.whereClause ? unifiedFilters.whereClause.replace('WHERE', 'AND') : ''}
        ORDER BY occurred_at DESC
        LIMIT 50
      `, unifiedFilters.params),
      
        // Company Errors - Recent errors from all processing stages
        pool.query(`
          SELECT DISTINCT
            'company' as entity_type,
          co.company_id as entity_id,
          CASE 
            WHEN co.website_scraping_status = 'failed' THEN 'website_scraping'

            WHEN co.overview_v2_status = 'failed' THEN 'company_overview_v2'
            WHEN co.investment_criteria_status = 'failed' THEN 'company_investment_criteria'
            ELSE 'unknown'
          END as stage,
          CASE 
            WHEN co.website_scraping_status = 'failed' THEN COALESCE(co.website_scraping_error, 'Website scraping failed')

            WHEN co.overview_v2_status = 'failed' THEN COALESCE(co.overview_v2_error, 'Company overview V2 failed')
            WHEN co.investment_criteria_status = 'failed' THEN COALESCE(co.investment_criteria_error, 'Company investment criteria failed')
            ELSE 'Unknown error'
          END as error_message,
          CASE 
            WHEN co.website_scraping_status = 'failed' THEN 'Website Scraping'

            WHEN co.overview_v2_status = 'failed' THEN 'Company Overview V2'
            WHEN co.investment_criteria_status = 'failed' THEN 'Company Investment Criteria'
            ELSE 'Unknown Stage'
          END as error_category,
          co.updated_at as occurred_at,
          COALESCE(co.processing_error_count, 0) as retry_count
        FROM companies co
        INNER JOIN contacts c ON co.company_id = c.company_id
        WHERE (co.website_scraping_status = 'failed' OR co.overview_v2_status = 'failed' OR co.investment_criteria_status = 'failed')
        ${unifiedFilters.whereClause ? unifiedFilters.whereClause.replace('WHERE', 'AND') : ''}
        ORDER BY occurred_at DESC
        LIMIT 50
      `, unifiedFilters.params),
      
      // News Errors - Recent errors from all processing stages
      pool.query(`
        SELECT 
          'news' as entity_type,
          n.id as entity_id,
          CASE 
            WHEN n.fetch_error IS NOT NULL THEN 'news_fetch'
            WHEN n.enrichment_status = 'failed' THEN 'news_enrichment'
            ELSE 'unknown'
          END as stage,
          CASE 
            WHEN n.fetch_error IS NOT NULL THEN n.fetch_error
            WHEN n.enrichment_status = 'failed' THEN COALESCE(n.enrichment_error, 'News enrichment failed')
            ELSE 'Unknown error'
          END as error_message,
          CASE 
            WHEN n.fetch_error IS NOT NULL THEN 'News Fetch'
            WHEN n.enrichment_status = 'failed' THEN 'News Enrichment'
            ELSE 'Unknown Stage'
          END as error_category,
          n.updated_at as occurred_at,
          1 as retry_count
        FROM news n
        WHERE (n.fetch_error IS NOT NULL OR n.enrichment_status = 'failed')
        AND (bad_url = false OR bad_url IS NULL)
        ${newsFilters.whereClause ? newsFilters.whereClause.replace('WHERE', 'AND') : ''}
        ORDER BY occurred_at DESC
        LIMIT 50
      `, newsFilters.params)
    ]);
    
    // Format response to match ProcessingStatsRaw interface exactly
    const response = {
      stats: {
        contacts: {
          total_contacts: contactStats.rows[0].total_contacts,
          email_validation_total: Number(contactStats.rows[0].email_validation_pending) + Number(contactStats.rows[0].email_validation_running) + Number(contactStats.rows[0].email_validation_completed) + Number(contactStats.rows[0].email_validation_failed) + Number(contactStats.rows[0].email_validation_error),
          email_validation_pending: contactStats.rows[0].email_validation_pending,
          email_validation_running: contactStats.rows[0].email_validation_running,
          email_validation_completed: contactStats.rows[0].email_validation_completed,
          email_validation_failed: contactStats.rows[0].email_validation_failed,
          email_validation_error: contactStats.rows[0].email_validation_error,
          contact_enrichment_v2_total: Number(contactStats.rows[0].contact_enrichment_v2_pending) + Number(contactStats.rows[0].contact_enrichment_v2_running) + Number(contactStats.rows[0].contact_enrichment_v2_completed) + Number(contactStats.rows[0].contact_enrichment_v2_failed) + Number(contactStats.rows[0].contact_enrichment_v2_error),
          contact_enrichment_v2_pending: contactStats.rows[0].contact_enrichment_v2_pending,
          contact_enrichment_v2_running: contactStats.rows[0].contact_enrichment_v2_running,
          contact_enrichment_v2_completed: contactStats.rows[0].contact_enrichment_v2_completed,
          contact_enrichment_v2_failed: contactStats.rows[0].contact_enrichment_v2_failed,
          contact_enrichment_v2_error: contactStats.rows[0].contact_enrichment_v2_error,
          contact_investment_criteria_total: Number(contactStats.rows[0].contact_investment_criteria_pending) + Number(contactStats.rows[0].contact_investment_criteria_running) + Number(contactStats.rows[0].contact_investment_criteria_completed) + Number(contactStats.rows[0].contact_investment_criteria_failed) + Number(contactStats.rows[0].contact_investment_criteria_error),
          contact_investment_criteria_pending: contactStats.rows[0].contact_investment_criteria_pending,
          contact_investment_criteria_running: contactStats.rows[0].contact_investment_criteria_running,
          contact_investment_criteria_completed: contactStats.rows[0].contact_investment_criteria_completed,
          contact_investment_criteria_failed: contactStats.rows[0].contact_investment_criteria_failed,
          contact_investment_criteria_error: contactStats.rows[0].contact_investment_criteria_error,
          email_generation_total: Number(contactStats.rows[0].email_generation_pending) + Number(contactStats.rows[0].email_generation_running) + Number(contactStats.rows[0].email_generation_completed) + Number(contactStats.rows[0].email_generation_failed) + Number(contactStats.rows[0].email_generation_error),
          email_generation_pending: contactStats.rows[0].email_generation_pending,
          email_generation_running: contactStats.rows[0].email_generation_running,
          email_generation_completed: contactStats.rows[0].email_generation_completed,
          email_generation_failed: contactStats.rows[0].email_generation_failed,
          email_generation_error: contactStats.rows[0].email_generation_error
        },
        companies: {
          total_companies: companyStats.rows[0].total_companies,

          company_overview_v2_total: Number(companyStats.rows[0].company_overview_v2_pending) + Number(companyStats.rows[0].company_overview_v2_running) + Number(companyStats.rows[0].company_overview_v2_completed) + Number(companyStats.rows[0].company_overview_v2_failed) + Number(companyStats.rows[0].company_overview_v2_error),
          company_overview_v2_pending: companyStats.rows[0].company_overview_v2_pending,
          company_overview_v2_running: companyStats.rows[0].company_overview_v2_running,
          company_overview_v2_completed: companyStats.rows[0].company_overview_v2_completed,
          company_overview_v2_failed: companyStats.rows[0].company_overview_v2_failed,
          company_overview_v2_error: companyStats.rows[0].company_overview_v2_error,
          company_investment_criteria_total: Number(companyStats.rows[0].company_investment_criteria_pending) + Number(companyStats.rows[0].company_investment_criteria_running) + Number(companyStats.rows[0].company_investment_criteria_completed) + Number(companyStats.rows[0].company_investment_criteria_failed) + Number(companyStats.rows[0].company_investment_criteria_error),
          company_investment_criteria_pending: companyStats.rows[0].company_investment_criteria_pending,
          company_investment_criteria_running: companyStats.rows[0].company_investment_criteria_running,
          company_investment_criteria_completed: companyStats.rows[0].company_investment_criteria_completed,
          company_investment_criteria_failed: companyStats.rows[0].company_investment_criteria_failed,
          company_investment_criteria_error: companyStats.rows[0].company_investment_criteria_error,
          website_scraping_total: Number(companyStats.rows[0].web_crawler_pending) + Number(companyStats.rows[0].web_crawler_running) + Number(companyStats.rows[0].web_crawler_completed) + Number(companyStats.rows[0].web_crawler_failed) + Number(companyStats.rows[0].web_crawler_error),
          website_scraping_pending: companyStats.rows[0].web_crawler_pending,
          website_scraping_running: companyStats.rows[0].web_crawler_running,
          website_scraping_completed: companyStats.rows[0].web_crawler_completed,
          website_scraping_failed: companyStats.rows[0].web_crawler_failed,
          website_scraping_error: companyStats.rows[0].web_crawler_error
        },
        news: {
          total_news: newsStats.rows[0].total_news,
          fetch_total: Number(newsStats.rows[0].fetch_pending) + Number(newsStats.rows[0].fetch_running) + Number(newsStats.rows[0].fetch_completed) + Number(newsStats.rows[0].fetch_failed) + Number(newsStats.rows[0].fetch_error),
          fetch_pending: newsStats.rows[0].fetch_pending,
          fetch_running: newsStats.rows[0].fetch_running,
          fetch_completed: newsStats.rows[0].fetch_completed,
          fetch_failed: newsStats.rows[0].fetch_failed,
          fetch_error: newsStats.rows[0].fetch_error,
          enrichment_total: Number(newsStats.rows[0].enrichment_pending) + Number(newsStats.rows[0].enrichment_completed) + Number(newsStats.rows[0].enrichment_failed) + Number(newsStats.rows[0].enrichment_error),
          enrichment_pending: newsStats.rows[0].enrichment_pending,
          enrichment_completed: newsStats.rows[0].enrichment_completed,
          enrichment_failed: newsStats.rows[0].enrichment_failed,
          enrichment_error: newsStats.rows[0].enrichment_error
        }
      },
      errors: {
        contacts: contactErrors.rows.filter(e => e.entity_type === 'contact'),
        companies: companyErrors.rows.filter(e => e.entity_type === 'company'),
        news: newsErrors.rows
      },
      metadata: {
        timestamp: new Date().toISOString(),
        filters: filters,
        unified_view: {
          total_entities: Number(contactStats.rows[0].total_contacts) + Number(companyStats.rows[0].total_companies),
          total_contacts: contactStats.rows[0].total_contacts,
          total_companies: companyStats.rows[0].total_companies
        }
      }
    };
    
    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching processing stats:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 