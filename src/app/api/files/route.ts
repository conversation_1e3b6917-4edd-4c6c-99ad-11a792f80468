import { NextRequest, NextResponse } from "next/server";
import { FileManager } from "@/lib/utils/fileManager";
import {
  FileUploadRequest,
  FileRelationshipRequest,
  FileSearchFilters,
  FileUploadResponse,
  FileListResponse,
  FileProcessingOptions,
  FileRelationship,
} from "@/types/file";

// GET /api/files - Search and list files
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Extract search filters
    const filters: FileSearchFilters = {};

    if (searchParams.get("file_name")) {
      filters.file_name = searchParams.get("file_name")!;
    }

    if (searchParams.get("original_name")) {
      filters.original_name = searchParams.get("original_name")!;
    }

    if (searchParams.get("mime_type")) {
      filters.mime_type = searchParams.get("mime_type")!;
    }

    if (searchParams.get("uploaded_by")) {
      filters.uploaded_by = searchParams.get("uploaded_by")!;
    }

    if (searchParams.get("access_level")) {
      filters.access_level = searchParams.get("access_level") as
        | "private"
        | "team"
        | "public";
    }

    if (searchParams.get("uploaded_after")) {
      filters.uploaded_after = searchParams.get("uploaded_after")!;
    }

    if (searchParams.get("uploaded_before")) {
      filters.uploaded_before = searchParams.get("uploaded_before")!;
    }

    if (searchParams.get("file_size_min")) {
      filters.file_size_min = parseInt(searchParams.get("file_size_min")!);
    }

    if (searchParams.get("file_size_max")) {
      filters.file_size_max = parseInt(searchParams.get("file_size_max")!);
    }

    if (searchParams.get("has_relationships") === "true") {
      filters.has_relationships = true;
    }

    if (searchParams.get("target_table_name")) {
      filters.target_table_name = searchParams.get("target_table_name")!;
    }

    // Pagination
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("page_size") || "20");

    // Search files
    const result = await FileManager.searchFiles(filters, page, pageSize);

    const response: FileListResponse = {
      success: true,
      data: result,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error searching files:", error);
    return NextResponse.json(
      { success: false, message: "Failed to search files" },
      { status: 500 }
    );
  }
}

// POST /api/files - Upload a new file
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json(
        { success: false, message: "No file provided" },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const fileBuffer = Buffer.from(arrayBuffer);

    // Extract upload request data
    const uploadRequest: FileUploadRequest = {
      original_name: file.name,
      title: (formData.get("title") as string) || undefined,
      description: (formData.get("description") as string) || undefined,
      uploaded_by: (formData.get("uploaded_by") as string) || undefined,
      upload_source: (formData.get("upload_source") as string) || "web",
      is_public: formData.get("is_public") === "true",
      access_level:
        (formData.get("access_level") as "private" | "team" | "public") ||
        "private",
      tags: formData.get("tags")
        ? JSON.parse(formData.get("tags") as string)
        : undefined,
      metadata: formData.get("metadata")
        ? JSON.parse(formData.get("metadata") as string)
        : undefined,
      custom_fields: formData.get("custom_fields")
        ? JSON.parse(formData.get("custom_fields") as string)
        : undefined,
    };

    // Extract processing options
    const options: FileProcessingOptions = {
      validate_table_column: formData.get("validate_table_column") === "true",
      allow_duplicates: formData.get("allow_duplicates") === "true",
      auto_generate_title: formData.get("auto_generate_title") === "true",
      preserve_original_name: formData.get("preserve_original_name") === "true",
    };

    // Upload file
    const { file: uploadedFile, isDuplicate } = await FileManager.uploadFile(
      fileBuffer,
      uploadRequest,
      options
    );

    // Check if relationship data is provided
    const targetTableName = formData.get("target_table_name") as string;
    const targetColumnName = formData.get("target_column_name") as string;
    const targetRowId = formData.get("target_row_id") as string;

    let relationship: FileRelationship | undefined = undefined;

    if (targetTableName && targetColumnName && targetRowId) {
      const relationshipRequest: FileRelationshipRequest = {
        target_table_name: targetTableName,
        target_column_name: targetColumnName,
        target_row_id: targetRowId,
        relationship_type:
          (formData.get("relationship_type") as string) || "attachment",
        relationship_title:
          (formData.get("relationship_title") as string) || undefined,
        relationship_notes:
          (formData.get("relationship_notes") as string) || undefined,
        display_order: formData.get("display_order")
          ? parseInt(formData.get("display_order") as string)
          : undefined,
        is_primary: formData.get("is_primary") === "true",
      };

      relationship = await FileManager.createFileRelationship(
        uploadedFile.file_id,
        relationshipRequest,
        options
      );
    }

    const response: FileUploadResponse = {
      success: true,
      file: uploadedFile,
      relationship,
      is_duplicate: isDuplicate,
      message: isDuplicate
        ? "File already exists"
        : "File uploaded successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error uploading file:", error);
    return NextResponse.json(
      { success: false, message: "Failed to upload file" },
      { status: 500 }
    );
  }
}
