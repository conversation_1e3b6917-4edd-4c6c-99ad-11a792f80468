import { NextRequest, NextResponse } from "next/server";
import { FileManager } from "@/lib/utils/fileManager";

// GET /api/files/entity/[type]/[id] - Get files for a specific entity
export async function GET(
  request: NextRequest,
  { params }: { params: { type: string; id: string } }
) {
  try {
    const entityType = params.type;
    const entityId = parseInt(params.id);

    if (isNaN(entityId)) {
      return NextResponse.json(
        { success: false, error: "Invalid entity ID" },
        { status: 400 }
      );
    }

    const files = await FileManager.getEntityFiles(entityType, entityId);

    return NextResponse.json({
      success: true,
      entity_type: entityType,
      entity_id: entityId,
      files,
      count: files.length,
    });
  } catch (error) {
    console.error("Error fetching entity files:", error);
    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch entity files",
      },
      { status: 500 }
    );
  }
}

// POST /api/files/entity/[type]/[id] - Upload file for a specific entity
export async function POST(
  request: NextRequest,
  { params }: { params: { type: string; id: string } }
) {
  try {
    const entityType = params.type;
    const entityId = parseInt(params.id);

    if (isNaN(entityId)) {
      return NextResponse.json(
        { success: false, error: "Invalid entity ID" },
        { status: 400 }
      );
    }

    const formData = await request.formData();

    // Extract file
    const file = formData.get("file") as File;
    if (!file) {
      return NextResponse.json(
        { success: false, error: "No file provided" },
        { status: 400 }
      );
    }

    // Extract metadata
    const title = (formData.get("title") as string) || undefined;
    const description = (formData.get("description") as string) || undefined;
    const tags = formData.get("tags") as string;
    const metadata = formData.get("metadata") as string;
    const custom_fields = formData.get("custom_fields") as string;
    const is_public = formData.get("is_public") === "true";
    const access_level = (formData.get("access_level") as string) || "private";
    const uploaded_by = (formData.get("uploaded_by") as string) || undefined;
    const upload_source = (formData.get("upload_source") as string) || "web";
    const relationship_type =
      (formData.get("relationship_type") as string) || "attachment";
    const is_primary = formData.get("is_primary") === "true";

    // Parse JSON fields
    let parsedTags: string[] | undefined;
    let parsedMetadata: Record<string, any> | undefined;
    let parsedCustomFields: Record<string, any> | undefined;

    try {
      if (tags) parsedTags = JSON.parse(tags);
      if (metadata) parsedMetadata = JSON.parse(metadata);
      if (custom_fields) parsedCustomFields = JSON.parse(custom_fields);
    } catch (parseError) {
      return NextResponse.json(
        { success: false, error: "Invalid JSON in metadata fields" },
        { status: 400 }
      );
    }

    // Upload file for entity
    const result = await FileManager.uploadFileForEntity(
      {
        file,
        title,
        description,
        tags: parsedTags,
        metadata: parsedMetadata,
        custom_fields: parsedCustomFields,
        is_public,
        access_level: access_level as any,
        uploaded_by,
        upload_source,
      },
      entityType,
      entityId,
      relationship_type as any,
      is_primary
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error uploading file for entity:", error);
    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to upload file for entity",
      },
      { status: 500 }
    );
  }
}
