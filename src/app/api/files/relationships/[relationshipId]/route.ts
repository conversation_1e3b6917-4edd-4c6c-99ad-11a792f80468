import { NextRequest, NextResponse } from "next/server";
import { FileManager } from "@/lib/utils/fileManager";

// PUT /api/files/relationships/[relationshipId] - Update a file relationship
export async function PUT(
  request: NextRequest,
  { params }: { params: { relationshipId: string } }
) {
  try {
    const { relationshipId } = params;
    const body = await request.json();

    // Update the relationship using FileManager
    const updatedRelationship = await FileManager.updateFileRelationship(
      relationshipId,
      body
    );

    if (!updatedRelationship) {
      return NextResponse.json(
        {
          success: false,
          message: "Relationship not found or no changes made",
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      relationship: updatedRelationship,
      message: "Relationship updated successfully",
    });
  } catch (error) {
    console.error("Error updating file relationship:", error);
    return NextResponse.json(
      { success: false, message: "Failed to update file relationship" },
      { status: 500 }
    );
  }
}

// DELETE /api/files/relationships/[relationshipId] - Delete a file relationship
export async function DELETE(
  request: NextRequest,
  { params }: { params: { relationshipId: string } }
) {
  try {
    const { relationshipId } = params;

    // Delete the relationship
    const deleted = await FileManager.deleteFileRelationship(relationshipId);

    if (!deleted) {
      return NextResponse.json(
        { success: false, message: "Relationship not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "File relationship deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting file relationship:", error);
    return NextResponse.json(
      { success: false, message: "Failed to delete file relationship" },
      { status: 500 }
    );
  }
}
