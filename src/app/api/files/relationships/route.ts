import { NextRequest, NextResponse } from "next/server";
import { FileManager } from "@/lib/utils/fileManager";
import { FileRelationshipRequest, FileProcessingOptions } from "@/types/file";

// POST /api/files/relationships - Create a new file relationship
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const {
      file_id,
      target_table_name,
      target_column_name,
      target_row_id,
      relationship_type = "attachment",
      relationship_title,
      relationship_notes,
      display_order = 0,
      is_primary = false,
      validate_table_column = false,
      allow_duplicates = false,
    } = body;

    // Validate required fields
    if (
      !file_id ||
      !target_table_name ||
      !target_column_name ||
      !target_row_id
    ) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Missing required fields: file_id, target_table_name, target_column_name, target_row_id",
        },
        { status: 400 }
      );
    }

    // Validate file exists
    const file = await FileManager.getFileById(file_id);
    if (!file) {
      return NextResponse.json(
        { success: false, message: "File not found" },
        { status: 404 }
      );
    }

    // Create relationship request
    const relationshipRequest: FileRelationshipRequest = {
      target_table_name,
      target_column_name,
      target_row_id,
      relationship_type,
      relationship_title,
      relationship_notes,
      display_order,
      is_primary,
    };

    // Processing options
    const options: FileProcessingOptions = {
      validate_table_column,
      allow_duplicates,
    };

    // Create relationship
    const relationship = await FileManager.createFileRelationship(
      file_id,
      relationshipRequest,
      options
    );

    return NextResponse.json({
      success: true,
      relationship,
      message: "File relationship created successfully",
    });
  } catch (error) {
    console.error("Error creating file relationship:", error);
    return NextResponse.json(
      { success: false, message: "Failed to create file relationship" },
      { status: 500 }
    );
  }
}

// GET /api/files/relationships - Get relationships with filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const fileId = searchParams.get("file_id");
    const targetTableName = searchParams.get("target_table_name");
    const targetColumnName = searchParams.get("target_column_name");
    const targetRowId = searchParams.get("target_row_id");
    const relationshipType = searchParams.get("relationship_type");

    if (fileId) {
      // Get relationships for a specific file
      const relationships = await FileManager.getFileRelationships({
        file_id: fileId,
      });
      return NextResponse.json({
        success: true,
        relationships,
      });
    }

    if (targetTableName && targetColumnName && targetRowId) {
      // Get files for a specific table/column/row
      const files = await FileManager.getTableFiles({
        table_name: targetTableName,
        column_name: targetColumnName,
        row_id: targetRowId,
      });

      return NextResponse.json({
        success: true,
        files,
      });
    }

    return NextResponse.json(
      {
        success: false,
        message: "Must provide file_id or target table/column/row parameters",
      },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error getting file relationships:", error);
    return NextResponse.json(
      { success: false, message: "Failed to get file relationships" },
      { status: 500 }
    );
  }
}
