import { NextRequest, NextResponse } from "next/server";
import { FileManager } from "@/lib/utils/fileManager";
import { FileDetailResponse, FileDeleteResponse } from "@/types/file";

// GET /api/files/[fileId] - Get file details and relationships
export async function GET(
  request: NextRequest,
  { params }: { params: { fileId: string } }
) {
  try {
    const { fileId } = params;

    // Get file details
    const file = await FileManager.getFileById(fileId);

    if (!file) {
      return NextResponse.json(
        { success: false, message: "File not found" },
        { status: 404 }
      );
    }

    // Get file relationships
    const relationships = await FileManager.getFileRelationships({
      file_id: fileId,
    });

    const response: FileDetailResponse = {
      success: true,
      file,
      relationships,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error getting file details:", error);
    return NextResponse.json(
      { success: false, message: "Failed to get file details" },
      { status: 500 }
    );
  }
}

// PUT /api/files/[fileId] - Update file metadata
export async function PUT(
  request: NextRequest,
  { params }: { params: { fileId: string } }
) {
  try {
    const { fileId } = params;
    const body = await request.json();

    // Validate file exists
    const existingFile = await FileManager.getFileById(fileId);
    if (!existingFile) {
      return NextResponse.json(
        { success: false, message: "File not found" },
        { status: 404 }
      );
    }

    // Update file
    const updatedFile = await FileManager.updateFile(fileId, body);

    if (!updatedFile) {
      return NextResponse.json(
        { success: false, message: "Failed to update file" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      file: updatedFile,
      message: "File updated successfully",
    });
  } catch (error) {
    console.error("Error updating file:", error);
    return NextResponse.json(
      { success: false, message: "Failed to update file" },
      { status: 500 }
    );
  }
}

// DELETE /api/files/[fileId] - Delete file and all relationships
export async function DELETE(
  request: NextRequest,
  { params }: { params: { fileId: string } }
) {
  try {
    const { fileId } = params;

    // Validate file exists
    const existingFile = await FileManager.getFileById(fileId);
    if (!existingFile) {
      return NextResponse.json(
        { success: false, message: "File not found" },
        { status: 404 }
      );
    }

    // Delete file (relationships will be deleted via CASCADE)
    const deleted = await FileManager.deleteFile(fileId);

    if (!deleted) {
      return NextResponse.json(
        { success: false, message: "Failed to delete file" },
        { status: 500 }
      );
    }

    const response: FileDeleteResponse = {
      success: true,
      message: "File deleted successfully",
      deleted_count: 1,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error deleting file:", error);
    return NextResponse.json(
      { success: false, message: "Failed to delete file" },
      { status: 500 }
    );
  }
}
