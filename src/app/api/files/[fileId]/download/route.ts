import { NextRequest, NextResponse } from "next/server";
import { FileManager } from "@/lib/utils/fileManager";

export async function GET(
  request: NextRequest,
  { params }: { params: { fileId: string } }
) {
  try {
    const fileId = params.fileId;

    // Get file information from database
    const file = await FileManager.getFileById(fileId);
    if (!file) {
      return NextResponse.json({ error: "File not found" }, { status: 404 });
    }

    // Get file path from metadata
    const filePath = file.metadata?.file_path;
    if (!filePath) {
      return NextResponse.json(
        { error: "File path not found" },
        { status: 404 }
      );
    }

    // Read file from disk
    const fileBuffer = await FileManager.getFileFromDisk(filePath);
    if (!fileBuffer) {
      return NextResponse.json(
        { error: "File not found on disk" },
        { status: 404 }
      );
    }

    // Create response with appropriate headers
    const response = new NextResponse(fileBuffer);

    // Set content type
    response.headers.set("Content-Type", file.mime_type);

    // Set content disposition for download
    response.headers.set(
      "Content-Disposition",
      `attachment; filename="${file.original_name}"`
    );

    // Set content length
    response.headers.set("Content-Length", file.file_size_bytes.toString());

    // Set cache headers
    response.headers.set("Cache-Control", "private, max-age=3600");

    return response;
  } catch (error) {
    console.error("Error downloading file:", error);
    return NextResponse.json(
      { error: "Failed to download file" },
      { status: 500 }
    );
  }
}
