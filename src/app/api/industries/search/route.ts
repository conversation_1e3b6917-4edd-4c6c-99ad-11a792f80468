import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q') || ''
    
    if (query.length < 2) {
      return NextResponse.json([])
    }

    // Search for unique industries from multiple sources
    const searchQuery = `
      SELECT DISTINCT value, source, sort_order
      FROM (
        -- From industry_mapping table (standardized values)
        SELECT 
          standardized_value as value, 
          'mapping' as source,
          CASE 
            WHEN LOWER(standardized_value) = LOWER($2) THEN 0 
            WHEN LOWER(standardized_value) LIKE LOWER($3) THEN 1
            ELSE 2 
          END as sort_order
        FROM industry_mapping 
        WHERE 
          standardized_value IS NOT NULL 
          AND standardized_value != ''
          AND LOWER(standardized_value) LIKE LOWER($1)
        
        UNION ALL
        
        -- From companies table
        SELECT 
          industry as value, 
          'companies' as source,
          CASE 
            WHEN LOWER(industry) = LOWER($2) THEN 0 
            WHEN LOWER(industry) LIKE LOWER($3) THEN 1
            ELSE 2 
          END as sort_order
        FROM companies 
        WHERE 
          industry IS NOT NULL 
          AND industry != ''
          AND LOWER(industry) LIKE LOWER($1)
          AND industry NOT IN (
            SELECT DISTINCT standardized_value 
            FROM industry_mapping 
            WHERE standardized_value IS NOT NULL AND standardized_value != ''
          )
      ) combined_results
      ORDER BY sort_order, value
      LIMIT 20
    `

    // Create different pattern matches for better search quality
    const exactTerm = query
    const startsWith = `${query}%`
    const anywhere = `%${query}%`

    const result = await pool.query(searchQuery, [
      anywhere,    // $1: match anywhere in industry
      exactTerm,   // $2: exact match
      startsWith   // $3: starts with query
    ])
    
    // Return unique values only
    const uniqueValues = [...new Set(result.rows.map(row => row.value))]
    
    return NextResponse.json(uniqueValues)
  } catch (error) {
    console.error('Error searching industries:', error)
    return NextResponse.json(
      { error: 'Failed to search industries' },
      { status: 500 }
    )
  }
} 