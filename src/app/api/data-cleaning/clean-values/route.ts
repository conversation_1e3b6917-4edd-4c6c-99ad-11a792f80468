import { pool } from '@/lib/db'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const query = `
      SELECT * FROM clean_criteria_values 
      ORDER BY field, value
    `
    const result = await pool.query(query)
    
    // Group by field
    const groupedValues = result.rows.reduce((acc, row) => {
      if (!acc[row.field]) {
        acc[row.field] = []
      }
      acc[row.field].push(row)
      return acc
    }, {})

    return NextResponse.json(groupedValues)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: 'Database error' }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { field, value } = body

    const query = `
      INSERT INTO clean_criteria_values (field, value)
      VALUES ($1, $2)
      RETURNING *
    `
    const result = await pool.query(query, [field, value])
    
    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: 'Database error' }, { status: 500 })
  }
} 