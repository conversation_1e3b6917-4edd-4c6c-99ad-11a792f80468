import { pool } from '@/lib/db'
import { NextResponse } from 'next/server'

interface PopulationResult {
  success: boolean;
  totalProcessed: number;
  successfulInserts: number;
  errors: Array<{
    field: string;
    value: string;
    error: string;
  }>;
}

export async function POST() {
  const result: PopulationResult = {
    success: false,
    totalProcessed: 0,
    successfulInserts: 0,
    errors: []
  }

  try {
    // Get all clean values grouped by field
    const cleanValuesQuery = `
      SELECT field, value 
      FROM clean_criteria_values 
      ORDER BY field, value
    `
    const cleanValues = await pool.query(cleanValuesQuery)
    result.totalProcessed = cleanValues.rows.length

    // For each clean value, insert into corresponding table
    for (const row of cleanValues.rows) {
      try {
        const tableName = row.field
        
        // Verify table exists before attempting insert
        const tableCheckQuery = `
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          )
        `
        const tableExists = await pool.query(tableCheckQuery, [tableName])
        
        if (!tableExists.rows[0].exists) {
          result.errors.push({
            field: row.field,
            value: row.value,
            error: `Table ${tableName} does not exist`
          })
          continue
        }

        const insertQuery = `
          INSERT INTO ${tableName} (value)
          VALUES ($1)
          ON CONFLICT (value) DO NOTHING
          RETURNING value
        `
        const insertResult = await pool.query(insertQuery, [row.value])
        
        // If a row was returned, it means the insert was successful
        if (insertResult.rows.length > 0) {
          result.successfulInserts++
        }
      } catch (error) {
        console.error(`Error processing ${row.field}:${row.value}:`, error)
        result.errors.push({
          field: row.field,
          value: row.value,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Consider it a success if we processed at least some values successfully
    result.success = result.successfulInserts > 0

    return NextResponse.json(result)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({
      ...result,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}