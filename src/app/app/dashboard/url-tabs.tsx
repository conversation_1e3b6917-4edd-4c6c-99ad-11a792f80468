import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useEffect, useState } from "react";
import <PERSON> from "papaparse";

interface UrlData {
  url: string;
}

export default function UrlTabs() {
  const [creUrls, setCreUrls] = useState<UrlData[]>([]);
  const [observerUrls, setObserverUrls] = useState<UrlData[]>([]);

  useEffect(() => {
    // Load CRE URLs
    fetch("/cre_urls.csv")
      .then((response) => response.text())
      .then((csv) => {
        const results = Papa.parse<UrlData>(csv, { header: true });
        setCreUrls(results.data);
      });

    // Load Observer URLs
    fetch("/observer_urls.csv")
      .then((response) => response.text())
      .then((csv) => {
        const results = Papa.parse<UrlData>(csv, { header: true });
        setObserverUrls(results.data);
      });
  }, []);

  return (
    <Tabs defaultValue="cre" className="w-full">
      <TabsList>
        <TabsTrigger value="cre">CRE</TabsTrigger>
        <TabsTrigger value="observer">Observer</TabsTrigger>
      </TabsList>

      <TabsContent value="cre">
        <div className="space-y-2">
          {creUrls.map((item, index) => (
            <div key={index} className="p-2 bg-white rounded shadow">
              <a
                href={item.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                {item.url}
              </a>
            </div>
          ))}
        </div>
      </TabsContent>

      <TabsContent value="observer">
        <div className="space-y-2">
          {observerUrls.map((item, index) => (
            <div key={index} className="p-2 bg-white rounded shadow">
              <a
                href={item.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                {item.url}
              </a>
            </div>
          ))}
        </div>
      </TabsContent>
    </Tabs>
  );
}
