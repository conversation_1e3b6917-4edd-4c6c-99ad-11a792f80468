"use client";

import NewsMonitor from "@/components/dashboard/NewsMonitor";

export default function NewsPage() {
  return (
    <div className="min-h-screen bg-white">
      <main className="w-full px-6 py-6">
        {/* Header */}
        <header className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              News Dashboard
            </h1>
            <p className="text-sm text-gray-500 mt-1">
              Monitor and manage real estate news from multiple sources with deal extraction capabilities
            </p>
          </div>
        </header>

        {/* News Monitor Component */}
        <NewsMonitor isActive={true} />
      </main>
    </div>
  );
} 