'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Users, DollarSign, Database, Sparkles, Newspaper, Target } from 'lucide-react'
import { cn } from '@/lib/utils'
import { SimpleCompanyV2Metrics } from '@/components/dashboard/data-quality/SimpleCompanyV2Metrics'
import { SimpleCompanyICMetrics } from '@/components/dashboard/data-quality/SimpleCompanyICMetrics'
import { SimpleContactICMetrics } from '@/components/dashboard/data-quality/SimpleContactICMetrics'
import { SimpleContactEnrichmentV2Metrics } from '@/components/dashboard/data-quality/SimpleContactEnrichmentV2Metrics'
import { NewsEnrichmentMetrics } from '@/components/dashboard/data-quality/NewsEnrichmentMetrics'
import { DealsDataQualityMetrics } from '@/components/dashboard/data-quality/DealsDataQualityMetrics'
import { V2MatchingQualityMetrics } from '@/components/dashboard/data-quality/V2MatchingQualityMetrics'

type EntityType = 'company_v2_simple' | 'company_investment_criteria' | 'contact_enrichment_v2' | 'contact_investment_criteria' | 'news' | 'deals' | 'matching_v2'

interface EntityConfig {
  id: EntityType
  name: string
  icon: React.ComponentType<any>
  description: string
  color: {
    primary: string
    secondary: string
    accent: string
    buttonBg: string
    buttonHover: string
  }
  apiEndpoint: string
  metricsComponent: React.ComponentType<any>
  useGrafanaStyle?: boolean
}

const entityConfigs: EntityConfig[] = [
  {
    id: 'company_v2_simple',
    name: 'Company Overview',
    icon: Database,
    description: 'Company V2 processing status, field completeness, and hourly processing stats',
    color: {
      primary: 'text-emerald-600',
      secondary: 'text-teal-600',
      accent: 'text-emerald-500',
      buttonBg: 'bg-emerald-600 hover:bg-emerald-700',
      buttonHover: 'text-emerald-600'
    },
    apiEndpoint: '/api/data-quality/company-overview-v2',
    metricsComponent: SimpleCompanyV2Metrics,
    useGrafanaStyle: false
  },
  {
    id: 'company_investment_criteria',
    name: 'Company IC',
    icon: DollarSign,
    description: 'Investment criteria extraction status, mapping validation, and processing analytics',
    color: {
      primary: 'text-indigo-600',
      secondary: 'text-cyan-600',
      accent: 'text-indigo-500',
      buttonBg: 'bg-indigo-600 hover:bg-indigo-700',
      buttonHover: 'text-indigo-600'
    },
    apiEndpoint: '/api/data-quality/company-investment-criteria',
    metricsComponent: SimpleCompanyICMetrics,
    useGrafanaStyle: false
  },
  {
    id: 'contact_enrichment_v2',
    name: 'Contact Enrichment',
    icon: Sparkles,
    description: 'V2 contact enrichment with enhanced fields, processing metrics, and data completeness',
    color: {
      primary: 'text-purple-600',
      secondary: 'text-violet-600',
      accent: 'text-purple-500',
      buttonBg: 'bg-purple-600 hover:bg-purple-700',
      buttonHover: 'text-purple-600'
    },
    apiEndpoint: '/api/data-quality/contact-enrichment-v2',
    metricsComponent: SimpleContactEnrichmentV2Metrics,
    useGrafanaStyle: false
  },
  {
    id: 'contact_investment_criteria',
    name: 'Contact IC',
    icon: Users,
    description: 'Contact investment criteria extraction, mapping validation, and personalization quality',
    color: {
      primary: 'text-pink-600',
      secondary: 'text-rose-600',
      accent: 'text-pink-500',
      buttonBg: 'bg-pink-600 hover:bg-pink-700',
      buttonHover: 'text-pink-600'
    },
    apiEndpoint: '/api/data-quality/contact-investment-criteria',
    metricsComponent: SimpleContactICMetrics,
    useGrafanaStyle: false
  },
  {
    id: 'news',
    name: 'News Enrichment',
    icon: Newspaper,
    description: 'Monitor news enrichment data processing and quality metrics',
    color: {
      primary: 'text-purple-600',
      secondary: 'text-orange-600',
      accent: 'text-purple-500',
      buttonBg: 'bg-purple-600 hover:bg-purple-700',
      buttonHover: 'text-purple-600'
    },
    apiEndpoint: '/api/data-quality/news-enrichment',
    metricsComponent: NewsEnrichmentMetrics,
    useGrafanaStyle: false
  },
  {
    id: 'deals',
    name: 'Deals + Investment Criteria',
    icon: DollarSign,
    description: 'Monitor deal overview, debt, and equity criteria quality metrics',
    color: {
      primary: 'text-blue-600',
      secondary: 'text-indigo-600',
      accent: 'text-blue-500',
      buttonBg: 'bg-blue-600 hover:bg-blue-700',
      buttonHover: 'text-blue-600'
    },
    apiEndpoint: '/api/data-quality/deals',
    metricsComponent: DealsDataQualityMetrics,
    useGrafanaStyle: false
  },
  {
    id: 'matching_v2',
    name: 'V2 Matching System',
    icon: Target,
    description: 'Comprehensive data quality metrics for V2 matching system across all tables and fields',
    color: {
      primary: 'text-amber-600',
      secondary: 'text-orange-600',
      accent: 'text-amber-500',
      buttonBg: 'bg-amber-600 hover:bg-amber-700',
      buttonHover: 'text-amber-600'
    },
    apiEndpoint: '/api/data-quality/matching-v2',
    metricsComponent: V2MatchingQualityMetrics,
    useGrafanaStyle: false
  }
]

export default function DataQualityDashboard() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  // Get entity from URL params or default to first entity
  const entityParam = searchParams?.get('entity') as EntityType
  const activeEntity = entityConfigs.find(config => config.id === entityParam)?.id || entityConfigs[0].id
  const activeConfig = entityConfigs.find(config => config.id === activeEntity) || entityConfigs[0]

  // Fetch data when active entity changes
  useEffect(() => {
    fetchData()
  }, [activeEntity])

  const fetchData = async () => {
    setLoading(true)
    try {
      const response = await fetch(activeConfig.apiEndpoint)
      const result = await response.json()
      setData(result)
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleEntityChange = (entityId: EntityType) => {
    router.push(`/dashboard/data-quality?entity=${entityId}`)
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchData()
    setTimeout(() => setRefreshing(false), 1000)
  }

  // Get record count based on active entity
  const getRecordCount = () => {
    if (!data?.data) return 'Loading records...'
    
    switch (activeEntity) {
      case 'company_v2_simple':
        return data.data?.totalRows 
          ? `${data.data.totalRows.toLocaleString()} companies tracked`
          : 'Loading companies...'
      case 'company_investment_criteria':
        return data.data?.totalRows 
          ? `${data.data.totalRows.toLocaleString()} companies tracked (${data.data.completedICRows || 0} IC extracted)`
          : 'Loading companies...'
      case 'contact_enrichment_v2':
        return data.data?.totalRows 
          ? `${data.data.totalRows.toLocaleString()} contacts tracked (${data.data.completedV2Rows || 0} V2 enriched)`
          : 'Loading contacts...'
      case 'contact_investment_criteria':
        return data.data?.totalRows 
          ? `${data.data.totalRows.toLocaleString()} contacts tracked (${data.data.completedICRows || 0} IC extracted)`
          : 'Loading contacts...'
      case 'news':
        const newsTotal = (data.data.newsData?.totalRecords || 0) + 
                         (data.data.dealsData?.totalRecords || 0) + 
                         (data.data.companiesData?.totalRecords || 0) + 
                         (data.data.personsData?.totalRecords || 0)
        return newsTotal > 0 ? `${newsTotal.toLocaleString()} news records` : 'Loading records...'
      case 'deals':
        return data.data?.totalRecords 
          ? `${data.data.totalRecords.toLocaleString()} deal records`
          : 'Loading deals...'
      case 'matching_v2':
        return data.data?.summary?.total_records 
          ? `${data.data.summary.total_records.toLocaleString()} records across all tables`
          : 'Loading V2 matching data...'
      default:
        return 'Loading records...'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Standard Header */}
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <div className="space-y-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold tracking-tight text-gray-900">Data Quality Dashboard</h1>
            </div>
            {/* Add buttons for the pages of the dashboard */}
            <div className="flex items-center gap-4">
              <div className="flex bg-gray-100 rounded-lg p-1">
                {entityConfigs.map((config) => {
                  const IconComponent = config.icon
                  const isActive = activeEntity === config.id
                  
                  return (
                    <button
                      key={config.id}
                      onClick={() => handleEntityChange(config.id)}
                      className={cn(
                        "flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200",
                        isActive 
                          ? "bg-white text-gray-900 shadow-sm border border-gray-200" 
                          : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                      )}
                    >
                      <IconComponent className={cn(
                        "h-4 w-4",
                        isActive ? "text-blue-600" : "text-gray-500"
                      )} />
                      <span className="hidden md:inline">{config.name}</span>
                      <span className="md:hidden">{config.name.split(' ')[0]}</span>
                    </button>
                  )
                })}
              </div>
              <div className="flex items-center gap-3">
                <div className="text-sm text-gray-600 bg-white px-3 py-1 rounded-md border">
                  {getRecordCount()}
                </div>
                <Button 
                  onClick={handleRefresh} 
                  variant="outline" 
                  size="sm"
                  disabled={refreshing}
                  className="bg-white hover:bg-gray-50"
                >
                  {refreshing ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600" />
                  ) : (
                    'Refresh'
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>



      {/* Active Entity Content */}
      <div className="min-h-[600px] p-4 md:p-6">
        <activeConfig.metricsComponent 
          data={data?.data} 
          loading={loading}
        />
      </div>
    </div>
  )
} 