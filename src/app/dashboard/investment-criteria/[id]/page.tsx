import React from 'react'
import InvestmentCriteriaDetailView from '@/components/dashboard/investment-criteria/InvestmentCriteriaDetailView'

interface InvestmentCriteriaDetailPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function InvestmentCriteriaDetailPage({ params }: InvestmentCriteriaDetailPageProps) {
  const { id } = await params
  return <InvestmentCriteriaDetailView id={id} />
} 