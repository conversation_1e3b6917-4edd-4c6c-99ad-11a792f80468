"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { FileUpload } from "@/components/ui/file-upload";
import { Upload, FileText, CheckCircle, AlertCircle } from "lucide-react";

// Placeholder for company upload UI/component
const CompanyUploadTab = () => (
  <div className="py-8">
    <h2 className="text-xl font-semibold mb-4">Company Upload (Coming Soon)</h2>
    <p className="text-gray-600">Upload and manage company data here.</p>
  </div>
);

const DealUploadTab = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [mode, setMode] = useState<"text" | "file">("file");
  const [uploading, setUploading] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [dbStatus, setDbStatus] = useState<{
    saved: boolean;
    error?: string;
  } | null>(null);

  const handleModeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMode(e.target.value as "text" | "file");
    setResult(null);
    setError(null);
    setDbStatus(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (files.length === 0) return;

    setUploading(true);
    setResult(null);
    setError(null);
    setDbStatus(null);

    try {
      const formData = new FormData();

      // Add all files with descriptive names
      files.forEach((file, index) => {
        const fileType = getFileType(file.name);
        formData.append(`file_${index}`, file);
        formData.append(`file_type_${index}`, fileType);
      });

      formData.append("mode", mode);
      formData.append("file_count", files.length.toString());

      const res = await fetch("/api/deals/upload", {
        method: "POST",
        body: formData,
      });

      const data = await res.json();
      if (res.ok) {
        setResult(data.message || "Upload successful!");

        // Handle database status
        if (data.databaseSaved) {
          setDbStatus({ saved: true });
        } else if (data.databaseError) {
          setDbStatus({ saved: false, error: data.databaseError });
        }
      } else {
        setError(data.error || "Upload failed.");
      }
    } catch (err: any) {
      setError(err.message || "Upload failed.");
    } finally {
      setUploading(false);
    }
  };

  const getFileType = (fileName: string): string => {
    const name = fileName.toLowerCase();
    if (name.includes("memorandum") || name.includes("mem"))
      return "memorandum";
    if (name.includes("underwriting") || name.includes("uw"))
      return "underwriting";
    if (name.includes("term") || name.includes("sheet")) return "term_sheet";
    if (name.includes("proforma") || name.includes("model")) return "proforma";
    if (name.includes("budget") || name.includes("construction"))
      return "budget";
    if (name.includes("analysis") || name.includes("market")) return "analysis";
    return "document";
  };

  return (
    <div className="max-w-4xl mx-auto py-10">
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Deal Upload</h2>
        <p className="text-gray-600">
          Upload multiple deal documents (memorandum, underwriting, term sheets,
          etc.) for AI-powered extraction.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Upload Section */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload Documents
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <FileUpload
                onFilesSelected={setFiles}
                acceptedFileTypes={[
                  ".pdf",
                  ".csv",
                  ".xlsx",
                  ".xls",
                  ".doc",
                  ".docx",
                ]}
                maxFiles={10}
                maxFileSize={30 * 1024 * 1024} // 30MB
                placeholder="Drop your deal documents here"
                description="Upload memorandum, underwriting, term sheets, pro forma models, and other deal documents. Multiple files supported."
                disabled={uploading}
              />

              <div className="border-t pt-6">
                <h3 className="font-semibold mb-3">Extraction Mode</h3>
                <div className="flex gap-6">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="mode"
                      value="text"
                      checked={mode === "text"}
                      onChange={handleModeChange}
                      disabled={uploading}
                    />
                    <span>Text Extraction (Legacy)</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="mode"
                      value="file"
                      checked={mode === "file"}
                      onChange={handleModeChange}
                      disabled={uploading}
                    />
                    <span>Direct File (Gemini 2.0 Flash)</span>
                  </label>
                </div>
              </div>

              <Button
                onClick={handleSubmit}
                disabled={uploading || files.length === 0}
                size="lg"
                className="w-full bg-blue-600 hover:bg-blue-700"
              >
                {uploading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Processing Documents...
                  </>
                ) : (
                  <>
                    <Upload className="h-5 w-5 mr-2" />
                    Process {files.length} Document
                    {files.length !== 1 ? "s" : ""}
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Info Section */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Upload Info
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Supported Documents</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Investment Memorandums (IMs)</li>
                  <li>• Offering Memorandums (OMs)</li>
                  <li>• Term Sheets</li>
                  <li>• Pro Forma Models</li>
                  <li>• Construction Budgets</li>
                  <li>• Market Analysis Reports</li>
                  <li>• Due Diligence Reports</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-2">File Requirements</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• PDF, CSV, XLSX, DOC, DOCX</li>
                  <li>• Up to 30MB per file</li>
                  <li>• Maximum 10 files per upload</li>
                  <li>• Gemini 2.0 Flash processing</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Results */}
          {result && (
            <Card className="border-green-200 bg-green-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-green-700">
                  <CheckCircle className="h-5 w-5" />
                  <p className="font-medium">{result}</p>
                </div>

                {/* Database Status */}
                {dbStatus && (
                  <div className="mt-3 pt-3 border-t border-green-200">
                    {dbStatus.saved ? (
                      <div className="flex items-center gap-2 text-green-600">
                        <CheckCircle className="h-4 w-4" />
                        <span className="text-sm">
                          Data saved to database successfully
                        </span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 text-orange-600">
                        <AlertCircle className="h-4 w-4" />
                        <span className="text-sm">
                          Database save failed: {dbStatus.error}
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {error && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-red-700">
                  <AlertCircle className="h-5 w-5" />
                  <p className="font-medium">{error}</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

const DataCenterPage = () => {
  const [tab, setTab] = useState<"company" | "deal">("company");

  return (
    <div className="max-w-3xl mx-auto py-10">
      <h1 className="text-2xl font-bold mb-6">Data Center</h1>
      <div className="mb-6 border-b">
        <nav className="flex gap-6">
          <button
            className={`pb-2 border-b-2 transition-colors ${
              tab === "company"
                ? "border-blue-600 text-blue-700 font-semibold"
                : "border-transparent text-gray-500"
            }`}
            onClick={() => setTab("company")}
          >
            Company
          </button>
          <button
            className={`pb-2 border-b-2 transition-colors ${
              tab === "deal"
                ? "border-blue-600 text-blue-700 font-semibold"
                : "border-transparent text-gray-500"
            }`}
            onClick={() => setTab("deal")}
          >
            Deal
          </button>
        </nav>
      </div>
      <div>
        {tab === "company" && <CompanyUploadTab />}
        {tab === "deal" && <DealUploadTab />}
      </div>
    </div>
  );
};

export default DataCenterPage;
