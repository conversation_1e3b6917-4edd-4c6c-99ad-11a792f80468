'use client';

import React from 'react';
import ContactDetail from "@/components/dashboard/people/ContactDetail";
import { useRouter, useParams } from "next/navigation";

export default function ContactDetailPage() {
    const router = useRouter();
    const params = useParams();
    // The contactId from useParams could be a string or string[], so we need to ensure it's a string
    const contactId = params?.contactId ? 
        (Array.isArray(params.contactId) ? params.contactId[0] : params.contactId) : 
        '';

    return (
        <ContactDetail
            contactId={contactId}
            onBack={() => router.back()}
        />
    )
} 