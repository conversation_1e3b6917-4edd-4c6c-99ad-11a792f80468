'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import AddContact from '@/components/dashboard/people/AddContact'

export default function AddContactPage() {
  const router = useRouter()

  const handleBack = () => {
    router.push('/dashboard/people')
  }

  const handleSuccess = (contactId: number) => {
    // Navigate to the contact detail page after successful creation
    router.push(`/dashboard/people/${contactId}`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="w-full p-6">
        <AddContact 
          onBack={handleBack}
          onSuccess={handleSuccess}
        />
      </div>
    </div>
  )
}
