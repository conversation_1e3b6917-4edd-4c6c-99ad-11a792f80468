'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { Loader2, Save, RefreshCw, AlertTriangle, Copy, Settings, Globe, Target, CopyCheck } from 'lucide-react';

interface CapitalPositionWeight {
  capital_position: string;
  field_name: string;
  weight: number;
  description: string;
  is_active: boolean;
  normalized_weight?: number;
  normalized_percentage?: number;
  is_custom?: boolean; // Whether this weight overrides the common weight
}

interface CapitalPositionWeights {
  capital_position: string;
  field_weights: CapitalPositionWeight[];
  total_weight: number;
  normalized_total: number;
}

interface CommonWeight {
  field_name: string;
  weight: number;
  description: string;
  is_active: boolean;
}

const CAPITAL_POSITIONS = [
  'Senior Debt',
  'Mezzanine',
  'Preferred Equity',
  'Common Equity',
  'General Partner (GP)',
  'Limited Partner (LP)',
  'Stretch Senior',
  'Co-GP',
  'Joint Venture (JV)'
];

const FIELD_NAMES = [
  'location',
  'deal_size',
  'property_types',
  'strategies',
  'target_return',
  'loan_to_value',
  'loan_to_cost',
  'interest_rate',
  'loan_term',
  'loan_dscr',
  'hold_period'
];

const FIELD_DESCRIPTIONS: { [key: string]: string } = {
  'location': 'Geographic matching (region/state/city hierarchy)',
  'deal_size': 'Deal size overlap with complex fuzzy logic',
  'property_types': 'Property type array matching',
  'strategies': 'Investment strategies array matching',
  'target_return': 'Target return/IRR range matching',
  'loan_to_value': 'LTV range overlap matching',
  'loan_to_cost': 'LTC range overlap matching',
  'interest_rate': 'Interest rate comparison matching',
  'loan_term': 'Loan term range matching',
  'loan_dscr': 'DSCR range overlap matching',
  'hold_period': 'Hold period range matching'
};

export default function V2FieldWeightsPage() {
  const [capitalPositions, setCapitalPositions] = useState<CapitalPositionWeights[]>([]);
  const [commonWeights, setCommonWeights] = useState<CommonWeight[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('common');
  const [copiedPosition, setCopiedPosition] = useState<string | null>(null);

  // Load all capital position weights
  const loadCapitalPositionWeights = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const promises = CAPITAL_POSITIONS.map(async (position) => {
        const response = await fetch(`/api/matching-v2/capital-position-weights/${encodeURIComponent(position)}`);
        if (response.ok) {
          return await response.json();
        } else {
          console.error(`Failed to load weights for ${position}`);
          return {
            capital_position: position,
            field_weights: [],
            total_weight: 0,
            normalized_total: 0
          };
        }
      });

      const results = await Promise.all(promises);
      setCapitalPositions(results);
      
      // Initialize common weights from the first position or create defaults
      if (results.length > 0 && results[0].field_weights.length > 0) {
        const firstPosition = results[0];
        const common = FIELD_NAMES.map(fieldName => {
          const existingWeight = firstPosition.field_weights.find(fw => fw.field_name === fieldName);
          return {
            field_name: fieldName,
            weight: existingWeight?.weight || 0.1,
            description: FIELD_DESCRIPTIONS[fieldName] || `Weight for ${fieldName}`,
            is_active: existingWeight?.is_active || true
          };
        });
        setCommonWeights(common);
      } else {
        // Create default common weights
        const defaultCommon = FIELD_NAMES.map(fieldName => ({
          field_name: fieldName,
          weight: fieldName === 'location' ? 0.25 : fieldName === 'deal_size' ? 0.20 : 0.1,
          description: FIELD_DESCRIPTIONS[fieldName] || `Weight for ${fieldName}`,
          is_active: true
        }));
        setCommonWeights(defaultCommon);
      }
    } catch (err) {
      setError('Failed to load capital position weights');
      console.error('Error loading weights:', err);
    } finally {
      setLoading(false);
    }
  };

  // Apply common weights to all capital positions
  const applyCommonWeights = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const promises = CAPITAL_POSITIONS.map(async (position) => {
        const fieldWeights = commonWeights.map(common => ({
          capital_position: position,
          field_name: common.field_name,
          weight: common.weight,
          description: common.description,
          is_active: common.is_active,
          is_custom: false
        }));

        const response = await fetch(`/api/matching-v2/capital-position-weights/${encodeURIComponent(position)}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ field_weights: fieldWeights }),
        });

        if (!response.ok) {
          throw new Error(`Failed to update ${position}`);
        }
      });

      await Promise.all(promises);
      setSuccess('Common weights applied to all capital positions successfully!');
      await loadCapitalPositionWeights(); // Reload to get updated data
    } catch (err) {
      setError('Failed to apply common weights');
      console.error('Error applying common weights:', err);
    } finally {
      setSaving(false);
    }
  };

  // Copy weights from one position to another
  const copyWeights = async (fromPosition: string, toPosition: string) => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const sourcePosition = capitalPositions.find(cp => cp.capital_position === fromPosition);
      if (!sourcePosition) {
        throw new Error('Source position not found');
      }

      const fieldWeights = sourcePosition.field_weights.map(fw => ({
        ...fw,
        capital_position: toPosition,
        is_custom: true
      }));

      const response = await fetch(`/api/matching-v2/capital-position-weights/${encodeURIComponent(toPosition)}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ field_weights: fieldWeights }),
      });

      if (!response.ok) {
        throw new Error(`Failed to copy weights to ${toPosition}`);
      }

      setSuccess(`Weights copied from ${fromPosition} to ${toPosition} successfully!`);
      setCopiedPosition(toPosition);
      setTimeout(() => setCopiedPosition(null), 2000);
      await loadCapitalPositionWeights();
    } catch (err) {
      setError('Failed to copy weights');
      console.error('Error copying weights:', err);
    } finally {
      setSaving(false);
    }
  };

  // Save weights for a specific capital position
  const saveWeights = async (capitalPosition: string, fieldWeights: CapitalPositionWeight[]) => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch(`/api/matching-v2/capital-position-weights/${encodeURIComponent(capitalPosition)}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ field_weights: fieldWeights }),
      });

      if (response.ok) {
        setSuccess(`Weights saved successfully for ${capitalPosition}`);
        await loadCapitalPositionWeights(); // Reload to get updated data
      } else {
        const errorData = await response.json();
        setError(`Failed to save weights: ${errorData.error}`);
      }
    } catch (err) {
      setError('Failed to save weights');
      console.error('Error saving weights:', err);
    } finally {
      setSaving(false);
    }
  };

  // Update common weight
  const updateCommonWeight = (fieldName: string, newWeight: number) => {
    setCommonWeights(prev => prev.map(cw => 
      cw.field_name === fieldName ? { ...cw, weight: newWeight } : cw
    ));
  };

  // Update weight for a specific field
  const updateWeight = (capitalPosition: string, fieldName: string, newWeight: number) => {
    setCapitalPositions(prev => prev.map(cp => {
      if (cp.capital_position === capitalPosition) {
        return {
          ...cp,
          field_weights: cp.field_weights.map(fw => 
            fw.field_name === fieldName 
              ? { ...fw, weight: newWeight, is_custom: true }
              : fw
          )
        };
      }
      return cp;
    }));
  };

  // Toggle field active status
  const toggleFieldActive = (capitalPosition: string, fieldName: string) => {
    setCapitalPositions(prev => prev.map(cp => {
      if (cp.capital_position === capitalPosition) {
        return {
          ...cp,
          field_weights: cp.field_weights.map(fw => 
            fw.field_name === fieldName 
              ? { ...fw, is_active: !fw.is_active, is_custom: true }
              : fw
          )
        };
      }
      return cp;
    }));
  };

  // Reset position to use common weights
  const resetToCommon = async (capitalPosition: string) => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const fieldWeights = commonWeights.map(common => ({
        capital_position: capitalPosition,
        field_name: common.field_name,
        weight: common.weight,
        description: common.description,
        is_active: common.is_active,
        is_custom: false
      }));

      const response = await fetch(`/api/matching-v2/capital-position-weights/${encodeURIComponent(capitalPosition)}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ field_weights: fieldWeights }),
      });

      if (response.ok) {
        setSuccess(`${capitalPosition} reset to common weights successfully!`);
        await loadCapitalPositionWeights();
      } else {
        const errorData = await response.json();
        setError(`Failed to reset ${capitalPosition}: ${errorData.error}`);
      }
    } catch (err) {
      setError('Failed to reset to common weights');
      console.error('Error resetting weights:', err);
    } finally {
      setSaving(false);
    }
  };

  // Calculate total weight for a capital position
  const calculateTotalWeight = (fieldWeights: CapitalPositionWeight[]) => {
    return fieldWeights.filter(fw => fw.is_active).reduce((sum, fw) => sum + fw.weight, 0);
  };

  // Normalize weights to sum to 1
  const normalizeWeights = (fieldWeights: CapitalPositionWeight[]) => {
    const activeWeights = fieldWeights.filter(fw => fw.is_active);
    const total = calculateTotalWeight(activeWeights);
    if (total === 0) return fieldWeights;

    return fieldWeights.map(fw => ({
      ...fw,
      normalized_weight: fw.is_active ? fw.weight / total : 0,
      normalized_percentage: fw.is_active ? Math.round((fw.weight / total) * 100) : 0
    }));
  };

  useEffect(() => {
    loadCapitalPositionWeights();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading capital position weights...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">V2 Field Weights Configuration</h1>
          <p className="text-muted-foreground">
            Configure common weights and customize per capital position for the V2 matching system
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={loadCapitalPositionWeights} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={applyCommonWeights} disabled={saving} className="bg-blue-600 hover:bg-blue-700">
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Applying...
              </>
            ) : (
              <>
                <Globe className="h-4 w-4 mr-2" />
                Apply to All
              </>
            )}
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert>
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4 lg:grid-cols-6">
          <TabsTrigger value="common" className="text-xs flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Common
          </TabsTrigger>
          {CAPITAL_POSITIONS.map((position) => (
            <TabsTrigger key={position} value={position} className="text-xs">
              {position}
            </TabsTrigger>
          ))}
        </TabsList>

        {/* Common Weights Tab */}
        <TabsContent value="common" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Common Field Weights
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Set default weights that apply to all capital positions. Individual positions can override these.
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {commonWeights.map((commonWeight) => (
                  <div key={commonWeight.field_name} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <div className="flex-1">
                      <Label className="font-medium capitalize">{commonWeight.field_name.replace(/_/g, ' ')}</Label>
                      <p className="text-sm text-muted-foreground">{commonWeight.description}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Input
                        type="number"
                        min="0"
                        max="1"
                        step="0.01"
                        value={commonWeight.weight}
                        onChange={(e) => updateCommonWeight(commonWeight.field_name, parseFloat(e.target.value) || 0)}
                        className="w-20"
                      />
                      <span className="text-sm text-muted-foreground w-12">
                        {Math.round(commonWeight.weight * 100)}%
                      </span>
                    </div>
                  </div>
                ))}
                
                <Separator />
                
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">
                    Total: {Math.round(commonWeights.reduce((sum, cw) => sum + cw.weight, 0) * 100)}%
                  </div>
                  <Button
                    onClick={applyCommonWeights}
                    disabled={saving}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {saving ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Applying...
                      </>
                    ) : (
                      <>
                        <Globe className="h-4 w-4 mr-2" />
                        Apply to All Positions
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Individual Capital Position Tabs */}
        {CAPITAL_POSITIONS.map((position) => {
          const capitalPosition = capitalPositions.find(cp => cp.capital_position === position);
          const fieldWeights = capitalPosition?.field_weights || [];
          const normalizedWeights = normalizeWeights(fieldWeights);
          const totalWeight = calculateTotalWeight(fieldWeights);
          const customFieldsCount = fieldWeights.filter(fw => fw.is_custom).length;

          return (
            <TabsContent key={position} value={position} className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{position} Field Weights</span>
                    <div className="flex items-center space-x-2">
                      <Badge variant={totalWeight > 1.01 ? "destructive" : totalWeight > 0.99 ? "default" : "secondary"}>
                        Total: {Math.round(totalWeight * 100)}%
                      </Badge>
                      {customFieldsCount > 0 && (
                        <Badge variant="outline" className="text-orange-600 border-orange-600">
                          {customFieldsCount} Custom
                        </Badge>
                      )}
                    </div>
                  </CardTitle>
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-muted-foreground">
                      {customFieldsCount > 0 
                        ? `${customFieldsCount} fields customized from common weights`
                        : 'Using common weights (no customizations)'
                      }
                    </p>
                    <div className="flex items-center space-x-2">
                      <Button
                        onClick={() => resetToCommon(position)}
                        variant="outline"
                        size="sm"
                        disabled={saving || customFieldsCount === 0}
                      >
                        Reset to Common
                      </Button>
                      <Button
                        onClick={() => copyWeights(position, 'Senior Debt')}
                        variant="outline"
                        size="sm"
                        disabled={saving}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50"
                      >
                        {copiedPosition === position ? (
                          <>
                            <CopyCheck className="h-4 w-4 mr-2" />
                            Copied!
                          </>
                        ) : (
                          <>
                            <Copy className="h-4 w-4 mr-2" />
                            Copy to Senior Debt
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {normalizedWeights.map((fieldWeight) => {
                      const commonWeight = commonWeights.find(cw => cw.field_name === fieldWeight.field_name);
                      const isCustom = fieldWeight.is_custom;
                      const isDifferentFromCommon = commonWeight && Math.abs(fieldWeight.weight - commonWeight.weight) > 0.001;

                      return (
                        <div key={fieldWeight.field_name} className={`flex items-center space-x-4 p-4 border rounded-lg ${
                          isCustom ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'
                        }`}>
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <Label className="font-medium capitalize">{fieldWeight.field_name.replace(/_/g, ' ')}</Label>
                              {isCustom && (
                                <Badge variant="outline" size="sm" className="text-blue-600 border-blue-600">
                                  Custom
                                </Badge>
                              )}
                              {isDifferentFromCommon && commonWeight && (
                                <Badge variant="outline" size="sm" className="text-orange-600 border-orange-600">
                                  {fieldWeight.weight > commonWeight.weight ? '+' : ''}
                                  {Math.round((fieldWeight.weight - commonWeight.weight) * 100)}%
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground">{fieldWeight.description}</p>
                            {commonWeight && (
                              <p className="text-xs text-blue-600">
                                Common: {Math.round(commonWeight.weight * 100)}%
                              </p>
                            )}
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              checked={fieldWeight.is_active}
                              onCheckedChange={() => toggleFieldActive(position, fieldWeight.field_name)}
                            />
                            <Input
                              type="number"
                              min="0"
                              max="1"
                              step="0.01"
                              value={fieldWeight.weight}
                              onChange={(e) => updateWeight(position, fieldWeight.field_name, parseFloat(e.target.value) || 0)}
                              className="w-20"
                            />
                            <span className="text-sm text-muted-foreground w-12">
                              {fieldWeight.normalized_percentage}%
                            </span>
                          </div>
                        </div>
                      );
                    })}
                    
                    {fieldWeights.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        No field weights configured for {position}
                      </div>
                    )}

                    <div className="flex justify-end space-x-2">
                      <Button
                        onClick={() => saveWeights(position, fieldWeights)}
                        disabled={saving}
                      >
                        {saving ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Save Weights
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          );
        })}
      </Tabs>
    </div>
  );
}
