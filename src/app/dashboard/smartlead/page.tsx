'use client';

import { Suspense, useState, useEffect } from 'react';
import { 
  CampaignStats 
} from '@/components/dashboard/smartlead/CampaignStats';
import { 
  ContactEmailThreads 
} from '@/components/dashboard/smartlead/ContactEmailThreads';
import { 
  ContactSelector 
} from '@/components/dashboard/smartlead/ContactSelector';
import { 
  CampaignManager 
} from '@/components/dashboard/smartlead/CampaignManager';
import CampaignsTab from '@/components/dashboard/smartlead/CampaignsTab';
import LeadsTab from '@/components/dashboard/smartlead/LeadsTab';
import MessageHistoryTab from '@/components/dashboard/smartlead/MessageHistoryTab';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Users, 
  Mail, 
  BarChart3, 
  MessageSquare, 
  Settings,
  RefreshCw,
  Filter,
  Download,
  Eye,
  TrendingUp,
  AlertTriangle,
  Plus,
  Upload,
  Activity,
  Target,
  Zap,
  Calendar,
  Clock
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from 'sonner';

interface SmartleadStats {
  statusCounts: Array<{ status: string; count: number }>;
  pendingCount: number;
  dailyCounts: Array<{ date: string; count: number }>;
  recentActivity: Array<{
    contact_id: number;
    first_name: string;
    last_name: string;
    email: string;
    smartlead_status: string;
    last_email_sent_at: string;
    company_name: string;
  }>;
}

export default function SmartleadDashboardPage() {
  const [selectedContactId, setSelectedContactId] = useState<number | null>(null);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [campaignFilter, setCampaignFilter] = useState('all');
  const [smartleadStats, setSmartleadStats] = useState<SmartleadStats | null>(null);
  const [isLoadingStats, setIsLoadingStats] = useState(false);

  // Fetch overall Smartlead statistics
  useEffect(() => {
    const fetchSmartleadStats = async () => {
      try {
        setIsLoadingStats(true);
        const response = await fetch('/api/smartlead/stats');
        if (!response.ok) {
          throw new Error(`Failed to fetch stats: ${response.status}`);
        }
        const data = await response.json();
        setSmartleadStats(data);
      } catch (error) {
        console.error('Error fetching Smartlead stats:', error);
        toast.error('Failed to load Smartlead statistics');
      } finally {
        setIsLoadingStats(false);
      }
    };

    fetchSmartleadStats();
  }, []);

  const handleContactSelect = (contactId: number) => {
    setSelectedContactId(contactId);
  };

  const handleCampaignSelect = (campaignId: string) => {
    setSelectedCampaignId(campaignId);
  };

  const handleBackToOverview = () => {
    setSelectedContactId(null);
    setSelectedCampaignId(null);
  };

  // If a contact is selected, show the contact detail view
  if (selectedContactId !== null) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={handleBackToOverview}
              className="flex items-center space-x-2"
            >
              ← Back to Overview
            </Button>
            <h1 className="text-3xl font-bold">Contact Conversations</h1>
          </div>
        </div>
        
        <Suspense fallback={<div>Loading contact threads...</div>}>
          <ContactEmailThreads contactId={selectedContactId} />
        </Suspense>
      </div>
    );
  }

  // If a campaign is selected, show the campaign management view
  if (selectedCampaignId !== null) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={handleBackToOverview}
              className="flex items-center space-x-2"
            >
              ← Back to Overview
            </Button>
            <h1 className="text-3xl font-bold">Campaign Management</h1>
          </div>
        </div>
        
        <Suspense fallback={<div>Loading campaign details...</div>}>
          <CampaignManager 
            selectedCampaignId={selectedCampaignId}
            onCampaignSelect={handleCampaignSelect}
          />
        </Suspense>
      </div>
    );
  }

  // Calculate totals from stats
  const totalLeads = smartleadStats?.statusCounts.reduce((sum, item) => Number(sum) + Number(item.count), 0) || 0;
  const totalSent = smartleadStats?.statusCounts.find(item => item.status === 'SENT')?.count || 0;
  const totalOpened = smartleadStats?.statusCounts.find(item => item.status === 'OPENED')?.count || 0;
  const totalReplied = smartleadStats?.statusCounts.find(item => item.status === 'REPLIED')?.count || 0;
  
  const openRate = totalSent > 0 ? ((totalOpened / totalSent) * 100).toFixed(1) : '0';
  const replyRate = totalSent > 0 ? ((totalReplied / totalSent) * 100).toFixed(1) : '0';

  return (
    <div className="container mx-auto p-6">
      {/* Header Section */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">Smartlead Email Campaigns</h1>
          <p className="text-muted-foreground">
            Comprehensive campaign management and analytics dashboard
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => window.location.reload()}
            disabled={isLoadingStats}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoadingStats ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>


      {/* Main Content with Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Overview</span>
          </TabsTrigger>
          <TabsTrigger value="campaigns" className="flex items-center space-x-2">
            <Mail className="h-4 w-4" />
            <span>Campaigns</span>
          </TabsTrigger>
          <TabsTrigger value="leads" className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>Leads</span>
          </TabsTrigger>
          <TabsTrigger value="messages" className="flex items-center space-x-2">
            <MessageSquare className="h-4 w-4" />
            <span>Messages</span>
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Campaign Stats */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Campaign Performance</span>
                    <Badge variant="outline" className="bg-green-100 text-green-800">
                      Live Data
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Suspense fallback={<div>Loading campaign stats...</div>}>
                    <CampaignStats onContactSelect={handleContactSelect} />
                  </Suspense>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions & Recent Activity */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button 
                    className="w-full justify-start" 
                    variant="outline"
                    onClick={() => setActiveTab('campaigns')}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create New Campaign
                  </Button>
                  <Button 
                    className="w-full justify-start" 
                    variant="outline"
                    onClick={() => setActiveTab('leads')}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Import Leads
                  </Button>
                  <Button 
                    className="w-full justify-start" 
                    variant="outline"
                    onClick={() => setActiveTab('messages')}
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    View Message History
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <Settings className="h-4 w-4 mr-2" />
                    Campaign Settings
                  </Button>
                </CardContent>
              </Card>

              {/* Status Distribution */}
              {smartleadStats && (
                <Card>
                  <CardHeader>
                    <CardTitle>Status Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {smartleadStats.statusCounts.map((item) => (
                        <div key={item.status} className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className={`w-3 h-3 rounded-full ${
                              item.status === 'SENT' ? 'bg-purple-500' :
                              item.status === 'DELIVERED' ? 'bg-blue-500' :
                              item.status === 'OPENED' ? 'bg-green-500' :
                              item.status === 'REPLIED' ? 'bg-emerald-500' :
                              item.status === 'BOUNCED' ? 'bg-red-500' :
                              'bg-gray-500'
                            }`} />
                            <span className="text-sm font-medium">{item.status}</span>
                          </div>
                          <span className="text-sm font-bold">{item.count}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Recent Activity */}
              {smartleadStats?.recentActivity && (
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {smartleadStats.recentActivity.slice(0, 5).map((activity, index) => (
                        <div key={index} className="flex items-center space-x-3">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>
                              {activity.first_name.charAt(0)}{activity.last_name.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium">
                              {activity.first_name} {activity.last_name}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {activity.smartlead_status} • {activity.company_name}
                            </p>
                          </div>
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${
                              activity.smartlead_status === 'REPLIED' ? 'bg-emerald-100 text-emerald-800' :
                              activity.smartlead_status === 'OPENED' ? 'bg-green-100 text-green-800' :
                              activity.smartlead_status === 'SENT' ? 'bg-purple-100 text-purple-800' :
                              'bg-gray-100 text-gray-800'
                            }`}
                          >
                            {activity.smartlead_status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        {/* Campaigns Tab */}
        <TabsContent value="campaigns" className="space-y-6">
          <Suspense fallback={<div>Loading campaigns...</div>}>
            <CampaignManager onCampaignSelect={handleCampaignSelect} />
          </Suspense>
        </TabsContent>

        {/* Leads Tab */}
        <TabsContent value="leads" className="space-y-6">
          <Suspense fallback={<div>Loading leads...</div>}>
            <LeadsTab onContactSelect={handleContactSelect} />
          </Suspense>
        </TabsContent>

        {/* Messages Tab */}
        <TabsContent value="messages" className="space-y-6">
          <Suspense fallback={<div>Loading message history...</div>}>
            <MessageHistoryTab />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  );
} 