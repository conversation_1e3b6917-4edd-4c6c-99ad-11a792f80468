export interface CentralMapping {
  id: number;
  type: string;
  level_1: string;
  value_1: string;
  level_2?: string;
  value_2?: string;
  level_3?: string;
  value_3?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface MappingGroup {
  type: string;
  mappings: CentralMapping[];
}

export interface MappingStats {
  totalRows?: number;
  totalMappings?: number;
  insertedCount: number;
  skippedCount?: number;
  errors?: string[];
}

export interface MappingUploadResponse {
  success: boolean;
  message?: string;
  error?: string;
  details?: string;
  stats?: MappingStats;
}

export interface MappingApiResponse {
  success: boolean;
  data?: CentralMapping[] | CentralMapping;
  error?: string;
}

export interface CSVRow {
  [key: string]: string;
}

export interface MappingFormData {
  type: string;
  level_1: string;
  value_1: string;
  level_2: string;
  value_2: string;
  level_3: string;
  value_3: string;
  is_active: boolean;
}

export interface MappingFilters {
  type?: string;
  active?: boolean;
  searchTerm?: string;
}

// New hierarchical types
export interface HierarchyRow {
  id: number;
  values: string[];
  fullMapping: CentralMapping;
}

export interface HierarchicalTypeData {
  type: string;
  levels: string[];
  hierarchyRows: HierarchyRow[];
}

export interface MappingType {
  type: string;
  count: number;
}

export interface HierarchicalApiResponse {
  success: boolean;
  data?: HierarchicalTypeData | MappingType[];
  error?: string;
} 