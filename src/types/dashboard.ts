// Dashboard-specific types for intuitive lead pipeline visualization

export interface StageMetrics {
  total: number;
  pending: number;
  running: number;
  completed: number;
  failed: number;
  error: number;
  success_rate: number;
  avg_processing_time_hours?: number;
  last_24h_completed: number;
  conversion_rate?: number;
}

export interface StageDetails {
  stage_name: string;
  stage_key: string;
  display_name: string;
  description: string;
  metrics: StageMetrics;
  prerequisites: string[];
  next_stage?: string;
  is_final_stage: boolean;
  business_impact: string;
}

export interface LeadPipelineOverview {
  total_leads: number;
  active_leads: number;
  completed_leads: number;
  failed_leads: number;
  overall_conversion_rate: number;
  avg_pipeline_time_days: number;
  daily_throughput: number;
  bottleneck_stage?: string;
  health_score: number; // 0-100
}

export interface ContactPipelineData {
  overview: LeadPipelineOverview;
  stages: StageDetails[];
  funnel_data: {
    stage_name: string;
    count: number;
    conversion_rate: number;
    drop_off_rate: number;
  }[];
  recent_activity: {
    stage: string;
    count: number;
    timestamp: string;
    trend: 'up' | 'down' | 'stable';
  }[];
}

export interface CompanyPipelineData {
  overview: {
    total_companies: number;
    active_companies: number;
    completed_companies: number;
    failed_companies: number;
    overall_conversion_rate: number;
  };
  stages: StageDetails[];
  funnel_data: {
    stage_name: string;
    count: number;
    conversion_rate: number;
  }[];
}

export interface DashboardFilters {
  dateRange: '1d' | '7d' | '30d' | '90d';
  source?: string;
  region?: string;
  capital_type?: string;
  stage?: string;
}

export interface TimelineDataPoint {
  date: string;
  stage: string;
  completed: number;
  failed: number;
  pending: number;
  running: number;
}

export interface ErrorAnalysis {
  error_type: string;
  count: number;
  percentage: number;
  recent_examples: {
    entity_id: number;
    entity_type: 'contact' | 'company';
    stage: string;
    error_message: string;
    occurred_at: string;
    retry_count: number;
  }[];
  suggested_actions: string[];
}

export interface PerformanceMetrics {
  processing_speed: {
    stage: string;
    avg_time_minutes: number;
    median_time_minutes: number;
    p95_time_minutes: number;
  }[];
  throughput: {
    stage: string;
    per_hour: number;
    per_day: number;
    trend: 'increasing' | 'decreasing' | 'stable';
  }[];
  error_rates: {
    stage: string;
    error_rate_percentage: number;
    trend: 'improving' | 'worsening' | 'stable';
  }[];
}

export interface DashboardResponse {
  success: boolean;
  data: {
    contacts: ContactPipelineData;
    companies: CompanyPipelineData;
    performance: PerformanceMetrics;
    errors: ErrorAnalysis[];
    timeline: TimelineDataPoint[];
  };
  metadata: {
    generated_at: string;
    filters_applied: DashboardFilters;
    data_freshness: string;
    next_refresh_at: string;
  };
}

// Business-friendly stage mappings
export const CONTACT_STAGE_MAPPING = {
  email_verification: {
    display_name: "Email Validation",
    description: "Verifying email addresses for deliverability",
    business_impact: "Ensures high email delivery rates and protects sender reputation",
    icon: "mail-check"
  },
  osint: {
    display_name: "Lead Research", 
    description: "Gathering intelligence and background information",
    business_impact: "Enriches lead profiles for better targeting and personalization",
    icon: "search"
  },
  overview_extraction: {
    display_name: "Profile Building",
    description: "Extracting and structuring lead information",
    business_impact: "Creates comprehensive lead profiles for sales teams",
    icon: "user-plus"
  },
  classification: {
    display_name: "Lead Scoring",
    description: "Categorizing and scoring lead quality",
    business_impact: "Prioritizes high-value prospects for sales focus",
    icon: "target"
  },
  contact_enrichment: {
    display_name: "Contact Enrichment",
    description: "Unified OSINT research, profile extraction, and classification",
    business_impact: "Comprehensive lead enrichment in a single step for maximum efficiency",
    icon: "sparkles"
  },
  email_generation: {
    display_name: "Content Creation",
    description: "Generating personalized email content",
    business_impact: "Creates tailored messaging to improve response rates",
    icon: "edit"
  },
  email_sending: {
    display_name: "Outreach Delivery",
    description: "Sending personalized emails to prospects",
    business_impact: "Initiates sales conversations with qualified leads",
    icon: "send"
  }
} as const;

export const COMPANY_STAGE_MAPPING = {
  website_scraping: {
    display_name: "Company Research",
    description: "Extracting information from company websites",
    business_impact: "Gathers company intelligence for better targeting",
    icon: "globe"
  },
  company_overview: {
    display_name: "Company Profiling",
    description: "Creating comprehensive company profiles",
    business_impact: "Provides context for personalized outreach",
    icon: "building"
  },
  company_overview_v2: {
    display_name: "Enhanced Company Profiling",
    description: "Creating comprehensive company profiles with advanced AI extraction",
    business_impact: "Provides detailed company intelligence for highly personalized outreach",
    icon: "building-2"
  }
} as const;
