  // Processing State Management Types

export type ContactProcessingState = 
  | 'email_unverified'
  | 'email_verified'
  | 'osint_pending'
  | 'osint_completed'
  | 'classification_pending'
  | 'classification_completed'
  | 'investment_criteria_pending'
  | 'investment_criteria_completed'
  | 'email_generation_pending'
  | 'email_generated'
  | 'email_sent'
  | 'failed'
  | 'error';

export type CompanyProcessingState = 
  | 'website_unprocessed'
  | 'website_scraped'
  | 'overview_pending'
  | 'overview_completed'
  | 'investment_criteria_pending'
  | 'investment_criteria_completed'
  | 'failed'
  | 'error';

export type NewsProcessingState = 
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'error';

// Individual processing stage status
export type ProcessingStatus = 
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'error';

export type ProcessingStage = 
  | 'email_validation'
  | 'contact_enrichment_v2'
  | 'contact_investment_criteria'
  | 'email_generation'
  | 'company_overview_v2'
  | 'company_investment_criteria'
  | 'website_scraping'
  | 'news_fetch'
  | 'news_enrichment';

// Base stats for each processing stage
export interface StageStats {
  total: number;
  pending: number;
  running: number;
  completed: number;
  failed: number;
  error: number;
  success_rate: number;
  last_processed?: string;
}

// Raw API response structure for contacts
export interface ContactStatsRaw {
  total_contacts: number | string;
  email_validation_total: number | string;
  email_validation_pending: number | string;
  email_validation_completed: number | string;
  email_validation_failed: number | string;
  email_validation_error: number | string;

  email_generation_pending: number | string;
  email_generation_completed: number | string;
  email_generation_failed: number | string;
  email_generation_error: number | string;
}

// Raw API response structure for companies
export interface CompanyStatsRaw {
  total_companies: number | string;

  web_crawler_pending: number | string;
  web_crawler_completed: number | string;
  web_crawler_failed: number | string;
  web_crawler_error: number | string;
}

// Raw API response structure for news
export interface NewsStatsRaw {
  total_news: number | string;
  fetch_pending: number | string;
  fetch_completed: number | string;
  fetch_failed: number | string;
  enrichment_pending: number | string;
  enrichment_completed: number | string;
  enrichment_failed: number | string;
  enrichment_error: number | string;
}

// Contact processing stats (transformed)
export interface ContactStats {
  email_validation: StageStats;
  email_generation: StageStats;
}

// Company processing stats (transformed)
export interface CompanyStats {
  web_crawler: StageStats;
}

// News processing stats (transformed)
export interface NewsStats {
  news_fetch: StageStats;
  news_enrichment: StageStats;
}

export interface ProcessingError {
  entity_type: 'contact' | 'company' | 'news';
  entity_id: number;
  stage: ProcessingStage;
  error_message: string;
  error_category?: string;
  occurred_at: string;
  retry_count: number;
}

// Raw API response structure
export interface ProcessingStatsRaw {
  stats: {
    contacts: ContactStatsRaw;
    companies: CompanyStatsRaw;
    news: NewsStatsRaw;
  };
  errors: {
    contacts: ProcessingError[];
    companies: ProcessingError[];
    news: ProcessingError[];
  };
  metadata?: {
    timestamp: string;
    filters?: {
      contacts?: string;
      companies?: string;
    };
  };
}

// Complete processing stats (transformed for UI)
export interface ProcessingStats {
  stats: {
    contacts: ContactStatsRaw;
    companies: CompanyStatsRaw;
    news: NewsStatsRaw;
  };
  errors: {
    contacts: ProcessingError[];
    companies: ProcessingError[];
    news: ProcessingError[];
  };
  metadata?: {
    timestamp: string;
    filters?: ProcessingFilters;
    unified_view?: {
      total_entities: number | string;
      total_contacts: number | string;
      total_companies: number | string;
    };
  };
}

export interface ProcessingJob {
  id: string;
  type: ProcessingStage;
  entity_type: 'contact' | 'company' | 'news';
  entity_id: number;
  status: ProcessingStatus;
  priority: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  metadata?: Record<string, any>;
}

export interface TimelineEntry {
  date: string;
  stage: ProcessingStage;
  entity_type: 'contact' | 'company' | 'news';
  processed_count: number;
}

export interface ProcessingFilter {
  entity_type?: 'contact' | 'company' | 'news' | 'all';
  state?: string;
  date_range?: {
    start: string;
    end: string;
  };
  stage?: ProcessingStage;
}

export interface ProcessingConfig {
  batch_size: number;
  concurrency: number;
  retry_attempts: number;
  enabled_stages: ProcessingStage[];
}

// Contact with processing information
export interface ContactWithProcessing {
  contact_id: number;
  first_name: string;
  last_name: string;
  email: string;
  company_name: string;
  processing_state: ContactProcessingState;
  email_verification_status: ProcessingStatus;
  email_verification_error?: string;
  osint_status: ProcessingStatus;
  osint_error?: string;
  overview_extraction_status: ProcessingStatus;
  overview_extraction_error?: string;
  classification_status: ProcessingStatus;
  classification_error?: string;
  email_generation_status: ProcessingStatus;
  email_generation_error?: string;
  email_sending_status: ProcessingStatus;
  email_sending_error?: string;
  processing_error_count: number;
  last_processed_at?: string;
  can_advance: boolean;
}

// Company with processing information  
export interface CompanyWithProcessing {
  company_id: number;
  company_name: string;
  company_website: string;
  processing_state: CompanyProcessingState;
  website_scraping_status: ProcessingStatus;
  website_scraping_error?: string;
  company_overview_status: ProcessingStatus;
  company_overview_error?: string;
  processing_error_count: number;
  last_processed_at?: string;
  can_advance: boolean;
  contact_count: number;
}

// News with processing information
export interface NewsWithProcessing {
  news_id: number;
  title: string;
  url: string;
  fetched: boolean;
  processing_state: NewsProcessingState;
  html_fetching_status: ProcessingStatus;
  html_fetching_error?: string;
  processing_error_count: number;
  last_processed_at?: string;
  can_advance: boolean;
}

// API Response types
export interface ProcessingResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_prev: boolean;
} 

export interface ContactData extends EntityData {
  contact_id: number
  first_name?: string
  last_name?: string
  full_name?: string
  title?: string
  email: string
  linkedin_url?: string
  company_id?: number
  company_name?: string
  company_website?: string
  industry?: string
  contact_country?: string
  phone_number?: string
  processing_state: ContactProcessingState
  extracted?: boolean
  searched?: boolean
  category?: string
  capital_type?: string
  capital_position?: string
  company_type?: string
  extra_attrs?: any
}

export interface EnrichmentResult {
  osint_profile: string
  executive_summary: string
  career_timeline: string[]
  notable_activities: Array<{
    title: string
    date: string
    snippet: string
    url?: string
  }>
  education: string[]
  personal_tidbits: string[]
  conversation_hooks: string[]
  sources: string[]
  companyType: string
  capitalPosition: string
  confidence: number
  reasoning: string
}

export interface EntityData {
  id: number
}

export interface ProcessingFilters {
  // Basic filters
  source?: string | string[]
  dateRange?: string
  
  // Contact processing status filters
  contact_enrichment_v2_status?: string
  contact_email_verification_status?: string
  email_generation_status?: string
  email_sending_status?: string

  // Company processing status filters
  company_overview_v2_status?: string
  website_scraping_status?: string
  
  // Contact-specific filters
  emailStatus?: string[]
  contactCompanyType?: string[]
  contactCapitalPosition?: string[]
  jobTier?: string[]
  
  // Geographic filters
  contactCountries?: string[]
  contactStates?: string[]
  contactCities?: string[]
  regions?: string[]
  states?: string[]
  cities?: string[]
  countries?: string[]
  
  // Investment criteria filters
  capitalPosition?: string[]
  propertyTypes?: string[]
  strategies?: string[]
  dealSizeMin?: number
  dealSizeMax?: number
  targetReturnMin?: number
  targetReturnMax?: number
  
  // Boolean flags
  extracted?: boolean
  searched?: boolean
  emailGenerated?: boolean
  enriched?: boolean
  hasSmartleadId?: boolean
  hasBeenReachedOut?: boolean
}

export interface ProcessorOptions {
  limit?: number
  singleId?: number
  multiIds?: number[]
  filters?: ProcessingFilters
  batchSize?: number
  batchDelay?: number // Delay between batches in milliseconds (deprecated - use bottleneckConfig instead)
  entityType?: 'contact' | 'company' | 'both'
  campaignId?: string // Add campaign ID for email generation and Smartlead sync
  bottleneckConfig?: import('../lib/processors/BaseProcessor').BottleneckConfig // Processor-specific Bottleneck configuration
}

export interface ProcessorResult {
  processed: number
  successful: number
  failed: number
  errors: string[]
}


export interface MappingData {
  [key: string]: string[]
}

export interface CompanyData extends EntityData {
  company_id: number
  company_name: string
  company_website: string
  industry?: string
  processing_state: CompanyProcessingState
}

// Unified entity data that combines contact and company information
export interface UnifiedEntityData extends EntityData {
  // Common fields
  id: number // Primary ID (contact_id or company_id)
  entity_type: 'contact' | 'company'
  
  // Contact-specific fields (when entity_type is 'contact')
  contact_id?: number
  first_name?: string
  last_name?: string
  full_name?: string
  title?: string
  email?: string
  linkedin_url?: string
  company_id?: number
  contact_country?: string
  contact_city?: string
  contact_state?: string
  phone_number?: string
  contact_processing_state?: ContactProcessingState
  extracted?: boolean
  searched?: boolean
  category?: string
  capital_type?: string
  capital_position?: string
  company_type?: string
  extra_attrs?: any
  
  // Company-specific fields (when entity_type is 'company')
  company_name?: string
  company_website?: string
  industry?: string
  company_processing_state?: CompanyProcessingState
  
  // Shared fields (available for both types)
  source?: string
  created_at?: string
  updated_at?: string
}
