export interface CompanyFilters {
  // Pagination
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'

  // Universal Search
  searchTerm?: string
  companyId?: number
  companyName?: string

  // Company Profile Metrics (from company_extracted_data table)
  companyType?: string[]           // companytype field
  businessModel?: string           // businessmodel field (text search)
  companyIndustry?: string[]       // companyindustry field
  capitalPosition?: string[]       // capitalposition field
  fundSizeMin?: number            // fundsize parsed to thousands
  fundSizeMax?: number
  aumMin?: number                 // aum parsed to thousands  
  aumMax?: number
  numberOfPropertiesMin?: number  // numberofproperties field
  numberOfPropertiesMax?: number
  headquarters?: string[]         // headquarters field
  numberOfOfficesMin?: number     // numberofoffices field
  numberOfOfficesMax?: number
  foundedYearMin?: number         // foundedyear field
  foundedYearMax?: number
  numberOfEmployeesMin?: number   // numberofemployees parsed from range
  numberOfEmployeesMax?: number
  
  // Track Record Metrics (from company_extracted_data table)
  totalTransactions?: string       // totaltransactions field (text search)
  totalSquareFeet?: string         // totalsquarefeet field (text search)
  totalUnits?: string              // totalunits field (text search)
  historicalReturns?: string       // historicalreturns field (text search)
  portfolioValue?: string          // portfoliovalue field (text search)
  
  // Investment Focus & Strategy (from company_extracted_data JSONB fields)
  investmentFocus?: string[]       // investmentfocus JSONB array
  propertyTypes?: string[]         // propertytypes JSONB array
  strategies?: string[]            // strategies JSONB array
  assetClasses?: string[]          // assetclasses JSONB array
  extractedGeographicFocus?: string[]  // geographicfocus JSONB array (extracted data)
  targetMarkets?: string[]         // targetmarkets JSONB array
  capitalSources?: string[]        // capitalsources JSONB array
  financialProducts?: string[]     // financialproducts JSONB array
  partnerships?: string[]          // partnerships JSONB array
  officeLocations?: string[]       // officelocations JSONB array
  
  // Deal Size & Investment Criteria (from company_extracted_data)
  extractedDealSizeMin?: string    // dealsize field (text search, extracted data)
  extractedDealSizeMax?: string
  minimumDealSize?: string         // minimumdealsize field (text search)
  maximumDealSize?: string         // maximumdealsize field (text search)
  holdPeriod?: string              // holdperiod field (text search)
  riskProfile?: string             // riskprofile field (text search)
  targetReturn?: string            // targetreturn field (text search)
  
  // Contact Information (from company_extracted_data)
  website?: string                 // website field (text search)
  mainPhone?: string               // mainphone field (text search)
  mainEmail?: string               // mainemail field (text search)
  linkedinUrl?: string             // linkedin_url field (text search)
  
  // Mission & Strategy (from company_extracted_data)
  mission?: string                 // mission field (text search)
  approach?: string                // approach field (text search)
  
  // Basic Company Fields
  industry?: string[]             // companies.industry
  companyState?: string[]         // companies.company_state
  companyCity?: string[]          // companies.company_city
  companyCountry?: string[]       // companies.company_country
  processed?: boolean             // companies.processed
  extracted?: boolean             // companies.extracted
  
  // Legacy Overview Structure Fields (from companies.overview JSONB)
  primaryIndustry?: string[]       // overview.primary_industry
  coreCapitalPosition?: string[]   // overview.capital_position
  investmentStrategy?: string[]    // overview.investment_strategy
  holdHorizon?: string[]          // overview.hold_horizon
  
  // Legacy Overview Geographic Focus (from overview.investment_program.geography)
  geographicFocus?: string[]
  
  // Legacy Overview Property Types (from overview.investment_program.property_types)
  legacyPropertyTypes?: string[]
  
  // Property Subcategories (from hierarchical filtering)
  propertySubcategories?: string[]
  
  // Asset Types (from overview.investment_program.asset_type)
  assetTypes?: string[]
  
  // Deal Structure (from overview.investment_program.deal_structure)
  dealStructure?: string[]
  
  // Capital Commitments (from overview.capital_commitments)
  debtRangeMin?: string
  debtRangeMax?: string
  equityRangeMin?: string
  equityRangeMax?: string
  
  // Legacy Investment Focus (from overview.companyProfile.investmentFocus)
  legacyInvestmentFocus?: string[]
  
  // Processing Status Filters
  processingState?: string[]       // companies.processing_state
  websiteScrapingStatus?: string[] // companies.website_scraping_status
  companyOverviewStatus?: string[] // companies.company_overview_status
  
  // Investment Criteria Relationship Filters
  hasInvestmentCriteria?: boolean
  targetReturnMin?: number
  targetReturnMax?: number
  dealSizeMin?: number            // From related investment criteria
  dealSizeMax?: number
  minHoldPeriodMin?: number       // From related investment criteria 
  minHoldPeriodMax?: number
  maxHoldPeriodMin?: number
  maxHoldPeriodMax?: number
  criteriaPropertyTypes?: string[]  // From related investment criteria
  criteriaStrategies?: string[]     // From related investment criteria
  criteriaRegions?: string[]        // From related investment criteria
  criteriaStates?: string[]         // From related investment criteria
  criteriaCities?: string[]         // From related investment criteria
}

export interface CompanyFilterOptions {
  // Company Profile Options (from central_mapping table)
  companyTypes?: string[]
  businessModels?: string[]
  companyIndustries?: string[]
  capitalPositions?: string[]
  
  // Geographic Options
  headquarters?: string[]
  states?: string[]
  cities?: string[]
  countries?: string[]
  geographicFocus?: string[]
  
  // Legacy Overview Options
  primaryIndustries?: string[]
  investmentStrategies?: string[]
  holdHorizons?: string[]
  
  // Property & Asset Options
  propertyTypes?: string[]
  propertySubcategories?: string[]
  assetTypes?: string[]
  dealStructures?: string[]
  investmentFocus?: string[]
  
  // Processing Status Options
  processingStates?: string[]
  websiteScrapingStatuses?: string[]
  companyOverviewStatuses?: string[]
  
  // Investment Criteria Options
  criteriaPropertyTypes?: string[]
  criteriaStrategies?: string[]
  criteriaRegions?: string[]
  criteriaStates?: string[]
  criteriaCities?: string[]
  
  // Hierarchical Mappings for advanced filtering
  hierarchicalMappings?: {
    [type: string]: {
      flat: string[]
      hierarchical: {
        [parent: string]: {
          value: string
          children: {
            [childType: string]: string[]
          }
        }
      }
    }
  }
  
  // Range Options
  ranges?: {
    fundSize?: { min: number; max: number }
    aum?: { min: number; max: number }
    numberOfProperties?: { min: number; max: number }
    numberOfOffices?: { min: number; max: number }
    foundedYear?: { min: number; max: number }
    numberOfEmployees?: { min: number; max: number }
    targetReturn?: { min: number; max: number }
    dealSize?: { min: number; max: number }
    holdPeriod?: { min: number; max: number }
  }
}

export interface Company {
  company_id: number
  company_name: string
  company_linkedin?: string
  company_address?: string
  company_city?: string
  company_state?: string
  company_zip?: string
  company_website?: string
  industry?: string
  company_phone?: string
  founded_year?: number
  company_country?: string
  extra_attrs?: any
  created_at?: string
  updated_at?: string
  canonical_handle?: string
  processed?: boolean
  extracted?: boolean
  overview?: CompanyOverview
  source?: string
  
  // Processing status fields
  processing_state?: string
  website_scraping_status?: string
  website_scraping_date?: string
  website_scraping_error?: string
  company_overview_status?: string
  company_overview_date?: string
  company_overview_error?: string
  last_processed_stage?: string
  last_processed_at?: string
  processing_error_count?: number
  
  // Calculated fields (from investment criteria relationships)
  investment_criteria_count?: number
  has_investment_criteria?: boolean
  
  // Aggregated investment criteria data
  criteria_target_returns?: number[]
  criteria_deal_sizes?: number[]
  criteria_property_types?: string[]
  criteria_strategies?: string[]
  criteria_regions?: string[]
  criteria_states?: string[]
  criteria_cities?: string[]
  
  // Overview derived metrics
  fund_size_parsed?: number      // Parsed from overview.companyProfile.fundSize
  aum_parsed?: number           // Parsed from overview.companyProfile.aum
  employees_min?: number        // Parsed from overview.companyProfile.numberOfEmployees range
  employees_max?: number        // Parsed from overview.companyProfile.numberOfEmployees range
}

export interface CompanyOverview {
  // Company Profile (from company-overview.ts prompt)
  companyProfile?: {
    companyName?: string
    companyType?: string
    companyIndustry?: string
    capitalPosition?: string
    businessModel?: string
    fundSize?: string            // e.g. "$500M", "$2.3B"
    aum?: string                // Assets under management
    numberOfProperties?: number
    headquarters?: string
    numberOfOffices?: number
    officeLocations?: string[]
    foundedYear?: number
    numberOfEmployees?: string   // Range or specific number
    investmentFocus?: string[]
  }
  
  // Executive Team
  executiveTeam?: Array<{
    first_name?: string
    last_name?: string
    full_name?: string
    title?: string
    headline?: string
    seniority?: string
    email?: string
    personal_email?: string
    email_status?: string
    linkedin_url?: string
    contact_city?: string
    contact_state?: string
    contact_country?: string
    phone?: string
    twitter_url?: string
    facebook_url?: string
    instagram_url?: string
    youtube_url?: string
  }>
  
  // Recent Deals
  recentDeals?: Array<{
    property?: string
    location?: string
    dealType?: string
    dealTypeNormalized?: string
    dealTypeNormalizedSpecialty?: string
    capital_position?: string
    strategies?: string
    amount?: string
    date?: string
    propertyTypes?: string[]
    propertySubcategories?: string[]
    squareFeet?: string
    units?: number
  }>
  
  // Investment Strategy
  investmentStrategy?: {
    mission?: string
    approach?: string
  }
  
  // Track Record
  trackRecord?: {
    totalTransactions?: string
    totalSquareFeet?: string
    totalUnits?: string
    historicalReturns?: string
    portfolioValue?: string
  }
  
  // Partnerships
  partnerships?: string[]
  
  // Contact Info
  contactInfo?: {
    website?: string
    mainPhone?: string
    mainEmail?: string
    socialMedia?: {
      linkedin?: string
      twitter?: string
      facebook?: string
      instagram?: string
      youtube?: string
    }
  }
  
  // Legacy fields (existing overview structure)
  hold_horizon?: string
  lending_program?: any
  capital_position?: string[]
  primary_industry?: string
  structure_history?: string
  cre_ecosystem_role?: string
  executive_contacts?: Array<{
    name?: string
    email?: string
    phone?: string
    title?: string
  }>
  investment_program?: {
    geography?: string
    asset_type?: string[]
    hold_period?: string
    deal_structure?: string[]
    property_types?: string[]
    equity_size_range?: string
  }
  capital_commitments?: {
    debt_range?: string
    equity_range?: string
  }
  investment_strategy?: string[]
  recent_transactions?: Array<{
    location?: string
    asset_type?: string
    close_date?: string
    capital_deployed?: string
  }>
}

export const COMPANY_SORT_OPTIONS = [
  // Default Options
  { value: 'updated_at', label: 'Last Updated (Newest First)', icon: 'Clock' },
  { value: 'created_at', label: 'Created Date (Newest First)', icon: 'Calendar' },
  { value: 'company_name', label: 'Company Name (A-Z)', icon: 'Building' },
  
  // Company Profile Metrics (from company_extracted_data - prioritize non-null)
  { value: 'extracted_fund_size', label: 'Fund Size (Non-null First, Largest)', icon: 'DollarSign' },
  { value: 'extracted_aum', label: 'AUM (Non-null First, Largest)', icon: 'Banknote' },
  { value: 'extracted_founded_year', label: 'Founded Year (Non-null First, Newest)', icon: 'Calendar' },
  { value: 'extracted_number_of_properties', label: 'Properties Count (Non-null First, Most)', icon: 'Building2' },
  { value: 'extracted_number_of_offices', label: 'Offices Count (Non-null First, Most)', icon: 'Building' },
  { value: 'extracted_headquarters', label: 'Headquarters (Non-null First, A-Z)', icon: 'MapPin' },
  
  // Company Type & Industry (prioritize non-null)
  { value: 'extracted_company_type', label: 'Company Type (Non-null First, A-Z)', icon: 'Factory' },
  { value: 'extracted_company_industry', label: 'Industry (Non-null First, A-Z)', icon: 'Briefcase' },
  { value: 'extracted_capital_position', label: 'Capital Position (Non-null First, A-Z)', icon: 'Crown' },
  
  // Track Record & Performance (prioritize non-null)
  { value: 'extracted_total_transactions', label: 'Total Transactions (Non-null First)', icon: 'Activity' },
  { value: 'extracted_total_square_feet', label: 'Total Square Feet (Non-null First)', icon: 'Home' },
  { value: 'extracted_total_units', label: 'Total Units (Non-null First)', icon: 'Building2' },
  { value: 'extracted_portfolio_value', label: 'Portfolio Value (Non-null First)', icon: 'PieChart' },
  { value: 'extracted_historical_returns', label: 'Historical Returns (Non-null First)', icon: 'TrendingUp' },
  
  // Investment Focus & Strategy (prioritize non-null)
  { value: 'extracted_investment_focus_count', label: 'Investment Focus Areas (Most First)', icon: 'Target' },
  { value: 'extracted_property_types_count', label: 'Property Types Count (Most First)', icon: 'Building' },
  { value: 'extracted_strategies_count', label: 'Strategies Count (Most First)', icon: 'LineChart' },
  { value: 'extracted_geographic_focus_count', label: 'Geographic Markets (Most First)', icon: 'Globe' },
  
  // Deal Size & Criteria (prioritize non-null)
  { value: 'extracted_minimum_deal_size', label: 'Min Deal Size (Non-null First)', icon: 'Calculator' },
  { value: 'extracted_maximum_deal_size', label: 'Max Deal Size (Non-null First)', icon: 'Calculator' },
  { value: 'extracted_target_return', label: 'Target Return (Non-null First)', icon: 'TrendingUp' },
  
  // Contact & Communication (prioritize non-null)
  { value: 'extracted_website', label: 'Website (Non-null First, A-Z)', icon: 'Globe' },
  { value: 'extracted_linkedin_url', label: 'LinkedIn Profile (Non-null First)', icon: 'User' },
  { value: 'extracted_main_phone', label: 'Phone Number (Non-null First)', icon: 'Phone' },
  { value: 'extracted_main_email', label: 'Email Address (Non-null First)', icon: 'Mail' },
  
  // Processing Status & Data Quality
  { value: 'last_processed_at', label: 'Last Processed (Recent First)', icon: 'Clock' },
  { value: 'processing_error_count', label: 'Processing Errors (Fewest First)', icon: 'AlertCircle' },
  { value: 'extracted_data_completeness', label: 'Data Completeness (Most Complete First)', icon: 'BarChart3' },
  
  // Investment Criteria Related (from relationships)
  { value: 'investment_criteria_count', label: 'Investment Criteria Count (Most First)', icon: 'Target' },
  { value: 'criteria_target_return_max', label: 'Criteria Target Return (Highest First)', icon: 'TrendingUp' },
  { value: 'criteria_deal_size_max', label: 'Criteria Deal Size (Largest First)', icon: 'DollarSign' },
  
  // Contact & Team Data
  { value: 'contact_count', label: 'Contact Count (Most First)', icon: 'Users' },
  { value: 'executive_team_count', label: 'Executive Team Size (Most First)', icon: 'Users' },
] as const

export type CompanySortOption = typeof COMPANY_SORT_OPTIONS[number]['value'] 