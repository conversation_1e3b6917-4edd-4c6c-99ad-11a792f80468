import { useState, useCallback } from "react";
import { CSVParserService, ParsedData } from "@/lib/services/CSVParserService";
import { UploadService } from "@/lib/services/UploadService";

// Remove the processor import - we'll use the API instead
export interface InvestmentCriteriaRecord {
  id: string;
  capitalPosition: string;
  dealSize: string;
  recordType: 'investment_criteria' | 'debt_instrument';
  location: {
    country?: string[];
    region?: string[];
    state?: string[];
    city?: string[];
    focus?: string[];
  };
  propertyType: {
    propertyTypes?: string[];
    propertySubTypes?: string[];
    strategies?: string[];
  };
  dealSizeInfo: {
    minimumDealSize?: number;
    maximumDealSize?: number;
  };
  loanDetails?: {
    loanProgram?: string[];
    structuredLoanTranche?: string[];
    loanType?: string[];
    loanTerm?: {
      min?: number;
      max?: number;
      years?: number;
    };
    interestRates?: {
      sofr?: number;
      wsj?: number;
      prime?: number;
      libor?: number;
      fiveYT?: number;
      tenYT?: number;
      base?: number;
    };
    loanToValue?: {
      min?: number;
      max?: number;
    };
    loanToCost?: {
      min?: number;
      max?: number;
    };
    fees?: {
      originationMin?: number;
      originationMax?: number;
      exitMin?: number;
      exitMax?: number;
    };
    dscr?: {
      min?: number;
      max?: number;
    };
    recourseLoan?: boolean;
    closingTime?: number;
  };
  notes?: string;
}

export interface FileUploadState {
  file: File | null;
  loading: boolean;
  error: string | null;
  parsedData: ParsedData | null;
  investmentCriteriaRecords: InvestmentCriteriaRecord[];
  processingErrors: string[];
  processingWarnings: string[];
  uploadId?: number; // Track upload ID for manual triggering
  // Add progress tracking
  uploadProgress: {
    phase: 'idle' | 'uploading' | 'processing' | 'completed' | 'error' | 'waiting_for_trigger' | 'uploaded_ready_for_processing';
    currentRow: number;
    totalRows: number;
    percentage: number;
    statusMessage: string;
  };
}

export interface FileUploadActions {
  setFile: (file: File | null) => void;
  parseFile: (file: File) => Promise<void>;
  processInvestmentCriteria: (headerMappings: Record<string, string>, structuredMappings?: any) => Promise<void>;
  resetUpload: () => void;
  validateFile: (file: File) => { valid: boolean; error?: string };
  getFileInfo: (file: File) => any;
  triggerWorkerManually: (uploadId: number) => Promise<void>;
  checkUploadStatus: (uploadId: number) => Promise<void>;
  setUploadId: (uploadId: number) => void;
}

export function useFileUpload(): [FileUploadState, FileUploadActions] {
  const [state, setState] = useState<FileUploadState>({
    file: null,
    loading: false,
    error: null,
    parsedData: null,
    investmentCriteriaRecords: [],
    processingErrors: [],
    processingWarnings: [],
    uploadId: undefined, // Initialize uploadId
    // Add progress tracking
    uploadProgress: {
      phase: 'idle',
      currentRow: 0,
      totalRows: 0,
      percentage: 0,
      statusMessage: 'Ready to upload'
    }
  });

  const setFile = useCallback((file: File | null) => {
    setState(prev => ({
      ...prev,
      file,
      error: null,
      parsedData: null,
      investmentCriteriaRecords: [],
      processingErrors: [],
      processingWarnings: [],
      uploadId: undefined, // Clear uploadId on new file
      uploadProgress: {
        phase: 'idle',
        currentRow: 0,
        totalRows: 0,
        percentage: 0,
        statusMessage: 'Ready to upload'
      }
    }));
  }, []);

  const parseFile = useCallback(async (file: File) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Validate file first
      const validation = CSVParserService.validateFile(file);
      if (!validation.valid) {
        setState(prev => ({
          ...prev,
          loading: false,
          error: validation.error || "Invalid file"
        }));
        return;
      }

      // Parse the file
      const result = await CSVParserService.parseFile(file);
      
      if (result.success && result.data) {
        setState(prev => ({
          ...prev,
          loading: false,
          parsedData: result.data || null,
          file
        }));
      } else {
        setState(prev => ({
          ...prev,
          loading: false,
          error: result.error || "Failed to parse file"
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "Unknown error occurred"
      }));
    }
  }, []);

  const processInvestmentCriteria = useCallback(async (headerMappings: Record<string, string>, structuredMappings?: any) => {
    if (!state.parsedData?.data || !state.file) return;

    const totalRows = state.parsedData.data.length;
    
    setState(prev => ({ 
      ...prev, 
      loading: true,
      uploadProgress: {
        phase: 'uploading',
        currentRow: 0,
        totalRows,
        percentage: 0,
        statusMessage: 'Preparing upload...'
      }
    }));

    try {
      // Step 1: Send original file directly to backend for processing
      setState(prev => ({ 
        ...prev,
        uploadProgress: {
          ...prev.uploadProgress,
          statusMessage: 'Uploading file to server...'
        }
      }));

      const formData = new FormData();
      
      // Send the original file directly - no CSV recreation needed
      formData.append('file', state.file);
      formData.append('headerMappings', JSON.stringify(headerMappings));
      
      // IMPORTANT: Send structured mappings if available
      if (structuredMappings) {
        formData.append('structuredMappings', JSON.stringify(structuredMappings));
      }
      
      formData.append('source', `Upload ${new Date().toISOString().split('T')[0]}`);

      // Store data using async upload API (backend will handle file parsing)
      const uploadResponse = await fetch('/api/investors/upload-async', {
        method: 'POST',
        body: formData
      });

      if (!uploadResponse.ok) {
        throw new Error(`Upload failed: ${uploadResponse.statusText}`);
      }

      const uploadResult = await uploadResponse.json();
      
      if (!uploadResult.success || !uploadResult.upload_id) {
        throw new Error(uploadResult.error || 'Failed to store upload data');
      }

      const uploadId = uploadResult.upload_id;
      setState(prev => ({ ...prev, uploadId })); // Set uploadId
      
      // Step 2: Data uploaded successfully, but auto-trigger is disabled
      setState(prev => ({ 
        ...prev,
        loading: false, // Stop loading since upload is complete
        uploadProgress: {
          phase: 'waiting_for_trigger',
          currentRow: 0,
          totalRows,
          percentage: 10, // 10% for upload completion
          statusMessage: 'Data uploaded successfully! Manual worker trigger required to start processing.'
        }
      }));

      // AUTO TRIGGER COMMENTED OUT - Worker needs to be triggered manually
      // Note: The user can now call triggerWorkerManually(uploadId) to start processing

    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "Processing failed",
        uploadProgress: {
          ...prev.uploadProgress,
          phase: 'error',
          statusMessage: `Error: ${error instanceof Error ? error.message : "Processing failed"}`
        }
      }));
    }
  }, [state.parsedData, state.file]);

  const resetUpload = useCallback(() => {
    setState({
      file: null,
      loading: false,
      error: null,
      parsedData: null,
      investmentCriteriaRecords: [],
      processingErrors: [],
      processingWarnings: [],
      uploadId: undefined, // Clear uploadId on reset
      uploadProgress: {
        phase: 'idle',
        currentRow: 0,
        totalRows: 0,
        percentage: 0,
        statusMessage: 'Ready to upload'
      }
    });
  }, []);

  const validateFile = useCallback((file: File) => {
    return CSVParserService.validateFile(file);
  }, []);

  const getFileInfo = useCallback((file: File) => {
    return CSVParserService.getFileInfo(file);
  }, []);

  const triggerWorkerManually = useCallback(async (uploadId: number) => {
    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
      uploadProgress: {
        ...prev.uploadProgress,
        phase: 'processing',
        statusMessage: 'Triggering worker...'
      }
    }));

    try {
      const workerResponse = await fetch('/api/workers/process-uploads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          upload_id: uploadId
        })
      });

      if (!workerResponse.ok) {
        throw new Error(`Failed to trigger worker: ${workerResponse.statusText}`);
      }

      console.log('Worker triggered successfully for upload:', uploadId);
      
      // Start polling for progress updates
      await pollUploadProgress(uploadId);

    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "Failed to trigger worker",
        uploadProgress: {
          ...prev.uploadProgress,
          phase: 'error',
          statusMessage: `Error triggering worker: ${error instanceof Error ? error.message : "Unknown error"}`
        }
      }));
    }
  }, []);

  const pollUploadProgress = useCallback(async (uploadId: number) => {
    let completed = false;
    let attempts = 0;
    const maxAttempts = 300; // 15 minutes max (3 second intervals)
    
    setState(prev => ({ 
      ...prev,
      uploadProgress: {
        ...prev.uploadProgress,
        statusMessage: 'Worker started - processing data in background...'
      }
    }));
    
    while (!completed && attempts < maxAttempts) {
      attempts++;
      
      try {
        // Check upload status
        const statusResponse = await fetch(`/api/investors/upload-status/${uploadId}`);
        
        if (!statusResponse.ok) {
          throw new Error(`Failed to get upload status: ${statusResponse.statusText}`);
        }
        
        const statusResult = await statusResponse.json();
        
        if (statusResult.success) {
          const processedRows = statusResult.progress?.processedRows || 0;
          const totalRows = statusResult.progress?.totalRows || state.parsedData?.data.length || 0;
          const progressPercentage = Math.min(10 + Math.round((processedRows / totalRows) * 80), 90); // 10-90% for processing
          
          setState(prev => ({ 
            ...prev,
            uploadProgress: {
              ...prev.uploadProgress,
              currentRow: processedRows,
              totalRows,
              percentage: progressPercentage,
              statusMessage: `Worker processing: ${processedRows}/${totalRows} rows. Companies: ${statusResult.stats?.companiesProcessed || 0}, Contacts: ${statusResult.stats?.contactsProcessed || 0}`
            }
          }));
          
          // Check if processing is complete
          if (statusResult.status === 'completed') {
            completed = true;
            
            // Get final results
            try {
              const resultsResponse = await fetch(`/api/investors/upload-results/${uploadId}`);
              
              if (resultsResponse.ok) {
                const resultsData = await resultsResponse.json();
                
                setState(prev => ({
                  ...prev,
                  loading: false,
                  investmentCriteriaRecords: resultsData.records || [],
                  processingErrors: resultsData.errors || [],
                  processingWarnings: resultsData.warnings || [],
                  uploadProgress: {
                    phase: 'completed',
                    currentRow: totalRows,
                    totalRows,
                    percentage: 100,
                    statusMessage: `Completed! Worker processed ${totalRows} rows. Created ${resultsData.stats?.companiesCreated || 0} companies, ${resultsData.stats?.contactsCreated || 0} contacts, ${resultsData.stats?.investmentCriteriaCreated || 0} investment criteria.`
                  }
                }));
              } else {
                // Fallback completion message if results API fails
                setState(prev => ({
                  ...prev,
                  loading: false,
                  uploadProgress: {
                    phase: 'completed',
                    currentRow: totalRows,
                    totalRows,
                    percentage: 100,
                    statusMessage: `Processing completed! Worker finished processing ${totalRows} rows.`
                  }
                }));
              }
            } catch (resultsError) {
              console.error('Error getting results:', resultsError);
              setState(prev => ({
                ...prev,
                loading: false,
                uploadProgress: {
                  phase: 'completed',
                  currentRow: totalRows,
                  totalRows,
                  percentage: 100,
                  statusMessage: `Processing completed! Worker finished processing ${totalRows} rows.`
                }
              }));
            }
            
            // Explicitly break out of the while loop
            console.log('Upload processing completed, stopping polling');
            return;
          } else if (statusResult.status === 'failed') {
            completed = true;
            throw new Error(statusResult.errorMessage || 'Processing failed');
          }
          
        } else {
          throw new Error(statusResult.error || 'Failed to get upload status');
        }
        
      } catch (pollingError) {
        console.error('Error polling upload status:', pollingError);
        
        // If polling fails too many times, show warning but continue
        if (attempts % 10 === 0) { // Every 10 attempts (30 seconds), show warning
          setState(prev => ({ 
            ...prev,
            uploadProgress: {
              ...prev.uploadProgress,
              statusMessage: `Worker continues processing... (status check failed, attempt ${attempts})`
            }
          }));
        }
      }
      
      // Wait 3 seconds before next poll (reduced frequency)
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    // If we reached max attempts without completion
    if (attempts >= maxAttempts && !completed) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: "Processing timeout - the operation may still be running in the background",
        uploadProgress: {
          ...prev.uploadProgress,
          phase: 'error',
          statusMessage: "Timeout waiting for processing to complete"
        }
      }));
    }
  }, [state.parsedData]);

  // Manual status check function without polling
  const checkUploadStatus = useCallback(async (uploadId: number) => {
    try {
      const statusResponse = await fetch(`/api/investors/upload-status/${uploadId}`);
      
      if (!statusResponse.ok) {
        throw new Error(`Failed to get upload status: ${statusResponse.statusText}`);
      }
      
      const statusResult = await statusResponse.json();
      
      if (statusResult.success) {
        const processedRows = statusResult.progress?.processedRows || 0;
        const totalRows = statusResult.progress?.totalRows || state.parsedData?.data.length || 0;
        const progressPercentage = Math.min(10 + Math.round((processedRows / totalRows) * 80), 90);
        
        setState(prev => ({ 
          ...prev,
          uploadProgress: {
            ...prev.uploadProgress,
            currentRow: processedRows,
            totalRows,
            percentage: progressPercentage,
            statusMessage: `Status check: ${processedRows}/${totalRows} rows. Companies: ${statusResult.stats?.companiesProcessed || 0}, Contacts: ${statusResult.stats?.contactsProcessed || 0}`
          }
        }));
        
        // Check if processing is complete
        if (statusResult.status === 'completed') {
          setState(prev => ({
            ...prev,
            loading: false,
            uploadProgress: {
              phase: 'completed',
              currentRow: totalRows,
              totalRows,
              percentage: 100,
              statusMessage: `Completed! Worker processed ${totalRows} rows.`
            }
          }));
        } else if (statusResult.status === 'failed') {
          setState(prev => ({
            ...prev,
            loading: false,
            error: statusResult.errorMessage || 'Processing failed',
            uploadProgress: {
              phase: 'error',
              currentRow: processedRows,
              totalRows,
              percentage: progressPercentage,
              statusMessage: `Failed: ${statusResult.errorMessage || 'Processing failed'}`
            }
          }));
        }
      } else {
        throw new Error(statusResult.error || 'Failed to get upload status');
      }
    } catch (error) {
      console.error('Error checking upload status:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to check upload status',
        uploadProgress: {
          ...prev.uploadProgress,
          statusMessage: 'Error checking status'
        }
      }));
    }
  }, [state.parsedData]);

  const setUploadId = useCallback((uploadId: number) => {
    setState(prev => {
      const totalRows = prev.parsedData?.data.length || 0;
      return {
        ...prev,
        uploadId,
        uploadProgress: {
          ...prev.uploadProgress,
          phase: 'waiting_for_trigger',
          totalRows,
          statusMessage: `Upload completed. Ready to process ${totalRows} rows.`
        }
      };
    });
  }, []);

  const actions: FileUploadActions = {
    setFile,
    parseFile,
    processInvestmentCriteria,
    resetUpload,
    validateFile,
    getFileInfo,
    triggerWorkerManually,
    checkUploadStatus,
    setUploadId
  };

  return [state, actions];
} 