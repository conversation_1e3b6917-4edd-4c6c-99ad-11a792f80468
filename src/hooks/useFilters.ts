import { useState, useEffect, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { FilterState, filtersToQueryParams, queryParamsToFilters } from '@/lib/utils'

interface UseFiltersOptions {
  defaultFilters?: FilterState
  persistKey?: string
  onFilterChange?: (filters: FilterState) => void
}

export function useFilters({
  defaultFilters = {},
  persistKey,
  onFilterChange
}: UseFiltersOptions = {}) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // Initialize filters from URL or localStorage
  const initFilters = useCallback((): FilterState => {
    // Try to get filters from URL first
    if (searchParams) {
      const urlFilters = queryParamsToFilters(searchParams as unknown as URLSearchParams)
      if (Object.keys(urlFilters).length > 0) {
        return urlFilters
      }
    }
    
    // Try to get from localStorage if persistKey is provided
    if (persistKey && typeof window !== 'undefined') {
      try {
        const storedFilters = localStorage.getItem(`filters_${persistKey}`)
        if (storedFilters) {
          return JSON.parse(storedFilters)
        }
      } catch (error) {
        console.error('Failed to parse stored filters:', error)
      }
    }
    
    // Fall back to default filters
    return defaultFilters
  }, [searchParams, persistKey, defaultFilters])

  const [filters, setFilters] = useState<FilterState>(initFilters)

  // Update URL when filters change
  useEffect(() => {
    const params = filtersToQueryParams(filters)
    const currentUrl = new URL(window.location.href)
    
    // Preserve pagination parameters
    const page = searchParams?.get('page')
    if (page) params.set('page', page)
    
    const limit = searchParams?.get('limit')
    if (limit) params.set('limit', limit)
    
    const sort = searchParams?.get('sort')
    if (sort) params.set('sort', sort)
    
    const direction = searchParams?.get('direction')
    if (direction) params.set('direction', direction)
    
    // Update URL
    const newSearch = params.toString()
    const newPath = `${window.location.pathname}${newSearch ? '?' + newSearch : ''}`
    
    // Only update if the URL would change
    if (newPath !== window.location.pathname + window.location.search) {
      router.push(newPath, { scroll: false })
    }
    
    // Store in localStorage if persistKey is provided
    if (persistKey) {
      localStorage.setItem(`filters_${persistKey}`, JSON.stringify(filters))
    }
    
    // Call external handler if provided
    if (onFilterChange) {
      onFilterChange(filters)
    }
  }, [filters, router, searchParams, persistKey, onFilterChange])

  // Update filters when URL changes - but only on initial load or explicit URL changes
  // This prevents infinite loops between URL updates and filter state updates
  useEffect(() => {
    // Skip this effect if we're in the middle of a filter-initiated URL update
    if (Object.keys(filters).length > 0) return;
    
    const urlFilters = queryParamsToFilters(searchParams as unknown as URLSearchParams)
    
    // Only update if there are URL filters and they're different from current state
    if (Object.keys(urlFilters).length > 0 && JSON.stringify(urlFilters) !== JSON.stringify(filters)) {
      setFilters(prev => {
        // Preserve any filters that aren't in the URL
        const newFilters = { ...prev }
        
        // Update with URL filters
        Object.entries(urlFilters).forEach(([key, value]) => {
          newFilters[key] = value
        })
        
        return newFilters
      })
    }
  }, [searchParams])

  // Handler to update filters
  const updateFilters = useCallback((newFilters: FilterState) => {
    setFilters(newFilters)
  }, [])

  // Handler to remove a specific filter
  const removeFilter = useCallback((key: string, value?: string | number) => {
    setFilters(prev => {
      const newFilters = { ...prev }
      
      if (value !== undefined && Array.isArray(newFilters[key])) {
        // Remove specific value from array
        newFilters[key] = newFilters[key].filter((item: string | number) => item !== value)
        
        // Remove the key if array is empty
        if (newFilters[key].length === 0) {
          delete newFilters[key]
        }
      } else {
        // Remove the entire key
        delete newFilters[key]
      }
      
      return newFilters
    })
  }, [])

  // Handler to clear all filters
  const clearFilters = useCallback(() => {
    setFilters({})
  }, [])

  return {
    filters,
    updateFilters,
    removeFilter,
    clearFilters
  }
} 