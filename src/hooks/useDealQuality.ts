import { useState, useEffect } from 'react';

interface FieldDetail {
  field: string;
  hasValue: boolean;
  value: any;
  displayName: string;
}

interface CriteriaDetail {
  criteria_id: number;
  capital_position: string[] | null;
  qualityScore: number;
  completedFields: number;
  totalFields: number;
  fieldDetails: FieldDetail[];
  missingFields: string[];
}

interface DealQualityData {
  deal_id: number;
  deal_name: string;
  overall_quality: number;
  deal: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    fieldDetails: FieldDetail[];
    missingFields: string[];
  };
  investment_criteria: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    criteriaCount: number;
    individualCriteria: CriteriaDetail[];
  };
}

interface UseDealQualityReturn {
  qualityData: DealQualityData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useDealQuality = (dealId: number): UseDealQualityReturn => {
  const [qualityData, setQualityData] = useState<DealQualityData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchQualityData = async () => {
    if (!dealId) {
      setError('No deal ID provided');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/deals/${dealId}/quality`);
      if (!response.ok) {
        throw new Error(`Failed to fetch quality data: ${response.statusText}`);
      }
      
      const data = await response.json();
      setQualityData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching quality data');
      setQualityData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQualityData();
  }, [dealId]);

  return {
    qualityData,
    loading,
    error,
    refetch: fetchQualityData
  };
}; 