import { useState, useEffect, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'

export type SortDirection = 'asc' | 'desc'

interface UseSortingOptions {
  defaultField?: string
  defaultDirection?: SortDirection
  persistKey?: string
  onSortChange?: (field: string, direction: SortDirection) => void
}

export function useSorting({
  defaultField = 'updated_at',
  defaultDirection = 'desc',
  persistKey,
  onSortChange
}: UseSortingOptions = {}) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // Initialize sort state from URL or localStorage
  const initSort = useCallback(() => {
    // Try to get from URL first
    if (searchParams) {
      const urlField = searchParams.get('sort')
      const urlDirection = searchParams.get('direction') as SortDirection | null
      
      if (urlField) {
        return {
          field: urlField,
          direction: urlDirection && ['asc', 'desc'].includes(urlDirection) ? urlDirection : defaultDirection
        }
      }
    }
    
    // Try to get from localStorage if persistKey is provided
    if (persistKey && typeof window !== 'undefined') {
      try {
        const storedSort = localStorage.getItem(`sort_${persistKey}`)
        if (storedSort) {
          return JSON.parse(storedSort)
        }
      } catch (error) {
        console.error('Failed to parse stored sort:', error)
      }
    }
    
    // Fall back to defaults
    return { field: defaultField, direction: defaultDirection }
  }, [searchParams, persistKey, defaultField, defaultDirection])

  const [sortField, setSortField] = useState<string>(initSort().field)
  const [sortDirection, setSortDirection] = useState<SortDirection>(initSort().direction)

  // Update URL when sort changes
  useEffect(() => {
    const params = new URLSearchParams(window.location.search)
    
    // Update sort parameters
    params.set('sort', sortField)
    params.set('direction', sortDirection)
    
    // Update URL
    const newSearch = params.toString()
    const newPath = `${window.location.pathname}?${newSearch}`
    
    // Only update if the URL would change
    if (newPath !== window.location.pathname + window.location.search) {
      router.push(newPath, { scroll: false })
    }
    
    // Store in localStorage if persistKey is provided
    if (persistKey) {
      localStorage.setItem(`sort_${persistKey}`, JSON.stringify({ field: sortField, direction: sortDirection }))
    }
    
    // Call external handler if provided
    if (onSortChange) {
      onSortChange(sortField, sortDirection)
    }
  }, [sortField, sortDirection, router, persistKey, onSortChange])

  // Update sort when URL changes
  useEffect(() => {
    const urlField = searchParams?.get('sort')
    const urlDirection = searchParams?.get('direction') as SortDirection | null
    
    if (urlField && urlField !== sortField) {
      setSortField(urlField)
    }
    
    if (urlDirection && ['asc', 'desc'].includes(urlDirection) && urlDirection !== sortDirection) {
      setSortDirection(urlDirection)
    }
  }, [searchParams, sortField, sortDirection])

  // Handler to change sort
  const setSort = useCallback((field: string, direction?: SortDirection) => {
    setSortField(field)
    
    if (direction) {
      setSortDirection(direction)
    } else if (field === sortField) {
      // Toggle direction if same field
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')
    } else {
      // Default to ascending for new field
      setSortDirection('asc')
    }
  }, [sortField])

  return {
    sortField,
    sortDirection,
    setSort
  }
} 