import { useState, useCallback, useRef } from 'react';

export interface UploadProgressState {
  uploading: boolean;
  progress: number;
  status: 'idle' | 'uploading' | 'processing' | 'completed' | 'failed';
  statusDescription: string;
  estimatedTimeRemaining: string | null;
  nextAction: string;
  uploadId: number | null;
  error: string | null;
  result: any;
}

export interface UploadProgressActions {
  startUpload: () => void;
  updateProgress: (uploadId: number) => Promise<void>;
  completeUpload: (result: any) => void;
  failUpload: (error: string) => void;
  reset: () => void;
  pollProgress: (uploadId: number) => void;
  stopPolling: () => void;
}

const POLLING_INTERVAL = 2000; // Poll every 2 seconds

export function useUploadProgress(): [UploadProgressState, UploadProgressActions] {
  const [state, setState] = useState<UploadProgressState>({
    uploading: false,
    progress: 0,
    status: 'idle',
    statusDescription: '',
    estimatedTimeRemaining: null,
    nextAction: '',
    uploadId: null,
    error: null,
    result: null
  });

  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const startUpload = useCallback(() => {
    setState(prev => ({
      ...prev,
      uploading: true,
      progress: 0,
      status: 'uploading',
      statusDescription: 'Uploading file...',
      estimatedTimeRemaining: '2-5 minutes',
      nextAction: 'Please wait while we process your file',
      error: null,
      result: null
    }));
  }, []);

  const updateProgress = useCallback(async (uploadId: number) => {
    try {
      const response = await fetch(`/api/investors/upload-async?upload_id=${uploadId}`);
      const data = await response.json();
      
      if (data.success && data.upload) {
        const upload = data.upload;
        setState(prev => ({
          ...prev,
          uploadId,
          progress: upload.progress_percentage || 0,
          status: upload.status === 'completed' ? 'completed' : 
                  upload.status === 'failed' ? 'failed' : 
                  upload.status === 'processing' ? 'processing' : prev.status,
          statusDescription: upload.status_description || '',
          estimatedTimeRemaining: upload.estimated_time_remaining,
          nextAction: upload.next_action || '',
          error: upload.error_message || null,
          uploading: !upload.is_complete && !upload.has_errors
        }));

        // If completed or failed, stop polling
        if (upload.is_complete || upload.has_errors) {
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }
          
          if (upload.is_complete) {
            setState(prev => ({ ...prev, result: upload }));
          }
        }
      }
    } catch (error) {
      console.error('Error updating progress:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to check upload status',
        status: 'failed',
        uploading: false
      }));
    }
  }, []);

  const pollProgress = useCallback((uploadId: number) => {
    // Clear any existing polling
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    // Start polling
    pollingIntervalRef.current = setInterval(() => {
      updateProgress(uploadId);
    }, POLLING_INTERVAL);

    // Also update immediately
    updateProgress(uploadId);
  }, [updateProgress]);

  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  }, []);

  const completeUpload = useCallback((result: any) => {
    setState(prev => ({
      ...prev,
      uploading: false,
      progress: 100,
      status: 'completed',
      statusDescription: 'Upload completed successfully!',
      estimatedTimeRemaining: null,
      nextAction: 'View processed data in dashboard',
      result
    }));
    stopPolling();
  }, [stopPolling]);

  const failUpload = useCallback((error: string) => {
    setState(prev => ({
      ...prev,
      uploading: false,
      status: 'failed',
      statusDescription: 'Upload failed',
      estimatedTimeRemaining: null,
      nextAction: 'Please try again or contact support',
      error
    }));
    stopPolling();
  }, [stopPolling]);

  const reset = useCallback(() => {
    stopPolling();
    setState({
      uploading: false,
      progress: 0,
      status: 'idle',
      statusDescription: '',
      estimatedTimeRemaining: null,
      nextAction: '',
      uploadId: null,
      error: null,
      result: null
    });
  }, [stopPolling]);

  const actions: UploadProgressActions = {
    startUpload,
    updateProgress,
    completeUpload,
    failUpload,
    reset,
    pollProgress,
    stopPolling
  };

  return [state, actions];
} 