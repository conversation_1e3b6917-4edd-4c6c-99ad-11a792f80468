import { useState, useEffect, useCallback, useRef } from "react";
import { useRouter, useSearchParams, usePathname } from "next/navigation";

interface UseQueryParamsStateOptions {
  scroll?: boolean;
  replace?: boolean;
}

export function useQueryParamsState(
  initialState: Record<string, string>,
  options: UseQueryParamsStateOptions = {}
) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { scroll = false, replace = false } = options;

  // Store initial state in ref to avoid dependency issues
  const initialStateRef = useRef(initialState);

  const [state, setState] = useState<Record<string, string>>(() => {
    const state = { ...initialStateRef.current };

    for (const key in initialStateRef.current) {
      const urlValue = searchParams.get(key);
      if (urlValue !== null) {
        state[key] = urlValue;
      }
    }

    return state;
  });

  // Update state when URL changes
  useEffect(() => {
    const newState = { ...initialStateRef.current };

    for (const key in initialStateRef.current) {
      const urlValue = searchParams.get(key);
      if (urlValue !== null) {
        newState[key] = urlValue;
      }
    }

    setState(newState);
  }, [searchParams]);

  // Function to update state and URL
  const updateState = useCallback(
    (updates: Record<string, string>) => {
      const newState = { ...state, ...updates };
      setState(newState);

      // Update URL - preserve existing params but update our state
      const params = new URLSearchParams(searchParams.toString());

      // Update our state parameters
      for (const [key, value] of Object.entries(updates)) {
        if (value === undefined || value === null || value === "") {
          params.delete(key);
        } else {
          params.set(key, value);
        }
      }

      const queryString = params.toString();
      const newUrl = queryString ? `${pathname}?${queryString}` : pathname;

      if (replace) {
        router.replace(newUrl, { scroll });
      } else {
        router.push(newUrl, { scroll });
      }
    },
    [state, pathname, router, scroll, replace, searchParams]
  );

  // Function to update a single key
  const setValue = useCallback(
    (key: string, value: string) => {
      updateState({ [key]: value });
    },
    [updateState]
  );

  return {
    state,
    setState: updateState,
    setValue,
    searchParams: Object.fromEntries(searchParams.entries()),
  };
}
