import { useState, useCallback, useEffect } from "react";
import { UploadService, HeaderMappingData } from "@/lib/services/UploadService";

export interface HeaderMappingState {
  headerMappings: Record<string, string[]>;  // One CSV header to multiple DB fields
  mappingData: HeaderMappingData | null;
  showMappingSection: boolean;
  mappingConfirmed: boolean;
  loading: boolean;
  loadingFields: boolean;
  hasInitialMapping: boolean;
}

export interface HeaderMappingActions {
  setHeaderMappings: (mappings: Record<string, string[]>) => void;
  confirmMapping: (mappings: Record<string, string[]>, uploadId?: number) => void;
  toggleMappingSection: () => Promise<void>;
  reAnalyzeMappings: (csvHeaders: string[], sampleData: Record<string, any>) => Promise<void>;
  isAISuggestion: (header: string) => boolean;
  isAIOriginalSuggestion: (header: string, dbField: string) => boolean;
  addMapping: (csvHeader: string, dbField: string) => void;
  removeMapping: (csvHeader: string, dbField: string) => void;
  reset: () => void;
}

export function useHeaderMapping(
  csvHeaders: string[] = [],
  sampleData: Record<string, any> | null = null
): [HeaderMappingState, HeaderMappingActions] {
  const [state, setState] = useState<HeaderMappingState>({
    headerMappings: {},
    mappingData: null,
    showMappingSection: false,
    mappingConfirmed: false,
    loading: false,
    loadingFields: false,
    hasInitialMapping: false
  });

  const setHeaderMappings = useCallback((mappings: Record<string, string[]>) => {
    setState(prev => ({
      ...prev,
      headerMappings: mappings
    }));
  }, []);

  const confirmMapping = useCallback(async (mappings: Record<string, string[]>, uploadId?: number) => {
    setState(prev => ({
      ...prev,
      headerMappings: mappings,
      mappingConfirmed: true
    }));

    // If we have an upload ID, save the mappings to the database
    if (uploadId) {
      try {
        const response = await fetch('/api/investors/save-header-mappings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            upload_id: uploadId,
            header_mappings: mappings,
            structured_mappings: state.mappingData
          })
        });

        const result = await response.json();
        if (!result.success) {
          console.error('Failed to save header mappings:', result.error);
        } else {
          console.log('Header mappings saved successfully');
        }
      } catch (error) {
        console.error('Error saving header mappings:', error);
      }
    }
  }, [state.mappingData]);

  const addMapping = useCallback((csvHeader: string, dbField: string) => {
    setState(prev => {
      const newMappings = { ...prev.headerMappings };
      if (!newMappings[csvHeader]) {
        newMappings[csvHeader] = [];
      }
      if (!newMappings[csvHeader].includes(dbField)) {
        newMappings[csvHeader] = [...newMappings[csvHeader], dbField];
      }
      return {
        ...prev,
        headerMappings: newMappings
      };
    });
  }, []);

  const removeMapping = useCallback((csvHeader: string, dbField: string) => {
    setState(prev => {
      const newMappings = { ...prev.headerMappings };
      if (newMappings[csvHeader]) {
        newMappings[csvHeader] = newMappings[csvHeader].filter(field => field !== dbField);
        if (newMappings[csvHeader].length === 0) {
          delete newMappings[csvHeader];
        }
      }
      return {
        ...prev,
        headerMappings: newMappings
      };
    });
  }, []);

  const fetchHeaderMappings = useCallback(async (headers: string[], sample: Record<string, any>) => {
    if (state.hasInitialMapping) return;

    try {
      setState(prev => ({ ...prev, loading: true }));
      const mappingData = await UploadService.fetchHeaderMappings(headers);
      
      // Convert structured mappings to array mappings for the UI
      const arrayMappings: Record<string, string[]> = {};
      
      // Process all mapping categories from API response
      const allMappingCategories = [
        mappingData.company_mappings || {},
        mappingData.contact_mappings || {},
        mappingData.investment_criteria_central_mappings || {},
        mappingData.investment_criteria_debt_mappings || {},
        mappingData.investment_criteria_equity_mappings || {}
      ];
      
      // Invert the mapping structure: dbField: [csvHeaders] → csvHeader: [dbFields]
      allMappingCategories.forEach(categoryMappings => {
        Object.entries(categoryMappings).forEach(([dbField, csvHeaders]) => {
          if (Array.isArray(csvHeaders)) {
            csvHeaders.forEach(csvHeader => {
              if (!arrayMappings[csvHeader]) {
                arrayMappings[csvHeader] = [];
              }
              if (!arrayMappings[csvHeader].includes(dbField)) {
                arrayMappings[csvHeader].push(dbField);
              }
            });
          }
        });
      });

      setState(prev => ({
        ...prev,
        mappingData,
        headerMappings: arrayMappings,
        loading: false,
        hasInitialMapping: true
      }));
    } catch (error) {
      console.error('[ERROR] Failed to fetch header mappings:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch mappings'
      }));
    }
  }, [state.hasInitialMapping]);

  const toggleMappingSection = useCallback(async () => {
    setState(prev => ({
      ...prev,
      showMappingSection: !prev.showMappingSection
    }));
  }, []);

  const reAnalyzeMappings = useCallback(async (headers: string[], sample: Record<string, any>) => {
    setState(prev => ({ ...prev, hasInitialMapping: false }));
    await fetchHeaderMappings(headers, sample);
  }, [fetchHeaderMappings]);

  const reset = useCallback(() => {
    setState({
      headerMappings: {},
      mappingData: null,
      showMappingSection: false,
      mappingConfirmed: false,
      loading: false,
      loadingFields: false,
      hasInitialMapping: false
    });
  }, []);

  // Auto-fetch mappings when headers are available and we don't have them yet
  useEffect(() => {
    if (
      csvHeaders.length > 0 &&
      sampleData &&
      !state.hasInitialMapping &&
      !state.loading
    ) {
      fetchHeaderMappings(csvHeaders, sampleData);
    }
  }, [csvHeaders, sampleData, state.hasInitialMapping, state.loading, fetchHeaderMappings]);

  const actions: HeaderMappingActions = {
    setHeaderMappings,
    confirmMapping,
    addMapping,
    removeMapping,
    toggleMappingSection,
    reAnalyzeMappings,
    isAISuggestion: () => false, // Simplified - all mappings come from AI
    isAIOriginalSuggestion: () => false, // Simplified - not needed anymore
    reset
  };

  return [state, actions];
} 