import { useState, useEffect, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'

interface UsePaginationOptions {
  defaultPage?: number
  defaultLimit?: number
  persistKey?: string
  onPageChange?: (page: number, limit: number) => void
}

export function usePagination({
  defaultPage = 1,
  defaultLimit = 20,
  persistKey,
  onPageChange
}: UsePaginationOptions = {}) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // Initialize pagination state from URL or localStorage
  const initPagination = useCallback(() => {
    // Try to get from URL first
    if (searchParams) {
      const urlPage = searchParams.get('page')
      const urlLimit = searchParams.get('limit')
      
      return {
        page: urlPage ? parseInt(urlPage, 10) : defaultPage,
        limit: urlLimit ? parseInt(urlLimit, 10) : defaultLimit
      }
    }
    
    // Try to get from localStorage if persistKey is provided
    if (persistKey && typeof window !== 'undefined') {
      try {
        const storedPagination = localStorage.getItem(`pagination_${persistKey}`)
        if (storedPagination) {
          return JSON.parse(storedPagination)
        }
      } catch (error) {
        console.error('Failed to parse stored pagination:', error)
      }
    }
    
    // Fall back to defaults
    return { page: defaultPage, limit: defaultLimit }
  }, [searchParams, persistKey, defaultPage, defaultLimit])

  const [page, setPage] = useState<number>(initPagination().page)
  const [limit, setLimit] = useState<number>(initPagination().limit)
  const [totalItems, setTotalItems] = useState<number>(0)
  const [totalPages, setTotalPages] = useState<number>(1)

  // Calculate total pages when total items or limit changes
  useEffect(() => {
    setTotalPages(Math.max(1, Math.ceil(totalItems / limit)))
  }, [totalItems, limit])

  // Update URL when pagination changes
  useEffect(() => {
    const params = new URLSearchParams(window.location.search)
    
    // Update pagination parameters
    params.set('page', page.toString())
    params.set('limit', limit.toString())
    
    // Update URL
    const newSearch = params.toString()
    const newPath = `${window.location.pathname}?${newSearch}`
    
    // Only update if the URL would change
    if (newPath !== window.location.pathname + window.location.search) {
      router.push(newPath, { scroll: false })
    }
    
    // Store in localStorage if persistKey is provided
    if (persistKey) {
      localStorage.setItem(`pagination_${persistKey}`, JSON.stringify({ page, limit }))
    }
    
    // Call external handler if provided
    if (onPageChange) {
      onPageChange(page, limit)
    }
  }, [page, limit, router, persistKey, onPageChange])

  // Update pagination when URL changes
  useEffect(() => {
    const urlPage = searchParams?.get('page')
    const urlLimit = searchParams?.get('limit')
    
    if (urlPage) {
      const parsedPage = parseInt(urlPage, 10)
      if (!isNaN(parsedPage) && parsedPage !== page) {
        setPage(parsedPage)
      }
    }
    
    if (urlLimit) {
      const parsedLimit = parseInt(urlLimit, 10)
      if (!isNaN(parsedLimit) && parsedLimit !== limit) {
        setLimit(parsedLimit)
      }
    }
  }, [searchParams, page, limit])

  // Handler to change page
  const goToPage = useCallback((newPage: number) => {
    // Ensure page is within bounds
    const boundedPage = Math.max(1, Math.min(newPage, totalPages))
    setPage(boundedPage)
  }, [totalPages])

  // Handler to change limit
  const setPageSize = useCallback((newLimit: number) => {
    setLimit(newLimit)
    // Reset to first page when changing limit
    setPage(1)
  }, [])

  // Handler to set total items
  const setTotal = useCallback((total: number) => {
    setTotalItems(total)
  }, [])

  return {
    page,
    limit,
    totalItems,
    totalPages,
    goToPage,
    setPageSize,
    setTotal
  }
} 