import { useState, useCallback, useEffect } from "react";
import { ConflictPreviewRow, ConflictUploadResult } from "@/types/conflict";
import { UploadService, UploadOptions } from "@/lib/services/UploadService";
import { InvestmentCriteriaRecord } from "@/lib/processors/InvestmentCriteriaProcessor";

export interface ConflictDetectionState {
  previewData: ConflictPreviewRow[];
  checking: boolean;
  uploading: boolean;
  progress: number;
  result: ConflictUploadResult | null;
  selectedRows: Set<number>;
  currentConflictCount: number;
}

export interface ConflictDetectionActions {
  checkConflicts: (data: Record<string, any>[], headerMappings: Record<string, string>) => Promise<void>;
  uploadWithConflicts: (
    selectedData: Record<string, any>[],
    headerMappings: Record<string, string>,
    investmentCriteriaRecords: InvestmentCriteriaRecord[],
    options?: UploadOptions
  ) => Promise<void>;
  toggleRowSelection: (index: number) => void;
  toggleSelectAll: () => void;
  fetchConflictCount: () => Promise<void>;
  reset: () => void;
}

export function useConflictDetection(): [ConflictDetectionState, ConflictDetectionActions] {
  const [state, setState] = useState<ConflictDetectionState>({
    previewData: [],
    checking: false,
    uploading: false,
    progress: 0,
    result: null,
    selectedRows: new Set(),
    currentConflictCount: 0
  });

  const checkConflicts = useCallback(async (
    data: Record<string, any>[],
    headerMappings: Record<string, string>
  ) => {
    setState(prev => ({ 
      ...prev, 
      checking: true, 
      result: null,
      previewData: []
    }));

    try {
      const result = await UploadService.checkConflicts(data, headerMappings);
      
      if (result.success && result.data) {
        // Initialize all rows as selected
        const selectedRows = new Set<number>();
        result.data.forEach((_, index) => selectedRows.add(index));
        
        setState(prev => ({
          ...prev,
          checking: false,
          previewData: result.data || [],
          selectedRows
        }));
      } else {
        setState(prev => ({
          ...prev,
          checking: false,
          result: {
            success: false,
            error: result.error || "Conflict check failed",
            conflicts: { companies: [], contacts: [] },
            stats: {
              companies: { total: 0, added: 0, updated_with_conflicts: 0, skipped: 0, error: 0 },
              contacts: { total: 0, added: 0, updated_with_conflicts: 0, skipped: 0, error: 0 }
            },
            logs: []
          }
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        checking: false,
        result: {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error occurred",
          conflicts: { companies: [], contacts: [] },
          stats: {
            companies: { total: 0, added: 0, updated_with_conflicts: 0, skipped: 0, error: 0 },
            contacts: { total: 0, added: 0, updated_with_conflicts: 0, skipped: 0, error: 0 }
          },
          logs: []
        }
      }));
    }
  }, []);

  const uploadWithConflicts = useCallback(async (
    selectedData: Record<string, any>[],
    headerMappings: Record<string, string>,
    investmentCriteriaRecords: InvestmentCriteriaRecord[],
    options: UploadOptions = {}
  ) => {
    setState(prev => ({ ...prev, uploading: true, progress: 0 }));

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setState(prev => {
        if (prev.progress < 90) {
          return { ...prev, progress: prev.progress + 10 };
        }
        return prev;
      });
    }, 500);

    try {
      const result = await UploadService.uploadWithConflicts(
        selectedData,
        headerMappings,
        investmentCriteriaRecords,
        options
      );

      clearInterval(progressInterval);
      setState(prev => ({
        ...prev,
        uploading: false,
        progress: 100,
        result
      }));

      // Refresh conflict count after upload
      if (result.success) {
        setTimeout(() => {
          fetchConflictCount();
        }, 1000);
      }
    } catch (error) {
      clearInterval(progressInterval);
      setState(prev => ({
        ...prev,
        uploading: false,
        progress: 0,
        result: {
          success: false,
          error: error instanceof Error ? error.message : "Upload failed",
          conflicts: { companies: [], contacts: [] },
          stats: {
            companies: { total: 0, added: 0, updated_with_conflicts: 0, skipped: 0, error: 0 },
            contacts: { total: 0, added: 0, updated_with_conflicts: 0, skipped: 0, error: 0 }
          },
          logs: []
        }
      }));
    }
  }, []);

  const toggleRowSelection = useCallback((index: number) => {
    setState(prev => {
      const newSelected = new Set(prev.selectedRows);
      if (newSelected.has(index)) {
        newSelected.delete(index);
      } else {
        newSelected.add(index);
      }
      return { ...prev, selectedRows: newSelected };
    });
  }, []);

  const toggleSelectAll = useCallback(() => {
    setState(prev => {
      const allSelected = prev.selectedRows.size === prev.previewData.length;
      const newSelected = new Set<number>();
      
      if (!allSelected) {
        prev.previewData.forEach((_, index) => newSelected.add(index));
      }
      
      return { ...prev, selectedRows: newSelected };
    });
  }, []);

  const fetchConflictCount = useCallback(async () => {
    try {
      const count = await UploadService.fetchConflictCount();
      setState(prev => ({ ...prev, currentConflictCount: count }));
    } catch (error) {
      console.error("Error fetching conflict count:", error);
    }
  }, []);

  const reset = useCallback(() => {
    setState({
      previewData: [],
      checking: false,
      uploading: false,
      progress: 0,
      result: null,
      selectedRows: new Set(),
      currentConflictCount: 0
    });
  }, []);

  // Fetch conflict count on mount
  useEffect(() => {
    fetchConflictCount();
  }, [fetchConflictCount]);

  const actions: ConflictDetectionActions = {
    checkConflicts,
    uploadWithConflicts,
    toggleRowSelection,
    toggleSelectAll,
    fetchConflictCount,
    reset
  };

  return [state, actions];
} 