import { Job } from "bullmq";
import { DealProcessor } from "@/lib/processors/DealProcessor";
import { FileManager } from "@/lib/utils/fileManager";
import { pool } from "@/lib/db";
import {
  findPotentialDealDuplicates,
  type PotentialDealDuplicate,
} from "@/lib/utils/dealConflictDetector";
import {
  generateDealEmbedding,
  DealEmbeddingData,
} from "@/lib/embeddings/EmbeddingService";
import { buildTableSchemaForPrompt } from '../utils/dbSchema';
import { DataFormatter, defaultDataFormatter } from '../utils/dataFormatter';

export interface DealProcessingJobData {
  files: Array<{
    buffer: string; // Base64 encoded buffer
    mimeType: string;
    fileName: string;
  }>;
  dealId?: number; // For file reprocessing jobs
  contactId?: string;
  contactIds?: number[]; // Add support for multiple contacts
  contactEmail?: string;
  createdBy?: string;
  llmModel?:
    | "gemini-flash"
    | "gemini-pro"
    | "openai-4o"
    | "openai-4o-mini"
    | "openai-o1-preview"
    | "openai-o1-mini"; // Add model selection
  jobMetadata: {
    originalFileNames: string[];
    fileCount: number;
    uploadTimestamp: string;
  };
}

export interface DealProcessingResult {
  success: boolean;
  dealId?: number;
  message: string;
  extractedData?: any;
  uploadedFiles?: Array<{
    file_id: string;
    original_name: string;
    file_path: string;
    is_duplicate: boolean;
    relationship_type: string;
    is_primary: boolean;
  }>;
  duplicates?: PotentialDealDuplicate[];
  error?: string;
}

// Utility functions (copied from original route)
function camelToSnake(str: string): string {
  return str.replace(/[A-Z]/g, (letter: string) => `_${letter.toLowerCase()}`);
}

function keysToSnake(obj: unknown): unknown {
  if (Array.isArray(obj)) {
    return obj.map(keysToSnake);
  } else if (obj && typeof obj === "object" && obj.constructor === Object) {
    const newObj: Record<string, unknown> = {};
    for (const key in obj as Record<string, unknown>) {
      if (Object.hasOwnProperty.call(obj, key)) {
        newObj[camelToSnake(key)] = keysToSnake(
          (obj as Record<string, unknown>)[key]
        );
      }
    }
    return newObj;
  }
  return obj;
}

async function getDealsTableColumns(): Promise<Set<string>> {
  const res = await pool.query(
    `SELECT column_name FROM information_schema.columns WHERE table_name = 'deals' AND table_schema = 'public'`
  );
  return new Set(
    res.rows.map((row: { column_name: string }) => row.column_name)
  );
}

async function getInvestmentCriteriaColumns(): Promise<Set<string>> {
  const res = await pool.query(
    `SELECT column_name FROM information_schema.columns WHERE table_name = 'investment_criteria' AND table_schema = 'public'`
  );
  return new Set(
    res.rows.map((row: { column_name: string }) => row.column_name)
  );
}

export { getDealsTableColumns, getInvestmentCriteriaColumns };

function normalizeMinMaxFields(
  criteria: Record<string, any>,
  pairs: [string, string][]
) {
  for (const [minKey, maxKey] of pairs) {
    const minVal = criteria[minKey];
    const maxVal = criteria[maxKey];
    if (typeof minVal === "string" && minVal.includes("-")) {
      const [min, max] = minVal
        .split("-")
        .map((s) => parseFloat(s.replace(/[^0-9.]/g, "")));
      criteria[minKey] = min;
      criteria[maxKey] = max;
    } else if (typeof maxVal === "string" && maxVal.includes("-")) {
      const [min, max] = maxVal
        .split("-")
        .map((s) => parseFloat(s.replace(/[^0-9.]/g, "")));
      criteria[minKey] = min;
      criteria[maxKey] = max;
    } else if (minVal != null && (maxVal == null || maxVal === "")) {
      criteria[maxKey] = minVal;
    } else if (maxVal != null && (minVal == null || minVal === "")) {
      criteria[minKey] = maxVal;
    }
  }
}

async function createLLMOutputFile(
  dealId: number,
  result: any,
  fileNames: string[],
  llmModel: string = "gemini"
): Promise<void> {
  try {
    const isMultipleFiles = fileNames.length > 1;
    const llmResponse = result.llmResponse;

    if (!llmResponse) {
      console.warn("No LLM response available for LLM output file creation");
      return;
    }

    const rawLLMOutput = {
      timestamp: new Date().toISOString(),
      dealId: dealId,
      ...(isMultipleFiles
        ? {
            fileCount: fileNames.length,
            files: fileNames.map((fileName) => ({
              fileName,
            })),
          }
        : {
            fileName: fileNames[0],
          }),
      llmProvider: llmModel === "openai" ? "openai" : "gemini",
      prompt: {
        system: "Deal extraction prompt",
        user: isMultipleFiles
          ? `Analysis of ${fileNames.length} files together`
          : "File analysis request",
      },
      response: {
        raw: llmResponse.content || JSON.stringify(llmResponse),
        parsed: result.extractedData,
        parseError: null,
      },
      metadata: {
        processingDuration: result.processingDuration || 0,
        extractionMethod: "simplified",
        confidence:
          result.extractedData?.metadata?.extraction_confidence || "unknown",
      },
    };

    const jsonBuffer = Buffer.from(
      JSON.stringify(rawLLMOutput, null, 2),
      "utf-8"
    );
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const outputFileName = isMultipleFiles
      ? `deal_${dealId}_llm_output_multiple_${timestamp}.json`
      : `deal_${dealId}_llm_output_${timestamp}.json`;

    const uploadRequest = {
      original_name: outputFileName,
      title: isMultipleFiles
        ? `Raw LLM Output for Deal ${dealId} (Multiple Files)`
        : `Raw LLM Output for Deal ${dealId}`,
      description: isMultipleFiles
        ? `Raw Gemini LLM output and processing details for ${fileNames.length} files processed together`
        : `Raw Gemini LLM output and processing details for deal extraction`,
      uploaded_by: "system",
      upload_source: "deal_processor",
      access_level: "private" as const,
      metadata: {
        deal_id: dealId,
        file_type: "llm_output",
        extraction_timestamp: new Date().toISOString(),
        llm_provider: llmModel?.startsWith("openai") ? "openai" : "gemini",
        ...(isMultipleFiles
          ? {
              file_count: fileNames.length,
              original_filenames: fileNames,
            }
          : {
              original_filename: fileNames[0],
            }),
      },
      tags: [
        "llm_output",
        "deal_processing",
        llmModel,
        ...(isMultipleFiles ? ["multiple_files"] : []),
      ],
    };

    const { file: uploadedFile } = await FileManager.uploadFile(
      jsonBuffer,
      uploadRequest
    );

    const relationshipRequest = {
      target_table_name: "deals",
      target_column_name: "deal_id",
      target_row_id: dealId.toString(),
      relationship_type: "llm_output",
      relationship_title: isMultipleFiles
        ? "Raw LLM Processing Output (Multiple Files)"
        : "Raw LLM Processing Output",
      relationship_notes: isMultipleFiles
        ? `Contains the raw LLM response and processing details for ${fileNames.length} files`
        : "Contains the raw LLM response and processing details",
      is_primary: false,
      display_order: 999,
    };

    await FileManager.createFileRelationship(
      uploadedFile.file_id,
      relationshipRequest
    );
  } catch (error) {
    console.error("Error creating LLM output file:", error);
  }
}

async function processDealData(
  result: any,
  dealsTableColumns: Set<string>,
  fileNames: string[], // fileNames parameter for metadata
  llmModel?: string // Add llmModel parameter
): Promise<
  | number
  | null
  | {
      success: false;
      duplicates: PotentialDealDuplicate[];
      message: string;
    }
> {
  let dealId: number | null = null;

  // Insert into deals table
  if (result.extractedData) {
    // Combine coreFields and extraFields from the DealProcessor
    const extractedCoreFields = result.extractedData.coreFields || {};
    const extractedExtraFields = result.extractedData.extraFields || {};
    const extractedMetadata = result.extractedData.metadata || {};

    const coreFieldsSnake = keysToSnake(extractedCoreFields) as Record<
      string,
      any
    >;
    const extraFieldsSnake = keysToSnake(extractedExtraFields) as Record<
      string,
      any
    >;
    const allFields = {
      ...coreFieldsSnake,
      ...extraFieldsSnake,
    } as Record<string, any>;

    console.log("ALL EXTRACTED FIELDS:", JSON.stringify(allFields, null, 2));
    console.log(
      "EXTRACTED METADATA:",
      JSON.stringify(extractedMetadata, null, 2)
    );
    console.log("DEALS TABLE COLUMNS:", Array.from(dealsTableColumns));

    // Get column types for the deals table
    const dealsTableColumnTypes = await DataFormatter.getColumnTypes('deals', pool);

    // Extract investment_criteria before processing other fields
    const investmentCriteriaData =
      result.extractedData.investmentCriteria ||    // ← Added this - direct from extractedData
      extractedCoreFields.investmentCriteria ||
      extractedExtraFields.investmentCriteria ||
      allFields["investment_criteria"] ||
      allFields["investmentCriteria"];
    delete allFields["investment_criteria"];
    delete allFields["investmentCriteria"];

    const coreFields: Record<string, any> = {};
    const extraFields: Record<string, any> = {};

    // Use data formatter to properly format all fields
    const formattedFields = defaultDataFormatter.formatRecordForInsertion(allFields, dealsTableColumnTypes);

    for (const key in formattedFields) {
      if (dealsTableColumns.has(key)) {
        coreFields[key] = formattedFields[key];
        console.log(
          `✓ MAPPED TO DEALS TABLE: ${key} = ${JSON.stringify(formattedFields[key])} (type: ${dealsTableColumnTypes[key]})`
        );
      } else {
        extraFields[key] = formattedFields[key];
        console.log(
          `→ MAPPED TO EXTRA_FIELDS: ${key} = ${JSON.stringify(formattedFields[key])}`
        );
      }
    }

    // Add metadata fields to coreFields if they exist in the deals table
    const isMultipleFiles = fileNames.length > 1;
    const totalFileSize = 0; // We don't have file size info in job context

    const metadataFields = {
      // Processing metadata
      extraction_timestamp: new Date(),
      llm_model_used:
        extractedMetadata.llm_model_used || llmModel || "gemini-flash",
      llm_provider: extractedMetadata.llm_provider || "gemini",
      processor_version: extractedMetadata.processor_version || "1.0",
      processing_duration_ms: result.processingDuration || 0,

      // Document metadata
      document_type: Array.isArray(extractedMetadata.document_type)
        ? extractedMetadata.document_type
        : extractedMetadata.document_type
        ? [extractedMetadata.document_type]
        : ["deal_document"],
      extraction_method: isMultipleFiles
        ? ["simplified_multiple"]
        : ["simplified"],
      document_source: fileNames.map(() => "application/pdf"), // Default since we don't have mime types in job context
      document_filename: fileNames,
      document_size_bytes: totalFileSize,

      // Extraction quality metadata
      extraction_confidence:
        extractedMetadata.extraction_confidence || "medium",
      processing_notes:
        extractedMetadata.processing_notes ||
        (isMultipleFiles
          ? `Processed ${fileNames.length} files together using Gemini 2.5 Flash`
          : `Processed single file using Gemini 2.5 Flash`),
      review_status: "pending",
      status: "active",

      // Additional metadata if available
      data_quality_issues: extractedMetadata.data_quality_issues || null,
      missing_critical_fields:
        extractedMetadata.missing_critical_fields || null,
    };

    // Only add metadata fields that exist in the deals table
    for (const [key, value] of Object.entries(metadataFields)) {
      if (dealsTableColumns.has(key)) {
        coreFields[key] = value;
        console.log(
          `✓ ADDED METADATA TO DEALS TABLE: ${key} = ${JSON.stringify(value)}`
        );
      } else {
        extraFields[key] = value;
        console.log(
          `→ ADDED METADATA TO EXTRA_FIELDS: ${key} = ${JSON.stringify(value)}`
        );
      }
    }

    // Always include extra_fields
    coreFields.extra_fields = JSON.stringify(extraFields);
    // Remove deal_id if present (auto-incremented)
    delete coreFields.deal_id;

    console.log(
      "FINAL CORE FIELDS FOR INSERT:",
      JSON.stringify(coreFields, null, 2)
    );

    // === CONFLICT DETECTION ===
    console.log("🚨 === CHECKING FOR DEAL CONFLICTS ===");
    console.log("🎯 Deal data being checked for duplicates:", {
      deal_name: coreFields.deal_name,
      sponsor_name: coreFields.sponsor_name,
      city: coreFields.city,
      state: coreFields.state,
      region: coreFields.region,
      property_type: coreFields.property_type,
      all_core_fields: Object.keys(coreFields),
    });

    try {
      console.log("📞 Calling findPotentialDealDuplicates...");
      const potentialDuplicates = await findPotentialDealDuplicates(coreFields);
      console.log("📨 Received response from duplicate detection:", {
        count: potentialDuplicates.length,
        duplicates: potentialDuplicates,
      });

      if (potentialDuplicates.length > 0) {
        console.log(
          `🚨 Found ${potentialDuplicates.length} potential deal duplicates - RETURNING CONFLICT RESPONSE`
        );
        console.log(
          "🔄 Duplicates details:",
          JSON.stringify(potentialDuplicates, null, 2)
        );

        // Insert each conflict into deal_conflicts table
        for (const duplicate of potentialDuplicates) {
          await pool.query(
            `INSERT INTO deal_conflicts (deal_id, conflict_data, status) VALUES ($1, $2, 'pending')`,
            [duplicate.deal_id, JSON.stringify(duplicate)]
          );
        }

        // Return the duplicates instead of creating the deal
        return {
          success: false,
          duplicates: potentialDuplicates,
          message: `Found ${potentialDuplicates.length} potential duplicate deal(s). Please review and resolve conflicts.`,
        };
      }

      console.log("✅ No conflicts detected, proceeding with deal creation");
    } catch (conflictError) {
      console.error("❌ Error during conflict detection:", conflictError);
      if (conflictError instanceof Error) {
        console.error("❌ Stack trace:", conflictError.stack);
      }
      // Continue with insertion if conflict detection fails
      console.log(
        "⚠️  Conflict detection failed, proceeding with deal creation"
      );
    }

    // Build insert
    const columns = Object.keys(coreFields);
    const values = Object.values(coreFields);
    const placeholders = columns.map((_, i) => `$${i + 1}`).join(", ");
    const sql = `INSERT INTO public.deals (${columns.join(
      ", "
    )}) VALUES (${placeholders}) RETURNING deal_id`;

    console.log("DEALS INSERT SQL:", sql);
    console.log("DEALS INSERT VALUES:", values);

    const insertResult = await pool.query(sql, values);
    dealId = insertResult.rows[0]?.deal_id || null;

    console.log("✓ INSERTED DEAL ID:", dealId);

    // Generate and store embedding for the newly created deal
    // COMMENTED OUT: deal_embedding column doesn't exist in deals table
    /*
    if (dealId) {
      try {
        console.log("🔤 Generating vector embedding for deal...");
        const embeddingData: DealEmbeddingData = {
          deal_name: coreFields.deal_name,
          sponsor_name: coreFields.sponsor_name,
          property_type: coreFields.property_type,
          city: coreFields.city,
          state: coreFields.state,
          region: coreFields.region,
          deal_summary: coreFields.deal_summary,
          description: coreFields.description,
        };

        const embedding = await generateDealEmbedding(embeddingData);
        const embeddingArray = `[${embedding.join(",")}]`;

        // Update the deal with the embedding
        const updateEmbeddingQuery = `
          UPDATE public.deals 
          SET deal_embedding = $1::vector,
              embedding_model = 'text-embedding-004',
              embedding_created_at = NOW()
          WHERE deal_id = $2
        `;

        await pool.query(updateEmbeddingQuery, [embeddingArray, dealId]);
        console.log(`✅ Generated and stored embedding for deal ${dealId}`);
      } catch (embeddingError) {
        console.error(
          `⚠️ Failed to generate embedding for deal ${dealId}:`,
          embeddingError
        );
        // Don't fail the entire upload if embedding generation fails
        console.log(
          "⚠️ Continuing with deal creation despite embedding failure"
        );
      }
    }
    */

    // --- Insert multiple investment criteria if present ---
    if (dealId && investmentCriteriaData) {
      console.log("=== OPTIMIZED INVESTMENT CRITERIA INSERTION ===");
      console.log(
        "INVESTMENT CRITERIA DATA:",
        JSON.stringify(investmentCriteriaData, null, 2)
      );
      
      // Debug: Check specifically for array fields in the original data
      if (Array.isArray(investmentCriteriaData)) {
        investmentCriteriaData.forEach((criteria, index) => {
          console.log(`🔍 ORIGINAL CRITERIA ${index}:`, {
            capital_position: criteria.capital_position,
            property_types: criteria.property_types,
            strategies: criteria.strategies,
            state: criteria.state,
            minimum_deal_size: criteria.minimum_deal_size,
            maximum_deal_size: criteria.maximum_deal_size
          });
        });
      }

      const investmentArr = Array.isArray(investmentCriteriaData)
        ? investmentCriteriaData
        : [investmentCriteriaData];

      if (investmentArr.length > 0) {
        const investmentCriteriaColumns = await getInvestmentCriteriaColumns();

        console.log(
          "INVESTMENT CRITERIA TABLE COLUMNS:",
          Array.from(investmentCriteriaColumns)
        );

        for (let criteriaObj of investmentArr) {
          console.log(
            "PROCESSING CRITERIA OBJECT:",
            JSON.stringify(criteriaObj, null, 2)
          );

          // Convert to snake_case
          criteriaObj = keysToSnake(criteriaObj) as Record<string, any>;
          console.log(
            "AFTER SNAKE_CASE:",
            JSON.stringify(criteriaObj, null, 2)
          );

          // === OPTIMIZED FIELD ORDERING LOGIC ===
          const core: Record<string, any> = {};
          const extra: Record<string, any> = {};

          // 1. REQUIRED FIELDS FIRST (entity relationship)
          core.entity_type = "Deal";
          core.entity_id = dealId;

          // 2. IDENTIFICATION FIELDS
          const identificationFields = [
            "target_return",
            "historical_irr",
            "historical_em",
          ];

          // 3. ARRAY FIELDS (property types, strategies, locations)
          const arrayFields = [
            "property_types",
            "property_sub_categories",
            "strategies",
            "financial_products",
            "country",
            "region",
            "state",
            "city",
            "loan_program",
            "loan_type",
            "loan_type_normalized",
            "structured_loan_tranche",
            "recourse_loan",
            "capital_position",
          ];

          // 4. NUMERIC RANGE FIELDS (optimized min/max handling)
          const numericRangeFields = [
            { min: "minimum_deal_size", max: "maximum_deal_size" },
            { min: "min_hold_period", max: "max_hold_period" },
            { min: "min_loan_term", max: "max_loan_term" },
            { min: "min_loan_dscr", max: "max_loan_dscr" },
            { min: "loan_to_value_min", max: "loan_to_value_max" },
            { min: "loan_to_cost_min", max: "loan_to_cost_max" },
            {
              min: "loan_origination_fee_min",
              max: "loan_origination_fee_max",
            },
            { min: "loan_exit_fee_min", max: "loan_exit_fee_max" },
          ];

          // 5. SINGLE NUMERIC FIELDS
          const singleNumericFields = [
            "interest_rate",
            "interest_rate_sofr",
            "interest_rate_wsj",
            "interest_rate_prime",
            "closing_time_weeks",
          ];

          // 6. TEXT FIELDS
          const textFields = ["capital_source", "created_by", "updated_by"];

          // 7. PROCESSING METADATA (timestamps, flags)
          const metadataFields = ["is_active", "created_at", "updated_at"];

          // === PROCESS FIELDS IN OPTIMIZED ORDER ===

          // Process identification fields first
          for (const field of identificationFields) {
            if (
              criteriaObj[field] !== undefined &&
              criteriaObj[field] !== null
            ) {
              if (investmentCriteriaColumns.has(field)) {
                core[field] = criteriaObj[field];
                console.log(
                  `✓ IDENTIFICATION: ${field} = ${JSON.stringify(
                    criteriaObj[field]
                  )}`
                );
              } else {
                extra[field] = criteriaObj[field];
              }
            }
          }

          // Process array fields
          for (const field of arrayFields) {
            if (
              criteriaObj[field] !== undefined &&
              criteriaObj[field] !== null
            ) {
              if (investmentCriteriaColumns.has(field)) {
                // Debug: Log original field value before processing
                console.log(`🔍 ARRAY FIELD DEBUG - ${field}:`, {
                  original: criteriaObj[field],
                  type: typeof criteriaObj[field],
                  isArray: Array.isArray(criteriaObj[field]),
                  length: Array.isArray(criteriaObj[field]) ? criteriaObj[field].length : 'not array'
                });
                
                // Ensure array fields are properly formatted for PostgreSQL
                const arrayValue = Array.isArray(criteriaObj[field])
                  ? criteriaObj[field]
                  : [criteriaObj[field]];
                core[field] = arrayValue;
                console.log(
                  `✓ ARRAY: ${field} = ${JSON.stringify(arrayValue)}`
                );
              } else {
                extra[field] = criteriaObj[field];
              }
            }
          }

          // Process numeric range fields with optimized min/max logic
          for (const { min, max } of numericRangeFields) {
            const minVal = criteriaObj[min];
            const maxVal = criteriaObj[max];

            // Handle range strings (e.g., "10-50" -> min: 10, max: 50)
            if (typeof minVal === "string" && minVal.includes("-")) {
              const [minPart, maxPart] = minVal
                .split("-")
                .map((s) => parseFloat(s.replace(/[^0-9.]/g, "")));
              if (!isNaN(minPart) && !isNaN(maxPart)) {
                if (investmentCriteriaColumns.has(min)) core[min] = minPart;
                if (investmentCriteriaColumns.has(max)) core[max] = maxPart;
                console.log(`✓ RANGE: ${min}=${minPart}, ${max}=${maxPart}`);
              }
            } else if (typeof maxVal === "string" && maxVal.includes("-")) {
              const [minPart, maxPart] = maxVal
                .split("-")
                .map((s) => parseFloat(s.replace(/[^0-9.]/g, "")));
              if (!isNaN(minPart) && !isNaN(maxPart)) {
                if (investmentCriteriaColumns.has(min)) core[min] = minPart;
                if (investmentCriteriaColumns.has(max)) core[max] = maxPart;
                console.log(`✓ RANGE: ${min}=${minPart}, ${max}=${maxPart}`);
              }
            } else {
              // Handle individual min/max values
              if (minVal !== undefined && minVal !== null) {
                const numericMin =
                  typeof minVal === "number" ? minVal : parseFloat(minVal);
                if (!isNaN(numericMin)) {
                  if (investmentCriteriaColumns.has(min)) {
                    core[min] = numericMin;
                    console.log(`✓ MIN: ${min} = ${numericMin}`);
                  } else {
                    extra[min] = numericMin;
                  }
                }
              }

              if (maxVal !== undefined && maxVal !== null) {
                const numericMax =
                  typeof maxVal === "number" ? maxVal : parseFloat(maxVal);
                if (!isNaN(numericMax)) {
                  if (investmentCriteriaColumns.has(max)) {
                    core[max] = numericMax;
                    console.log(`✓ MAX: ${max} = ${numericMax}`);
                  } else {
                    extra[max] = numericMax;
                  }
                }
              }
            }
          }

          // Process single numeric fields
          for (const field of singleNumericFields) {
            if (
              criteriaObj[field] !== undefined &&
              criteriaObj[field] !== null
            ) {
              const numericValue =
                typeof criteriaObj[field] === "number"
                  ? criteriaObj[field]
                  : parseFloat(criteriaObj[field]);

              if (!isNaN(numericValue)) {
                if (investmentCriteriaColumns.has(field)) {
                  core[field] = numericValue;
                  console.log(`✓ NUMERIC: ${field} = ${numericValue}`);
                } else {
                  extra[field] = numericValue;
                }
              }
            }
          }

          // Process text fields
          for (const field of textFields) {
            if (
              criteriaObj[field] !== undefined &&
              criteriaObj[field] !== null &&
              criteriaObj[field] !== ""
            ) {
              if (investmentCriteriaColumns.has(field)) {
                core[field] = criteriaObj[field];
                console.log(
                  `✓ TEXT: ${field} = ${JSON.stringify(criteriaObj[field])}`
                );
              } else {
                extra[field] = criteriaObj[field];
              }
            }
          }

          // Process metadata fields
          for (const field of metadataFields) {
            if (
              criteriaObj[field] !== undefined &&
              criteriaObj[field] !== null
            ) {
              if (investmentCriteriaColumns.has(field)) {
                core[field] = criteriaObj[field];
                console.log(
                  `✓ METADATA: ${field} = ${JSON.stringify(criteriaObj[field])}`
                );
              } else {
                extra[field] = criteriaObj[field];
              }
            }
          }

          // Process any remaining fields not in the predefined categories
          const processedFields = new Set([
            "entity_type",
            "entity_id",
            ...identificationFields,
            ...arrayFields,
            ...numericRangeFields.flatMap((f) => [f.min, f.max]),
            ...singleNumericFields,
            ...textFields,
            ...metadataFields,
          ]);

          for (const [key, value] of Object.entries(criteriaObj)) {
            if (
              !processedFields.has(key) &&
              value !== undefined &&
              value !== null
            ) {
              if (investmentCriteriaColumns.has(key)) {
                core[key] = value;
                console.log(`✓ REMAINING: ${key} = ${JSON.stringify(value)}`);
              } else {
                extra[key] = value;
                console.log(`→ EXTRA: ${key} = ${JSON.stringify(value)}`);
              }
            }
          }

          // Add processor context metadata to extra_fields instead of core
          if (result.extractedData?.metadata) {
            const meta = result.extractedData.metadata;
            if (meta.llm_model_used) extra.llm_model_used = meta.llm_model_used;
            if (meta.llm_provider) extra.llm_provider = meta.llm_provider;
            if (meta.processor_version)
              extra.processor_version = meta.processor_version;
            if (meta.extraction_method)
              extra.extraction_method = meta.extraction_method;
          }

          // Set default values
          if (!core.is_active) core.is_active = true;
          if (!core.created_at) core.created_at = new Date();
          if (!core.updated_at) core.updated_at = new Date();

          // Always include extra_fields (even if empty) - ensure it's a valid JSON object
          core.extra_fields = Object.keys(extra).length > 0 ? extra : {};

          // Remove auto-incremented field
          delete core.criteria_id;

          // Fetch column types for the investment_criteria table
          const criteriaColumnTypeResult = await pool.query(
            `SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'investment_criteria' AND table_schema = 'public'`
          );
          const criteriaTableColumnTypes: Record<string, string> = {};
          criteriaColumnTypeResult.rows.forEach((row: { column_name: string, data_type: string }) => {
            criteriaTableColumnTypes[row.column_name] = row.data_type;
          });

          // Coerce all values in core to correct type based on column type
          for (const key of Object.keys(core)) {
            const type = criteriaTableColumnTypes[key];
            let value = core[key];
            if (type === "integer") {
              value = parseInt(value, 10);
              if (isNaN(value)) { delete core[key]; continue; }
            } else if (type === "numeric" || type === "double precision" || type === "real") {
              value = parseFloat(value);
              if (isNaN(value)) { delete core[key]; continue; }
            } else if (type === "boolean") {
              value = Boolean(value);
            }
            core[key] = value;
          }

          console.log(
            "OPTIMIZED FINAL CORE FIELDS:",
            JSON.stringify(core, null, 2)
          );

          // === ORDERED SQL GENERATION ===
          // Order columns to match the actual database table structure
          const orderedColumns = [
            "entity_type",
            "entity_id",
            "target_return",
            "property_types",
            "property_sub_categories",
            "strategies",
            "minimum_deal_size",
            "maximum_deal_size",
            "min_hold_period",
            "max_hold_period",
            "financial_products",
            "historical_irr",
            "historical_em",
            "country",
            "region",
            "state",
            "city",
            "loan_program",
            "loan_type",
            "capital_source",
            "structured_loan_tranche",
            "min_loan_term",
            "max_loan_term",
            "interest_rate",
            "interest_rate_sofr",
            "interest_rate_wsj",
            "interest_rate_prime",
            "loan_to_value_max",
            "loan_to_cost_max",
            "loan_origination_fee_max",
            "loan_exit_fee_max",
            "min_loan_dscr",
            "max_loan_dscr",
            "recourse_loan",
            "extra_fields",
            "created_at",
            "updated_at",
            "created_by",
            "updated_by",
            "is_active",
            "capital_position",
            "loan_type_normalized",
            "loan_to_value_min",
            "loan_to_cost_min",
            "loan_origination_fee_min",
            "loan_exit_fee_min",
            "closing_time_weeks",
          ];

          // Build final column list (only include columns that exist in core and table)
          const finalColumns = orderedColumns.filter(
            (col) =>
              core.hasOwnProperty(col) && investmentCriteriaColumns.has(col)
          );

          // Add any remaining columns not in the ordered list
          for (const col of Object.keys(core)) {
            if (
              !finalColumns.includes(col) &&
              investmentCriteriaColumns.has(col)
            ) {
              finalColumns.push(col);
            }
          }

          const finalValues = finalColumns.map((col) => {
            const value = core[col];

            // Handle JSONB fields properly by stringifying them
            if (col === "extra_fields" && value && typeof value === "object") {
              return JSON.stringify(value);
            }

            // Handle array fields - ensure they're properly formatted as arrays
            const textArrayFields = [
              "property_types",
              "property_sub_categories",
              "strategies",
              "country",
              "region",
              "state",
              "city",
              "loan_program",
              "loan_type",
              "structured_loan_tranche",
              "recourse_loan",
              "capital_position",
              "loan_type_normalized",
            ];

            const jsonbArrayFields = ["financial_products"];

            if (
              textArrayFields.includes(col) &&
              value !== null &&
              value !== undefined
            ) {
              // Handle text arrays
              if (Array.isArray(value)) {
                return value;
              }
              // If it's a string, convert to array
              if (typeof value === "string") {
                return [value];
              }
              // If it's some other type, wrap in array
              return [value];
            }

            if (
              jsonbArrayFields.includes(col) &&
              value !== null &&
              value !== undefined
            ) {
              // Handle JSONB arrays - each element needs to be a valid JSON string
              if (Array.isArray(value)) {
                return value.map((item) => JSON.stringify(item));
              }
              // If it's a string, convert to array of JSON strings
              if (typeof value === "string") {
                return [JSON.stringify(value)];
              }
              // If it's an object, convert to JSON string and wrap in array
              if (typeof value === "object") {
                return [JSON.stringify(value)];
              }
              // If it's some other type, convert to JSON string and wrap in array
              return [JSON.stringify(value)];
            }

            return value;
          });
          const placeholders = finalColumns
            .map((_, i) => `$${i + 1}`)
            .join(", ");

          const sql = `INSERT INTO public.investment_criteria (${finalColumns.join(
            ", "
          )}) VALUES (${placeholders}) RETURNING criteria_id`;

          console.log("OPTIMIZED CRITERIA INSERT SQL:", sql);
          console.log("OPTIMIZED CRITERIA INSERT VALUES:", finalValues);

          // Debug: Log each parameter with its position and type
          finalValues.forEach((value, index) => {
            console.log(`Parameter $${index + 1} (${finalColumns[index]}):`, {
              value,
              type: typeof value,
              isArray: Array.isArray(value),
              stringified: JSON.stringify(value),
            });
          });

          // Find parameter $8 specifically for debugging
          if (finalValues.length >= 8) {
            console.log("🚨 PARAMETER $8 DEBUG:", {
              column: finalColumns[7],
              value: finalValues[7],
              type: typeof finalValues[7],
              isArray: Array.isArray(finalValues[7]),
              stringified: JSON.stringify(finalValues[7]),
            });
          }

          const criteriaResult = await pool.query(sql, finalValues);
          console.log(
            "✅ INSERTED OPTIMIZED CRITERIA ID:",
            criteriaResult.rows[0]?.criteria_id
          );
        }
      }
    }
    // --- End optimized investment criteria insert ---
  }

  return dealId;
}

// Main job handler
export async function processDealUpload(
  job: Job<DealProcessingJobData>
): Promise<DealProcessingResult> {
  const { files, contactId, contactEmail, createdBy, llmModel, jobMetadata } =
    job.data;

  // Debug logging to track model selection in job
  console.log("🔍 DealProcessorJob - Model Selection Debug:");
  console.log("  - Received llmModel from job data:", llmModel);
  console.log("  - Full job data:", JSON.stringify(job.data, null, 2));

  // Declare uploadedFiles at function level for error handling
  const uploadedFiles: Array<{
    file_id: string;
    original_name: string;
    file_path: string;
    is_duplicate: boolean;
    relationship_type: string;
    is_primary: boolean;
  }> = [];

  try {
    // Update progress
    await job.updateProgress(10);

    // Convert base64 buffers back to Buffer objects
    const fileBuffers = files.map((file) => ({
      buffer: Buffer.from(file.buffer, "base64"),
      mimeType: file.mimeType,
      fileName: file.fileName,
    }));

    // Update progress
    await job.updateProgress(20);

    // Validate contact if provided
    if (contactEmail && !contactId) {
      const contactResult = await pool.query(
        "SELECT contact_id FROM public.contacts WHERE email = $1 OR personal_email = $1 LIMIT 1",
        [contactEmail]
      );

      if (contactResult.rows.length === 0) {
        throw new Error(
          `Contact with email ${contactEmail} not found in contacts table`
        );
      }
    }

    if (contactId) {
      const contactCheck = await pool.query(
        "SELECT contact_id FROM public.contacts WHERE contact_id = $1",
        [parseInt(contactId)]
      );

      if (contactCheck.rows.length === 0) {
        throw new Error(
          `Contact with ID ${contactId} not found in contacts table`
        );
      }
    }

    // Update progress
    await job.updateProgress(30);

    // Process files using DealProcessor
    const processor = new DealProcessor({
      useUniversalPrompt: true,
      skipDatabaseInsert: true,
      llmModel: llmModel || "gemini-flash", // Use selected model or default to gemini-flash
    });

    let result;
    let dealId: number | null = null;

    // Update progress
    await job.updateProgress(40);

    // Check if this is a file reprocessing job (has dealId in job data)
    const isReprocessing = job.data.dealId !== undefined;
    
    if (isReprocessing && job.data.dealId) {
      // Update existing deal
      result = await processor.updateExistingDeal(job.data.dealId, fileBuffers);
      dealId = job.data.dealId;
    } else {
      // Create new deal
      result = await processor.processFiles(fileBuffers);
    }

    if (!result || !result.success) {
      throw new Error(result?.error || "Failed to process files");
    }

    // Update progress
    await job.updateProgress(60);

    // Fetch dynamic schemas for prompt generation
    const dealSchema = await buildTableSchemaForPrompt('deals');
    const investmentCriteriaSchema = await buildTableSchemaForPrompt('investment_criteria');

    // Example: Pass these schemas to your prompt generation (if using LLM here)
    // const prompt = generateDealExtractionPrompt(false, mappings, { deal: dealSchema, investmentCriteria: investmentCriteriaSchema });

    // Fetch deals table columns for dynamic insert
    const dealsTableColumns = await getDealsTableColumns();
    const investmentCriteriaColumns = await getInvestmentCriteriaColumns();

    // After extracting data from LLM, filter output before DB insert
    // (Assume result.extractedData.dealData and result.extractedData.investmentCriteriaData)
    if (result && result.extractedData) {
      if (result.extractedData.dealData) {
        result.extractedData.dealData = Object.fromEntries(
          Object.entries(result.extractedData.dealData).filter(([key]) => dealsTableColumns.has(key))
        );
      }
      if (Array.isArray(result.extractedData.investmentCriteriaData)) {
        result.extractedData.investmentCriteriaData = result.extractedData.investmentCriteriaData.map((ic: Record<string, any>) =>
          Object.fromEntries(
            Object.entries(ic).filter(([key]) => investmentCriteriaColumns.has(key))
          )
        );
      }
    }

    // Process deal data
    const processResult = await processDealData(
      result,
      dealsTableColumns,
      jobMetadata.originalFileNames,
      llmModel
    );

    // Check if duplicates were found
    if (
      processResult &&
      typeof processResult === "object" &&
      "duplicates" in processResult
    ) {
      return {
        success: false,
        duplicates: processResult.duplicates,
        message: processResult.message,
        extractedData: result.extractedData,
      };
    }

    dealId = processResult as number | null;

    if (!dealId) {
      throw new Error("Failed to create deal record");
    }

    // Update progress
    await job.updateProgress(70);

    // Upload files to FileManager and create relationships


    for (let i = 0; i < fileBuffers.length; i++) {
      const file = fileBuffers[i];
      const fileType = getFileType(file.fileName);

      // Upload file using FileManager
      const uploadRequest = {
        original_name: file.fileName,
        title: `${fileType} for Deal ${dealId}`,
        description: `Uploaded as part of deal extraction process`,
        uploaded_by: contactEmail || createdBy || "system",
        upload_source: "deal_extraction",
        access_level: "private" as const,
        metadata: {
          deal_id: dealId,
          file_type: fileType,
          extraction_timestamp: new Date().toISOString(),
          original_file_index: i,
          job_id: job.id,
        },
      };

      const {
        file: uploadedFile,
        isDuplicate,
        filePath,
      } = await FileManager.uploadFile(file.buffer, uploadRequest);

      // Create relationship to the deal
      const relationshipRequest = {
        target_table_name: "deals",
        target_column_name: "deal_id",
        target_row_id: dealId.toString(),
        relationship_type: fileType,
        relationship_title: `${fileType} document`,
        is_primary: i === 0, // First file is primary
        display_order: i,
      };

      const relationship = await FileManager.createFileRelationship(
        uploadedFile.file_id,
        relationshipRequest
      );

      uploadedFiles.push({
        file_id: uploadedFile.file_id,
        original_name: uploadedFile.original_name,
        file_path: filePath,
        is_duplicate: isDuplicate,
        relationship_type: relationship.relationship_type || fileType,
        is_primary: relationship.is_primary || false,
      });
    }

    // Update progress
    await job.updateProgress(85);

    // Create LLM output file and relationship
    await createLLMOutputFile(
      dealId,
      result,
      jobMetadata.originalFileNames,
      llmModel
    );

    // Handle contact relationships
    if (dealId) {
      console.log(`🔗 Processing contact relationships for deal ${dealId}`);
      console.log(`📋 Job data contactIds:`, job.data.contactIds);
      console.log(`📋 Job data contactId:`, job.data.contactId);
      
      // Handle multiple contacts (new system)
      if (job.data.contactIds && job.data.contactIds.length > 0) {
        console.log(`👥 Inserting ${job.data.contactIds.length} contact relationships`);
        
        // Insert multiple contact relationships
        for (const contactId of job.data.contactIds) {
          console.log(`➕ Adding contact ${contactId} to deal ${dealId}`);
          await pool.query(
            "INSERT INTO public.deal_contacts (deal_id, contact_id) VALUES ($1, $2) ON CONFLICT (deal_id, contact_id) DO NOTHING",
            [dealId, contactId]
          );
        }
        
        // For backward compatibility, set the first contact as the primary contact
        console.log(`🏷️ Setting primary contact ${job.data.contactIds[0]} for deal ${dealId}`);
        await pool.query(
          "UPDATE public.deals SET contact_id = $1 WHERE deal_id = $2",
          [job.data.contactIds[0], dealId]
        );
        
        console.log(`✅ Successfully processed ${job.data.contactIds.length} contacts for deal ${dealId}`);
      }
      // Handle single contact (backward compatibility)
      else if (contactId) {
        console.log(`👤 Processing single contact ${contactId} for deal ${dealId}`);
        
        await pool.query(
          "UPDATE public.deals SET contact_id = $1 WHERE deal_id = $2",
          [parseInt(contactId), dealId]
        );
        
        // Also add to the new junction table for consistency
        await pool.query(
          "INSERT INTO public.deal_contacts (deal_id, contact_id) VALUES ($1, $2) ON CONFLICT (deal_id, contact_id) DO NOTHING",
          [dealId, parseInt(contactId)]
        );
        
        console.log(`✅ Successfully processed single contact for deal ${dealId}`);
      } else {
        console.log(`ℹ️ No contacts to process for deal ${dealId}`);
      }
    }

    // Ensure at least one investment_criteria row exists for the deal
    const countRes = await pool.query(
      "SELECT COUNT(*) FROM public.investment_criteria WHERE entity_type = 'Deal' AND entity_id = $1",
      [dealId]
    );
    const count = parseInt(countRes.rows[0].count, 10);
    if (count === 0) {
      await pool.query(
        "INSERT INTO public.investment_criteria (entity_type, entity_id) VALUES ($1, $2)",
        ["Deal", dealId]
      );
    }

    // Update file metadata to mark as completed
    for (const uploadedFile of uploadedFiles) {
      await pool.query(`
        UPDATE files 
        SET metadata = jsonb_set(
          COALESCE(metadata, '{}'::jsonb),
          '{processing_status}',
          '"completed"'
        ),
        metadata = jsonb_set(
          COALESCE(metadata, '{}'::jsonb),
          '{processing_completed_at}',
          $1::jsonb
        )
        WHERE file_id = $2
      `, [
        JSON.stringify(new Date().toISOString()),
        uploadedFile.file_id
      ]);
    }

    // Update progress
    await job.updateProgress(100);

    return {
      success: true,
      dealId,
      message: "Files processed successfully",
      extractedData: result.extractedData,
      uploadedFiles,
    };
  } catch (error) {
    console.error("Deal processing job failed:", error);
    
    // Update file metadata to mark as failed
    try {
      for (const uploadedFile of uploadedFiles || []) {
        await pool.query(`
          UPDATE files 
          SET metadata = jsonb_set(
            COALESCE(metadata, '{}'::jsonb),
            '{processing_status}',
            '"failed"'
          ),
          metadata = jsonb_set(
            COALESCE(metadata, '{}'::jsonb),
            '{processing_error}',
            $1::jsonb
          )
          WHERE file_id = $2
        `, [
          JSON.stringify(error instanceof Error ? error.message : "Unknown error"),
          uploadedFile.file_id
        ]);
      }
    } catch (metadataError) {
      console.error("Failed to update file metadata:", metadataError);
    }
    
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      message: "Failed to process deal upload",
    };
  }
}

// Helper function to determine file type
function getFileType(fileName: string): string {
  const name = fileName.toLowerCase();
  if (name.includes("memorandum") || name.includes("mem")) return "memorandum";
  if (name.includes("underwriting") || name.includes("uw"))
    return "underwriting";
  if (name.includes("term") || name.includes("sheet")) return "term_sheet";
  if (name.includes("proforma") || name.includes("model")) return "proforma";
  if (name.includes("budget") || name.includes("construction")) return "budget";
  if (name.includes("analysis") || name.includes("market")) return "analysis";
  return "document";
}
