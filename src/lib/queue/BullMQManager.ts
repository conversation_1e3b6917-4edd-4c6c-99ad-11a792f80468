import { Queue, Worker, Job, QueueEvents } from "bullmq";
import { pool } from "@/lib/db";
import {
  DealProcessingJobData,
  DealProcessingResult,
  processDealUpload,
} from "./DealProcessorJob";
import { GmailEmailFetcherProcessor } from "../processors/GmailEmailFetcherProcessor";
import { FirefliesTranscriptProcessor } from "../processors/FirefliesTranscriptProcessor";
import { processRequirementExtraction } from "../workers/requirement-extraction-worker";
import path from "path";
import { fileURLToPath } from "url";

// ES module __dirname workaround
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration interface for easy Redis switching
interface QueueConfig {
  useRedis: boolean;
  redisOptions?: {
    host: string;
    port: number;
    password?: string;
    db?: number;
  };
  defaultJobOptions?: {
    removeOnComplete?: number;
    removeOnFail?: number;
    attempts?: number;
    backoff?: {
      type: string;
      delay: number;
    };
  };
}

// Job status types
export type JobStatus =
  | "waiting"
  | "active"
  | "completed"
  | "failed"
  | "delayed"
  | "paused"
  | "cancelled";

// Job data interface for persistence
export interface JobRecord {
  job_id: string;
  queue_name: string;
  job_name: string;
  status: JobStatus;
  priority: number;
  attempts: number;
  max_attempts: number;
  data?: any;
  result?: any;
  error_message?: string;
  stack_trace?: string;
  progress: number;
  progress_data?: any;
  created_at: Date;
  updated_at: Date;
  started_at?: Date;
  completed_at?: Date;
  failed_at?: Date;
  created_by?: string;
  processor_id?: string;
  processing_duration?: number;
  metadata?: any;
}

// Job file record interface
export interface JobFileRecord {
  job_id: string;
  file_name: string;
  file_type: string;
  file_size: number;
  relationship_type: string;
  is_primary: boolean;
  metadata?: any;
}

export class BullMQManager {
  private static instance: BullMQManager;
  private config: QueueConfig;
  private connectionOptions: any;
  private dealQueue!: Queue;
  private dealWorker!: Worker;
  private queueEvents!: QueueEvents;
  private isInitialized = false;
  private gmailQueue!: Queue;
  private gmailWorker!: Worker;
  private firefliesQueue!: Queue;
  private firefliesWorker!: Worker;

  constructor(
    config: QueueConfig = {
      useRedis: true,
      redisOptions: {
        host: "localhost",
        port: 6379,
      },
    }
  ) {
    this.config = config;
    this.connectionOptions = this.getConnectionOptions();
    this.initialize();
    // Workers are not started automatically
  }

  // Singleton pattern
  public static getInstance(config?: QueueConfig): BullMQManager {
    if (!BullMQManager.instance) {
      BullMQManager.instance = new BullMQManager(config);
    }
    return BullMQManager.instance;
  }

  private getConnectionOptions() {
    if (this.config.useRedis && this.config.redisOptions) {
      return this.config.redisOptions;
    }

    // Use memory storage - no Redis required
    // BullMQ will use memory storage when connection is undefined
    return undefined;
  }

  private initialize() {
    if (this.isInitialized) return;
    try {
      // Initialize deal processing queue
      this.dealQueue = new Queue("deal-processing", {
        connection: this.connectionOptions,
        defaultJobOptions: this.config.defaultJobOptions || {
          removeOnComplete: { count: 10 },
          removeOnFail: { count: 50 },
          attempts: 3,
          backoff: {
            type: "exponential",
            delay: 2000,
          },
        },
      });
      // Initialize queue events for monitoring
      this.queueEvents = new QueueEvents("deal-processing", {
        connection: this.connectionOptions,
      });

      // Initialize Gmail queue
      this.gmailQueue = new Queue("gmail-email-fetch", {
        connection: this.connectionOptions,
        defaultJobOptions: {
          removeOnComplete: { count: 10 },
          removeOnFail: { count: 50 },
          attempts: 3,
          backoff: {
            type: "exponential",
            delay: 2000,
          },
        },
      });

      // Initialize Fireflies queue
      this.firefliesQueue = new Queue("fireflies-transcript-fetch", {
        connection: this.connectionOptions,
        defaultJobOptions: {
          removeOnComplete: { count: 10 },
          removeOnFail: { count: 50 },
          attempts: 3,
          backoff: {
            type: "exponential",
            delay: 2000,
          },
        },
      });

      // Do not start workers here
      this.isInitialized = true;
      console.log("BullMQ Manager (deal-processing, gmail-email-fetch, and fireflies-transcript-fetch queues) initialized successfully");
    } catch (error) {
      console.error("Failed to initialize BullMQ Manager:", error);
      throw error;
    }
  }

  // Public method to start the deal worker
  public startDealWorker() {
    if (this.dealWorker) {
      console.log("Deal worker already started.");
      return;
    }
    this.dealWorker = new Worker(
      "deal-processing",
      this.processJob.bind(this),
      {
        connection: this.connectionOptions,
        concurrency: 2, // Process 2 jobs concurrently
        removeOnComplete: { count: 10 },
        removeOnFail: { count: 50 },
      }
    );
    this.setupEventListeners();
    console.log("Deal worker started.");
  }

  // Public method to start the Gmail worker
  public startGmailWorker() {
    if (this.gmailWorker) {
      console.log("Gmail worker already started.");
      return;
    }
    this.gmailWorker = new Worker(
      "gmail-email-fetch",
      this.processGmailJob.bind(this),
      {
        connection: this.connectionOptions,
        concurrency: 1,
        removeOnComplete: { count: 10 },
        removeOnFail: { count: 50 },
      }
    );
    console.log("Gmail worker started.");
  }

  // Public method to start the Fireflies worker
  public startFirefliesWorker() {
    if (this.firefliesWorker) {
      console.log("Fireflies worker already started.");
      return;
    }
    this.firefliesWorker = new Worker(
      "fireflies-transcript-fetch",
      this.processFirefliesJob.bind(this),
      {
        connection: this.connectionOptions,
        concurrency: 1,
        removeOnComplete: { count: 10 },
        removeOnFail: { count: 50 },
      }
    );
    console.log("Fireflies worker started.");
  }



  // Getter methods for queue access
  public getGmailQueue(): Queue {
    return this.gmailQueue;
  }

  public getFirefliesQueue(): Queue {
    return this.firefliesQueue;
  }

  public getDealQueue(): Queue {
    return this.dealQueue;
  }



  // Gmail job processor
  private async processGmailJob(job: Job): Promise<void> {
    console.log(`[GmailJob] Starting Gmail fetch job (job id: ${job.id})`);
    try {
      const processor = new GmailEmailFetcherProcessor(
        path.resolve(__dirname, "../../service-creds.json")
      );
      await processor.run();
      console.log(`[GmailJob] Completed Gmail fetch job (job id: ${job.id})`);
    } catch (error) {
      console.error(
        `[GmailJob] Failed Gmail fetch job (job id: ${job.id}):`,
        error
      );
      throw error;
    }
  }

  // Fireflies job processor
  private async processFirefliesJob(job: Job): Promise<void> {
    console.log(`[FirefliesJob] Starting Fireflies transcript fetch job (job id: ${job.id})`);
    try {
      const processor = new FirefliesTranscriptProcessor();
      await processor.run();
      console.log(`[FirefliesJob] Completed Fireflies transcript fetch job (job id: ${job.id})`);
    } catch (error) {
      console.error(
        `[FirefliesJob] Failed Fireflies transcript fetch job (job id: ${job.id}):`,
        error
      );
      throw error;
    }
  }

  // Deal V2 job processor
  private async processDealV2Job(job: Job): Promise<any> {
    console.log(`[DealV2Job] Starting Deal V2 processing job (job id: ${job.id})`);
    try {
      // Import DealProcessorV2 dynamically to avoid circular dependencies
      const { DealProcessorV2 } = await import("../processors/DealProcessorV2");
      
      const processor = new DealProcessorV2({
        llmModel: "gemini-flash",
      });
      
      // Convert job data to the format expected by processFiles
      const files = job.data.files.map((file: any) => ({
        buffer: Buffer.from(file.buffer, 'base64'), // Decode base64 buffer
        mimeType: file.mimeType,
        fileName: file.fileName
      }));
      
      const result = await processor.processFiles(files);
      console.log(`[DealV2Job] Completed Deal V2 processing job (job id: ${job.id})`);
      return result;
    } catch (error) {
      console.error(
        `[DealV2Job] Failed Deal V2 processing job (job id: ${job.id}):`,
        error
      );
      throw error;
    }
  }

  private async processJob(
    job: Job<any>
  ): Promise<any> {
    console.log(`Processing job ${job.id} of type ${job.name}`);

    try {
      // Update job status in database
      await this.updateJobInDatabase(job.id!, {
        status: "active",
        started_at: new Date(),
        processor_id: process.pid.toString(),
      });

      let result;
      
      // Route to appropriate processor based on job name
      if (job.name === 'requirement-extraction') {
        result = await processRequirementExtraction(job);
      } else if (job.name === 'process-deal-v2-upload') {
        // Use DealProcessorV2 for V2 processing
        result = await this.processDealV2Job(job);
      } else {
        // Default to deal processing
        result = await processDealUpload(job);
      }

      // Update progress and result in database
      const jobIdToUpdate = job.name === 'requirement-extraction' && job.data?.databaseJobId 
        ? job.data.databaseJobId 
        : job.id!;
      
      await this.updateJobInDatabase(jobIdToUpdate, {
        status: result.success ? "completed" : "failed",
        progress: 100,
        result: result,
        completed_at: result.success ? new Date() : undefined,
        failed_at: result.success ? undefined : new Date(),
        error_message: result.error,
        processing_duration: job.processedOn ? Date.now() - job.processedOn : 0,
      });

      return result;
    } catch (error) {
      console.error(`Job ${job.id} failed:`, error);

      // Update job status to failed
      const jobIdToUpdate = job.name === 'requirement-extraction' && job.data?.databaseJobId 
        ? job.data.databaseJobId 
        : job.id!;
      
      await this.updateJobInDatabase(jobIdToUpdate, {
        status: "failed",
        failed_at: new Date(),
        error_message: error instanceof Error ? error.message : "Unknown error",
        stack_trace: error instanceof Error ? error.stack : undefined,
        processing_duration: job.processedOn ? Date.now() - job.processedOn : 0,
      });

      throw error;
    }
  }

  private setupEventListeners() {
    // Listen for job progress updates
    this.dealWorker.on("progress", async (job: Job, progress: any) => {
      const progressValue = typeof progress === "number" ? progress : 0;
      const jobIdToUpdate = job.name === 'requirement-extraction' && job.data?.databaseJobId 
        ? job.data.databaseJobId 
        : job.id!;
      
      await this.updateJobInDatabase(jobIdToUpdate, {
        progress: progressValue,
        updated_at: new Date(),
      });
    });

    // Listen for job completion
    this.dealWorker.on("completed", async (job: Job, result: any) => {
      console.log(`Job ${job.id} completed successfully`);
      const jobIdToLog = job.name === 'requirement-extraction' && job.data?.databaseJobId 
        ? job.data.databaseJobId 
        : job.id!;
      
      await this.logJobEvent(jobIdToLog, "info", "Job completed successfully", {
        result: result,
      });
    });

    // Listen for job failure
    this.dealWorker.on("failed", async (job: Job | undefined, error: Error) => {
      if (job && job.id) {
        console.error(`Job ${job.id} failed:`, error.message);
        const jobIdToLog = job.name === 'requirement-extraction' && job.data?.databaseJobId 
          ? job.data.databaseJobId 
          : job.id;
        
        await this.logJobEvent(
          jobIdToLog,
          "error",
          `Job failed: ${error.message}`,
          {
            error: error.message,
            stack: error.stack,
          }
        );
      } else {
        console.error("Unknown job failed:", error.message);
      }
    });

    // Listen for queue events
    this.queueEvents.on("waiting", async ({ jobId }) => {
      console.log(`Job ${jobId} is waiting`);
      await this.logJobEvent(jobId, "info", "Job is waiting in queue");
    });

    this.queueEvents.on("active", async ({ jobId }) => {
      console.log(`Job ${jobId} is now active`);
      await this.logJobEvent(jobId, "info", "Job is now active");
    });

    this.queueEvents.on("completed", async ({ jobId }) => {
      console.log(`Job ${jobId} completed`);
    });

    this.queueEvents.on("failed", async ({ jobId, failedReason }) => {
      console.log(`Job ${jobId} failed: ${failedReason}`);
    });
  }

  // Public method to add a deal processing job
  public async addDealProcessingJob(
    jobData: DealProcessingJobData,
    options: {
      priority?: number;
      delay?: number;
      createdBy?: string;
    } = {}
  ): Promise<string> {
    try {
      if (!this.isInitialized) {
        throw new Error("BullMQ Manager not initialized");
      }

      // Add job to BullMQ queue
      const job = await this.dealQueue.add("process-deal-upload", jobData, {
        priority: options.priority || 0,
        delay: options.delay || 0,
        removeOnComplete: { count: 10 },
        removeOnFail: { count: 50 },
        attempts: 3,
        backoff: {
          type: "exponential",
          delay: 2000,
        },
      });

      const jobId = job.id!;

      try {
        // Check if job already exists in database
        const existingJob = await this.getJobStatus(jobId);
        if (existingJob) {
          console.log(`Job ${jobId} already exists in database, updating instead of creating`);
          // Update existing job instead of creating new one
          await this.updateJobInDatabase(jobId, {
            status: "waiting",
            priority: options.priority || 0,
            data: jobData,
            metadata: {
              fileCount: jobData.files.length,
              originalFileNames: jobData.jobMetadata.originalFileNames,
              uploadTimestamp: jobData.jobMetadata.uploadTimestamp,
            },
          });
        } else {
          // Store job in database for persistence
          await this.createJobInDatabase({
            job_id: jobId,
            queue_name: "deal-processing",
            job_name: "process-deal-upload",
            status: "waiting",
            priority: options.priority || 0,
            attempts: 0,
            max_attempts: 3,
            data: jobData,
            progress: 0,
            created_by: options.createdBy,
            metadata: {
              fileCount: jobData.files.length,
              originalFileNames: jobData.jobMetadata.originalFileNames,
              uploadTimestamp: jobData.jobMetadata.uploadTimestamp,
            },
          });
        }

        // Store file information (only if job was newly created)
        if (!existingJob) {
          for (let i = 0; i < jobData.files.length; i++) {
            const file = jobData.files[i];
            await this.createJobFileInDatabase({
              job_id: jobId,
              file_name: file.fileName,
              file_type: file.mimeType,
              file_size: Buffer.from(file.buffer, "base64").length,
              relationship_type: "input",
              is_primary: i === 0,
              metadata: {
                fileIndex: i,
                originalName: jobData.jobMetadata.originalFileNames[i],
              },
            });
          }
        }

        // Log job creation
        await this.logJobEvent(jobId, "info", "Deal processing job created", {
          fileCount: jobData.files.length,
          createdBy: options.createdBy,
        });

        console.log(`Created deal processing job with ID: ${jobId}`);
        return jobId;
      } catch (dbError) {
        // If database insertion fails, remove the job from BullMQ queue
        console.error("Database insertion failed, removing job from queue:", dbError);
        await job.remove();
        throw dbError;
      }
    } catch (error) {
      console.error("Error adding deal processing job:", error);
      throw error;
    }
  }

  // Public method to add a V2 deal processing job using DealProcessorV2
  public async addDealV2ProcessingJob(
    jobData: DealProcessingJobData,
    options: {
      priority?: number;
      delay?: number;
      createdBy?: string;
    } = {}
  ): Promise<string> {
    try {
      if (!this.isInitialized) {
        throw new Error("BullMQ Manager not initialized");
      }

      // Add job to BullMQ queue with V2 processor
      const job = await this.dealQueue.add("process-deal-v2-upload", jobData, {
        priority: options.priority || 0,
        delay: options.delay || 0,
        removeOnComplete: { count: 10 },
        removeOnFail: { count: 50 },
        attempts: 3,
        backoff: {
          type: "exponential",
          delay: 2000,
        },
      });

      const jobId = job.id!;

      try {
        // Check if job already exists in database
        const existingJob = await this.getJobStatus(jobId);
        if (existingJob) {
          console.log(`Job ${jobId} already exists in database, updating instead of creating`);
          // Update existing job instead of creating new one
          await this.updateJobInDatabase(jobId, {
            status: "waiting",
            priority: options.priority || 0,
            data: jobData,
            metadata: {
              fileCount: jobData.files.length,
              originalFileNames: jobData.jobMetadata.originalFileNames,
              uploadTimestamp: jobData.jobMetadata.uploadTimestamp,
              processorVersion: "V2",
            },
          });
        } else {
          // Store job in database for persistence
          await this.createJobInDatabase({
            job_id: jobId,
            queue_name: "deal-processing-v2",
            job_name: "process-deal-v2-upload",
            status: "waiting",
            priority: options.priority || 0,
            attempts: 0,
            max_attempts: 3,
            data: jobData,
            progress: 0,
            created_by: options.createdBy,
            metadata: {
              fileCount: jobData.files.length,
              originalFileNames: jobData.jobMetadata.originalFileNames,
              uploadTimestamp: jobData.jobMetadata.uploadTimestamp,
              processorVersion: "V2",
            },
          });
        }

        // Store file information (only if job was newly created)
        if (!existingJob) {
          for (let i = 0; i < jobData.files.length; i++) {
            const file = jobData.files[i];
            await this.createJobFileInDatabase({
              job_id: jobId,
              file_name: file.fileName,
              file_type: file.mimeType,
              file_size: Buffer.from(file.buffer, "base64").length,
              relationship_type: "input",
              is_primary: i === 0,
              metadata: {
                fileIndex: i,
                originalName: jobData.jobMetadata.originalFileNames[i],
                processorVersion: "V2",
              },
            });
          }
        }

        // Log job creation
        await this.logJobEvent(jobId, "info", "Deal V2 processing job created", {
          fileCount: jobData.files.length,
          createdBy: options.createdBy,
          processorVersion: "V2",
        });

        console.log(`Created deal V2 processing job with ID: ${jobId}`);
        return jobId;
      } catch (dbError) {
        // If database insertion fails, remove the job from BullMQ queue
        console.error("Database insertion failed, removing job from queue:", dbError);
        await job.remove();
        throw dbError;
      }
    } catch (error) {
      console.error("Error adding deal V2 processing job:", error);
      throw error;
    }
  }

  // Public method to add a Gmail fetch job
  public async addGmailFetchJob(
    options: {
      repeat?: { cron: string };
      removeOnComplete?: boolean;
      removeOnFail?: boolean;
    } = {}
  ): Promise<string> {
    const jobOptions: any = {
      removeOnComplete: options.removeOnComplete ?? true,
      removeOnFail: options.removeOnFail ?? true,
    };
    if (options.repeat && options.repeat.cron) {
      jobOptions.repeat = { cron: options.repeat.cron };
    }
    const job = await this.gmailQueue.add(
      "fetch-gmail-emails",
      {},
      jobOptions
    );
    return job?.id as string;
  }

  // Public method to add a Fireflies fetch job
  public async addFirefliesFetchJob(
    options: {
      repeat?: { cron: string };
      removeOnComplete?: boolean;
      removeOnFail?: boolean;
    } = {}
  ): Promise<string> {
    const jobOptions: any = {
      removeOnComplete: options.removeOnComplete ?? true,
      removeOnFail: options.removeOnFail ?? true,
    };
    if (options.repeat && options.repeat.cron) {
      jobOptions.repeat = { cron: options.repeat.cron };
    }
    const job = await this.firefliesQueue.add(
      "fetch-fireflies-transcripts",
      {},
      jobOptions
    );
    return job?.id as string;
  }

  // Get job status and details
  public async getJobStatus(jobId: string): Promise<JobRecord | null> {
    try {
      const result = await pool.query(
        "SELECT * FROM public.jobs WHERE job_id = $1",
        [jobId]
      );

      return result.rows[0] || null;
    } catch (error) {
      console.error(`Error getting job status for ${jobId}:`, error);
      return null;
    }
  }

  // Get recent jobs with pagination
  public async getRecentJobs(
    limit: number = 10,
    offset: number = 0,
    createdBy?: string
  ): Promise<JobRecord[]> {
    try {
      let query = `
        SELECT * FROM public.jobs 
        WHERE queue_name = 'deal-processing'
      `;
      const params: any[] = [];

      if (createdBy) {
        query += ` AND created_by = $${params.length + 1}`;
        params.push(createdBy);
      }

      query += ` ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${
        params.length + 2
      }`;
      params.push(limit, offset);

      const result = await pool.query(query, params);
      return result.rows;
    } catch (error) {
      console.error("Error getting recent jobs:", error);
      return [];
    }
  }

  // Get recent uploads with file details
  public async getRecentUploads(
    limit: number = 10,
    offset: number = 0,
    createdBy?: string
  ): Promise<any[]> {
    try {
      let query = `
        SELECT 
          j.job_id,
          j.status,
          j.progress,
          j.created_at,
          j.completed_at,
          j.error_message,
          j.created_by,
          j.metadata,
          array_agg(
            json_build_object(
              'file_name', jf.file_name,
              'file_type', jf.file_type,
              'file_size', jf.file_size,
              'is_primary', jf.is_primary
            ) ORDER BY jf.is_primary DESC, jf.job_file_id
          ) as files
        FROM public.jobs j
        LEFT JOIN public.job_files jf ON j.job_id = jf.job_id
        WHERE j.queue_name = 'deal-processing'
      `;
      const params: any[] = [];

      if (createdBy) {
        query += ` AND j.created_by = $${params.length + 1}`;
        params.push(createdBy);
      }

      query += ` 
        GROUP BY j.job_id, j.status, j.progress, j.created_at, j.completed_at, j.error_message, j.created_by, j.metadata
        ORDER BY j.created_at DESC 
        LIMIT $${params.length + 1} OFFSET $${params.length + 2}
      `;
      params.push(limit, offset);

      const result = await pool.query(query, params);
      return result.rows;
    } catch (error) {
      console.error("Error getting recent uploads:", error);
      return [];
    }
  }

  // Get queue statistics
  public async getQueueStats() {
    try {
      let bullStats = {};
      if (this.isInitialized) {
        bullStats = await this.dealQueue.getJobCounts();
      }

      const dbStats = await this.getQueueStatsFromDB();

      return {
        bull: bullStats,
        database: dbStats,
      };
    } catch (error) {
      console.error("Error getting queue stats:", error);
      return {
        bull: {},
        database: {},
      };
    }
  }

  private async getQueueStatsFromDB() {
    try {
      const result = await pool.query(`
        SELECT 
          status,
          COUNT(*) as count
        FROM public.jobs 
        WHERE queue_name = 'deal-processing'
        GROUP BY status
      `);

      const stats: Record<string, number> = {};
      result.rows.forEach((row) => {
        stats[row.status] = parseInt(row.count);
      });

      return stats;
    } catch (error) {
      console.error("Error getting DB queue stats:", error);
      return {};
    }
  }

  // Database operations
  private async createJobInDatabase(
    jobData: Partial<JobRecord>
  ): Promise<void> {
    const query = `
      INSERT INTO public.jobs (
        job_id, queue_name, job_name, status, priority, attempts, max_attempts,
        data, progress, created_by, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      ON CONFLICT (job_id) DO UPDATE SET
        updated_at = NOW(),
        status = EXCLUDED.status,
        priority = EXCLUDED.priority,
        data = EXCLUDED.data,
        metadata = EXCLUDED.metadata
    `;

    await pool.query(query, [
      jobData.job_id,
      jobData.queue_name,
      jobData.job_name,
      jobData.status,
      jobData.priority,
      jobData.attempts,
      jobData.max_attempts,
      JSON.stringify(jobData.data),
      jobData.progress,
      jobData.created_by,
      JSON.stringify(jobData.metadata),
    ]);
  }

  public async updateJobInDatabase(
    jobId: string,
    updates: Partial<JobRecord>
  ): Promise<void> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    for (const [key, value] of Object.entries(updates)) {
      if (value !== undefined) {
        fields.push(`${key} = $${paramIndex}`);

        // Handle JSON fields
        if (["data", "result", "progress_data", "metadata"].includes(key)) {
          values.push(JSON.stringify(value));
        } else {
          values.push(value);
        }

        paramIndex++;
      }
    }

    if (fields.length === 0) return;

    const query = `
      UPDATE public.jobs 
      SET ${fields.join(", ")}
      WHERE job_id = $${paramIndex}
    `;

    values.push(jobId);
    await pool.query(query, values);
  }

  private async createJobFileInDatabase(
    fileData: JobFileRecord
  ): Promise<void> {
    const query = `
      INSERT INTO public.job_files (
        job_id, file_name, file_type, file_size, relationship_type, is_primary, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `;

    try {
      await pool.query(query, [
        fileData.job_id,
        fileData.file_name,
        fileData.file_type,
        fileData.file_size,
        fileData.relationship_type,
        fileData.is_primary,
        JSON.stringify(fileData.metadata),
      ]);
    } catch (err) {
      // Optionally, handle duplicate errors gracefully
      const error = err as any;
      if (error.code === '23505') { // unique_violation
        // Ignore or log duplicate
        console.warn('Duplicate job_file entry ignored:', fileData.job_id, fileData.file_name);
      } else {
        throw err;
      }
    }
  }

  private async logJobEvent(
    jobId: string,
    level: "debug" | "info" | "warn" | "error",
    message: string,
    data?: any
  ): Promise<void> {
    try {
      const query = `
        INSERT INTO public.job_logs (job_id, level, message, data)
        VALUES ($1, $2, $3, $4)
      `;

      await pool.query(query, [
        jobId,
        level,
        message,
        data ? JSON.stringify(data) : null,
      ]);
    } catch (error) {
      console.error("Error logging job event:", error);
    }
  }

  // Cleanup methods
  public async cleanup(): Promise<void> {
    try {
      if (this.dealWorker) {
        await this.dealWorker.close();
        this.dealWorker = undefined as any;
      }
      if (this.dealQueue) {
        await this.dealQueue.close();
      }
      if (this.queueEvents) {
        await this.queueEvents.close();
      }
      if (this.gmailWorker) {
        await this.gmailWorker.close();
        this.gmailWorker = undefined as any;
      }
      if (this.gmailQueue) {
        await this.gmailQueue.close();
      }
      if (this.firefliesWorker) {
        await this.firefliesWorker.close();
        this.firefliesWorker = undefined as any;
      }
      if (this.firefliesQueue) {
        await this.firefliesQueue.close();
      }
      this.isInitialized = false;
      console.log("BullMQ Manager cleaned up successfully");
    } catch (error) {
      console.error("Error during cleanup:", error);
    }
  }

  // Method to switch to Redis in the future
  public async switchToRedis(
    redisOptions: QueueConfig["redisOptions"]
  ): Promise<void> {
    console.log("Switching to Redis configuration...");

    try {
      // Close current connections
      await this.cleanup();

      // Update configuration
      this.config.useRedis = true;
      this.config.redisOptions = redisOptions;
      this.connectionOptions = this.getConnectionOptions();

      // Reinitialize with Redis
      this.initialize();
      // Workers are not started automatically

      console.log("Successfully switched to Redis configuration");
    } catch (error) {
      console.error("Error switching to Redis:", error);
      throw error;
    }
  }

  // Health check method
  public async healthCheck(): Promise<{
    status: string;
    queueConnected: boolean;
    workerConnected: boolean;
    eventsConnected: boolean;
  }> {
    try {
      const queueHealth = this.dealQueue && this.isInitialized;
      const workerHealth =
        this.dealWorker && !this.dealWorker.isRunning() === false;
      const eventsHealth = this.queueEvents && this.isInitialized;

      return {
        status:
          queueHealth && workerHealth && eventsHealth ? "healthy" : "unhealthy",
        queueConnected: !!queueHealth,
        workerConnected: !!workerHealth,
        eventsConnected: !!eventsHealth,
      };
    } catch (error) {
      console.error("Error during health check:", error);
      return {
        status: "error",
        queueConnected: false,
        workerConnected: false,
        eventsConnected: false,
      };
    }
  }
}

// Export singleton instance with Redis configuration
export const bullMQManager = BullMQManager.getInstance({
  useRedis: true,
  redisOptions: {
    host: "localhost",
    port: 6379,
  },
});
