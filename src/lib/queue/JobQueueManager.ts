import { Queue, Worker, Job, QueueEvents } from "bullmq";
import { pool } from "@/lib/db";
import {
  DealProcessingJobData,
  DealProcessingResult,
  processDealUpload,
} from "./DealProcessorJob";

// Configuration interface for easy Redis switching
interface QueueConfig {
  useRedis: boolean;
  redisOptions?: {
    host: string;
    port: number;
    password?: string;
    db?: number;
  };
  defaultJobOptions?: {
    removeOnComplete?: number;
    removeOnFail?: number;
    attempts?: number;
    backoff?: {
      type: string;
      delay: number;
    };
  };
}

// Job status types
export type JobStatus =
  | "waiting"
  | "active"
  | "completed"
  | "failed"
  | "delayed"
  | "paused"
  | "cancelled";

// Job data interface for persistence
export interface JobRecord {
  job_id: string;
  queue_name: string;
  job_name: string;
  status: JobStatus;
  priority: number;
  attempts: number;
  max_attempts: number;
  data?: any;
  result?: any;
  error_message?: string;
  stack_trace?: string;
  progress: number;
  progress_data?: any;
  created_at: Date;
  updated_at: Date;
  started_at?: Date;
  completed_at?: Date;
  failed_at?: Date;
  created_by?: string;
  processor_id?: string;
  processing_duration?: number;
  metadata?: any;
}

// Job file record interface
export interface JobFileRecord {
  job_id: string;
  file_name: string;
  file_type: string;
  file_size: number;
  relationship_type: string;
  is_primary: boolean;
  metadata?: any;
}

export class JobQueueManager {
  private static instance: JobQueueManager;
  private config: QueueConfig;
  private connectionOptions: any;
  private dealQueue!: Queue;
  private dealWorker?: Worker;
  private queueEvents!: QueueEvents;
  private isInitialized = false;

  constructor(config: QueueConfig = { useRedis: false }) {
    this.config = config;
    this.connectionOptions = this.getConnectionOptions();
    this.initialize();
  }

  private initialize() {
    if (this.isInitialized) return;
    
    // Initialize deal processing queue
    this.dealQueue = new Queue("deal-processing", {
      connection: this.connectionOptions,
      defaultJobOptions: this.config.defaultJobOptions || {
        removeOnComplete: 10,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: "exponential",
          delay: 2000,
        },
      },
    });

    // Initialize queue events for monitoring
    this.queueEvents = new QueueEvents("deal-processing", {
      connection: this.connectionOptions,
    });

    this.isInitialized = true;
    console.log("JobQueueManager initialized successfully (queue only, no worker)");
  }

  // Public method to start the deal worker
  public startDealWorker() {
    if (this.dealWorker) {
      console.log("Deal worker already started.");
      return;
    }
    
    this.dealWorker = new Worker(
      "deal-processing",
      this.processJob.bind(this),
      {
        connection: this.connectionOptions,
        concurrency: 2, // Process 2 jobs concurrently
      }
    );
    
    this.setupEventListeners();
    console.log("Deal worker started.");
  }

  // Singleton pattern
  public static getInstance(config?: QueueConfig): JobQueueManager {
    if (!JobQueueManager.instance) {
      JobQueueManager.instance = new JobQueueManager(config);
    }
    return JobQueueManager.instance;
  }

  private getConnectionOptions() {
    if (this.config.useRedis && this.config.redisOptions) {
      return this.config.redisOptions;
    }

    // Use memory storage - no Redis required
    return undefined;
  }

  private async processJob(
    job: Job<DealProcessingJobData>
  ): Promise<DealProcessingResult> {
    console.log(`Processing job ${job.id} of type ${job.name}`);

    try {
      // Update job status in database
      await this.updateJobInDatabase(job.id!, {
        status: "active",
        started_at: new Date(),
        processor_id: process.pid.toString(),
      });

      // Process the actual job
      const result = await processDealUpload(job);

      // Update progress and result in database
      await this.updateJobInDatabase(job.id!, {
        status: result.success ? "completed" : "failed",
        progress: 100,
        result: result,
        completed_at: result.success ? new Date() : undefined,
        failed_at: result.success ? undefined : new Date(),
        error_message: result.error,
        processing_duration: Date.now() - job.processedOn!,
      });

      return result;
    } catch (error) {
      console.error(`Job ${job.id} failed:`, error);

      // Update job status to failed
      await this.updateJobInDatabase(job.id!, {
        status: "failed",
        failed_at: new Date(),
        error_message: error instanceof Error ? error.message : "Unknown error",
        stack_trace: error instanceof Error ? error.stack : undefined,
        processing_duration: Date.now() - job.processedOn!,
      });

      throw error;
    }
  }

  private setupEventListeners() {
    if (!this.dealWorker) return;
    
    // Listen for job progress updates
    this.dealWorker.on("progress", async (job: Job, progress: any) => {
      await this.updateJobInDatabase(job.id!, {
        progress: typeof progress === 'number' ? progress : 0,
        updated_at: new Date(),
      });
    });

    // Listen for job completion
    this.dealWorker.on("completed", async (job: Job, result: any) => {
      console.log(`Job ${job.id} completed successfully`);
    });

    // Listen for job failure
    this.dealWorker.on("failed", async (job: Job | undefined, error: Error) => {
      if (job) {
        console.error(`Job ${job.id} failed:`, error.message);
      }
    });

    // Listen for queue events
    this.queueEvents.on("waiting", async ({ jobId }) => {
      console.log(`Job ${jobId} is waiting`);
    });

    this.queueEvents.on("active", async ({ jobId }) => {
      console.log(`Job ${jobId} is now active`);
    });
  }

  // Public method to add a deal processing job
  public async addDealProcessingJob(
    jobData: DealProcessingJobData,
    options: {
      priority?: number;
      delay?: number;
      createdBy?: string;
    } = {}
  ): Promise<string> {
    try {
      // Add job to BullMQ queue
      const job = await this.dealQueue.add("process-deal-upload", jobData, {
        priority: options.priority || 0,
        delay: options.delay || 0,
      });

      const jobId = job.id!;

      // Store job in database for persistence
      await this.createJobInDatabase({
        job_id: jobId,
        queue_name: "deal-processing",
        job_name: "process-deal-upload",
        status: "waiting",
        priority: options.priority || 0,
        attempts: 0,
        max_attempts: 3,
        data: jobData,
        progress: 0,
        created_by: options.createdBy,
        metadata: {
          fileCount: jobData.files.length,
          originalFileNames: jobData.jobMetadata.originalFileNames,
          uploadTimestamp: jobData.jobMetadata.uploadTimestamp,
        },
      });

      // Store file information
      for (let i = 0; i < jobData.files.length; i++) {
        const file = jobData.files[i];
        await this.createJobFileInDatabase({
          job_id: jobId,
          file_name: file.fileName,
          file_type: file.mimeType,
          file_size: Buffer.from(file.buffer, "base64").length,
          relationship_type: "input",
          is_primary: i === 0,
          metadata: {
            fileIndex: i,
            originalName: jobData.jobMetadata.originalFileNames[i],
          },
        });
      }

      console.log(`Created deal processing job with ID: ${jobId}`);
      return jobId;
    } catch (error) {
      console.error("Error adding deal processing job:", error);
      throw error;
    }
  }

  // Get job status and details
  public async getJobStatus(jobId: string): Promise<JobRecord | null> {
    try {
      const result = await pool.query(
        "SELECT * FROM public.jobs WHERE job_id = $1",
        [jobId]
      );

      return result.rows[0] || null;
    } catch (error) {
      console.error(`Error getting job status for ${jobId}:`, error);
      return null;
    }
  }

  // Get recent jobs with pagination
  public async getRecentJobs(
    limit: number = 10,
    offset: number = 0,
    createdBy?: string
  ): Promise<JobRecord[]> {
    try {
      let query = `
        SELECT * FROM public.jobs 
        WHERE 1=1
      `;
      const params: any[] = [];

      if (createdBy) {
        query += ` AND created_by = $${params.length + 1}`;
        params.push(createdBy);
      }

      query += ` ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${
        params.length + 2
      }`;
      params.push(limit, offset);

      const result = await pool.query(query, params);
      return result.rows;
    } catch (error) {
      console.error("Error getting recent jobs:", error);
      return [];
    }
  }

  // Get queue statistics
  public async getQueueStats() {
    try {
      const [bullStats, dbStats] = await Promise.all([
        this.dealQueue.getJobCounts(),
        this.getQueueStatsFromDB(),
      ]);

      return {
        bull: bullStats,
        database: dbStats,
      };
    } catch (error) {
      console.error("Error getting queue stats:", error);
      return null;
    }
  }

  private async getQueueStatsFromDB() {
    try {
      const result = await pool.query(`
        SELECT 
          status,
          COUNT(*) as count
        FROM public.jobs 
        WHERE queue_name = 'deal-processing'
        GROUP BY status
      `);

      const stats: Record<string, number> = {};
      result.rows.forEach((row) => {
        stats[row.status] = parseInt(row.count);
      });

      return stats;
    } catch (error) {
      console.error("Error getting DB queue stats:", error);
      return {};
    }
  }

  // Database operations
  private async createJobInDatabase(
    jobData: Partial<JobRecord>
  ): Promise<void> {
    const query = `
      INSERT INTO public.jobs (
        job_id, queue_name, job_name, status, priority, attempts, max_attempts,
        data, progress, created_by, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
    `;

    await pool.query(query, [
      jobData.job_id,
      jobData.queue_name,
      jobData.job_name,
      jobData.status,
      jobData.priority,
      jobData.attempts,
      jobData.max_attempts,
      JSON.stringify(jobData.data),
      jobData.progress,
      jobData.created_by,
      JSON.stringify(jobData.metadata),
    ]);
  }

  private async updateJobInDatabase(
    jobId: string,
    updates: Partial<JobRecord>
  ): Promise<void> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    for (const [key, value] of Object.entries(updates)) {
      if (value !== undefined) {
        fields.push(`${key} = $${paramIndex}`);

        // Handle JSON fields
        if (["data", "result", "progress_data", "metadata"].includes(key)) {
          values.push(JSON.stringify(value));
        } else {
          values.push(value);
        }

        paramIndex++;
      }
    }

    if (fields.length === 0) return;

    const query = `
      UPDATE public.jobs 
      SET ${fields.join(", ")}
      WHERE job_id = $${paramIndex}
    `;

    values.push(jobId);
    await pool.query(query, values);
  }

  private async createJobFileInDatabase(
    fileData: JobFileRecord
  ): Promise<void> {
    const query = `
      INSERT INTO public.job_files (
        job_id, file_name, file_type, file_size, relationship_type, is_primary, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `;

    await pool.query(query, [
      fileData.job_id,
      fileData.file_name,
      fileData.file_type,
      fileData.file_size,
      fileData.relationship_type,
      fileData.is_primary,
      JSON.stringify(fileData.metadata),
    ]);
  }

  // Cleanup methods
  public async cleanup(): Promise<void> {
    if (this.dealWorker) {
      await this.dealWorker.close();
    }
    await this.dealQueue.close();
    await this.queueEvents.close();
  }

  // Method to switch to Redis in the future
  public async switchToRedis(
    redisOptions: QueueConfig["redisOptions"]
  ): Promise<void> {
    console.log("Switching to Redis configuration...");

    // Close current connections
    await this.cleanup();

    // Update configuration
    this.config.useRedis = true;
    this.config.redisOptions = redisOptions;
    this.connectionOptions = this.getConnectionOptions();

    // Reinitialize with Redis
    this.dealQueue = new Queue("deal-processing", {
      connection: this.connectionOptions,
      defaultJobOptions: this.config.defaultJobOptions,
    });

    this.queueEvents = new QueueEvents("deal-processing", {
      connection: this.connectionOptions,
    });

    // Note: Worker will need to be started separately with startDealWorker()
    console.log("Successfully switched to Redis configuration (queue only)");
  }
}

// Export singleton instance
export const jobQueueManager = JobQueueManager.getInstance();

// Types for external use
export type { DealProcessingJobData, DealProcessingResult };
