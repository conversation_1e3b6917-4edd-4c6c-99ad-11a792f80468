import { pool } from '@/lib/db'
import { fileStorageService, UPLOAD_SUBDIRECTORIES, StoredFileInfo } from './FileStorageService'
import { CSVParserService } from './CSVParserService'
import { InvestmentCriteriaProcessor } from '@/lib/processors/InvestmentCriteriaProcessor'
import * as XLSX from 'xlsx'

export interface AsyncUploadRequest {
  file: File
  headerMappings: Record<string, string[]> // Only support structured format
  structuredMappings: {
    company_mappings: Record<string, string[]>
    contact_mappings: Record<string, string[]>
    investment_criteria_mappings: Record<string, string[]>
    unmapped_headers: string[]
    database_fields: {
      companies: string[]
      contacts: string[]
      investment_criteria: string[]
    }
  }
  source?: string
  uploadedBy?: string
  llmMetadata?: {
    llmUsed: string
    prompt: string
    input: string
    output: string
    tokensUsed: number
  }
}

export interface UploadLogRecord {
  upload_id: number
  file_name: string
  file_path: string
  status: string
  total_rows: number
  processed_until?: number
  progress_percentage: number
  created_at: string
  processing_started_at?: string
  processing_completed_at?: string
  error_message?: string
  companies_processed?: number
  contacts_processed?: number
  conflicts_detected?: number
  header_mappings?: any
  mapped_columns?: any
  extra_attrs?: any
  // Enhanced structured mapping fields
  structured_mappings?: {
    companies?: Record<string, string[]>
    contacts?: Record<string, string[]>
    investment_criteria?: Record<string, string[]>
  } | null
  database_fields?: {
    companies?: string[]
    contacts?: string[]
    investment_criteria?: string[]
  } | null
  unmapped_headers?: string[]
  mapping_suggestions?: {
    missing_recommended_fields?: string[]
    data_quality_notes?: string[]
    special_mappings_applied?: string[]
  }
  llm_response_metadata?: {
    model?: string
    prompt_used?: string
    input_provided?: string
    output_received?: string
    tokens_consumed?: number
    response_timestamp?: string
  } | null
  processing_metadata?: {
    file_format?: string
    headers_count?: number
    data_rows_count?: number
  } | null
}

export interface AsyncUploadResult {
  success: boolean
  upload_id?: number
  message: string
  error?: string
  storedFile?: StoredFileInfo
}

export class AsyncUploadService {
  
  /**
   * Store uploaded file and data for background processing
   */
  static async storeUploadForProcessing(request: AsyncUploadRequest): Promise<AsyncUploadResult> {
    const client = await pool.connect()
    
    try {
      console.log('[AsyncUploadService] Starting upload processing for file:', request.file.name, 'Size:', request.file.size);
      await client.query('BEGIN')

      // 1. Store the file in filesystem
      console.log('[AsyncUploadService] Step 1: Storing file in filesystem...');
      const storedFile = await fileStorageService.storeFile(
        request.file, 
        UPLOAD_SUBDIRECTORIES.INVESTORS
      )
      console.log('[AsyncUploadService] File stored successfully:', storedFile.filePath);

      // 2. Parse the file to get data and headers
      console.log('[AsyncUploadService] Step 2: Parsing file...');
      const parsedData = await this.parseUploadedFile(request.file)
      console.log('[AsyncUploadService] File parsed successfully. Headers:', parsedData.headers.length, 'Rows:', parsedData.data.length);
      
      // 3. Create upload log record
      console.log('[AsyncUploadService] Step 3: Creating upload log record...');
      const uploadLogId = await this.createUploadLogRecord({
        storedFile,
        parsedData,
        headerMappings: request.headerMappings,
        structuredMappings: request.structuredMappings,
        source: request.source || `Equity Investors Import ${new Date().toISOString().split('T')[0]}`,
        uploadedBy: request.uploadedBy || 'system',
        llmMetadata: request.llmMetadata
      }, client)
      console.log('[AsyncUploadService] Upload log record created with ID:', uploadLogId);

      // 4. Store CSV data in upload_data_log table
      console.log('[AsyncUploadService] Step 4: Storing CSV data in database...');
      await this.storeCSVDataInGenericTable(uploadLogId, parsedData, request.headerMappings, client)
      console.log('[AsyncUploadService] CSV data stored successfully');

      await client.query('COMMIT')
      console.log('[AsyncUploadService] Transaction committed successfully');

      return {
        success: true,
        upload_id: uploadLogId,
        message: `Upload stored successfully. ${parsedData.data.length} rows queued for processing.`,
        storedFile
      }

    } catch (error) {
      await client.query('ROLLBACK')
      console.error('[AsyncUploadService] Error storing upload for processing:', error)
      
      return {
        success: false,
        message: 'Failed to store upload for processing',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    } finally {
      client.release()
      console.log('[AsyncUploadService] Database connection released');
    }
  }

  /**
   * Get upload status and progress with structured mapping data
   */
  static async getUploadStatus(uploadId: number): Promise<UploadLogRecord | null> {
    const client = await pool.connect()
    
    try {
      const result = await client.query(`
        SELECT 
          upload_id,
          file_name,
          file_path,
          status,
          total_rows,
          processed_until,
          CASE 
            WHEN total_rows > 0 THEN ROUND((processed_until::DECIMAL / total_rows) * 100, 2)
            ELSE 0
          END as progress_percentage,
          created_at,
          processing_started_at,
          processing_completed_at,
          error_message,
          companies_processed,
          contacts_processed,
          conflicts_detected,
          header_mappings,
          mapped_columns,
          extra_attrs
        FROM upload_logs 
        WHERE upload_id = $1
      `, [uploadId])

      const record = result.rows[0]
      if (!record) return null

      // Parse and enhance the record with structured mapping data
      const extraAttrs = record.extra_attrs || {}

      // Add structured mapping information to the record
      const enhancedRecord = {
        ...record,
        mapping_version: 'v2',
        structured_mappings: record.header_mappings,
        database_fields: typeof record.mapped_columns === 'string' ? 
          JSON.parse(record.mapped_columns) : record.mapped_columns,
        unmapped_headers: extraAttrs.unmapped_headers || [],
        llm_response_metadata: extraAttrs.llm_response || null,
        processing_metadata: extraAttrs.processing_metadata || null
      }

      return enhancedRecord
    } catch (error) {
      console.error('Error getting upload status:', error)
      return null
    } finally {
      client.release()
    }
  }

  /**
   * Get all uploads with their status and structured mapping data
   */
  static async getAllUploads(limit: number = 50, offset: number = 0): Promise<UploadLogRecord[]> {
    const client = await pool.connect()
    
    try {
      const result = await client.query(`
        SELECT 
          upload_id,
          file_name,
          file_path,
          status,
          total_rows,
          processed_until,
          CASE 
            WHEN total_rows > 0 THEN ROUND((processed_until::DECIMAL / total_rows) * 100, 2)
            ELSE 0
          END as progress_percentage,
          created_at,
          processing_started_at,
          processing_completed_at,
          error_message,
          companies_processed,
          contacts_processed,
          conflicts_detected,
          header_mappings,
          mapped_columns,
          extra_attrs
        FROM upload_logs 
        ORDER BY created_at DESC
        LIMIT $1 OFFSET $2
      `, [limit, offset])

      // Enhance each record with structured mapping data
      const enhancedRecords = result.rows.map(record => {
        const extraAttrs = record.extra_attrs || {}

        return {
          ...record,
          structured_mappings: record.header_mappings,
          database_fields: typeof record.mapped_columns === 'string' ? 
            JSON.parse(record.mapped_columns) : record.mapped_columns,
          unmapped_headers: extraAttrs.unmapped_headers || [],
          mapping_suggestions: extraAttrs.mapping_suggestions || {},
          llm_response_metadata: extraAttrs.llm_response || null,
          processing_metadata: extraAttrs.processing_metadata || null
        }
      })

      return enhancedRecords
    } catch (error) {
      console.error('Error getting uploads:', error)
      return []
    } finally {
      client.release()
    }
  }

  /**
   * Get uploads that are ready for processing with structured mapping data
   */
  static async getPendingUploads(): Promise<UploadLogRecord[]> {
    const client = await pool.connect()
    
    try {
      const result = await client.query(`
        SELECT 
          upload_id,
          file_name,
          file_path,
          status,
          total_rows,
          processed_until,
          CASE 
            WHEN total_rows > 0 THEN ROUND((processed_until::DECIMAL / total_rows) * 100, 2)
            ELSE 0
          END as progress_percentage,
          created_at,
          processing_started_at,
          processing_completed_at,
          error_message,
          companies_processed,
          contacts_processed,
          conflicts_detected,
          header_mappings,
          mapped_columns,
          extra_attrs
        FROM upload_logs
        WHERE status = 'pending' AND error_status != 'fatal'
        ORDER BY created_at ASC
      `)

      // Enhance each record with structured mapping data
      const enhancedRecords = result.rows.map(record => {
        const extraAttrs = record.extra_attrs || {}

        return {
          ...record,
          structured_mappings: record.header_mappings,
          database_fields: typeof record.mapped_columns === 'string' ? 
            JSON.parse(record.mapped_columns) : record.mapped_columns,
          unmapped_headers: extraAttrs.unmapped_headers || [],
          mapping_suggestions: extraAttrs.mapping_suggestions || {},
          llm_response_metadata: extraAttrs.llm_response || null,
          processing_metadata: extraAttrs.processing_metadata || null
        }
      })

      return enhancedRecords
    } catch (error) {
      console.error('Error getting pending uploads:', error)
      return []
    } finally {
      client.release()
    }
  }

  /**
   * Mark upload as started processing
   */
  static async markUploadAsProcessing(uploadId: number): Promise<boolean> {
    const client = await pool.connect()
    
    try {
      await client.query(`
        UPDATE upload_logs 
        SET status = 'processing',
            processing_started_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE upload_id = $1
      `, [uploadId])

      return true
    } catch (error) {
      console.error('Error marking upload as processing:', error)
      return false
    } finally {
      client.release()
    }
  }

  /**
   * Update processing progress
   */
  static async updateProgress(
    uploadId: number, 
    processedUntil: number,
    stats?: {
      companiesProcessed?: number
      contactsProcessed?: number
      conflictsDetected?: number
    }
  ): Promise<boolean> {
    const client = await pool.connect()
    
    try {
      const updateFields = ['processed_until = $2', 'updated_at = CURRENT_TIMESTAMP']
      const values = [uploadId, processedUntil]
      let paramIndex = 3

      if (stats?.companiesProcessed !== undefined) {
        updateFields.push(`companies_processed = $${paramIndex}`)
        values.push(stats.companiesProcessed)
        paramIndex++
      }

      if (stats?.contactsProcessed !== undefined) {
        updateFields.push(`contacts_processed = $${paramIndex}`)
        values.push(stats.contactsProcessed)
        paramIndex++
      }

      if (stats?.conflictsDetected !== undefined) {
        updateFields.push(`conflicts_detected = $${paramIndex}`)
        values.push(stats.conflictsDetected)
        paramIndex++
      }

      await client.query(`
        UPDATE upload_logs 
        SET ${updateFields.join(', ')}
        WHERE upload_id = $1
      `, values)

      return true
    } catch (error) {
      console.error('Error updating progress:', error)
      return false
    } finally {
      client.release()
    }
  }

  /**
   * Mark upload as completed
   */
  static async markUploadAsCompleted(uploadId: number): Promise<boolean> {
    const client = await pool.connect()
    
    try {
      await client.query(`
        UPDATE upload_logs 
        SET status = 'completed',
            is_completed = true,
            processing_completed_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE upload_id = $1
      `, [uploadId])

      return true
    } catch (error) {
      console.error('Error marking upload as completed:', error)
      return false
    } finally {
      client.release()
    }
  }

  /**
   * Mark upload as failed
   */
  static async markUploadAsFailed(uploadId: number, errorMessage: string, isFatal: boolean = false): Promise<boolean> {
    const client = await pool.connect()
    
    try {
      // Check current retry count
      const currentResult = await client.query(`
        SELECT retry_count FROM upload_logs WHERE upload_id = $1
      `, [uploadId])
      
      const currentRetryCount = currentResult.rows[0]?.retry_count || 0
      const maxRetries = 3 // Maximum retries allowed
      
      // Determine if this should be marked as fatal based on retry count
      const shouldMarkFatal = isFatal || (currentRetryCount >= maxRetries)
      const newStatus = shouldMarkFatal ? 'failed' : 'pending' // Reset to pending for retry
      
      await client.query(`
        UPDATE upload_logs 
        SET status = $4,
            error_status = $3,
            error_message = $2,
            retry_count = retry_count + 1,
            updated_at = CURRENT_TIMESTAMP,
            last_retry_at = CURRENT_TIMESTAMP
        WHERE upload_id = $1
      `, [uploadId, errorMessage, shouldMarkFatal ? 'fatal' : 'recoverable', newStatus])

      console.log(`Upload ${uploadId} marked as ${newStatus}. Retry count: ${currentRetryCount + 1}/${maxRetries}`)
      return true
    } catch (error) {
      console.error('Error marking upload as failed:', error)
      return false
    } finally {
      client.release()
    }
  }

  /**
   * Reset upload for retry (admin function)
   */
  static async resetUploadForRetry(uploadId: number): Promise<boolean> {
    const client = await pool.connect()
    
    try {
      await client.query(`
        UPDATE upload_logs 
        SET status = 'pending',
            error_status = NULL,
            error_message = NULL,
            processed_until = 0,
            processing_started_at = NULL,
            processing_completed_at = NULL,
            updated_at = CURRENT_TIMESTAMP
        WHERE upload_id = $1
      `, [uploadId])

      // Reset processed status for all data rows
      await client.query(`
        UPDATE upload_data_log 
        SET processed = false,
            error_status = NULL,
            error_message = NULL,
            updated_at = CURRENT_TIMESTAMP
        WHERE upload_log_id = $1
      `, [uploadId])

      return true
    } catch (error) {
      console.error('Error resetting upload for retry:', error)
      return false
    } finally {
      client.release()
    }
  }

  /**
   * Get data for processing from upload_data_log
   */
  static async getDataForProcessing(
    uploadId: number, 
    batchSize: number = 100, 
    startFromRow: number = 0
  ): Promise<any[]> {
    const client = await pool.connect()
    
    try {
      const result = await client.query(`
        SELECT *
        FROM upload_data_log
        WHERE upload_log_id = $1 
          AND processed = false
        ORDER BY row_number ASC
        LIMIT $2
      `, [uploadId, batchSize])

      return result.rows
    } catch (error) {
      console.error('Error getting data for processing:', error)
      return []
    } finally {
      client.release()
    }
  }

  /**
   * Mark data row as processed
   */
  static async markDataRowAsProcessed(dataId: number): Promise<boolean> {
    const client = await pool.connect()
    
    try {
      await client.query(`
        UPDATE upload_data_log 
        SET processed = true,
            updated_at = CURRENT_TIMESTAMP
        WHERE data_id = $1
      `, [dataId])

      return true
    } catch (error) {
      console.error('Error marking data row as processed:', error)
      return false
    } finally {
      client.release()
    }
  }

  // Private helper methods

  /**
   * Extract all database fields that will be populated from mappings
   */
  private static extractDatabaseFields(
    headerMappings: Record<string, string[]>
  ): string[] {
    const allDatabaseFields = new Set<string>()
    
    // Extract from headerMappings
    Object.values(headerMappings).forEach(dbFields => {
      if (Array.isArray(dbFields)) {
        dbFields.forEach(field => allDatabaseFields.add(field))
      }
    })
    
    return Array.from(allDatabaseFields)
  }

    private static async parseUploadedFile(file: File): Promise<{ headers: string[], data: any[] }> {
    try {
      console.log('[AsyncUploadService] Starting file parsing for:', file.name, 'Type:', file.type, 'Size:', file.size);
      
      // Convert File to ArrayBuffer for XLSX processing
      console.log('[AsyncUploadService] Converting file to ArrayBuffer...');
      const arrayBuffer = await file.arrayBuffer()
      console.log('[AsyncUploadService] ArrayBuffer created, size:', arrayBuffer.byteLength);
      
      // Use XLSX library to handle both CSV and Excel files
      console.log('[AsyncUploadService] Reading workbook with XLSX...');
      const workbook = XLSX.read(arrayBuffer, { type: 'array' })
      console.log('[AsyncUploadService] Workbook read successfully, sheets:', workbook.SheetNames);
      
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      
      if (!worksheet) {
        throw new Error('No worksheet found in file')
      }

      // Convert to JSON with header row
      console.log('[AsyncUploadService] Converting worksheet to JSON...');
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      console.log('[AsyncUploadService] JSON conversion complete, total rows:', jsonData.length);
      
      if (jsonData.length === 0) {
        throw new Error('No data found in file')
      }

      // First row contains headers
      console.log('[AsyncUploadService] Processing headers...');
      const headers = (jsonData[0] as any[])
        .map(h => h ? String(h).trim() : '')
        .filter(h => h.length > 0)
      
      if (headers.length === 0) {
        throw new Error('No headers found in file')
      }
      console.log('[AsyncUploadService] Headers processed:', headers.length, 'headers found');

      // Parse data rows and add row numbers
      console.log('[AsyncUploadService] Processing data rows...');
      const data: any[] = []
      for (let i = 1; i < jsonData.length; i++) {
        if (i % 500 === 0) {
          console.log('[AsyncUploadService] Processing row:', i, 'of', jsonData.length);
        }
        
        const row = jsonData[i] as any[]
        const rowData: any = { _rowNumber: i }
        let hasData = false
        
        headers.forEach((header, index) => {
          const cellValue = row[index]
          const cleanValue = cellValue ? String(cellValue).trim() : ''
          rowData[header] = cleanValue
          if (cleanValue) hasData = true
        })
        
        if (hasData) {
          data.push(rowData)
        }
      }

      console.log('[AsyncUploadService] File parsing completed successfully. Final data rows:', data.length);
      return { headers, data }
    } catch (error) {
      console.error('[AsyncUploadService] File parsing failed:', error);
      throw new Error(`File parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private static async createUploadLogRecord(
    params: {
      storedFile: StoredFileInfo
      parsedData: { headers: string[], data: any[] }
      headerMappings: Record<string, string[]>
      structuredMappings: {
        company_mappings: Record<string, string[]>
        contact_mappings: Record<string, string[]>
        investment_criteria_mappings: Record<string, string[]>
        unmapped_headers: string[]
        database_fields: {
          companies: string[]
          contacts: string[]
          investment_criteria: string[]
        }
      }
      source: string
      uploadedBy: string
      llmMetadata?: {
        llmUsed: string
        prompt: string
        input: string
        output: string
        tokensUsed: number
      }
    },
    client: any
  ): Promise<number> {
    const { storedFile, parsedData, headerMappings, structuredMappings, source, uploadedBy, llmMetadata } = params
    
    // Store structured mappings in header_mappings
    const finalHeaderMappings = {
      companies: structuredMappings.company_mappings,
      contacts: structuredMappings.contact_mappings,
      investment_criteria: structuredMappings.investment_criteria_mappings
    }
    
    // Store database_fields structure in mapped_columns
    let finalMappedColumns: any = {};
    if (structuredMappings && structuredMappings.database_fields) {
      const { companies, contacts, investment_criteria } = structuredMappings.database_fields;
      finalMappedColumns = { companies, contacts, investment_criteria };
    }

    // Store additional metadata in extra_attrs (denormalized)
    const extraAttrs = {
      mapping_version: 'v2',
      unmapped_headers: structuredMappings.unmapped_headers,
      llm_response: llmMetadata ? {
        model: llmMetadata.llmUsed,
        prompt_used: llmMetadata.prompt,
        input_provided: llmMetadata.input,
        output_received: llmMetadata.output,
        tokens_consumed: llmMetadata.tokensUsed,
        response_timestamp: new Date().toISOString()
      } : null,
      processing_metadata: {
        file_format: storedFile.fileType,
        headers_count: parsedData.headers.length,
        data_rows_count: parsedData.data.length
      }
    }

    const result = await client.query(`
      INSERT INTO upload_logs (
        file_name, file_path, file_size, file_type,
        original_headers, total_rows, header_mappings, mapped_columns,
        source, uploaded_by, llm_used, llm_prompt, llm_input, llm_output, tokens_used,
        extra_attrs
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      RETURNING upload_id
    `, [
      storedFile.fileName,
      storedFile.filePath,
      storedFile.size,
      storedFile.fileType,
      parsedData.headers,
      parsedData.data.length,
      finalHeaderMappings, // pass as object
      finalMappedColumns,  // pass as object
      source,
      uploadedBy,
      llmMetadata?.llmUsed || null,
      llmMetadata?.prompt || null,
      llmMetadata?.input || null,
      llmMetadata?.output || null,
      llmMetadata?.tokensUsed || 0,
      JSON.stringify(extraAttrs)
    ])

    return result.rows[0].upload_id
  }

  private static async storeCSVDataInGenericTable(
    uploadLogId: number,
    parsedData: { headers: string[], data: any[] },
    headerMappings: Record<string, string[]>,
    client: any
  ): Promise<void> {
    console.log('[AsyncUploadService] Starting CSV data storage for upload:', uploadLogId);
    console.log('[AsyncUploadService] Data to store:', parsedData.data.length, 'rows');
    
    // Create header mapping for this upload (key1 -> actual field name)
    const headersMap: Record<string, string> = {}
    parsedData.headers.forEach((header, index) => {
      headersMap[`key${index + 1}`] = header
    })
    console.log('[AsyncUploadService] Headers mapping created for', Object.keys(headersMap).length, 'columns');

    // Prepare batch insert data
    const batchSize = 100; // Insert 100 rows at a time
    const totalBatches = Math.ceil(parsedData.data.length / batchSize);
    console.log('[AsyncUploadService] Preparing to insert in', totalBatches, 'batches of', batchSize, 'rows');

    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const startIdx = batchIndex * batchSize;
      const endIdx = Math.min(startIdx + batchSize, parsedData.data.length);
      const batchRows = parsedData.data.slice(startIdx, endIdx);
      
      console.log('[AsyncUploadService] Processing batch', batchIndex + 1, 'of', totalBatches, '(rows', startIdx + 1, 'to', endIdx, ')');

      // Build batch insert query
      const values: any[] = [];
      const placeholders: string[] = [];
      let paramCounter = 1;

      for (let i = 0; i < batchRows.length; i++) {
        const row = batchRows[i];
        const rowIdx = startIdx + i;
        
        // Map data to generic key1, key2, etc. columns
        const keyValues: (string | null)[] = []
        for (let j = 1; j <= 50; j++) {
          const headerKey = `key${j}`
          const actualHeader = headersMap[headerKey]
          keyValues.push(actualHeader && row[actualHeader] ? String(row[actualHeader]).trim() : null)
        }

        // Add row parameters
        const rowPlaceholders: string[] = [];
        values.push(uploadLogId); // upload_log_id
        rowPlaceholders.push(`$${paramCounter++}`);
        
        values.push(rowIdx + 1); // row_number
        rowPlaceholders.push(`$${paramCounter++}`);
        
        values.push(JSON.stringify(headersMap)); // headers_map
        rowPlaceholders.push(`$${paramCounter++}`);
        
        // Add key1 through key50
        for (const keyValue of keyValues) {
          values.push(keyValue);
          rowPlaceholders.push(`$${paramCounter++}`);
        }
        
        placeholders.push(`(${rowPlaceholders.join(', ')})`);
      }

      // Execute batch insert
      const insertQuery = `
        INSERT INTO upload_data_log (
          upload_log_id, row_number, headers_map,
          key1, key2, key3, key4, key5, key6, key7, key8, key9, key10,
          key11, key12, key13, key14, key15, key16, key17, key18, key19, key20,
          key21, key22, key23, key24, key25, key26, key27, key28, key29, key30,
          key31, key32, key33, key34, key35, key36, key37, key38, key39, key40,
          key41, key42, key43, key44, key45, key46, key47, key48, key49, key50
        ) VALUES ${placeholders.join(', ')}
      `;

      await client.query(insertQuery, values);
      console.log('[AsyncUploadService] Batch', batchIndex + 1, 'inserted successfully');
    }
    
    console.log('[AsyncUploadService] CSV data storage completed successfully for', parsedData.data.length, 'rows');
  }
}

export default AsyncUploadService 