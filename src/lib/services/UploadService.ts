import { ConflictUploadResult, ConflictPreviewRow } from "@/types/conflict";
import { InvestmentCriteriaRecord } from "../processors/InvestmentCriteriaProcessor";

export interface UploadOptions {
  overwriteConflicts?: boolean;
  skipInvestmentCriteria?: boolean;
  skipCompanyAddress?: boolean;
  skipContactData?: boolean;
}

export interface ConflictCheckResult {
  success: boolean;
  data?: ConflictPreviewRow[];
  error?: string;
}

export interface UploadProcessingResult {
  success: boolean;
  uploaded: number;
  skipped: number;
  errors: string[];
}

export interface HeaderMappingData {
  success?: boolean;
  database_fields?: {
    companies: string[];
    contacts: string[];
    investment_criteria_central: string[];
    investment_criteria_debt: string[];
    investment_criteria_equity: string[];
  };
  headers?: string[];
  company_mappings?: Record<string, string[]>;
  contact_mappings?: Record<string, string[]>;
  investment_criteria_central_mappings?: Record<string, string[]>;
  investment_criteria_debt_mappings?: Record<string, string[]>;
  investment_criteria_equity_mappings?: Record<string, string[]>;
  unmapped_headers?: string[];
  suggestions?: {
    missing_recommended_fields?: string[];
    data_quality_notes?: string[];
    special_mappings_applied?: string[];
  };
  // LLM metadata from the mapping process
  llm_metadata?: {
    model?: string;
    prompt_used?: string;
    input_provided?: string;
    output_received?: string;
    tokens_consumed?: number;
    response_timestamp?: string;
    processing_time_ms?: number;
  };
}

export class UploadService {
  /**
   * Upload CSV data for processing and return upload ID
   */
  static async uploadCSVData(params: {
    data: any[];
    headers: string[];
    headerMappings: Record<string, string>;
    filename: string;
  }): Promise<{ success: boolean; uploadId?: number; error?: string }> {
    try {
      // Create a File object from the data
      const csvContent = [
        params.headers.join(','),
        ...params.data.map(row => 
          params.headers.map(header => {
            const value = row[header] || '';
            // Escape commas and quotes in CSV format
            return typeof value === 'string' && (value.includes(',') || value.includes('"')) 
              ? `"${value.replace(/"/g, '""')}"` 
              : value;
          }).join(',')
        )
      ].join('\n');
      
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const file = new File([blob], params.filename, { type: 'text/csv' });

      // Use the existing AsyncUploadService via API
      const formData = new FormData();
      formData.append('file', file);
      formData.append('headerMappings', JSON.stringify(params.headerMappings));
      formData.append('source', `Upload ${new Date().toISOString().split('T')[0]}`);

      const response = await fetch('/api/investors/upload-async', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        return { success: true, uploadId: result.upload_id };
      } else {
        return { success: false, error: result.error || 'Upload failed' };
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Check for conflicts in the data
   */
  static async checkConflicts(
    processedData: Record<string, any>[],
    headerMappings: Record<string, string>
  ): Promise<ConflictCheckResult> {
    try {
      const response = await fetch("/api/investors/check-conflicts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          data: processedData,
          headerMappings,
          source: `Data Upload ${new Date().toISOString().split('T')[0]}`,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        return {
          success: true,
          data: result.preview_data || []
        };
      } else {
        return {
          success: false,
          error: result.error || "Unknown error during conflict check"
        };
      }
    } catch (error) {
      return {
        success: false,
        error: `Conflict check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Upload data with conflict resolution
   */
  static async uploadWithConflicts(
    selectedData: Record<string, any>[],
    headerMappings: Record<string, string>,
    investmentCriteriaRecords: InvestmentCriteriaRecord[],
    options: UploadOptions = {}
  ): Promise<ConflictUploadResult> {
    try {
      const response = await fetch("/api/investors/upload-with-conflicts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          data: selectedData,
          headerMappings,
          source: `Data Upload ${new Date().toISOString().split('T')[0]}`,
          options,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      return {
        success: false,
        error: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        conflicts: { companies: [], contacts: [] },
        logs: [],
        stats: {
          companies: { total: 0, added: 0, updated_with_conflicts: 0, skipped: 0, error: 0 },
          contacts: { total: 0, added: 0, updated_with_conflicts: 0, skipped: 0, error: 0 }
        }
      };
    }
  }

  /**
   * Fetch AI-suggested header mappings
   */
  static async fetchHeaderMappings(csvHeaders: string[]): Promise<HeaderMappingData> {
    try {
      const response = await fetch('/api/investors/map-headers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ headers: csvHeaders }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const apiResponse = await response.json();
      
      // Return the API response directly - it already contains all the structured data we need
      return apiResponse;
      
    } catch (error) {
      console.error("Error fetching header mappings:", error);
      throw error;
    }
  }

  /**
   * Fetch current conflict count for investor uploads
   */
  static async fetchConflictCount(): Promise<number> {
    try {
      // Use investor-specific endpoint to get conflict count
      const response = await fetch("/api/investors/check-conflicts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          data: [], // Empty data to just get current conflict count
          headerMappings: {},
          source: "Conflict Count Check",
        }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        // Count total conflicts from the response
        const companyConflicts = result.data?.company_conflicts?.length || 0;
        const contactConflicts = result.data?.contact_conflicts?.length || 0;
        return companyConflicts + contactConflicts;
      } else {
        throw new Error(result.error || "Failed to fetch conflict count");
      }
    } catch (error) {
      console.error("Error fetching investor conflict count:", error);
      return 0;
    }
  }



} 