import <PERSON> from "papaparse";
import * as XLSX from "xlsx";

export interface ParsedData {
  headers: string[];
  data: Record<string, any>[];
  sampleRow: Record<string, any> | null;
}

export interface ParsingResult {
  success: boolean;
  data?: ParsedData;
  error?: string;
}

export class CSVParserService {
  /**
   * Parse CSV or XLSX file and return structured data
   */
  static async parseFile(file: File): Promise<ParsingResult> {
    try {
      const content = await file.arrayBuffer();
      
      if (file.name.endsWith(".xlsx")) {
        return await this.parseExcelFile(content);
      } else {
        return await this.parseCSVFile(file);
      }
    } catch (error) {
      return {
        success: false,
        error: `File parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Parse Excel file using xlsx (SheetJS)
   */
  private static async parseExcelFile(content: ArrayBuffer): Promise<ParsingResult> {
    try {
      const workbook = XLSX.read(content);
      const sheetName = workbook.SheetNames[0];
      
      if (!sheetName) {
        return {
          success: false,
          error: "No worksheet found in Excel file"
        };
      }

      const worksheet = workbook.Sheets[sheetName];
      
      // Convert sheet to array of arrays
      const rawData: any[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      if (rawData.length === 0) {
        return {
          success: false,
          error: "No data found in Excel file"
        };
      }

      // Get headers from first row and clean them
      const headerRow = rawData[0];
      const headers: string[] = headerRow
        .map((cell: any) => {
          if (cell === null || cell === undefined) return '';
          return String(cell).trim();
        })
        .filter((header: string) => header && header.length > 0);
      
      if (headers.length === 0) {
        return {
          success: false,
          error: "No valid headers found in Excel file"
        };
      }

      // Parse data rows (skip header row)
      const data: Record<string, any>[] = [];
      const maxRows = Math.min(rawData.length, 1001); // Limit to 1000 data rows
      
      for (let i = 1; i < maxRows; i++) {
        const row = rawData[i];
        if (!row) continue;
        
        const rowData: Record<string, any> = {};
        let hasData = false;
        
        headers.forEach((header: string, index: number) => {
          const cellValue = row[index];
          const cleanValue = cellValue === null || cellValue === undefined ? '' : String(cellValue).trim();
          rowData[header] = cleanValue;
          if (cleanValue) hasData = true;
        });
        
        if (hasData) {
          data.push(rowData);
        }
      }

      return {
        success: true,
        data: {
          headers,
          data,
          sampleRow: data.length > 0 ? data[0] : null
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `Excel parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Parse CSV file using PapaParse
   */
  private static async parseCSVFile(file: File): Promise<ParsingResult> {
    return new Promise((resolve) => {
      Papa.parse<Record<string, any>>(file, {
        header: true,
        skipEmptyLines: true,
        complete: (results: any) => {
          try {
            if (results.errors.length > 0) {
              resolve({
                success: false,
                error: `CSV parsing errors: ${results.errors.map((e: any) => e.message).join(', ')}`
              });
              return;
            }

            const data = results.data as Record<string, any>[];
            const headers = results.meta.fields || [];
            
            if (headers.length === 0) {
              resolve({
                success: false,
                error: "No headers found in CSV file"
              });
              return;
            }

            // Clean headers and data
            const cleanHeaders = headers.map((h: string) => h.trim()).filter((h: string) => h.length > 0);
            const cleanData = data.map((row: Record<string, any>) => {
              const cleanRow: Record<string, any> = {};
              cleanHeaders.forEach((header: string) => {
                cleanRow[header] = (row[header] || '').toString().trim();
              });
              return cleanRow;
            }).filter((row: Record<string, any>) => Object.values(row).some((val: any) => val && val.length > 0));

            resolve({
              success: true,
              data: {
                headers: cleanHeaders,
                data: cleanData,
                sampleRow: cleanData.length > 0 ? cleanData[0] : null
              }
            });
          } catch (error) {
            resolve({
              success: false,
              error: `CSV data processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
            });
          }
        },
        error: (error: Error) => {
          resolve({
            success: false,
            error: `CSV parsing failed: ${error.message}`
          });
        }
      });
    });
  }

  /**
   * Validate file before parsing
   */
  static validateFile(file: File): { valid: boolean; error?: string } {
    // Check file type (no size limit enforced)
    const isCSV = file.type === "text/csv" || file.name.endsWith(".csv");
    const isExcel = file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" || 
                   file.name.endsWith(".xlsx");

    if (!isCSV && !isExcel) {
      return {
        valid: false,
        error: "Only CSV and XLSX files are supported"
      };
    }

    return { valid: true };
  }

  /**
   * Get file info for display
   */
  static getFileInfo(file: File) {
    return {
      name: file.name,
      size: file.size,
      sizeFormatted: `${(file.size / 1024).toFixed(1)} KB`,
      type: file.name.endsWith('.xlsx') ? 'Excel' : 'CSV'
    };
  }
} 