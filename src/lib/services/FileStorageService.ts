import fs from 'fs/promises'
import path from 'path'
import { randomUUID } from 'crypto'

export interface StoredFileInfo {
  filePath: string
  fileName: string
  originalName: string
  size: number
  fileType: string
  storedAt: Date
}

export interface FileStorageConfig {
  baseDir: string
  maxFileSize: number // in bytes
  allowedExtensions: string[]
  preserveOriginalName: boolean
}

export class FileStorageService {
  private config: FileStorageConfig

  constructor(config?: Partial<FileStorageConfig>) {
    this.config = {
      baseDir: config?.baseDir || path.join(process.cwd(), 'data', 'uploads'),
      maxFileSize: config?.maxFileSize || 1024 * 1024 * 1024, // 1GB default (effectively no limit)
      allowedExtensions: config?.allowedExtensions || ['.csv', '.xlsx', '.xls'],
      preserveOriginalName: config?.preserveOriginalName || false
    }
  }

  /**
   * Store a file from File object (browser upload)
   */
  async storeFile(file: File, subDirectory?: string): Promise<StoredFileInfo> {
    // Validate file
    this.validateFile(file)

    // Create directory structure
    const uploadDir = subDirectory 
      ? path.join(this.config.baseDir, subDirectory)
      : path.join(this.config.baseDir, this.getDateBasedPath())

    await this.ensureDirectoryExists(uploadDir)

    // Generate unique filename
    const fileName = this.generateFileName(file.name)
    const filePath = path.join(uploadDir, fileName)

    // Convert File to Buffer and save
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)
    
    await fs.writeFile(filePath, buffer)

    return {
      filePath: filePath,
      fileName: fileName,
      originalName: file.name,
      size: file.size,
      fileType: this.getFileExtension(file.name),
      storedAt: new Date()
    }
  }

  /**
   * Store a file from buffer data
   */
  async storeFileFromBuffer(
    buffer: Buffer, 
    originalName: string, 
    subDirectory?: string
  ): Promise<StoredFileInfo> {
    // Validate buffer size
    if (buffer.length > this.config.maxFileSize) {
      throw new Error(`File size ${buffer.length} exceeds maximum allowed size of ${this.config.maxFileSize}`)
    }

    // Validate file extension
    const extension = this.getFileExtension(originalName)
    if (!this.config.allowedExtensions.includes(extension)) {
      throw new Error(`File type ${extension} is not allowed`)
    }

    // Create directory structure
    const uploadDir = subDirectory 
      ? path.join(this.config.baseDir, subDirectory)
      : path.join(this.config.baseDir, this.getDateBasedPath())

    await this.ensureDirectoryExists(uploadDir)

    // Generate unique filename
    const fileName = this.generateFileName(originalName)
    const filePath = path.join(uploadDir, fileName)

    // Save file
    await fs.writeFile(filePath, buffer)

    return {
      filePath: filePath,
      fileName: fileName,
      originalName: originalName,
      size: buffer.length,
      fileType: extension,
      storedAt: new Date()
    }
  }

  /**
   * Read a stored file
   */
  async readFile(filePath: string): Promise<Buffer> {
    try {
      return await fs.readFile(filePath)
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error}`)
    }
  }

  /**
   * Check if file exists
   */
  async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath)
      return true
    } catch {
      return false
    }
  }

  /**
   * Delete a stored file
   */
  async deleteFile(filePath: string): Promise<boolean> {
    try {
      await fs.unlink(filePath)
      return true
    } catch (error) {
      console.error(`Failed to delete file ${filePath}:`, error)
      return false
    }
  }

  /**
   * Get file stats
   */
  async getFileStats(filePath: string) {
    try {
      return await fs.stat(filePath)
    } catch (error) {
      throw new Error(`Failed to get file stats for ${filePath}: ${error}`)
    }
  }

  /**
   * Clean up old files (older than specified days)
   */
  async cleanupOldFiles(daysOld: number = 30): Promise<number> {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysOld)

    let deletedCount = 0

    try {
      const cleanupRecursive = async (dir: string) => {
        const entries = await fs.readdir(dir, { withFileTypes: true })
        
        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name)
          
          if (entry.isDirectory()) {
            await cleanupRecursive(fullPath)
            
            // Try to remove directory if empty
            try {
              const dirContents = await fs.readdir(fullPath)
              if (dirContents.length === 0) {
                await fs.rmdir(fullPath)
              }
            } catch {
              // Directory not empty or other error, ignore
            }
          } else {
            const stats = await fs.stat(fullPath)
            if (stats.mtime < cutoffDate) {
              await fs.unlink(fullPath)
              deletedCount++
            }
          }
        }
      }

      await cleanupRecursive(this.config.baseDir)
    } catch (error) {
      console.error('Error during cleanup:', error)
    }

    return deletedCount
  }

  /**
   * Get storage usage statistics
   */
  async getStorageStats() {
    const stats = {
      totalFiles: 0,
      totalSize: 0,
      filesByType: {} as Record<string, number>,
      sizeByType: {} as Record<string, number>
    }

    try {
      const calculateStats = async (dir: string) => {
        const entries = await fs.readdir(dir, { withFileTypes: true })
        
        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name)
          
          if (entry.isDirectory()) {
            await calculateStats(fullPath)
          } else {
            const fileStat = await fs.stat(fullPath)
            const extension = this.getFileExtension(entry.name)
            
            stats.totalFiles++
            stats.totalSize += fileStat.size
            
            stats.filesByType[extension] = (stats.filesByType[extension] || 0) + 1
            stats.sizeByType[extension] = (stats.sizeByType[extension] || 0) + fileStat.size
          }
        }
      }

      if (await this.directoryExists(this.config.baseDir)) {
        await calculateStats(this.config.baseDir)
      }
    } catch (error) {
      console.error('Error calculating storage stats:', error)
    }

    return stats
  }

  // Private helper methods

  private validateFile(file: File): void {
    if (file.size > this.config.maxFileSize) {
      throw new Error(`File size ${file.size} exceeds maximum allowed size of ${this.config.maxFileSize}`)
    }

    const extension = this.getFileExtension(file.name)
    if (!this.config.allowedExtensions.includes(extension)) {
      throw new Error(`File type ${extension} is not allowed. Allowed types: ${this.config.allowedExtensions.join(', ')}`)
    }
  }

  private generateFileName(originalName: string): string {
    if (this.config.preserveOriginalName) {
      // Add timestamp prefix to avoid conflicts
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const extension = this.getFileExtension(originalName)
      const baseName = path.basename(originalName, extension)
      return `${timestamp}_${baseName}${extension}`
    } else {
      // Generate UUID-based filename
      const extension = this.getFileExtension(originalName)
      return `${randomUUID()}${extension}`
    }
  }

  private getFileExtension(filename: string): string {
    return path.extname(filename).toLowerCase()
  }

  private getDateBasedPath(): string {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    return path.join(year.toString(), month, day)
  }

  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.mkdir(dirPath, { recursive: true })
    } catch (error) {
      throw new Error(`Failed to create directory ${dirPath}: ${error}`)
    }
  }

  private async directoryExists(dirPath: string): Promise<boolean> {
    try {
      const stats = await fs.stat(dirPath)
      return stats.isDirectory()
    } catch {
      return false
    }
  }
}

// Export a default instance with standard configuration
export const fileStorageService = new FileStorageService({
  baseDir: path.join(process.cwd(), 'data', 'uploads'),
  maxFileSize: 1024 * 1024 * 1024, // 1GB (effectively no limit)
  allowedExtensions: ['.csv', '.xlsx', '.xls'],
  preserveOriginalName: true
})

// Export upload subdirectories constants
export const UPLOAD_SUBDIRECTORIES = {
  INVESTORS: 'investors',
  DEALS: 'deals',
  COMPANIES: 'companies',
  TEMP: 'temp'
} as const 