import { pool } from '../db'

export interface StandardizedLocation {
  raw_location: string
  standardized_city?: string
  standardized_state?: string
  standardized_region?: string
  standardized_country: string
  confidence: number
}

export interface LocationExtractionResult {
  locations: StandardizedLocation[]
  primary_location?: StandardizedLocation
}

/**
 * Service for standardizing location data using mapping tables
 * Uses city_mapping, state_mapping, and geographic_region_mapping tables
 */
export class LocationStandardizationService {
  private cityMappingCache: Map<string, string> = new Map()
  private stateMappingCache: Map<string, string> = new Map()
  private regionMappingCache: Map<string, { base_region: string; region_focus: string }> = new Map()
  private cacheLoaded = false

  /**
   * Load mapping data into cache for performance
   */
  private async loadMappingCache(): Promise<void> {
    if (this.cacheLoaded) return

    try {
      const client = await pool.connect()
      
      try {
        // Load city mappings
        const cityResults = await client.query('SELECT raw_value, standardized_value FROM city_mapping')
        for (const row of cityResults.rows) {
          this.cityMappingCache.set(row.raw_value.toLowerCase().trim(), row.standardized_value)
        }

        // Load state mappings
        const stateResults = await client.query('SELECT raw_value, standardized_value FROM state_mapping')
        for (const row of stateResults.rows) {
          this.stateMappingCache.set(row.raw_value.toLowerCase().trim(), row.standardized_value)
        }

        // Load region mappings
        const regionResults = await client.query('SELECT raw_value, base_region, region_focus FROM geographic_region_mapping')
        for (const row of regionResults.rows) {
          this.regionMappingCache.set(row.raw_value.toLowerCase().trim(), {
            base_region: row.base_region,
            region_focus: row.region_focus
          })
        }

        this.cacheLoaded = true
        console.log(`Loaded location mappings: ${this.cityMappingCache.size} cities, ${this.stateMappingCache.size} states, ${this.regionMappingCache.size} regions`)
      } finally {
        client.release()
      }
    } catch (error) {
      console.error('Error loading location mapping cache:', error)
      throw error
    }
  }

  /**
   * Extract and standardize locations from news article text
   */
  async extractAndStandardizeLocations(articleText: string, articleTitle?: string): Promise<LocationExtractionResult> {
    await this.loadMappingCache()

    // Combine title and text for location extraction
    const fullText = `${articleTitle || ''} ${articleText}`.toLowerCase()
    
    // Extract potential locations using various patterns
    const extractedLocations = this.extractLocationCandidates(fullText)
    
    // Standardize each location
    const standardizedLocations: StandardizedLocation[] = []
    
    for (const rawLocation of extractedLocations) {
      const standardized = await this.standardizeLocation(rawLocation)
      if (standardized.confidence > 0.3) { // Only include if we have decent confidence
        standardizedLocations.push(standardized)
      }
    }

    // Remove duplicates and find primary location
    const uniqueLocations = this.removeDuplicateLocations(standardizedLocations)
    const primaryLocation = this.findPrimaryLocation(uniqueLocations, fullText)

    return {
      locations: uniqueLocations,
      primary_location: primaryLocation
    }
  }

  /**
   * Standardize a single location string
   */
  async standardizeLocation(rawLocation: string): Promise<StandardizedLocation> {
    await this.loadMappingCache()

    const normalized = rawLocation.toLowerCase().trim()
    let confidence = 0.5
    
    const result: StandardizedLocation = {
      raw_location: rawLocation,
      standardized_country: 'United States',
      confidence: 0
    }

    // Try to extract city, state, and region information
    const locationParts = this.parseLocationString(normalized)
    
    // Standardize city
    if (locationParts.city) {
      const standardizedCity = this.cityMappingCache.get(locationParts.city)
      if (standardizedCity) {
        result.standardized_city = standardizedCity
        confidence += 0.3
      } else if (this.isLikelyCity(locationParts.city)) {
        result.standardized_city = this.titleCase(locationParts.city)
        confidence += 0.2
      }
    }

    // Standardize state
    if (locationParts.state) {
      const standardizedState = this.stateMappingCache.get(locationParts.state)
      if (standardizedState) {
        result.standardized_state = standardizedState
        confidence += 0.3
      } else if (this.isLikelyState(locationParts.state)) {
        result.standardized_state = this.titleCase(locationParts.state)
        confidence += 0.2
      }
    }

    // Standardize region
    if (locationParts.region) {
      const regionMapping = this.regionMappingCache.get(locationParts.region)
      if (regionMapping) {
        result.standardized_region = regionMapping.base_region
        confidence += 0.2
      }
    }

    // If we have state but no region, try to infer region
    if (result.standardized_state && !result.standardized_region) {
      result.standardized_region = this.inferRegionFromState(result.standardized_state)
      if (result.standardized_region) {
        confidence += 0.1
      }
    }

    result.confidence = Math.min(confidence, 1.0)
    return result
  }

  /**
   * Extract location candidates from text using patterns
   */
  private extractLocationCandidates(text: string): string[] {
    const candidates = new Set<string>()

    // Pattern 1: City, State format
    const cityStatePattern = /\b([a-z\s]+),\s*([a-z]{2,})\b/gi
    let match
    while ((match = cityStatePattern.exec(text)) !== null) {
      candidates.add(match[0].trim())
      candidates.add(match[1].trim()) // City
      candidates.add(match[2].trim()) // State
    }

    // Pattern 2: State names
    const statePattern = /\b(alabama|alaska|arizona|arkansas|california|colorado|connecticut|delaware|florida|georgia|hawaii|idaho|illinois|indiana|iowa|kansas|kentucky|louisiana|maine|maryland|massachusetts|michigan|minnesota|mississippi|missouri|montana|nebraska|nevada|new\s+hampshire|new\s+jersey|new\s+mexico|new\s+york|north\s+carolina|north\s+dakota|ohio|oklahoma|oregon|pennsylvania|rhode\s+island|south\s+carolina|south\s+dakota|tennessee|texas|utah|vermont|virginia|washington|west\s+virginia|wisconsin|wyoming)\b/gi
    while ((match = statePattern.exec(text)) !== null) {
      candidates.add(match[0].trim())
    }

    // Pattern 3: State abbreviations
    const stateAbbrPattern = /\b(AL|AK|AZ|AR|CA|CO|CT|DE|FL|GA|HI|ID|IL|IN|IA|KS|KY|LA|ME|MD|MA|MI|MN|MS|MO|MT|NE|NV|NH|NJ|NM|NY|NC|ND|OH|OK|OR|PA|RI|SC|SD|TN|TX|UT|VT|VA|WA|WV|WI|WY)\b/g
    while ((match = stateAbbrPattern.exec(text)) !== null) {
      candidates.add(match[0].trim())
    }

    // Pattern 4: Major cities (common ones mentioned in real estate news)
    const majorCitiesPattern = /\b(new\s+york|los\s+angeles|chicago|houston|phoenix|philadelphia|san\s+antonio|san\s+diego|dallas|san\s+jose|austin|jacksonville|fort\s+worth|columbus|charlotte|san\s+francisco|indianapolis|seattle|denver|washington|boston|el\s+paso|detroit|nashville|portland|memphis|oklahoma\s+city|las\s+vegas|louisville|baltimore|milwaukee|albuquerque|tucson|fresno|sacramento|mesa|kansas\s+city|atlanta|long\s+beach|colorado\s+springs|raleigh|miami|virginia\s+beach|omaha|oakland|minneapolis|tulsa|arlington|tampa|new\s+orleans|wichita|cleveland|bakersfield|aurora|anaheim|honolulu|santa\s+ana|corpus\s+christi|riverside|lexington|stockton|toledo|st\s+paul|newark|greensboro|plano|henderson|lincoln|buffalo|jersey\s+city|chula\s+vista|fort\s+wayne|orlando|st\s+petersburg|chandler|laredo|norfolk|durham|madison|lubbock|irvine|winston-salem|glendale|garland|hialeah|reno|chesapeake|gilbert|baton\s+rouge|irving|scottsdale|north\s+las\s+vegas|fremont|boise|richmond|san\s+bernardino|birmingham|spokane|rochester|des\s+moines|modesto|fayetteville|tacoma|oxnard|fontana|columbus|montgomery|moreno\s+valley|shreveport|aurora|yonkers|akron|huntington\s+beach|little\s+rock|augusta|amarillo|glendale|mobile|grand\s+rapids|salt\s+lake\s+city|tallahassee|huntsville|grand\s+prairie|knoxville|worcester|newport\s+news|brownsville|overland\s+park|santa\s+clarita|providence|garden\s+grove|chattanooga|oceanside|jackson|fort\s+lauderdale|santa\s+rosa|rancho\s+cucamonga|port\s+st\s+lucie|tempe|ontario|vancouver|cape\s+coral|sioux\s+falls|springfield|peoria|pembroke\s+pines|elk\s+grove|salem|lancaster|corona|eugene|palmdale|salinas|springfield|pasadena|fort\s+collins|hayward|pomona|cary|rockford|alexandria|escondido|mckinney|kansas\s+city|joliet|sunnyvale)\b/gi
    while ((match = majorCitiesPattern.exec(text)) !== null) {
      candidates.add(match[0].trim())
    }

    return Array.from(candidates).filter(candidate => candidate.length > 2)
  }

  /**
   * Parse a location string into components
   */
  private parseLocationString(location: string): { city?: string; state?: string; region?: string } {
    const result: { city?: string; state?: string; region?: string } = {}

    // Handle "City, State" format
    if (location.includes(',')) {
      const parts = location.split(',').map(p => p.trim())
      if (parts.length === 2) {
        result.city = parts[0]
        result.state = parts[1]
      }
    }
    // Handle state-only or region-only
    else {
      // Check if it's a state
      if (this.stateMappingCache.has(location) || this.isLikelyState(location)) {
        result.state = location
      }
      // Check if it's a region
      else if (this.regionMappingCache.has(location)) {
        result.region = location
      }
      // Otherwise assume it's a city
      else {
        result.city = location
      }
    }

    return result
  }

  /**
   * Check if a string is likely a city name
   */
  private isLikelyCity(text: string): boolean {
    // Basic heuristics for city detection
    return text.length >= 3 && 
           text.length <= 50 && 
           /^[a-z\s-']+$/i.test(text) &&
           !this.isCommonWord(text)
  }

  /**
   * Check if a string is likely a state name
   */
  private isLikelyState(text: string): boolean {
    const stateNames = [
      'alabama', 'alaska', 'arizona', 'arkansas', 'california', 'colorado', 'connecticut', 
      'delaware', 'florida', 'georgia', 'hawaii', 'idaho', 'illinois', 'indiana', 'iowa', 
      'kansas', 'kentucky', 'louisiana', 'maine', 'maryland', 'massachusetts', 'michigan', 
      'minnesota', 'mississippi', 'missouri', 'montana', 'nebraska', 'nevada', 'new hampshire', 
      'new jersey', 'new mexico', 'new york', 'north carolina', 'north dakota', 'ohio', 
      'oklahoma', 'oregon', 'pennsylvania', 'rhode island', 'south carolina', 'south dakota', 
      'tennessee', 'texas', 'utah', 'vermont', 'virginia', 'washington', 'west virginia', 
      'wisconsin', 'wyoming'
    ]
    
    const stateAbbrs = [
      'al', 'ak', 'az', 'ar', 'ca', 'co', 'ct', 'de', 'fl', 'ga', 'hi', 'id', 'il', 'in', 
      'ia', 'ks', 'ky', 'la', 'me', 'md', 'ma', 'mi', 'mn', 'ms', 'mo', 'mt', 'ne', 'nv', 
      'nh', 'nj', 'nm', 'ny', 'nc', 'nd', 'oh', 'ok', 'or', 'pa', 'ri', 'sc', 'sd', 'tn', 
      'tx', 'ut', 'vt', 'va', 'wa', 'wv', 'wi', 'wy'
    ]

    return stateNames.includes(text.toLowerCase()) || stateAbbrs.includes(text.toLowerCase())
  }

  /**
   * Check if text is a common word that shouldn't be treated as a location
   */
  private isCommonWord(text: string): boolean {
    const commonWords = [
      'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'real', 'estate', 'property', 'building', 'development', 'acquisition', 'sale',
      'purchase', 'investment', 'fund', 'capital', 'market', 'commercial', 'residential',
      'office', 'retail', 'industrial', 'multifamily', 'apartment', 'hotel', 'warehouse'
    ]
    
    return commonWords.includes(text.toLowerCase())
  }

  /**
   * Convert to title case
   */
  private titleCase(text: string): string {
    return text.toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  /**
   * Infer region from state
   */
  private inferRegionFromState(state: string): string | undefined {
    const stateToRegion: Record<string, string> = {
      // Northeast
      'Maine': 'Northeast', 'New Hampshire': 'Northeast', 'Vermont': 'Northeast', 
      'Massachusetts': 'Northeast', 'Rhode Island': 'Northeast', 'Connecticut': 'Northeast',
      'New York': 'Northeast', 'New Jersey': 'Northeast', 'Pennsylvania': 'Northeast',
      
      // Southeast
      'Delaware': 'Southeast', 'Maryland': 'Southeast', 'Virginia': 'Southeast', 
      'West Virginia': 'Southeast', 'Kentucky': 'Southeast', 'Tennessee': 'Southeast',
      'North Carolina': 'Southeast', 'South Carolina': 'Southeast', 'Georgia': 'Southeast',
      'Florida': 'Southeast', 'Alabama': 'Southeast', 'Mississippi': 'Southeast',
      'Louisiana': 'Southeast', 'Arkansas': 'Southeast',
      
      // Midwest
      'Ohio': 'Midwest', 'Indiana': 'Midwest', 'Illinois': 'Midwest', 'Michigan': 'Midwest',
      'Wisconsin': 'Midwest', 'Minnesota': 'Midwest', 'Iowa': 'Midwest', 'Missouri': 'Midwest',
      'North Dakota': 'Midwest', 'South Dakota': 'Midwest', 'Nebraska': 'Midwest', 'Kansas': 'Midwest',
      
      // Southwest
      'Texas': 'Southwest', 'Oklahoma': 'Southwest', 'New Mexico': 'Southwest', 'Arizona': 'Southwest',
      
      // West
      'Colorado': 'West', 'Wyoming': 'West', 'Montana': 'West', 'Idaho': 'West',
      'Washington': 'West', 'Oregon': 'West', 'Utah': 'West', 'Nevada': 'West',
      'California': 'West', 'Alaska': 'West', 'Hawaii': 'West'
    }

    return stateToRegion[state]
  }

  /**
   * Remove duplicate locations
   */
  private removeDuplicateLocations(locations: StandardizedLocation[]): StandardizedLocation[] {
    const seen = new Set<string>()
    const unique: StandardizedLocation[] = []

    for (const location of locations) {
      const key = `${location.standardized_city || ''}_${location.standardized_state || ''}_${location.standardized_region || ''}`
      if (!seen.has(key)) {
        seen.add(key)
        unique.push(location)
      }
    }

    return unique.sort((a, b) => b.confidence - a.confidence)
  }

  /**
   * Find the primary location based on confidence and frequency in text
   */
  private findPrimaryLocation(locations: StandardizedLocation[], fullText: string): StandardizedLocation | undefined {
    if (locations.length === 0) return undefined
    if (locations.length === 1) return locations[0]

    // Score locations based on confidence and text frequency
    const scoredLocations = locations.map(location => {
      let score = location.confidence
      
      // Boost score if location appears multiple times in text
      const occurrences = (fullText.match(new RegExp(location.raw_location.toLowerCase(), 'g')) || []).length
      score += (occurrences - 1) * 0.1
      
      // Boost score if it's a complete city/state combination
      if (location.standardized_city && location.standardized_state) {
        score += 0.2
      }

      return { location, score }
    })

    return scoredLocations.sort((a, b) => b.score - a.score)[0].location
  }
} 