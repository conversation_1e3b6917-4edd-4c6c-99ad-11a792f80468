/**
 * Base class for LLM (Large Language Model) providers
 * Provides common interfaces and methods for different LLM implementations
 */

export type LLMMessage = {
  role: 'system' | 'user' | 'assistant';
  content: string;
};

export type LLMRequestOptions = {
  temperature?: number;
  maxTokens?: number;
  model?: string;
  timeoutMs?: number;
};

export type LLMResponse = {
  content: string;
  usage?: {
    promptTokens?: number;
    completionTokens?: number;
    totalTokens?: number;
  };
  provider: string;
  model: string;
};

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LoggerInterface {
  log(level: LogLevel, message: string): void;
}

export abstract class BaseLLMProvider {
  protected name: string;
  protected logger: LoggerInterface;
  protected lastCallTimestamp: number = 0;
  protected defaultRateLimitMs: number = 1000; // 1 second default rate limit
  protected defaultModel: string;
  protected defaultOptions: LLMRequestOptions;

  constructor(name: string, logger: LoggerInterface, defaultOptions: LLMRequestOptions = {}) {
    this.name = name;
    this.logger = logger;
    this.defaultModel = defaultOptions.model || 'default';
    this.defaultOptions = {
      temperature: 0.1,
      maxTokens: 8000, // Increased from 2000 to handle comprehensive responses
      timeoutMs: 30000,
      ...defaultOptions
    };
  }

  /**
   * Primary method to call the LLM with a set of messages
   */
  public abstract callLLM(
    messages: LLMMessage[],
    options?: LLMRequestOptions
  ): Promise<LLMResponse>;

  /**
   * Utility method to enforce rate limiting between API calls
   */
  protected async rateLimitedCall<T>(
    fn: () => Promise<T>,
    rateLimitMs: number = this.defaultRateLimitMs
  ): Promise<T> {
    const now = Date.now();
    const elapsed = now - this.lastCallTimestamp;
    
    if (elapsed < rateLimitMs) {
      const delay = rateLimitMs - elapsed;
      this.logger.log('debug', `Rate limiting: waiting ${delay}ms before next API call`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    
    this.lastCallTimestamp = Date.now();
    return fn();
  }

  /**
   * Simple method to get provider name
   */
  public getName(): string {
    return this.name;
  }

  /**
   * Parse JSON response from LLM with provider-specific error handling and cleanup
   * Each provider should implement its own parsing logic based on response format
   */
  public abstract parseJsonResponse<T = any>(content: string): T | null;

  /**
   * Common JSON repair methods that can be used by all providers
   */
  protected repairTruncatedJson(jsonContent: string): string | null {
    try {
      let fixed = jsonContent.trim();
      
      // Remove any incomplete trailing content that might cause parsing issues
      // 1. Remove incomplete string values (strings that don't end with quotes)
      fixed = fixed.replace(/:\s*"[^"]*$/, ': null');
      
      // 2. Remove incomplete object/array elements at the end
      fixed = fixed.replace(/,\s*[^,}\]]*$/, '');
      
      // 3. Remove trailing commas
      fixed = fixed.replace(/,(\s*[}\]])/, '$1');
      
      // 4. Handle incomplete key-value pairs
      fixed = fixed.replace(/:\s*$/, ': null');
      
      // 5. Remove any trailing incomplete content after the last valid character
      const lastValidChar = Math.max(
        fixed.lastIndexOf('}'),
        fixed.lastIndexOf(']'),
        fixed.lastIndexOf('"'),
        fixed.lastIndexOf('null'),
        fixed.lastIndexOf('true'),
        fixed.lastIndexOf('false')
      );
      
      if (lastValidChar > 0) {
        // Find the next structural character after the last valid content
        let cutPoint = lastValidChar + 1;
        for (let i = lastValidChar + 1; i < fixed.length; i++) {
          const char = fixed[i];
          if (char === ',' || char === '}' || char === ']') {
            cutPoint = i + 1;
            break;
          }
          if (char === '"' || char === '{' || char === '[') {
            cutPoint = i;
            break;
          }
        }
        fixed = fixed.substring(0, cutPoint);
      }
      
      // Count open/close braces and brackets
      const openBraces = (fixed.match(/\{/g) || []).length;
      const closeBraces = (fixed.match(/\}/g) || []).length;
      const openBrackets = (fixed.match(/\[/g) || []).length;
      const closeBrackets = (fixed.match(/\]/g) || []).length;
      
      // Close missing brackets and braces
      const missingCloseBrackets = openBrackets - closeBrackets;
      const missingCloseBraces = openBraces - closeBraces;
      
      for (let i = 0; i < missingCloseBrackets; i++) {
        fixed += ']';
      }
      for (let i = 0; i < missingCloseBraces; i++) {
        fixed += '}';
      }
      
      return fixed;
    } catch (error) {
      console.log('Error repairing truncated JSON:', error)
      return null;
    }
  }

  /**
   * Advanced JSON repair for complex truncation scenarios
   */
  protected repairComplexTruncatedJson(jsonContent: string): string | null {
    try {
      let repairAttempt = jsonContent.trim();

      // Extract just the JSON part if it exists
      const firstBrace = repairAttempt.indexOf("{");
      if (firstBrace === -1) {
        return null;
      }

      repairAttempt = repairAttempt.substring(firstBrace);

      // Try to find the last complete object by counting braces
      let braceCount = 0;
      let lastValidIndex = -1;

      for (let i = 0; i < repairAttempt.length; i++) {
        if (repairAttempt[i] === "{") {
          braceCount++;
        } else if (repairAttempt[i] === "}") {
          braceCount--;
          if (braceCount === 0) {
            lastValidIndex = i;
            break;
          }
        }
      }

      if (lastValidIndex > 0) {
        repairAttempt = repairAttempt.substring(0, lastValidIndex + 1);
        return repairAttempt;
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Method to handle errors
   */
  protected handleError(error: unknown): never {
    const errorMessage = error instanceof Error ? error.message : String(error);
    this.logger.log('error', `${this.name} API error: ${errorMessage}`);
    throw new Error(`${this.name} API error: ${errorMessage}`);
  }
} 