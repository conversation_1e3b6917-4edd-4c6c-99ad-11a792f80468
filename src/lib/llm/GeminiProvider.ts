import {
  BaseLLMProvider,
  LLMMessage,
  LLMRequestOptions,
  LLMResponse,
  LoggerInterface,
} from "./BaseLLMProvider";
import { GoogleGenerativeAI } from "@google/generative-ai";

/**
 * Extended LLMMessage type for Gemini file support
 */
export type GeminiFilePart = {
  fileBuffer: Buffer;
  mimeType: string;
};

export type GeminiLLMMessage =
  | (LLMMessage & { parts?: (string | GeminiFilePart)[] })
  | {
      role: "system" | "user" | "assistant";
      parts: (string | GeminiFilePart)[];
    };

/**
 * GeminiProvider: Google Gemini LLM API implementation using @google/generative-ai SDK
 * Supports text and file (PDF/image) input.
 */
export class GeminiProvider extends BaseLLMProvider {
  private apiKey: string;
  private genAI: GoogleGenerativeAI;

  constructor(
    logger: LoggerInterface,
    apiKey?: string,
    defaultOptions: LLMRequestOptions = {}
  ) {
    super("Gemini", logger, defaultOptions);
    this.apiKey = apiKey || process.env.GEMINI_API_KEY || "";
    if (!this.apiKey) {
      throw new Error(
        "Gemini API key is required. Set GEMINI_API_KEY environment variable or pass as parameter"
      );
    }
    this.genAI = new GoogleGenerativeAI(this.apiKey);
  }

  /**
   * Call Gemini with text-only messages (legacy, for compatibility)
   */
  public async callLLM(
    messages: LLMMessage[],
    options?: LLMRequestOptions
  ): Promise<LLMResponse> {
    // Convert to GeminiLLMMessage format
    const geminiMessages: GeminiLLMMessage[] = messages.map((msg) => ({
      role: msg.role,
      parts: [msg.content],
    }));
    return this.callLLMWithFiles(geminiMessages, options);
  }

  /**
   * Call Gemini with support for file parts (PDF, image, etc.) using the SDK
   * Each message part can be a string (text) or a GeminiFilePart (fileBuffer + mimeType)
   * Uses the configured model or defaults to gemini-2.5-flash for multimodal
   */
  public async callLLMWithFiles(
    messages: GeminiLLMMessage[],
    options?: LLMRequestOptions,
    useFlash: boolean = true // default to gemini-2.5-flash for multimodal
  ): Promise<LLMResponse> {
    try {
      // Only one user message is supported for multimodal (Gemini SDK limitation)
      // We'll concatenate all user/system/assistant messages into a single prompt
      const textParts: string[] = [];
      const fileParts: { inlineData: { data: string; mimeType: string } }[] = [];
      for (const msg of messages) {
        // If msg has 'content', use it; otherwise, use 'parts'
        const parts =
          "content" in msg
            ? (msg as any).parts || [msg.content]
            : (msg as any).parts;
        for (const part of parts) {
          if (typeof part === "string") {
            textParts.push(part);
          } else if (
            part &&
            typeof part === "object" &&
            "fileBuffer" in part &&
            "mimeType" in part
          ) {
            fileParts.push({
              inlineData: {
                data: part.fileBuffer.toString("base64"),
                mimeType: part.mimeType,
              },
            });
          }
        }
      }
      // Prepare the model - use options or default
      const mergedOptions = {
        ...this.defaultOptions,
        ...options,
      };
      const modelName =
        mergedOptions.model || (useFlash ? "gemini-2.5-flash" : "gemini-pro");
      const model = this.genAI.getGenerativeModel({ model: modelName });
      // Build the input array for generateContent
      const input: any[] = [];
      if (textParts.length > 0) input.push({ text: textParts.join("\n") });
      input.push(...fileParts);
      // Call the SDK
      const result = await model.generateContent(input);
      const content = result.response.text();
      if (!content) {
        throw new Error("Empty response from Gemini");
      }
      return {
        content,
        provider: this.name,
        model: modelName,
        usage: undefined, // SDK does not return usage info
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Parse JSON response from Gemini with robust error handling and cleanup
   * Handles Gemini-specific patterns and response formatting
   */
  public parseJsonResponse<T = any>(content: string): T | null {
    try {
      this.logger.log('debug', `Parsing Gemini response content (length: ${content.length})`);
      
      let jsonContent = content.trim();
      
      // Gemini responses usually don't have <think> tags, but handle them if present
      if (jsonContent.includes('<think>') && jsonContent.includes('</think>')) {
        const thinkEndIndex = jsonContent.lastIndexOf('</think>');
        if (thinkEndIndex !== -1) {
          jsonContent = jsonContent.substring(thinkEndIndex + 8).trim(); // 8 = length of '</think>'
          this.logger.log('debug', `Extracted content after </think> tags`);
        }
      }
      
      // Try to extract from markdown code blocks first
      const markdownJsonMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/);
      if (markdownJsonMatch) {
        jsonContent = markdownJsonMatch[1].trim();
        this.logger.log('debug', `Extracted JSON from markdown code block`);
      } else {
        // Try to find JSON object in the content
        const jsonMatch = jsonContent.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          jsonContent = jsonMatch[0];
          this.logger.log('debug', `Extracted JSON using regex match`);
        } else {
          // Remove any leading/trailing text that isn't JSON
          jsonContent = jsonContent.replace(/^[^{]*/, '').replace(/[^}]*$/, '');
        }
      }
      
      if (!jsonContent) {
        throw new Error('No JSON content found in response');
      }
      
      // Clean up common JSON formatting issues specific to Gemini responses
      jsonContent = jsonContent
        .replace(/^\s*```json\s*/, '') // Remove leading ```json
        .replace(/\s*```\s*$/, '') // Remove trailing ```
        .replace(/,\s*}/g, '}') // Remove trailing commas before closing braces
        .replace(/,\s*]/g, ']') // Remove trailing commas before closing brackets
        .replace(/\/\*[\s\S]*?\*\//g, '') // Remove /* */ comments
        .replace(/\/\/.*$/gm, '') // Remove // comments
        .replace(/,(\s*[}\]])/g, '$1') // Remove trailing commas
        .trim();
      
      // Try to parse the JSON
      let parsed: T;
      try {
        parsed = JSON.parse(jsonContent);
      } catch (parseError) {
        this.logger.log('warn', `Initial JSON parse failed: ${parseError}. Attempting to fix truncated JSON...`);
        
        // Try to fix truncated JSON using base class methods
        const fixedJson = this.repairTruncatedJson(jsonContent);
        if (fixedJson) {
          parsed = JSON.parse(fixedJson);
          this.logger.log('info', `Successfully parsed fixed JSON`);
        } else {
          // Try advanced repair for complex truncation
          const complexFixedJson = this.repairComplexTruncatedJson(jsonContent);
          if (complexFixedJson) {
            parsed = JSON.parse(complexFixedJson);
            this.logger.log('info', `Successfully parsed complex-repaired JSON`);
          } else {
            throw parseError;
          }
        }
      }
      
      this.logger.log('debug', 'Successfully parsed Gemini JSON response');
      return parsed;
    } catch (error) {
      this.logger.log('error', `Failed to parse Gemini JSON response: ${error}`);
      this.logger.log('debug', `Raw content preview: ${content.substring(0, 1000)}...`);
      return null;
    }
  }
}
