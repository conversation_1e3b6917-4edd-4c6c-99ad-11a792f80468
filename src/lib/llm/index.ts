/**
 * LLM (Large Language Model) Provider System
 *
 * This module provides a unified interface for interacting with different LLM
 * providers like OpenAI (GPT) and Perplexity.
 */

// Export the base types and interfaces
export {
  BaseLLMProvider,
  type LLMMessage,
  type LLMRequestOptions,
  type LLMResponse,
  type LoggerInterface,
  type LogLevel,
} from "./BaseLLMProvider";

// Export the specific providers
export { OpenAIProvider } from "./OpenAIProvider";
export { PerplexityProvider } from "./PerplexityProvider";
export { GeminiProvider } from "./GeminiProvider";

// Export the factory and fallback functionality
export {
  LLMFactory,
  FallbackLLMProvider,
  type LLMProviderType,
} from "./LLMFactory";

import { LoggerInterface, LogLevel } from "./BaseLLMProvider";

// Convenient function to create a logger adapter from the standard BaseProcessor log method
export function createProcessorLoggerAdapter(
  logFn: (level: LogLevel, message: string) => void
): LoggerInterface {
  return {
    log: (level: LogLevel, message: string) => logFn(level, message),
  };
}
