import OpenAI from "openai";
import {
  Base<PERSON><PERSON><PERSON>ider,
  LLMMessage,
  LLMRequestOptions,
  LLMResponse,
  LoggerInterface,
} from "./BaseLLMProvider";
import pLimit from "p-limit";
import { traceable } from "langsmith/traceable";
import { wrapOpenAI } from "langsmith/wrappers";

/**
 * OpenAI (GPT) implementation of the LLM provider with LangSmith tracing
 */
export class OpenA<PERSON>rovider extends BaseLLMProvider {
  private client: OpenAI;
  private readonly defaultGptModel = "gpt-4.1";
  private concurrencyLimit = pLimit(5);
  private isTracingEnabled: boolean;

  constructor(
    logger: LoggerInterface,
    apiKey?: string,
    defaultOptions: LLMRequestOptions = {},
    enableTracing: boolean = true
  ) {
    super("OpenAI", logger, {
      model: defaultOptions.model || "gpt-4.1",
      ...defaultOptions,
    });

    // Use API key from env if not provided
    const openaiApiKey = apiKey || process.env.OPENAI_API_KEY;

    if (!openaiApiKey) {
      throw new Error(
        "OpenAI API key is required. Set OPENAI_API_KEY environment variable or pass as parameter"
      );
    }

    this.isTracingEnabled = enableTracing;

    // Create OpenAI client
    const rawClient = new OpenAI({
      apiKey: openaiApiKey,
    });

    // Wrap with LangSmith tracing if enabled
    this.client = this.isTracingEnabled ? wrapOpenAI(rawClient) : rawClient;

    if (this.isTracingEnabled) {
      this.logger.log(
        "info",
        "OpenAI provider initialized with LangSmith tracing enabled"
      );
    }
  }

  /**
   * Call OpenAI's chat completion API with optional tracing
   */
  public async callLLM(
    messages: LLMMessage[],
    options?: LLMRequestOptions
  ): Promise<LLMResponse> {
    // Create the core implementation function
    const coreImplementation = async (
      messages: LLMMessage[],
      options?: LLMRequestOptions
    ): Promise<LLMResponse> => {
      try {
        const mergedOptions = {
          ...this.defaultOptions,
          ...options,
        };

        const model = mergedOptions.model || this.defaultGptModel;
        this.logger.log("info", `Calling OpenAI with model ${model}`);

        // Convert our generic messages to OpenAI's format
        // Handle both string content and vision content arrays
        const openaiMessages = messages.map((message) => ({
          role: message.role,
          content: (message as any).content, // Allow content to be string or array for vision
        }));

        // Call OpenAI with rate limiting and concurrency limiting
        const response = await this.concurrencyLimit(() =>
          this.rateLimitedCall(async () => {
            return await this.client.chat.completions.create({
              model,
              messages: openaiMessages,
              temperature: mergedOptions.temperature,
              max_tokens: mergedOptions.maxTokens,
            });
          }, 1000)
        );

        // Extract the content from the response
        const content = response.choices[0]?.message?.content || "";

        if (!content) {
          throw new Error("Empty response from OpenAI");
        }

        // Return in our standardized format
        return {
          content,
          usage: {
            promptTokens: response.usage?.prompt_tokens,
            completionTokens: response.usage?.completion_tokens,
            totalTokens: response.usage?.total_tokens,
          },
          provider: this.name,
          model: response.model || model,
        };
      } catch (error) {
        return this.handleError(error);
      }
    };

    // If tracing is enabled, wrap the function with traceable
    if (this.isTracingEnabled) {
      const traceableCallLLM = traceable(coreImplementation, {
        name: `${this.name}_chat_completion`,
        tags: ["llm", "openai", "chat"],
        metadata: {
          provider: this.name,
          model: options?.model || this.defaultGptModel,
        },
      });

      return await traceableCallLLM(messages, options);
    } else {
      return await coreImplementation(messages, options);
    }
  }

  /**
   * Create a traceable pipeline function for complex workflows
   */
  public createTraceablePipeline<T>(
    name: string,
    pipelineFunction: (
      input: T,
      provider: OpenAIProvider
    ) => Promise<LLMResponse>,
    tags: string[] = []
  ) {
    if (!this.isTracingEnabled) {
      return async (input: T) => pipelineFunction(input, this);
    }

    return traceable(async (input: T) => pipelineFunction(input, this), {
      name,
      tags: ["pipeline", "openai", ...tags],
      metadata: {
        provider: this.name,
      },
    });
  }

  /**
   * Parse JSON response from OpenAI with robust error handling and cleanup
   * Handles OpenAI-specific patterns and structured output formatting
   */
  public parseJsonResponse<T = any>(content: string): T | null {
    try {
      this.logger.log('debug', `Parsing OpenAI response content (length: ${content.length})`);
      
      let jsonContent = content.trim();
      
      // OpenAI responses usually don't have <think> tags, but handle them if present
      if (jsonContent.includes('<think>') && jsonContent.includes('</think>')) {
        const thinkEndIndex = jsonContent.lastIndexOf('</think>');
        if (thinkEndIndex !== -1) {
          jsonContent = jsonContent.substring(thinkEndIndex + 8).trim(); // 8 = length of '</think>'
          this.logger.log('debug', `Extracted content after </think> tags`);
        }
      }
      
      // Try to extract from markdown code blocks first
      const markdownJsonMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/);
      if (markdownJsonMatch) {
        jsonContent = markdownJsonMatch[1].trim();
        this.logger.log('debug', `Extracted JSON from markdown code block`);
      } else {
        // Try to find JSON object in the content
        const jsonMatch = jsonContent.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          jsonContent = jsonMatch[0];
          this.logger.log('debug', `Extracted JSON using regex match`);
        } else {
          // Remove any leading/trailing text that isn't JSON
          jsonContent = jsonContent.replace(/^[^{]*/, '').replace(/[^}]*$/, '');
        }
      }
      
      if (!jsonContent) {
        throw new Error('No JSON content found in response');
      }
      
      // Clean up common JSON formatting issues specific to OpenAI responses
      jsonContent = jsonContent
        .replace(/^\s*```json\s*/, '') // Remove leading ```json
        .replace(/\s*```\s*$/, '') // Remove trailing ```
        .replace(/,\s*}/g, '}') // Remove trailing commas before closing braces
        .replace(/,\s*]/g, ']') // Remove trailing commas before closing brackets
        .replace(/\/\*[\s\S]*?\*\//g, '') // Remove /* */ comments
        .replace(/\/\/.*$/gm, '') // Remove // comments
        .replace(/,(\s*[}\]])/g, '$1') // Remove trailing commas
        .trim();
      
      // Try to parse the JSON
      let parsed: T;
      try {
        parsed = JSON.parse(jsonContent);
      } catch (parseError) {
        this.logger.log('warn', `Initial JSON parse failed: ${parseError}. Attempting to fix truncated JSON...`);
        
        // Try to fix truncated JSON using base class methods
        const fixedJson = this.repairTruncatedJson(jsonContent);
        if (fixedJson) {
          parsed = JSON.parse(fixedJson);
          this.logger.log('info', `Successfully parsed fixed JSON`);
        } else {
          // Try advanced repair for complex truncation
          const complexFixedJson = this.repairComplexTruncatedJson(jsonContent);
          if (complexFixedJson) {
            parsed = JSON.parse(complexFixedJson);
            this.logger.log('info', `Successfully parsed complex-repaired JSON`);
          } else {
            throw parseError;
          }
        }
      }
      
      this.logger.log('debug', 'Successfully parsed OpenAI JSON response');
      return parsed;
    } catch (error) {
      this.logger.log('error', `Failed to parse OpenAI JSON response: ${error}`);
      this.logger.log('debug', `Raw content preview: ${content.substring(0, 1000)}...`);
      return null;
    }
  }

  /**
   * Get the tracing status
   */
  public isTracingEnabledStatus(): boolean {
    return this.isTracingEnabled;
  }

  /**
   * Enable or disable tracing (requires recreating the client)
   */
  public setTracingEnabled(enabled: boolean, apiKey?: string) {
    if (this.isTracingEnabled === enabled) {
      return; // No change needed
    }

    this.isTracingEnabled = enabled;

    // Recreate client with or without tracing
    const openaiApiKey = apiKey || process.env.OPENAI_API_KEY;
    if (!openaiApiKey) {
      throw new Error("OpenAI API key is required");
    }

    const rawClient = new OpenAI({
      apiKey: openaiApiKey,
    });

    this.client = this.isTracingEnabled ? wrapOpenAI(rawClient) : rawClient;

    this.logger.log(
      "info",
      `OpenAI provider tracing ${enabled ? "enabled" : "disabled"}`
    );
  }
}
