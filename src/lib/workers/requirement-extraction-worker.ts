import { Job } from 'bull';
import { pool } from '../db';
import { DealRequirementProcessor } from '../processors/DealRequirementProcessor';

interface RequirementExtractionJobData {
  jobId: number;
  dealId: number;
  files: Array<{
    fileId: number;
    originalName: string;
    filePath: string;
    fileSize: number;
    relationshipType: string;
    title: string;
    buffer: string | null; // Base64 encoded buffer
    mimeType: string;
    fileName: string;
  }>;
}

export async function processRequirementExtraction(job: any) {
  console.log("🔍 Requirement extraction worker called");
  console.log("🔍 Job data keys:", Object.keys(job.data || {}));
  console.log("🔍 Job data:", JSON.stringify(job.data, null, 2));
  
  const { databaseJobId, dealId, files } = job.data || {};
  const jobId = databaseJobId; // Use the database job ID for database operations

  console.log("🔍 Extracted data:");
  console.log("  - databaseJobId:", databaseJobId);
  console.log("  - dealId:", dealId);
  console.log("  - files count:", files?.length || 0);
  console.log("  - timestamp:", job.data?.timestamp);
  console.log("  - apiCallTime:", job.data?.apiCallTime);
  console.log("  - job.id:", job.id);

  try {
    // Update job status to active
    await pool.query(`
      UPDATE jobs 
      SET status = $1, updated_at = NOW() 
      WHERE job_id = $2
    `, ['active', jobId]);

    // Initialize the requirement processor
    const requirementProcessor = new DealRequirementProcessor(dealId);

    // Prepare files for processing (convert base64 buffers back to Buffer objects)
    const processedFiles = await Promise.all(files.map(async (file: any) => {
      console.log(`Processing file: ${file.fileName || file.originalName || file.title || 'unknown'}`);
      console.log(`File buffer length: ${file.buffer?.length || 0}`);
      console.log(`File mimeType: ${file.mimeType}`);
      console.log(`File object keys: ${Object.keys(file)}`);
      console.log(`File buffer type: ${typeof file.buffer}`);
      
      let buffer: Buffer;
      
      if (!file.buffer) {
        console.log(`No buffer found, fetching file from disk: ${file.filePath}`);
        // Fallback: fetch file directly from disk
        const { FileManager } = await import('../utils/fileManager');
        const fileContent = await FileManager.getFileFromDisk(file.filePath);
        
        if (!fileContent) {
          throw new Error(`Could not fetch file from disk: ${file.filePath}`);
        }
        
        buffer = fileContent;
        console.log(`Fetched file from disk: ${file.filePath} - Size: ${buffer.length} bytes`);
      } else {
        buffer = Buffer.from(file.buffer, "base64");
        console.log(`Converted buffer length: ${buffer.length}`);
      }
      
      return {
        buffer: buffer,
        mimeType: file.mimeType || 'text/plain',
        fileName: file.fileName || file.originalName || file.title || 'unknown'
      };
    }));
    
    console.log(`Total files to process: ${processedFiles.length}`);
    processedFiles.forEach((file: any, index: number) => {
      console.log(`File ${index + 1}: ${file.fileName} (${file.mimeType}) - Buffer size: ${file.buffer.length} bytes`);
    });

    // Process files
    const result = await requirementProcessor.processFiles(processedFiles);

    // Update job status based on result
    const finalStatus = result.success ? 'completed' : 'failed';
    await pool.query(`
      UPDATE jobs 
      SET 
        status = $1, 
        result = $2,
        completed_at = NOW(),
        updated_at = NOW() 
      WHERE job_id = $3
    `, [
      finalStatus,
      JSON.stringify(result),
      jobId
    ]);

    // Update entity status
    await requirementProcessor.updateEntityStatus(dealId, result.success, result.error);

    console.log(`Requirement extraction completed for deal ${dealId}, job ${jobId}`);
    return result;

  } catch (error) {
    console.error(`Error in requirement extraction job ${jobId}:`, error);

    // Update job status to failed
    await pool.query(`
      UPDATE jobs 
      SET 
        status = 'failed', 
        result = $1,
        completed_at = NOW(),
        updated_at = NOW() 
      WHERE job_id = $2
    `, [
      JSON.stringify({ error: error instanceof Error ? error.message : String(error) }),
      jobId
    ]);

    // Update entity status
    const requirementProcessor = new DealRequirementProcessor(dealId);
    await requirementProcessor.updateEntityStatus(dealId, false, error instanceof Error ? error.message : String(error));

    throw error;
  }
} 