import { BullMQManager } from "../queue/BullMQManager";
import path from "path";
import { fileURLToPath } from "url";

(async () => {
  // Use the service account credentials from src/service-creds.json (ESM compatible)
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = path.dirname(__filename);
  const serviceAccountPath = path.resolve(
    __dirname,
    "../../service-creds.json"
  );
  process.env.GOOGLE_APPLICATION_CREDENTIALS = serviceAccountPath;
  const bullManager = BullMQManager.getInstance();
  // Schedule the Gmail fetch job to run every 5 minutes
  await bullManager.addGmailFetchJob({
    repeat: { cron: "* * * * *" }, // every 1 minute
    removeOnComplete: true,
    removeOnFail: true,
  });
  console.log(
    "Scheduled Gmail fetch job to run every 5 minutes using service creds at:",
    serviceAccountPath
  );
})();
