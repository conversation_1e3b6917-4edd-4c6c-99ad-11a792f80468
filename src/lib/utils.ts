import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export type FilterState = {
  [key: string]: any
}

// Utility to convert filter state to API query parameters
export function filtersToQueryParams(filters: FilterState): URLSearchParams {
  const params = new URLSearchParams()
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value === undefined || value === null) return
    
    if (Array.isArray(value)) {
      // Handle array values (multi-select filters)
      if (value.length > 0) {
        params.append(key, JSON.stringify(value))
      }
    } else if (typeof value === 'object') {
      // Handle range values
      if ('min' in value && 'max' in value) {
        if (value.min !== undefined) params.append(`${key}_min`, value.min.toString())
        if (value.max !== undefined) params.append(`${key}_max`, value.max.toString())
      } else {
        // Handle other object values
        params.append(key, JSON.stringify(value))
      }
    } else if (typeof value === 'boolean') {
      // Handle boolean values
      params.append(key, value.toString())
    } else if (value !== '') {
      // Handle string and number values
      params.append(key, value.toString())
    }
  })
  
  return params
}

// Utility to parse query parameters back to filter state
export function queryParamsToFilters(params: URLSearchParams): FilterState {
  const filters: FilterState = {}
  
  params.forEach((value, key) => {
    if (key.endsWith('_min')) {
      const baseKey = key.replace('_min', '')
      filters[baseKey] = filters[baseKey] || {}
      filters[baseKey].min = parseFloat(value)
    } else if (key.endsWith('_max')) {
      const baseKey = key.replace('_max', '')
      filters[baseKey] = filters[baseKey] || {}
      filters[baseKey].max = parseFloat(value)
    } else {
      try {
        // Try to parse as JSON (for arrays and objects)
        filters[key] = JSON.parse(value)
      } catch (e) {
        // Handle as string or boolean
        if (value === 'true') filters[key] = true
        else if (value === 'false') filters[key] = false
        else filters[key] = value
      }
    }
  })
  
  return filters
}

// Function to format currency values
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    maximumFractionDigits: 0
  }).format(value)
}

// Format large numbers with abbreviations (K, M, B)
export function formatNumber(num: number): string {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(1) + 'B'
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// Format a range of numbers (like deal size)
export function formatRange(min: number, max: number, formatter: (num: number) => string = formatNumber): string {
  if (min === 0 && max === 0) return 'Any'
  if (max === 0 || max === Number.MAX_SAFE_INTEGER) return `${formatter(min)}+`
  return `${formatter(min)} - ${formatter(max)}`
}

/**
 * Compare two normalized investment criteria objects and return a match score and reasons.
 * Simple scoring for backward compatibility - used by cron jobs.
 * @param {any} criteria - Investment criteria object (from contact)
 * @param {any} deal - Deal object
 * @returns {{ score: number, reasons: string[] }}
 */
export function matchInvestmentCriteria(criteria: any, deal: any): { score: number; reasons: string[] } {
  let score = 0;
  const reasons: string[] = [];
  
  // Simple deal size match (30% weight)
  if (criteria.minimum_deal_size && criteria.maximum_deal_size && deal.deal_size) {
    const dealSize = parseFloat(deal.deal_size);
    if (dealSize >= criteria.minimum_deal_size && dealSize <= criteria.maximum_deal_size) {
      score += 0.3;
      reasons.push(`Deal size match: $${dealSize}M within range`);
    }
  }
  
  // Property type match (25% weight)
  if (criteria.property_types && deal.property_types) {
    const criteriaTypes = Array.isArray(criteria.property_types) ? criteria.property_types : [criteria.property_types];
    const dealTypes = Array.isArray(deal.property_types) ? deal.property_types : [deal.property_types];
    const intersection = criteriaTypes.filter((type: string) => dealTypes.includes(type));
    if (intersection.length > 0) {
      score += 0.25;
      reasons.push(`Property type match: ${intersection.join(', ')}`);
    }
  }
  
  // Strategy match (20% weight) 
  if (criteria.strategies && deal.strategies) {
    const criteriaStrategies = Array.isArray(criteria.strategies) ? criteria.strategies : [criteria.strategies];
    const dealStrategies = Array.isArray(deal.strategies) ? deal.strategies : [deal.strategies];
    const intersection = criteriaStrategies.filter((strategy: string) => dealStrategies.includes(strategy));
    if (intersection.length > 0) {
      score += 0.2;
      reasons.push(`Strategy match: ${intersection.join(', ')}`);
    }
  }
  
  // Location match (15% weight)
  if (criteria.region && deal.region) {
    const criteriaRegions = Array.isArray(criteria.region) ? criteria.region : [criteria.region];
    const dealRegions = Array.isArray(deal.region) ? deal.region : [deal.region];
    const intersection = criteriaRegions.filter((region: string) => dealRegions.includes(region));
    if (intersection.length > 0) {
      score += 0.15;
      reasons.push(`Region match: ${intersection.join(', ')}`);
    }
  }
  
  // Financial products match (10% weight)
  if (criteria.financial_products && deal.financial_products) {
    const criteriaProducts = Array.isArray(criteria.financial_products) ? criteria.financial_products : [criteria.financial_products];
    const dealProducts = Array.isArray(deal.financial_products) ? deal.financial_products : [deal.financial_products];
    const intersection = criteriaProducts.filter((product: string) => dealProducts.includes(product));
    if (intersection.length > 0) {
      score += 0.1;
      reasons.push(`Financial product match: ${intersection.join(', ')}`);
    }
  }
  
  return {
    score: Math.round(score * 100), // Convert to percentage
    reasons
  };
}
