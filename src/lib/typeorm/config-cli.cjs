const { DataSource } = require("typeorm");
const dotenv = require("dotenv");
const path = require("path");

// Load environment variables
dotenv.config();

// TypeORM CLI configuration
module.exports = new DataSource({
  type: "postgres",
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "5432"),
  username: process.env.DB_USER || "postgres",
  password: process.env.DB_PASSWORD || "password",
  database: process.env.DB_DATABASE || "anax_dashboard",
  synchronize: false,
  logging: process.env.NODE_ENV === "development",
  // Auto-discover entities in the entities folder
  entities: [path.join(__dirname, "entities/*.ts")],
  migrations: [path.join(__dirname, "migrations/*.ts")],
  subscribers: [],
  schema: "public",
  migrationsTableName: "typeorm_migrations",
});