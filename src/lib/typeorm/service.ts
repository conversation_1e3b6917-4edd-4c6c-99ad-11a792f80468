import { AppDataSource } from "./config";
// Import entities directly so TypeORM can discover them
import { DealsV2 } from "./entities/DealsV2";
import { DealNsfField } from "./entities/DealNsfField";
import { Property } from "./entities/Property";
import { Owner } from "./entities/Owner";
import { InvestmentCriteriaDebt } from "./entities/InvestmentCriteriaDebt";
import { InvestmentCriteriaEquity } from "./entities/InvestmentCriteriaEquity";
import { DealContact } from "./entities/DealContact";
import type { IDealsV2, IDealNsfField, IProperty, IOwner } from "./types";

class TypeORMService {
  private static instance: TypeORMService;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): TypeORMService {
    if (!TypeORMService.instance) {
      TypeORMService.instance = new TypeORMService();
    }
    return TypeORMService.instance;
  }

  public async initialize(): Promise<void> {
    if (!this.isInitialized) {
      try {
        await AppDataSource.initialize();
        this.isInitialized = true;
        console.log("✅ TypeORM DataSource initialized successfully");
      } catch (error) {
        console.error("❌ Error initializing TypeORM DataSource:", error);
        throw error;
      }
    }
  }

  public async close(): Promise<void> {
    if (this.isInitialized && AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      this.isInitialized = false;
      console.log("✅ TypeORM DataSource closed successfully");
    }
  }

  // Repository getters using imported entities directly
  public getDealsRepository() {
    if (!this.isInitialized) {
      throw new Error("TypeORM service not initialized. Call initialize() first.");
    }
    return AppDataSource.getRepository(DealsV2);
  }

  public getNsfRepository() {
    if (!this.isInitialized) {
      throw new Error("TypeORM service not initialized. Call initialize() first.");
    }
    return AppDataSource.getRepository(DealNsfField);
  }

  public getPropertyRepository() {
    if (!this.isInitialized) {
      throw new Error("TypeORM service not initialized. Call initialize() first.");
    }
    return AppDataSource.getRepository(Property);
  }

  public getOwnerRepository() {
    if (!this.isInitialized) {
      throw new Error("TypeORM service not initialized. Call initialize() first.");
    }
    return AppDataSource.getRepository(Owner);
  }



  public getInvestmentCriteriaDebtRepository() {
    if (!this.isInitialized) {
      throw new Error("TypeORM service not initialized. Call initialize() first.");
    }
    return AppDataSource.getRepository(InvestmentCriteriaDebt);
  }

  public getInvestmentCriteriaEquityRepository() {
    if (!this.isInitialized) {
      throw new Error("TypeORM service not initialized. Call initialize() first.");
    }
    return AppDataSource.getRepository(InvestmentCriteriaEquity);
  }

  public getDealContactRepository() {
    if (!this.isInitialized) {
      throw new Error("TypeORM service not initialized. Call initialize() first.");
    }
    return AppDataSource.getRepository(DealContact);
  }

  // Helper method to check if initialized
  public getInitialized(): boolean {
    return this.isInitialized;
  }

  // Get the raw DataSource if needed
  public getDataSource() {
    if (!this.isInitialized) {
      throw new Error("TypeORM service not initialized. Call initialize() first.");
    }
    return AppDataSource;
  }
}

// Export singleton instance
export const typeORMService = TypeORMService.getInstance();

// Export the class for testing purposes
export { TypeORMService }; 