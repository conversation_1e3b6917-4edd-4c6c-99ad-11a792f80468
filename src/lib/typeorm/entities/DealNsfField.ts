import { <PERSON>tity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, CreateDateColumn, UpdateDateColumn } from "typeorm";
// Use interface to avoid circular dependency
import type { IDealsV2 } from "../types";

@Entity({ name: "deal_nsf_fields" })
export class DealNsfField {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "deal_id", type: "integer" })
  dealId: number;

  // OLD FIELDS - Keeping for backward compatibility
  @Column({ 
    name: "deal_type", 
    type: "text",
    comment: "Type: debt, equity, acquisition, hard_cost, soft_cost, finance, refinance, development, repositioning, other"
  })
  dealType: string;

  @Column({ 
    name: "nsf_context", 
    type: "text",
    comment: "Context: sources or uses_total"
  })
  nsfContext: string;

  @Column({ 
    name: "capital_position", 
    type: "text",
    nullable: true,
    comment: "Capital position from central mapping table (e.g., Senior Debt, Common Equity, etc.)"
  })
  capitalPosition: string;

  // NEW FIELDS - Sources/Uses structure
  @Column({ 
    name: "source_type", 
    type: "text",
    nullable: true,
    comment: "NEW: Sources dropdown - Senior Debt, Mezzanine, General Partner (GP), Limited Partner (LP), Preferred Equity"
  })
  sourceType: string;

  @Column({ 
    name: "use_type", 
    type: "text",
    nullable: true,
    comment: "NEW: Uses dropdown - Acquisition, Hard Cost, Soft Cost, Financing Cost"
  })
  useType: string;

  // Core NSF measurements - Moved to properties table
  // @Column({ name: "gsf_gross_square_foot", type: "numeric", nullable: true })
  // gsfGrossSquareFoot: number;

  // @Column({ name: "zfa_zoning_floor_area", type: "numeric", nullable: true })
  // zfaZoningFloorArea: number;

  // @Column({ name: "total_nsf_net_square_foot", type: "numeric", nullable: true })
  // totalNsfNetSquareFoot: number;

  // Financial amounts (context-dependent)
  @Column({ name: "amount", type: "numeric", nullable: true, comment: "Main amount for this deal_type and nsf_context" })
  amount: number;

  // Per-unit metrics
  @Column({ name: "amount_per_gsf", type: "numeric", nullable: true })
  amountPerGsf: number;

  @Column({ name: "amount_per_nsf", type: "numeric", nullable: true })
  amountPerNsf: number;

  @Column({ name: "amount_per_zfa", type: "numeric", nullable: true })
  amountPerZfa: number;

  // Percentage
  @Column({ name: "percentage_of_total", type: "numeric", nullable: true })
  percentageOfTotal: number;

  // Flexible additional fields
  @Column({ name: "additional_info", type: "jsonb", nullable: true })
  additionalInfo: any;

  @Column({
    name: "is_required",
    type: "boolean",
    default: false,
    comment: "Indicates whether this capital position is required for the deal (based on ask_capital_position)"
  })
  isRequired: boolean;

  @CreateDateColumn({ name: "created_at", type: "timestamp with time zone" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", type: "timestamp with time zone" })
  updatedAt: Date;

  @ManyToOne("dealsv2", "nsfFields")
  @JoinColumn({ name: "deal_id" })
  deal: IDealsV2;
} 