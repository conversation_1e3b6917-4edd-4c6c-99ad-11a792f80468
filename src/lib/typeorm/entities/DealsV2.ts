import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToMany, ManyToOne, JoinColumn, CreateDateColumn, UpdateDateColumn } from "typeorm";
// Use interfaces to avoid circular dependencies
import type { IDealNsfField, IProperty } from "../types";

@Entity({ name: "dealsv2" })
export class DealsV2 {
  @PrimaryGeneratedColumn({ name: "deal_id" })
  dealId: number;

  @Column({ name: "deal_name", type: "text", nullable: true })
  dealName: string;

  @Column({ name: "summary", type: "text", nullable: true })
  summary: string;

  @Column({ name: "ask_capital_position", type: "text", array: true, nullable: true })
  askCapitalPosition: string[];

  @Column({ name: "ask_amount", type: "numeric", array: true, nullable: true })
  askAmount: number[];

  @Column({ name: "capital_raise_timeline", type: "text", nullable: true })
  capitalRaiseTimeline: string;

  @Column({ name: "date_received", type: "date", nullable: true })
  dateReceived: Date;

  @Column({ name: "deal_stage", type: "text", nullable: true })
  dealStage: string;

  @Column({ name: "deal_type", type: "text", nullable: true })
  dealType: string;

  @Column({ name: "date_closed", type: "date", nullable: true })
  dateClosed: Date;

  @Column({ name: "date_under_contract", type: "date", nullable: true })
  dateUnderContract: Date;

  @Column({ name: "strategy", type: "text", nullable: true })
  strategy: string;

  @Column({ name: "hold_period", type: "numeric", nullable: true })
  holdPeriod: number;

  @Column({ name: "deal_status", type: "text", nullable: true })
  dealStatus: string;

  @Column({ name: "yield_on_cost", type: "numeric", nullable: true })
  yieldOnCost: number;

  @Column({ name: "common_equity_internal_rate_of_return_irr", type: "numeric", nullable: true })
  commonEquityInternalRateOfReturnIrr: number;

  @Column({ name: "common_equity_equity_multiple", type: "numeric", nullable: true })
  commonEquityEquityMultiple: number;

  @Column({ name: "gp_equity_multiple", type: "numeric", nullable: true })
  gpEquityMultiple: number;

  @Column({ name: "gp_internal_rate_of_return_irr", type: "numeric", nullable: true })
  gpInternalRateOfReturnIrr: number;

  @Column({ name: "lp_equity_multiple", type: "numeric", nullable: true })
  lpEquityMultiple: number;

  @Column({ name: "lp_internal_rate_of_return_irr", type: "numeric", nullable: true })
  lpInternalRateOfReturnIrr: number;

  @Column({ name: "preferred_equity_internal_rate_of_return_irr", type: "numeric", nullable: true })
  preferredEquityInternalRateOfReturnIrr: number;

  @Column({ name: "preferred_equity_equity_multiple", type: "numeric", nullable: true })
  preferredEquityEquityMultiple: number;

  @Column({ name: "total_internal_rate_of_return_irr", type: "numeric", nullable: true })
  totalInternalRateOfReturnIrr: number;

  @Column({ name: "total_equity_multiple", type: "numeric", nullable: true })
  totalEquityMultiple: number;

  @Column({ name: "num_affordable_housing_1bedroom_units", type: "integer", nullable: true })
  numAffordableHousing1bedroomUnits: number;

  @Column({ name: "num_affordable_housing_2bedroom_units", type: "integer", nullable: true })
  numAffordableHousing2bedroomUnits: number;

  @Column({ name: "num_affordable_housing_3bedroom_units", type: "integer", nullable: true })
  numAffordableHousing3bedroomUnits: number;

  @Column({ name: "num_affordable_housing_studios_units", type: "integer", nullable: true })
  numAffordableHousingStudiosUnits: number;

  @Column({ name: "num_market_rate_1bedroom_units", type: "integer", nullable: true })
  numMarketRate1bedroomUnits: number;

  @Column({ name: "num_market_rate_2bedroom_units", type: "integer", nullable: true })
  numMarketRate2bedroomUnits: number;

  @Column({ name: "num_market_rate_3bedroom_units", type: "integer", nullable: true })
  numMarketRate3bedroomUnits: number;

  @Column({ name: "num_market_rate_studios_units", type: "integer", nullable: true })
  numMarketRateStudiosUnits: number;

  @Column({ name: "affordable_housing_rent_1bedroom_unit", type: "numeric", nullable: true })
  affordableHousingRent1bedroomUnit: number;

  @Column({ name: "affordable_housing_rent_2bedroom_unit", type: "numeric", nullable: true })
  affordableHousingRent2bedroomUnit: number;

  @Column({ name: "affordable_housing_rent_3bedroom_unit", type: "numeric", nullable: true })
  affordableHousingRent3bedroomUnit: number;

  @Column({ name: "affordable_housing_rent_studio_unit", type: "numeric", nullable: true })
  affordableHousingRentStudioUnit: number;

  @Column({ name: "affordable_housing_sale_1bedroom_unit", type: "numeric", nullable: true })
  affordableHousingSale1bedroomUnit: number;

  @Column({ name: "affordable_housing_sale_2bedroom_unit", type: "numeric", nullable: true })
  affordableHousingSale2bedroomUnit: number;

  @Column({ name: "affordable_housing_sale_3bedroom_unit", type: "numeric", nullable: true })
  affordableHousingSale3bedroomUnit: number;

  @Column({ name: "affordable_housing_sale_studio_unit", type: "numeric", nullable: true })
  affordableHousingSaleStudioUnit: number;

  @Column({ name: "market_rate_rent_1bedroom_unit", type: "numeric", nullable: true })
  marketRateRent1bedroomUnit: number;

  @Column({ name: "market_rate_rent_2bedroom_unit", type: "numeric", nullable: true })
  marketRateRent2bedroomUnit: number;

  @Column({ name: "market_rate_rent_3bedroom_unit", type: "numeric", nullable: true })
  marketRateRent3bedroomUnit: number;

  @Column({ name: "market_rate_sale_1bedroom_unit", type: "numeric", nullable: true })
  marketRateSale1bedroomUnit: number;

  @Column({ name: "market_rate_sale_2bedroom_unit", type: "numeric", nullable: true })
  marketRateSale2bedroomUnit: number;

  @Column({ name: "market_rate_sale_3bedroom_unit", type: "numeric", nullable: true })
  marketRateSale3bedroomUnit: number;

  @Column({ name: "market_rate_sale_studio_unit", type: "numeric", nullable: true })
  marketRateSaleStudioUnit: number;

  @Column({ name: "community_facility_rent", type: "numeric", nullable: true })
  communityFacilityRent: number;

  @Column({ name: "community_facility_rent_additional", type: "numeric", nullable: true })
  communityFacilityRentAdditional: number;

  @Column({ name: "community_facility_sale_price", type: "numeric", nullable: true })
  communityFacilitySalePrice: number;

  @Column({ name: "community_facility_sale_price_additional", type: "numeric", nullable: true })
  communityFacilitySalePriceAdditional: number;

  @Column({ name: "cost_per_total_nsf_net_square_foot", type: "numeric", nullable: true })
  costPerTotalNsfNetSquareFoot: number;

  @Column({ name: "cost_per_zoning_floor_area", type: "numeric", nullable: true })
  costPerZoningFloorArea: number;

  @Column({ name: "purchase_price", type: "numeric", nullable: true })
  purchasePrice: number;

  @Column({ name: "total_project_cost", type: "numeric", nullable: true })
  totalProjectCost: number;

  @Column({ name: "deal_campaign_date", type: "date", nullable: true })
  dealCampaignDate: Date;

  @Column({ name: "deal_campaign_emails_additional_info_requested_received", type: "integer", nullable: true })
  dealCampaignEmailsAdditionalInfoRequestedReceived: number;

  @Column({ name: "deal_campaign_emails_bounce_back_received", type: "integer", nullable: true })
  dealCampaignEmailsBounceBackReceived: number;

  @Column({ name: "deal_campaign_emails_out_of_office_response_received", type: "integer", nullable: true })
  dealCampaignEmailsOutOfOfficeResponseReceived: number;

  @Column({ name: "deal_campaign_emails_response_received", type: "integer", nullable: true })
  dealCampaignEmailsResponseReceived: number;

  @Column({ name: "deal_campaign_emails_sent_out", type: "integer", nullable: true })
  dealCampaignEmailsSentOut: number;

  @Column({ name: "deal_campaign_emails_soft_quotes_received", type: "integer", nullable: true })
  dealCampaignEmailsSoftQuotesReceived: number;

  @Column({ name: "deal_campaign_emails_term_sheets_received", type: "integer", nullable: true })
  dealCampaignEmailsTermSheetsReceived: number;

  @Column({ name: "deal_campaign_status", type: "text", nullable: true })
  dealCampaignStatus: string;

  @Column({ name: "deal_campaign_lead_score", type: "numeric", nullable: true })
  dealCampaignLeadScore: number;

  @Column({ name: "deal_campaign_predicted_scenario", type: "varchar", nullable: true })
  dealCampaignPredictedScenario: string;

  @Column({ name: "residential_nsf_net_square_foot", type: "numeric", nullable: true })
  residentialNsfNetSquareFoot: number;

  @Column({ name: "retail_nsf_net_square_foot", type: "numeric", nullable: true })
  retailNsfNetSquareFoot: number;

  @Column({ name: "occupancy_rate", type: "numeric", nullable: true })
  occupancyRate: number;

  @Column({ name: "community_facility_nsf_net_square_foot", type: "numeric", nullable: true })
  communityFacilityNsfNetSquareFoot: number;

  @Column({ name: "office_nsf_net_square_foot", type: "numeric", nullable: true })
  officeNsfNetSquareFoot: number;

  // Core NSF measurements moved to properties table
  // @Column({ name: "total_nsf_net_square_foot", type: "numeric", nullable: true })
  // totalNsfNetSquareFoot: number;

  // @Column({ name: "gsf_gross_square_foot", type: "numeric", nullable: true })
  // gsfGrossSquareFoot: number;

  // @Column({ name: "zfa_zoning_floor_area", type: "numeric", nullable: true })
  // zfaZoningFloorArea: number;

  @Column({ name: "num_apartment_units", type: "integer", nullable: true })
  numApartmentUnits: number;

  @Column({ name: "closing_time", type: "numeric", nullable: true })
  closingTime: number;

  @Column({ name: "hotel_keys", type: "integer", nullable: true })
  hotelKeys: number;

  @Column({ name: "parking_sf", type: "numeric", nullable: true })
  parkingSf: number;

  @Column({ name: "parking_spots", type: "integer", nullable: true })
  parkingSpots: number;

  @Column({ name: "total_num_affordable_housing_units", type: "integer", nullable: true })
  totalNumAffordableHousingUnits: number;

  @Column({ name: "total_num_market_rate_units", type: "integer", nullable: true })
  totalNumMarketRateUnits: number;

  @Column({ name: "lien_position", type: "text", nullable: true })
  lienPosition: string;

  @Column({ name: "structured_lien_tranche", type: "text", nullable: true })
  structuredLienTranche: string;

  @Column({ name: "loan_amount", type: "numeric", nullable: true })
  loanAmount: number;

  @Column({ name: "interest_rate", type: "numeric", nullable: true })
  interestRate: number;

  @Column({ name: "loan_interest_rate_based_off_sofr", type: "numeric", nullable: true })
  loanInterestRateBasedOffSofr: number;

  @Column({ name: "loan_interest_rate_based_off_wsj", type: "numeric", nullable: true })
  loanInterestRateBasedOffWsj: number;

  @Column({ name: "loan_interest_rate_based_off_prime", type: "numeric", nullable: true })
  loanInterestRateBasedOffPrime: number;

  @Column({ name: "loan_interest_rate_based_off_3yt", type: "numeric", nullable: true })
  loanInterestRateBasedOff3yt: number;

  @Column({ name: "loan_interest_rate_based_off_5yt", type: "numeric", nullable: true })
  loanInterestRateBasedOff5yt: number;

  @Column({ name: "loan_interest_rate_based_off_10yt", type: "numeric", nullable: true })
  loanInterestRateBasedOff10yt: number;

  @Column({ name: "loan_interest_rate_based_off_30yt", type: "numeric", nullable: true })
  loanInterestRateBasedOff30yt: number;

  @Column({ name: "loan_term", type: "numeric", nullable: true })
  loanTerm: number;

  @Column({ name: "loan_to_cost_ltc", type: "numeric", nullable: true })
  loanToCostLtc: number;

  @Column({ name: "loan_to_value_ltv", type: "numeric", nullable: true })
  loanToValueLtv: number;

  @Column({ name: "loan_type", type: "text", nullable: true })
  loanType: string;

  @Column({ name: "dscr", type: "numeric", nullable: true })
  dscr: number;

  @Column({ name: "exit_cap_rate", type: "numeric", nullable: true })
  exitCapRate: number;

  @Column({ name: "recourse", type: "text", nullable: true })
  recourse: string;

  @Column({ name: "senior_debt_cost_gsf_gross_square_foot", type: "numeric", nullable: true })
  seniorDebtCostGsfGrossSquareFoot: number;

  @Column({ name: "senior_debt_cost_hotel_keys", type: "numeric", nullable: true })
  seniorDebtCostHotelKeys: number;

  @Column({ name: "senior_debt_cost_per_apartment_units", type: "numeric", nullable: true })
  seniorDebtCostPerApartmentUnits: number;

  @Column({ name: "senior_debt_nsf_net_square_foot", type: "numeric", nullable: true })
  seniorDebtNsfNetSquareFoot: number;

  @Column({ name: "senior_debt_zoning_floor_area", type: "numeric", nullable: true })
  seniorDebtZoningFloorArea: number;

  @Column({ name: "takeout_loan_per_nsf", type: "numeric", nullable: true })
  takeoutLoanPerNsf: number;

  @Column({ name: "takeout_loan_amortization", type: "numeric", nullable: true })
  takeoutLoanAmortization: number;

  @Column({ name: "takeout_loan_amount", type: "numeric", nullable: true })
  takeoutLoanAmount: number;

  @Column({ name: "takeout_loan_debt_yield", type: "numeric", nullable: true })
  takeoutLoanDebtYield: number;

  @Column({ name: "takeout_loan_dscr", type: "numeric", nullable: true })
  takeoutLoanDscr: number;

  @Column({ name: "takeout_loan_interest_rate", type: "numeric", nullable: true })
  takeoutLoanInterestRate: number;

  @Column({ name: "takeout_loan_interest_rate_based_off_sofr", type: "numeric", nullable: true })
  takeoutLoanInterestRateBasedOffSofr: number;

  @Column({ name: "takeout_loan_interest_rate_based_off_wsj", type: "numeric", nullable: true })
  takeoutLoanInterestRateBasedOffWsj: number;

  @Column({ name: "takeout_loan_interest_rate_based_off_prime", type: "numeric", nullable: true })
  takeoutLoanInterestRateBasedOffPrime: number;

  @Column({ name: "takeout_loan_interest_rate_based_off_3yt", type: "numeric", nullable: true })
  takeoutLoanInterestRateBasedOff3yt: number;

  @Column({ name: "takeout_loan_interest_rate_based_off_5yt", type: "numeric", nullable: true })
  takeoutLoanInterestRateBasedOff5yt: number;

  @Column({ name: "takeout_loan_interest_rate_based_off_10yt", type: "numeric", nullable: true })
  takeoutLoanInterestRateBasedOff10yt: number;

  @Column({ name: "takeout_loan_interest_rate_based_off_30yt", type: "numeric", nullable: true })
  takeoutLoanInterestRateBasedOff30yt: number;

  @Column({ name: "takeout_loan_io_period", type: "numeric", nullable: true })
  takeoutLoanIoPeriod: number;

  @Column({ name: "takeout_loan_loan_to_cost_ltc", type: "numeric", nullable: true })
  takeoutLoanLoanToCostLtc: number;

  @Column({ name: "takeout_loan_loan_to_value_ltv", type: "numeric", nullable: true })
  takeoutLoanLoanToValueLtv: number;

  @Column({ name: "takeout_loan_position", type: "text", nullable: true })
  takeoutLoanPosition: string;

  @Column({ name: "takeout_loan_term", type: "numeric", nullable: true })
  takeoutLoanTerm: number;

  @Column({ name: "takeout_loan_type", type: "text", nullable: true })
  takeoutLoanType: string;

  // Core deal fields
  @Column({ name: "missing_critical_fields", type: "jsonb", nullable: true })
  missingCriticalFields: any;

  @Column({ name: "priority", type: "text", nullable: true })
  priority: string;

  @Column({ name: "processing_duration_ms", type: "integer", nullable: true })
  processingDurationMs: number;

  @Column({ name: "processing_notes", type: "text", nullable: true })
  processingNotes: string;
    
  @Column({ name: "processor_version", type: "text", nullable: true })
  processorVersion: string;

  @Column({ name: "review_notes", type: "text", nullable: true })
  reviewNotes: string;

  @Column({ name: "review_status", type: "text", nullable: true })
  reviewStatus: string;

  @Column({ name: "reviewed_at", type: "timestamp with time zone", nullable: true })
  reviewedAt: Date;

  @Column({ name: "reviewed_by", type: "text", nullable: true })
  reviewedBy: string;

  @Column({ name: "is_distressed", type: "boolean", nullable: true })
  isDistressed: boolean;

  @Column({ name: "is_internal_only", type: "boolean", nullable: true })
  isInternalOnly: boolean;

  @Column({ name: "llm_model_used", type: "text", nullable: true })
  llmModelUsed: string;

  @Column({ name: "llm_provider", type: "text", nullable: true })
  llmProvider: string;

  @Column({ name: "document_filename", type: "text", array: true, nullable: true })
  documentFilename: string[];

  @Column({ name: "document_size_bytes", type: "integer", nullable: true })
  documentSizeBytes: number;

  @Column({ name: "document_source", type: "text", array: true, nullable: true })
  documentSource: string[];

  @Column({ name: "document_type", type: "text", array: true, nullable: true })
  documentType: string[];

  @Column({ name: "extra_fields", type: "jsonb", nullable: true })
  extraFields: any;

  @Column({ name: "extraction_confidence", type: "text", nullable: true })
  extractionConfidence: string;

  @Column({ name: "extraction_method", type: "text", array: true, nullable: true })
  extractionMethod: string[];

  @Column({ name: "extraction_timestamp", type: "timestamp with time zone", nullable: true })
  extractionTimestamp: Date;

  @Column({ name: "data_quality_issues", type: "jsonb", nullable: true })
  dataQualityIssues: any;

  // Conflicts field for storing conflict information from multiple file processing
  @Column({ name: "conflicts", type: "jsonb", nullable: true })
  conflicts: any;

  // Property reference
  @Column({ name: "property_id", type: "integer", nullable: true })
  propertyId: number;

  @ManyToOne("properties", "deals")
  @JoinColumn({ name: "property_id" })
  property: IProperty;

  // NSF fields reference
  @OneToMany("deal_nsf_fields", "deal")
  nsfFields: IDealNsfField[];

  @CreateDateColumn({ name: "created_at", type: "timestamp with time zone" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", type: "timestamp with time zone" })
  updatedAt: Date;
} 