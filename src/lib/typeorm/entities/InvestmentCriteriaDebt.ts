import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, <PERSON>umn, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn, UpdateDateColumn } from "typeorm";
import type { IInvestmentCriteria } from "../types";

@Entity({ name: "investment_criteria_debt" })
export class InvestmentCriteriaDebt {
  @PrimaryGeneratedColumn({ name: "investment_criteria_debt_id" })
  investmentCriteriaDebtId: number;

  @Column({ name: "investment_criteria_id", type: "integer" })
  investmentCriteriaId: number;

  // Additional info
  @Column({ name: "notes", type: "text", nullable: true })
  notes: string;

  // Borrower & Closing
  @Column({ name: "closing_time", type: "numeric", nullable: true })
  closingTime: number;

  @Column({ name: "future_facilities", type: "text", nullable: true })
  futureFacilities: string;

  @Column({ name: "eligible_borrower", type: "text", nullable: true })
  eligibleBorrower: string;

  @Column({ name: "occupancy_requirements", type: "text", nullable: true })
  occupancyRequirements: string;

  // Covenants & Terms
  @Column({ name: "lien_position", type: "text", nullable: true })
  lienPosition: string;

  @Column({ name: "min_loan_dscr", type: "numeric", nullable: true })
  minLoanDscr: number;

  @Column({ name: "max_loan_dscr", type: "numeric", nullable: true })
  maxLoanDscr: number;

  @Column({ name: "recourse_loan", type: "text", nullable: true })
  recourseLoan: string;

  @Column({ name: "loan_min_debt_yield", type: "text", nullable: true })
  loanMinDebtYield: string;

  @Column({ name: "prepayment", type: "text", nullable: true })
  prepayment: string;

  @Column({ name: "yield_maintenance", type: "text", nullable: true })
  yieldMaintenance: string;

  // Deposits
  @Column({ name: "application_deposit", type: "decimal", nullable: true })
  applicationDeposit: number;

  @Column({ name: "good_faith_deposit", type: "numeric", nullable: true })
  goodFaithDeposit: number;

  // Fees
  @Column({ name: "loan_origination_max_fee", type: "numeric", nullable: true })
  loanOriginationMaxFee: number;

  @Column({ name: "loan_origination_min_fee", type: "numeric", nullable: true })
  loanOriginationMinFee: number;

  @Column({ name: "loan_exit_min_fee", type: "numeric", nullable: true })
  loanExitMinFee: number;

  @Column({ name: "loan_exit_max_fee", type: "numeric", nullable: true })
  loanExitMaxFee: number;

  // Interest Rate
  @Column({ name: "loan_interest_rate", type: "numeric", nullable: true })
  loanInterestRate: number;

  @Column({ name: "loan_interest_rate_based_off_sofr", type: "numeric", nullable: true })
  loanInterestRateBasedOffSofr: number;

  @Column({ name: "loan_interest_rate_based_off_wsj", type: "numeric", nullable: true })
  loanInterestRateBasedOffWsj: number;

  @Column({ name: "loan_interest_rate_based_off_prime", type: "numeric", nullable: true })
  loanInterestRateBasedOffPrime: number;

  @Column({ name: "loan_interest_rate_based_off_3yt", type: "numeric", nullable: true })
  loanInterestRateBasedOff3yt: number;

  @Column({ name: "loan_interest_rate_based_off_5yt", type: "numeric", nullable: true })
  loanInterestRateBasedOff5yt: number;

  @Column({ name: "loan_interest_rate_based_off_10yt", type: "numeric", nullable: true })
  loanInterestRateBasedOff10yt: number;

  @Column({ name: "loan_interest_rate_based_off_30yt", type: "numeric", nullable: true })
  loanInterestRateBasedOff30yt: number;

  @Column({ name: "rate_lock", type: "text", nullable: true })
  rateLock: string;

  @Column({ name: "rate_type", type: "text", nullable: true })
  rateType: string;

  // Loan Sizing
  @Column({ name: "loan_to_value_max", type: "numeric", nullable: true })
  loanToValueMax: number;

  @Column({ name: "loan_to_value_min", type: "numeric", nullable: true })
  loanToValueMin: number;

  @Column({ name: "loan_to_cost_min", type: "numeric", nullable: true })
  loanToCostMin: number;

  @Column({ name: "loan_to_cost_max", type: "numeric", nullable: true })
  loanToCostMax: number;

  // Program Detail
  @Column({ name: "debt_program_overview", type: "text", nullable: true })
  debtProgramOverview: string;

  @Column({ name: "loan_type", type: "text", nullable: true })
  loanType: string;

  @Column({ name: "loan_type_normalized", type: "text", nullable: true })
  loanTypeNormalized: string;

  @Column({ name: "structured_loan_tranche", type: "text", nullable: true })
  structuredLoanTranche: string;

  @Column({ name: "loan_program", type: "text", nullable: true })
  loanProgram: string;

  // Term & Amortization
  @Column({ name: "min_loan_term", type: "numeric", nullable: true })
  minLoanTerm: number;

  @Column({ name: "max_loan_term", type: "numeric", nullable: true })
  maxLoanTerm: number;

  @Column({ name: "amortization", type: "text", nullable: true })
  amortization: string;

  @CreateDateColumn({ name: "created_at", type: "timestamp with time zone" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", type: "timestamp with time zone" })
  updatedAt: Date;


}
