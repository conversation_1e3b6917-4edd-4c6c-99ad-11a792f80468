import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn, UpdateDateColumn } from "typeorm";
// Use interface to avoid circular dependencies
import type { IProperty } from "../types";

@Entity({ name: "owners" })
export class Owner {
  @PrimaryGeneratedColumn({ name: "owner_id" })
  ownerId: number;

  @Column({ name: "first_name", type: "varchar", nullable: true })
  firstName: string;

  @Column({ name: "last_name", type: "varchar", nullable: true })
  lastName: string;

  @Column({ name: "owner_name", type: "varchar", nullable: true })
  ownerName: string;

  @Column({ name: "entity_type", type: "varchar", nullable: true })
  entityType: string;

  @Column({ name: "contact_person", type: "varchar", nullable: true })
  contactPerson: string;

  @Column({ name: "phone_number", type: "varchar", nullable: true })
  phoneNumber: string;

  @Column({ name: "additional_phone_number", type: "varchar", nullable: true })
  additionalPhoneNumber: string;

  @Column({ name: "email_address", type: "varchar", nullable: true })
  emailAddress: string;

  @Column({ name: "additional_email_address", type: "varchar", nullable: true })
  additionalEmailAddress: string;

  @Column({ name: "mailing_address", type: "varchar", nullable: true })
  mailingAddress: string;

  @Column({ name: "mailing_city", type: "varchar", nullable: true })
  mailingCity: string;

  @Column({ name: "mailing_state", type: "varchar", nullable: true })
  mailingState: string;

  @Column({ name: "mailing_zipcode", type: "varchar", nullable: true })
  mailingZipcode: string;

  // Properties relationship
  @OneToMany("properties", "owner")
  properties: IProperty[];

  @CreateDateColumn({ name: "created_at", type: "timestamp with time zone" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", type: "timestamp with time zone" })
  updatedAt: Date;
} 