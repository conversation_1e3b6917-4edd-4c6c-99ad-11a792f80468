import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, <PERSON>umn, CreateDateColumn, UpdateDateColumn } from "typeorm";

@Entity("deal_contacts")
export class DealContact {
  @PrimaryGeneratedColumn({ name: "id", type: "integer" })
  id: number;

  @Column({ name: "deal_id", type: "integer", nullable: true })
  dealId: number | null;

  @Column({ name: "deal_v2_id", type: "integer", nullable: true })
  dealV2Id: number | null;

  @Column({ name: "contact_id", type: "integer" })
  contactId: number;

  @Column({ name: "deal_version", type: "varchar", length: 10, default: () => "'v1'" })
  dealVersion: string;

  @CreateDateColumn({ name: "created_at", type: "timestamp with time zone", default: () => "now()" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", type: "timestamp with time zone", default: () => "now()" })
  updatedAt: Date;

  // Helper method to determine if this is a V2 deal contact
  isV2Deal(): boolean {
    return this.dealVersion === "v2" && this.dealV2Id !== null;
  }

  // Helper method to get the appropriate deal ID
  getDealId(): number | null {
    return this.isV2Deal() ? this.dealV2Id : this.dealId;
  }
}
