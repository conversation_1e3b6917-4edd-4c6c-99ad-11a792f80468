import { Repository } from "typeorm";
import { Deal } from "../entities/Deal";
import { DealNsfField } from "../entities/DealNsfField";
import { getDataSource } from "../config";

export class DealRepository {
  private dealRepository: Repository<Deal>;
  private nsfRepository: Repository<DealNsfField>;

  constructor() {
    const dataSource = getDataSource();
    this.dealRepository = dataSource.getRepository(Deal);
    this.nsfRepository = dataSource.getRepository(DealNsfField);
  }

  // Deal methods
  async findById(dealId: number): Promise<Deal | null> {
    return this.dealRepository.findOne({
      where: { dealId },
      relations: ['nsfFields']
    });
  }

  async findByContactId(contactId: number): Promise<Deal[]> {
    return this.dealRepository.find({
      where: { contactId },
      relations: ['nsfFields']
    });
  }

  async findByStatus(status: string): Promise<Deal[]> {
    return this.dealRepository.find({
      where: { status },
      relations: ['nsfFields']
    });
  }

  async findByDealStage(dealStage: string): Promise<Deal[]> {
    return this.dealRepository.find({
      where: { dealStage },
      relations: ['nsfFields']
    });
  }

  async create(dealData: Partial<Deal>): Promise<Deal> {
    const deal = this.dealRepository.create(dealData);
    return this.dealRepository.save(deal);
  }

  async update(dealId: number, dealData: Partial<Deal>): Promise<Deal | null> {
    await this.dealRepository.update(dealId, dealData);
    return this.findById(dealId);
  }

  async delete(dealId: number): Promise<boolean> {
    const result = await this.dealRepository.delete(dealId);
    return result.affected !== 0;
  }

  // NSF Field methods
  async findNsfFieldsByDealId(dealId: number): Promise<DealNsfField[]> {
    return this.nsfRepository.find({
      where: { dealId }
    });
  }

  async findNsfFieldsByDealType(dealType: string): Promise<DealNsfField[]> {
    return this.nsfRepository.find({
      where: { dealType },
      relations: ['deal']
    });
  }

  async findNsfFieldsByContext(nsfContext: string): Promise<DealNsfField[]> {
    return this.nsfRepository.find({
      where: { nsfContext },
      relations: ['deal']
    });
  }

  async createNsfField(nsfData: Partial<DealNsfField>): Promise<DealNsfField> {
    const nsfField = this.nsfRepository.create(nsfData);
    return this.nsfRepository.save(nsfField);
  }

  async updateNsfField(id: number, nsfData: Partial<DealNsfField>): Promise<DealNsfField | null> {
    await this.nsfRepository.update(id, nsfData);
    return this.nsfRepository.findOne({ where: { id } });
  }

  async deleteNsfField(id: number): Promise<boolean> {
    const result = await this.nsfRepository.delete(id);
    return result.affected !== 0;
  }

  // Complex queries
  async findDealsWithNsfFields(criteria: {
    dealType?: string;
    nsfContext?: string;
    status?: string;
    contactId?: number;
  }): Promise<Deal[]> {
    const queryBuilder = this.dealRepository
      .createQueryBuilder('deal')
      .leftJoinAndSelect('deal.nsfFields', 'nsf')
      .where('1=1');

    if (criteria.status) {
      queryBuilder.andWhere('deal.status = :status', { status: criteria.status });
    }

    if (criteria.contactId) {
      queryBuilder.andWhere('deal.contactId = :contactId', { contactId: criteria.contactId });
    }

    if (criteria.dealType) {
      queryBuilder.andWhere('nsf.dealType = :dealType', { dealType: criteria.dealType });
    }

    if (criteria.nsfContext) {
      queryBuilder.andWhere('nsf.nsfContext = :nsfContext', { nsfContext: criteria.nsfContext });
    }

    return queryBuilder.getMany();
  }

  async getDealSummary(dealId: number): Promise<{
    deal: Deal;
    nsfSummary: {
      sources: DealNsfField[];
      uses_total: DealNsfField[];
    };
  } | null> {
    const deal = await this.findById(dealId);
    if (!deal) return null;

    const nsfFields = await this.findNsfFieldsByDealId(dealId);
    
    const nsfSummary = {
      sources: nsfFields.filter(field => field.nsfContext === 'sources'),
      uses_total: nsfFields.filter(field => field.nsfContext === 'uses_total')
    };

    return { deal, nsfSummary };
  }
} 