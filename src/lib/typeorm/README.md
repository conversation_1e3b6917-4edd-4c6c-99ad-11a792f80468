# TypeORM Setup for Deals and NSF Fields

This directory contains the TypeORM configuration, entities, migrations, and repositories for the `deals` and `deal_nsf_fields` tables.

## Structure

```
src/lib/typeorm/
├── config.ts                 # TypeORM configuration
├── entities/                 # Entity definitions
│   ├── Deal.ts              # Deal entity
│   └── DealNsfField.ts      # Deal NSF Fields entity
├── migrations/               # Database migrations
│   └── 1700000000000-CreateDealsAndNsfTables.ts
├── repositories/             # Repository classes
│   └── DealRepository.ts     # Deal repository with methods
├── example-usage.ts          # Usage examples
└── README.md                 # This file
```

## Key Features

### 1. Simplified NSF Structure
- **NSF Context**: Only `sources` and `uses_total` (as requested)
- **Deal Types**: `debt`, `equity`, `acquisition`, `hard_cost`, `soft_cost`, `finance`, `refinance`, `development`, `repositioning`, `other`
- **Normalized Design**: NSF fields are stored in a separate table linked to deals

### 2. Entity Relationships
- `Deal` has a one-to-many relationship with `DealNsfField`
- Each NSF field is linked to a specific deal with a specific type and context
- Foreign key constraint with CASCADE delete

### 3. Database Constraints
- Unique constraint on `(deal_id, deal_type, nsf_context)` combination
- Proper indexes for performance
- Foreign key relationships

## Usage

### Basic Setup

```typescript
import { initializeTypeORM, closeTypeORM } from "./config";
import { DealRepository } from "./repositories/DealRepository";

// Initialize connection
await initializeTypeORM();

// Use repository
const dealRepo = new DealRepository();

// Close connection when done
await closeTypeORM();
```

### Creating a Deal with NSF Fields

```typescript
// Create deal
const deal = await dealRepo.create({
  dealName: "Sample Deal",
  sponsorName: "ABC Capital",
  status: "active"
});

// Create NSF fields
const sourcesNsf = await dealRepo.createNsfField({
  dealId: deal.dealId,
  dealType: "equity",
  nsfContext: "sources",
  gsfGrossSquareFoot: 50000,
  totalNsfNetSquareFoot: 45000
});

const usesTotalNsf = await dealRepo.createNsfField({
  dealId: deal.dealId,
  dealType: "acquisition",
  nsfContext: "uses_total",
  gsfGrossSquareFoot: 50000,
  totalNsfNetSquareFoot: 45000
});
```

### Querying Deals

```typescript
// Find deal with NSF fields
const deal = await dealRepo.findById(dealId);

// Find deals by criteria
const equityDeals = await dealRepo.findDealsWithNsfFields({
  dealType: "equity",
  status: "active"
});

// Get deal summary
const summary = await dealRepo.getDealSummary(dealId);
```

## Migration

The migration file `1700000000000-CreateDealsAndNsfTables.ts` creates both tables with:

1. **deals table**: All the Fireflies columns (excluding simplified NSF fields)
2. **deal_nsf_fields table**: Simplified NSF structure with deal_type and nsf_context

To run migrations:

```typescript
import { AppDataSource } from "./config";

// Run migrations
await AppDataSource.runMigrations();
```

## Important Notes

1. **NSF Fields**: The simplified NSF fields (only `sources` and `uses_total`) are NOT in the main deals table
2. **Deal Types**: The `deal_type` field in NSF table includes cost categories like `hard_cost`, `soft_cost`, `finance`
3. **Relationships**: Use the repository methods to maintain proper relationships
4. **Performance**: Indexes are created on commonly queried fields

## Configuration

The TypeORM config is set to:
- Only connect to the specific tables (deals and deal_nsf_fields)
- Not auto-sync to avoid affecting other tables
- Include proper logging in development
- Use environment variables for database connection

## Environment Variables

```bash
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=anax_dashboard
NODE_ENV=development
```

## Example API Usage

See `example-usage.ts` for complete examples of:
- Creating deals and NSF fields
- Querying with complex criteria
- Updating records
- Managing relationships 