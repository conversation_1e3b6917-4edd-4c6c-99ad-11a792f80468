// Type definitions for entities to avoid circular dependencies
export interface IDealsV2 {
  dealId: number;
  dealName?: string;
  summary?: string;
  dealType?: string;
  // Add other properties as needed
}

export interface IDealNsfField {
  id: number;
  dealId: number;
  dealType: string;
  nsfContext: string;
  capitalPosition?: string;
  // Add other properties as needed
}

export interface IProperty {
  propertyId: number;
  address?: string;
  city?: string;
  state?: string;
  // Add other properties as needed
}

export interface IOwner {
  ownerId: number;
  firstName?: string;
  lastName?: string;
  // Add other properties as needed
}

export interface IInvestmentCriteria {
  criteriaId: number;
  entityType: string;
  entityId: string;
  // Add other properties as needed
  debtCriteria?: IInvestmentCriteriaDebt[];
  equityCriteria?: IInvestmentCriteriaEquity[];
}

export interface IInvestmentCriteriaDebt {
  investmentCriteriaDebtId: number;
  investmentCriteriaId: number;
  notes?: string;
  closingTime?: number;
  futureFacilities?: string;
  eligibleBorrower?: string;
  occupancyRequirements?: string;
  lienPosition?: string;
  minLoanDscr?: number;
  maxLoanDscr?: number;
  recourseLoan?: string;
  loanMinDebtYield?: string;
  prepayment?: string;
  yieldMaintenance?: string;
  applicationDeposit?: number;
  goodFaithDeposit?: number;
  loanOriginationMaxFee?: number;
  loanOriginationMinFee?: number;
  loanExitMinFee?: number;
  loanExitMaxFee?: number;
  loanInterestRate?: number;
  loanInterestRateBasedOffSofr?: number;
  loanInterestRateBasedOffWsj?: number;
  loanInterestRateBasedOffPrime?: number;
  loanInterestRateBasedOff3yt?: number;
  loanInterestRateBasedOff5yt?: number;
  loanInterestRateBasedOff10yt?: number;
  loanInterestRateBasedOff30yt?: number;
  rateLock?: string;
  rateType?: string;
  loanToValueMax?: number;
  loanToValueMin?: number;
  loanToCostMin?: number;
  loanToCostMax?: number;
  debtProgramOverview?: string;
  loanType?: string;
  loanTypeNormalized?: string;
  structuredLoanTranche?: string;
  loanProgram?: string;
  minLoanTerm?: number;
  maxLoanTerm?: number;
  amortization?: string;
  createdAt: Date;
  updatedAt: Date;
  investmentCriteria?: IInvestmentCriteria;
}

export interface IInvestmentCriteriaEquity {
  investmentCriteriaEquityId: number;
  investmentCriteriaId: number;
  targetReturn?: number;
  minimumInternalRateOfReturn?: number;
  minimumYieldOnCost?: number;
  minimumEquityMultiple?: number;
  targetCashOnCashMin?: number;
  minHoldPeriodYears?: number;
  maxHoldPeriodYears?: number;
  ownershipRequirement?: string;
  attachmentPoint?: number;
  maxLeverageTolerance?: number;
  typicalClosingTimelineDays?: number;
  proofOfFundsRequirement?: boolean;
  notes?: string;
  equityProgramOverview?: string;
  occupancyRequirements?: string;
  createdAt: Date;
  updatedAt: Date;
  investmentCriteria?: IInvestmentCriteria;
} 