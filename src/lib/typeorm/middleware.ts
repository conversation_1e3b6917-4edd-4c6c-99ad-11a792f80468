import { NextRequest, NextResponse } from "next/server";
import { typeORMService } from "./service";

export async function withTypeORM(
  request: NextRequest,
  handler: (req: NextRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    // Initialize TypeORM service if not already initialized
    if (!typeORMService.getInitialized()) {
      await typeORMService.initialize();
    }

    // Execute the handler
    const response = await handler(request);

    // Note: We don't close the connection here as it's shared across requests
    // The connection will be managed by the application lifecycle

    return response;
  } catch (error) {
    console.error("TypeORM middleware error:", error);
    
    // If there's a connection error, try to reinitialize
    if (error instanceof Error && error.message.includes("connection")) {
      try {
        await typeORMService.close();
        await typeORMService.initialize();
        
        // Retry the handler once
        const response = await handler(request);
        return response;
      } catch (retryError) {
        console.error("TypeORM retry failed:", retryError);
      }
    }
    
    return NextResponse.json(
      { error: "Database connection error", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// Alternative: Higher-order function for API routes
export function withTypeORMHandler(handler: (req: NextRequest) => Promise<NextResponse>) {
  return async (request: NextRequest): Promise<NextResponse> => {
    return withTypeORM(request, handler);
  };
} 