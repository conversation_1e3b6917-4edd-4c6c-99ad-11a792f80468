import { MigrationInterface, QueryRunner } from "typeorm";

export class AutoGeneratedMigration1755719578047 implements MigrationInterface {
    name = 'AutoGeneratedMigration1755719578047'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "investment_criteria_equity" ADD "yield_on_cost" numeric`);
        await queryRunner.query(`ALTER TABLE "investment_criteria_equity" ADD "target_return_irr_on_equity" numeric`);
        await queryRunner.query(`ALTER TABLE "investment_criteria_equity" ADD "equity_multiple" numeric`);
        await queryRunner.query(`ALTER TABLE "investment_criteria_equity" ADD "position_specific_irr" numeric`);
        await queryRunner.query(`ALTER TABLE "investment_criteria_equity" ADD "position_specific_equity_multiple" numeric`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "investment_criteria_equity" DROP COLUMN "position_specific_equity_multiple"`);
        await queryRunner.query(`ALTER TABLE "investment_criteria_equity" DROP COLUMN "position_specific_irr"`);
        await queryRunner.query(`ALTER TABLE "investment_criteria_equity" DROP COLUMN "equity_multiple"`);
        await queryRunner.query(`ALTER TABLE "investment_criteria_equity" DROP COLUMN "target_return_irr_on_equity"`);
        await queryRunner.query(`ALTER TABLE "investment_criteria_equity" DROP COLUMN "yield_on_cost"`);
    }

}
