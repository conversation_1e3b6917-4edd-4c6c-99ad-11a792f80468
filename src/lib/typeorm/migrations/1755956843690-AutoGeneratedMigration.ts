import { MigrationInterface, QueryRunner } from "typeorm";

export class AutoGeneratedMigration1755956843690 implements MigrationInterface {
    name = 'AutoGeneratedMigration1755956843690'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "dealsv2" ADD "deal_type" text`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "dealsv2" DROP COLUMN "deal_type"`);
    }

}
