import { MigrationInterface, QueryRunner } from "typeorm";

export class AutoGeneratedMigration1755601919523 implements MigrationInterface {
    name = 'AutoGeneratedMigration1755601919523'

    public async up(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE "deal_nsf_fields" ADD "is_required" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`COMMENT ON COLUMN "deal_nsf_fields"."is_required" IS 'Indicates whether this capital position is required for the deal (based on ask_capital_position)'`);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE "deal_nsf_fields" DROP COLUMN "is_required"`);

    }

}
