import { MigrationInterface, QueryRunner } from "typeorm";

export class AutoGeneratedMigration1755886132976 implements MigrationInterface {
    name = 'AutoGeneratedMigration1755886132976'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "dealsv2" ADD "purchase_price" numeric`);
        await queryRunner.query(`ALTER TABLE "dealsv2" ADD "total_project_cost" numeric`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "dealsv2" DROP COLUMN "total_project_cost"`);
        await queryRunner.query(`ALTER TABLE "dealsv2" DROP COLUMN "purchase_price"`);
    }

}
