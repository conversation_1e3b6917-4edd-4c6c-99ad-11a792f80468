import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateDealContactsForV21755509000000 implements MigrationInterface {
    name = 'UpdateDealContactsForV21755509000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Make existing V1 foreign key nullable to allow V2-only rows
        await queryRunner.query(`ALTER TABLE "deal_contacts" ALTER COLUMN "deal_id" DROP NOT NULL`);
        
        // Add a new column to distinguish between V1 and V2 deals
        await queryRunner.query(`ALTER TABLE "deal_contacts" ADD COLUMN IF NOT EXISTS "deal_version" VARCHAR(10) DEFAULT 'v1'`);
        
        // Add a new column for V2 deal ID
        await queryRunner.query(`ALTER TABLE "deal_contacts" ADD COLUMN IF NOT EXISTS "deal_v2_id" INTEGER`);
        
        // Create index on the new column for performance (use a consistent lowercase, unquoted name)
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS idx_deal_contacts_deal_v2_id ON "deal_contacts" ("deal_v2_id")`);
        
        // Add foreign key constraint for V2 deals if it doesn't already exist
        await queryRunner.query(`DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.table_constraints
                WHERE table_name = 'deal_contacts'
                  AND constraint_name = 'deal_contacts_deal_v2_id_fkey'
            ) THEN
                ALTER TABLE "deal_contacts"
                ADD CONSTRAINT "deal_contacts_deal_v2_id_fkey"
                FOREIGN KEY ("deal_v2_id") REFERENCES "dealsv2"("deal_id") ON DELETE CASCADE;
            END IF;
        END $$;`);
        
        // Update existing records to have deal_version = 'v1'
        await queryRunner.query(`UPDATE "deal_contacts" SET "deal_version" = 'v1' WHERE "deal_version" IS NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove foreign key constraint for V2 deals
        await queryRunner.query(`ALTER TABLE "deal_contacts" DROP CONSTRAINT IF EXISTS "deal_contacts_deal_v2_id_fkey"`);
        
        // Drop index
        await queryRunner.query(`DROP INDEX IF EXISTS idx_deal_contacts_deal_v2_id`);
        
        // Remove the new columns
        await queryRunner.query(`ALTER TABLE "deal_contacts" DROP COLUMN IF EXISTS "deal_v2_id"`);
        await queryRunner.query(`ALTER TABLE "deal_contacts" DROP COLUMN IF EXISTS "deal_version"`);
        
        // Restore NOT NULL on original V1 foreign key column
        await queryRunner.query(`ALTER TABLE "deal_contacts" ALTER COLUMN "deal_id" SET NOT NULL`);
    }
}
