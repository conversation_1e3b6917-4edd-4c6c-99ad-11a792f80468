import { MigrationInterface, QueryRunner } from "typeorm";

export class AutoGeneratedMigration1755606410155 implements MigrationInterface {
    name = 'AutoGeneratedMigration1755606410155'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "deal_contacts" DROP CONSTRAINT "deal_contacts_contact_id_fkey"`);
        await queryRunner.query(`ALTER TABLE "deal_contacts" DROP CONSTRAINT "deal_contacts_deal_id_fkey"`);
        await queryRunner.query(`ALTER TABLE "deal_contacts" DROP CONSTRAINT "deal_contacts_deal_v2_id_fkey"`);
        await queryRunner.query(`DROP INDEX "public"."idx_deal_contacts_deal_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_deal_contacts_contact_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_deal_contacts_deal_v2_id"`);
        await queryRunner.query(`ALTER TABLE "deal_contacts" DROP CONSTRAINT "deal_contacts_deal_id_contact_id_key"`);
        await queryRunner.query(`COMMENT ON TABLE "deal_contacts" IS NULL`);
        await queryRunner.query(`ALTER TABLE "dealsv2" ADD "conflicts" jsonb`);
        await queryRunner.query(`COMMENT ON COLUMN "deal_contacts"."deal_id" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "deal_contacts"."contact_id" IS NULL`);
        await queryRunner.query(`ALTER TABLE "deal_contacts" ALTER COLUMN "deal_version" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "deal_contacts" ALTER COLUMN "created_at" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "deal_contacts" ALTER COLUMN "updated_at" SET NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "deal_contacts" ALTER COLUMN "updated_at" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "deal_contacts" ALTER COLUMN "created_at" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "deal_contacts" ALTER COLUMN "deal_version" DROP NOT NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "deal_contacts"."contact_id" IS 'Reference to the contact'`);
        await queryRunner.query(`COMMENT ON COLUMN "deal_contacts"."deal_id" IS 'Reference to the deal'`);
        await queryRunner.query(`ALTER TABLE "dealsv2" DROP COLUMN "conflicts"`);
        await queryRunner.query(`COMMENT ON TABLE "deal_contacts" IS 'Junction table linking deals to multiple contacts'`);
        await queryRunner.query(`ALTER TABLE "deal_contacts" ADD CONSTRAINT "deal_contacts_deal_id_contact_id_key" UNIQUE ("deal_id", "contact_id")`);
        await queryRunner.query(`CREATE INDEX "idx_deal_contacts_deal_v2_id" ON "deal_contacts" ("deal_v2_id") `);
        await queryRunner.query(`CREATE INDEX "idx_deal_contacts_contact_id" ON "deal_contacts" ("contact_id") `);
        await queryRunner.query(`CREATE INDEX "idx_deal_contacts_deal_id" ON "deal_contacts" ("deal_id") `);
        await queryRunner.query(`ALTER TABLE "deal_contacts" ADD CONSTRAINT "deal_contacts_deal_v2_id_fkey" FOREIGN KEY ("deal_v2_id") REFERENCES "dealsv2"("deal_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "deal_contacts" ADD CONSTRAINT "deal_contacts_deal_id_fkey" FOREIGN KEY ("deal_id") REFERENCES "deals"("deal_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "deal_contacts" ADD CONSTRAINT "deal_contacts_contact_id_fkey" FOREIGN KEY ("contact_id") REFERENCES "contacts"("contact_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

}
