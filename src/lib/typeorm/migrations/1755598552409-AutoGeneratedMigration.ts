import { MigrationInterface, QueryRunner } from "typeorm";

export class AutoGeneratedMigration1755598552409 implements MigrationInterface {
    name = 'AutoGeneratedMigration1755598552409'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "deal_nsf_fields" ADD "source_type" text`);
        await queryRunner.query(`COMMENT ON COLUMN "deal_nsf_fields"."source_type" IS 'NEW: Sources dropdown - Senior Debt, Mezzanine, General Partner (GP), Limited Partner (LP), Preferred Equity'`);
        await queryRunner.query(`ALTER TABLE "deal_nsf_fields" ADD "use_type" text`);
        await queryRunner.query(`COMMENT ON COLUMN "deal_nsf_fields"."use_type" IS 'NEW: Uses dropdown - Acquisition, Hard Cost, Soft Cost, Financing Cost'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`COMMENT ON COLUMN "deal_nsf_fields"."use_type" IS 'NEW: Uses dropdown - Acquisition, Hard Cost, Soft Cost, Financing Cost'`);
        await queryRunner.query(`ALTER TABLE "deal_nsf_fields" DROP COLUMN "use_type"`);
        await queryRunner.query(`COMMENT ON COLUMN "deal_nsf_fields"."source_type" IS 'NEW: Sources dropdown - Senior Debt, Mezzanine, General Partner (GP), Limited Partner (LP), Preferred Equity'`);
        await queryRunner.query(`ALTER TABLE "deal_nsf_fields" DROP COLUMN "source_type"`);
    }

}
