import { MigrationInterface, QueryRunner } from "typeorm";

export class AutoGeneratedMigration1755865558121 implements MigrationInterface {
    name = 'AutoGeneratedMigration1755865558121'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "deal_nsf_fields" DROP COLUMN "gsf_gross_square_foot"`);
        await queryRunner.query(`ALTER TABLE "deal_nsf_fields" DROP COLUMN "zfa_zoning_floor_area"`);
        await queryRunner.query(`ALTER TABLE "deal_nsf_fields" DROP COLUMN "total_nsf_net_square_foot"`);
        await queryRunner.query(`ALTER TABLE "dealsv2" DROP COLUMN "total_nsf_net_square_foot"`);
        await queryRunner.query(`ALTER TABLE "properties" ADD "total_nsf_net_square_foot" numeric`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "properties" DROP COLUMN "total_nsf_net_square_foot"`);
        await queryRunner.query(`ALTER TABLE "dealsv2" ADD "total_nsf_net_square_foot" numeric`);
        await queryRunner.query(`ALTER TABLE "deal_nsf_fields" ADD "total_nsf_net_square_foot" numeric`);
        await queryRunner.query(`ALTER TABLE "deal_nsf_fields" ADD "zfa_zoning_floor_area" numeric`);
        await queryRunner.query(`ALTER TABLE "deal_nsf_fields" ADD "gsf_gross_square_foot" numeric`);
    }

}
