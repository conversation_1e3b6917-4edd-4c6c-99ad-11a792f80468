import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateInvestmentCriteriaDebtAndEquityTables1755508207029 implements MigrationInterface {
    name = 'CreateInvestmentCriteriaDebtAndEquityTables1755508207029'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "investment_criteria_debt" ("investment_criteria_debt_id" SERIAL NOT NULL, "investment_criteria_id" integer NOT NULL, "notes" text, "closing_time" numeric, "future_facilities" text, "eligible_borrower" text, "occupancy_requirements" text, "lien_position" text, "min_loan_dscr" numeric, "max_loan_dscr" numeric, "recourse_loan" text, "loan_min_debt_yield" text, "prepayment" text, "yield_maintenance" text, "application_deposit" numeric, "good_faith_deposit" numeric, "loan_origination_max_fee" numeric, "loan_origination_min_fee" numeric, "loan_exit_min_fee" numeric, "loan_exit_max_fee" numeric, "loan_interest_rate" numeric, "loan_interest_rate_based_off_sofr" numeric, "loan_interest_rate_based_off_wsj" numeric, "loan_interest_rate_based_off_prime" numeric, "loan_interest_rate_based_off_3yt" numeric, "loan_interest_rate_based_off_5yt" numeric, "loan_interest_rate_based_off_10yt" numeric, "loan_interest_rate_based_off_30yt" numeric, "rate_lock" text, "rate_type" text, "loan_to_value_max" numeric, "loan_to_value_min" numeric, "loan_to_cost_min" numeric, "loan_to_cost_max" numeric, "debt_program_overview" text, "loan_type" text, "loan_type_normalized" text, "structured_loan_tranche" text, "loan_program" text, "min_loan_term" numeric, "max_loan_term" numeric, "amortization" text, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_d0c94e41d9398e16819c91c97f8" PRIMARY KEY ("investment_criteria_debt_id"))`);
        await queryRunner.query(`CREATE TABLE "investment_criteria_equity" ("investment_criteria_equity_id" SERIAL NOT NULL, "investment_criteria_id" integer NOT NULL, "target_return" numeric, "minimum_internal_rate_of_return" numeric, "minimum_yield_on_cost" numeric, "minimum_equity_multiple" numeric, "target_cash_on_cash_min" numeric, "min_hold_period_years" integer, "max_hold_period_years" integer, "ownership_requirement" text, "attachment_point" numeric, "max_leverage_tolerance" numeric, "typical_closing_timeline_days" integer, "proof_of_funds_requirement" boolean, "notes" text, "equity_program_overview" text, "occupancy_requirements" text, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_3bbdb4edb130e4611b610adf469" PRIMARY KEY ("investment_criteria_equity_id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "investment_criteria_equity"`);
        await queryRunner.query(`DROP TABLE "investment_criteria_debt"`);
    }

}
