import { MigrationInterface, QueryRunner } from "typeorm";

export class AddCapitalPositionToDealNsfFields1755506135601 implements MigrationInterface {
    name = 'AddCapitalPositionToDealNsfFields1755506135601'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "deal_nsf_fields" ADD "capital_position" text`);
        await queryRunner.query(`COMMENT ON COLUMN "deal_nsf_fields"."capital_position" IS 'Capital position from central mapping table (e.g., Senior Debt, Common Equity, etc.)'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`COMMENT ON COLUMN "deal_nsf_fields"."capital_position" IS 'Capital position from central mapping table (e.g., Senior Debt, Common Equity, etc.)'`);
        await queryRunner.query(`ALTER TABLE "deal_nsf_fields" DROP COLUMN "capital_position"`);
    }

}
