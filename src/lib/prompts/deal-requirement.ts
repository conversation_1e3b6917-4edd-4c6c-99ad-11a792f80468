// Deal Requirement Extraction Prompt
// Focused on extracting capital positions and requirements from deal documents

export function generateDealRequirementPrompt(): string {
  return `Analyze the provided deal documents and extract all capital positions being sought.

Look for capital requests by identifying **base terms** that are followed by or combined with *modifier words**
**Debt Base Terms:**
  - "acquisition financing"
  - "acquisition loan"
  - "bridge financing"
  - "bridge loan"
  - "construction financing"
  - "construction loan"
  - "debt financing"
  - "development financing"
  - "development loan"
  - "financing"
  - "junior debt"
  - "mezz debt"
  - "mezzanine financing"
  - "permanent loan"
  - "refinancing"
  - "senior debt"
  - "senior loan"
  - "subordinate debt"
  - "subordinate financing"

** Equity Base Terms:**
  - "common equity"
  - "common equity investment"
  - "co-gp"
  - "co-gp investment"
  - "co-general partner"
  - "equity"
  - "equity investment"
  - "general partner equity"
  - "gp equity"
  - "gp equity investment"
  - "limited partner equity"
  - "limited partner investment"
  - "limited parter"
  - "lp equity"
  - "lp"
  - "pref equity investment"
  - "pref equity"
  - "preferred equity"
  - "preferred equity investment"
  - "sponsor co-invest"
  - "sponsor equity"

** General Base Terms:**
  - "capital"
  - "capital raise"
  - "fundraising"
  - "funding"
  - "investment"
  - "investment opportunity"
  - "recapitalization"
  - "sources of funds"
  - "total capitalization"

**Modifier Words (indicating a request):**
  - "required"
  - "requested"
  - "needed"
  - "sought"
  - "amount"
  - "request"
  - "ask" 

Use these exact categories for capital_position or if they exist use closest from central mapping:
// Equity: "Common Equity", "Co-GP Equity", "GP Equity", "JV Equity", "LP Equity", "Preferred Equity"
// Debt: "Senior Debt", "Stretch Senior Debt", "Mezzanine Debt"

Return a JSON object with this structure:
{
  "capital_requests": [
    {
      "capital_position": "string",// MUST be one of the exact values from the central mapping.
      "amount": "number",// MUST be a raw integer or float. NO commas, currency symbols ($), or text.
      "loan_purpose": "string | null",
      "notes": "string"// MUST be a raw integer or float. NO commas, currency symbols ($), or text.
    }
  ]
}

Start your response with { and end with }. Nothing else.`;
}

export function generateDealRequirementPromptWithText(dealText: string): string {
  const basePrompt = generateDealRequirementPrompt();
  
  return `${basePrompt}

[DEAL_DOCUMENTS]:
${dealText}

**CRITICAL: RESPONSE FORMAT MUST BE JSON - Start your response immediately with { and end with }. Nothing else.**`;
}

export function generateDealRequirementPromptWithMappings(mappings: { [key: string]: string[] }): string {
  const capitalPositionMappings = mappings['Capital Position'] || [];
  const capitalPositionOptions = capitalPositionMappings.join(', ');
  
  return `Analyze the provided deal documents and extract all capital positions being sought.

Look for capital requests by identifying **base terms** that are followed by or combined with *modifier words**
**Debt Base Terms:**
  - "acquisition financing"
  - "acquisition loan"
  - "bridge financing"
  - "bridge loan"
  - "construction financing"
  - "construction loan"
  - "debt financing"
  - "development financing"
  - "development loan"
  - "financing"
  - "junior debt"
  - "mezz debt"
  - "mezzanine financing"
  - "permanent loan"
  - "refinancing"
  - "senior debt"
  - "senior loan"
  - "subordinate debt"
  - "subordinate financing"

** Equity Base Terms:**
  - "common equity"
  - "common equity investment"
  - "co-gp"
  - "co-gp investment"
  - "co-general partner"
  - "equity"
  - "equity investment"
  - "general partner equity"
  - "gp equity"
  - "gp equity investment"
  - "limited partner equity"
  - "limited partner investment"
  - "limited parter"
  - "lp equity"
  - "lp"
  - "pref equity investment"
  - "pref equity"
  - "preferred equity"
  - "preferred equity investment"
  - "sponsor co-invest"
  - "sponsor equity"

** General Base Terms:**
  - "capital"
  - "capital raise"
  - "fundraising"
  - "funding"
  - "investment"
  - "investment opportunity"
  - "recapitalization"
  - "sources of funds"
  - "total capitalization"

**Modifier Words (indicating a request):**
  - "required"
  - "requested"
  - "needed"
  - "sought"
  - "amount"
  - "request"
  - "ask" 

Use these exact categories for capital_position (from central mapping):
${capitalPositionOptions}

Return a JSON object with this structure:
{
  "capital_requests": [
    {
      "capital_position": "string",// MUST be one of the exact values from the central mapping.
      "amount": "number",// MUST be a raw integer or float. NO commas, currency symbols ($), or text.
      "loan_purpose": "string | null",
      "notes": "string"// MUST be a raw integer or float. NO commas, currency symbols ($), or text.
    }
  ]
}

Start your response with { and end with }. Nothing else.`;
}

export function generateDealRequirementPromptWithCentralMapping(mappings: { [key: string]: string[] }): string {
  const capitalPositionMappings = mappings['Capital Position'] || [];
  const capitalPositionOptions = capitalPositionMappings.join(', ');
  
  // Build central mapping context
  const centralMappingContext = Object.entries(mappings)
    .map(([category, values]) => `${category}: ${values.join(', ')}`)
    .join('\n');
  
  return `Analyze the provided deal documents and extract all capital positions being sought.

**CENTRAL MAPPING REFERENCE:**
${centralMappingContext}

Look for capital requests by identifying **base terms** that are followed by or combined with *modifier words**
**Debt Base Terms:**
  - "acquisition financing"
  - "acquisition loan"
  - "bridge financing"
  - "bridge loan"
  - "construction financing"
  - "construction loan"
  - "debt financing"
  - "development financing"
  - "development loan"
  - "financing"
  - "junior debt"
  - "mezz debt"
  - "mezzanine financing"
  - "permanent loan"
  - "refinancing"
  - "senior debt"
  - "senior loan"
  - "subordinate debt"
  - "subordinate financing"

** Equity Base Terms:**
  - "common equity"
  - "common equity investment"
  - "co-gp"
  - "co-gp investment"
  - "co-general partner"
  - "equity"
  - "equity investment"
  - "general partner equity"
  - "gp equity"
  - "gp equity investment"
  - "limited partner equity"
  - "limited partner investment"
  - "limited parter"
  - "lp equity"
  - "lp"
  - "pref equity investment"
  - "pref equity"
  - "preferred equity"
  - "preferred equity investment"
  - "sponsor co-invest"
  - "sponsor equity"

** General Base Terms:**
  - "capital"
  - "capital raise"
  - "fundraising"
  - "funding"
  - "investment"
  - "investment opportunity"
  - "recapitalization"
  - "sources of funds"
  - "total capitalization"

**Modifier Words (indicating a request):**
  - "required"
  - "requested"
  - "needed"
  - "sought"
  - "amount"
  - "request"
  - "ask" 

**CRITICAL: Use ONLY these exact categories for capital_position from the central mapping:**
${capitalPositionOptions}

Return a JSON object with this structure:
{
  "capital_requests": [
    {
      "capital_position": "string",// MUST be one of the exact values from the central mapping.
      "amount": "number",// MUST be a raw integer or float. NO commas, currency symbols ($), or text.
      "loan_purpose": "string | null",
      "notes": "string"// MUST be a raw integer or float. NO commas, currency symbols ($), or text.
    }
  ]
}

**IMPORTANT:**
- Only use capital_position values that exactly match the central mapping options above
- If you find a capital request that doesn't match any of the central mapping options, skip it
- Ensure the capital_position field contains only valid values from the central mapping

Start your response with { and end with }. Nothing else.`;
}
