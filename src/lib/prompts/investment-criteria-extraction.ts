// Investment Criteria Extraction Map
// This system extracts investment preferences and criteria from documents

export const investmentCriteriaExtractionMap = {
  // Investment Return Criteria
  target_return: {
    category: "Investment Return",
    subcategory: "Target Return",
    key: "target_return",
    description: "Target return as decimal (e.g., 0.15 for 15%)",
    possible_values: ["number"],
    value_hints: [
      "Look for target return, desired return, or return expectations",
    ],
    required: false,
    max_length: 100,
  },

  // Property and Asset Criteria
  property_types: {
    category: "Property and Asset",
    subcategory: "Property Types",
    key: "property_types",
    description: "Preferred property type (one of values)",
    possible_values: [
      "Multifamily",
      "Office",
      "Retail",
      "Industrial",
      "Hotel",
      "Mixed Use",
      "Land",
      "Special Purpose",
      "Healthcare",
      "Self-Storage",
      "Data Center",
      "Single Family",
      "Student Housing",
      "Senior Housing",
      "Manufactured Housing",
      "Mobile Home Parks",
    ],
    value_hints: [
      "Look for property type preferences, asset types, or investment focus",
    ],
    required: false,
    max_length: 500,
  },
  property_sub_categories: {
    category: "Property and Asset",
    subcategory: "Property Subcategories",
    key: "property_sub_categories",
    description: "Preferred property subcategory (one of values)",
    possible_values: [
      "Class A",
      "Class B",
      "Class C",
      "Core",
      "Core Plus",
      "Value-Add",
      "Opportunistic",
      "Distressed",
      "Ground-Up Development",
      "Renovation",
      "Repositioning",
      "Stabilized",
      "Non-Stabilized",
    ],
    value_hints: [
      "Look for property class, investment strategy, or property condition preferences",
    ],
    required: false,
    max_length: 500,
  },
  strategies: {
    category: "Property and Asset",
    subcategory: "Investment Strategies",
    key: "strategies",
    description: "Preferred investment strategy (one of values)",
    possible_values: [
      "Acquisition",
      "Development",
      "Refinance",
      "Construction",
      "Joint Venture",
      "Disposition",
      "Mezzanine",
      "Preferred Equity",
      "Bridge Loan",
      "Permanent Loan",
      "Ground Lease",
      "Sale-Leaseback",
      "1031 Exchange",
      "Opportunistic",
      "Value-Add",
      "Core Plus",
      "Core",
    ],
    value_hints: [
      "Look for investment strategies, deal types, or transaction preferences",
    ],
    required: false,
    max_length: 500,
  },
  asset_classes: {
    category: "Property and Asset",
    subcategory: "Asset Classes",
    key: "asset_classes",
    description: "Preferred asset class (one of values)",
    possible_values: [
      "Core",
      "Core Plus",
      "Value-Add",
      "Opportunistic",
      "Distressed",
      "Rescue Capital",
      "Ground-Up Development",
      "Stabilized",
      "Non-Stabilized",
      "Institutional",
      "Private Equity",
      "Real Estate Investment Trust (REIT)",
      "Real Estate Operating Company (REOC)",
    ],
    value_hints: [
      "Look for asset class preferences, risk profiles, or investment style",
    ],
    required: false,
    max_length: 500,
  },

  // Deal Size Criteria
  minimum_deal_size: {
    category: "Deal Size",
    subcategory: "Minimum Size",
    key: "minimum_deal_size",
    description: "Minimum deal size in dollars",
    possible_values: ["number"],
    value_hints: [
      "Look for minimum deal size, smallest transaction, or lower bound",
    ],
    required: false,
    max_length: 100,
  },
  maximum_deal_size: {
    category: "Deal Size",
    subcategory: "Maximum Size",
    key: "maximum_deal_size",
    description: "Maximum deal size in dollars",
    possible_values: ["number"],
    value_hints: [
      "Look for maximum deal size, largest transaction, or upper bound",
    ],
    required: false,
    max_length: 100,
  },

  // Hold Period Criteria
  min_hold_period: {
    category: "Hold Period",
    subcategory: "Minimum Period",
    key: "min_hold_period",
    description: "Minimum hold period in months",
    possible_values: ["number"],
    value_hints: ["Look for minimum hold period, shortest investment term"],
    required: false,
    max_length: 100,
  },
  max_hold_period: {
    category: "Hold Period",
    subcategory: "Maximum Period",
    key: "max_hold_period",
    description: "Maximum hold period in months",
    possible_values: ["number"],
    value_hints: ["Look for maximum hold period, longest investment term"],
    required: false,
    max_length: 100,
  },

  // Financial Products and Performance
  financial_products: {
    category: "Financial Products",
    subcategory: "Products",
    key: "financial_products",
    description: "Preferred financial product (one of values)",
    possible_values: [
      "Equity",
      "Debt",
      "Mezzanine",
      "Preferred Equity",
      "Bridge Loan",
      "Construction Loan",
      "Permanent Loan",
      "CMBS",
      "Agency Debt",
      "Life Company Debt",
      "Bank Debt",
      "Private Debt",
      "Joint Venture",
      "Fund Investment",
      "REIT Investment",
    ],
    value_hints: [
      "Look for preferred financial products, investment vehicles, or capital structures",
    ],
    required: false,
    max_length: 500,
  },
  historical_irr: {
    category: "Performance",
    subcategory: "Historical IRR",
    key: "historical_irr",
    description: "Historical IRR as decimal",
    possible_values: ["number"],
    value_hints: ["Look for historical IRR, past performance, or track record"],
    required: false,
    max_length: 100,
  },
  historical_em: {
    category: "Performance",
    subcategory: "Historical EM",
    key: "historical_em",
    description: "Historical Equity Multiple as decimal",
    possible_values: ["number"],
    value_hints: [
      "Look for historical equity multiple, past returns, or performance metrics",
    ],
    required: false,
    max_length: 100,
  },

  // Geographic Criteria
  country: {
    category: "Geographic",
    subcategory: "Country",
    key: "country",
    description: "Preferred country (one of values)",
    possible_values: [
      "United States",
      "Canada",
      "Mexico",
      "United Kingdom",
      "Germany",
      "France",
      "Spain",
      "Italy",
      "Netherlands",
      "Belgium",
      "Switzerland",
      "Austria",
      "Sweden",
      "Norway",
      "Denmark",
      "Finland",
      "Australia",
      "New Zealand",
      "Japan",
      "South Korea",
      "Singapore",
      "Hong Kong",
      "China",
      "India",
      "Brazil",
      "Argentina",
      "Chile",
      "Colombia",
      "Peru",
    ],
    value_hints: [
      "Look for country preferences, geographic focus, or international markets",
    ],
    required: false,
    max_length: 500,
  },
  region: {
    category: "Geographic",
    subcategory: "Region",
    key: "region",
    description: "Preferred region (one of values)",
    possible_values: [
      "Northeast",
      "Southeast",
      "Southwest",
      "Midwest",
      "West Coast",
      "Mountain West",
      "Pacific Northwest",
      "Sunbelt",
      "Rust Belt",
      "Coastal",
      "Inland",
      "Urban",
      "Suburban",
      "Rural",
    ],
    value_hints: [
      "Look for regional preferences, geographic focus, or market areas",
    ],
    required: false,
    max_length: 500,
  },
  state: {
    category: "Geographic",
    subcategory: "State",
    key: "state",
    description: "Preferred state (one of values)",
    possible_values: ["string"],
    value_hints: [
      "Look for state preferences, specific markets, or geographic focus",
    ],
    required: false,
    max_length: 1000,
  },
  city: {
    category: "Geographic",
    subcategory: "City",
    key: "city",
    description: "Preferred city (one of values)",
    possible_values: ["string"],
    value_hints: [
      "Look for city preferences, specific markets, or urban focus",
    ],
    required: false,
    max_length: 1000,
  },

  // Loan and Capital Criteria
  loan_program: {
    category: "Loan and Capital",
    subcategory: "Loan Programs",
    key: "loan_program",
    description: "Preferred loan program (one of values)",
    possible_values: [
      "Fannie Mae",
      "Freddie Mac",
      "FHA",
      "HUD",
      "USDA",
      "SBA",
      "Conventional",
      "Portfolio",
      "CMBS",
      "Life Company",
      "Bridge",
      "Construction",
      "Permanent",
      "Mezzanine",
      "Preferred Equity",
    ],
    value_hints: [
      "Look for loan program preferences, financing sources, or capital providers",
    ],
    required: false,
    max_length: 500,
  },
  loan_type: {
    category: "Loan and Capital",
    subcategory: "Loan Types",
    key: "loan_type",
    description: "Preferred loan type (one of values)",
    possible_values: [
      "Acquisition",
      "Bridge",
      "Construction",
      "Permanent",
      "Refinance",
      "Mezzanine",
      "Senior Debt",
      "Subordinate Debt",
      "Preferred Equity",
      "Common Equity",
      "Joint Venture",
    ],
    value_hints: [
      "Look for loan type preferences, financing structures, or capital types",
    ],
    required: false,
    max_length: 500,
  },
  capital_type: {
    category: "Loan and Capital",
    subcategory: "Capital Types",
    key: "capital_type",
    description: "Preferred capital type (one of values)",
    possible_values: [
      "Equity",
      "Debt",
      "Hybrid",
      "Mezzanine",
      "Preferred Equity",
      "Common Equity",
      "Joint Venture",
      "Fund Investment",
      "REIT Investment",
    ],
    value_hints: [
      "Look for capital type preferences, investment structures, or financing types",
    ],
    required: false,
    max_length: 500,
  },
  capital_source: {
    category: "Loan and Capital",
    subcategory: "Capital Source",
    key: "capital_source",
    description: "Primary capital source preference",
    possible_values: [
      "Institutional",
      "Private Equity",
      "Family Office",
      "High Net Worth",
      "Pension Fund",
      "Insurance Company",
      "Bank",
      "Credit Union",
      "Life Company",
      "CMBS",
      "Agency",
      "Government",
      "Foreign Investment",
      "Crowdfunding",
      "REIT",
    ],
    value_hints: [
      "Look for primary capital source, funding preference, or investor type",
    ],
    required: false,
    max_length: 100,
  },

  // Structured Finance
  structured_loan_tranche: {
    category: "Structured Finance",
    subcategory: "Loan Tranches",
    key: "structured_loan_tranche",
    description: "Preferred structured loan tranche (one of values)",
    possible_values: [
      "Senior",
      "Mezzanine",
      "Junior Mezzanine",
      "Preferred Equity",
      "Common Equity",
      "First Lien",
      "Second Lien",
      "Third Lien",
      "A-Note",
      "B-Note",
      "C-Note",
      "D-Note",
    ],
    value_hints: [
      "Look for structured finance preferences, tranche positions, or capital stack",
    ],
    required: false,
    max_length: 500,
  },

  // Loan Term Criteria
  min_loan_term: {
    category: "Loan Terms",
    subcategory: "Minimum Term",
    key: "min_loan_term",
    description: "Minimum loan term in months",
    possible_values: ["number"],
    value_hints: ["Look for minimum loan term, shortest financing period"],
    required: false,
    max_length: 100,
  },
  max_loan_term: {
    category: "Loan Terms",
    subcategory: "Maximum Term",
    key: "max_loan_term",
    description: "Maximum loan term in months",
    possible_values: ["number"],
    value_hints: ["Look for maximum loan term, longest financing period"],
    required: false,
    max_length: 100,
  },

  // Interest Rate Criteria
  loan_interest_rate: {
    category: "Interest Rates",
    subcategory: "General Rate",
    key: "loan_interest_rate",
    description: "Preferred loan interest rate as decimal",
    possible_values: ["number"],
    value_hints: [
      "Look for preferred interest rate, rate expectations, or pricing",
    ],
    required: false,
    max_length: 100,
  },
  loan_interest_rate_sofr: {
    category: "Interest Rates",
    subcategory: "SOFR Rate",
    key: "loan_interest_rate_sofr",
    description: "SOFR-based interest rate as decimal",
    possible_values: ["number"],
    value_hints: [
      "Look for SOFR-based rates, Secured Overnight Financing Rate",
    ],
    required: false,
    max_length: 100,
  },
  loan_interest_rate_wsj: {
    category: "Interest Rates",
    subcategory: "WSJ Rate",
    key: "loan_interest_rate_wsj",
    description: "WSJ-based interest rate as decimal",
    possible_values: ["number"],
    value_hints: ["Look for WSJ-based rates, Wall Street Journal Prime Rate"],
    required: false,
    max_length: 100,
  },
  loan_interest_rate_prime: {
    category: "Interest Rates",
    subcategory: "Prime Rate",
    key: "loan_interest_rate_prime",
    description: "Prime-based interest rate as decimal",
    possible_values: ["number"],
    value_hints: ["Look for Prime-based rates, Prime Rate"],
    required: false,
    max_length: 100,
  },

  // Loan Metrics
  loan_ltv: {
    category: "Loan Metrics",
    subcategory: "LTV",
    key: "loan_ltv",
    description: "Preferred Loan-to-Value ratio as decimal",
    possible_values: ["number"],
    value_hints: ["Look for LTV preferences, loan-to-value ratios"],
    required: false,
    max_length: 100,
  },
  loan_ltc: {
    category: "Loan Metrics",
    subcategory: "LTC",
    key: "loan_ltc",
    description: "Preferred Loan-to-Cost ratio as decimal",
    possible_values: ["number"],
    value_hints: ["Look for LTC preferences, loan-to-cost ratios"],
    required: false,
    max_length: 100,
  },
  loan_origination_fee: {
    category: "Loan Metrics",
    subcategory: "Origination Fee",
    key: "loan_origination_fee",
    description: "Preferred loan origination fee as decimal",
    possible_values: ["number"],
    value_hints: ["Look for origination fee preferences, upfront costs"],
    required: false,
    max_length: 100,
  },
  loan_exit_fee: {
    category: "Loan Metrics",
    subcategory: "Exit Fee",
    key: "loan_exit_fee",
    description: "Preferred loan exit fee as decimal",
    possible_values: ["number"],
    value_hints: ["Look for exit fee preferences, prepayment penalties"],
    required: false,
    max_length: 100,
  },

  // DSCR Criteria
  min_loan_dscr: {
    category: "DSCR",
    subcategory: "Minimum DSCR",
    key: "min_loan_dscr",
    description: "Minimum preferred Debt Service Coverage Ratio",
    possible_values: ["number"],
    value_hints: ["Look for minimum DSCR requirements, debt service coverage"],
    required: false,
    max_length: 100,
  },
  max_loan_dscr: {
    category: "DSCR",
    subcategory: "Maximum DSCR",
    key: "max_loan_dscr",
    description: "Maximum preferred Debt Service Coverage Ratio",
    possible_values: ["number"],
    value_hints: ["Look for maximum DSCR requirements, debt service coverage"],
    required: false,
    max_length: 100,
  },

  // Loan Structure
  recourse_loan: {
    category: "Loan Structure",
    subcategory: "Recourse",
    key: "recourse_loan",
    description: "Recourse loan preference",
    possible_values: [
      "Full Recourse",
      "Non-Recourse",
      "Partial Recourse",
      "Carve-Out Recourse",
      "Springing Recourse",
      "No Preference",
    ],
    value_hints: ["Look for recourse preferences, guarantee requirements"],
    required: false,
    max_length: 100,
  },

  // Additional Data
  extra_fields: {
    category: "Additional Data",
    subcategory: "Extra",
    key: "extra_fields",
    description:
      "All additional fields found in the document that are not core fields. Organize into logical categories.",
    possible_values: ["JSON object"],
    value_hints: [
      "Store any additional fields found in the document that are not core fields. Organize into categories like:",
      "- investment_preferences: Additional investment preferences",
      "- risk_tolerance: Risk tolerance levels and preferences",
      "- timeline_preferences: Investment timeline preferences",
      "- relationship_preferences: Preferred relationship structures",
      "- reporting_preferences: Reporting and communication preferences",
      "- exit_strategies: Preferred exit strategies",
      "- co_investment: Co-investment preferences",
      "- fees: Fee structure preferences",
      "- covenants: Covenant preferences",
      "- notes: Important notes, assumptions, clarifications",
    ],
    required: false,
    max_length: 5000,
  },
};

// Prompt generation functions for InvestmentCriteriaProcessor
export function generateInvestmentCriteriaExtractionPrompt(
  isMultipleFiles: boolean = false
): string {
  const fileContext = isMultipleFiles
    ? "**IMPORTANT**: You are analyzing MULTIPLE files together. Combine and synthesize information from all files to create the most complete and accurate investment criteria profile."
    : "";

  return `You are an expert real estate investment analyst. Extract structured investment criteria and preferences from real estate documents, investment memorandums, and investor materials.

**CRITICAL INSTRUCTIONS:**
- You MUST return a single valid JSON object that conforms to the extraction schema below.
- Return ONLY the JSON object. Do NOT include any commentary, markdown, code blocks, or extra text.
- DO NOT include any thinking tags like <think></think> or reasoning sections.
- DO NOT include any explanatory text before or after the JSON.
- Make sure all property names in the JSON are properly quoted with double quotes.
- Ensure the JSON is properly formatted with proper use of braces, commas, and quotes.
- Escape any special characters in string values properly.
- If information for a field isn't available, use null for that field.
- Always prioritize accuracy over completeness. It's better to leave a field empty than to provide incorrect information.
- For fields marked with "one of values", return a single value from the provided options.
- For single-option fields, return a single value.
${fileContext}

**INVESTMENT CRITERIA EXTRACTION GUIDELINES:**
- Extract ALL investment preferences and criteria found in the document.
- Look for explicit statements about investment preferences, deal criteria, and requirements.
- Identify geographic preferences, property type preferences, and deal size ranges.
- Extract financial criteria like target returns, loan terms, and interest rate preferences.
- Look for risk tolerance indicators and investment strategy preferences.
- Identify capital structure preferences and financing requirements.
- Extract timeline preferences and hold period requirements.
- Look for performance expectations and historical track records.
- Identify relationship preferences and co-investment criteria.
- Capture any additional investment criteria not covered by the standard fields.

**RESPONSE FORMAT: Start your response immediately with { and end with }. Nothing else.**

## EXTRACTION SCHEMA

Extract the following fields with exact column names and data types:

${Object.entries(investmentCriteriaExtractionMap)
  .map(([key, config]) => {
    const possibleValues = Array.isArray(config.possible_values)
      ? config.possible_values.join(", ")
      : config.possible_values;
    const singleOptionNote =
      config.possible_values.length > 1 &&
      config.possible_values[0] !== "number" &&
      config.possible_values[0] !== "string" &&
      config.possible_values[0] !== "JSON object"
        ? " (one of values)"
        : "";
    return `- **${key}**${singleOptionNote} (${possibleValues}): ${config.description}`;
  })
  .join("\n")}

## OUTPUT FORMAT

Return a JSON object with the following structure:

{
  "extracted_data": {
    "target_return": 0.15,
    "property_types": "Multifamily",
    "property_sub_categories": "Class A",
    "strategies": "Acquisition",
    "asset_classes": "Core Plus",
    "minimum_deal_size": 5000000,
    "maximum_deal_size": 50000000,
    "min_hold_period": 36,
    "max_hold_period": 84,
    "financial_products": "Equity",
    "historical_irr": 0.18,
    "historical_em": 2.1,
    "country": "United States",
    "region": "Northeast",
    "state": "New York",
    "city": "New York City",
    "loan_program": "Conventional",
    "loan_type": "Permanent",
    "capital_type": "Equity",
    "capital_source": "Institutional",
    "structured_loan_tranche": "Senior",
    "min_loan_term": 60,
    "max_loan_term": 120,
    "loan_interest_rate": 0.065,
    "loan_interest_rate_sofr": 0.055,
    "loan_interest_rate_wsj": 0.075,
    "loan_interest_rate_prime": 0.085,
    "loan_ltv": 0.75,
    "loan_ltc": 0.8,
    "loan_origination_fee": 0.01,
    "loan_exit_fee": 0.005,
    "min_loan_dscr": 1.25,
    "max_loan_dscr": 1.5,
    "recourse_loan": "Non-Recourse",
    "extra_fields": {
      "investment_preferences": {
        "risk_tolerance": "Moderate",
        "investment_horizon": "5-7 years",
        "liquidity_preference": "Low"
      },
      "relationship_preferences": {
        "co_investment": "Yes",
        "partnership_structure": "GP/LP",
        "reporting_frequency": "Quarterly"
      },
      "exit_strategies": {
        "preferred_exits": ["Sale", "Refinance"],
        "exit_timeline": "5-7 years"
      },
      "fees": {
        "management_fee": "1.5%",
        "carried_interest": "20%",
        "hurdle_rate": "8%"
      },
      "covenants": {
        "financial_covenants": "Standard",
        "reporting_requirements": "Quarterly financials"
      },
      "notes": {
        "important_notes": "Focus on institutional quality assets",
        "assumptions": "Conservative underwriting standards",
        "clarifications": "Open to both debt and equity investments"
      }
    }
  },
  "confidence_scores": {
    "target_return": 0.95,
    "property_types": 0.9,
    "minimum_deal_size": 0.85,
    // ... confidence for each field (0-1 scale)
  },
  "data_quality_issues": [
    "Missing target return specification",
    "Unclear geographic preferences",
    // ... list of issues found
  ],
  "suggestions": [
    "Consider specifying minimum DSCR requirements",
    "Clarify co-investment preferences",
    // ... suggestions for improvement
  ],
  "normalization_applied": [
    "Converted '15%' to '0.15'",
    "Normalized 'Multifamily' to standard format",
    // ... list of normalizations applied
  ],
  "fuzzy_matches_suggested": [
    "Suggested 'Core Plus' as alternative to 'Core'",
    "Recommended 'Value-Add' based on risk profile",
    // ... fuzzy matching suggestions
  ]
}

**If you do not follow these instructions, your output will be rejected.**`;
}

export function generateMultipleFilesInvestmentCriteriaPrompt(
  files: Array<{ fileName: string }>
): string {
  const basePrompt = generateInvestmentCriteriaExtractionPrompt(true);

  const fileList = files
    .map((f, i) => `File ${i + 1}: ${f.fileName}`)
    .join("\n");

  return `${basePrompt}

**FILES TO ANALYZE:**
${fileList}

**MULTIPLE FILES INVESTMENT CRITERIA GUIDANCE:**
- Cross-reference investment criteria across all files to create comprehensive preferences.
- Combine geographic preferences from different sources.
- Merge property type and strategy preferences from various documents.
- Consolidate financial criteria and return expectations.
- Ensure consistency in deal size ranges and hold periods.
- Aggregate loan and capital preferences from different sources.
- Combine risk tolerance indicators from multiple documents.
- Merge timeline and exit strategy preferences.
- Consolidate relationship and co-investment criteria.
- Include references to all related documents and materials.

Please analyze all files together to extract comprehensive investment criteria.`;
}
