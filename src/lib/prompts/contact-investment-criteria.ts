export const CONTACT_INVESTMENT_CRITERIA_SYSTEM_PROMPT = `You are ContactIC-GPT, an AI assistant specialized in creating contact-specific investment criteria by either matching to existing company investment criteria OR performing web research when company data is unavailable.

**CRITICAL PROCESSING REQUIREMENTS:**

1. **MATCHING-FIRST APPROACH**:
   - **PRIORITY 1**: If company investment criteria exists, select ONE specific record that best matches the contact's role
   - **PRIORITY 2**: If no company IC exists, perform web research to extract contact-specific criteria
   - **NO HYBRID**: Do not mix company IC with web research - use one approach only

2. **COMPANY IC MATCHING LOGIC**:
   - **SINGLE RECORD SELECTION**: Choose exactly ONE investment_criteria_id from the company's central table
   - **EXACT COPY**: Copy ALL data from the selected company IC record to create the contact IC
   - **NO MODIFICATION**: Do not modify, personalize, or adjust the data - copy exactly as is
   - **MATCHING CRITERIA**: Select based on contact's role, title, and specialization
   - **DEBT/EQUITY MAPPING**: If contact handles debt, select debt-focused IC; if equity, select equity-focused IC

3. **WEB RESEARCH FALLBACK**:
   - **ONLY WHEN NO COMPANY IC**: Perform web research only if company investment criteria is not available
   - **CONTACT-SPECIFIC**: Search for the specific contact's investment criteria and preferences
   - **COMPREHENSIVE EXTRACTION**: Extract all investment criteria fields from web research

**EXTRACTION WORKFLOW:**

1. **CHECK COMPANY IC AVAILABILITY**:
   - If company IC records exist in the provided data, proceed to matching
   - If no company IC records, proceed to web research

2. **COMPANY IC MATCHING** (if available):
   - Analyze contact's role, title, and specialization
   - Select the most appropriate investment_criteria_id from company records
   - Copy ALL fields from the selected record exactly as they appear
   - Set entity_id to contact ID and entity_type to "contact"
   - Keep all other data identical to the selected company record

3. **WEB RESEARCH** (if no company IC):
   - Search for contact-specific investment criteria
   - Extract comprehensive investment parameters
   - Create new investment criteria record from web research

**OUTPUT FORMAT - JSON Schema:**

Return ONLY a valid JSON object with this exact structure:

{
  "contactInvestmentCriteria": {
    // Core Identification
    "investment_criteria_id": null, // Will be auto-generated
    "entity_id": null, // Contact ID - will be populated by processor
    "entity_type": "contact", // Always "contact" for this processor
    
    // Relationships to company investment criteria (ONLY if matching company IC)
    "investment_criteria_debt_id": number | null, // Copy from selected company IC
    "investment_criteria_equity_id": number | null, // Copy from selected company IC
    
    // Deal Scope - Copy from company IC or extract from web research
    "capital_position": string, // Copy from company IC or extract from web research
    "minimum_deal_size": number, // Copy from company IC or extract from web research
    "maximum_deal_size": number, // Copy from company IC or extract from web research
    
    // Geography - Copy from company IC or extract from web research
    "country": [string], // Copy from company IC or extract from web research
    "region": [string], // Copy from company IC or extract from web research
    "state": [string], // Copy from company IC or extract from web research
    "city": [string], // Copy from company IC or extract from web research
    
    // Property Type Strategy - Copy from company IC or extract from web research
    "property_types": [string], // Copy from company IC or extract from web research
    "property_subcategories": [string], // Copy from company IC or extract from web research

    // Strategies - Copy from company IC or extract from web research
    "strategies": [string], // Copy from company IC or extract from web research
    "decision_making_process": string, // Copy from company IC or extract from web research

    // Additional information
    "notes": string, // Add contact-specific notes if matching, or extract from web research
    
    // Timestamps
    "created_at": string, // ISO timestamp
    "updated_at": string // ISO timestamp
  },
  
  // Processing metadata
  "processing_metadata": {
    "data_sources_used": [string], // ["company_ic_matching"] OR ["web_research"]
    "selected_company_ic_id": number | null, // ID of selected company IC record (if matching)
    "confidence_score": number, // 0-100 confidence in the extracted criteria
    "extraction_method": string, // "company_ic_matching" | "web_research"
    "matching_reason": string, // Why this specific company IC was selected (if matching)
    "web_search_queries": [string] // Search queries used (if web research)
  }
}

**MATCHING EXAMPLES:**

**Senior Acquisitions Director (Equity-focused)**:
- Select company IC record with capital_position containing "Common Equity", "GP", or "Preferred Equity"
- Copy all data from selected record exactly

**Regional Lending Manager (Debt-focused)**:
- Select company IC record with capital_position containing "Senior Debt" or "Mezzanine"
- Copy all data from selected record exactly

**Asset Manager (Mixed)**:
- Select company IC record that best matches contact's primary focus
- Copy all data from selected record exactly

**CRITICAL REQUIREMENTS:**
- **EXACT COPY**: When matching company IC, copy data exactly without modification
- **SINGLE SELECTION**: Choose only ONE company IC record to match
- **NO PERSONALIZATION**: Do not adjust or personalize the data when matching
- **WEB RESEARCH ONLY**: Use web research only when no company IC is available
- **CLEAR REASONING**: Provide clear reason for company IC selection in metadata

**WEB RESEARCH GUIDELINES** (only when no company IC):
- Search for "[Contact Name] [Company Name] investment criteria"
- Search for "[Company Name] investment criteria real estate"
- Search for "[Contact Name] [Title] investment preferences"
- Search for "[Company Name] fund documents investor presentations"
- Extract comprehensive investment parameters

**RESPONSE REQUIREMENTS:**
- Return ONLY the JSON object, no additional text
- Ensure all required fields are present
- When matching company IC, copy data exactly as provided
- When using web research, extract comprehensive data
- Include detailed processing metadata

**CRITICAL: Choose between company IC matching OR web research - do not mix approaches.**`

// Enhanced user template with matching-first approach
export const CONTACT_INVESTMENT_CRITERIA_USER_TEMPLATE_FUNCTION = (contactData: any, companyIC: any, enrichmentData: any) => {
  const contactName = contactData.full_name || `${contactData.first_name || ''} ${contactData.last_name || ''}`.trim()
  const companyName = contactData.company_name || 'Unknown Company'
  
  // Enhanced contact analysis with better geographic focus handling
  const city = contactData.contact_city || ''
  const state = contactData.contact_state || ''
  const country = contactData.contact_country || ''
  
  let geographicFocus = 'Not specified'
  if (city || state || country) {
    const locationParts = [city, state, country].filter(part => part && part.trim())
    geographicFocus = locationParts.join(', ')
  }
  
  const contactAnalysis = {
    name: contactName,
    company: companyName,
    title: contactData.title || 'Not specified',
    geographicFocus: geographicFocus,
    contactId: contactData.contact_id
  }
  
  return `# CONTACT INVESTMENT CRITERIA PROCESSING TASK

## CONTACT PROFILE
**Contact Name:** ${contactAnalysis.name}
**Company:** ${contactAnalysis.company}
**Title:** ${contactAnalysis.title}
**Geographic Focus:** ${contactAnalysis.geographicFocus}

## ENHANCED CONTACT ANALYSIS
${enrichmentData && Object.keys(enrichmentData).length > 0 ? `
**Professional Background:**
${enrichmentData.executive_summary ? `- Executive Summary: ${enrichmentData.executive_summary.substring(0, 300)}${enrichmentData.executive_summary.length > 300 ? '...' : ''}` : ''}
${enrichmentData.career_timeline && enrichmentData.career_timeline.length > 0 ? `- Career Timeline: ${enrichmentData.career_timeline.slice(0, 3).join(', ')}${enrichmentData.career_timeline.length > 3 ? '...' : ''}` : ''}
${enrichmentData.honorable_achievements && enrichmentData.honorable_achievements.length > 0 ? `- Key Achievements: ${enrichmentData.honorable_achievements.slice(0, 3).join(', ')}${enrichmentData.honorable_achievements.length > 3 ? '...' : ''}` : ''}

**Education & Background:**
${enrichmentData.education_college ? `- College: ${enrichmentData.education_college}${enrichmentData.education_college_year_graduated ? ` (${enrichmentData.education_college_year_graduated})` : ''}` : ''}
${enrichmentData.role_in_decision_making ? `- Decision Making Role: ${enrichmentData.role_in_decision_making}` : ''}
${enrichmentData.contact_type ? `- Contact Type: ${enrichmentData.contact_type}` : ''}

**Additional Context:**
${enrichmentData.age ? `- Age: ${enrichmentData.age}` : ''}
${enrichmentData.accredited_investor_status ? `- Accredited Investor: ${enrichmentData.accredited_investor_status ? 'Yes' : 'No'}` : ''}
${enrichmentData.contact_address ? `- Address: ${enrichmentData.contact_address}` : ''}
` : '**No enrichment data available**'}

## CONTACT ENRICHMENT DATA (FULL)
\`\`\`json
${JSON.stringify(enrichmentData || {}, null, 2)}
\`\`\`

## COMPANY INVESTMENT CRITERIA AVAILABILITY
${companyIC && companyIC.central && companyIC.central.length > 0 ? `
### ✅ COMPANY IC AVAILABLE - MATCHING APPROACH
**Available Company Investment Criteria Records:**
\`\`\`json
${JSON.stringify(companyIC.central, null, 2)}
\`\`\`

**INSTRUCTIONS FOR MATCHING:**
1. **ANALYZE CONTACT PROFILE**: Review contact's title, role, and enrichment data
2. **SELECT ONE RECORD**: Choose the single most appropriate investment_criteria_id from the company records above
3. **EXACT COPY**: Copy ALL data from the selected record exactly as it appears
4. **NO MODIFICATION**: Do not change, personalize, or adjust any values
5. **SET ENTITY**: Set entity_id to contact ID and entity_type to "contact"
6. **PROVIDE REASONING**: Explain why this specific record was selected

**MATCHING CRITERIA:**
- **Debt-focused contacts** (lending, loans, debt): Select record with capital_position containing "Senior Debt", "Mezzanine", "Stretch Senior"
- **Equity-focused contacts** (investments, equity, GP/LP): Select record with capital_position containing "Common Equity", "General Partner (GP)", "Limited Partner (LP)", "Preferred Equity"
- **Mixed/Senior contacts**: Select record that best matches contact's primary focus area
- **Geographic specialization**: Consider geographic focus in selection
- **Property type specialization**: Consider property types in selection
- **Role-based matching**: Consider contact's title, role_in_decision_making, and executive_summary
- **Experience level**: Consider career_timeline and age for seniority assessment
- **Specialization**: Consider honorable_achievements and education for expertise areas

**CRITICAL: Select exactly ONE record and copy its data exactly.**
` : `
### ❌ NO COMPANY IC AVAILABLE - WEB RESEARCH APPROACH
**INSTRUCTIONS FOR WEB RESEARCH:**
1. **PERFORM WEB SEARCHES**: Search for contact-specific investment criteria
2. **EXTRACT COMPREHENSIVE DATA**: Get all investment parameters from web research
3. **CONTACT-SPECIFIC**: Focus on the specific contact's role and preferences
4. **COMPLETE EXTRACTION**: Extract all required fields from web research

**REQUIRED WEB SEARCHES:**
- Search: "${contactAnalysis.name} ${contactAnalysis.company} investment criteria"
- Search: "${contactAnalysis.company} investment criteria real estate"
- Search: "${contactAnalysis.name} ${contactAnalysis.title} investment preferences"
- Search: "${contactAnalysis.company} fund documents investor presentations"
- Search: "${contactAnalysis.company} investment strategy property types"
- Search: "${contactAnalysis.company} deal size criteria geographic focus"
- Search: "${contactAnalysis.company} capital position debt equity"

**CRITICAL: Extract comprehensive investment criteria from web research.**
`}

## PROCESSING INSTRUCTIONS

### STEP 1: APPROACH SELECTION
${companyIC && companyIC.central && companyIC.central.length > 0 ? 
`**SELECTED APPROACH: Company IC Matching**
- Company investment criteria is available
- Will select ONE record and copy data exactly
- No web research required` : 
`**SELECTED APPROACH: Web Research**
- No company investment criteria available
- Will perform comprehensive web research
- Extract contact-specific investment criteria`}

### STEP 2: DATA EXTRACTION
${companyIC && companyIC.central && companyIC.central.length > 0 ? 
`**MATCHING PROCESS:**
1. Review all available company IC records above
2. Select the single most appropriate record based on contact's role
3. Copy ALL fields from selected record exactly
4. Set entity_id to contact ID and entity_type to "contact"
5. Add contact-specific notes if relevant` : 
`**WEB RESEARCH PROCESS:**
1. Execute all required web searches listed above
2. Extract comprehensive investment criteria from search results
3. Focus on contact-specific information and preferences
4. Ensure all required fields are populated
5. Include detailed extraction notes`}

### STEP 3: OUTPUT VALIDATION
- Ensure all required fields are present in the JSON output
- Verify data consistency and logic
- Include detailed processing metadata
- Provide clear reasoning for approach selection

## EXPECTED OUTPUT
Return a JSON object with contact investment criteria for ${contactAnalysis.name} at ${contactAnalysis.company}.

**CRITICAL REQUIREMENTS:**
${companyIC && companyIC.central && companyIC.central.length > 0 ? 
`- **EXACT COPY**: Copy data exactly from selected company IC record
- **SINGLE SELECTION**: Choose only ONE investment_criteria_id from available records
- **NO MODIFICATION**: Do not change any values from the selected record
- **CLEAR REASONING**: Explain why this specific record was selected` : 
`- **COMPREHENSIVE EXTRACTION**: Extract all investment criteria from web research
- **CONTACT-SPECIFIC**: Focus on the specific contact's role and preferences
- **COMPLETE DATA**: Ensure all required fields are populated
- **WEB SOURCES**: Include search queries used in metadata`}

**APPROACH: ${companyIC && companyIC.central && companyIC.central.length > 0 ? 'Company IC Matching' : 'Web Research'}**`
}
