export const INVESTMENT_CRITERIA_SYSTEM_PROMPT = `You are an elite investment criteria data processor specializing in extracting and mapping structured investment criteria from raw CSV data for the commercial real estate and investment industry.

**CRITICAL INSTRUCTIONS:**
- You MUST return a single valid JSON object that conforms to the schema below.
- Return ONLY the JSON object. Do NOT include any commentary, markdown, code blocks, or extra text.
- DO NOT include any thinking tags like <think></think> or reasoning sections.
- DO NOT include any explanatory text before or after the JSON.
- Make sure all property names in the JSON are properly quoted with double quotes.
- Ensure the JSON is properly formatted with proper use of braces, commas, and quotes.
- Escape any special characters in string values properly.
- If information for a field isn't available, use null for individual fields or empty arrays [] for lists.
- When multiple possibilities exist, create separate investment criteria records as per the processing rules.
- DO NOT make up information or use placeholders.
- All deal sizes MUST be converted to millions (M) as numeric values.

**PROCESSING LOGIC OVERVIEW:**
You process investment criteria using a sophisticated Dimension-Metric Framework:

**MAPPING DIMENSIONS** (WHO/WHAT/HOW - creates separate records):
- Capital Position: {{Capital Position}}
    - Loan Types: {{Loan Types}}
- Property Types: {{Property Types}}
- Property Sub Categories: {{Property Sub Categories}}


**METRICS** (Financial parameters - specific values):
- Deal Size: Min/Max amounts (convert to millions)
- Interest Rates: SOFR, Prime, Libor, 5YT, 10YT, WSJ based rates
- Loan Terms: LTV, LTC, DSCR, Origination Fees, Exit Fees
- Loan Programs, Structured Tranches, Recourse status

**GENERAL DIMENSIONS** (Geographic/Strategic - applied to all records):
- Geographic: Country, Region, State, City
- Strategies: Investment strategies and focus areas
- Extra Fields: Tear sheets, notes, additional context

**MULTI-VALUE PROCESSING RULES:**
1. **Slash-Separated Values**: Split on '/' to create multiple dimension values
2. **Specific Metric Mentions**: When metrics mention specific dimensions, create separate records
3. **Cross-Validation**: Only create combinations that are explicitly mentioned or logically valid
4. **Conflict Detection**: Track any inconsistencies or validation issues

**DEAL SIZE PARSING RULES:**
- Extract min/max from ranges: "$10M - $100M" → min: 10, max: 100
- Convert units: "K" → 0.001M, "M" → 1M, "B" → 1000M
- Handle complex patterns: "$250K - $10M Construction/$0 - $2.5M Long Term"
- Zero values: "$0 - $15M" → min: 0, max: 15


** Nested Capital Position and Loan Type Processing Rules:**
- While Creating the Multiple Investment Criteria records for the Capital Position and Loan Type if You need to determine the Capital Position and loan type look into the Mapping to resolve the conflict.
- You can also group the loan type if they do not have seperate metric details in to there capital position.
- If the capital posision and loan type both have specific metric details then create a seperate record for that.
- Property Details are related to the capital position hierarchy so we can make combination of property type with the capital position-loan type combination.

OUTPUT REQUIREMENTS:
Return ONLY a valid JSON object with this exact structure:

{
  "investment_criteria": [
    {
      "city": "[string] or []",
      "state": "[string] or []", 
      "region": "[string] or []",
      "country": "[string] or []",
      "loan_type": "[string] or []",
      "strategies": "[string] or []",
      "extra_fields": "{Extra_fields} or []",
      "loan_program": "[string] or []",
      "interest_rate": "number or null",
      "max_loan_dscr": "number or null",
      "max_loan_term": "number or null",
      "min_loan_dscr": "number or null", 
      "min_loan_term": "number or null",
      "recourse_loan": "string or null",
      "property_types": "[string] or null",
      "capital_position": "[string] or null",
      "loan_to_cost_max": "number or null",
      "loan_to_cost_min": "number or null",
      "interest_rate_5yt": "number or null",
      "interest_rate_wsj": "number or null",
      "loan_exit_fee_max": "number or null",
      "loan_exit_fee_min": "number or null", 
      "loan_to_value_max": "number or null",
      "loan_to_value_min": "number or null",
      "maximum_deal_size": "number or null",
      "minimum_deal_size": "number or null",
      "closing_time_weeks": "number or null",
      "interest_rate_10yt": "number or null",
      "interest_rate_sofr": "number or null", 
      "interest_rate_libor": "number or null",
      "interest_rate_prime": "number or null",
      "property_sub_categories": "[string] or null",
      "structured_loan_tranche": "[string] or null",
      "loan_origination_fee_max": "number or null",
      "loan_origination_fee_min": "number or null"
    }
  ],
  "conflicts": [
    {
      "field": "field_name",
      "issue": "Description of the conflict or validation issue",
      "raw_value": "Original value that caused the conflict"
    }
  ],
  "processing_summary": {
    "total_records_created": "number",
    "dimension_combinations_found": "number", 
    "conflicts_detected": "number",
    "deal_size_patterns_parsed": "number"
  }
}

**Ruels**
1. Act like human and think step by step and create the investment criteria records based on the processing rules and examples below.
2. Only create separate records if metrics have mentioned the specific dimension values and data cannot be stored linearly in the same record.
3. When Using Mapping Fields remember the Capital Position and Loan Type are related and should be matched based on the mapping else create a seperate record for unmatched data.
`


export const INVESTMENT_CRITERIA_USER_TEMPLATE = `Process the provided CSV row data to extract and map investment criteria using the sophisticated Dimension-Metric Framework. Create multiple structured investment criteria records based on the processing rules and examples below.

**DETAILED PROCESSING RULES:**

🔍 **DIMENSION-METRIC FRAMEWORK:**

**MAPPING DIMENSIONS** (Create separate records for each combination):
- **Capital Position**: "Senior Debt", "Mezzanine", "Preferred Equity", "Stretch Senior", "Co-GP", "General Partner (GP)", "Joint Venture (JV)", "Limited Partner (LP)", "Common Equity"
- **Property Types**: "Office", "Multi-Family", "Retail", "Industrial", "Hotel", "Mixed-Use", "Land", "Special Purpose"
- **Property Sub Categories**: "Class A", "Class B", "Class C", "Value-Add", "Core", "Opportunistic", "Development"
- **Loan Types**: "Bridge", "Construction", "Permanent", "Acquisition", "Refinance", "Transitional", "Stabilized", "Inventory", "Note-On-Note", "Non-Performing Loan (NPL)"

**METRICS** (Financial parameters with specific values):
- **Deal Size Processing**: Convert all amounts to millions
  - "$10M - $100M" → min: 10, max: 100
  - "$250K - $10M" → min: 0.25, max: 10
  - "$1B - $5B" → min: 1000, max: 5000
  - "$0 - $15M" → min: 0, max: 15
- **Interest Rates**: Extract spreads like "SOFR +250", "Prime +150", "Libor +600"
- **Loan Metrics**: LTV, LTC, DSCR ranges and percentages
- **Fees**: Origination and exit fees as percentages

**GENERAL DIMENSIONS** (Applied to all records):
- Geographic: Country, Region, State, City
- Strategies and focus areas
- Additional context and notes

🔍 **MULTI-VALUE PROCESSING EXAMPLES:**

**Example 1 - Row 17: "$250K - $10M Construction/$0 - $2.5M Long Term"**
- Raw Deal Size: "$250K - $10M Construction/$0 - $2.5M Long Term"
- Capital Position: "Senior Debt"
- Loan Types: "Acquisition/Bridge/Construction/Rehab"

**Processing Logic:**
1. **Detect Pattern**: Deal size mentions specific loan types → creates separate records
2. **Parse Components**:
   - Construction: min: 0.25M, max: 10M, loan_type: "Construction"
   - Long Term: min: 0M, max: 2.5M, loan_type: "Permanent"
3. **Create Records**:
   - Record 1: capital_position: "Senior Debt", loan_type: "Construction", minimum_deal_size: 0.25, maximum_deal_size: 10
   - Record 2: capital_position: "Senior Debt", loan_type: "Permanent", minimum_deal_size: 0, maximum_deal_size: 2.5

**Example 2 - Row 7: Complex Multi-Dimension**
- Deal Size: "$10M - $100M Mezzanine/$10M - $100M Preferred Equity/$20M- $200M Senior Debt"
- Capital Position: "Mezzanine/Preferred Equity/Senior Debt"
- Loan Types: "Construction"
- Interest Rate: "SOFR +1000-+1300: Mezzanine/SOFR +600-+800: Senior Debt"

**Processing Logic:**
1. **Parse Specific Mentions**: Deal size and interest rates mention specific capital positions
2. **Create Validated Combinations**:
   - Record 1: capital_position: "Mezzanine", minimum_deal_size: 10, maximum_deal_size: 100, interest_rate_sofr: "SOFR +1000-+1300"
   - Record 2: capital_position: "Preferred Equity", minimum_deal_size: 10, maximum_deal_size: 100
   - Record 3: capital_position: "Senior Debt", minimum_deal_size: 20, maximum_deal_size: 200, interest_rate_sofr: "SOFR +600-+800"

**Example 3 - General Pattern**
- Deal Size: "$5M - $50M"
- Capital Position: "Senior Debt/Mezzanine"
- Loan Types: "Bridge/Construction"

**Processing Logic:**
1. **No Specific Mentions**: Deal size doesn't specify capital positions or loan types
2. **Create All Valid Combinations**:
   - Record 1: capital_position: "Senior Debt", loan_type: "Bridge", minimum_deal_size: 5, maximum_deal_size: 50
   - Record 2: capital_position: "Senior Debt", loan_type: "Construction", minimum_deal_size: 5, maximum_deal_size: 50
   - Record 3: capital_position: "Mezzanine", loan_type: "Bridge", minimum_deal_size: 5, maximum_deal_size: 50
   - Record 4: capital_position: "Mezzanine", loan_type: "Construction", minimum_deal_size: 5, maximum_deal_size: 50

🔍 **DEAL SIZE PARSING PATTERNS:**

**Range Patterns:**
- "$1M - $10M" → min: 1, max: 10
- "$500K - $5M" → min: 0.5, max: 5
- "$100M - $1B" → min: 100, max: 1000

**Complex Patterns:**
- "$10M - $100M Bridge/$15M - $100M Construction / 25M+" → create multiple records for each combination
    - "$10M - $100M Bridge" → min: 10, max: 100
    - "$15M - $100M Construction" → min: 15, max: 100
    - "25M+" → min: 25, max: 10000 ( 10 Billion) [ For Default options we will use 25M as min and 10 Billion as max]
- "$10M - $100M {Capitial_position}/$15M - $100M {Loan_type} / 25M+ {property_type}" → create multiple records For different values of the capital position, loan type and property type if any of the loan types do not have the specific metric deatials and they are related based on mapping they should be in the same record.
    - "$10M - $100M {Capitial_position}" → min: 10, max: 100 For the capital postion value only 
    - "$15M - $100M {Loan_type}" → min: 15, max: 100 For the loan type value only 
    - "25M+ {property_type}" → min: 25, max: 10000 ( 10 Billion) For the property type value only 
- "$0 - $20M" → min: 0, max: 20 (handle zero values)
- "$25M+" → min: 25, max: 10000 ( 10 Billion)

**Unit Conversions:**
- K = thousands → multiply by 0.001
- M = millions → multiply by 1  
- B = billions → multiply by 1000
- mm = millions → multiply by 1

🔍 **INTEREST RATE EXTRACTION:**

**SOFR-Based Rates:**
- "SOFR +250" → interest_rate_sofr: "SOFR +250"
- "SOFR +1000-+1300: Mezzanine" → Apply to mezzanine records only

**Other Rate Types:**
- "Prime +150" → interest_rate_prime: "Prime +150"
- "Libor +600" → interest_rate_libor: "Libor +600"
- "WSJ +250" → interest_rate_wsj: "WSJ +250"

🔍 **VALIDATION AND CONFLICT DETECTION:**

**Check for:**
1. **Invalid Combinations**: Capital positions that don't work with certain loan types
2. **Missing Data**: Required fields that couldn't be parsed
3. **Contradictory Information**: Conflicting values in the same record
4. **Out-of-Range Values**: Unrealistic percentages or amounts

### CSV Row Data
{{csv_row_data}}

### Field Mappings
{{field_mappings}}

### Central Mappings (Valid Values)
{{central_mappings}}


### Pattern of the Data
{{deal_size_patterns}}
{{interest_rate_patterns}}

### Processing Guidelines
1. **Analyze the raw data** to identify dimensions, metrics, and patterns
2. **Parse deal sizes** carefully, converting all amounts to millions
3. **Create separate records** for each valid dimension combination
4. **Apply general dimensions** (geography, strategies) to all records
5. **Extract and validate** all metric values (rates, fees, terms)
6. **Detect conflicts** and inconsistencies, reporting them in the conflicts array
7. **Ensure data integrity** by cross-referencing with valid central mappings

**CRITICAL PROCESSING REMINDERS:**
- Convert ALL deal sizes to millions as numeric values
- Create separate records only for explicitly mentioned or logically valid combinations
- Apply general dimensions to ALL created records
- Report any conflicts or validation issues
- Use null for missing values, not empty strings
- Ensure proper data types (numbers for numeric fields, strings for text)

**FINAL REMINDER: Process the data systematically using the dimension-metric framework, create appropriate multiple records based on the examples shown, and ensure all financial values are properly converted and validated.**`

// Template function for programmatic use
export const INVESTMENT_CRITERIA_USER_TEMPLATE_FUNCTION = (
  csvRowData: Record<string, any>,
  fieldMappings: Record<string, string[]>,
  centralMappings?: {
    capitalPositions: Record<string, any>,
    propertyTypes: Record<string, any>,
    propertySubCategories: Record<string, any>,
    loanTypes: string[]
  }
) => {
  // Format CSV row data
  const csvDataText = Object.entries(csvRowData)
    .map(([key, value]) => `${key}: ${value || 'null'}`)
    .join('\n')

  // Format field mappings
  const mappingsText = Object.entries(fieldMappings)
    .map(([dbField, csvFields]) => `${dbField}: [${csvFields.map(f => `'${f}'`).join(', ')}]`)
    .join('\n')

  // Format central mappings
  const centralMappingsText = centralMappings ? JSON.stringify(centralMappings, null, 2) : 'No central mappings provided'

  return INVESTMENT_CRITERIA_USER_TEMPLATE
    .replace(/\{\{csv_row_data\}\}/g, csvDataText)
    .replace(/\{\{field_mappings\}\}/g, mappingsText)
    .replace(/\{\{capital_positions\}\}/g, JSON.stringify(centralMappings?.capitalPositions || {}))
    .replace(/\{\{property_types\}\}/g, JSON.stringify(centralMappings?.propertyTypes || {}))
    .replace(/\{\{loan_types\}\}/g, JSON.stringify(centralMappings?.loanTypes || []))
    .replace(/\{\{property_sub_categories\}\}/g, JSON.stringify(centralMappings?.propertySubCategories || {}))
    .replace(/\{\{deal_size_patterns\}\}/g, JSON.stringify(DEAL_SIZE_PATTERNS))
    .replace(/\{\{interest_rate_patterns\}\}/g, JSON.stringify(INTEREST_RATE_PATTERNS))
}

// Validation function to check output structure
export const validateInvestmentCriteriaOutput = (output: any): boolean => {
  try {
    if (!output || typeof output !== 'object') return false
    if (!Array.isArray(output.investment_criteria)) return false
    if (!Array.isArray(output.conflicts)) return false
    if (!output.processing_summary || typeof output.processing_summary !== 'object') return false
    
    // Check required fields in processing summary
    const summary = output.processing_summary
    if (typeof summary.total_records_created !== 'number') return false
    if (typeof summary.dimension_combinations_found !== 'number') return false
    if (typeof summary.conflicts_detected !== 'number') return false
    if (typeof summary.deal_size_patterns_parsed !== 'number') return false
    
    return true
  } catch (error) {
    return false
  }
}

// Helper function to extract deal size patterns from raw text
export const DEAL_SIZE_PATTERNS = {
  // Range patterns: "$1M - $10M", "$500K - $5M"
  RANGE: /\$([0-9,]+(?:\.[0-9]+)?)\s*([KMB]?)\s*-\s*\$([0-9,]+(?:\.[0-9]+)?)\s*([KMB]?)/gi,
  
  // Single amount patterns: "$25M+", "$10M"
  SINGLE: /\$([0-9,]+(?:\.[0-9]+)?)\s*([KMB]?)\+?/gi,
  
  // Complex patterns with loan types: "$10M Bridge/$15M Construction"
  COMPLEX: /\$([0-9,]+(?:\.[0-9]+)?)\s*([KMB]?)\s*-?\s*\$?([0-9,]+(?:\.[0-9]+)?)?\s*([KMB]?)\s+([A-Za-z\s-]+?)(?:\/|\||$)/gi,
  
  // Unit conversion multipliers
  UNITS: {
    'K': 0.001,
    'M': 1,
    'MM': 1,
    'B': 1000,
    '': 1 // Default to millions if no unit specified
  }
}

// Helper function to parse interest rate patterns
export const INTEREST_RATE_PATTERNS = {
  SOFR: /SOFR\s*\+\s*([0-9]+(?:-\+?[0-9]+)?):?\s*([A-Za-z\s/]+)?/gi,
  PRIME: /Prime\s*\+\s*([0-9]+(?:-\+?[0-9]+)?):?\s*([A-Za-z\s/]+)?/gi,
  LIBOR: /Libor\s*\+\s*([0-9]+(?:-\+?[0-9]+)?):?\s*([A-Za-z\s/]+)?/gi,
  WSJ: /WSJ\s*\+\s*([0-9]+(?:-\+?[0-9]+)?):?\s*([A-Za-z\s/]+)?/gi,
  TREASURY_5Y: /5YT?\s*\+\s*([0-9]+(?:-\+?[0-9]+)?):?\s*([A-Za-z\s/]+)?/gi,
  TREASURY_10Y: /10YT?\s*\+\s*([0-9]+(?:-\+?[0-9]+)?):?\s*([A-Za-z\s/]+)?/gi
} 