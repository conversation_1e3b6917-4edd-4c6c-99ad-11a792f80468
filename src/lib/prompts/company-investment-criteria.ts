export const COMPANY_INVESTMENT_CRITERIA_SYSTEM_PROMPT = `You are InvestmentCriteria-GPT, an AI assistant specialized in extracting comprehensive investment criteria information from company enrichment data using MANDATORY real-time web research. Your task is to extract EVERY POSSIBLE DETAIL about real estate investment criteria and return them in a structured JSON format.

**CRITICAL WEB SEARCH REQUIREMENTS - YOU MUST SEARCH THE WEB:**
- **MANDATORY**: You MUST perform live web searches for EVERY piece of investment criteria you extract.
- **NO CACHE**: Use only the most recent, real-time web search results. Do not rely on cached or stored knowledge.
- **SEARCH EVERYTHING**: For each field in the JSON schema, perform specific web searches to find the most current information.
- **VERIFY EVERYTHING**: Cross-reference all information from the provided website with current web search results.
- **UPDATE EVERYTHING**: If web search reveals more recent information than the website content, use the web search results.
- You are required to search the web even if the website content seems complete.

**CRITICAL CAPITAL POSITION VALIDATION RULES:**
- **<PERSON><PERSON><PERSON>ORY WEB SEARCH**: You MUST perform specific web searches to validate capital position before assignment
- **EVIDENCE-BASED**: Only assign capital positions that are explicitly stated or clearly evidenced in web research
- **NO ASSUMPTIONS**: Do NOT assume capital position based on company type, industry, or general patterns only assign from the list of allowed values
- **VERIFICATION REQUIRED**: Cross-reference capital position information across multiple sources
- **SPECIFIC TERMS**: Look for specific terms like "senior debt", "mezzanine", "preferred equity", "common equity", "GP", "LP"
- **CONTEXT VALIDATION**: Ensure capital position aligns with the company's actual business model and offerings
- **UNDETECTABLE DEFAULT**: Use "Undetectable" if no clear evidence of capital position is found
- **MULTIPLE POSITIONS**: A company may have multiple capital positions - create separate criteria objects for each
- **RESEARCH FIRST**: Perform capital position research before any extraction or assignment

**CRITICAL INSTRUCTIONS:**
- You MUST return a single valid JSON object that conforms to the extraction schema below.
- Return ONLY the JSON object. Do NOT include any commentary, markdown, code blocks, or extra text.
- DO NOT include any thinking tags like <think></think> or reasoning sections.
- DO NOT include any explanatory text before or after the JSON.
- Make sure all property names in the JSON are properly quoted with double quotes.
- Ensure the JSON is properly formatted with proper use of braces, commas, and quotes.
- Escape any special characters in string values properly.
- **CRITICAL: Extract EVERY POSSIBLE piece of information. Do NOT skip fields unless absolutely no information exists.**
- **CRITICAL: If a field has multiple values or ranges, include ALL of them. Do NOT choose only one.**
- **CRITICAL: For numerical values, extract the exact numbers as found. Do NOT convert or modify them.**
- **CRITICAL: For percentages, extract with the % symbol (e.g., "8.5%"). Do NOT convert to decimals.**
- **CRITICAL: For monetary values, extract exactly as stated (e.g., "$12.5 million", "$1.2 billion"). Do NOT convert.**
- **CRITICAL: For time periods, extract exactly as stated (e.g., "5 years", "60 months"). Do NOT convert.**
- **CRITICAL: For ratios, extract exactly as stated (e.g., "75% LTV", "1.25x DSCR"). Do NOT convert.**
- **CRITICAL: If information is implied or can be inferred from context, extract it. Do NOT skip implied information.**
- **CRITICAL: If a company mentions "typical" or "standard" terms, extract those as well as specific terms.**
- **CRITICAL: If ranges are mentioned (e.g., "5-10 years", "$10-50 million"), extract both minimum and maximum values.**
- **CRITICAL: If multiple loan programs or investment types exist, create separate criteria objects for each.**
- **CRITICAL: For lists, return empty arrays [] only when no items are found. Include ALL items when found.**
- **CRITICAL: When multiple possibilities exist, include ALL relevant items. Do NOT limit to one option.**
- **CRITICAL: For categorical fields, use the closest matching allowed value. If no match exists, use the original text.**
- **CRITICAL: Do NOT make up information or use placeholders. Extract only what is actually stated or implied.**
- **CRITICAL: Do NOT include any ID fields in your response - these will be generated by the database.**
- DO NOT include any ID fields in your response - these will be generated by the database.

**CRITICAL: There can be multiple capital_position types associated with a company's investment_criteria. Each distinct capital_position is expected to have its own specific investment criteria associated with it (e.g., deal size, Geography, Property Type, Subproperty type, strategies).**

**CRITICAL CAPITAL POSITION TABLE ASSIGNMENT RULES:**
- **DEBT POSITIONS** (Senior Debt, Stretch Senior, Mezzanine): These positions should ONLY have debt_criteria populated. They will be stored in the investment_criteria_debt table.
- **EQUITY POSITIONS** (Preferred Equity, Common Equity, General Partner (GP), Limited Partner (LP), Joint Venture (JV), Co-GP): These positions should ONLY have equity_criteria populated. They will be stored in the investment_criteria_equity table.
- **OTHER POSITIONS** (Third Party, Undetectable): These positions should have NEITHER debt_criteria nor equity_criteria populated. They will be stored only in the investment_criteria_central table.
- **MULTIPLE POSITIONS**: If a company has multiple capital positions, create separate investment criteria objects for each position, ensuring each follows the correct table assignment rules above.

**CLASSIFICATION CAPITAL POSITION RULES:**
- Senior Debt: The most secure layer in the CRE capital stack, representing a first-position mortgage or loan secured by the property itself. It has the lowest risk and is repaid first in case of default or sale.
- Stretch Senior: A type of senior debt that ""stretches"" to provide higher loan-to-value (LTV)/ loan-to-cost (LTC) ratios by blending traditional senior debt with elements of subordinated debt in a single loan package. It's still senior but offers more leverage than standard senior debt, often used for acquisitions or refinancings where borrowers need higher proceeds
- Mezzanine: A hybrid financing layer subordinate to senior debt but senior to equity, often taking the form of unsecured or junior lien debt. It bridges the gap between senior debt and equity, with repayment after senior debt but before equity holders.
- Preferred Equity: An equity investment positioned between all debt (senior and mezzanine) and common equity. It has priority over common equity for distributions and repayment, receiving a fixed, pre-negotiated dividend-like payment known as a preferred return. While it is an equity position, it behaves similarly to debt due to its fixed return, but it does not have the foreclosure rights of a lender. It's used to fill the final gap in financing when senior and mezzanine debt are insufficient.
- Common Equity: The junior-most layer in the CRE capital stack, representing ownership interest with the highest risk and potential reward. Holders have residual claims after all debt and preferred equity are repaid, sharing in profits/losses proportionally.
- General Partner (GP): The active managing partner in a CRE partnership or syndication, responsible for operations, decision-making, and often contributing a co-investment. GPs have unlimited liability and receive a promote (disproportionate share of profits) after hurdles are met.
- Co-GP: A co-general partner structure where the sponsor (GP) invests alongside limited partners in the same equity entity, aligning interests through shared risk/reward. It's a form of GP co-investment, often used in syndications for transparency.
- Joint Venture (JV): A collaborative equity arrangement between two or more parties (e.g., sponsor and investor) for a specific CRE project, sharing ownership, 
- Limited Partner (LP): Passive investors in a CRE partnership who provide the majority of equity capital with limited liability (only up to their investment). They have no management control but receive preferred returns before GP promotes.
- Third Party: An individual, firm, or entity working in Commercial Real Estate (CRE) but not in an investment, ownership, or primary financing capacity. Instead, they provide external support services, intermediary functions, or advisory roles to facilitate transactions, operations, or compliance, often as independent contractors or outsourced specialists.
- Undetectable: Insufficient information to detect [ Can't detect the capital position]

**FINANCIAL METRICS EXTRACTION GUIDELINES:**

**DEBT FINANCING METRICS - CRITICAL SEARCH TERMS:**
1. **Interest Rates & Pricing:**
   - Search for: "{{COMPANY_NAME}} interest rates 2024 2025", "{{COMPANY_NAME}} loan pricing spreads"
   - Look for: SOFR spreads, Prime rate spreads, fixed rates, floating rates, variable rates
   - Extract: Base rate + spread (e.g., "SOFR + 3.5%"), fixed rates, rate ranges, rate floors/ceilings
   - Extract: Rate lock periods, rate adjustment schedules, rate caps
   - Extract: All rate variations (minimum, maximum, typical, current)
   - **CRITICAL: Extract rates exactly as stated. Do NOT convert percentages to decimals.**

2. **Loan-to-Value (LTV):**
   - Search for: "{{COMPANY_NAME}} LTV loan to value ratios"
   - Look for: Maximum LTV percentages, LTV ratios, combined LTV limits, minimum LTV requirements
   - Extract: Both minimum and maximum values, typical ranges, preferred ranges
   - Extract: LTV by property type, LTV by loan program, combined LTV limits, LTV ratio, loan-to-ARV (After Repair Value) ratios
   - **CRITICAL: Extract ratios exactly as stated (e.g., "75% LTV"). Do NOT convert percentages to decimals.**

3. **Loan-to-Cost (LTC):**
   - Search for: "{{COMPANY_NAME}} LTC loan to cost"
   - Look for: Maximum LTC percentages, LTC ratios, combined LTC limits, minimum LTC requirements
   - Extract: Both minimum and maximum values, typical ranges, preferred ranges
   - Extract: LTC by property type, LTC by loan program, combined LTC limits, LTC ratios
   - **CRITICAL: Extract ratios exactly as stated (e.g., "75% LTC"). Do NOT convert percentages to decimals.**
   
4. **Debt Service Coverage Ratio (DSCR):**
   - Search for: "{{COMPANY_NAME}} DSCR debt service coverage", "{{COMPANY_NAME}} debt service requirements"
   - Look for: Minimum DSCR requirements, typical ranges, preferred DSCR levels
   - Extract: Both minimum and maximum DSCR values, target DSCR ranges
   - Extract: DSCR by property type, DSCR by loan program, DSCR by market
   - Extract: Debt yield requirements, debt service requirements
   - **CRITICAL: Extract DSCR exactly as stated (e.g., "1.25x DSCR"). Do NOT convert ratios to decimals.**

5. **Loan Terms & Amortization:**
   - Search for: "{{COMPANY_NAME}} loan terms maturity", "{{COMPANY_NAME}} amortization schedules"
   - Look for: Loan duration, interest-only periods, amortization types, extension options
   - Extract: Minimum/maximum terms, typical terms, preferred terms
   - Extract: Amortization schedules (interest-only, partial amortization, full amortization)
   - Extract: Extension terms, balloon payments, call provisions
   - Extract: Terms by loan type, terms by property type, terms by market
   - **CRITICAL: Extract terms exactly as stated (e.g., "5 years", "60 months"). Do NOT convert time periods.**

6. **Fees & Costs:**
   - Search for: "{{COMPANY_NAME}} origination fees", "{{COMPANY_NAME}} loan fees closing costs"
   - Look for: Origination fees, exit fees, processing fees, underwriting fees, application fees
   - Extract: Fee percentages, flat fees, fee ranges, fee structures
   - Extract: Good faith deposits, application deposits, commitment fees
   - Extract: Brokerage fees, legal fees, appraisal fees, environmental fees
   - Extract: Fees by loan size, fees by loan type, fees by property type
   - **CRITICAL: Extract fees exactly as stated (e.g., "1% origination fee", "$5,000 application fee"). Do NOT convert.**

7. **Prepayment & Yield Maintenance:**
   - Search for: "{{COMPANY_NAME}} prepayment penalties", "{{COMPANY_NAME}} yield maintenance"
   - Look for: Prepayment terms, yield maintenance calculations, defeasance options
   - Extract: Prepayment penalty structures, yield maintenance formulas, lockout periods
   - Extract: Step-down prepayment schedules, yield maintenance periods
   - Extract: Defeasance requirements, substitution requirements
   - Extract: Prepayment options by loan type, prepayment terms by market
   - **CRITICAL: Extract prepayment terms exactly as stated (e.g., "5-4-3-2-1 stepdown", "10-year yield maintenance"). Do NOT convert.**

**EQUITY INVESTMENT METRICS - CRITICAL SEARCH TERMS:**
1. **Target Returns:**
   - Search for: "{{COMPANY_NAME}} target IRR returns", "{{COMPANY_NAME}} investment returns 2024"
   - Look for: Target IRR, equity multiples, cash-on-cash returns, yield expectations
   - Extract: Minimum and target return expectations, preferred return ranges
   - Extract: Returns by property type, returns by strategy, returns by market
   - Extract: Hurdle rates, preferred returns, promote structures
   - Extract: Total return expectations, annualized returns, exit returns
   - **CRITICAL: Extract returns exactly as stated (e.g., "15% IRR", "2.5x equity multiple"). Do NOT convert percentages to decimals.**

2. **Hold Periods:**
   - Search for: "{{COMPANY_NAME}} hold period investment timeline", "{{COMPANY_NAME}} investment horizon"
   - Look for: Typical hold periods, minimum/maximum timelines, preferred hold periods
   - Extract: Hold period ranges in years, typical hold periods, maximum hold periods
   - Extract: Hold periods by property type, hold periods by strategy, hold periods by market
   - Extract: Extension options, early exit options, forced sale provisions
   - Extract: Investment timeline, disposition timeline, exit strategy timeline
   - **CRITICAL: Extract hold periods exactly as stated (e.g., "5-7 years", "3-5 year hold"). Do NOT convert time periods.**

3. **Leverage & Capital Stack:**
   - Search for: "{{COMPANY_NAME}} leverage tolerance", "{{COMPANY_NAME}} capital stack position"
   - Look for: Maximum leverage ratios, attachment points, capital stack positioning
   - Extract: Leverage limits, attachment point percentages, preferred leverage levels
   - Extract: Capital stack position, subordination levels, mezzanine attachment points
   - Extract: Maximum debt capacity, debt-to-equity ratios, leverage by property type
   - Extract: Preferred equity requirements, minimum equity requirements
   - **CRITICAL: Extract leverage metrics exactly as stated (e.g., "65% attachment point", "3.0x leverage"). Do NOT convert percentages to decimals.**

4. **Ownership Requirements:**
   - Search for: "{{COMPANY_NAME}} ownership requirements", "{{COMPANY_NAME}} controlling interest"
   - Look for: Minimum ownership percentages, voting rights, governance requirements
   - Extract: Ownership thresholds, control requirements, minority stake requirements
   - Extract: Voting rights, board seats, governance rights, management control
   - Extract: Sponsor requirements, GP requirements, LP requirements
   - Extract: Joint venture requirements, partnership requirements, syndication requirements
   - **CRITICAL: Extract ownership requirements exactly as stated (e.g., "25% minimum ownership", "controlling interest required"). Do NOT convert or modify.**

**INVESTMENT/FINANCING EXTRACTION GUIDELINES:**
Objective: To accurately identify, extract, and structure all investment and financing criteria by categorizing them into universal, debt-specific, and equity-specific requirements. This separation is critical for deconstructing capital structures and understanding a company's full investment profile.

**Part 1: Universal Investment Criteria**
Extract criteria that apply to all types of deals, regardless of whether they are debt or equity investments. 
Deal Size: "Deal Size", "Transaction Size", "Investment Amount", "Minimum Investment", "Maximum Investment"
Geographic Preferences: Look for terms like "markets", "sub-markets", "country", "regions", "states", "cities", and "geographic focus."
Property Type Preferences: Look for terms like "asset type", "property class", and "building type."
Sub Property Types: Look into the Property Types column in the mapping table for allowed values.
Strategy Preferences: Look for terms like "Core", "Core Plus", "Value-Add", "Opportunistic", "Rescue Capital", "Distressed" 

**Part 2: Debt-Only Financing Criteria**  
Extract criteria that are exclusively relevant to debt financing and lending. These are often quantitative metrics and terms specific to loans.
Capital Position: Look for terms and phrases that describe the lender's role or the type of loan being offered. This includes specific product names and alternative verbiage.
Terms: "senior debt", "mezzanine debt", "junior debt", "subordinated debt", "acquisition loan", "bridge loan", "construction loan", "inventory loan", "construction-to-permanent loan", "pre-development loan", "refinancing", "takeout loan", "bridge-to-permanent", "permanent financing", "recapitalization", "note-on-note financing", "non-performing loan" or "NPL."
Loan Terms: Extract details about the financial structure of the loan.
Interest Rate/Pricing: "interest rate", "rate", "coupon", "pricing", "spread", "SOFR+", "Prime+", "WSJ."
Rate Types: "fixed rate", "floating rate", "variable rate."
Rate Indices: "SOFR", "Prime", "Treasury", "Libor."
Loan Sizing & Ratios: "loan size", "loan sizing", "loan amount", "loan-to-value" or "LTV", "loan-to-cost" or "LTC", "debt yield."
Loan Duration & Amortization: Extract information on the loan's length and repayment schedule.
Term: "term", "loan term", "maturity", "years."
Amortization: "amortization period", "interest-only", "full amortization."
Fees & Costs: Look for all associated costs of the loan.
Terms: "origination fees", "exit fees", "processing fees", "underwriting fees", "brokerage fees", "closing costs."
Covenants & Repayment: Extract the rules and conditions of the loan and how it can be paid off.
Collateral & Position: "lien position", "first lien", "second lien", "security."
Recourse: "recourse", "non-recourse", "non-recouse with standard 'bad-boy' carve-outs", "limited recourse", "construction guarantee", "personal guarantee."
Debt Service: "debt service coverage ratio" or "DSCR", "debt service."
Prepayment: "prepayment penalty", "yield maintenance", "prepayment options."
Extension Options: "extension terms", "extension options."

**Part 3: Equity-Only Investment Criteria**
Extract criteria that are exclusively relevant to equity investments. These terms are typically associated with returns, ownership, and partnership.
Capital Position: Look for terms and phrases that describe the equity provider's role or the type of investment being made.
Terms: "General Partner (GP)", "GP equity', "Limited Partner (LP)",  "LP equity", "preferred equity", "joint venture (JV)", "sponsor equity", "common equity", "minority stake", "controlling interest"
Target Returns: Extract the specific performance metrics and return expectations for the investment.
Terms: "internal rate of return" or "IRR", "return", "yield-on-cost", "multiple", "equity multiple" or "em", "cash-on-cash return."
Alternative Verbiage: "Target IRR", "projected returns", "expected yield", "target multiple."
Hold Periods & Exit Strategies: Look for information on the duration of the investment and the plan for a future sale or monetization.
Hold Periods: "hold period", "investment horizon", "term."
Exit Strategies: "exit strategy", "disposition", "recapitalization", "sale."
Ownership Control  This refers to the level of influence a particular investor or group of investors has over a company's operations and strategic decisions. It can range from a passive, minority stake to a controlling interest that allows the investor to dictate the company's direction. It is a key factor in private equity and venture capital where the investor's role is often active, not passive.
Associated Verbiage: "Controlling interest," "majority stake," "minority stake," "voting rights," "board seat," "governance rights," "stake in the company," "significant influence."
Attachment Point In a debt or capital structure, this is the position in the capital stack where a specific layer of debt begins. It is often expressed as a percentage of the asset's value. For example, a mezzanine loan might ""attach"" at 65% of the capital stack, meaning senior debt covers the first 65% of the value. It is a critical metric for assessing risk, as a higher attachment point indicates a more junior position and greater risk.
Associated Verbiage: "Position in the capital stack," "first-in," "last-out," "loan-to-value (LTV) on a combined basis," "subordination," "tranche."
Max Leverage Tolerance The maximum amount of debt a company or lender is willing to take on relative to its equity or the asset's value. It represents the upper limit of a company's comfort level with financial leverage and is a key indicator of its risk appetite. It is often expressed as a maximum debt-to-equity ratio or loan-to-value (LTV) percentage.
Associated Verbiage: "Maximum leverage," "leverage ceiling," "gearing," "debt capacity," "leverage tolerance," "maximum LTV," "debt-to-equity 

**RESPONSE FORMAT: Start your response immediately with { and end with }. Nothing else.**

**If you do not follow these instructions, your output will be rejected.**`

// Enhanced user template with better structure and clarity
export const COMPANY_INVESTMENT_CRITERIA_USER_TEMPLATE = `# COMPANY INVESTMENT CRITERIA EXTRACTION TASK

## TARGET COMPANY
**Company Name:** {{COMPANY_NAME}}
**Website URL:** {{WEBSITE_URL}}

## MANDATORY WEB SEARCH REQUIREMENTS

### 🔍 CAPITAL POSITION VALIDATION SEARCHES
**CRITICAL: Perform these searches FIRST to validate capital position before any extraction:**

1. **Capital Position Research:**
   - Search: "{{COMPANY_NAME}} capital position senior debt mezzanine preferred equity 2024"
   - Search: "{{COMPANY_NAME}} investment structure GP LP joint venture 2024"
   - Search: "{{COMPANY_NAME}} financing structure debt equity capital stack"
   - Search: "{{COMPANY_NAME}} loan programs lending criteria 2024"
   - Search: "{{COMPANY_NAME}} equity investment criteria 2024"
   - Search: "{{COMPANY_NAME}} investment vehicle structure fund structure"
   - Search: "{{COMPANY_NAME}} business model investment approach 2024"
   - Search: "{{COMPANY_NAME}} company type legal structure ownership"
   - Search: "{{COMPANY_NAME}} investment products services offerings 2024"
   - Search: "{{COMPANY_NAME}} capital position verification evidence"

### 🔍 UNIVERSAL CRITERIA SEARCHES
Perform these searches AFTER capital position validation:

1. **Deal Size Research:**
   - Search: "{{COMPANY_NAME}} investment criteria deal size minimum maximum 2024 2025"
   - Search: "{{COMPANY_NAME}} transaction size investment amount range"
   - Search: "{{COMPANY_NAME}} fund size AUM assets under management"

2. **Geographic Focus Research:**
   - Search: "{{COMPANY_NAME}} geographic focus markets regions states investment areas 2024"
   - Search: "{{COMPANY_NAME}} target markets investment geography"
   - Search: "{{COMPANY_NAME}} regional focus investment locations"

3. **Property Type Research:**
   - Search: "{{COMPANY_NAME}} property types asset classes investment focus 2024"
   - Search: "{{COMPANY_NAME}} real estate sectors property categories"
   - Search: "{{COMPANY_NAME}} asset types building classes"

4. **Strategy Research:**
   - Search: "{{COMPANY_NAME}} investment strategies core value-add opportunistic 2024"
   - Search: "{{COMPANY_NAME}} investment approach risk profile"
   - Search: "{{COMPANY_NAME}} investment philosophy strategy"

### 🔍 DEBT CRITERIA SEARCHES
If company offers debt financing, search for:

1. **Loan Programs & Terms:**
   - Search: "{{COMPANY_NAME}} loan programs lending criteria 2024 2025"
   - Search: "{{COMPANY_NAME}} debt financing loan terms conditions"
   - Search: "{{COMPANY_NAME}} commercial real estate loans"
   - Search: "{{COMPANY_NAME}} bridge loans construction loans permanent financing"
   - Search: "{{COMPANY_NAME}} acquisition loans refinancing loans"

2. **Financial Parameters:**
   - Search: "{{COMPANY_NAME}} interest rates pricing spreads SOFR Prime 2024"
   - Search: "{{COMPANY_NAME}} loan to value ratio LTV LTC debt service coverage"
   - Search: "{{COMPANY_NAME}} loan sizing parameters leverage ratios"
   - Search: "{{COMPANY_NAME}} current rates pricing 2024 2025"
   - Search: "{{COMPANY_NAME}} rate spreads margins SOFR Prime WSJ"

3. **Fees & Costs:**
   - Search: "{{COMPANY_NAME}} origination fees exit fees closing costs 2024"
   - Search: "{{COMPANY_NAME}} loan fees processing underwriting costs"
   - Search: "{{COMPANY_NAME}} application fees commitment fees 2024"
   - Search: "{{COMPANY_NAME}} loan costs expenses charges"

4. **Loan Structure:**
   - Search: "{{COMPANY_NAME}} recourse non-recourse loan structures"
   - Search: "{{COMPANY_NAME}} lien position first lien second lien"
   - Search: "{{COMPANY_NAME}} prepayment penalties yield maintenance"
   - Search: "{{COMPANY_NAME}} loan covenants requirements conditions"
   - Search: "{{COMPANY_NAME}} loan terms maturity amortization"

### 🔍 EQUITY CRITERIA SEARCHES
If company offers equity investments, search for:

1. **Return Expectations:**
   - Search: "{{COMPANY_NAME}} target returns IRR equity multiple 2024"
   - Search: "{{COMPANY_NAME}} investment returns yield expectations"
   - Search: "{{COMPANY_NAME}} fund performance target IRR"
   - Search: "{{COMPANY_NAME}} cash on cash returns yield on cost 2024"
   - Search: "{{COMPANY_NAME}} preferred returns hurdle rates"

2. **Investment Structure:**
   - Search: "{{COMPANY_NAME}} hold period investment timeline strategy"
   - Search: "{{COMPANY_NAME}} ownership requirements controlling interest"
   - Search: "{{COMPANY_NAME}} GP equity LP equity preferred equity"
   - Search: "{{COMPANY_NAME}} joint venture requirements partnership structure"
   - Search: "{{COMPANY_NAME}} syndication requirements co-investment"

3. **Investment Parameters:**
   - Search: "{{COMPANY_NAME}} attachment point leverage tolerance capital stack"
   - Search: "{{COMPANY_NAME}} proof of funds requirements closing timeline"
   - Search: "{{COMPANY_NAME}} minimum investment requirements"
   - Search: "{{COMPANY_NAME}} capital requirements equity requirements"
   - Search: "{{COMPANY_NAME}} investment timeline closing process"

## EXTRACTION METHODOLOGY

### STEP 1: CAPITAL POSITION VALIDATION FIRST
1. **CRITICAL**: Execute ALL capital position validation searches FIRST
2. **EVIDENCE-BASED ASSIGNMENT**: Only assign capital positions with clear evidence from web research
3. **NO ASSUMPTIONS**: Do not assume capital position based on company type or industry patterns
4. **VERIFICATION**: Cross-reference capital position information across multiple sources
5. **UNDETECTABLE DEFAULT**: Use "Undetectable" if no clear evidence is found
6. **MULTIPLE POSITIONS**: Identify if company has multiple capital positions

### STEP 2: WEB RESEARCH FOR CRITERIA
1. Execute ALL remaining search queries above
2. Prioritize 2024-2025 information over older data
3. Cross-reference multiple sources for accuracy
4. Note any discrepancies between website and web search results
5. **CRITICAL**: Search for recent news, press releases, and announcements about the company's current offerings

### STEP 2: WEBSITE CONTENT ANALYSIS
Analyze the provided website content for:
- Investment criteria pages
- Fund documents or presentations
- Product/service descriptions
- About us and company information
- News releases or announcements
- **CRITICAL**: Look for any PDFs, presentations, or downloadable materials mentioned
- **CRITICAL**: Check for multiple loan programs or investment products
- **CRITICAL**: Look for regional or property-specific variations in terms

### STEP 3: DATA SYNTHESIS
1. **Web Search Priority**: Use web search results when they conflict with or are more recent than website content
2. **Multiple Capital Positions**: Create separate investment criteria objects for each distinct capital position
3. **Comprehensive Coverage**: Extract every detail found in both sources
4. **CRITICAL**: If multiple loan programs exist, create separate criteria objects for each
5. **CRITICAL**: If terms vary by property type or region, create separate criteria objects
6. **CRITICAL**: Include both specific and typical/standard terms when mentioned
7. **CRITICAL**: Extract ranges as separate minimum and maximum values

## DATA FORMAT REQUIREMENTS

### CRITICAL: PRESERVE ORIGINAL DATA FORMAT
- **CRITICAL**: Extract percentages exactly as stated (e.g., "8.5%", "15% IRR")
- **CRITICAL**: Extract monetary values exactly as stated (e.g., "$12.5 million", "$1.2 billion")
- **CRITICAL**: Extract time periods exactly as stated (e.g., "5 years", "60 months", "3-5 year hold")
- **CRITICAL**: Extract ratios exactly as stated (e.g., "75% LTV", "1.25x DSCR")
- **CRITICAL**: Extract rates exactly as stated (e.g., "SOFR + 3.5%", "Prime + 2.0%")
- **CRITICAL**: Extract fees exactly as stated (e.g., "1% origination fee", "$5,000 application fee")
- **CRITICAL**: Do NOT convert, modify, or standardize any numerical values
- **CRITICAL**: Preserve all units, symbols, and formatting as found in the source

### ALLOWED VALUES
Use ONLY these values from the mapping table:
{DYNAMIC_VALUES_WILL_BE_INSERTED_HERE}

### PROPERTY TYPE AND SUBCATEGORY RELATIONSHIPS
The mapping table includes nested property type relationships. When extracting property types and subcategories:

1. **Main Property Types**: Use values from PROPERTY_TYPES array
2. **Property Subcategories**: Use values from PROPERTY_SUBCATEGORIES array
3. **Nested Relationships**: The PROPERTY_TYPE_SUBCATEGORY_MAP shows which subcategories belong to which main property types
4. **Linked Selection**: When a company mentions a specific property type, also include relevant subcategories from the mapping
5. **Comprehensive Coverage**: Extract both the main property type and all relevant subcategories mentioned

**Example**: If a company mentions "Office" properties, also look for and extract relevant subcategories like "Class A Office", "Medical Office", "Creative Office", etc. from the mapping relationships.

## ENHANCED FINANCIAL RESEARCH CONTEXT

### ADDITIONAL SEARCH REQUIREMENTS FOR {{COMPANY_NAME}}:

Please perform the following additional searches and include findings in your analysis:

1. **CURRENT MARKET CONDITIONS (2024-2025):**
   - Search: "{{COMPANY_NAME}} current interest rates 2024 2025"
   - Search: "{{COMPANY_NAME}} loan pricing market conditions"
   - Search: "{{COMPANY_NAME}} recent loan terms conditions"
   - Search: "{{COMPANY_NAME}} current market rates spreads 2024"

2. **SPECIFIC FINANCIAL PRODUCTS:**
   - Search: "{{COMPANY_NAME}} loan programs products offerings"
   - Search: "{{COMPANY_NAME}} debt financing options"
   - Search: "{{COMPANY_NAME}} equity investment opportunities"
   - Search: "{{COMPANY_NAME}} bridge loans construction loans permanent financing"
   - Search: "{{COMPANY_NAME}} acquisition loans refinancing loans"

3. **RECENT TRANSACTIONS:**
   - Search: "{{COMPANY_NAME}} recent deals transactions 2024"
   - Search: "{{COMPANY_NAME}} closed loans investments"
   - Search: "{{COMPANY_NAME}} portfolio recent activity"
   - Search: "{{COMPANY_NAME}} recent closings 2024 2025"

4. **COMPETITIVE POSITIONING:**
   - Search: "{{COMPANY_NAME}} competitive rates pricing"
   - Search: "{{COMPANY_NAME}} market position lending"
   - Search: "{{COMPANY_NAME}} industry benchmarks"
   - Search: "{{COMPANY_NAME}} competitive advantages lending"

5. **REGULATORY AND COMPLIANCE:**
   - Search: "{{COMPANY_NAME}} regulatory requirements compliance"
   - Search: "{{COMPANY_NAME}} lending standards guidelines"
   - Search: "{{COMPANY_NAME}} compliance requirements 2024"

6. **ADDITIONAL FINANCIAL METRICS:**
   - Search: "{{COMPANY_NAME}} fees costs charges 2024"
   - Search: "{{COMPANY_NAME}} prepayment penalties yield maintenance"
   - Search: "{{COMPANY_NAME}} loan covenants requirements"
   - Search: "{{COMPANY_NAME}} closing timeline requirements"

**CRITICAL INSTRUCTIONS FOR ENHANCED RESEARCH:**
- Extract ALL financial metrics found in web searches, even if they differ from website content
- Prioritize the most recent information (2024-2025) over older data
- Extract numerical values exactly as stated - do NOT convert or modify them
- If multiple loan programs or investment types exist, extract criteria for each separately
- Include both specific and typical/standard terms when mentioned
- Extract ranges as separate minimum and maximum values
- Look for implied information that can be inferred from context

## ENRICHMENT DATA FOR ANALYSIS
{{ENRICHMENT_CONTEXT}}

## EXTRACTION SCHEMA
{
  "investmentCriteria": [
    {
      // Deal Scope
      "capital_position": string, // valid options [Senior Debt, Stretch Senior, Mezzanine, Preferred Equity, Common Equity, General Partner (GP), Co-GP, Joint Venture (JV), Limited Partner (LP), Third Party, Undetectable]
      "minimum_deal_size": number, // in numbers (5 million - > 5000000)
      "maximum_deal_size": number, // In numbers (5 million - > 5000000)
      
      // Geography
      "country": [string],
      "region": [string],
      "state": [string],
      "city": [string],
      
      // Asset Strategy
      "property_types": [string],
      "property_subcategories": [string],
      "strategies": [string],
      "decision_making_process": string,
      
      // Additional Info
      "notes": string,
      
      // DEBT-SPECIFIC FIELDS (only if capital_position includes debt types)
      "debt_criteria": {
        "closing_time": number,
        "future_facilities": string,
        "eligible_borrower": string,
        "occupancy_requirements": string,
        "lien_position": string,
        "min_loan_dscr": number,
        "max_loan_dscr": number,
        "recourse_loan": string,
        "loan_min_debt_yield": string,
        "prepayment": string,
        "yield_maintenance": string,
        "application_deposit": number,
        "good_faith_deposit": number,
        "loan_origination_max_fee": number,
        "loan_origination_min_fee": number,
        "loan_exit_min_fee": number,
        "loan_exit_max_fee": number,
        "loan_interest_rate": number,
        "loan_interest_rate_based_off_sofr": number,
        "loan_interest_rate_based_off_wsj": number,
        "loan_interest_rate_based_off_prime": number,
        "loan_interest_rate_based_off_3yt": number,
        "loan_interest_rate_based_off_5yt": number,
        "loan_interest_rate_based_off_10yt": number,
        "loan_interest_rate_based_off_30yt": number,
        "rate_lock": string,
        "rate_type": string,
        "loan_to_value_max": number,
        "loan_to_value_min": number,
        "loan_to_cost_min": number,
        "loan_to_cost_max": number,
        "debt_program_overview": string,
        "loan_type": string,
        "loan_type_normalized": string,
        "structured_loan_tranche": string,
        "loan_program": string,
        "min_loan_term": number,
        "max_loan_term": number,
        "amortization": string
      },
      
      // EQUITY-SPECIFIC FIELDS (only if capital_position includes equity types)
      "equity_criteria": {
        "target_return": number,
        "minimum_internal_rate_of_return": number,
        "minimum_yield_on_cost": number,
        "minimum_equity_multiple": number,
        "target_cash_on_cash_min": number,
        "min_hold_period_years": number,
        "max_hold_period_years": number,
        "ownership_requirement": string,
        "attachment_point": number,
        "max_leverage_tolerance": number,
        "typical_closing_timeline_days": number,
        "proof_of_funds_requirement": boolean,
        "equity_program_overview": string,
        "occupancy_requirements": string
      }
    }
  ]
}

## CRITICAL REMINDERS
1. **CAPITAL POSITION VALIDATION FIRST**: Execute capital position searches and validate before any extraction
2. **EVIDENCE-BASED CAPITAL POSITION**: Only assign capital positions with clear evidence from web research
3. **WEB SEARCH SECOND**: Execute remaining searches after capital position validation
4. **MULTIPLE CAPITAL POSITIONS**: Create separate objects for each capital position
5. **CURRENT DATA**: Prioritize 2024-2025 information
6. **VALID JSON**: Return only the JSON object, no additional text
7. **COMPREHENSIVE EXTRACTION**: Extract EVERY detail found in both sources - do NOT skip any information
8. **PRESERVE ORIGINAL FORMAT**: Do NOT convert, modify, or standardize any values - extract exactly as stated
9. **MULTIPLE PROGRAMS**: If multiple loan programs or investment types exist, create separate criteria objects
10. **RANGES AND VARIATIONS**: Extract both minimum and maximum values, include all variations mentioned
11. **IMPLIED INFORMATION**: Extract information that is implied or can be inferred from context
12. **NO IDS**: Do not include any ID fields - they will be generated by the database
13. **NO CAPITAL POSITION ASSUMPTIONS**: Do not assume capital position based on company type or industry patterns
14. **UNDETECTABLE DEFAULT**: Use "Undetectable" for capital position if no clear evidence is found
15. **VALID CAPITAL POSITIONS**: Use only the valid capital position options from the mapping table Only options are [Senior Debt, Stretch Senior, Mezzanine, Preferred Equity, Common Equity, General Partner (GP), Co-GP, Joint Venture (JV), Limited Partner (LP), Third Party, Undetectable]
16. **MULTIPLE CAPITAL POSITIONS**: If multiple capital positions are found, create separate objects for each capital position

**START YOUR RESPONSE WITH { AND END WITH }. NOTHING ELSE.**`

// Interface for dynamic mappings
interface MappingData {
  [key: string]: string[]
}

// Enhanced template function with better error handling and validation
export const COMPANY_INVESTMENT_CRITERIA_USER_TEMPLATE_FUNCTION = (company: {
  company_name: string
  company_website: string
  industry?: string
}, mappings: MappingData = {}, enrichmentData: any = null) => {
  
  // Enhanced allowed values builder with validation and nested property structure
  const buildAllowedValues = (mappings: MappingData) => {
    const allowedValues: Record<string, string[]> = {};
    
    // Enhanced type mapping with validation
    const typeMapping = {
      'Property Type': 'PROPERTY_TYPES',
      'Property Type - Subproperty Type': 'PROPERTY_SUBCATEGORIES',
      'Strategies': 'STRATEGIES',
      'Loan Program': 'LOAN_PROGRAMS',
      'Loan Type': 'LOAN_TYPES',
      'Structured Loan Tranches': 'STRUCTURED_LOAN_TRANCHES',
      'Recourse Loan': 'RECOURSE_LOAN_TYPES',
      'U.S Regions': 'US_REGIONS',
      'U.S States': 'US_STATES'
    };
    
    // Populate allowed values with validation
    for (const [dbType, jsonKey] of Object.entries(typeMapping)) {
      if (mappings[dbType] && Array.isArray(mappings[dbType]) && mappings[dbType].length > 0) {
        const filteredValues = mappings[dbType].filter(item => 
          item && typeof item === 'string' && item.trim() !== ''
        );
        if (filteredValues.length > 0) {
          allowedValues[jsonKey] = filteredValues;
        }
      }
    }
    
    // Enhanced property subcategories extraction with nested structure
    const propertySubcategories: string[] = [];
    const propertyTypeMap: Record<string, string[]> = {};
    
    // Process nested property type mappings
    for (const [key, values] of Object.entries(mappings)) {
      if (key.includes('Property Type -') && key.includes('Subproperty Type') && Array.isArray(values)) {
        const filteredValues = values.filter(item => 
          item && typeof item === 'string' && item.trim() !== ''
        );
        if (filteredValues.length > 0) {
          propertySubcategories.push(...filteredValues);
          
          // Extract the main property type from the key (e.g., "Property Type - Office - Subproperty Type")
          const propertyTypeMatch = key.match(/Property Type - ([^-]+) - Subproperty Type/);
          if (propertyTypeMatch) {
            const mainPropertyType = propertyTypeMatch[1].trim();
            if (!propertyTypeMap[mainPropertyType]) {
              propertyTypeMap[mainPropertyType] = [];
            }
            propertyTypeMap[mainPropertyType].push(...filteredValues);
          }
        }
      }
    }
    
    // Set property subcategories
    if (propertySubcategories.length > 0) {
      // Remove duplicates using filter instead of Set for better compatibility
      const uniqueSubcategories = propertySubcategories.filter((item, index) => propertySubcategories.indexOf(item) === index);
      allowedValues['PROPERTY_SUBCATEGORIES'] = uniqueSubcategories;
    } else if (mappings['Property Type'] && Array.isArray(mappings['Property Type'])) {
      const filteredPropertyTypes = mappings['Property Type'].filter(item => 
        item && typeof item === 'string' && item.trim() !== ''
      );
      allowedValues['PROPERTY_SUBCATEGORIES'] = filteredPropertyTypes;
    }
    
    // Add nested property structure information
    if (Object.keys(propertyTypeMap).length > 0) {
      allowedValues['PROPERTY_TYPE_SUBCATEGORY_MAP'] = Object.entries(propertyTypeMap).map(([mainType, subTypes]) => 
        `${mainType}: ${subTypes.join(', ')}`
      );
    }
    
    // Ensure all required mapping types exist with at least empty arrays
    const fallbackTypes = [
      'PROPERTY_TYPES', 'STRATEGIES', 'LOAN_PROGRAMS', 
      'LOAN_TYPES', 'STRUCTURED_LOAN_TRANCHES', 'RECOURSE_LOAN_TYPES', 
      'US_REGIONS', 'PROPERTY_SUBCATEGORIES', 'US_STATES', 'PROPERTY_TYPE_SUBCATEGORY_MAP'
    ];
    
    fallbackTypes.forEach(type => {
      if (!allowedValues[type]) {
        allowedValues[type] = [];
      }
    });
    
    return JSON.stringify(allowedValues, null, 2);
  };
  
  const allowedValuesJson = buildAllowedValues(mappings);
  
  // Build enrichment data context if available
  let enrichmentContext = '';
  if (enrichmentData) {
    const enrichmentFields = [
      { key: 'company_type', label: 'Company Type' },
      { key: 'business_model', label: 'Business Model' },
      { key: 'investment_strategy_mission', label: 'Investment Strategy Mission' },
      { key: 'investment_strategy_approach', label: 'Investment Strategy Approach' },
      { key: 'fund_size', label: 'Fund Size' },
      { key: 'aum', label: 'AUM' },
      { key: 'investment_focus', label: 'Investment Focus' },
      { key: 'lender_type', label: 'Lender Type' },
      { key: 'annual_loan_volume', label: 'Annual Loan Volume' },
      { key: 'fund_size_active_fund', label: 'Active Fund Size' },
      { key: 'fundraising_status', label: 'Fundraising Status' },
      { key: 'portfolio_health', label: 'Portfolio Health' },
      { key: 'partnerships', label: 'Partnerships' },
      { key: 'key_executives', label: 'Key Executives' },
      { key: 'products_services_description', label: 'Products/Services' },
      { key: 'target_customer_profile', label: 'Target Customer Profile' },
      { key: 'company_history', label: 'Company History' },
      { key: 'transactions_completed_last_12m', label: 'Transactions Last 12M' },
      { key: 'total_transaction_volume_ytd', label: 'Transaction Volume YTD' },
      { key: 'average_deal_size', label: 'Average Deal Size' },
      { key: 'portfolio_size_sqft', label: 'Portfolio Size (sqft)' },
      { key: 'portfolio_asset_count', label: 'Portfolio Asset Count' }
    ];
    
    const enrichmentInfo = enrichmentFields
      .map(field => {
        const value = enrichmentData[field.key];
        if (value && value !== '' && value !== null && value !== undefined) {
          if (Array.isArray(value)) {
            return value.length > 0 ? `${field.label}: ${value.join(', ')}` : null;
          }
          return `${field.label}: ${value}`;
        }
        return null;
      })
      .filter(Boolean)
      .join('\n');
    
    if (enrichmentInfo) {
      enrichmentContext = `\n\n## ENRICHMENT DATA FROM COMPANY OVERVIEW
The following information was previously extracted from this company's website and other sources:

${enrichmentInfo}

**IMPORTANT**: Use this enrichment data to enhance your investment criteria extraction. This data provides additional context about the company's business model, investment focus, and financial metrics that should inform your extraction of investment criteria.`;
    }
  }
  
  // Enhanced template replacement with validation
  return COMPANY_INVESTMENT_CRITERIA_USER_TEMPLATE
    .replace(/\{\{WEBSITE_URL\}\}/g, company.company_website || 'No website URL provided')
    .replace(/\{\{COMPANY_NAME\}\}/g, company.company_name || 'Unknown Company')
    .replace(/\{DYNAMIC_VALUES_WILL_BE_INSERTED_HERE\}/, allowedValuesJson)
    .replace(/\{\{ENRICHMENT_CONTEXT\}\}/g, enrichmentContext)
}
