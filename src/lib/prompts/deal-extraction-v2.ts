// V2 Deal Extraction Prompt
// Focused on the new DealsV2 schema with enhanced fields
// Now with dynamic column fetching and simplified explanations

import { pool } from "@/lib/db";

// DYNAMIC SCHEMA FETCHING REQUIREMENT
//
// Before processing any deal data, you MUST dynamically fetch the current database schema for these tables:
//
// 1. investment_criteria - Main investment criteria table
// 2. investment_criteria_debt - Debt-specific investment criteria  
// 3. investment_criteria_equity - Equity-specific investment criteria (including new fields)
// 4. deal_nsf_fields - NSF field data
//
// Use the following dynamic queries with pool.query to get the current column structure:
//
// -- Get investment_criteria columns
// const investmentCriteriaColumns = await pool.query(`
//   SELECT column_name, data_type, is_nullable 
//   FROM information_schema.columns 
//   WHERE table_name = 'investment_criteria' 
//   ORDER BY ordinal_position;
// `);
//
// -- Get investment_criteria_debt columns  
// const debtColumns = await pool.query(`
//   SELECT column_name, data_type, is_nullable
//   FROM information_schema.columns 
//   WHERE table_name = 'investment_criteria_debt'
//   ORDER BY ordinal_position;
// `);
//
// -- Get investment_criteria_equity columns (including new fields)
// const equityColumns = await pool.query(`
//   SELECT column_name, data_type, is_nullable
//   FROM information_schema.columns 
//   WHERE table_name = 'investment_criteria_equity'
//   ORDER BY ordinal_position;
// `);
//
// -- Get deal_nsf_fields columns
// const nsfColumns = await pool.query(`
//   SELECT column_name, data_type, is_nullable
//   FROM information_schema.columns 
//   WHERE table_name = 'deal_nsf_fields'
//   ORDER BY ordinal_position;
// `);
//
// CRITICAL: Use the actual column names and data types from the query results

// IMPORTANT: ID HANDLING
// The AI should NEVER generate or provide any database IDs
// All IDs (primary keys, foreign keys) will be automatically generated by the database
// Focus only on providing the nested data structure and field values
// The processor will handle all ID generation and linking automatically

export async function generateDealExtractionV2Prompt(
  isMultipleFiles: boolean = false,
  mappings?: { [key: string]: string[] }
): Promise<string> {
  const fileContext = isMultipleFiles
    ? `**IMPORTANT**: You are analyzing MULTIPLE files together. Combine and synthesize information from all files to create the most complete and accurate deal profile.

**🔍 CONFLICT RESOLUTION SYSTEM:**
When you encounter conflicting data across multiple files for the same field, you MUST include a "conflicts" field in your response with detailed conflict information.

**Conflict Detection Rules:**
- Compare the same field across all files (e.g., total_nsf_net_square_foot, deal_name, strategy, etc.)
- If values differ for the same field, mark it as a conflict
- Prioritize files with more comprehensive data about other fields
- Consider document type, date, and source credibility

**Conflict Resolution Priority:**
1. **Official Documents** > summaries > drafts
2. **Recent Documents** > older documents  
3. **Detailed Breakdowns** > general statements
4. **Consistent Data** > inconsistent data
5. **Files with More Complete Information** > files with limited data

**Conflict Output Format:**
\`\`\`json
{
  "deal_name": "Downtown Mixed-Use Development",
  "total_nsf_net_square_foot": 250000,
  "conflicts": {
    "total_nsf_net_square_foot": {
      "chosen_value": 250000,
      "chosen_file": "File A - Official Pro Forma",
      "reason": "File A contains official pro forma with detailed breakdown, most reliable source",
      "alternatives": [
        {
          "value": 280000,
          "file": "File B - Preliminary Budget", 
          "context": "Early estimate before final calculations"
        },
        {
          "value": 240000,
          "file": "File C - Marketing Summary",
          "context": "Rounded for marketing purposes"
        }
      ]
    },
    "nsf_fields": {
      "sources": {
        "Senior Debt": {
          "amount": {
            "chosen_value": 20000000,
            "chosen_file": "File A - Official Pro Forma",
            "reason": "File A contains official loan terms and amounts",
            "alternatives": [
              {
                "value": 18000000,
                "file": "File B - Preliminary Budget",
                "context": "Initial loan estimate before final terms"
              }
            ]
          }
        },
        "Common Equity": {
          "amount": {
            "chosen_value": 5000000,
            "chosen_file": "File A - Official Pro Forma",
            "reason": "File A contains detailed equity breakdown",
            "alternatives": [
              {
                "value": 6000000,
                "file": "File B - Preliminary Budget",
                "context": "Higher equity requirement in budget"
              }
            ]
          }
        }
      },
      "uses": {
        "Acquisition": {
          "amount": {
            "chosen_value": 15000000,
            "chosen_file": "File A - Official Pro Forma",
            "reason": "File A contains detailed acquisition cost breakdown",
            "alternatives": [
              {
                "value": 16000000,
                "file": "File B - Preliminary Budget",
                "context": "Early acquisition estimate"
              }
            ]
          }
        },
        "Hard Cost": {
          "amount": {
            "chosen_value": 8000000,
            "chosen_file": "File A - Official Pro Forma",
            "reason": "File A contains detailed construction cost breakdown",
            "alternatives": [
              {
                "value": 7500000,
                "file": "File B - Preliminary Budget",
                "context": "Lower construction estimate"
              }
            ]
          }
        }
      }
    }
  }
}
\`\`\`

**Conflict Structure for NSF Fields:**
When conflicts exist in NSF fields, structure them to match the NSF fields format:

1. **Sources Conflicts**: Use nsf_fields.sources.{capital_position}.{field}
   - nsf_fields.sources.Senior Debt.amount - for debt amounts
   - nsf_fields.sources.Common Equity.amount - for equity amounts
   - nsf_fields.sources.Preferred Equity.amount - for preferred equity amounts

2. **Uses Conflicts**: Use nsf_fields.uses.{use_type}.{field}
   - nsf_fields.uses.Acquisition.amount - for acquisition costs
   - nsf_fields.uses.Hard Cost.amount - for construction costs
   - nsf_fields.uses.Soft Cost.amount - for soft costs

3. **Other Deal Fields**: Use actual field names
   - deal_name - deal name
   - total_nsf_net_square_foot - total NSF
   - strategy - investment strategy

**Conflict Field Structure:**
- **chosen_value**: The value you selected as most reliable
- **chosen_file**: The file name that provided the chosen value
- **reason**: Explanation of why this value was chosen over alternatives
- **alternatives**: Array of other values found with their source files and context

**Important Notes:**
- Include conflicts for ANY field where you found different values across files
- Be specific about why you chose one value over others
- Provide context for each alternative value
- If no conflicts exist, omit the "conflicts" field entirely`
    : "";

  // Fetch dynamic schema information for investment criteria tables
  let debtSchema = "";
  let equitySchema = "";
  let debtFields = "";
  let equityFields = "";
  
  try {
    // Fetch debt table schema
    const debtColumns = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'investment_criteria_debt' AND table_schema = 'public'
      ORDER BY ordinal_position
    `);
    
    // Fetch equity table schema
    const equityColumns = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'investment_criteria_equity' AND table_schema = 'public'
      ORDER BY ordinal_position
    `);
    
    // Generate JSON schema for debt
    const debtJsonStructure = debtColumns.rows.map((col: any) => {
      const isNullable = col.is_nullable === 'YES';
      const nullableSuffix = isNullable ? '|null' : '';
      
      let jsonType = 'any';
      if (col.data_type.includes('numeric') || col.data_type.includes('integer')) {
        jsonType = `number${nullableSuffix}`;
      } else if (col.data_type === 'boolean') {
        jsonType = `boolean${nullableSuffix}`;
      } else if (col.data_type.includes('text') || col.data_type.includes('character')) {
        jsonType = `string${nullableSuffix}`;
      } else if (col.data_type.includes('timestamp')) {
        jsonType = `string${nullableSuffix}`;
      }
      
      return `      "${col.column_name}": "${jsonType}"`;
    }).join(',\n');
    
    debtSchema = `{
  "investment_criteria_debt": [
    {
${debtJsonStructure}
    }
  ]
}`;
    
    // Generate JSON schema for equity
    const equityJsonStructure = equityColumns.rows.map((col: any) => {
      const isNullable = col.is_nullable === 'YES';
      const nullableSuffix = isNullable ? '|null' : '';
      
      let jsonType = 'any';
      if (col.data_type.includes('numeric') || col.data_type.includes('integer')) {
        jsonType = `number${nullableSuffix}`;
      } else if (col.data_type === 'boolean') {
        jsonType = `boolean${nullableSuffix}`;
      } else if (col.data_type.includes('text') || col.data_type.includes('character')) {
        jsonType = `string${nullableSuffix}`;
      } else if (col.data_type.includes('timestamp')) {
        jsonType = `string${nullableSuffix}`;
      }
      
      return `      "${col.column_name}": "${jsonType}"`;
    }).join(',\n');
    
    equitySchema = `{
  "investment_criteria_equity": [
    {
${equityJsonStructure}
    }
  ]
}`;
    
    // Generate field descriptions
    debtFields = debtColumns.rows.map((col: any) => {
      const isNullable = col.is_nullable === 'YES' ? ' (nullable)' : ' (required)';
      return `- **${col.column_name}**: ${col.data_type}${isNullable}`;
    }).join('\n');
    
    equityFields = equityColumns.rows.map((col: any) => {
      const isNullable = col.is_nullable === 'YES' ? ' (nullable)' : ' (required)';
      return `- **${col.column_name}**: ${col.data_type}${isNullable}`;
    }).join('\n');
    
  } catch (error) {
    console.warn("Failed to fetch dynamic schema, using fallback:", error);
    debtSchema = "// Fallback schema - dynamic fetching failed";
    equitySchema = "// Fallback schema - dynamic fetching failed";
    debtFields = "// Fallback fields - dynamic fetching failed";
    equityFields = "// Fallback fields - dynamic fetching failed";
  }

  // Generate mappings section if provided
  let allowedValuesSection = "";
  if (mappings) {
    allowedValuesSection = `

## 🎯 CENTRAL MAPPINGS & NORMALIZATION RULES

**ALLOWED VALUES FOR NORMALIZATION (from central mapping tables):**
\`\`\`json
${JSON.stringify(mappings, null, 2)}
\`\`\`

### 📍 CAPITAL POSITION MAPPING - CRITICAL FOR DEAL CLASSIFICATION

The **\`capital_position\`** field is critical for structuring deal information and matching capital requirements.
- You **MUST** use only the values from the "Capital Position" mapping table above
- Each capital position must map exactly to **one** value from the allowed list
- Capital position must be picked from the central mappings anything that uses capital position must be normalized from the central mappings

**Conditional Field Requirements:**
**🏦 For Debt Positions** (e.g., Senior Debt, Mezzanine, Stretch Senior Debt), include these fields:
   - Loan Amount (in raw dollars)
   - Loan Type  
   - Structured Loan Tranche
   - LTC (Loan-to-Cost) as decimal
   - LTV (Loan-to-Value) as decimal
   - Loan Term (in years)
   - Interest Rate as decimal
   - Recourse terms
   - DSCR (Debt Service Coverage Ratio)
   - Closing Time

**💰 For Equity Positions** (e.g., Common Equity, Co-GP Equity, GP Equity, LP Equity, JV Equity, Preferred Equity), include these fields:
   - Investment Amount (in raw dollars)
   - Attachment Point as decimal
   - Internal Rate of Return (IRR) as decimal
   - Equity Multiple (EM) as decimal
   - Hold Period (in years)
   - Yield on Cost

**Mapping Rules & Constraints:**

1. Capital Position & Terminology Rules
These rules are designed to ensure consistency and prevent the creation of redundant or incorrect capital position labels.
    -	**Consolidate Overlapping Terms:** If a source uses multiple terms for the same capital position (e.g., "Common Equity" and "LP Equity"), you must use the most specific and relevant term from the mapping table.
        - Reinforcement: Do not create a new position. Select from the predefined list of terms to ensure a standardized output.
    -	**Strict Adherence:** You are strictly forbidden from inventing new capital position terms. Use ONLY the predefined values from the provided mapping table.
        - Reinforcement: Any term not found in the mapping table must be ignored or captured in a separate, non-structured notes field if applicable.

2. Capital Stack & Deal Structure Rules
These rules focus on how to handle the structure of a deal, particularly when it involves multiple stages or nested components.
    -	**Single Position Rule:** A deal's capital stack can have only one instance of each specific capital position type. A deal can have both "Senior Debt" and "GP Equity," but it cannot have two separate "Senior Debt" positions.
        - Reinforcement: This ensures that each deal is represented as a single, unique, and logical capital stack.
    -	**CRITICAL: Capital Stack by Stage:** You must treat the capital stack for each stage of a deal as a distinct and separate entity. If a deal has a "Construction" stage and a subsequent "Permanent" stage, you must extract the full capital stack for each stage independently. Do not merge or combine the positions.
        - Reinforcement: Look for stage-specific headers or descriptions (e.g., “Acquisition loan details” “Bridge Loan details” "Construction Loan Details," "Permanent Financing Plan", etc.). Create a separate nested object for each stage to accurately reflect the financing changes over the project's lifecycle.

3. Financial Calculation & Logic Rules
This rule addresses how to handle the aggregation of financial figures to prevent miscalculations.
    -	**Position Math:** You MUST distinguish between a total capital position and its component parts. If a source provides a total equity value (e.g., "Common Equity of $20M") and also breaks it down by investor type (e.g., "GP of $2M" and "LP of $18M"), you must not double-count the numbers.
        - Instruction: Extract all three values, but do not sum them as separate totals. The total equity requirement is $20M.
    -	** Joint Venture as Total Equity:** The term "Joint Venture" can represent the total equity required for a deal, which may be synonymous with "Common Equity."
        - Instruction: If a source mentions a "Joint Venture amount" that is equal to the sum of GP and LP equity, treat the "Joint Venture" value as the total equity for the deal. Do not add this to the total again.
        - Example: If a document states "Joint Venture - $20M" and also mentions "GP Equity - $2M" and "LP Equity - $18M," the total equity for the deal is $20M. The Joint Venture is the total equity, not an additional layer of capital.
        - Output format: In this scenario, your output should show the GP equity and LP equity as nested components under a parent common_equity or total_equity key. This clearly shows their relationship as parts of a single whole.
    -	Joint Venture as a Component:** In some cases, a "Joint Venture" may be a portion of the total equity, not the sum. This often occurs when a company partners with an external investor for a specific percentage of the deal.
        - Instruction: If the Joint Venture amount is less than the total equity, it is a component. In this scenario, the balance of the equity (total equity minus the joint venture amount) would represent other contributions. Extract both values as distinct components of the overall capital stack.
        - Output format: In this scenario, your output should show the GP equity and LP equity as nested components under a parent common_equity or total_equity key. This clearly shows their relationship as parts of a single whole.


## 🏗️ NESTED INVESTMENT CRITERIA STRUCTURE

**CRITICAL**: The investment criteria system uses a nested structure where:

**Level 1: investment_criteria (Base Table)**
- Contains the core record linking to the deal
- Stores entity_type, entity_id, and capital_position
- Acts as the parent record for either debt or equity criteria

**Level 2: investment_criteria_debt OR investment_criteria_equity (Child Tables)**
- Each investment_criteria record links to EXACTLY ONE child record
- Never both debt and equity for the same parent
- The child record contains all the specific criteria fields

**Structure Example:**
\`\`\`json
{
  "investment_criteria": [
    {
      "capital_position": "Senior Debt",
      "investment_criteria_debt": {
        "loan_type": "Senior Debt",
        "loan_interest_rate": 0.075,
        "min_loan_term": 5,
        "max_loan_term": 10,
        "loan_to_value_max": 0.65,
        "min_loan_dscr": 1.25,
        "recourse_loan": "Non-Recourse"
      }
    },
    {
      "capital_position": "GP Equity",
      "investment_criteria_equity": {
        "equity_type": "GP Equity",
        "target_return": 0.15,
        "minimum_internal_rate_of_return": 0.12,
        "min_hold_period_years": 3,
        "max_hold_period_years": 7
      }
    }
  ]
}
\`\`\`

**Key Points:**
- Each capital position gets ONE investment_criteria record
- That record contains ONE nested child record (debt OR equity)
- The nested structure ensures proper data organization
- All fields from the child tables are available for extraction
- **NO IDs NEEDED** - Database will automatically generate all required IDs
- Focus on providing the nested structure and field values, not database relationships

### 🎯 STRATEGY MAPPING - INVESTMENT APPROACH CLASSIFICATION

The strategy field is critical for deal matching. Use ONLY values from the "Strategies" mapping table.

**Required Strategy Mapping Rules:**
🔥 **Terms that MUST map to "Opportunistic":**
  - ground up development
  - development
  - distressed
  - construction
  - rescue capital
  - special situations

🔧 **Terms that MUST map to "Value-Add":**
  - redevelopment
  - repositioning  
  - stabilization
  - renovation

**General Strategy Guidelines:**
- Look for primary strategy terms: Value-Add, Core, Core Plus, Opportunistic
- Map any unlisted strategy-related terms to the closest allowed value from mapping table
- Never create strategy terms not in the provided mapping table

### 🏢 PROPERTY TYPE MAPPING - ASSET CLASSIFICATION

Property types are essential for deal matching. Use ONLY values from the "Property Types" mapping table.

**Common Property Type Terms to Look For:**
- assemblage → map to closest allowed value
- healthcare → map to closest allowed value  
- hospitality → map to closest allowed value
- industrial → map to closest allowed value
- land → map to closest allowed value
- mixed-use → map to closest allowed value
- multifamily → map to closest allowed value
- office → map to closest allowed value
- portfolio → map to closest allowed value
- retail → map to closest allowed value
- special situation → map to closest allowed value

**Property Type Rules:**
- Map any property-related term to the closest allowed value from the mapping table
- Never invent property type terms not in the provided mapping table
- When multiple property types exist, include all relevant ones from the mapping

### 🌍 GEOGRAPHIC MAPPING

For geographic fields (country, region, state, city), use the values from respective mapping tables when available.

**🚨 CRITICAL MAPPING REQUIREMENTS:**
- For ALL categorical fields (capital_position, strategies, property_types, etc.), use ONLY exact values from the mapping tables
- Do not use hardcoded values, examples, or invented terms
- When in doubt, choose the closest match from the allowed values
- If no close match exists, use null rather than inventing a term
`;
  }

  // Dynamically fetch dealsv2 table columns from the DB
  const dealsSchemaRes = await pool.query(
    `SELECT column_name FROM information_schema.columns WHERE table_name = 'dealsv2' AND table_schema = 'public' ORDER BY ordinal_position`
  );
  const dealsColumns = dealsSchemaRes.rows.map((row: { column_name: string }) => row.column_name);

  // Dynamically fetch properties table columns for property information
  const propertiesSchemaRes = await pool.query(
    `SELECT column_name FROM information_schema.columns WHERE table_name = 'properties' AND table_schema = 'public' ORDER BY ordinal_position`
  );
  const propertiesColumns = propertiesSchemaRes.rows.map((row: { column_name: string }) => row.column_name);

  // Dynamically fetch deal_nsf_fields table columns
  const nsfSchemaRes = await pool.query(
    `SELECT column_name FROM information_schema.columns WHERE table_name = 'deal_nsf_fields' AND table_schema = 'public' ORDER BY ordinal_position`
  );
  const nsfColumns = nsfSchemaRes.rows.map((row: { column_name: string }) => row.column_name);

  return `🚨 CRITICAL: YOU MUST RESPOND ONLY IN JSON FORMAT 🚨
🚨 YOUR RESPONSE MUST BE VALID JSON ONLY 🚨
🚨 START WITH { AND END WITH } - NO OTHER TEXT, NO EXPLANATIONS, NO MARKDOWN 🚨
🚨 IF YOU DON'T RETURN VALID JSON, THE SYSTEM WILL FAIL 🚨
🚨 MAXIMUM RESPONSE LENGTH: 8000 CHARACTERS TO PREVENT TRUNCATION 🚨
🚨 TEST YOUR JSON WITH JSON.parse() BEFORE RETURNING 🚨

**JSON RESPONSE REQUIREMENTS:**
- Response must be valid JSON that can be parsed by JSON.parse()
- No markdown formatting (no \`\`\`json or \`\`\`)
- No explanatory text before or after the JSON
- No line breaks or formatting outside the JSON structure
- Must start with { and end with }
- All field names must be in double quotes
- All string values must be in double quotes
- Use null for missing values, not undefined or empty strings
- Arrays must be properly formatted with square brackets
- No trailing commas
- No comments in the JSON

- **CONCISE VALUES: Use short, clear text to stay within limits**



**🚨 MULTIPLE JSON ENFORCEMENT POINTS 🚨**
- This is the FIRST reminder: RESPOND ONLY IN JSON FORMAT
- You will see this reminder again throughout the prompt
- Every reminder reinforces the same requirement: JSON ONLY
- **CRITICAL**: Keep responses under 8000 characters to prevent truncation
- **CRITICAL**: Use concise field values, avoid verbose descriptions
- **CRITICAL**: Focus on essential data, not lengthy explanations

You are an expert real estate deal analyst specializing in extracting comprehensive deal information from real estate documents. Your task is to extract comprehensive deal information from real estate documents and structure it according to the V2 schema with simplified explanations and guidance.

**🚨 CRITICAL UNIQUENESS RULE**: Each capital position (e.g., "Senior Debt", "Common Equity", "GP Equity" map from capital position mapping table) can appear ONLY ONCE in both the NSF fields array and the investment criteria array. Each capital position should have its own separate record with its specific amount.

**🚨 RESPONSE FORMAT REQUIREMENTS 🚨**
- **ONLY return valid JSON - nothing else**
- **Maximum response length: 8000 characters**
- **Start with { and end with }**
- **No text before or after the JSON**
- **No explanations, no markdown, no formatting**
- **Just the raw JSON object**
- **CRITICAL: Keep field values concise to stay within limits**
- **CRITICAL: Use short, clear descriptions for summary fields**
- **CRITICAL: Avoid verbose explanations in processing_notes**

**CRITICAL NSF DATA CONSISTENCY REQUIREMENTS:**

1. **Single Source of Truth for NSF Data**: 
   - All NSF (Net Square Footage) calculations must be consistent across the entire document
   - The total NSF must equal the sum of all individual NSF components (residential, commercial, retail, etc.)
   - Cross-reference all NSF values mentioned in the document to ensure consistency

2. **Sources and Uses Total Matching (NSF Data validations)**:
   - The total amount from all sources must equal the total amount from all uses
   - **Common Equity Exclusion Rule**: If GP (General Partner) and LP (Limited Partner) equity are present, exclude Common Equity from the total sources calculation
   - When calculating total sources: Total = Sum of all sources - Common Equity (if GP/LP present)
   - This ensures sources and uses totals match correctly
   - Each source must have a corresponding entry in investment criteria following its rules

3. **NSF Data Validation**:
   - Verify that NSF totals match the sum of their components
   - Ensure NSF values are consistent between sources and uses sections
   - Flag any inconsistencies in NSF calculations
   - Use the most authoritative source for NSF data (usually the main property summary or budget)

3. **NSF Calculation Rules**:
   - Total NSF = Residential NSF + Commercial NSF + Retail NSF + Community Facility NSF + Other NSF
   - All NSF values should be in square feet
   - NSF values should be positive numbers
   - Round NSF values to whole numbers unless specified otherwise

4. **NSF Data Quality Checks**:
   - Ensure NSF values are reasonable for the property type and size
   - Cross-reference NSF with GSF (Gross Square Footage) to ensure logical relationships
   - Verify that NSF percentages of total make mathematical sense
   - Check that NSF-based cost calculations ($/NSF) are consistent

**CRITICAL LOCATION DATA REQUIREMENTS:**

1. **State Name Normalization**:
   - **ALWAYS** use full state names instead of abbreviations
   - Convert all state abbreviations to full names (e.g., TX → Texas, CA → California, NY → New York)
   - Common conversions: TX→Texas, CA→California, NY→New York, FL→Florida, IL→Illinois, PA→Pennsylvania, OH→Ohio, GA→Georgia, NC→North Carolina, MI→Michigan, NJ→New Jersey, VA→Virginia, WA→Washington, AZ→Arizona, MA→Massachusetts, TN→Tennessee, IN→Indiana, MO→Missouri, MD→Maryland, CO→Colorado, MN→Minnesota, WI→Wisconsin, SC→South Carolina, AL→Alabama, LA→Louisiana, KY→Kentucky, OR→Oregon, OK→Oklahoma, CT→Connecticut, UT→Utah, IA→Iowa, NV→Nevada, AR→Arkansas, MS→Mississippi, KS→Kansas, NM→New Mexico, NE→Nebraska, WV→West Virginia, ID→Idaho, HI→Hawaii, NH→New Hampshire, ME→Maine, MT→Montana, RI→Rhode Island, DE→Delaware, SD→South Dakota, ND→North Dakota, AK→Alaska, VT→Vermont, WY→Wyoming
   - **NEVER** store state abbreviations in the database
   
2. **Zip Code Extraction & Validation**:
   - **MANDATORY**: Extract zip codes for all properties with complete addresses
   - Look for zip codes in property addresses, contact information, and property details
   - If zip code is missing but city and state are provided, search for the most likely zip code based on the address
   - Common zip code patterns: 5-digit (12345), 9-digit (12345-6789), or ZIP+4 format
   - Validate zip codes are in correct format (5 digits minimum)
   - **PRIORITY**: Complete address information (street, city, state, zip) for all property records

3. **Address Completeness**:
   - Ensure all property addresses include: Street Address, City, State (full name), Zip Code
   - If any component is missing, flag it in data_quality_issues
   - Cross-reference address components for consistency
   - Use the most complete and accurate address information available

${fileContext}
${allowedValuesSection}

## 🏗️ DEAL EXTRACTION V2 - ENHANCED CAPITAL POSITION STRUCTURE

**CRITICAL NEW REQUIREMENT: Investment Criteria for Individual Capital Positions**

For each capital position (debt or equity), you MUST extract and structure investment criteria separately. This is essential for proper deal structuring and matching.

**🚨 UNIQUENESS REQUIREMENT**: Each capital position can have ONLY ONE NSF field record and ONLY ONE investment criteria record. Each capital position should have its own separate records with its specific amounts and criteria.

### 📊 INVESTMENT CRITERIA EXTRACTION RULES

**1. DEBT CAPITAL POSITIONS (Senior Debt, Mezzanine, Bridge Loan, etc.):**
For each debt position found, extract these fields into \`investment_criteria\` array with nested \`investment_criteria_debt\`:
- **capital_position**: The exact capital position name from mapping table
- **loan_type**: Type of loan (e.g., "Senior Debt", "Mezzanine", "Bridge Loan")
- **loan_type_normalized**: Normalized loan type
- **loan_interest_rate**: Interest rate as decimal
- **rate_type**: Type of rate (Fixed, Variable, etc.)
- **rate_lock**: Rate lock information
- **loan_interest_rate_based_off_sofr**: SOFR-based rate as decimal
- **loan_interest_rate_based_off_wsj**: WSJ-based rate as decimal
- **loan_interest_rate_based_off_prime**: Prime-based rate as decimal
- **loan_interest_rate_based_off_3yt**: 3Y Treasury-based rate as decimal
- **loan_interest_rate_based_off_5yt**: 5Y Treasury-based rate as decimal
- **loan_interest_rate_based_off_10yt**: 10Y Treasury-based rate as decimal
- **loan_interest_rate_based_off_30yt**: 30Y Treasury-based rate as decimal
- **min_loan_term** and **max_loan_term**: Loan term range in years
- **loan_to_value_min** and **loan_to_value_max**: LTV range as percentages
- **loan_to_cost_min** and **loan_to_cost_max**: LTC range as percentages
- **min_loan_dscr** and **max_loan_dscr**: DSCR range
- **loan_min_debt_yield**: Minimum debt yield
- **recourse_loan**: Recourse information
- **lien_position**: Lien position
- **amortization**: Amortization type
- **prepayment**: Prepayment terms
- **yield_maintenance**: Yield maintenance terms
- **application_deposit**: Application deposit amount
- **good_faith_deposit**: Good faith deposit amount
- **loan_origination_min_fee** and **loan_origination_max_fee**: Origination fee range
- **loan_exit_min_fee** and **loan_exit_max_fee**: Exit fee range
- **closing_time**: Closing time in days
- **future_facilities**: Future facilities information
- **eligible_borrower**: Eligible borrower requirements
- **occupancy_requirements**: Occupancy requirements
- **debt_program_overview**: Overview of debt program
- **structured_loan_tranche**: Structured loan tranche information
- **loan_program**: Loan program details
- **notes**: Any additional debt-specific information

**2. EQUITY CAPITAL POSITIONS (Pick from central mappings capital position table):**
For each equity position found, extract these fields into \`investment_criteria\` array with nested \`investment_criteria_equity\`:
- **capital_position**: The exact capital position name from mapping table
- **equity_type**: Type of equity (Pick from central mappings capital position table)
- **target_return**: Target return as decimal (e.g., 0.15 for 15%)
- **minimum_internal_rate_of_return**: Minimum IRR as decimal
- **minimum_yield_on_cost**: Minimum yield on cost as decimal
- **minimum_equity_multiple**: Minimum equity multiple
- **target_cash_on_cash_min**: Target cash-on-cash return as decimal
- **min_hold_period_years** and **max_hold_period_years**: Hold period range
- **ownership_requirement**: Ownership requirements ("Majority", "Minority", etc.)
- **attachment_point**: Attachment point as decimal
- **max_leverage_tolerance**: Maximum leverage tolerance as decimal
- **typical_closing_timeline_days**: Closing timeline in days
- **proof_of_funds_requirement**: Boolean for proof of funds requirement
- **equity_program_overview**: Overview of equity program
- **occupancy_requirements**: Occupancy requirements
- **notes**: Any additional equity-specific information

**NEW EQUITY FIELDS - Moved from Deal:**
- **yield_on_cost**: Yield on cost as decimal (e.g., 0.08 for 8%)
- **target_return_irr_on_equity**: Target IRR for equity as decimal
- **equity_multiple**: Target equity multiple for this position

**POSITION-SPECIFIC FIELDS (Automatically Determined):**
- **position_specific_irr**: IRR specific to this equity position (automatically becomes GP IRR if capital_position is "GP Equity"(Pick from central mappings capital position table))
- **position_specific_equity_multiple**: Equity multiple specific to this position (automatically becomes GP multiple if capital_position is "GP Equity")

**IMPORTANT**: Do NOT create separate GP/LP columns in the deal. The position-specific metrics are automatically determined by the capital position name.

**3. CAPITAL POSITION LINKING RULES:**
- Each investment criteria MUST be linked to exactly ONE capital position
- One capital position can have only ONE investment criteria (debt OR equity)
- **CRITICAL**: The \`capital_position\` field in investment criteria MUST use ONLY values from the Capital Position mapping table
- **NEVER invent or create new capital position names** - use ONLY the exact values provided in the mapping
- The \`capital_position\` field in investment criteria MUST match the \`ask_capital_position\` values from the mapping table
- Do NOT create investment criteria for "uses" - only for "sources"

**4. NSF FIELDS STRUCTURE:**
Maintain the existing NSF fields structure but ensure each NSF field has:
- **capital_position**: **MUST use ONLY exact values from Capital Position mapping table**
- **deal_type**: Either "debt" or "equity" or uses based on the position/source type
- **source_type**: **MUST use ONLY exact values from Capital Position mapping table** (same as capital_position)
- **use_type**: For uses, describe the use (e.g., "Acquisition", "Hard Cost", "Soft Cost")

**5. AUTOMATIC INVESTMENT CRITERIA CREATION:**
When you scrape a dealnsf record that is a "source" of capital (not a "use"), you MUST automatically:

**Step 1: Create the dealnsf record** as you currently do

**Step 2: Create a corresponding investment_criteria record:**
- Set capital_position to the exact value from the central mapping table
- **DO NOT provide entity_type, entity_id, or any other IDs** - these will be handled by the processor

**Step 3: Conditional Linking Based on Capital Position Type:**
- **If the capital position is a DEBT type** (e.g., "Senior Debt", "Mezzanine", "Bridge Loan"):
  - Create a new investment_criteria_debt record
  - Populate it with scraped debt-specific data
  - **DO NOT provide investment_criteria_id** - this will be automatically linked by the processor
  - Set capital_position to match the investment_criteria record
  - **NESTED STRUCTURE**: The investment_criteria record now contains the investment_criteria_debt record inside it

- **If the capital position is an EQUITY type** (e.g., Pick from central mappings capital position table):
  - Create a new investment_criteria_equity record
  - Populate it with scraped equity-specific data including the newly moved fields:
    - yield_on_cost (from deal.yieldOnCost)
    - target_return_irr_on_equity (from deal.commonEquityInternalRateOfReturnIrr)
    - equity_multiple (from deal.commonEquityEquityMultiple)
  - **Automatically set position-specific fields based on capital position:**
    - If capital_position contains "GP" → position_specific_irr becomes GP IRR, position_specific_equity_multiple becomes GP multiple
    - If capital_position contains "LP" → position_specific_irr becomes LP IRR, position_specific_equity_multiple becomes LP multiple
    - If capital_position contains "Common" → position_specific_irr becomes Common Equity IRR, position_specific_equity_multiple becomes Common Equity multiple
  - **DO NOT provide investment_criteria_id** - this will be automatically linked by the processor
  - Set capital_position to match the investment_criteria record
  - **NESTED STRUCTURE**: The investment_criteria record now contains the investment_criteria_equity record inside it

**IMPORTANT CONSTRAINTS:**
- A single InvestmentCriteria record can be linked to either an InvestmentCriteriaDebt record OR an InvestmentCriteriaEquity record, but NEVER both
- The nested structure means: investment_criteria → contains → investment_criteria_debt OR investment_criteria_equity
- **NO ID GENERATION**: Do not provide any IDs - the database and processor will handle all ID generation and linking automatically
- Do NOT create investment criteria for "uses" - only for "sources"
- All capital_position values MUST come from the central mapping table

## SIMPLIFIED EXTRACTION GUIDANCE

**CRITICAL CONCEPTS EXPLAINED:**

**🏗️ HARD COST**: This refers to the physical construction of a project, excluding land acquisition and soft costs. Hard costs include:
- Demolition 
- Construction Costs (Foundation and structural work, Framing and roofing, Mechanical, electrical, and plumbing systems, Finishes and fixtures, Labor costs for construction, Materials and equipment) 
- Indirect Hard Costs (General Conditions, GC Fee, Insurance, Contingency) 
*Values should be expressed as raw dollar amounts (e.g., 25500000 for $25.5 million)*

**💰 RAW DOLLAR VALUES**: All monetary amounts should be stored as raw dollar values for consistency:
- $14,000,000 → 14000000
- $250,000 → 250000
- $2.5 billion → 2500000000

**📊 NSF (Net Square Footage)**: The actual usable square footage after deducting common areas, walls, mechanical spaces
**🏢 GSF (Gross Square Footage)**: Total building square footage including all areas
**📐 ZFA (Zoning Floor Area)**: Maximum allowable floor area per zoning regulations

## 📋 CAPITAL STACK ANALYSIS FRAMEWORK

Use this structured approach to analyze and extract capital stack information:

### **1. Overall Deal Summary**
*(This section captures information that applies to the entire project)*

**Core Deal Elements:**
- **Executive Summary**: Brief overview of the deal
- **Property Type & Sub-type**: Use exact values from Property Types mapping table
- **Location**: City, State, Region, Country (use geographic mappings when available)
- **Strategy**: Use exact values from Strategies mapping table (e.g., Value-Add, Ground-Up Development)

**Square Footage Breakdown:**
- **Gross Square Footage (GSF)**: Total building area
- **Zoning Floor Area (ZFA)**: Maximum allowable per zoning
- **Net Square Footage (NSF)** breakdown:
  - Commercial (NSF): Total commercial space
    - Retail (NSF): Retail component
    - Community Space (NSF): Community facilities
  - Residential (NSF): Residential component
  - Parking (NSF): Parking areas

### **2. Total Project Cost / Total Capitalization**
*(Extract each component with total amount, percentage of total, and cost per square foot)*

| Component | Total Amount (raw dollars) | % of Total | per GSF | per ZFA | per NSF |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **Acquisition** | Extract as raw number | As decimal | $ per GSF | $ per ZFA | $ per NSF |
| **Hard Costs** | Extract as raw number | As decimal | $ per GSF | $ per ZFA | $ per NSF |
| **Soft Costs** | Extract as raw number | As decimal | $ per GSF | $ per ZFA | $ per NSF |
| **Financing Costs** | Extract as raw number | As decimal | $ per GSF | $ per ZFA | $ per NSF |
| **Total** | Sum all components | 1.0 (100%) | Total per GSF | Total per ZFA | Total per NSF |

### **3. Total Capital Stack**
*(Extract each funding source with total amount, percentage of total capital, and amount per square foot)*

**🚨 COMMON EQUITY EXCLUSION RULE**: If GP (General Partner) and LP (Limited Partner) equity are present, exclude Common Equity from the total sources calculation to ensure sources and uses totals match.

| Source | Total Amount (raw dollars) | % of Total | per GSF | per ZFA | per NSF |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **Senior Debt** | Extract as raw number | As decimal | $ per GSF | $ per ZFA | $ per NSF |
| **Mezzanine Debt** | Extract as raw number | As decimal | $ per GSF | $ per ZFA | $ per NSF |
| **General Partner Equity** | Extract as raw number | As decimal | $ per GSF | $ per ZFA | $ per NSF |
| **Limited Partner Equity** | Extract as raw number | As decimal | $ per GSF | $ per ZFA | $ per NSF |
| **Preferred Equity** | Extract as raw number | As decimal | $ per GSF | $ per ZFA | $ per NSF |
| **Common Equity** | Extract as raw number | As decimal | $ per GSF | $ per ZFA | $ per NSF |
| **Total** | Sum all sources (excluding Common Equity if GP/LP present) | 1.0 (100%) | Total per GSF | Total per ZFA | Total per NSF |

### **4. Equity Stack Details**
*(For each equity type mentioned, create detailed metrics - use exact Capital Position mapping values)*

**[Equity Type - use exact value from Capital Position mapping table]**
- **CRITICAL**: Use ONLY exact allowed values from Capital Position mapping table for normalization
- **DO NOT** use examples or invent new types
- **If extracted value not in mapping**: Use closest allowed value and note the mapping

**Required Equity Metrics:**
- **Amount Required**: In raw dollars
- **Target IRR**: As decimal (e.g., 0.15 for 15%)
- **Target Equity Multiple**: As decimal (e.g., 1.8 for 1.8x)
- **Hold Period**: In years
- **Yield on Cost**: As decimal (e.g., 0.07 for 7%)
- **Key Terms**: Preferred return, waterfall structure, etc.

### **5. Debt Stack Details**
*(For each debt type mentioned, create detailed metrics - use exact Capital Position mapping values)*

**[Debt Type - use exact value from Capital Position mapping table]**
- **CRITICAL**: Use ONLY exact allowed values from Capital Position mapping table for normalization
- **DO NOT** use examples or invent new types
- **If extracted value not in mapping**: Use closest allowed value and note the mapping

**Required Debt Metrics:**
- **Loan Amount**: In raw dollars
- **LTV (Loan-to-Value)**: As decimal (e.g., 0.75 for 75%)
- **LTC (Loan-to-Cost)**: As decimal (e.g., 0.80 for 80%)
- **Interest Rate**: As decimal (e.g., 0.065 for 6.5%)
- **Loan Term**: In years
- **DSCR**: Debt Service Coverage Ratio as decimal
- **Debt Yield**: As decimal if available
- **Origination Fee**: As decimal (e.g., 0.01 for 1%)
- **Exit Fee**: As decimal if applicable
- **Recourse**: Full recourse, non-recourse, etc.

## DYNAMIC DATABASE SCHEMA

**DEALS TABLE COLUMNS (DealsV2):**
${dealsColumns.join(", ")}

**PROPERTIES TABLE COLUMNS:**
${propertiesColumns.join(", ")}

**NSF FIELDS TABLE COLUMNS:**
${nsfColumns.join(", ")}

## EXTRACTION REQUIREMENTS

Extract ALL available information from the documents and structure it into the following JSON format using the dynamic schema above:

\`\`\`json
{
  // All fields from DealsV2 table - extract any relevant data for these columns
  // Use null for missing values, follow the data types from the entity definition
  
  // Key Deal Fields
  "deal_name": "string",                    // Name of the deal/project (meaningful name, not random text)
  "summary": "string",                      // Executive summary of the deal
  "ask_capital_position": ["string"],       // Array of capital positions - USE ONLY Capital Position mapping values
"capital_position": "string",            // Single capital position value (not array) for investment_criteria_central table from capital position mapping
  "ask_amount": [number],                   // Array of amounts requested (in raw dollars)
  "strategy": "string",                     // Investment strategy - USE ONLY Strategies mapping values (Core, Core Plus, Value-Add, Opportunistic)
  "deal_stage": "string",                   // Current stage of the deal
  "deal_status": "string",                  // Status of the deal
  "hold_period": number,                    // Expected hold period in years
  
  // Financial Returns & Metrics (all as decimals: 8.5% = 0.085)
  "yield_on_cost": number,
  "common_equity_internal_rate_of_return_irr": number,
  "common_equity_equity_multiple": number,
  "gp_equity_multiple": number,
  "gp_internal_rate_of_return_irr": number,
  "lp_equity_multiple": number,
  "lp_internal_rate_of_return_irr": number,
  "preferred_equity_internal_rate_of_return_irr": number,
  "preferred_equity_equity_multiple": number,
  "total_internal_rate_of_return_irr": number,
  "total_equity_multiple": number,
  
  // Square Footage & Unit Information
  "residential_nsf_net_square_foot": number,
  "retail_nsf_net_square_foot": number,
  "office_nsf_net_square_foot": number,
  "community_facility_nsf_net_square_foot": number,
  "total_nsf_net_square_foot": number,
  "num_apartment_units": number,
  "hotel_keys": number,
  "parking_sf": number,
  "parking_spots": number,
  
  // Debt & Financing (amounts in raw dollars)
  "loan_amount": number,                    // Total loan amount in raw dollars
  "interest_rate": number,                  // Interest rate as decimal (7.5% = 0.075)
  "loan_term": number,                      // Loan term in years
  "loan_to_cost_ltc": number,              // Loan-to-cost ratio as decimal
  "loan_to_value_ltv": number,             // Loan-to-value ratio as decimal
  "loan_type": "string",                   // Type of loan
  "dscr": number,                          // Debt service coverage ratio
  "recourse": "string",                    // Recourse terms
  
  // Processing & Quality Metadata
  "extraction_confidence": "string",        // "high", "medium", or "low"
  "data_quality_issues": ["string"],       // List of any data quality issues found
  "missing_critical_fields": ["string"],   // List of important missing fields
  "processing_notes": "string",            // Notes about processing
  "is_distressed": boolean,                // Whether this is a distressed deal
  "document_type": ["string"],             // Type(s) of documents processed
  "document_source": ["string"],           // Source(s) of documents
  
  // Additional fields as needed from the DealsV2 schema - include any others you find relevant
}
\`\`\`

## PROPERTY INFORMATION

Extract property information using the dynamic properties table schema:

\`\`\`json
{
  "property": {
    // Core Property Details
    "address": "string",                     // Full street address
    "city": "string",                        // City name
    "state": "string",                       // State name
    "zipcode": "string",                     // ZIP code
    "region": "string",                      // Geographic region
    "country": "string",                     // Country
    "market": "string",                      // Primary market
    "submarket": "string",                   // Submarket area
    "neighborhood": "string",                // Neighborhood
    
    // Property Classifications  
    "property_type": "string",               // Primary property type - USE ONLY Property Types mapping values
    "subproperty_type": "string",           // Property subtype
    "property_status": "string",             // Current status
    
    // Physical Characteristics
    "building_sqft": number,                 // Building square footage
    "gsf_gross_square_foot": number,         // Gross square footage
    "zfa_zoning_floor_area": number,         // Zoning floor area
    "lot_area": number,                      // Lot area in square feet
    "land_acres": number,                    // Land in acres
    "number_of_units": number,               // Number of units
    "year_built": number,                    // Year built
    "year_renovated": number,                // Year renovated
    
    // Financial & Valuation
    "appraisal_value": number,               // Appraised value (in raw dollars)
    "appraisal_value_date": "YYYY-MM-DD",   // Date of appraisal
    
    // Other fields from properties table schema as relevant
    "property_description": "string",        // Description of the property
    "floor_area_ratio": number,             // Floor area ratio
    "far": number,                          // Floor area ratio (FAR)
    "environmental_risk_score": number       // Environmental risk assessment
  }
}
\`\`\`

## NSF FIELDS - CRITICAL FOR CAPITAL STACK ANALYSIS (SOURCES/USES STRUCTURE)

**🚨 MANDATORY NSF FIELDS EXTRACTION**: You MUST create separate records for EACH funding source and cost category found in the deal using the Capital Stack Analysis Framework above.

**🚨 CRITICAL NSF FIELD REQUIREMENTS - NO NULL VALUES 🚨**

**🚨 UNIQUE CAPITAL POSITION RULE**: Each capital position can appear ONLY ONCE in the NSF fields array. Each capital position should have its own separate record with its specific amount.

**🚨 DUPLICATE PREVENTION RULES:**
- **NO DUPLICATE SOURCES**: Each "source_type" (capital position) can appear only ONCE in the sources section
- **NO DUPLICATE USES**: Each "use_type" can appear only ONCE in the uses section  
- **UNIQUE COMBINATIONS**: The combination of "source_type" + "use_type" must be unique across all NSF fields
- **CONSOLIDATE AMOUNTS**: If multiple sources mention the same capital position, combine their amounts into ONE record
- **VALIDATE UNIQUENESS**: Before returning, ensure no duplicate "source_type" or "use_type" values exist

### **SOURCES SECTION** (Where money comes from)
- **source_type**: REQUIRED field - MUST be one of these exact values from Capital Position mapping:
  * **CRITICAL**: Use ONLY exact values from the "Capital Position" mapping table above
  * **DO NOT** use hardcoded values like "Senior Debt", "Mezzanine", etc.
  * **If extracted value not in mapping**: Use closest allowed value from mapping
  * **Available values**: See the Capital Position mapping table in the CENTRAL MAPPINGS section above
  * **UNIQUE CONSTRAINT**: Each capital position (e.g., "Senior Debt", "Common Equity") can only appear once in the sources
  * **CONSOLIDATION RULE**: If multiple sources mention the same capital position, add their amounts together and create ONE record

### **USES SECTION** (How money is spent)
- **use_type**: REQUIRED field - MUST be one of these exact values:
  * "Acquisition" - for property purchase costs
  * "Hard Cost" - for construction and development costs
  * "Soft Cost" - for permits, legal, engineering costs
  * "Financing Cost" - for financing and origination costs
  * **UNIQUE CONSTRAINT**: Each use type can only appear once
  * **CONSOLIDATION RULE**: If multiple sources mention the same use type, add their amounts together and create ONE record

**🚨 VALIDATION CHECKLIST BEFORE RETURNING:**
1. **No duplicate "source_type" values** in sources section
2. **No duplicate "use_type" values** in uses section  
3. **All "source_type" values** are from the Capital Position mapping table
4. **All amounts are consolidated** for duplicate capital positions
5. **Total sources = Total uses** (excluding Common Equity if GP/LP present)

  - **NEVER use null for source_type and use_type** - can use capital positions from central mapping  

Extract NSF fields information using the dynamic schema and capital stack structure:

\`\`\`json
{
  "nsf_fields": [
    {
      // Core Classification (REQUIRED - NEVER NULL)
      "source_type": "string", // MUST be exact value from Capital Position mapping table above
      "use_type": "Acquisition|Hard Cost|Soft Cost|Financing Cost",
      
      // OLD FIELDS - Backward compatibility (keep for existing data)
      "deal_type": "debt|equity|acquisition|hard_cost|soft_cost|finance|refinance|development|repositioning|other",
      "nsf_context": "sources|uses_total",  // sources = funding sources, uses_total = how money is used
      "capital_position": "string|null",    // Capital position from central mapping table (e.g., Senior Debt, Common Equity, etc.) - use exact values from mapping table
      "nsf_context": "sources|uses_total",
      // Square Footage Measurements
      "gsf_gross_square_foot": number,              // Gross square footage
      "zfa_zoning_floor_area": number,              // Zoning floor area  
      "total_nsf_net_square_foot": number,          // Total net square footage
      
      // Financial Information (amounts in raw dollars)
      "amount": number,                             // Amount for this source/use (in raw dollars)
      "amount_per_gsf": number,                     // Amount per gross square foot
      "amount_per_nsf": number,                     // Amount per net square foot
      "amount_per_zfa": number,                     // Amount per zoning floor area
      "percentage_of_total": number,                // Percentage of total deal (as decimal)
      
      // Additional flexible data
      "additional_info": {}
    }
  ]
}
\`\`\`

**🚨 CRITICAL NSF FIELD VALIDATION 🚨**
- **source_type**: MUST be one of the exact values listed above - NEVER null
- **use_type**: MUST be one of the exact values listed above - NEVER null
- **deal_type**: MUST be one of the exact values listed above - NEVER null
- **nsf_context**: MUST be one of the exact values listed above - NEVER null
- **These fields are database constraints** - null values will cause the entire processing to fail

**🚨 STANDARDIZED FORMULAS FOR AMOUNT PER SQUARE FOOT CALCULATIONS 🚨**

**Total Deal Amount**: The sum of all values in the Sources section must equal the sum of all values in the Uses section.

**Formula 1: Amount per GSF (Gross Square Foot)**
- Formula: Amount per GSF = Total Deal Amount / Gross Square Foot
- Example: If GSF = 1,000 and Total Deal Amount = $1,250,000, then Amount per GSF = $1,250,000 / 1,000 = $1,250

**Formula 2: Amount per NSF (Net Square Foot)**
- Formula: Amount per NSF = Total Deal Amount / Net Square Foot
- Example: If NSF = 800 and Total Deal Amount = $1,250,000, then Amount per NSF = $1,250,000 / 800 = $1,562.50

**Formula 3: Amount per ZFA (Zoning Floor Area)**
- Formula: Amount per ZFA = Total Deal Amount / Zoning Floor Area
- Example: If ZFA = 1,200 and Total Deal Amount = $1,250,000, then Amount per ZFA = $1,250,000 / 1,200 = $1,041.67

**🎯 Key Requirements:**
- Calculate percentage_of_total for each component (percentages should sum to 1.0)
- **MANDATORY**: Calculate all three per-square-foot metrics (GSF, NSF, ZFA) for every source and use
- **Total Deal Amount**: Sum of all sources must equal sum of all uses
- Create separate records for each distinct funding source and cost category
- Use exact predefined values for source_type and use_type fields

**NSF FIELDS EXTRACTION EXAMPLES:**

**🚨 UNIQUENESS EXAMPLE**: Each capital position appears ONLY ONCE in the sources section. If multiple sources mention the same position, combine them into one record.

**Example 1 - Sources (Funding Sources):**
- Senior Debt: $20M (67% of total) → {"source_type": "[Exact value from Capital Position mapping]", "deal_type": "debt", "nsf_context": "sources", "amount": 20000000, "percentage_of_total": 0.67, "amount_per_gsf": 2000, "amount_per_nsf": 2500, "amount_per_zfa": 1667, ...}
- General Partner (GP): $5M (17% of total) → {"source_type": "[Exact value from Capital Position mapping]", "deal_type": "equity", "nsf_context": "sources", "amount": 5000000, "percentage_of_total": 0.17, "amount_per_gsf": 500, "amount_per_nsf": 625, "amount_per_zfa": 417, ...}
- Limited Partner (LP): $5M (17% of total) → {"source_type": "[Exact value from Capital Position mapping]", "deal_type": "equity", "nsf_context": "sources", "amount": 5000000, "percentage_of_total": 0.17, "amount_per_gsf": 500, "amount_per_nsf": 625, "amount_per_zfa": 417, ...}

**🚨 IMPORTANT**: If the document mentions "Senior Debt" multiple times (e.g., "Senior Debt $15M" and "Senior Debt $5M"), create separate records for each mention to maintain accuracy.

**Example 2 - Uses (Cost Categories):**
- Acquisition: $15M (50% of total) → {"use_type": "Acquisition", "deal_type": "acquisition", "nsf_context": "uses_total", "amount": 15000000, "percentage_of_total": 0.50, "amount_per_gsf": 1500, "amount_per_nsf": 1875, "amount_per_zfa": 1250, ...}
- Hard Cost: $10M (33% of total) → {"use_type": "Hard Cost", "deal_type": "hard_cost", "nsf_context": "uses_total", "amount": 10000000, "percentage_of_total": 0.33, "amount_per_gsf": 1000, "amount_per_nsf": 1250, "amount_per_zfa": 833, ...}
- Soft Cost: $3M (10% of total) → {"use_type": "Soft Cost", "deal_type": "soft_cost", "nsf_context": "uses_total", "amount": 3000000, "percentage_of_total": 0.10, "amount_per_gsf": 300, "amount_per_nsf": 375, "amount_per_zfa": 250, ...}
- Financing Cost: $2M (7% of total) → {"use_type": "Financing Cost", "deal_type": "finance", "nsf_context": "uses_total", "amount": 2000000, "percentage_of_total": 0.07, "amount_per_gsf": 200, "amount_per_nsf": 250, "amount_per_zfa": 167, ...}

## INVESTMENT CRITERIA DEBT & EQUITY EXTRACTION

**🚨 MANDATORY CRITERIA EXTRACTION**: Extract detailed debt and equity criteria for each capital position found in the deal.

**🚨 UNIQUE CAPITAL POSITION RULE**: Each capital position can have ONLY ONE investment criteria record. Each capital position should have its own separate investment criteria record.

### Investment Criteria Debt Structure:
\`\`\`json
${debtSchema}
\`\`\`

### Investment Criteria Equity Structure:
\`\`\`json
${equitySchema}
\`\`\`

### Investment Criteria Field Descriptions:

**Debt Criteria Fields:**
${debtFields}

**Equity Criteria Fields:**
${equityFields}

**🎯 CRITERIA EXTRACTION REQUIREMENTS:**
- **DEBT CRITERIA**: Extract for each debt position (Senior Debt, Mezzanine, Bridge, etc.) - ONE record per position
- **EQUITY CRITERIA**: Extract for each equity position (Pick from central mappings capital position table) - ONE record per position
- **UNIQUE CONSTRAINT**: Each capital position can only have one investment criteria record
- **USE MAPPING TABLES**: When mappings provided, use only values from Capital Position mapping
- **COMPREHENSIVE EXTRACTION**: Extract ALL available criteria fields, use null for missing data
- **PERCENTAGES**: Store as decimals (e.g., 8.5% → 0.085)
- **AMOUNTS**: Store as raw dollar values (e.g., $1M → 1000000)

## CRITICAL EXTRACTION RULES

**🚨 JSON ENFORCEMENT REMINDER 🚨**
- This is the FOURTH reminder: RESPOND ONLY IN JSON FORMAT
- Your response must start with { and end with }
- No explanatory text, no markdown, no other formatting

**🎯 CORE REQUIREMENTS:**

1. **Comprehensive Extraction**: Extract EVERY piece of information available in the documents
2. **Monetary Values**: Store all amounts as raw dollar values (e.g., $14M → 14000000, $250K → 250000)
3. **Percentage Values**: Use decimals (e.g., 8.5% → 0.085)
4. **Date Formatting**: Use YYYY-MM-DD for dates, YYYY-MM-DDTHH:MM:SSZ for timestamps
5. **Arrays**: Create arrays for fields like ask_capital_position - include ALL mentioned positions
6. **Missing Data**: Use null for missing values, don't omit fields
7. **Deal Names**: Ensure deal_name is meaningful (not random text like "Refinance,Office")
8. **🎯 MAPPING COMPLIANCE**: When mappings are provided, use ONLY values from the mapping tables for:
   - ask_capital_position (use Capital Position mapping)
   - strategy (use Strategies mapping)
   - property_type in property section (use Property Types mapping)
   - Geographic fields (use respective geographic mappings)
9. **📋 CAPITAL STACK FRAMEWORK**: Follow the Capital Stack Analysis Framework above for structured extraction of:
   - Total Project Cost components (acquisition, hard costs, soft costs, financing)
   - Total Capital Stack sources (senior debt, mezzanine, equity types)
   - Detailed equity and debt stack metrics with exact mapping values
10. ** Multiple Capital Raises in a Single Deal**: A single deal may involve a need to raise multiple types of capital simultaneously. In such cases, the outpout must clearly separate each capital raise. 
    -  When a source indicates a need to raise capital for more than one position (e.g., "seeking senior debt of $20M and LP equity of $5M"), you must treat each as a distinct, separate requirement. Do not 
       combine or nest these under a single parent key.

**📊 NSF FIELDS REQUIREMENTS:**
- **MANDATORY**: Create separate NSF records for EACH capital position found
- **NEVER** return empty nsf_fields array
- Each capital position = separate NSF record with proper deal_type and nsf_context
- **UNIQUE CONSTRAINT**: Each capital position can appear ONLY ONCE in the NSF fields array
- **USE MAPPING TABLES**: When mappings provided, use only values from Capital Position mapping for ask_capital_position fields

**🏦 INVESTMENT CRITERIA DEBT & EQUITY TABLES:**
- **MANDATORY**: Extract detailed debt and equity criteria for each capital position
- **DEBT CRITERIA**: For each debt position (Pick from central mappings capital position table), extract detailed loan terms, rates, fees, covenants - ONE record per position
- **EQUITY CRITERIA**: For each equity position (Pick from central mappings capital position table), extract detailed return requirements, hold periods, ownership terms - ONE record per position
- **UNIQUE CONSTRAINT**: Each capital position can have ONLY ONE investment criteria record
- **USE MAPPING TABLES**: When mappings provided, use only values from Capital Position mapping for loan_type and equity_type fields
- **STRUCTURED EXTRACTION**: Follow the detailed field mapping below for comprehensive criteria extraction

**🚨 CRITICAL CAPITAL POSITION MAPPING REQUIREMENTS 🚨**
- **NEVER invent or create new capital position names**
- **Use ONLY exact values from the Capital Position mapping table provided above**
- **If a document mentions a capital position not in the mapping table, map it to the closest allowed value**
- **All capital_position fields in investment criteria and NSF fields MUST match the mapping table exactly**
- **This is a database constraint - incorrect values will cause processing to fail**
- **UNIQUE CONSTRAINT**: Each capital position can appear ONLY ONCE in both NSF fields and investment criteria. Each position should have separate records with specific amounts.
- **Examples of what NOT to do:**
  - ❌ "Senior Debt" → "Senior Debt" (if not in mapping table)
  - ❌ "Mezzanine Financing" → "Mezzanine Financing" (if not in mapping table)
  - ❌ "GP Investment" → "GP Investment" (if not in mapping table)
  - ❌ Multiple records for the same capital position
- **Examples of what TO do:**
  - ✅ "Senior Debt" → map to closest allowed value from Capital Position mapping
  - ✅ "Mezzanine Financing" → map to closest allowed value from Capital Position mapping
  - ✅ "GP Investment" → map to closest allowed value from Capital Position mapping
  - ✅ ONE record per capital position in both NSF fields and investment criteria

**🚨 REQUIRED FIELD MAPPING - NO NULL VALUES ALLOWED 🚨**
- **deal_type mapping** (REQUIRED - never null):
  * Senior Debt/Loans → "debt"
  * Mezzanine Debt → "debt" 
  * Equity positions (GP, LP, Preferred) → "equity"
  * Property purchase → "acquisition"
  * Construction costs → "hard_cost"
  * Permits, legal, engineering → "soft_cost"
  * Loan origination → "finance"
  * Refinancing → "refinance"
  * Development activities → "development"
  * Repositioning → "repositioning"
  * **If uncertain → use "other" (never null)**

- **nsf_context mapping** (REQUIRED - never null):
  * Funding sources (debt, equity) → "sources"
  * Cost categories (acquisition, construction) → "uses_total"
  * **If uncertain → use "sources" (never null)**

**✅ DATA QUALITY:**
- Rate extraction_confidence as "high", "medium", or "low"
- List data quality issues in data_quality_issues array
- Flag missing critical fields in missing_critical_fields array
- Include processing notes for any assumptions or clarifications

**🏗️ FINANCIAL CONCEPTS EXPLAINED:**
- **Hard Costs**: Physical construction costs (foundation, framing, MEP, finishes)
- **Soft Costs**: Development costs (permits, legal, engineering, marketing)
- **Acquisition**: Land/property purchase price
- **LTC (Loan-to-Cost)**: Loan amount ÷ total development cost
- **LTV (Loan-to-Value)**: Loan amount ÷ property value
- **DSCR**: Net operating income ÷ debt service
- **IRR**: Internal rate of return (as decimal)
- **Equity Multiple**: Cash returned ÷ cash invested

**🚨 CRITICAL**: Use the dynamic database schema columns listed above. Return ONLY the JSON object, no additional text or explanations.

**🚨 FINAL JSON VALIDATION REQUIREMENTS 🚨**
- Your response MUST be parseable by JSON.parse()
- NO markdown formatting whatsoever
- NO explanatory text before or after
- NO "Here's the JSON:" or similar text
- Start immediately with { and end with }
- The entire response must be valid JSON
- **CRITICAL**: Keep your response concise - avoid verbose descriptions
- **CRITICAL**: Use short, clear field names and values

- **CRITICAL**: If you cannot extract data, return: {"error": "No extractable data found", "deal_data": {}, "property_data": {}, "nsf_fields": []}
- **CRITICAL**: Test your JSON with JSON.parse() before returning

**🚨 CRITICAL FIELD REQUIREMENTS - NO NULL VALUES ALLOWED 🚨**
- **deal_type**: MUST be one of: "debt", "equity", "acquisition", "hard_cost", "soft_cost", "finance", "refinance", "development", "repositioning", "other"
- **nsf_context**: MUST be one of: "sources", "uses_total"
- **NEVER return null for these fields** - if uncertain, use "other" for deal_type and "sources" for nsf_context
- **These fields are database constraints** - null values will cause processing to fail

**🚨 MULTIPLE JSON ENFORCEMENT POINTS THROUGHOUT PROMPT 🚨**
- This is the SECOND reminder: RESPOND ONLY IN JSON FORMAT
- You will see this reminder again at the end
- Every reminder reinforces the same requirement: JSON ONLY

**RESPONSE FORMAT**: Start immediately with { and end with }. NO OTHER TEXT ALLOWED.

**🚨 FINAL CRITICAL REMINDER 🚨**
- This is the THIRD reminder: RESPOND ONLY IN JSON FORMAT
- Start your response immediately with { and end with }
- Nothing else allowed - NO TEXT, NO EXPLANATIONS, NO MARKDOWN
- The entire response must be valid JSON that can be parsed by JSON.parse()

**🚨 FINAL NSF FIELD REMINDER 🚨**
- **deal_type**: NEVER null - use "other" if uncertain
- **nsf_context**: NEVER null - use "sources" if uncertain
- **These fields are database constraints** - null values = processing failure

**🚨 FINAL CAPITAL POSITION MAPPING REMINDER 🚨**
- **capital_position**: MUST use ONLY exact values from Capital Position mapping table
- **source_type**: MUST use ONLY exact values from Capital Position mapping table
- **ask_capital_position**: MUST use ONLY exact values from Capital Position mapping table
- **NEVER invent or create new capital position names** - this will cause processing to fail
- **UNIQUE CONSTRAINT**: Each capital position can appear ONLY ONCE in both NSF fields and investment criteria. Each position should have separate records with specific amounts.
- **These fields are database constraints** - incorrect values = processing failure

**🚨 ULTIMATE JSON ENFORCEMENT - FINAL WARNING 🚨**
- **RESPOND WITH NOTHING BUT VALID JSON**
- **START: {**
- **END: }**
- **NO TEXT BEFORE OR AFTER**
- **NO MARKDOWN**
- **JUST THE JSON OBJECT**

- **TEST WITH JSON.parse() BEFORE RETURNING**
- **IF INVALID JSON, THE SYSTEM WILL CRASH**`;
} 
