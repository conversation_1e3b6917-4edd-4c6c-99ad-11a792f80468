// Simplified Deal Extraction Map
// Focused on core fields for deal matching and analysis

import { pool } from "@/lib/db";

export async function generateDealExtractionPrompt(
  isMultipleFiles: boolean = false,
  mappings?: { [key: string]: string[] }
): Promise<string> {
  const fileContext = isMultipleFiles
    ? "**IMPORTANT**: You are analyzing MULTIPLE files together. Combine and synthesize information from all files to create the most complete and accurate deal profile."
    : "";

  let allowedValuesSection = "";
  if (mappings) {
    allowedValuesSection = `\n\n**ALLOWED VALUES FOR NORMALIZATION (from mapping table):**\n${JSON.stringify(
      mappings,
      null,
      2
    )}\n\n**CRITICAL MAPPING REQUIREMENTS:**\n-

The **\`capital_position\`** field is critical for structuring investment criteria and matching it with the capital requested.
- You **must** use only the values from the "Capital Position" mapping table.
- Each investment criteria object must be assigned exactly **one** \`capital_position\` value.

### Conditional Sub-key Requirements
The sub-keys included in the output object **must** change based on whether the position is debt or equity.
**1. For Debt Positions** (e.g., Senior Debt, Mezzanine, Stretch Senior Debt), the output may **only** include the following sub-keys:
    - "Loan Amount"
    - "Loan Type"
    - "Structured Loan Tranche"
    - "LTC (Loan-to-Cost)"
    - "LTV (Loan-to-Value)"
    - "Loan Term"
    - "Interest Rate"
    - "Recourse"
    - "DSCR" (Debt Service Coverage Ratio)
    - "Closing Time"

**2. For Equity Positions** (e.g., Common Equity, Co-GP Equity, GP Equity, LP Equity, JV Equity, Preferred Equity), the output may **only** include the following sub-keys:
    - "Investment Amount"
    - "Attachment Point"
    - "Internal Rate of Return (IRR)"
    - "Equity Multiple (EM)"
    - "Hold Period"

### Rules & Constraints
- **Consolidate Overlapping Terms:** If a deal mentions multiple, similar capital positions (e.g., both "Common Equity" and "LP Equity"), select only the single most referenced or most certain term from the mapping to represent the request.
- **Single Senior Tranche:** A deal's investment criteria should not have more than one Senior Debt position. Identify the primary senior loan and extract its details.
- **Adhere to Mapping:** Never invent or use a capital position term that is not in the provided mapping table.

## Strategy Mapping 🎯
The strategies field is critical for deal matching. You must use only the values from the "Strategies" mapping table.

### Specific Mapping Rules
Apply these required mappings first:

The following terms must be mapped to "Opportunistic":
  - 'ground up'
  - 'development'
  - 'distressed'
  - 'construction'
  - 'rescue capital'
  - 'special situations'
  
The following terms must be mapped to "Value-Add":
  - 'redevelopment'
  - 'repositioning'
  - 'stabilization'

### General Rules
Look for primary strategy terms like Value-Add, Core, Core Plus, and Opportunistic.
For any other strategy-related term not covered by the specific rules, map it to the closest allowed value from the mapping table.
Never invent or use a strategy term that is not in the provided mapping table.

## Property Type Mapping 🏢
The property_types field is essential for deal matching.
•	You must use only the values from the "Property Types" mapping table.
•	Look for propety type terms:
  - "assemblage" 
  - "healthcare"
  - "hospitality"
  - "industrial"
  - "land"
  - "mixed-use"
  - "multifamily"
  - "office"
  - "portfolio"
  - "retail"
  - "special situation"
•	Map any property-related term to the closest allowed value from the mapping.
•	Never invent or use a property type term that is not in the provided mapping table.
`;}

  // Dynamically fetch investment_criteria columns from the DB
  const schemaRes = await pool.query(
    `SELECT column_name FROM information_schema.columns WHERE table_name = 'investment_criteria' AND table_schema = 'public' ORDER BY ordinal_position`
  );
  const investmentCriteriaColumns = schemaRes.rows.map((row: { column_name: string }) => row.column_name).join(", ");

  return `🚨 CRITICAL: YOU MUST RESPOND ONLY IN JSON FORMAT 🚨
Your response MUST start with { and end with }. NO OTHER TEXT ALLOWED.

You are an expert real estate deal analyst specializing in extracting comprehensive deal information and investment criteria from real estate documents. Extract EVERY POSSIBLE detail including deal sizes, loan amounts, financial metrics, property details, and investment preferences.

**DEAL SIMPLIFIED EXTRACTION GUIDANCE:**

  **GENERAL DIMENSIONS** (Capital Position/Geographic/Strategic - applied to all records):
  - Capital_Position: Equity Positions** (e.g., Common Equity, Co-GP Equity, GP Equity, LP Equity, JV Equity, Preferred Equity) For Debt Positions** (e.g., Senior Debt, Mezzanine, Stretch Senior Debt)
  - Geographic: Country, Region, State, City
  - Strategies: Core, Core Plus, Value-Add, Opportunistic 
  - Extra Fields: Tear sheets, notes, additional context

  **DEAL SIZE  PARSING RULES:**
Extract min/max from ranges: "$10M - $100M" → min: 10, max: 100
Convert units: "K" → 0.001M, "M" → 1M, "B" → 1000M
Handle complex patterns: "$250K - $10M Construction/$0 - $2.5M Long Term"
Zero values: "$0 - $15M" → min: 0, max: 15
 Deal size means the size of a deal, if lets say capital position is equity or something else from mapping the deal size will be that value not all.


**INVESTMENT CRITERIA DATABASE SCHEMA:**
The following are the columns in the investment_criteria database table. If you find information in the document that is relevant to any of these fields, include it in the output for each investmentCriteria object:

DYNAMIC INVESTMENT COLUMNS ${investmentCriteriaColumns}

**CRITICAL INVESTMENT CRITERIA EXTRACTION RULES:**

1. **CAPITAL STACK TO INVESTMENT CRITERIA MAPPING**: You MUST create separate investment criteria objects for EACH capital position found in the deal or the minimum combination required for creating a criteria. This is mandatory.

2. **STRATEGIES EXTRACTION IS MANDATORY**: You MUST extract and map investment strategies from the document. Look for(these are search patterns, can also be in mapping table, not the values themselves, use the mapping table to fill the values):
   - Strategy terms: "Value-Add", "Core", "Core Plus", "Opportunistic", "Ground-Up Development", "Acquisition", "Refinance", "Repositioning", "Stabilization"
     - The following terms must be mapped to "Opportunistic":
        - 'ground up'
        - 'development'
        - 'distressed'
        - 'construction'
        - 'rescue capital'
        - 'special situations'
    - The following terms must be mapped to "Value-Add":
        - 'redevelopment'
        - 'repositioning'
        - 'stabilization'
   - Use ONLY values from the "Strategies" mapping table provided above
   - If you find strategy information, you MUST include it in the strategies field of investment criteria objects
   - NEVER leave strategies as empty array if strategy information is found in the document

3. **CAPITAL POSITION MAPPING RULES:**
   - You MUST use the exact values from the "Capital Position" mapping table provided above
   - Look for the closest match in the mapping table for each capital stack component
   - Map common terms to allowed values:
     * Senior debt/loan → Use closest match from Capital Position mapping
     * Mezzanine/subordinate debt → Use closest match from Capital Position mapping  
     * Preferred equity → Use closest match from Capital Position mapping
     * GP/Sponsor equity → Use closest match from Capital Position mapping
     * LP/Investor equity → Use closest match from Capital Position mapping
     * Total debt (unspecified) → Use closest debt-related value from mapping
     * Total equity (unspecified) → Use closest equity-related value from mapping

4. **DEAL SIZE EXTRACTION FROM CAPITAL STACKS:**
   - For each capital position or logical combination, extract the dollar amount and use it as both minimumDealSize and maximumDealSize
   - Convert to millions: $14M → 14, $250K → 0.25, $2.5B → 2500
   - If range is given like "$5M-$10M", use 5 as minimum and 10 as maximum
   - When combining positions (e.g., GP + LP = Total Equity), sum the amounts appropriately

5. **CRITERIA CREATION APPROACH:**
   - Create investment criteria objects that capture meaningful capital requirements from the deal
   - You can create separate objects for distinct positions OR combine related positions when appropriate
   - Prioritize creating criteria with sufficient data for useful deal matching and analysis
   - Each criteria object must have a unique capitalPosition array using ONLY allowed values from the mapping table
   - Include geographic, property type, and financial information in each criteria object
   - Use the dynamic database columns listed above
   - For ALL categorical fields (capitalPosition, propertyTypes, strategies, etc.), ONLY use values from the mapping table

Try to fit these stacks into investment criteria objects, if there are multiple capital positions, create multiple investment criteria objects. and criterias for other unique combinations.

1. Overall Deal Summary
(This section is for information that applies to the entire project.)
  - Executive Summary: 
  - Property Type & Sub-type:
  - Location: (City, State, Region, Country)
  - Strategy: (e.g., Value-Add, Ground-Up Development)
  - Gross Square Footage(GSF): 
  - Zoning Floor Area (ZFA):
  - Net Square Footage (NSF):
      - Commercial (NSF):
          - Retail (NSF):
          - Community Space (NSF): 
      - Residential (NSF): 
      - Parking (NSF):
  - Total Project Cost / Total Capitalization: 
  (For each component, provide the total amount, its percentage of the total project cost, and the cost per square foot.)
      | Component | Total Amount | % of Total | per GSF | per ZFA | per NSF |
      | :--- | :--- | :--- | :--- | :--- | :--- |
      | Acquisition | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | Hard Costs | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | Soft Costs | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | Financing Costs | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | Total | $XX,XXX,XXX | 100% | $XXX | $XXX | $XXX |

  - Total Capital Stack: 
   (For each source, provide the total amount, its percentage of the total capital stack, and the amount per square foot.)
      | Source | Total Amount | % of Total | per GSF | per ZFA | per NSF |
      | :--- | :--- | :--- | :--- | :--- | :--- |
      | Senior Debt | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | Mezzanine Debt | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | General Partner Equity | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | Limited Partner Equity | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | Preferred Equity | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | Total | $XX,XXX,XXX | 100% | $XXX | $XXX | $XXX |

2. Equity Stack
(For each type of equity mentioned in the document, create a sub-section and list its specific metrics. If a type is not mentioned, omit its section.)
[Equity Type - use exact value from Capital Position mapping table]
- For all equity types, you MUST use the exact allowed values from the Capital Position mapping table provided above for normalization. Do NOT use examples or invent new types. If the extracted value is not in the allowed list, use the closest allowed value from the mapping table and log the mapping in the output.
  - Amount Required:
  - Target IRR:
  - Target Equity Multiple:
  - Hold Period:
  - Key Terms: (e.g., preferred return, waterfall structure)

3. Debt Stack
(For each type of debt mentioned, create a sub-section and list its specific metrics. If a type is not mentioned, omit its section.)
[Debt Type - use exact value from Capital Position mapping table]
- For all debt types, you MUST use the exact allowed values from the Capital Position mapping table provided above for normalization. Do NOT use examples or invent new types. If the extracted value is not in the allowed list, use the closest allowed value from the mapping table and log the mapping in the output.
- Loan Amount:
- LTV (Loan-to-Value):
- LTC (Loan-to-Cost):
- Interest Rate:
- Loan Term:
- DSCR:
- Debt Yield:
- Origination Fee:
- Exit Fee:
- Recourse:

**CRITICAL INVESTMENT CRITERIA EXTRACTION EXAMPLES:**

Example 1: If you find a detailed capital stack like:
- Senior Debt: $20M
- Mezzanine: $5M  
- GP Equity: $3M
- LP Equity: $7M

You could create separate criteria objects OR combine logical groupings:
Option A - Separate positions:
1. {"entity_type": "Deal", "capital_position": [<CLOSEST_DEBT_VALUE_FROM_MAPPING>], "minimum_deal_size": 20, "maximum_deal_size": 20, "notes": "Senior Debt", ...other fields}
2. {"entity_type": "Deal", "capital_position": [<CLOSEST_MEZZANINE_VALUE_FROM_MAPPING>], "minimum_deal_size": 5, "maximum_deal_size": 5, "notes": "Mezzanine", ...other fields}  
3. {"entity_type": "Deal", "capital_position": [<CLOSEST_EQUITY_VALUE_FROM_MAPPING>], "minimum_deal_size": 10, "maximum_deal_size": 10, "notes": "Total Equity (GP+LP)", ...other fields}

Option B - Strategic groupings:
1. {"entity_type": "Deal", "capital_position": [<CLOSEST_DEBT_VALUE_FROM_MAPPING>], "minimum_deal_size": 25, "maximum_deal_size": 25, "notes": "Total Debt (Senior+Mezz)", ...other fields}
2. {"entity_type": "Deal", "capital_position": [<CLOSEST_EQUITY_VALUE_FROM_MAPPING>], "minimum_deal_size": 10, "maximum_deal_size": 10, "notes": "Total Equity", ...other fields}

Example 2: If you only find:
- Total Debt: $25M
- Total Equity: $10M

Create 2 investment criteria objects:
1. {"entity_type": "Deal", "capital_position": [<CLOSEST_DEBT_VALUE_FROM_MAPPING>], "minimum_deal_size": 25, "maximum_deal_size": 25, "notes": "Total Debt", ...other fields}
2. {"entity_type": "Deal", "capital_position": [<CLOSEST_EQUITY_VALUE_FROM_MAPPING>], "minimum_deal_size": 10, "maximum_deal_size": 10, "notes": "Total Equity", ...other fields}

**GUIDANCE**: Create meaningful investment criteria that capture the deal's capital requirements. Combine positions when it makes logical sense, but ensure each criteria has sufficient data to be useful for matching and analysis.

**SHARED CRITERIA FIELDS**: For each investment criteria object, also include:
- entityType: "Deal"
- propertyTypes: [use exact values from Property Types mapping table]
- state: [use exact values from geographic mapping table]
- city: [use exact values from geographic mapping table]
- targetReturn: [extracted IRR if available]
- capitalSource: [sponsor/developer name]
- notes: [relevant details about the capital position]
- strategies: [use exact values from Strategies mapping table if applicable]


**CRITICAL MAPPING REQUIREMENT**: For ALL categorical/array fields in investment criteria, you MUST use only the exact values provided in the mapping tables above. Do not use hardcoded values, examples, or invented terms.

Try to fit these stacks into investment criteria objects, if there are multiple capital positions, create multiple investment criteria objects. and criterias for other unique combinations.

**CRITICAL INSTRUCTIONS:**
- You MUST return a single valid JSON object that conforms to the extraction schema below.
- Return ONLY the JSON object. Do NOT include any commentary, markdown, code blocks, or extra text.
- DO NOT include any thinking tags like <think></think> or reasoning sections.
- DO NOT include any explanatory text before or after the JSON.
- Make sure all property names in the JSON are properly quoted with double quotes.
- Ensure the JSON is properly formatted with proper use of braces, commas, and quotes.
- Escape any special characters in string values properly.
- If information for a field isn't available, use null for that field.

**CRITICAL DATA FORMATTING REQUIREMENTS:**
- For all numeric fields (including monetary and percentage fields), output a raw number only:
  - For monetary values, output the value as a number in dollars (e.g., 14000000 for $14.0 million, 12500000 for $12.5 million).
  - For percentages, output the value as a decimal (e.g., 0.085 for 8.5%).
  - DO NOT include any currency symbols, commas, units, or percent signs in numeric fields.
  - NUMERIC OVERFLOW PROTECTION: Never output numbers larger than 999999999999 (1 trillion) or smaller than -999999999999.
  - There might be only minValues or maxValues, if that is the case consider them to be both.
  - for names make sure those make sense and something like Refinance,Office and which might not be an actual name not be included in the dealName

**CRITICAL ARRAY FORMATTING REQUIREMENTS:**
- For all array fields (city, state, region, country, property_types, strategies, etc.):
  - Use ONLY simple, clean strings without special characters
  - DO NOT include commas, quotes, backslashes, or curly braces in array values
  - DO NOT include complex location strings like "Queens, New York" or "Brooklyn, New York"
  - Instead, split complex locations: "Queens, New York" → ["Queens", "New York"]
  - Keep array values under 500 characters each
  - Clean all array values: remove newlines, tabs, extra spaces, and special characters

**CRITICAL STRING FORMATTING:**
- Remove any problematic characters: {}, ", \, newlines, tabs
- Normalize whitespace: replace multiple spaces with single space

- For lists/arrays, return empty arrays [] when no items are found.
- When multiple possibilities exist, include ALL relevant items.
- For categorical fields, only use allowed values as specified in the mapping table.
- DO NOT make up information or use placeholders.
- STRICTLY enforce character-length limits by truncating excess characters (use an ellipsis … if truncation occurs) so the final JSON never exceeds the specified limits.
- Always prioritize accuracy over completeness. It's better to leave a field empty than to provide incorrect information.
- BE EXTREMELY THOROUGH in extracting investment criteria - look for deal sizes, loan amounts, interest rates, LTV ratios, fees, terms, geographic preferences, property types, and all financial metrics.
- **MANDATORY**: Create separate investment criteria objects for each capital position found in the deal.
${fileContext}
${allowedValuesSection}

🚨 JSON FORMAT REQUIRED - NO BULLET POINTS OR PLAIN TEXT ALLOWED 🚨

🚨🚨🚨 CRITICAL: YOU MUST EXTRACT INVESTMENT CRITERIA 🚨🚨🚨
- MANDATORY: Create investment criteria objects for EVERY capital position found (Senior Debt, Mezzanine, GP Equity, LP Equity, etc.(from capital position mapping table))
- NEVER return empty investmentCriteria array - always extract capital stack information
- Each capital position = separate investment criteria object
- Use exact database column names (snake_case) from the schema above

**EXTRACTION SCHEMA:**
{
  "dealName": "string", // Name of the deal or project, make sure the name is like an actual name not some random text added after or anything
  "sponsorName": "string", // Name of the deal sponsor or developer
  "address": "string", // Property address (street address, full address)
  "zipCode": "string", // Zip code of the property
  "neighborhood": "string", // Neighborhood of the property
  "propertyDescription": "string", // Description of the property
  "lotArea": "number", // Lot area in square feet
  "floorAreaRatio": "number", // Floor area ratio
  "zoningSquareFootage": "number", // Zoning square footage
  "yieldOnCost": "number", // Yield on cost as decimal
  "projectedGpEquityMultiple": "number", // Projected GP equity multiple
  "projectedGpIrr": "number", // Projected GP IRR
  "projectedLpEquityMultiple": "number", // Projected LP equity multiple
  "projectedLpIrr": "number", // Projected LP IRR
  "projectedTotalEquityMultiple": "number", // Projected total equity multiple
  "projectedTotalIrr": "number", // Projected total IRR
  "status": "string", // Deal status
  "dealStage": "string", // Current deal stage
  "priority": "string", // Priority level
  "documentType": ["string"], // Types of documents processed (array, multiple files allowed)
  "extractionConfidence": "string", // Confidence level of extraction
  "processingNotes": "string", // Notes about processing
  "extractionMethod": ["string"], // Methods used for extraction (array, multiple files allowed)
  "documentSource": ["string"], // Sources of the documents (array, multiple files allowed)
  "documentFilename": ["string"], // Original filenames (array, multiple files allowed)
  "reviewStatus": "string", // Review status
  "reviewedBy": "string", // Name of reviewer
  "reviewNotes": "string", // Notes from review
  "dataQualityIssues": "object", // Data quality issues found
  "missingCriticalFields": "object", // Missing critical fields
  "extraFields": "object", // All additional fields found in the document that are not core fields
  "investmentCriteria": [
    {
      "entity_type": "Deal", // Always "Deal"
      "entity_id": null, // Will be set by processor
      "capital_position": ["<VALUE_FROM_CAPITAL_POSITION_MAPPING>"], // Array with ONE exact value from mapping
      "minimum_deal_size": "number", // Deal size in millions (e.g., 20 for $20M)
      "maximum_deal_size": "number", // Same as minimum for exact amounts
      "property_types": ["<VALUES_FROM_PROPERTY_TYPE_MAPPING>"], // Array from mapping table
      "strategies": ["<VALUES_FROM_STRATEGIES_MAPPING>"], // Array from mapping table  
      "state": ["<VALUES_FROM_STATE_MAPPING>"], // Array from mapping table
      "city": ["<VALUES_FROM_CITY_MAPPING>"], // Array from mapping table
      "region": ["<VALUES_FROM_REGION_MAPPING>"], // Array from mapping table cross check with state, if not found in mapping table, use the nearest match
      "country": ["Mostly Gonna be US for now, but still verify if those are correct reading different location fields"]
      "target_return": "number", // IRR as decimal (e.g., 0.15 for 15%)
      "capital_source": "string", // Sponsor name
      "notes": "string", // Description of this capital position
      "created_at": "string", // Current ISO timestamp
      "updated_at": "string", // Current ISO timestamp  
      "is_active": true // Always true
      // Use other fields from ${investmentCriteriaColumns} as needed
    }
  ] // MANDATORY: Create one object per capital position found 
}

**INVESTMENT CRITERIA EXTRACTION - COMPREHENSIVE APPROACH:**
Extract ALL investment preferences, criteria, and requirements found in the document. Look for:
- Total Project Sources: Identify the total capitalization of the project, This sould equal the sum of total debt and total equity 
- Total Debt:  Calcuate and state sum of Senior Debt + Mezzanine 
- Senior Debt: Extract the specific $ amount. If not mentioned, state "Not Specified" 
- Mezzanine: Extract the Specific $ amount. If not mentioned, state "Not Specified"
- Acquisition: Should be treated as a total equity, if not mentioned, state "Not Specified", this must be a criteria with the sum of all of them making the total criteria or if mentioned in the document make a criteria for this aswell.
- General Partner (GP): Extract the specific $ amount. If not mentioned, state "Not Specified"
- Limited Partner (LP): Extract the specific $ amount. If not mentioned, state "Not Specified"
- Preferred Equity: Extract the specific $ amount. If not mentioned, state "Not Specified"
- Total Equity: Calculate and state the sum of General Partner (GP) + Limited Partner (LP) + Preferred Equity 
- IMPORTANT: If the document does not break down the equity into GP, LP, or Preferred components but provides a single, aggregated figure for "Total Equity" or "Equity," you must extract that single amount and assign it directly to the Total Equity field. In this case, the individual components (GP, LP, Preferred) should be marked as "Not Specified."
- FINANCIAL METRICS: target returns, IRR expectations, equity multiples, yield requirements
- GEOGRAPHIC PREFERENCES: target markets, states, cities, regions
- PROPERTY TYPES: asset class preferences, property categories, building types
- LOAN TERMS: interest rates, LTV ratios, loan periods, fees, DSCR requirements
- INVESTMENT STRATEGIES: acquisition vs development, core vs value-add, etc.
- CAPITAL POSITIONS: use the mapping provided for capital positions, also split criterias based on position, only a single position
- HOLD PERIODS: investment timelines, exit strategies

**CRITICAL FIELD MAPPING REQUIREMENTS:**
- Use EXACT database column names as specified in the schema below
- Total Project Sources:
- Total Debt: 
- Senior Debt: 
- Mezzanine: 
- General Partner (GP): 
- Limited Partner (LP):
- Preferred Equity: 
- Total Equity: 
- For LTV: Use "loanLtv" for main value, "loanToValueMin"/"loanToValueMax" for ranges
- For LTC: Use "loanLtc" for main value, "loanToCostMin"/"loanToCostMax" for ranges
- For interest rates: Use "loanInterestRate", "loanInterestRateSofr", "loanInterestRateWsj", "loanInterestRatePrime"
- For loan terms: Use "minLoanTerm" and "maxLoanTerm" (in months)
- For hold periods: Use "minHoldPeriod" and "maxHoldPeriod" (in months)
- Convert all percentages to decimals (e.g., 8.5% becomes 0.085)
- Convert all monetary values to raw numbers in dollars (e.g., $14M becomes 14000000)

**COMMON EXTRACTION EXAMPLES:**
- "Total Project Sources: $15,000,000"
- "Total Debt: $10,000,000"
  - "Senior Debt: $10,000,000"
  - "Mezzanine: Not Specified" 
- "Total Equity: $5,000,000" 
  - "General Partner (GP): Not Specified" 
  - "Limited Partner (LP): Not Specified" 
  - "Preferred Equity: Not Specified" 
- "LTV: up to 75%" → loanLtv: 0.75, loanToValueMax: 0.75
- "Interest rate: SOFR + 250 bps" → loanInterestRateSofr: 0.025 (if current SOFR implied)
- "Loan term: 3-5 years" → minLoanTerm: 36, maxLoanTerm: 60
- "Hold period: 5-7 years" → minHoldPeriod: 60, maxHoldPeriod: 84
- "Target return: 15-18% IRR" → targetReturn: 0.15 (use minimum for main field)
- "Markets: New York, California, Texas" → state: [use closest matches from geographic mapping table]
- "Property types: Multifamily, Office" → propertyTypes: [use exact matches from Property Types mapping table]
- **"Strategy: Value-Add(examples only use mapping)" → strategies: [use exact match from Strategies mapping table]**
- **"Investment Approach: Core Plus (examples only use mapping)" → strategies: [use exact match from Strategies mapping table]**
- **"Business Plan: Acquisition and Repositioning (examples only use mapping)" → strategies: [use exact matches from Strategies mapping table]**

Look for MULTIPLE investment criteria (different funds, strategies, products, or investment vehicles). Each distinct set of criteria should be a separate object in the investmentCriteria array.

For all numeric fields:
- For monetary values, output the value as a number in millions (e.g., 14 for $14.0 million)
- For percentages, output the value as a decimal (e.g., 0.085 for 8.5%)
- DO NOT include any currency symbols, commas, units, or percent signs


**FINANCIAL EXTRACTION GUIDELINES:**
Objective: To accurately identify, extract, and structure all financial figures from a document, with a specific focus on deconstructing the deal's capital stack.

Part 1: Initial Keyword Search
Begin by scanning the document for any monetary figures or financial keywords. This initial pass is designed to locate all potential financial data points. Look for terms including, but not limited to:
- General Deal Size: "Total Project Cost", "Total Development Cost", "Total Deal Size", "Total Sources", "Total Uses", "Total Budget", "Total Project Budget", Total Capitalization"
- Debt-Related: "Loan Amount", "Debt Amount", "financing", "note", "mortgage", "Total Loan Amount", "Senior Debt", "Mezzanine Debt", "First Position", "Second Position"
- Equity-Related: "Equity Amount", "Capital Requirement", "Investment Amount", "Partner Contribution" 
- Budget- Related: "Acquisition Cost", "Construction Cost", "Construction Budget", "Development Budget", "Development Costs", "financing Cost", "Purhcase Price", "Soft Cost", 

Part 2: Capital Stack Deconstruction 
After identifying financial figures, your primary goal is to assemble them into a structured capital stack. Use the following definitions to categorize each extracted amount.
- Total Project Sources 
    - Definition: The total capitalization of the deal. It Represents the sum of all debt and all equity 
    - Keywords: "Total Project Cost", "Total Development Cost", "Total Deal Size", "Total Sources", "Total Uses", "Total Budget", "Total Project Budget", Total Capitalization"
    - Formuala: Total Debt + Total Equity 
- Total Debt 
    - Definition: The sum of all borrowed capital.
    - Keywords: "Loan Amount", "Debt Amount", "financing", "note", "mortgage", "Total Loan Amount", "Senior Debt", "Mezzanine Debt", "First Position", "Second Position"
    - Formuala: Senior Debt + Mezzanine Debt
        - Senior Debt
            - Definition: The primary, first-lien loan, often from a bank. It has the lowest risk and lowest interest rate among debt pieces.
            - Keywords: "Senior loan," "senior note," "first mortgage," "bank loan," "conventional financing", "First Position"  
        - Mezzanine
            - Definition: A subordinate loan that sits between senior debt and equity. It carries higher risk and a higher interest rate than senior debt.
            - Keywords: "Mezzanine," "mezz debt," "subordinate debt," "junior debt."
- Total Equity
    - Definition: The sum of all invested capital from owners and partners. It is the first-loss position.
    - Keywords: "Total equity," "required equity," "sponsor equity," "partner equity."
    - Formula: General Partner (GP) + Limited Partner (LP) + Preferred Equity
        - General Partner (GP)
            - Definition: The equity contributed by the deal sponsor or operator.
            - Keywords: "GP contribution," "sponsor equity," "sponsor co-invest", "Co-GP" 
        - Limited Partner (LP)
            - Definition: The equity contributed by passive investors or partners.
            - Keywords: "LP equity," "investor equity," "third-party equity", "Limited Partner"
        - Preferred Equity
            - Definition: An equity investment that has priority over common equity (GP/LP) for distributions, receiving a fixed-rate return before other equity holders.
            - Keywords: "Preferred equity," "pref equity," "preferred investment."

**INVESTMENT CRITERIA CREATION GUIDANCE:**
For capital positions found in the deal, create investment criteria objects that capture meaningful capital requirements. You can create separate objects for distinct positions OR combine related positions when it makes logical sense. 

**CRITICAL**: Use the EXACT database column names listed above in the Investment Criteria Database Schema section. Here are the available columns to populate:

USE DYNAMIC INVESTMENT COLUMNS MENTIONED ABOVE FOR OTHER COLUMNS NOT LISTED BELOW

**Example structure using actual database columns:**
{
  "entity_type": "Deal", // Always use "Deal"
  "entity_id": null, // Will be set by processor
  "capital_position": ["<VALUE_FROM_CAPITAL_POSITION_MAPPING>"], // Array with ONE exact value from Capital Position mapping table
  "minimum_deal_size": <AMOUNT_IN_MILLIONS>, // e.g., 20 for $20M
  "maximum_deal_size": <AMOUNT_IN_MILLIONS>, // Same as minimum for exact amounts
  "property_types": ["<VALUES_FROM_PROPERTY_TYPE_MAPPING>"], // Array of exact values from Property Types mapping table
  "strategies": ["<VALUES_FROM_STRATEGIES_MAPPING>"], // Array of exact values from Strategies mapping table
  "state": ["<VALUES_FROM_STATE_MAPPING>"], // Array of exact values from geographic mapping table
  "city": ["<VALUES_FROM_CITY_MAPPING>"], // Array of exact values from cities mapping table
  "target_return": <EXTRACTED_IRR_AS_DECIMAL>, // e.g., 0.15 for 15%
  "capital_source": "<SPONSOR_NAME>", // e.g., "ABC Development"
  "notes": "<DESCRIPTION>", // e.g., "Senior Debt component of XYZ Project"
  "created_at": "<CURRENT_ISO_TIMESTAMP>", // Current timestamp
  "updated_at": "<CURRENT_ISO_TIMESTAMP>", // Current timestamp
  "is_active": true // Always true for new records
  // Include any other relevant fields from the DYNAMIC database columns listed above
}

Conditional Rule: If the document provides a single figure for "Total Equity" without breaking it down into GP, LP, or Preferred components, assign the entire amount to Total Equity and mark the sub-components as "Not Specified."
 
- For all deal size and monetary fields, output the value as a number in millions (e.g., $14M → 14, $250K → 0.25, $2.5B → 2500)

- **FINANCIAL METRICS & PERCENTAGES**: Extract ALL percentage values including:
  - LTV (Loan-to-Value) ratios - look for "LTV", "loan to value", percentage of loan amount to property value
  - LTC (Loan-to-Cost) ratios - look for "LTC", "loan to cost", percentage of loan amount to total cost
  - Interest rates - look for "rate", "interest", "coupon", "pricing", SOFR+, WSJ+, Prime+
  - Returns - IRR, equity multiple, yield, cap rates, cash-on-cash returns
  - Fees - origination fees, exit fees, processing fees, underwriting fees
  - DSCR (Debt Service Coverage Ratio) - look for "DSCR", "debt service coverage", coverage ratios

- **GEOGRAPHIC REFERENCES**: Capture ALL location mentions:
  - **Property Address**: Full street address, building address, property location
  - Specific addresses, cities, states, zip codes
  - Market areas, MSAs, submarkets, regions
  - Target markets, preferred locations, geographic focus areas
  - Investment criteria locations, market preferences

- **PROPERTY DETAILS**: Extract ALL property information:
  - Property types (multifamily, office, retail, industrial, hotel, etc.)
  - Strategy (core, core plus, value-add, opportunistic, etc.)
  - Building details (square footage, units, stories, age, condition)
  - Investment strategies (acquisition, development, refinance, etc.)

- **LOAN TERMS & CONDITIONS**: Find ALL lending details:
  - Loan types (construction, permanent, bridge, acquisition, refinance, mezzanine)
  - Loan terms (duration, maturity, amortization)
  - Rate types (fixed, floating, SOFR-based, prime-based)
  - Loan programs (agency, bank, life company, CMBS)
  - Recourse (full recourse, non-recourse, limited recourse)

- **INVESTMENT PREFERENCES**: Look for ANY criteria or requirements:
  - Target returns, yield requirements, performance expectations
  - Hold periods, investment timelines, exit strategies
  - Risk tolerance, asset quality preferences
  - Capital structure preferences (senior debt, mezzanine, equity)
  - Partnership structures, co-investment requirements

- **HISTORICAL PERFORMANCE**: Extract ANY track record data:
  - Historical IRR, historical equity multiples
  - Past performance metrics, achieved returns
  - Portfolio performance, fund performance

- **TIMING & PROCESS**: Capture timeline information:
  - Closing timeframes, processing times
  - Construction schedules, development timelines
  - Hold periods, exit timelines
  - Due diligence periods, approval processes

- **SEARCH PATTERNS**: Look for these common terms and variations //(use the mapping table to fill the values, these are search patterns, can also be in mapping table, not the values themselves):
  - **Property Address**: "address", "location", "street address", "property address", "building address", "site address", "property location"
  - Total Project Sources: "Total Project Cost", "Total Development Cost", "Total Deal Size", "Total Sources", "Total Uses", "Total Budget", "Total Project Budget", Total Capitalization"
  - Total Debt: "Loan Amount", "Debt Amount", "financing", "note", "mortgage", "Total Loan Amount", "Senior Debt", "Mezzanine Debt", "First Position", "Second Position"
  - Senior Debt: "Senior loan," "senior note," "first mortgage," "bank loan," "conventional financing", "First Position"  
  - Mezzanine: "Mezzanine," "mezz debt," "subordinate debt," "junior debt", "Section Position"
  - Total Equity: "Total equity," "required equity," "sponsor equity," "partner equity."
  - General Partner (GP): "GP contribution," "sponsor equity," "sponsor co-invest", "Co-GP" 
  - Limited Partner (LP): "LP equity," "investor equity," "third-party equity", "Limited Partner"
  - Preferred Equity: "Preferred equity," "pref equity," "preferred investment."
  - Loan amount: "debt amount", "financing amount", "loan size", "borrowing"
  - LTV: "loan-to-value", "LTV", "loan to value ratio", "leverage"
  - Interest rate: "rate", "pricing", "coupon", "interest", "spread", "SOFR+", "Prime+"
  - Returns: "IRR", "return", "yield", "multiple", "equity multiple", "cash-on-cash"
  - Property type: "asset type", "property class", "building type", "real estate type"
  - **Strategy: "strategy", "investment strategy", "investment approach", "business plan", "investment thesis", "investment style", "risk profile", "investment philosophy", "value-add", "core", "core plus", "opportunistic", "acquisition", "refinance", "repositioning", "stabilization"**

**EXTRA FIELDS EXTRACTION GUIDELINES:**
- Extract ALL additional information found in the document, not just the core fields.
- Organize extra fields into logical categories for better data management.
- Look for financial metrics, property details, market data, timelines, legal entities, tenant information, risk factors, contact information, and any other relevant data.
- Include derived calculations, projections, and assumptions.
- Capture important notes, clarifications, and contextual information.
- Store related documents, attachments, and references.
- Include any data that might be useful for deal analysis, matching, or decision-making.

**FINAL CRITICAL REMINDER:**
- Use ONLY the exact values from the mapping tables provided above for ALL categorical fields
- Create meaningful investment criteria objects based on capital positions found in the deal (can be individual positions or logical combinations)
- Each capitalPosition array must contain only ONE value from the Capital Position mapping table
- Ensure each criteria object has sufficient data to be useful for deal matching and analysis
- Do not use hardcoded examples, invented values, or non-mapped terms

**RESPONSE FORMAT: Start your response immediately with { and end with }. Nothing else.**
`;
}

export async function generateMultipleFilesPrompt(
  files: Array<{ fileName: string }>,
  mappings?: { [key: string]: string[] }
): Promise<string> {
  const basePrompt = await generateDealExtractionPrompt(true, mappings);

  const fileList = files
    .map((f, i) => `File ${i + 1}: ${f.fileName}`)
    .join("\n");

  return `${basePrompt}

**FILES TO ANALYZE:**
${fileList}

**MULTIPLE FILES EXTRA FIELDS GUIDANCE:**
- Cross-reference information across all files to create comprehensive extra_fields.
- Combine financial data from different sources (e.g., pro forma from one file, actuals from another).
- Merge property details from various documents (e.g., architectural plans, surveys, appraisals).
- Consolidate market data from different reports and analyses.
- Ensure timeline information is consistent across all documents.
- Aggregate all legal entity information from various sources.
- Combine tenant information from different reports.
- Merge risk factors identified across multiple documents.
- Consolidate contact information from all sources.
- Include references to all related documents and attachments.

Please analyze all files together to extract comprehensive deal information.

**CRITICAL: RESPONSE FORMAT MUST BE JSON - Start your response immediately with { and end with }. Nothing else.**`;
}