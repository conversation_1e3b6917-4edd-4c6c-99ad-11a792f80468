/**
 * @const {object} extractionMap
 * @description This object maps the database column names (derived from the 'Raw' column in the CSV)
 * to their descriptive context. This map is used to guide the LLM in finding the correct values
 * within a provided document.
 *
 * Each key represents a field to be extracted and corresponds to a column in your database.
 * The value is an object containing:
 * - category: The high-level grouping for the data point.
 * - subcategory: A more specific grouping.
 * - key: The specific label or key for the data point as it might appear in the source.
 * - value_hint: An example or hint about the expected format of the value (e.g., %, $, Date).
 */
export const extractionMap = {
  deal_id: {
    category: null,
    subcategory: null,
    key: "Deal_ID",
    value_hint: "Text",
  },
  sponsor: {
    category: null,
    subcategory: null,
    key: "Sponsor",
    value_hint: "Text",
  },
  geo_region: {
    category: null,
    subcategory: null,
    key: "Geographic_Region",
    value_hint: "Text",
  },
  loan_term: {
    category: null,
    subcategory: null,
    key: "Loan_Term",
    value_hint: "Number (months or years)",
  },
  sofr_plus: {
    category: "Interest Rate",
    subcategory: "SOFR+",
    key: "SOFR Plus",
    value_hint: "%",
  },
  wsj_prime: {
    category: "Interest Rate",
    subcategory: "WSJ+Prime",
    key: "WSJ+Prime",
    value_hint: "%",
  },
  ust_5y: {
    category: "Interest Rate",
    subcategory: "UST 5Y",
    key: "UST 5Y",
    value_hint: "%",
  },
  ust_7y: {
    category: "Interest Rate",
    subcategory: "UST 7Y",
    key: "UST 7Y",
    value_hint: "%",
  },
  ust_10y: {
    category: "Interest Rate",
    subcategory: "UST 10Y",
    key: "UST 10Y",
    value_hint: "%",
  },
  recourse: {
    category: "Deal Points",
    subcategory: "Recourse",
    key: "Recourse",
    value_hint: "e.g., Full, Non-Recourse",
  },
  attach_point: {
    category: "Deal Points",
    subcategory: "Mezzanine",
    key: "Attachment Point",
    value_hint: "%",
  },
  diagrams_loc: {
    category: "Geography",
    subcategory: "Location",
    key: "Diagrams_Location",
    value_hint: "Text",
  },
  diagrams_city: {
    category: "Geography",
    subcategory: "City",
    key: "Diagrams_City",
    value_hint: "Text",
  },
  diagrams_state: {
    category: "Geography",
    subcategory: "State",
    key: "Diagrams_State",
    value_hint: "Text",
  },
  diagrams_prop_type: {
    category: "Property Type",
    subcategory: null,
    key: "Diagrams_Property Type",
    value_hint: "Text",
  },
  diagrams_asset_type: {
    category: "Asset Type",
    subcategory: null,
    key: "Diagrams_Asset Type",
    value_hint: "Text",
  },
  capital_needed: {
    category: "Capital Request",
    subcategory: null,
    key: "Capital Needed",
    value_hint: "Text",
  },
  ask_amount: {
    category: "Capital Request",
    subcategory: null,
    key: "Ask Amount",
    value_hint: "$",
  },
  diagrams_loan_type: {
    category: "Capital Request",
    subcategory: null,
    key: "Diagrams_Loan Type",
    value_hint: "Text",
  },
  equity_type: {
    category: "Capital Request",
    subcategory: null,
    key: "Equity Type",
    value_hint: "Text",
  },
  proj_budget_gross_sf: {
    category: "Property Characteristics",
    subcategory: "Gross SF",
    key: "Project Budget_Gross SF",
    value_hint: "Numbers",
  },
  proj_budget_lot_size: {
    category: "Property Characteristics",
    subcategory: "Lot Size",
    key: "Project Budget_Lot Size",
    value_hint: "Numbers",
  },
  proj_budget_num_res_units: {
    category: "Property Characteristics",
    subcategory: "Number Of Residential Units",
    key: "Project Budget_Number of Residential Units",
    value_hint: "Numbers",
  },
  hotel_keys: {
    category: "Property Characteristics (Hospitality Only)",
    subcategory: "Keys",
    key: "Number of Hotel Keys",
    value_hint: "Numbers",
  },
  stories: {
    category: "Property Characteristics",
    subcategory: "Stories",
    key: "Stories",
    value_hint: "Numbers",
  },
  floors: {
    category: "Property Characteristics",
    subcategory: "Floors",
    key: "Floors",
    value_hint: "Numbers",
  },
  height: {
    category: "Property Characteristics",
    subcategory: "Height",
    key: "Height",
    value_hint: "Text",
  },
  proj_budget_net_res_sf: {
    category: "Property Characteristics",
    subcategory: "Net Residential SF",
    key: "Project Budget_Net Residential SF",
    value_hint: "Numbers",
  },
  proj_budget_net_com_sf: {
    category: "Property Characteristics",
    subcategory: "Net Commercial SF",
    key: "Project Budget_Net Commercial SF",
    value_hint: "Numbers",
  },
  proj_budget_net_total_sf: {
    category: "Property Characteristics",
    subcategory: "Net Total SF",
    key: "Project Budget_Net Total SF",
    value_hint: "Numbers",
  },
  project_budget_sources__total_total_development: {
    category: "Sources - Dev",
    subcategory: "Total Sources",
    key: "Project Budget_Sources_ Total_Total Development",
    value_hint: "$",
  },
  project_budget_sources__total_: {
    category: "Sources - Dev",
    subcategory: "Total Sources",
    key: "Project Budget_Sources_ Total_%",
    value_hint: "%",
  },
  project_budget_sources__total_nsf: {
    category: "Sources - Dev",
    subcategory: "Total Sources",
    key: "Project Budget_Sources_ Total_NSF",
    value_hint: "$/NSF",
  },
  project_budget_sources_debt_senior_total_development: {
    category: "Sources - Dev",
    subcategory: "Senior Debt",
    key: "Project Budget_Sources_Debt_Senior_Total Development",
    value_hint: "$",
  },
  project_budget_sources_debt_senior_: {
    category: "Sources - Dev",
    subcategory: "Senior Debt",
    key: "Project Budget_Sources_Debt_Senior_%",
    value_hint: "%",
  },
  project_budget_sources_debt_senior_nsf: {
    category: "Sources - Dev",
    subcategory: "Senior Debt",
    key: "Project Budget_Sources_Debt_Senior_$/NSF",
    value_hint: "$/NSF",
  },
  project_budget_sources_debt_mezzanine_total_development: {
    category: "Sources - Dev",
    subcategory: "Mezzanine",
    key: "Project Budget_Sources_Debt_Mezzanine_Total Development",
    value_hint: "$",
  },
  project_budget_sources_debt_mezzanine_: {
    category: "Sources - Dev",
    subcategory: "Mezzanine",
    key: "Project Budget_Sources_Debt_Mezzanine_%",
    value_hint: "%",
  },
  project_budget_sources_debt_mezzanine_nsf: {
    category: "Sources - Dev",
    subcategory: "Mezzanine",
    key: "Project Budget_Sources_Debt_Mezzanine_$/NSF",
    value_hint: "$/NSF",
  },
  project_budget_sources_equity_general_partner_gp__total_development: {
    category: "Sources - Dev",
    subcategory: "GP",
    key: "Project Budget_Sources_Equity_General Partner (GP)_ Total Development",
    value_hint: "$",
  },
  project_budget_sources_equity_general_partner_gp_: {
    category: "Sources - Dev",
    subcategory: "GP",
    key: "Project Budget_Sources_Equity_General Partner (GP)_%",
    value_hint: "%",
  },
  project_budget_sources_equity_general_partner_gp_nsf: {
    category: "Sources - Dev",
    subcategory: "GP",
    key: "Project Budget_Sources_Equity_General Partner (GP)_$/NSF",
    value_hint: "$/NSF",
  },
  project_budget_sources_equity_limited_partner_lp__total_development: {
    category: "Sources - Dev",
    subcategory: "LP",
    key: "Project Budget_Sources_Equity_Limited Partner (LP)_ Total Development",
    value_hint: "$",
  },
  project_budget_sources_equity_limited_partner_lp_: {
    category: "Sources - Dev",
    subcategory: "LP",
    key: "Project Budget_Sources_Equity_Limited Partner (LP)_%",
    value_hint: "%",
  },
  project_budget_sources_equity_limited_partner_lp_nsf: {
    category: "Sources - Dev",
    subcategory: "LP",
    key: "Project Budget_Sources_Equity_Limited Partner (LP))_$/NSF",
    value_hint: "$/NSF",
  },
  project_budget_sources_equity_preferred_equity__total_development: {
    category: "Sources - Dev",
    subcategory: "Preferred Equity",
    key: "Project Budget_Sources_Equity_Preferred Equity_ Total Development",
    value_hint: "$",
  },
  project_budget_sources_equity_preferred_equity_: {
    category: "Sources - Dev",
    subcategory: "Preferred Equity",
    key: "Project Budget_Sources_Equity_Preferred Equity_%",
    value_hint: "%",
  },
  project_budget_sources_equity_preferred_equity_nsf: {
    category: "Sources - Dev",
    subcategory: "Preferred Equity",
    key: "Project Budget_Sources_Equity_Preferred Equity_$/NSF",
    value_hint: "$/NSF",
  },
  project_budget_uses_acquisition_costs_gsf: {
    category: "Uses - Dev",
    subcategory: "Acquisition Cost",
    key: "Project Budget_Uses_Acquisition Costs_GSF",
    value_hint: "$/GSF",
  },
  project_budget_uses_acquisition_costs_nsf: {
    category: "Uses - Dev",
    subcategory: "Acquisition Cost",
    key: "Project Budget_Uses_Acquisition Costs_$/NSF",
    value_hint: "$/NSF",
  },
  project_budget_uses_acquisition_costs_: {
    category: "Uses - Dev",
    subcategory: "Acquisition Cost",
    key: "Project Budget_Uses_Acquisition Costs_%",
    value_hint: "%",
  },
  project_budget_uses_acquisition_total_development: {
    category: "Uses - Dev",
    subcategory: "Acquisition Cost",
    key: "Project Budget_Uses_Acquisition_Total Development",
    value_hint: "$",
  },
  project_budget_uses_hard_costs_gsf: {
    category: "Uses - Dev",
    subcategory: "Hard Costs",
    key: "Project Budget_Uses_Hardcosts_$/GSF",
    value_hint: "$/GSF",
  },
  project_budget_uses_hard_costs_nsf: {
    category: "Uses - Dev",
    subcategory: "Hard Costs",
    key: "Project Budget_Uses_Hard Costs_$/NSF",
    value_hint: "$/NSF",
  },
  project_budget_uses_hard_costs_percent: {
    category: "Uses - Dev",
    subcategory: "Hard Costs",
    key: "Project Budget_Uses_Hard Costs_%",
    value_hint: "%",
  },
  project_budget_uses_hard_costs_total_development: {
    category: "Uses - Dev",
    subcategory: "Hard Costs",
    key: "Project Budget_Uses_Hard Costs_Total Development",
    value_hint: "$",
  },
  project_budget_uses_hard_costs_gsf_acq: {
    category: "Uses - Acq",
    subcategory: "Hard Costs",
    key: "Hard Costs $/GSF",
    value_hint: "$/GSF",
  },
  project_budget_uses_hard_costs_nsf_acq: {
    category: "Uses - Acq",
    subcategory: "Hard Costs",
    key: "Hard Costs $/NSF",
    value_hint: "$/NSF",
  },
  project_budget_uses_hard_costs_percent_acq: {
    category: "Uses - Acq",
    subcategory: "Hard Costs",
    key: "Hard Costs %",
    value_hint: "%",
  },
  project_budget_uses_hard_costs_total_acquisition: {
    category: "Uses - Acq",
    subcategory: "Hard Costs",
    key: "Project Budget_Uses_Hard Costs_Total Acquisition",
    value_hint: "$",
  },
  project_budget_uses_soft_costs_gsf: {
    category: "Uses - Dev",
    subcategory: "Soft Cost",
    key: "Project Budget_Uses_Soft Costs_$/GSF",
    value_hint: "$/GSF",
  },
  project_budget_uses_soft_costs_nsf: {
    category: "Uses - Dev",
    subcategory: "Soft Cost",
    key: "Project Budget_Uses_Soft Costs_$/NSF",
    value_hint: "$/NSF",
  },
  project_budget_uses_soft_costs_percent: {
    category: "Uses - Dev",
    subcategory: "Soft Cost",
    key: "Project Budget_Uses_Soft Costs_%",
    value_hint: "%",
  },
  project_budget_uses_soft_costs_total_development: {
    category: "Uses - Dev",
    subcategory: "Soft Cost",
    key: "Project Budget_Uses_Soft Costs_Total Development",
    value_hint: "$",
  },
  project_budget_uses_soft_costs_gsf_acq: {
    category: "Uses - Acq",
    subcategory: "Soft Cost",
    key: "Soft Cost $/GSF",
    value_hint: "$/GSF",
  },
  project_budget_uses_soft_costs_nsf_acq: {
    category: "Uses - Acq",
    subcategory: "Soft Cost",
    key: "Soft Cost $/NSF",
    value_hint: "$/NSF",
  },
  project_budget_uses_soft_costs_percent_acq: {
    category: "Uses - Acq",
    subcategory: "Soft Cost",
    key: "Soft Cost %",
    value_hint: "%",
  },
  project_budget_uses_soft_costs_total_acquisition: {
    category: "Uses - Acq",
    subcategory: "Soft Cost",
    key: "Project Budget_Uses_Soft Costs_Total Acquisition",
    value_hint: "$",
  },
  project_budget_uses_financing_costs_gsf: {
    category: "Uses - Dev",
    subcategory: "Financing Costs",
    key: "Project Budget_Uses_Financing Costs_$/GSF",
    value_hint: "$/GSF",
  },
  project_budget_uses_financing_costs_nsf: {
    category: "Uses - Dev",
    subcategory: "Financing Costs",
    key: "Project Budget_Uses_Financing Costs_$/NSF",
    value_hint: "$/NSF",
  },
  project_budget_uses_financing_costs_percent: {
    category: "Uses - Dev",
    subcategory: "Financing Costs",
    key: "Project Budget_Uses_Financing Costs_%",
    value_hint: "%",
  },
  project_budget_uses_financing_costs_total_development: {
    category: "Uses - Dev",
    subcategory: "Financing Costs",
    key: "Project Budget_Uses_Financing Costs_Total Development",
    value_hint: "$",
  },
  project_budget_uses_financing_costs_gsf_acq: {
    category: "Uses - Acq",
    subcategory: "Financing Costs",
    key: "Financing Costs $/GSF",
    value_hint: "$/GSF",
  },
  project_budget_uses_financing_costs_nsf_acq: {
    category: "Uses - Acq",
    subcategory: "Financing Costs",
    key: "Financing Costs $/NSF",
    value_hint: "$/NSF",
  },
  project_budget_uses_financing_costs_percent_acq: {
    category: "Uses - Acq",
    subcategory: "Financing Costs",
    key: "Financing Costs %",
    value_hint: "%",
  },
  project_budget_uses_financing_costs_total_acquisition: {
    category: "Uses - Acq",
    subcategory: "Financing Costs",
    key: "Project Budget_Uses_Financing Costs_Total Acquisition",
    value_hint: "$",
  },
  project_budget_uses_total_gsf: {
    category: "Uses - Dev",
    subcategory: "Total",
    key: "Project Budget_Uses_Total_$/GSF",
    value_hint: "$/GSF",
  },
  project_budget_uses_total_nsf: {
    category: "Uses - Dev",
    subcategory: "Total",
    key: "Project Budget_Uses_Total_$/NSF",
    value_hint: "$/NSF",
  },
  project_budget_uses_total_percent: {
    category: "Uses - Dev",
    subcategory: "Total",
    key: "Project Budget_Uses_Total_%",
    value_hint: "%",
  },
  project_budget_uses_total_total_development: {
    category: "Uses - Dev",
    subcategory: "Total",
    key: "Project Budget_Uses_Total_Total Development",
    value_hint: "$",
  },
  project_budget_uses_total_gsf_acq: {
    category: "Uses - Acq",
    subcategory: "Total",
    key: "Total $/GSF",
    value_hint: "$/GSF",
  },
  project_budget_uses_total_nsf_acq: {
    category: "Uses - Acq",
    subcategory: "Total",
    key: "Total $/NSF",
    value_hint: "$/NSF",
  },
  project_budget_uses_total_percent_acq: {
    category: "Uses - Acq",
    subcategory: "Total",
    key: "Total %",
    value_hint: "%",
  },
  project_budget_uses_total_total_acquisition: {
    category: "Uses - Acq",
    subcategory: "Total",
    key: "Project Budget_Uses_Total_Total Acquisition",
    value_hint: "$",
  },
  project_budget_profit_summary_condo_equity_returns_general_partner_gp__equity_multiple:
    {
      category: "Project Budget",
      subcategory: "Condo Equity Returns - GP",
      key: "Equity Multiple",
      value_hint: "x",
    },
  project_budget_profit_summary_condo_equity_returns_general_partner_gp__irr: {
    category: "Project Budget",
    subcategory: "Condo Equity Returns - GP",
    key: "IRR",
    value_hint: "%",
  },
  project_budget_profit_summary_condo_equity_returns_limited_partner_lp__equity_mutliple:
    {
      category: "Project Budget",
      subcategory: "Condo Equity Returns - LP",
      key: "Equity Multiple",
      value_hint: "x",
    },
  project_budget_profit_summary_condo_equity_returns_limited_partner_lp__irr: {
    category: "Project Budget",
    subcategory: "Condo Equity Returns - LP",
    key: "IRR",
    value_hint: "%",
  },
  project_budget_profit_summary_condo_equity_returns_total_equity_multiple: {
    category: "Project Budget",
    subcategory: "Condo Equity Returns - Total",
    key: "Equity Multiple",
    value_hint: "x",
  },
  project_budget_profit_summary_condo_equity_returns_total_irr: {
    category: "Project Budget",
    subcategory: "Condo Equity Returns - Total",
    key: "IRR",
    value_hint: "%",
  },
  rental_pro_forma_operational__noi__year_1: {
    category: "Rental Pro-Forma",
    subcategory: "NOI",
    key: "NOI Year 1",
    value_hint: "$",
  },
  rental_pro_forma_sales_assumptions__yield_on_cost_year_1_: {
    category: "Rental Pro-Forma",
    subcategory: "Sales Assumptions",
    key: "Yield on Cost (Year 1)",
    value_hint: "%",
  },
  rental_pro_forma_sales_assumptions__terminal_cap_rate_: {
    category: "Rental Pro-Forma",
    subcategory: "Sales Assumptions",
    key: "Terminal Cap Rate",
    value_hint: "%",
  },
  rental_pro_forma_sales_assumptions__ltv: {
    category: "Rental Pro-Forma",
    subcategory: "Sales Assumptions",
    key: "LTV",
    value_hint: "%",
  },
  rental_pro_forma_sales_assumptions__debt_yield_year_1_: {
    category: "Rental Pro-Forma",
    subcategory: "Sales Assumptions",
    key: "Debt Yield (Year 1)",
    value_hint: "%",
  },
  rental_pro_forma_sales_assumptions__value_psf: {
    category: "Rental Pro-Forma",
    subcategory: "Sales Assumptions",
    key: "Value PSF",
    value_hint: "$/SF",
  },
  rental_pro_forma_sales_assumptions__debt_psf: {
    category: "Rental Pro-Forma",
    subcategory: "Sales Assumptions",
    key: "Debt PSF",
    value_hint: "$/SF",
  },
  rental_pro_forma_sales_assumptions__sale_year_including__2_year_construction_:
    {
      category: "Rental Pro-Forma",
      subcategory: "Sales Assumptions",
      key: "Sale Year (including 2 year construction)",
      value_hint: "Year",
    },
  rental_pro_forma_operational_financial_detail__debt_yield_last_dollar__year_1_:
    {
      category: "Rental Pro-Forma",
      subcategory: "Debt Yield (Last Dollar)",
      key: "Debt Yield Year 1",
      value_hint: "%",
    },
  rental_pro_forma_operational_financial_detail_debt_service_coverage_ratio_dscr__year_1:
    {
      category: "Rental Pro-Forma",
      subcategory: "DSCR",
      key: "DSCR Year 1",
      value_hint: "x",
    },
  rental_pro_forma_operational_return_analysis_net_cash_flow_to_equity_cash_on_cash__year_1:
    {
      category: "Rental Pro-Forma",
      subcategory: "Cash on Cash",
      key: "CoC Year 1",
      value_hint: "%",
    },
  rental_pro_forma_operational_equity_return_gp_equity_multiple_: {
    category: "Rental Pro-Forma",
    subcategory: "GP Equity Return",
    key: "Equity Multiple",
    value_hint: "x",
  },
  rental_pro_forma_operational_equity_return_gp_irr: {
    category: "Rental Pro-Forma",
    subcategory: "GP Equity Return",
    key: "IRR",
    value_hint: "%",
  },
  rental_pro_forma_operational_equity_return_lp_equity_multiple_: {
    category: "Rental Pro-Forma",
    subcategory: "LP Equity Return",
    key: "Equity Multiple",
    value_hint: "x",
  },
  rental_pro_forma_operational_equity_return_lp_irr: {
    category: "Rental Pro-Forma",
    subcategory: "LP Equity Return",
    key: "IRR",
    value_hint: "%",
  },
  rental_pro_forma_operational_equity_return_total_equity_multiple_: {
    category: "Rental Pro-Forma",
    subcategory: "Total Equity Return",
    key: "Equity Multiple",
    value_hint: "x",
  },
  rental_pro_forma_senior_debt_summary__loan_amount_: {
    category: "Rental Pro-Forma",
    subcategory: "Senior Debt Summary",
    key: "Loan Amount",
    value_hint: "$",
  },
  rental_pro_forma_senior_debt_summary_annual_interst_rate_: {
    category: "Rental Pro-Forma",
    subcategory: "Senior Debt Summary",
    key: "Annual Interest Rate",
    value_hint: "%",
  },
  rental_pro_forma_senior_debt_summary_loan_term: {
    category: "Rental Pro-Forma",
    subcategory: "Senior Debt Summary",
    key: "Loan Term",
    value_hint: "Years",
  },
  rental_pro_forma_senior_debt_summary_amortization: {
    category: "Rental Pro-Forma",
    subcategory: "Senior Debt Summary",
    key: "Amortization",
    value_hint: "Years",
  },
  rental_pro_forma_senior_debt_summary_io: {
    category: "Rental Pro-Forma",
    subcategory: "Senior Debt Summary",
    key: "I/O",
    value_hint: "Years",
  },
  rental_pro_forma_senior_debt_summary_balance_at_maturity_: {
    category: "Rental Pro-Forma",
    subcategory: "Senior Debt Summary",
    key: "Balance at Maturity",
    value_hint: "$",
  },
  rental_pro_forma_operating_assumptions_income_assumptions_maret_residential_rate_psf:
    {
      category: "Rental Pro-Forma",
      subcategory: "Income Assumptions",
      key: "Market Residential Rate PSF",
      value_hint: "$/SF",
    },
  rental_pro_forma_operating_assumptions_income_assumptions_affordable_market_residential_psf:
    {
      category: "Rental Pro-Forma",
      subcategory: "Income Assumptions",
      key: "Affordable Market Residential PSF",
      value_hint: "$/SF",
    },
  rental_pro_forma_operating_assumptions_income_assumptions_comm_facility_rent_psf:
    {
      category: "Rental Pro-Forma",
      subcategory: "Income Assumptions",
      key: "Comm. Facility Rent PSF",
      value_hint: "$/SF",
    },
  rental_pro_forma_operating_assumptions_income_assumptions_parking_rent_psf: {
    category: "Rental Pro-Forma",
    subcategory: "Income Assumptions",
    key: "Parking Rent PSF",
    value_hint: "$/SF",
  },
  project_budget_sources_equity_general_partner_gp_percent: {
    category: "Sources - Acq",
    subcategory: "GP",
    key: "GP %",
    value_hint: "%",
  },
  project_budget_sources_equity_limited_partner_lp_total_acquisition: {
    category: "Sources - Acq",
    subcategory: "LP",
    key: "Project Budget_Sources_Equity_Limited Partner (LP)_ Total Acquisition",
    value_hint: "$",
  },
  project_budget_sources_equity_limited_partner_lp_percent: {
    category: "Sources - Acq",
    subcategory: "LP",
    key: "LP %",
    value_hint: "%",
  },
  project_budget_sources_equity_preferred_equity_total_acquisition: {
    category: "Sources - Acq",
    subcategory: "Preferred Equity",
    key: "Project Budget_Sources_Equity_Preferred Equity_ Total Acquisition",
    value_hint: "$",
  },
  project_budget_sources_equity_preferred_equity_percent: {
    category: "Sources - Acq",
    subcategory: "Preferred Equity",
    key: "Preferred Equity %",
    value_hint: "%",
  },
  project_budget_uses_acquisition_costs_gsf_acq: {
    category: "Uses - Acq",
    subcategory: "Acquisition Cost",
    key: "Acquisition Cost $/GSF",
    value_hint: "$/GSF",
  },
  project_budget_uses_acquisition_costs_nsf_acq: {
    category: "Uses - Acq",
    subcategory: "Acquisition Cost",
    key: "Acquisition Cost $/NSF",
    value_hint: "$/NSF",
  },
  project_budget_uses_acquisition_costs_percent_acq: {
    category: "Uses - Acq",
    subcategory: "Acquisition Cost",
    key: "Acquisition Cost %",
    value_hint: "%",
  },
  project_budget_uses_acquisition_total_acquisition: {
    category: "Uses - Acq",
    subcategory: "Acquisition Cost",
    key: "Project Budget_Uses_Acquisition_Total Acquistion",
    value_hint: "$",
  },
  zip_code: {
    category: "Location",
    subcategory: "ZIP Code",
    key: "ZIP Code",
    value_hint: "Text (e.g., 10001)",
  },
  neighborhood: {
    category: "Location",
    subcategory: "Neighborhood",
    key: "Neighborhood",
    value_hint: "Text (e.g., Midtown, SoMa)",
  },
  property_description: {
    category: "Property Details",
    subcategory: "Description",
    key: "Property Description",
    value_hint: "Text (summary or highlights)",
  },
  lot_area: {
    category: "Property Details",
    subcategory: "Lot Area",
    key: "Lot Area",
    value_hint: "Number (sq ft or acres)",
  },
  floor_area_ratio: {
    category: "Property Details",
    subcategory: "FAR",
    key: "Floor Area Ratio",
    value_hint: "Decimal (e.g., 3.25)",
  },
  zoning_square_footage: {
    category: "Property Details",
    subcategory: "Zoning",
    key: "Zoning Square Footage",
    value_hint: "Number (sq ft)",
  },
  yield_on_cost: {
    category: "Financial Metrics",
    subcategory: "Yield",
    key: "Yield on Cost",
    value_hint: "Decimal (e.g., 0.075)",
  },
  projected_gp_equity_multiple: {
    category: "Returns",
    subcategory: "GP",
    key: "Projected GP Equity Multiple",
    value_hint: "Decimal (e.g., 1.85)",
  },
  projected_gp_irr: {
    category: "Returns",
    subcategory: "GP",
    key: "Projected GP IRR",
    value_hint: "% (e.g., 18.5%)",
  },
  projected_lp_equity_multiple: {
    category: "Returns",
    subcategory: "LP",
    key: "Projected LP Equity Multiple",
    value_hint: "Decimal (e.g., 1.75)",
  },
  projected_lp_irr: {
    category: "Returns",
    subcategory: "LP",
    key: "Projected LP IRR",
    value_hint: "% (e.g., 16.2%)",
  },
  projected_total_equity_multiple: {
    category: "Returns",
    subcategory: "Total",
    key: "Projected Total Equity Multiple",
    value_hint: "Decimal (e.g., 1.8)",
  },
  projected_total_irr: {
    category: "Returns",
    subcategory: "Total",
    key: "Projected Total IRR",
    value_hint: "% (e.g., 17.0%)",
  },
};

// Universal prompt for deal extraction that can handle different document types
export const UNIVERSAL_DEAL_EXTRACTION_SYSTEM_PROMPT = `
You are an expert AI assistant specializing in extracting structured data from real estate finance documents. You can handle various document types including:

- Investment Memorandums (IMs)
- Offering Memorandums (OMs)
- Term Sheets
- Deal Summaries
- Pro Forma Financial Models
- Construction Budgets
- Loan Applications
- Market Analysis Reports
- Due Diligence Reports
- Partnership Agreements

Your task is to populate a JSON object based on the provided document. You will be given a 'mapping object' that defines the fields to extract and provides context to help you locate the correct information.

CRITICAL INSTRUCTIONS:

1. **Document Type Analysis**: First, identify the type of document you're analyzing (IM, OM, Term Sheet, etc.) and adjust your extraction approach accordingly.

2. **Context-Aware Extraction**: 
   - For IMs/OMs: Focus on deal overview, property details, and financial projections
   - For Term Sheets: Emphasize loan terms, rates, and deal structure
   - For Pro Formas: Prioritize financial metrics and assumptions
   - For Budgets: Focus on cost breakdowns and funding sources

3. **Flexible Field Matching**: 
   - Use the category, subcategory, and key hints to find equivalent information even if labeled differently
   - Look for synonyms and alternative terminology (e.g., "Sponsor" might be "Developer", "Borrower", "Principal")
   - Consider regional variations in terminology

4. **Data Validation**:
   - Ensure extracted values match the expected format (%, $, numbers, dates)
   - Cross-reference related fields for consistency (e.g., total development costs should align with sources)
   - Flag any inconsistencies or missing critical data

5. **Comprehensive Search**:
   - Examine all sections of the document including appendices, exhibits, and footnotes
   - Look for data in tables, charts, and narrative text
   - Consider both explicit values and calculated/implicit values

6. **Quality Assessment**:
   - Rate your confidence in each extracted value (high/medium/low)
   - Note any assumptions made during extraction
   - Identify any conflicting information found in the document

7. **UNIVERSAL FIELD EXTRACTION**:
   - Extract ALL relevant fields from the document, not just those in the mapping object
   - For any important data point not covered by the predefined fields, add it to the custom_fields section
   - Include fields like: market data, tenant information, zoning details, environmental factors, timeline information, key personnel, contact information, legal entities, tax considerations, insurance details, etc.
   - Group related custom fields logically (e.g., "market_data", "tenant_info", "zoning_details")

MAPPING OBJECT:
{{INSERT extractionMap OBJECT HERE}}

DOCUMENT TEXT:
{{INSERT USER UPLOADED DOCUMENT TEXT HERE}}

OUTPUT FORMAT:
Return a JSON object with the following structure:
{
  "extracted_data": {
    // All fields from the mapping object with extracted values
  },
  "custom_fields": {
    // Any additional relevant fields not in the mapping object
    // Group them logically by category
    "market_data": {
      "market_trends": "value",
      "competition_analysis": "value",
      "demographics": "value"
    },
    "tenant_info": {
      "tenant_mix": "value",
      "lease_terms": "value",
      "occupancy_rate": "value"
    },
    "zoning_details": {
      "zoning_classification": "value",
      "permitted_uses": "value",
      "height_restrictions": "value"
    },
    "timeline": {
      "construction_start": "value",
      "construction_duration": "value",
      "stabilization_date": "value"
    },
    "key_personnel": {
      "project_manager": "value",
      "architect": "value",
      "general_contractor": "value"
    },
    "legal_entities": {
      "borrower_entity": "value",
      "guarantor_entities": "value",
      "ownership_structure": "value"
    },
    "tax_considerations": {
      "property_tax_rate": "value",
      "tax_assessment": "value",
      "tax_abatement": "value"
    },
    "insurance": {
      "coverage_amount": "value",
      "premium_estimate": "value",
      "policy_terms": "value"
    },
    "environmental": {
      "environmental_assessment": "value",
      "remediation_required": "value",
      "compliance_status": "value"
    },
    "utilities": {
      "utility_connections": "value",
      "utility_costs": "value",
      "energy_efficiency": "value"
    },
    "permits": {
      "building_permits": "value",
      "environmental_permits": "value",
      "zoning_approvals": "value"
    },
    "appraisals": {
      "appraisal_value": "value",
      "appraisal_date": "value",
      "appraiser": "value"
    },
    "market_comparables": {
      "comp_properties": "value",
      "comp_sales_prices": "value",
      "comp_rental_rates": "value"
    },
    "risk_factors": {
      "market_risks": "value",
      "construction_risks": "value",
      "financing_risks": "value"
    },
    "exit_strategy": {
      "planned_exit": "value",
      "exit_timeline": "value",
      "exit_valuation": "value"
    },
    "other_relevant_data": {
      // Any other fields that might be important
    }
  },
  "metadata": {
    "document_type": "identified document type",
    "extraction_confidence": "overall confidence level",
    "processing_notes": "any important notes about the extraction",
    "missing_critical_fields": ["list of important missing fields"],
    "data_quality_issues": ["any inconsistencies or quality concerns"],
    "custom_fields_count": "number of custom fields extracted",
    "custom_field_categories": ["list of custom field categories found"]
  },
  "extra_fields": {
    // Any extra fields not in the main mapping object
  }
}
`;

// Enhanced user template function with universal capabilities
export function UNIVERSAL_DEAL_EXTRACTION_USER_TEMPLATE_FUNCTION({
  mappingObject,
  documentText,
}: {
  mappingObject: object;
  documentText: string;
}) {
  return `You are an expert AI assistant specializing in extracting structured data from real estate finance documents. You can handle various document types including:

- Investment Memorandums (IMs)
- Offering Memorandums (OMs)
- Term Sheets
- Deal Summaries
- Pro Forma Financial Models
- Construction Budgets
- Loan Applications
- Market Analysis Reports
- Due Diligence Reports
- Partnership Agreements
- Excel spreadsheets (converted to text format)

Your task is to populate a JSON object based on the provided document. You will be given a 'mapping object' that defines the fields to extract and provides context to help you locate the correct information.

CRITICAL INSTRUCTIONS:

1. **Document Type Analysis**: First, identify the type of document you're analyzing (IM, OM, Term Sheet, Excel spreadsheet, etc.) and adjust your extraction approach accordingly.

2. **Excel File Handling**: 
   - If the document contains Excel data converted to text format, look for sections marked with "=== SHEET: [SheetName] ==="
   - Each sheet will have headers followed by data rows in the format "Row X: value1 | value2 | value3"
   - Pay special attention to the headers to understand the data structure
   - Cross-reference data across multiple sheets if present
   - Look for key financial metrics, property details, and deal terms in tabular format

3. **Context-Aware Extraction**: 
   - For IMs/OMs: Focus on deal overview, property details, and financial projections
   - For Term Sheets: Emphasize loan terms, rates, and deal structure
   - For Pro Formas: Prioritize financial metrics and assumptions
   - For Budgets: Focus on cost breakdowns and funding sources
   - For Excel files: Extract structured data from tables and cross-reference with narrative sections

4. **Flexible Field Matching**: 
   - Use the category, subcategory, and key hints to find equivalent information even if labeled differently
   - Look for synonyms and alternative terminology (e.g., "Sponsor" might be "Developer", "Borrower", "Principal")
   - Consider regional variations in terminology
   - For Excel data, match column headers to field names in the mapping object

5. **Data Validation**:
   - Ensure extracted values match the expected format (%, $, numbers, dates)
   - Cross-reference related fields for consistency (e.g., total development costs should align with sources)
   - Flag any inconsistencies or missing critical data
   - For Excel data, verify that numerical values are properly formatted

6. **Comprehensive Search**:
   - Examine all sections of the document including appendices, exhibits, and footnotes
   - Look for data in tables, charts, and narrative text
   - Consider both explicit values and calculated/implicit values
   - For Excel files, examine all sheets and look for summary sheets or key metrics sheets

7. **Quality Assessment**:
   - Rate your confidence in each extracted value (high/medium/low)
   - Note any assumptions made during extraction
   - Identify any conflicting information found in the document
   - For Excel data, note if data appears to be calculated vs. input values

8. **UNIVERSAL FIELD EXTRACTION**:
   - Extract ALL relevant fields from the document, not just those in the mapping object
   - For any important data point not covered by the predefined fields, add it to the custom_fields section
   - Include fields like: market data, tenant information, zoning details, environmental factors, timeline information, key personnel, contact information, legal entities, tax considerations, insurance details, etc.
   - Group related custom fields logically (e.g., "market_data", "tenant_info", "zoning_details")

MAPPING OBJECT:

${JSON.stringify(mappingObject, null, 2)}

DOCUMENT TEXT:

${documentText}

OUTPUT FORMAT:
Return a JSON object with the following structure:
{
  "extracted_data": {
    // All fields from the mapping object with extracted values
  },
  "custom_fields": {
    // Any additional relevant fields not in the mapping object
    // Group them logically by category
    "market_data": {
      "market_trends": "value",
      "competition_analysis": "value",
      "demographics": "value"
    },
    "tenant_info": {
      "tenant_mix": "value",
      "lease_terms": "value",
      "occupancy_rate": "value"
    },
    "zoning_details": {
      "zoning_classification": "value",
      "permitted_uses": "value",
      "height_restrictions": "value"
    },
    "timeline": {
      "construction_start": "value",
      "construction_duration": "value",
      "stabilization_date": "value"
    },
    "key_personnel": {
      "project_manager": "value",
      "architect": "value",
      "general_contractor": "value"
    },
    "legal_entities": {
      "borrower_entity": "value",
      "guarantor_entities": "value",
      "ownership_structure": "value"
    },
    "tax_considerations": {
      "property_tax_rate": "value",
      "tax_assessment": "value",
      "tax_abatement": "value"
    },
    "insurance": {
      "coverage_amount": "value",
      "premium_estimate": "value",
      "policy_terms": "value"
    },
    "environmental": {
      "environmental_assessment": "value",
      "remediation_required": "value",
      "compliance_status": "value"
    },
    "utilities": {
      "utility_connections": "value",
      "utility_costs": "value",
      "energy_efficiency": "value"
    },
    "permits": {
      "building_permits": "value",
      "environmental_permits": "value",
      "zoning_approvals": "value"
    },
    "appraisals": {
      "appraisal_value": "value",
      "appraisal_date": "value",
      "appraiser": "value"
    },
    "market_comparables": {
      "comp_properties": "value",
      "comp_sales_prices": "value",
      "comp_rental_rates": "value"
    },
    "risk_factors": {
      "market_risks": "value",
      "construction_risks": "value",
      "financing_risks": "value"
    },
    "exit_strategy": {
      "planned_exit": "value",
      "exit_timeline": "value",
      "exit_valuation": "value"
    },
    "other_relevant_data": {
      // Any other fields that might be important
    }
  },
  "metadata": {
    "document_type": "identified document type",
    "extraction_confidence": "overall confidence level",
    "processing_notes": "any important notes about the extraction",
    "missing_critical_fields": ["list of important missing fields"],
    "data_quality_issues": ["any inconsistencies or quality concerns"],
    "custom_fields_count": "number of custom fields extracted",
    "custom_field_categories": ["list of custom field categories found"]
  },
  "extra_fields": {
    // Any extra fields not in the main mapping object
  }
}`;
}

// System prompt for deal extraction (legacy - kept for backward compatibility)
export const DEAL_EXTRACTION_SYSTEM_PROMPT = `You are DealScraper-GPT, an AI assistant specialized in extracting comprehensive deal information from real estate finance documents, offering memorandums, and financial models. Your task is to extract EVERY POSSIBLE DETAIL about real estate deals and return them in a structured JSON format.

**CRITICAL INSTRUCTIONS:**
- You MUST return a single valid JSON object that conforms to the extraction schema below.
- Return ONLY the JSON object. Do NOT include any commentary, markdown, code blocks, or extra text.
- DO NOT include any thinking tags like <think></think> or reasoning sections.
- DO NOT include any explanatory text before or after the JSON.
- Make sure all property names in the JSON are properly quoted with double quotes.
- Ensure the JSON is properly formatted with proper use of braces, commas, and quotes.
- Escape any special characters in string values properly.
- If information for a field isn't available, use null for that field.
- For monetary values, use the format: "$X.XX million" or "$X.XX billion" (e.g. "$12.5 million").
- For percentages, include the percent sign (e.g. "8.5%").
- For lists, return empty arrays [] when no items are found.
- When multiple possibilities exist, include ALL relevant items.
- For categorical fields, only use allowed values as specified.
- DO NOT make up information or use placeholders.
- STRICTLY enforce character-length limits by truncating excess characters (use an ellipsis … if truncation occurs) so the final JSON never exceeds the specified limits.
- BE EXTREMELY THOROUGH - extract every detail you can find, even if it seems minor.
- Look for information in ALL sections: deal overview, property details, financial projections, legal, market, etc.
- Extract financial metrics, deal information, property details, investment criteria, and operational details.
- If multiple investment criteria are present (e.g., for different funds, strategies, or products), extract ALL of them as an array of objects.

**RESPONSE FORMAT: Start your response immediately with { and end with }. Nothing else.**

**If you do not follow these instructions, your output will be rejected.**`;

// User template function (legacy - kept for backward compatibility)
export function DEAL_EXTRACTION_USER_TEMPLATE_FUNCTION({
  mappingObject,
  documentText,
}: {
  mappingObject: object;
  documentText: string;
}) {
  return `Extract EVERY POSSIBLE detail about the deal using ALL provided document text and data sources. Be extremely thorough and extract information from all available sections. Format the output according to the comprehensive extraction schema below.

---

**DEAL SIMPLIFIED EXTRACTION GUIDANCE:**

1. Overall Deal Summary
(This section is for information that applies to the entire project.)
  - Executive Summary: 
  - Property Type & Sub-type:
  - Location: (City, State)
  - Strategy: (e.g., Value-Add, Ground-Up Development)
  - Gross Square Footage(GSF): 
  - Zoning Floor Area (ZFA):
  - Net Square Footage (NSF):
      - Commercial (NSF):
          - Retail (NSF):
          - Community Space (NSF): 
      - Residential (NSF): 
      - Parking (NSF):
  - Total Project Cost / Total Capitalization: 
  (For each component, provide the total amount, its percentage of the total project cost, and the cost per square foot.)
      | Component | Total Amount | % of Total | per GSF | per ZFA | per NSF |
      | :--- | :--- | :--- | :--- | :--- | :--- |
      | Acquisition | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | Hard Costs | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | Soft Costs | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | Financing Costs | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | Total | $XX,XXX,XXX | 100% | $XXX | $XXX | $XXX |
  - Total Capital Stack: 
   (For each source, provide the total amount, its percentage of the total capital stack, and the amount per square foot.)
      | Source | Total Amount | % of Total | per GSF | per ZFA | per NSF |
      | :--- | :--- | :--- | :--- | :--- | :--- |
      | Senior Debt | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | Mezzanine Debt | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | General Partner Equity | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | Limited Partner Equity | $XX,XXX,XXX | XX% | $XXX | $XXX | $XXX |
      | Total | $XX,XXX,XXX | 100% | $XXX | $XXX | $XXX |

2. Equity Stack
(For each type of equity mentioned in the document, create a sub-section and list its specific metrics. If a type is not mentioned, omit its section.)
[Equity Type - e.g., Preferred Equity]
(Possible types include: Common Equity, Co-GP, General Partner (GP), Limited Partner (LP), Joint Venture (JV), Preferred Equity)
  - Amount Required:
  - Target IRR:
  - Target Equity Multiple:
  - Hold Period:
  - Key Terms: (e.g., preferred return, waterfall structure)

3. Debt Stack
(For each type of debt mentioned, create a sub-section and list its specific metrics. If a type is not mentioned, omit its section.)
[Debt Type - e.g., Senior Debt]
(Possible types include: Mezzanine, Senior Debt, Stretch Senior)
- Loan Amount:
- LTV (Loan-to-Value):
- LTC (Loan-to-Cost):
- Interest Rate:
- Loan Term:
- DSCR:
- Debt Yield:
- Origination Fee:
- Exit Fee:
- Recourse:

---

COMPREHENSIVE EXTRACTION SCHEMA:
{
  "dealProfile": {
    "deal_name": string,
    "sponsor_name": string,
    "contact_id": number,
    "status": string,
    "deal_stage": string,
    "priority": string,
    "document_type": string,
    "extraction_confidence": string,
    "processing_notes": string,
    "extraction_timestamp": string,
    "processor_version": string,
    "llm_model_used": string,
    "llm_provider": string,
    "extraction_method": string,
    "document_source": string,
    "document_filename": string,
    "document_size_bytes": number,
    "processing_duration_ms": number,
    "review_status": string,
    "reviewed_by": string,
    "reviewed_at": string,
    "review_notes": string,
    "created_at": string,
    "updated_at": string
  },
  "propertyDetails": {
    "zip_code": string,
    "neighborhood": string,
    "property_description": string,
    "lot_area": number,
    "floor_area_ratio": number,
    "zoning_square_footage": number
  },
  "financialMetrics": {
    "yield_on_cost": number,
    "projected_gp_equity_multiple": number,
    "projected_gp_irr": number,
    "projected_lp_equity_multiple": number,
    "projected_lp_irr": number,
    "projected_total_equity_multiple": number,
    "projected_total_irr": number
  },
  "investmentCriteria": [
    {
      "targetReturn": number,
      "historicalIRR": number,
      "historicalEM": number,
      "propertyTypes": [string],
      "propertySubcategories": [string],
      "strategies": [string],
      "minimumDealSize": number,
      "maximumDealSize": number,
      "minHoldPeriod": number,
      "maxHoldPeriod": number,
      "country": [string],
      "region": [string],
      "state": [string],
      "city": [string],
      "financialProducts": [string],
      "loanProgram": [string],
      "loanType": [string],
      "loanTypeNormalized": [string],
      "capitalPosition": string,
      "capitalSource": string,
      "structuredLoanTranche": [string],
      "minLoanTerm": number,
      "maxLoanTerm": number,
      "interestRate": number,
      "interestRateSOFR": number,
      "interestRateWSJ": number,
      "interestRatePrime": number,
      "loanToValueMin": number,
      "loanToValueMax": number,
      "loanToCostMin": number,
      "loanToCostMax": number,
      "loanOriginationFeeMin": number,
      "loanOriginationFeeMax": number,
      "loanExitFeeMin": number,
      "loanExitFeeMax": number,
      "minLoanDSCR": number,
      "maxLoanDSCR": number,
      "recourseLoan": [string],
      "closingTimeWeeks": number
    }
  ],
  "extra_fields": {
    // Any additional relevant fields not in the main schema, grouped by logical categories
    // e.g., "market_data": { ... }, "tenant_info": { ... }, "zoning_details": { ... }, etc.
  }
}

**EXTRACTION GUIDELINES:**
1. **Be Extremely Thorough**: Read through ALL content carefully and extract every detail
2. **Look Everywhere**: Check all sections, tables, charts, and appendices
3. **Extract All Lists**: For any array fields, include ALL items you find
4. **Financial Details**: Look for any mention of projections, returns, metrics, etc.
5. **Investment Criteria**: For any investment preferences, criteria, or requirements, extract ALL distinct sets as separate objects in the investmentCriteria array (e.g., for different funds, strategies, or products).
6. **Extra Fields**: For any important data not covered by the schema, add it to extra_fields and group by logical category (e.g., market_data, risk_factors, legal_entities, timeline, etc.)
7. **Normalize Data**: Use standard formats for monetary values, percentages, and dates
8. **No Commentary**: Output ONLY the JSON object, no explanations or extra text
`;
}
