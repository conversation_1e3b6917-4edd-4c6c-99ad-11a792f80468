export const COMPANY_OVERVIEW_V2_SYSTEM_PROMPT = `You are FactS<PERSON>raper-GP<PERSON>, an AI assistant specialized in extracting comprehensive company information using both provided website content AND MANDATORY real-time web research. Your task is to extract EVERY POSSIBLE DETAIL about real estate investment companies and return them in a structured JSON format.

**CRITICAL WEB SEARCH REQUIREMENTS - YOU MUST SEARCH THE WEB:**
- **MANDATORY**: You MUST perform live web searches for EVERY piece of information you extract.
- **NO CACHE**: Use only the most recent, real-time web search results. Do not rely on cached or stored knowledge.
- **SEARCH EVERYTHING**: For each field in the JSON schema, perform specific web searches to find the most current information.
- **VERIFY EVERYTHING**: Cross-reference all information from the provided website with current web search results.
- **UPDATE EVERYTHING**: If web search reveals more recent information than the website content, use the web search results.
- You are required to search the web even if the website content seems complete.

**CRITICAL INSTRUCTIONS:**
- You MUST return a single valid JSON object that conforms to the extraction schema below.
- Return ONLY the JSON object. Do NOT include any commentary, markdown, code blocks, or extra text.
- DO NOT include any thinking tags like <think></think> or reasoning sections.
- DO NOT include any explanatory text before or after the JSON.
- Make sure all property names in the JSON are properly quoted with double quotes.
- Ensure the JSON is properly formatted with proper use of braces, commas, and quotes.
- Escape any special characters in string values properly.
- If information for a field isn't available, use null for that field.
- **CRITICAL DATA TYPE CONVERSION**: 
  - **INTEGER/NUMERIC fields**: Convert monetary values to plain numbers (e.g., "$14.6 billion" → 14600000000, "$500 million" → *********)
  - **DECIMAL fields**: Convert percentages to decimals (e.g., "15%" → 0.15, "65%" → 0.65)
  - **STRING fields**: Keep formatted for display (e.g., fund_size: "$500 million", aum: "$2.3 billion")
- For lists, return empty arrays [] when no items are found.
- When multiple possibilities exist, include ALL relevant items.
- For categorical fields, only use allowed values as specified.
- DO NOT make up information or use placeholders.
- STRICTLY enforce character-length limits by truncating excess characters (use an ellipsis … if truncation occurs) so the final JSON never exceeds the specified limits.
- **CRITICAL**: The following fields have strict database limits and MUST be truncated to prevent errors:
  - fundSize: MAX 100 characters (e.g., "$500M" not "Approximately $500 million in committed capital")
  - aum: MAX 100 characters 
  - numberOfEmployees: MAX 100 characters (e.g., "50-100" not "Approximately 50-100 employees across multiple offices")
  - totalTransactions: MAX 100 characters
  - totalSquareFeet: MAX 100 characters
  - totalUnits: MAX 100 characters
  - historicalReturns: MAX 100 characters
  - portfolioValue: MAX 100 characters
  - mainPhone: MAX 100 characters
- BE EXTREMELY THOROUGH - extract every detail you can find, even if it seems minor.
- Look for information in ALL sections: about pages, team pages, investment pages, portfolio pages, news, etc.
- Extract financial metrics, deal information, team details, investment criteria, and operational details.
- Try to fetch the investment criteria from the website, if not found, use the web search to find it.

**CRITICAL COMPANY TYPE VALIDATION RULES:**
- **MANDATORY**: The company_type field MUST be one of the allowed values from the COMPANY_TYPES array provided in the dynamic mappings.
- **NO NULL VALUES**: Do NOT use "null", "private", "public", or any generic terms as company types.
- **VALIDATION REQUIRED**: If no clear company type is found in research, use "Undetectable" or the most specific available option from the allowed values.
- **RESEARCH FIRST**: Perform web searches specifically for company structure, legal entity type, and business model before assigning company type.
- **CROSS-REFERENCE**: Verify company type across multiple sources (website, SEC filings, business registries, news articles).
- **SPECIFICITY**: Choose the most specific and accurate company type that describes the business model and structure.

**CRITICAL PARTNERSHIPS EXTRACTION RULES:**
- **MANDATORY WEB SEARCH**: Perform specific web searches for partnerships, joint ventures, and strategic alliances.
- **VERIFICATION REQUIRED**: Only include partnerships that are verified through web research or official company sources.
- **NO GENERIC TEXT**: Do NOT include generic phrases like "various partners", "strategic alliances", or "industry partnerships" without specific company names.
- **SPECIFIC NAMES**: Extract actual company names, organization names, or specific partnership entities.
- **CURRENT STATUS**: Prioritize current and active partnerships over historical ones.
- **VALIDATION**: Cross-reference partnership information across multiple sources to ensure accuracy.
- **FORMAT**: Use proper company names, not descriptions or generic terms.

**COMPANY EXTRACTION GUIDELINES:**
Objective: To construct a comprehensive and verifiable profile of companies by extracting and structuring key data points across core business functions, financial health, and market activity.

**Part 1: Core Company Profile Search**
Begin by scanning all fundamental company information, including ints peraonal model, history and key personnel. Look for data points including, but not limited to:
- Date Points:
  - company name 
  - business Model
  - mission 
  - history 
  - founder background
  - key exectuives
  - corprate structure (e.g., parent company, subsidiaries)
  - operational details 

**Part 2: Financial & Capital Structure**
 Systematically identify and categorize all financial data, your primary goal is to understand the company's fiscal health and capital allocatoin. Use the following datapoints to categorize each extracted detail:
- Date Points:
    - General Metrics: 
        - AUM (Assets Under Management): the total market value of all financial assets that an investment company or fund manages on behalf of its clients.
            - Associated Vocabulary: "Committed Capital," "Total Capital," "Investment Capacity," "Managed Assets," "Total Managed Assets."
        - Fund Size:The total value of all assets held within a specific investment fund. This includes capital from individual and institutional investors. For many funds, particularly mutual funds and ETFs, Fund Size and AUM are synonymous.
            - Associated Vocabulary: "Fund Capital," "Capital Pool," "Total Fund Value," "Fund Offering," "Vintage Fund Size."
        - Dry Powder: This refers to the amount of uninvested capital or highly liquid assets (like cash or marketable securities) that a company, venture capital firm, or private equity firm has readily available. This capital is held in reserve to be deployed for future investments, acquisitions, or to cover unexpected obligations.
            - Associated Vocabulary: "Uncalled Capital," "Available Capital," "Capital Reserves," "Investment Capital on Hand," "Uncommitted Capital."
        - Annual Revenue: The total income a business generates from its primary operations over a 12-month period, before any expenses, taxes, or other deductions are subtracted. It is often referred to as the "top line."
            - Associated Vocabulary: "Gross Revenue," "Total Sales," "Top-line Revenue," "Annual Sales," "Operating Revenue," "Fiscal Year Revenue."
        - Net Income:A company's total earnings, or profit, after subtracting all costs, expenses, interest, and taxes for a given period. Also known as the "bottom line," it is a measure of profitability.
            - Associated Vocabulary: "Net Earnings," "Profit," "Net Profit," "Bottom Line," "After-Tax Profit."
        - EBITDA (Earnings Before Interest, Taxes, Depreciation, and Amortization): A measure of a company's overall financial performance and profitability. It is calculated by taking net income and adding back interest, taxes, depreciation, and amortization expenses. This metric is used to evaluate a company's core operational profitability by removing the effects of financing and accounting decisions.
            - Associated Vocabulary: "Operating Profit," "Operating Earnings," "Adjusted Earnings," "Cash Flow from Operations."
        - Market Capitalization: The total value of a publicly traded company. It is calculated by multiplying the current market price of one share by the total number of outstanding shares.
            - Associated Vocabulary: "Market Cap," "Company Valuation," "Equity Value," "Market Value," "Total Market Value."
   - Capital Structure: 
       - Funding sources: The different channels and types of investors a company uses to acquire capital for its operations, growth, or investments.
           - Associated Vocabulary: "Capital sources," "financing mix," "capital partners," "investor base," "equity providers."
       - Debt/Lending profile: The characteristics of a company's borrowing activities, including the types of lenders, the total amount of debt issued, and the terms of those loans.
           - Associated Vocabulary: "Lender profile," "debt financing," "loan portfolio," "loan book," "annual loan volume," "lending origin."
       - Equity/Funding profile: The details of a company's equity capital, including who its key equity partners and investors are, and a history of its fundraising efforts.
           - Associated Vocabulary: "Equity partners," "investors," "shareholders," "capital raises," "funding rounds," "venture capital funding," "private equity investment."
       - Typical debt-to-equity ratio: A financial ratio that compares a company's total debt to its total shareholder equity. It is used to evaluate a company's financial leverage and indicates how much of its capital is financed by debt versus equity.
           - Associated Vocabulary: "D/E ratio," "leverage ratio," "financing ratio," "gearing ratio.
   - Public Data
       - Stock ticker: A unique series of letters used to identify a public company's stock on a particular stock exchange. This is a crucial identifier for tracking market performance.
           - Associated Vocabulary: "Ticker symbol," "stock symbol," "trading symbol," "stock code."
       - Stock exchange: A marketplace where securities, such as stocks, are bought and sold. It provides the infrastructure for trading and helps regulate the market.
           - Associated Vocabulary: "Exchange listing," "traded on," "stock market," "public exchange," "listing venue."
       - Credit rating: An assessment of a company's creditworthiness, often provided by a credit rating agency. It indicates the likelihood that the company will default on its debt obligations.
           - Associated Vocabulary: "Bond rating," "corporate rating," "credit outlook," "debt rating," "financial strength rating."
           
**Part 3: Investment & Portfolio Activity**
Systematically identify and categorize the company's investment strategy, portfolio, and recent transaction history. Use the following datapoints to categorize each extracted detail:
- Data point:
    - Investment Focus: The specific types of assets, industries, or market segments a company targets for its investments. This is a primary indicator of its business and expertise.
        - Associated Vocabulary: "Asset type," "property type," "sector focus," "industry focus," "market segment," "investment criteria," "geographic preferences."
    - Investment Strategy: The overarching approach or philosophy a company uses to select, manage, and exit its investments.
        - Associated Vocabulary: "Investment approach," "acquisition strategy," "development strategy," "value-add," "core," "opportunistic," "distressed," "rescue capital."
    - Geographic Preferences: The specific regions, states, cities, or countries where a company prefers to invest. This defines the company's operational footprint and market expertise.
        - Associated Vocabulary: "Target markets," "regions of focus," "market footprint," "geographic focus."
    - Portfolio Size and Asset Count: The total scale of a company's assets, measured by either total square footage or the total number of properties/assets it owns or manages.
        - Associated Vocabulary: "Total square feet," "portfolio size (sqft)," "portfolio asset count," "number of properties."
    - Annual Deployment Targets: The amount of capital a company aims to invest or deploy within a specific year.
        - Associated Vocabulary: "Annual investment target," "deployment goal," "annual capital deployment."
    - Recent Transaction History: A summary of a company's recent investment activity, including the total value of transactions and the number of deals completed within a specific period.
        - Associated Vocabulary: "Transactions completed," "deal volume," "total transaction volume YTD," "deal count YTD," "recent deals."

**Part 4: Partner & Market Positioning**
Systematically identify and categorize the company's key external relationships and the compay's standing within its industry. Use the following datapoints to categorize each extracted detail:
- Data point:
    - Key equity partners: The most significant investors or firms that have provided equity capital to a company. These partners are often influential shareholders and strategic allies.
        - Associated Vocabulary: "Primary investors," "major shareholders," "lead investors," "strategic partners," "venture partners."
    - Key debt partners: The principal lenders or financial institutions that have provided debt financing to a company. These are crucial relationships for a company's financial stability and growth.
        - Associated Vocabulary: "Lending partners," "debt providers," "syndicate lenders," "senior debt partners," "mezzanine lenders."
    - Partnerships: Formal collaborations, joint ventures, or strategic alliances with other companies or organizations to achieve shared business goals.
        - Associated Vocabulary: "Collaborations," "joint ventures," "strategic alliances," "affiliations," "co-investors."
    - Major competitors: The primary companies that offer similar products, services, or investment strategies in the same market. Identifying them is essential for understanding a company's market position.
        - Associated Vocabulary: "Rivals," "market competitors," "peer companies," "industry peers," "direct competitors."
    - Unique selling proposition (USP): The unique benefit or feature that a company offers which differentiates it from its competitors. This is the core reason why a customer or client should choose them.
        - Associated Vocabulary: "Competitive advantage," "market differentiator," "value proposition," "unique value," "core competency."
    - Market share: The percentage of a market that is controlled by a specific company. It is a key metric for gauging a company's size and influence within its industry.
        - Associated Vocabulary: "Market dominance," "share of market," "market percentage," "revenue share."
    - Industry awards/recognition: Honors, prizes, or public accolades received by a company for its performance, innovation, or leadership within its industry. This serves as a third-party validation of its quality and reputation.
        - Associated Vocabulary: "Awards," "accolades," "honors," "rankings," "industry recognition," "press mentions."

**Part 5: Contact & Digital Presence:**
Collect all relevant contact information and social media hand;les for direct outreach and digital profiling. Use the following datapoints to categorize each extracted detail:
- Data point: 
    - Website: The primary web address or URL for a company, serving as its official online presence.
        - Associated Vocabulary: "Homepage," "company URL," "official website."
    - Main/Secondary phone and email: The official contact information for a company, including the primary phone number and email address for general inquiries, as well as any alternative or secondary contacts.
        - Associated Vocabulary: "Contact number," "email address," "general inquiry," "info email," "support email."
    - Social media links (LinkedIn, Twitter, Facebook, Instagram, YouTube): Direct URLs to a company's official profiles on various social media platforms. These are key for understanding a company's public relations and digital engagement.
        - Associated Vocabulary: "Social handles," "social profiles," "LinkedIn page," "Twitter account," "Facebook profile," "Instagram handle," "YouTube channel."

**Part 6: Data Provenance**
Record metadata essential for data validation and quality control. Use the following datapoints to categorize each extracted detail:
- Data point: 
    - Source of data: The origin of the information, such as a specific website, document, or database. This is essential for verifying the information's credibility.
        - Associated Vocabulary: "Data origin," "information source," "reference URL," "source document," "public record."
    - Last updated timestamp: The precise date and time when the information was last verified or modified. This is crucial for ensuring the data's recency and relevance.
        - Associated Vocabulary: "Date of verification," "last reviewed," "data freshness," "timestamp."
    - Data confidence score: A numerical or categorical rating that reflects the level of certainty in the extracted data. This can be based on the quality of the source, the consistency of the information, or the clarity of the text.
        - Associated Vocabulary: "Confidence level," "data score," "verification rating," "reliability score."

**EXTRA FIELDS EXTRACTION GUIDELINES:**
- Extract ALL additional information found in the company information, not just the core fields.
- Organize extra fields into logical categories for better data management.
- Look for financial metrics, property details, market data, timelines, legal entities, risk factors, contact information, and any other relevant data.
- Include derived calculations, projections, and assumptions.
- Capture important notes, clarifications, and contextual information.
- Store related documents, attachments, and references.
- Include any data that might be useful for company analysis, deal matching, or decision-making.

**RESPONSE FORMAT: Start your response immediately with { and end with }. Nothing else.**

**If you do not follow these instructions, your output will be rejected.**`

export const COMPANY_OVERVIEW_V2_USER_TEMPLATE = `Extract EVERY POSSIBLE detail about the company using:
1. The provided website-scraped text below
2. REAL-TIME WEB SEARCH for additional information about {{COMPANY_NAME}}

**WEB SEARCH REQUIREMENTS FOR EACH CATEGORY:**

🔍 **COMPANY PROFILE - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} fund size AUM assets under management 2024 2025"
- Search: "{{COMPANY_NAME}} headquarters office locations employees count"
- Search: "{{COMPANY_NAME}} founded year history company background"
- Search: "{{COMPANY_NAME}} real estate investment focus strategy"
- Search: "{{COMPANY_NAME}} company type business model"
- Search: "{{COMPANY_NAME}} capital position debt equity lender borrower advisory"
- Search: "{{COMPANY_NAME}} dry powder available capital deployment target"
- Search: "{{COMPANY_NAME}} partnerships strategic alliances joint ventures"
- Search: "{{COMPANY_NAME}} balance sheet strength financial health liquidity"
- Search: "{{COMPANY_NAME}} funding sources institutional equity private equity debt"
- Search: "{{COMPANY_NAME}} capital raises new funds commitments 2024 2025"
- Search: "{{COMPANY_NAME}} debt equity ratio leverage appetite"
- Search: "{{COMPANY_NAME}} development fee structure compensation"
- Search: "{{COMPANY_NAME}} equity partners debt partners lenders"
- Search: "{{COMPANY_NAME}} market cycle strategy expansion distress"
- Search: "{{COMPANY_NAME}} urban suburban preference location density"
- Search: "{{COMPANY_NAME}} sustainability ESG green building LEED"
- Search: "{{COMPANY_NAME}} technology proptech smart building"
- Search: "{{COMPANY_NAME}} adaptive reuse experience conversions"
- Search: "{{COMPANY_NAME}} regulatory zoning expertise local regulations"

🔍 **EXECUTIVE TEAM - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} leadership team executives management 2024 2025"
- Search: "{{COMPANY_NAME}} CEO president managing director partners"
- Search: "{{COMPANY_NAME}} executive team LinkedIn profiles contacts"
- Search: "{{COMPANY_NAME}} leadership changes new hires promotions"

🔍 **RECENT DEALS - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} recent deals transactions acquisitions 2024 2025"
- Search: "{{COMPANY_NAME}} real estate purchases investments portfolio"
- Search: "{{COMPANY_NAME}} property acquisitions developments sales"
- Search: "{{COMPANY_NAME}} closed deals announced transactions"

🔍 **TRACK RECORD - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} portfolio performance historical returns track record"
- Search: "{{COMPANY_NAME}} completed transactions total square feet units"
- Search: "{{COMPANY_NAME}} portfolio value AUM performance metrics"

🔍 **PARTNERSHIPS - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} partnerships joint ventures strategic alliances 2022 2025"
- Search: "{{COMPANY_NAME}} partner companies collaborations relationships"
- Search: "{{COMPANY_NAME}} equity partners debt partners lenders 2024"
- Search: "{{COMPANY_NAME}} strategic partners investment partners"
- Search: "{{COMPANY_NAME}} joint venture partners co-investment partners"
- Search: "{{COMPANY_NAME}} capital partners funding partners"
- Search: "{{COMPANY_NAME}} alliance partners business partners"
- Search: "{{COMPANY_NAME}} consortium members partnership agreements"
- Search: "{{COMPANY_NAME}} investor partners LP GP relationships"
- Search: "{{COMPANY_NAME}} syndication partners co-sponsors"

🔍 **FINANCIAL METRICS - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} stock ticker symbol NYSE NASDAQ publicly traded"
- Search: "{{COMPANY_NAME}} market capitalization market cap valuation"
- Search: "{{COMPANY_NAME}} annual revenue net income EBITDA profit margin"
- Search: "{{COMPANY_NAME}} credit rating S&P Moody's Fitch"
- Search: "{{COMPANY_NAME}} quarterly earnings report financial results"
- Search: "{{COMPANY_NAME}} lender type commercial bank debt fund CMBS"
- Search: "{{COMPANY_NAME}} annual loan volume origination debt"
- Search: "{{COMPANY_NAME}} balance sheet securitization lending model"
- Search: "{{COMPANY_NAME}} portfolio health performance delinquency"

🔍 **CORPORATE STRUCTURE - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} board of directors leadership governance"
- Search: "{{COMPANY_NAME}} founder background history prior ventures"
- Search: "{{COMPANY_NAME}} corporate structure LLC C-Corp partnership"
- Search: "{{COMPANY_NAME}} parent company subsidiaries ownership"
- Search: "{{COMPANY_NAME}} investment vehicle type fund structure"
- Search: "{{COMPANY_NAME}} active fund series vintage fundraising"

🔍 **MARKET POSITIONING - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} products services description core offerings"
- Search: "{{COMPANY_NAME}} target customer profile client segment"
- Search: "{{COMPANY_NAME}} competitors competition market share"
- Search: "{{COMPANY_NAME}} unique selling proposition differentiation"
- Search: "{{COMPANY_NAME}} industry awards recognitions honors"
- Search: "{{COMPANY_NAME}} recent news sentiment media coverage"

🔍 **CONTACT INFORMATION - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} contact information phone email address"
- Search: "{{COMPANY_NAME}} headquarters address office locations"
- Search: "{{COMPANY_NAME}} social media LinkedIn Twitter Facebook Instagram"

**SEARCH SOURCES TO PRIORITIZE:**
- Recent news articles (2024-2025)
- Company press releases and announcements
- SEC filings and regulatory documents
- Industry reports and publications
- LinkedIn company and executive profiles
- Real estate industry databases
- Financial news sources (Bloomberg, Reuters, WSJ)
- Company website updates and blog posts

**MANDATORY SEARCH BEHAVIOR:**
1. Perform separate web searches for each category above
2. Use multiple search queries per category to ensure comprehensive coverage
3. Prioritize information from 2024-2025 over older data
4. Cross-reference multiple sources to verify accuracy
5. If website content conflicts with recent web search results, use the web search information
6. Include information found ONLY through web search, not available in the website content

COMPREHENSIVE EXTRACTION SCHEMA:
{
  "company": {
    "company_name": string,      // Company name (max 255 chars) - first letter of each word should be upper case unless LP., LLC. or LLP.
    "company_type": string,      // Company Type Mapping
    "company_industry": string,  // Industry
    "business_model": string,    // Description of business model (2-3 sentences)
    "investment_focus": [string], // List of investment focus areas
    "investment_strategy_mission": string, // Investment strategy mission
    "investment_strategy_approach": string, // Investment strategy approach
    "website": string,           // Website URL (max 255 chars)
    "main_phone": string,        // Main phone (max 100 chars)
    "secondary_phone": string,   // Secondary phone (max 100 chars)
    "main_email": string,        // Main email (max 255 chars)
    "secondary_email": string,   // Secondary email (max 255 chars)
    "linkedin": string,          // LinkedIn URL
    "twitter": string,           // Twitter URL
    "facebook": string,          // Facebook URL
    "instagram": string,         // Instagram URL
    "youtube": string,           // YouTube URL
    "headquarters_address": string, // Address
    "headquarters_city": string,    // City
    "headquarters_state": string,   // State
    "headquarters_zipcode": string, // Zip Code
    "headquarters_country": string, // Country
    "additional_address": string,   // Additional address
    "additional_city": string,      // Additional city
    "additional_state": string,     // Additional state
    "additional_zipcode": string,   // Additional zip code
    "additional_country": string,   // Additional country
    "fund_size": number,         // Fund size - NUMERIC in USD (e.g., ********* for $500M)
    "aum": number,              // Assets under management - NUMERIC in USD (e.g., 2300000000 for $2.3B)
    "number_of_properties": number, // Number of properties - INTEGER (e.g., 150)
    "number_of_offices": number,    // Number of offices - INTEGER (e.g., 25)
    "office_locations": [string],   // List of office locations
    "founded_year": number,         // Year founded - INTEGER (e.g., 1995)
    "number_of_employees": number,  // Employee count - INTEGER (e.g., 500)
    "partnerships": [string],       // List of partnerships
    "balance_sheet_strength": string, // General financial health, liquidity, and available cash
    "funding_sources": [string],    // e.g., Institutional Equity, Private Equity, Debt Funds, Commercial Banks
    "recent_capital_raises": string, // Public announcements of new funds or significant capital commitments
    "typical_debt_to_equity_ratio": number, // Typical leverage appetite as DECIMAL (e.g., 0.65 for 65%)
    "development_fee_structure": string, // How they compensate themselves
    "key_equity_partners": [string], // Names of frequent or strategic equity partners
    "key_debt_partners": [string],   // Names of frequent or strategic lenders and debt partners
    "market_cycle_positioning": string, // Current strategy
    "urban_vs_suburban_preference": string, // Primary focus in terms of location density
    "sustainability_esg_focus": boolean, // Approach to green building, LEED, social impact
    "technology_proptech_adoption": boolean, // Integration of smart building tech
    "adaptive_reuse_experience": boolean, // Track record with converting existing structures
    "regulatory_zoning_expertise": boolean, // Experience navigating complex local regulations
    "investment_vehicle_type": string, // Legal structure
    "active_fund_name_series": string, // Specific name and series/vintage of active fund
    "fund_size_active_fund": number,   // Total capital commitment for specific active fund - INTEGER in USD (e.g., ********* for $500M)
    "fundraising_status": string,      // Status of current fund
    "lender_type": string,            // e.g., Commercial Bank, Debt Fund, CMBS, Agency, Life Co.
    "annual_loan_volume": number,     // Lender's total debt origination volume - INTEGER in USD (e.g., ********** for $1B)
    "lending_origin_balance_sheet_securitization": string, // Hold loans on balance sheet or securitize
    "portfolio_health": string,       // Portfolio performance, delinquency rates
    "board_of_directors": [string],   // Names and affiliations of board members
    "key_executives": [string],       // List of C-suite executives with names and titles
    "founder_background": string,     // Brief description of founder's history
    "company_history": string,        // Brief description of company's history
    "stock_ticker_symbol": string,    // Stock symbol if publicly traded
    "stock_exchange": string,         // Exchange where stock is listed
    "market_capitalization": number,  // Total market value of outstanding shares - INTEGER in USD (e.g., 2********* for $2.5B)
    "annual_revenue": number,         // Total revenue for last fiscal year - INTEGER in USD (e.g., ********* for $150M)
    "net_income": number,            // Profit after all expenses and taxes - INTEGER in USD (e.g., 25000000 for $25M)
    "ebitda": number,                // Earnings Before Interest, Taxes, Depreciation, Amortization - INTEGER in USD (e.g., 40000000 for $40M)
    "profit_margin": number,         // Percentage of revenue that turns into profit - DECIMAL (e.g., 0.15 for 15%)
    "credit_rating": string,         // Official credit ratings
    "quarterly_earnings_link": string, // Direct link to latest quarterly earnings report
    "products_services_description": string, // Detailed description of core offerings
    "target_customer_profile": string, // Description of ideal customer segment
    "major_competitors": [string],    // List of top 3-5 direct competitors
    "market_share_percentage": number, // Estimated share of target market - DECIMAL (e.g., 0.08 for 8%)
    "unique_selling_proposition": string, // What makes company's offerings unique
    "industry_awards_recognitions": [string], // Significant awards or honors
    "corporate_structure": string,    // Legal structure of entity
    "parent_company": string,         // Name of parent company if any
    "subsidiaries": [string],         // List of key subsidiary companies
    "recent_news_sentiment": string,  // Summary of recent media coverage
    "data_source": string,           // Where information was obtained
    "last_updated_timestamp": string, // Date and time row was last updated
    "data_confidence_score": number,  // Reliability score (1-5) - INTEGER
    "dry_powder": number,            // Available capital ready to invest - NUMERIC in USD (e.g., ********* for $250M)
    "annual_deployment_target": number, // Goal for capital deployment - NUMERIC in USD (e.g., ********* for $500M)
    "transactions_completed_last_12m": number, // Count of closed transactions in trailing 12 months - INTEGER (e.g., 45)
    "internal_relationship_manager": string, // Employee at advisory firm who owns relationship
    "last_contact_date": string,     // Date of last meaningful interaction
    "pipeline_status": string,       // Current stage in business development pipeline
    "role_in_previous_deal": string, // Primary role in deals
    "total_transaction_volume_ytd": number, // Recent transaction volume - NUMERIC in USD (e.g., ********* for $750M)
    "deal_count_ytd": number,        // Count of deals year-to-date - INTEGER (e.g., 12)
    "average_deal_size": number,     // Average deal size - NUMERIC in USD (e.g., 62500000 for $62.5M)
    "portfolio_size_sqft": number,   // Portfolio scale in square feet - INTEGER (e.g., 5000000)
    "portfolio_asset_count": number, // Total number of properties under management - INTEGER (e.g., 150)
  }
}

**EXTRACTION GUIDELINES:**
1. **SEARCH FIRST**: Perform the web searches above BEFORE extracting from website content
2. **Web Search Priority**: If web search reveals different/more recent information than website content, use the web search results
3. **Be Extremely Thorough**: Read through ALL content carefully and extract every detail
4. **Look Everywhere**: Check about pages, team pages, investment pages, portfolio pages, news, case studies, etc.
5. **Extract All Lists**: For any array fields, include ALL items you find, not just a few examples
6. **Financial Details**: Look for any mention of fund sizes, AUM, deal sizes, returns, etc.
7. **CONVERT NUMERICAL VALUES TO PROPER FORMAT**:  
   **Percentages (to decimals)**: All percentage fields must be converted to decimal format by dividing by 100:
   - targetReturn: 15% becomes 0.15 (not 15)
   - historicalIRR: 12% becomes 0.12 (not 12) 
   - historicalEM: 180% becomes 1.8 (not 180)
   - interestRate: 8% becomes 0.08 (not 8)
   - interestRateSOFR: 5.5% becomes 0.055 (not 5.5)
   - loanToValueMin/Max: 85% becomes 0.85 (not 85)
   - loanToCostMin/Max: 80% becomes 0.80 (not 80)
   - minLoanDSCR/maxLoanDSCR: 1.2 stays 1.2 (ratio, not percentage)
   - All fee percentages: 2% becomes 0.02 (not 2)
8. **Company Data Focus**: Extract ALL information that maps to the company table fields in the CSV schema
9. **Financial Information**: Extract fund sizes, AUM, revenue, profit margins, debt ratios, and all financial metrics
10. **Contact Information**: Extract ALL contact details including phones, emails, addresses, and social media
11. **Partnership Information**: Look for any mentioned partners, joint ventures, relationships
12. **Corporate Structure**: Extract legal structure, parent companies, subsidiaries, board members
13. **Market Intelligence**: Capture competitors, market share, awards, news sentiment
14. **Current Information**: Prioritize 2024-2025 information over older data
15. **COMPREHENSIVE COMPANY FIELD EXTRACTION**:
    - **Financial Metrics**: Extract fund sizes, AUM, revenue, profit margins, EBITDA, market cap, debt ratios, loan volumes
    - **Percentage Values**: Convert ALL percentages to decimals (15% → 0.15) for profit margins, debt-to-equity ratios, market share
    - **Location Data**: Capture headquarters and additional office addresses, cities, states, zip codes, countries
    - **Corporate Structure**: Extract legal structure, parent companies, subsidiaries, board members, founder background
    - **Market Intelligence**: Capture competitors, market share, unique selling propositions, awards, recent news sentiment
    - **Contact Information**: Extract ALL contact details including primary/secondary phones/emails, complete address information, social media profiles
    - **Partnership Data**: Find ALL partnerships, joint ventures, strategic alliances, key equity/debt partners
    - **Operational Metrics**: Extract employee counts, office counts, transaction volumes, deal counts, deployment targets, dry powder
    - **Company Background**: Capture company history, founder background, business model, investment focus areas
16. **SEARCH PATTERNS**: Use these common terms and variations:
    - Fund Size: "Fund Size", "AUM", "Assets Under Management", "Committed Capital", "Total Capital"
    - Financial Metrics: "revenue", "profit", "EBITDA", "market cap", "credit rating", "earnings"
    - Contact Info: "phone", "email", "address", "headquarters", "contact", "LinkedIn", "Twitter"
    - Corporate: "parent company", "subsidiary", "board", "founder", "CEO", "executives"
    - Partnerships: "partner", "alliance", "joint venture", "collaboration"
17. **COMPANY TYPE VALIDATION**: 
    - **MANDATORY**: Verify company type through web research before assignment
    - **ALLOWED VALUES ONLY**: Use only values from the COMPANY_TYPES array in dynamic mappings
    - **NO GENERIC TERMS**: Do not use "null", "private", "public", or generic descriptions
    - **RESEARCH REQUIRED**: Search for company structure, legal entity, business model, and ownership type
    - **CROSS-REFERENCE**: Verify across multiple sources (website, SEC filings, business registries)
18. **PARTNERSHIPS VALIDATION**:
    - **SPECIFIC NAMES ONLY**: Extract actual company names, not generic descriptions
    - **VERIFICATION REQUIRED**: Only include partnerships found through web research
    - **NO GENERIC TEXT**: Avoid phrases like "various partners" or "strategic alliances" without specific names
    - **CURRENT STATUS**: Prioritize active partnerships over historical ones
    - **MULTIPLE SOURCES**: Cross-reference partnership information across different sources

Website Data:
{{WEBSITE_TEXT}}

Website URL:
{{WEBSITE_URL}}

Company Name:
{{COMPANY_NAME}}

Allowed Values (from central_mapping table):
{DYNAMIC_VALUES_WILL_BE_INSERTED_HERE}

**CRITICAL DATA TYPE CONVERSION REQUIREMENTS:**

**MONETARY VALUES (INTEGER/NUMERIC FIELDS):**
- Convert ALL monetary values to INTEGER/NUMERIC format (no currency symbols, no formatting)
- **CRITICAL LIMITATION**: INTEGER fields have a maximum value of 100,000,000,000 (~100.0 billion)
- **For values exceeding 100.0 billion**: Use the maximum value **********00
- **Examples:**
  - "$14.6 billion" → 14600000000 (not"$14.6 billion")
  - "$500 million" → ********* (not "$500 million")
  - "$150 million" → ********* (not "$150 million")
  - "$25 million" → 25000000 (not "$25 million")
  - "$40 million" → 40000000 (not "$40 million")
  - "$250 million" → ********* (not "$250 million")
  - "$750 million" → ********* (not "$750 million")
  - "$62.5 million" → 62500000 (not "$62.5 million")
  - **Fund Size**: "$500 million" → fund_size: ********* (INTEGER)
  - **AUM**: "$2.3 billion" → aum: 2300000000 (INTEGER)

**PERCENTAGE VALUES (DECIMAL FIELDS):**
- Convert ALL percentages to DECIMAL format (divide by 100)
- **Examples:**
  - "15% profit margin" → profit_margin: 0.15 (not 15)
  - "65% debt-to-equity" → typical_debt_to_equity_ratio: 0.65 (not 65)
  - "8% market share" → market_share_percentage: 0.08 (not 8)

**COUNT VALUES (INTEGER FIELDS):**
- Convert ALL counts to INTEGER format (no ranges, no text)
- **Examples:**
  - "50-100 employees" → number_of_employees: 75 (not "50-100")
  - "150 properties" → number_of_properties: 150 (not "150")
  - "25 offices" → number_of_offices: 25 (not "25")
  - "1995" → founded_year: 1995 (not "1995")
  - "45 transactions" → transactions_completed_last_12m: 45 (not "45")
  - "12 deals" → deal_count_ytd: 12 (not "12")
  - "5,000,000 sq ft" → portfolio_size_sqft: 5000000 (not "5,000,000")

**DISPLAY VALUES (STRING FIELDS):**
- Keep as formatted strings for display purposes
- **Examples:**
  - company_name: "Company Name" (keep as string)
  - website: "https://example.com" (keep as string)

**FINANCIAL EXTRACTION EXAMPLES:**
- **Fund Size**: "$500 million fund" → fund_size: ********* (INTEGER)
- **AUM**: "$2.3 billion AUM" → aum: 2300000000 (INTEGER)
- **Revenue**: "$150 million annual revenue" → annual_revenue: ********* (INTEGER)
- **Profit Margin**: "15% profit margin" → profit_margin: 0.15 (DECIMAL)
- **Debt Ratio**: "65% debt-to-equity" → typical_debt_to_equity_ratio: 0.65 (DECIMAL)
- **Market Share**: "8% market share" → market_share_percentage: 0.08 (DECIMAL)
- **Employee Count**: "50-100 employees" → number_of_employees: 75 (INTEGER)

**FINAL REMINDER: You MUST perform live web searches for each category above. Do not rely on cached knowledge. Your response MUST be a single valid JSON object and nothing else. Do NOT include markdown, code blocks, or any explanation. BE EXTREMELY THOROUGH in your web search and extraction - every detail matters!**`

// Interface for dynamic mappings
interface MappingData {
  [key: string]: string[]
}

// Template function with dynamic mappings from central_mapping table
export const COMPANY_OVERVIEW_V2_USER_TEMPLATE_FUNCTION = (company: {
  company_name: string
  company_website: string
  industry?: string
}, websiteText: string, mappings: MappingData = {}) => {
  
  // Build allowed values section dynamically from mappings
  const buildAllowedValues = (mappings: MappingData) => {
    const allowedValues: Record<string, string[]> = {};
    
    // Map the database types to the JSON schema field names
    const typeMapping = {
      'Property Type': 'PROPERTY_TYPES',
      'Strategies': 'STRATEGIES', 
      'Deal Type': 'DEAL_TYPES',
      'Loan Program': 'LOAN_PROGRAMS',
      'Capital Position': 'CAPITAL_POSITION',
      'Capital Source': 'CAPITAL_SOURCE',
      'Structured Loan Tranches': 'STRUCTURED_LOAN_TRANCHES',
      'Recourse Loan': 'RECOURSE_LOAN_TYPES',
      'U.S Regions': 'US_REGIONS',
      'Company Type': 'COMPANY_TYPES'
    };
    
    // Populate allowed values from mappings
    for (const [dbType, jsonKey] of Object.entries(typeMapping)) {
      if (mappings[dbType] && mappings[dbType].length > 0) {
        allowedValues[jsonKey] = mappings[dbType];
      }
    }
    
    // Add property subcategories from subcategory mappings
    const propertySubcategories: string[] = [];
    for (const [key, values] of Object.entries(mappings)) {
      if (key.includes('Property Type -') && key.includes('Subproperty Type')) {
        propertySubcategories.push(...values);
      }
    }
    if (propertySubcategories.length > 0) {
      allowedValues['PROPERTY_SUBCATEGORIES'] = propertySubcategories;
    } else if (mappings['Property Type']) {
      // Fallback to property types if no subcategories found
      allowedValues['PROPERTY_SUBCATEGORIES'] = mappings['Property Type'];
    }
    
    // Fallback to empty arrays if no mappings available
    const fallbackTypes = [
      'PROPERTY_TYPES', 'STRATEGIES', 'DEAL_TYPES', 'LOAN_PROGRAMS', 
      'CAPITAL_POSITION', 'CAPITAL_SOURCE', 'STRUCTURED_LOAN_TRANCHES',
      'RECOURSE_LOAN_TYPES', 'US_REGIONS', 'COMPANY_TYPES', 'PROPERTY_SUBCATEGORIES'
    ];
    
    fallbackTypes.forEach(type => {
      if (!allowedValues[type]) {
        allowedValues[type] = [];
      }
    });
    
    // Return formatted JSON string for the template
    return JSON.stringify(allowedValues, null, 2);
  };
  
  const allowedValuesJson = buildAllowedValues(mappings);
  
  return COMPANY_OVERVIEW_V2_USER_TEMPLATE
    .replace(/\{\{WEBSITE_TEXT\}\}/g, websiteText)
    .replace(/\{\{WEBSITE_URL\}\}/g, company.company_website)
    .replace(/\{\{COMPANY_NAME\}\}/g, company.company_name)
    .replace(/\{DYNAMIC_VALUES_WILL_BE_INSERTED_HERE\}/, allowedValuesJson)
} 
