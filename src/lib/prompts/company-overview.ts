export const COMPANY_OVERVIEW_SYSTEM_PROMPT = `You are FactScraper-GP<PERSON>, an AI assistant specialized in extracting comprehensive company information using both provided website content AND MANDATORY real-time web research. Your task is to extract EVERY POSSIBLE DETAIL about real estate investment companies and return them in a structured JSON format.

**CRITICAL WEB SEARCH REQUIREMENTS - YOU MUST SEARCH THE WEB:**
- **MANDATORY**: You MUST perform live web searches for EVERY piece of information you extract.
- **NO CACHE**: Use only the most recent, real-time web search results. Do not rely on cached or stored knowledge.
- **SEARCH EVERYTHING**: For each field in the JSON schema, perform specific web searches to find the most current information.
- **VERIFY EVERYTHING**: Cross-reference all information from the provided website with current web search results.
- **UPDATE EVERYTHING**: If web search reveals more recent information than the website content, use the web search results.
- You are required to search the web even if the website content seems complete.

**CRITICAL INSTRUCTIONS:**
- You MUST return a single valid JSON object that conforms to the extraction schema below.
- Return ONLY the JSON object. Do NOT include any commentary, markdown, code blocks, or extra text.
- DO NOT include any thinking tags like <think></think> or reasoning sections.
- DO NOT include any explanatory text before or after the JSON.
- Make sure all property names in the JSON are properly quoted with double quotes.
- Ensure the JSON is properly formatted with proper use of braces, commas, and quotes.
- Escape any special characters in string values properly.
- If information for a field isn't available, use null for that field.
- For monetary values, use the format: "$X.XX million" (e.g. "$12.5 million") For billion, use the format: "$x billion" (e.g. "$1 billion").
- Values are in millions.
- For percentages, include the percent sign (e.g. "8.5%").
- For lists, return empty arrays [] when no items are found.
- When multiple possibilities exist, include ALL relevant items.
- For categorical fields, only use allowed values as specified.
- DO NOT make up information or use placeholders.
- STRICTLY enforce character-length limits by truncating excess characters (use an ellipsis … if truncation occurs) so the final JSON never exceeds the specified limits.
- **CRITICAL**: The following fields have strict database limits and MUST be truncated to prevent errors:
  - fundSize: MAX 100 characters (e.g., "$500M" not "Approximately $500 million in committed capital")
  - aum: MAX 100 characters 
  - numberOfEmployees: MAX 100 characters (e.g., "50-100" not "Approximately 50-100 employees across multiple offices")
  - totalTransactions: MAX 100 characters
  - totalSquareFeet: MAX 100 characters
  - totalUnits: MAX 100 characters
  - historicalReturns: MAX 100 characters
  - portfolioValue: MAX 100 characters
  - mainPhone: MAX 100 characters
- BE EXTREMELY THOROUGH - extract every detail you can find, even if it seems minor.
- Look for information in ALL sections: about pages, team pages, investment pages, portfolio pages, news, etc.
- Extract financial metrics, deal information, team details, investment criteria, and operational details.
- Try to fetch the investment criteria from the website, if not found, use the web search to find it.
- **CRITICAL FINANCIAL EXTRACTION**: You MUST extract ALL financial data including:
  - Target returns (IRR, equity multiples, yields)
  - Deal sizes (minimum and maximum investment amounts)
  - Interest rates (SOFR, Prime, WSJ spreads)
  - Loan terms (LTV, LTC ratios, DSCR requirements)
  - Fees (origination, exit, processing fees)
  - Hold periods and exit timelines
  - Recourse requirements
  - Closing timelines
- **CRITICAL**: Distinguish between company-level capital position (company's market role) and investment criteria capital position (specific investment products). For example: An advisory firm's companyProfile.capitalPosition should be "Advisory", while their investmentCriteria.capitalPosition might be "Senior Debt" for specific products they advise on.
  CLASSIFICATION CAPITAL POSITION RULES:
  - Senior Debt: The most secure layer in the CRE capital stack, representing a first-position mortgage or loan secured by the property itself. It has the lowest risk and is repaid first in case of default or sale.
  - Stretch Senior: A type of senior debt that ""stretches"" to provide higher loan-to-value (LTV)/ loan-to-cost (LTC) ratios by blending traditional senior debt with elements of subordinated debt in a single loan package. It's still senior but offers more leverage than standard senior debt, often used for acquisitions or refinancings where borrowers need higher proceeds.
  - Mezzanine: A hybrid financing layer subordinate to senior debt but senior to equity, often taking the form of unsecured or junior lien debt. It bridges the gap between senior debt and equity, with repayment after senior debt but before equity holders.
  - Preferred Equity: An equity investment positioned between all debt (senior and mezzanine) and common equity. It has priority over common equity for distributions and repayment, receiving a fixed, pre-negotiated dividend-like payment known as a preferred return. While it is an equity position, it behaves similarly to debt due to its fixed return, but it does not have the foreclosure rights of a lender. It's used to fill the final gap in financing when senior and mezzanine debt are insufficient.
  - Common Equity: The junior-most layer in the CRE capital stack, representing ownership interest with the highest risk and potential reward. Holders have residual claims after all debt and preferred equity are repaid, sharing in profits/losses proportionally.
  - General Partner (GP): The active managing partner in a CRE partnership or syndication, responsible for operations, decision-making, and often contributing a co-investment. GPs have unlimited liability and receive a promote (disproportionate share of profits) after hurdles are met.
  - Co-GP: A co-general partner structure where the sponsor (GP) invests alongside limited partners in the same equity entity, aligning interests through shared risk/reward. It's a form of GP co-investment, often used in syndications for transparency.
  - Joint Venture (JV): A collaborative equity arrangement between two or more parties (e.g., sponsor and investor) for a specific CRE project, sharing ownership, risks, and returns. In the capital stack, it often refers to JV equity, which is subordinate to debt but can include preferred or common positions.
  - Limited Partner (LP): Passive investors in a CRE partnership who provide the majority of equity capital with limited liability (only up to their investment). They have no management control but receive preferred returns before GP promotes.
  - Third Party: An individual, firm, or entity working in Commercial Real Estate (CRE) but not in an investment, ownership, or primary financing capacity. Instead, they provide external support services, intermediary functions, or advisory roles to facilitate transactions, operations, or compliance, often as independent contractors or outsourced specialists.
  - Undetectable: Insufficient information to detect [ Can't detect the capital position]

**FINANCIAL EXTRACTION GUIDELINES:**
Objective: To accurately identify, extract, and structure all financial figures from company information, with a specific focus on deconstructing capital structures and investment criteria.

**Part 1: Initial Keyword Search**
Begin by scanning all available information for any monetary figures or financial keywords. This initial pass is designed to locate all potential financial data points. Look for terms including, but not limited to:
- General Fund Size: "Fund Size", "Assets Under Management", "AUM", "Committed Capital", "Total Capital", "Investment Capacity"
- Deal Size: "Deal Size", "Transaction Size", "Investment Amount", "Minimum Investment", "Maximum Investment"
- Debt-Related: "Loan Amount", "Debt Amount", "financing", "note", "mortgage", "Total Loan Amount", "Senior Debt", "Mezzanine Debt"
- Equity-Related: "Equity Amount", "Capital Requirement", "Investment Amount", "Partner Contribution", "GP Equity", "LP Equity"
- Returns: "Target Return", "IRR", "Equity Multiple", "Yield", "Cash-on-Cash Return", "Historical Returns"

**Part 2: Capital Stack Analysis**
After identifying financial figures, your primary goal is to understand the company's capital structure and investment criteria. Use the following definitions to categorize each extracted amount:
- **Fund Size/AUM**: The total amount of capital the company manages or has access to
- **Deal Size Range**: The minimum and maximum size of individual investments the company makes
- **Capital Position**: The company's role in the capital stack (lender, borrower, equity investor, advisor)
- **Investment Criteria**: Specific requirements for investments including:
  - Target returns (IRR, equity multiples, yields)
  - Deal Size ($ amount)  
  - Geographic preferences (regions, states, cities)
  - Property type preferences (multifamily, office, retail, etc.)
  - Strategies (Core, Core Plus, Value-Add, Opportunistic, Distressed, Rescue Capital)
  - Hold periods and exit strategies
  - Loan terms and conditions (LTV, LTC, interest rates, DSCR)

**FINANCIAL METRICS & PERCENTAGES**: Extract ALL percentage values including:
- Target returns - look for "IRR", "return", "yield", "multiple", "equity multiple", "cash-on-cash"
- Interest rates - look for "rate", "interest", "coupon", "pricing", "SOFR+", "Prime+", "5YT", "10YT", "WSJ+" 
- Loan-to-Value ratios - look for "LTV", "loan to value", percentage of loan amount to property value
- Loan-to-Cost ratios - look for "LTC", "loan to cost", percentage of loan amount to total cost
- Fees - origination fees, exit fees, processing fees, underwriting fees
- DSCR (Debt Service Coverage Ratio) - look for "DSCR", "debt service coverage", coverage ratios

**SEARCH PATTERNS**: Look for these common terms and variations:
- Fund Size: "Fund Size", "AUM", "Assets Under Management", "Committed Capital", "Total Capital"
- Deal Size: "Deal Size", "Transaction Size", "Investment Amount", "Minimum Investment", "Maximum Investment"
- Target Returns: "IRR", "return", "yield", "multiple", "equity multiple", "cash-on-cash"
- Interest Rates: "rate", "pricing", "coupon", "interest", "spread", "SOFR+", "Prime+"
- Property Types: "asset type", "property class", "building type", "real estate type"
- Geographic Focus: "markets", "regions", "states", "cities", "geographic focus"
- Investment Strategies: "acquisition", "development", "value-add", "core", "opportunistic"

**EXTRA FIELDS EXTRACTION GUIDELINES:**
- Extract ALL additional information found in the company information, not just the core fields.
- Organize extra fields into logical categories for better data management.
- Look for financial metrics, property details, market data, timelines, legal entities, risk factors, contact information, and any other relevant data.
- Include derived calculations, projections, and assumptions.
- Capture important notes, clarifications, and contextual information.
- Store related documents, attachments, and references.
- Include any data that might be useful for company analysis, deal matching, or decision-making.

**RESPONSE FORMAT: Start your response immediately with { and end with }. Nothing else.**

**If you do not follow these instructions, your output will be rejected.**`

export const COMPANY_OVERVIEW_USER_TEMPLATE = `Extract EVERY POSSIBLE detail about the company using:
1. The provided website-scraped text below
2. REAL-TIME WEB SEARCH for additional information about {{COMPANY_NAME}}

**WEB SEARCH REQUIREMENTS FOR EACH CATEGORY:**

🔍 **COMPANY PROFILE - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} fund size AUM assets under management 2024 2025"
- Search: "{{COMPANY_NAME}} headquarters office locations employees count"
- Search: "{{COMPANY_NAME}} founded year history company background"
- Search: "{{COMPANY_NAME}} real estate investment focus strategy"
- Search: "{{COMPANY_NAME}} company type business model"
- Search: "{{COMPANY_NAME}} capital position debt equity lender borrower advisory"

🔍 **EXECUTIVE TEAM - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} leadership team executives management 2024 2025"
- Search: "{{COMPANY_NAME}} CEO president managing director partners"
- Search: "{{COMPANY_NAME}} executive team LinkedIn profiles contacts"
- Search: "{{COMPANY_NAME}} leadership changes new hires promotions"

🔍 **RECENT DEALS - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} recent deals transactions acquisitions 2024 2025"
- Search: "{{COMPANY_NAME}} real estate purchases investments portfolio"
- Search: "{{COMPANY_NAME}} property acquisitions developments sales"
- Search: "{{COMPANY_NAME}} closed deals announced transactions"

🔍 **INVESTMENT CRITERIA - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} investment criteria requirements minimum maximum deal size"
- Search: "{{COMPANY_NAME}} target returns IRR equity multiple investment strategy"
- Search: "{{COMPANY_NAME}} property types geographic focus investment regions"
- Search: "{{COMPANY_NAME}} hold period investment timeline strategy"
- Search: "{{COMPANY_NAME}} loan programs lending criteria interest rates"
- Search: "{{COMPANY_NAME}} loan terms LTV LTC debt service coverage ratio"
- Search: "{{COMPANY_NAME}} financing criteria minimum investment maximum investment"
- Search: "{{COMPANY_NAME}} capital requirements equity requirements debt requirements"
- Search: "{{COMPANY_NAME}} interest rates pricing spreads SOFR Prime WSJ"
- Search: "{{COMPANY_NAME}} loan to value ratio loan to cost ratio"
- Search: "{{COMPANY_NAME}} origination fees exit fees closing costs"
- Search: "{{COMPANY_NAME}} recourse non-recourse loan structures"
- Search: "{{COMPANY_NAME}} closing timeline funding timeline"

🔍 **TRACK RECORD - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} portfolio performance historical returns track record"
- Search: "{{COMPANY_NAME}} completed transactions total square feet units"
- Search: "{{COMPANY_NAME}} portfolio value AUM performance metrics"

🔍 **PARTNERSHIPS - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} partnerships joint ventures strategic alliances"
- Search: "{{COMPANY_NAME}} partner companies collaborations relationships"

🔍 **CONTACT INFORMATION - SEARCH NOW:**
- Search: "{{COMPANY_NAME}} contact information phone email address"
- Search: "{{COMPANY_NAME}} social media LinkedIn Twitter Facebook Instagram"

**SEARCH SOURCES TO PRIORITIZE:**
- Recent news articles (2024-2025)
- Company press releases and announcements
- SEC filings and regulatory documents
- Industry reports and publications
- LinkedIn company and executive profiles
- Real estate industry databases
- Financial news sources (Bloomberg, Reuters, WSJ)
- Company website updates and blog posts

**MANDATORY SEARCH BEHAVIOR:**
1. Perform separate web searches for each category above
2. Use multiple search queries per category to ensure comprehensive coverage
3. Prioritize information from 2024-2025 over older data
4. Cross-reference multiple sources to verify accuracy
5. If website content conflicts with recent web search results, use the web search information
6. Include information found ONLY through web search, not available in the website content

COMPREHENSIVE EXTRACTION SCHEMA:
{
  "companyProfile": {
    "companyName": string,      // Extracted company name as it appears on the website (max 255 chars)
    "companyType": string,      // Allowed values: Use COMPANY_TYPES from the allowed values section below (max 255 chars)
    "companyIndustry": string, // Industry classification (max 255 chars)
    "capitalPosition": string, // Allowed values: Use CAPITAL_POSITION from the allowed values section below (max 255 chars)
    "businessModel": string,    // Detailed description of how the company operates and makes money (2-3 sentences)
    "fundSize": string,         // e.g. "$500M", "$2.3B", null if unknown (STRICT max 100 chars - truncate if needed)
    "aum": string,              // Assets under management, e.g. "$1.2B" (STRICT max 100 chars - truncate if needed)
    "numberOfProperties": number, // Number of properties in portfolio if mentioned
    "headquarters": string,     // City, State format preferred (max 255 chars)
    "numberOfOffices": number,  // Total number of offices if mentioned
    "officeLocations": [string], // List of office locations besides HQ 
    "foundedYear": number,      // e.g. 1995, null if unknown
    "numberOfEmployees": string, // Range or specific number if mentioned (STRICT max 100 chars - truncate if needed)
    "investmentFocus": [string], // COMPREHENSIVE list of investment focus areas - extract ALL mentioned
  },
  "executiveTeam": [
    {
      "first_name": string,      // First name of executive
      "last_name": string,       // Last name of executive
      "full_name": string,       // Full name of executive
      "title": string,           // Job title
      "headline": string,        // Headline or summary if available
      "seniority": string,       // Seniority level if available
      "email": string,           // Work email if available, null if not
      "personal_email": string,  // Personal email if available, null if not
      "email_status": string,    // Email status if available
      "linkedin_url": string,    // LinkedIn profile URL if available
      "contact_city": string,    // City if available
      "contact_state": string,   // State if available
      "contact_country": string, // Country if available
      "phone": string,           // Phone number if available, null if not
      "twitter_url": string,     // Twitter profile URL if available, null if not
      "facebook_url": string,    // Facebook profile URL if available, null if not
      "instagram_url": string,   // Instagram profile URL if available, null if not
      "youtube_url": string,     // YouTube profile URL if available, null if not
    }
  ],
  "recentDeals": [
    {
      "property": string,   // Property name or description
      "location": string,   // Location of the property
      "dealType": string,   // Allowed values: Use DEAL_TYPES from the allowed values section below
      "dealTypeNormalized": string, // Normalized deal type classification
      "dealTypeNormalizedSpecialty": string, // Specialty deal type classification
      "capital_position": string, // Allowed values: Use CAPITAL_POSITION from the allowed values section below
      "strategies": string, // Allowed values: Use STRATEGIES from the allowed values section below
      "amount": string,     // Dollar amount if available, in millions format
      "date": string,       // Date in any format, recent is preferred
      "propertyTypes": [string], // Allowed values: Use PROPERTY_TYPES from the allowed values section below
      "propertySubcategories": [string], // Allowed values: Use PROPERTY_SUBCATEGORIES from the allowed values section below
      "squareFeet": string, // Square footage if available
      "units": number       // Number of units if applicable (for multifamily, etc.)
    }
  ],
  "investmentStrategy": {
    "mission": string,       // Company's mission statement if available
    "approach": string,      // Detailed description of investment approach/philosophy
  },
  "investmentCriteria": [
    {
      // Investment Strategy & Returns
      "targetReturn": number,    // Target return as decimal (e.g. 0.155 for 15.5%)
      "historicalIRR": number,   // Historical IRR as decimal (e.g. 0.12 for 12%)
      "historicalEM": number,    // Historical equity multiple as decimal (e.g. 1.8 for 180%)
      
      // Property & Asset Focus
      "propertyTypes": [string], // Allowed values: Use PROPERTY_TYPES from the allowed values section below
      "propertySubcategories": [string], // Allowed values: Use PROPERTY_SUBCATEGORIES from the allowed values section below
      "strategies": [string],    // Allowed values: Use STRATEGIES from the allowed values section below
      
      // Deal Size & Hold Period
      "minimumDealSize": number, // Minimum deal size in millions (e.g. 5 for $5M)
      "maximumDealSize": number, // Maximum deal size in millions (e.g. 50 for $50M)
      "minHoldPeriod": number,   // Minimum hold period in months
      "maxHoldPeriod": number,   // Maximum hold period in months
      
      // Geographic Focus
      "country": [string],       // Countries they invest in (e.g. ["United States"])
      "region": [string],        // Allowed values: Use US_REGIONS from the allowed values section below
      "state": [string],         // US states (specific state names)
      "city": [string],          // Specific cities they target
      
      // Financial Products & Loan Details
      "financialProducts": [string], // Financial products offered (e.g. ["Equity", "Debt", "Mezzanine"])
      "loanProgram": [string],   // Allowed values: Use LOAN_PROGRAMS from the allowed values section below
      "loanType": [string],      // Allowed values: Use LOAN_TYPES from the allowed values section below (if available in mappings)
      "loanTypeNormalized": [string], // Normalized loan types (multioption)
      "capitalPosition": string, // Allowed values: Use CAPITAL_POSITION from the allowed values section below (create new entry for multiple positions)
      "capitalSource": string,   // Allowed values: Use CAPITAL_SOURCE from the allowed values section below
      "structuredLoanTranche": [string], // Allowed values: Use STRUCTURED_LOAN_TRANCHES from the allowed values section below
      
      // Loan Terms & Conditions
      "minLoanTerm": number,     // Minimum loan term in months
      "maxLoanTerm": number,     // Maximum loan term in months
      "interestRate": number,    // Base interest rate as decimal (e.g. 0.08 for 8%)
      "interestRateSOFR": number, // SOFR-based interest rate spread as decimal (e.g. 0.055 for 5.5%)
      "interestRateWSJ": number, // WSJ Prime-based interest rate spread as decimal
      "interestRatePrime": number, // Prime-based interest rate spread as decimal
      
      // Loan-to-Value Ratios
      "loanToValueMin": number,  // Minimum loan-to-value ratio as decimal (e.g. 0.85 for 85%)
      "loanToValueMax": number,  // Maximum loan-to-value ratio as decimal (e.g. 0.85 for 85%)
      
      // Loan-to-Cost Ratios
      "loanToCostMin": number,   // Minimum loan-to-cost ratio as decimal (e.g. 0.80 for 80%)
      "loanToCostMax": number,   // Maximum loan-to-cost ratio as decimal (e.g. 0.80 for 80%)
      
      // Loan Fees
      "loanOriginationFeeMin": number, // Minimum loan origination fee as decimal (e.g. 0.02 for 2%)
      "loanOriginationFeeMax": number, // Maximum loan origination fee as decimal (e.g. 0.02 for 2%)
      "loanExitFeeMin": number,  // Minimum loan exit fee as decimal (e.g. 0.01 for 1%)
      "loanExitFeeMax": number,  // Maximum loan exit fee as decimal (e.g. 0.01 for 1%)
      
      // DSCR Requirements
      "minLoanDSCR": number,     // Minimum debt service coverage ratio (e.g. 1.2, NOT percentage)
      "maxLoanDSCR": number,     // Maximum debt service coverage ratio (e.g. 1.5, NOT percentage)
      
      // Recourse & Timing
      "recourceLoan": [string],  // Allowed values: Use RECOURSE_LOAN_TYPES from the allowed values section below (multioption)
      "closingTimeWeeks": number // Closing time in weeks
    }
  ],
  "trackRecord": {
    "totalTransactions": string, // Total number/value of transactions completed (STRICT max 100 chars - truncate if needed)
    "totalSquareFeet": string,   // Total square footage acquired/developed/managed (STRICT max 100 chars - truncate if needed)
    "totalUnits": string,        // Total units acquired/developed/managed (STRICT max 100 chars - truncate if needed)
    "historicalReturns": string, // Historical return metrics if mentioned (STRICT max 100 chars - truncate if needed)
    "portfolioValue": string     // Current portfolio value if mentioned (STRICT max 100 chars - truncate if needed)
  },
  "partnerships": [string], // COMPREHENSIVE list of partnerships and joint ventures
  "contactInfo": {
    "website": string,     // Company website URL (max 255 chars)
    "mainPhone": string,   // Main company phone number (STRICT max 100 chars - truncate if needed)
    "mainEmail": string,   // Main company email address (max 255 chars)
    "socialMedia": {       // Social media profiles - extract ALL found
      "linkedin": string,
      "twitter": string,
      "facebook": string,
      "instagram": string,
      "youtube": string
    }
  }
}

**EXTRACTION GUIDELINES:**
1. **SEARCH FIRST**: Perform the web searches above BEFORE extracting from website content
2. **Web Search Priority**: If web search reveals different/more recent information than website content, use the web search results
3. **Be Extremely Thorough**: Read through ALL content carefully and extract every detail
4. **Look Everywhere**: Check about pages, team pages, investment pages, portfolio pages, news, case studies, etc.
5. **Extract All Lists**: For any array fields, include ALL items you find, not just a few examples
6. **Financial Details**: Look for any mention of fund sizes, AUM, deal sizes, returns, etc.
7. **CONVERT NUMERICAL VALUES TO PROPER FORMAT**:  
   **Percentages (to decimals)**: All percentage fields must be converted to decimal format by dividing by 100:
   - targetReturn: 15% becomes 0.15 (not 15)
   - historicalIRR: 12% becomes 0.12 (not 12) 
   - historicalEM: 180% becomes 1.8 (not 180)
   - interestRate: 8% becomes 0.08 (not 8)
   - interestRateSOFR: 5.5% becomes 0.055 (not 5.5)
   - loanToValueMin/Max: 85% becomes 0.85 (not 85)
   - loanToCostMin/Max: 80% becomes 0.80 (not 80)
   - minLoanDSCR/maxLoanDSCR: 1.2 stays 1.2 (ratio, not percentage)
   - All fee percentages: 2% becomes 0.02 (not 2)
8. **Investment Criteria**: Extract ALL investment preferences, criteria, and requirements. Look for MULTIPLE investment criteria (different funds, strategies, products, or investment vehicles). Each distinct set of criteria should be a separate object in the investmentCriteria array.
   **CRITICAL FINANCIAL EXTRACTION REQUIREMENTS:**
   - **Target Returns**: Look for "IRR", "return", "yield", "multiple", "equity multiple", "cash-on-cash return", "target return", "expected return"
   - **Deal Sizes**: Look for "deal size", "transaction size", "minimum investment", "maximum investment", "investment amount", "financing amount"
   - **Loan Terms**: Look for "loan term", "maturity", "interest rate", "pricing", "spread", "SOFR+", "Prime+", "WSJ+"
   - **LTV/LTC Ratios**: Look for "loan to value", "LTV", "loan to cost", "LTC", "leverage ratio"
   - **DSCR Requirements**: Look for "debt service coverage ratio", "DSCR", "coverage ratio"
   - **Fees**: Look for "origination fee", "exit fee", "processing fee", "underwriting fee"
   - **Recourse**: Look for "recourse", "non-recourse", "personal guarantee"
   - **Timing**: Look for "closing time", "funding timeline", "hold period", "exit timeline"
   **CONVERT ALL PERCENTAGES TO DECIMALS**: 15% becomes 0.15, 8.5% becomes 0.085
   **CONVERT DEAL SIZES TO MILLIONS**: $5M becomes 5, $50M becomes 50
9. **INFER CRITERIA FROM DEALS**: If explicit investment criteria are not available or limited, analyze the recentDeals data to infer investment criteria. From recent deals, derive:
   - Deal size ranges (min/max from actual deal amounts - converted to millions format)
   - Property types and subcategories (from completed transactions)
   - Geographic focus (from deal locations)
   - Strategies (from deal types - acquisition, development, etc.)
   - Capital positions (from deal structures)
   Create investment criteria objects based on these patterns and include them in the investmentCriteria array.
10. **Financing Criteria**: Extract ALL financing preferences, criteria, and requirements
11. **Team Information**: Extract ALL team members with as much detail as possible, including social media profiles (LinkedIn, Twitter, Facebook, Instagram, YouTube)
12. **Deal Information**: Look for any recent transactions, acquisitions, developments
13. **Geographic Information**: Extract ALL markets, countrys, regions, states, cities mentioned
14. **Property Information**: Extract ALL property types, property subcategory types, strategies mentioned
15. **Partnership Information**: Look for any mentioned partners, joint ventures, relationships
16. **Current Information**: Prioritize 2024-2025 information over older data
17. When you find a financial product, map this into investment criteria.
18. **CAPITAL POSITION DISTINCTION**: 
   - Fetch the capital position from the company profile and investment criteria.
19. **FINANCIAL EXTRACTION BEST PRACTICES**:
    - Look for fund sizes, AUM, deal sizes, returns, interest rates, LTV ratios, fees, terms
    - Extract ALL percentage values including target returns, IRR, equity multiples, yields, cap rates
    - Capture ALL location mentions: addresses, cities, states, zip codes, market areas, MSAs
    - Extract ALL property information: types, strategies, building details, investment strategies
    - Find ALL lending details: loan types, terms, rate types, programs, recourse
    - Look for ANY criteria or requirements: target returns, hold periods, risk tolerance
    - Extract ANY track record data: historical IRR, equity multiples, past performance
    - Capture timeline information: closing timeframes, construction schedules, hold periods
20. **SEARCH PATTERNS**: Use these common terms and variations for comprehensive extraction:
    - Fund Size: "Fund Size", "AUM", "Assets Under Management", "Committed Capital", "Total Capital"
    - Deal Size: "Deal Size", "Transaction Size", "Investment Amount", "Minimum Investment", "Maximum Investment"
    - Target Returns: "IRR", "return", "yield", "multiple", "equity multiple", "cash-on-cash"
    - Interest Rates: "rate", "pricing", "coupon", "interest", "spread", "SOFR+", "Prime+"
    - Property Types: "asset type", "property class", "building type", "real estate type"
    - Geographic Focus: "markets", "regions", "states", "cities", "geographic focus"
    - Investment Strategies: "acquisition", "development", "value-add", "core", "opportunistic"

Website Data:
{{WEBSITE_TEXT}}

Website URL:
{{WEBSITE_URL}}

Company Name:
{{COMPANY_NAME}}

Allowed Values (from central_mapping table):
{DYNAMIC_VALUES_WILL_BE_INSERTED_HERE}

**FINANCIAL EXTRACTION EXAMPLES:**
- **Target Returns**: "15% IRR" → targetReturn: 0.15, "12% cash-on-cash" → targetReturn: 0.12
- **Deal Sizes**: "$5M minimum" → minimumDealSize: 5000, "$50M maximum" → maximumDealSize: 50000
- **Interest Rates**: "SOFR + 3.5%" → interestRateSOFR: 0.035, "Prime + 2%" → interestRatePrime: 0.02
- **LTV Ratios**: "up to 75% LTV" → loanToValueMax: 0.75, "65-75% LTV" → loanToValueMin: 0.65, loanToValueMax: 0.75
- **LTC Ratios**: "up to 80% LTC" → loanToCostMax: 0.80, "70-80% LTC" → loanToCostMin: 0.70, loanToCostMax: 0.80
- **DSCR**: "minimum 1.25 DSCR" → minLoanDSCR: 1.25, "1.25-1.50 DSCR" → minLoanDSCR: 1.25, maxLoanDSCR: 1.50
- **Fees**: "2% origination fee" → loanOriginationFeeMax: 0.02, "1% exit fee" → loanExitFeeMax: 0.01
- **Loan Terms**: "5-10 year terms" → minLoanTerm: 60, maxLoanTerm: 120 (in months)
- **Hold Periods**: "3-5 year hold" → minHoldPeriod: 36, maxHoldPeriod: 60 (in months)
- **Closing Time**: "4-6 weeks closing" → closingTimeWeeks: 5

**FINAL REMINDER: You MUST perform live web searches for each category above. Do not rely on cached knowledge. Your response MUST be a single valid JSON object and nothing else. Do NOT include markdown, code blocks, or any explanation. BE EXTREMELY THOROUGH in your web search and extraction - every detail matters!**`

// Interface for dynamic mappings
interface MappingData {
  [key: string]: string[]
}

// Template function with dynamic mappings from central_mapping table
export const COMPANY_OVERVIEW_USER_TEMPLATE_FUNCTION = (company: {
  company_name: string
  company_website: string
  industry?: string
}, websiteText: string, mappings: MappingData = {}) => {
  
  // Build allowed values section dynamically from mappings
  const buildAllowedValues = (mappings: MappingData) => {
    const allowedValues: Record<string, string[]> = {};
    
    // Map the database types to the JSON schema field names
    const typeMapping = {
      'Property Type': 'PROPERTY_TYPES',
      'Strategies': 'STRATEGIES', 
      'Deal Type': 'DEAL_TYPES',
      'Loan Program': 'LOAN_PROGRAMS',
      'Capital Position': 'CAPITAL_POSITION',
      'Capital Source': 'CAPITAL_SOURCE',
      'Structured Loan Tranches': 'STRUCTURED_LOAN_TRANCHES',
      'Recourse Loan': 'RECOURSE_LOAN_TYPES',
      'U.S Regions': 'US_REGIONS',
      'Company Type': 'COMPANY_TYPES'
    };
    
    // Populate allowed values from mappings
    for (const [dbType, jsonKey] of Object.entries(typeMapping)) {
      if (mappings[dbType] && mappings[dbType].length > 0) {
        allowedValues[jsonKey] = mappings[dbType];
      }
    }
    
    // Add property subcategories from subcategory mappings
    const propertySubcategories: string[] = [];
    for (const [key, values] of Object.entries(mappings)) {
      if (key.includes('Property Type -') && key.includes('Subproperty Type')) {
        propertySubcategories.push(...values);
      }
    }
    if (propertySubcategories.length > 0) {
      allowedValues['PROPERTY_SUBCATEGORIES'] = propertySubcategories;
    } else if (mappings['Property Type']) {
      // Fallback to property types if no subcategories found
      allowedValues['PROPERTY_SUBCATEGORIES'] = mappings['Property Type'];
    }
    
    // Fallback to empty arrays if no mappings available
    const fallbackTypes = [
      'PROPERTY_TYPES', 'STRATEGIES', 'DEAL_TYPES', 'LOAN_PROGRAMS', 
      'CAPITAL_POSITION', 'CAPITAL_SOURCE', 'STRUCTURED_LOAN_TRANCHES',
      'RECOURSE_LOAN_TYPES', 'US_REGIONS', 'COMPANY_TYPES', 'PROPERTY_SUBCATEGORIES'
    ];
    
    fallbackTypes.forEach(type => {
      if (!allowedValues[type]) {
        allowedValues[type] = [];
      }
    });
    
    // Return formatted JSON string for the template
    return JSON.stringify(allowedValues, null, 2);
  };
  
  const allowedValuesJson = buildAllowedValues(mappings);
  
  return COMPANY_OVERVIEW_USER_TEMPLATE
    .replace(/\{\{WEBSITE_TEXT\}\}/g, websiteText)
    .replace(/\{\{WEBSITE_URL\}\}/g, company.company_website)
    .replace(/\{\{COMPANY_NAME\}\}/g, company.company_name)
    .replace(/\{DYNAMIC_VALUES_WILL_BE_INSERTED_HERE\}/, allowedValuesJson)
} 
