export const NEWS_ENRICHMENT_SYSTEM_PROMPT = `You are NewsAnalyzer-GPT, an AI assistant specialized in extracting comprehensive structured information from real estate news articles. Your task is to extract EVERY POSSIBLE DETAIL about deals, companies, people, and market information from news articles and return them in a structured JSON format.

**CRITICAL WEB SEARCH REQUIREMENTS - YOU MUST SEARCH THE WEB:**
- **MANDATORY**: You MUST perform live web searches to verify and enhance information extracted from the news article.
- **NO CACHE**: Use only the most recent, real-time web search results. Do not rely on cached or stored knowledge.
- **SEARCH EVERYTHING**: For each entity (company, person, deal) mentioned, perform specific web searches to find additional context.
- **VERIFY EVERYTHING**: Cross-reference all information from the article with current web search results.
- **UPDATE EVERYTHING**: If web search reveals more recent or accurate information, use the web search results.

**CRITICAL INSTRUCTIONS:**
- You MUST return a single valid JSON object that conforms to the extraction schema below.
- Return ONLY the JSON object. Do NOT include any commentary, markdown, code blocks, or extra text.
- DO NOT include any thinking tags like <think></think> or reasoning sections.
- DO NOT include any explanatory text before or after the JSON.
- Make sure all property names in the JSON are properly quoted with double quotes.
- Ensure the JSON is properly formatted with proper use of braces, commas, and quotes.
- If information for a field isn't available, use null for that field.
- For monetary values, convert to millions format (e.g., $5M becomes 5, $2.3B becomes 2300).
- For percentages, convert to decimal format (e.g., 15% becomes 0.15).
- For lists, return empty arrays [] when no items are found.
- When multiple possibilities exist, include ALL relevant items.
- For categorical fields, only use allowed values as specified.
- DO NOT make up information or use placeholders.
- STRICTLY enforce character-length limits by truncating excess characters (use an ellipsis … if truncation occurs).
- BE EXTREMELY THOROUGH - extract every detail you can find, even if it seems minor.
- **MULTIPLE DEALS**: If an article mentions multiple deals, create separate deal objects for each distinct transaction.

**RESPONSE FORMAT: Start your response immediately with { and end with }. Nothing else.**

**If you do not follow these instructions, your output will be rejected.**`

export const NEWS_ENRICHMENT_USER_TEMPLATE = `Extract EVERY POSSIBLE detail from this real estate news article using:
1. The provided news article content below
2. REAL-TIME WEB SEARCH for additional information about mentioned entities

**WEB SEARCH REQUIREMENTS FOR EACH CATEGORY:**

🔍 **COMPANIES - SEARCH NOW:**
- For each company mentioned: Search "{{COMPANY_NAME}} real estate company profile 2024 2025"
- Search: "{{COMPANY_NAME}} recent deals transactions acquisitions"
- Search: "{{COMPANY_NAME}} leadership team executives"
- Search: "{{COMPANY_NAME}} business focus strategy property types"

🔍 **PEOPLE - SEARCH NOW:**
- For each person mentioned: Search "{{PERSON_NAME}} {{COMPANY_NAME}} real estate executive"
- Search: "{{PERSON_NAME}} LinkedIn profile contact information"
- Search: "{{PERSON_NAME}} recent transactions deals investments"

🔍 **DEALS - SEARCH NOW:**
- Search: "{{PROPERTY_ADDRESS}} real estate transaction deal details"
- Search: "{{PROPERTY_ADDRESS}} property information square feet units"
- Search: "{{DEAL_VALUE}} {{PROPERTY_TYPE}} {{LOCATION}} real estate transaction"

🔍 **MARKET CONTEXT - SEARCH NOW:**
- Search: "{{LOCATION}} real estate market conditions 2024 2025"
- Search: "{{LOCATION}} property values cap rates market trends"
- Search: "{{PROPERTY_TYPE}} real estate market {{LOCATION}} investment activity"

**SEARCH SOURCES TO PRIORITIZE:**
- Recent news articles (2024-2025)
- Company press releases and announcements
- Real estate industry publications (Commercial Observer, CBRE, etc.)
- LinkedIn profiles and company pages
- SEC filings and regulatory documents
- Property transaction databases
- Local real estate news sources

**MANDATORY SEARCH BEHAVIOR:**
1. Perform separate web searches for each category above
2. Use multiple search queries per entity to ensure comprehensive coverage
3. Prioritize information from 2024-2025 over older data
4. Cross-reference multiple sources to verify accuracy
5. If article content conflicts with recent web search results, use the web search information
6. Include information found ONLY through web search, not available in the article content

COMPREHENSIVE EXTRACTION SCHEMA:
{
  "newsDate": string, // Date of the news article in YYYY-MM-DD format (publication_date)
  
  // Core Article Content (already extracted, but include for reference)
  "newsTopic": string,                 // Primary topic of the article VARCHAR(100)
  "marketTrendTags": [string],         // Keywords identifying market trends
  "summary": string,                  // Summary of the article
  
  // Entity Information
  "entityInformation": {
    "buyerName": [string],             // Name of the acquiring party or entity
    "sellerName": [string],            // Name of the selling party or entity
    "lenderName": [string],            // Name of the financing entity, lender, investment bank, or fund
    "brokerName": [string],            // Name of any brokerage firm or individual mentioned
    "companyName": [string],           // Name of the Company
    "equityPartner": [string],         // Any LP, GP, or Co-GP mentioned in the deal
    "developerName": [string],         // Name of the developer if construction-related
    "tenantName": [string],            // Name of the tenant if leasing deal
    "reportAuthor": [string],          // The analyst, economist, or broker quoted or authoring
    "linkedEntities": [                // All organizations or people mentioned
      {
        "name": string,                // Entity name
        "role": string,                // Role in context
        "confidenceScore": number      // Confidence in identification
      }
    ]
  },
  
  // Property & Project Details (for each deal)
  "deals": [
    {
      "propertyType": [string],          // Allowed values: Use PROPERTY_TYPES from allowed values
      "subPropertyType": [string],       // Allowed values: Use PROPERTY_SUBCATEGORIES from allowed values
      "strategies": [string],          // Allowed values: Use STRATEGIES from allowed values
      "projectName": [string],         // Official or unofficial name of building/development
      "address": [string],             // Specific street address if disclosed
      "locationCity": [string],        // City where project is located
      "locationState": [string],       // U.S. state where project is located
      "locationNeighborhood": [string], // Granular neighborhood location
      "zipCode": [string],             // ZIP code of the property
      "squareFootage": number,         // Size of building/project/lease in square feet
      "unitCount": number,             // Number of units for multifamily assets
      "constructionType": [string],      // Type of construction (New Development, Adaptive Reuse, etc.)
      "projectTimeline": string,       // Projected completion date or development phases
      "jobCreation": number,           // Number of jobs mentioned as being created
      "subsidyInfo": string,           // Details on tax abatements, TIFs, PILOTs, etc.
      
      // Transaction Details
      "dealType": [string],            // Allowed values: Use DEAL_TYPES from allowed values
      "dealSize": number,              // Total size of transaction in millions USD
      "dealStatus": string,            // Current status (Announced, In Progress, Closed) VARCHAR(50)
      "capRate": number,               // Capitalization rate as decimal
      "pricePerSf": number,            // Price per square foot
      "loanType": [string],            // Type of loan instrument
      "equityType": [string],          // Type of equity
      
      // Capital & Fund Information
      "financingType": [string],       // Type of financing used in deal
      "capitalStackNotes": [string],   // Description of financing stack and components
      "capitalMarketsTopic": string,   // Primary financial topic discussed
      "interestRateImpact": [string],  // Description of interest rate effects
      "fundLoanType": [string],        // Type of loan instrument (fund level)
      "fundEquityType": [string],      // Type of equity (fund level)
      "fundName": [string],            // Specific name of fund mentioned
      "fundType": [string],            // Type of fund (Debt Fund, Opportunity Fund, REIT)
      "fundSize": number,              // Total size of fund in millions
      "fundStrategy": [string],        // Investment strategy of fund
      "fundraisingStatus": string,     // Current fundraising status VARCHAR(50)
      "capitalRaised": number,         // Amount of capital raised to date in millions
      "targetIrr": number,             // Reported target IRR as decimal
      "syndicationInfo": object,       // Information on lead arranger and participants
      
      // Distress & Financial Instruments
      "distressFlag": boolean,         // Indicator for distressed debt or workout
      "loanPerformance": string,       // Performance status of loan or portfolio [VARCHAR(50)] 
      "delinquencyRate": number,       // Reported delinquency rate as decimal
      "defaultNotices": number,        // Count of reported default events
      "restructuringEvent": string,    // Details if loan/equity/fund was restructured
      "recoveryEstimate": number,      // Estimated recovery value as decimal
      "cmbsData": object,              // Mention of specific CMBS securities/pools/tranches
      "ratingAgencyAction": [string],  // Rating agency actions (Upgrade, Downgrade, etc.)
      
      "confidenceScore": number        // Overall confidence in deal extraction
    }
  ],
  
  // Market-Level Metrics
  "marketMetrics": {
    "marketName": [string],            // Market discussed (NYC, Midwest, Sunbelt)
    "submarketName": [string],         // Granular submarket name
    "timePeriod": [string],            // Time period covered by report
    "vacancyRate": number,             // Vacancy rate as decimal
    "availabilityRate": number,        // Rate of marketed space available as decimal
    "rentalRate": number,              // Asking or effective rental rate
    "absorptionRate": number,          // Net change in occupied space
    "transactionVolume": number,       // Total transaction volume in millions
    "constructionPipeline": number,    // Volume of active development
    "deliveries": number,              // New space delivered in current period
    "occupancyChange": number          // Change in occupancy level as decimal
  },
  
  // Market-Level Trends & Commentary
  "marketTrends": {
    "rentalTrend": [string],           // Direction of rental rates
    "capRateAvg": number,              // Average cap rate as decimal
    "capRateTrend": [string],          // Direction of cap rates
    "newSupplyTrend": [string],        // Trend for new supply
    "demandTrend": [string],           // Trend for market demand
    "salesVolumeChange": number,       // YoY or QoQ change in sales activity as decimal
    "leasingActivity": [string],       // Qualitative summary of leasing trends
    "sentimentSummary": string,        // Summary of market sentiment
    "forecastSummary": string,         // Forward-looking commentary or data
    "macroeconomicCommentary": string, // Market-wide outlook on Fed policy, inflation, etc.
    "remoteWorkImpact": string,        // Commentary on remote work impact
    "distressIndicator": boolean       // Indicator of rising distress trend
  },
  
  // LLM / System Fields
  "systemFields": {
    "llmTags": [string],             // LLM-extracted meta labels
    "quotesLlmTags": [               // Attributed quotes extracted from article
      {
        "source": string,            // Quote source
        "role": string,              // Source role
        "statement": string,         // Quote statement
        "context": string            // Quote context
      }
    ],
    "sourceConfidence": number,      // Confidence score for extraction accuracy (0.00 to 1.00)
    "extractionNotes": string        // Logging, notes, feedback related to parsing
  }
}

**EXTRACTION GUIDELINES:**
1. **SEARCH FIRST**: Perform the web searches above BEFORE extracting from article content
2. **Web Search Priority**: If web search reveals different/more recent information than article content, use the web search results
3. **Be Extremely Thorough**: Read through ALL content carefully and extract every detail
4. **CONVERT NUMERICAL VALUES TO PROPER FORMAT**: 
   **Deal Sizes (to millions)**: Convert all dollar amounts to millions format:
   - $5M becomes 5 (not 5000)
   - $145M becomes 145 (not 145000)
   - $2.5B becomes 2500 (not 2500000)
   
   **Percentages (to decimals)**: Convert all percentages to decimal format:
   - 15% becomes 0.15 (not 15)
   - 6.5% cap rate becomes 0.065 (not 6.5)
   - 85% LTV becomes 0.85 (not 85)
5. **Multiple Deals**: If article mentions multiple distinct deals, create separate deal objects for each
6. **Location Extraction**: Extract ALL location mentions, from specific addresses to broad market areas
7. **Company and People**: Extract ALL companies and people mentioned, even if they play minor roles
8. **Market Intelligence**: Extract any market data, trends, conditions, or competitive intelligence mentioned
9. **Current Information**: Prioritize 2024-2025 information over older data from web searches
10. **Grouped Information**: Group related information logically (e.g., all property details together, all market metrics together)

**Article Information (Already Extracted):**
Title: {{ARTICLE_TITLE}}
Content: {{ARTICLE_TEXT}}
Source: {{ARTICLE_SOURCE}}
URL: {{ARTICLE_URL}}

**Note: Title and content have already been extracted. Do NOT extract them again. Focus on the comprehensive deal and entity extraction from the provided content above.**

Allowed Values (from central_mapping table):
{DYNAMIC_VALUES_WILL_BE_INSERTED_HERE}

**FINAL REMINDER: You MUST perform live web searches for each category above. Do not rely on cached knowledge. Your response MUST be a single valid JSON object and nothing else. Do NOT include markdown, code blocks, or any explanation. BE EXTREMELY THOROUGH in your web search and extraction - every detail matters! For multiple deals, create separate deal objects!**`

// Interface for dynamic mappings
interface MappingData {
  [key: string]: string[]
}

// Template function with dynamic mappings from central_mapping table
export const NEWS_ENRICHMENT_USER_TEMPLATE_FUNCTION = (article: {
  source?: string
  title?: string
  text?: string
  url?: string
}, mappings: MappingData = {}) => {
  
  // Build allowed values section dynamically from mappings
  const buildAllowedValues = (mappings: MappingData) => {
    const allowedValues: Record<string, string[]> = {};
    
    // Map the database types to the JSON schema field names
    const typeMapping = {
      'Property Type': 'PROPERTY_TYPES',
      'Strategies': 'STRATEGIES', 
      'Deal Type': 'DEAL_TYPES',
      'Capital Position': 'CAPITAL_POSITION',
      'U.S Regions': 'US_REGIONS',
      'Company Type': 'COMPANY_TYPES'
    };
    
    // Populate allowed values from mappings
    for (const [dbType, jsonKey] of Object.entries(typeMapping)) {
      if (mappings[dbType] && mappings[dbType].length > 0) {
        allowedValues[jsonKey] = mappings[dbType];
      }
    }
    
    // Add property subcategories from subcategory mappings
    const propertySubcategories: string[] = [];
    for (const [key, values] of Object.entries(mappings)) {
      if (key.includes('Property Type -') && key.includes('Subproperty Type')) {
        propertySubcategories.push(...values);
      }
    }
    if (propertySubcategories.length > 0) {
      allowedValues['PROPERTY_SUBCATEGORIES'] = propertySubcategories;
    } else if (mappings['Property Type']) {
      // Fallback to property types if no subcategories found
      allowedValues['PROPERTY_SUBCATEGORIES'] = mappings['Property Type'];
    }
    
    // Fallback to empty arrays if no mappings available
    const fallbackTypes = [
      'PROPERTY_TYPES', 'STRATEGIES', 'DEAL_TYPES', 
      'CAPITAL_POSITION', 'US_REGIONS', 'COMPANY_TYPES', 'PROPERTY_SUBCATEGORIES'
    ];
    
    fallbackTypes.forEach(type => {
      if (!allowedValues[type]) {
        allowedValues[type] = [];
      }
    });
    
    // Return formatted JSON string for the template
    return JSON.stringify(allowedValues, null, 2);
  };
  
  const allowedValuesJson = buildAllowedValues(mappings);
  
  return NEWS_ENRICHMENT_USER_TEMPLATE
    .replace(/\{\{ARTICLE_TITLE\}\}/g, article.title || 'Unknown')
    .replace(/\{\{ARTICLE_TEXT\}\}/g, article.text || 'Unknown')
    .replace(/\{\{ARTICLE_SOURCE\}\}/g, article.source || 'Unknown')
    .replace(/\{\{ARTICLE_URL\}\}/g, article.url || 'Unknown')
    .replace(/\{DYNAMIC_VALUES_WILL_BE_INSERTED_HERE\}/, allowedValuesJson)
} 