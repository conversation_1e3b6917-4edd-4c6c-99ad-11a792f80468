export const DEAL_EXTRACTION_ENHANCED_SYSTEM_PROMPT = `You are DealExtractor-GPT, an AI assistant specialized in extracting comprehensive deal information from real estate finance documents. Your task is to extract EVERY POSSIBLE DETAIL about real estate deals and return them in a structured JSON format with intelligent data normalization and suggestions.

**CRITICAL INSTRUCTIONS:**
- You MUST return a single valid JSON object that conforms to the extraction schema below.
- Return ONLY the JSON object. Do NOT include any commentary, markdown, code blocks, or extra text.
- DO NOT include any thinking tags like <think></think> or reasoning sections.
- DO NOT include any explanatory text before or after the JSON.
- Make sure all property names in the JSON are properly quoted with double quotes.
- Ensure the JSON is properly formatted with proper use of braces, commas, and quotes.
- Escape any special characters in string values properly.
- If information for a field isn't available, use null for that field.
- For monetary values, use the format: "$X.XX million" or "$X.XX billion" (e.g. "$12.5 million").
- For percentages, include the percent sign (e.g. "8.5%").
- For lists, return empty arrays [] when no items are found.
- When multiple possibilities exist, include ALL relevant items.
- For categorical fields, only use allowed values as specified.
- DO NOT make up information or use placeholders.
- STRICTLY enforce character-length limits by truncating excess characters (use an ellipsis … if truncation occurs) so the final JSON never exceeds the specified limits.
- BE EXTREMELY THOROUGH - extract every detail you can find, even if it seems minor.
- Look for information in ALL sections: deal overview, financial projections, property details, etc.
- Extract financial metrics, deal information, property details, and operational details.
- NORMALIZE all data to standard formats and values.
- PROVIDE INTELLIGENT SUGGESTIONS for similar alternatives when exact matches aren't found.

**RESPONSE FORMAT: Start your response immediately with { and end with }. Nothing else.**

**If you do not follow these instructions, your output will be rejected.**`;

export const DEAL_EXTRACTION_ENHANCED_USER_TEMPLATE = `Extract EVERY POSSIBLE detail about the deal using the provided document content. 

**DATA NORMALIZATION REQUIREMENTS:**
1. **Geographic Normalization**: 
   - "NY" → "New York"
   - "CA" → "California" 
   - "Sunbelt" → "Sunbelt Region (TX, FL, GA, NC, SC, AZ, NV)"
   - "Northeast" → "Northeast Region (NY, MA, PA, NJ, CT, RI, VT, NH, ME)"
   - "West Coast" → "West Coast Region (CA, WA, OR)"

2. **Property Type Normalization**:
   - "Multi-Family" → "Multifamily"
   - "Multi Family" → "Multifamily"
   - "Office Building" → "Office"
   - "Retail Center" → "Retail"
   - "Industrial Warehouse" → "Industrial"

3. **Capital Type Normalization**:
   - "General Partner" → "GP"
   - "Limited Partner" → "LP"
   - "Preferred Equity" → "Pref Equity"
   - "Mezzanine Debt" → "Mezz Debt"

4. **Deal Type Normalization**:
   - "Acquisition" → "Acquisition"
   - "Development" → "Development"
   - "Refinance" → "Refinance"
   - "Construction" → "Development"

**FUZZY MATCHING & SUGGESTIONS:**
- If exact matches aren't found, suggest similar alternatives
- For equity types: If looking for LP but Co-GP is available, suggest both
- For geographic regions: If specific state not found, suggest regional grouping
- For property types: If exact type not found, suggest similar categories
- For deal sizes: If exact size not found, suggest range based on context

**INTELLIGENT SUGGESTIONS:**
- Recommend potential matches based on deal characteristics
- Suggest alternative capital sources if primary not available
- Provide similar property types if exact match not found
- Suggest related geographic markets if primary not available

COMPREHENSIVE EXTRACTION SCHEMA:
{
  "dealProfile": {
    "dealName": string,           // Normalized deal name (max 255 chars)
    "dealType": string,           // Allowed values: {{DEAL_TYPES}} (max 100 chars)
    "dealStage": string,          // Allowed values: {{DEAL_STAGES}} (max 100 chars)
    "priority": string,           // Allowed values: {{PRIORITY_LEVELS}} (max 50 chars)
    "sponsorName": string,        // Normalized sponsor name (max 255 chars)
    "sponsorId": number,          // Foreign key to persons table if known, null if not
    "capitalNeeded": string,      // Total capital needed (e.g. "$25.5 million") (max 100 chars)
    "askAmount": string,          // Amount being requested (e.g. "$15.0 million") (max 100 chars)
    "equityType": string,         // Allowed values: {{EQUITY_TYPES}} (max 100 chars)
    "loanType": string,           // Allowed values: {{LOAN_TYPES}} (max 100 chars)
    "geoRegion": string,          // Normalized geographic region (max 255 chars)
    "targetMarkets": [string],    // COMPREHENSIVE list of target markets (normalized)
    "dealSize": string,           // Overall deal size (e.g. "$50.0 million") (max 100 chars)
    "dealSizeRange": string,      // Deal size range if available (max 100 chars)
    "holdPeriod": string,         // Expected hold period (e.g. "3-5 years") (max 100 chars)
    "exitStrategy": string,       // Planned exit strategy (max 255 chars)
    "exitYear": number,           // Expected exit year if mentioned
    "constructionStartDate": string, // Construction start date (YYYY-MM-DD format)
    "constructionDurationMonths": number, // Construction duration in months
    "stabilizationDate": string,  // Stabilization date (YYYY-MM-DD format)
    "dealDescription": string     // Brief description of the deal (max 500 chars)
  },
  "propertyDetails": {
    "propertyType": string,       // Allowed values: {{PROPERTY_TYPES}} (max 100 chars)
    "assetType": string,          // Allowed values: {{ASSET_TYPES}} (max 100 chars)
    "propertyAddress": string,    // Full property address (max 255 chars)
    "propertyCity": string,       // Normalized city name (max 100 chars)
    "propertyState": string,      // Normalized state name (max 50 chars)
    "propertyZip": string,        // ZIP code (max 20 chars)
    "grossSf": number,            // Gross square footage
    "netSf": number,              // Net square footage
    "lotSize": number,            // Lot size in square feet or acres
    "numResidentialUnits": number, // Number of residential units
    "numCommercialUnits": number, // Number of commercial units
    "hotelKeys": number,          // Number of hotel keys/rooms
    "stories": number,            // Number of stories
    "floors": number,             // Number of floors
    "height": string,             // Building height (max 100 chars)
    "propertySubtype": string,    // More specific property type (max 100 chars)
    "zoningClassification": string, // Zoning classification if mentioned (max 100 chars)
    "permittedUses": [string],    // List of permitted uses
    "propertyFeatures": [string],  // List of property features and amenities
    "investmentCriteriaCountry": string, // Target country for investment (max 100 chars)
    "investmentCriteriaGeographicRegion": string, // Target geographic region (max 100 chars)
    "investmentCriteriaState": string, // Target state(s) (max 255 chars)
    "investmentCriteriaCity": string, // Target city(ies) (max 255 chars)
    "investmentCriteriaDealSize": string, // Preferred deal size range (max 100 chars)
    "investmentCriteriaPropertyType": string, // Preferred property type (max 100 chars)
    "investmentCriteriaPropertyTypeSubcategory": string, // Preferred property subcategory (max 100 chars)
    "investmentCriteriaAssetType": string, // Preferred asset type (max 100 chars)
    "investmentCriteriaLoanType": string, // Preferred loan type (max 100 chars)
    "investmentCriteriaLoanTypeSubcategoryShortTerm": string, // Short term loan subcategory (max 100 chars)
    "investmentCriteriaLoanTypeSubcategoryLongTerm": string, // Long term loan subcategory (max 100 chars)
    "investmentCriteriaLoanTermYears": string, // Preferred loan term in years (max 50 chars)
    "investmentCriteriaLoanInterestRateBasedOff": string, // Interest rate basis (SOFR/WSJ/Prime) (max 50 chars)
    "investmentCriteriaLoanInterestRate": number, // Preferred interest rate as decimal
    "investmentCriteriaLoanToValue": number, // Preferred loan to value ratio as decimal
    "investmentCriteriaLoanToCost": number, // Preferred loan to cost ratio as decimal
    "investmentCriteriaLoanOriginationFeePercent": number, // Preferred origination fee percentage as decimal
    "investmentCriteriaLoanExitFeePercent": number, // Preferred exit fee percentage as decimal
    "investmentCriteriaRecourseLoan": string, // Recourse loan preference (max 50 chars)
    "investmentCriteriaLoanDscr": number, // Preferred debt service coverage ratio as decimal
    "investmentCriteriaClosingTime": string, // Preferred closing time (max 100 chars)
    "investmentCriteriaTearSheet": string // Tear sheet preference (max 100 chars)
  },
  "financialTerms": {
    "loanAmount": number,         // Total loan amount
    "loanTermMonths": number,     // Loan term in months
    "interestRate": number,       // Interest rate as decimal (e.g., 0.075 for 7.5%)
    "interestRateType": string,   // Allowed values: {{INTEREST_RATE_TYPES}} (max 50 chars)
    "sofrPlus": number,           // SOFR plus spread as decimal
    "wsjPrime": number,           // WSJ Prime rate as decimal
    "ust5y": number,              // 5-year Treasury rate as decimal
    "ust7y": number,              // 7-year Treasury rate as decimal
    "ust10y": number,             // 10-year Treasury rate as decimal
    "recourse": string,           // Recourse terms (max 100 chars)
    "attachPoint": number,        // Mezzanine attachment point as decimal
    "ltv": number,                // Loan to value ratio as decimal
    "debtYieldYr1": number,       // Debt yield year 1 as decimal
    "valuePsf": number,           // Value per square foot
    "debtPsf": number,            // Debt per square foot
    "seniorDebtAmount": number,   // Senior debt amount
    "seniorDebtRate": number,     // Senior debt rate as decimal
    "seniorDebtTerm": number,     // Senior debt term in months
    "seniorDebtAmortization": string, // Amortization schedule (max 100 chars)
    "seniorDebtIoPeriod": number, // Interest-only period in months
    "seniorDebtBalanceMaturity": number // Senior debt balance at maturity
  },
  "financialMetrics": {
    "noiYr1": number,             // NOI year 1
    "noiYr2": number,             // NOI year 2
    "noiYr3": number,             // NOI year 3
    "noiYr4": number,             // NOI year 4
    "noiYr5": number,             // NOI year 5
    "yieldOnCostYr1": number,     // Yield on cost year 1 as decimal
    "terminalCapRate": number,    // Terminal cap rate as decimal
    "dscrYr1": number,            // DSCR year 1
    "dscrYr2": number,            // DSCR year 2
    "dscrYr3": number,            // DSCR year 3
    "cocYr1": number,             // Cash on cash return year 1 as decimal
    "cocYr2": number,             // Cash on cash return year 2 as decimal
    "cocYr3": number,             // Cash on cash return year 3 as decimal
    "marketResRatePsf": number,   // Market residential rate per square foot
    "affordableMarketResPsf": number, // Affordable market residential rate per square foot
    "commercialFacilityRentPsf": number, // Commercial facility rent per square foot
    "parkingRentPsf": number      // Parking rent per square foot
  },
  "returns": {
    "gpEquityMultiple": number,   // GP equity multiple
    "gpIrr": number,              // GP IRR as decimal
    "lpEquityMultiple": number,   // LP equity multiple
    "lpIrr": number,              // LP IRR as decimal
    "totalEquityMultiple": number, // Total equity multiple
    "totalIrr": number,           // Total IRR as decimal
    "targetReturn": string,       // Target return range (max 100 chars)
    "returnMetrics": [string]     // List of return metrics mentioned
  },
  "projectBudget": {
    "totalDevelopmentCost": number, // Total development cost
    "acquisitionCost": number,    // Acquisition cost
    "constructionCost": number,   // Construction cost
    "softCosts": number,          // Soft costs
    "financingCosts": number,     // Financing costs
    "budgetBreakdown": {          // Detailed budget breakdown
      "hardCosts": number,        // Hard construction costs
      "landCosts": number,        // Land acquisition costs
      "entitlementCosts": number, // Entitlement and permitting costs
      "contingency": number,      // Contingency reserve
      "otherCosts": number        // Other miscellaneous costs
    }
  },
  "sourcesOfFunds": {
    "seniorDebtFunding": number,  // Senior debt funding amount
    "mezzanineDebtFunding": number, // Mezzanine debt funding amount
    "gpEquityFunding": number,    // GP equity funding amount
    "lpEquityFunding": number,    // LP equity funding amount
    "preferredEquityFunding": number, // Preferred equity funding amount
    "otherSources": [string],     // List of other funding sources
    "totalFunding": number        // Total funding amount
  },
  "usesOfFunds": {
    "acquisitionFunding": number, // Funding for acquisition
    "constructionFunding": number, // Funding for construction
    "totalFunding": number,       // Total funding amount
    "fundingBreakdown": {         // Detailed funding breakdown
      "landAcquisition": number,  // Land acquisition funding
      "construction": number,     // Construction funding
      "workingCapital": number,   // Working capital funding
      "reserves": number,         // Reserve funding
      "otherUses": number         // Other funding uses
    }
  },
  "marketData": {
    "marketTrends": string,       // Market trends analysis (max 500 chars)
    "competitionAnalysis": string, // Competition analysis (max 500 chars)
    "demographics": string,       // Demographics information (max 500 chars)
    "marketComparables": [string], // List of market comparables
    "marketRisks": [string],      // List of market risks
    "marketOpportunities": [string] // List of market opportunities
  },
  "tenantInfo": {
    "tenantMix": string,          // Tenant mix description (max 500 chars)
    "leaseTerms": string,         // Lease terms summary (max 500 chars)
    "occupancyRate": number,      // Current occupancy rate as decimal
    "tenantTypes": [string],      // List of tenant types
    "leaseExpirations": [string], // List of lease expirations
    "tenantCredit": [string]      // List of tenant credit ratings
  },
  "legalEntities": {
    "borrowerEntity": string,     // Borrower entity name (max 255 chars)
    "guarantorEntities": [string], // List of guarantor entities
    "ownershipStructure": string, // Ownership structure description (max 500 chars)
    "partnershipStructure": string, // Partnership structure (max 500 chars)
    "taxConsiderations": [string] // List of tax considerations
  },
  "riskFactors": {
    "marketRisks": [string],      // List of market risks
    "constructionRisks": [string], // List of construction risks
    "financingRisks": [string],   // List of financing risks
    "operationalRisks": [string], // List of operational risks
    "regulatoryRisks": [string],  // List of regulatory risks
    "environmentalRisks": [string], // List of environmental risks
    "riskMitigation": [string]    // List of risk mitigation strategies
  },
  "suggestions": {
    "alternativeCapitalSources": [string], // Suggested alternative capital sources
    "similarPropertyTypes": [string],      // Suggested similar property types
    "relatedGeographicMarkets": [string],  // Suggested related geographic markets
    "potentialPartners": [string],         // Suggested potential partners
    "alternativeDealStructures": [string], // Suggested alternative deal structures
    "recommendations": [string]            // General recommendations
  },
  "metadata": {
    "documentType": string,       // Type of document processed (max 100 chars)
    "extractionConfidence": string, // Overall confidence level (max 50 chars)
    "processingNotes": string,    // Important notes about extraction (max 500 chars)
    "missingCriticalFields": [string], // List of important missing fields
    "dataQualityIssues": [string], // List of data quality issues
    "normalizationApplied": [string], // List of normalizations applied
    "fuzzyMatches": [string],     // List of fuzzy matches made
    "suggestionsProvided": [string], // List of suggestions provided
    "extractionTimestamp": string, // When extraction was performed
    "processorVersion": string,   // Version of processor used
    "llmModelUsed": string,       // LLM model used for extraction
    "llmProvider": string,        // LLM provider
    "extractionMethod": string,   // Method used for extraction
    "documentSource": string,     // Source type of document
    "documentFilename": string,   // Original filename
    "documentSizeBytes": number,  // Size of document in bytes
    "processingDurationMs": number, // Processing time in milliseconds
    "confidenceScores": {         // Confidence scores for different aspects
      "overall": number,          // Overall confidence (0-1)
      "financialData": number,    // Financial data confidence (0-1)
      "propertyData": number,     // Property data confidence (0-1)
      "marketData": number,       // Market data confidence (0-1)
      "normalization": number     // Normalization confidence (0-1)
    },
    "customFields": {},           // Additional fields not in standard schema
    "extraFields": {},            // Extra fields from document processing
    "tags": [string],             // Array of tags for categorizing
    "status": string,             // Processing status
    "reviewStatus": string,       // Review status
    "reviewedBy": string,         // User who reviewed
    "reviewedAt": string,         // When review was performed
    "reviewNotes": string         // Notes from review process
  }
}

**EXTRACTION GUIDELINES:**
1. **Be Extremely Thorough**: Read through ALL content carefully and extract every detail
2. **Normalize All Data**: Apply standard formats and values to all fields
3. **Handle Fuzzy Matching**: Suggest alternatives when exact matches aren't found
4. **Provide Intelligent Suggestions**: Recommend similar options based on deal characteristics
5. **Cross-Reference Data**: Ensure consistency across related fields
6. **Extract Financial Details**: Look for all monetary values, rates, and metrics
7. **Identify Risk Factors**: Extract all mentioned risks and mitigation strategies
8. **Analyze Market Data**: Extract market trends, competition, and demographics
9. **Document Legal Structure**: Extract entity information and ownership details
10. **Assess Data Quality**: Flag any inconsistencies or missing critical information

Document Content:
{{DOCUMENT_TEXT}}

Document Type:
{{DOCUMENT_TYPE}}

Allowed Values:
{
  "DEAL_TYPES": "Acquisition, Development, Refinance, Construction, Joint Venture, Disposition, Mezzanine, Preferred Equity, Bridge Loan, Permanent Loan",
  "DEAL_STAGES": "Underwriting, Due Diligence, Closing, Pre-Development, Construction, Stabilization, Operations, Exit",
  "PRIORITY_LEVELS": "High, Medium, Low, Urgent",
  "EQUITY_TYPES": "GP, LP, Co-GP, Preferred Equity, Common Equity, JV Partner, Sponsor Equity",
  "LOAN_TYPES": "Construction, Permanent, Bridge, Acquisition, Refinance, Mezzanine, Preferred Equity, Senior Debt",
  "PROPERTY_TYPES": "Multifamily, Office, Retail, Industrial, Hospitality, Mixed Use, Single Family, Land, Healthcare, Special Use, Self-Storage, Data Center",
  "ASSET_TYPES": "Core, Core Plus, Value-Add, Opportunistic, Distressed, Rescue Capital, Ground-Up Development",
  "INTEREST_RATE_TYPES": "Fixed, Floating, SOFR-based, Prime-based, Treasury-based, Hybrid"
}

**REMEMBER: Your response MUST be a single valid JSON object and nothing else. Do NOT include markdown, code blocks, or any explanation. BE EXTREMELY THOROUGH in your extraction - every detail matters! APPLY DATA NORMALIZATION and PROVIDE INTELLIGENT SUGGESTIONS!**`;

// Template function for backward compatibility
export const DEAL_EXTRACTION_ENHANCED_FUNCTION = (
  documentText: string,
  documentType: string = "document"
) => {
  return DEAL_EXTRACTION_ENHANCED_USER_TEMPLATE.replace(
    /\{\{DOCUMENT_TEXT\}\}/g,
    documentText
  ).replace(/\{\{DOCUMENT_TYPE\}\}/g, documentType);
};
