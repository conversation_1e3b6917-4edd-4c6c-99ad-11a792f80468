// Test file for deal requirement prompt
// This demonstrates how to use the deal requirement extraction

import { generateDealRequirementPrompt, generateDealRequirementPromptWithText } from "./deal-requirement";

// Sample deal document text for testing
const sampleDealText = `
PROJECT OVERVIEW:
ABC Development is seeking capital for a $50M multifamily development project in Austin, Texas.

CAPITAL REQUIREMENTS:
- Senior Debt: $35M (70% LTV)
- Mezzanine Debt: $5M (10% of total project cost)
- GP Equity: $3M (6% sponsor contribution)
- LP Equity: $7M (14% investor equity)

INVESTMENT TERMS:
- Target IRR: 15-18%
- Hold Period: 5-7 years
- Property Type: Multifamily
- Strategy: Value-Add Development
`;

// Test the base prompt generation
console.log("=== Base Deal Requirement Prompt ===");
const basePrompt = generateDealRequirementPrompt();
console.log(basePrompt.substring(0, 500) + "..."); // Show first 500 chars

// Test the prompt with sample text
console.log("\n=== Deal Requirement Prompt with Sample Text ===");
const promptWithText = generateDealRequirementPromptWithText(sampleDealText);
console.log(promptWithText.substring(0, 500) + "..."); // Show first 500 chars

// Expected output structure
const expectedOutput = {
  capital_requests: [
    {
      capital_type: "Senior Debt",
      amount: 35,
      loan_purpose: "Multifamily development financing",
      notes: "70% LTV, $35M senior debt for Austin multifamily project"
    },
    {
      capital_type: "Mezzanine Debt", 
      amount: 5,
      loan_purpose: "Subordinate financing for development",
      notes: "10% of total project cost, $5M mezzanine debt"
    },
    {
      capital_type: "GP Equity",
      amount: 3,
      loan_purpose: null,
      notes: "6% sponsor contribution, $3M GP equity"
    },
    {
      capital_type: "LP Equity",
      amount: 7,
      loan_purpose: null,
      notes: "14% investor equity, $7M LP equity"
    }
  ]
};

console.log("\n=== Expected Output Structure ===");
console.log(JSON.stringify(expectedOutput, null, 2));

// Usage example for API integration
export async function testDealRequirementExtraction(text: string) {
  try {
    // This would be the actual API call
    const response = await fetch('/api/deals/requirements', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: text,
        model: 'gemini-flash'
      })
    });

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error testing deal requirement extraction:', error);
    throw error;
  }
}

// Export for use in other files
export { sampleDealText, expectedOutput }; 