#!/usr/bin/env node

import { ScrapingProcessor, NewsFetchingProcessor } from './ScrapingProcessor';
import { ScraperManager } from './ScraperManager';
import { BisnowScraper } from './BisnowScraper';
import { NewsFetcher } from './NewsFetcher';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function testScrapingSystem() {
  console.log('🚀 Testing Scraping System...\n');

  // Test 1: Test ScraperManager initialization
  console.log('Test 1: ScraperManager Initialization');
  try {
    const manager = new ScraperManager({
      headless: true,
      maxConcurrentScrapers: 1
    });
    
    await manager.initialize();
    const status = manager.getStatus();
    console.log('✅ ScraperManager initialized successfully');
    console.log(`   Available sites: ${status.availableSites.join(', ')}`);
    await manager.cleanup();
  } catch (error) {
    console.error('❌ ScraperManager initialization failed:', error);
  }

  // Test 2: Test NewsFetcher
  console.log('\nTest 2: NewsFetcher');
  try {
    const newsFetcher = new NewsFetcher({
      headless: true,
      batchSize: 5,
      minContentSize: 1000
    });
    
    console.log('✅ NewsFetcher created successfully');
    
    // Test getting unprocessed news count
    const unprocessedCount = await newsFetcher.getUnprocessedNewsCount();
    console.log(`   Unprocessed news articles: ${unprocessedCount}`);
    
    await newsFetcher.cleanup();
  } catch (error) {
    console.error('❌ NewsFetcher test failed:', error);
  }

  // Test 3: Test ScrapingProcessor
  console.log('\nTest 3: ScrapingProcessor');
  try {
    const processor = new ScrapingProcessor('TestScrapingProcessor');
    
    console.log('✅ ScrapingProcessor created successfully');
    console.log('   Note: Running full scraping would take time, skipping actual scraping');
    
    // We won't run the full process in test mode as it would take too long
    // processor.process() would run the full scraping
    
  } catch (error) {
    console.error('❌ ScrapingProcessor test failed:', error);
  }

  // Test 4: Test NewsFetchingProcessor
  console.log('\nTest 4: NewsFetchingProcessor');
  try {
    const processor = new NewsFetchingProcessor('TestNewsFetchingProcessor');
    
    console.log('✅ NewsFetchingProcessor created successfully');
    console.log('   Note: Running full fetching would take time, skipping actual fetching');
    
    // We won't run the full process in test mode as it would take too long
    // processor.process() would run the full fetching
    
  } catch (error) {
    console.error('❌ NewsFetchingProcessor test failed:', error);
  }

  // Test 5: Test Database Connection
  console.log('\nTest 5: Database Connection');
  try {
    const { pool } = await import('../db');
    const client = await pool.connect();
    
    // Test basic query
    const result = await client.query('SELECT NOW() as current_time');
    console.log('✅ Database connection successful');
    console.log(`   Current time: ${result.rows[0].current_time}`);
    
    client.release();
  } catch (error) {
    console.error('❌ Database connection failed:', error);
  }

  console.log('\n🎉 Testing completed!');
}

// Run tests if this file is executed directly
if (process.argv[1] === __filename) {
  testScrapingSystem().catch(console.error);
}

export { testScrapingSystem }; 