#!/usr/bin/env node

/**
 * Test script to run a single scraper and see detailed output
 * 
 * Usage:
 * npx tsx src/lib/scrapers/test-single-scraper.ts [siteName] [maxPages]
 * 
 * Examples:
 * npx tsx src/lib/scrapers/test-single-scraper.ts bisnow 5
 * npx tsx src/lib/scrapers/test-single-scraper.ts therealdeal 3
 */

import { ScraperManager } from './ScraperManager';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function testSingleScraper(siteName: string, maxPages: number = 5): Promise<void> {
  console.log(`🚀 Testing individual scraper for: ${siteName}`);
  console.log(`📄 Max pages to scrape: ${maxPages}`);
  console.log(`🔓 Login attempt: false (guest access only)\n`);

  const manager = new ScraperManager({
    headless: false, // Set to false to see what's happening
    maxConcurrentScrapers: 1,
    waitTimeBetweenSites: 1000
  });

  try {
    // Initialize the manager
    console.log('🔧 Initializing ScraperManager...');
    await manager.initialize();
    
    const status = manager.getStatus();
    console.log(`✅ ScraperManager initialized. Available sites: ${status.availableSites.join(', ')}`);
    
    if (!status.availableSites.includes(siteName)) {
      console.error(`❌ Site '${siteName}' not found. Available sites: ${status.availableSites.join(', ')}`);
      return;
    }
    
    console.log(`\n🎯 Starting scraper for: ${siteName}`);
    console.log('═'.repeat(50));
    
    const startTime = Date.now();
    
    // Run the single scraper
    const result = await manager.runSingleScraper(siteName, false, maxPages);
    
    const duration = Date.now() - startTime;
    
    console.log('═'.repeat(50));
    console.log(`\n📊 Scraping Results for ${siteName}:`);
    console.log(`⏱️  Duration: ${Math.round(duration / 1000)}s`);
    console.log(`✅ Success: ${result.success}`);
    console.log(`🔗 New links found: ${result.newLinksFound}`);
    console.log(`📊 Total links processed: ${result.totalLinksProcessed}`);
    
    if (result.errors.length > 0) {
      console.log(`❌ Errors (${result.errors.length}):`);
      result.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    } else {
      console.log(`✅ No errors occurred`);
    }
    
    // Check database for recent entries
    console.log(`\n🗄️  Checking database for recent ${siteName} entries...`);
    await checkRecentEntries(siteName);
    
  } catch (error) {
    console.error(`❌ Error during testing: ${error}`);
  } finally {
    // Clean up
    console.log('\n🧹 Cleaning up...');
    await manager.cleanup();
    console.log('✅ Cleanup completed');
  }
}

async function checkRecentEntries(siteName: string): Promise<void> {
  try {
    const { pool } = await import('../db');
    const client = await pool.connect();
    
    // Get recent entries for this news source
    const result = await client.query(`
      SELECT 
        id, 
        url, 
        news_source, 
        fetched, 
        enriched, 
        created_at
      FROM news 
      WHERE news_source = $1 
      ORDER BY created_at DESC 
      LIMIT 10
    `, [siteName]);
    
    client.release();
    
    if (result.rows.length === 0) {
      console.log(`   ⚠️  No entries found for news source: ${siteName}`);
      
      // Check if there are any entries at all
      const totalClient = await pool.connect();
      const totalResult = await totalClient.query('SELECT COUNT(*) as count FROM news');
      totalClient.release();
      
      const totalCount = totalResult.rows[0].count;
      console.log(`   📊 Total entries in news table: ${totalCount}`);
    } else {
      console.log(`   📋 Recent ${siteName} entries (showing ${result.rows.length}):`);
      
      result.rows.forEach((row, index) => {
        const createdAt = new Date(row.created_at).toLocaleString();
        const status = `F:${row.fetched ? '✅' : '❌'} E:${row.enriched ? '✅' : '❌'}`;
        console.log(`   ${index + 1}. [${createdAt}] ${status} ${row.url.substring(0, 80)}...`);
      });
      
      // Get counts by status
      const statusClient = await pool.connect();
      const statusResult = await statusClient.query(`
        SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN fetched THEN 1 ELSE 0 END) as fetched_count,
          SUM(CASE WHEN enriched THEN 1 ELSE 0 END) as enriched_count
        FROM news 
        WHERE news_source = $1
      `, [siteName]);
      statusClient.release();
      
      const stats = statusResult.rows[0];
      console.log(`   📈 ${siteName} Statistics:`);
      console.log(`      Total: ${stats.total}`);
      console.log(`      Fetched: ${stats.fetched_count} (${Math.round((stats.fetched_count / stats.total) * 100)}%)`);
      console.log(`      Enriched: ${stats.enriched_count} (${Math.round((stats.enriched_count / stats.total) * 100)}%)`);
    }
    
  } catch (error) {
    console.error(`   ❌ Error checking database: ${error}`);
  }
}

async function listAvailableSites(): Promise<void> {
  const manager = new ScraperManager();
  await manager.initialize();
  const status = manager.getStatus();
  await manager.cleanup();
  
  console.log('\n📋 Available sites:');
  status.availableSites.forEach((site, index) => {
    console.log(`   ${index + 1}. ${site}`);
  });
}

async function main(): Promise<void> {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
    console.log('🔍 Single Scraper Test Tool');
    console.log('\nUsage: npx tsx src/lib/scrapers/test-single-scraper.ts [siteName] [maxPages]');
    console.log('\nExamples:');
    console.log('  npx tsx src/lib/scrapers/test-single-scraper.ts bisnow 5');
    console.log('  npx tsx src/lib/scrapers/test-single-scraper.ts therealdeal 3');
    console.log('  npx tsx src/lib/scrapers/test-single-scraper.ts --list');
    console.log('\nOptions:');
    console.log('  --list, -l    List available sites');
    console.log('  --help, -h    Show this help message');
    return;
  }
  
  if (args[0] === '--list' || args[0] === '-l') {
    await listAvailableSites();
    return;
  }
  
  const siteName = args[0];
  const maxPages = args[1] ? parseInt(args[1], 10) : 5;
  
  if (isNaN(maxPages) || maxPages <= 0) {
    console.error('❌ Invalid maxPages value. Must be a positive number.');
    return;
  }
  
  console.log('🚀 Starting single scraper test...\n');
  await testSingleScraper(siteName, maxPages);
  console.log('\n🎉 Test completed!');
}

// Run if this file is executed directly
if (process.argv[1] === __filename) {
  main().catch((error) => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}

export { testSingleScraper, checkRecentEntries }; 