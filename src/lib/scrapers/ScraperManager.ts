import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { BaseScraper, SiteConfig, ScrapingResult } from './BaseScraper';
import { BisnowScraper } from './BisnowScraper';
import { TheRealDealScraper } from './TheRealDealScraper';
import { GlobestScraper } from './GlobestScraper';
import { PincusScraper } from './PincusScraper';
import { NewsFetcher } from './NewsFetcher';

export interface ScraperManagerOptions {
  headless?: boolean;
  maxConcurrentScrapers?: number;
  scraperTimeout?: number;
  waitTimeBetweenSites?: number;
  restartBrowserFrequency?: number;
}

export interface ScrapingSession {
  sessionId: string;
  startTime: Date;
  endTime?: Date;
  results: Map<string, ScrapingResult>;
  errors: string[];
  totalNewLinks: number;
  totalProcessed: number;
}

export class ScraperManager {
  private browser: Browser | null = null;
  private options: Required<ScraperManagerOptions>;
  private siteConfigs: Map<string, SiteConfig> = new Map();
  private scraperInstances: Map<string, BaseScraper> = new Map();
  private newsFetcher: NewsFetcher | null = null;
  private isRunning: boolean = false;
  private currentSession: ScrapingSession | null = null;
  private processedCount: number = 0;

  constructor(options: ScraperManagerOptions = {}) {
    this.options = {
      headless: options.headless ?? true,
      maxConcurrentScrapers: options.maxConcurrentScrapers ?? 1,
      scraperTimeout: options.scraperTimeout ?? 30 * 60 * 1000, // 30 minutes
      waitTimeBetweenSites: options.waitTimeBetweenSites ?? 5000,
      restartBrowserFrequency: options.restartBrowserFrequency ?? 100
    };

    this.initializeDefaultConfigs();
  }

  private initializeDefaultConfigs(): void {
    // Bisnow configuration
    this.siteConfigs.set('bisnow', {
      name: 'bisnow',
      domain: 'bisnow.com',
      home_url: 'https://www.bisnow.com',
      login_trigger_class: 'logIn',
      email_field_id: 'login_email_signin',
      password_field_id: 'login_password',
      submit_button_class: 'do-login-btn',
      success_indicator_xpath: "//*[contains(text(), 'My Account')]",
      already_logged_in_indicators: ['My Account'],
      not_logged_in_indicators: ['Sign In', 'Log In', 'Login', 'Sign Up', 'Register'],
      email: process.env.BISNOW_EMAIL,
      password: process.env.BISNOW_PASSWORD
    });

    // TheRealDeal configuration
    this.siteConfigs.set('therealdeal', {
      name: 'therealdeal',
      domain: 'therealdeal.com',
      home_url: 'https://therealdeal.com',
      login_url: 'https://therealdeal.com/login/',
      alternate_login_url: 'https://therealdeal.com/account/login/',
      profile_url: 'https://therealdeal.com/account/',
      email_field_selector: 'input[name="email"], input#username',
      password_field_selector: 'input[type="password"], input#password',
      submit_button_selector: 'button[type="submit"], button.login-btn',
      already_logged_in_indicators: ['My Account', 'Log Out', 'Sign Out'],
      not_logged_in_indicators: ['Sign In', 'Log In', 'Login', 'Sign Up', 'Register'],
      email: process.env.THEREALDEAL_EMAIL,
      password: process.env.THEREALDEAL_PASSWORD
    });

    // GlobeSt configuration
    this.siteConfigs.set('globest', {
      name: 'globest',
      domain: 'globest.com',
      home_url: 'https://www.globest.com',
      login_url: 'https://alm.dragonforms.com/loading.do?omedasite=ALMMD_GLOBEST_Log&returnUrl=https%3A%2F%2Fwww.globest.com%2F',
      email_field_name: 'demo146750',
      email_field_id: 'id13',
      password_field_name: 'demo146753',
      password_field_id: 'id16',
      remember_me_id: 'id721_658',
      submit_button_selector: 'div#submitbtn input[type="submit"]',
      already_logged_in_indicators: ['My Account', 'Sign Out', 'Account'],
      not_logged_in_indicators: ['Sign In', 'Log In', 'Login', 'Sign Up', 'Register'],
      email: process.env.GLOBEST_EMAIL,
      password: process.env.GLOBEST_PASSWORD
    });

    // Pincus configuration
    this.siteConfigs.set('pincus', {
      name: 'pincus',
      domain: 'pincusco.com',
      home_url: 'https://www.pincusco.com',
      login_url: 'https://www.pincusco.com/wp-login.php',
      email_field_id: 'user_login',
      password_field_id: 'user_pass',
      submit_button_id: 'wp-submit',
      remember_me_id: 'rememberme',
      already_logged_in_indicators: ['Log out', 'My Account', 'Subscriber Profile'],
      not_logged_in_indicators: ['Sign In', 'Log In', 'Login', 'Sign Up', 'Register'],
      email: process.env.PINCUS_EMAIL,
      password: process.env.PINCUS_PASSWORD
    });
  }

  async initialize(): Promise<void> {
    try {
      console.log('[INFO] Initializing ScraperManager...');
      
      this.browser = await puppeteer.launch({
        headless: this.options.headless,
        args: [
          '--no-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
          '--disable-extensions',
          '--disable-popup-blocking',
          '--enable-cookies',
          '--disable-cookie-encryption',
          '--dns-prefetch-disable=false',
          '--enable-features=NetworkServiceInProcess',
          '--page-load-strategy=eager',
          '--dns-servers=8.8.8.8,8.8.4.4',
          '--socket-reuse-policy=2',
          '--http-cache'
        ]
      });

      // Initialize NewsFetcher
      this.newsFetcher = new NewsFetcher({
        headless: this.options.headless,
        batchSize: 20
      });

      console.log('[INFO] ScraperManager initialized successfully.');
    } catch (error) {
      console.error('[ERROR] Failed to initialize ScraperManager:', error);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    try {
      console.log('[INFO] Cleaning up ScraperManager...');
      
      // Clear scrapers
      this.scraperInstances.clear();
      
      // Cleanup NewsFetcher
      if (this.newsFetcher) {
        await this.newsFetcher.cleanup();
        this.newsFetcher = null;
      }
      
      // Close browser
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }
      
      this.isRunning = false;
      console.log('[INFO] ScraperManager cleanup completed.');
    } catch (error) {
      console.error('[ERROR] Error during ScraperManager cleanup:', error);
    }
  }

  private async restartBrowserIfNeeded(): Promise<void> {
    if (this.processedCount >= this.options.restartBrowserFrequency) {
      console.log(`[INFO] Restarting browser after processing ${this.processedCount} operations...`);
      
      // Clear existing scrapers
      this.scraperInstances.clear();
      
      // Restart browser
      if (this.browser) {
        await this.browser.close();
      }
      
      this.browser = await puppeteer.launch({
        headless: this.options.headless,
        args: [
          '--no-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
          '--disable-extensions',
          '--disable-popup-blocking',
          '--enable-cookies',
          '--disable-cookie-encryption'
        ]
      });
      
      this.processedCount = 0;
      console.log('[INFO] Browser restarted successfully.');
    }
  }

  private async createScraperInstance(siteName: string): Promise<BaseScraper | undefined> {
    if (!this.browser) {
      await this.initialize();
    }

    const config = this.siteConfigs.get(siteName);
    if (!config) {
      console.error(`[ERROR] No configuration found for site: ${siteName}`);
      return undefined;
    }

    try {
      const page = await this.browser!.newPage();
      
      // Set user agent to avoid bot detection
      await page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      );

      let scraper: BaseScraper;

      switch (siteName) {
        case 'bisnow':
          scraper = new BisnowScraper(this.browser!, page, config, 1000);
          break;
        case 'therealdeal':
          scraper = new TheRealDealScraper(this.browser!, page, config, 1000);
          break;
        case 'globest':
          scraper = new GlobestScraper(this.browser!, page, config, 1000);
          break;
        case 'pincus':
          scraper = new PincusScraper(this.browser!, page, config, 1000);
          break;
        default:
          console.warn(`[WARNING] Scraper not implemented for site: ${siteName}`);
          await page.close();
          return undefined;
      }

      this.scraperInstances.set(siteName, scraper);
      return scraper;
    } catch (error) {
      console.error(`[ERROR] Failed to create scraper for ${siteName}:`, error);
      return undefined;
    }
  }

  async runSingleScraper(siteName: string, tryLogin: boolean = false, maxPages: number = 30): Promise<ScrapingResult> {
    const defaultResult: ScrapingResult = {
      newLinksFound: 0,
      totalLinksProcessed: 0,
      errors: [`Failed to initialize scraper for ${siteName}`],
      success: false
    };

    try {
      await this.restartBrowserIfNeeded();
      
      console.log(`[INFO] Starting scraper for ${siteName}`);
      
      let scraper = this.scraperInstances.get(siteName);
      if (!scraper) {
        scraper = await this.createScraperInstance(siteName);
        if (!scraper) {
          return defaultResult;
        }
      }

      // Set timeout for scraper
      const timeoutPromise = new Promise<ScrapingResult>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Scraper timeout after ${this.options.scraperTimeout}ms`));
        }, this.options.scraperTimeout);
      });

      const scraperPromise = scraper.runScraper(tryLogin, maxPages);
      
      const result = await Promise.race([scraperPromise, timeoutPromise]);
      
      this.processedCount++;
      console.log(`[INFO] Completed scraping for ${siteName}. Found ${result.newLinksFound} new links.`);
      
      return result;
    } catch (error) {
      console.error(`[ERROR] Error running scraper for ${siteName}:`, error);
      return {
        ...defaultResult,
        errors: [String(error)]
      };
    }
  }

  async runAllScrapers(tryLogin: boolean = false, maxPages: number = 30): Promise<ScrapingSession> {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const session: ScrapingSession = {
      sessionId,
      startTime: new Date(),
      results: new Map(),
      errors: [],
      totalNewLinks: 0,
      totalProcessed: 0
    };

    this.currentSession = session;
    this.isRunning = true;

    try {
      console.log(`[INFO] Starting scraping session ${sessionId} for all sites...`);
      
      if (!this.browser) {
        await this.initialize();
      }

      const enabledSites = Array.from(this.siteConfigs.keys());
      console.log(`[INFO] Running scrapers for sites: ${enabledSites.join(', ')}`);

      for (const siteName of enabledSites) {
        if (!this.isRunning) {
          console.log(`[INFO] Scraping session ${sessionId} was stopped.`);
          break;
        }

        try {
          console.log(`\n[INFO] Processing site: ${siteName}`);
          const result = await this.runSingleScraper(siteName, tryLogin, maxPages);
          
          session.results.set(siteName, result);
          session.totalNewLinks += result.newLinksFound;
          session.totalProcessed += result.totalLinksProcessed;
          
          if (!result.success) {
            session.errors.push(`${siteName}: ${result.errors.join(', ')}`);
          }

          // Wait between sites to avoid overwhelming servers
          if (enabledSites.indexOf(siteName) < enabledSites.length - 1) {
            console.log(`[INFO] Waiting ${this.options.waitTimeBetweenSites}ms before next site...`);
            await new Promise(resolve => setTimeout(resolve, this.options.waitTimeBetweenSites));
          }
        } catch (error) {
          console.error(`[ERROR] Error processing site ${siteName}:`, error);
          session.errors.push(`${siteName}: ${String(error)}`);
        }
      }

      session.endTime = new Date();
      const duration = session.endTime.getTime() - session.startTime.getTime();
      
      console.log(`\n[INFO] Scraping session ${sessionId} completed in ${Math.round(duration / 1000)}s`);
      console.log(`[INFO] Total new links found: ${session.totalNewLinks}`);
      console.log(`[INFO] Total links processed: ${session.totalProcessed}`);
      
      if (session.errors.length > 0) {
        console.log(`[WARNING] Errors encountered: ${session.errors.length}`);
      }

    } catch (error) {
      console.error(`[ERROR] Error in scraping session ${sessionId}:`, error);
      session.errors.push(String(error));
      session.endTime = new Date();
    } finally {
      this.isRunning = false;
      this.currentSession = null;
    }

    return session;
  }

  async runNewsFetcher(): Promise<void> {
    if (!this.newsFetcher) {
      this.newsFetcher = new NewsFetcher({
        headless: this.options.headless
      });
    }

    console.log('[INFO] Running news fetcher...');
    const results = await this.newsFetcher.fetchUnprocessedNews();
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    console.log(`[INFO] News fetcher completed. Success: ${successful}, Failed: ${failed}`);
  }

  async startContinuousOperation(scrapingIntervalMs: number = 3600000, fetchingIntervalMs: number = 300000): Promise<void> {
    console.log(`[INFO] Starting continuous scraping operation...`);
    console.log(`[INFO] Scraping interval: ${scrapingIntervalMs}ms (${Math.round(scrapingIntervalMs / 60000)} minutes)`);
    console.log(`[INFO] Fetching interval: ${fetchingIntervalMs}ms (${Math.round(fetchingIntervalMs / 60000)} minutes)`);

    // Run initial scraping
    await this.runAllScrapers();

    // Set up scraping interval
    const scrapingInterval = setInterval(async () => {
      if (!this.isRunning) {
        try {
          await this.runAllScrapers();
        } catch (error) {
          console.error('[ERROR] Error in scheduled scraping:', error);
        }
      } else {
        console.log('[INFO] Previous scraping session still running, skipping this cycle...');
      }
    }, scrapingIntervalMs);

    // Set up fetching interval
    const fetchingInterval = setInterval(async () => {
      try {
        await this.runNewsFetcher();
      } catch (error) {
        console.error('[ERROR] Error in scheduled news fetching:', error);
      }
    }, fetchingIntervalMs);

    // Handle graceful shutdown
    const shutdown = async () => {
      console.log('[INFO] Shutting down continuous operation...');
      clearInterval(scrapingInterval);
      clearInterval(fetchingInterval);
      await this.cleanup();
      process.exit(0);
    };

    process.on('SIGINT', shutdown);
    process.on('SIGTERM', shutdown);
  }

  stopCurrentSession(): void {
    if (this.isRunning && this.currentSession) {
      console.log(`[INFO] Stopping current scraping session ${this.currentSession.sessionId}...`);
      this.isRunning = false;
    } else {
      console.log('[INFO] No active scraping session to stop.');
    }
  }

  getStatus(): {
    isRunning: boolean;
    currentSession: ScrapingSession | null;
    availableSites: string[];
    processedCount: number;
  } {
    return {
      isRunning: this.isRunning,
      currentSession: this.currentSession,
      availableSites: Array.from(this.siteConfigs.keys()),
      processedCount: this.processedCount
    };
  }

  addSiteConfig(siteName: string, config: SiteConfig): void {
    this.siteConfigs.set(siteName, config);
    console.log(`[INFO] Added configuration for site: ${siteName}`);
  }

  removeSiteConfig(siteName: string): boolean {
    const removed = this.siteConfigs.delete(siteName);
    if (removed) {
      // Also remove scraper instance if it exists
      this.scraperInstances.delete(siteName);
      console.log(`[INFO] Removed configuration for site: ${siteName}`);
    }
    return removed;
  }
} 