import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { pool } from '@/lib/db';

export interface FetchResult {
  id: number;
  url: string;
  success: boolean;
  error?: string;
  contentLength?: number;
}

export interface NewsFetcherOptions {
  headless?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  pageLoadTimeout?: number;
  minContentSize?: number;
  batchSize?: number;
  restartFrequency?: number;
}

export class NewsFetcher {
  private browser: Browser | null = null;
  private page: Page | null = null;
  private options: Required<NewsFetcherOptions>;
  private processedCount: number = 0;

  constructor(options: NewsFetcherOptions = {}) {
    this.options = {
      headless: options.headless ?? true,
      maxRetries: options.maxRetries ?? 3,
      retryDelay: options.retryDelay ?? 2000,
      pageLoadTimeout: options.pageLoadTimeout ?? 30000,
      minContentSize: options.minContentSize ?? 1000,
      batchSize: options.batchSize ?? 50,
      restartFrequency: options.restartFrequency ?? 50
    };
  }

  async initialize(): Promise<void> {
    try {
      console.log('[INFO] Launching browser for news fetching...');
      this.browser = await puppeteer.launch({
        headless: this.options.headless,
        args: [
          '--no-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
          '--disable-extensions',
          '--disable-popup-blocking',
          '--dns-prefetch-disable=false',
          '--enable-features=NetworkServiceInProcess',
          '--page-load-strategy=eager',
          '--dns-servers=8.8.8.8,8.8.4.4',
          '--socket-reuse-policy=2',
          '--http-cache'
        ]
      });

      this.page = await this.browser.newPage();
      
      // Set page configurations
      await this.page.setDefaultTimeout(this.options.pageLoadTimeout);
      await this.page.setDefaultNavigationTimeout(this.options.pageLoadTimeout);
      
      // Set user agent to avoid bot detection
      await this.page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      );

      console.log('[INFO] Browser initialized successfully for news fetching.');
    } catch (error) {
      console.error('[ERROR] Failed to initialize browser:', error);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    try {
      if (this.page) {
        await this.page.close();
        this.page = null;
      }
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }
      console.log('[INFO] Browser cleanup completed.');
    } catch (error) {
      console.error('[ERROR] Error during browser cleanup:', error);
    }
  }

  private async restartBrowserIfNeeded(): Promise<void> {
    if (this.processedCount >= this.options.restartFrequency) {
      console.log(`[INFO] Restarting browser after processing ${this.processedCount} URLs...`);
      await this.cleanup();
      await this.initialize();
      this.processedCount = 0;
    }
  }

  async fetchUnprocessedNews(): Promise<FetchResult[]> {
    if (!this.browser || !this.page) {
      await this.initialize();
    }

    const client = await pool.connect();
    const results: FetchResult[] = [];

    try {
      // Query for rows that haven't been fetched yet - exclude bad URLs
      const query = `
        SELECT id, url
        FROM news
        WHERE fetched = false
        AND (bad_url IS NULL OR bad_url = false)
        ORDER BY id DESC
        LIMIT $1
      `;
      
      const queryResult = await client.query(query, [this.options.batchSize]);
      const rows = queryResult.rows;
      
      console.log(`[INFO] Found ${rows.length} URLs to fetch.`);

      for (const row of rows) {
        const { id, url } = row;
        console.log(`\n[INFO] Processing ID=${id}, URL=${url}`);
        
        const fetchResult = await this.fetchSingleUrl(id, url, client);
        results.push(fetchResult);
        
        this.processedCount++;
        await this.restartBrowserIfNeeded();
        
        // Add delay between requests
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

    } catch (error) {
      console.error('[ERROR] Error in fetchUnprocessedNews:', error);
    } finally {
      client.release();
    }

    return results;
  }

  private async fetchSingleUrl(id: number, url: string, client: any): Promise<FetchResult> {
    const result: FetchResult = {
      id,
      url,
      success: false
    };

    for (let attempt = 1; attempt <= this.options.maxRetries; attempt++) {
      try {
        console.log(`[INFO] Attempt ${attempt}/${this.options.maxRetries} - Navigating to URL...`);
        
        // Navigate to URL
        const currentUrl = this.page!.url();
        await this.page!.goto(url, { 
          waitUntil: 'networkidle2', 
          timeout: this.options.pageLoadTimeout 
        });
        
        // Wait for initial load
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Verify we actually navigated to the new URL
        const newUrl = this.page!.url();
        if (newUrl === currentUrl) {
          throw new Error('Failed to navigate - still on previous URL');
        }

        // Verify we're on the correct URL (or a redirect from it)
        if (!newUrl.startsWith(url) && !url.startsWith(newUrl)) {
          console.log(`[WARNING] URL mismatch - expected: ${url}, got: ${newUrl}`);
        }

        // Get page content
        const rawHtml = await this.page!.content();
        
        // Verify we got meaningful content
        if (rawHtml.length < this.options.minContentSize) {
          console.log(`[WARNING] Page content seems too small (${rawHtml.length} chars), waiting 5 more seconds...`);
          await new Promise(resolve => setTimeout(resolve, 5000));
          const retryHtml = await this.page!.content();
          
          if (retryHtml.length < this.options.minContentSize) {
            throw new Error(`Page content still too small after waiting (${retryHtml.length} chars)`);
          }
          
          // Use the retry content
          result.contentLength = retryHtml.length;
          console.log(`[INFO] Successfully navigated to ${newUrl}`);
          console.log(`[INFO] Fetched ${retryHtml.length} characters of HTML.`);

          // Update database
          await this.updateDatabase(client, id, retryHtml);
        } else {
          result.contentLength = rawHtml.length;
          console.log(`[INFO] Successfully navigated to ${newUrl}`);
          console.log(`[INFO] Fetched ${rawHtml.length} characters of HTML.`);

          // Update database
          await this.updateDatabase(client, id, rawHtml);
        }

        result.success = true;
        console.log(`[INFO] Successfully updated row ID=${id} as fetched.`);
        break;

      } catch (error: any) {
        const errorMessage = error.message || String(error);
        console.log(`[ERROR] Attempt ${attempt} failed for ID=${id} (${url}): ${errorMessage}`);
        
        if (attempt === this.options.maxRetries) {
          result.error = errorMessage;
          console.log(`[ERROR] All attempts failed for ID=${id}. Marking as failed.`);
          
          // Mark as failed in database
          try {
            await client.query(
              'UPDATE news SET fetched = true, fetch_error = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
              [errorMessage, id]
            );
          } catch (dbError) {
            console.log(`[ERROR] Failed to mark ID=${id} as failed in database: ${dbError}`);
          }
        } else {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, this.options.retryDelay * attempt));
        }
      }
    }

    return result;
  }

  private async updateDatabase(client: any, id: number, rawHtml: string): Promise<void> {
    try {
      const updateQuery = `
        UPDATE news
        SET raw_html = $1,
            fetched = true,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `;
      
      await client.query(updateQuery, [rawHtml, id]);
    } catch (error) {
      console.log(`[ERROR] Failed to update database for ID=${id}: ${error}`);
      throw error;
    }
  }

  async fetchSpecificUrl(id: number, url: string): Promise<FetchResult> {
    if (!this.browser || !this.page) {
      await this.initialize();
    }

    const client = await pool.connect();
    
    try {
      const result = await this.fetchSingleUrl(id, url, client);
      return result;
    } finally {
      client.release();
    }
  }

  async getUnfetchedCount(): Promise<number> {
    const client = await pool.connect();
    
    try {
      const result = await client.query(
        'SELECT COUNT(*) as count FROM news WHERE fetched = false AND (bad_url IS NULL OR bad_url = false)'
      );
      return parseInt(result.rows[0].count);
    } finally {
      client.release();
    }
  }

  async getUnprocessedNewsCount(): Promise<number> {
    return this.getUnfetchedCount();
  }

  async runContinuousFetching(intervalMs: number = 60000): Promise<void> {
    console.log(`[INFO] Starting continuous news fetching with ${intervalMs}ms interval...`);
    
    const fetchCycle = async () => {
      try {
        const unfetchedCount = await this.getUnfetchedCount();
        if (unfetchedCount > 0) {
          console.log(`[INFO] Found ${unfetchedCount} unfetched URLs. Starting fetch cycle...`);
          const results = await this.fetchUnprocessedNews();
          const successful = results.filter(r => r.success).length;
          const failed = results.filter(r => !r.success).length;
          console.log(`[INFO] Fetch cycle completed. Success: ${successful}, Failed: ${failed}`);
        } else {
          console.log('[INFO] No unfetched URLs found.');
        }
      } catch (error) {
        console.error('[ERROR] Error in fetch cycle:', error);
      }
    };

    // Run initial fetch
    await fetchCycle();
    
    // Set up interval
    const intervalId = setInterval(fetchCycle, intervalMs);
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('[INFO] Received SIGINT, shutting down news fetcher...');
      clearInterval(intervalId);
      this.cleanup().then(() => {
        process.exit(0);
      });
    });

    process.on('SIGTERM', () => {
      console.log('[INFO] Received SIGTERM, shutting down news fetcher...');
      clearInterval(intervalId);
      this.cleanup().then(() => {
        process.exit(0);
      });
    });
  }
} 