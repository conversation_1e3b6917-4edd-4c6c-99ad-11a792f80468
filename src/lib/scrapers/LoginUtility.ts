import { Page } from 'puppeteer';
import * as cheerio from 'cheerio';

export interface LoginConfiguration {
  siteName: string;
  domain: string;
  
  // URLs
  homeUrl: string;
  loginUrl?: string;
  alternateLoginUrl?: string;
  profileUrl?: string;
  
  // Login form selectors
  loginTriggerClass?: string;
  loginTriggerSelector?: string;
  emailFieldId?: string;
  emailFieldSelector?: string;
  emailFieldName?: string;
  passwordFieldId?: string;
  passwordFieldSelector?: string;
  passwordFieldName?: string;
  submitButtonId?: string;
  submitButtonClass?: string;
  submitButtonSelector?: string;
  rememberMeId?: string;
  rememberMeSelector?: string;
  
  // Success/failure indicators
  successIndicatorXpath?: string;
  successIndicatorSelector?: string;
  alreadyLoggedInIndicators: string[];
  notLoggedInIndicators: string[];
  
  // Credentials
  email?: string;
  password?: string;
  
  // Timeouts and options
  loginTimeout?: number;
  waitAfterLogin?: number;
  takeScreenshots?: boolean;
  
  // Custom login flow handlers
  customLoginHandler?: (page: Page, config: LoginConfiguration) => Promise<boolean>;
  customLoggedInChecker?: (page: Page, config: LoginConfiguration) => Promise<boolean>;
}

export interface LoginResult {
  success: boolean;
  alreadyLoggedIn: boolean;
  error?: string;
  message?: string;
  screenshots?: string[];
}

export class LoginUtility {
  private configs: Map<string, LoginConfiguration> = new Map();
  private logger: (level: string, message: string) => void;
  
  constructor(logger?: (level: string, message: string) => void) {
    this.logger = logger || ((level, message) => console.log(`[${level.toUpperCase()}] ${message}`));
    this.initializeDefaultConfigs();
  }

  private initializeDefaultConfigs(): void {
    // Bisnow configuration
    this.configs.set('bisnow', {
      siteName: 'bisnow',
      domain: 'bisnow.com',
      homeUrl: 'https://www.bisnow.com',
      loginTriggerClass: 'logIn',
      emailFieldId: 'login_email_signin',
      passwordFieldId: 'login_password',
      submitButtonClass: 'do-login-btn',
      successIndicatorXpath: "//*[contains(text(), 'My Account')]",
      alreadyLoggedInIndicators: ['My Account'],
      notLoggedInIndicators: ['Sign In', 'Log In', 'Login', 'Sign Up', 'Register'],
      email: process.env.BISNOW_EMAIL || '<EMAIL>',
      password: process.env.BISNOW_PASSWORD || '@!!sN8MvZrwueuh',
      loginTimeout: 15000,
      waitAfterLogin: 2000,
      takeScreenshots: true
    });

    // TheRealDeal configuration
    this.configs.set('therealdeal', {
      siteName: 'therealdeal',
      domain: 'therealdeal.com',
      homeUrl: 'https://therealdeal.com',
      loginUrl: 'https://therealdeal.com/login/',
      alternateLoginUrl: 'https://therealdeal.com/account/login/',
      profileUrl: 'https://therealdeal.com/account/',
      emailFieldSelector: 'input#username[name="email"]',
      passwordFieldSelector: 'input#password[type="password"]',
      submitButtonSelector: 'button[actionlogin][type="submit"], button[type="submit"]',
      rememberMeSelector: 'remember-me input[type="checkbox"], .consent input[type="checkbox"]',
      alreadyLoggedInIndicators: ['My Account', 'Log Out', 'Sign Out', 'Profile', 'dashboard'],
      notLoggedInIndicators: ['Sign In', 'Log In', 'Login', 'Sign Up', 'Register', 'piano__title'],
      email: process.env.THEREALDEAL_EMAIL || '<EMAIL>',
      password: process.env.THEREALDEAL_PASSWORD || 'Brooklyn1',
      loginTimeout: 30000,
      waitAfterLogin: 3000,
      takeScreenshots: true
    });

    // GlobeSt configuration
    this.configs.set('globest', {
      siteName: 'globest',
      domain: 'globest.com',
      homeUrl: 'https://www.globest.com',
      loginUrl: 'https://alm.dragonforms.com/loading.do?omedasite=ALMMD_GLOBEST_Log&returnUrl=https%3A%2F%2Fwww.globest.com%2F',
      emailFieldName: 'demo146750',
      emailFieldId: 'id13',
      passwordFieldName: 'demo146753',
      passwordFieldId: 'id16',
      rememberMeId: 'id721_658',
      submitButtonSelector: 'div#submitbtn input[type="submit"]',
      alreadyLoggedInIndicators: ['My Account', 'Sign Out', 'Account', 'Profile'],
      notLoggedInIndicators: ['Sign In', 'Log In', 'Login', 'Sign Up', 'Register'],
      email: process.env.GLOBEST_EMAIL,
      password: process.env.GLOBEST_PASSWORD,
      loginTimeout: 15000,
      waitAfterLogin: 2000,
      takeScreenshots: true
    });

    // Pincus configuration
    this.configs.set('pincusco', {
      siteName: 'pincusco',
      domain: 'pincusco.com',
      homeUrl: 'https://www.pincusco.com',
      loginUrl: 'https://www.pincusco.com/wp-login.php',
      emailFieldId: 'user_login',
      passwordFieldId: 'user_pass',
      submitButtonId: 'wp-submit',
      rememberMeId: 'rememberme',
      alreadyLoggedInIndicators: ['Log out', 'My Account', 'Subscriber Profile'],
      notLoggedInIndicators: ['Sign In', 'Log In', 'Login', 'Sign Up', 'Register', 'leaky_paywall_message_wrap', 'leaky_paywall_message'],
      email: process.env.PINCUSCO_EMAIL || '<EMAIL>',
      password: process.env.PINCUSCO_PASSWORD || 'Brooklyn1',
      loginTimeout: 15000,
      waitAfterLogin: 2000,
      takeScreenshots: true
    });
  }

  /**
   * Add or update a site configuration
   */
  addSiteConfig(config: LoginConfiguration): void {
    this.configs.set(config.siteName, config);
  }

  /**
   * Get configuration for a site
   */
  getSiteConfig(siteName: string): LoginConfiguration | null {
    return this.configs.get(siteName) || null;
  }

  /**
   * Get site configuration by domain
   */
  getSiteConfigByDomain(domain: string): LoginConfiguration | null {
    for (const [, config] of this.configs) {
      if (config.domain === domain || domain.includes(config.domain)) {
        return config;
      }
    }
    return null;
  }

  /**
   * Detect site from URL
   */
  detectSiteFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();
      
      for (const [siteName, config] of this.configs) {
        if (hostname.includes(config.domain)) {
          return siteName;
        }
      }
      
      return null;
    } catch (error) {
      this.logger('warn', `Failed to parse URL for site detection: ${url}`);
      return null;
    }
  }

  /**
   * Check if user is logged in to a site
   */
  async isLoggedIn(page: Page, siteNameOrConfig: string | LoginConfiguration): Promise<boolean> {
    const config = typeof siteNameOrConfig === 'string' 
      ? this.getSiteConfig(siteNameOrConfig) 
      : siteNameOrConfig;

    if (!config) {
      this.logger('warn', `No configuration found for site: ${siteNameOrConfig}`);
      return false;
    }

    try {
      this.logger('debug', `Checking login status for ${config.siteName}`);
      
      // Use custom checker if provided
      if (config.customLoggedInChecker) {
        return await config.customLoggedInChecker(page, config);
      }

      // Get page content
      const pageContent = await page.content();
      const lowerPageContent = pageContent.toLowerCase();
      
      // Check for logged in indicators
      for (const indicator of config.alreadyLoggedInIndicators) {
        if (lowerPageContent.includes(indicator.toLowerCase())) {
          this.logger('debug', `Detected logged-in state for ${config.siteName} (found: '${indicator}')`);
          return true;
        }
      }

      // Check for not logged in indicators
      for (const indicator of config.notLoggedInIndicators) {
        if (lowerPageContent.includes(indicator.toLowerCase())) {
          this.logger('debug', `Detected not-logged-in indicator: '${indicator}'`);
          return false;
        }
      }

      // If no clear indicators, assume logged in (conservative approach)
      this.logger('debug', `No clear login indicators found for ${config.siteName}, assuming logged in`);
      return true;

    } catch (error) {
      this.logger('warn', `Error checking login status for ${config.siteName}: ${error}`);
      return false;
    }
  }

  /**
   * Perform login to a site
   */
  async login(page: Page, siteNameOrConfig: string | LoginConfiguration): Promise<LoginResult> {
    const config = typeof siteNameOrConfig === 'string' 
      ? this.getSiteConfig(siteNameOrConfig) 
      : siteNameOrConfig;

    if (!config) {
      return {
        success: false,
        alreadyLoggedIn: false,
        error: `No configuration found for site: ${siteNameOrConfig}`
      };
    }

    try {
      this.logger('info', `Starting login process for ${config.siteName}`);
      
      // Check if already logged in
      const alreadyLoggedIn = await this.isLoggedIn(page, config);
      if (alreadyLoggedIn) {
        this.logger('info', `Already logged in to ${config.siteName}`);
        return {
          success: true,
          alreadyLoggedIn: true,
          message: `Already logged in to ${config.siteName}`
        };
      }

      // Check credentials
      if (!config.email || !config.password) {
        return {
          success: false,
          alreadyLoggedIn: false,
          error: `No credentials provided for ${config.siteName}`
        };
      }

      // Use custom handler if provided
      if (config.customLoginHandler) {
        const success = await config.customLoginHandler(page, config);
        return {
          success,
          alreadyLoggedIn: false,
          message: success ? `Successfully logged in to ${config.siteName}` : `Failed to login to ${config.siteName}`
        };
      }

      // Navigate to login page if specified
      if (config.loginUrl) {
        this.logger('debug', `Navigating to login page: ${config.loginUrl}`);
        await page.goto(config.loginUrl, { waitUntil: 'networkidle2', timeout: config.loginTimeout });
      }

      // Handle different login flows
      const loginSuccess = await this.performSiteSpecificLogin(page, config);
      
      if (loginSuccess) {
        // Wait after login
        if (config.waitAfterLogin) {
          await new Promise(resolve => setTimeout(resolve, config.waitAfterLogin));
        }

        // Verify login was successful
        const finalCheck = await this.isLoggedIn(page, config);
        return {
          success: finalCheck,
          alreadyLoggedIn: false,
          message: finalCheck 
            ? `Successfully logged in to ${config.siteName}` 
            : `Login process completed but verification failed for ${config.siteName}`
        };
      }

      return {
        success: false,
        alreadyLoggedIn: false,
        error: `Login failed for ${config.siteName}`
      };

    } catch (error) {
      this.logger('error', `Login error for ${config.siteName}: ${error}`);
      return {
        success: false,
        alreadyLoggedIn: false,
        error: `Login failed for ${config.siteName}: ${error}`
      };
    }
  }

  /**
   * Perform site-specific login logic
   */
  private async performSiteSpecificLogin(page: Page, config: LoginConfiguration): Promise<boolean> {
    switch (config.siteName) {
      case 'bisnow':
        return await this.loginBisnow(page, config);
      case 'therealdeal':
        return await this.loginTheRealDeal(page, config);
      case 'globest':
        return await this.loginGlobest(page, config);
      case 'pincusco':
        return await this.loginPincusco(page, config);
      default:
        return await this.loginGeneric(page, config);
    }
  }

  /**
   * Bisnow-specific login
   */
  private async loginBisnow(page: Page, config: LoginConfiguration): Promise<boolean> {
    try {
      // Navigate to homepage first
      await page.goto(config.homeUrl, { waitUntil: 'networkidle2' });
      
      // Click login trigger
      const loginTrigger = `.${config.loginTriggerClass}`;
      await page.waitForSelector(loginTrigger, { timeout: 10000 });
      await page.click(loginTrigger);
      
      // Wait for login modal
      await page.waitForSelector(`#${config.emailFieldId}`, { visible: true, timeout: 10000 });
      
      // Fill email
      await page.type(`#${config.emailFieldId}`, config.email!);
      
      // Fill password
      await page.type(`#${config.passwordFieldId}`, config.password!);
      
      // Click submit
      await page.click(`.${config.submitButtonClass}`);
      
      // Wait for login completion
      await page.waitForSelector('a[href*="account"], .my-account, [class*="account"]', { timeout: 15000 });
      
      return true;
    } catch (error) {
      this.logger('error', `Bisnow login failed: ${error}`);
      return false;
    }
  }

  /**
   * TheRealDeal-specific login
   */
  private async loginTheRealDeal(page: Page, config: LoginConfiguration): Promise<boolean> {
    try {
      this.logger('debug', 'Starting TheRealDeal login process');
      
      // Set user agent and headers to avoid bot detection
      await page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
      await page.setExtraHTTPHeaders({
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      });
      
      // Navigate to login page
      this.logger('debug', `Navigating to login URL: ${config.loginUrl}`);
      await page.goto(config.loginUrl!, { waitUntil: 'networkidle2', timeout: config.loginTimeout });
      
      // Wait for Piano ID form to load (it's loaded via JavaScript)
      this.logger('debug', 'Waiting for Piano ID form to load...');
      await new Promise(resolve => setTimeout(resolve, 6000));
      
      // Debug: Check what elements are actually on the page
      this.logger('debug', 'Checking page elements...');
      const pageContent = await page.content();
      this.logger('debug', `Page content length: ${pageContent.length}`);
      
      // Check for specific elements
      const checkElements = [
        'form',
        'input#username',
        'input[name="email"]',
        'input[type="password"]',
        '.piano__title',
        'screen-login',
        '[class*="piano"]',
        'iframe'
      ];
      
      for (const selector of checkElements) {
        const elements = await page.$$(selector);
        this.logger('debug', `Found ${elements.length} elements matching '${selector}'`);
      }
      
      // Check if login form is in an iframe (Piano ID often uses iframes)
      const iframes = await page.$$('iframe');
      this.logger('debug', `Found ${iframes.length} iframes, checking for login form...`);
      
      let loginFrame = null;
      for (let i = 0; i < iframes.length; i++) {
        try {
          const frame = await iframes[i].contentFrame();
          if (frame) {
            // Check if this iframe contains login elements
            const hasEmailField = await frame.$('input#username, input[name="email"], input[type="email"], input[type="text"]');
            const hasPasswordField = await frame.$('input[type="password"]');
            const hasSubmitButton = await frame.$('button[type="submit"], input[type="submit"]');
            
            if (hasEmailField && hasPasswordField) {
              this.logger('debug', `Found login form in iframe ${i}`);
              loginFrame = frame;
              break;
            }
          }
        } catch (error) {
          this.logger('debug', `Could not access iframe ${i}: ${error}`);
        }
      }
      
      // If no login form found in iframes, check main page
      if (!loginFrame) {
        const loginFormSelector = 'form, input#username, .piano__title, screen-login';
        try {
          await page.waitForSelector(loginFormSelector, { timeout: 5000 });
          this.logger('debug', 'Login form detected on main page');
          loginFrame = page.mainFrame();
        } catch (error) {
          this.logger('error', 'Login form not found on main page or in any iframe');
          
          // Take screenshot for debugging
          if (config.takeScreenshots) {
            try {
              await page.screenshot({ path: `debug-therealdeal-no-form-${Date.now()}.png`, fullPage: true });
              this.logger('debug', 'Screenshot taken for debugging');
            } catch (screenshotError) {
              this.logger('warn', `Screenshot failed: ${screenshotError}`);
            }
          }
          return false;
        }
      }
      
      // Find and fill email field
      this.logger('debug', 'Looking for email field...');
      const emailFieldSelectors = [
        'input#username[name="email"]',
        'input#username',
        'input[name="email"]',
        'input[type="email"]',
        'input[type="text"]'
      ];
      
      let emailField = null;
      for (const selector of emailFieldSelectors) {
        try {
          await loginFrame.waitForSelector(selector, { visible: true, timeout: 5000 });
          emailField = await loginFrame.$(selector);
          if (emailField) {
            this.logger('debug', `Email field found using selector: ${selector}`);
            break;
          }
        } catch (error) {
          this.logger('debug', `Email selector ${selector} not found, trying next...`);
        }
      }
      
      if (!emailField) {
        this.logger('error', 'No email field found');
        return false;
      }
      
      // Clear and fill email field
      await emailField.click();
      await emailField.evaluate(el => (el as HTMLInputElement).value = '');
      await emailField.type(config.email!, { delay: 100 });
      this.logger('debug', `Email field filled with: ${config.email}`);
      
      // Find and fill password field
      this.logger('debug', 'Looking for password field...');
      const passwordFieldSelectors = [
        'input#password[type="password"]',
        'input#password',
        'input[type="password"]'
      ];
      
      let passwordField = null;
      for (const selector of passwordFieldSelectors) {
        try {
          await loginFrame.waitForSelector(selector, { visible: true, timeout: 5000 });
          passwordField = await loginFrame.$(selector);
          if (passwordField) {
            this.logger('debug', `Password field found using selector: ${selector}`);
            break;
          }
        } catch (error) {
          this.logger('debug', `Password selector ${selector} not found, trying next...`);
        }
      }
      
      if (!passwordField) {
        this.logger('error', 'No password field found');
        return false;
      }
      
      // Clear and fill password field
      await passwordField.click();
      await passwordField.evaluate(el => (el as HTMLInputElement).value = '');
      await passwordField.type(config.password!, { delay: 100 });
      this.logger('debug', 'Password field filled');
      
      // Find and check "Stay signed in" checkbox
      this.logger('debug', 'Looking for "Stay signed in" checkbox...');
      const checkboxSelectors = [
        ...(config.rememberMeSelector ? config.rememberMeSelector.split(', ') : []),
        'remember-me input[type="checkbox"]',
        '.consent input[type="checkbox"]',
        'input[type="checkbox"]'
      ];
      
      let checkbox = null;
      for (const selector of checkboxSelectors) {
        try {
          await loginFrame.waitForSelector(selector, { visible: true, timeout: 3000 });
          checkbox = await loginFrame.$(selector);
          if (checkbox) {
            this.logger('debug', `Found checkbox using selector: ${selector}`);
            break;
          }
        } catch (error) {
          this.logger('debug', `Checkbox selector ${selector} not found, trying next...`);
        }
      }
      
      if (checkbox) {
        // Check if checkbox is already checked
        const isChecked = await checkbox.evaluate(el => (el as HTMLInputElement).checked);
        if (!isChecked) {
          await checkbox.click();
          this.logger('debug', 'Checked "Stay signed in" checkbox');
        } else {
          this.logger('debug', 'Checkbox already checked');
        }
      } else {
        this.logger('debug', 'No "Stay signed in" checkbox found');
      }
      
      // Wait for form validation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Find and click submit button
      this.logger('debug', 'Looking for submit button...');
      const submitButtonSelectors = [
        'button[actionlogin][type="submit"]',
        'button[type="submit"]',
        'button[onclick*="login"]',
        'button.btn-primary',
        'input[type="submit"]'
      ];
      
      let submitButton = null;
      for (const selector of submitButtonSelectors) {
        try {
          await loginFrame.waitForSelector(selector, { visible: true, timeout: 3000 });
          submitButton = await loginFrame.$(selector);
          if (submitButton) {
            this.logger('debug', `Found submit button using selector: ${selector}`);
            break;
          }
        } catch (error) {
          this.logger('debug', `Submit button selector ${selector} not found, trying next...`);
        }
      }
      
      if (!submitButton) {
        this.logger('error', 'No submit button found');
        return false;
      }
      
      // Click submit button and wait for response
      this.logger('debug', 'Clicking submit button...');
      await submitButton.click();
      
      // Wait for either navigation or error message
      try {
        await Promise.race([
          page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 20000 }),
          loginFrame.waitForSelector('.form-field__invalid[style*="display: block"]', { timeout: 5000 })
            .then(() => { throw new Error('Login form validation error detected'); })
        ]);
        this.logger('debug', 'Navigation completed after login');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('validation error')) {
          this.logger('error', 'Login form validation failed - check credentials');
          
          // Try to get the specific error message
          try {
            const errorElements = await loginFrame.$$('.form-field__invalid[style*="display: block"]');
            for (const errorEl of errorElements) {
              const errorText = await errorEl.evaluate(el => el.textContent);
              this.logger('error', `Login error: ${errorText}`);
            }
          } catch (e) {
            this.logger('debug', 'Could not read error message');
          }
          return false;
        }
        
        // Check if we're on a different page (login may have succeeded)
        const currentUrl = page.url();
        if (currentUrl !== config.loginUrl && !currentUrl.includes('login')) {
          this.logger('debug', 'Login appears successful - redirected to different page');
        } else {
          this.logger('warn', 'Navigation timeout, continuing with verification');
          
          // Check for any error messages in the iframe
          try {
            const errorElements = await loginFrame.$$('.form-field__invalid, .error-message, .alert-danger');
            if (errorElements.length > 0) {
              for (const errorEl of errorElements) {
                const errorText = await errorEl.evaluate(el => el.textContent);
                const isVisible = await errorEl.isVisible();
                if (isVisible && errorText?.trim()) {
                  this.logger('error', `Login error detected: ${errorText.trim()}`);
                }
              }
            }
          } catch (e) {
            this.logger('debug', 'Could not check for error messages');
          }
        }
      }
      
      // Additional wait after login
      await new Promise(resolve => setTimeout(resolve, config.waitAfterLogin || 3000));
      
      return true;
    } catch (error) {
      this.logger('error', `TheRealDeal login failed: ${error}`);
      return false;
    }
  }

  /**
   * GlobeSt-specific login
   */
  private async loginGlobest(page: Page, config: LoginConfiguration): Promise<boolean> {
    try {
      // Navigate to login page
      await page.goto(config.loginUrl!, { waitUntil: 'networkidle2' });
      
      // Find email field (try by ID first, then by name)
      let emailField = await page.$(`#${config.emailFieldId}`);
      if (!emailField) {
        emailField = await page.$(`input[name="${config.emailFieldName}"]`);
      }
      
      if (!emailField) {
        throw new Error('Email field not found');
      }
      
      // Fill email
      await emailField.type(config.email!);
      
      // Find password field
      let passwordField = await page.$(`#${config.passwordFieldId}`);
      if (!passwordField) {
        passwordField = await page.$(`input[name="${config.passwordFieldName}"]`);
      }
      
      if (!passwordField) {
        throw new Error('Password field not found');
      }
      
      // Fill password
      await passwordField.type(config.password!);
      
      // Check remember me if available
      if (config.rememberMeId) {
        try {
          const rememberMe = await page.$(`#${config.rememberMeId}`);
          if (rememberMe) {
            const isChecked = await rememberMe.evaluate((el: any) => el.checked);
            if (!isChecked) {
              await rememberMe.click();
            }
          }
        } catch (error) {
          this.logger('debug', `Remember me checkbox not available: ${error}`);
        }
      }
      
      // Click submit
      await page.click(config.submitButtonSelector!);
      
      // Wait for redirect
      await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 15000 });
      
      return true;
    } catch (error) {
      this.logger('error', `GlobeSt login failed: ${error}`);
      return false;
    }
  }

  /**
   * PincusCo-specific login
   */
  private async loginPincusco(page: Page, config: LoginConfiguration): Promise<boolean> {
    try {
      // Navigate to login page
      await page.goto(config.loginUrl!, { waitUntil: 'networkidle2' });
      
      // Fill email (username)
      await page.waitForSelector(`#${config.emailFieldId}`, { timeout: 10000 });
      await page.type(`#${config.emailFieldId}`, config.email!);
      
      // Fill password
      await page.type(`#${config.passwordFieldId}`, config.password!);
      
      // Check remember me if available
      if (config.rememberMeId) {
        try {
          const rememberMe = await page.$(`#${config.rememberMeId}`);
          if (rememberMe) {
            await rememberMe.click();
          }
        } catch (error) {
          this.logger('debug', `Remember me checkbox not available: ${error}`);
        }
      }
      
      // Click submit
      await page.click(`#${config.submitButtonId}`);
      
      // Wait for redirect
      await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 15000 });
      
      return true;
    } catch (error) {
      this.logger('error', `PincusCo login failed: ${error}`);
      return false;
    }
  }

  /**
   * Generic login flow
   */
  private async loginGeneric(page: Page, config: LoginConfiguration): Promise<boolean> {
    try {
      // Find email field
      const emailSelector = config.emailFieldId ? `#${config.emailFieldId}` : 
                         config.emailFieldSelector ? config.emailFieldSelector :
                         config.emailFieldName ? `input[name="${config.emailFieldName}"]` : null;
      
      if (!emailSelector) {
        throw new Error('No email field selector available');
      }
      
      await page.waitForSelector(emailSelector, { timeout: 10000 });
      await page.type(emailSelector, config.email!);
      
      // Find password field
      const passwordSelector = config.passwordFieldId ? `#${config.passwordFieldId}` : 
                            config.passwordFieldSelector ? config.passwordFieldSelector :
                            config.passwordFieldName ? `input[name="${config.passwordFieldName}"]` : null;
      
      if (!passwordSelector) {
        throw new Error('No password field selector available');
      }
      
      await page.type(passwordSelector, config.password!);
      
      // Find submit button
      const submitSelector = config.submitButtonId ? `#${config.submitButtonId}` :
                          config.submitButtonClass ? `.${config.submitButtonClass}` :
                          config.submitButtonSelector ? config.submitButtonSelector : null;
      
      if (!submitSelector) {
        throw new Error('No submit button selector available');
      }
      
      await page.click(submitSelector);
      
      // Wait for navigation or login completion
      try {
        await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 15000 });
      } catch (error) {
        // Navigation might not happen, continue
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
      
      return true;
    } catch (error) {
      this.logger('error', `Generic login failed: ${error}`);
      return false;
    }
  }

  /**
   * Get list of supported sites
   */
  getSupportedSites(): string[] {
    return Array.from(this.configs.keys());
  }

  /**
   * Check if a site is supported
   */
  isSiteSupported(siteName: string): boolean {
    return this.configs.has(siteName);
  }
} 