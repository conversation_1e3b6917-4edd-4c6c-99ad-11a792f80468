import { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { Base<PERSON><PERSON>rap<PERSON>, SiteConfig, ScrapingResult } from './BaseScraper';
import * as path from 'path';

export class BisnowScraper extends BaseScraper {
  private startUrl: string;

  constructor(browser: Browser, page: Page, siteConfig: SiteConfig, waitTime: number = 1000) {
    super(browser, page, siteConfig, waitTime);
    this.startUrl = 'https://www.bisnow.com/';
  }

  async isLoggedIn(): Promise<boolean> {
    try {
      console.log(`[INFO] Checking login status for ${this.siteName}`);
      
      // Get the indicators that show we're logged in
      const indicators = this.siteConfig.already_logged_in_indicators || [];
      if (indicators.length === 0) {
        console.log(`[WARNING] No login indicators defined for ${this.siteName}. Cannot determine login status.`);
        return false;
      }
      
      // Get the indicators that show we're NOT logged in
      const notLoggedInIndicators = this.siteConfig.not_logged_in_indicators || 
        ['Sign In', 'Log In', 'Login', 'Sign Up', 'Register'];

      // Get page content
      const pageContent = await this.page.content();
      const lowerPageContent = pageContent.toLowerCase();
      
      // Check for logged in indicators
      for (const indicator of indicators) {
        if (lowerPageContent.includes(indicator.toLowerCase())) {
          console.log(`[INFO] Detected logged-in state for ${this.siteName} (found: '${indicator}')`);
          return true;
        }
      }

      // Check for not logged in indicators
      for (const indicator of notLoggedInIndicators) {
        if (lowerPageContent.includes(indicator.toLowerCase())) {
          console.log(`[INFO] Detected not-logged-in indicator: '${indicator}'`);
          return false;
        }
      }

      // If we reached here, we're likely logged in
      return true;
    } catch (error) {
      console.log(`[WARNING] Error checking login status for ${this.siteName}: ${error}`);
      return false;
    }
  }

  async login(): Promise<boolean> {
    try {
      // Check if already logged in
      if (await this.isLoggedIn()) {
        console.log(`[INFO] Already logged in to ${this.siteName}`);
        return true;
      }

      // Look for the login element with the specified class
      console.log('[INFO] Looking for login button on Bisnow homepage...');
      const loginTriggerClass = this.siteConfig.login_trigger_class || 'logIn';
      
      await this.page.waitForSelector(`.${loginTriggerClass}`, { timeout: 10000 });
      
      // Click the login button to open the modal
      console.log('[INFO] Clicking login button to open login modal...');
      await this.page.click(`.${loginTriggerClass}`);
      
      // Wait for the login modal to appear and email field to be visible
      console.log('[INFO] Waiting for login modal to appear...');
      const emailFieldId = this.siteConfig.email_field_id || 'login_email_signin';
      await this.page.waitForSelector(`#${emailFieldId}`, { visible: true, timeout: 10000 });
      
      // Get credentials
      const email = this.siteConfig.email || process.env.BISNOW_EMAIL || '';
      const password = this.siteConfig.password || process.env.BISNOW_PASSWORD || '';
      
      if (!email || !password) {
        console.log('[ERROR] No email or password provided for Bisnow login');
        return false;
      }
      
      // Enter email
      console.log(`[INFO] Entering email: ${email}`);
      await this.page.type(`#${emailFieldId}`, email);
      
      // Find and fill password field
      const passwordFieldId = this.siteConfig.password_field_id || 'login_password';
      console.log('[INFO] Entering password');
      await this.page.type(`#${passwordFieldId}`, password);
      
      // Click the login button in the modal
      console.log('[INFO] Clicking login button in modal...');
      const submitButtonClass = this.siteConfig.submit_button_class || 'do-login-btn';
      await this.page.click(`.${submitButtonClass}`);
      
      // Wait for login to complete - look for success indicator
      console.log('[INFO] Waiting for login to complete...');
      const successIndicator = this.siteConfig.success_indicator_xpath || "//*[contains(text(), 'My Account')]";
      
      // Convert XPath to CSS selector (simplified approach)
      const successSelector = 'a[href*="account"], .my-account, [class*="account"]';
      await this.page.waitForSelector(successSelector, { timeout: 15000 });
      
      // Verify login was successful
      const loginSuccessful = await this.isLoggedIn();
      if (loginSuccessful) {
        console.log('[INFO] Successfully logged in to Bisnow');
        return true;
      } else {
        console.log('[WARNING] Login process completed but login indicators not found for Bisnow');
        return false;
      }
    } catch (error) {
      console.log(`[WARNING] Failed to login to Bisnow: ${error}`);
      return false;
    }
  }

  async scrapeLinks(maxPages: number = 30): Promise<ScrapingResult> {
    console.log(`[INFO] Starting ${this.siteName} link scraping...`);
    
    const result: ScrapingResult = {
      newLinksFound: 0,
      totalLinksProcessed: 0,
      errors: [],
      success: false
    };
    
    // Navigate to the homepage
    if (!(await this.safeNavigate(this.startUrl))) {
      result.errors.push(`Could not access ${this.siteName} website`);
      return result;
    }
    
    let consecutiveNoNew = 0;
    const newLinksFile = path.join(this.profileDir, 'new_links.txt');
    
    try {
      for (let i = 1; i <= maxPages; i++) {
        console.log(`[${this.siteName.toUpperCase()}] Page ${i}: Fetching links...`);
        
        // Get all links on the page
        const linkElements = await this.page.$$eval('a', anchors => 
          anchors.map(anchor => anchor.href).filter(href => href)
        );
        
        // Filter valid article links
        const validLinks: string[] = [];
        let totalLinksFound = 0;
        
        for (const href of linkElements) {
          totalLinksFound++;
          if (this.isValidArticleUrl(href)) {
            validLinks.push(href);
          } else {
            console.log(`[${this.siteName.toUpperCase()}] Page ${i}: ❌ Filtered out non-article URL: ${href}`);
          }
        }
        
        // Store new valid links in database
        const newlyInserted = await this.storeNewLinksInDb(validLinks);
        const insertCount = newlyInserted.length;
        await this.saveNewLinksToFile(newlyInserted, newLinksFile);
        
        result.totalLinksProcessed += totalLinksFound;
        result.newLinksFound += insertCount;
        
        console.log(`[${this.siteName.toUpperCase()}] Page ${i}: Found ${totalLinksFound} total links, filtered to ${validLinks.length} valid links, inserted ${insertCount} new ones.`);
        
        if (insertCount === 0) {
          consecutiveNoNew++;
          console.log(`[${this.siteName.toUpperCase()}] Page ${i}: No new article links found. Consecutive count: ${consecutiveNoNew}/${this.maxConsecutiveNoNew}`);
          if (consecutiveNoNew >= this.maxConsecutiveNoNew) {
            console.log(`[${this.siteName.toUpperCase()}] Hit max consecutive pages (${this.maxConsecutiveNoNew}) with 0 new article inserts. Stopping.`);
            break;
          }
        } else {
          consecutiveNoNew = 0;
          console.log(`[${this.siteName.toUpperCase()}] Page ${i}: Found ${insertCount} new articles. Reset consecutive counter.`);
        }
        
        // Scroll to bottom to load more content
        await this.scrollToBottom();
        
        // Wait before next page
        await new Promise(resolve => setTimeout(resolve, this.waitTime));
      }
      
      result.success = true;
      console.log(`[INFO] Finished ${this.siteName} scraping.`);
      
    } catch (error) {
      console.log(`[ERROR] Error during ${this.siteName} scraping: ${error}`);
      result.errors.push(String(error));
    }
    
    return result;
  }

  protected isValidArticleUrl(url: string): boolean {
    if (!super.isValidArticleUrl(url)) {
      return false;
    }
    
    // Bisnow-specific validation - be more permissive
    const normalizedUrl = url.toLowerCase();
    
    // Exclude specific non-article patterns for Bisnow
    const bisnowExcludePatterns = [
      '/biswire',
      '/blogs/',
      '/top-talent',
      '/event-products',
      '/apply-to-speak',
      '/legal/',
      '/orders',
      '/preferences',
      '/videos',
      '/subscriptions',
      '/employer',
      '/firstdraftlive',
      'selectleaders.com',
      'careers.bisnow.com',
      'bisnowelevate.com'
    ];
    
    // Check if URL contains any excluded patterns
    if (bisnowExcludePatterns.some(pattern => normalizedUrl.includes(pattern))) {
      return false;
    }
    
    // Real estate keywords for content detection
    const realEstateKeywords = [
      'hotel', 'office', 'residential', 'commercial', 'retail', 'industrial',
      'property', 'building', 'development', 'construction', 'real-estate',
      'investment', 'sale', 'lease', 'rent', 'acquisition', 'financing',
      'mortgage', 'loan', 'deal', 'transaction', 'market', 'portfolio',
      'reit', 'fund', 'capital', 'equity', 'debt', 'refinance', 'refi',
      'manhattan', 'brooklyn', 'queens', 'bronx', 'nyc', 'new-york',
      'washington', 'dc', 'philadelphia', 'boston', 'atlanta', 'dallas',
      'apartment', 'condo', 'co-op', 'townhouse', 'warehouse', 'facility',
      'plaza', 'tower', 'center', 'complex', 'square', 'avenue', 'street',
      'million', 'billion', 'sf', 'sqft', 'acre', 'floor', 'story',
      'zoning', 'permits', 'planning', 'approval', 'closing', 'sold'
    ];
    
    // Check if URL contains real estate keywords
    const hasRealEstateContent = realEstateKeywords.some(keyword => 
      normalizedUrl.includes(keyword)
    );
    
    // Bisnow section patterns (but don't require them)
    const bisnowSectionPatterns = [
      '/news/', '/events/', '/articles/'
    ];
    
    const hasKnownSection = bisnowSectionPatterns.some(pattern => url.includes(pattern));
    
    // Also check for URLs with meaningful content (longer descriptive paths)
    const urlPath = url.split('bisnow.com')[1] || '';
    const hasDescriptiveContent = urlPath.length > 15 && urlPath.includes('-');
    
    // Numeric ID patterns (Bisnow articles often have IDs)
    const hasNumericId = /\/[\w-]+-\d+\/?$/.test(url);
    
    // Accept if it has real estate content, known sections, descriptive content, or numeric IDs
    return hasRealEstateContent || hasKnownSection || hasDescriptiveContent || hasNumericId;
  }
} 