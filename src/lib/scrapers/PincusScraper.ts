import { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SiteConfig, ScrapingResult } from './BaseScraper';
import * as path from 'path';

export class PincusScraper extends BaseScraper {
  private startUrl: string;

  constructor(browser: Browser, page: Page, siteConfig: SiteConfig, waitTime: number = 1000) {
    super(browser, page, siteConfig, waitTime);
    this.startUrl = 'https://www.pincusco.com/';
  }

  async isLoggedIn(): Promise<boolean> {
    try {
      console.log(`[INFO] Checking login status for ${this.siteName}`);
      
      const content = await this.page.content();
      const pageSource = content.toLowerCase();
      
      const loggedInIndicators = this.siteConfig.already_logged_in_indicators || ['log out', 'my account', 'subscriber profile'];
      
      // Check for logged in indicators
      for (const indicator of loggedInIndicators) {
        if (pageSource.includes(indicator.toLowerCase())) {
          console.log(`[INFO] Detected logged-in state for ${this.siteName} (found: '${indicator}')`);
          return true;
        }
      }
      
      console.log(`[INFO] No login indicators found for ${this.siteName}`);
      return false;
    } catch (error) {
      console.log(`[WARNING] Error checking login status for ${this.siteName}: ${error}`);
      return false;
    }
  }

  async login(): Promise<boolean> {
    console.log(`[INFO] ${this.siteName} login not implemented - proceeding without login`);
    return true;
  }

  async scrapeLinks(maxPages: number = 30): Promise<ScrapingResult> {
    console.log(`[INFO] Starting ${this.siteName} link scraping...`);
    
    const result: ScrapingResult = {
      newLinksFound: 0,
      totalLinksProcessed: 0,
      errors: [],
      success: false
    };
    
    // Navigate to the homepage
    if (!(await this.safeNavigate(this.startUrl))) {
      result.errors.push(`Could not access ${this.siteName} website`);
      return result;
    }
    
    let consecutiveNoNew = 0;
    const newLinksFile = path.join(this.profileDir, 'new_links.txt');
    
    try {
      for (let i = 1; i <= maxPages; i++) {
        console.log(`[${this.siteName.toUpperCase()}] Page ${i}: Fetching links...`);
        
        // Get all links on the page, focusing on article links
        const linkElements = await this.page.$$eval('a', anchors => 
          anchors.map(anchor => anchor.href).filter(href => href)
        );
        
        // Filter valid article links
        const validLinks: string[] = [];
        let totalLinksFound = 0;
        
        for (const href of linkElements) {
          totalLinksFound++;
          if (this.isValidArticleUrl(href)) {
            validLinks.push(href);
          } else {
            console.log(`[${this.siteName.toUpperCase()}] Page ${i}: ❌ Filtered out non-article URL: ${href}`);
          }
        }
        
        // Store new valid links in database
        const newlyInserted = await this.storeNewLinksInDb(validLinks);
        const insertCount = newlyInserted.length;
        await this.saveNewLinksToFile(newlyInserted, newLinksFile);
        
        result.totalLinksProcessed += totalLinksFound;
        result.newLinksFound += insertCount;
        
        console.log(`[${this.siteName.toUpperCase()}] Page ${i}: Found ${totalLinksFound} total links, filtered to ${validLinks.length} valid links, inserted ${insertCount} new ones.`);
        
        if (insertCount === 0) {
          consecutiveNoNew++;
          console.log(`[${this.siteName.toUpperCase()}] Page ${i}: No new article links found. Consecutive count: ${consecutiveNoNew}/${this.maxConsecutiveNoNew}`);
          if (consecutiveNoNew >= this.maxConsecutiveNoNew) {
            console.log(`[${this.siteName.toUpperCase()}] Hit max consecutive pages (${this.maxConsecutiveNoNew}) with 0 new article inserts. Stopping.`);
            break;
          }
        } else {
          consecutiveNoNew = 0;
          console.log(`[${this.siteName.toUpperCase()}] Page ${i}: Found ${insertCount} new articles. Reset consecutive counter.`);
        }
        
        // Scroll and wait, then try to navigate to more content
        await this.scrollToBottom();
        await new Promise(resolve => setTimeout(resolve, this.waitTime));
        
        // Try to find pagination or refresh
        await this.page.reload();
        await new Promise(resolve => setTimeout(resolve, this.waitTime));
      }
      
      result.success = true;
      console.log(`[INFO] Finished ${this.siteName} scraping.`);
      
    } catch (error) {
      console.log(`[ERROR] Error during ${this.siteName} scraping: ${error}`);
      result.errors.push(String(error));
    }
    
    return result;
  }

  protected isValidArticleUrl(url: string): boolean {
    if (!super.isValidArticleUrl(url)) {
      return false;
    }
    
    // PincusCo-specific validation - be more permissive
    const normalizedUrl = url.toLowerCase();
    
    // Exclude specific non-article patterns for PincusCo
    const pincusExcludePatterns = [
      '/wp-admin',
      '/wp-login',
      '/feed',
      '/author/',
      '/date/',
      '/page/',
      '/tag/',
      '/category/page/',  // Allow categories but not category pagination
      '/events/',
      '/careers/',
      '/advertise'
    ];
    
    // Check if URL contains any excluded patterns
    if (pincusExcludePatterns.some(pattern => normalizedUrl.includes(pattern))) {
      return false;
    }
    
    // For PincusCo, most legitimate articles have descriptive URLs
    // Let's check for real estate related keywords or meaningful content
    const realEstateKeywords = [
      'hotel', 'office', 'residential', 'commercial', 'retail', 'industrial',
      'property', 'building', 'development', 'construction', 'real-estate',
      'investment', 'sale', 'lease', 'rent', 'acquisition', 'financing',
      'mortgage', 'loan', 'deal', 'transaction', 'market', 'portfolio',
      'reit', 'fund', 'capital', 'equity', 'debt', 'refinance', 'refi',
      'manhattan', 'brooklyn', 'queens', 'bronx', 'nyc', 'new-york',
      'apartment', 'condo', 'co-op', 'townhouse', 'warehouse', 'facility',
      'plaza', 'tower', 'center', 'complex', 'square', 'avenue', 'street',
      'million', 'billion', 'sf', 'sqft', 'acre', 'floor', 'story',
      'zoning', 'permits', 'planning', 'approval', 'closing', 'sold'
    ];
    
    // Check if URL contains real estate keywords
    const hasRealEstateContent = realEstateKeywords.some(keyword => 
      normalizedUrl.includes(keyword)
    );
    
    // Also check for URLs with meaningful content (longer descriptive paths)
    const urlPath = url.split(this.siteConfig.domain)[1] || '';
    const hasDescriptiveContent = urlPath.length > 20 && urlPath.includes('-');
    
    // Date patterns (articles often have dates in URLs)
    const hasDatePattern = /\/\d{4}\/\d{2}\//.test(url) || /\/\d{4}\//.test(url);
    
    // Numeric ID patterns (some articles have IDs)
    const hasNumericId = /\/[\w-]+-\d+\/?$/.test(url);
    
    // Accept if it has real estate content, descriptive content, dates, or numeric IDs
    return hasRealEstateContent || hasDescriptiveContent || hasDatePattern || hasNumericId;
  }
}
