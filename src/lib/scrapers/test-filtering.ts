import { <PERSON>nc<PERSON>Scraper } from './PincusScraper';
import { TheRealDealScraper } from './TheRealDealScraper';
import { BisnowScraper } from './BisnowScraper';
import { GlobestScraper } from './GlobestScraper';

// Test URLs that were previously being filtered out
const testUrls = [
  // PincusCo URLs that were filtered out
  'https://www.pincusco.com/magna-hospitality-is-ucc-auction-winner-of-moinians-hilton-branded-midtown-west-hotel-valued-at-143-9m/',
  'https://www.pincusco.com/feil-organization-signs-15m-refi-with-citibank-for-office-in-penn-plaza/',
  'https://www.pincusco.com/category/transfers/', // This might still be filtered out as it's a category page
  
  // TheRealDeal sample URLs
  'https://therealdeal.com/new-york/2024/01/15/major-office-deal-closes-in-manhattan/',
  'https://therealdeal.com/residential/brooklyn-condo-development-sells-for-200m/',
  'https://therealdeal.com/commercial/retail-property-acquisition-midtown/',
  
  // Bisnow sample URLs
  'https://www.bisnow.com/national/news/hotel-hospitality/hotel-investment-reaches-record-high-123456',
  'https://www.bisnow.com/philadelphia/news/commercial-real-estate/office-building-sells-downtown-456789',
  
  // GlobeSt sample URLs
  'https://www.globest.com/sectors/office/major-office-building-transaction-manhattan/',
  'https://www.globest.com/multifamily/apartment-complex-acquisition-brooklyn/'
];

// Mock configuration for testing
const mockSiteConfig = {
  name: 'Test Site',
  domain: 'example.com',
  home_url: 'https://example.com'
};

// Test function
async function testFilteringLogic() {
  console.log('Testing improved filtering logic...\n');
  
  // Create mock scraper instances for testing
  const scrapers = [
    { name: 'PincusScraper', instance: new PincusScraper(null as any, null as any, { ...mockSiteConfig, domain: 'pincusco.com' }) },
    { name: 'TheRealDealScraper', instance: new TheRealDealScraper(null as any, null as any, { ...mockSiteConfig, domain: 'therealdeal.com' }) },
    { name: 'BisnowScraper', instance: new BisnowScraper(null as any, null as any, { ...mockSiteConfig, domain: 'bisnow.com' }) },
    { name: 'GlobestScraper', instance: new GlobestScraper(null as any, null as any, { ...mockSiteConfig, domain: 'globest.com' }) }
  ];
  
  for (const { name, instance } of scrapers) {
    console.log(`\n=== Testing ${name} ===`);
    
    let passedCount = 0;
    let totalCount = 0;
    
    for (const url of testUrls) {
      // Only test URLs that match the scraper's domain
      if (url.includes(instance['siteConfig'].domain)) {
        totalCount++;
        const isValid = (instance as any).isValidArticleUrl(url);
        
        console.log(`${isValid ? '✅' : '❌'} ${url}`);
        
        if (isValid) {
          passedCount++;
        }
      }
    }
    
    console.log(`\nResults: ${passedCount}/${totalCount} URLs passed validation`);
  }
  
  console.log('\n=== Summary ===');
  console.log('The improved filtering logic should now accept more legitimate article URLs');
  console.log('while still filtering out clearly non-article URLs like contact pages, etc.');
  console.log('\nKey improvements:');
  console.log('1. Uses real estate keyword detection');
  console.log('2. Accepts descriptive URLs with meaningful content');
  console.log('3. More permissive pattern matching');
  console.log('4. Better exclusion patterns for non-articles');
}

// Run the test
testFilteringLogic().catch(console.error); 