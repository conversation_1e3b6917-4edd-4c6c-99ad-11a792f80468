import { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { <PERSON><PERSON><PERSON>rap<PERSON>, SiteConfig, ScrapingResult } from './BaseScraper';
import * as path from 'path';

export class TheRealDealScraper extends BaseScraper {
  private startUrl: string;

  constructor(browser: Browser, page: Page, siteConfig: SiteConfig, waitTime: number = 1000) {
    super(browser, page, siteConfig, waitTime);
    this.startUrl = 'https://therealdeal.com/sector/commercial/';
  }

  async isLoggedIn(): Promise<boolean> {
    try {
      console.log(`[INFO] Checking login status for ${this.siteName}`);
      
      // Check current page for login indicators
      const content = await this.page.content();
      const pageSource = content.toLowerCase();
      
      // Check for logged in indicators
      const loggedInIndicators = this.siteConfig.already_logged_in_indicators || ['my account', 'log out', 'sign out'];
      for (const indicator of loggedInIndicators) {
        if (pageSource.includes(indicator.toLowerCase())) {
          console.log(`[INFO] Detected logged-in state for ${this.siteName} (found: '${indicator}')`);
          return true;
        }
      }
      
      // Check for not logged in indicators
      const notLoggedInIndicators = this.siteConfig.not_logged_in_indicators || ['sign in', 'log in', 'login', 'sign up', 'register'];
      for (const indicator of notLoggedInIndicators) {
        if (pageSource.includes(indicator.toLowerCase())) {
          console.log(`[INFO] Detected not-logged-in indicator: '${indicator}'`);
          return false;
        }
      }
      
      return false;
    } catch (error) {
      console.log(`[WARNING] Error checking login status for ${this.siteName}: ${error}`);
      return false;
    }
  }

  async login(): Promise<boolean> {
    try {
      const loginUrl = this.siteConfig.login_url || 'https://therealdeal.com/login/';
      console.log(`[INFO] Navigating to login page: ${loginUrl}`);
      
      if (!(await this.safeNavigate(loginUrl))) {
        // Try alternate login URL
        const alternateLoginUrl = this.siteConfig.alternate_login_url || 'https://therealdeal.com/account/login/';
        console.log(`[INFO] Primary login URL failed. Trying alternate: ${alternateLoginUrl}`);
        if (!(await this.safeNavigate(alternateLoginUrl))) {
          console.log('[ERROR] Failed to navigate to any login page');
          return false;
        }
      }
      
      console.log('[INFO] Successfully loaded login page');
      await this.takeScreenshot(`${this.siteName}_login_page.png`);
      
      // Get credentials
      const email = this.siteConfig.email || process.env.THEREALDEAL_EMAIL || '';
      const password = this.siteConfig.password || process.env.THEREALDEAL_PASSWORD || '';
      
             if (!email || !password) {
         console.log('[ERROR] No credentials provided for TheRealDeal login');
         return false;
       }
      
      // Check for login form in iframes
      const iframes = await this.page.$$('iframe');
      let iframeFound = false;
      
      if (iframes.length > 0) {
        console.log(`[INFO] Found ${iframes.length} iframes on the page. Checking each one...`);
        
        for (let i = 0; i < iframes.length; i++) {
          try {
            const frame = await iframes[i].contentFrame();
            if (frame) {
              const emailFields = await frame.$$('input[type="email"], input[name="email"], input.email, input#email');
              const passwordFields = await frame.$$('input[type="password"], input[name="password"], input.password, input#password');
              
              if (emailFields.length > 0 && passwordFields.length > 0) {
                console.log(`[INFO] Found login form in iframe ${i + 1}`);
                iframeFound = true;
                
                // Fill the form in the iframe
                await emailFields[0].type(email);
                await passwordFields[0].type(password);
                
                // Try to submit the form
                const submitButtons = await frame.$$('button[type="submit"], input[type="submit"]');
                if (submitButtons.length > 0) {
                  await submitButtons[0].click();
                  console.log('[INFO] Login form submitted in iframe');
                  break;
                }
              }
            }
          } catch (error) {
            console.log(`[WARNING] Error checking iframe ${i + 1}: ${error}`);
          }
        }
      }
      
      if (!iframeFound) {
        console.log('[INFO] No login form found in iframes. Trying main page...');
        
        // Try to fill form on main page
        const formFilled = await this.page.evaluate((email, password) => {
          // Find email field
          const emailSelectors = [
            'input[type="email"]',
            'input[name="email"]',
            'input.email',
            'input#email'
          ];
          
          let emailField = null;
          for (const selector of emailSelectors) {
            emailField = document.querySelector(selector);
            if (emailField && emailField.offsetParent !== null) {
              break;
            }
          }
          
          // Find password field
          const passwordSelectors = [
            'input[type="password"]',
            'input[name="password"]',
            'input.password',
            'input#password'
          ];
          
          let passwordField = null;
          for (const selector of passwordSelectors) {
            passwordField = document.querySelector(selector);
            if (passwordField && passwordField.offsetParent !== null) {
              break;
            }
          }
          
          if (emailField && passwordField) {
            (emailField as HTMLInputElement).value = email;
            (passwordField as HTMLInputElement).value = password;
            
            // Dispatch events
            emailField.dispatchEvent(new Event('input', { bubbles: true }));
            passwordField.dispatchEvent(new Event('input', { bubbles: true }));
            
            // Try to submit
            const submitSelectors = [
              'button[type="submit"]',
              'input[type="submit"]',
              'button.submit',
              'button[class*="submit"]',
              'button[class*="login"]'
            ];
            
            for (const selector of submitSelectors) {
              const button = document.querySelector(selector);
              if (button && button.offsetParent !== null) {
                (button as HTMLButtonElement).click();
                return true;
              }
            }
          }
          
          return false;
        }, email, password);
        
        if (!formFilled) {
          console.log('[ERROR] Failed to fill and submit login form');
          return false;
        }
      }
      
      // Wait for login to complete
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Verify login was successful
      if (await this.isLoggedIn()) {
        console.log(`[INFO] Login successful for ${this.siteName}`);
        return true;
      } else {
        console.log(`[WARNING] Login attempt completed but login indicators not found for ${this.siteName}`);
        return false;
      }
      
    } catch (error) {
      console.log(`[ERROR] Exception during ${this.siteName} login: ${error}`);
      await this.takeScreenshot(`${this.siteName}_error.png`);
      return false;
    }
  }

  async scrapeLinks(maxPages: number = 30): Promise<ScrapingResult> {
    console.log(`[INFO] Starting ${this.siteName} link scraping...`);
    
    const result: ScrapingResult = {
      newLinksFound: 0,
      totalLinksProcessed: 0,
      errors: [],
      success: false
    };
    
    // Navigate to the commercial page
    if (!(await this.safeNavigate(this.startUrl))) {
      result.errors.push(`Could not access ${this.siteName} website`);
      return result;
    }
    
    let consecutiveNoNew = 0;
    let loadMoreCount = 0;
    const newLinksFile = path.join(this.profileDir, 'new_links.txt');
    
    try {
      for (let i = 1; i <= maxPages; i++) {
        console.log(`[${this.siteName.toUpperCase()}] Page ${i}: Fetching links...`);
        
        // Get all links on the page
        const linkElements = await this.page.$$eval('a', anchors => 
          anchors.map(anchor => anchor.href).filter(href => href)
        );
        
        // Filter valid article links
        const validLinks: string[] = [];
        let totalLinksFound = 0;
        
        for (const href of linkElements) {
          totalLinksFound++;
          if (this.isValidArticleUrl(href)) {
            validLinks.push(href);
          } else {
            console.log(`[${this.siteName.toUpperCase()}] Page ${i}: ❌ Filtered out non-article URL: ${href}`);
          }
        }
        
        // Store new valid links in database
        const newlyInserted = await this.storeNewLinksInDb(validLinks);
        const insertCount = newlyInserted.length;
        await this.saveNewLinksToFile(newlyInserted, newLinksFile);
        
        result.totalLinksProcessed += totalLinksFound;
        result.newLinksFound += insertCount;
        
        console.log(`[${this.siteName.toUpperCase()}] Page ${i}: Found ${totalLinksFound} total links, filtered to ${validLinks.length} valid links, inserted ${insertCount} new ones.`);
        
        if (insertCount === 0) {
          consecutiveNoNew++;
          console.log(`[${this.siteName.toUpperCase()}] Page ${i}: No new article links found. Consecutive count: ${consecutiveNoNew}/${this.maxConsecutiveNoNew}`);
          if (consecutiveNoNew >= this.maxConsecutiveNoNew) {
            console.log(`[${this.siteName.toUpperCase()}] Hit max consecutive pages (${this.maxConsecutiveNoNew}) with 0 new article inserts. Stopping.`);
            break;
          }
        } else {
          consecutiveNoNew = 0;
          console.log(`[${this.siteName.toUpperCase()}] Page ${i}: Found ${insertCount} new articles. Reset consecutive counter.`);
        }
        
        // Try to click "Load more" button
        const clicked = await this.clickLoadMoreButton();
        
        if (!clicked) {
          console.log(`[${this.siteName.toUpperCase()}] No more 'Load more' button found. Stopping.`);
          break;
        }
        
        loadMoreCount++;
        await this.scrollToBottom();
      }
      
      result.success = true;
      console.log(`[INFO] Finished ${this.siteName} scraping. Clicked 'Load more' ${loadMoreCount} times.`);
      
    } catch (error) {
      console.log(`[ERROR] Error during ${this.siteName} scraping: ${error}`);
      result.errors.push(String(error));
    }
    
    return result;
  }

  private async clickLoadMoreButton(): Promise<boolean> {
    const maxRetries = 3;
    
    for (let retry = 0; retry < maxRetries; retry++) {
      try {
        console.log(`[${this.siteName.toUpperCase()}] Attempting to find 'Load more' button (attempt ${retry + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const buttonSelectors = [
          'button.BlogrollScroller_loadMore__XHN9N.Button_root__eW05B.Button_primary__q4znz.Button_medium__Y3eCE',
          'button.BlogrollScroller_loadMore__XHN9N',
          'button[type="button"].BlogrollScroller_loadMore__XHN9N',
          'button.Button_primary__q4znz.Button_medium__Y3eCE'
        ];
        
        for (let selectorIndex = 0; selectorIndex < buttonSelectors.length; selectorIndex++) {
          try {
            const selector = buttonSelectors[selectorIndex];
            console.log(`[${this.siteName.toUpperCase()}] Trying selector ${selectorIndex + 1}: ${selector}`);
            
            const button = await this.page.$(selector);
            if (button) {
              const isVisible = await button.isIntersectingViewport();
              const isEnabled = await button.evaluate(el => !el.disabled);
              
              if (isVisible && isEnabled) {
                const buttonText = await button.evaluate(el => el.textContent?.trim() || '');
                console.log(`[${this.siteName.toUpperCase()}] Found button with text: '${buttonText}'`);
                
                // Scroll to button
                await this.page.evaluate(el => {
                  el.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, button);
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Try to click
                try {
                  await button.click();
                  console.log(`[${this.siteName.toUpperCase()}] Successfully clicked 'Load more' button`);
                  await new Promise(resolve => setTimeout(resolve, 3000));
                  return true;
                } catch (clickError) {
                  console.log(`[${this.siteName.toUpperCase()}] Direct click failed, trying JavaScript click: ${clickError}`);
                  await this.page.evaluate(el => el.click(), button);
                  console.log(`[${this.siteName.toUpperCase()}] JavaScript click successful`);
                  await new Promise(resolve => setTimeout(resolve, 3000));
                  return true;
                }
              }
            }
          } catch (selectorError) {
            console.log(`[${this.siteName.toUpperCase()}] Selector ${selectorIndex + 1} failed: ${selectorError}`);
          }
        }
        
        // Try finding by text content
        const buttons = await this.page.$$('button');
        for (const button of buttons) {
          try {
            const buttonText = await button.evaluate(el => el.textContent?.trim().toLowerCase() || '');
            const isVisible = await button.isIntersectingViewport();
            const isEnabled = await button.evaluate(el => !el.disabled);
            
            if (buttonText.includes('load more') && isVisible && isEnabled) {
              console.log(`[${this.siteName.toUpperCase()}] Found 'Load more' button via text search`);
              await this.page.evaluate(el => {
                el.scrollIntoView({ behavior: 'smooth', block: 'center' });
              }, button);
              await new Promise(resolve => setTimeout(resolve, 1000));
              await button.click();
              console.log(`[${this.siteName.toUpperCase()}] Successfully clicked button via text search`);
              await new Promise(resolve => setTimeout(resolve, 3000));
              return true;
            }
          } catch (buttonError) {
            // Continue to next button
          }
        }
        
        // If no button found, scroll and try again
        if (retry < maxRetries - 1) {
          console.log(`[${this.siteName.toUpperCase()}] No button found, scrolling and retrying...`);
          await this.scrollToBottom();
          await new Promise(resolve => setTimeout(resolve, 2000));
          await this.takeScreenshot(`${this.siteName}_loadmore_retry_${retry}.png`);
        }
        
      } catch (error) {
        console.log(`[${this.siteName.toUpperCase()}] Error in attempt ${retry + 1}: ${error}`);
        if (retry < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }
    
    console.log(`[${this.siteName.toUpperCase()}] Failed to find 'Load more' button after ${maxRetries} attempts`);
    return false;
  }

  protected isValidArticleUrl(url: string): boolean {
    if (!super.isValidArticleUrl(url)) {
      return false;
    }
    
    // TheRealDeal-specific validation - be more permissive
    const normalizedUrl = url.toLowerCase();
    
    // Exclude specific non-article patterns for TheRealDeal
    const trdExcludePatterns = [
      '/user/',
      '/account/',
      '/events/',
      '/careers/',
      '/advertise',
      '/subscribe',
      '/newsletter',
      '/podcast/',
      '/video/',
      '/photo/',
      '/event/',
      '/webinar'
    ];
    
    // Check if URL contains any excluded patterns
    if (trdExcludePatterns.some(pattern => normalizedUrl.includes(pattern))) {
      return false;
    }
    
    // TheRealDeal articles often have these patterns or real estate content
    const realEstateKeywords = [
      'hotel', 'office', 'residential', 'commercial', 'retail', 'industrial',
      'property', 'building', 'development', 'construction', 'real-estate',
      'investment', 'sale', 'lease', 'rent', 'acquisition', 'financing',
      'mortgage', 'loan', 'deal', 'transaction', 'market', 'portfolio',
      'reit', 'fund', 'capital', 'equity', 'debt', 'refinance', 'refi',
      'manhattan', 'brooklyn', 'queens', 'bronx', 'nyc', 'new-york',
      'los-angeles', 'chicago', 'miami', 'boston', 'dallas', 'houston',
      'apartment', 'condo', 'co-op', 'townhouse', 'warehouse', 'facility',
      'plaza', 'tower', 'center', 'complex', 'square', 'avenue', 'street',
      'million', 'billion', 'sf', 'sqft', 'acre', 'floor', 'story',
      'zoning', 'permits', 'planning', 'approval', 'closing', 'sold'
    ];
    
    // Check if URL contains real estate keywords
    const hasRealEstateContent = realEstateKeywords.some(keyword => 
      normalizedUrl.includes(keyword)
    );
    
    // TheRealDeal section patterns (but don't require them)
    const trdSectionPatterns = [
      '/news/', '/articles/', '/sector/', '/commercial/', '/residential/',
      '/new-york/', '/los-angeles/', '/chicago/', '/miami/', '/national/'
    ];
    
    const hasKnownSection = trdSectionPatterns.some(pattern => url.includes(pattern));
    
    // Also check for URLs with meaningful content (longer descriptive paths)
    const urlPath = url.split('therealdeal.com')[1] || '';
    const hasDescriptiveContent = urlPath.length > 15 && urlPath.includes('-');
    
    // Numeric ID patterns (TheRealDeal articles often have IDs)
    const hasNumericId = /\/[\w-]+-\d+\/?$/.test(url);
    
    // Accept if it has real estate content, known sections, descriptive content, or numeric IDs
    return hasRealEstateContent || hasKnownSection || hasDescriptiveContent || hasNumericId;
  }
} 