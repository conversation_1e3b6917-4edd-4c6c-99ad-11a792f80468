import { ScraperManager, ScrapingSession } from './ScraperManager';

export interface ScrapingProcessorResult {
  processed: number;
  successful: number;
  failed: number;
  errors: string[];
  details: string[];
}

export class ScrapingProcessor {
  private scraperManager: ScraperManager;
  private name: string;

  constructor(name: string = 'ScrapingProcessor') {
    this.name = name;
    this.scraperManager = new ScraperManager({
      headless: true,
      maxConcurrentScrapers: 1,
      waitTimeBetweenSites: 5000
    });
  }

  private log(level: string, message: string): void {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${this.name}] [${level.toUpperCase()}] ${message}`);
  }

  async process(): Promise<ScrapingProcessorResult> {
    const result: ScrapingProcessorResult = {
      processed: 0,
      successful: 0,
      failed: 0,
      errors: [],
      details: []
    };

    try {
      this.log('info', 'Starting news scraping process...');
      
      // Initialize the scraper manager
      await this.scraperManager.initialize();
      
      // Run all scrapers
      const session: ScrapingSession = await this.scraperManager.runAllScrapers(false, 30);
      
      // Process results
      result.processed = session.results.size;
      
      for (const [siteName, siteResult] of session.results) {
        if (siteResult.success) {
          result.successful++;
          result.details.push(`${siteName}: Found ${siteResult.newLinksFound} new links`);
        } else {
          result.failed++;
          result.errors.push(`${siteName}: ${siteResult.errors.join(', ')}`);
        }
      }
      
      // Add session-level errors
      if (session.errors.length > 0) {
        result.errors.push(...session.errors);
      }
      
      this.log('info', `Scraping completed. Total new links: ${session.totalNewLinks}, Total processed: ${session.totalProcessed}`);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.log('error', `Error in scraping process: ${errorMessage}`);
      result.errors.push(errorMessage);
      result.failed = 1;
    } finally {
      // Cleanup
      await this.scraperManager.cleanup();
    }

    return result;
  }
}

export class NewsFetchingProcessor {
  private scraperManager: ScraperManager;
  private name: string;

  constructor(name: string = 'NewsFetchingProcessor') {
    this.name = name;
    this.scraperManager = new ScraperManager({
      headless: true
    });
  }

  private log(level: string, message: string): void {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${this.name}] [${level.toUpperCase()}] ${message}`);
  }

  async process(): Promise<ScrapingProcessorResult> {
    const result: ScrapingProcessorResult = {
      processed: 0,
      successful: 0,
      failed: 0,
      errors: [],
      details: []
    };

    try {
      this.log('info', 'Starting news fetching process...');
      
      // Initialize the scraper manager
      await this.scraperManager.initialize();
      
      // Run news fetcher
      await this.scraperManager.runNewsFetcher();
      
      // Note: NewsFetcher doesn't return detailed results in the current implementation
      // You might want to modify it to return more detailed results
      result.processed = 1;
      result.successful = 1;
      result.details.push('News fetching completed');
      
      this.log('info', 'News fetching completed successfully');
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.log('error', `Error in news fetching process: ${errorMessage}`);
      result.errors.push(errorMessage);
      result.failed = 1;
    } finally {
      // Cleanup
      await this.scraperManager.cleanup();
    }

    return result;
  }
} 