/**
 * Data formatting utilities for database insertion
 * Handles array formatting, numeric validation, and type conversion
 */

export interface DataFormatterOptions {
  maxNumericValue?: number;
  minNumericValue?: number;
  maxArrayLength?: number;
  maxStringLength?: number;
}

export class DataFormatter {
  private options: DataFormatterOptions;

  constructor(options: DataFormatterOptions = {}) {
    this.options = {
      maxNumericValue: 999999999999, // ~1 trillion
      minNumericValue: -999999999999,
      maxArrayLength: 100,
      maxStringLength: 1000,
      ...options
    };
  }

  /**
   * Format a value for database insertion based on column type
   */
  formatValueForColumn(value: any, columnType: string, columnName: string): any {
    if (value === null || value === undefined) {
      return null;
    }

    try {
      switch (columnType) {
        case 'ARRAY':
          return this.formatArrayValue(value, columnName);
        case 'numeric':
        case 'double precision':
        case 'real':
          return this.formatNumericValue(value, columnName);
        case 'integer':
        case 'bigint':
          return this.formatIntegerValue(value, columnName);
        case 'boolean':
          return this.formatBooleanValue(value);
        case 'timestamp with time zone':
        case 'timestamp without time zone':
          return this.formatTimestampValue(value);
        case 'text':
        case 'character varying':
          return this.formatTextValue(value);
        default:
          return value;
      }
    } catch (error) {
      console.warn(`Error formatting value for column ${columnName}:`, error);
      return null;
    }
  }

  /**
   * Format array values for PostgreSQL array columns
   */
  private formatArrayValue(value: any, columnName: string): any[] | null {
    if (!value) return null;

    let arrayValue: any[];

    // Handle different input formats
    if (Array.isArray(value)) {
      arrayValue = value;
    } else if (typeof value === 'string') {
      // Handle comma-separated strings
      if (value.includes(',')) {
        arrayValue = value.split(',').map(item => item.trim()).filter(item => item.length > 0);
      } else {
        // Single value as array
        arrayValue = [value.trim()];
      }
    } else {
      // Convert to array
      arrayValue = [String(value)];
    }

    // Filter out empty/null values and limit length
    arrayValue = arrayValue
      .filter(item => item !== null && item !== undefined && item !== '')
      .slice(0, this.options.maxArrayLength!);

    // Special handling for location columns that might contain complex strings
    if (this.isLocationColumn(columnName)) {
      arrayValue = this.cleanLocationArray(arrayValue);
    }

    return arrayValue.length > 0 ? arrayValue : null;
  }

  /**
   * Clean location arrays to prevent malformed array literal errors
   */
  private cleanLocationArray(array: any[]): string[] {
    return array.map(item => {
      let cleanItem = String(item).trim();
      
      // Remove problematic characters that cause PostgreSQL array literal issues
      cleanItem = cleanItem
        .replace(/[{}"]/g, '') // Remove PostgreSQL array literal delimiters
        .replace(/\\/g, '') // Remove backslashes
        .replace(/\n/g, ' ') // Replace newlines with spaces
        .replace(/\r/g, ' ') // Replace carriage returns with spaces
        .replace(/\t/g, ' ') // Replace tabs with spaces
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();

      // Limit length
      if (cleanItem.length > this.options.maxStringLength!) {
        cleanItem = cleanItem.substring(0, this.options.maxStringLength!);
      }

      return cleanItem;
    }).filter(item => item.length > 0);
  }

  /**
   * Check if a column is a location-related column
   */
  private isLocationColumn(columnName: string): boolean {
    const locationColumns = [
      'city', 'state', 'region', 'country', 'location_focus',
      'property_types', 'property_sub_categories', 'strategies',
      'financial_products', 'loan_program', 'loan_type',
      'recourse_loan', 'structured_loan_tranche', 'capital_position'
    ];
    return locationColumns.includes(columnName.toLowerCase());
  }

  /**
   * Format numeric values with overflow protection
   */
  private formatNumericValue(value: any, columnName: string): number | null {
    if (value === null || value === undefined || value === '') {
      return null;
    }

    let numValue: number;

    if (typeof value === 'number') {
      numValue = value;
    } else if (typeof value === 'string') {
      // Remove currency symbols, commas, and other formatting
      const cleanValue = value
        .replace(/[$,€£¥₹₿]/g, '') // Remove currency symbols
        .replace(/,/g, '') // Remove commas
        .replace(/%/g, '') // Remove percentage signs
        .replace(/\s+/g, '') // Remove whitespace
        .trim();

      numValue = parseFloat(cleanValue);
    } else {
      numValue = Number(value);
    }

    // Check for valid number
    if (isNaN(numValue) || !Number.isFinite(numValue)) {
      console.warn(`Invalid numeric value for column ${columnName}: ${value}`);
      return null;
    }

    // Check bounds
    if (numValue > this.options.maxNumericValue!) {
      console.warn(`Numeric overflow for column ${columnName}: ${numValue} exceeds maximum ${this.options.maxNumericValue}`);
      return this.options.maxNumericValue;
    }

    if (numValue < this.options.minNumericValue!) {
      console.warn(`Numeric underflow for column ${columnName}: ${numValue} below minimum ${this.options.minNumericValue}`);
      return this.options.minNumericValue;
    }

    return numValue;
  }

  /**
   * Format integer values
   */
  private formatIntegerValue(value: any, columnName: string): number | null {
    const numValue = this.formatNumericValue(value, columnName);
    if (numValue === null) return null;
    
    return Math.round(numValue);
  }

  /**
   * Format boolean values
   */
  private formatBooleanValue(value: any): boolean {
    if (typeof value === 'boolean') return value;
    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase().trim();
      return ['true', '1', 'yes', 'on'].includes(lowerValue);
    }
    if (typeof value === 'number') return value !== 0;
    return Boolean(value);
  }

  /**
   * Format timestamp values
   */
  private formatTimestampValue(value: any): string | null {
    if (!value) return null;

    // Handle ISO timestamp placeholders
    if (typeof value === 'string' && value.includes('ISO_TIMESTAMP_PLACEHOLDER')) {
      return new Date().toISOString();
    }

    try {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        console.warn(`Invalid timestamp value: ${value}`);
        return null;
      }
      return date.toISOString();
    } catch (error) {
      console.warn(`Error parsing timestamp ${value}:`, error);
      return null;
    }
  }

  /**
   * Format text values
   */
  private formatTextValue(value: any): string | null {
    if (value === null || value === undefined) return null;
    
    let textValue = String(value).trim();
    
    // Limit length
    if (textValue.length > this.options.maxStringLength!) {
      textValue = textValue.substring(0, this.options.maxStringLength!);
    }
    
    return textValue.length > 0 ? textValue : null;
  }

  /**
   * Validate and format a complete record for database insertion
   */
  formatRecordForInsertion(
    record: Record<string, any>, 
    columnTypes: Record<string, string>
  ): Record<string, any> {
    const formattedRecord: Record<string, any> = {};

    for (const [columnName, value] of Object.entries(record)) {
      const columnType = columnTypes[columnName];
      if (columnType) {
        formattedRecord[columnName] = this.formatValueForColumn(value, columnType, columnName);
      } else {
        // If column type is unknown, try to format as text
        formattedRecord[columnName] = this.formatTextValue(value);
      }
    }

    return formattedRecord;
  }

  /**
   * Get column types from database schema
   */
  static async getColumnTypes(tableName: string, pool: any): Promise<Record<string, string>> {
    const result = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = $1 AND table_schema = 'public'
      ORDER BY ordinal_position
    `, [tableName]);

    const columnTypes: Record<string, string> = {};
    result.rows.forEach((row: any) => {
      columnTypes[row.column_name] = row.data_type;
    });

    return columnTypes;
  }
}

// Export a default instance with conservative settings
export const defaultDataFormatter = new DataFormatter({
  maxNumericValue: 999999999999,
  minNumericValue: -999999999999,
  maxArrayLength: 50,
  maxStringLength: 500
}); 