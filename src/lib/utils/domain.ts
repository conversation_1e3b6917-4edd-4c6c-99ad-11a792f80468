/**
 * Domain extraction using the Public Suffix List when available.
 * - Prefer using `tldts` (kept up-to-date with the PSL) for accurate eTLD+1.
 * - Falls back to a heuristic that handles common multi-level TLD patterns
 *   (e.g., co.uk, com.au, us.com) without maintaining an exhaustive list here.
 * 
 * To enable the accurate path, install:
 *   npm i tldts --save
 */

let tldts: any = null;
try {
  // Optional dependency. If not installed, gracefully degrade to the fallback logic.
  // eslint-disable-next-line import/no-extraneous-dependencies, global-require
  tldts = require('tldts');
} catch (_) {
  tldts = null;
}

function isIpAddress(hostname: string): boolean {
  // IPv4 or IPv6 (very loose check for IPv6)
  return /^(\d{1,3}\.){3}\d{1,3}$/.test(hostname) || /:/.test(hostname);
}

/**
 * Extract domain from a URL string with improved accuracy
 * @param url - The URL to extract domain from
 * @returns The extracted domain or empty string if invalid
 */
export function extractDomain(url: string): string {
  try {
    if (!url) return "";
    if (!url.startsWith("http")) {
      url = `https://${url}`;
    }
    const parsedUrl = new URL(url);
    const hostname = parsedUrl.hostname;

    // Preferred: use tldts when available for PSL-accurate extraction
    if (tldts && typeof tldts.getDomain === 'function') {
      const registrable = tldts.getDomain(hostname, { allowPrivateDomains: true });
      if (registrable) {
        return registrable;
      }
    }

    // Fallback heuristic: handle common multi-level TLD tokens
    const parts = hostname.split('.');
    if (parts.length < 2) return hostname;

    // A broad set of tokens used as second-level categories across ccTLDs and
    // CentralNic-style namespaces under .com/.net (co, com, net, org, gov, edu, etc.).
    // This is NOT exhaustive like PSL, but covers common cases until `tldts` is installed.
    const secondLevelTokens = new Set([
      'ac','co','com','net','org','gov','edu','mil','int','nom','ne','or','id','me','sch','plc','ltd',
      // Country codes used as pseudo second-level under .com/.net (e.g., us.com, uk.net)
      'us','uk','de','in','br','au','ca','jp','fr','it','es','ru','mx','nl','se','ch','no','fi','dk',
      'ie','nz','za','pl','tr','ar','at','be','hk','kr','tw','pt','gr','cz','hu','sg','il','my','ro',
      'ua','th','sk','bg','si','lt','lv','ee','vn'
    ]);

    if (secondLevelTokens.has(parts[parts.length - 2])) {
      return parts.slice(-3).join('.');
    }
    return parts.slice(-2).join('.');
  } catch (error) {
    console.error('Invalid URL:', error);
    return "";
  }
}

/**
 * Format website URL to include protocol
 * @param website - The website URL to format
 * @returns Formatted URL with https:// protocol
 */
export function formatWebsite(website: string): string {
  if (!website) return ''
  if (!website.startsWith('http://') && !website.startsWith('https://')) {
    return `https://${website}`
  }
  return website
}

/**
 * Normalize LinkedIn URL by removing protocol and www for comparison
 * @param url - The LinkedIn URL to normalize
 * @returns Normalized LinkedIn URL for comparison
 */
export function normalizeLinkedInUrl(url: string): string {
  if (!url) return '';
  
  try {
    // Remove protocol and www, then compare the path
    let normalized = url.toLowerCase().trim();
    
    // Remove http:// or https://
    normalized = normalized.replace(/^https?:\/\//, '');
    
    // Remove www. if present
    normalized = normalized.replace(/^www\./, '');
    
    // Ensure it starts with linkedin.com
    if (!normalized.startsWith('linkedin.com')) {
      return '';
    }
    
    return normalized;
  } catch (error) {
    console.error(`Error normalizing LinkedIn URL: ${error}`);
    return '';
  }
}

/**
 * Interface for company website data
 */
export interface CompanyWebsite {
  company_id: number;
  company_name: string;
  website: string;
  extracted_domain?: string;
}

/**
 * Interface for domain matching results
 */
export interface DomainMatch {
  company_id: number;
  company_name: string;
  website: string;
  extracted_domain: string;
  has_matching_domain: boolean;
  matching_companies?: Array<{
    company_id: number;
    company_name: string;
    website: string;
  }>;
}

/**
 * Fetch all websites and company IDs from the database
 * @returns Promise<CompanyWebsite[]> Array of company website data
 */
export async function fetchAllCompanyWebsites(): Promise<CompanyWebsite[]> {
  try {
    const { pool } = await import('../db');
    
    const query = `
      SELECT 
        company_id,
        companyname as company_name,
        website
      FROM company_extracted_data 
      WHERE website IS NOT NULL 
        AND website != '' 
        AND website != 'N/A'
        AND website != 'n/a'
      ORDER BY company_id
    `;
    
    const result = await pool.query(query);
    
    // Process and extract domains
    const companies: CompanyWebsite[] = result.rows.map((row: any) => ({
      company_id: row.company_id,
      company_name: row.company_name,
      website: row.website,
      extracted_domain: extractDomain(row.website)
    }));
    
    return companies;
  } catch (error) {
    console.error('Error fetching company websites:', error);
    throw error;
  }
}

/**
 * Check for matching domains among companies
 * @param companies - Array of company website data
 * @returns Promise<DomainMatch[]> Array of domain matching results
 */
export async function checkMatchingDomains(companies?: CompanyWebsite[]): Promise<DomainMatch[]> {
  try {
    // If no companies provided, fetch them
    const companyData = companies || await fetchAllCompanyWebsites();
    
    // Create a map of domains to companies
    const domainMap = new Map<string, Array<{company_id: number, company_name: string, website: string}>>();
    
    // Group companies by extracted domain
    companyData.forEach(company => {
      if (company.extracted_domain) {
        if (!domainMap.has(company.extracted_domain)) {
          domainMap.set(company.extracted_domain, []);
        }
        domainMap.get(company.extracted_domain)!.push({
          company_id: company.company_id,
          company_name: company.company_name,
          website: company.website
        });
      }
    });
    
    // Generate results
    const results: DomainMatch[] = companyData.map(company => {
      const matchingCompanies = company.extracted_domain ? 
        domainMap.get(company.extracted_domain) || [] : [];
      
      return {
        company_id: company.company_id,
        company_name: company.company_name,
        website: company.website,
        extracted_domain: company.extracted_domain || '',
        has_matching_domain: matchingCompanies.length > 1,
        matching_companies: matchingCompanies.length > 1 ? 
          matchingCompanies.filter(c => c.company_id !== company.company_id) : undefined
      };
    });
    
    return results;
  } catch (error) {
    console.error('Error checking matching domains:', error);
    throw error;
  }
}

/**
 * Get companies with matching domains (duplicates)
 * @returns Promise<DomainMatch[]> Array of companies that have matching domains
 */
export async function getCompaniesWithMatchingDomains(): Promise<DomainMatch[]> {
  try {
    const allResults = await checkMatchingDomains();
    return allResults.filter(result => result.has_matching_domain);
  } catch (error) {
    console.error('Error getting companies with matching domains:', error);
    throw error;
  }
}

/**
 * Get unique domains and their associated companies
 * @returns Promise<Map<string, CompanyWebsite[]>> Map of domains to companies
 */
export async function getUniqueDomainsWithCompanies(): Promise<Map<string, CompanyWebsite[]>> {
  try {
    const companies = await fetchAllCompanyWebsites();
    const domainMap = new Map<string, CompanyWebsite[]>();
    
    companies.forEach(company => {
      if (company.extracted_domain) {
        if (!domainMap.has(company.extracted_domain)) {
          domainMap.set(company.extracted_domain, []);
        }
        domainMap.get(company.extracted_domain)!.push(company);
      }
    });
    
    return domainMap;
  } catch (error) {
    console.error('Error getting unique domains with companies:', error);
    throw error;
  }
}

// Example usage:
// console.log(extractDomain('sub.example.com'));         // Output: example.com
// console.log(extractDomain('https://www.example.co.uk'));       // Output: example.co.uk
// console.log(extractDomain('https://example.com'));             // Output: example.com
// console.log(extractDomain('https://***************'));         // Output: ***************
// console.log(extractDomain('https://subdomain.example.com.au')); // Output: example.com.au
// console.log(extractDomain('https://localhost:3000'));          // Output: localhost
// console.log(extractDomain('https://invalid-url'));             // Output: null
// console.log(extractDomain(''));                                // Output: null
// console.log(extractDomain('http://www.mzoosleepmask.us.com'));             // Output: mzoosleepmask.us.com
// console.log(extractDomain('https://shopkhim.myshopify.com'));             // Output: shopkhim.myshopify.com 