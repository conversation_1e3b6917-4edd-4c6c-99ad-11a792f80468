import { ProcessingStatsRaw, ProcessingStats, ContactStatsRaw, CompanyStatsRaw, NewsStatsRaw, ContactStats, CompanyStats, NewsStats, StageStats } from '@/types/processing';

/**
 * Safely converts a value to a number
 */
function toNumber(value: any): number {
  if (typeof value === 'number') return value;
  if (typeof value === 'string') {
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? 0 : parsed;
  }
  return 0;
}

/**
 * Transforms raw contact stats to structured ContactStats for UI consumption
 */
export function transformContactStats(raw: ContactStatsRaw): ContactStats {
  const createStageStats = (prefix: string, specificTotal?: number): StageStats => {
    const pending = toNumber((raw as any)[`${prefix}_pending`]) || 0;
    const completed = toNumber((raw as any)[`${prefix}_completed`]) || 0;
    const failed = toNumber((raw as any)[`${prefix}_failed`]) || 0;
    const error = toNumber((raw as any)[`${prefix}_error`]) || 0;
    const running = 0; // API doesn't provide running status
    
    // Use specific total if provided, otherwise calculate from stage components
    const total = specificTotal || (pending + completed + failed + error);
    
    return {
      total,
      pending,
      running,
      completed,
      failed,
      error,
      success_rate: total > 0 ? (completed / total) * 100 : 0
    };
  };

  return {
    email_validation: createStageStats('email_validation', toNumber(raw.email_validation_total)),
    email_generation: createStageStats('email_generation')
  };
}

/**
 * Transforms raw company stats to structured CompanyStats for UI consumption
 */
export function transformCompanyStats(raw: CompanyStatsRaw): CompanyStats {
  const createStageStats = (prefix: string): StageStats => {
    const pending = toNumber((raw as any)[`${prefix}_pending`]) || 0;
    const completed = toNumber((raw as any)[`${prefix}_completed`]) || 0;
    const failed = toNumber((raw as any)[`${prefix}_failed`]) || 0;
    const error = toNumber((raw as any)[`${prefix}_error`]) || 0;
    const running = 0; // API doesn't provide running status
    
    // Calculate total from stage components
    const total = pending + completed + failed + error;
    
    return {
      total,
      pending,
      running,
      completed,
      failed,
      error,
      success_rate: total > 0 ? (completed / total) * 100 : 0
    };
  };

  return {
    web_crawler: createStageStats('web_crawler')
  };
}

/**
 * Transforms raw news stats to structured NewsStats for UI consumption
 */
export function transformNewsStats(raw: NewsStatsRaw): NewsStats {
  const createStageStats = (prefix: string): StageStats => {
    const pending = toNumber((raw as any)[`${prefix}_pending`]) || 0;
    const completed = toNumber((raw as any)[`${prefix}_completed`]) || 0;
    const failed = toNumber((raw as any)[`${prefix}_failed`]) || 0;
    const error = toNumber((raw as any)[`${prefix}_error`]) || 0;
    const running = 0; // API doesn't provide running status
    
    // Calculate total from stage components
    const total = pending + completed + failed + error;
    
    return {
      total,
      pending,
      running,
      completed,
      failed,
      error,
      success_rate: total > 0 ? (completed / total) * 100 : 0
    };
  };

  return {
    news_fetch: createStageStats('fetch'),
    news_enrichment: createStageStats('enrichment')
  };
}

/**
 * Transforms raw processing stats API response to structured ProcessingStats for UI consumption
 */
export function transformProcessingStats(raw: ProcessingStatsRaw): ProcessingStats {

  return {
    stats: {
      contacts: raw.stats.contacts,
      companies: raw.stats.companies,
      news: raw.stats.news
    },
    errors: {
      contacts: raw.errors.contacts,
      companies: raw.errors.companies,
      news: raw.errors.news
    },
    metadata: {
      timestamp: raw.metadata?.timestamp || new Date().toISOString(),
      unified_view: {
        total_entities: 0,
        total_contacts: 0,
        total_companies: 0
      }
    }
  };
}

/**
 * Checks if the API response has the expected structure
 */
export function isProcessingStatsRaw(data: any): data is ProcessingStatsRaw {
  return (
    data &&
    data.stats &&
    data.stats.contacts &&
    data.stats.companies &&
    data.stats.news &&
    data.errors &&
    (typeof data.stats.contacts.total_contacts === 'number' || typeof data.stats.contacts.total_contacts === 'string') &&
    (typeof data.stats.companies.total_companies === 'number' || typeof data.stats.companies.total_companies === 'string') &&
    (typeof data.stats.news.total_news === 'number' || typeof data.stats.news.total_news === 'string') &&
    Array.isArray(data.errors.contacts) &&
    Array.isArray(data.errors.companies) &&
    Array.isArray(data.errors.news)
  );
} 