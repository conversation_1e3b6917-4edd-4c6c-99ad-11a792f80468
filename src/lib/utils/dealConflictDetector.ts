import { pool } from "@/lib/db";
import {
  generateDealEmbedding,
  DealEmbeddingData,
} from "@/lib/embeddings/EmbeddingService";

export interface PotentialDealDuplicate {
  deal_id: number;
  deal_name: string | null;
  sponsor_name: string | null;
  neighborhood: string | null;
  zip_code: string | null;
  property_description: string | null;
  city: string | null; // from investment_criteria
  state: string | null; // from investment_criteria
  property_type: string | null;
  created_at: string;
  similarity_score: number;
  match_reason: string;
}

/**
 * Calculate string similarity using Jaro-Winkler algorithm
 * Returns a value between 0 and 1, where 1 is an exact match
 */
function jaroWinklerSimilarity(s1: string, s2: string): number {
  // Normalize strings
  const str1 = s1.toLowerCase().trim();
  const str2 = s2.toLowerCase().trim();

  if (str1 === str2) return 1.0;
  if (str1.length === 0 || str2.length === 0) return 0.0;

  // Calculate Jaro similarity
  const matchDistance = Math.floor(Math.max(str1.length, str2.length) / 2) - 1;
  const s1Matches = new Array(str1.length).fill(false);
  const s2Matches = new Array(str2.length).fill(false);

  let matches = 0;
  let transpositions = 0;

  // Find matches
  for (let i = 0; i < str1.length; i++) {
    const start = Math.max(0, i - matchDistance);
    const end = Math.min(i + matchDistance + 1, str2.length);

    for (let j = start; j < end; j++) {
      if (s2Matches[j] || str1[i] !== str2[j]) continue;
      s1Matches[i] = true;
      s2Matches[j] = true;
      matches++;
      break;
    }
  }

  if (matches === 0) return 0.0;

  // Calculate transpositions
  let k = 0;
  for (let i = 0; i < str1.length; i++) {
    if (!s1Matches[i]) continue;
    while (!s2Matches[k]) k++;
    if (str1[i] !== str2[k]) transpositions++;
    k++;
  }

  const jaro =
    (matches / str1.length +
      matches / str2.length +
      (matches - transpositions / 2) / matches) /
    3;

  // Calculate Jaro-Winkler similarity with prefix scaling
  const prefixLength = Math.min(4, Math.min(str1.length, str2.length));
  let prefix = 0;
  for (let i = 0; i < prefixLength; i++) {
    if (str1[i] === str2[i]) prefix++;
    else break;
  }

  return jaro + 0.1 * prefix * (1 - jaro);
}

function normalizeArrayOrString(
  val: string | string[] | null | undefined
): string[] {
  if (Array.isArray(val)) {
    return val.map((v) => (v || "").toLowerCase().trim()).filter(Boolean);
  }
  if (typeof val === "string") {
    return [(val || "").toLowerCase().trim()];
  }
  return [];
}

/**
 * Check if two locations match
 * Considers city, state, and region for matching
 */
function locationsMatch(
  existing: {
    neighborhood?: string | null;
    zip_code?: string | null;
    property_description?: string | null;
    city?: string | string[] | null;
    state?: string | string[] | null;
  },
  incoming: {
    neighborhood?: string | null;
    zip_code?: string | null;
    property_description?: string | null;
    city?: string | string[] | null;
    state?: string | string[] | null;
  }
): boolean {
  const existingNeighborhood = (existing.neighborhood || "")
    .toLowerCase()
    .trim();
  const incomingNeighborhood = (incoming.neighborhood || "")
    .toLowerCase()
    .trim();
  if (
    existingNeighborhood &&
    incomingNeighborhood &&
    existingNeighborhood === incomingNeighborhood
  ) {
    return true;
  }
  const existingZip = (existing.zip_code || "").toLowerCase().trim();
  const incomingZip = (incoming.zip_code || "").toLowerCase().trim();
  if (existingZip && incomingZip && existingZip === incomingZip) {
    return true;
  }
  const existingDesc = (existing.property_description || "")
    .toLowerCase()
    .trim();
  const incomingDesc = (incoming.property_description || "")
    .toLowerCase()
    .trim();
  if (existingDesc && incomingDesc && existingDesc === incomingDesc) {
    return true;
  }
  // Compare all values in city/state arrays
  const existingCities = normalizeArrayOrString(existing.city);
  const incomingCities = normalizeArrayOrString(incoming.city);
  if (
    existingCities.length &&
    incomingCities.length &&
    existingCities.some((ec) => incomingCities.includes(ec))
  ) {
    return true;
  }
  const existingStates = normalizeArrayOrString(existing.state);
  const incomingStates = normalizeArrayOrString(incoming.state);
  if (
    existingStates.length &&
    incomingStates.length &&
    existingStates.some((es) => incomingStates.includes(es))
  ) {
    return true;
  }
  return false;
}

/**
 * Find potential deal duplicates using name and location only (no embeddings)
 */
export async function findPotentialDealDuplicates(
  dealData: {
    deal_name?: string | null;
    sponsor_name?: string | null;
    neighborhood?: string | null;
    zip_code?: string | null;
    property_description?: string | null;
    city?: string | null; // from investment_criteria
    state?: string | null; // from investment_criteria
    property_type?: string | null;
    deal_summary?: string | null;
    description?: string | null;
    [key: string]: any;
  },
  _similarityThreshold: number = 0.85
): Promise<PotentialDealDuplicate[]> {
  console.log("🔍 === NAME+LOCATION DUPLICATE DETECTION STARTED ===");
  console.log("📊 Incoming Deal Data:", dealData);
  try {
    const incomingDealName = (dealData.deal_name || "").trim().toLowerCase();
    if (!incomingDealName) {
      return [];
    }
    const incomingCity = Array.isArray(dealData.city)
      ? dealData.city[0]
      : dealData.city;
    const incomingState = Array.isArray(dealData.state)
      ? dealData.state[0]
      : dealData.state;
    const incomingNeighborhood = (dealData.neighborhood || "")
      .toLowerCase()
      .trim();
    const incomingZip = (dealData.zip_code || "").toLowerCase().trim();
    // Query all deals with the same deal_name (case-insensitive)
    const sql = `
      SELECT d.deal_id, d.deal_name, d.sponsor_name, d.neighborhood, d.zip_code, d.property_description, d.created_at,
        ic.city, ic.state
      FROM public.deals d
      LEFT JOIN public.investment_criteria ic ON ic.entity_type = 'Deal' AND ic.entity_id = d.deal_id::text
      WHERE LOWER(TRIM(d.deal_name)) = $1
        AND d.status NOT IN ('deleted', 'Replaced')
    `;
    const result = await pool.query(sql, [incomingDealName]);
    const potentialDuplicates: PotentialDealDuplicate[] = [];
    for (const deal of result.rows) {
      // Check for location match
      let locationMatch = false;
      // Neighborhood
      if (
        deal.neighborhood &&
        incomingNeighborhood &&
        deal.neighborhood.toLowerCase().trim() === incomingNeighborhood
      ) {
        locationMatch = true;
      }
      // Zip code
      if (
        !locationMatch &&
        deal.zip_code &&
        incomingZip &&
        deal.zip_code.toLowerCase().trim() === incomingZip
      ) {
        locationMatch = true;
      }
      // City
      if (!locationMatch && deal.city && incomingCity) {
        const dealCities = Array.isArray(deal.city) ? deal.city : [deal.city];
        const incomingCities = Array.isArray(incomingCity)
          ? incomingCity
          : [incomingCity];
        if (
          dealCities.some((c: string) =>
            incomingCities.includes(c.toLowerCase().trim())
          )
        ) {
          locationMatch = true;
        }
      }
      // State
      if (!locationMatch && deal.state && incomingState) {
        const dealStates = Array.isArray(deal.state)
          ? deal.state
          : [deal.state];
        const incomingStates = Array.isArray(incomingState)
          ? incomingState
          : [incomingState];
        if (
          dealStates.some((s: string) =>
            incomingStates.includes(s.toLowerCase().trim())
          )
        ) {
          locationMatch = true;
        }
      }
      if (locationMatch) {
        potentialDuplicates.push({
          deal_id: deal.deal_id,
          deal_name: deal.deal_name,
          sponsor_name: deal.sponsor_name,
          neighborhood: deal.neighborhood,
          zip_code: deal.zip_code,
          property_description: deal.property_description,
          city: deal.city,
          state: deal.state,
          property_type: deal.property_type,
          created_at: deal.created_at,
          similarity_score: 1,
          match_reason: "deal_name + location match (no embeddings)",
        });
      }
    }
    return potentialDuplicates;
  } catch (error) {
    console.error("❌ Error in name+location duplicate detection:", error);
    return [];
  }
}

/**
 * Fallback duplicate detection using basic name matching
 * Used when vector search fails
 */
async function fallbackDuplicateDetection(
  dealData: any,
  similarityThreshold: number
): Promise<PotentialDealDuplicate[]> {
  const incomingDealName = (dealData.deal_name || "").trim().toLowerCase();
  if (!incomingDealName) {
    return [];
  }
  const incomingCity = Array.isArray(dealData.city)
    ? dealData.city[0]
    : dealData.city;
  const incomingState = Array.isArray(dealData.state)
    ? dealData.state[0]
    : dealData.state;
  const incomingNeighborhood = dealData.neighborhood || null;
  const incomingZip = dealData.zip_code || null;
  const fallbackQuery = `
    SELECT d.deal_id, d.deal_name, d.sponsor_name, d.neighborhood, d.zip_code, d.property_description, d.created_at,
      ic.city, ic.state
    FROM public.deals d
    LEFT JOIN public.investment_criteria ic ON ic.entity_type = 'Deal' AND ic.entity_id = d.deal_id::text
    WHERE d.status NOT IN ('deleted', 'Replaced')
      AND d.deal_name IS NOT NULL 
      AND d.deal_name != ''
      AND (
        LOWER(d.deal_name) LIKE $1
        OR ($2 IS NOT NULL AND $2 = ANY(ic.city))
        OR ($3 IS NOT NULL AND $3 = ANY(ic.state))
        OR ($4 IS NOT NULL AND d.neighborhood = $4)
        OR ($5 IS NOT NULL AND d.zip_code = $5)
      )
    LIMIT 5
  `;
  const searchTerms = [
    `%${incomingDealName}%`,
    incomingCity,
    incomingState,
    incomingNeighborhood,
    incomingZip,
  ];
  try {
    const result = await pool.query(fallbackQuery, searchTerms);
    return result.rows.map((deal) => ({
      deal_id: deal.deal_id,
      deal_name: deal.deal_name,
      sponsor_name: deal.sponsor_name,
      neighborhood: deal.neighborhood,
      zip_code: deal.zip_code,
      property_description: deal.property_description,
      city: deal.city,
      state: deal.state,
      property_type: deal.property_type,
      created_at: deal.created_at,
      similarity_score: 0.7, // Approximate similarity for fallback
      match_reason: "Basic name match (fallback method)",
    }));
  } catch (error) {
    console.error("❌ Fallback duplicate detection also failed:", error);
    return [];
  }
}

// TEST FUNCTION: Run conflict detection on all Fairview deals in the DB
export async function testFairviewConflicts() {
  const fairviewDeals = await pool.query(`
    SELECT d.deal_id, d.deal_name, d.zip_code, d.neighborhood, d.property_description, ic.city, ic.state
    FROM public.deals d
    LEFT JOIN public.investment_criteria ic ON ic.entity_type = 'Deal' AND ic.entity_id = d.deal_id::text
    WHERE d.deal_name ILIKE '%fairview%'
  `);
  const deals = fairviewDeals.rows;
  let foundConflict = false;
  for (let i = 0; i < deals.length; i++) {
    for (let j = 0; j < deals.length; j++) {
      if (i === j) continue;
      const dealA = deals[i];
      const dealB = deals[j];
      // Run locationsMatch and deal_name match
      const nameA = (dealA.deal_name || "").toLowerCase().trim();
      const nameB = (dealB.deal_name || "").toLowerCase().trim();
      const nameMatch = nameA === nameB;
      const locMatch = locationsMatch(dealA, dealB);
      console.log("--- Comparing Deals ---");
      console.log(
        `Deal A ID: ${dealA.deal_id}, Name: '${dealA.deal_name}', Neighborhood: '${dealA.neighborhood}', Zip: '${dealA.zip_code}', Desc: '${dealA.property_description}', City:`,
        dealA.city,
        "State:",
        dealA.state
      );
      console.log(
        `Deal B ID: ${dealB.deal_id}, Name: '${dealB.deal_name}', Neighborhood: '${dealB.neighborhood}', Zip: '${dealB.zip_code}', Desc: '${dealB.property_description}', City:`,
        dealB.city,
        "State:",
        dealB.state
      );
      console.log(`Name match: ${nameMatch}, Location match: ${locMatch}`);
      if (nameMatch && locMatch) {
        foundConflict = true;
        console.log(
          `CONFLICT: Deal ${dealA.deal_id} and Deal ${dealB.deal_id} match on name and location.`
        );
        console.log("Deal A:", dealA);
        console.log("Deal B:", dealB);
      }
    }
  }
  if (!foundConflict) {
    console.log("No conflicts detected among Fairview deals.");
  }
}
