import { getDealsTableColumns, getInvestmentCriteriaColumns } from '../queue/DealProcessorJob';

/**
 * Fetches the columns for a given table and returns them as an array of strings.
 */
export async function getTableColumns(table: 'deals' | 'investment_criteria'): Promise<string[]> {
  if (table === 'deals') {
    return Array.from(await getDealsTableColumns());
  } else {
    return Array.from(await getInvestmentCriteriaColumns());
  }
}

/**
 * Builds a JSON schema object for the given table, for use in LLM prompt generation.
 * Optionally accepts a map of descriptions for each field.
 */
export async function buildTableSchemaForPrompt(table: 'deals' | 'investment_criteria', descriptions?: Record<string, string>) {
  const columns = await getTableColumns(table);
  const schema: Record<string, string> = {};
  for (const col of columns) {
    schema[col] = descriptions?.[col] || 'Field from DB';
  }
  return schema;
}

export { getDealsTableColumns, getInvestmentCriteriaColumns }; 