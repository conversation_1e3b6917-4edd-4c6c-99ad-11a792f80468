export const formatNumber = (value: number | null, unit?: string): string => {
  if (value === null || value === undefined) return 'N/A';
  if (unit) {
    return `${value.toLocaleString()} ${unit}`;
  }
  return value.toLocaleString();
};

export const formatRawCurrency = (value: number | null): string => {
  if (value === null || value === undefined) return 'N/A';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

export const formatPercentage = (value: number | null): string => {
  if (value === null || value === undefined) return 'N/A';
  return `${(value * 100).toFixed(2)}%`;
};
