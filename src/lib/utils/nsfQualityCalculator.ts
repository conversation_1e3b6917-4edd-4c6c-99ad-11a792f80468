// NSF Quality Calculator for V2 Deal Processing
export const NSF_FIELD_MAPPINGS = {
  debt: ['dealType', 'nsfContext', 'gsfGrossSquareFoot', 'zfaZoningFloorArea', 'totalNsfNetSquareFoot', 'amount', 'amountPerGsf', 'amountPerNsf', 'amountPerZfa', 'percentageOfTotal', 'additionalInfo'],
  equity: ['dealType', 'nsfContext', 'gsfGrossSquareFoot', 'zfaZoningFloorArea', 'totalNsfNetSquareFoot', 'amount', 'amountPerGsf', 'amountPerNsf', 'amountPerZfa', 'percentageOfTotal', 'additionalInfo'],
  acquisition: ['dealType', 'nsfContext', 'gsfGrossSquareFoot', 'zfaZoningFloorArea', 'totalNsfNetSquareFoot', 'amount', 'amountPerGsf', 'amountPerNsf', 'amountPerZfa', 'percentageOfTotal', 'additionalInfo'],
  hard_cost: ['dealType', 'nsfContext', 'gsfGrossSquareFoot', 'zfaZoningFloorArea', 'totalNsfNetSquareFoot', 'amount', 'amountPerGsf', 'amountPerNsf', 'amountPerZfa', 'percentageOfTotal', 'additionalInfo'],
  soft_cost: ['dealType', 'nsfContext', 'gsfGrossSquareFoot', 'zfaZoningFloorArea', 'totalNsfNetSquareFoot', 'amount', 'amountPerGsf', 'amountPerNsf', 'amountPerZfa', 'percentageOfTotal', 'additionalInfo'],
  finance: ['dealType', 'nsfContext', 'gsfGrossSquareFoot', 'zfaZoningFloorArea', 'totalNsfNetSquareFoot', 'amount', 'amountPerGsf', 'amountPerNsf', 'amountPerZfa', 'percentageOfTotal', 'additionalInfo']
};

// Actual DealsV2 database fields (excluding system fields)
export const DEAL_FIELDS = [
  'dealName', 'summary', 'askCapitalPosition', 'askAmount', 'capitalRaiseTimeline',
  'dateReceived', 'dealStage', 'dateClosed', 'dateUnderContract', 'strategy',
  'holdPeriod', 'dealStatus', 'yieldOnCost', 'commonEquityInternalRateOfReturnIrr',
  'commonEquityEquityMultiple', 'gpEquityMultiple', 'gpInternalRateOfReturnIrr',
  'lpEquityMultiple', 'lpInternalRateOfReturnIrr', 'preferredEquityInternalRateOfReturnIrr',
  'preferredEquityEquityMultiple', 'totalInternalRateOfReturnIrr', 'totalEquityMultiple',
  'extractionConfidence', 'processingNotes', 'missingCriticalFields', 'dataQualityIssues',
  'extractionMethod', 'extractionTimestamp', 'processorVersion', 'llmModelUsed', 'llmProvider'
];

// Property fields
export const PROPERTY_FIELDS = [
  'address', 'city', 'state', 'zipcode', 'region', 'country', 'market', 'submarket',
  'neighborhood', 'propertyType', 'subpropertyType', 'propertyStatus', 'buildingSqft',
  'gsfGrossSquareFoot', 'zfaZoningFloorArea', 'lotArea', 'landAcres', 'numberOfUnits',
  'yearBuilt', 'yearRenovated', 'appraisalValue', 'appraisalValueDate', 'propertyDescription',
  'floorAreaRatio', 'far', 'environmentalRiskScore'
];

export const calculateCompletedFields = (data: any, fields: string[]): number => {
  return fields.reduce((count, field) => {
    const value = data[field];
    if (value !== null && value !== undefined && value !== '' && 
        !(Array.isArray(value) && value.length === 0)) {
      return count + 1;
    }
    return count;
  }, 0);
};

export const calculateNSFQuality = (nsfFields: any[]) => {
  const qualityByType: any = {};
  
  Object.keys(NSF_FIELD_MAPPINGS).forEach(type => {
    const typeFields = nsfFields.filter(f => f.deal_type === type);
    if (typeFields.length > 0) {
      let totalCompletedFields = 0;
      let totalPossibleFields = 0;
      
      typeFields.forEach(field => {
        NSF_FIELD_MAPPINGS[type as keyof typeof NSF_FIELD_MAPPINGS].forEach(fieldName => {
          totalPossibleFields++;
          if (field[fieldName] !== null && field[fieldName] !== undefined && field[fieldName] !== '') {
            totalCompletedFields++;
          }
        });
      });
      
      qualityByType[type] = {
        qualityScore: totalPossibleFields > 0 ? Math.round((totalCompletedFields / totalPossibleFields) * 100) : 0,
        completedFields: totalCompletedFields,
        totalFields: totalPossibleFields,
        recordCount: typeFields.length
      };
    }
  });
  
  return qualityByType;
};

export const calculateOverallQuality = (dealData: any, nsfFields: any[]) => {
  const dealCompleted = calculateCompletedFields(dealData, DEAL_FIELDS);
  const propertyCompleted = dealData.property ? calculateCompletedFields(dealData.property, PROPERTY_FIELDS) : 0;
  
  const nsfQuality = calculateNSFQuality(nsfFields);
  const nsfTypes = Object.keys(nsfQuality);
  const nsfCompleted = nsfTypes.reduce((total, type) => total + nsfQuality[type].completedFields, 0);
  const nsfTotal = nsfTypes.reduce((total, type) => total + nsfQuality[type].totalFields, 0);
  
  const totalCompleted = dealCompleted + propertyCompleted + nsfCompleted;
  const totalFields = DEAL_FIELDS.length + PROPERTY_FIELDS.length + nsfTotal;
  
  return {
    overall: {
      qualityScore: totalFields > 0 ? Math.round((totalCompleted / totalFields) * 100) : 0,
      completedFields: totalCompleted,
      totalFields: totalFields,
      dealCompleted,
      propertyCompleted,
      nsfCompleted
    },
    deal: {
      qualityScore: Math.round((dealCompleted / DEAL_FIELDS.length) * 100),
      completedFields: dealCompleted,
      totalFields: DEAL_FIELDS.length
    },
    property: {
      qualityScore: dealData.property ? Math.round((propertyCompleted / PROPERTY_FIELDS.length) * 100) : 0,
      completedFields: propertyCompleted,
      totalFields: PROPERTY_FIELDS.length
    },
    nsf: nsfQuality
  };
}; 