import * as cheerio from 'cheerio';
import pdfParse from "pdf-parse/lib/pdf-parse";
import { parse } from "csv-parse/sync";
import * as XLSX from "xlsx";
import mammoth from "mammoth";
/**
 * Enhanced text extraction utility for cleaning HTML content
 * This utility provides comprehensive text extraction with better filtering
 */
export class TextExtractor {
  
  /**
   * Extract clean text from HTML content with comprehensive filtering
   */
  static extractCleanText(html: string): string {
    try {
      const $ = cheerio.load(html, {
        xmlMode: false
      });
      
      // Remove unwanted elements that don't contribute to meaningful content
      $('script, style, noscript, iframe, embed, object, applet, canvas, svg, img, video, audio, form, input, textarea, select, button, nav, footer, header, aside, menu, menuitem').remove();
      
      // Remove meta tags and other non-content elements
      $('meta, link, title, base, bdo, br, hr, area, map, param, source, track, wbr').remove();
      
      // Remove elements with common ad/analytics classes
      $('[class*="ad"], [class*="ads"], [class*="analytics"], [class*="tracking"], [class*="cookie"], [class*="popup"], [class*="modal"], [class*="banner"]').remove();
      
      // Remove elements with common ad/analytics IDs
      $('[id*="ad"], [id*="ads"], [id*="analytics"], [id*="tracking"], [id*="cookie"], [id*="popup"], [id*="modal"], [id*="banner"]').remove();
      
      // Remove empty elements
      $('*').each((_, element) => {
        const $el = $(element);
        if ($el.children().length === 0 && $el.text().trim() === '') {
          $el.remove();
        }
      });
      
      // Extract text content
      let text = $('body').text();
      
      // Clean up the extracted text
      text = this.cleanExtractedText(text);
      
      return text;
    } catch (error) {
      console.error('Error extracting text:', error);
      return '';
    }
  }
  
  /**
   * Clean up extracted text by removing extra whitespace and normalizing
   */
  private static cleanExtractedText(text: string): string {
    return text
      // Replace multiple whitespace characters with single space
      .replace(/\s+/g, ' ')
      // Remove leading/trailing whitespace
      .trim()
      // Remove common unwanted patterns
      .replace(/^\s*[©®™]\s*/g, '') // Remove copyright symbols at start
      .replace(/\s*[©®™]\s*$/g, '') // Remove copyright symbols at end
      .replace(/^\s*All rights reserved\.?\s*$/gim, '') // Remove "All rights reserved"
      .replace(/^\s*Privacy Policy\s*$/gim, '') // Remove "Privacy Policy"
      .replace(/^\s*Terms of Service\s*$/gim, '') // Remove "Terms of Service"
      .replace(/^\s*Cookie Policy\s*$/gim, '') // Remove "Cookie Policy"
      // Remove empty lines
      .replace(/\n\s*\n/g, '\n')
      // Final trim
      .trim();
  }
  
  /**
   * Test the text extraction on a sample HTML
   */
  static testExtraction(html: string): { originalLength: number; extractedLength: number; extractedText: string } {
    const originalLength = html.length;
    const extractedText = this.extractCleanText(html);
    const extractedLength = extractedText.length;
    
    return {
      originalLength,
      extractedLength,
      extractedText
    };
  }
  
  /**
   * Validate if extracted text is meaningful (not just empty or too short)
   */
  static isValidContent(text: string, minLength: number = 100): boolean {
    if (!text || text.trim().length === 0) {
      return false;
    }
    
    // Check if text is too short
    if (text.trim().length < minLength) {
      return false;
    }
    
    // Check if text contains mostly special characters or numbers
    const alphanumericRatio = (text.match(/[a-zA-Z]/g) || []).length / text.length;
    if (alphanumericRatio < 0.3) {
      return false;
    }
    
    return true;
  }
}


// Placeholder: import csvParse from 'csv-parse';
// Placeholder: import xlsx from 'xlsx';

/**
 * Abstract base class for file-to-text extraction
 */
export abstract class AbstractTextExtractor {
  abstract extractText(fileBuffer: Buffer): Promise<string>;
}

/**
 * PDF text extractor using pdf-parse
 */
export class PDFTextExtractor extends AbstractTextExtractor {
  async extractText(fileBuffer: Buffer): Promise<string> {
    const data = await pdfParse(fileBuffer);
    return data.text;
  }
}

/**
 * CSV text extractor using csv-parse
 */
export class CSVTextExtractor extends AbstractTextExtractor {
  async extractText(fileBuffer: Buffer): Promise<string> {
    // Parse CSV buffer to rows
    const csvString = fileBuffer.toString("utf-8");
    const records = parse(csvString, { skip_empty_lines: true });
    // Join rows and columns into a readable text
    return records.map((row: any[]) => row.join(", ")).join("\n");
  }
}

/**
 * XLSX text extractor using xlsx
 */
export class XLSXTextExtractor extends AbstractTextExtractor {
  async extractText(fileBuffer: Buffer): Promise<string> {
    // Read workbook from buffer
    const workbook = XLSX.read(fileBuffer, { type: "buffer" });
    let allText = "";
    // Iterate over all sheets
    workbook.SheetNames.forEach((sheetName, sheetIdx) => {
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];
      allText += `=== SHEET: ${sheetName} ===\n`;
      if (jsonData.length > 0) {
        const headers = jsonData[0] || [];
        allText += `Headers: ${headers.join(" | ")}\n\n`;
        for (let rowIndex = 1; rowIndex < jsonData.length; rowIndex++) {
          const row = jsonData[rowIndex] || [];
          const rowText = row.map((cell: any) => String(cell || "")).join(" | ");
          allText += `Row ${rowIndex}: ${rowText}\n`;
        }
      } else {
        allText += "(Empty sheet)\n";
      }
      allText += "\n";
    });
    return allText.trim();
  }
}

/**
 * Word document text extractor using mammoth
 */
export class WordTextExtractor extends AbstractTextExtractor {
  async extractText(fileBuffer: Buffer): Promise<string> {
    try {
      const result = await mammoth.extractRawText({ buffer: fileBuffer });
      return result.value;
    } catch (error) {
      throw new Error(`Failed to extract text from Word document: ${error}`);
    }
  }
}
