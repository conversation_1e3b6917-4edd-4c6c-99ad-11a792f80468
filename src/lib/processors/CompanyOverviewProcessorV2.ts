import { BaseProcessor } from './BaseProcessor'
import { CompanyProcessingState, EntityData, ProcessorOptions, CompanyData, UnifiedEntityData } from '../../types/processing'
import { LLMFactory, LLMMessage, createProcessorLoggerAdapter, LogLevel } from '../llm'
import { BaseScraper } from '../scrapers/BaseScraper'
import { COMPANY_OVERVIEW_V2_SYSTEM_PROMPT, COMPANY_OVERVIEW_V2_USER_TEMPLATE_FUNCTION } from '../prompts/company-overview-v2'

// Enhanced data model to match the new comprehensive schema exactly
interface CompanyOverviewDataV2 {
  // Core Company Information
  company_name?: string
  company_type?: string
  company_industry?: string
  business_model?: string
  founded_year?: number
  
  // Investment & Strategy
  investment_focus?: string[]
  investment_strategy_mission?: string
  investment_strategy_approach?: string
  
  // Contact Information
  website?: string
  main_phone?: string
  secondary_phone?: string
  main_email?: string
  secondary_email?: string
  linkedin?: string
  twitter?: string
  facebook?: string
  instagram?: string
  youtube?: string
  
  // Address Information
  headquarters_address?: string
  headquarters_city?: string
  headquarters_state?: string
  headquarters_zipcode?: string
  headquarters_country?: string
  additional_address?: string
  additional_city?: string
  additional_state?: string
  additional_zipcode?: string
  additional_country?: string
  office_locations?: string[]
  
  // Company Metrics
  fund_size?: number
  aum?: number
  number_of_properties?: number
  number_of_offices?: number
  number_of_employees?: number
  annual_revenue?: number
  net_income?: number
  ebitda?: number
  profit_margin?: number
  market_capitalization?: number
  market_share_percentage?: number
  
  // Financial Information
  balance_sheet_strength?: string
  funding_sources?: string[]
  recent_capital_raises?: string
  typical_debt_to_equity_ratio?: number
  development_fee_structure?: string
  credit_rating?: string
  dry_powder?: number
  annual_deployment_target?: number
  
  // Investment & Fund Information
  investment_vehicle_type?: string
  active_fund_name_series?: string
  fund_size_active_fund?: number
  fundraising_status?: string
  lender_type?: string
  annual_loan_volume?: number
  lending_origin_balance_sheet_securitization?: string
  portfolio_health?: string
  
  // Partnership & Leadership
  partnerships?: string[]
  key_equity_partners?: string[]
  key_debt_partners?: string[]
  board_of_directors?: string[]
  key_executives?: string[]
  founder_background?: string
  
  // Market Positioning & Strategy
  market_cycle_positioning?: string
  urban_vs_suburban_preference?: string
  sustainability_esg_focus?: boolean
  technology_proptech_adoption?: boolean
  adaptive_reuse_experience?: boolean
  regulatory_zoning_expertise?: boolean
  
  // Corporate Structure
  corporate_structure?: string
  parent_company?: string
  subsidiaries?: string[]
  stock_ticker_symbol?: string
  stock_exchange?: string
  
  // Business Information
  products_services_description?: string
  target_customer_profile?: string
  major_competitors?: string[]
  unique_selling_proposition?: string
  industry_awards_recognitions?: string[]
  company_history?: string
  
  // Transaction & Portfolio Data
  transactions_completed_last_12m?: number
  total_transaction_volume_ytd?: number
  deal_count_ytd?: number
  average_deal_size?: number
  portfolio_size_sqft?: number
  portfolio_asset_count?: number
  role_in_previous_deal?: string
  
  // Relationship & Pipeline Data
  internal_relationship_manager?: string
  last_contact_date?: string
  pipeline_status?: string
  recent_news_sentiment?: string
  
  // Data Quality & Processing
  data_source?: string
  data_confidence_score?: number
  quarterly_earnings_link?: string
}

export class CompanyOverviewProcessorV2 extends BaseProcessor {
  private llmProvider;
  private queryParams: any[] = []
  private queries: string[] = []
  private companiesProcessed: number = 0

  constructor(options: ProcessorOptions = {}) {
    // Perplexity API specific rate limiting configuration
    const perplexityBottleneckConfig = {
      maxConcurrent: 10,                   // Reduced from 5 to prevent queue overflow
      minTime: 1500,                      // 1.5 seconds between requests
      retryAttempts: 3,                   // Standard retries for LLM API
      retryDelayBase: 2000,               // 2 second base delay for retries
      retryDelayMax: 30000,               // Max 30 second retry delay
      timeout: 240000,                    // 4 minutes timeout for LLM processing
      highWater: 300,                     // Higher queue limit for batch processing
      strategy: 'OVERFLOW' as any,        // Use OVERFLOW strategy
      defaultPriority: 3,                 // Higher priority for company overview
      enableJobMetrics: true              // Track LLM API performance
    }

    super('CompanyOverviewV2', {
      ...options,
      bottleneckConfig: options.bottleneckConfig || perplexityBottleneckConfig
    })
    
    // Create a logger adapter that uses our log method
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this));
    
    // Use Perplexity for web-enhanced research capabilities
    this.llmProvider = LLMFactory.createProvider(
      'perplexity',
      loggerAdapter,
      {
        apiKey: process.env.PERPLEXITY_API_KEY,
      }
    );
  }

  async getUnprocessedEntities(): Promise<UnifiedEntityData[]> {
    const baseFilters = {
      websiteScrapingStatus: 'completed'
    }
    const specificFilters = {
      companyOverviewV2Status: 'pending'
    }
    return await this.getUnprocessedUnifiedEntities(baseFilters, specificFilters, 'company')
  }

  async processEntity(entity: UnifiedEntityData): Promise<{ success: boolean; error?: string }> {
    // Only process companies for company overview
    if (entity.entity_type !== 'company') {
      return { success: false, error: 'Company overview only supports companies' }
    }
    
    try {
      this.log('info', `Extracting comprehensive company overview for: ${entity.company_name}`)

      // Set status to running
      await this.updateOverviewV2Status(entity.id, 'running')

      // Get company content
      const content = await this.getCompanyContent(entity.id)
      
      if ((!content || content.trim().length === 0 ) && (!this.options.multiIds || this.options.multiIds.length === 0)) {
        this.log('warn', `No content found for company ${entity.id}`)
        await this.updateOverviewV2Status(entity.id, 'failed', 'No content available for extraction')
        return { success: false, error: 'No content available for extraction' }
      }

      // Extract comprehensive company overview
      const { data: overviewData, llmResponse, llmUsage } = await this.extractCompanyOverviewV2(entity, content);
      
      if (!overviewData) {
        await this.updateOverviewV2Status(entity.id, 'failed', 'Failed to extract company overview')
        return { success: false, error: 'Failed to extract company overview' }
      }

      // Store the extracted data directly to companies table
      await this.storeCompanyOverviewV2(entity.id, overviewData, llmResponse || undefined, llmUsage || undefined)

      this.log('info', `Successfully extracted comprehensive company overview for ${entity.company_name}`)
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error extracting company overview for ${entity.company_name}: ${errorMessage}`)
      await this.updateOverviewV2Status(entity.id, 'failed', errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean): Promise<void> {
    if (success) {
      await this.updateOverviewV2Status(entityId, 'completed');
      this.log('info', `Company ${entityId} marked as processed with comprehensive data extracted`);
    } else {
      // For failed processing, mark as failed and increment error count
      await this.updateOverviewV2Status(entityId, 'failed',  "Failed to update comprehensive overview");
      await this.incrementProcessingErrorCount('company', entityId);
      this.log('info', `Company ${entityId} processing failed, marked as failed`);
    }
  }

  /**
   * Extract comprehensive company overview using LLM provider
   */
  private async extractCompanyOverviewV2(
    company: UnifiedEntityData, 
    content: string
  ): Promise<{ data: CompanyOverviewDataV2 | null; llmResponse?: string; llmUsage?: any }> {
    try {
      const messages = await this.buildExtractionMessagesV2(company, content);
      const model = 'sonar'

      // Use the LLM provider with fallback support and better model for web search
      const response = await this.llmProvider.callLLM(messages, {
        model: model,
        temperature: 0.3, // Lower temperature for more consistent extraction
        maxTokens: 15000 // Increased token limit for comprehensive extraction
      });

      if (!response.content) {
        throw new Error(`No response content from ${response.provider}`);
      }

      this.log('info', `Extracted comprehensive overview using ${response.provider} (${response.model})`);

      // Parse the structured response
      const parsedResponse = this.parseOverviewResponseV2(response.content);
      
      return {
        data: parsedResponse,
        llmResponse: response.content,
        llmUsage: response.usage
      };
    } catch (error) {
      this.log('error', `Error calling LLM extraction API: ${error}`);
      return { data: null };
    }
  }

  /**
   * Build messages for LLM comprehensive company overview extraction
   */
  private async buildExtractionMessagesV2(
    company: UnifiedEntityData, 
    content: string
  ): Promise<LLMMessage[]> {
    // Use the static getMappings method from BaseScraper
    const mappings = await BaseScraper.getMappings();
          const userPrompt = COMPANY_OVERVIEW_V2_USER_TEMPLATE_FUNCTION({
      company_name: company.company_name || '',
      company_website: company.company_website || '',
      industry: company.industry || ''
    }, content, mappings);

    return [
      { role: 'system', content: COMPANY_OVERVIEW_V2_SYSTEM_PROMPT },
      { role: 'user', content: userPrompt }
    ]
  }

  /**
   * Parse LLM response to extract comprehensive company overview data
   */
  private parseOverviewResponseV2(content: string): CompanyOverviewDataV2 | null {
    try {
      this.log('debug', `Parsing comprehensive LLM response content (length: ${content.length})`);
      
      // Clean the content and extract JSON
      let jsonContent = content.trim();
      
      // Handle Perplexity format: JSON comes AFTER </think> tag, not inside it
      const thinkMatch = jsonContent.match(/<think>[\s\S]*?<\/think>\s*([\s\S]*)/);
      if (thinkMatch) {
        // Extract content that comes AFTER the </think> tag
        const afterThinkContent = thinkMatch[1].trim();
        this.log('debug', `Extracted content after <think> tags (${afterThinkContent.length} chars)`);
        
        if (afterThinkContent) {
          jsonContent = afterThinkContent;
          this.log('debug', `Using content after </think> tag`);
        } else {
          throw new Error('No content found after </think> tag');
        }
      } else {
        // Remove any thinking tags if they appear without content
        jsonContent = jsonContent.replace(/<think>[\s\S]*?<\/think>/g, '');
        
        // Remove any leading/trailing text that isn't JSON
        jsonContent = jsonContent.replace(/^[^{]*/, '').replace(/[^}]*$/, '');
        
        // Try to extract from markdown code blocks
        const markdownJsonMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/);
        if (markdownJsonMatch) {
          jsonContent = markdownJsonMatch[1].trim();
          this.log('debug', `Extracted JSON from markdown code block`);
        } else {
          // Look for the main JSON object
          const jsonMatch = jsonContent.match(/\{[\s\S]*\}/);
          if (!jsonMatch) {
            throw new Error('No JSON found in response');
          }
          jsonContent = jsonMatch[0];
          this.log('debug', `Extracted JSON using direct object match`);
        }
      }

      // Clean up the JSON content
      jsonContent = jsonContent.trim()

      // Check if JSON appears to be truncated (doesn't end with closing brace)
      if (!jsonContent.endsWith('}')) {
        this.log('warn', `JSON appears to be truncated (doesn't end with }). Attempting to parse anyway.`)
      }

      const result = JSON.parse(jsonContent)

      this.log('info', `Successfully parsed comprehensive extraction response with ${Object.keys(result).length} fields`)
      
      // Handle nested structure from prompt: { "company": { ... } }
      let companyData = result;
      if (result.company && typeof result.company === 'object') {
        companyData = result.company;
        this.log('debug', `Extracted company data from nested structure`);
      }
      
      // Clean and return the result
      return this.cleanNullValues(companyData) as CompanyOverviewDataV2
    } catch (error) {
      this.log('error', `Error parsing comprehensive overview response: ${error}`);
      
      // Log a portion of the content for debugging
      const contentPreview = content.length > 1500 ? content.substring(0, 1500) + '...' : content;
      this.log('debug', `Response content preview: ${contentPreview}`);
      
      return null;
    }
  }

  /**
   * Store comprehensive extracted company overview directly in companies table
   */
  private async storeCompanyOverviewV2(companyId: number, data: CompanyOverviewDataV2, llmResponse?: string, llmUsage?: any): Promise<void> {
    this.log('debug', `Storing comprehensive overview data for company ${companyId}`);
    
    const sql = `
      UPDATE companies SET
        -- Core Company Information
        industry = COALESCE($1, industry),
        company_type = COALESCE($2, company_type),
        business_model = COALESCE($3, business_model),
        founded_year = COALESCE($4, founded_year),
        
        -- Investment & Strategy
        investment_focus = COALESCE($5, investment_focus),
        investment_strategy_mission = COALESCE($6, investment_strategy_mission),
        investment_strategy_approach = COALESCE($7, investment_strategy_approach),
        
        -- Contact Information
        company_website = COALESCE($8, company_website),
        company_phone = COALESCE($9, company_phone),
        secondary_phone = COALESCE($10, secondary_phone),
        main_email = COALESCE($11, main_email),
        secondary_email = COALESCE($12, secondary_email),
        company_linkedin = COALESCE($13, company_linkedin),
        twitter = COALESCE($14, twitter),
        facebook = COALESCE($15, facebook),
        instagram = COALESCE($16, instagram),
        youtube = COALESCE($17, youtube),
        
        -- Address Information
        company_address = COALESCE($18, company_address),
        company_city = COALESCE($19, company_city),
        company_state = COALESCE($20, company_state),
        company_zip = COALESCE($21, company_zip),
        company_country = COALESCE($22, company_country),
        additional_address = COALESCE($23, additional_address),
        additional_city = COALESCE($24, additional_city),
        additional_state = COALESCE($25, additional_state),
        additional_zipcode = COALESCE($26, additional_zipcode),
        additional_country = COALESCE($27, additional_country),
        office_locations = COALESCE($28, office_locations),
        
        -- Company Metrics
        fund_size = COALESCE($29, fund_size),
        aum = COALESCE($30, aum),
        number_of_properties = COALESCE($31, number_of_properties),
        number_of_offices = COALESCE($32, number_of_offices),
        number_of_employees = COALESCE($33, number_of_employees),
        annual_revenue = COALESCE($34, annual_revenue),
        net_income = COALESCE($35, net_income),
        ebitda = COALESCE($36, ebitda),
        profit_margin = COALESCE($37, profit_margin),
        market_capitalization = COALESCE($38, market_capitalization),
        market_share_percentage = COALESCE($39, market_share_percentage),
        
        -- Financial Information
        balance_sheet_strength = COALESCE($40, balance_sheet_strength),
        funding_sources = COALESCE($41, funding_sources),
        recent_capital_raises = COALESCE($42, recent_capital_raises),
        typical_debt_to_equity_ratio = COALESCE($43, typical_debt_to_equity_ratio),
        development_fee_structure = COALESCE($44, development_fee_structure),
        credit_rating = COALESCE($45, credit_rating),
        dry_powder = COALESCE($46, dry_powder),
        annual_deployment_target = COALESCE($47, annual_deployment_target),
        
        -- Investment & Fund Information
        investment_vehicle_type = COALESCE($48, investment_vehicle_type),
        active_fund_name_series = COALESCE($49, active_fund_name_series),
        fund_size_active_fund = COALESCE($50, fund_size_active_fund),
        fundraising_status = COALESCE($51, fundraising_status),
        lender_type = COALESCE($52, lender_type),
        annual_loan_volume = COALESCE($53, annual_loan_volume),
        lending_origin = COALESCE($54, lending_origin),
        portfolio_health = COALESCE($55, portfolio_health),
        
        -- Partnership & Leadership
        partnerships = COALESCE($56, partnerships),
        key_equity_partners = COALESCE($57, key_equity_partners),
        key_debt_partners = COALESCE($58, key_debt_partners),
        board_of_directors = COALESCE($59, board_of_directors),
        key_executives = COALESCE($60, key_executives),
        founder_background = COALESCE($61, founder_background),
        
        -- Market Positioning & Strategy
        market_cycle_positioning = COALESCE($62, market_cycle_positioning),
        urban_vs_suburban_preference = COALESCE($63, urban_vs_suburban_preference),
        sustainability_esg_focus = COALESCE($64, sustainability_esg_focus),
        technology_proptech_adoption = COALESCE($65, technology_proptech_adoption),
        adaptive_reuse_experience = COALESCE($66, adaptive_reuse_experience),
        regulatory_zoning_expertise = COALESCE($67, regulatory_zoning_expertise),
        
        -- Corporate Structure
        corporate_structure = COALESCE($68, corporate_structure),
        parent_company = COALESCE($69, parent_company),
        subsidiaries = COALESCE($70, subsidiaries),
        stock_ticker_symbol = COALESCE($71, stock_ticker_symbol),
        stock_exchange = COALESCE($72, stock_exchange),
        
        -- Business Information
        products_services_description = COALESCE($73, products_services_description),
        target_customer_profile = COALESCE($74, target_customer_profile),
        major_competitors = COALESCE($75, major_competitors),
        unique_selling_proposition = COALESCE($76, unique_selling_proposition),
        industry_awards_recognitions = COALESCE($77, industry_awards_recognitions),
        company_history = COALESCE($78, company_history),
        
        -- Transaction & Portfolio Data
        transactions_completed_last_12m = COALESCE($79, transactions_completed_last_12m),
        total_transaction_volume_ytd = COALESCE($80, total_transaction_volume_ytd),
        deal_count_ytd = COALESCE($81, deal_count_ytd),
        average_deal_size = COALESCE($82, average_deal_size),
        portfolio_size_sqft = COALESCE($83, portfolio_size_sqft),
        portfolio_asset_count = COALESCE($84, portfolio_asset_count),
        role_in_previous_deal = COALESCE($85, role_in_previous_deal),
        
        -- Relationship & Pipeline Data
        internal_relationship_manager = COALESCE($86, internal_relationship_manager),
        last_contact_date = COALESCE($87, last_contact_date),
        pipeline_status = COALESCE($88, pipeline_status),
        recent_news_sentiment = COALESCE($89, recent_news_sentiment),
        
        -- Data Quality & Processing
        data_source = COALESCE($90, data_source),
        data_confidence_score = COALESCE($91, data_confidence_score),
        quarterly_earnings_link = COALESCE($92, quarterly_earnings_link),
        
        -- Processing metadata
        updated_at = NOW(),
        last_updated_timestamp = NOW(),
        llm_used = $93,
        llm_response = $94,
        llm_token_usage = $95
        
      WHERE company_id = $96
    `
    
    // Prepare parameters array
    const params = [
      data.company_industry || null,                          // $1 - industry field
      data.company_type || null,                              // $2
      data.business_model || null,                            // $3
      data.founded_year || null,                              // $4
      data.investment_focus ? this.arrayToPostgresArray(data.investment_focus) : null,  // $5
      data.investment_strategy_mission || null,               // $6
      data.investment_strategy_approach || null,              // $7
      data.website || null,                                   // $8
      data.main_phone || null,                                // $9
      data.secondary_phone || null,                           // $10
      data.main_email || null,                                // $11
      data.secondary_email || null,                           // $12
      data.linkedin || null,                                  // $13
      data.twitter || null,                                   // $14
      data.facebook || null,                                  // $15
      data.instagram || null,                                 // $16
      data.youtube || null,                                   // $17
      data.headquarters_address || null,                      // $18
      data.headquarters_city || null,                         // $19
      data.headquarters_state || null,                        // $20
      data.headquarters_zipcode || null,                      // $21
      data.headquarters_country || null,                      // $22
      data.additional_address || null,                        // $23
      data.additional_city || null,                           // $24
      data.additional_state || null,                          // $25
      data.additional_zipcode || null,                        // $26
      data.additional_country || null,                        // $27
      data.office_locations ? this.arrayToPostgresArray(data.office_locations) : null,  // $28
      data.fund_size || null,                                 // $29
      data.aum || null,                                       // $30
      data.number_of_properties || null,                      // $31
      data.number_of_offices || null,                         // $32
      data.number_of_employees || null,                       // $33
      data.annual_revenue || null,                            // $34
      data.net_income || null,                                // $35
      data.ebitda || null,                                    // $36
      data.profit_margin || null,                             // $37
      data.market_capitalization || null,                     // $38
      data.market_share_percentage || null,                   // $39
      data.balance_sheet_strength || null,                    // $40
      data.funding_sources ? this.arrayToPostgresArray(data.funding_sources) : null,    // $41
      data.recent_capital_raises || null,                     // $42
      data.typical_debt_to_equity_ratio || null,              // $43
      data.development_fee_structure || null,                 // $44
      data.credit_rating || null,                             // $45
      data.dry_powder || null,                                // $46
      data.annual_deployment_target || null,                  // $47
      data.investment_vehicle_type || null,                   // $48
      data.active_fund_name_series || null,                   // $49
      data.fund_size_active_fund || null,                     // $50
      data.fundraising_status || null,                        // $51
      data.lender_type || null,                               // $52
      data.annual_loan_volume || null,                        // $53
      data.lending_origin_balance_sheet_securitization || null, // $54
      data.portfolio_health || null,                          // $55
      data.partnerships ? this.arrayToPostgresArray(data.partnerships) : null,        // $56
      data.key_equity_partners ? this.arrayToPostgresArray(data.key_equity_partners) : null,  // $57
      data.key_debt_partners ? this.arrayToPostgresArray(data.key_debt_partners) : null,  // $58
      data.board_of_directors ? this.arrayToPostgresArray(data.board_of_directors) : null,  // $59
      data.key_executives ? this.arrayToPostgresArray(data.key_executives) : null,      // $60
      data.founder_background || null,                        // $61
      data.market_cycle_positioning || null,                  // $62
      data.urban_vs_suburban_preference || null,              // $63
      data.sustainability_esg_focus || null,                  // $64
      data.technology_proptech_adoption || null,              // $65
      data.adaptive_reuse_experience || null,                 // $66
      data.regulatory_zoning_expertise || null,               // $67
      data.corporate_structure || null,                       // $68
      data.parent_company || null,                            // $69
      data.subsidiaries ? this.arrayToPostgresArray(data.subsidiaries) : null,        // $70
      data.stock_ticker_symbol || null,                       // $71
      data.stock_exchange || null,                            // $72
      data.products_services_description || null,             // $73
      data.target_customer_profile || null,                   // $74
      data.major_competitors ? this.arrayToPostgresArray(data.major_competitors) : null,  // $75
      data.unique_selling_proposition || null,                // $76
      data.industry_awards_recognitions ? this.arrayToPostgresArray(data.industry_awards_recognitions) : null,  // $77
      data.company_history || null,                           // $78
      data.transactions_completed_last_12m || null,           // $79
      data.total_transaction_volume_ytd || null,              // $80
      data.deal_count_ytd || null,                            // $81
      data.average_deal_size || null,                         // $82
      data.portfolio_size_sqft || null,                       // $83
      data.portfolio_asset_count || null,                     // $84
      data.role_in_previous_deal || null,                     // $85
      data.internal_relationship_manager || null,             // $86
      data.last_contact_date || null,                         // $87
      data.pipeline_status || null,                           // $88
      data.recent_news_sentiment || null,                     // $89
      data.data_source || null,                               // $90
      data.data_confidence_score || null,                     // $91
        data.quarterly_earnings_link || null,                   // $92
      'perplexity-sonar',                                     // $93 - LLM used
      llmResponse || null,                                    // $94 - LLM response
      llmUsage ? JSON.stringify(llmUsage) : null,            // $95 - LLM usage
      companyId                                               // $96
    ]
    
    await this.query(sql, params)
    
    this.log('debug', `Stored comprehensive company overview for company ${companyId}`)
  }

  /**
   * Convert JavaScript array to PostgreSQL array literal format
   */
  private arrayToPostgresArray(arr: string[] | null | undefined): string {
    if (!arr || arr.length === 0) {
      return '{}' // Return empty PostgreSQL array instead of null
    }
    
    // Escape quotes and wrap values that contain spaces or special characters
    const escapedValues = arr.map(value => {
      if (typeof value !== 'string') {
        return String(value)
      }
      
      // If value contains spaces, commas, quotes, or special characters, wrap in quotes and escape
      if (value.includes(' ') || value.includes(',') || value.includes('"') || value.includes('\'') || value.includes('{') || value.includes('}')) {
        return `"${value.replace(/"/g, '\\"')}"` 
      }
      
      return value
    })
    
    return `{${escapedValues.join(',')}}`
  }

  /**
   * Clean out null, undefined, and empty values from an object
   */
  private cleanNullValues<T>(obj: T): T {
    const result = { ...obj } as any
    
    for (const key in result) {
      const value = result[key]
      
      // Remove null/undefined values
      if (value === null || value === undefined) {
        delete result[key]
        continue
      }
      
      // Handle arrays - remove empty arrays and clean arrays of objects
      if (Array.isArray(value)) {
        if (value.length === 0) {
          delete result[key]
        } else if (typeof value[0] === 'object') {
          result[key] = value.map(item => this.cleanNullValues(item))
            .filter(item => Object.keys(item).length > 0)
          
          if (result[key].length === 0) {
            delete result[key]
          }
        }
      }
      
      // Handle objects recursively
      else if (typeof value === 'object') {
        result[key] = this.cleanNullValues(value)
        if (Object.keys(result[key]).length === 0) {
          delete result[key]
        }
      }
      
      // Handle empty strings
      else if (typeof value === 'string' && value.trim() === '') {
        delete result[key]
      }
    }
    
    return result as T
  }
}
