// Test the new conflicts field approach
async function testConflictResolution() {
  console.log("Testing new conflicts field approach...");
  
  // Test data with conflicts field
  const testData = {
    deal_name: "Test Deal",
    total_project_cost: 25000000,
    hold_period: 5,
    yield_on_cost: 0.085,
    conflicts: {
      total_project_cost: {
        chosen_value: 25000000,
        source_file: "File A - Official Pro Forma",
        confidence: "High",
        alternatives: [
          {
            value: 28000000,
            file: "File B - Preliminary Budget",
            context: "Early estimate before detailed analysis"
          },
          {
            value: 24000000,
            file: "File C - Marketing Summary",
            context: "Rounded for marketing purposes"
          }
        ],
        resolution_notes: "File A contains official pro forma with detailed cost breakdown, most reliable source"
      },
      hold_period: {
        chosen_value: 5,
        source_file: "File A - Investment Memo",
        confidence: "Medium",
        alternatives: [
          {
            value: 7,
            file: "File B - Preliminary Summary",
            context: "Conservative estimate"
          }
        ],
        resolution_notes: "File A provides more detailed analysis, File B was preliminary"
      }
    }
  };

  console.log("Test data with conflicts:", JSON.stringify(testData, null, 2));
  
  // Simulate how the processor would handle this
  const extraFields = {
    ...Object.keys(testData)
      .filter(key => !['deal_name', 'total_project_cost', 'hold_period', 'yield_on_cost', 'conflicts'].includes(key))
      .reduce((obj, key) => {
        obj[key] = testData[key];
        return obj;
      }, {} as any),
    // Add conflicts field if present
    ...(testData.conflicts && { conflicts: testData.conflicts })
  };
  
  console.log("Extra fields (including conflicts):", JSON.stringify(extraFields, null, 2));
  
  // Test conflict detection
  const hasConflict = (fieldName: string): boolean => {
    return testData.conflicts && testData.conflicts[fieldName] !== undefined;
  };
  
  console.log("Conflict detection:");
  console.log("  total_project_cost has conflict:", hasConflict('total_project_cost'));
  console.log("  hold_period has conflict:", hasConflict('hold_period'));
  console.log("  yield_on_cost has conflict:", hasConflict('yield_on_cost'));
  
  console.log("Test completed!");
}

testConflictResolution().catch(console.error);
