# InvestmentCriteriaProcessor Test Suite

## Overview

This comprehensive test suite validates the `InvestmentCriteriaProcessor` class, which handles the complex parsing and processing of investment criteria data from CSV uploads. The tests use real data patterns from the Master Sheet CSV file to ensure robust coverage of all edge cases.

## Test Coverage

### 1. Capital Position Extraction (`extractCapitalPositions`)
- ✅ Single capital position: `"Senior Debt"`
- ✅ Multiple positions: `"Mezzanine/Preferred Equity/Senior Debt"`
- ✅ Complex positions: `"CO-GP/General Partner (GP)/Joint Venture (JV)/Limited Partner (LP)/Mezzanine/Preferred Equity/Senior Debt"`
- ✅ Missing capital position (error handling)

### 2. Deal Size Extraction (`extractDealSizes`)
- ✅ A10 Capital format: `"$7M - $100M+ Bridge/$7M - $100M+ Note Purchase/$10M - $100M Stabilized"`
- ✅ 3650 Capital format: `"$25M+ Senior Debt/$10M+ Mezzanine"`
- ✅ A&S Capital format: `"$250K - $10M Construction/$0 - $2.5M Long Term"`

### 3. Location Data Extraction (`extractEnhancedLocationData`)
- ✅ Focus areas: `"Nationwide (Focus: California/Florida/New Jersey/New York)"`
- ✅ All US states: Complete state list parsing
- ✅ Specific regions: `"New York Metropolitan Area"`
- ✅ International: `"Canada/Mexico/United States"`
- ✅ Multiple geographic levels: Country/Region/State/City

### 4. Property Data Extraction (`extractEnhancedPropertyData`)
- ✅ A10 Capital properties: Healthcare, Hospitality, Industrial, etc.
- ✅ 3650 Capital all types: Including SFR and Special-Use
- ✅ Property subtypes: Medical Office, Self-Storage, Student Housing
- ✅ Strategies: Opportunistic, Value-Add, Core, Core Plus

### 5. Loan Data Extraction (`extractEnhancedLoanData`)
- ✅ A10 Capital comprehensive loan data
- ✅ A&S Capital construction loans
- ✅ 3650 Capital complex loan structures
- ✅ All loan fields: Terms, rates, LTV, LTC, fees, DSCR, closing time, recourse

### 6. Loan-Type-Specific Data Extraction (`extractLoanTypeSpecificData`)
- ✅ **Bridge-specific data**: `"70% Bridge"` from `"70% Bridge/65% Stabilized"`
- ✅ **Stabilized-specific data**: `"65% Stabilized"` from `"70% Bridge/65% Stabilized"`
- ✅ **All interest rate types**: SOFR, WSJ, Prime, LIBOR, 5YT, 10YT with loan-type separation
- ✅ **Complex DSCR**: `"1.25x Bridge/1.20x Stabilized/1.35x Construction"`
- ✅ **Loan terms**: `"3 Years Bridge & Note Purchase (1+1 Years Extension)/1 - 20 Years Stabilized"`
- ✅ **Fees**: `"1% Bridge/.5% Stabilized"`

### 7. Complete Row Processing (`processCSVRow`)
- ✅ **A10 Capital Senior Debt**: Creates separate records for Bridge, Note Purchase, Stabilized
- ✅ **3650 Capital Multi-Position**: Creates records for Mezzanine, Preferred Equity, Senior Debt
- ✅ **7 Acre Investments Preferred Equity**: Single investment criteria record
- ✅ **A&S Capital Construction**: Specialized construction loan processing
- ✅ **Abco Capital Complex**: Multiple capital positions with all loan types

### 8. Edge Cases and Error Handling
- ✅ Empty CSV rows
- ✅ Malformed deal sizes
- ✅ Special characters in loan data
- ✅ Percentage value conversion (15.5% → 0.155)
- ✅ Missing capital position validation
- ✅ Invalid numeric values

## Real Data Test Cases

### A10 Capital Senior Debt
```csv
Capital Position: Senior Debt
Deal Size: $7M - $100M+ Bridge/$7M - $100M+ Note Purchase/$10M - $100M Stabilized
Loan Terms: 3 Years Bridge & Note Purchase (1+1 Years Extension)/1 - 20 Years Stabilized
LTV: 70% Bridge/65% Stabilized
Fees: 1% Bridge/.5% Stabilized
```
**Expected Result**: 3 separate investment criteria records for Bridge, Note Purchase, and Stabilized

### 3650 Capital Multi-Position
```csv
Capital Position: Mezzanine/Preferred Equity/Senior Debt
Deal Size: $25M+ Senior Debt/$10M+ Mezzanine
Interest Rate: 8.5%+ Senior Debt/12%+ Mezzanine
LTV: 85% Bridge/75% Permanent
```
**Expected Result**: Multiple records for each capital position with appropriate loan-type separation

### A&S Capital Construction
```csv
Capital Position: Senior Debt
Deal Size: $250K - $10M Construction/$0 - $2.5M Long Term
Loan Program: C-Pace
Interest Rate: 11%
LTV: 65%
LTC: 80%
```
**Expected Result**: Construction and Long Term loan records with specialized data

## Test Execution

### Prerequisites
Install test dependencies:
```bash
pnpm install
```

### Running Tests

#### Basic Test Run
```bash
pnpm test
```

#### Watch Mode (recommended for development)
```bash
pnpm test:watch
```

#### Coverage Report
```bash
pnpm test:coverage
```

#### Using Custom Test Runner
```bash
# Basic run
node scripts/run-tests.js

# Watch mode
node scripts/run-tests.js --watch

# Coverage report
node scripts/run-tests.js --coverage

# Specific test file
node scripts/run-tests.js --specific=InvestmentCriteriaProcessor

# Verbose output
node scripts/run-tests.js --verbose
```

## Test Structure

```
src/lib/processors/__tests__/
├── InvestmentCriteriaProcessor.test.ts  # Main test file (805 lines)
├── README.md                            # This documentation
└── __mocks__/                           # Mock files (if needed)
```

## Key Features Tested

### 1. Loan-Type-Specific Data Separation
The most critical feature - ensuring that data like `"70% Bridge/65% Stabilized"` correctly creates:
- Bridge record with 70% LTV
- Stabilized record with 65% LTV

### 2. Multiple Capital Position Handling
Complex capital positions like `"Mezzanine/Preferred Equity/Senior Debt"` create separate investment criteria records for each position.

### 3. Enhanced Business Rules
- Rule 1: Capital Position is required
- Senior Debt gets loan-type separation based on central mapping
- Other positions get standard processing

### 4. Data Type Conversion
- Percentage values (15.5% → 0.155)
- Loan terms (3 Years → 36 months)
- DSCR parsing (1.25x → 1.25)
- Closing time (3-4 weeks → 3.5)

### 5. Geographic Focus Areas
Intelligent parsing of `"Nationwide (Focus: California/Florida/New Jersey/New York)"` into:
- Region: ["Nationwide"]
- Focus: ["California", "Florida", "New Jersey", "New York"]

## Debugging Tests

### Enable Console Output
To see debug logs during tests, modify `src/setupTests.ts`:
```typescript
// Comment out these lines to see console output
// console.error = jest.fn();
// console.warn = jest.fn();
// console.log = jest.fn();
```

### Run Single Test
```bash
pnpm test -- --testNamePattern="should extract Bridge-specific data"
```

### Debug Mode
```bash
node --inspect-brk node_modules/.bin/jest --runInBand
```

## Coverage Goals

- **Functions**: 95%+ coverage
- **Lines**: 90%+ coverage
- **Branches**: 85%+ coverage
- **Statements**: 90%+ coverage

## Adding New Tests

When adding new test cases:

1. **Use real CSV data** from actual uploads
2. **Test both success and failure** scenarios
3. **Validate all output fields** in the generated records
4. **Test edge cases** like empty values, special characters
5. **Document the expected behavior** in comments

## Continuous Integration

These tests should be run:
- On every commit (pre-commit hook)
- In CI/CD pipeline before deployment
- Before merging pull requests
- When modifying InvestmentCriteriaProcessor logic

## Performance Considerations

- Tests run in **Node.js environment** (not browser)
- **Database operations are mocked** for speed
- **API calls are mocked** to avoid external dependencies
- Individual tests should complete in **< 1 second**
- Full suite should complete in **< 30 seconds** 