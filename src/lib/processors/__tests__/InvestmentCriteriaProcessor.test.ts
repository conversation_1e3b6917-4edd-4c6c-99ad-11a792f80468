import { InvestmentCriteriaProcessor, ProcessingInput, ProcessingResult } from '../InvestmentCriteriaProcessor';

// Mock the capital position mapping API
global.fetch = jest.fn();

const mockCapitalPositionMappings = new Map([
  ['Senior Debt', ['Bridge', 'Stabilized', 'Permanent', 'Construction', 'Acquisition', 'Refinance', 'Note Purchase']]
]);

beforeEach(() => {
  (fetch as jest.Mock).mockResolvedValue({
    ok: true,
    json: async () => ({
      success: true,
      mappings: Object.fromEntries(mockCapitalPositionMappings)
    })
  });
});

describe('InvestmentCriteriaProcessor', () => {
  
  describe('extractCapitalPositions', () => {
    it('should extract single capital position', () => {
      const csvRow = { 'Capital Position': 'Senior Debt' };
      const headerMappings = { 'Capital Position': 'capital_position' };
      
      const result = InvestmentCriteriaProcessor['extractCapitalPositions'](csvRow, headerMappings);
      
      expect(result).toEqual(['Senior Debt']);
    });

    it('should extract multiple capital positions with forward slashes', () => {
      const csvRow = { 'Capital Position': 'Mezzanine/Preferred Equity/Senior Debt' };
      const headerMappings = { 'Capital Position': 'capital_position' };
      
      const result = InvestmentCriteriaProcessor['extractCapitalPositions'](csvRow, headerMappings);
      
      expect(result).toEqual(['Mezzanine', 'Preferred Equity', 'Senior Debt']);
    });

    it('should extract complex capital positions', () => {
      const csvRow = { 'Capital Position': 'CO-GP/General Partner (GP)/Joint Venture (JV)/Limited Partner (LP)/Mezzanine/Preferred Equity/Senior Debt' };
      const headerMappings = { 'Capital Position': 'capital_position' };
      
      const result = InvestmentCriteriaProcessor['extractCapitalPositions'](csvRow, headerMappings);
      
      expect(result).toEqual([
        'CO-GP', 'General Partner (GP)', 'Joint Venture (JV)', 
        'Limited Partner (LP)', 'Mezzanine', 'Preferred Equity', 'Senior Debt'
      ]);
    });

    it('should return empty array when no capital position found', () => {
      const csvRow = { 'Some Field': 'Some Value' };
      const headerMappings = { 'Some Field': 'some_field' };
      
      const result = InvestmentCriteriaProcessor['extractCapitalPositions'](csvRow, headerMappings);
      
      expect(result).toEqual([]);
    });
  });

  describe('extractDealSizes', () => {
    it('should extract loan-type-specific deal sizes from A10 Capital data', () => {
      const csvRow = { 
        '(Investment Criteria) Deal Size': '$7M - $100M+ Bridge/$7M - $100M+ Note Purchase/$10M - $100M Stabilized' 
      };
      const headerMappings = { '(Investment Criteria) Deal Size': 'deal_size' };
      
      const result = InvestmentCriteriaProcessor['extractDealSizes'](csvRow, headerMappings);
      
      expect(result).toEqual([
        '$7M - $100M+ Bridge/$7M - $100M+ Note Purchase/$10M - $100M Stabilized'
      ]);
    });

    it('should extract simple deal size ranges', () => {
      const csvRow = { 
        '(Investment Criteria) Deal Size': '$25M+ Senior Debt/$10M+ Mezzanine' 
      };
      const headerMappings = { '(Investment Criteria) Deal Size': 'deal_size' };
      
      const result = InvestmentCriteriaProcessor['extractDealSizes'](csvRow, headerMappings);
      
      expect(result).toEqual(['$25M+ Senior Debt/$10M+ Mezzanine']);
    });

    it('should extract construction and long-term deal sizes', () => {
      const csvRow = { 
        '(Investment Criteria) Deal Size': '$250K - $10M Construction/$0 - $2.5M Long Term' 
      };
      const headerMappings = { '(Investment Criteria) Deal Size': 'deal_size' };
      
      const result = InvestmentCriteriaProcessor['extractDealSizes'](csvRow, headerMappings);
      
      expect(result).toEqual(['$250K - $10M Construction/$0 - $2.5M Long Term']);
    });
  });

  describe('extractEnhancedLocationData', () => {
    it('should extract location data with focus areas', () => {
      const csvRow = {
        '(Investment Criteria) Country': 'United States',
        '(Investment Criteria) Geographic Region': 'Nationwide (Focus: California/Florida/New Jersey/New York)',
        '(Investment Criteria) State': 'Alabama/Alaska/Arizona/Arkansas/California/Colorado/Connecticut/Delaware/Florida/Georgia/Hawaii/Idaho/Illinois/Indiana/Iowa/Kansas/Kentucky/Louisiana/Maine/Maryland/Massachusetts/Michigan/Minnesota/Mississippi/Missouri/Montana/Nebraska/Nevada/New Hampshire/New Jersey/New Mexico/New York/North Carolina/North Dakota/Ohio/Oklahoma/Oregon/Pennsylvania/Rhode Island/South Carolina/South Dakota/Tennessee/Texas/Utah/Vermont/Virginia/Washington/West Virginia/Wisconsin/Wyoming',
        '(Investment Criteria) City': ''
      };
      const headerMappings = {
        '(Investment Criteria) Country': 'country',
        '(Investment Criteria) Geographic Region': 'region',
        '(Investment Criteria) State': 'state',
        '(Investment Criteria) City': 'city'
      };
      
      const result = InvestmentCriteriaProcessor['extractEnhancedLocationData'](csvRow, headerMappings);
      
      expect(result.country).toEqual(['United States']);
      expect(result.region).toEqual(['Nationwide']);
      expect(result.focus).toEqual(['California', 'Florida', 'New Jersey', 'New York']);
      expect(result.state).toContain('California');
      expect(result.state).toContain('Florida');
      expect(result.state).toContain('Wyoming');
    });

    it('should extract specific regional focus', () => {
      const csvRow = {
        '(Investment Criteria) Geographic Region': 'New York Metropolitan Area',
        '(Investment Criteria) State': 'Florida/Kansas',
        '(Investment Criteria) City': 'Miami'
      };
      const headerMappings = {
        '(Investment Criteria) Geographic Region': 'region',
        '(Investment Criteria) State': 'state',
        '(Investment Criteria) City': 'city'
      };
      
      const result = InvestmentCriteriaProcessor['extractEnhancedLocationData'](csvRow, headerMappings);
      
      expect(result.region).toEqual(['New York Metropolitan Area']);
      expect(result.state).toEqual(['Florida', 'Kansas']);
      expect(result.city).toEqual(['Miami']);
    });

    it('should extract international locations', () => {
      const csvRow = {
        '(Investment Criteria) Country': 'Canada/Mexico/United States',
        '(Investment Criteria) Geographic Region': 'Asia/Europe/North America'
      };
      const headerMappings = {
        '(Investment Criteria) Country': 'country',
        '(Investment Criteria) Geographic Region': 'region'
      };
      
      const result = InvestmentCriteriaProcessor['extractEnhancedLocationData'](csvRow, headerMappings);
      
      expect(result.country).toEqual(['Canada', 'Mexico', 'United States']);
      expect(result.region).toEqual(['Asia', 'Europe', 'North America']);
    });
  });

  describe('extractEnhancedPropertyData', () => {
    it('should extract comprehensive property data from A10 Capital', () => {
      const csvRow = {
        '(Investment Criteria) Property Type': 'Healthcare/Hospitality/Industrial/Land/Mixed-Use/Multi-Family/Retail',
        '(Investment Criteria) Property Subproperty Type': 'Medical Office/Self-Storage/Student Housing',
        '(Investment Criteria) Strategies': 'Opportunistic/Value-Add'
      };
      const headerMappings = {
        '(Investment Criteria) Property Type': 'property_types',
        '(Investment Criteria) Property Subproperty Type': 'property_sub_types',
        '(Investment Criteria) Strategies': 'strategies'
      };
      
      const result = InvestmentCriteriaProcessor['extractEnhancedPropertyData'](csvRow, headerMappings);
      
      expect(result.propertyTypes).toEqual([
        'Healthcare', 'Hospitality', 'Industrial', 'Land', 'Mixed-Use', 'Multi-Family', 'Retail'
      ]);
      expect(result.propertySubTypes).toEqual([
        'Medical Office', 'Self-Storage', 'Student Housing'
      ]);
      expect(result.strategies).toEqual(['Opportunistic', 'Value-Add']);
    });

    it('should extract all property types from 3650 Capital', () => {
      const csvRow = {
        '(Investment Criteria) Property Type': 'Assemblage/Healthcare/Hospitality/Industrial/Land/Mixed-Use/Multi-Family/Office/Retail/Single-Family Residence (Sfr)/Special-Use',
        '(Investment Criteria) Property Subproperty Type': '',
        '(Investment Criteria) Strategies': 'Opportunistic/Value-Add'
      };
      const headerMappings = {
        '(Investment Criteria) Property Type': 'property_types',
        '(Investment Criteria) Property Subproperty Type': 'property_sub_types',
        '(Investment Criteria) Strategies': 'strategies'
      };
      
      const result = InvestmentCriteriaProcessor['extractEnhancedPropertyData'](csvRow, headerMappings);
      
      expect(result.propertyTypes).toContain('Assemblage');
      expect(result.propertyTypes).toContain('Single-Family Residence (Sfr)');
      expect(result.propertyTypes).toContain('Special-Use');
      expect(result.propertySubTypes).toEqual([]);
    });

    it('should handle specialized property subtypes', () => {
      const csvRow = {
        '(Investment Criteria) Property Type': 'Hospitality/Industrial/Mixed-Use/Multi-Family/Office/Retail',
        '(Investment Criteria) Property Subproperty Type': 'Condo/Hotel/Logistics/Shopping Center',
        '(Investment Criteria) Strategies': 'Core/Core Plus'
      };
      const headerMappings = {
        '(Investment Criteria) Property Type': 'property_types',
        '(Investment Criteria) Property Subproperty Type': 'property_sub_types',
        '(Investment Criteria) Strategies': 'strategies'
      };
      
      const result = InvestmentCriteriaProcessor['extractEnhancedPropertyData'](csvRow, headerMappings);
      
      expect(result.propertySubTypes).toEqual(['Condo', 'Hotel', 'Logistics', 'Shopping Center']);
      expect(result.strategies).toEqual(['Core', 'Core Plus']);
    });
  });

  describe('extractEnhancedLoanData', () => {
    it('should extract comprehensive loan data from A10 Capital', () => {
      const csvRow = {
        '(Investment Criteria) Loan Program': '',
        '(Investment Criteria) Structured Loan Tranche': 'Senior/Whole Loan',
        '(Investment Criteria) Loan Type': 'Acquisition/Bridge/Note Purchase',
        '(Investment Criteria) Loan Type.1': 'Permanent/Refinance',
        '(Investment Criteria) Loan Term (Years)': '3 Years Bridge & Note Purchase (1+1 Years Extension)/1 - 20 Years Stabilized',
        '(Investment Criteria) Loan To Value': '70% Bridge/65% Stabilized',
        '(Investment Criteria) Loan To Cost': '',
        '(Investment Criteria) Loan Origination Fee (%)': '1% Bridge/.5% Stabilized',
        '(Investment Criteria) Loan Exit Fee (%)': '1% Bridge',
        '(Investment Criteria) Recourse Loan': 'Non Recourse',
        '(Investment Criteria) Loan DSCR': '',
        '(Investment Criteria) Closing Time': ''
      };
      const headerMappings = {
        '(Investment Criteria) Loan Program': 'loan_program',
        '(Investment Criteria) Structured Loan Tranche': 'structured_loan_tranche',
        '(Investment Criteria) Loan Type': 'loan_type',
        '(Investment Criteria) Loan Type.1': 'loan_type_2',
        '(Investment Criteria) Loan Term (Years)': 'loan_term',
        '(Investment Criteria) Loan To Value': 'loan_to_value',
        '(Investment Criteria) Loan To Cost': 'loan_to_cost',
        '(Investment Criteria) Loan Origination Fee (%)': 'loan_origination_fee',
        '(Investment Criteria) Loan Exit Fee (%)': 'loan_exit_fee',
        '(Investment Criteria) Recourse Loan': 'recourse_loan',
        '(Investment Criteria) Loan DSCR': 'loan_dscr',
        '(Investment Criteria) Closing Time': 'closing_time'
      };
      
      const result = InvestmentCriteriaProcessor['extractEnhancedLoanData'](csvRow, headerMappings);
      
      expect(result.structuredLoanTranche).toEqual(['Senior', 'Whole Loan']);
      expect(result.loanType).toEqual(['Acquisition', 'Bridge', 'Note Purchase', 'Permanent', 'Refinance', 'Stabilized']);
      expect(result.recourseLoan).toBe('non recourse');
    });

    it('should extract A&S Capital construction loan data', () => {
      const csvRow = {
        '(Investment Criteria) Loan Program': 'C-Pace',
        '(Investment Criteria) Structured Loan Tranche': 'Whole Loan',
        '(Investment Criteria) Loan Type': 'Acquisition/Bridge/Construction/Rehab',
        '(Investment Criteria) Loan Term (Years)': '1 - 2 Years',
        '(Investment Criteria) Loan Interest Rate': '11%',
        '(Investment Criteria) Loan To Value': '65%',
        '(Investment Criteria) Loan To Cost': '80%',
        '(Investment Criteria) Loan Origination Fee (%)': '2%',
        '(Investment Criteria) Closing Time': '3-4 Weeks'
      };
      const headerMappings = {
        '(Investment Criteria) Loan Program': 'loan_program',
        '(Investment Criteria) Structured Loan Tranche': 'structured_loan_tranche',
        '(Investment Criteria) Loan Type': 'loan_type',
        '(Investment Criteria) Loan Term (Years)': 'loan_term',
        '(Investment Criteria) Loan Interest Rate': 'interest_rate',
        '(Investment Criteria) Loan To Value': 'loan_to_value',
        '(Investment Criteria) Loan To Cost': 'loan_to_cost',
        '(Investment Criteria) Loan Origination Fee (%)': 'loan_origination_fee',
        '(Investment Criteria) Closing Time': 'closing_time'
      };
      
      const result = InvestmentCriteriaProcessor['extractEnhancedLoanData'](csvRow, headerMappings);
      
      expect(result.loanProgram).toEqual(['C-Pace']);
      expect(result.loanType).toEqual(['Acquisition', 'Bridge', 'Construction', 'Rehab']);
      expect(result.interestRates?.base).toBe(11);
      expect(result.loanToValue?.min).toBe(65);
      expect(result.loanToCost?.min).toBe(80);
      expect(result.closingTime).toBe(3.5); // 3-4 weeks average
    });

    it('should extract 3650 Capital complex loan data', () => {
      const csvRow = {
        '(Investment Criteria) Structured Loan Tranche': 'Senior Subordinated/Whole Loan',
        '(Investment Criteria) Loan Type': 'Bridge/Construction/Note-On-Note',
        '(Investment Criteria) Loan Type.1': 'Permanent',
        '(Investment Criteria) Loan Term (Years)': '1 - 5 Years Senior Debt/2 - 3 Years Transitional/10 Years Permanent/3 - 7 Years Special Situations',
        '(Investment Criteria) Loan Interest Rate': '8.5%+ Senior Debt/12%+ Mezzanine',
        '(Investment Criteria) Loan To Value': '85% Bridge/75% Permanent',
        '(Investment Criteria) Loan Origination Fee (%)': 'None',
        '(Investment Criteria) Loan DSCR': '1.25x (1.2x for multifamily)'
      };
      const headerMappings = {
        '(Investment Criteria) Structured Loan Tranche': 'structured_loan_tranche',
        '(Investment Criteria) Loan Type': 'loan_type',
        '(Investment Criteria) Loan Type.1': 'loan_type_2',
        '(Investment Criteria) Loan Term (Years)': 'loan_term',
        '(Investment Criteria) Loan Interest Rate': 'interest_rate',
        '(Investment Criteria) Loan To Value': 'loan_to_value',
        '(Investment Criteria) Loan Origination Fee (%)': 'loan_origination_fee',
        '(Investment Criteria) Loan DSCR': 'loan_dscr'
      };
      
      const result = InvestmentCriteriaProcessor['extractEnhancedLoanData'](csvRow, headerMappings);
      
      expect(result.structuredLoanTranche).toEqual(['Senior Subordinated', 'Whole Loan']);
      expect(result.loanType).toEqual(['Bridge', 'Construction', 'Note-On-Note', 'Permanent']);
      expect(result.dscr?.min).toBe(1.25);
    });
  });

  describe('extractLoanTypeSpecificData', () => {
    it('should extract Bridge-specific data from A10 Capital', () => {
      const csvRow = {
        '(Investment Criteria) Deal Size': '$7M - $100M+ Bridge/$7M - $100M+ Note Purchase/$10M - $100M Stabilized',
        '(Investment Criteria) Loan Term (Years)': '3 Years Bridge & Note Purchase (1+1 Years Extension)/1 - 20 Years Stabilized',
        '(Investment Criteria) Loan To Value': '70% Bridge/65% Stabilized',
        '(Investment Criteria) Loan Origination Fee (%)': '1% Bridge/.5% Stabilized',
        '(Investment Criteria) Loan Exit Fee (%)': '1% Bridge',
        '(Investment Criteria) Recourse Loan': 'Non Recourse'
      };
      const headerMappings = {
        '(Investment Criteria) Deal Size': 'deal_size',
        '(Investment Criteria) Loan Term (Years)': 'loan_term',
        '(Investment Criteria) Loan To Value': 'loan_to_value',
        '(Investment Criteria) Loan Origination Fee (%)': 'loan_origination_fee',
        '(Investment Criteria) Loan Exit Fee (%)': 'loan_exit_fee',
        '(Investment Criteria) Recourse Loan': 'recourse_loan'
      };
      const allDealSizes = ['$7M - $100M+ Bridge/$7M - $100M+ Note Purchase/$10M - $100M Stabilized'];
      
      const result = InvestmentCriteriaProcessor['extractLoanTypeSpecificData']('Bridge', csvRow, headerMappings, allDealSizes);
      
      expect(result.dealSizes).toEqual(allDealSizes);
      expect(result.terms.specific).toContain('3 Years Bridge');
      expect(result.loanToValue.min).toBe(70);
      expect(result.fees.origination.min).toBe(1);
      expect(result.fees.exit.min).toBe(1);
      expect(result.recourseLoan).toBe('non recourse');
    });

    it('should extract Stabilized-specific data from A10 Capital', () => {
      const csvRow = {
        '(Investment Criteria) Deal Size': '$7M - $100M+ Bridge/$7M - $100M+ Note Purchase/$10M - $100M Stabilized',
        '(Investment Criteria) Loan Term (Years)': '3 Years Bridge & Note Purchase (1+1 Years Extension)/1 - 20 Years Stabilized',
        '(Investment Criteria) Loan To Value': '70% Bridge/65% Stabilized',
        '(Investment Criteria) Loan Origination Fee (%)': '1% Bridge/.5% Stabilized'
      };
      const headerMappings = {
        '(Investment Criteria) Deal Size': 'deal_size',
        '(Investment Criteria) Loan Term (Years)': 'loan_term',
        '(Investment Criteria) Loan To Value': 'loan_to_value',
        '(Investment Criteria) Loan Origination Fee (%)': 'loan_origination_fee'
      };
      const allDealSizes = ['$7M - $100M+ Bridge/$7M - $100M+ Note Purchase/$10M - $100M Stabilized'];
      
      const result = InvestmentCriteriaProcessor['extractLoanTypeSpecificData']('Stabilized', csvRow, headerMappings, allDealSizes);
      
      expect(result.terms.specific).toContain('1 - 20 Years Stabilized');
      expect(result.loanToValue.min).toBe(65);
      expect(result.fees.origination.min).toBe(0.5);
    });

    it('should extract 3650 Capital loan-type-specific data', () => {
      const csvRow = {
        '(Investment Criteria) Loan Interest Rate': '8.5%+ Senior Debt/12%+ Mezzanine',
        '(Investment Criteria) Loan To Value': '85% Bridge/75% Permanent',
        '(Investment Criteria) Loan Term (Years)': '1 - 5 Years Senior Debt/2 - 3 Years Transitional/10 Years Permanent/3 - 7 Years Special Situations'
      };
      const headerMappings = {
        '(Investment Criteria) Loan Interest Rate': 'interest_rate',
        '(Investment Criteria) Loan To Value': 'loan_to_value',
        '(Investment Criteria) Loan Term (Years)': 'loan_term'
      };
      const allDealSizes = ['$25M+ Senior Debt/$10M+ Mezzanine'];
      
      const bridgeResult = InvestmentCriteriaProcessor['extractLoanTypeSpecificData']('Bridge', csvRow, headerMappings, allDealSizes);
      const permanentResult = InvestmentCriteriaProcessor['extractLoanTypeSpecificData']('Permanent', csvRow, headerMappings, allDealSizes);
      
      expect(bridgeResult.loanToValue.min).toBe(85);
      expect(permanentResult.loanToValue.min).toBe(75);
      expect(permanentResult.terms.specific).toContain('10 Years Permanent');
    });

    it('should handle all interest rate types with loan-type-specific values', () => {
      const csvRow = {
        'Loan Interest Rate Based Off SOFR': '5.5% Bridge/4.8% Stabilized',
        'Loan Interest Rate Based Off WSJ': '6.2% Bridge/5.5% Stabilized',
        'Loan Interest Rate Based Off Prime': '7.0% Bridge/6.5% Stabilized',
        'Loan Interest Rate Based off Libor': '5.8% Bridge/5.3% Stabilized',
        'Loan Interest Rate Based off 5YT': '5.2% Bridge/4.9% Stabilized',
        'Loan Interest Rate Based off 10YT': '5.5% Bridge/5.2% Stabilized'
      };
      const headerMappings = {
        'Loan Interest Rate Based Off SOFR': 'sofr_rate',
        'Loan Interest Rate Based Off WSJ': 'wsj_rate',
        'Loan Interest Rate Based Off Prime': 'prime_rate',
        'Loan Interest Rate Based off Libor': 'libor_rate',
        'Loan Interest Rate Based off 5YT': '5yt_rate',
        'Loan Interest Rate Based off 10YT': '10yt_rate'
      };
      const allDealSizes = ['$10M - $50M'];
      
      const bridgeResult = InvestmentCriteriaProcessor['extractLoanTypeSpecificData']('Bridge', csvRow, headerMappings, allDealSizes);
      const stabilizedResult = InvestmentCriteriaProcessor['extractLoanTypeSpecificData']('Stabilized', csvRow, headerMappings, allDealSizes);
      
      expect(bridgeResult.interestRates.sofr).toBe(5.5);
      expect(bridgeResult.interestRates.wsj).toBe(6.2);
      expect(bridgeResult.interestRates.prime).toBe(7.0);
      
      expect(stabilizedResult.interestRates.sofr).toBe(4.8);
      expect(stabilizedResult.interestRates.wsj).toBe(5.5);
      expect(stabilizedResult.interestRates.prime).toBe(6.5);
    });

    it('should handle complex DSCR with loan-type-specific values', () => {
      const csvRow = {
        '(Investment Criteria) Loan DSCR': '1.25x Bridge/1.20x Stabilized/1.35x Construction'
      };
      const headerMappings = {
        '(Investment Criteria) Loan DSCR': 'loan_dscr'
      };
      const allDealSizes = ['$5M - $50M'];
      
      const bridgeResult = InvestmentCriteriaProcessor['extractLoanTypeSpecificData']('Bridge', csvRow, headerMappings, allDealSizes);
      const stabilizedResult = InvestmentCriteriaProcessor['extractLoanTypeSpecificData']('Stabilized', csvRow, headerMappings, allDealSizes);
      const constructionResult = InvestmentCriteriaProcessor['extractLoanTypeSpecificData']('Construction', csvRow, headerMappings, allDealSizes);
      
      expect(bridgeResult.dscr.min).toBe(1.25);
      expect(stabilizedResult.dscr.min).toBe(1.20);
      expect(constructionResult.dscr.min).toBe(1.35);
    });
  });

  describe('processCSVRow - Complete Integration Tests', () => {
    it('should process A10 Capital Senior Debt row with loan-type separation', async () => {
      const csvRow = {
        'Capital Position': 'Senior Debt',
        'Email': '<EMAIL>',
        'First Name': 'Alex',
        'Last Name': 'Biagioli',
        'Company': 'A10 Capital',
        '(Investment Criteria) Country': 'United States',
        '(Investment Criteria) Geographic Region': 'Nationwide (Focus: California/Florida/New Jersey/New York)',
        '(Investment Criteria) Deal Size': '$7M - $100M+ Bridge/$7M - $100M+ Note Purchase/$10M - $100M Stabilized',
        '(Investment Criteria) Property Type': 'Healthcare/Hospitality/Industrial/Land/Mixed-Use/Multi-Family/Retail',
        '(Investment Criteria) Loan Type': 'Acquisition/Bridge/Note Purchase',
        '(Investment Criteria) Loan Type.1': 'Permanent/Refinance',
        '(Investment Criteria) Loan Term (Years)': '3 Years Bridge & Note Purchase (1+1 Years Extension)/1 - 20 Years Stabilized',
        '(Investment Criteria) Loan To Value': '70% Bridge/65% Stabilized',
        '(Investment Criteria) Loan Origination Fee (%)': '1% Bridge/.5% Stabilized',
        '(Investment Criteria) Recourse Loan': 'Non Recourse'
      };
      
      const headerMappings = {
        'Capital Position': 'capital_position',
        'Email': 'email',
        'First Name': 'first_name',
        'Last Name': 'last_name',
        'Company': 'company_name',
        '(Investment Criteria) Country': 'country',
        '(Investment Criteria) Geographic Region': 'region',
        '(Investment Criteria) Deal Size': 'deal_size',
        '(Investment Criteria) Property Type': 'property_types',
        '(Investment Criteria) Loan Type': 'loan_type',
        '(Investment Criteria) Loan Type.1': 'loan_type_2',
        '(Investment Criteria) Loan Term (Years)': 'loan_term',
        '(Investment Criteria) Loan To Value': 'loan_to_value',
        '(Investment Criteria) Loan Origination Fee (%)': 'loan_origination_fee',
        '(Investment Criteria) Recourse Loan': 'recourse_loan'
      };

      const input: ProcessingInput = { csvRow, headerMappings };
      const result = await InvestmentCriteriaProcessor.processCSVRow(input);

      expect(result.errors).toEqual([]);
      expect(result.records.length).toBeGreaterThan(1); // Should create multiple records for different loan types
      
      // Check for Bridge-specific record
      const bridgeRecord = result.records.find(r => 
        r.loanDetails?.loanType?.includes('Bridge')
      );
      expect(bridgeRecord).toBeDefined();
      expect(bridgeRecord?.loanDetails?.loanToValue?.min).toBe(70);
      expect(bridgeRecord?.loanDetails?.fees?.originationMin).toBe(1);

      // Check for Stabilized-specific record
      const stabilizedRecord = result.records.find(r => 
        r.loanDetails?.loanType?.includes('Stabilized')
      );
      expect(stabilizedRecord).toBeDefined();
      expect(stabilizedRecord?.loanDetails?.loanToValue?.min).toBe(65);
      expect(stabilizedRecord?.loanDetails?.fees?.originationMin).toBe(0.5);
    });

    it('should process multiple capital positions (3650 Capital)', async () => {
      const csvRow = {
        'Capital Position': 'Mezzanine/Preferred Equity/Senior Debt',
        'Email': '<EMAIL>',
        'First Name': 'Andrew',
        'Last Name': 'Parower',
        'Company': '3650 Capital',
        '(Investment Criteria) Deal Size': '$25M+ Senior Debt/$10M+ Mezzanine',
        '(Investment Criteria) Property Type': 'Assemblage/Healthcare/Hospitality/Industrial/Land/Mixed-Use/Multi-Family/Office/Retail/Single-Family Residence (Sfr)/Special-Use',
        '(Investment Criteria) Loan Type': 'Bridge/Construction/Note-On-Note',
        '(Investment Criteria) Loan Type.1': 'Permanent',
        '(Investment Criteria) Loan Term (Years)': '1 - 5 Years Senior Debt/2 - 3 Years Transitional/10 Years Permanent/3 - 7 Years Special Situations',
        '(Investment Criteria) Loan Interest Rate': '8.5%+ Senior Debt/12%+ Mezzanine',
        '(Investment Criteria) Loan To Value': '85% Bridge/75% Permanent',
        '(Investment Criteria) Loan DSCR': '1.25x (1.2x for multifamily)'
      };
      
      const headerMappings = {
        'Capital Position': 'capital_position',
        'Email': 'email',
        'First Name': 'first_name',
        'Last Name': 'last_name',
        'Company': 'company_name',
        '(Investment Criteria) Deal Size': 'deal_size',
        '(Investment Criteria) Property Type': 'property_types',
        '(Investment Criteria) Loan Type': 'loan_type',
        '(Investment Criteria) Loan Type.1': 'loan_type_2',
        '(Investment Criteria) Loan Term (Years)': 'loan_term',
        '(Investment Criteria) Loan Interest Rate': 'interest_rate',
        '(Investment Criteria) Loan To Value': 'loan_to_value',
        '(Investment Criteria) Loan DSCR': 'loan_dscr'
      };

      const input: ProcessingInput = { csvRow, headerMappings };
      const result = await InvestmentCriteriaProcessor.processCSVRow(input);

      expect(result.errors).toEqual([]);
      expect(result.records.length).toBeGreaterThan(2); // Should create records for each capital position
      
      // Should have records for all capital positions
      const capitalPositions = result.records.map(r => r.capitalPosition);
      expect(capitalPositions).toContain('Mezzanine');
      expect(capitalPositions).toContain('Preferred Equity');
      expect(capitalPositions).toContain('Senior Debt');
    });

    it('should process Preferred Equity (7 Acre Investments)', async () => {
      const csvRow = {
        'Capital Position': 'Preferred Equity',
        'Email': '<EMAIL>',
        'First Name': 'Adam',
        'Last Name': 'Fletcher',
        'Company': '7 Acre Investments',
        '(Investment Criteria) Country': 'United States',
        '(Investment Criteria) State': 'Florida/Kansas',
        '(Investment Criteria) City': 'Miami',
        '(Investment Criteria) Deal Size': '$5M - $20M',
        '(Investment Criteria) Property Type': 'Mixed-Use/Multi-Family',
        '(Investment Criteria) Strategies': 'Rescue Capital',
        '(Investment Criteria) Loan To Value': 'up to 80% of total value',
        'Notes': 'Behind Fannie, Freddie and Agency Debt'
      };
      
      const headerMappings = {
        'Capital Position': 'capital_position',
        'Email': 'email',
        'First Name': 'first_name',
        'Last Name': 'last_name',
        'Company': 'company_name',
        '(Investment Criteria) Country': 'country',
        '(Investment Criteria) State': 'state',
        '(Investment Criteria) City': 'city',
        '(Investment Criteria) Deal Size': 'deal_size',
        '(Investment Criteria) Property Type': 'property_types',
        '(Investment Criteria) Strategies': 'strategies',
        '(Investment Criteria) Loan To Value': 'loan_to_value',
        'Notes': 'notes'
      };

      const input: ProcessingInput = { csvRow, headerMappings };
      const result = await InvestmentCriteriaProcessor.processCSVRow(input);

      expect(result.errors).toEqual([]);
      expect(result.records.length).toBe(1);
      expect(result.records[0].capitalPosition).toBe('Preferred Equity');
      expect(result.records[0].recordType).toBe('investment_criteria');
      expect(result.records[0].location.state).toEqual(['Florida', 'Kansas']);
      expect(result.records[0].location.city).toEqual(['Miami']);
    });

    it('should handle missing capital position', async () => {
      const csvRow = {
        'Email': '<EMAIL>',
        'First Name': 'Test',
        'Last Name': 'User',
        'Company': 'Test Company'
      };
      
      const headerMappings = {
        'Email': 'email',
        'First Name': 'first_name',
        'Last Name': 'last_name',
        'Company': 'company_name'
      };

      const input: ProcessingInput = { csvRow, headerMappings };
      const result = await InvestmentCriteriaProcessor.processCSVRow(input);

      expect(result.errors).toContain('No valid Capital Position found in the row. This field is required.');
      expect(result.records).toEqual([]);
    });

    it('should handle A&S Capital construction loans', async () => {
      const csvRow = {
        'Capital Position': 'Senior Debt',
        'Email': '<EMAIL>',
        'First Name': 'Alexis',
        'Last Name': 'Agopian',
        'Company': 'A&S Capital',
        '(Investment Criteria) Deal Size': '$250K - $10M Construction/$0 - $2.5M Long Term',
        '(Investment Criteria) Property Type': 'Mixed-Use/Multi-Family/Single-Family Residential (Sf)',
        '(Investment Criteria) Strategies': 'Opportunistic/Value-Add',
        '(Investment Criteria) Loan Program': 'C-Pace',
        '(Investment Criteria) Structured Loan Tranche': 'Whole Loan',
        '(Investment Criteria) Loan Type': 'Acquisition/Bridge/Construction/Rehab',
        '(Investment Criteria) Loan Term (Years)': '1 - 2 Years',
        '(Investment Criteria) Loan Interest Rate': '11%',
        '(Investment Criteria) Loan To Value': '65%',
        '(Investment Criteria) Loan To Cost': '80%',
        '(Investment Criteria) Loan Origination Fee (%)': '2%',
        '(Investment Criteria) Closing Time': '3-4 Weeks'
      };
      
      const headerMappings = {
        'Capital Position': 'capital_position',
        'Email': 'email',
        'First Name': 'first_name',
        'Last Name': 'last_name',
        'Company': 'company_name',
        '(Investment Criteria) Deal Size': 'deal_size',
        '(Investment Criteria) Property Type': 'property_types',
        '(Investment Criteria) Strategies': 'strategies',
        '(Investment Criteria) Loan Program': 'loan_program',
        '(Investment Criteria) Structured Loan Tranche': 'structured_loan_tranche',
        '(Investment Criteria) Loan Type': 'loan_type',
        '(Investment Criteria) Loan Term (Years)': 'loan_term',
        '(Investment Criteria) Loan Interest Rate': 'interest_rate',
        '(Investment Criteria) Loan To Value': 'loan_to_value',
        '(Investment Criteria) Loan To Cost': 'loan_to_cost',
        '(Investment Criteria) Loan Origination Fee (%)': 'loan_origination_fee',
        '(Investment Criteria) Closing Time': 'closing_time'
      };

      const input: ProcessingInput = { csvRow, headerMappings };
      const result = await InvestmentCriteriaProcessor.processCSVRow(input);

      expect(result.errors).toEqual([]);
      expect(result.records.length).toBeGreaterThan(0);
      
      const record = result.records[0];
      expect(record.capitalPosition).toBe('Senior Debt');
      expect(record.recordType).toBe('debt_instrument');
      expect(record.loanDetails?.loanProgram).toContain('C-Pace');
      expect(record.loanDetails?.interestRates?.base).toBe(11);
      expect(record.loanDetails?.loanToValue?.min).toBe(65);
      expect(record.loanDetails?.loanToCost?.min).toBe(80);
      expect(record.loanDetails?.closingTime).toBe(3.5); // Average of 3-4 weeks
    });

    it('should handle complex capital positions with CO-GP', async () => {
      const csvRow = {
        'Capital Position': 'CO-GP/General Partner (GP)/Joint Venture (JV)/Limited Partner (LP)/Mezzanine/Preferred Equity/Senior Debt',
        'Email': '<EMAIL>',
        'First Name': 'David',
        'Last Name': 'Bess',
        'Company': 'Abco Capital Partners',
        '(Investment Criteria) Deal Size': '$1M - $200M',
        '(Investment Criteria) Property Type': 'Hospitality/Industrial/Mixed-Use/Multi-Family/Office/Retail',
        '(Investment Criteria) Property Subproperty Type': 'Hotels/Single Tenant Nnn Property/Self-Storage',
        '(Investment Criteria) Strategies': 'Opportunistic/Value-Add',
        '(Investment Criteria) Loan Program': 'C-Pace',
        '(Investment Criteria) Structured Loan Tranche': 'Whole Loan',
        '(Investment Criteria) Loan Type': 'Acquisition/Bridge/Construction'
      };
      
      const headerMappings = {
        'Capital Position': 'capital_position',
        'Email': 'email',
        'First Name': 'first_name',
        'Last Name': 'last_name',
        'Company': 'company_name',
        '(Investment Criteria) Deal Size': 'deal_size',
        '(Investment Criteria) Property Type': 'property_types',
        '(Investment Criteria) Property Subproperty Type': 'property_sub_types',
        '(Investment Criteria) Strategies': 'strategies',
        '(Investment Criteria) Loan Program': 'loan_program',
        '(Investment Criteria) Structured Loan Tranche': 'structured_loan_tranche',
        '(Investment Criteria) Loan Type': 'loan_type'
      };

      const input: ProcessingInput = { csvRow, headerMappings };
      const result = await InvestmentCriteriaProcessor.processCSVRow(input);

      expect(result.errors).toEqual([]);
      expect(result.records.length).toBeGreaterThan(5); // Should create records for all capital positions
      
      const capitalPositions = result.records.map(r => r.capitalPosition);
      expect(capitalPositions).toContain('CO-GP');
      expect(capitalPositions).toContain('General Partner (GP)');
      expect(capitalPositions).toContain('Joint Venture (JV)');
      expect(capitalPositions).toContain('Limited Partner (LP)');
      expect(capitalPositions).toContain('Mezzanine');
      expect(capitalPositions).toContain('Preferred Equity');
      expect(capitalPositions).toContain('Senior Debt');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty CSV row', async () => {
      const input: ProcessingInput = { csvRow: {}, headerMappings: {} };
      const result = await InvestmentCriteriaProcessor.processCSVRow(input);

      expect(result.errors).toContain('No valid Capital Position found in the row. This field is required.');
      expect(result.records).toEqual([]);
    });

    it('should handle malformed deal sizes', async () => {
      const csvRow = {
        'Capital Position': 'Senior Debt',
        '(Investment Criteria) Deal Size': 'Invalid deal size format'
      };
      const headerMappings = {
        'Capital Position': 'capital_position',
        '(Investment Criteria) Deal Size': 'deal_size'
      };

      const input: ProcessingInput = { csvRow, headerMappings };
      const result = await InvestmentCriteriaProcessor.processCSVRow(input);

      expect(result.errors).toEqual([]);
      expect(result.records.length).toBeGreaterThan(0);
      // Should still create records but with original deal size text
    });

    it('should handle special characters in loan data', async () => {
      const csvRow = {
        'Capital Position': 'Senior Debt',
        '(Investment Criteria) Deal Size': '$5M - $100M',
        '(Investment Criteria) Loan DSCR': '1.25x (1.2x for multifamily)',
        '(Investment Criteria) Recourse Loan': 'Non-Recourse/Recourse'
      };
      const headerMappings = {
        'Capital Position': 'capital_position',
        '(Investment Criteria) Deal Size': 'deal_size',
        '(Investment Criteria) Loan DSCR': 'loan_dscr',
        '(Investment Criteria) Recourse Loan': 'recourse_loan'
      };

      const input: ProcessingInput = { csvRow, headerMappings };
      const result = await InvestmentCriteriaProcessor.processCSVRow(input);

      expect(result.errors).toEqual([]);
      expect(result.records.length).toBeGreaterThan(0);
      
      const record = result.records[0];
      expect(record.loanDetails?.dscr?.min).toBe(1.25);
    });

    it('should handle percentage values in numeric fields', async () => {
      const csvRow = {
        'Capital Position': 'Senior Debt',
        '(Investment Criteria) Deal Size': '$10M - $50M',
        '(Investment Criteria) Loan Interest Rate': '15.5%', // Should convert to 0.155
        '(Investment Criteria) Loan To Value': '75.5%', // Should convert to 0.755
        '(Investment Criteria) Loan Origination Fee (%)': '2.25%' // Should convert to 0.0225
      };
      const headerMappings = {
        'Capital Position': 'capital_position',
        '(Investment Criteria) Deal Size': 'deal_size',
        '(Investment Criteria) Loan Interest Rate': 'interest_rate',
        '(Investment Criteria) Loan To Value': 'loan_to_value',
        '(Investment Criteria) Loan Origination Fee (%)': 'loan_origination_fee'
      };

      const input: ProcessingInput = { csvRow, headerMappings };
      const result = await InvestmentCriteriaProcessor.processCSVRow(input);

      expect(result.errors).toEqual([]);
      expect(result.records.length).toBeGreaterThan(0);
      
      const record = result.records[0];
      expect(record.loanDetails?.interestRates?.base).toBe(0.155);
      expect(record.loanDetails?.loanToValue?.min).toBe(0.755);
    });
  });
}); 