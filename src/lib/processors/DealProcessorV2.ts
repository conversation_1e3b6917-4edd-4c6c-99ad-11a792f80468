import { BaseProcessor } from "@/lib/processors/BaseProcessor";
import { EntityData, ProcessorOptions } from "@/types/processing";
import { generateDealExtractionV2Prompt } from "../prompts/deal-extraction-v2";
import {
  LLMFactory,
  createProcessor<PERSON>ogger<PERSON><PERSON>pter,
  LLMMessage,
  LLMResponse,
} from "../llm";
import {
  AbstractTextExtractor,
  PDFTextExtractor,
  CSVTextExtractor,
  XLSXTextExtractor,
  WordTextExtractor,
} from "../utils/textExtractor";
import { AppDataSource } from "../typeorm/config";
import { calculateOverallQuality } from "../utils/nsfQualityCalculator";
// Use interfaces to avoid circular dependencies
import type { IDealsV2, IDealNsfField, IProperty, IOwner } from "../typeorm/types";
import { InvestmentCriteriaDebt } from "../typeorm/entities/InvestmentCriteriaDebt";
import { InvestmentCriteriaEquity } from "../typeorm/entities/InvestmentCriteriaEquity";
import * as XLSX from "xlsx";
import * as fs from "fs";
import * as path from "path";

// Extended processor options for DealProcessorV2
interface DealProcessorV2Options extends ProcessorOptions {
  useUniversalPrompt?: boolean;
  skipDatabaseInsert?: boolean;
  llmModel?:
    | "gemini-flash"
    | "gemini-pro"
    | "openai-4o"
    | "openai-4o-mini"
    | "openai-o1-preview"
    | "openai-o1-mini";
}

// Interface for the V2 extraction response
interface DealV2ExtractionResponse {
  deal_data: Record<string, any>;
  property_data?: Record<string, any>;
  nsf_fields?: Record<string, any>[];
  metadata: {
    document_type: string;
    extraction_confidence: string;
    processing_notes: string;
    missing_critical_fields: string[];
    data_quality_issues: string[];
    extraction_method: string[];
    extraction_timestamp: string;
    quality_metrics?: any;
  };
}

export class DealProcessorV2 extends BaseProcessor {
  private llmProvider;
  private useUniversalPrompt: boolean;
  private skipDatabaseInsert: boolean;
  private selectedModel: string = "gemini-flash";

  constructor(options: DealProcessorV2Options = {}) {
    super("DealProcessorV2", options);
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this));

    // Determine which model to use
    const selectedModel = options.llmModel || "gemini-flash";
    this.selectedModel = selectedModel;

    // Create the appropriate provider based on model selection
    if (selectedModel.startsWith("openai")) {
      let modelName: string;
      switch (selectedModel) {
        case "openai-4o":
          modelName = "gpt-4o";
          break;
        case "openai-4o-mini":
          modelName = "gpt-4o-mini";
          break;
        case "openai-o1-preview":
          modelName = "gpt-4o";
          break;
        case "openai-o1-mini":
          modelName = "gpt-4o-mini";
          break;
        default:
          modelName = "gpt-4o";
      }
      this.llmProvider = LLMFactory.createProvider("openai", loggerAdapter, { 
        defaultOptions: { 
          model: modelName,
          maxTokens: 15000 // Increased for comprehensive deal extraction
        } 
      });
    } else {
      // Map the model name to the actual Gemini model (same as V1 processor)
      const modelName = selectedModel === "gemini-pro" ? "gemini-2.5-pro" : "gemini-2.5-flash";
      
      this.llmProvider = LLMFactory.createProvider("gemini", loggerAdapter, { 
        defaultOptions: { 
          model: modelName,
          maxTokens: 15000 // Increased for comprehensive deal extraction
        } 
      });
    }

    this.useUniversalPrompt = options.useUniversalPrompt || false;
    this.skipDatabaseInsert = options.skipDatabaseInsert || false;
  }

  // Unified processing method - handles both single and multiple files
  async processFiles(
    files: Array<{
      buffer: Buffer;
      mimeType: string;
      fileName: string;
    }>
  ): Promise<{
    success: boolean;
    dealId?: number;
    error?: string;
    extractedData?: DealV2ExtractionResponse;
    llmResponse?: any;
    processingDuration?: number;
  }> {
    const startTime = Date.now();

    try {
      this.log("info", `Processing ${files.length} files together with V2 processor`);

      // Excel to CSV conversion and Word to text conversion for each file
      const uploadFiles = await Promise.all(files.map(async (file) => {
        const isExcel =
          file.mimeType === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
          file.mimeType === "application/vnd.ms-excel" ||
          file.fileName.toLowerCase().endsWith(".xlsx") ||
          file.fileName.toLowerCase().endsWith(".xls");
        const isWord =
          file.mimeType === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
          file.mimeType === "application/msword" ||
          file.fileName.toLowerCase().endsWith(".docx") ||
          file.fileName.toLowerCase().endsWith(".doc");
        
        if (isExcel) {
          try {
            const workbook = XLSX.read(file.buffer, { type: "buffer" });
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];
            const csvContent = XLSX.utils.sheet_to_csv(worksheet);
            return {
              buffer: Buffer.from(csvContent, "utf-8"),
              mimeType: "text/csv",
              fileName: file.fileName.replace(/\.(xlsx|xls)$/i, ".csv"),
            };
          } catch (err) {
            this.log(
              "warn",
              `Failed to convert Excel to CSV for ${file.fileName}: ${err}. Uploading original file.`
            );
            return file;
          }
        } else if (isWord) {
          try {
            const extractor = DealProcessorV2.getExtractor(file.mimeType);
            if (extractor) {
              const textContent = await extractor.extractText(file.buffer);
              return {
                buffer: Buffer.from(textContent, "utf-8"),
                mimeType: "text/plain",
                fileName: file.fileName.replace(/\.(docx|doc)$/i, ".txt"),
              };
            } else {
              this.log(
                "warn",
                `No extractor available for Word document ${file.fileName}. Uploading original file.`
              );
              return file;
            }
          } catch (err) {
            this.log(
              "warn",
              `Failed to convert Word document to text for ${file.fileName}: ${err}. Uploading original file.`
            );
            return file;
          }
        }
        return file;
      }));

      // Extract text from all files
      const fileContents: Array<{
        fileName: string;
        content: string;
        type: string;
      }> = [];
      let combinedContent = "";

      for (const file of uploadFiles) {
        let documentContent = "";
        const extractor = DealProcessorV2.getExtractor(file.mimeType);
        if (extractor) {
          try {
            documentContent = await extractor.extractText(file.buffer);
          } catch (err) {
            this.log(
              "warn",
              `Failed to extract text from ${file.fileName}: ${err}. Skipping file.`
            );
            continue;
          }
        } else {
          this.log(
            "warn",
            `No extractor available for ${file.fileName} (${file.mimeType}). Skipping file.`
          );
          continue;
        }

        fileContents.push({
          fileName: file.fileName,
          content: documentContent,
          type: file.mimeType,
        });

        combinedContent += `\n\n--- ${file.fileName} ---\n\n${documentContent}`;
      }

      if (fileContents.length === 0) {
        throw new Error("No files could be processed successfully");
      }

      // Fetch central mapping data for enhanced prompt generation
      const centralMappings = await this.fetchCentralMappings();
      this.log("info", `Fetched central mappings: ${Object.keys(centralMappings).length} mapping categories`);
      
      // Generate the V2 extraction prompt with central mappings
      const prompt = await generateDealExtractionV2Prompt(files.length > 1, centralMappings);
      
      // Log the prompt being used for debugging
      this.log("info", `Using V2 extraction prompt with central mappings for ${files.length} file(s)`);
      this.log("debug", `Prompt preview: ${prompt.substring(0, 200)}...`);
      
      // Save prompt log for debugging (like V1 processor)
      const promptLogPath = await this.savePromptLog(prompt, files, combinedContent);
      this.log("info", `Prompt logged to: ${promptLogPath}`);
      
      // Process with LLM
      const llmResponse = await this.processWithLLM(prompt, [combinedContent]);
      
      // Log LLM response for debugging
      this.log("info", `LLM response received: ${llmResponse.content.length} characters`);
      this.log("debug", `LLM response preview: ${llmResponse.content.substring(0, 200)}...`);
      
      // Save complete LLM response for debugging
      const responseLogPath = await this.saveResponseLog(llmResponse, files);
      this.log("info", `Complete LLM response saved to: ${responseLogPath}`);
      
      // Parse the response
      const parsedData = await this.parseLLMResponse(llmResponse);
      
      // Save parsed JSON exactly as parsed (no transformations)
      const parsedJsonPath = await this.saveParsedJson(parsedData, files);
      this.log("info", `Parsed JSON saved to: ${parsedJsonPath}`);
      
      // Validate the parsed data structure
      this.validateParsedData(parsedData);
      
      // Save to database if not skipped
      let dealId: number | undefined;
      if (!this.skipDatabaseInsert) {
        dealId = await this.saveToDatabase(parsedData);
        
        // Link processed files to the deal
        if (dealId && files.length > 0) {
          await this.linkFilesToDeal(dealId, files);
        }
      }

      const processingDuration = Date.now() - startTime;

      // Calculate accurate data quality metrics
      const qualityMetrics = calculateOverallQuality(parsedData, parsedData.nsf_fields || []);
      
      return {
        success: true,
        dealId,
        extractedData: {
          deal_data: parsedData, // AI returns flat structure, so use parsedData directly
          property_data: parsedData.property,
          nsf_fields: parsedData.nsf_fields,
          metadata: {
            document_type: this.determineDocumentType(fileContents),
            extraction_confidence: parsedData.extraction_confidence || "medium",
            processing_notes: parsedData.processing_notes || "",
            missing_critical_fields: parsedData.missing_critical_fields || [],
            data_quality_issues: parsedData.data_quality_issues || [],
            extraction_method: parsedData.extraction_method || ["llm"],
            extraction_timestamp: new Date().toISOString(),
            // Add accurate quality metrics
            quality_metrics: qualityMetrics
          }
        },
        llmResponse,
        processingDuration
      };
      
    } catch (error) {
      const processingDuration = Date.now() - startTime;
      this.log("error", `Error processing files: ${error}`);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        processingDuration
      };
    }
  }

  // Legacy method for backward compatibility - wraps single file in array
  async processFileDirectly(
    fileBuffer: Buffer,
    mimeType: string,
    fileName: string
  ): Promise<{
    success: boolean;
    dealId?: number;
    error?: string;
    extractedData?: DealV2ExtractionResponse;
    llmResponse?: any;
    processingDuration?: number;
  }> {
    // Convert single file to array and use unified method
    return this.processFiles([{ buffer: fileBuffer, mimeType, fileName }]);
  }

  // Method to process files from file paths (for the upload API)
  async processFilesFromPaths(
    filePaths: string[]
  ): Promise<{
    success: boolean;
    dealId?: number;
    error?: string;
    extractedData?: DealV2ExtractionResponse;
    llmResponse?: any;
    processingDuration?: number;
  }> {
    try {
      const files = filePaths.map(filePath => {
        const buffer = fs.readFileSync(filePath);
        const mimeType = this.getMimeType(filePath);
        const fileName = path.basename(filePath);
        return { buffer, mimeType, fileName };
      });
      
      return await this.processFiles(files);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private async processWithLLM(prompt: string, texts: string[]): Promise<LLMResponse> {
    const combinedText = texts.join("\n\n---\n\n");
    
    const messages: LLMMessage[] = [
      {
        role: "system",
        content: prompt
      },
      {
        role: "user",
        content: `Please analyze the following real estate documents and extract deal information:\n\n${combinedText}`
      }
    ];
    
    return await this.llmProvider.callLLM(messages);
  }

  private async parseLLMResponse(response: LLMResponse): Promise<any> {
    try {
      // Clean up the response content to handle markdown-wrapped JSON
      let jsonContent = response.content.trim();

      this.log(
        "debug",
        `Raw LLM response length: ${jsonContent.length} characters`
      );

      // Handle markdown-formatted JSON responses
      if (jsonContent.includes("```json")) {
        const jsonMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonContent = jsonMatch[1].trim();
        }
      } else if (jsonContent.includes("```")) {
        const codeMatch = jsonContent.match(/```\s*([\s\S]*?)\s*```/);
        if (codeMatch) {
          jsonContent = codeMatch[1].trim();
        }
      }

      // Clean up any remaining markdown or extra text
      const firstBrace = jsonContent.indexOf("{");
      const lastBrace = jsonContent.lastIndexOf("}");

      if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
        jsonContent = jsonContent.substring(firstBrace, lastBrace + 1);
      }

      jsonContent = jsonContent
        .replace(/^\s*```\s*/, "")
        .replace(/\s*```\s*$/, "")
        .replace(/^[^{]*/, "")
        .replace(/[^}]*$/, "")
        .trim();

      this.log(
        "debug",
        `Cleaned JSON content length: ${jsonContent.length} characters`
      );
      this.log(
        "debug",
        `Attempting to parse JSON: ${jsonContent.substring(0, 200)}...`
      );

      // Try to parse the JSON
      return JSON.parse(jsonContent);
      
    } catch (err) {
      this.log(
        "error",
        `Failed to parse LLM JSON output: ${err}. Raw response length: ${response.content.length}`
      );
      this.log(
        "error",
        `Raw response preview: ${response.content.substring(0, 1000)}...`
      );

      // Check if response is obviously not JSON (very short or no braces)
      if (response.content.length < 100 || !response.content.includes("{")) {
        this.log("error", `LLM returned non-JSON response: "${response.content}"`);
        
        // Create error file for debugging
        const errorFilePath = await this.createErrorFile(
          `json_parse_failed_${Date.now()}`,
          `LLM returned non-JSON response instead of required JSON format`,
          response.content,
          {
            responseLength: response.content.length,
            hasBraces: response.content.includes("{"),
            processor: 'DealProcessorV2'
          }
        );
        
        throw new Error(`LLM returned non-JSON response instead of required JSON format. Response: "${response.content.substring(0, 200)}..." Error file: ${errorFilePath}`);
      }

      // Try to salvage partial JSON by attempting to repair common issues
      try {
        let repairAttempt = response.content.trim();

        // Extract just the JSON part if it exists
        const firstBrace = repairAttempt.indexOf("{");
        if (firstBrace !== -1) {
          repairAttempt = repairAttempt.substring(firstBrace);

          // Try to find the last complete object by counting braces
          let braceCount = 0;
          let lastValidIndex = -1;

          for (let i = 0; i < repairAttempt.length; i++) {
            if (repairAttempt[i] === "{") {
              braceCount++;
            } else if (repairAttempt[i] === "}") {
              braceCount--;
              if (braceCount === 0) {
                lastValidIndex = i;
                break;
              }
            }
          }

          if (lastValidIndex > 0) {
            repairAttempt = repairAttempt.substring(0, lastValidIndex + 1);
            this.log(
              "debug",
              `Attempting to parse repaired JSON: ${repairAttempt.substring(0, 200)}...`
            );
            const repaired = JSON.parse(repairAttempt);
            this.log("info", "Successfully recovered from truncated JSON response");
            return repaired;
          } else {
            throw new Error("Could not repair truncated JSON");
          }
        } else {
          throw new Error("No JSON object found in response");
        }
      } catch (repairErr) {
        this.log("error", `Failed to repair JSON: ${repairErr}`);
        throw new Error(`Failed to parse LLM output: ${err}. Response may be truncated or malformed.`);
      }
    }
  }

  private async saveToDatabase(parsedData: any): Promise<number> {
    try {
      // Only initialize if not already connected
      if (!AppDataSource.isInitialized) {
        await AppDataSource.initialize();
      }
      
      // Log the parsed data structure for debugging
      this.log("info", `Parsed data structure: deal_name=${!!parsedData.deal_name}, property=${!!parsedData.property}, nsf_fields=${Array.isArray(parsedData.nsf_fields) ? parsedData.nsf_fields.length : 'not array'}`);
      if (parsedData.nsf_fields && Array.isArray(parsedData.nsf_fields)) {
        this.log("debug", `NSF fields structure: ${JSON.stringify(parsedData.nsf_fields.map(f => ({ deal_type: f.deal_type, nsf_context: f.nsf_context })), null, 2)}`);
      }
      
      // Import entities using ES module imports
      const { Property } = await import("../typeorm/entities/Property");
      const { DealsV2 } = await import("../typeorm/entities/DealsV2");
      const { DealNsfField } = await import("../typeorm/entities/DealNsfField");
      
      // Save property first if it exists
      let property: any = null;
      if (parsedData.property) {
        const propertyRepository = AppDataSource.getRepository(Property);
        
        // Map AI response fields (snake_case) to entity fields (camelCase)
        const propertyData = {
          address: parsedData.property.address || null,
          city: parsedData.property.city || null,
          state: parsedData.property.state || null,
          zipcode: parsedData.property.zipcode || null,
          region: parsedData.property.region || null,
          country: parsedData.property.country || null,
          market: parsedData.property.market || null,
          submarket: parsedData.property.submarket || null,
          neighborhood: parsedData.property.neighborhood || null,
          propertyType: parsedData.property.property_type || null,
          subpropertyType: parsedData.property.subproperty_type || null,
          buildingSqft: parsedData.property.building_sqft || null,
          landAcres: parsedData.property.land_acres || null,
          lotArea: parsedData.property.lot_area || null,
          yearBuilt: parsedData.property.year_built || null,
          yearRenovated: parsedData.property.year_renovated || null,
          latitude: parsedData.property.latitude || null,
          longitude: parsedData.property.longitude || null,
          propertyStatus: parsedData.property.property_status || null,
          numberOfUnits: parsedData.property.number_of_units || null,
          appraisalValue: parsedData.property.appraisal_value || null,
          appraisalValueDate: parsedData.property.appraisal_value_date || null,
          // GSF, ZFA, and Total NSF fields - now properly mapped to property
          gsfGrossSquareFoot: parsedData.property.gsf_gross_square_foot || null,
          zfaZoningFloorArea: parsedData.property.zfa_zoning_floor_area || null,
          totalNsfNetSquareFoot: parsedData.property.total_nsf_net_square_foot || null,
          far: parsedData.property.far || null,
          historicalOccupancyTrend: parsedData.property.historical_occupancy_trend || null,
          environmentalRiskScore: parsedData.property.environmental_risk_score || null,
          propertyDescription: parsedData.property.property_description || null,
          floorAreaRatio: parsedData.property.floor_area_ratio || null,
          zoningSquareFootage: parsedData.property.zoning_square_footage || null,
        };
        
        property = propertyRepository.create(propertyData);
        await propertyRepository.save(property);
        this.log("info", `Saved property: ${parsedData.property.address}, ${parsedData.property.city}`);
        this.log("debug", `Property GSF: ${propertyData.gsfGrossSquareFoot}, ZFA: ${propertyData.zfaZoningFloorArea}, Total NSF: ${propertyData.totalNsfNetSquareFoot}`);
      }
      
      // Save deal
      const dealsRepository = AppDataSource.getRepository(DealsV2);
      
      // Map AI response fields (snake_case) to entity fields (camelCase)
      // AI returns flat structure, so access fields directly from parsedData
      const dealData = {
        // Core deal fields
        dealName: parsedData.deal_name || null,
        summary: parsedData.summary || null,
        askCapitalPosition: parsedData.ask_capital_position || null,
        askAmount: parsedData.ask_amount || null,
        capitalRaiseTimeline: parsedData.capital_raise_timeline || null,
        dateReceived: parsedData.date_received || null,
        dealStage: parsedData.deal_stage || null,
        dateClosed: parsedData.date_closed || null,
        dateUnderContract: parsedData.date_under_contract || null,
        strategy: parsedData.strategy || null,
        holdPeriod: parsedData.hold_period || null,
        dealStatus: parsedData.deal_status || null,
        
        // Financial metrics
        yieldOnCost: parsedData.yield_on_cost || null,
        commonEquityInternalRateOfReturnIrr: parsedData.common_equity_internal_rate_of_return_irr || null,
        commonEquityEquityMultiple: parsedData.common_equity_equity_multiple || null,
        gpEquityMultiple: parsedData.gp_equity_multiple || null,
        gpInternalRateOfReturnIrr: parsedData.gp_internal_rate_of_return_irr || null,
        lpEquityMultiple: parsedData.lp_equity_multiple || null,
        lpInternalRateOfReturnIrr: parsedData.lp_internal_rate_of_return_irr || null,
        preferredEquityInternalRateOfReturnIrr: parsedData.preferred_equity_internal_rate_of_return_irr || null,
        preferredEquityEquityMultiple: parsedData.preferred_equity_equity_multiple || null,
        totalInternalRateOfReturnIrr: parsedData.total_internal_rate_of_return_irr || null,
        totalEquityMultiple: parsedData.total_equity_multiple || null,
        
        // Property details
        property: property,
        
        // Additional metadata
        extractionConfidence: parsedData.extraction_confidence || "medium",
        processingNotes: parsedData.processing_notes || "",
        missingCriticalFields: parsedData.missing_critical_fields || [],
        dataQualityIssues: parsedData.data_quality_issues || [],
        extractionMethod: parsedData.extraction_method || ["llm"],
        extractionTimestamp: parsedData.extraction_timestamp || new Date().toISOString(),
        processorVersion: "v2",
        llmModelUsed: this.selectedModel,
        llmProvider: this.selectedModel.startsWith("openai") ? "openai" : "gemini",
        
        // Store any additional fields from AI response in extraFields
        extraFields: Object.keys(parsedData)
          .filter(key => !['deal_name', 'summary', 'ask_capital_position', 'ask_amount', 'capital_raise_timeline', 
                           'date_received', 'deal_stage', 'date_closed', 'date_under_contract', 'strategy', 
                           'hold_period', 'deal_status', 'yield_on_cost', 'common_equity_internal_rate_of_return_irr',
                           'common_equity_equity_multiple', 'gp_equity_multiple', 'gp_internal_rate_of_return_irr',
                           'lp_equity_multiple', 'lp_internal_rate_of_return_irr', 'preferred_equity_internal_rate_of_return_irr',
                           'preferred_equity_equity_multiple', 'total_internal_rate_of_return_irr', 'total_equity_multiple',
                           'extraction_confidence', 'processing_notes', 'missing_critical_fields', 'data_quality_issues',
                           'extraction_method', 'extraction_timestamp', 'property', 'nsf_fields', 'conflicts'].includes(key))
          .reduce((obj, key) => {
            obj[key] = parsedData[key];
            return obj;
          }, {} as any),
        // Add conflicts field if present
        ...(parsedData.conflicts && { conflicts: parsedData.conflicts })
      };
      
      this.log("debug", `Creating deal with mapped data: ${JSON.stringify(dealData, null, 2)}`);
      
      // Log the key field mappings for debugging
      this.log("info", `Key field mappings:`);
      this.log("info", `  deal_name: "${parsedData.deal_name}" -> dealName: "${dealData.dealName}"`);
      this.log("info", `  summary: "${parsedData.summary?.substring(0, 100)}..." -> summary: "${dealData.summary?.substring(0, 100)}..."`);
      this.log("info", `  strategy: "${parsedData.strategy}" -> strategy: "${dealData.strategy}"`);
      this.log("info", `  hold_period: ${parsedData.hold_period} -> holdPeriod: ${dealData.holdPeriod}`);
      this.log("info", `  total_irr: ${parsedData.total_internal_rate_of_return_irr} -> totalInternalRateOfReturnIrr: ${dealData.totalInternalRateOfReturnIrr}`);
      
      const deal = dealsRepository.create(dealData);
      const savedDeal = await dealsRepository.save(deal);
      
      // Ensure savedDeal is a single entity, not an array
      if (Array.isArray(savedDeal)) {
        this.log("error", `Expected single deal entity, got array with ${savedDeal.length} items`);
        throw new Error(`Expected single deal entity, got array with ${savedDeal.length} items`);
      }

      // Type assertion to help TypeScript understand the structure
      const dealEntity = savedDeal as any;
      
      this.log("info", `✅ Successfully saved deal to database:`);
      this.log("info", `  Deal ID: ${dealEntity.dealId}`);
      this.log("info", `  Deal Name: ${dealEntity.dealName}`);
      this.log("info", `  Strategy: ${dealEntity.strategy}`);
      this.log("info", `  Hold Period: ${dealEntity.holdPeriod} years`);
      this.log("info", `  Total IRR: ${dealEntity.totalInternalRateOfReturnIrr}`);
      this.log("info", `  Total Multiple: ${dealEntity.totalEquityMultiple}x`);
      
      // Process Investment Criteria Debt and Equity
      if ((parsedData.investment_criteria && parsedData.investment_criteria.length > 0)) {
        
        this.log("info", `Processing ${parsedData.investment_criteria.length} investment criteria records with nested structure`);
        
        // Dynamically fetch database columns for all investment criteria tables
        let investmentCriteriaCentralColumns: any[] = [];
        let investmentCriteriaDebtColumns: any[] = [];
        let investmentCriteriaEquityColumns: any[] = [];
        
        try {
          // Fetch investment_criteria_central table columns
          const iccResult = await AppDataSource.query(`
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'investment_criteria_central' 
            ORDER BY ordinal_position
          `);
          investmentCriteriaCentralColumns = iccResult;
          
          // Fetch investment_criteria_debt table columns
          const icdResult = await AppDataSource.query(`
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'investment_criteria_debt' 
            ORDER BY ordinal_position
          `);
          investmentCriteriaDebtColumns = icdResult;
          
          // Fetch investment_criteria_equity table columns
          const iceResult = await AppDataSource.query(`
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'investment_criteria_equity' 
            ORDER BY ordinal_position
          `);
          investmentCriteriaEquityColumns = iceResult;
          
          this.log("info", `✅ Dynamically fetched database schemas:
            - investment_criteria_central: ${investmentCriteriaCentralColumns.length} columns
            - investment_criteria_debt: ${investmentCriteriaDebtColumns.length} columns  
            - investment_criteria_equity: ${investmentCriteriaEquityColumns.length} columns`);
        } catch (error) {
          this.log("warn", `Failed to fetch dynamic schemas, using fallback: ${error}`);
        }
        
        for (const criteriaData of parsedData.investment_criteria) {
          // Create InvestmentCriteria record for each capital position
          this.log("info", `Creating InvestmentCriteria record for capital position: ${criteriaData.capital_position}`);
          
          // Only insert fields that exist in investment_criteria_central table
          const investmentCriteriaData = {
            entityType: "deal_v2",
            entityId: dealEntity.dealId.toString(),
            capitalPosition: criteriaData.capital_position || null, // Keep as string since column is TEXT
            strategies: dealEntity.strategy ? [dealEntity.strategy] : null,
            // Use property data if available - wrap location fields in arrays for PostgreSQL
            country: dealEntity.property?.country ? [dealEntity.property.country] : null,
            region: dealEntity.property?.region ? [dealEntity.property.region] : null,
            state: dealEntity.property?.state ? [dealEntity.property.state] : null,
            city: dealEntity.property?.city ? [dealEntity.property.city] : null,
            propertyTypes: dealEntity.property?.property_type ? [dealEntity.property.property_type] : null,
            propertySubcategories: dealEntity.property?.subproperty_type ? [dealEntity.property.subproperty_type] : null,
            notes: null // Will be populated after debt/equity criteria are created
          };

          // Validate array fields to prevent PostgreSQL array literal errors
          const validateArrayField = (field: any, fieldName: string): any => {
            if (field === null || field === undefined) return null;
            if (!Array.isArray(field)) {
              this.log("warn", `Field ${fieldName} is not an array, converting: ${field}`);
              return [field];
            }
            return field;
          };

          // Ensure all array fields are properly formatted
          investmentCriteriaData.country = validateArrayField(investmentCriteriaData.country, 'country');
          investmentCriteriaData.region = validateArrayField(investmentCriteriaData.region, 'region');
          investmentCriteriaData.state = validateArrayField(investmentCriteriaData.state, 'state');
          investmentCriteriaData.city = validateArrayField(investmentCriteriaData.city, 'city');
          investmentCriteriaData.propertyTypes = validateArrayField(investmentCriteriaData.propertyTypes, 'propertyTypes');
          investmentCriteriaData.propertySubcategories = validateArrayField(investmentCriteriaData.propertySubcategories, 'propertySubcategories');
          
          // Insert using raw SQL with only existing columns
          const result = await AppDataSource.query(`
            INSERT INTO investment_criteria_central (
              entity_type, entity_id, capital_position, strategies, country, region, state, city, 
              property_types, property_subcategories, notes, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW())
            RETURNING investment_criteria_id
          `, [
            investmentCriteriaData.entityType,
            investmentCriteriaData.entityId,
            investmentCriteriaData.capitalPosition,
            investmentCriteriaData.strategies,
            investmentCriteriaData.country,
            investmentCriteriaData.region,
            investmentCriteriaData.state,
            investmentCriteriaData.city,
            investmentCriteriaData.propertyTypes,
            investmentCriteriaData.propertySubcategories,
            investmentCriteriaData.notes
          ]);
          
          const savedInvestmentCriteriaId = result[0].investment_criteria_id;
          this.log("info", `✅ Created InvestmentCriteria record with ID: ${savedInvestmentCriteriaId} for ${criteriaData.capital_position}`);

          // Process nested Investment Criteria Debt
          if (criteriaData.investment_criteria_debt) {
            this.log("info", `Processing nested debt criteria for ${criteriaData.capital_position}`);
            const debtRepository = AppDataSource.getRepository(InvestmentCriteriaDebt);
            
            // Dynamically build debt criteria data object based on available columns
            const debtCriteriaData: any = {
              investmentCriteriaId: savedInvestmentCriteriaId
            };
            
            // Map all available debt fields dynamically
            if (criteriaData.investment_criteria_debt.notes !== undefined) debtCriteriaData.notes = criteriaData.investment_criteria_debt.notes;
            if (criteriaData.investment_criteria_debt.closing_time !== undefined) debtCriteriaData.closingTime = criteriaData.investment_criteria_debt.closing_time;
            if (criteriaData.investment_criteria_debt.future_facilities !== undefined) debtCriteriaData.futureFacilities = criteriaData.investment_criteria_debt.future_facilities;
            if (criteriaData.investment_criteria_debt.eligible_borrower !== undefined) debtCriteriaData.eligibleBorrower = criteriaData.investment_criteria_debt.eligible_borrower;
            if (criteriaData.investment_criteria_debt.occupancy_requirements !== undefined) debtCriteriaData.occupancyRequirements = criteriaData.investment_criteria_debt.occupancy_requirements;
            if (criteriaData.investment_criteria_debt.lien_position !== undefined) debtCriteriaData.lienPosition = criteriaData.investment_criteria_debt.lien_position;
            if (criteriaData.investment_criteria_debt.min_loan_dscr !== undefined) debtCriteriaData.minLoanDscr = criteriaData.investment_criteria_debt.min_loan_dscr;
            if (criteriaData.investment_criteria_debt.max_loan_dscr !== undefined) debtCriteriaData.maxLoanDscr = criteriaData.investment_criteria_debt.max_loan_dscr;
            if (criteriaData.investment_criteria_debt.recourse_loan !== undefined) debtCriteriaData.recourseLoan = criteriaData.investment_criteria_debt.recourse_loan;
            if (criteriaData.investment_criteria_debt.loan_min_debt_yield !== undefined) debtCriteriaData.loanMinDebtYield = criteriaData.investment_criteria_debt.loan_min_debt_yield;
            if (criteriaData.investment_criteria_debt.prepayment !== undefined) debtCriteriaData.prepayment = criteriaData.investment_criteria_debt.prepayment;
            if (criteriaData.investment_criteria_debt.yield_maintenance !== undefined) debtCriteriaData.yieldMaintenance = criteriaData.investment_criteria_debt.yield_maintenance;
            if (criteriaData.investment_criteria_debt.application_deposit !== undefined) debtCriteriaData.applicationDeposit = criteriaData.investment_criteria_debt.application_deposit;
            if (criteriaData.investment_criteria_debt.good_faith_deposit !== undefined) debtCriteriaData.goodFaithDeposit = criteriaData.investment_criteria_debt.good_faith_deposit;
            if (criteriaData.investment_criteria_debt.loan_origination_max_fee !== undefined) debtCriteriaData.loanOriginationMaxFee = criteriaData.investment_criteria_debt.loan_origination_max_fee;
            if (criteriaData.investment_criteria_debt.loan_origination_min_fee !== undefined) debtCriteriaData.loanOriginationMinFee = criteriaData.investment_criteria_debt.loan_origination_min_fee;
            if (criteriaData.investment_criteria_debt.loan_exit_min_fee !== undefined) debtCriteriaData.loanExitMinFee = criteriaData.investment_criteria_debt.loan_exit_min_fee;
            if (criteriaData.investment_criteria_debt.loan_exit_max_fee !== undefined) debtCriteriaData.loanExitMaxFee = criteriaData.investment_criteria_debt.loan_exit_max_fee;
            if (criteriaData.investment_criteria_debt.loan_interest_rate !== undefined) debtCriteriaData.loanInterestRate = criteriaData.investment_criteria_debt.loan_interest_rate;
            if (criteriaData.investment_criteria_debt.loan_interest_rate_based_off_sofr !== undefined) debtCriteriaData.loanInterestRateBasedOffSofr = criteriaData.investment_criteria_debt.loan_interest_rate_based_off_sofr;
            if (criteriaData.investment_criteria_debt.loan_interest_rate_based_off_wsj !== undefined) debtCriteriaData.loanInterestRateBasedOffWsj = criteriaData.investment_criteria_debt.loan_interest_rate_based_off_wsj;
            if (criteriaData.investment_criteria_debt.loan_interest_rate_based_off_prime !== undefined) debtCriteriaData.loanInterestRateBasedOffPrime = criteriaData.investment_criteria_debt.loan_interest_rate_based_off_prime;
            if (criteriaData.investment_criteria_debt.loan_interest_rate_based_off_3yt !== undefined) debtCriteriaData.loanInterestRateBasedOff3yt = criteriaData.investment_criteria_debt.loan_interest_rate_based_off_3yt;
            if (criteriaData.investment_criteria_debt.loan_interest_rate_based_off_5yt !== undefined) debtCriteriaData.loanInterestRateBasedOff5yt = criteriaData.investment_criteria_debt.loan_interest_rate_based_off_5yt;
            if (criteriaData.investment_criteria_debt.loan_interest_rate_based_off_10yt !== undefined) debtCriteriaData.loanInterestRateBasedOff10yt = criteriaData.investment_criteria_debt.loan_interest_rate_based_off_10yt;
            if (criteriaData.investment_criteria_debt.loan_interest_rate_based_off_30yt !== undefined) debtCriteriaData.loanInterestRateBasedOff30yt = criteriaData.investment_criteria_debt.loan_interest_rate_based_off_30yt;
            if (criteriaData.investment_criteria_debt.rate_lock !== undefined) debtCriteriaData.rateLock = criteriaData.investment_criteria_debt.rate_lock;
            if (criteriaData.investment_criteria_debt.rate_type !== undefined) debtCriteriaData.rateType = criteriaData.investment_criteria_debt.rate_type;
            if (criteriaData.investment_criteria_debt.loan_to_value_max !== undefined) debtCriteriaData.loanToValueMax = criteriaData.investment_criteria_debt.loan_to_value_max;
            if (criteriaData.investment_criteria_debt.loan_to_value_min !== undefined) debtCriteriaData.loanToValueMin = criteriaData.investment_criteria_debt.loan_to_value_min;
            if (criteriaData.investment_criteria_debt.loan_to_cost_min !== undefined) debtCriteriaData.loanToCostMin = criteriaData.investment_criteria_debt.loan_to_cost_min;
            if (criteriaData.investment_criteria_debt.loan_to_cost_max !== undefined) debtCriteriaData.loanToCostMax = criteriaData.investment_criteria_debt.loan_to_cost_max;
            if (criteriaData.investment_criteria_debt.debt_program_overview !== undefined) debtCriteriaData.debtProgramOverview = criteriaData.investment_criteria_debt.debt_program_overview;
            if (criteriaData.investment_criteria_debt.loan_type !== undefined) debtCriteriaData.loanType = criteriaData.investment_criteria_debt.loan_type;
            if (criteriaData.investment_criteria_debt.loan_type_normalized !== undefined) debtCriteriaData.loanTypeNormalized = criteriaData.investment_criteria_debt.loan_type_normalized;
            if (criteriaData.investment_criteria_debt.structured_loan_tranche !== undefined) debtCriteriaData.structuredLoanTranche = criteriaData.investment_criteria_debt.structured_loan_tranche;
            if (criteriaData.investment_criteria_debt.loan_program !== undefined) debtCriteriaData.loanProgram = criteriaData.investment_criteria_debt.loan_program;
            if (criteriaData.investment_criteria_debt.min_loan_term !== undefined) debtCriteriaData.minLoanTerm = criteriaData.investment_criteria_debt.min_loan_term;
            if (criteriaData.investment_criteria_debt.max_loan_term !== undefined) debtCriteriaData.maxLoanTerm = criteriaData.investment_criteria_debt.max_loan_term;
            if (criteriaData.investment_criteria_debt.amortization !== undefined) debtCriteriaData.amortization = criteriaData.investment_criteria_debt.amortization;
            
            const debtCriteria = debtRepository.create(debtCriteriaData);
            const savedDebtCriteria = await debtRepository.save(debtCriteria);
            
            // Update the investment_criteria_central record with the debt criteria ID
            await AppDataSource.query(`
              UPDATE investment_criteria_central 
              SET investment_criteria_debt_id = $1, notes = $2
              WHERE investment_criteria_id = $3
            `, [savedDebtCriteria.investmentCriteriaDebtId, `Debt criteria created for ${criteriaData.capital_position}`, savedInvestmentCriteriaId]);
            
            this.log("info", `✅ Saved nested debt criteria for ${criteriaData.capital_position}: loan_type=${criteriaData.investment_criteria_debt.loan_type}, interest_rate=${criteriaData.investment_criteria_debt.loan_interest_rate}`);
          }

          // Process nested Investment Criteria Equity
          if (criteriaData.investment_criteria_equity) {
            this.log("info", `Processing nested equity criteria for ${criteriaData.capital_position}`);
            const equityRepository = AppDataSource.getRepository(InvestmentCriteriaEquity);
            
            // Dynamically build equity criteria data object based on available columns
            const equityCriteriaData: any = {
              investmentCriteriaId: savedInvestmentCriteriaId
            };
            
            // Map all available equity fields dynamically
            if (criteriaData.investment_criteria_equity.equity_type !== undefined) equityCriteriaData.equityType = criteriaData.investment_criteria_equity.equity_type;
            if (criteriaData.investment_criteria_equity.target_return !== undefined) equityCriteriaData.targetReturn = criteriaData.investment_criteria_equity.target_return;
            if (criteriaData.investment_criteria_equity.minimum_internal_rate_of_return !== undefined) equityCriteriaData.minimumInternalRateOfReturn = criteriaData.investment_criteria_equity.minimum_internal_rate_of_return;
            if (criteriaData.investment_criteria_equity.minimum_yield_on_cost !== undefined) equityCriteriaData.minimumYieldOnCost = criteriaData.investment_criteria_equity.minimum_yield_on_cost;
            if (criteriaData.investment_criteria_equity.minimum_equity_multiple !== undefined) equityCriteriaData.minimumEquityMultiple = criteriaData.investment_criteria_equity.minimum_equity_multiple;
            if (criteriaData.investment_criteria_equity.target_cash_on_cash_min !== undefined) equityCriteriaData.targetCashOnCashMin = criteriaData.investment_criteria_equity.target_cash_on_cash_min;
            if (criteriaData.investment_criteria_equity.min_hold_period_years !== undefined) equityCriteriaData.minHoldPeriodYears = criteriaData.investment_criteria_equity.min_hold_period_years;
            if (criteriaData.investment_criteria_equity.max_hold_period_years !== undefined) equityCriteriaData.maxHoldPeriodYears = criteriaData.investment_criteria_equity.max_hold_period_years;
            if (criteriaData.investment_criteria_equity.ownership_requirement !== undefined) equityCriteriaData.ownershipRequirement = criteriaData.investment_criteria_equity.ownership_requirement;
            if (criteriaData.investment_criteria_equity.attachment_point !== undefined) equityCriteriaData.attachmentPoint = criteriaData.investment_criteria_equity.attachment_point;
            if (criteriaData.investment_criteria_equity.max_leverage_tolerance !== undefined) equityCriteriaData.maxLeverageTolerance = criteriaData.investment_criteria_equity.max_leverage_tolerance;
            if (criteriaData.investment_criteria_equity.typical_closing_timeline_days !== undefined) equityCriteriaData.typicalClosingTimelineDays = criteriaData.investment_criteria_equity.typical_closing_timeline_days;
            if (criteriaData.investment_criteria_equity.proof_of_funds_requirement !== undefined) equityCriteriaData.proofOfFundsRequirement = criteriaData.investment_criteria_equity.proof_of_funds_requirement;
            if (criteriaData.investment_criteria_equity.notes !== undefined) equityCriteriaData.notes = criteriaData.investment_criteria_equity.notes;
            if (criteriaData.investment_criteria_equity.equity_program_overview !== undefined) equityCriteriaData.equityProgramOverview = criteriaData.investment_criteria_equity.equity_program_overview;
            if (criteriaData.investment_criteria_equity.occupancy_requirements !== undefined) equityCriteriaData.occupancyRequirements = criteriaData.investment_criteria_equity.occupancy_requirements;
            
            // NEW FIELDS - Moved from deal
            if (criteriaData.investment_criteria_equity.yield_on_cost !== undefined) equityCriteriaData.yieldOnCost = criteriaData.investment_criteria_equity.yield_on_cost;
            if (criteriaData.investment_criteria_equity.target_return_irr_on_equity !== undefined) equityCriteriaData.targetReturnIrrOnEquity = criteriaData.investment_criteria_equity.target_return_irr_on_equity;
            if (criteriaData.investment_criteria_equity.equity_multiple !== undefined) equityCriteriaData.equityMultiple = criteriaData.investment_criteria_equity.equity_multiple;
            
            // NEW FIELDS - Position specific metrics
            if (criteriaData.investment_criteria_equity.position_specific_irr !== undefined) equityCriteriaData.positionSpecificIrr = criteriaData.investment_criteria_equity.position_specific_irr;
            if (criteriaData.investment_criteria_equity.position_specific_equity_multiple !== undefined) equityCriteriaData.positionSpecificEquityMultiple = criteriaData.investment_criteria_equity.position_specific_equity_multiple;
            
            const equityCriteria = equityRepository.create(equityCriteriaData);
            const savedEquityCriteria = await equityRepository.save(equityCriteria);
            
            // Update the investment_criteria_central record with the equity criteria ID
            await AppDataSource.query(`
              UPDATE investment_criteria_central 
              SET investment_criteria_equity_id = $1, notes = $2
              WHERE investment_criteria_id = $3
            `, [savedEquityCriteria.investmentCriteriaEquityId, `Equity criteria created for ${criteriaData.capital_position}`, savedInvestmentCriteriaId]);
            
            this.log("info", `✅ Saved nested equity criteria for ${criteriaData.capital_position}: target_return=${criteriaData.investment_criteria_equity.target_return}, min_irr=${criteriaData.investment_criteria_equity.minimum_internal_rate_of_return}`);
          }
        }
        
        this.log("info", `Successfully processed and saved ${parsedData.investment_criteria.length} investment criteria records with nested structure`);
      } else {
        this.log("warn", "No investment criteria to save");
      }
      
      // Process NSF Fields
      if (parsedData.nsf_fields && Array.isArray(parsedData.nsf_fields) && parsedData.nsf_fields.length > 0) {
        this.log("info", `Processing ${parsedData.nsf_fields.length} NSF fields`);
        
        // Calculate and validate metrics
        const processedNsfFields = this.calculateAndValidateMetrics(parsedData.nsf_fields, parsedData.ask_capital_position);
        
        // Track processed fields to avoid duplicates
        const processedSourceTypes = new Set<string>();
        const processedUseTypes = new Set<string>();
        let skippedDuplicates = 0;
        
        // Save each NSF field to the database
        for (const nsfField of processedNsfFields) {
          // Check for duplicates and skip them
          if (nsfField.source_type && nsfField.nsf_context === 'sources') {
            if (processedSourceTypes.has(nsfField.source_type)) {
              this.log("warn", `⚠️ Skipping duplicate source_type: ${nsfField.source_type} (amount: ${nsfField.amount})`);
              skippedDuplicates++;
              continue;
            }
            processedSourceTypes.add(nsfField.source_type);
          }
          
          if (nsfField.use_type && nsfField.nsf_context === 'uses_total') {
            if (processedUseTypes.has(nsfField.use_type)) {
              this.log("warn", `⚠️ Skipping duplicate use_type: ${nsfField.use_type} (amount: ${nsfField.amount})`);
              skippedDuplicates++;
              continue;
            }
            processedUseTypes.add(nsfField.use_type);
          }
          
          const nsfRepository = AppDataSource.getRepository(DealNsfField);
          
          const nsfData = {
            dealId: (savedDeal as any).dealId,
            sourceType: nsfField.source_type || null,
            useType: nsfField.use_type || null,
            dealType: nsfField.deal_type || null,
            nsfContext: nsfField.nsf_context || null,
            capitalPosition: nsfField.capital_position || null,
            // Core NSF measurements moved to properties table
            // gsfGrossSquareFoot: nsfField.gsf_gross_square_foot || null,
            // zfaZoningFloorArea: nsfField.zfa_zoning_floor_area || null,
            // totalNsfNetSquareFoot: nsfField.total_nsf_net_square_foot || null,
            amount: nsfField.amount || null,
            amountPerGsf: nsfField.amount_per_gsf || null,
            amountPerNsf: nsfField.amount_per_nsf || null,
            amountPerZfa: nsfField.amount_per_zfa || null,
            percentageOfTotal: nsfField.percentage_of_total || null,
            isRequired: nsfField.is_required || false,
            additionalInfo: nsfField.additional_info ? JSON.stringify(nsfField.additional_info) : null
          };
          
          const nsfRecord = nsfRepository.create(nsfData);
          await nsfRepository.save(nsfRecord);
        }
        
        this.log("info", `✅ Successfully saved ${processedNsfFields.length - skippedDuplicates} NSF fields to database (skipped ${skippedDuplicates} duplicates)`);
      } else {
        this.log("warn", "No NSF fields to save");
      }
      
      return savedDeal.dealId;
      
    } catch (error) {
      this.log("error", `Error saving to database: ${error}`);
      throw error;
    } finally {
      if (AppDataSource.isInitialized) {
        await AppDataSource.destroy();
      }
    }
  }

  private determineDocumentType(fileContents: Array<{ fileName: string; content: string; type: string }>): string {
    if (fileContents.length === 0) return "unknown";
    
    const types = fileContents.map(f => f.type);
    if (types.includes("application/pdf")) return "pdf";
    if (types.includes("text/csv")) return "spreadsheet";
    if (types.includes("text/plain")) return "document";
    
    return "mixed";
  }

  private getMimeType(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    switch (ext) {
      case ".pdf": return "application/pdf";
      case ".xlsx": return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      case ".xls": return "application/vnd.ms-excel";
      case ".csv": return "text/csv";
      case ".docx": return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
      case ".doc": return "application/msword";
      default: return "application/octet-stream";
    }
  }

  private validateParsedData(parsedData: any): void {
    // Ensure the parsed data has the expected structure
    if (!parsedData || typeof parsedData !== 'object') {
      throw new Error('Parsed data must be an object');
    }

    // Check for required top-level fields - AI returns flat structure
    if (!parsedData.deal_name && !parsedData.summary && !parsedData.ask_capital_position) {
      throw new Error('Parsed data must contain deal_name, summary, or ask_capital_position');
    }

    // Ensure property exists (can be null)
    if (parsedData.property === undefined) {
      parsedData.property = null;
    }

    // Ensure nsf_fields exists and is an array
    if (!Array.isArray(parsedData.nsf_fields)) {
      parsedData.nsf_fields = [];
    }

    this.log("info", "Data structure validation passed - flat structure detected");
  }

  /**
   * Calculate and cross-verify per-square-foot metrics for Sources/Uses data
   * @param nsfFields Array of NSF field data from AI extraction
   * @param askCapitalPosition Array of required capital positions from the deal
   * @returns Processed NSF fields with calculated and validated metrics
   */
  private calculateAndValidateMetrics(nsfFields: any[], askCapitalPosition: string[] | null): any[] {
    if (!nsfFields || nsfFields.length === 0) {
      return [];
    }

    this.log("info", `Calculating and validating metrics for ${nsfFields.length} NSF fields`);

    // Apply Capital Stack Hierarchy Business Logic
    const processedFields = this.applyCapitalStackHierarchy(nsfFields);

    // Calculate and validate isRequired field
    const fieldsWithIsRequired = this.calculateAndValidateIsRequired(processedFields, askCapitalPosition);

    // Calculate total deal amount from sources after hierarchy processing
    const sources = fieldsWithIsRequired.filter(field => field.nsf_context === 'sources');
    const uses = fieldsWithIsRequired.filter(field => field.nsf_context === 'uses_total');
    
    const totalSourcesAmount = sources.reduce((sum, field) => sum + (field.amount || 0), 0);
    const totalUsesAmount = uses.reduce((sum, field) => sum + (field.amount || 0), 0);

    this.log("info", `Total Sources Amount: $${totalSourcesAmount.toLocaleString()}`);
    this.log("info", `Total Uses Amount: $${totalUsesAmount.toLocaleString()}`);

    // Validate that sources and uses amounts match (within 1% tolerance)
    const difference = Math.abs(totalSourcesAmount - totalUsesAmount);
    const tolerance = Math.max(totalSourcesAmount, totalUsesAmount) * 0.01; // 1% tolerance
    
    if (difference > tolerance) {
      this.log("warn", `Sources and Uses amounts don't match: Sources=${totalSourcesAmount}, Uses=${totalUsesAmount}, Difference=${difference}`);
    } else {
      this.log("info", "✅ Sources and Uses amounts match within tolerance");
    }

    // Core NSF measurements moved to properties table - these calculations need to be updated
    // to fetch values from the property instead of individual NSF fields
    // const firstFieldWithSqFt = fieldsWithIsRequired.find(field => 
    //   field.gsf_gross_square_foot || field.total_nsf_net_square_foot || field.zfa_zoning_floor_area
    // );

    // const gsf = firstFieldWithSqFt?.gsf_gross_square_foot || 0;
    // const nsf = firstFieldWithSqFt?.total_nsf_net_square_foot || 0;
    // const zfa = firstFieldWithSqFt?.zfa_zoning_floor_area || 0;

    // this.log("info", `Square Footage - GSF: ${gsf.toLocaleString()}, NSF: ${nsf.toLocaleString()}, ZFA: ${zfa.toLocaleString()}`);

    // Core NSF measurements moved to properties table - this calculation logic needs to be updated
    // to fetch values from the property instead of individual NSF fields
    // For now, return the fields as-is without per-square-foot calculations
    
    this.log("info", "⚠️ NSF field processing temporarily disabled - needs update for property-based measurements");
    
    const finalProcessedFields = fieldsWithIsRequired.map((field, index) => {
      // Return field as-is for now
      return {
        ...field,
        // Per-square-foot calculations will be added back when property integration is complete
        amount_per_gsf: field.amount_per_gsf || 0,
        amount_per_nsf: field.amount_per_nsf || 0,
        amount_per_zfa: field.amount_per_zfa || 0
      };
    });

    return finalProcessedFields;
  }

  /**
   * Apply Capital Stack Hierarchy Business Logic
   * Implements the rules for GP/LP/Common Equity relationships and structural roles
   * @param nsfFields Array of NSF field data from AI extraction
   * @returns Processed NSF fields with applied business logic
   */
  private applyCapitalStackHierarchy(nsfFields: any[]): any[] {
    this.log("info", "Applying Capital Stack Hierarchy Business Logic");

    // Step 1: Determine the Common Equity and its Sources
    const sources = nsfFields.filter(field => field.nsf_context === 'sources');
    
    // Find GP and LP values
    const gpField = sources.find(field => 
      field.capital_position === 'General Partner (GP)' || 
      field.sourceType === 'General Partner (GP)' ||
      field.deal_type === 'equity' && field.capital_position === 'GP Equity'
    );
    
    const lpField = sources.find(field => 
      field.capital_position === 'Limited Partner (LP)' || 
      field.sourceType === 'Limited Partner (LP)' ||
      field.deal_type === 'equity' && field.capital_position === 'LP Equity'
    );
    
    const commonEquityField = sources.find(field => 
      field.capital_position === 'Common Equity' || 
      field.sourceType === 'Common Equity' ||
      field.deal_type === 'equity' && field.capital_position === 'Common Equity'
    );

    // Find structural role fields (Joint Venture, Co-GP) - these are informational only
    const jointVentureField = sources.find(field => 
      field.capital_position === 'Joint Venture' || 
      field.sourceType === 'Joint Venture' ||
      field.deal_type === 'equity' && field.capital_position === 'Joint Venture'
    );
    
    const coGpField = sources.find(field => 
      field.capital_position === 'Co-GP' || 
      field.sourceType === 'Co-GP' ||
      field.deal_type === 'equity' && field.capital_position === 'Co-GP'
    );

    // Log initial state
    this.log("debug", `Initial Capital Stack Analysis:`);
    this.log("debug", `  GP Field: ${gpField ? `$${gpField.amount?.toLocaleString()}` : 'Not found'}`);
    this.log("debug", `  LP Field: ${lpField ? `$${lpField.amount?.toLocaleString()}` : 'Not found'}`);
    this.log("debug", `  Common Equity Field: ${commonEquityField ? `$${commonEquityField.amount?.toLocaleString()}` : 'Not found'}`);
    this.log("debug", `  Joint Venture Field: ${jointVentureField ? `$${jointVentureField.amount?.toLocaleString()}` : 'Not found'}`);
    this.log("debug", `  Co-GP Field: ${coGpField ? `$${coGpField.amount?.toLocaleString()}` : 'Not found'}`);

    // Apply Rule of Precedence: If GP and LP are provided, use them to calculate Common Equity
    let processedFields = [...nsfFields];
    
    if (gpField && lpField) {
      this.log("info", "✅ GP and LP values found - applying Rule of Precedence");
      
      const gpAmount = gpField.amount || 0;
      const lpAmount = lpField.amount || 0;
      const calculatedCommonEquity = gpAmount + lpAmount;
      
      this.log("info", `Calculated Common Equity: GP($${gpAmount.toLocaleString()}) + LP($${lpAmount.toLocaleString()}) = $${calculatedCommonEquity.toLocaleString()}`);
      
      // If there's a separate Common Equity field, flag it as redundant
      if (commonEquityField) {
        const commonEquityAmount = commonEquityField.amount || 0;
        const difference = Math.abs(calculatedCommonEquity - commonEquityAmount);
        const tolerance = Math.max(calculatedCommonEquity, commonEquityAmount) * 0.01; // 1% tolerance
        
        if (difference > tolerance) {
          this.log("warn", `⚠️ Common Equity discrepancy: Calculated($${calculatedCommonEquity.toLocaleString()}) vs Provided($${commonEquityAmount.toLocaleString()}) - Using calculated value`);
        } else {
          this.log("info", "✅ Common Equity values match within tolerance");
        }
        
        // Update the Common Equity field with calculated value
        commonEquityField.amount = calculatedCommonEquity;
        commonEquityField._businessLogic = {
          applied_rule: "Rule of Precedence",
          calculated_from: "GP + LP",
          original_value: commonEquityAmount,
          calculated_value: calculatedCommonEquity
        };
      } else {
        // Create a new Common Equity field
        const newCommonEquityField = {
          ...gpField,
          id: undefined, // Will be assigned by database
          capital_position: 'Common Equity',
          sourceType: 'Common Equity',
          deal_type: 'equity',
          nsf_context: 'sources',
          amount: calculatedCommonEquity,
          _businessLogic: {
            applied_rule: "Rule of Precedence",
            calculated_from: "GP + LP",
            calculated_value: calculatedCommonEquity
          }
        };
        processedFields.push(newCommonEquityField);
      }
      
    } else if (commonEquityField && !gpField && !lpField) {
      // Apply Standard Derivation Rule: When only Common Equity is known, derive GP/LP
      this.log("info", "✅ Common Equity found, GP/LP not found - applying Standard Derivation Rule");
      
      const commonEquityAmount = commonEquityField.amount || 0;
      const derivedGpAmount = commonEquityAmount * 0.10; // 10%
      const derivedLpAmount = commonEquityAmount * 0.90; // 90%
      
      this.log("info", `Derived GP: $${commonEquityAmount.toLocaleString()} * 0.10 = $${derivedGpAmount.toLocaleString()}`);
      this.log("info", `Derived LP: $${commonEquityAmount.toLocaleString()} * 0.90 = $${derivedLpAmount.toLocaleString()}`);
      
      // Create derived GP field
      const derivedGpField = {
        ...commonEquityField,
        id: undefined,
        capital_position: 'General Partner (GP)',
        sourceType: 'General Partner (GP)',
        deal_type: 'equity',
        nsf_context: 'sources',
        amount: derivedGpAmount,
        _businessLogic: {
          applied_rule: "Standard Derivation Rule",
          calculated_from: "Common Equity * 0.10",
          source_value: commonEquityAmount,
          calculated_value: derivedGpAmount
        }
      };
      
      // Create derived LP field
      const derivedLpField = {
        ...commonEquityField,
        id: undefined,
        capital_position: 'Limited Partner (LP)',
        sourceType: 'Limited Partner (LP)',
        deal_type: 'equity',
        nsf_context: 'sources',
        amount: derivedLpAmount,
        _businessLogic: {
          applied_rule: "Standard Derivation Rule",
          calculated_from: "Common Equity * 0.90",
          source_value: commonEquityAmount,
          calculated_value: derivedLpAmount
        }
      };
      
      processedFields.push(derivedGpField, derivedLpField);
    }

    // Apply Rule for Informational Roles: Remove Joint Venture and Co-GP from financial calculations
    if (jointVentureField || coGpField) {
      this.log("info", "ℹ️ Structural roles found - applying Rule for Informational Roles");
      
      if (jointVentureField) {
        this.log("info", `Joint Venture ($${jointVentureField.amount?.toLocaleString()}) - Marked as informational only`);
        jointVentureField._businessLogic = {
          applied_rule: "Rule for Informational Roles",
          role_type: "structural",
          financial_inclusion: false,
          purpose: "display_only"
        };
      }
      
      if (coGpField) {
        this.log("info", `Co-GP ($${coGpField.amount?.toLocaleString()}) - Marked as informational only`);
        coGpField._businessLogic = {
          applied_rule: "Rule for Informational Roles",
          role_type: "structural",
          financial_inclusion: false,
          purpose: "display_only"
        };
      }
    }

    // Step 2: Calculate Total Debt & Total Equity
    const updatedSources = processedFields.filter(field => field.nsf_context === 'sources');
    
    // Calculate Total Debt (exclude informational roles)
    const debtFields = updatedSources.filter(field => 
      (field.capital_position === 'Senior Debt' || field.sourceType === 'Senior Debt') ||
      (field.capital_position === 'Mezzanine' || field.sourceType === 'Mezzanine') ||
      (field.deal_type === 'debt')
    );
    
    const totalDebt = debtFields.reduce((sum, field) => {
      // Exclude informational roles from financial calculations
      if (field._businessLogic?.financial_inclusion === false) {
        return sum;
      }
      return sum + (field.amount || 0);
    }, 0);
    
    // Calculate Total Equity (exclude informational roles)
    const equityFields = updatedSources.filter(field => 
      (field.capital_position === 'Common Equity' || field.sourceType === 'Common Equity') ||
      (field.capital_position === 'Preferred Equity' || field.sourceType === 'Preferred Equity') ||
      (field.capital_position === 'General Partner (GP)' || field.sourceType === 'General Partner (GP)') ||
      (field.capital_position === 'Limited Partner (LP)' || field.sourceType === 'Limited Partner (LP)') ||
      (field.deal_type === 'equity')
    );
    
    const totalEquity = equityFields.reduce((sum, field) => {
      // Exclude informational roles from financial calculations
      if (field._businessLogic?.financial_inclusion === false) {
        return sum;
      }
      return sum + (field.amount || 0);
    }, 0);

    // Step 3: Calculate Total Project Cost
    const totalProjectCost = totalDebt + totalEquity;
    
    this.log("info", "📊 Capital Stack Summary:");
    this.log("info", `  Total Debt: $${totalDebt.toLocaleString()}`);
    this.log("info", `  Total Equity: $${totalEquity.toLocaleString()}`);
    this.log("info", `  Total Project Cost: $${totalProjectCost.toLocaleString()}`);

    // Add summary metadata to all fields
    processedFields = processedFields.map(field => ({
      ...field,
      _capitalStackSummary: {
        total_debt: totalDebt,
        total_equity: totalEquity,
        total_project_cost: totalProjectCost,
        is_debt_component: debtFields.includes(field),
        is_equity_component: equityFields.includes(field),
        is_informational_only: field._businessLogic?.financial_inclusion === false
      }
    }));

    return processedFields;
  }

  private async createErrorFile(
    fileName: string,
    errorMessage: string,
    rawResponse: string,
    metadata: any
  ): Promise<string> {
    try {
      const errorDir = path.join(process.cwd(), 'error-files');
      if (!fs.existsSync(errorDir)) {
        fs.mkdirSync(errorDir, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const errorFilePath = path.join(errorDir, `${fileName}_${timestamp}.json`);

      const errorData = {
        timestamp: new Date().toISOString(),
        error: errorMessage,
        rawResponse,
        metadata,
        processor: 'DealProcessorV2'
      };

      fs.writeFileSync(errorFilePath, JSON.stringify(errorData, null, 2));
      this.log("info", `Error file created: ${errorFilePath}`);
      return errorFilePath;
    } catch (error) {
      this.log("error", `Failed to create error file: ${error}`);
      return 'error-file-creation-failed';
    }
  }

  /**
   * Link processed files to the deal for display in DealFiles component
   */
  private async linkFilesToDeal(dealId: number, files: Array<{ fileName: string; mimeType: string; buffer: Buffer }>): Promise<void> {
    try {
      this.log("info", `Linking ${files.length} files to deal ${dealId}`);
      
      // Import FileManager
      const { FileManager } = await import("../utils/fileManager");
      
      for (const file of files) {
        try {
          // First, upload the file to the file system
          const uploadRequest = {
            original_name: file.fileName,
            title: `Processing Input: ${file.fileName}`,
            description: `File processed by DealProcessorV2 for deal extraction`,
            uploaded_by: "system",
            upload_source: "processor",
            is_public: false,
            access_level: "private" as const,
            tags: ["deal-processing", "v2"],
            metadata: {
              processor: "DealProcessorV2",
              deal_id: dealId,
              processing_timestamp: new Date().toISOString()
            }
          };

          const options = {
            validate_table_column: false,
            allow_duplicates: true,
            auto_generate_title: false,
            preserve_original_name: true
          };

          // Upload the file
          const { file: uploadedFile } = await FileManager.uploadFile(
            file.buffer,
            uploadRequest,
            options
          );

          // Create file relationship for the uploaded file
          const relationshipRequest = {
            target_table_name: "dealsv2",
            target_column_name: "deal_id",
            target_row_id: dealId.toString(),
            relationship_type: "llm_input",
            relationship_title: `Processing Input: ${file.fileName}`,
            relationship_notes: `File processed by DealProcessorV2 for deal extraction`,
            is_primary: false,
            display_order: 1
          };
          
          // Link the uploaded file to the deal
          await FileManager.createFileRelationship(uploadedFile.file_id, relationshipRequest);
          this.log("info", `✅ Linked file ${file.fileName} (ID: ${uploadedFile.file_id}) to deal ${dealId}`);
        } catch (error) {
          this.log("error", `Failed to link file ${file.fileName} to deal ${dealId}: ${error}`);
        }
      }
      
      this.log("info", `Successfully linked ${files.length} files to deal ${dealId}`);
    } catch (error) {
      this.log("error", `Error linking files to deal ${dealId}: ${error}`);
    }
  }

  /**
   * Save the complete prompt being sent to the LLM for debugging (like V1 processor)
   */
  private async savePromptLog(
    prompt: string,
    files: Array<{ fileName: string; mimeType: string; buffer: Buffer }>,
    combinedContent: string
  ): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      
      // Truncate filenames to prevent ENAMETOOLONG error
      const sanitizedFileNames = files.map(f => {
        const sanitized = f.fileName.replace(/[^a-zA-Z0-9.-]/g, "_");
        // Limit each filename to 50 characters to prevent extremely long names
        return sanitized.length > 50 ? sanitized.substring(0, 50) + "..." : sanitized;
      }).join("_");
      
      // Limit total filename length to prevent ENAMETOOLONG error
      const maxFileNameLength = 100; // Conservative limit
      let finalFileName = sanitizedFileNames;
      if (finalFileName.length > maxFileNameLength) {
        finalFileName = finalFileName.substring(0, maxFileNameLength) + "...";
      }
      
      const promptFileName = `prompt-v2-${finalFileName}-${timestamp}.json`;
      const promptDir = path.join(process.cwd(), "prompt-logs");
      
      // Ensure prompt logs directory exists
      if (!fs.existsSync(promptDir)) {
        fs.mkdirSync(promptDir, { recursive: true });
      }
      
      const promptFilePath = path.join(promptDir, promptFileName);
      
      const promptData = {
        timestamp: new Date().toISOString(),
        processor: "DealProcessorV2",
        model: this.selectedModel,
        fileNames: files.map(f => f.fileName),
        fileCount: files.length,
        fileTypes: files.map(f => f.mimeType),
        prompt: prompt,
        combinedContentLength: combinedContent.length,
        combinedContentPreview: combinedContent.substring(0, 500) + (combinedContent.length > 500 ? "..." : ""),
        totalFiles: files.length
      };
      
      fs.writeFileSync(promptFilePath, JSON.stringify(promptData, null, 2));
      this.log("info", `Prompt details saved to: ${promptFilePath}`);
      
      return promptFilePath;
    } catch (err) {
      this.log("error", `Failed to create prompt log file: ${err}`);
      return `Failed to create prompt log file: ${err}`;
    }
  }

  /**
   * Save the complete LLM response for debugging (includes the full raw AI output)
   */
  private async saveResponseLog(
    llmResponse: LLMResponse,
    files: Array<{ fileName: string; mimeType: string; buffer: Buffer }>
  ): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      
      // Truncate filenames to prevent ENAMETOOLONG error
      const sanitizedFileNames = files.map(f => {
        const sanitized = f.fileName.replace(/[^a-zA-Z0-9.-]/g, "_");
        // Limit each filename to 50 characters to prevent extremely long names
        return sanitized.length > 50 ? sanitized.substring(0, 50) + "..." : sanitized;
      }).join("_");
      
      // Limit total filename length to prevent ENAMETOOLONG error
      const maxFileNameLength = 100; // Conservative limit
      let finalFileName = sanitizedFileNames;
      if (finalFileName.length > maxFileNameLength) {
        finalFileName = finalFileName.substring(0, maxFileNameLength) + "...";
      }
      
      const responseFileName = `response-v2-${finalFileName}-${timestamp}.json`;
      const responseDir = path.join(process.cwd(), "prompt-logs");
      
      // Ensure response logs directory exists
      if (!fs.existsSync(responseDir)) {
        fs.mkdirSync(responseDir, { recursive: true });
      }
      
      const responseFilePath = path.join(responseDir, responseFileName);
      
      const responseData = {
        timestamp: new Date().toISOString(),
        processor: "DealProcessorV2",
        model: this.selectedModel,
        fileNames: files.map(f => f.fileName),
        fileCount: files.length,
        responseLength: llmResponse.content.length,
        rawResponse: llmResponse.content, // THE COMPLETE RAW AI OUTPUT
        responsePreview: llmResponse.content.substring(0, 1000) + (llmResponse.content.length > 1000 ? "..." : ""),
        metadata: {
          totalFiles: files.length,
          fileTypes: files.map(f => f.mimeType)
        }
      };
      
      fs.writeFileSync(responseFilePath, JSON.stringify(responseData, null, 2));
      this.log("info", `Complete LLM response saved to: ${responseFilePath}`);
      
      return responseFilePath;
    } catch (err) {
      this.log("error", `Failed to create response log file: ${err}`);
      return `Failed to create response log file: ${err}`;
    }
  }

  // Required abstract method implementations (not used for single-file processing)
  async getUnprocessedEntities(): Promise<EntityData[]> {
    return [];
  }

  async processEntity(entity: EntityData): Promise<{ success: boolean; error?: string }> {
    return { success: false, error: "Not implemented for single-file processing" };
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    // Not used for single-file processing
  }

  // Static method to get the appropriate text extractor
  static getExtractor(mimeType: string): AbstractTextExtractor | null {
    switch (mimeType) {
      case "application/pdf":
        return new PDFTextExtractor();
      case "text/csv":
        return new CSVTextExtractor();
      case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
      case "application/vnd.ms-excel":
        return new XLSXTextExtractor();
      case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
      case "application/msword":
        return new WordTextExtractor();
      default:
        return null;
    }
  }

  /**
   * Save the parsed JSON exactly as parsed from LLM (no transformations)
   */
  private async saveParsedJson(
    parsedData: any,
    files: Array<{ fileName: string; mimeType: string; buffer: Buffer }>
  ): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      // Truncate filenames to avoid ENAMETOOLONG errors
      const sanitizedFileNames = files.map(f => f.fileName.replace(/[^a-zA-Z0-9.-]/g, "_")).join("_");
      const truncatedFileNames = sanitizedFileNames.length > 100 ? sanitizedFileNames.substring(0, 100) + "_truncated" : sanitizedFileNames;
      const parsedJsonFileName = `parsed-json-v2-${truncatedFileNames}-${timestamp}.json`;
      const parsedJsonDir = path.join(process.cwd(), "prompt-logs");
      
      // Ensure parsed JSON logs directory exists
      if (!fs.existsSync(parsedJsonDir)) {
        fs.mkdirSync(parsedJsonDir, { recursive: true });
      }
      
      const parsedJsonFilePath = path.join(parsedJsonDir, parsedJsonFileName);
      
      // Save ONLY the parsed JSON data, exactly as parsed (no metadata, no transformations)
      const jsonContent = JSON.stringify(parsedData, null, 2);
      fs.writeFileSync(parsedJsonFilePath, jsonContent);
      this.log("info", `Parsed JSON saved to: ${parsedJsonFilePath}`);
      
      return parsedJsonFilePath;
    } catch (err) {
      this.log("error", `Failed to create parsed JSON file: ${err}`);
      return `Failed to create parsed JSON file: ${err}`;
    }
  }

  /**
   * Fetch central mapping data from the database for enhanced prompt generation
   * @returns Object containing mapping categories and their values
   */
  private async fetchCentralMappings(): Promise<{ [key: string]: string[] }> {
    try {
      this.log("info", "Fetching central mapping data for prompt enhancement");
      
      // Only initialize if not already connected
      if (!AppDataSource.isInitialized) {
        await AppDataSource.initialize();
      }
      
      // Fetch all central mappings using correct column names
      const mappingsResult = await AppDataSource.query(`
        SELECT 
          type as mapping_category,
          value_1 as mapping_value,
          value_2 as mapping_value_2,
          value_3 as mapping_value_3,
          level_1,
          level_2,
          level_3
        FROM central_mapping
        WHERE is_active = true
        ORDER BY type, level_1, value_1
      `);
      
      // Group mappings by category
      const mappings: { [key: string]: string[] } = {};
      
      for (const row of mappingsResult) {
        const category = row.mapping_category;
        const value1 = row.mapping_value;
        const value2 = row.mapping_value_2;
        const value3 = row.mapping_value_3;
        
        if (!mappings[category]) {
          mappings[category] = [];
        }
        
        // Add value_1 if it exists and isn't already in the array
        if (value1 && !mappings[category].includes(value1)) {
          mappings[category].push(value1);
        }
        
        // Add value_2 if it exists and isn't already in the array
        if (value2 && !mappings[category].includes(value2)) {
          mappings[category].push(value2);
        }
        
        // Add value_3 if it exists and isn't already in the array
        if (value3 && !mappings[category].includes(value3)) {
          mappings[category].push(value3);
        }
      }
      
      this.log("info", `Fetched ${Object.keys(mappings).length} mapping categories with ${mappingsResult.length} total values`);
      
      // Log some examples for debugging
      Object.entries(mappings).forEach(([category, values]) => {
        this.log("debug", `  ${category}: ${values.length} values (${values.slice(0, 3).join(", ")}${values.length > 3 ? "..." : ""})`);
      });
      
      return mappings;
      
    } catch (error) {
      this.log("warn", `Failed to fetch central mappings: ${error}. Using empty mappings.`);
      return {};
    }
  }

  /**
   * Determine if a capital position is required based on ask_capital_position
   * @param capitalPosition The capital position to check
   * @param askCapitalPosition Array of required capital positions from the deal
   * @returns boolean indicating if the position is required
   */
  private isCapitalPositionRequired(capitalPosition: string | null, askCapitalPosition: string[] | null): boolean {
    if (!capitalPosition || !askCapitalPosition || askCapitalPosition.length === 0) {
      return false;
    }
    
    return askCapitalPosition.includes(capitalPosition);
  }

  /**
   * Calculate and validate isRequired field for NSF fields
   * @param nsfFields Array of NSF field data
   * @param askCapitalPosition Array of required capital positions from the deal
   * @returns Processed NSF fields with validated isRequired field
   */
  private calculateAndValidateIsRequired(nsfFields: any[], askCapitalPosition: string[] | null): any[] {
    this.log("info", `Calculating and validating isRequired for ${nsfFields.length} NSF fields`);
    
    if (!askCapitalPosition || askCapitalPosition.length === 0) {
      this.log("warn", "No ask_capital_position found - all positions will be marked as not required");
      return nsfFields.map(field => ({
        ...field,
        isRequired: false
      }));
    }

    this.log("info", `Ask Capital Positions: ${askCapitalPosition.join(", ")}`);

    const processedFields = nsfFields.map((field, index) => {
      // Determine the capital position to check
      const capitalPosition = field.capital_position || field.sourceType || null;
      
      // Calculate isRequired
      const isRequired = this.isCapitalPositionRequired(capitalPosition, askCapitalPosition);
      
      // Log the calculation for debugging
      this.log("debug", `Field ${index + 1}: capitalPosition="${capitalPosition}", isRequired=${isRequired}`);
      
      // Validate that required fields have proper data
      if (isRequired) {
        if (!capitalPosition) {
          this.log("warn", `⚠️ Required field ${index + 1} missing capital position`);
        }
        if (!field.amount || field.amount === 0) {
          this.log("warn", `⚠️ Required field ${index + 1} missing or zero amount`);
        }
      }

      return {
        ...field,
        isRequired,
        _validation: {
          capital_position_checked: capitalPosition,
          ask_capital_positions: askCapitalPosition,
          is_required_calculated: isRequired,
          validation_notes: isRequired && (!capitalPosition || !field.amount) ? "Required field missing critical data" : null
        }
      };
    });

    // Summary validation
    const requiredFields = processedFields.filter(field => field.isRequired);
    const missingRequiredData = requiredFields.filter(field => 
      !field.capital_position && !field.sourceType || !field.amount || field.amount === 0
    );

    this.log("info", `📊 IsRequired Summary:`);
    this.log("info", `  Total fields: ${processedFields.length}`);
    this.log("info", `  Required fields: ${requiredFields.length}`);
    this.log("info", `  Fields missing required data: ${missingRequiredData.length}`);

    if (missingRequiredData.length > 0) {
      this.log("warn", `⚠️ ${missingRequiredData.length} required fields are missing critical data`);
    } else if (requiredFields.length > 0) {
      this.log("info", "✅ All required fields have proper data");
    }

    return processedFields;
  }

}