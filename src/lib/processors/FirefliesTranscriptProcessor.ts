import { pool } from "@/lib/db";

interface FirefliesAccount {
  id: string;
  name: string;
  api_key: string;
  last_sync_at?: string;
  status: string;
}

interface FirefliesTranscript {
  id: string;
  title: string;
  date: string;
  duration: number; // Duration in minutes (already correct from API)
  participants: string[];
  sentences: Array<{
    text: string;
    raw_text: string;
    speaker_name: string;
    speaker_id: string;
    start_time: number;
    end_time: number;
  }>;
  metadata?: any;
}

export class FirefliesTranscriptProcessor {
  private accounts: FirefliesAccount[] = [];

  constructor() {}

  async fetchAccounts(): Promise<FirefliesAccount[]> {
    const result = await pool.query(
      "SELECT id, name, api_key, last_sync_at, status FROM fireflies_accounts WHERE status = 'active' ORDER BY name ASC"
    );
    this.accounts = result.rows;
    console.log(`[FirefliesProcessor] Found ${this.accounts.length} active accounts:`, 
      this.accounts.map(acc => ({ id: acc.id, name: acc.name, hasApiKey: !!acc.api_key }))
    );
    return this.accounts;
  }

  async fetchTranscriptsForAccount(account: FirefliesAccount): Promise<FirefliesTranscript[]> {
    const apiKey = account.api_key;
    
    if (!apiKey || apiKey.trim() === '') {
      throw new Error(`Invalid API key for account ${account.name}`);
    }
    
    const baseUrl = "https://api.fireflies.ai/graphql";
    
    // First, let's try to introspect the schema to see what's available
    const introspectionQuery = `
      query IntrospectionQuery {
        __schema {
          queryType {
            name
            fields {
              name
              type {
                name
              }
            }
          }
        }
      }
    `;
    
    // GraphQL query to fetch transcripts using the correct API structure
    // Based on the official Fireflies API schema
    const query = `
      query GetTranscripts($limit: Int) {
        transcripts(limit: $limit) {
          id
          title
          date
          duration
          participants
          sentences {
            text
            raw_text
            speaker_name
            speaker_id
            start_time
            end_time
          }
        }
      }
    `;

    try {
      console.log(`[FirefliesProcessor] Fetching transcripts for account: ${account.name}`);
      console.log(`[FirefliesProcessor] API Key length: ${apiKey ? apiKey.length : 0}`);
      
      // First, let's try to introspect to see what queries are available
      console.log(`[FirefliesProcessor] Testing API with introspection...`);
      const introspectionResponse = await fetch(baseUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
        },
        body: JSON.stringify({ query: introspectionQuery }),
      });
      
      if (introspectionResponse.ok) {
        const introspectionData = await introspectionResponse.json();
        console.log(`[FirefliesProcessor] Available queries:`, 
          introspectionData.data?.__schema?.queryType?.fields?.map((f: any) => f.name) || []
        );
      } else {
        console.log(`[FirefliesProcessor] Introspection failed, proceeding with transcript query...`);
      }
      
      // Try the main query first
      const requestBody = {
        query,
        variables: {
          limit: 50,
          offset: 0,
        },
      };

      console.log(`[FirefliesProcessor] Request body:`, JSON.stringify(requestBody, null, 2));

      const response = await fetch(baseUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
        },
        body: JSON.stringify(requestBody),
      });

      console.log(`[FirefliesProcessor] Response status: ${response.status}`);
      console.log(`[FirefliesProcessor] Response headers:`, Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[FirefliesProcessor] Error response body:`, errorText);
        throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
      }

      const data = await response.json();
      console.log(`[FirefliesProcessor] Response data keys:`, Object.keys(data));
      
      if (data.errors) {
        console.error(`[FirefliesProcessor] GraphQL errors:`, data.errors);
        throw new Error(`GraphQL errors: ${JSON.stringify(data.errors)}`);
      }

      if (!data.data || !data.data.transcripts) {
        console.error(`[FirefliesProcessor] Unexpected response structure:`, data);
        console.log(`[FirefliesProcessor] Available data keys:`, Object.keys(data.data || {}));
        throw new Error(`Unexpected response structure: ${JSON.stringify(data)}`);
      }

      console.log(`[FirefliesProcessor] Found ${data.data.transcripts.length} transcripts`);

      // Log the first transcript structure for debugging
      if (data.data.transcripts.length > 0) {
        console.log(`[FirefliesProcessor] Sample transcript structure:`, JSON.stringify(data.data.transcripts[0], null, 2));
      }

      return data.data.transcripts.map((transcript: any) => ({
        id: transcript.id,
        title: transcript.title,
        date: transcript.date,
        duration: transcript.duration, // Already in minutes from API
        participants: transcript.participants || [],
        sentences: transcript.sentences || [],
        metadata: transcript,
      }));
    } catch (error) {
      console.error(`[FirefliesProcessor] Error fetching transcripts for account ${account.name}:`, error);
      throw error;
    }
  }

  async processAndStoreTranscript(
    transcript: FirefliesTranscript,
    account: FirefliesAccount
  ) {
    const providerTranscriptId = transcript.id;
    const title = transcript.title;
    console.log(`[FirefliesTranscriptProcessor] Raw date value:`, transcript.date, `type:`, typeof transcript.date);
    const meetingDate = new Date(transcript.date);
    console.log(`[FirefliesTranscriptProcessor] Parsed meeting date:`, meetingDate);
    // Duration is already in minutes from the API, but may be decimal - convert to integer
    console.log(`[FirefliesTranscriptProcessor] Raw duration value:`, transcript.duration, `type:`, typeof transcript.duration);
    const duration = Math.round(transcript.duration);
    console.log(`[FirefliesTranscriptProcessor] Duration: ${transcript.duration}m -> ${duration}m (rounded) for transcript ${transcript.id}`);
    const participants = transcript.participants;
    
    // Store sentences as JSONB array (per Fireflies schema)
    const sentencesData = transcript.sentences || [];
    
    // Debug: Log a sample sentence to see the structure
    if (sentencesData.length > 0) {
      console.log(`[FirefliesTranscriptProcessor] Sample sentence structure:`, JSON.stringify(sentencesData[0], null, 2));
    }
    
    // Ensure sentences data is properly serializable for JSONB
    let sentencesJson;
    try {
      // Clean the sentences data to ensure it's valid JSON
      const cleanedSentences = sentencesData.map((s: any) => ({
        index: typeof s.index === 'number' ? s.index : 0,
        text: String(s.text || '').replace(/[\u0000-\u001F\u007F-\u009F]/g, ''), // Remove control characters
        raw_text: String(s.raw_text || '').replace(/[\u0000-\u001F\u007F-\u009F]/g, ''),
        start_time: String(s.start_time || ''),
        end_time: String(s.end_time || ''),
        speaker_id: String(s.speaker_id || ''),
        speaker_name: String(s.speaker_name || '').replace(/[\u0000-\u001F\u007F-\u009F]/g, ''),
        ai_filters: s.ai_filters || null
      }));
      
      sentencesJson = JSON.stringify(cleanedSentences);
      console.log(`[FirefliesTranscriptProcessor] Sentences JSON serialization successful, length: ${sentencesJson.length}`);
    } catch (jsonError) {
      console.error(`[FirefliesTranscriptProcessor] JSON serialization failed:`, jsonError);
      console.log(`[FirefliesTranscriptProcessor] Using empty array for sentences`);
      sentencesJson = JSON.stringify([]);
    }
    
    // Create transcript_text from sentences for searchability
    const transcriptText = sentencesData.map((s: any) => s.text || '').join(' ');
    
    const metadata = JSON.stringify(transcript.metadata || {});
    const now = new Date();

    // Upsert transcript by provider_transcript_id and account_id
    try {
      console.log(`[FirefliesTranscriptProcessor] Inserting transcript with values:`, {
        providerTranscriptId,
        accountId: account.id,
        title,
        meetingDate,
        duration,
        participantsCount: participants?.length || 0,
        transcriptTextLength: transcriptText?.length || 0,
        sentencesCount: transcript.sentences?.length || 0
      });

      const transcriptResult = await pool.query(
        `INSERT INTO fireflies_transcripts (
          provider_transcript_id, account_id, title, meeting_date, duration, 
          participants, transcript_text, sentences, metadata, created_at, updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8::jsonb, $9::jsonb, $10, $11)
        ON CONFLICT (provider_transcript_id, account_id) DO UPDATE SET 
          title = EXCLUDED.title,
          meeting_date = EXCLUDED.meeting_date,
          duration = EXCLUDED.duration,
          participants = EXCLUDED.participants,
          transcript_text = EXCLUDED.transcript_text,
          sentences = EXCLUDED.sentences,
          metadata = EXCLUDED.metadata,
          updated_at = EXCLUDED.updated_at
        RETURNING id`,
        [
          providerTranscriptId,
          account.id,
          title,
          meetingDate,
          duration,
          participants,
          transcriptText,
          sentencesJson, // Store sentences as JSONB array
          metadata,
          now,
          now,
        ]
      );

      console.log(`[FirefliesTranscriptProcessor] Successfully inserted transcript with ID:`, transcriptResult.rows[0].id);
      return transcriptResult.rows[0].id;
    } catch (error) {
      console.error(`[FirefliesTranscriptProcessor] Database error for transcript ${transcript.id}:`, error);
      console.error(`[FirefliesTranscriptProcessor] Values that caused error:`, {
        providerTranscriptId,
        accountId: account.id,
        title,
        meetingDate,
        duration,
        participants,
        transcriptTextLength: transcriptText?.length || 0,
        sentencesCount: transcript.sentences?.length || 0
      });
      throw error;
    }
  }

  async updateAccountSyncStatus(accountId: string, success: boolean, error?: string) {
    const now = new Date();
    
    if (success) {
      await pool.query(
        `UPDATE fireflies_accounts 
         SET last_sync_at = $1, error_count = 0, last_error = NULL, updated_at = $1
         WHERE id = $2`,
        [now, accountId]
      );
    } else {
      await pool.query(
        `UPDATE fireflies_accounts 
         SET error_count = error_count + 1, last_error = $1, updated_at = $2
         WHERE id = $3`,
        [error || "Unknown error", now, accountId]
      );
    }
  }

  async run() {
    console.log("[FirefliesTranscriptProcessor] Starting Fireflies transcript sync");
    
    const accounts = await this.fetchAccounts();
    console.log(`[FirefliesTranscriptProcessor] Found ${accounts.length} active accounts`);

    for (const account of accounts) {
      try {
        console.log(`[FirefliesTranscriptProcessor] Processing account: ${account.name}`);
        
        const transcripts = await this.fetchTranscriptsForAccount(account);
        console.log(`[FirefliesTranscriptProcessor] Found ${transcripts.length} transcripts for account: ${account.name}`);

        let processedCount = 0;
        for (const transcript of transcripts) {
          try {
            await this.processAndStoreTranscript(transcript, account);
            processedCount++;
          } catch (err) {
            console.error(
              `[FirefliesTranscriptProcessor] Error processing transcript ${transcript.id} for account ${account.name}:`,
              err
            );
          }
        }

        await this.updateAccountSyncStatus(account.id, true);
        console.log(
          `[FirefliesTranscriptProcessor] Successfully processed ${processedCount} transcripts for account: ${account.name}`
        );
      } catch (err) {
        console.error(
          `[FirefliesTranscriptProcessor] Error processing account ${account.name}:`,
          err
        );
        await this.updateAccountSyncStatus(account.id, false, err instanceof Error ? err.message : "Unknown error");
      }
    }

    console.log("[FirefliesTranscriptProcessor] Fireflies transcript sync completed");
  }
}