import { pool } from '../db'
import Bottleneck from 'bottleneck'

export type LogLevel = 'debug' | 'info' | 'warn' | 'error'
import { CompanyData, CompanyProcessingState, ContactProcessingState, EntityData, ProcessorOptions, ProcessorResult, UnifiedEntityData } from '../../types/processing'

// Bottleneck configuration interface for processor-wise control
export interface BottleneckConfig {
  // Rate limiting
  maxConcurrent?: number           // Max concurrent jobs
  minTime?: number                 // Minimum time between job starts (ms)
  highWater?: number              // Queue high water mark
  strategy?: Bottleneck.Strategy   // Queue overflow strategy
  
  // Retry configuration
  retryAttempts?: number          // Number of retry attempts
  retryDelayBase?: number         // Base delay for exponential backoff (ms)
  retryDelayMax?: number          // Maximum retry delay (ms)
  
  // Job priorities
  defaultPriority?: number        // Default job priority (lower = higher priority)
  
  // Timeouts
  timeout?: number                // Job timeout (ms)
  
  // Monitoring
  enableJobMetrics?: boolean      // Enable job execution metrics
}


export abstract class BaseProcessor {
  protected pool: typeof pool
  protected options: ProcessorOptions
  protected bottleneck: Bottleneck
  protected bottleneckConfig: BottleneckConfig

  protected name: string
  private logLevel: LogLevel
  private jobMetrics: Map<string, { startTime: number; endTime?: number; success?: boolean; error?: string }>

  constructor(name: string, options: ProcessorOptions = {}) {
    this.name = name
    this.pool = pool
    this.options = {
      ...options
    }
    
    // Initialize job metrics
    this.jobMetrics = new Map()
    
    // Set log level based on environment
    const envLogLevel = process.env.LOG_LEVEL as LogLevel
    const isProduction = process.env.NODE_ENV === 'production'
    
    // In production, default to 'info' unless explicitly set to debug
    // In development, default to 'debug'
    // Debug logs show timestamps, other levels don't
    const defaultLogLevel: LogLevel = isProduction ? 'info' : 'debug'
    this.logLevel = ['debug', 'info', 'warn', 'error'].includes(envLogLevel) ? envLogLevel : defaultLogLevel
    
    this.log('info', `BaseProcessor constructor called with options: ${JSON.stringify(options)}`)
    
    // Initialize Bottleneck configuration with defaults
    this.bottleneckConfig = this.getDefaultBottleneckConfig()
    
    // Allow processor-specific configuration override
    if (options.bottleneckConfig) {
      this.bottleneckConfig = { ...this.bottleneckConfig, ...options.bottleneckConfig }
    }
    
    // Create Bottleneck instance with comprehensive configuration
    this.bottleneck = new Bottleneck({
      maxConcurrent: this.bottleneckConfig.maxConcurrent,
      minTime: this.bottleneckConfig.minTime,
      highWater: this.bottleneckConfig.highWater,
      strategy: this.bottleneckConfig.strategy,
      // Add timeout and retry configuration to prevent job drops
      timeout: this.bottleneckConfig.timeout,
      retryDelayBase: this.bottleneckConfig.retryDelayBase,
      retryDelayMax: this.bottleneckConfig.retryDelayMax
    })
    
    // Set up Bottleneck event listeners for monitoring
    this.setupBottleneckMonitoring()
    
    this.log('info', `Initialized ${this.name} with Bottleneck config: ${JSON.stringify(this.bottleneckConfig)}`)
  }

  protected async query(sql: string, params: unknown[] = []): Promise<Record<string, unknown>[]> {
    const client = await this.pool.connect()
    try {
      const result = await client.query(sql, params)
      return result.rows
    } finally {
      client.release()
    }
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    }
    return levels[level] >= levels[this.logLevel]
  }

  protected log(level: LogLevel, message: string): void {
    // Only log if the level meets or exceeds the configured log level
    if (!this.shouldLog(level)) {
      return
    }

    // Show timestamp only for debug logs
    const timestamp = level === 'debug' ? `[${new Date().toISOString()}] ` : ''
    console.log(`${timestamp}[${this.name}] [${level.toUpperCase()}] ${message}`)
  }

  /**
   * Get current log level for debugging purposes
   */
  protected getLogLevel(): LogLevel {
    return this.logLevel
  }

  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * @deprecated Use bottleneck.schedule() instead for better rate limiting and concurrency control
   */
  protected async rateLimitedCall<T>(
    fn: () => Promise<T>,
    delayMs: number
  ): Promise<T> {
    this.log('warn', 'rateLimitedCall is deprecated, consider using bottleneck.schedule() instead')
    await this.sleep(delayMs)
    return await fn()
  }

  /**
   * Get default Bottleneck configuration
   * Can be overridden by processors for specific needs
   */
  protected getDefaultBottleneckConfig(): BottleneckConfig {
    return {
      maxConcurrent: parseInt(process.env.PROCESSOR_MAX_CONCURRENT || '3'),
      minTime: parseInt(process.env.PROCESSOR_MIN_TIME || '100'),
      highWater: parseInt(process.env.PROCESSOR_HIGH_WATER || '1000'), // Increased from 50 to handle large queues
      strategy: Bottleneck.strategy.OVERFLOW,  // Changed from LEAK to OVERFLOW for better queue management
      retryAttempts: parseInt(process.env.PROCESSOR_RETRY_ATTEMPTS || '3'),
      retryDelayBase: parseInt(process.env.PROCESSOR_RETRY_DELAY_BASE || '1000'),
      retryDelayMax: parseInt(process.env.PROCESSOR_RETRY_DELAY_MAX || '30000'),
      defaultPriority: 5,
      timeout: parseInt(process.env.PROCESSOR_JOB_TIMEOUT || '180000'), // Reduced to 3 minutes to prevent timeouts
      enableJobMetrics: process.env.PROCESSOR_ENABLE_METRICS === 'true'
    }
  }

  /**
   * Set up Bottleneck monitoring and event listeners
   */
  private setupBottleneckMonitoring(): void {
    if (!this.bottleneckConfig.enableJobMetrics) {
      return
    }

    this.bottleneck.on('message', (message) => {
      this.log('debug', `Bottleneck: ${message}`)
    })

    this.bottleneck.on('failed', (error, jobInfo) => {
      this.log('error', `Job failed: ${error.message}. Job info: ${JSON.stringify(jobInfo)}`)
      
      if (jobInfo.options.id) {
        const metric = this.jobMetrics.get(jobInfo.options.id)
        if (metric) {
          metric.endTime = Date.now()
          metric.success = false
          metric.error = error.message
        }
      }
    })

    this.bottleneck.on('retry', (error: any, jobInfo: any) => {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('warn', `Retrying job due to error: ${errorMessage}. Job info: ${JSON.stringify(jobInfo)}`)
    })

    this.bottleneck.on('done', (jobInfo) => {
      if (jobInfo.options.id) {
        const metric = this.jobMetrics.get(jobInfo.options.id)
        if (metric && !metric.endTime) {
          metric.endTime = Date.now()
          metric.success = true
        }
      }
    })

    this.bottleneck.on('idle', () => {
      this.log('debug', `${this.name} Bottleneck queue is idle`)
    })

    this.bottleneck.on('empty', () => {
      this.log('debug', `${this.name} Bottleneck queue is empty`)
    })
  }

  /**
   * Execute a job with Bottleneck rate limiting and retry logic
   */
  protected async executeWithBottleneck<T>(
    jobFn: () => Promise<T>,
    jobId: string,
    priority?: number,
    retryOnFailure: boolean = true
  ): Promise<T> {
    const startTime = Date.now()
    
    if (this.bottleneckConfig.enableJobMetrics) {
      this.jobMetrics.set(jobId, { startTime })
    }

    const executeJob = async (): Promise<T> => {
      try {
        const result = await this.bottleneck.schedule(
          { 
            priority: priority ?? this.bottleneckConfig.defaultPriority, 
            id: jobId 
          },
          jobFn
        )
        
        this.log('debug', `Job ${jobId} completed successfully in ${Date.now() - startTime}ms`)
        return result
      } catch (error) {
        this.log('error', `Job ${jobId} failed: ${error}`)
        throw error
      }
    }

    if (!retryOnFailure || !this.bottleneckConfig.retryAttempts) {
      return await executeJob()
    }

    // Implement exponential backoff retry logic
    let lastError: Error = new Error('Unknown error')
    
    for (let attempt = 0; attempt <= (this.bottleneckConfig.retryAttempts || 0); attempt++) {
      try {
        return await executeJob()
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))
        
        if (attempt === (this.bottleneckConfig.retryAttempts || 0)) {
          this.log('error', `Job ${jobId} failed after ${attempt + 1} attempts: ${lastError.message}`)
          break
        }

        const delay = Math.min(
          (this.bottleneckConfig.retryDelayBase || 1000) * Math.pow(2, attempt),
          this.bottleneckConfig.retryDelayMax || 30000
        )
        
        this.log('warn', `Job ${jobId} failed on attempt ${attempt + 1}, retrying in ${delay}ms: ${lastError.message}`)
        await this.sleep(delay)
      }
    }

    throw lastError
  }

  /**
   * Get job execution metrics
   */
  protected getJobMetrics(): { [jobId: string]: { duration?: number; success?: boolean; error?: string } } {
    const metrics: { [jobId: string]: { duration?: number; success?: boolean; error?: string } } = {}
    
    Array.from(this.jobMetrics.entries()).forEach(([jobId, metric]) => {
      metrics[jobId] = {
        success: metric.success,
        error: metric.error
      }
      
      if (metric.endTime) {
        metrics[jobId].duration = metric.endTime - metric.startTime
      }
    })
    
    return metrics
  }

  /**
   * Clear job metrics (useful for long-running processors)
   */
  protected clearJobMetrics(): void {
    this.jobMetrics.clear()
    this.log('debug', 'Cleared job metrics')
  }

  /**
   * Cleanup Bottleneck instance and resources
   * Should be called when processor is no longer needed
   */
  protected async cleanup(): Promise<void> {
    try {
      // Stop accepting new jobs and wait for existing jobs to complete
      await this.bottleneck.stop()
      this.log('info', `${this.name} processor cleanup completed`)
    } catch (error) {
      this.log('error', `Error during processor cleanup: ${error}`)
    }
  }

  /**
   * Get current Bottleneck status and metrics
   */
  protected getBottleneckStatus(): {
    running: number
    done: number
    queued: number
    empty: boolean
  } {
    // Note: Some Bottleneck methods may return promises in newer versions
    // For now, using basic status that should be synchronous
    try {
      return {
        running: (this.bottleneck as any)._running || 0,
        done: (this.bottleneck as any)._done || 0,
        queued: (this.bottleneck as any)._queued?.length || 0,
        empty: (this.bottleneck as any)._queued?.length === 0
      }
    } catch (error) {
      this.log('warn', `Could not get bottleneck status: ${error}`)
      return {
        running: 0,
        done: 0,
        queued: 0,
        empty: true
      }
    }
  }

  // Helper method to add date range filter condition
  protected addDateRangeFilter(conditions: string[], params: unknown[], paramIndex: number): number {
    if (this.options.filters?.dateRange && this.options.filters.dateRange !== 'all') {
      const dateRange = this.options.filters.dateRange

      switch (dateRange) {
        case 'today':
          conditions.push(`created_at >= CURRENT_DATE`)
          break
        case 'week':
          conditions.push(`created_at >= CURRENT_DATE - INTERVAL '7 days'`)
          break
        case 'month':
          conditions.push(`created_at >= CURRENT_DATE - INTERVAL '30 days'`)
          break
        case 'quarter':
          conditions.push(`created_at >= CURRENT_DATE - INTERVAL '90 days'`)
          break
      }
    }

    return paramIndex
  }

  // Abstract methods that must be implemented by concrete processors
  abstract getUnprocessedEntities(): Promise<EntityData[]>
  abstract processEntity(entity: EntityData): Promise<{ success: boolean; error?: string }>
  abstract updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void>

  // Main processing loop with proper batching and Bottleneck integration
  async process(): Promise<ProcessorResult> {
    const startTime = Date.now()
    this.log('info', `Starting ${this.name} processing with Bottleneck batching...`)

    const entities = await this.getUnprocessedEntities()
    this.log('info', `Found ${entities.length} entities to process`)

    const results = {
      processed: 0,
      successful: 0,
      failed: 0,
      errors: [] as string[]
    }

    if (entities.length === 0) {
      this.log('info', 'No entities to process')
      return results
    }

    // Clear previous metrics
    if (this.bottleneckConfig.enableJobMetrics) {
      this.clearJobMetrics()
    }

    // Get batch configuration - use smaller batches for better control
    const batchSize = this.options.batchSize || Math.max(10, Math.min(50, Math.ceil(entities.length / 10)))
    const batchDelay = this.options.batchDelay || 500  // 500ms between batches
    const totalBatches = Math.ceil(entities.length / batchSize)

    this.log('info', `Processing ${entities.length} entities in ${totalBatches} batches of ~${batchSize} (max concurrency: ${this.bottleneckConfig.maxConcurrent})`)

    // Process entities in proper batches
    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const batchStart = batchIndex * batchSize
      const batchEnd = Math.min(batchStart + batchSize, entities.length)
      const batch = entities.slice(batchStart, batchEnd)
      const currentBatch = batchIndex + 1
      
      this.log('info', `Processing batch ${currentBatch}/${totalBatches} - ${batch.length} entities`)

      // Create processing jobs for current batch only
      const batchJobs = batch.map((entity, indexInBatch) => {
        const globalIndex = batchStart + indexInBatch
        const jobId = `${this.name}-batch${currentBatch}-entity-${entity.id}-${Date.now()}-${globalIndex}`
        
        return this.executeWithBottleneck(
          async () => {
            this.log('debug', `Processing ${this.name} entity ${entity.id} [${globalIndex + 1}/${entities.length}] in batch ${currentBatch}`)
            
            const result = await this.processEntity(entity)
            await this.updateEntityStatus(entity.id, result.success, result.error)
            
            return { entity, result, globalIndex: globalIndex + 1, batchIndex: currentBatch }
          },
          jobId,
          this.bottleneckConfig.defaultPriority,
          true // Enable retry
        )
      })

      // Process current batch with Bottleneck managing concurrency within the batch
      const batchResults = await Promise.allSettled(batchJobs)

      // Process batch results
      for (let i = 0; i < batchResults.length; i++) {
        const jobResult = batchResults[i]
        const entity = batch[i]
        const globalIndex = batchStart + i
        
        results.processed++
        
        if (jobResult.status === 'fulfilled') {
          const { result, batchIndex } = jobResult.value
          
          if (result.success) {
            results.successful++
            this.log('debug', `Successfully processed entity ${entity.id} [${globalIndex + 1}/${entities.length}] in batch ${batchIndex}`)
          } else {
            results.failed++
            if (result.error) {
              results.errors.push(result.error)
            }
            this.log('warn', `Failed to process entity ${entity.id} [${globalIndex + 1}/${entities.length}] in batch ${batchIndex}: ${result.error || 'Unknown error'}`)
          }
        } else {
          results.failed++
          const errorMessage = jobResult.reason instanceof Error ? jobResult.reason.message : String(jobResult.reason)
          results.errors.push(errorMessage)
          this.log('error', `Error processing entity ${entity.id} [${globalIndex + 1}/${entities.length}] in batch ${currentBatch}: ${errorMessage}`)

          // Try to update entity status on failure
          try {
            await this.updateEntityStatus(entity.id, false, errorMessage)
          } catch (updateError) {
            this.log('error', `Failed to update entity status: ${updateError}`)
          }
        }
      }

      // Log batch completion
      const batchSuccessful = batchResults.filter(r => r.status === 'fulfilled' && r.value.result.success).length
      const batchFailed = batchResults.filter(r => r.status === 'rejected' || (r.status === 'fulfilled' && !r.value.result.success)).length
      this.log('info', `Batch ${currentBatch}/${totalBatches} completed: ${batchSuccessful} successful, ${batchFailed} failed`)

      // Log current Bottleneck status
      const bottleneckStatus = this.getBottleneckStatus()
      this.log('debug', `Bottleneck status after batch ${currentBatch}: ${JSON.stringify(bottleneckStatus)}`)

      // Wait between batches (except for the last batch)
      if (batchIndex < totalBatches - 1 && batchDelay > 0) {
        this.log('debug', `Waiting ${batchDelay}ms before next batch...`)
        await this.sleep(batchDelay)
      }
    }

    const duration = Date.now() - startTime
    
    // Log metrics if enabled
    if (this.bottleneckConfig.enableJobMetrics) {
      const metrics = this.getJobMetrics()
      const metricsSummary = {
        totalJobs: Object.keys(metrics).length,
        successfulJobs: Object.values(metrics).filter(m => m.success).length,
        failedJobs: Object.values(metrics).filter(m => m.success === false).length,
        averageDuration: Object.values(metrics)
          .filter(m => m.duration)
          .reduce((sum, m) => sum + (m.duration || 0), 0) / Object.values(metrics).filter(m => m.duration).length || 0
      }
      
      this.log('info', `Job metrics: ${JSON.stringify(metricsSummary)}`)
    }

    this.log('info', `${this.name} processing completed in ${duration}ms. Processed: ${results.processed}, Successful: ${results.successful}, Failed: ${results.failed}`)

    return results
  }

  /**
   * Get unified entities (contacts and companies) with proper ID handling
   * This function combines contacts and companies into a single result set
   * while maintaining distinct IDs for each entity type
   * Uses the same unified view pattern as the processing stats route
   */
  async getUnprocessedUnifiedEntities(
    baseConditions: string[],
    specificConditions: string[],
    entityType?: 'contact' | 'company' | 'both'
  ): Promise<UnifiedEntityData[]> {
    const results: UnifiedEntityData[] = []

    // Build unified filters like the processing stats route
    const whereConditions: string[] = []
    const queryParams: any[] = []
    let paramIndex = 1

    // Apply base conditions
    whereConditions.push(...baseConditions)
    whereConditions.push(...specificConditions)

    // Apply source filter
    if (this.options.filters?.source && this.options.filters.source !== 'all') {
      whereConditions.push(`c.source = $${paramIndex}`)
      queryParams.push(this.options.filters.source)
      paramIndex++
    }

    // Apply date range filter
    if (this.options.filters?.dateRange && this.options.filters.dateRange !== 'all') {
      const dateRange = this.options.filters.dateRange
      switch (dateRange) {
        case 'today':
          whereConditions.push(`c.created_at >= CURRENT_DATE`)
          break
        case 'week':
          whereConditions.push(`c.created_at >= CURRENT_DATE - INTERVAL '7 days'`)
          break
        case 'month':
          whereConditions.push(`c.created_at >= CURRENT_DATE - INTERVAL '30 days'`)
          break
        case 'quarter':
          whereConditions.push(`c.created_at >= CURRENT_DATE - INTERVAL '90 days'`)
          break
      }
    }

    // Apply contact-specific filters
    if (this.options.filters?.contact_enrichment_status && this.options.filters.contact_enrichment_status !== 'all') {
      whereConditions.push(`c.contact_enrichment_status = $${paramIndex}`)
      queryParams.push(this.options.filters.contact_enrichment_status)
      paramIndex++
    }

    if (this.options.filters?.contact_email_verification_status && this.options.filters.contact_email_verification_status !== 'all') {
      whereConditions.push(`c.email_verification_status = $${paramIndex}`)
      queryParams.push(this.options.filters.contact_email_verification_status)
      paramIndex++
    }

    // Apply company-specific filters
    if (this.options.filters?.company_overview_status && this.options.filters.company_overview_status !== 'all') {
      whereConditions.push(`co.company_overview_status = $${paramIndex}`)
      queryParams.push(this.options.filters.company_overview_status)
      paramIndex++
    }

    // Apply ID filters
    let idFilter = ''
    if (this.options.singleId) {
      if (entityType === 'contact' || !entityType) {
        idFilter = `c.contact_id = $${paramIndex}`
      } else {
        idFilter = `co.company_id = $${paramIndex}`
      }
      queryParams.push(this.options.singleId)
      paramIndex++
    } else if (this.options.multiIds && this.options.multiIds.length > 0) {
      if (entityType === 'contact' || !entityType) {
        idFilter = `c.contact_id = ANY($${paramIndex})`
      } else {
        idFilter = `co.company_id = ANY($${paramIndex})`
      }
      queryParams.push(this.options.multiIds)
      paramIndex++
    }

    if (idFilter) {
      whereConditions.push(idFilter)
    }

    // Build the final WHERE clause
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

    // Build ORDER BY clause
    const orderClause = `ORDER BY c.contact_id DESC`

    // Build the unified query using the same pattern as the processing stats route
    const unifiedSql = `
      SELECT DISTINCT
        c.contact_id,
        c.first_name,
        c.last_name,
        c.full_name,
        c.title,
        c.headline,
        c.seniority,
        c.email,
        c.personal_email,
        c.email_status,
        c.linkedin_url,
        c.contact_city,
        c.contact_state,
        c.contact_country,
        c.phone_number,
        c.capital_type,
        c.capital_position,
        c.company_type,
        c.contact_category,
        c.category,
        c.source,
        c.region,
        c.notes,
        c.created_at,
        c.updated_at,
        c.extracted,
        c.searched,
        c.email_generated,
        c.enriched,
        c.smartlead_lead_id,
        c.smartlead_status,
        c.last_email_sent_at,
        c.processing_state,
        c.email_verification_status,
        c.email_verification_date,
        c.email_verification_error,
        c.contact_enrichment_status,
        c.contact_enrichment_date,
        c.contact_enrichment_error,
        c.email_generation_status,
        c.email_generation_date,
        c.email_generation_error,
        c.osint_status,
        c.osint_date,
        c.osint_error,
        c.classification_status,
        c.classification_date,
        c.classification_error,
        c.email_sending_status,
        c.email_sending_date,
        c.email_sending_error,
        c.processing_error_count,
        c.classification_confidence,
        c.classification_reasoning,
        c.conflict_status,
        c.conflicts,
        
        -- Company data
        co.company_name,
        co.company_website,
        co.industry as company_industry,
        co.company_id,
        co.website_scraping_status,
        co.company_overview_status,
        co.website_scraping_error,
        co.company_overview_error,
        co.processing_error_count as company_processing_error_count
        
      FROM contacts c
      JOIN companies co ON c.company_id = co.company_id
      ${whereClause}
      ${orderClause}
      ${this.options.limit && !this.options.multiIds ? `LIMIT ${this.options.limit}` : ''}
    `

    console.log('Unified SQL:', unifiedSql)
    console.log('Unified Params:', queryParams)
    const unifiedResults = await this.query(unifiedSql, queryParams)

    // Extract data based on entity type
    for (const row of unifiedResults) {
      if (entityType === 'contact' || entityType === 'both' || !entityType) {
        // Add contact data
        results.push({
          id: row.contact_id as number,
          entity_type: 'contact' as const,

          // Contact-specific fields
          contact_id: row.contact_id as number | undefined,
          first_name: row.first_name as string | undefined,
          last_name: row.last_name as string | undefined,
          full_name: row.full_name as string | undefined,
          title: row.title as string | undefined,
          email: row.email as string | undefined,
          linkedin_url: row.linkedin_url as string | undefined,
          company_id: row.company_id as number | undefined,
          contact_country: row.contact_country as string | undefined,
          phone_number: row.phone_number as string | undefined,
          contact_processing_state: row.processing_state as ContactProcessingState | undefined,
          extracted: row.extracted as boolean | undefined,
          searched: row.searched as boolean | undefined,
          category: row.category as string | undefined,
          capital_type: row.capital_type as string | undefined,
          capital_position: row.capital_position as string | undefined,
          company_type: row.company_type as string | undefined,
          extra_attrs: row.extra_attrs as any,

          // Company-specific fields
          company_name: row.company_name as string | undefined,
          company_website: row.company_website as string | undefined,
          industry: row.company_industry as string | undefined,
          company_processing_state: row.company_overview_status as CompanyProcessingState | undefined,

          // Shared fields
          source: row.source as string | undefined,
          created_at: row.created_at as string | undefined,
          updated_at: row.updated_at as string | undefined
        })
      }

      if (entityType === 'company' || entityType === 'both' || !entityType) {
        // Add company data
        results.push({
          id: row.company_id as number,
          entity_type: 'company' as const,

          // Contact-specific fields (undefined for companies)
          contact_id: undefined,
          first_name: undefined,
          last_name: undefined,
          full_name: undefined,
          title: undefined,
          email: undefined,
          linkedin_url: undefined,
          company_id: row.company_id as number | undefined,
          contact_country: undefined,
          phone_number: undefined,
          contact_processing_state: undefined,
          extracted: undefined,
          searched: undefined,
          category: undefined,
          capital_type: undefined,
          capital_position: undefined,
          company_type: undefined,
          extra_attrs: undefined,

          // Company-specific fields
          company_name: row.company_name as string | undefined,
          company_website: row.company_website as string | undefined,
          industry: row.company_industry as string | undefined,
          company_processing_state: row.company_overview_status as CompanyProcessingState | undefined,

          // Shared fields
          source: row.source as string | undefined,
          created_at: row.created_at as string | undefined,
          updated_at: row.updated_at as string | undefined
        })
      }
    }

    return results
  }

  /**
   * Get unprocessed entities using unified filtering approach (matching stats route)
   * @param baseConditions - Base conditions that apply to all entities
   * @param specificConditions - Processor-specific conditions (skipped when multiId is provided)
   * @param entityType - Type of entity to process ('contact' or 'company')
   * @param filters - Optional processing filters
   */
  protected async getUnprocessedEntitiesUnified(
    baseConditions: string[],
    specificConditions: string[],
    entityType: 'contact' | 'company',
    filters?: any
  ): Promise<UnifiedEntityData[]> {
    try {
      // Build conditions array
      const allConditions: string[] = [...baseConditions];
      
      // Add specific conditions only if not processing specific IDs
      if (!this.options.multiIds && !this.options.singleId) {
        allConditions.push(...specificConditions);
      }
      
      // Apply filters if provided
      const params: any[] = [];
      let paramIndex = 1;
      
      // Add multiIds or singleId parameters first if they exist
      if (this.options.multiIds && this.options.multiIds.length > 0) {
        params.push(this.options.multiIds);
        paramIndex++;
      } else if (this.options.singleId) {
        params.push(this.options.singleId);
        paramIndex++;
      }
      
      // Handle multiIds scenario - process only specific IDs
      if (this.options.multiIds && this.options.multiIds.length > 0) {
        if (entityType === 'contact') {
          allConditions.push(`c.contact_id = ANY($1)`);
        } else {
          allConditions.push(`co.company_id = ANY($1)`);
        }
      }
      
      // Handle singleId scenario
      if (this.options.singleId) {
        if (entityType === 'contact') {
          allConditions.push(`c.contact_id = $1`);
        } else {
          allConditions.push(`co.company_id = $1`);
        }
      }
      
      if (filters) {
        // Source filter
        if (filters.source && filters.source.length > 0) {
          // Handle source as either string or array
          if (Array.isArray(filters.source)) {
            if (filters.source.length === 1) {
              // Single value - use equality
              allConditions.push(`c.source = $${paramIndex}`);
              params.push(filters.source[0]);
            } else {
              // Multiple values - use ANY
              allConditions.push(`c.source = ANY($${paramIndex})`);
              params.push(filters.source);
            }
          } else {
            // Single string value
            allConditions.push(`c.source = $${paramIndex}`);
            params.push(filters.source);
          }
          paramIndex++;
        }
        
        // Contact enrichment status filter
        if (filters.contact_enrichment_status && filters.contact_enrichment_status.length > 0) {
          // Handle as either string or array
          if (Array.isArray(filters.contact_enrichment_status)) {
            if (filters.contact_enrichment_status.length === 1) {
              // Single value - use equality
              allConditions.push(`c.contact_enrichment_status = $${paramIndex}`);
              params.push(filters.contact_enrichment_status[0]);
            } else {
              // Multiple values - use ANY
              allConditions.push(`c.contact_enrichment_status = ANY($${paramIndex})`);
              params.push(filters.contact_enrichment_status);
            }
          } else {
            // Single string value
            allConditions.push(`c.contact_enrichment_status = $${paramIndex}`);
            params.push(filters.contact_enrichment_status);
          }
          paramIndex++;
        }
        
        // Email verification status filter
        if (filters.contact_email_verification_status && filters.contact_email_verification_status.length > 0) {
          // Handle as either string or array
          if (Array.isArray(filters.contact_email_verification_status)) {
            if (filters.contact_email_verification_status.length === 1) {
              // Single value - use equality
              allConditions.push(`c.email_verification_status = $${paramIndex}`);
              params.push(filters.contact_email_verification_status[0]);
            } else {
              // Multiple values - use ANY
              allConditions.push(`c.email_verification_status = ANY($${paramIndex})`);
              params.push(filters.contact_email_verification_status);
            }
          } else {
            // Single string value
            allConditions.push(`c.email_verification_status = $${paramIndex}`);
            params.push(filters.contact_email_verification_status);
          }
          paramIndex++;
        }
        
        // Email generation status filter
        if (filters.email_generation_status && filters.email_generation_status.length > 0) {
          // Handle email generation status filter with support for 'not_started' (NULL values)
          const processedStatuses = filters.email_generation_status.map((status: string) => 
            status === 'not_started' ? null : status
          );
          
          // Build condition that handles both NULL and non-NULL values
          const nullConditions: string[] = [];
          const nonNullConditions: string[] = [];
          
          processedStatuses.forEach((status: string | null, index: number) => {
            if (status === null) {
              nullConditions.push(`c.email_generation_status IS NULL`);
            } else {
              nonNullConditions.push(`c.email_generation_status = $${paramIndex + index}`);
            }
          });
          
          const emailGenConditions = [...nullConditions, ...nonNullConditions];
          if (emailGenConditions.length > 0) {
            allConditions.push(`(${emailGenConditions.join(' OR ')})`);
            // Add non-null values to params
            processedStatuses.filter((status: string | null) => status !== null).forEach((status: string) => {
              params.push(status);
            });
            paramIndex += processedStatuses.filter((status: string | null) => status !== null).length;
          }
        }
        
        // Email sending status filter
        if (filters.email_sending_status && filters.email_sending_status.length > 0) {
          allConditions.push(`c.email_sending_status = ANY($${paramIndex})`);
          params.push(filters.email_sending_status);
          paramIndex++;
        }
        
        // Company overview status filter
        if (filters.company_overview_status && filters.company_overview_status.length > 0) {
          allConditions.push(`co.company_overview_status = ANY($${paramIndex})`);
          params.push(filters.company_overview_status);
          paramIndex++;
        }
        
        // Website scraping status filter
        if (filters.website_scraping_status && filters.website_scraping_status.length > 0) {
          allConditions.push(`co.website_scraping_status = ANY($${paramIndex})`);
          params.push(filters.website_scraping_status);
          paramIndex++;
        }
        
        // Job tier filter
        if (filters.jobTier && filters.jobTier.length > 0) {
          allConditions.push(`c.company_type = ANY($${paramIndex})`);
          params.push(filters.jobTier);
          paramIndex++;
        }
        
        // Email status filter
        if (filters.email_status && filters.email_status.length > 0) {
          allConditions.push(`c.email_status = ANY($${paramIndex})`);
          params.push(filters.email_status);
          paramIndex++;
        }
        
        // Contact company type filter
        if (filters.contactCompanyType && filters.contactCompanyType.length > 0) {
          const companyTypeConditions = filters.contactCompanyType.map((_: string, index: number) => `c.company_type ILIKE $${paramIndex + index}`);
          allConditions.push(`(${companyTypeConditions.join(' OR ')})`);
          filters.contactCompanyType.forEach((type: string) => {
            params.push(`%${type}%`);
            paramIndex++;
          });
        }
        
        // Contact capital position filter
        if (filters.contactCapitalPosition && filters.contactCapitalPosition.length > 0) {
          allConditions.push(`c.capital_position && $${paramIndex}::text[]`);
          params.push(filters.contactCapitalPosition);
          paramIndex++;
        }
        
        // Geographic filters
        if (filters.contactCountries && filters.contactCountries.length > 0) {
          const countryConditions = filters.contactCountries.map((_: string, index: number) => `c.contact_country ILIKE $${paramIndex + index}`);
          allConditions.push(`(${countryConditions.join(' OR ')})`);
          filters.contactCountries.forEach((country: string) => {
            params.push(`%${country}%`);
            paramIndex++;
          });
        }
        
        if (filters.contactStates && filters.contactStates.length > 0) {
          const stateConditions = filters.contactStates.map((_: string, index: number) => `c.contact_state ILIKE $${paramIndex + index}`);
          allConditions.push(`(${stateConditions.join(' OR ')})`);
          filters.contactStates.forEach((state: string) => {
            params.push(`%${state}%`);
            paramIndex++;
          });
        }
        
        if (filters.contactCities && filters.contactCities.length > 0) {
          const cityConditions = filters.contactCities.map((_: string, index: number) => `c.contact_city ILIKE $${paramIndex + index}`);
          allConditions.push(`(${cityConditions.join(' OR ')})`);
          filters.contactCities.forEach((city: string) => {
            params.push(`%${city}%`);
            paramIndex++;
          });
        }
        
        // Boolean flags
        if (filters.extracted !== undefined) {
          allConditions.push(`c.extracted = $${paramIndex}`);
          params.push(filters.extracted);
          paramIndex++;
        }
        
        if (filters.searched !== undefined) {
          allConditions.push(`c.searched = $${paramIndex}`);
          params.push(filters.searched);
          paramIndex++;
        }
        
        if (filters.emailGenerated !== undefined) {
          allConditions.push(`c.email_generated = $${paramIndex}`);
          params.push(filters.emailGenerated);
          paramIndex++;
        }
        
        if (filters.enriched !== undefined) {
          allConditions.push(`c.enriched = $${paramIndex}`);
          params.push(filters.enriched);
          paramIndex++;
        }
        
        if (filters.hasSmartleadId !== undefined) {
          if (filters.hasSmartleadId) {
            allConditions.push(`c.smartlead_lead_id IS NOT NULL`);
          } else {
            allConditions.push(`c.smartlead_lead_id IS NULL`);
          }
        }
      }
      
      // Build WHERE clause
      const whereClause = allConditions.length > 0 ? `WHERE ${allConditions.join(' AND ')}` : '';
      
      // Build unified query similar to stats route
      const sql = `
        SELECT ${entityType === 'company' ? 'DISTINCT ON (co.company_id)' : 'DISTINCT'}
          c.contact_id,
          co.company_id,
          c.first_name,
          c.last_name,
          c.email,
          c.title,
          c.phone_number,
          c.linkedin_url,
          c.contact_city,
          c.contact_state,
          c.contact_country,
          c.email_verification_status,
          c.enriched,
          c.contact_enrichment_status,
          c.email_generation_status,
          co.company_name,
          co.company_website,
          co.industry,
          co.company_overview_status,
          co.website_scraping_status,
          c.created_at as contact_created_at,
          co.created_at as company_created_at,
          c.updated_at as contact_updated_at,
          co.updated_at as company_updated_at,
          '${entityType}' as entity_type
        FROM contacts c
        JOIN companies co ON c.company_id = co.company_id
        ${whereClause}
        ORDER BY 
          ${entityType === 'company' ? 'co.company_id, co.updated_at' : 'c.updated_at'}
        ${!this.options.multiIds ? `LIMIT ${this.options.limit || this.options.batchSize || 50}` : ''}
      `;
      
      this.log('debug', `Executing unified query: ${sql}`);
      this.log('debug', `Query parameters: ${JSON.stringify(params)}`);
      this.log('debug', `Parameter count: ${params.length}`);
      this.log('debug', `Conditions count: ${allConditions.length}`);
      
      const result = await this.query(sql, params);
      
      // Map to UnifiedEntityData format
      const unifiedEntities: UnifiedEntityData[] = result.map((row: any) => ({
        id: entityType === 'contact' ? row.contact_id : row.company_id,
        entity_type: entityType,
        contact_id: row.contact_id,
        company_id: row.company_id,
        first_name: row.first_name,
        last_name: row.last_name,
        email: row.email,
        title: row.title,
        phone_number: row.phone_number,
        linkedin_url: row.linkedin_url,
        contact_city: row.contact_city,
        contact_state: row.contact_state,
        contact_country: row.contact_country,
        email_verification_status: row.email_verification_status,
        enriched: row.enriched,
        contact_enrichment_status: row.contact_enrichment_status,
        email_generation_status: row.email_generation_status,
        company_name: row.company_name,
        company_website: row.company_website,
        industry: row.industry,
        company_overview_status: row.company_overview_status,
        website_scraping_status: row.website_scraping_status,
        created_at: entityType === 'contact' ? row.contact_created_at : row.company_created_at,
        updated_at: entityType === 'contact' ? row.contact_updated_at : row.company_updated_at
      }));
      
      this.log('info', `Found ${unifiedEntities.length} unprocessed ${entityType} entities`);
      return unifiedEntities;
      
    } catch (error) {
      this.log('error', `Error getting unprocessed entities: ${error}`);
      throw error;
    }
  }

  /**
   * Get combined content from all scraped pages for a company
   */
  protected async getCompanyContent(companyId: number): Promise<string> {
    try {
      // Updated query to use company_web_pages instead of company_pages
      // Note: page_type column might not exist in company_web_pages table
      const sql = `
        SELECT url, extracted_text as content
        FROM company_web_pages
        WHERE company_id = $1
        AND extracted_text IS NOT NULL
        ORDER BY 
          LENGTH(extracted_text) DESC
        LIMIT 20
      `

      const pages = await this.query(sql, [companyId])

      if (pages.length === 0) {
        this.log('warn', `No content found for company ${companyId}`)
        return ''
      }

      // Combine content with URL context (removing page_type which might not exist)
      const combinedContent = pages
        .map((page: Record<string, unknown>) =>
          `\n=== URL: ${page.url as string} ===\n${page.content as string}`)
        .join('\n\n').slice(0, 100000)


      this.log('info', `Combined ${pages.length} pages of content for company ${companyId}`)
      return combinedContent
    } catch (error) {
      this.log('error', `Error fetching company content for ${companyId}: ${error}`)
      return ''
    }
  }

  /**
   * Update contact email verification status
   */
  protected async updateContactEmailVerificationStatus(
    contactId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET email_verification_status = $1::text, 
          email_verification_error = $2,
          email_verification_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE email_verification_date END,
          updated_at = NOW() 
      WHERE contact_id = $3
    `
    try {
      await this.query(sql, [status, error || null, contactId])
      this.log('debug', `Updated email verification status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating email verification status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Update contact email generation status
   */
  protected async updateContactEmailGenerationStatus(
    contactId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET email_generation_status = $1::text, 
          email_generation_error = $2,
          email_generated = $3,
          email_generation_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE email_generation_date END,
          updated_at = NOW() 
      WHERE contact_id = $4
    `
    try {
      // Update legacy email_generated field for backward compatibility
      const emailGenerated = status === 'completed' ? true : false
      await this.query(sql, [status, error || null, emailGenerated, contactId])
      this.log('debug', `Updated email generation status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating email generation status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Update contact email sending status
   */
  protected async updateContactEmailSendingStatus(
    contactId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET email_sending_status = $1::text, 
          email_sending_error = $2,
          email_sending_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE email_sending_date END,
          updated_at = NOW() 
      WHERE contact_id = $3
    `
    try {
      await this.query(sql, [status, error || null, contactId])
      this.log('debug', `Updated email sending status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating email sending status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Update contact enrichment status
   */
  protected async updateContactEnrichmentStatus(
    contactId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET contact_enrichment_status = $1::text, 
          contact_enrichment_error = $2,
          contact_enrichment_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE contact_enrichment_date END,
          updated_at = NOW() 
      WHERE contact_id = $3
    `
    try {
      await this.query(sql, [status, error || null, contactId])
      this.log('debug', `Updated contact enrichment status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating contact enrichment status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Update contact enrichment V2 status
   */
  protected async updateContactEnrichmentV2Status(
    contactId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET contact_enrichment_v2_status = $1::text, 
          contact_enrichment_v2_error = $2,
          contact_enrichment_v2_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE contact_enrichment_v2_date END,
          updated_at = NOW() 
      WHERE contact_id = $3
    `
    try {
      await this.query(sql, [status, error || null, contactId])
      this.log('debug', `Updated contact enrichment V2 status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating contact enrichment V2 status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Update company website scraping status
   */
  protected async updateCompanyWebscrapingStatus(
    companyId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE companies 
      SET website_scraping_status = $1::text, 
          website_scraping_error = $2,
          website_scraping_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE website_scraping_date END,
          updated_at = NOW() 
      WHERE company_id = $3
    `
    try {
      await this.query(sql, [status, error || null, companyId])
      this.log('debug', `Updated website scraping status for company ${companyId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating website scraping status for company ${companyId}: ${error}`)
    }
  }

  /**
   * Update company overview extraction status
   */
  protected async updateCompanyOverviewStatus(
    companyId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE companies 
      SET company_overview_status = $1::text, 
          company_overview_error = $2,
          company_overview_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE company_overview_date END,
          updated_at = NOW() 
      WHERE company_id = $3
    `
    try {
      await this.query(sql, [status, error || null, companyId])
      this.log('debug', `Updated company overview status for company ${companyId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating company overview status for company ${companyId}: ${error}`)
    }
  }

  /**
   * Update company overview V2 extraction status
   */
  protected async updateOverviewV2Status(
    companyId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE companies 
      SET 
        overview_v2_status = $1::text,
        overview_v2_error = $2,
        overview_v2_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE overview_v2_date END,
        overview_v2_error_count = CASE WHEN $1::text = 'failed' THEN COALESCE(overview_v2_error_count, 0) + 1 ELSE overview_v2_error_count END,
        updated_at = NOW() 
      WHERE company_id = $3
    `
    try {
      await this.query(sql, [status, error || null, companyId])
      this.log('debug', `Updated overview V2 status for company ${companyId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating overview V2 status for company ${companyId}: ${error}`)
    }
  }

  /**
   * Get news entries that need HTML fetching
   */
  protected async getNewsForHTMLFetching(limit?: number, singleId?: number): Promise<Record<string, unknown>[]> {
    const conditions = [
      'fetched = false'
    ]

    const params: unknown[] = []
    let paramIndex = 1

    // Handle multi-ID processing
    if (this.options.multiIds && this.options.multiIds.length > 0) {
      this.log('info', `Processing multiple news IDs for HTML fetching: ${this.options.multiIds.join(', ')}`)

      // Create placeholder string for IN clause
      const placeholders = this.options.multiIds.map((_, index) => `$${paramIndex + index}`).join(', ')
      conditions.push(`id IN (${placeholders})`)

      // Add all IDs to params
      this.options.multiIds.forEach(id => params.push(id))
      paramIndex += this.options.multiIds.length

      // For multi-ID runs, allow reprocessing regardless of status
      const pendingConditionIndex = conditions.findIndex(c => c.includes('fetched'))
      if (pendingConditionIndex !== -1) {
        conditions.splice(pendingConditionIndex, 1)
      }
    }

    if (singleId) {
      // First check if the single news entry exists
      const checkSql = `
        SELECT id, url, fetched
        FROM news 
        WHERE id = $1
      `
      try {
        const checkResult = await this.query(checkSql, [singleId])
        if (checkResult.length === 0) {
          this.log('warn', `News entry ${singleId} not found`)
          return []
        }

        const news = checkResult[0]

        if (news.fetched) {
          this.log('info', `News entry ${singleId} already has HTML fetched - will reprocess for single run`)
          // For single runs, allow reprocessing even if already fetched
        }
      } catch (error) {
        this.log('error', `Error checking single news entry ${singleId}: ${error}`)
        return []
      }

      conditions.push(`id = $${paramIndex}`)
      params.push(singleId)
      paramIndex++
    }

    const sql = `
      SELECT id, url, fetched, created_at, updated_at
      FROM news 
      WHERE (bad_url = false OR bad_url IS NULL) AND ${conditions.join(' AND ')}
      ORDER BY id DESC
      ${limit && !this.options.multiIds ? `LIMIT ${limit}` : ''}
    `

    try {
      return await this.query(sql, params)
    } catch (error) {
      this.log('error', `Error fetching news for HTML fetching: ${error}`)
      return []
    }
  }

  /**
   * Update news HTML fetching status
   */
  protected async updateNewsHTMLFetchingStatus(
    newsId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    // Since news table uses the 'fetched' boolean field
    const sql = `
      UPDATE news 
      SET fetched = $1,
          fetch_status = $2,
          fetch_error = $3,
          updated_at = NOW() 
      WHERE id = $4
    `
    try {
      const fetched = status === 'completed'
      await this.query(sql, [fetched, status, error || null, newsId])
      this.log('debug', `Updated news HTML fetching status for news ${newsId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating news HTML fetching status for news ${newsId}: ${error}`)
    }
  }

  /**
   * Update news enrichment status
   */
  protected async updateNewsEnrichmentStatus(
    newsId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    try {
      const extracted = status === 'completed'

      if (error) {
        // Update with error
        const sql = `
          UPDATE news 
          SET extracted = $1,
              enrichment_status = $2,
              enrichment_error = $3,
              updated_at = NOW()
          WHERE id = $4
        `
        await this.query(sql, [extracted, status, error, newsId])
      } else {
        // Update without error
        const sql = `
          UPDATE news 
          SET extracted = $1,
              enrichment_status = $2,
              enrichment_error = $3,
              updated_at = NOW()
          WHERE id = $4
        `
        await this.query(sql, [extracted, status, null, newsId])
      }

      this.log('debug', `Updated news enrichment status for news ${newsId}: ${status}${error ? ` (error: ${error})` : ''}`)
    } catch (dbError) {
      this.log('error', `Error updating news enrichment status for news ${newsId}: ${dbError}`)
    }
  }

  /**
   * Increment processing error count
   */
  protected async incrementProcessingErrorCount(entityType: 'contact' | 'company', entityId: number): Promise<void> {
    const table = entityType === 'contact' ? 'contacts' : 'companies'
    const idColumn = entityType === 'contact' ? 'contact_id' : 'company_id'

    const sql = `
      UPDATE ${table} 
      SET processing_error_count = COALESCE(processing_error_count, 0) + 1,
          updated_at = NOW() 
      WHERE ${idColumn} = $1
    `
    try {
      await this.query(sql, [entityId])
      this.log('debug', `Incremented processing error count for ${entityType} ${entityId}`)
    } catch (error) {
      this.log('error', `Error incrementing processing error count for ${entityType} ${entityId}: ${error}`)
    }
  }

  /**
   * Save processing attempt information to the processing_attempts column
   */
  protected async saveProcessingAttempt(
    entityType: 'contact' | 'company',
    entityId: number,
    stage: string,
    usage?: any,
    model?: string,
    tokens?: number,
    success?: boolean,
    error?: string
  ): Promise<void> {
    const table = entityType === 'contact' ? 'contacts' : 'companies'
    const idColumn = entityType === 'contact' ? 'contact_id' : 'company_id'

    try {
      // Create the attempt record
      const attemptRecord = {
        timestamp: new Date().toISOString(),
        stage: stage,
        success: success ?? true,
        model: model,
        tokens: tokens,
        usage: usage,
        ...(error && { error: error })
      }

      // Get current processing_attempts
      const getCurrentSql = `
        SELECT processing_attempts 
        FROM ${table} 
        WHERE ${idColumn} = $1
      `

      const currentResult = await this.query(getCurrentSql, [entityId])
      let currentAttempts: Record<string, any[]> = {}

      if (currentResult.length > 0 && currentResult[0].processing_attempts) {
        currentAttempts = currentResult[0].processing_attempts as Record<string, any[]>
      }

      // Add the new attempt (using stage as key, keeping array of attempts per stage)
      if (!currentAttempts[stage]) {
        currentAttempts[stage] = []
      }

      // Keep only the last 5 attempts per stage to avoid bloating
      if (currentAttempts[stage].length >= 5) {
        currentAttempts[stage] = currentAttempts[stage].slice(-4)
      }

      currentAttempts[stage].push(attemptRecord)

      // Update the processing_attempts column
      const updateSql = `
        UPDATE ${table} 
        SET processing_attempts = $1::json,
            updated_at = NOW() 
        WHERE ${idColumn} = $2
      `

      await this.query(updateSql, [JSON.stringify(currentAttempts), entityId])
      this.log('debug', `Saved processing attempt for ${entityType} ${entityId}, stage: ${stage}, success: ${success}`)

    } catch (error) {
      this.log('error', `Error saving processing attempt for ${entityType} ${entityId}: ${error}`)
    }
  }

  // Legacy status update methods (deprecated - use new status column methods above)

  /**
   * Use updateContactEmailVerificationStatus instead
   */
  protected async updateContactEmailStatus(contactId: number, status: string): Promise<void> {
    this.log('warn', 'updateContactEmailStatus is deprecated, use updateContactEmailVerificationStatus instead')
    const sql = `UPDATE contacts SET email_status = $1, updated_at = NOW() WHERE contact_id = $2`
    try {
      await this.query(sql, [status, contactId])
    } catch (error) {
      this.log('error', `Error updating email status for contact ${contactId}: ${error}`)
    }
  }
}
