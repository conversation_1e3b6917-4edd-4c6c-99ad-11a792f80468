import { ContactProcessingState, EntityData, ProcessorOptions, ContactData, UnifiedEntityData } from '../../types/processing'
import { BaseProcessor } from './BaseProcessor'
import axios from 'axios'


// BulkEmailChecker API configuration
const BULK_EMAIL_CHECKER_API_URL = "https://api.bulkemailchecker.com/real-time/"
const BULK_EMAIL_CHECKER_API_KEY = process.env.BULK_EMAIL_CHECKER_API_KEY

export class EmailValidatorProcessor extends BaseProcessor {
  // Store email status results to pass to updateEntityStatus
  private emailStatusResults: Map<number, string> = new Map()

  constructor(options: ProcessorOptions = {}) {
    // BulkEmailChecker API specific rate limiting configuration
    const emailValidatorBottleneckConfig = {
      maxConcurrent: 10,                   // Reduced from 10 to prevent queue overflow
      minTime: 1000,                      // Reduced to 1 second between requests
      retryAttempts: 2,                   // Fewer retries for external API
      retryDelayBase: 3000,               // 3 second base delay for retries
      retryDelayMax: 15000,               // Max 15 second retry delay
      timeout: 120000,                    // 2 minutes timeout for email verification
      highWater: 500,                     // Higher queue limit for batch processing
      strategy: 'OVERFLOW' as any,         // Use OVERFLOW strategy
      defaultPriority: 5,                 // Normal priority
      enableJobMetrics: true              // Track performance for API monitoring
    }

    super('EmailValidator', {
      ...options,
      bottleneckConfig: options.bottleneckConfig || emailValidatorBottleneckConfig
    })
  }

  async getUnprocessedEntities(): Promise<UnifiedEntityData[]> {
    const baseConditions: string[] = [
      'c.email IS NOT NULL',
      'c.email != \'\'',
      'c.email != \' \''
    ]

    const specificConditions: string[] = [
      'COALESCE(c.email_verification_status, \'pending\') = \'pending\''
    ]

    // Use new unified approach that matches stats route
    return await this.getUnprocessedEntitiesUnified(baseConditions, specificConditions, 'contact', this.options.filters)
  }

  async processEntity(entity: UnifiedEntityData): Promise<{ success: boolean; error?: string }> {
    // Only process contacts for email validation
    if (entity.entity_type !== 'contact') {
      return { success: false, error: 'Email validation only supports contacts' }
    }

    try {
      this.log('info', `Validating email for contact ${entity.contact_id}: ${entity.email}`)

      // Set status to running
      await this.updateContactEmailVerificationStatus(entity.contact_id!, 'running')

      // Basic email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(entity.email!)) {
        // Store email status for updateEntityStatus
        this.emailStatusResults.set(entity.contact_id!, 'Invalid')
        return { success: false, error: 'Invalid email format' }
      }

      // Call the email validation service
      const emailStatus = await this.validateEmailWithService(entity.email!)

      // Store email status for updateEntityStatus
      this.emailStatusResults.set(entity.contact_id!, emailStatus)

      this.log('info', `Email validation result for ${entity.email}: ${emailStatus}`)

      // Consider the process successful if we got a result (even if it's Unknown)
      // Only treat Invalid as a failure
      return {
        success: emailStatus !== 'Invalid',
        error: emailStatus === 'Invalid' ? 'Email validation failed' : undefined
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)

      // Store email status for updateEntityStatus
      this.emailStatusResults.set(entity.contact_id!, 'Unknown')

      this.log('error', `Error validating email for contact ${entity.contact_id}: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    // Get the stored email status result
    const emailStatus = this.emailStatusResults.get(entityId) || 'Unknown'

    try {
      if (success) {
        // Email validation completed successfully (got a result)
        await this.updateContactEmailVerificationStatus(entityId, 'completed')
        await this.updateContactEmailStatusField(entityId, emailStatus)
      } else {
        // Email validation failed (API error or invalid format)
        await this.updateContactEmailVerificationStatus(entityId, 'failed', error)
        await this.incrementProcessingErrorCount('contact', entityId)

        // Still update the email status with whatever result we got
        await this.updateContactEmailStatusField(entityId, emailStatus)
      }
    } catch (updateError) {
      this.log('error', `Error updating status for contact ${entityId}: ${updateError}`)
      await this.updateContactEmailVerificationStatus(entityId, 'failed', 'Status update failed')
    }

    // Clean up stored result
    this.emailStatusResults.delete(entityId)
  }

  /**
   * Validate email using BulkEmailChecker API
   * @param email Email address to validate
   * @returns Email status string ('Verified', 'Invalid', 'Unknown', 'Failed')
   */
  private async validateEmailWithService(email: string): Promise<string> {
    try {
      this.log('info', `Validating email with BulkEmailChecker API: ${email}`)

      // URL encode the email
      const encodedEmail = encodeURIComponent(email)

      // Create the API URL
      const url = `${BULK_EMAIL_CHECKER_API_URL}?key=${BULK_EMAIL_CHECKER_API_KEY}&email=${encodedEmail}`

      // Make the API request
      const response = await axios.get(url, { timeout: 15000 })

      // Check if the request was successful
      if (response.status !== 200) {
        this.log('error', `API request failed with status code ${response.status}: ${response.statusText}`)
        return 'Unknown'
      }

      // Parse the response
      const result = response.data

      // Map API response to our values
      const statusValue = result.status || 'unknown'

      if (statusValue === 'passed') {
        this.log('info', `Verified: ${email}`)
        return 'Verified'
      } else if (statusValue === 'failed') {
        this.log('info', `Invalid: ${email}`)
        return 'Invalid'
      } else {  // status === 'unknown' or any other status
        this.log('info', `Unknown: ${email} (API status: ${statusValue})`)
        return 'Unknown'
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error validating email with API: ${errorMessage}`)
      return 'Unknown'
    }
  }

  /**
   * Update the email_status field in the database
   */
  private async updateContactEmailStatusField(contactId: number, status: string): Promise<void> {
    const sql = `UPDATE contacts SET email_status = $1, updated_at = NOW() WHERE contact_id = $2`
    try {
      await this.query(sql, [status, contactId])
      this.log('debug', `Updated email status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating email status for contact ${contactId}: ${error}`)
      throw error
    }
  }
} 