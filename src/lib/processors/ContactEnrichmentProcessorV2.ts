import { BaseProcessor } from './BaseProcessor'
import { ContactProcessingState, EntityData, ProcessorOptions, ContactData, UnifiedEntityData } from '../../types/processing'
import { LLMFactory, LLMMessage, createProcessor<PERSON>oggerAdapter } from '../llm'
import { CONTACT_ENRICHMENT_V2_SYSTEM_PROMPT, CONTACT_ENRICHMENT_V2_USER_TEMPLATE_FUNCTION } from '../prompts'
import { BaseScraper } from '../scrapers/BaseScraper'

interface EnrichmentV2Result {
  // Personal Information
  executive_summary?: string
  career_timeline?: string[]
  
  // Contact Information  
  additional_email?: string
  phone_number_secondary?: string
  
  // Social Media
  twitter?: string
  facebook?: string
  instagram?: string
  youtube?: string
  
  // Education
  education_college?: string
  education_college_year_graduated?: string
  education_high_school?: string
  education_high_school_year_graduated?: string
  
  // Personal Details
  honorable_achievements?: string[]
  hobbies?: string[]
  age?: string
  
  // Location Details (using existing fields)
  contact_address?: string
  contact_zip_code?: string
  
  // Contact Metadata
  contact_type?: string
  relationship_owner?: string
  role_in_decision_making?: string
  
  // Interaction Tracking
  source_of_introduction?: string
  
  // Compliance
  accredited_investor_status?: boolean
  
  // Analysis metadata
  confidence?: number
  reasoning?: string
  input_data?: string
  prompt_content?: string
}

export class ContactEnrichmentProcessorV2 extends BaseProcessor {
  private llmProvider;

  constructor(options: ProcessorOptions = {}) {
    // Perplexity API specific rate limiting configuration for contact enrichment
    const contactEnrichmentBottleneckConfig = {
      maxConcurrent: 10,                   // Reduced from 5 to prevent queue overflow
      minTime: 1800,                      // 1.8 seconds between requests (more conservative for enrichment)
      retryAttempts: 3,                   // Standard retries for LLM API
      retryDelayBase: 2500,               // 2.5 second base delay for retries
      retryDelayMax: 30000,               // Max 30 second retry delay
      timeout: 240000,                    // 4 minutes timeout for LLM processing
      highWater: 300,                     // Higher queue limit for batch processing
      strategy: 'OVERFLOW' as any,        // Use OVERFLOW strategy
      defaultPriority: 4,                 // Normal priority for contact enrichment
      enableJobMetrics: true              // Track LLM API performance
    }

    super('ContactEnrichmentV2', {
      ...options,
      bottleneckConfig: options.bottleneckConfig || contactEnrichmentBottleneckConfig
    })

    // Create a logger adapter that uses our log method
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this));

    // Create LLM provider using Perplexity sonar-deep-research model
    this.llmProvider = LLMFactory.createProvider(
      'perplexity',
      loggerAdapter,
      {
        apiKey: process.env.PERPLEXITY_API_KEY,
      }
    );
  }

  async getUnprocessedEntities(): Promise<UnifiedEntityData[]> {
    const baseConditions = [
      'c.email_verification_status = \'completed\'',
      // Require that v1 enrichment is completed before v2 processing
    ]
    const specificConditions: string[] = [
      'COALESCE(c.contact_enrichment_v2_status, \'pending\') = \'pending\'',
      'c.contact_enrichment_v2_status != \'completed\''
    ]
    return await this.getUnprocessedEntitiesUnified(baseConditions, specificConditions, 'contact', this.options.filters)
  }

  async processEntity(entity: UnifiedEntityData): Promise<{ success: boolean; error?: string }> {
    // Only process contacts for contact enrichment
    if (entity.entity_type !== 'contact') {
      return { success: false, error: 'Contact enrichment V2 only supports contacts' }
    }

    try {
      this.log('info', `Enriching contact V2: ${entity.first_name} ${entity.last_name} (${entity.email})`)

      // Set status to running
      await this.updateContactEnrichmentV2Status(entity.contact_id!, 'running')

      // Create enrichment record in contact_enrichment table with v2 flag
      const enrichmentId = await this.createContactEnrichmentV2(entity.contact_id!, {
        status: 'running',
        processing_attempts: 1
      })

      // Perform comprehensive V2 enrichment
      const result = await this.performComprehensiveV2Enrichment(entity, enrichmentId)

      if (result.success) {
        this.log('info', `Successfully enriched contact V2 ${entity.contact_id}`)
      } else {
        this.log('error', `Failed to enrich contact V2 ${entity.contact_id}: ${result.error}`)
      }

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error enriching contact V2 ${entity.contact_id}: ${errorMessage}`)
      await this.updateContactEnrichmentV2Status(entity.contact_id!, 'failed', errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Perform comprehensive contact enrichment V2 with enhanced data collection
   */
  private async performComprehensiveV2Enrichment(contact: UnifiedEntityData, enrichmentId: number): Promise<{
    success: boolean;
    error?: string
  }> {
    try {
      // Build comprehensive V2 enrichment prompt
      const prompt = await this.buildEnrichmentV2Prompt(contact)

      // Call LLM API
      const apiResponse = await this.callLLMAPI(prompt)

      if (!apiResponse.success) {
        await this.updateContactEnrichmentV2Status(contact.contact_id!, 'failed', apiResponse.error)
        return { success: false, error: apiResponse.error || 'LLM API call failed' }
      }

      // Parse the response to extract enrichment data
      const enrichmentData = this.parseEnrichmentV2Response(apiResponse.content!)
      if (!enrichmentData) {
        await this.updateContactEnrichmentV2Status(contact.contact_id!, 'failed', 'Failed to parse response')
        return { success: false, error: 'Failed to parse enrichment V2 response' }
      }

      // Save all the enrichment data
      const saveResult = await this.saveEnrichmentV2Data(
        contact.contact_id!,
        enrichmentData,
        apiResponse.tokens!,
        apiResponse.usage,
        apiResponse.model,
        enrichmentId
      )

      if (saveResult.success) {
        this.log('info', `Comprehensive V2 enrichment completed and saved for contact ${contact.contact_id}`)
        await this.updateContactEnrichmentV2Status(contact.contact_id!, 'completed')
        return { success: true }
      } else {
        await this.updateContactEnrichmentV2Status(contact.contact_id!, 'failed', saveResult.error)
        return { success: false, error: saveResult.error }
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error in comprehensive V2 enrichment: ${errorMessage}`)
      await this.updateContactEnrichmentV2Status(contact.contact_id!, 'failed', errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Build comprehensive V2 enrichment prompt
   */
  private async buildEnrichmentV2Prompt(contact: UnifiedEntityData): Promise<{ system: string; user: string }> {
    // Use the static getMappings method from BaseScraper
    const mappings = await BaseScraper.getMappings();
    const userPrompt = CONTACT_ENRICHMENT_V2_USER_TEMPLATE_FUNCTION({
      first_name: contact.first_name || '',
      last_name: contact.last_name || '',
      email: contact.email || '',
      title: contact.title || '',
      company_name: contact.company_name || '',
      company_website: contact.company_website || '',
      linkedin_url: contact.linkedin_url || '',
      industry: contact.industry || '',
      contact_country: contact.contact_country || '',
      phone_number: contact.phone_number || '',
      contact_city: (contact as any).contact_city || '',
      contact_state: (contact as any).contact_state || ''
    }, mappings);
    return {
      system: CONTACT_ENRICHMENT_V2_SYSTEM_PROMPT,
      user: userPrompt
    };
  }

  /**
   * Call LLM API for comprehensive V2 enrichment
   */
  private async callLLMAPI(
    prompt: { system: string; user: string }
  ): Promise<{ success: boolean; content?: string; tokens?: number; usage?: any; model?: string; error?: string }> {
    try {
      const messages: LLMMessage[] = [
        { role: 'system', content: prompt.system },
        { role: 'user', content: prompt.user }
      ];

      // Use the LLM provider (Perplexity sonar-deep-research)
      const response = await this.llmProvider.callLLM(messages, {
        temperature: 0.3, // Lower temperature for more consistent structured output
        maxTokens: 4000, // Increased for more detailed responses
        model: 'sonar',
      });

      if (!response.content) {
        return { success: false, error: `No content in API response from ${response.provider}` };
      }

      const tokens = response.usage?.totalTokens || 0;
      this.log('info', `API call successful using ${response.provider} (${response.model}). Tokens used: ${tokens}`);

      return {
        success: true,
        content: response.content,
        tokens: tokens,
        usage: response.usage,
        model: response.model
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.log('error', `LLM API call failed: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }
  }

  private parseEnrichmentV2Response(content: string): EnrichmentV2Result | null {
    try {
      this.log('debug', `Parsing enrichment V2 response content (length: ${content.length})`)

      // Remove thinking tags if present
      let jsonContent = content.replace(/<think>[\s\S]*?<\/think>/g, '')

      // Check if content has <think> tags and extract content after them
      if (content.includes('<think>') && content.includes('</think>')) {
        const thinkEndIndex = content.lastIndexOf('</think>')
        if (thinkEndIndex !== -1) {
          jsonContent = content.substring(thinkEndIndex + 8).trim() // 8 = length of '</think>'
          this.log('debug', `Extracted content after </think> tags`)
        }
      }

      // Try to extract from markdown code blocks first
      const markdownJsonMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/)
      if (markdownJsonMatch) {
        jsonContent = markdownJsonMatch[1].trim()
        this.log('debug', `Extracted JSON from markdown code block`)
      } else {
        // Fallback: try to find JSON object in the content
        const jsonMatch = jsonContent.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          jsonContent = jsonMatch[0]
          this.log('debug', `Extracted JSON using fallback regex`)
        } else {
          this.log('warn', `No JSON found in response content`)
          return null
        }
      }

      // Clean up the JSON content
      jsonContent = jsonContent.trim()

      // Check if JSON appears to be truncated (doesn't end with closing brace)
      if (!jsonContent.endsWith('}')) {
        this.log('warn', `JSON appears to be truncated (doesn't end with }). Attempting to parse anyway.`)
      }

      const parsed = JSON.parse(jsonContent)

      this.log('info', `Successfully parsed enrichment V2 response with ${Object.keys(parsed).length} fields`)

      return {
        // Personal Information
        executive_summary: parsed?.executive_summary || '',
        career_timeline: Array.isArray(parsed?.career_timeline) ? parsed.career_timeline : [],
        
        // Contact Information  
        additional_email: parsed?.additional_email || '',
        phone_number_secondary: parsed?.phone_number_secondary || parsed?.additional_phone || '',
        
        // Social Media
        twitter: parsed?.twitter || '',
        facebook: parsed?.facebook || '',
        instagram: parsed?.instagram || '',
        youtube: parsed?.youtube || '',
        
        // Education
        education_college: parsed?.education_college || '',
        education_college_year_graduated: parsed?.education_college_year_graduated || '',
        education_high_school: parsed?.education_high_school || '',
        education_high_school_year_graduated: parsed?.education_high_school_year_graduated || '',
        
        // Personal Details
        honorable_achievements: Array.isArray(parsed?.honorable_achievements) ? parsed.honorable_achievements : [],
        hobbies: Array.isArray(parsed?.hobbies) ? parsed.hobbies : [],
        age: parsed?.age || '',
        
        // Location Details
        contact_address: parsed?.address || parsed?.contact_address || '',
        contact_zip_code: parsed?.zipcode || parsed?.contact_zip_code || '',
        
        // Contact Metadata
        contact_type: parsed?.contact_type || '',
        relationship_owner: parsed?.relationship_owner || '',
        role_in_decision_making: parsed?.role_in_decision_making || '',
        
        // Interaction Tracking
        source_of_introduction: parsed?.source_of_introduction || '',
        
        // Compliance
        accredited_investor_status: parsed?.accredited_investor_status === true || parsed?.accredited_investor_status === 'true',
        
        // Analysis metadata
        confidence: parsed?.confidence || 0,
        reasoning: parsed?.reasoning || 'No reasoning provided'
      }

    } catch (error) {
      this.log('error', `Failed to parse JSON response: ${error}`)
      this.log('debug', `Response content preview: ${content.substring(0, 500)}...`)
      return null
    }
  }

  /**
   * Create new contact enrichment V2 record and return its ID
   */
  private async createContactEnrichmentV2(contactId: number, data: {
    status?: string
    processing_attempts?: number
    error_message?: string
    completed_at?: string
  }): Promise<number> {
    const fields = Object.keys(data)
    const values = Object.values(data)

    // Check if the contact_id already exists in contact_enrichment table
    const checkSql = `
      SELECT id FROM contact_enrichment WHERE contact_id = $1
    `
    let result = await this.query(checkSql, [contactId])
    if (result.length > 0) {
      return result[0].id as number
    }

    // Insert new record and return the ID with explicit type casting
    const fieldClause = fields.join(', ')
    const valueClause = values.map((_, index) => {
      const paramIndex = index + 2
      // Add explicit type casting for known fields to ensure consistency
      switch (fields[index]) {
        case 'processing_attempts':
          return `$${paramIndex}::integer`
        case 'status':
          return `$${paramIndex}::varchar`
        case 'error_message':
          return `$${paramIndex}::text`
        case 'completed_at':
          return `$${paramIndex}::timestamp with time zone`
        default:
          return `$${paramIndex}`
      }
    }).join(', ')
    
    const sql = `
      INSERT INTO contact_enrichment (contact_id, ${fieldClause}, created_at, updated_at)
      VALUES ($1, ${valueClause}, NOW(), NOW())
      RETURNING id
    `
    result = await this.query(sql, [contactId, ...values])
    return result[0].id as number
  }

  /**
   * Update existing contact enrichment V2 record by ID
   */
  private async updateContactEnrichmentV2(enrichmentId: number, data: {
    status?: string
    processing_attempts?: number
    error_message?: string
    completed_at?: string
    // V2 specific fields will be stored in existing columns
    input_data?: string
    prompt_content?: string
    tokens_used?: number
    llm_model?: string
    llm_usage?: string
  }): Promise<void> {
    const fields = Object.keys(data)
    const values = Object.values(data)

    if (fields.length === 0) return

    // Update existing record with explicit type casting to avoid parameter type inconsistency
    const setClause = fields.map((field, index) => {
      const paramIndex = index + 2
      // Add explicit type casting for known fields to ensure consistency
      switch (field) {
        case 'tokens_used':
          return `${field} = $${paramIndex}::integer`
        case 'processing_attempts':
          return `${field} = $${paramIndex}::integer`
        case 'llm_usage':
          return `${field} = $${paramIndex}::jsonb`
        case 'input_data':
          return `${field} = $${paramIndex}::jsonb`
        case 'status':
          return `${field} = $${paramIndex}::varchar`
        case 'error_message':
          return `${field} = $${paramIndex}::text`
        case 'prompt_content':
          return `${field} = $${paramIndex}::text`
        case 'llm_model':
          return `${field} = $${paramIndex}::varchar`
        case 'completed_at':
          return `${field} = $${paramIndex}::timestamp with time zone`
        default:
          return `${field} = $${paramIndex}`
      }
    }).join(', ')
    
    const sql = `
      UPDATE contact_enrichment 
      SET ${setClause}, updated_at = NOW()
      WHERE id = $1
    `
    await this.query(sql, [enrichmentId, ...values])
  }

  /**
   * Save comprehensive V2 enrichment data to both contacts table and contact_enrichment table
   */
  private async saveEnrichmentV2Data(
    contactId: number,
    enrichmentData: EnrichmentV2Result,
    tokens: number,
    usage?: any,
    model?: string,
    enrichmentId?: number
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // First update the contacts table with the new V2 fields
      const contactUpdateSql = `
        UPDATE contacts SET
          additional_email = COALESCE($2::text, additional_email),
          twitter = COALESCE($3::text, twitter),
          facebook = COALESCE($4::text, facebook),
          instagram = COALESCE($5::text, instagram),
          youtube = COALESCE($6::text, youtube),
          executive_summary = COALESCE($7::text, executive_summary),
          career_timeline = COALESCE($8::jsonb, career_timeline),
          education_college = COALESCE($9::text, education_college),
          education_college_year_graduated = COALESCE($10::text, education_college_year_graduated),
          education_high_school = COALESCE($11::text, education_high_school),
          education_high_school_year_graduated = COALESCE($12::text, education_high_school_year_graduated),
          honorable_achievements = COALESCE($13::jsonb, honorable_achievements),
          hobbies = COALESCE($14::jsonb, hobbies),
          age = COALESCE($15::text, age),
          contact_address = COALESCE($16::text, contact_address),
          contact_zip_code = COALESCE($17::text, contact_zip_code),
          contact_type = COALESCE($18::text, contact_type),
          relationship_owner = COALESCE($19::text, relationship_owner),
          role_in_decision_making = COALESCE($20::text, role_in_decision_making),
          source_of_introduction = COALESCE($21::text, source_of_introduction),
          accredited_investor_status = COALESCE($22::boolean, accredited_investor_status),
          phone_number_secondary = COALESCE($23::text, phone_number_secondary),
          updated_at = NOW()
        WHERE contact_id = $1
      `

      await this.query(contactUpdateSql, [
        contactId,
        enrichmentData.additional_email,
        enrichmentData.twitter,
        enrichmentData.facebook,
        enrichmentData.instagram,
        enrichmentData.youtube,
        enrichmentData.executive_summary ? enrichmentData.executive_summary.substring(0, 5000) : null, // Truncate if too long
        JSON.stringify(enrichmentData.career_timeline),
        enrichmentData.education_college,
        enrichmentData.education_college_year_graduated,
        enrichmentData.education_high_school,
        enrichmentData.education_high_school_year_graduated,
        JSON.stringify(enrichmentData.honorable_achievements),
        JSON.stringify(enrichmentData.hobbies),
        enrichmentData.age,
        enrichmentData.contact_address,
        enrichmentData.contact_zip_code,
        enrichmentData.contact_type,
        enrichmentData.relationship_owner,
        enrichmentData.role_in_decision_making,
        enrichmentData.source_of_introduction,
        enrichmentData.accredited_investor_status,
        enrichmentData.phone_number_secondary
      ])

      // Update the contact_enrichment table with comprehensive V2 data
      if (enrichmentId) {
        await this.updateContactEnrichmentV2(enrichmentId, {
          status: 'completed',
          completed_at: new Date().toISOString(),
          input_data: JSON.stringify(enrichmentData),
          tokens_used: tokens,
          llm_model: model,
          llm_usage: JSON.stringify(usage)
        })
      }

      // Save processing attempt information
      await this.saveProcessingAttempt('contact', contactId, 'contact_enrichment_v2', usage, model, tokens, true)

      this.log('info', `✓ Contact ${contactId} enrichment V2 data saved successfully`)
      return { success: true }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Failed to save enrichment V2 data: ${errorMessage}`)

      // Save failed processing attempt
      try {
        await this.saveProcessingAttempt('contact', contactId, 'contact_enrichment_v2', usage, model, tokens, false, errorMessage)
      } catch (attemptError) {
        this.log('error', `Failed to save processing attempt: ${attemptError}`)
      }

      return { success: false, error: errorMessage }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    if (success) {
      // V2 Enrichment completed successfully
      await this.updateContactEnrichmentV2Status(entityId, 'completed')
    } else {
      // V2 Enrichment failed
      await this.updateContactEnrichmentV2Status(entityId, 'failed', error)
      await this.incrementProcessingErrorCount('contact', entityId)
    }
  }

  /**
   * Update contact enrichment V2 status by contact_id
   */
  protected async updateContactEnrichmentV2Status(contactId: number, status: string, error?: string): Promise<void> {
    const updateSql = `
      UPDATE contacts 
      SET contact_enrichment_v2_status = $2::varchar,
          contact_enrichment_v2_error = $3::text,
          contact_enrichment_v2_date = CASE WHEN $2 = 'completed' THEN NOW() ELSE contact_enrichment_v2_date END,
          updated_at = NOW() 
      WHERE contact_id = $1
    `

    await this.query(updateSql, [contactId, status, error])

    this.log('info', `✓ Contact ${contactId} V2 status updated successfully to ${status}`)
  }
}
