// Enhanced Investment Criteria Processor
// Handles dimensions and metrics separately for professional investment criteria processing

export interface GlobalRowData {
  // Company and contact data as-is
  companyData: Record<string, any>;
  contactData: Record<string, any>;
  // Other investment criteria data (non-special columns)
  otherData: Record<string, any>;
}

export interface DimensionData {
  propertyTypes: string[];
  capitalPositions: string[];
  loanTypes: string[];
}

export interface MetricData {
  dealSizes: string[];
  loanTerms: string[];
  loanExitFees: string[];
  loanOriginationFees: string[];
  interestRates: {
    sofr?: string;
    libor?: string;
    prime?: string;
    tenYT?: string;
    fiveYT?: string;
    wsj?: string;
  };
  // Additional loan-related metrics
  interestRate?: string;
  minLoanDscr?: string;
  maxLoanDscr?: string;
  loanToCostMin?: string;
  loanToCostMax?: string;
  loanToValueMin?: string;
  loanToValueMax?: string;
  structuredLoanTranche?: string;
  minClosingTimeWeeks?: string | number;
  maxClosingTimeWeeks?: string | number;
  recourseLoan?: string;
  recourseLoanArray?: string[]; // Preserve original array structure
  // Specialized maps for ALL metrics - dimension-specific values with defaults
  dealSizeMap?: Map<string, string>;
  loanTermMap?: Map<string, string>;
  loanExitFeesMap?: Map<string, string>;
  loanOriginationFeesMap?: Map<string, string>;
  loanToCostMap?: Map<string, string>;
  loanToValueMap?: Map<string, string>;
  interestRateMap?: Map<string, string>;
  minLoanDscrMap?: Map<string, string>;
  maxLoanDscrMap?: Map<string, string>;
  minClosingTimeWeeksMap?: Map<string, string>;
  maxClosingTimeWeeksMap?: Map<string, string>;
  recourseLoanMap?: Map<string, string>;
  // Interest rate specific maps
  interestRatesSofrMap?: Map<string, string>;
  interestRatesLiborMap?: Map<string, string>;
  interestRatesPrimeMap?: Map<string, string>;
  interestRatesTenYTMap?: Map<string, string>;
  interestRatesFiveYTMap?: Map<string, string>;
  interestRatesWsjMap?: Map<string, string>;
}

export interface EnhancedInvestmentCriteriaRecord {
  id: string;
  globalData: GlobalRowData;
  dimensions: {
    capitalPosition: string;
    propertyType: string;
    loanType: string;
  };
  metrics: {
    dealSize?: string;
    dealSizeMin?: number;
    dealSizeMax?: number;
    loanTerm?: string;
    loanExitFee?: string;
    loanOriginationFee?: string;
    interestRates: Record<string, string>;
    // Additional loan-related metrics
    interestRate?: string;
    minLoanDscr?: string;
    maxLoanDscr?: string;
    loanToCostMin?: string;
    loanToCostMax?: string;
    loanToValueMin?: string;
    loanToValueMax?: string;
    structuredLoanTranche?: string;
    minClosingTimeWeeks?: string;
    maxClosingTimeWeeks?: string;
    recourseLoan?: string | string[];
  };
  notes: string;
}

export interface CentralMappings {
  capitalPositions: Record<string, string[]>;
  propertyTypes: Record<string, string[]>;
}

export interface EnhancedDimensionKeyword {
  keyword: string;
  dimensionType: 'capital_position' | 'loan_type' | 'property_type' | 'special_case';
  originalValue: string;
  propertyType?: string;
  mappedValues: string[];
  mappedFromCapitalPosition?: string;
  isFromMapping?: boolean;
}

export interface ProcessingInput {
  csvRow: Record<string, any>;
  rawHeaderMappings: {
    companies: Record<string, string[]>;
    contacts: Record<string, string[]>;
    investment_criteria_central: Record<string, string[]>;
    investment_criteria_debt: Record<string, string[]>;
    investment_criteria_equity: Record<string, string[]>;
  };
  centralMappings: CentralMappings;
}

export class InvestmentCriteriaProcessor {
  /**
   * Main processing method that handles the enhanced investment criteria logic
   * Returns restructured data with separate contact, company, and IC records
   */
  static async processEnhanced(input: ProcessingInput): Promise<{
    contactData: Record<string, any>;
    companyData: Record<string, any>;
    investmentCriteriaData: Record<string, any>[];
    errors: string[];
    warnings: string[];
    recordCount: number;
    investmentCriteriaCreated: number;
  }> {
    const { csvRow, rawHeaderMappings, centralMappings } = input;
    const records: EnhancedInvestmentCriteriaRecord[] = [];
    const errors: string[] = [];
    const warnings: string[] = [];
    const investmentCriteriaCreated = 0;

    try {
      console.log('[DEBUG] Processing enhanced investment criteria...');
      
      // Step 1: Create global row data (everything except special columns)
      const globalData = this.createGlobalRowData(csvRow, rawHeaderMappings);
      console.log('[DEBUG] Global data:', JSON.stringify(globalData, null, 2));
      
      // Step 2: Extract and parse dimensions
      const dimensionData = this.extractDimensions(csvRow, rawHeaderMappings, centralMappings);
      
      // Check if we have capital position - if not, skip investment criteria processing
      if (dimensionData.capitalPositions.length === 0) {
        console.log('[DEBUG] No capital position found - skipping investment criteria processing for this row');
        return {
          contactData: globalData.contactData,
          companyData: globalData.companyData,
          investmentCriteriaData: [],
          errors: [],
          warnings: ['No capital position found - investment criteria processing skipped'],
          recordCount: 0,
          investmentCriteriaCreated: 0
        };
      }
      
      console.log('[DEBUG] Dimension data:', JSON.stringify(dimensionData, null, 2));
      
      // Step 3 & 4: Apply enhanced decision tree logic with mapping-aware metrics
      const generatedRecords = this.applyEnhancedDecisionTreeLogic(
        globalData,
        dimensionData,
        csvRow,
        rawHeaderMappings,
        centralMappings
      );
      
      records.push(...generatedRecords);
      
      console.log(`[DEBUG] Generated ${records.length} investment criteria records`);

      // Convert to legacy format first, then to flat database format
      const legacyInvestmentCriteriaData = this.convertToLegacyFormat(records);
      const flatInvestmentCriteriaData = this.convertToFlatDatabaseFormat(legacyInvestmentCriteriaData);
      
      // console.log(`[DEBUG] Flat investment criteria data: ${JSON.stringify(flatInvestmentCriteriaData, null, 2)}`);
      
      return {
        contactData: globalData.contactData,
        companyData: globalData.companyData,
        investmentCriteriaData: flatInvestmentCriteriaData,
        errors,
        warnings,
        recordCount: flatInvestmentCriteriaData.length,
        investmentCriteriaCreated
      };

    } catch (error) {
      console.error('[ERROR] Investment criteria processing failed:', error);
      errors.push(`Investment criteria processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      // Return empty data with errors
      return {
        contactData: {},
        companyData: {},
        investmentCriteriaData: [],
        errors,
        warnings,
        recordCount: 0,
        investmentCriteriaCreated: 0
      };
    }
  }








  /**
   * Creates global row data containing company, contact, and other non-special data
   * Uses rawHeaderMappings to properly categorize data by table
   */
  private static createGlobalRowData(
    csvRow: Record<string, any>,
    rawHeaderMappings: {
      companies: Record<string, string[]>;
      contacts: Record<string, string[]>;
      investment_criteria_central: Record<string, string[]>;
      investment_criteria_debt: Record<string, string[]>;
      investment_criteria_equity: Record<string, string[]>;
    }
  ): GlobalRowData {
    const companyData: Record<string, any> = {};
    const contactData: Record<string, any> = {};
    const otherData: Record<string, any> = {};

    // Helper function to extract and combine values from multiple headers
    const extractAndCombineValues = (headers: string[]): string | undefined => {
      const values = headers
        .map(header => csvRow[header])
        .filter(value => value !== undefined && value !== null && value !== '')
        .map(value => String(value).trim())
        .filter(value => value.length > 0);
      
      return values.length > 0 ? values.join(' / ') : undefined;
    };

    // Helper function to extract array values (for fields that should be arrays in DB)
    const extractArrayValues = (headers: string[]): string[] => {
      const values = headers
        .map(header => csvRow[header])
        .filter(value => value !== undefined && value !== null && value !== '')
        .map(value => String(value).trim())
        .filter(value => value.length > 0);
      
      // Split by common delimiters and flatten
      const allValues: string[] = [];
      values.forEach(value => {
        // Split by / or , and add to array
        const splitValues = value.split(/[\/,]/).map(v => v.trim()).filter(v => v.length > 0);
        allValues.push(...splitValues);
      });
      
      // Remove duplicates
      return [...new Set(allValues)];
    };

    // Process company mappings - handle capital_position as array, others as combined strings
    Object.entries(rawHeaderMappings.companies).forEach(([dbField, csvHeaders]) => {
      if (dbField === 'capital_position') {
        const arrayValues = extractArrayValues(csvHeaders);
        console.log(`[DEBUG] Company data: ${dbField} - [${arrayValues.join(', ')}] (as array)`);
        if (arrayValues.length > 0) {
          companyData[dbField] = arrayValues;
        }
      } else {
        const combinedValue = extractAndCombineValues(csvHeaders);
        console.log(`[DEBUG] Company data: ${dbField} - ${combinedValue}`);
        if (combinedValue) {
          companyData[dbField] = combinedValue;
        }
      }
    });

    // Process contact mappings - handle capital_position as array, others as combined strings
    Object.entries(rawHeaderMappings.contacts).forEach(([dbField, csvHeaders]) => {
      if (dbField === 'capital_position') {
        const arrayValues = extractArrayValues(csvHeaders);
        // console.log(`[DEBUG] Contact data: ${dbField} - [${arrayValues.join(', ')}] (as array)`);
        if (arrayValues.length > 0) {
          contactData[dbField] = arrayValues;
        }
      } else {
        const combinedValue = extractAndCombineValues(csvHeaders);
        // console.log(`[DEBUG] Contact data: ${dbField} - ${combinedValue}`);
        if (combinedValue) {
          contactData[dbField] = combinedValue;
        }
      }
    });

    // Process investment criteria mappings - combine multiple headers for same DB field
    // Handle all three investment criteria tables
    [...Object.entries(rawHeaderMappings.investment_criteria_central || {}),
     ...Object.entries(rawHeaderMappings.investment_criteria_debt || {}),
     ...Object.entries(rawHeaderMappings.investment_criteria_equity || {})].forEach(([dbField, csvHeaders]) => {
      const combinedValue = extractAndCombineValues(csvHeaders);
      if (combinedValue) {
        otherData[dbField] = combinedValue;
      }
    });

    // Ensure notes are extracted if present in any mapping
    let notesValue: string | undefined;
    
    // Check all mappings for notes field
    [...Object.entries(rawHeaderMappings.companies), 
     ...Object.entries(rawHeaderMappings.contacts), 
     ...Object.entries(rawHeaderMappings.investment_criteria_central || {}),
     ...Object.entries(rawHeaderMappings.investment_criteria_debt || {}),
     ...Object.entries(rawHeaderMappings.investment_criteria_equity || {})].forEach(([dbField, csvHeaders]) => {
      if (dbField === 'notes' || dbField.toLowerCase().includes('note')) {
        const extractedNotes = extractAndCombineValues(csvHeaders);
        if (extractedNotes && extractedNotes.trim() !== '') {
          notesValue = extractedNotes;
          console.log(`[DEBUG] Notes extracted: ${extractedNotes}`);
        }
      }
    });

    // Add notes to otherData if found
    if (notesValue) {
      otherData.notes = notesValue;
    }

    return { companyData, contactData, otherData };
  }

  /**
   * Extracts and parses dimension data
   */
  private static extractDimensions(
    csvRow: Record<string, any>,
    rawHeaderMappings: {
      companies: Record<string, string[]>;
      contacts: Record<string, string[]>;
      investment_criteria_central: Record<string, string[]>;
      investment_criteria_debt: Record<string, string[]>;
      investment_criteria_equity: Record<string, string[]>;
    },
    centralMappings: CentralMappings
  ): DimensionData {
    const dimensionData: DimensionData = {
      propertyTypes: [],
      capitalPositions: [],
      loanTypes: []
    };

    // Check if there's capital position data - if not, skip investment criteria processing
    let hasCapitalPosition = false;

    // Extract dimensions from all investment criteria table mappings
    [...Object.entries(rawHeaderMappings.investment_criteria_central || {}),
     ...Object.entries(rawHeaderMappings.investment_criteria_debt || {}),
     ...Object.entries(rawHeaderMappings.investment_criteria_equity || {})].forEach(([dbField, csvHeaders]) => {
      csvHeaders.forEach(csvHeader => {
        const value = csvRow[csvHeader];
        if (!value) return;

        switch (dbField) {
          case 'property_types':
            dimensionData.propertyTypes.push(...this.parseDelimitedValues(value));
            break;
          case 'capital_position':
            hasCapitalPosition = true;
            dimensionData.capitalPositions.push(...this.parseDelimitedValues(value));
            
            // NOTE: We no longer automatically expand all capital positions into loan types
            // Only explicit loan types from CSV will be included via the 'loan_type' case
            break;
          case 'loan_type':
            // Add explicit loan types from CSV
            dimensionData.loanTypes.push(...this.parseDelimitedValues(value));
            break;
        }
      });
    });

    // Remove duplicates
    dimensionData.propertyTypes = [...new Set(dimensionData.propertyTypes)];
    dimensionData.capitalPositions = [...new Set(dimensionData.capitalPositions)];
    dimensionData.loanTypes = [...new Set(dimensionData.loanTypes)];

    // console.log('[DEBUG] Final extracted dimensions:', {
    //   propertyTypes: dimensionData.propertyTypes,
    //   capitalPositions: dimensionData.capitalPositions,
    //   loanTypes: dimensionData.loanTypes,
    //   hasCapitalPosition
    // });

    // If no capital position, return empty dimension data to signal skipping
    if (!hasCapitalPosition) {
      console.log('[DEBUG] No capital position found - skipping investment criteria processing');
      return {
        propertyTypes: [],
        capitalPositions: [],
        loanTypes: []
      };
    }

    return dimensionData;
  }

  /**
   * Extracts and parses metric data into specialized maps
   */
  private static extractMetrics(
    csvRow: Record<string, any>,
    rawHeaderMappings: {
      companies: Record<string, string[]>;
      contacts: Record<string, string[]>;
      investment_criteria_central: Record<string, string[]>;
      investment_criteria_debt: Record<string, string[]>;
      investment_criteria_equity: Record<string, string[]>;
    }
  ): MetricData {
    const metricData: MetricData = {
      dealSizes: [],
      loanTerms: [],
      loanExitFees: [],
      loanOriginationFees: [],
      interestRates: {}
    };

    // Raw metric storage for creating specialized maps
    const rawDealSizes: string[] = [];
    const rawLoanTerms: string[] = [];
    const rawLoanExitFees: string[] = [];
    const rawLoanOriginationFees: string[] = [];
    let rawLoanToCost: string = '';
    let rawLoanToValue: string = '';
    let rawInterestRate: string = '';
    let rawMinLoanDscr: string = '';
    let rawMaxLoanDscr: string = '';
    let rawStructuredLoanTranche: string = '';
    let rawClosingTimeWeeks: string = '';
    let rawMinClosingTimeWeeks: string = '';
    let rawMaxClosingTimeWeeks: string = '';
    let rawRecourseLoan: string = '';
    let rawInterestRateSofr: string = '';
    let rawInterestRateLibor: string = '';
    let rawInterestRatePrime: string = '';
    let rawInterestRateTenYT: string = '';
    let rawInterestRateFiveYT: string = '';
    let rawInterestRateWsj: string = '';

    // Extract raw metrics from all investment criteria table mappings
    [...Object.entries(rawHeaderMappings.investment_criteria_central || {}),
     ...Object.entries(rawHeaderMappings.investment_criteria_debt || {}),
     ...Object.entries(rawHeaderMappings.investment_criteria_equity || {})].forEach(([dbField, csvHeaders]) => {
      csvHeaders.forEach(csvHeader => {
        const value = csvRow[csvHeader];
        if (!value) return;

        switch (dbField) {
          case 'minimum_deal_size':
          case 'maximum_deal_size':
            rawDealSizes.push(...this.parseDelimitedValues(value));
            break;
          case 'min_loan_term':
          case 'max_loan_term':
            rawLoanTerms.push(...this.parseDelimitedValues(value));
            break;
          case 'loan_exit_fee_min':
          case 'loan_exit_fee_max':
            rawLoanExitFees.push(...this.parseDelimitedValues(value));
            metricData.loanExitFees.push(...this.parseDelimitedValues(value));
            break;
          case 'loan_origination_fee_min':
          case 'loan_origination_fee_max':
            rawLoanOriginationFees.push(...this.parseDelimitedValues(value));
            metricData.loanOriginationFees.push(...this.parseDelimitedValues(value));
            break;
          case 'interest_rate_sofr':
            // CHANGED: Always treat interest rates as metrics that can be split by dimensions
            rawInterestRateSofr = value;
            metricData.interestRates.sofr = value;
            break;
          case 'interest_rate_libor':
            rawInterestRateLibor = value;
            metricData.interestRates.libor = value;
            break;
          case 'interest_rate_prime':
            rawInterestRatePrime = value;
            metricData.interestRates.prime = value;
            break;
          case 'interest_rate_10yt':
            rawInterestRateTenYT = value;
            metricData.interestRates.tenYT = value;
            break;
          case 'interest_rate_5yt':
            rawInterestRateFiveYT = value;
            metricData.interestRates.fiveYT = value;
            break;
          case 'interest_rate_wsj':
            rawInterestRateWsj = value;
            metricData.interestRates.wsj = value;
            break;
          // Additional loan-related metrics (apply delimiter splitting)
          case 'interest_rate':
            const interestRateVals = this.parseDelimitedValues(value);
            rawInterestRate = interestRateVals.join(' / ');
            metricData.interestRate = interestRateVals.join(' / ');
            break;
          case 'min_loan_dscr':
            const minDscrVals = this.parseDelimitedValues(value);
            rawMinLoanDscr = minDscrVals.join(' / ');
            metricData.minLoanDscr = minDscrVals.join(' / ');
            break;
          case 'max_loan_dscr':
            const maxDscrVals = this.parseDelimitedValues(value);
            rawMaxLoanDscr = maxDscrVals.join(' / ');
            metricData.maxLoanDscr = maxDscrVals.join(' / ');
            break;
          case 'loan_to_cost_min':
          case 'loan_to_cost_max':
            const ltcVals = this.parseDelimitedValues(value);
            rawLoanToCost = ltcVals.join(' / ');
            metricData.loanToCostMin = ltcVals.join(' / ');
            metricData.loanToCostMax = ltcVals.join(' / ');
            break;
          case 'loan_to_value_min':
          case 'loan_to_value_max':
            const ltvVals = this.parseDelimitedValues(value);
            rawLoanToValue = ltvVals.join(' / ');
            metricData.loanToValueMin = ltvVals.join(' / ');
            metricData.loanToValueMax = ltvVals.join(' / ');
            break;
          case 'structured_loan_tranche':
            const trancheVals = this.parseDelimitedValues(value);
            rawStructuredLoanTranche = trancheVals.join(' / ');
            metricData.structuredLoanTranche = trancheVals.join(' / ');
            break;
          case 'min_closing_time_weeks':
          case 'max_closing_time_weeks':
          case 'closing_time_weeks':
            const closingTimeVals = this.parseDelimitedValues(value);
            rawClosingTimeWeeks = closingTimeVals.join(' / ');
            
            // Parse individual closing time values to extract min/max
            const parsedClosingTimes = closingTimeVals.map(val => this.parseClosingTime(val));
            const minTimes = parsedClosingTimes.map(parsed => parsed.min).filter(t => t !== undefined);
            const maxTimes = parsedClosingTimes.map(parsed => parsed.max).filter(t => t !== undefined);
            
            if (minTimes.length > 0) {
              rawMinClosingTimeWeeks = minTimes.join(' / ');
              // For database storage, use the minimum of all min times
              metricData.minClosingTimeWeeks = Math.min(...minTimes);
            }
            if (maxTimes.length > 0) {
              rawMaxClosingTimeWeeks = maxTimes.join(' / ');
              // For database storage, use the maximum of all max times
              metricData.maxClosingTimeWeeks = Math.max(...maxTimes);
            }
            break;
          case 'recourse_loan':
            const recourseVals = this.parseDelimitedValues(value);
            rawRecourseLoan = recourseVals.join(' / ');
            metricData.recourseLoan = recourseVals.join(' / ');
            // Preserve original array structure
            metricData.recourseLoanArray = recourseVals;
            break;
        }
      });
    });

    // Keep original arrays for backward compatibility
    metricData.dealSizes = [...new Set(rawDealSizes)];
    metricData.loanTerms = [...new Set(rawLoanTerms)];
    metricData.loanExitFees = [...new Set(metricData.loanExitFees)];
    metricData.loanOriginationFees = [...new Set(metricData.loanOriginationFees)];
    
    return metricData;
  }

  /**
   * Enhanced version that creates specialized maps with mapping data and dimension context
   */
  private static extractMetricsWithMappings(
    csvRow: Record<string, any>,
    rawHeaderMappings: {
      companies: Record<string, string[]>;
      contacts: Record<string, string[]>;
      investment_criteria_central: Record<string, string[]>;
      investment_criteria_debt: Record<string, string[]>;
      investment_criteria_equity: Record<string, string[]>;
    },
    dimensionData: DimensionData,
    centralMappings: CentralMappings
  ): MetricData {
    // First get basic metrics
    const metricData = this.extractMetrics(csvRow, rawHeaderMappings);
    
    // Create comprehensive dimension keywords from mappings and dimension data
    const dimensionKeywords = this.createComprehensiveDimensionKeywords(dimensionData, centralMappings);
    
    // console.log('[DEBUG] Comprehensive dimension keywords:', dimensionKeywords);

    // Raw metric storage for creating specialized maps
    const rawDealSizes: string[] = [];
    const rawLoanTerms: string[] = [];
    const rawLoanExitFees: string[] = [];
    const rawLoanOriginationFees: string[] = [];
    let rawLoanToCost: string = '';
    let rawLoanToValue: string = '';
    let rawInterestRate: string = '';
    let rawMinLoanDscr: string = '';
    let rawMaxLoanDscr: string = '';
    let rawStructuredLoanTranche: string = '';
    let rawClosingTimeWeeks: string = '';
    let rawMinClosingTimeWeeks: string = '';
    let rawMaxClosingTimeWeeks: string = '';
    let rawRecourseLoan: string = '';
    let rawInterestRateSofr: string = '';
    let rawInterestRateLibor: string = '';
    let rawInterestRatePrime: string = '';
    let rawInterestRateTenYT: string = '';
    let rawInterestRateFiveYT: string = '';
    let rawInterestRateWsj: string = '';

    // Re-extract raw metrics for specialized map creation
    [...Object.entries(rawHeaderMappings.investment_criteria_central || {}),
     ...Object.entries(rawHeaderMappings.investment_criteria_debt || {}),
     ...Object.entries(rawHeaderMappings.investment_criteria_equity || {})].forEach(([dbField, csvHeaders]) => {
      csvHeaders.forEach(csvHeader => {
        const value = csvRow[csvHeader];
        if (!value) return;

        switch (dbField) {
          case 'minimum_deal_size':
          case 'maximum_deal_size':
            rawDealSizes.push(...this.parseDelimitedValues(value));
            break;
          case 'min_loan_term':
          case 'max_loan_term':
            rawLoanTerms.push(...this.parseDelimitedValues(value));
            break;
          case 'loan_exit_fee_min':
          case 'loan_exit_fee_max':
            rawLoanExitFees.push(...this.parseDelimitedValues(value));
            break;
          case 'loan_origination_fee_min':
          case 'loan_origination_fee_max':
            rawLoanOriginationFees.push(...this.parseDelimitedValues(value));
            break;
          case 'interest_rate':
            // Split interest rate values by delimiters
            const interestRateValues = this.parseDelimitedValues(value);
            rawInterestRate = interestRateValues.join(' / ');
            break;
          case 'min_loan_dscr':
            // Split DSCR values by delimiters
            const minDscrValues = this.parseDelimitedValues(value);
            rawMinLoanDscr = minDscrValues.join(' / ');
            break;
          case 'max_loan_dscr':
            // Split DSCR values by delimiters
            const maxDscrValues = this.parseDelimitedValues(value);
            rawMaxLoanDscr = maxDscrValues.join(' / ');
            break;
          case 'loan_to_cost_min':
          case 'loan_to_cost_max':
            // Split LTC values by delimiters
            const ltcValues = this.parseDelimitedValues(value);
            rawLoanToCost = ltcValues.join(' / ');
            break;
          case 'loan_to_value_min':
          case 'loan_to_value_max':
            // Split LTV values by delimiters
            const ltvValues = this.parseDelimitedValues(value);
            rawLoanToValue = ltvValues.join(' / ');
            break;
          case 'structured_loan_tranche':
            // Split loan tranche values by delimiters
            const trancheValues = this.parseDelimitedValues(value);
            rawStructuredLoanTranche = trancheValues.join(' / ');
            break;
          case 'min_closing_time_weeks':
          case 'max_closing_time_weeks':
          case 'closing_time_weeks':
            // Split closing time values by delimiters
            const closingTimeValues = this.parseDelimitedValues(value);
            rawClosingTimeWeeks = closingTimeValues.join(' / ');
            
            // Parse individual closing time values to extract min/max
            const parsedClosingTimesWithMappings = closingTimeValues.map(val => this.parseClosingTime(val));
            const minTimesWithMappings = parsedClosingTimesWithMappings.map(parsed => parsed.min).filter(t => t !== undefined);
            const maxTimesWithMappings = parsedClosingTimesWithMappings.map(parsed => parsed.max).filter(t => t !== undefined);
            
            if (minTimesWithMappings.length > 0) {
              rawMinClosingTimeWeeks = minTimesWithMappings.join(' / ');
              // For database storage, use the minimum of all min times
              metricData.minClosingTimeWeeks = Math.min(...minTimesWithMappings);
            }
            if (maxTimesWithMappings.length > 0) {
              rawMaxClosingTimeWeeks = maxTimesWithMappings.join(' / ');
              // For database storage, use the maximum of all max times
              metricData.maxClosingTimeWeeks = Math.max(...maxTimesWithMappings);
            }
            break;
          case 'recourse_loan':
            // Split recourse loan values by delimiters
            const recourseValues = this.parseDelimitedValues(value);
            rawRecourseLoan = recourseValues.join(' / ');
            // Preserve original array structure
            metricData.recourseLoanArray = recourseValues;
            break;
          case 'interest_rate_sofr':
            // CHANGED: Always treat as metric that can be split by dimensions
            rawInterestRateSofr = value;
            break;
          case 'interest_rate_libor':
            rawInterestRateLibor = value;
            break;
          case 'interest_rate_prime':
            rawInterestRatePrime = value;
            break;
          case 'interest_rate_10yt':
            rawInterestRateTenYT = value;
            break;
          case 'interest_rate_5yt':
            rawInterestRateFiveYT = value;
            break;
          case 'interest_rate_wsj':
            rawInterestRateWsj = value;
            break;
        }
      });
    });
    
    // Create specialized maps with enhanced mapping detection
    metricData.dealSizeMap = this.createSpecializedMetricMap(rawDealSizes, dimensionKeywords);
    metricData.loanTermMap = this.createSpecializedMetricMap(rawLoanTerms, dimensionKeywords);
    metricData.loanExitFeesMap = this.createSpecializedMetricMap(rawLoanExitFees, dimensionKeywords);
    metricData.loanOriginationFeesMap = this.createSpecializedMetricMap(rawLoanOriginationFees, dimensionKeywords);
    metricData.loanToCostMap = this.createSpecializedMetricMap(rawLoanToCost ? [rawLoanToCost] : [], dimensionKeywords);
    metricData.loanToValueMap = this.createSpecializedMetricMap(rawLoanToValue ? [rawLoanToValue] : [], dimensionKeywords);
    metricData.interestRateMap = this.createSpecializedMetricMap(rawInterestRate ? [rawInterestRate] : [], dimensionKeywords);
    metricData.minLoanDscrMap = this.createSpecializedMetricMap(rawMinLoanDscr ? [rawMinLoanDscr] : [], dimensionKeywords);
    metricData.maxLoanDscrMap = this.createSpecializedMetricMap(rawMaxLoanDscr ? [rawMaxLoanDscr] : [], dimensionKeywords);
    metricData.minClosingTimeWeeksMap = this.createSpecializedMetricMap(rawMinClosingTimeWeeks ? [rawMinClosingTimeWeeks] : [], dimensionKeywords);
    metricData.maxClosingTimeWeeksMap = this.createSpecializedMetricMap(rawMaxClosingTimeWeeks ? [rawMaxClosingTimeWeeks] : [], dimensionKeywords);
    metricData.recourseLoanMap = this.createSpecializedMetricMap(rawRecourseLoan ? [rawRecourseLoan] : [], dimensionKeywords);
    
    // CHANGED: Create specialized maps for interest rates treating them as metrics
    metricData.interestRatesSofrMap = this.createSpecializedMetricMap(rawInterestRateSofr ? [rawInterestRateSofr] : [], dimensionKeywords);
    metricData.interestRatesLiborMap = this.createSpecializedMetricMap(rawInterestRateLibor ? [rawInterestRateLibor] : [], dimensionKeywords);
    metricData.interestRatesPrimeMap = this.createSpecializedMetricMap(rawInterestRatePrime ? [rawInterestRatePrime] : [], dimensionKeywords);
    metricData.interestRatesTenYTMap = this.createSpecializedMetricMap(rawInterestRateTenYT ? [rawInterestRateTenYT] : [], dimensionKeywords);
    metricData.interestRatesFiveYTMap = this.createSpecializedMetricMap(rawInterestRateFiveYT ? [rawInterestRateFiveYT] : [], dimensionKeywords);
    metricData.interestRatesWsjMap = this.createSpecializedMetricMap(rawInterestRateWsj ? [rawInterestRateWsj] : [], dimensionKeywords);

    console.log('[DEBUG] Enhanced specialized metric maps with mapping context:', {
      dealSizeMap: metricData.dealSizeMap,
      loanTermMap: metricData.loanTermMap,
      loanToCostMap: metricData.loanToCostMap,
      interestRateMap: metricData.interestRateMap,
      minLoanDscrMap: metricData.minLoanDscrMap,
      interestRatesSofrMap: metricData.interestRatesSofrMap,
    });

    return metricData;
  }

  /**
   * Creates comprehensive dimension keywords from mapping data and dimension values
   * This allows us to detect which dimension data got matched at the map creation point
   */
  private static createComprehensiveDimensionKeywords(
    dimensionData: DimensionData,
    centralMappings: CentralMappings
  ): EnhancedDimensionKeyword[] {
    const keywords: EnhancedDimensionKeyword[] = [];

    // Add capital positions from dimension data with mapping context
    dimensionData.capitalPositions.forEach(capitalPos => {
      keywords.push({
        keyword: capitalPos.toLowerCase(),
        dimensionType: 'capital_position',
        originalValue: capitalPos,
        propertyType: undefined,
        mappedValues: centralMappings.capitalPositions[capitalPos] || []
      });
    });

    // Add loan types from dimension data
    dimensionData.loanTypes.forEach(loanType => {
      // Find which capital position this loan type is mapped to
      let mappedFromCapitalPosition: string | undefined;
      Object.entries(centralMappings.capitalPositions).forEach(([capitalPos, loanTypes]) => {
        if (loanTypes.includes(loanType)) {
          mappedFromCapitalPosition = capitalPos;
        }
      });

      keywords.push({
        keyword: loanType.toLowerCase(),
        dimensionType: 'loan_type',
        originalValue: loanType,
        propertyType: undefined,
        mappedValues: [],
        mappedFromCapitalPosition
      });
    });

    // Add property types from dimension data
    dimensionData.propertyTypes.forEach(propertyType => {
      keywords.push({
        keyword: propertyType.toLowerCase(),
        dimensionType: 'property_type',
        originalValue: propertyType,
        propertyType: propertyType,
        mappedValues: centralMappings.propertyTypes[propertyType] || []
      });
    });

    // Add additional mapped values from central mappings that might not be in dimension data
    Object.entries(centralMappings.capitalPositions).forEach(([capitalPos, loanTypes]) => {
      // Check if capital position is already added
      const existing = keywords.find(k => k.originalValue === capitalPos && k.dimensionType === 'capital_position');
      if (!existing) {
        keywords.push({
          keyword: capitalPos.toLowerCase(),
          dimensionType: 'capital_position',
          originalValue: capitalPos,
          propertyType: undefined,
          mappedValues: loanTypes,
          isFromMapping: true
        });
      }
    });

    // Add special cases
    keywords.push({
      keyword: 'special situations',
      dimensionType: 'special_case',
      originalValue: 'Special Situations',
      propertyType: undefined,
      mappedValues: []
    });

    // console.log('[DEBUG] Created comprehensive dimension keywords:', keywords);
    return keywords;
  }



  /**
   * Enhanced decision tree logic that uses mapping-aware metrics
   */
  private static applyEnhancedDecisionTreeLogic(
    globalData: GlobalRowData,
    dimensionData: DimensionData,
    csvRow: Record<string, any>,
    rawHeaderMappings: {
      companies: Record<string, string[]>;
      contacts: Record<string, string[]>;
      investment_criteria_central: Record<string, string[]>;
      investment_criteria_debt: Record<string, string[]>;
      investment_criteria_equity: Record<string, string[]>;
    },
    centralMappings: CentralMappings
  ): EnhancedInvestmentCriteriaRecord[] {
    // Use the enhanced metrics extraction with mapping context
    const enhancedMetricData = this.extractMetricsWithMappings(csvRow, rawHeaderMappings, dimensionData, centralMappings);

    // Check if there are specific metrics (e.g., detailed deal sizes with capital positions/loan types)
    const hasSpecificMetrics = this.checkForSpecificMetrics(enhancedMetricData, centralMappings, dimensionData);

    if (hasSpecificMetrics) {
      // Create specific records using enhanced metrics with mapping context
      const specificRecords = this.createSpecificRecords(globalData, dimensionData, enhancedMetricData, centralMappings);
      return specificRecords;
    } else {
      // Create general records with all dimensions clubbed
      const generalRecords = this.createGeneralRecords(globalData, dimensionData, enhancedMetricData);
      return generalRecords;
    }
  }

  /**
   * Check if metrics contain specific mentions of dimensions
   */
  private static checkForSpecificMetrics(metricData: MetricData, centralMappings: CentralMappings, dimensionData: DimensionData): boolean {
    // Collect all metric values that might contain dimension mentions
    const allMetricValues: string[] = [];
    
    // Add deal sizes (most likely to have specific mentions)
    allMetricValues.push(...metricData.dealSizes);
    allMetricValues.push(...metricData.loanTerms);
    allMetricValues.push(...metricData.loanExitFees);
    allMetricValues.push(...metricData.loanOriginationFees);
    
    // Add other string metrics that might have specific mentions
    if (metricData.structuredLoanTranche) allMetricValues.push(metricData.structuredLoanTranche);
    if (metricData.minClosingTimeWeeks) allMetricValues.push(String(metricData.minClosingTimeWeeks));
    if (metricData.maxClosingTimeWeeks) allMetricValues.push(String(metricData.maxClosingTimeWeeks));
    if (metricData.recourseLoan) allMetricValues.push(metricData.recourseLoan);

    // Use dynamic keywords from database instead of hardcoded values
    const dynamicCapitalPositions = Object.keys(centralMappings.capitalPositions).map(pos => pos.toLowerCase());
    const dynamicLoanTypes = dimensionData.loanTypes.map(type => type.toLowerCase());
    const nonmatchingCases = ['special situations']; // Keep as business logic
    
    const allKeywords = [
      ...dynamicCapitalPositions,
      ...dynamicLoanTypes,
      ...nonmatchingCases
    ];

    // Check if any metric values contain dimension keywords
    for (const value of allMetricValues) {
      const lowerValue = value.toLowerCase();
      for (const keyword of allKeywords) {
        if (lowerValue.includes(keyword)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Creates records for specific metric-dimension combinations with hierarchical logic
   */
  private static createSpecificRecords(
    globalData: GlobalRowData,
    dimensionData: DimensionData,
    metricData: MetricData,
    centralMappings: CentralMappings
  ): EnhancedInvestmentCriteriaRecord[] {
    // Use the new specialized maps approach directly
    return this.createSpecificCombinationsFromMaps(globalData, dimensionData, metricData, centralMappings);
  }



  /**
   * New method: Creates specific combinations using specialized metric maps
   * Based on actual metric analysis findings, not hardcoded combinations
   */
  private static createSpecificCombinationsFromMaps(
    globalData: GlobalRowData,
    dimensionData: DimensionData,
    metricData: MetricData,
    centralMappings: CentralMappings
  ): EnhancedInvestmentCriteriaRecord[] {
    const records: EnhancedInvestmentCriteriaRecord[] = [];
    
    console.log('[DEBUG] Creating specific combinations from actual metric analysis...');

    // Helper function to get metric value for a specific combination or default
    const getMetricValue = (map: Map<string, string> | undefined, keys: string[]): string | undefined => {
      if (!map || map.size === 0) return undefined;
      
      // Try to find specific value for the combination (exact match only)
      for (const key of keys) {
        if (map.has(key.toLowerCase())) {
          return map.get(key.toLowerCase());
        }
      }
      
      // REMOVED: No longer fallback to mapped loan types to prevent cross-contamination
      // Only return default if no specific mapping exists AND there's a true default
      const defaultValue = map.get('default');
      if (defaultValue) {
        console.log(`[DEBUG] Using default value for keys [${keys.join(', ')}]: ${defaultValue}`);
        return defaultValue;
      }
      
      // Return undefined if no specific mapping and no default - prevents mismatched assignments
      console.log(`[DEBUG] No mapping found for keys [${keys.join(', ')}] - avoiding mismatch`);
      return undefined;
    };

    // Collect all specific dimension keywords that have actual metric mappings
    const specificCombinationsFound = new Set<string>();
    
    // Check all metric maps for specific dimension keywords
    const allMaps = [
      metricData.dealSizeMap,
      metricData.loanTermMap,
      metricData.loanExitFeesMap,
      metricData.loanOriginationFeesMap,
      metricData.loanToCostMap,
      metricData.loanToValueMap,
      metricData.interestRateMap,
      metricData.minLoanDscrMap,
      metricData.maxLoanDscrMap,
      metricData.minClosingTimeWeeksMap,
      metricData.maxClosingTimeWeeksMap,
      metricData.recourseLoanMap,
      metricData.interestRatesSofrMap,
      metricData.interestRatesLiborMap,
      metricData.interestRatesPrimeMap,
      metricData.interestRatesTenYTMap,
      metricData.interestRatesFiveYTMap,
      metricData.interestRatesWsjMap
    ];

    // Find all specific dimension keywords that have non-default mappings
    allMaps.forEach(map => {
      if (map) {
        map.forEach((value, key) => {
          if (key !== 'default') {
            specificCombinationsFound.add(key);
          }
        });
      }
    });

    console.log('[DEBUG] Specific combinations found in metrics:', Array.from(specificCombinationsFound));

    // Track which dimensions have been used in specific records
    const usedLoanTypes = new Set<string>();
    const usedCapitalPositions = new Set<string>();

    // Create specific records for each found combination
    specificCombinationsFound.forEach(dimensionKeyword => {
      // Find the corresponding dimension data for this keyword
      let targetCapitalPosition: string | undefined;
      let targetLoanType: string | undefined;
      let targetPropertyType: string | undefined;

      // Map keyword back to dimension
      if (dimensionData.capitalPositions.some(cp => cp.toLowerCase() === dimensionKeyword)) {
        targetCapitalPosition = dimensionData.capitalPositions.find(cp => cp.toLowerCase() === dimensionKeyword);
        usedCapitalPositions.add(targetCapitalPosition!);
      } else if (dimensionData.loanTypes.some(lt => lt.toLowerCase() === dimensionKeyword)) {
        targetLoanType = dimensionData.loanTypes.find(lt => lt.toLowerCase() === dimensionKeyword);
        usedLoanTypes.add(targetLoanType!);
        
        // Find which capital position this loan type belongs to
        Object.entries(centralMappings.capitalPositions).forEach(([capitalPos, loanTypes]) => {
          if (loanTypes.includes(targetLoanType!)) {
            // Use the capital position from our actual data, not from mapping
            if (dimensionData.capitalPositions.includes(capitalPos)) {
              targetCapitalPosition = capitalPos;
              usedCapitalPositions.add(capitalPos);
            }
          }
        });
        
        // If no mapping found, use the first capital position from our data
        if (!targetCapitalPosition && dimensionData.capitalPositions.length > 0) {
          targetCapitalPosition = dimensionData.capitalPositions[0];
          usedCapitalPositions.add(targetCapitalPosition);
        }
      } else if (dimensionData.propertyTypes.some(pt => pt.toLowerCase() === dimensionKeyword)) {
        targetPropertyType = dimensionData.propertyTypes.find(pt => pt.toLowerCase() === dimensionKeyword);
      }

      // Create specific record for this combination
      if (targetCapitalPosition || targetLoanType) {
        // CHANGED: Extract ALL loan metrics from specialized maps
        const specificMetrics = {
          dealSize: getMetricValue(metricData.dealSizeMap, [dimensionKeyword]),
          loanTerm: getMetricValue(metricData.loanTermMap, [dimensionKeyword]),
          loanExitFee: getMetricValue(metricData.loanExitFeesMap, [dimensionKeyword]),
          loanOriginationFee: getMetricValue(metricData.loanOriginationFeesMap, [dimensionKeyword]),
          loanToCost: getMetricValue(metricData.loanToCostMap, [dimensionKeyword]),
          loanToValue: getMetricValue(metricData.loanToValueMap, [dimensionKeyword]),
          interestRate: getMetricValue(metricData.interestRateMap, [dimensionKeyword]),
          minLoanDscr: getMetricValue(metricData.minLoanDscrMap, [dimensionKeyword]),
          maxLoanDscr: getMetricValue(metricData.maxLoanDscrMap, [dimensionKeyword]),
          minClosingTimeWeeks: getMetricValue(metricData.minClosingTimeWeeksMap, [dimensionKeyword]),
          maxClosingTimeWeeks: getMetricValue(metricData.maxClosingTimeWeeksMap, [dimensionKeyword]),
          recourseLoan: getMetricValue(metricData.recourseLoanMap, [dimensionKeyword]),
          // Interest rate specific values
          interestRateSofr: getMetricValue(metricData.interestRatesSofrMap, [dimensionKeyword]),
          interestRateLibor: getMetricValue(metricData.interestRatesLiborMap, [dimensionKeyword]),
          interestRatePrime: getMetricValue(metricData.interestRatesPrimeMap, [dimensionKeyword]),
          interestRateTenYT: getMetricValue(metricData.interestRatesTenYTMap, [dimensionKeyword]),
          interestRateFiveYT: getMetricValue(metricData.interestRatesFiveYTMap, [dimensionKeyword]),
          interestRateWsj: getMetricValue(metricData.interestRatesWsjMap, [dimensionKeyword])
        };

         records.push(this.createAdvancedRecord(
          globalData,
          {
            capitalPosition: targetCapitalPosition || dimensionData.capitalPositions.join(', '),
            propertyType: dimensionData.propertyTypes.join(', '),
            loanType: targetLoanType || ''
          },
          specificMetrics,
          globalData.otherData.notes || `Specific combination found for ${dimensionKeyword} with dedicated metrics`
        ));

        console.log(`[DEBUG] Created specific record for dimension: ${dimensionKeyword}`);
      }
    });

    // Create one general record for remaining dimensions with default values
    const remainingLoanTypes = dimensionData.loanTypes.filter(lt => !usedLoanTypes.has(lt));
    const remainingCapitalPositions = dimensionData.capitalPositions.filter(cp => !usedCapitalPositions.has(cp));
    
    if (remainingLoanTypes.length > 0 || remainingCapitalPositions.length > 0 || specificCombinationsFound.size === 0) {
      // CHANGED: Extract ALL loan metrics for default record too
      const defaultMetrics = {
        dealSize: getMetricValue(metricData.dealSizeMap, ['default']),
        loanTerm: getMetricValue(metricData.loanTermMap, ['default']),
        loanExitFee: getMetricValue(metricData.loanExitFeesMap, ['default']),
        loanOriginationFee: getMetricValue(metricData.loanOriginationFeesMap, ['default']),
        loanToCost: getMetricValue(metricData.loanToCostMap, ['default']),
        loanToValue: getMetricValue(metricData.loanToValueMap, ['default']),
        interestRate: getMetricValue(metricData.interestRateMap, ['default']),
        minLoanDscr: getMetricValue(metricData.minLoanDscrMap, ['default']),
        maxLoanDscr: getMetricValue(metricData.maxLoanDscrMap, ['default']),
        minClosingTimeWeeks: getMetricValue(metricData.minClosingTimeWeeksMap, ['default']),
        maxClosingTimeWeeks: getMetricValue(metricData.maxClosingTimeWeeksMap, ['default']),
        recourseLoan: getMetricValue(metricData.recourseLoanMap, ['default']),
        // Interest rate specific values
        interestRateSofr: getMetricValue(metricData.interestRatesSofrMap, ['default']),
        interestRateLibor: getMetricValue(metricData.interestRatesLiborMap, ['default']),
        interestRatePrime: getMetricValue(metricData.interestRatesPrimeMap, ['default']),
        interestRateTenYT: getMetricValue(metricData.interestRatesTenYTMap, ['default']),
        interestRateFiveYT: getMetricValue(metricData.interestRatesFiveYTMap, ['default']),
        interestRateWsj: getMetricValue(metricData.interestRatesWsjMap, ['default'])
      };

      const generalCapitalPositions = remainingCapitalPositions.length > 0 ? 
        remainingCapitalPositions.join(', ') : 
        dimensionData.capitalPositions.join(', ');
      
      const generalLoanTypes = remainingLoanTypes.length > 0 ? 
        remainingLoanTypes.join(', ') : 
        'General';

      records.push(this.createAdvancedRecord(
        globalData,
        {
          capitalPosition: generalCapitalPositions,
          propertyType: dimensionData.propertyTypes.join(', '),
          loanType: generalLoanTypes
        },
        defaultMetrics,
        globalData.otherData.notes || `General record with remaining dimensions and default metrics. Remaining loan types: ${remainingLoanTypes.join(', ') || 'None'}`
      ));

      console.log(`[DEBUG] Created general record for remaining dimensions`);
    }

    console.log(`[DEBUG] Created ${records.length} total records: ${specificCombinationsFound.size} specific + ${records.length - specificCombinationsFound.size} general`);
    return records;
  }
   /**
    * Creates general records with all dimensions clubbed (fallback when no specific metrics found)
    */
   private static createGeneralRecords(
     globalData: GlobalRowData,
     dimensionData: DimensionData,
     metricData: MetricData
   ): EnhancedInvestmentCriteriaRecord[] {
     const records: EnhancedInvestmentCriteriaRecord[] = [];

     // Combine all dimensions into single strings
     const combinedPropertyTypes = dimensionData.propertyTypes.join(', ');
     const combinedCapitalPositions = dimensionData.capitalPositions.join(', ');
     const combinedLoanTypes = dimensionData.loanTypes.join(', ');

     // Combine all metrics into single values
     const combinedDealSizes = metricData.dealSizes.join('/');
     const combinedLoanTerms = metricData.loanTerms.join('/');
     const combinedExitFees = metricData.loanExitFees.join('/');
     const combinedOriginationFees = metricData.loanOriginationFees.join('/');

     const record = this.createSingleRecord(
       globalData,
       {
         capitalPosition: combinedCapitalPositions,
         propertyType: combinedPropertyTypes,
         loanType: combinedLoanTypes
       },
       {
         dealSize: combinedDealSizes || undefined,
         loanTerm: combinedLoanTerms || undefined,
         loanExitFee: combinedExitFees || undefined,
         loanOriginationFee: combinedOriginationFees || undefined,
         interestRates: metricData.interestRates,
         // Additional loan-related metrics
         interestRate: metricData.interestRate,
         minLoanDscr: metricData.minLoanDscr,
         maxLoanDscr: metricData.maxLoanDscr,
         loanToCostMin: metricData.loanToCostMin,
         loanToCostMax: metricData.loanToCostMax,
         loanToValueMin: metricData.loanToValueMin,
         loanToValueMax: metricData.loanToValueMax,
         structuredLoanTranche: metricData.structuredLoanTranche,
         minClosingTimeWeeks: typeof metricData.minClosingTimeWeeks === 'number' ? String(metricData.minClosingTimeWeeks) : metricData.minClosingTimeWeeks,
         maxClosingTimeWeeks: typeof metricData.maxClosingTimeWeeks === 'number' ? String(metricData.maxClosingTimeWeeks) : metricData.maxClosingTimeWeeks,
         recourseLoan: metricData.recourseLoan,
         recourseLoanArray: metricData.recourseLoanArray
       },
       globalData.otherData.notes || `Clubbed dimensions with general metrics. ${this.formatOriginalMetrics(metricData)}`
     );

     records.push(record);
     return records;
   }



  /**
   * Comprehensive closing time parser that handles various formats
   * Converts everything to weeks for consistency
   */
  private static parseClosingTime(closingTimeString: string): { min?: number; max?: number; raw: string } {
    if (!closingTimeString) return { raw: '' };

    // Remove extra spaces and normalize
    const normalized = closingTimeString.trim().replace(/\s+/g, ' ');
    
    // Convert time string to weeks
    const parseTime = (timeStr: string): number | null => {
      const cleanStr = timeStr.replace(/[,]/g, '').trim().toLowerCase();
      
      // Handle zero specifically
      if (cleanStr === '0') {
        return 0;
      }
      
      // Extract number and unit
      const match = cleanStr.match(/(\d+(?:\.\d+)?)\s*(days?|weeks?|d|w)?/i);
      if (!match) return null;
      
      const num = parseFloat(match[1]);
      const unit = match[2] || 'weeks'; // Default to weeks if no unit specified
      
      if (isNaN(num)) return null;
      
      // Convert to weeks
      if (unit.startsWith('d')) {
        return num / 7; // Convert days to weeks
      } else {
        return num; // Already in weeks
      }
    };

    // Split by '/' to handle multiple closing times in one string
    const parts = normalized.split('/');
    let overallMin: number | undefined;
    let overallMax: number | undefined;

    for (const part of parts) {
      const trimmedPart = part.trim();
      let min: number | undefined;
      let max: number | undefined;

      // Pattern 1: "Traditional 2-3 Weeks Institutional Bridge: 3-4 Weeks" (complex with descriptions)
      // Extract all ranges from the string
      const allRanges = [...trimmedPart.matchAll(/(\d+(?:\.\d+)?)\s*-\s*(\d+(?:\.\d+)?)\s*(days?|weeks?|d|w)?/gi)];
      if (allRanges.length > 0) {
        const allMins: number[] = [];
        const allMaxs: number[] = [];
        
        for (const match of allRanges) {
          const unit = match[3] || 'weeks';
          const parsedMin = parseTime(`${match[1]} ${unit}`);
          const parsedMax = parseTime(`${match[2]} ${unit}`);
          if (parsedMin !== null) allMins.push(parsedMin);
          if (parsedMax !== null) allMaxs.push(parsedMax);
        }
        
        if (allMins.length > 0) min = Math.min(...allMins);
        if (allMaxs.length > 0) max = Math.max(...allMaxs);
      }

      // Pattern 2: "3-4 Weeks" or "30-60 Days" (simple range)
      const rangeMatch = trimmedPart.match(/^(\d+(?:\.\d+)?)\s*-\s*(\d+(?:\.\d+)?)\s*(days?|weeks?|d|w)?$/i);
      if (rangeMatch && allRanges.length === 0) {
        const unit = rangeMatch[3] || 'weeks';
        const parsedMin = parseTime(`${rangeMatch[1]} ${unit}`);
        const parsedMax = parseTime(`${rangeMatch[2]} ${unit}`);
        min = parsedMin !== null ? parsedMin : undefined;
        max = parsedMax !== null ? parsedMax : undefined;
      }

      // Pattern 3: "30-45" (no unit specified, assume weeks)
      const noUnitRangeMatch = trimmedPart.match(/^(\d+(?:\.\d+)?)\s*-\s*(\d+(?:\.\d+)?)$/);
      if (noUnitRangeMatch && allRanges.length === 0 && !rangeMatch) {
        const parsedMin = parseTime(`${noUnitRangeMatch[1]} weeks`);
        const parsedMax = parseTime(`${noUnitRangeMatch[2]} weeks`);
        min = parsedMin !== null ? parsedMin : undefined;
        max = parsedMax !== null ? parsedMax : undefined;
      }

      // Pattern 4: "45 Days" or "2 weeks" (single value)
      const singleMatch = trimmedPart.match(/^(\d+(?:\.\d+)?)\s*(days?|weeks?|d|w)?$/i);
      if (singleMatch && allRanges.length === 0 && !rangeMatch && !noUnitRangeMatch) {
        const unit = singleMatch[2] || 'weeks';
        const parsedValue = parseTime(`${singleMatch[1]} ${unit}`);
        min = parsedValue !== null ? parsedValue : undefined;
        max = min; // Single value acts as both min and max
      }

      // Update overall min/max
      if (min !== undefined && min !== null) {
        overallMin = overallMin === undefined ? min : Math.min(overallMin, min);
      }
      if (max !== undefined && max !== null) {
        overallMax = overallMax === undefined ? max : Math.max(overallMax, max);
      }
    }

    return {
      min: overallMin,
      max: overallMax,
      raw: normalized
    };
  }

  /**
   * Creates an advanced investment criteria record using specialized metric values
   */
  private static createAdvancedRecord(
    globalData: GlobalRowData,
    dimensions: { capitalPosition: string; propertyType: string; loanType: string },
    advancedMetrics: {
      dealSize?: string;
      loanTerm?: string;
      loanExitFee?: string;
      loanOriginationFee?: string;
      loanToCost?: string;
      loanToValue?: string;
      interestRate?: string;
      minLoanDscr?: string;
      maxLoanDscr?: string;
      structuredLoanTranche?: string;
      minClosingTimeWeeks?: string;
      maxClosingTimeWeeks?: string;
      recourseLoan?: string;
      // Interest rate specific fields
      interestRateSofr?: string;
      interestRateLibor?: string;
      interestRatePrime?: string;
      interestRateTenYT?: string;
      interestRateFiveYT?: string;
      interestRateWsj?: string;
      [key: string]: string | undefined;
    },
    notes: string
  ): EnhancedInvestmentCriteriaRecord {
    const id = this.generateRecordId(dimensions.capitalPosition);

    // Parse deal size using the new comprehensive parser
    const dealSizeInfo = this.parseDealSize(advancedMetrics.dealSize || '');
    
    // Debug logging for deal size parsing
    if (dealSizeInfo.raw) {
      console.log(`[DEBUG] Advanced deal size parsing: "${dealSizeInfo.raw}" -> min: ${dealSizeInfo.min}, max: ${dealSizeInfo.max}`);
    }

    // FIXED: Parse interest rates from specialized maps to numeric values
    const interestRates: Record<string, string> = {};
    
    // Parse SOFR interest rate
    if (advancedMetrics.interestRateSofr) {
      const parsedSofr = this.parseInterestRateFromMetric(advancedMetrics.interestRateSofr, 'sofr', dimensions.capitalPosition);
      if (parsedSofr !== null) {
        interestRates.sofr = parsedSofr.toString();
        console.log(`[DEBUG] Advanced SOFR parsing: "${advancedMetrics.interestRateSofr}" -> ${parsedSofr} for ${dimensions.capitalPosition}`);
      }
    }
    
    // Parse LIBOR interest rate
    if (advancedMetrics.interestRateLibor) {
      const parsedLibor = this.parseInterestRateFromMetric(advancedMetrics.interestRateLibor, 'libor', dimensions.capitalPosition);
      if (parsedLibor !== null) {
        interestRates.libor = parsedLibor.toString();
        console.log(`[DEBUG] Advanced LIBOR parsing: "${advancedMetrics.interestRateLibor}" -> ${parsedLibor} for ${dimensions.capitalPosition}`);
      }
    }
    
    // Parse Prime interest rate
    if (advancedMetrics.interestRatePrime) {
      const parsedPrime = this.parseInterestRateFromMetric(advancedMetrics.interestRatePrime, 'prime', dimensions.capitalPosition);
      if (parsedPrime !== null) {
        interestRates.prime = parsedPrime.toString();
        console.log(`[DEBUG] Advanced Prime parsing: "${advancedMetrics.interestRatePrime}" -> ${parsedPrime} for ${dimensions.capitalPosition}`);
      }
    }
    
    // Parse 10YT interest rate
    if (advancedMetrics.interestRateTenYT) {
      const parsedTenYT = this.parseInterestRateFromMetric(advancedMetrics.interestRateTenYT, 'tenYT', dimensions.capitalPosition);
      if (parsedTenYT !== null) {
        interestRates.tenYT = parsedTenYT.toString();
        console.log(`[DEBUG] Advanced 10YT parsing: "${advancedMetrics.interestRateTenYT}" -> ${parsedTenYT} for ${dimensions.capitalPosition}`);
      }
    }
    
    // Parse 5YT interest rate
    if (advancedMetrics.interestRateFiveYT) {
      const parsedFiveYT = this.parseInterestRateFromMetric(advancedMetrics.interestRateFiveYT, 'fiveYT', dimensions.capitalPosition);
      if (parsedFiveYT !== null) {
        interestRates.fiveYT = parsedFiveYT.toString();
        console.log(`[DEBUG] Advanced 5YT parsing: "${advancedMetrics.interestRateFiveYT}" -> ${parsedFiveYT} for ${dimensions.capitalPosition}`);
      }
    }
    
    // Parse WSJ interest rate
    if (advancedMetrics.interestRateWsj) {
      const parsedWsj = this.parseInterestRateFromMetric(advancedMetrics.interestRateWsj, 'wsj', dimensions.capitalPosition);
      if (parsedWsj !== null) {
        interestRates.wsj = parsedWsj.toString();
        console.log(`[DEBUG] Advanced WSJ parsing: "${advancedMetrics.interestRateWsj}" -> ${parsedWsj} for ${dimensions.capitalPosition}`);
      }
    }

    // FIXED: Parse generic interest rate field for dimension-specific values
    let parsedGenericInterestRate: string | undefined;
    if (advancedMetrics.interestRate) {
      const genericRate = this.parseGenericInterestRate(advancedMetrics.interestRate, dimensions.capitalPosition);
      if (genericRate !== null) {
        parsedGenericInterestRate = genericRate.toString();
        console.log(`[DEBUG] Advanced Generic Interest Rate parsing: "${advancedMetrics.interestRate}" -> ${genericRate} for ${dimensions.capitalPosition}`);
      }
    }

    // Compose metric summary for notes
    const metricSummary = [
      advancedMetrics.dealSize ? `Deal Size: ${advancedMetrics.dealSize}` : '',
      advancedMetrics.loanTerm ? `Loan Term: ${advancedMetrics.loanTerm}` : '',
      advancedMetrics.loanExitFee ? `Loan Exit Fee: ${advancedMetrics.loanExitFee}` : '',
      advancedMetrics.loanOriginationFee ? `Loan Origination Fee: ${advancedMetrics.loanOriginationFee}` : '',
      advancedMetrics.loanToCost ? `Loan To Cost: ${advancedMetrics.loanToCost}` : '',
      advancedMetrics.loanToValue ? `Loan To Value: ${advancedMetrics.loanToValue}` : '',
      advancedMetrics.interestRate ? `Interest Rate: ${advancedMetrics.interestRate}` : '',
      advancedMetrics.minLoanDscr ? `Min DSCR: ${advancedMetrics.minLoanDscr}` : '',
      advancedMetrics.maxLoanDscr ? `Max DSCR: ${advancedMetrics.maxLoanDscr}` : '',
      advancedMetrics.structuredLoanTranche ? `Structured Loan Tranche: ${advancedMetrics.structuredLoanTranche}` : '',
      advancedMetrics.minClosingTimeWeeks ? `Min Closing Time: ${advancedMetrics.minClosingTimeWeeks}` : '',
      advancedMetrics.maxClosingTimeWeeks ? `Max Closing Time: ${advancedMetrics.maxClosingTimeWeeks}` : '',
      advancedMetrics.recourseLoan ? `Recourse Loan: ${advancedMetrics.recourseLoan}` : '',
      // Add interest rate info to notes
      Object.keys(interestRates).length > 0 ? `Interest Rates: ${Object.entries(interestRates).map(([key, value]) => `${key.toUpperCase()}: ${value}`).join(', ')}` : ''
    ].filter(Boolean).join('; ');

    const combinedNotes = metricSummary
      ? (notes ? `${notes} | Initial Metrics: ${metricSummary}` : `Initial Metrics: ${metricSummary}`)
      : notes;

    return {
      id,
      globalData,
      dimensions,
      metrics: {
        dealSize: dealSizeInfo.raw,
        dealSizeMin: dealSizeInfo.min,
        dealSizeMax: dealSizeInfo.max,
        loanTerm: advancedMetrics.loanTerm,
        loanExitFee: advancedMetrics.loanExitFee,
        loanOriginationFee: advancedMetrics.loanOriginationFee,
        interestRates: interestRates,
        // FIXED: Use parsed generic interest rate instead of raw string
        interestRate: parsedGenericInterestRate,
        minLoanDscr: advancedMetrics.minLoanDscr,
        maxLoanDscr: advancedMetrics.maxLoanDscr,
        loanToCostMin: advancedMetrics.loanToCost,
        loanToCostMax: advancedMetrics.loanToCost,
        loanToValueMin: advancedMetrics.loanToValue,
        loanToValueMax: advancedMetrics.loanToValue,  
        structuredLoanTranche: advancedMetrics.structuredLoanTranche,
        minClosingTimeWeeks: advancedMetrics.minClosingTimeWeeks,
        maxClosingTimeWeeks: advancedMetrics.maxClosingTimeWeeks,
        recourseLoan: advancedMetrics.recourseLoan
      },
      notes: combinedNotes
    };
  }

  /**
   * Creates a single investment criteria record
   */
  private static createSingleRecord(
    globalData: GlobalRowData,
    dimensions: { capitalPosition: string; propertyType: string; loanType: string },
    metrics: { 
      dealSize?: string; 
      loanTerm?: string;
      loanExitFee?: string;
      loanOriginationFee?: string;
      interestRates: Record<string, string>;
      // Additional loan-related metrics
      interestRate?: string;
      minLoanDscr?: string;
      maxLoanDscr?: string;
      loanToCostMin?: string;
      loanToCostMax?: string;
      loanToValueMin?: string;
      loanToValueMax?: string;
      structuredLoanTranche?: string;
      minClosingTimeWeeks?: string;
      maxClosingTimeWeeks?: string;
      recourseLoan?: string;
      recourseLoanArray?: string[]; // Preserve original array structure
    },
    notes: string
  ): EnhancedInvestmentCriteriaRecord {
    const id = this.generateRecordId(dimensions.capitalPosition);

    // Parse deal size using the new comprehensive parser
    const dealSizeInfo = this.parseDealSize(metrics.dealSize || '');
    
    // Debug logging for deal size parsing
    if (dealSizeInfo.raw) {
      console.log(`[DEBUG] Deal size parsing: "${dealSizeInfo.raw}" -> min: ${dealSizeInfo.min}, max: ${dealSizeInfo.max}`);
    }

    // Compose metric summary for notes
    const metricSummary = [
      metrics.dealSize ? `Deal Size: ${metrics.dealSize}` : '',
      metrics.loanTerm ? `Loan Term: ${metrics.loanTerm}` : '',
      metrics.loanExitFee ? `Loan Exit Fee: ${metrics.loanExitFee}` : '',
      metrics.loanOriginationFee ? `Loan Origination Fee: ${metrics.loanOriginationFee}` : '',
      metrics.interestRate ? `Interest Rate: ${metrics.interestRate}` : '',
      metrics.minLoanDscr ? `Min DSCR: ${metrics.minLoanDscr}` : '',
      metrics.maxLoanDscr ? `Max DSCR: ${metrics.maxLoanDscr}` : '',
      metrics.loanToCostMin ? `Loan To Cost Min: ${metrics.loanToCostMin}` : '',
      metrics.loanToCostMax ? `Loan To Cost Max: ${metrics.loanToCostMax}` : '',
      metrics.loanToValueMin ? `Loan To Value Min: ${metrics.loanToValueMin}` : '',
      metrics.loanToValueMax ? `Loan To Value Max: ${metrics.loanToValueMax}` : '',
      metrics.structuredLoanTranche ? `Structured Loan Tranche: ${metrics.structuredLoanTranche}` : '',
      metrics.minClosingTimeWeeks ? `Closing Time Weeks: ${metrics.minClosingTimeWeeks}` : '',
      metrics.maxClosingTimeWeeks ? `Closing Time Weeks: ${metrics.maxClosingTimeWeeks}` : '',
      metrics.recourseLoan ? `Recourse Loan: ${metrics.recourseLoan}` : ''
    ].filter(Boolean).join('; ');

    const combinedNotes = metricSummary
      ? (notes ? `${notes} | Initial Metrics: ${metricSummary}` : `Initial Metrics: ${metricSummary}`)
      : notes;

    return {
      id,
      globalData,
      dimensions,
      metrics: {
        dealSize: dealSizeInfo.raw,
        dealSizeMin: dealSizeInfo.min,
        dealSizeMax: dealSizeInfo.max,
        loanTerm: metrics.loanTerm,
        loanExitFee: metrics.loanExitFee,
        loanOriginationFee: metrics.loanOriginationFee,
        interestRates: metrics.interestRates,
        // Additional loan-related metrics
        interestRate: metrics.interestRate,
        minLoanDscr: metrics.minLoanDscr,
        maxLoanDscr: metrics.maxLoanDscr,
        loanToCostMin: metrics.loanToCostMin,
        loanToCostMax: metrics.loanToCostMax,
        loanToValueMin: metrics.loanToValueMin,
        loanToValueMax: metrics.loanToValueMax,
        structuredLoanTranche: metrics.structuredLoanTranche,
        minClosingTimeWeeks: metrics.minClosingTimeWeeks,
        maxClosingTimeWeeks: metrics.maxClosingTimeWeeks,
        recourseLoan: metrics.recourseLoanArray || (metrics.recourseLoan ? this.parseDelimitedValues(metrics.recourseLoan) : undefined)
      },
      notes: combinedNotes
    };
  }

  /**
   * Creates specialized metric maps from raw metric values using enhanced dimension keywords
   * Maps dimension-specific values to their metrics, with defaults for unspecified dimensions
   * Now includes mapping context and property type information
   */
  private static createSpecializedMetricMap(values: string[], dimensionKeywords: EnhancedDimensionKeyword[]): Map<string, string> {
    const map = new Map<string, string>();
    
    let hasSpecificMappings = false;
    let defaultValue: string | null = null;
    
    // Parse each value to extract dimension-specific mappings using enhanced keywords
    for (const value of values) {
      // FIXED: Handle complex interest rate formats that contain multiple dimension-specific values
      // Split by both ' / ' (for previously joined delimited values) and '/' (for interest rate formats)
      let individualValues: string[];
      
      if (value.includes(' / ')) {
        individualValues = value.split(' / ');
      } else if (value.includes('/')) {
        // For interest rate formats like "SOFR +1000-+1300: Mezzanine/SOFR +600-+800: Senior Debt"
        individualValues = value.split('/');
      } else {
        individualValues = [value];
      }
      
      for (const individualValue of individualValues) {
        const lowerValue = individualValue.toLowerCase();
        let foundDimension = false;
        
        // Check if this value contains any enhanced dimension keywords
        for (const enhancedKeyword of dimensionKeywords) {
          if (lowerValue.includes(enhancedKeyword.keyword)) {
            // FIXED: For complex interest rate formats, extract the specific portion for this dimension
            let mappedValue = individualValue.trim();
            
            // Special handling for interest rate formats: extract only the relevant portion
            if (individualValue.includes(':') && (individualValue.toLowerCase().includes('sofr') || 
                individualValue.toLowerCase().includes('libor') || 
                individualValue.toLowerCase().includes('prime') || 
                individualValue.toLowerCase().includes('wsj') || 
                individualValue.toLowerCase().includes('yt'))) {
              // For formats like "SOFR +600-+800: Senior Debt", use the whole segment
              mappedValue = individualValue.trim();
            }
            
            // Use the original value as the key for consistency with legacy code
            map.set(enhancedKeyword.keyword, mappedValue);
            hasSpecificMappings = true;
            foundDimension = true;
            
            console.log(`[DEBUG] Metric value "${individualValue}" matched dimension: ${enhancedKeyword.keyword} -> "${mappedValue}"`);
            break;
          }
        }
        
        // If no dimension found, treat as potential default
        if (!foundDimension && !defaultValue) {
          defaultValue = individualValue.trim();
        }
      }
    }
    
    // If we have specific mappings but also a potential default, add it
    if (defaultValue && (hasSpecificMappings || values.length === 1)) {
      map.set('default', defaultValue);
    }
    
    // If no specific mappings found but we have values, treat first as default
    if (!hasSpecificMappings && values.length > 0) {
      // Take the first individual value from the first entry
      const firstValue = values[0];
      let firstIndividualValue: string;
      
      if (firstValue.includes(' / ')) {
        firstIndividualValue = firstValue.split(' / ')[0];
      } else if (firstValue.includes('/')) {
        firstIndividualValue = firstValue.split('/')[0];
      } else {
        firstIndividualValue = firstValue;
      }
      
      map.set('default', firstIndividualValue.trim());
    }
    
    return map;
  }

  /**
   * Detect if an interest rate value contains complex format that shouldn't be split
   * Complex formats: "SOFR: +475-+650: Senior Debt/SOFR: +800-+1000: Mezzanine"
   */
  private static isComplexInterestRateFormat(value: string): boolean {
    if (!value || typeof value !== 'string') return false;
    
    // Check for patterns like: RATE_TYPE: +number-+number: CAPITAL_POSITION/RATE_TYPE: +number-+number: CAPITAL_POSITION
    const complexPattern = /(?:sofr|libor|prime|10yt|5yt|wsj)[:+\s]*\+?\d+-?\+?\d+.*[:\/].*(?:sofr|libor|prime|10yt|5yt|wsj)[:+\s]*\+?\d+/i;
    return complexPattern.test(value);
  }

  /**
   * Parses delimited values (supports both '/' and ',' delimiters)
   */
  private static parseDelimitedValues(value: string): string[] {
    if (!value) return [];
    
    // Split by both '/' and ',' and clean up
    const values = value.split(/[\/,]/)
      .map(v => v.trim())
      .filter(v => v.length > 0);
    
    return values;
  }

  /**
   * Generates a unique record ID
   */
  private static generateRecordId(prefix: string): string {
    const timestamp = Date.now();
    const random = Math.random();
    return `${prefix}_${timestamp}_${random}`;
  }

  /**
   * Fetches central mappings from database (server-side)
   */
  static async fetchCentralMappingsFromDatabase(): Promise<CentralMappings> {
    const mappings: CentralMappings = {
      capitalPositions: {},
      propertyTypes: {}
    };

    try {
      const { pool } = await import('@/lib/db');
      const client = await pool.connect();

      try {
        // Fetch capital position to loan type mappings
        const capitalQuery = `
          SELECT value_1 as capital_position, value_2 as loan_type 
          FROM central_mapping 
          WHERE type = 'Capital Position' 
          AND value_2 IS NOT NULL 
          AND is_active = true
          ORDER BY value_1, value_2
        `;
        
        const capitalResult = await client.query(capitalQuery);
        
        // Group by capital position
        capitalResult.rows.forEach(row => {
          const capitalPosition = row.capital_position;
          const loanType = row.loan_type;
          
          if (!mappings.capitalPositions[capitalPosition]) {
            mappings.capitalPositions[capitalPosition] = [];
          }
          mappings.capitalPositions[capitalPosition].push(loanType);
        });

        // Fetch property type hierarchy mappings
        const propertyQuery = `
          SELECT value_1 as property_type, value_2 as sub_type 
          FROM central_mapping 
          WHERE type = 'Property Type' 
          AND is_active = true
          ORDER BY value_1, value_2
        `;
        
        const propertyResult = await client.query(propertyQuery);
        
        // Group by property type
        propertyResult.rows.forEach(row => {
          const propertyType = row.property_type;
          const subType = row.sub_type;
          
          if (!mappings.propertyTypes[propertyType]) {
            mappings.propertyTypes[propertyType] = [];
          }
          if (subType) {
            mappings.propertyTypes[propertyType].push(subType);
          }
        });

        console.log(`[DEBUG] Fetched central mappings: ${Object.keys(mappings.capitalPositions).length} capital positions, ${Object.keys(mappings.propertyTypes).length} property types`);

      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error fetching central mappings from database:', error);
    }

    return mappings;
  }

   /**
    * Backward compatibility method - converts enhanced records to legacy format
    */
   static convertToLegacyFormat(enhancedRecords: EnhancedInvestmentCriteriaRecord[]): InvestmentCriteriaRecord[] {
     return enhancedRecords.map(enhanced => {
       // Parse interest rates from string format to numbers
       const interestRates: Record<string, number> = {};
       Object.entries(enhanced.metrics.interestRates).forEach(([key, value]) => {
         if (value) {
           const numValue = this.parseNumeric(value);
           if (numValue !== null) {
             interestRates[key] = numValue;
           }
         }
       });

       // Parse loan term from string
       const loanTerm: { min?: number; max?: number; years?: number } = {};
       if (enhanced.metrics.loanTerm) {
         // Updated regex to handle decimals that start with "." (like ".5")
         const termMatch = enhanced.metrics.loanTerm.match(/(\d*\.?\d+)\s*(?:-\s*(\d*\.?\d+))?\s*years?/i);
         if (termMatch) {
           if (termMatch[2]) {
             loanTerm.min = parseFloat(termMatch[1]);
             loanTerm.max = parseFloat(termMatch[2]);
           } else {
             loanTerm.years = parseFloat(termMatch[1]);
           }
         }
       }

       // Parse fees
       const fees: { originationMin?: number; originationMax?: number; exitMin?: number; exitMax?: number } = {};
       if (enhanced.metrics.loanOriginationFee) {
         const origMatch = enhanced.metrics.loanOriginationFee.match(/(\d+(?:\.\d+)?)/);
         if (origMatch) {
           fees.originationMin = parseFloat(origMatch[1]);
         }
       }
       if (enhanced.metrics.loanExitFee) {
         const exitMatch = enhanced.metrics.loanExitFee.match(/(\d+(?:\.\d+)?)/);
         if (exitMatch) {
           fees.exitMin = parseFloat(exitMatch[1]);
         }
       }

       // Parse loan-to-cost and loan-to-value
       const loanToCost: { min?: number; max?: number } = {};
       if (enhanced.metrics.loanToCostMin || enhanced.metrics.loanToCostMax) {
         const ltcValue = enhanced.metrics.loanToCostMin || enhanced.metrics.loanToCostMax;
         if (ltcValue) {
           const parsedLTC = this.parseLTVLTCValue(ltcValue);
           if (parsedLTC) {
             loanToCost.min = parsedLTC.min;
             loanToCost.max = parsedLTC.max;
           }
         }
       }

       const loanToValue: { min?: number; max?: number } = {};
       if (enhanced.metrics.loanToValueMin || enhanced.metrics.loanToValueMax) {
         const ltvValue = enhanced.metrics.loanToValueMin || enhanced.metrics.loanToValueMax;
         if (ltvValue) {
           const parsedLTV = this.parseLTVLTCValue(ltvValue);
           if (parsedLTV) {
             loanToValue.min = parsedLTV.min;
             loanToValue.max = parsedLTV.max;
           }
         }
       }

       // Parse DSCR (Debt Service Coverage Ratio) - should remain as multipliers, not percentages
       const dscr: { min?: number; max?: number } = {};
       if (enhanced.metrics.minLoanDscr) {
         const dscrMin = this.parseNumeric(enhanced.metrics.minLoanDscr, 'dscr');
         if (dscrMin !== null) {
           dscr.min = dscrMin;
         }
       }
       if (enhanced.metrics.maxLoanDscr) {
         const dscrMax = this.parseNumeric(enhanced.metrics.maxLoanDscr, 'dscr');
         if (dscrMax !== null) {
           dscr.max = dscrMax;
         }
       }

       // Parse closing time fields from enhanced metrics
       let minClosingTimeWeeks: number | undefined;
       let maxClosingTimeWeeks: number | undefined;
       
       if (enhanced.metrics.minClosingTimeWeeks) {
         const parsedMin = typeof enhanced.metrics.minClosingTimeWeeks === 'string' 
           ? this.parseNumeric(enhanced.metrics.minClosingTimeWeeks, 'general')
           : enhanced.metrics.minClosingTimeWeeks;
         if (parsedMin !== null && parsedMin !== undefined) {
           minClosingTimeWeeks = parsedMin;
         }
       }
       
       if (enhanced.metrics.maxClosingTimeWeeks) {
         const parsedMax = typeof enhanced.metrics.maxClosingTimeWeeks === 'string' 
           ? this.parseNumeric(enhanced.metrics.maxClosingTimeWeeks, 'general')
           : enhanced.metrics.maxClosingTimeWeeks;
         if (parsedMax !== null && parsedMax !== undefined) {
           maxClosingTimeWeeks = parsedMax;
         }
       }

       // Check if this record should have loan details
       const hasLoanSpecificMetrics = enhanced.dimensions.loanType || 
         Object.keys(loanTerm).length > 0 ||
         Object.keys(interestRates).length > 0 ||
         Object.keys(fees).length > 0 ||
         Object.keys(loanToCost).length > 0 ||
         Object.keys(loanToValue).length > 0 ||
         Object.keys(dscr).length > 0 ||
         enhanced.globalData.otherData.loan_program ||
         enhanced.globalData.otherData.structured_loan_tranche ||
         minClosingTimeWeeks !== undefined ||
         maxClosingTimeWeeks !== undefined;

       const legacy: InvestmentCriteriaRecord = {
         id: enhanced.id,
         capitalPosition: enhanced.dimensions.capitalPosition,
         dealSize: enhanced.metrics.dealSize || '',
         recordType: enhanced.dimensions.loanType ? 'debt_instrument' : 'investment_criteria',
         location: {
           country: enhanced.globalData.otherData.country ? [enhanced.globalData.otherData.country] : undefined,
           region: enhanced.globalData.otherData.region ? [enhanced.globalData.otherData.region] : undefined,
           state: enhanced.globalData.otherData.state ? [enhanced.globalData.otherData.state] : undefined,
           city: enhanced.globalData.otherData.city ? [enhanced.globalData.otherData.city] : undefined
         },
         propertyType: {
           propertyTypes: enhanced.dimensions.propertyType ? [enhanced.dimensions.propertyType] : undefined,
           propertySubTypes: enhanced.globalData.otherData.property_sub_categories ? [enhanced.globalData.otherData.property_sub_categories] : undefined,
           strategies: enhanced.globalData.otherData.strategies ? [enhanced.globalData.otherData.strategies] : undefined
         },
         dealSizeInfo: {
           minimumDealSize: enhanced.metrics.dealSizeMin,
           maximumDealSize: enhanced.metrics.dealSizeMax
         },
         // ADD MISSING INVESTMENT STRATEGY FIELDS - Extract from globalData.otherData
         investmentStrategy: {
           targetReturn: enhanced.globalData.otherData.target_return ? (this.parseNumeric(enhanced.globalData.otherData.target_return, 'percentage') ?? undefined) : undefined,
           minHoldPeriod: enhanced.globalData.otherData.min_hold_period ? (this.parseNumeric(enhanced.globalData.otherData.min_hold_period, 'general') ?? undefined) : undefined,
           maxHoldPeriod: enhanced.globalData.otherData.max_hold_period ? (this.parseNumeric(enhanced.globalData.otherData.max_hold_period, 'general') ?? undefined) : undefined
         },
         loanDetails: hasLoanSpecificMetrics ? {
           loanType: enhanced.dimensions.loanType ? [enhanced.dimensions.loanType] : undefined,
           loanTerm: Object.keys(loanTerm).length > 0 ? loanTerm : undefined,
           interestRates: Object.keys(interestRates).length > 0 ? interestRates : undefined,
           // FIXED: Add generic interest rate field mapping
           interestRate: enhanced.metrics.interestRate,
           fees: Object.keys(fees).length > 0 ? fees : undefined,
           loanToCost: Object.keys(loanToCost).length > 0 ? loanToCost : undefined,
           loanToValue: Object.keys(loanToValue).length > 0 ? loanToValue : undefined,
           dscr: Object.keys(dscr).length > 0 ? dscr : undefined,
           loanProgram: enhanced.globalData.otherData.loan_program ? [enhanced.globalData.otherData.loan_program] : undefined,
           structuredLoanTranche: enhanced.globalData.otherData.structured_loan_tranche ? [enhanced.globalData.otherData.structured_loan_tranche] : undefined,
           recourseLoan: Array.isArray(enhanced.metrics.recourseLoan) 
             ? enhanced.metrics.recourseLoan 
             : (enhanced.metrics.recourseLoan ? this.parseDelimitedValues(enhanced.metrics.recourseLoan) : undefined),
           minClosingTimeWeeks: minClosingTimeWeeks,
           maxClosingTimeWeeks: maxClosingTimeWeeks
         } : undefined,
         notes: enhanced.notes
       };

       return legacy;
     });
   }

   /**
    * Legacy method for backward compatibility
    */
   static async processCSVRow(input: ProcessingInput): Promise<ProcessingResult> {
     // Use the enhanced processor which now returns the proper structure
     const enhancedResult = await this.processEnhanced(input);
     console.log(`[DEBUG] Enhanced result: ${JSON.stringify(enhancedResult)}`);
     return {
       contactData: enhancedResult.contactData,
       companyData: enhancedResult.companyData,
       investmentCriteriaData: enhancedResult.investmentCriteriaData,
       errors: enhancedResult.errors,
       warnings: enhancedResult.warnings,
       recordCount: enhancedResult.recordCount
     };
   }

  /**
   * Parse SOFR interest rate strings with various formats
   * Handles: SOFR +350-+450, SOFR +350, SOFR: +475-+650: Senior Debt/SOFR: +800-+1000: Mezzanine
   */
  /**
   * Parse interest rate strings with various formats for all rate types (SOFR, LIBOR, Prime, 10YT, 5YT, WSJ)
   * Handles: 
   * - SOFR +350-+450 -> AVG of (350-450) -> /100 -> 4.0
   * - SOFR +350 -> 3.5
   * - SOFR: +475-+650: Senior Debt/SOFR: +800-+1000: Mezzanine
   * - Same patterns for LIBOR, Prime, 10YT, 5YT, WSJ
   */
  private static parseInterestRate(value: string, rateType: string, capitalPosition?: string): number | null {
    if (!value || typeof value !== 'string') {
      return null;
    }

    console.log(`[DEBUG] Parsing ${rateType.toUpperCase()} rate: "${value}" for capital position: ${capitalPosition}`);

    // Create flexible rate patterns for different rate types
    const ratePatterns = {
      'sofr': /sofr/i,
      'libor': /libor/i,
      'prime': /prime/i,
      'tenYT': /(?:10yt|10-?year|ten-?year)/i,
      'fiveYT': /(?:5yt|5-?year|five-?year)/i,
      'wsj': /wsj/i
    };

    const pattern = ratePatterns[rateType as keyof typeof ratePatterns];
    if (!pattern) {
      console.log(`[DEBUG] Unknown rate type: ${rateType}`);
      return null;
    }

    // Handle complex format: "SOFR: +475-+650: Senior Debt/SOFR: +800-+1000: Mezzanine"
    if (value.includes('/') && capitalPosition) {
      const segments = value.split('/');
      
      for (const segment of segments) {
        const trimmed = segment.trim();
        // Check if this segment is for the current capital position
        if (trimmed.toLowerCase().includes(capitalPosition.toLowerCase())) {
          // Extract the rate range from this segment: "SOFR: +800-+1000: Mezzanine"
          const complexMatch = trimmed.match(new RegExp(`${pattern.source}[:+\\s]*\\+?(\\d+)-\\+?(\\d+)`, 'i'));
          if (complexMatch) {
            const min = parseInt(complexMatch[1]);
            const max = parseInt(complexMatch[2]);
            const average = (min + max) / 2;
            const finalRate = average / 100; // Convert basis points to decimal
            console.log(`[DEBUG] ${rateType.toUpperCase()} complex format: ${min}-${max} -> avg: ${average} -> final: ${finalRate}`);
            return Math.round(finalRate * 1000) / 1000;
          }
        }
      }
    }

    // Handle simple range format: "SOFR +350-+450" or "LIBOR: +475-+650"
    const rangeMatch = value.match(new RegExp(`${pattern.source}[:+\\s]*\\+?(\\d+)-\\+?(\\d+)`, 'i'));
    if (rangeMatch) {
      const min = parseInt(rangeMatch[1]);
      const max = parseInt(rangeMatch[2]);
      const average = (min + max) / 2;
      const finalRate = average / 100; // Convert basis points to decimal
      console.log(`[DEBUG] ${rateType.toUpperCase()} range format: ${min}-${max} -> avg: ${average} -> final: ${finalRate}`);
      return Math.round(finalRate * 1000) / 1000;
    }

    // Handle single value format: "SOFR +350" or "Prime: +475"
    const singleMatch = value.match(new RegExp(`${pattern.source}[:+\\s]*\\+?(\\d+)`, 'i'));
    if (singleMatch) {
      const rateValue = parseInt(singleMatch[1]);
      const finalRate = rateValue / 100; // Convert basis points to decimal
      console.log(`[DEBUG] ${rateType.toUpperCase()} single format: ${rateValue} -> final: ${finalRate}`);
      return Math.round(finalRate * 1000) / 1000;
    }

    console.log(`[DEBUG] ${rateType.toUpperCase()} parsing failed for: "${value}"`);
    return null;
  }

  /**
   * Parse interest rate from specialized metric maps for specific dimensions
   * This method handles the dimension-specific parsing of interest rates
   */
  private static parseInterestRateFromMetric(value: string, rateType: string, capitalPosition?: string): number | null {
    if (!value || typeof value !== 'string') {
      return null;
    }

    console.log(`[DEBUG] Parsing ${rateType.toUpperCase()} rate from metric: "${value}" for capital position: ${capitalPosition}`);

    // Create flexible rate patterns for different rate types
    const ratePatterns = {
      'sofr': /sofr/i,
      'libor': /libor/i,
      'prime': /prime/i,
      'tenYT': /(?:10yt|10-?year|ten-?year)/i,
      'fiveYT': /(?:5yt|5-?year|five-?year)/i,
      'wsj': /wsj/i
    };

    const pattern = ratePatterns[rateType as keyof typeof ratePatterns];
    if (!pattern) {
      console.log(`[DEBUG] Unknown rate type: ${rateType}`);
      return null;
    }

    // Handle complex format with dimensions: "SOFR: +475-+650: Senior Debt/SOFR: +800-+1000: Mezzanine"
    if (value.includes('/') && capitalPosition) {
      const segments = value.split('/');
      
      for (const segment of segments) {
        const trimmed = segment.trim();
        // Check if this segment is for the current capital position
        if (trimmed.toLowerCase().includes(capitalPosition.toLowerCase())) {
          // Extract the rate range from this segment: "SOFR: +800-+1000: Mezzanine"
          const complexMatch = trimmed.match(new RegExp(`${pattern.source}[:+\\s]*\\+?(\\d+)-\\+?(\\d+)`, 'i'));
          if (complexMatch) {
            const min = parseInt(complexMatch[1]);
            const max = parseInt(complexMatch[2]);
            const average = (min + max) / 2;
            // FIXED: Don't divide by 100 - basis points are already in the correct scale
            // 700 basis points = 7%, not 0.07
            const finalRate = average / 100; // Convert basis points to percentage (700 → 7)
            console.log(`[DEBUG] ${rateType.toUpperCase()} complex format: ${min}-${max} -> avg: ${average} -> final: ${finalRate}% for ${capitalPosition}`);
            return Math.round(finalRate * 1000) / 1000;
          }
          
          // Try single value in complex format: "SOFR: +800: Mezzanine"
          const complexSingleMatch = trimmed.match(new RegExp(`${pattern.source}[:+\\s]*\\+?(\\d+)`, 'i'));
          if (complexSingleMatch) {
            const rateValue = parseInt(complexSingleMatch[1]);
            // FIXED: Don't divide by 100 - basis points are already in the correct scale
            const finalRate = rateValue / 100; // Convert basis points to percentage (700 → 7)
            console.log(`[DEBUG] ${rateType.toUpperCase()} complex single format: ${rateValue} -> final: ${finalRate}% for ${capitalPosition}`);
            return Math.round(finalRate * 1000) / 1000;
          }
        }
      }
    }

    // Handle simple range format: "SOFR +350-+450" or "LIBOR: +475-+650"
    const rangeMatch = value.match(new RegExp(`${pattern.source}[:+\\s]*\\+?(\\d+)-\\+?(\\d+)`, 'i'));
    if (rangeMatch) {
      const min = parseInt(rangeMatch[1]);
      const max = parseInt(rangeMatch[2]);
      const average = (min + max) / 2;
      // FIXED: Don't divide by 100 - basis points are already in the correct scale
      const finalRate = average / 100; // Convert basis points to percentage (700 → 7)
      console.log(`[DEBUG] ${rateType.toUpperCase()} range format: ${min}-${max} -> avg: ${average} -> final: ${finalRate}%`);
      return Math.round(finalRate * 1000) / 1000;
    }

    // Handle single value format: "SOFR +350" or "Prime: +475"
    const singleMatch = value.match(new RegExp(`${pattern.source}[:+\\s]*\\+?(\\d+)`, 'i'));
    if (singleMatch) {
      const rateValue = parseInt(singleMatch[1]);
      // FIXED: Don't divide by 100 - basis points are already in the correct scale
      const finalRate = rateValue / 100; // Convert basis points to percentage (700 → 7)
      console.log(`[DEBUG] ${rateType.toUpperCase()} single format: ${rateValue} -> final: ${finalRate}%`);
      return Math.round(finalRate * 1000) / 1000;
    }

    console.log(`[DEBUG] ${rateType.toUpperCase()} parsing failed for: "${value}"`);
    return null;
  }

  /**
   * Parse generic interest rate field for dimension-specific values
   * Handles formats like "8.5%+ Senior Debt", "12%+ Mezzanine", etc.
   */
  private static parseGenericInterestRate(value: string, capitalPosition?: string): number | null {
    if (!value || typeof value !== 'string') {
      return null;
    }

    console.log(`[DEBUG] Parsing generic interest rate: "${value}" for capital position: ${capitalPosition}`);

    // Handle complex format with dimensions: "8.5%+ Senior Debt/12%+ Mezzanine"
    if (value.includes('/') && capitalPosition) {
      const segments = value.split('/');
      
      for (const segment of segments) {
        const trimmed = segment.trim();
        // Check if this segment is for the current capital position
        if (trimmed.toLowerCase().includes(capitalPosition.toLowerCase())) {
          // Extract the rate from this segment: "8.5%+ Senior Debt"
          const complexMatch = trimmed.match(/(\d+(?:\.\d+)?)\s*%?\s*\+?/);
          if (complexMatch) {
            const rate = parseFloat(complexMatch[1]);
            console.log(`[DEBUG] Generic interest rate complex format: ${rate}% for ${capitalPosition}`);
            return Math.round(rate * 1000) / 1000;
          }
        }
      }
    }

    // Handle simple format: "8.5%+" or "12%"
    const simpleMatch = value.match(/(\d+(?:\.\d+)?)\s*%?\s*\+?/);
    if (simpleMatch) {
      const rate = parseFloat(simpleMatch[1]);
      console.log(`[DEBUG] Generic interest rate simple format: ${rate}%`);
      return Math.round(rate * 1000) / 1000;
    }

    console.log(`[DEBUG] Generic interest rate parsing failed for: "${value}"`);
    return null;
  }

  /**
   * Comprehensive deal size parser that handles various formats
   */
  private static parseDealSize(dealSizeString: string): { min?: number; max?: number; raw: string } {
    if (!dealSizeString) return { raw: '' };

    // Remove extra spaces and normalize
    const normalized = dealSizeString.trim().replace(/\s+/g, ' ');
    
    // Convert size string to number (in millions)
    const parseSize = (sizeStr: string): number | null => {
      const cleanStr = sizeStr.replace(/[,$]/g, '').trim();
      
      // Handle zero specifically
      if (cleanStr === '0') {
        return 0;
      }
      
      // Handle different units
      if (cleanStr.includes('B') || cleanStr.includes('b')) {
        const num = parseFloat(cleanStr.replace(/[Bb]/g, ''));
        return num * 1000; // Convert billions to millions
      } else if (cleanStr.includes('M') || cleanStr.includes('m')) {
        const num = parseFloat(cleanStr.replace(/[Mm]/g, ''));
        return num;
      } else if (cleanStr.includes('K') || cleanStr.includes('k')) {
        const num = parseFloat(cleanStr.replace(/[Kk]/g, ''));
        return num / 1000; // Convert thousands to millions
      } else if (cleanStr.includes('mm') || cleanStr.includes('MM')) {
        const num = parseFloat(cleanStr.replace(/[mM]{2}/g, ''));
        return num;
      } else {
        // Try to parse as raw number (assume millions)
        const num = parseFloat(cleanStr);
        return isNaN(num) ? null : num;
      }
    };

    // Split by '/' to handle multiple deal sizes in one string
    const parts = normalized.split('/');
    let overallMin: number | undefined;
    let overallMax: number | undefined;
    let hasRanges = false; // Track if we have actual ranges vs single values

    for (const part of parts) {
      const trimmedPart = part.trim();
      let min: number | undefined;
      let max: number | undefined;

      // Pattern 1: $10M - $100M (range) - improved to handle various formats
      const rangeMatch = trimmedPart.match(/\$?(\d+(?:\.\d+)?)\s*([KkMmBb]{1,2}|mm|MM)?\s*-\s*\$?(\d+(?:\.\d+)?)\s*([KkMmBb]{1,2}|mm|MM)/);
      if (rangeMatch) {
        const unit1 = rangeMatch[2] || 'M'; // Default to M if no unit
        const unit2 = rangeMatch[4] || 'M'; // Default to M if no unit
        const parsedMin = parseSize(`${rangeMatch[1]}${unit1}`);
        const parsedMax = parseSize(`${rangeMatch[3]}${unit2}`);
        min = parsedMin !== null ? parsedMin : undefined;
        max = parsedMax !== null ? parsedMax : undefined;
        hasRanges = true;
      }

      // Pattern 3: $0 - $25M (starting from zero)
      const zeroRangeMatch = trimmedPart.match(/\$?0\s*-\s*\$?(\d+(?:\.\d+)?)\s*([KkMmBb]{1,2}|mm|MM)/);
      if (zeroRangeMatch && !rangeMatch) {
        min = 0;
        const parsedMax = parseSize(`${zeroRangeMatch[1]}${zeroRangeMatch[2]}`);
        max = parsedMax !== null ? parsedMax : undefined;
        hasRanges = true;
      }

      // Pattern 2: $25M+ (minimum only) - set max to 1000M (1B)
      const minOnlyMatch = trimmedPart.match(/\$?(\d+(?:\.\d+)?)\s*([KkMmBb]{1,2}|mm|MM)\+/);
      if (minOnlyMatch && !rangeMatch && !zeroRangeMatch) {
        const parsedMin = parseSize(`${minOnlyMatch[1]}${minOnlyMatch[2]}`);
        min = parsedMin !== null ? parsedMin : undefined;
        max = 1000; // Set maximum to 1000M (1B) for "+" format
        hasRanges = true; // Mark as having ranges since we set specific min/max
      }

      // Pattern 4: Just $25M (single value)
      const singleMatch = trimmedPart.match(/\$?(\d+(?:\.\d+)?)\s*([KkMmBb]{1,2}|mm|MM)(?!\+|\s*-)/);
      if (singleMatch && !rangeMatch && !minOnlyMatch && !zeroRangeMatch) {
        const parsedValue = parseSize(`${singleMatch[1]}${singleMatch[2]}`);
        min = parsedValue !== null ? parsedValue : undefined;
        max = min; // Single value acts as both min and max
      }

      // Pattern 5: Complex patterns with descriptions like "$25M+ Senior Debt"
      const complexMatch = trimmedPart.match(/\$?(\d+(?:\.\d+)?)\s*([KkMmBb]{1,2}|mm|MM)\+?\s*(?:-\s*\$?(\d+(?:\.\d+)?)\s*([KkMmBb]{1,2}|mm|MM))?/);
      if (complexMatch && !rangeMatch && !minOnlyMatch && !zeroRangeMatch && !singleMatch) {
        const parsedMin = parseSize(`${complexMatch[1]}${complexMatch[2]}`);
        min = parsedMin !== null ? parsedMin : undefined;
        if (complexMatch[3] && complexMatch[4]) {
          const parsedMax = parseSize(`${complexMatch[3]}${complexMatch[4]}`);
          max = parsedMax !== null ? parsedMax : undefined;
          hasRanges = true;
        } else {
          // If no range in complex match, treat as single value
          max = min;
        }
      }

      // Update overall min/max
      if (min !== undefined && min !== null) {
        overallMin = overallMin === undefined ? min : Math.min(overallMin, min);
      }
      if (max !== undefined && max !== null) {
        overallMax = overallMax === undefined ? max : Math.max(overallMax, max);
      }
      
      // Special handling for zero values
      if (min === 0) {
        overallMin = 0;
      }
    }

    // Final adjustment: If we have only single values (no ranges) and only min is set, set max = min
    if (!hasRanges && overallMin !== undefined && overallMax === undefined) {
      overallMax = overallMin;
    }

    return {
      min: overallMin,
      max: overallMax,
      raw: normalized
    };
  }

  /**
   * Parse numeric values with improved handling for different field types
   */
  private static parseNumeric(value: string, fieldType?: 'dscr' | 'percentage' | 'general'): number | null {
    const numberMatch = value.match(/(\d*\.?\d+)/);
    if (numberMatch) {
      const num = parseFloat(numberMatch[1]);
      if (!isNaN(num)) {
        // Special handling for DSCR values - they should remain as multipliers
        if (fieldType === 'dscr' || value.toLowerCase().includes('x')) {
          // DSCR values like "1.25x" should remain as 1.25 (multiplier), not percentage
          return Math.round(num * 1000) / 1000;
        }
        
        // For other fields, convert basis points to percentage: divide by 100 if >= 100
        const finalNum = num >= 100 ? num / 100 : num;
        return Math.round(finalNum * 1000) / 1000;
      }
    }
     return null;
   }

  /**
   * Parse LTV (Loan-to-Value) and LTC (Loan-to-Cost) values with specific logic:
   * - "75%+" means min = 75%, max = 100%
   * - "75%" means min = 0%, max = 75%
   * - "60%-80%" means min = 60%, max = 80%
   */
  private static parseLTVLTCValue(value: string): { min: number; max: number } | null {
    if (!value || typeof value !== 'string') {
      return null;
    }

    const normalizedValue = value.trim();
    
    // Handle range format: "60%-80%" or "60% - 80%"
    const rangeMatch = normalizedValue.match(/(\d+(?:\.\d+)?)%?\s*-\s*(\d+(?:\.\d+)?)%?/);
    if (rangeMatch) {
      const minVal = parseFloat(rangeMatch[1]);
      const maxVal = parseFloat(rangeMatch[2]);
      if (!isNaN(minVal) && !isNaN(maxVal)) {
        return {
          min: Math.round((minVal >= 10 ? minVal / 100 : minVal) * 1000) / 1000,
          max: Math.round((maxVal >= 10 ? maxVal / 100 : maxVal) * 1000) / 1000
        };
      }
    }
    
    // Handle "+" format: "75%+" means min = 75%, max = 100%
    const plusMatch = normalizedValue.match(/(\d+(?:\.\d+)?)%?\+/);
    if (plusMatch) {
      const minVal = parseFloat(plusMatch[1]);
      if (!isNaN(minVal)) {
        return {
          min: Math.round((minVal >= 10 ? minVal / 100 : minVal) * 1000) / 1000,
          max: 1.0 // 100%
        };
      }
    }
    
    // Handle single value: "75%" means min = 0%, max = 75%
    const singleMatch = normalizedValue.match(/(\d+(?:\.\d+)?)%?/);
    if (singleMatch) {
      const maxVal = parseFloat(singleMatch[1]);
      if (!isNaN(maxVal)) {
        return {
          min: 0,
          max: Math.round((maxVal >= 10 ? maxVal / 100 : maxVal) * 1000) / 1000
        };
      }
    }
    
    return null;
  }



  /**
   * Helper function to format original metrics for notes
   */
  private static formatOriginalMetrics(metricData: MetricData): string {
    const metrics: string[] = [];
    
    if (metricData.dealSizes.length > 0) {
      metrics.push(`Deal Sizes: ${metricData.dealSizes.join(', ')}`);
    }
    if (metricData.loanTerms.length > 0) {
      metrics.push(`Loan Terms: ${metricData.loanTerms.join(', ')}`);
    }
    if (metricData.loanExitFees.length > 0) {
      metrics.push(`Exit Fees: ${metricData.loanExitFees.join(', ')}`);
    }
    if (metricData.loanOriginationFees.length > 0) {
      metrics.push(`Origination Fees: ${metricData.loanOriginationFees.join(', ')}`);
    }
    if (metricData.interestRate) {
      metrics.push(`Interest Rate: ${metricData.interestRate}`);
    }
    if (metricData.structuredLoanTranche) {
      metrics.push(`Loan Tranche: ${metricData.structuredLoanTranche}`);
    }
    if (metricData.minClosingTimeWeeks) {
      metrics.push(`Closing Time: ${metricData.minClosingTimeWeeks} - ${metricData.maxClosingTimeWeeks}`);
    }
    
    return metrics.length > 0 ? `Original Metrics: ${metrics.join('; ')}` : '';
  }

  /**
   * Convert investment criteria records to flat database column format
   * Returns dbColumns -> values mapping ready for database insertion
   */
  static convertToFlatDatabaseFormat(investmentCriteriaRecords: InvestmentCriteriaRecord[]): Record<string, any>[] {
    return investmentCriteriaRecords.map(record => {
      const flatRecord: Record<string, any> = {};

      // Basic identification fields
      flatRecord.entity_type = record.recordType === 'debt_instrument' ? 'Contact' : 'Company';
      flatRecord.entity_id = null; // Will be set by the caller
      
      // Capital position (required) - properly split delimited values
      flatRecord.capital_position = record.capitalPosition ? this.parseDelimitedValues(record.capitalPosition) : null;
      
      // Deal size information
      if (record.dealSizeInfo?.minimumDealSize !== undefined) {
        flatRecord.minimum_deal_size = record.dealSizeInfo.minimumDealSize;
      }
      if (record.dealSizeInfo?.maximumDealSize !== undefined) {
        flatRecord.maximum_deal_size = record.dealSizeInfo.maximumDealSize;
      }
      
      // Investment strategy and return information - ADD MISSING FIELDS
      // These fields were missing from the conversion but are supported in the UploadProcessor
      if (record.investmentStrategy) {
        // Target return
        if (record.investmentStrategy.targetReturn !== undefined) {
          flatRecord.target_return = record.investmentStrategy.targetReturn;
        }
        
        // Hold period information
        if (record.investmentStrategy.minHoldPeriod !== undefined) {
          flatRecord.min_hold_period = record.investmentStrategy.minHoldPeriod;
        }
        if (record.investmentStrategy.maxHoldPeriod !== undefined) {
          flatRecord.max_hold_period = record.investmentStrategy.maxHoldPeriod;
        }
      }
      
      // Location information - properly split delimited values
      if (record.location?.country && record.location.country.length > 0) {
        flatRecord.country = record.location.country.flatMap(country => this.parseDelimitedValues(country));
      }
      if (record.location?.region && record.location.region.length > 0) {
        flatRecord.region = record.location.region.flatMap(region => this.parseDelimitedValues(region));
      }
      if (record.location?.state && record.location.state.length > 0) {
        flatRecord.state = record.location.state.flatMap(state => this.parseDelimitedValues(state));
      }
      if (record.location?.city && record.location.city.length > 0) {
        flatRecord.city = record.location.city.flatMap(city => this.parseDelimitedValues(city));
      }
      if (record.location?.focus && record.location.focus.length > 0) {
        flatRecord.location_focus = record.location.focus.flatMap(focus => this.parseDelimitedValues(focus));
      }
      
      // Property type information - properly split delimited values
      if (record.propertyType?.propertyTypes && record.propertyType.propertyTypes.length > 0) {
        flatRecord.property_types = record.propertyType.propertyTypes.flatMap(type => this.parseDelimitedValues(type));
      }
      if (record.propertyType?.propertySubTypes && record.propertyType.propertySubTypes.length > 0) {
        flatRecord.property_sub_categories = record.propertyType.propertySubTypes.flatMap(subType => this.parseDelimitedValues(subType));
      }
      if (record.propertyType?.strategies && record.propertyType.strategies.length > 0) {
        flatRecord.strategies = record.propertyType.strategies.flatMap(strategy => this.parseDelimitedValues(strategy));
      }
      
      // Loan details (if applicable) - properly split delimited values
      if (record.loanDetails) {
        // Loan types
        if (record.loanDetails.loanType && record.loanDetails.loanType.length > 0) {
          flatRecord.loan_type = record.loanDetails.loanType.flatMap(type => this.parseDelimitedValues(type));
        }
        
        // Loan program
        if (record.loanDetails.loanProgram && record.loanDetails.loanProgram.length > 0) {
          flatRecord.loan_program = record.loanDetails.loanProgram.flatMap(program => this.parseDelimitedValues(program));
        }
        
        // Structured loan tranche
        if (record.loanDetails.structuredLoanTranche && record.loanDetails.structuredLoanTranche.length > 0) {
          flatRecord.structured_loan_tranche = record.loanDetails.structuredLoanTranche.flatMap(tranche => this.parseDelimitedValues(tranche));
        }
        
        // Loan terms
        if (record.loanDetails.loanTerm) {
          if (record.loanDetails.loanTerm.min !== undefined) {
            flatRecord.min_loan_term = record.loanDetails.loanTerm.min;
          }
          if (record.loanDetails.loanTerm.max !== undefined) {
            flatRecord.max_loan_term = record.loanDetails.loanTerm.max;
          }
          if (record.loanDetails.loanTerm.years !== undefined) {
            // If single years value, use it for both min and max
            flatRecord.min_loan_term = record.loanDetails.loanTerm.years;
            flatRecord.max_loan_term = record.loanDetails.loanTerm.years;
          }
        }
        
        // Interest rates
        if (record.loanDetails.interestRates) {
          if (record.loanDetails.interestRates.sofr !== undefined) {
            flatRecord.interest_rate_sofr = record.loanDetails.interestRates.sofr;
          }
          if (record.loanDetails.interestRates.libor !== undefined) {
            flatRecord.interest_rate_libor = record.loanDetails.interestRates.libor;
          }
          if (record.loanDetails.interestRates.prime !== undefined) {
            flatRecord.interest_rate_prime = record.loanDetails.interestRates.prime;
          }
          if (record.loanDetails.interestRates.wsj !== undefined) {
            flatRecord.interest_rate_wsj = record.loanDetails.interestRates.wsj;
          }
          if (record.loanDetails.interestRates.fiveYT !== undefined) {
            flatRecord.interest_rate_5yt = record.loanDetails.interestRates.fiveYT;
          }
          if (record.loanDetails.interestRates.tenYT !== undefined) {
            flatRecord.interest_rate_10yt = record.loanDetails.interestRates.tenYT;
          }
          if (record.loanDetails.interestRates.base !== undefined) {
            flatRecord.interest_rate = record.loanDetails.interestRates.base;
          }
        }
        
        // FIXED: Add generic interest rate field mapping
        // Map the generic interest rate field that contains dimension-specific rates
        if (record.loanDetails.interestRate !== undefined) {
          flatRecord.interest_rate = record.loanDetails.interestRate;
        }
        
        // Loan to value ratios
        if (record.loanDetails.loanToValue) {
          if (record.loanDetails.loanToValue.min !== undefined) {
            flatRecord.loan_to_value_min = record.loanDetails.loanToValue.min;
          }
          if (record.loanDetails.loanToValue.max !== undefined) {
            flatRecord.loan_to_value_max = record.loanDetails.loanToValue.max;
          }
        }
        
        // Loan to cost ratios
        if (record.loanDetails.loanToCost) {
          if (record.loanDetails.loanToCost.min !== undefined) {
            flatRecord.loan_to_cost_min = record.loanDetails.loanToCost.min;
          }
          if (record.loanDetails.loanToCost.max !== undefined) {
            flatRecord.loan_to_cost_max = record.loanDetails.loanToCost.max;
          }
        }
        
        // Fees
        if (record.loanDetails.fees) {
          if (record.loanDetails.fees.originationMin !== undefined) {
            flatRecord.loan_origination_fee_min = record.loanDetails.fees.originationMin;
          }
          if (record.loanDetails.fees.originationMax !== undefined) {
            flatRecord.loan_origination_fee_max = record.loanDetails.fees.originationMax;
          }
          if (record.loanDetails.fees.exitMin !== undefined) {
            flatRecord.loan_exit_fee_min = record.loanDetails.fees.exitMin;
          }
          if (record.loanDetails.fees.exitMax !== undefined) {
            flatRecord.loan_exit_fee_max = record.loanDetails.fees.exitMax;
          }
        }
        
        // DSCR (Debt Service Coverage Ratio)
        if (record.loanDetails.dscr) {
          if (record.loanDetails.dscr.min !== undefined) {
            flatRecord.min_loan_dscr = record.loanDetails.dscr.min;
          }
          if (record.loanDetails.dscr.max !== undefined) {
            flatRecord.max_loan_dscr = record.loanDetails.dscr.max;
          }
        }
        
        // Other loan details - properly split delimited values
        if (record.loanDetails.recourseLoan !== undefined) {
          if (Array.isArray(record.loanDetails.recourseLoan)) {
            // If it's already an array, use it as is (preserving original CSV structure)
            flatRecord.recourse_loan = record.loanDetails.recourseLoan;
          } else {
            // If it's a string, split it by delimiters
            flatRecord.recourse_loan = this.parseDelimitedValues(record.loanDetails.recourseLoan);
          }
        }
        if (record.loanDetails.closingTime !== undefined) {
          flatRecord.closing_time_weeks = record.loanDetails.closingTime;
        }
        
        // Handle min and max closing time fields
        if (record.loanDetails.minClosingTimeWeeks !== undefined) {
          flatRecord.min_closing_time_weeks = record.loanDetails.minClosingTimeWeeks;
        }
        if (record.loanDetails.maxClosingTimeWeeks !== undefined) {
          flatRecord.max_closing_time_weeks = record.loanDetails.maxClosingTimeWeeks;
        }
      }
      
      // Metadata fields
      flatRecord.created_at = new Date().toISOString();
      flatRecord.updated_at = new Date().toISOString();
      flatRecord.is_active = true;
      
      // Notes
      if (record.notes) {
        flatRecord.notes = record.notes;
      }
      
      // Clean up undefined values
      Object.keys(flatRecord).forEach(key => {
        if (flatRecord[key] === undefined || flatRecord[key] === null) {
          delete flatRecord[key];
        }
      });
      
      return flatRecord;
    });
  }
}

 // Legacy interfaces for backward compatibility
export interface InvestmentCriteriaRecord {
  id: string;
  capitalPosition: string;
  dealSize: string;
  recordType: 'investment_criteria' | 'debt_instrument';
  location: {
    country?: string[];
    region?: string[];
    state?: string[];
    city?: string[];
    focus?: string[];
  };
  propertyType: {
    propertyTypes?: string[];
    propertySubTypes?: string[];
    strategies?: string[];
  };
  dealSizeInfo: {
    minimumDealSize?: number;
    maximumDealSize?: number;
  };
  // ADD MISSING INVESTMENT STRATEGY FIELDS
  investmentStrategy?: {
    targetReturn?: number;
    minHoldPeriod?: number;
    maxHoldPeriod?: number;
  };
  loanDetails?: {
    loanProgram?: string[];
    structuredLoanTranche?: string[];
    loanType?: string[];
    loanTerm?: {
      min?: number;
      max?: number;
      years?: number;
    };
    interestRates?: {
      sofr?: number;
      wsj?: number;
      prime?: number;
      libor?: number;
      fiveYT?: number;
      tenYT?: number;
      base?: number;
    };
    // FIXED: Add generic interest rate field for dimension-specific rates
    interestRate?: string;
    loanToValue?: {
      min?: number;
      max?: number;
    };
    loanToCost?: {
      min?: number;
      max?: number;
    };
    fees?: {
      originationMin?: number;
      originationMax?: number;
      exitMin?: number;
      exitMax?: number;
    };
    dscr?: {
      min?: number;
      max?: number;
    };
    recourseLoan?: string[];
    closingTime?: number;
    minClosingTimeWeeks?: number;
    maxClosingTimeWeeks?: number;
  };
  notes?: string;
}

export interface ProcessingResult {
  contactData: Record<string, any>;
  companyData: Record<string, any>;
  investmentCriteriaData: Record<string, any>[];
  errors: string[];
  warnings: string[];
  recordCount: number;
} 