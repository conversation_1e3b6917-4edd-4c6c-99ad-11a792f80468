import { BaseProcessor } from './BaseProcessor'
import { EntityData, ProcessorOptions, UnifiedEntityData } from '../../types/processing'
import { LLMFactory, LLMMessage, createProcessorLoggerAdapter, LogLevel } from '../llm'
import { COMPANY_INVESTMENT_CRITERIA_SYSTEM_PROMPT, COMPANY_INVESTMENT_CRITERIA_USER_TEMPLATE_FUNCTION } from '../prompts/company-investment-criteria'

interface InvestmentCriteriaData {
  investmentCriteria: Array<{
    // Deal Scope
    capital_position: string
    minimum_deal_size?: number | null
    maximum_deal_size?: number | null
    
    // Geography
    country: string[]
    region: string[]
    state: string[]
    city: string[]
    
    // Asset Strategy
    property_types: string[]
    property_subcategories: string[]
    strategies: string[]
    decision_making_process?: string | null
    
    // Additional Info
    notes?: string | null
    
    // DEBT-SPECIFIC FIELDS
    debt_criteria?: {
      closing_time?: number | null
      future_facilities?: string | null
      eligible_borrower?: string | null
      occupancy_requirements?: string | null
      lien_position?: string | null
      min_loan_dscr?: number | null
      max_loan_dscr?: number | null
      recourse_loan?: string | null
      loan_min_debt_yield?: string | null
      prepayment?: string | null
      yield_maintenance?: string | null
      application_deposit?: number | null
      good_faith_deposit?: number | null
      loan_origination_max_fee?: number | null
      loan_origination_min_fee?: number | null
      loan_exit_min_fee?: number | null
      loan_exit_max_fee?: number | null
      loan_interest_rate?: number | null
      loan_interest_rate_based_off_sofr?: number | null
      loan_interest_rate_based_off_wsj?: number | null
      loan_interest_rate_based_off_prime?: number | null
      loan_interest_rate_based_off_3yt?: number | null
      loan_interest_rate_based_off_5yt?: number | null
      loan_interest_rate_based_off_10yt?: number | null
      loan_interest_rate_based_off_30yt?: number | null
      rate_lock?: string | null
      rate_type?: string | null
      loan_to_value_max?: number | null
      loan_to_value_min?: number | null
      loan_to_cost_min?: number | null
      loan_to_cost_max?: number | null
      debt_program_overview?: string | null
      loan_type?: string | null
      loan_type_normalized?: string | null
      structured_loan_tranche?: string | null
      loan_program?: string | null
      min_loan_term?: number | null
      max_loan_term?: number | null
      amortization?: string | null
    }
    
    // EQUITY-SPECIFIC FIELDS
    equity_criteria?: {
      target_return?: number | null
      minimum_internal_rate_of_return?: number | null
      minimum_yield_on_cost?: number | null
      minimum_equity_multiple?: number | null
      target_cash_on_cash_min?: number | null
      min_hold_period_years?: number | null
      max_hold_period_years?: number | null
      ownership_requirement?: string | null
      attachment_point?: number | null
      max_leverage_tolerance?: number | null
      typical_closing_timeline_days?: number | null
      proof_of_funds_requirement?: boolean | null
      equity_program_overview?: string | null
      occupancy_requirements?: string | null
    }
  }>
}

export class CompanyInvestmentCriteriaProcessor extends BaseProcessor {
  private llmProvider: any

  constructor(options: ProcessorOptions = {}) {
    // Perplexity API specific rate limiting configuration for investment criteria
    const investmentCriteriaBottleneckConfig = {
      maxConcurrent: 10,                   // Reduced from 5 to prevent queue overflow
      minTime: 2000,                      // 2 seconds between requests (more conservative for complex extraction)
      retryAttempts: 3,                   // Standard retries for LLM API
      retryDelayBase: 3000,               // 3 second base delay for retries
      retryDelayMax: 45000,               // Max 45 second retry delay (longer for complex tasks)
      timeout: 300000,                    // 5 minutes timeout for complex LLM processing
      highWater: 200,                     // Lower queue limit for complex tasks
      strategy: 'OVERFLOW' as any,        // Use OVERFLOW strategy
      defaultPriority: 6,                 // Lower priority for investment criteria
      enableJobMetrics: true              // Track LLM API performance
    }

    super('CompanyInvestmentCriteriaProcessor', {
      ...options,
      bottleneckConfig: options.bottleneckConfig || investmentCriteriaBottleneckConfig
    })
    
    // Create logger adapter for LLM provider
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this))
    
    // Use Perplexity for web-enhanced research capabilities
    this.llmProvider = LLMFactory.createProvider(
      'perplexity',
      loggerAdapter,
      {
        apiKey: process.env.PERPLEXITY_API_KEY,
      }
    )
  }

  async getUnprocessedEntities(): Promise<UnifiedEntityData[]> {
    const baseFilters = {
      companyOverviewV2Status: 'completed'
    }

    const specificFilters = {
      companyInvestmentCriteriaStatus: 'pending'
    }

    return await this.getUnprocessedUnifiedEntities(baseFilters, specificFilters, 'company')
  }

  async processEntity(entity: EntityData): Promise<{ success: boolean; error?: string }> {
    try {
      this.log('info', `Processing investment criteria for company ${entity.id}`)

      // Update status to running
      await this.updateCompanyInvestmentCriteriaStatus(entity.id, 'running')

      // Get enrichment data from companies table (populated by CompanyOverviewProcessorV2)
      const enrichmentData = await this.getCompanyEnrichmentData(entity.id)

      // Get central mappings for allowed values
      const mappings = await this.getCentralMappings()

      // Create the user prompt with enrichment data
      const userPrompt = COMPANY_INVESTMENT_CRITERIA_USER_TEMPLATE_FUNCTION(
        {
          company_name: (entity as any).company_name || 'Unknown Company',
          company_website: (entity as any).company_website || '',
          industry: (entity as any).industry
        },
        mappings,
        enrichmentData // Pass enrichment data to the template function
      )

      this.log('debug', `Generated user prompt for company ${entity.id} with enrichment data`)

      // Call LLM for investment criteria extraction
      const messages: LLMMessage[] = [
        { role: 'system', content: COMPANY_INVESTMENT_CRITERIA_SYSTEM_PROMPT },
        { role: 'user', content: userPrompt }
      ]
      
      const response = await this.llmProvider.callLLM(messages, {
        maxTokens: 12000, // Increased token limit for comprehensive extraction
        temperature: 0.1, // Low temperature for consistent, accurate extraction
        topP: 0.9,
        frequencyPenalty: 0.1,
        presencePenalty: 0.1
      })

      if (!response || !response.content) {
        const error = 'Empty response from LLM'
        await this.updateCompanyInvestmentCriteriaStatus(entity.id, 'failed', error)
        return { success: false, error }
      }

      // Parse the JSON response
      let extractedData: InvestmentCriteriaData
      try {
        extractedData = JSON.parse(response.content)
      } catch (parseError) {
        const error = `Failed to parse LLM response as JSON: ${parseError}`
        this.log('error', error)
        await this.updateCompanyInvestmentCriteriaStatus(entity.id, 'failed', error)
        return { success: false, error }
      }

      // Validate extracted data
      if (!extractedData.investmentCriteria || !Array.isArray(extractedData.investmentCriteria)) {
        const error = 'Invalid investment criteria data structure'
        await this.updateCompanyInvestmentCriteriaStatus(entity.id, 'failed', error)
        return { success: false, error }
      }

      // Save investment criteria to database
      const saved = await this.saveInvestmentCriteria(entity.id, extractedData.investmentCriteria)
      if (!saved) {
        const error = 'Failed to save investment criteria to database'
        await this.updateCompanyInvestmentCriteriaStatus(entity.id, 'failed', error)
        return { success: false, error }
      }

      // Save processing attempt information
      await this.saveProcessingAttempt(
        'company',
        entity.id,
        'company_investment_criteria',
        response.usage,
        response.model,
        response.usage?.total_tokens,
        true
      )

      // Update status to completed
      await this.updateCompanyInvestmentCriteriaStatus(entity.id, 'completed')

      this.log('info', `Successfully processed investment criteria for company ${entity.id}`)
      return { success: true }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error processing investment criteria for company ${entity.id}: ${errorMessage}`)

      await this.updateCompanyInvestmentCriteriaStatus(entity.id, 'error', errorMessage)
      await this.incrementProcessingErrorCount('company', entity.id)

      // Save failed processing attempt
      await this.saveProcessingAttempt(
        'company',
        entity.id,
        'company_investment_criteria',
        undefined,
        undefined,
        undefined,
        false,
        errorMessage
      )

      return { success: false, error: errorMessage }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    if (success) {
      await this.updateCompanyInvestmentCriteriaStatus(entityId, 'completed')
    } else {
      await this.updateCompanyInvestmentCriteriaStatus(entityId, 'failed', error)
    }
  }

  /**
   * Update company investment criteria extraction status
   */
  protected async updateCompanyInvestmentCriteriaStatus(
    companyId: number,
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE companies 
      SET investment_criteria_status = $1::text, 
          investment_criteria_error = $2,
          investment_criteria_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE investment_criteria_date END,
          updated_at = NOW() 
      WHERE company_id = $3
    `
    try {
      await this.query(sql, [status, error || null, companyId])
      this.log('debug', `Updated investment criteria status for company ${companyId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating investment criteria status for company ${companyId}: ${error}`)
    }
  }



  /**
   * Get central mappings for allowed values with nested property structure
   */
  private async getCentralMappings(): Promise<Record<string, string[]>> {
    try {
      // Enhanced query to handle hierarchical structure and get all values from all levels
      const sql = `
        SELECT type as mapping_type, 
               ARRAY_AGG(DISTINCT value_1) FILTER (WHERE value_1 IS NOT NULL) as values_1,
               ARRAY_AGG(DISTINCT value_2) FILTER (WHERE value_2 IS NOT NULL) as values_2,
               ARRAY_AGG(DISTINCT value_3) FILTER (WHERE value_3 IS NOT NULL) as values_3
        FROM central_mapping 
        WHERE is_active = true
        GROUP BY type
      `
      const result = await this.query(sql)
      
      const mappings: Record<string, string[]> = {}
      
      // Process each mapping type
      for (const row of result) {
        const mappingType = row.mapping_type as string
        const values1 = (row.values_1 as string[]) || []
        const values2 = (row.values_2 as string[]) || []
        const values3 = (row.values_3 as string[]) || []
        
        // Combine all values from all levels and remove duplicates
        const allValues = [...values1, ...values2, ...values3]
        const filteredValues = allValues.filter(value => 
          value && typeof value === 'string' && value.trim() !== ''
        )
        
        if (filteredValues.length > 0) {
          mappings[mappingType] = [...new Set(filteredValues)] // Remove duplicates
        }
      }
      
      // Special handling for Loan Type (from Capital Position subcategories)
      if (!mappings['Loan Type'] || mappings['Loan Type'].length === 0) {
        const loanTypeSql = `
          SELECT DISTINCT value_2 as loan_type
          FROM central_mapping 
          WHERE type = 'Capital Position' 
            AND level_2 = 'Loan Type' 
            AND value_2 IS NOT NULL 
            AND is_active = true
        `
        const loanTypeResult = await this.query(loanTypeSql)
        const loanTypes = loanTypeResult.map((row: any) => row.loan_type as string).filter(Boolean)
        
        if (loanTypes.length > 0) {
          mappings['Loan Type'] = loanTypes
        } else {
          // Fallback hardcoded list if no data found
          mappings['Loan Type'] = [
            'Acquisition', 'Bridge', 'Construction', 'Permanent', 'Refinance',
            'Mezzanine', 'Hard Money', 'SBA', 'FHA', 'VA', 'USDA',
            'CMBS', 'Portfolio', 'Mini-Perm', 'Ground Lease', 'Equipment'
          ]
        }
      }
      
      // Special handling for U.S States (from U.S Regions subcategories)
      if (!mappings['U.S States'] || mappings['U.S States'].length === 0) {
        const statesSql = `
          SELECT DISTINCT value_2 as state
          FROM central_mapping 
          WHERE type = 'U.S Regions' 
            AND level_2 = 'U.S Regions States' 
            AND value_2 IS NOT NULL 
            AND is_active = true
        `
        const statesResult = await this.query(statesSql)
        const states = statesResult.map((row: any) => row.state as string).filter(Boolean)
        
        if (states.length > 0) {
          mappings['U.S States'] = states
        } else {
          // Fallback hardcoded list if no data found
          mappings['U.S States'] = [
            'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut',
            'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa',
            'Kansas', 'Kentucky', 'Louisiana', 'Maine', 'Maryland', 'Massachusetts', 'Michigan',
            'Minnesota', 'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire',
            'New Jersey', 'New Mexico', 'New York', 'North Carolina', 'North Dakota', 'Ohio',
            'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota',
            'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington', 'West Virginia',
            'Wisconsin', 'Wyoming', 'District of Columbia'
          ]
        }
      }
      
      // Create Property Type and Subcategory mapping
      const propertyTypeSql = `
        SELECT value_1 as property_type, value_2 as subcategory
        FROM central_mapping 
        WHERE type = 'Property Type' 
          AND is_active = true
          AND value_1 IS NOT NULL
        ORDER BY value_1, value_2
      `
      const propertyTypeResult = await this.query(propertyTypeSql)
      
      const propertyTypeMap: Record<string, string[]> = {}
      for (const row of propertyTypeResult) {
        const propertyType = row.property_type as string
        const subcategory = row.subcategory as string
        
        if (propertyType && subcategory) {
          if (!propertyTypeMap[propertyType]) {
            propertyTypeMap[propertyType] = []
          }
          propertyTypeMap[propertyType].push(subcategory)
        }
      }
      
      // Add property type mappings in the format expected by the prompt
      for (const [propertyType, subcategories] of Object.entries(propertyTypeMap)) {
        const key = `Property Type - ${propertyType} - Subproperty Type`
        mappings[key] = subcategories
      }
      
      // Also add the main property types
      if (!mappings['Property Type']) {
        mappings['Property Type'] = Object.keys(propertyTypeMap)
      }
      
      // Ensure all required mapping types exist with at least empty arrays
      const requiredTypes = [
        'Property Type', 'Strategies', 'Capital Position', 'Loan Program', 
        'Loan Type', 'Structured Loan Tranches', 'Recourse Loan', 
        'U.S Regions', 'U.S States'
      ]
      
      requiredTypes.forEach(type => {
        if (!mappings[type]) {
          mappings[type] = []
        }
      })
      
      this.log('debug', `Loaded mappings: ${Object.keys(mappings).join(', ')}`)
      this.log('debug', `Loan Types count: ${mappings['Loan Type']?.length || 0}`)
      this.log('debug', `U.S States count: ${mappings['U.S States']?.length || 0}`)
      this.log('debug', `Property Types count: ${mappings['Property Type']?.length || 0}`)
      this.log('debug', `Property Type mappings: ${Object.keys(propertyTypeMap).join(', ')}`)
      
      return mappings
    } catch (error) {
      this.log('error', `Error fetching central mappings: ${error}`)
      return {}
    }
  }

  /**
   * Get company enrichment data from companies table (populated by CompanyOverviewProcessorV2)
   */
  private async getCompanyEnrichmentData(companyId: number): Promise<any> {
    try {
      const sql = `
        SELECT 
          company_name,
          company_type,
          industry,
          business_model,
          founded_year,
          investment_focus,
          investment_strategy_mission,
          investment_strategy_approach,
          company_website,
          company_phone,
          secondary_phone,
          main_email,
          secondary_email,
          company_linkedin,
          twitter,
          facebook,
          instagram,
          youtube,
          company_address,
          company_city,
          company_state,
          company_zip,
          company_country,
          additional_address,
          additional_city,
          additional_state,
          additional_zipcode,
          additional_country,
          office_locations,
          fund_size,
          aum,
          number_of_properties,
          number_of_offices,
          number_of_employees,
          annual_revenue,
          net_income,
          ebitda,
          profit_margin,
          market_capitalization,
          market_share_percentage,
          balance_sheet_strength,
          funding_sources,
          recent_capital_raises,
          typical_debt_to_equity_ratio,
          development_fee_structure,
          credit_rating,
          dry_powder,
          annual_deployment_target,
          investment_vehicle_type,
          active_fund_name_series,
          fund_size_active_fund,
          fundraising_status,
          lender_type,
          annual_loan_volume,
          lending_origin,
          portfolio_health,
          partnerships,
          key_equity_partners,
          key_debt_partners,
          board_of_directors,
          key_executives,
          founder_background,
          market_cycle_positioning,
          urban_vs_suburban_preference,
          sustainability_esg_focus,
          technology_proptech_adoption,
          adaptive_reuse_experience,
          regulatory_zoning_expertise,
          corporate_structure,
          parent_company,
          subsidiaries,
          stock_ticker_symbol,
          stock_exchange,
          products_services_description,
          target_customer_profile,
          major_competitors,
          unique_selling_proposition,
          industry_awards_recognitions,
          company_history,
          transactions_completed_last_12m,
          total_transaction_volume_ytd,
          deal_count_ytd,
          average_deal_size,
          portfolio_size_sqft,
          portfolio_asset_count,
          role_in_previous_deal,
          internal_relationship_manager,
          last_contact_date,
          pipeline_status,
          recent_news_sentiment,
          data_source,
          data_confidence_score,
          quarterly_earnings_link
        FROM companies
        WHERE company_id = $1
      `
      const result = await this.query(sql, [companyId])

      if (result.length === 0) {
        this.log('warn', `No enrichment data found for company ${companyId}`)
        return null
      }

      const enrichmentData = result[0]
      
      // Convert PostgreSQL arrays to JavaScript arrays
      const arrayFields = [
        'investment_focus', 'office_locations', 'funding_sources', 'partnerships',
        'key_equity_partners', 'key_debt_partners', 'board_of_directors', 'key_executives',
        'subsidiaries', 'major_competitors', 'industry_awards_recognitions'
      ]
      
      arrayFields.forEach(field => {
        if (enrichmentData[field] && typeof enrichmentData[field] === 'string') {
          try {
            // Parse PostgreSQL array format {item1,item2,item3}
            const arrayString = enrichmentData[field] as string
            if (arrayString.startsWith('{') && arrayString.endsWith('}')) {
              const items = arrayString.slice(1, -1).split(',').map(item => item.trim().replace(/"/g, ''))
              enrichmentData[field] = items.filter(item => item !== '')
            }
          } catch (error) {
            this.log('warn', `Error parsing array field ${field}: ${error}`)
            enrichmentData[field] = []
          }
        }
      })

      this.log('debug', `Retrieved enrichment data for company ${companyId} with ${Object.keys(enrichmentData).length} fields`)
      return enrichmentData
    } catch (error) {
      this.log('error', `Error fetching company enrichment data for ${companyId}: ${error}`)
      return null
    }
  }

  /**
   * Convert JavaScript array to PostgreSQL array literal format
   */
  private arrayToPostgresArray(arr: string[] | null | undefined): string | null {
    if (!arr || arr.length === 0) {
      return null
    }
    
    // Escape quotes and wrap values that contain spaces or special characters
    const escapedValues = arr.map(value => {
      if (typeof value !== 'string') {
        return String(value)
      }
      
      // If value contains spaces, commas, quotes, or special characters, wrap in quotes and escape
      if (value.includes(' ') || value.includes(',') || value.includes('"') || value.includes('\'') || value.includes('{') || value.includes('}')) {
        return `"${value.replace(/"/g, '\\"')}"` 
      }
      
      return value
    })
    
    return `{${escapedValues.join(',')}}`
  }

  /**
   * Save investment criteria to database
   */
  private async saveInvestmentCriteria(companyId: number, criteriaList: any[]): Promise<boolean> {
    const client = await this.pool.connect()
    try {
      await client.query('BEGIN')

      // Do not delete existing investment criteria for this company.
      // New criteria will be appended as additional rows linked to the same entity.

      // Insert new investment criteria
      for (const criteria of criteriaList) {
        // Insert into investment_criteria_central
        const centralSql = `
          INSERT INTO investment_criteria_central (
            entity_id, entity_type, investment_criteria_debt_id, investment_criteria_equity_id,
            capital_position, minimum_deal_size, maximum_deal_size,
            country, region, state, city, property_types, property_subcategories, 
            strategies, decision_making_process, notes, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
          RETURNING investment_criteria_id
        `

        const centralResult = await client.query(centralSql, [
          companyId,
          'company',
          null, // investment_criteria_debt_id - NULL for company records
          null, // investment_criteria_equity_id - NULL for company records
          criteria.capital_position || null,
          criteria.minimum_deal_size || null,
          criteria.maximum_deal_size || null,
          this.arrayToPostgresArray(criteria.country),
          this.arrayToPostgresArray(criteria.region),
          this.arrayToPostgresArray(criteria.state),
          this.arrayToPostgresArray(criteria.city),
          this.arrayToPostgresArray(criteria.property_types),
          this.arrayToPostgresArray(criteria.property_subcategories),
          this.arrayToPostgresArray(criteria.strategies),
          criteria.decision_making_process || null,
          criteria.notes || null,
          new Date(),
          new Date()
        ])

        const criteriaId = centralResult.rows[0].investment_criteria_id

        // Determine if this is a debt or equity position based on capital_position
        const isDebtPosition = ['Senior Debt', 'Stretch Senior', 'Mezzanine'].includes(criteria.capital_position)
        const isEquityPosition = ['Preferred Equity', 'Common Equity', 'General Partner (GP)', 'Limited Partner (LP)', 'Joint Venture (JV)', 'Co-GP'].includes(criteria.capital_position)
        const isOtherPosition = ['Third Party', 'Undetectable'].includes(criteria.capital_position)

        // Insert debt criteria ONLY for debt positions
        if (criteria.debt_criteria && isDebtPosition) {
          const debtSql = `
            INSERT INTO investment_criteria_debt (
              investment_criteria_id, notes, closing_time, future_facilities, eligible_borrower,
              occupancy_requirements, lien_position, min_loan_dscr, max_loan_dscr, recourse_loan,
              loan_min_debt_yield, prepayment, yield_maintenance, application_deposit, good_faith_deposit,
              loan_origination_max_fee, loan_origination_min_fee, loan_exit_min_fee, loan_exit_max_fee,
              loan_interest_rate, loan_interest_rate_based_off_sofr, loan_interest_rate_based_off_wsj,
              loan_interest_rate_based_off_prime, loan_interest_rate_based_off_3yt, loan_interest_rate_based_off_5yt,
              loan_interest_rate_based_off_10yt, loan_interest_rate_based_off_30yt, rate_lock, rate_type,
              loan_to_value_max, loan_to_value_min, loan_to_cost_min, loan_to_cost_max,
              debt_program_overview, loan_type, loan_type_normalized, structured_loan_tranche,
              loan_program, min_loan_term, max_loan_term, amortization, created_at, updated_at
            ) VALUES (
              $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19,
              $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43
            )
            RETURNING investment_criteria_debt_id
          `

          const debt = criteria.debt_criteria
          const debtResult = await client.query(debtSql, [
            criteriaId, debt.notes || null, debt.closing_time || null, debt.future_facilities || null,
            debt.eligible_borrower || null, debt.occupancy_requirements || null, debt.lien_position || null,
            debt.min_loan_dscr || null, debt.max_loan_dscr || null, debt.recourse_loan || null,
            debt.loan_min_debt_yield || null, debt.prepayment || null, debt.yield_maintenance || null,
            debt.application_deposit || null, debt.good_faith_deposit || null, debt.loan_origination_max_fee || null,
            debt.loan_origination_min_fee || null, debt.loan_exit_min_fee || null, debt.loan_exit_max_fee || null,
            debt.loan_interest_rate || null, debt.loan_interest_rate_based_off_sofr || null,
            debt.loan_interest_rate_based_off_wsj || null, debt.loan_interest_rate_based_off_prime || null,
            debt.loan_interest_rate_based_off_3yt || null, debt.loan_interest_rate_based_off_5yt || null,
            debt.loan_interest_rate_based_off_10yt || null, debt.loan_interest_rate_based_off_30yt || null,
            debt.rate_lock || null, debt.rate_type || null, debt.loan_to_value_max || null,
            debt.loan_to_value_min || null, debt.loan_to_cost_min || null, debt.loan_to_cost_max || null,
            debt.debt_program_overview || null, debt.loan_type || null, debt.loan_type_normalized || null,
            debt.structured_loan_tranche || null, debt.loan_program || null, debt.min_loan_term || null,
            debt.max_loan_term || null, debt.amortization || null, new Date(), new Date()
          ])
          const debtId = debtResult.rows[0].investment_criteria_debt_id
          // Update investment_criteria_central with the debt_id
          await client.query(
            'UPDATE investment_criteria_central SET investment_criteria_debt_id = $1 WHERE investment_criteria_id = $2',
            [debtId, criteriaId]
          )
        }

        // Insert equity criteria ONLY for equity positions
        if (criteria.equity_criteria && isEquityPosition) {
          const equitySql = `
            INSERT INTO investment_criteria_equity (
              investment_criteria_id, target_return, minimum_internal_rate_of_return, minimum_yield_on_cost,
              minimum_equity_multiple, target_cash_on_cash_min, min_hold_period_years, max_hold_period_years,
              ownership_requirement, attachment_point, max_leverage_tolerance, typical_closing_timeline_days,
              proof_of_funds_requirement, notes, equity_program_overview, occupancy_requirements, created_at, updated_at
            ) VALUES (
              $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18
            )
            RETURNING investment_criteria_equity_id
          `

          const equity = criteria.equity_criteria
          const equityResult = await client.query(equitySql, [
            criteriaId, equity.target_return || null, equity.minimum_internal_rate_of_return || null,
            equity.minimum_yield_on_cost || null, equity.minimum_equity_multiple || null,
            equity.target_cash_on_cash_min || null, equity.min_hold_period_years || null,
            equity.max_hold_period_years || null, equity.ownership_requirement || null,
            equity.attachment_point || null, equity.max_leverage_tolerance || null,
            equity.typical_closing_timeline_days || null, equity.proof_of_funds_requirement || null,
            equity.notes || null, equity.equity_program_overview || null, equity.occupancy_requirements || null,
            new Date(), new Date()
          ])

          // Update investment_criteria_central with the equity_id
          const equityId = equityResult.rows[0].investment_criteria_equity_id
          await client.query(
            'UPDATE investment_criteria_central SET investment_criteria_equity_id = $1 WHERE investment_criteria_id = $2',
            [equityId, criteriaId]
          )
        }

        // For other positions (Third Party, Undetectable), keep both debt_id and equity_id as NULL
        // This ensures they only exist in the central table
      }

      await client.query('COMMIT')
      this.log('info', `Saved ${criteriaList.length} investment criteria records for company ${companyId}`)
      return true

    } catch (error) {
      await client.query('ROLLBACK')
      this.log('error', `Error saving investment criteria for company ${companyId}: ${error}`)
      return false
    } finally {
      client.release()
    }
  }
}
