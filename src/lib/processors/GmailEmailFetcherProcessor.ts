import { google, gmail_v1 } from "googleapis";
import { JWT } from "google-auth-library";
import fs from "fs";
import { pool } from "../db";
import { GaxiosResponse } from "googleapis-common";

export class GmailEmailFetcherProcessor {
  private serviceAccountPath: string;
  private scopes: string[] = [
    "https://www.googleapis.com/auth/gmail.readonly",
    "https://www.googleapis.com/auth/gmail.modify",
  ];
  private jwtClient: JWT | null = null;

  constructor(serviceAccountPath: string) {
    this.serviceAccountPath = serviceAccountPath;
  }

  async authenticate() {
    // Load service account credentials
    const key = JSON.parse(fs.readFileSync(this.serviceAccountPath, "utf8"));
    // No longer use GMAIL_IMPERSONATE_EMAIL env variable
    // Just set up the JWT client for later impersonation
    this.jwtClient = new google.auth.JWT({
      email: key.client_email,
      key: key.private_key,
      scopes: this.scopes,
    });
  }

  async getGmailClientForUser(userEmail: string): Promise<gmail_v1.Gmail> {
    if (!this.jwtClient) throw new Error("Not authenticated");
    // Set the subject to the user to impersonate
    this.jwtClient.subject = userEmail;
    await this.jwtClient.authorize();
    return google.gmail({ version: "v1", auth: this.jwtClient });
  }

  async fetchAccounts(): Promise<string[]> {
    // Fetch a limited number of Gmail accounts to avoid spam/overload
    const limit = 5; // You can adjust this limit as needed
    const result = await pool.query(
      "SELECT email FROM gmail_accounts ORDER BY updated_at ASC LIMIT $1",
      [limit]
    );
    return result.rows.map((row: any) => row.email);
  }

  async fetchThreadsForAccount(accountEmail: string, lastHistoryId?: string) {
    const gmail = await this.getGmailClientForUser(accountEmail);
    let threads: gmail_v1.Schema$Thread[] = [];
    let latestHistoryId: string | undefined = undefined;
    if (lastHistoryId) {
      // Incremental sync: fetch history since lastHistoryId
      let nextPageToken: string | undefined = undefined;
      try {
        do {
          console.log(
            `[DEBUG] [fetchThreadsForAccount] (incremental) Fetching history page, nextPageToken:`,
            nextPageToken
          );
          const res: any = await gmail.users.history.list({
            userId: "me",
            startHistoryId: lastHistoryId,
            historyTypes: ["messageAdded"],
            pageToken: nextPageToken,
          });
          console.log(
            `[DEBUG] [fetchThreadsForAccount] (incremental) Fetched history page, got:`,
            res.data?.history?.length,
            "history records"
          );
          if (res.data && res.data.history) {
            for (const historyRecord of res.data.history) {
              if (historyRecord.messagesAdded) {
                for (const msgAdded of historyRecord.messagesAdded) {
                  if (msgAdded.message?.threadId) {
                    // Only add unique threadIds
                    if (
                      !threads.find((t) => t.id === msgAdded.message!.threadId)
                    ) {
                      threads.push({
                        id: msgAdded.message!.threadId,
                      } as gmail_v1.Schema$Thread);
                    }
                  }
                }
              }
            }
          }
          nextPageToken = res.data?.nextPageToken || undefined;
          if (res.data?.historyId) {
            latestHistoryId = res.data.historyId;
          }
        } while (nextPageToken);
      } catch (err: any) {
        if (err.code === 404 || err.status === 404) {
          // History ID too old, do a full sync and reset last_history_id
          console.warn(
            `[GmailEmailFetcherProcessor] HistoryId ${lastHistoryId} not found for ${accountEmail}, doing full sync.`
          );
          let nextPageToken: string | undefined = undefined;
          do {
            console.log(
              `[DEBUG] [fetchThreadsForAccount] (full sync fallback) Fetching threads page, nextPageToken:`,
              nextPageToken
            );
            const res: any = await gmail.users.threads.list({
              userId: "me",
              maxResults: 50,
              pageToken: nextPageToken,
            });
            console.log(
              `[DEBUG] [fetchThreadsForAccount] (full sync fallback) Fetched threads page, got:`,
              res.data?.threads?.length,
              "threads"
            );
            if (res.data && res.data.threads) {
              threads = threads.concat(res.data.threads);
            }
            nextPageToken = res.data?.nextPageToken || undefined;
          } while (nextPageToken);
          // After full sync, fetch the current historyId
          const profile = await gmail.users.getProfile({ userId: "me" });
          if (profile.data && profile.data.historyId) {
            latestHistoryId = profile.data.historyId;
          }
        } else {
          throw err;
        }
      }
    } else {
      // Full sync: fetch all threads
      let nextPageToken: string | undefined = undefined;
      do {
        console.log(
          `[DEBUG] [fetchThreadsForAccount] (full sync) Fetching threads page, nextPageToken:`,
          nextPageToken
        );
        const res: any = await gmail.users.threads.list({
          userId: "me",
          maxResults: 50,
          pageToken: nextPageToken,
        });
        console.log(
          `[DEBUG] [fetchThreadsForAccount] (full sync) Fetched threads page, got:`,
          res.data?.threads?.length,
          "threads"
        );
        if (res.data && res.data.threads) {
          threads = threads.concat(res.data.threads);
        }
        nextPageToken = res.data?.nextPageToken || undefined;
      } while (nextPageToken);
      // After full sync, fetch the current historyId
      const profile = await gmail.users.getProfile({ userId: "me" });
      if (profile.data && profile.data.historyId) {
        latestHistoryId = profile.data.historyId;
      }
    }
    return { threads, latestHistoryId };
  }

  async fetchMessagesForThread(accountEmail: string, threadId: string) {
    // Use Gmail API to fetch all messages in a thread
    const gmail = await this.getGmailClientForUser(accountEmail);
    const res = await gmail.users.threads.get({
      userId: "me",
      id: threadId,
      format: "full",
    });
    return res.data.messages || [];
  }

  async processAndStoreThread(
    thread: any,
    messages: any[],
    accountEmail: string
  ) {
    // 1. Upsert account and get last_history_id
    const accountResult = await pool.query(
      `INSERT INTO gmail_accounts (email, created_at, updated_at)
       VALUES ($1, NOW(), NOW())
       ON CONFLICT (email) DO UPDATE SET updated_at = NOW()
       RETURNING id, last_history_id`,
      [accountEmail]
    );
    const accountId = accountResult.rows[0].id;
    const lastHistoryId = accountResult.rows[0].last_history_id;

    // 2. Upsert thread by provider_thread_id and account_id
    const providerThreadId = thread.id;
    const subject =
      messages[0]?.payload?.headers?.find((h: any) => h.name === "Subject")
        ?.value || null;
    const metadata = JSON.stringify(thread);
    const now = new Date();
    const threadResult = await pool.query(
      `INSERT INTO gmail_threads (provider_thread_id, account_id, subject, metadata, created_at, updated_at)
       VALUES ($1, $2, $3, $4::jsonb, $5, $6)
       ON CONFLICT (provider_thread_id, account_id) DO UPDATE SET subject = EXCLUDED.subject, metadata = EXCLUDED.metadata, updated_at = EXCLUDED.updated_at
       RETURNING id`,
      [providerThreadId, accountId, subject, metadata, now, now]
    );
    const internalThreadId = threadResult.rows[0].id;

    // 3. Insert only new messages (skip if already exists by gmail_message_id)
    for (const msg of messages) {
      const gmailMessageId = msg.id;
      const headers = msg.payload?.headers || [];
      // Extract only email addresses for sender and recipients
      const fromHeader = headers.find((h: any) => h.name === "From")?.value;
      const tosHeader = headers
        .filter((h: any) => h.name === "To")
        .map((h: any) => h.value);
      // Use regex to extract email addresses
      const extractEmail = (str: string) => {
        const match =
          str && str.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
        return match ? match[0] : null;
      };
      const from = extractEmail(fromHeader);
      const tos = tosHeader.map(extractEmail).filter(Boolean);
      const subject = headers.find((h: any) => h.name === "Subject")?.value;
      const sentAt = headers.find((h: any) => h.name === "Date")?.value
        ? new Date(headers.find((h: any) => h.name === "Date").value)
        : null;
      let body = "";
      if (msg.payload?.body?.data) {
        body = Buffer.from(msg.payload.body.data, "base64").toString("utf8");
      } else if (msg.payload?.parts) {
        // Try to find the first text/plain or text/html part
        const part = msg.payload.parts.find(
          (p: any) => p.mimeType === "text/plain" || p.mimeType === "text/html"
        );
        if (part?.body?.data) {
          body = Buffer.from(part.body.data, "base64").toString("utf8");
        }
      }
      // Store all metadata (including headers)
      const messageMetadata = JSON.stringify({
        ...msg,
        headers,
      });
      await pool.query(
        `INSERT INTO gmail_messages (thread_id, gmail_message_id, sender, recipients, sent_at, subject, body, raw, metadata, created_at, account_id)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8::jsonb, $9::jsonb, $10, $11)
         ON CONFLICT (gmail_message_id) DO NOTHING`,
        [
          internalThreadId,
          gmailMessageId,
          from,
          tos,
          sentAt,
          subject,
          body,
          JSON.stringify(msg),
          messageMetadata,
          now,
          accountId,
        ]
      );
    }
    // Optionally: update last_history_id after processing (if available in thread/messages)
    // You may want to update this in a higher-level sync function after all threads/messages are processed.
  }

  async run() {
    await this.authenticate();
    if (!this.jwtClient) {
      console.warn(
        "[GmailEmailFetcherProcessor] No JWT client available. Aborting run."
      );
      return;
    }
    const accounts = await this.fetchAccounts();
    for (const accountEmail of accounts) {
      try {
        // Get last_history_id for this account
        const accountResult = await pool.query(
          `SELECT last_history_id FROM gmail_accounts WHERE email = $1`,
          [accountEmail]
        );
        const lastHistoryId = accountResult.rows[0]?.last_history_id;
        console.log(
          `[GmailEmailFetcherProcessor] Processing account: ${accountEmail} (last_history_id: ${lastHistoryId})`
        );
        // 1. Fetch threads for this account (incremental if possible)
        const { threads, latestHistoryId } = await this.fetchThreadsForAccount(
          accountEmail,
          lastHistoryId
        );
        console.log(
          `[GmailEmailFetcherProcessor] Found ${threads.length} threads for account: ${accountEmail}`
        );
        for (const thread of threads) {
          try {
            // 2. Fetch messages for this thread
            const messages = await this.fetchMessagesForThread(
              accountEmail,
              thread.id!
            );
            console.log(
              `[GmailEmailFetcherProcessor] Thread ${thread.id}: fetched ${messages.length} messages.`
            );
            // 3. Process and store in DB
            await this.processAndStoreThread(thread, messages, accountEmail);
            console.log(
              `[GmailEmailFetcherProcessor] Thread ${thread.id}: processed and stored.`
            );
          } catch (err) {
            console.error(
              `[GmailEmailFetcherProcessor] Error processing thread ${thread.id} for account ${accountEmail}:`,
              err
            );
          }
        }
        // 4. Update last_history_id if we got a new one (incremental sync)
        if (latestHistoryId) {
          await pool.query(
            `UPDATE gmail_accounts SET last_history_id = $1, updated_at = NOW() WHERE email = $2`,
            [latestHistoryId, accountEmail]
          );
        } else if (!lastHistoryId) {
          // If this was a full sync, fetch the current historyId and update
          const gmail = await this.getGmailClientForUser(accountEmail);
          const profile = await gmail.users.getProfile({ userId: "me" });
          if (profile.data && profile.data.historyId) {
            await pool.query(
              `UPDATE gmail_accounts SET last_history_id = $1, updated_at = NOW() WHERE email = $2`,
              [profile.data.historyId, accountEmail]
            );
            console.log(
              `[GmailEmailFetcherProcessor] Set initial last_history_id for ${accountEmail} to ${profile.data.historyId}`
            );
          }
        }
      } catch (err) {
        console.error(
          `[GmailEmailFetcherProcessor] Error processing account ${accountEmail}:`,
          err
        );
      }
    }
  }
}
