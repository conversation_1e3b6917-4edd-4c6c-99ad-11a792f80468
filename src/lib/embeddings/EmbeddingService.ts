import { GoogleGenerativeAI } from "@google/generative-ai";

// Initialize Gemini client
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "");

if (!process.env.GEMINI_API_KEY) {
  console.warn("GEMINI_API_KEY not found in environment variables");
}

export interface DealEmbeddingData {
  deal_name?: string | null;
  sponsor_name?: string | null;
  property_type?: string | null;
  city?: string | null;
  state?: string | null;
  region?: string | null;
  deal_summary?: string | null;
  description?: string | null;
}

/**
 * Generate a text representation of deal data for embedding
 */
export function dealToText(dealData: DealEmbeddingData): string {
  const parts: string[] = [];

  // Core identifying information
  if (dealData.deal_name) {
    parts.push(`Deal: ${dealData.deal_name}`);
  }

  if (dealData.sponsor_name) {
    parts.push(`Sponsor: ${dealData.sponsor_name}`);
  }

  if (dealData.property_type) {
    parts.push(`Property Type: ${dealData.property_type}`);
  }

  // Location information
  const locationParts: string[] = [];
  if (dealData.city) locationParts.push(dealData.city);
  if (dealData.state) locationParts.push(dealData.state);
  if (dealData.region) locationParts.push(dealData.region);

  if (locationParts.length > 0) {
    parts.push(`Location: ${locationParts.join(", ")}`);
  }

  // Additional descriptive information
  if (dealData.deal_summary) {
    parts.push(`Summary: ${dealData.deal_summary}`);
  }

  if (dealData.description) {
    parts.push(`Description: ${dealData.description}`);
  }

  return parts.join(" | ");
}

/**
 * Generate vector embedding for deal data using Gemini text-embedding-004
 */
export async function generateDealEmbedding(
  dealData: DealEmbeddingData
): Promise<number[]> {
  const text = dealToText(dealData);

  if (!text.trim()) {
    throw new Error("No meaningful text found to generate embedding");
  }

  console.log(`🔤 Generating Gemini embedding for text: "${text}"`);

  try {
    const model = genAI.getGenerativeModel({ model: "text-embedding-004" });
    const result = await model.embedContent(text);

    const embedding = result.embedding.values;
    if (!embedding || embedding.length === 0) {
      throw new Error("Empty embedding received from Gemini");
    }

    console.log(
      `✅ Generated Gemini embedding with ${embedding.length} dimensions`
    );

    return embedding;
  } catch (error) {
    console.error("❌ Error generating Gemini embedding:", error);
    throw new Error(`Failed to generate Gemini embedding: ${error}`);
  }
}

/**
 * Generate embeddings for multiple deals in batch using Gemini
 * Note: Gemini API doesn't support batch embedding, so we process individually
 */
export async function generateBatchEmbeddings(
  dealsData: DealEmbeddingData[]
): Promise<number[][]> {
  const texts = dealsData.map(dealToText).filter((text) => text.trim());

  if (texts.length === 0) {
    return [];
  }

  console.log(
    `🔤 Generating batch Gemini embeddings for ${texts.length} deals`
  );

  try {
    const embeddings: number[][] = [];
    const model = genAI.getGenerativeModel({ model: "text-embedding-004" });

    for (let i = 0; i < texts.length; i++) {
      const text = texts[i];
      console.log(
        `   Processing ${i + 1}/${texts.length}: "${text.substring(0, 100)}..."`
      );

      const result = await model.embedContent(text);
      const embedding = result.embedding.values;

      if (!embedding || embedding.length === 0) {
        throw new Error(`Empty embedding received for text ${i + 1}`);
      }

      embeddings.push(embedding);

      // Small delay to avoid rate limiting
      if (i < texts.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }

    console.log(
      `✅ Generated ${embeddings.length} Gemini embeddings with ${
        embeddings[0]?.length || 0
      } dimensions each`
    );

    return embeddings;
  } catch (error) {
    console.error("❌ Error generating batch Gemini embeddings:", error);
    throw new Error(`Failed to generate batch Gemini embeddings: ${error}`);
  }
}

/**
 * Calculate cosine similarity between two vectors
 */
export function cosineSimilarity(a: number[], b: number[]): number {
  if (a.length !== b.length) {
    throw new Error("Vectors must have the same length");
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  for (let i = 0; i < a.length; i++) {
    dotProduct += a[i] * b[i];
    normA += a[i] * a[i];
    normB += b[i] * b[i];
  }

  normA = Math.sqrt(normA);
  normB = Math.sqrt(normB);

  if (normA === 0 || normB === 0) {
    return 0;
  }

  return dotProduct / (normA * normB);
}

/**
 * Convert similarity score to percentage
 */
export function similarityToPercentage(similarity: number): number {
  return Math.round(similarity * 100);
}
