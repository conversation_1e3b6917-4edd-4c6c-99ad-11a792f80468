export interface ProcessorLimitConfig {
  // Default limits for each processor type
  email_validator: {
    defaultLimit: number
    maxLimit: number
    batchSize: number
  }
  company_web_crawler: {
    defaultLimit: number
    maxLimit: number
    batchSize: number
  }

  company_overview_v2: {
    defaultLimit: number
    maxLimit: number
    batchSize: number
  }
  company_investment_criteria: {
    defaultLimit: number
    maxLimit: number
    batchSize: number
  }

  contact_enrichment_v2: {
    defaultLimit: number
    maxLimit: number
    batchSize: number
  }
  contact_investment_criteria: {
    defaultLimit: number
    maxLimit: number
    batchSize: number
  }
  email_generation: {
    defaultLimit: number
    maxLimit: number
    batchSize: number
  }
  news_html_fetcher: {
    defaultLimit: number
    maxLimit: number
    batchSize: number
  }
  news_enrichment: {
    defaultLimit: number
    maxLimit: number
    batchSize: number
  }
}

// Default configuration values
const defaultProcessorLimits: ProcessorLimitConfig = {
  email_validator: {
    defaultLimit: 1000,
    maxLimit: 10000,
    batchSize: 100
  },
  company_web_crawler: {
    defaultLimit: 500,
    maxLimit: 10000,
    batchSize: 100
  },

  company_overview_v2: {
    defaultLimit: 500,
    maxLimit: 10000,
    batchSize: 100
  },
  company_investment_criteria: {
    defaultLimit: 500,
    maxLimit: 10000,
    batchSize: 100
  },

  contact_enrichment_v2: {
    defaultLimit: 100,
    maxLimit: 1000,
    batchSize: 20
  },
  contact_investment_criteria: {
    defaultLimit: 500,
    maxLimit: 10000,
    batchSize: 100
  },
  email_generation: {
    defaultLimit: 500,
    maxLimit: 10000,
    batchSize: 100
  },
  news_html_fetcher: {
    defaultLimit: 500,
    maxLimit: 10000,
    batchSize: 100
  },
  news_enrichment: {
    defaultLimit: 500,
    maxLimit: 10000,
    batchSize: 100
  }
}

// Environment variable mapping
const envVarMapping = {
  email_validator: {
    defaultLimit: 'EMAIL_VALIDATOR_DEFAULT_LIMIT',
    maxLimit: 'EMAIL_VALIDATOR_MAX_LIMIT',
    batchSize: 'EMAIL_VALIDATOR_BATCH_SIZE'
  },
  company_web_crawler: {
    defaultLimit: 'COMPANY_WEB_CRAWLER_DEFAULT_LIMIT',
    maxLimit: 'COMPANY_WEB_CRAWLER_MAX_LIMIT',
    batchSize: 'COMPANY_WEB_CRAWLER_BATCH_SIZE'
  },

  company_overview_v2: {
    defaultLimit: 'COMPANY_OVERVIEW_V2_DEFAULT_LIMIT',
    maxLimit: 'COMPANY_OVERVIEW_V2_MAX_LIMIT',
    batchSize: 'COMPANY_OVERVIEW_V2_BATCH_SIZE'
  },
  company_investment_criteria: {
    defaultLimit: 'COMPANY_INVESTMENT_CRITERIA_DEFAULT_LIMIT',
    maxLimit: 'COMPANY_INVESTMENT_CRITERIA_MAX_LIMIT',
    batchSize: 'COMPANY_INVESTMENT_CRITERIA_BATCH_SIZE'
  },

  contact_enrichment_v2: {
    defaultLimit: 'CONTACT_ENRICHMENT_V2_DEFAULT_LIMIT',
    maxLimit: 'CONTACT_ENRICHMENT_V2_MAX_LIMIT',
    batchSize: 'CONTACT_ENRICHMENT_V2_BATCH_SIZE'
  },
  contact_investment_criteria: {
    defaultLimit: 'CONTACT_INVESTMENT_CRITERIA_DEFAULT_LIMIT',
    maxLimit: 'CONTACT_INVESTMENT_CRITERIA_MAX_LIMIT',
    batchSize: 'CONTACT_INVESTMENT_CRITERIA_BATCH_SIZE'
  },
  email_generation: {
    defaultLimit: 'EMAIL_GENERATION_DEFAULT_LIMIT',
    maxLimit: 'EMAIL_GENERATION_MAX_LIMIT',
    batchSize: 'EMAIL_GENERATION_BATCH_SIZE'
  },
  news_html_fetcher: {
    defaultLimit: 'NEWS_HTML_FETCHER_DEFAULT_LIMIT',
    maxLimit: 'NEWS_HTML_FETCHER_MAX_LIMIT',
    batchSize: 'NEWS_HTML_FETCHER_BATCH_SIZE'
  },
  news_enrichment: {
    defaultLimit: 'NEWS_ENRICHMENT_DEFAULT_LIMIT',
    maxLimit: 'NEWS_ENRICHMENT_MAX_LIMIT',
    batchSize: 'NEWS_ENRICHMENT_BATCH_SIZE'
  }
}

/**
 * Load processor limits from environment variables with fallback to defaults
 */
function loadProcessorLimits(): ProcessorLimitConfig {
  const config: ProcessorLimitConfig = {} as ProcessorLimitConfig

  for (const [processorName, processorConfig] of Object.entries(defaultProcessorLimits)) {
    const envVars = envVarMapping[processorName as keyof typeof envVarMapping]
    
    config[processorName as keyof ProcessorLimitConfig] = {
      defaultLimit: parseInt(process.env[envVars.defaultLimit] || '') || processorConfig.defaultLimit,
      maxLimit: parseInt(process.env[envVars.maxLimit] || '') || processorConfig.maxLimit,
      batchSize: parseInt(process.env[envVars.batchSize] || '') || processorConfig.batchSize
    }
  }

  return config
}

/**
 * Get processor limits configuration
 */
export function getProcessorLimits(): ProcessorLimitConfig {
  return loadProcessorLimits()
}

/**
 * Get limit configuration for a specific processor
 */
export function getProcessorLimit(processorName: keyof ProcessorLimitConfig) {
  const config = getProcessorLimits()
  return config[processorName]
}

/**
 * Validate and sanitize a limit value for a processor
 */
export function validateProcessorLimit(
  processorName: keyof ProcessorLimitConfig,
  requestedLimit: number
): number {
  const config = getProcessorLimit(processorName)
  
  if (requestedLimit <= 0) {
    return config.defaultLimit
  }
  
  if (requestedLimit > config.maxLimit) {
    console.warn(`[ProcessorLimits] Requested limit ${requestedLimit} for ${processorName} exceeds max limit ${config.maxLimit}. Using max limit.`)
    return config.maxLimit
  }
  
  return requestedLimit
}

/**
 * Get batch size for a processor
 */
export function getProcessorBatchSize(processorName: keyof ProcessorLimitConfig): number {
  const config = getProcessorLimit(processorName)
  return config.batchSize
}

// Export the default configuration for reference
export { defaultProcessorLimits, envVarMapping }
