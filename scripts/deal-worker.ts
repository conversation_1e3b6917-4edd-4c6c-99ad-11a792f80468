import { BullMQManager } from "../src/lib/queue/BullMQManager";

(async () => {
  try {
    // Initialize BullMQManager
    const bullManager = BullMQManager.getInstance();
    bullManager.startDealWorker();
    console.log("Deal-processing worker started.");
    // Keep process alive
    process.stdin.resume();
  } catch (err) {
    console.error("Failed to start deal-processing worker:", err);
    process.exit(1);
  }
})();
