#!/usr/bin/env ts-node
import fs from 'fs';
import path from 'path';
import { parse } from 'csv-parse/sync';
import { Pool } from 'pg';

// ---- CONFIGURATION ----
const headerMappings = {
  contacts: {
    email: ["Email", "Personal Email"],
    linkedin_url: ["Linked-In Profile", "LinkedIn Link"],
    full_name: ["Full Name"],
    first_name: ["First Name"],
    last_name: ["Last Name"],
    company_name: ["Company", "Company Name"],
  },
  companies: {
    company_website: ["Company Website", "Company Website Full"],
    company_linkedin: ["Company LinkedIn Link"],
    company_name: ["Company", "Company Name"],
  },
};

const DB_USER = "anax_user"
const DB_PASSWORD = "anax_password"
const DB_HOST = "localhost"
const DB_PORT = 5432
const DB_DATABASE = "anax"
// ---- DB CONFIGURATION ----
const pool = new Pool({ 
  user: DB_USER,
  password: DB_PASSWORD,
  host: DB_HOST,
  port: DB_PORT,
  database: DB_DATABASE,
});

function getField(row: any, keys: string[]): string | undefined {
  for (const key of keys) {
    if (row[key] && row[key].toString().trim() !== '') return row[key].toString().trim();
  }
  return undefined;
}

function normalize(str: string | undefined): string | undefined {
  return str ? str.trim().toLowerCase() : undefined;
}

// Extract domain from URL
function extractDomain(url: string | undefined): string | undefined {
  if (!url) return undefined;
  
  try {
    // Add protocol if missing
    if (!url.match(/^https?:\/\//i)) {
      url = 'http://' + url;
    }
    
    const domain = new URL(url).hostname;
    return domain.replace(/^www\./, '').toLowerCase();
  } catch (e) {
    // If URL parsing fails, try basic extraction
    const match = url.match(/(?:https?:\/\/)?(?:www\.)?([^\/]+)/i);
    return match ? match[1].toLowerCase() : url.toLowerCase();
  }
}

// Normalize company name
function normalizeCompanyName(name: string | undefined): string | undefined {
  if (!name) return undefined;
  
  return name
    .toLowerCase()
    .replace(/\binc\.?\b|\bllc\.?\b|\bcorp\.?\b|\bcorporation\b|\bltd\.?\b|\blimited\b|\bgroup\b/g, '')
    .replace(/[^\w\s]/g, '')
    .replace(/\s+/g, ' ')
    .trim();
}

// Generate company key with multiple strategies
function generateCompanyKey(website: string | undefined, linkedin: string | undefined, name: string | undefined): string | undefined {
  // Try domain first (most reliable)
  if (website) {
    const domain = extractDomain(website);
    if (domain) return `domain:${domain}`;
  }
  
  // Try LinkedIn next
  if (linkedin) {
    const linkedinDomain = extractDomain(linkedin);
    if (linkedinDomain) return `linkedin:${linkedinDomain}`;
    
    // Try to extract company name from LinkedIn URL
    const match = linkedin.match(/linkedin\.com\/company\/([^\/]+)/i);
    if (match) return `linkedin:${match[1].toLowerCase()}`;
  }
  
  // Fall back to normalized company name
  if (name) {
    const normalizedName = normalizeCompanyName(name);
    if (normalizedName && normalizedName.length > 1) return `name:${normalizedName}`;
  }
  
  // If all else fails, return the first non-empty value
  return website || linkedin || name;
}

async function main() {
  const folder = 'data/LeadData/';

  // 1. Read all CSVs
  const files = fs.readdirSync(folder).filter(f => f.endsWith('.csv'));

  // Prepare for global stats
  const allContacts: Set<string> = new Set();
  const allCompanies: Set<string> = new Set();
  
  // Store per-file contacts and companies for CSV-to-CSV matching
  const fileContactsMap: Map<string, Set<string>> = new Map();
  const fileCompaniesMap: Map<string, Set<string>> = new Map();

  // 2. Fetch all existing contacts/companies from DB
  const client = await pool.connect();
  let dbContacts: Set<string> = new Set();
  let dbCompanies: Set<string> = new Set();
  try {
    // Contacts
    const contactRes = await client.query('SELECT LOWER(email) as email, LOWER(linkedin_url) as linkedin_url, LOWER(full_name) as full_name, LOWER(company_name) as company_name, LOWER(first_name) as first_name, LOWER(last_name) as last_name FROM contacts LEFT JOIN companies ON contacts.company_id = companies.company_id');
    for (const row of contactRes.rows) {
      const email = row.email;
      const linkedin_url = row.linkedin_url;
      const full_name = row.full_name;
      const company_name = row.company_name;
      const first_name = row.first_name;
      const last_name = row.last_name;
      const contactKey = email || linkedin_url || (full_name && company_name ? `${full_name}|${company_name}` : undefined) || ((first_name && last_name && company_name) ? `${first_name} ${last_name}|${company_name}` : undefined);
      if (contactKey) dbContacts.add(contactKey);
    }
    
    // Companies - improved matching
    const companyRes = await client.query('SELECT company_website, company_linkedin, company_name FROM companies');
    for (const row of companyRes.rows) {
      // Generate company key using the improved method
      const companyKey = generateCompanyKey(row.company_website, row.company_linkedin, row.company_name);
      if (companyKey) dbCompanies.add(companyKey);
    }
  } finally {
    client.release();
  }

  // 3. Per-file stats
  const fileStats: any[] = [];

  for (const file of files) {
    const filePath = path.join(folder, file);
    const content = fs.readFileSync(filePath, 'utf8');
    const records = parse(content, { columns: true, skip_empty_lines: true });
    const fileContacts: Set<string> = new Set();
    const fileCompanies: Set<string> = new Set();
    
    for (const row of records) {
      // Contacts
      const email = normalize(getField(row, headerMappings.contacts.email));
      const linkedin_url = normalize(getField(row, headerMappings.contacts.linkedin_url));
      const full_name = normalize(getField(row, headerMappings.contacts.full_name));
      const first_name = normalize(getField(row, headerMappings.contacts.first_name));
      const last_name = normalize(getField(row, headerMappings.contacts.last_name));
      const company_name = normalize(getField(row, headerMappings.contacts.company_name));
      const contactKey = email || linkedin_url || (full_name && company_name ? `${full_name}|${company_name}` : undefined) || ((first_name && last_name && company_name) ? `${first_name} ${last_name}|${company_name}` : undefined);
      
      if (contactKey) {
        fileContacts.add(contactKey);
        allContacts.add(contactKey);
      }
      
      // Companies - improved matching
      const company_website = normalize(getField(row, headerMappings.companies.company_website));
      const company_linkedin = normalize(getField(row, headerMappings.companies.company_linkedin));
      const company_name2 = normalize(getField(row, headerMappings.companies.company_name));
      
      // Generate company key using the improved method
      const companyKey = generateCompanyKey(company_website, company_linkedin, company_name2);
      
      if (companyKey) {
        fileCompanies.add(companyKey);
        allCompanies.add(companyKey);
      }
    }
    
    // Store for CSV-to-CSV matching
    fileContactsMap.set(file, fileContacts);
    fileCompaniesMap.set(file, fileCompanies);
    
    // Compare per-file
    let contactsNew = 0, contactsMatching = 0;
    for (const key of fileContacts) {
      if (dbContacts.has(key)) contactsMatching++;
      else contactsNew++;
    }
    
    let companiesNew = 0, companiesMatching = 0;
    for (const key of fileCompanies) {
      if (dbCompanies.has(key)) companiesMatching++;
      else companiesNew++;
    }
    
    fileStats.push({
      file,
      contacts: {
        total_unique_in_csv: fileContacts.size,
        new: contactsNew,
        matching: contactsMatching,
      },
      companies: {
        total_unique_in_csv: fileCompanies.size,
        new: companiesNew,
        matching: companiesMatching,
      }
    });
  }

  // 4. CSV-to-CSV matching
  const csvToCSVMatching: any[] = [];
  
  for (let i = 0; i < files.length; i++) {
    for (let j = i + 1; j < files.length; j++) {
      const file1 = files[i];
      const file2 = files[j];
      
      const file1Contacts = fileContactsMap.get(file1) || new Set();
      const file2Contacts = fileContactsMap.get(file2) || new Set();
      const file1Companies = fileCompaniesMap.get(file1) || new Set();
      const file2Companies = fileCompaniesMap.get(file2) || new Set();
      
      // Count overlaps
      let contactOverlap = 0;
      for (const key of file1Contacts) {
        if (file2Contacts.has(key)) contactOverlap++;
      }
      
      let companyOverlap = 0;
      for (const key of file1Companies) {
        if (file2Companies.has(key)) companyOverlap++;
      }
      
      csvToCSVMatching.push({
        file1,
        file2,
        contacts_overlap: contactOverlap,
        contacts_file1_unique: file1Contacts.size,
        contacts_file2_unique: file2Contacts.size,
        contacts_overlap_percentage: Math.round((contactOverlap / Math.min(file1Contacts.size, file2Contacts.size)) * 100),
        companies_overlap: companyOverlap,
        companies_file1_unique: file1Companies.size,
        companies_file2_unique: file2Companies.size,
        companies_overlap_percentage: Math.round((companyOverlap / Math.min(file1Companies.size, file2Companies.size)) * 100)
      });
    }
  }

  // 5. Global stats
  let contactsNew = 0, contactsMatching = 0;
  for (const key of allContacts) {
    if (dbContacts.has(key)) contactsMatching++;
    else contactsNew++;
  }
  
  let companiesNew = 0, companiesMatching = 0;
  for (const key of allCompanies) {
    if (dbCompanies.has(key)) companiesMatching++;
    else companiesNew++;
  }

  const result = {
    files: fileStats,
    csv_to_csv_matching: csvToCSVMatching,
    total: {
      contacts: {
        total_unique_in_csv: allContacts.size,
        new: contactsNew,
        matching: contactsMatching,
      },
      companies: {
        total_unique_in_csv: allCompanies.size,
        new: companiesNew,
        matching: companiesMatching,
      },
    }
  };
  console.log(JSON.stringify(result, null, 2));
  process.exit(0);
}

main().catch(err => {
  console.error(err);
  process.exit(1);
}); 