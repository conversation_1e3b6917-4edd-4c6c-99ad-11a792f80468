#!/usr/bin/env node

/**
 * Test script for V2 Filter APIs
 * 
 * This script tests all the V2 filter endpoints to ensure they return the expected data structure
 * that the frontend components are expecting.
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  gray: '\x1b[90m'
};

function log(color, ...args) {
  console.log(color + args.join(' ') + colors.reset);
}

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: data, error: e.message });
        }
      });
    });
    
    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function testEndpoint(name, url, expectedFields = []) {
  log(colors.blue, `\n🧪 Testing ${name}:`);
  log(colors.gray, `   URL: ${url}`);
  
  try {
    const result = await makeRequest(url);
    
    if (result.status !== 200) {
      log(colors.red, `   ❌ FAILED: HTTP ${result.status}`);
      console.log('   Response:', result.data);
      return false;
    }
    
    if (result.error) {
      log(colors.red, `   ❌ FAILED: JSON Parse Error: ${result.error}`);
      return false;
    }
    
    const data = result.data;
    
    // Check if it has the expected structure
    if (expectedFields.length > 0) {
      const missingFields = expectedFields.filter(field => !(field in data));
      if (missingFields.length > 0) {
        log(colors.yellow, `   ⚠️  Missing fields: ${missingFields.join(', ')}`);
      }
      
      const presentFields = expectedFields.filter(field => field in data);
      log(colors.green, `   ✅ Present fields (${presentFields.length}): ${presentFields.join(', ')}`);
      
      // Check if arrays have data
      for (const field of presentFields) {
        if (Array.isArray(data[field])) {
          log(colors.cyan, `      📊 ${field}: ${data[field].length} items`);
          if (data[field].length > 0) {
            const sample = data[field][0];
            if (sample.value && sample.label) {
              log(colors.gray, `         Sample: ${sample.value} (${sample.label})`);
            }
          }
        } else if (typeof data[field] === 'object') {
          log(colors.cyan, `      📊 ${field}: object with ${Object.keys(data[field]).length} keys`);
        }
      }
    } else {
      log(colors.green, `   ✅ SUCCESS: Response received`);
      log(colors.cyan, `      📊 Fields: ${Object.keys(data).join(', ')}`);
    }
    
    return true;
    
  } catch (error) {
    log(colors.red, `   ❌ FAILED: ${error.message}`);
    return false;
  }
}

async function runTests() {
  log(colors.cyan, '🚀 Starting V2 Filter API Tests\n');
  log(colors.gray, `Base URL: ${BASE_URL}`);
  
  const tests = [
    // Company V2 Filter APIs
    {
      name: 'Company Core Filters',
      url: `${BASE_URL}/api/companies/filter-options-v2?type=core`,
      expectedFields: [
        'companyAddresses', 'companyCities', 'companyStates', 'companyWebsites', 
        'industries', 'companyCountries', 'sources', 'websiteScrapingStatuses',
        'companyOverviewStatuses', 'overviewV2Statuses'
      ]
    },
    {
      name: 'Company Overview V2 Filters',
      url: `${BASE_URL}/api/companies/filter-options-v2?type=overview_v2`,
      expectedFields: [
        'companyTypes', 'businessModels', 'investmentFocus', 'investmentStrategyMissions',
        'investmentStrategyApproaches', 'headquartersAddresses', 'headquartersCities',
        'headquartersStates', 'headquartersCountries', 'officeLocations', 'balanceSheetStrengths',
        'fundingSources', 'fundraisingStatuses', 'lenderTypes', 'partnerships',
        'keyEquityPartners', 'keyDebtPartners', 'keyExecutives'
      ]
    },
    
    // Contact V2 Filter APIs
    {
      name: 'Contact Enrichment V2 Filters',
      url: `${BASE_URL}/api/contacts/filter-options-v2?type=enrichment_v2`,
      expectedFields: [
        'contactTypes', 'relationshipOwners', 'rolesInDecisionMaking', 'sourcesOfIntroduction',
        'educationColleges', 'educationHighSchools', 'ageRanges'
      ]
    },
    {
      name: 'Contact Company Status Filters',
      url: `${BASE_URL}/api/contacts/filter-options-v2?type=company_statuses`,
      expectedFields: ['websiteScrapingStatuses', 'companyOverviewStatuses', 'companyOverviewV2Statuses']
    },
    
    // Supporting APIs
    {
      name: 'Contact Sources',
      url: `${BASE_URL}/api/contacts/sources`,
      expectedFields: []
    },
    {
      name: 'Contact Job Tiers',
      url: `${BASE_URL}/api/contacts/job-tiers`,
      expectedFields: []
    },
    {
      name: 'Investment Criteria (Company)',
      url: `${BASE_URL}/api/investment-criteria/filter-options?entityType=Company`,
      expectedFields: [
        'capitalPositions', 'propertyTypes', 'propertySubcategories', 'strategies',
        'loanTypes', 'structuredLoanTranches', 'loanPrograms', 'recourseLoans',
        'countries', 'regions', 'states', 'cities'
      ]
    },
    {
      name: 'Investment Criteria (Contact)', 
      url: `${BASE_URL}/api/investment-criteria/filter-options?entityType=Contact`,
      expectedFields: [
        'capitalPositions', 'propertyTypes', 'propertySubcategories', 'strategies',
        'loanTypes', 'structuredLoanTranches', 'loanPrograms', 'recourseLoans',
        'countries', 'regions', 'states', 'cities'
      ]
    },
    {
      name: 'Investment Criteria Central (All)',
      url: `${BASE_URL}/api/investment-criteria/central-filter-options?type=all`,
      expectedFields: []
    }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    const result = await testEndpoint(test.name, test.url, test.expectedFields);
    if (result) {
      passed++;
    } else {
      failed++;
    }
  }
  
  // Summary
  log(colors.cyan, '\n📊 Test Summary:');
  log(colors.green, `   ✅ Passed: ${passed}`);
  log(colors.red, `   ❌ Failed: ${failed}`);
  log(colors.blue, `   📝 Total:  ${passed + failed}`);
  
  if (failed === 0) {
    log(colors.green, '\n🎉 All V2 Filter APIs are working correctly!');
    log(colors.gray, '   The UI should now have full access to all filter options.');
  } else {
    log(colors.yellow, '\n⚠️  Some APIs need attention. Check the failed tests above.');
  }
  
  process.exit(failed === 0 ? 0 : 1);
}

if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testEndpoint, runTests };
