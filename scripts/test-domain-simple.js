#!/usr/bin/env node

/**
 * Domain Analysis Script - Analyst Style Aggregation
 * Run with: node scripts/test-domain-simple.js
 */

const { Pool } = require('pg');
require('dotenv').config();

// Database connection
const pool = new Pool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || "5432"),
  database: process.env.DB_DATABASE,
});

// Domain extraction function (simplified version)
function extractDomain(url) {
  try {
    if (!url) return "";
    if (!url.startsWith("http")) {
      url = `https://${url}`;
    }
    const parsedUrl = new URL(url);
    const hostname = parsedUrl.hostname;

    // Fallback heuristic: handle common multi-level TLD tokens
    const parts = hostname.split('.');
    if (parts.length < 2) return hostname;

    const secondLevelTokens = new Set([
      'ac','co','com','net','org','gov','edu','mil','int','nom','ne','or','id','me','sch','plc','ltd',
      'us','uk','de','in','br','au','ca','jp','fr','it','es','ru','mx','nl','se','ch','no','fi','dk',
      'ie','nz','za','pl','tr','ar','at','be','hk','kr','tw','pt','gr','cz','hu','sg','il','my','ro',
      'ua','th','sk','bg','si','lt','lv','ee','vn'
    ]);

    if (secondLevelTokens.has(parts[parts.length - 2])) {
      return parts.slice(-3).join('.');
    }
    return parts.slice(-2).join('.');
  } catch (error) {
    return "";
  }
}

async function fetchAllCompanyWebsites() {
  try {
    const query = `
      SELECT 
        company_id,
        company_name,
        company_website as website
      FROM companies 
      WHERE company_website IS NOT NULL 
        AND company_website != '' 
        AND company_website != 'N/A'
        AND company_website != 'n/a'
      ORDER BY company_id
    `;
    
    const result = await pool.query(query);
    
    // Process and extract domains
    const companies = result.rows.map(row => ({
      company_id: row.company_id,
      company_name: row.company_name,
      website: row.website,
      extracted_domain: extractDomain(row.website)
    })).filter(company => company.extracted_domain); // Filter out invalid domains
    
    return companies;
  } catch (error) {
    console.error('Error fetching company websites:', error);
    throw error;
  }
}

function analyzeDomainData(companies) {
  console.log('\n' + '='.repeat(80));
  console.log('🔍 DOMAIN ANALYSIS REPORT');
  console.log('='.repeat(80));

  // 1. Basic Statistics
  console.log('\n📊 BASIC STATISTICS');
  console.log('-'.repeat(40));
  console.log(`Total Companies with Websites: ${companies.length.toLocaleString()}`);
  
  const uniqueDomains = new Set(companies.map(c => c.extracted_domain));
  console.log(`Unique Domains: ${uniqueDomains.size.toLocaleString()}`);
  
  const duplicateDomains = companies.length - uniqueDomains.size;
  console.log(`Duplicate Domains: ${duplicateDomains.toLocaleString()}`);
  console.log(`Duplicate Rate: ${((duplicateDomains / companies.length) * 100).toFixed(2)}%`);

  // 2. Domain Distribution Analysis
  console.log('\n📈 DOMAIN DISTRIBUTION ANALYSIS');
  console.log('-'.repeat(40));
  
  const domainMap = new Map();
  companies.forEach(company => {
    if (!domainMap.has(company.extracted_domain)) {
      domainMap.set(company.extracted_domain, []);
    }
    domainMap.get(company.extracted_domain).push(company);
  });

  // Count companies per domain
  const domainCounts = Array.from(domainMap.entries()).map(([domain, companies]) => ({
    domain,
    count: companies.length,
    companies
  })).sort((a, b) => b.count - a.count);

  // Distribution statistics
  const singleCompanyDomains = domainCounts.filter(d => d.count === 1).length;
  const multiCompanyDomains = domainCounts.filter(d => d.count > 1).length;
  
  console.log(`Domains with 1 company: ${singleCompanyDomains.toLocaleString()} (${((singleCompanyDomains / domainCounts.length) * 100).toFixed(1)}%)`);
  console.log(`Domains with 2+ companies: ${multiCompanyDomains.toLocaleString()} (${((multiCompanyDomains / domainCounts.length) * 100).toFixed(1)}%)`);

  // 3. Top Duplicate Domains
  console.log('\n🏆 TOP DUPLICATE DOMAINS');
  console.log('-'.repeat(40));
  
  const topDuplicates = domainCounts.filter(d => d.count > 1).slice(0, 10);
  topDuplicates.forEach((domain, index) => {
    console.log(`${index + 1}. ${domain.domain} (${domain.count} companies)`);
    domain.companies.forEach(company => {
      console.log(`   - ${company.company_name} (ID: ${company.company_id})`);
    });
    console.log('');
  });

  // 4. TLD Analysis
  console.log('\n🌐 TOP-LEVEL DOMAIN (TLD) ANALYSIS');
  console.log('-'.repeat(40));
  
  const tldCounts = new Map();
  companies.forEach(company => {
    const tld = company.extracted_domain.split('.').pop();
    tldCounts.set(tld, (tldCounts.get(tld) || 0) + 1);
  });

  const topTlds = Array.from(tldCounts.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10);

  topTlds.forEach(([tld, count], index) => {
    const percentage = ((count / companies.length) * 100).toFixed(1);
    console.log(`${index + 1}. .${tld}: ${count.toLocaleString()} (${percentage}%)`);
  });

  // 5. Data Quality Analysis
  console.log('\n🔧 DATA QUALITY ANALYSIS');
  console.log('-'.repeat(40));
  
  const invalidDomains = companies.filter(c => !c.extracted_domain || c.extracted_domain === '');
  const validDomains = companies.filter(c => c.extracted_domain && c.extracted_domain !== '');
  
  console.log(`Valid Domains: ${validDomains.length.toLocaleString()} (${((validDomains.length / companies.length) * 100).toFixed(1)}%)`);
  console.log(`Invalid Domains: ${invalidDomains.length.toLocaleString()} (${((invalidDomains.length / companies.length) * 100).toFixed(1)}%)`);

  // 6. Potential Merge Candidates
  console.log('\n🔗 POTENTIAL MERGE CANDIDATES');
  console.log('-'.repeat(40));
  
  const mergeCandidates = domainCounts.filter(d => d.count > 1);
  console.log(`Total merge candidate groups: ${mergeCandidates.length}`);
  console.log(`Total companies in merge candidates: ${mergeCandidates.reduce((sum, d) => sum + d.count, 0)}`);
  
  // Show some examples
  mergeCandidates.slice(0, 5).forEach((domain, index) => {
    console.log(`\nGroup ${index + 1}: ${domain.domain}`);
    domain.companies.forEach(company => {
      console.log(`  - ${company.company_name} (ID: ${company.company_id})`);
    });
  });

  // 7. Summary Recommendations
  console.log('\n💡 SUMMARY & RECOMMENDATIONS');
  console.log('-'.repeat(40));
  
  console.log(`• Data Quality: ${((validDomains.length / companies.length) * 100).toFixed(1)}% of domains are valid`);
  console.log(`• Duplication Rate: ${((duplicateDomains / companies.length) * 100).toFixed(2)}% of companies share domains`);
  console.log(`• Merge Opportunities: ${mergeCandidates.length} domain groups could be merged`);
  console.log(`• Most Common TLD: .${topTlds[0]?.[0]} (${topTlds[0]?.[1]} companies)`);
  
  if (mergeCandidates.length > 0) {
    console.log(`\n🚨 IMMEDIATE ACTIONS NEEDED:`);
    console.log(`• Review ${mergeCandidates.length} duplicate domain groups`);
    console.log(`• Consider merging ${mergeCandidates.reduce((sum, d) => sum + d.count, 0)} companies`);
    console.log(`• Focus on domains with highest company counts first`);
  }

  return {
    totalCompanies: companies.length,
    uniqueDomains: uniqueDomains.size,
    duplicateDomains: duplicateDomains,
    duplicateRate: (duplicateDomains / companies.length) * 100,
    mergeCandidates: mergeCandidates.length,
    topDuplicates: topDuplicates,
    topTlds: topTlds,
    domainCounts: domainCounts
  };
}

async function main() {
  console.log('🚀 Starting Domain Analysis...\n');
  
  try {
    // Step 1: Fetch all website -> id, name mappings
    console.log('📥 Fetching company websites...');
    const companies = await fetchAllCompanyWebsites();
    console.log(`✅ Fetched ${companies.length.toLocaleString()} companies with websites\n`);

    // Step 2: Convert websites to domains
    console.log('🔄 Converting websites to domains...');
    const companiesWithDomains = companies.map(company => ({
      ...company,
      extracted_domain: extractDomain(company.website)
    }));
    console.log(`✅ Extracted domains for ${companiesWithDomains.length.toLocaleString()} companies\n`);

    // Step 3: Provide analyst-style aggregation
    const analysis = analyzeDomainData(companiesWithDomains);
    
    console.log('\n' + '='.repeat(80));
    console.log('✅ ANALYSIS COMPLETE');
    console.log('='.repeat(80));
    
  } catch (error) {
    console.error('❌ Error in analysis:', error);
  } finally {
    await pool.end();
  }
}

// Run the analysis
main().catch(console.error);
