#!/usr/bin/env node

/**
 * <PERSON><PERSON> script for running the scraping system every 4 hours
 * 
 * Usage:
 * 1. Add to crontab: 0 */4 * * * cd /path/to/dash && npx tsx scripts/cron-scraper.ts
 * 2. Or run manually: npx tsx scripts/cron-scraper.ts
 */

import { ScrapingProcessor, NewsFetchingProcessor } from '../src/lib/scrapers/ScrapingProcessor';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface CronRunResult {
  success: boolean;
  scrapingResult?: any;
  fetchingResult?: any;
  error?: string;
  startTime: Date;
  endTime: Date;
  duration: number;
}

async function logMessage(level: string, message: string): Promise<void> {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [CRON-SCRAPER] [${level.toUpperCase()}] ${message}`;
  console.log(logMessage);
  
  // Also log to file for persistent logging
  const fs = await import('fs');
  const path = await import('path');
  const logDir = path.join(__dirname, '../logs');
  const logFile = path.join(logDir, 'scraper-cron.log');
  
  try {
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    fs.appendFileSync(logFile, logMessage + '\n');
  } catch (error) {
    console.error('Failed to write to log file:', error);
  }
}

async function runScrapingCron(): Promise<CronRunResult> {
  const startTime = new Date();
  await logMessage('info', 'Starting scheduled scraping operation...');
  
  const result: CronRunResult = {
    success: false,
    startTime,
    endTime: new Date(),
    duration: 0
  };

  try {
    // Step 1: Run scraping to find new links
    await logMessage('info', 'Phase 1: Running scrapers to find new links...');
    const scrapingProcessor = new ScrapingProcessor('CronScrapingProcessor');
    const scrapingResult = await scrapingProcessor.process();
    
    result.scrapingResult = scrapingResult;
    
    await logMessage('info', `Scraping completed. Processed: ${scrapingResult.processed}, Successful: ${scrapingResult.successful}, Failed: ${scrapingResult.failed}`);
    
    if (scrapingResult.errors.length > 0) {
      await logMessage('warn', `Scraping errors: ${scrapingResult.errors.join('; ')}`);
    }
    
    // Wait a bit between phases
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Step 2: Run news fetching to download content
    await logMessage('info', 'Phase 2: Running news fetcher to download content...');
    const fetchingProcessor = new NewsFetchingProcessor('CronFetchingProcessor');
    const fetchingResult = await fetchingProcessor.process();
    
    result.fetchingResult = fetchingResult;
    
    await logMessage('info', `News fetching completed. Processed: ${fetchingResult.processed}, Successful: ${fetchingResult.successful}, Failed: ${fetchingResult.failed}`);
    
    if (fetchingResult.errors.length > 0) {
      await logMessage('warn', `Fetching errors: ${fetchingResult.errors.join('; ')}`);
    }
    
    // Determine overall success
    result.success = scrapingResult.successful > 0 || fetchingResult.successful > 0;
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    await logMessage('error', `Cron job failed: ${errorMessage}`);
    result.error = errorMessage;
    result.success = false;
  }
  
  result.endTime = new Date();
  result.duration = result.endTime.getTime() - result.startTime.getTime();
  
  await logMessage('info', `Cron job completed in ${Math.round(result.duration / 1000)}s. Success: ${result.success}`);
  
  return result;
}

async function checkDatabaseHealth(): Promise<boolean> {
  try {
    const { pool } = await import('../src/lib/db');
    const client = await pool.connect();
    
    // Test basic query
    await client.query('SELECT 1');
    
    // Check if required tables exist
    const tableCheck = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('deal_news', 'news')
    `);
    
    client.release();
    
    const foundTables = tableCheck.rows.map(row => row.table_name);
    const requiredTables = ['deal_news'];
    const missingTables = requiredTables.filter(table => !foundTables.includes(table));
    
    if (missingTables.length > 0) {
      await logMessage('error', `Missing required tables: ${missingTables.join(', ')}`);
      return false;
    }
    
    await logMessage('info', 'Database health check passed');
    return true;
  } catch (error) {
    await logMessage('error', `Database health check failed: ${error}`);
    return false;
  }
}

// --- Matching Cron Job Addition ---
import { matchInvestmentCriteria } from '../src/lib/utils';

async function runContactDealMatchingCron() {
  await logMessage('info', 'Starting contact-to-deal matching cron job...');
  const startTime = new Date();
  const { pool } = await import('../src/lib/db');
  // Fetch all active contacts with investment criteria
  const contactsRes = await pool.query(`
    SELECT DISTINCT entity_id
    FROM investment_criteria
    WHERE entity_type = 'Contact' AND is_active = true
  `);
  const contactIds = contactsRes.rows.map(r => r.entity_id);
  await logMessage('info', `Found ${contactIds.length} contacts with investment criteria.`);
  // Fetch all deals
  const dealsRes = await pool.query(`SELECT * FROM deals WHERE status != 'deleted'`);
  const deals = dealsRes.rows;
  let totalMatches = 0;
  for (const contactId of contactIds) {
    // Fetch criteria for this contact
    const critRes = await pool.query(
      `SELECT * FROM investment_criteria WHERE entity_type = 'Contact' AND entity_id = $1 AND is_active = true LIMIT 1`,
      [String(contactId)]
    );
    const criteria = critRes.rows[0];
    if (!criteria) continue;
    // Score all deals
    const results = deals.map(deal => {
      const { score, reasons } = matchInvestmentCriteria(criteria, deal);
      return {
        deal_id: deal.deal_id,
        deal_name: deal.deal_name,
        score,
        reasons,
      };
    });
    const matches = results.filter(r => r.score > 0).sort((a, b) => b.score - a.score).slice(0, 3);
    totalMatches += matches.length;
    if (matches.length > 0) {
      await logMessage('info', `Contact ${contactId} top matches:`);
      for (const m of matches) {
        await logMessage('info', `  Deal: ${m.deal_name} (Score: ${m.score}) Reasons: ${m.reasons.join(', ')}`);
      }
    } else {
      await logMessage('info', `Contact ${contactId} has no matching deals.`);
    }
  }
  const duration = (new Date().getTime() - startTime.getTime()) / 1000;
  await logMessage('info', `Matching cron job completed. Total matches: ${totalMatches}. Duration: ${duration}s.`);
}

async function main(): Promise<void> {
  await logMessage('info', '=== Scraper Cron Job Started ===');
  
  // Check if database is healthy
  const dbHealthy = await checkDatabaseHealth();
  if (!dbHealthy) {
    await logMessage('error', 'Database health check failed. Aborting cron job.');
    process.exit(1);
  }
  
  // Run the scraping operation
  const result = await runScrapingCron();
  
  // Log summary
  await logMessage('info', '=== Cron Job Summary ===');
  await logMessage('info', `Duration: ${Math.round(result.duration / 1000)}s`);
  await logMessage('info', `Success: ${result.success}`);
  
  if (result.scrapingResult) {
    await logMessage('info', `Scraping: ${result.scrapingResult.successful}/${result.scrapingResult.processed} successful`);
  }
  
  if (result.fetchingResult) {
    await logMessage('info', `Fetching: ${result.fetchingResult.successful}/${result.fetchingResult.processed} successful`);
  }
  
  if (result.error) {
    await logMessage('error', `Error: ${result.error}`);
  }
  
  await logMessage('info', '=== Scraper Cron Job Completed ===');
  
  // Exit with appropriate code
  process.exit(result.success ? 0 : 1);
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  await logMessage('info', 'Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await logMessage('info', 'Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

// Run if this file is executed directly
if (process.argv[1] === __filename) {
  main().catch(async (error) => {
    await logMessage('error', `Unhandled error in main: ${error}`);
    process.exit(1);
  });
}

export { runScrapingCron, checkDatabaseHealth }; 