import { BullMQManager } from "../src/lib/queue/BullMQManager";

(async () => {
  try {
    const bullManager = BullMQManager.getInstance();
    // Always (re-)schedule the Fireflies fetch job on worker start
    await bullManager.addFirefliesFetchJob({
      repeat: { cron: "*/5 * * * *" }, // every 5 minutes
      removeOnComplete: true,
      removeOnFail: true,
    });
    console.log("Scheduled Fireflies fetch job to run every 5 minutes.");
    bullManager.startFirefliesWorker();
    console.log("Fireflies worker started.");
    // Keep process alive
    process.stdin.resume();
  } catch (err) {
    console.error("Failed to start Fireflies worker:", err);
    process.exit(1);
  }
})(); 