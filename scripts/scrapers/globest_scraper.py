import time
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from .base_scraper import BaseScraper

class GlobestScraper(BaseScraper):
    """
    Enhanced scraper for GlobeSt website that scrapes all sector pages
    """
    
    def __init__(self, driver, conn, site_config, wait_time=0):
        super().__init__(driver, conn, site_config, wait_time)
        self.site_name = "globest"
        self.news_source = "globest"
        self.start_url = "https://www.globest.com/"
        
    def is_logged_in(self):
        """Check if user is logged into GlobeSt by looking for Register link"""
        try:
            # If Register link is present, user is NOT logged in
            register_elements = self.driver.find_elements(By.XPATH, "//a[contains(@href, 'ALMMD_GLOBEST_Reg') and contains(text(), 'Register')]")
            
            if register_elements:
                print(f"[INFO] Found Register link - user is NOT logged in to {self.site_name}")
                return False
            else:
                print(f"[INFO] No Register link found - user appears to be logged in to {self.site_name}")
                return True
        except Exception as e:
            print(f"[WARNING] Error checking login status for {self.site_name}: {e}")
            return False
    
    def login(self):
        """Handle login for GlobeSt with the Dragon Forms login form"""
        try:
            # Navigate to login page
            login_url = self.site_config.get("login_url")
            print(f"[INFO] Navigating to login page: {login_url}")
            if not self.safe_navigate(login_url):
                print(f"[ERROR] Failed to navigate to login page for {self.site_name}")
                return False
                
            print(f"[INFO] Successfully loaded login page for {self.site_name}")
            self.take_screenshot(f"{self.site_name}_login_page.png")
            
            # Get credentials
            email = self.site_config.get("email", os.environ.get(f"{self.site_name.upper()}_EMAIL", ""))
            password = self.site_config.get("password", os.environ.get(f"{self.site_name.upper()}_PASSWORD", ""))
            
            if not email or not password:
                print(f"[ERROR] No credentials provided for {self.site_name} login")
                return False
            
            # Find and fill email field - try both id and name
            try:
                # Try by ID first
                email_field_id = self.site_config.get("email_field_id")
                print(f"[INFO] Looking for email field with ID: {email_field_id}")
                email_field = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.ID, email_field_id))
                )
            except Exception as e:
                print(f"[INFO] Could not find email field by ID: {e}")
                try:
                    # Try by name
                    email_field_name = self.site_config.get("email_field_name")
                    print(f"[INFO] Looking for email field with name: {email_field_name}")
                    email_field = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.NAME, email_field_name))
                    )
                except Exception as e2:
                    print(f"[ERROR] Could not find email field: {e2}")
                    return False
            
            # Enter email
            print(f"[INFO] Entering email: {email}")
            email_field.clear()
            email_field.send_keys(email)
            
            # Find and fill password field - try both id and name
            try:
                # Try by ID first
                password_field_id = self.site_config.get("password_field_id")
                print(f"[INFO] Looking for password field with ID: {password_field_id}")
                password_field = self.driver.find_element(By.ID, password_field_id)
            except Exception as e:
                print(f"[INFO] Could not find password field by ID: {e}")
                try:
                    # Try by name
                    password_field_name = self.site_config.get("password_field_name")
                    print(f"[INFO] Looking for password field with name: {password_field_name}")
                    password_field = self.driver.find_element(By.NAME, password_field_name)
                except Exception as e2:
                    print(f"[ERROR] Could not find password field: {e2}")
                    return False
            
            # Enter password
            print(f"[INFO] Entering password")
            password_field.clear()
            password_field.send_keys(password)
            
            # Check "Remember Me" box if present
            try:
                remember_me_id = self.site_config.get("remember_me_id")
                if remember_me_id:
                    print(f"[INFO] Looking for 'Remember Me' checkbox with ID: {remember_me_id}")
                    remember_me = self.driver.find_element(By.ID, remember_me_id)
                    if not remember_me.is_selected():
                        remember_me.click()
                        print(f"[INFO] Checked 'Remember Me' option")
            except Exception as e:
                print(f"[INFO] Remember Me checkbox not found or not clickable: {e}")
            
            # Find and click the submit button
            submit_selector = self.site_config.get("submit_button_selector", "div#submitbtn input[type='submit']")
            print(f"[INFO] Looking for submit button with selector: {submit_selector}")
            
            try:
                submit_button = self.driver.find_element(By.CSS_SELECTOR, submit_selector)
                print(f"[INFO] Clicking submit button")
                submit_button.click()
                self.driver.refresh()
            except Exception as e:
                print(f"[ERROR] Could not find or click submit button: {e}")
                # Try alternative method - JavaScript click
                try:
                    print(f"[INFO] Attempting to submit form via JavaScript")
                    self.driver.execute_script("document.querySelector('div#submitbtn input[type=\"submit\"]').click();")
                except Exception as e2:
                    print(f"[ERROR] JavaScript submit failed: {e2}")
                    return False
            
            # Wait for login to complete and redirect
            print(f"[INFO] Waiting for login to complete...")   
            time.sleep(1)
            if self.is_logged_in():
                print(f"[INFO] Successfully logged in to {self.site_name}")
                return True
            else:
                print(f"[WARNING] Login attempt completed but login indicators not found for {self.site_name}")
                return False
                
        except Exception as e:
            print(f"[ERROR] Exception during {self.site_name} login: {e}")
            self.take_screenshot(f"{self.site_name}_error.png")
            return False
    
    def discover_sector_urls(self):
        """Discover all sector URLs from the main GlobeSt website"""
        print(f"[{self.site_name.upper()}] Discovering sector URLs...")
        
        # Navigate to the homepage
        if not self.safe_navigate(self.start_url):
            print(f"[ERROR] Could not access {self.site_name} website to discover sectors.")
            return []
            
        sector_urls = set()
        
        try:
            # Look for links that contain '/sectors/' in the href
            sector_links = self.driver.find_elements(By.XPATH, "//a[contains(@href, '/sectors/')]")
            
            for link in sector_links:
                href = link.get_attribute('href')
                if href and '/sectors/' in href:
                    # Normalize the URL to get base sector URL
                    base_url = href.split('?')[0].rstrip('/')
                    if base_url.endswith('/sectors') or base_url.count('/') >= 4:  # e.g., /sectors/multifamily/
                        sector_urls.add(base_url)
            
            # Convert to list and sort for consistency
            sector_urls = sorted(list(sector_urls))
            
            print(f"[{self.site_name.upper()}] Found {len(sector_urls)} sector URLs:")
            for url in sector_urls:
                print(f"  - {url}")
                
            return sector_urls
            
        except Exception as e:
            print(f"[ERROR] Error discovering sector URLs: {e}")
            return []
    
    def extract_story_links(self):
        """Extract links from article sections (handles both story sections and article lists)"""
        story_links = []
        
        try:
            # First try to extract from Trending Stories and Recent Stories sections (homepage structure)
            trending_section = self.driver.find_elements(By.CSS_SELECTOR, '[data-cy="trending-stories"]')
            if trending_section:
                print(f"[{self.site_name.upper()}] Found Trending Stories section")
                trending_links = trending_section[0].find_elements(By.TAG_NAME, "a")
                for link in trending_links:
                    href = link.get_attribute('href')
                    if href and href.startswith(('http', '/')):
                        if href.startswith('/'):
                            href = f"https://www.globest.com{href}"
                        story_links.append(href)
                        
                print(f"[{self.site_name.upper()}] Found {len(trending_links)} links in Trending Stories")
            
            # Extract from Recent Stories section  
            recent_section = self.driver.find_elements(By.CSS_SELECTOR, '[data-cy="recent-stories"]')
            if recent_section:
                print(f"[{self.site_name.upper()}] Found Recent Stories section")
                recent_links = recent_section[0].find_elements(By.TAG_NAME, "a")
                initial_count = len(story_links)
                for link in recent_links:
                    href = link.get_attribute('href')
                    if href and href.startswith(('http', '/')):
                        if href.startswith('/'):
                            href = f"https://www.globest.com{href}"
                        story_links.append(href)
                        
                print(f"[{self.site_name.upper()}] Found {len(story_links) - initial_count} links in Recent Stories")
            
            # If no trending/recent sections found, extract from article list (sector page structure)
            if not story_links:
                print(f"[{self.site_name.upper()}] No trending/recent sections found, extracting from article list...")
                
                # Extract from article list section
                article_list_section = self.driver.find_elements(By.CSS_SELECTOR, '[data-cy="article-list"]')
                if article_list_section:
                    print(f"[{self.site_name.upper()}] Found article list section")
                    
                    # Get individual articles
                    articles = article_list_section[0].find_elements(By.CSS_SELECTOR, '[data-cy="article-list-article"]')
                    print(f"[{self.site_name.upper()}] Found {len(articles)} articles in list")
                    
                    for article in articles:
                        # Look for links within each article
                        article_links = article.find_elements(By.TAG_NAME, "a")
                        for link in article_links:
                            href = link.get_attribute('href')
                            if href and href.startswith(('http', '/')):
                                if href.startswith('/'):
                                    href = f"https://www.globest.com{href}"
                                # Only add article links (contain year)
                                if '/2025/' in href or '/2024/' in href or '/2023/' in href:
                                    story_links.append(href)
                    
                    print(f"[{self.site_name.upper()}] Extracted {len(story_links)} article links from article list")
                
                # Also try to get links directly from article titles
                title_elements = self.driver.find_elements(By.CSS_SELECTOR, '[data-cy="article-list-article-title"] a')
                for title_link in title_elements:
                    href = title_link.get_attribute('href')
                    if href and href.startswith(('http', '/')):
                        if href.startswith('/'):
                            href = f"https://www.globest.com{href}"
                        if '/2025/' in href or '/2024/' in href or '/2023/' in href:
                            story_links.append(href)
                
                print(f"[{self.site_name.upper()}] Added {len(title_elements)} links from article titles")
            
            # Remove duplicates while preserving order
            seen = set()
            unique_links = []
            for link in story_links:
                if link not in seen:
                    seen.add(link)
                    unique_links.append(link)
                    
            print(f"[{self.site_name.upper()}] Total unique story links found: {len(unique_links)}")
            
            # Show sample links for debugging
            if unique_links:
                print(f"[{self.site_name.upper()}] Sample links:")
                for i, link in enumerate(unique_links[:3]):
                    print(f"  {i+1}. {link}")
            
            return unique_links
            
        except Exception as e:
            print(f"[ERROR] Error extracting story links: {e}")
            return []
    
    def click_next_button(self):
        """Click the NEXT pagination button"""
        try:
            # Look for the NEXT button with the specific structure
            next_selectors = [
                # Most specific selector based on the provided HTML
                'a.page[data-v-79b62338] span.pnEnabled:contains("NEXT")',
                # Fallback selectors
                'a.page span:contains("NEXT")',
                'a[href*="page="] span:contains("NEXT")',
                'a.page .pnEnabled',
                'a:contains("NEXT")',
                'a[class*="page"]:contains("›")',
                # XPath alternatives
                '//a[@class="page"]//span[contains(text(), "NEXT")]',
                '//a[contains(@href, "page=")]//span[contains(text(), "NEXT")]',
                '//a//span[contains(@class, "pnEnabled") and contains(text(), "NEXT")]'
            ]
            
            for i, selector in enumerate(next_selectors):
                try:
                    print(f"[{self.site_name.upper()}] Trying NEXT button selector {i+1}: {selector}")
                    
                    if selector.startswith('//'):
                        # XPath selector
                        next_button = self.driver.find_element(By.XPATH, selector)
                    else:
                        # CSS selector (note: :contains() is not valid CSS, need to handle differently)
                        if ':contains(' in selector:
                            # For CSS selectors with :contains, convert to XPath
                            if 'NEXT' in selector:
                                xpath_selector = '//a[@class="page"]//span[contains(text(), "NEXT")]'
                                next_button = self.driver.find_element(By.XPATH, xpath_selector)
                            else:
                                continue
                        else:
                            next_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    # Check if button is visible and clickable
                    if next_button.is_displayed() and next_button.is_enabled():
                        # Scroll to button
                        self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", next_button)
                        time.sleep(1)
                        
                        # Get the parent link element if we found a span
                        if next_button.tag_name.lower() == 'span':
                            parent = next_button.find_element(By.XPATH, '..')
                            if parent.tag_name.lower() == 'a':
                                next_button = parent
                        
                        print(f"[{self.site_name.upper()}] Found NEXT button, clicking...")
                        next_button.click()
                        time.sleep(3)  # Wait for page to load
                        return True
                        
                except Exception as selector_error:
                    continue
            
            # If all specific selectors fail, try a more general approach
            print(f"[{self.site_name.upper()}] Trying general pagination button search...")
            all_links = self.driver.find_elements(By.TAG_NAME, "a")
            for link in all_links:
                try:
                    link_text = link.text.strip().lower()
                    if any(text in link_text for text in ['next', '›', '>']):
                        href = link.get_attribute('href')
                        if href and 'page=' in href:
                            print(f"[{self.site_name.upper()}] Found pagination link via text search: '{link.text.strip()}'")
                            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", link)
                            time.sleep(1)
                            link.click()
                            time.sleep(3)
                            return True
                except Exception:
                    continue
            
            print(f"[{self.site_name.upper()}] No NEXT button found")
            return False
            
        except Exception as e:
            print(f"[{self.site_name.upper()}] Error clicking NEXT button: {e}")
            return False
    
    def scrape_sector_page(self, sector_url, max_pages=10):
        """Scrape a specific sector page with pagination and enhanced filtering"""
        print(f"[{self.site_name.upper()}] Scraping sector: {sector_url}")
        
        if not self.safe_navigate(sector_url):
            print(f"[ERROR] Could not navigate to sector: {sector_url}")
            return []
            
        all_links = []
        new_links_file = os.path.join(os.path.dirname(self.profile_dir), "new_links.txt")
        consecutive_no_new = 0
        
        for page_num in range(1, max_pages + 1):
            print(f"[{self.site_name.upper()}] Sector page {page_num}: Extracting story links...")
            
            # Extract story links from current page
            story_links = self.extract_story_links()
            
            # Filter story links using enhanced filtering
            filtered_story_links = []
            if story_links:
                for link in story_links:
                    if self.is_valid_article_url(link):
                        filtered_story_links.append(link)
                    else:
                        print(f"[{self.site_name.upper()}] Page {page_num}: ❌ Filtered out non-article URL: {link}")
            
            newly_inserted_stories = []
            if filtered_story_links:
                # Store new filtered story links in database
                newly_inserted_stories = self.store_new_links_in_db(filtered_story_links, self.news_source)
                insert_count = len(newly_inserted_stories)
                self.save_new_links_to_file(newly_inserted_stories, new_links_file)
                all_links.extend(newly_inserted_stories)
                
                print(f"[{self.site_name.upper()}] Page {page_num}: Extracted {len(story_links)} story links, filtered to {len(filtered_story_links)}, inserted {insert_count} new ones.")
            else:
                print(f"[{self.site_name.upper()}] Page {page_num}: No valid story links found after filtering.")
            
            # Also get all other links on the page as fallback, but filter them properly
            link_elements = self.driver.find_elements(By.TAG_NAME, "a")
            all_valid_links = []
            for lnk in link_elements:
                href = lnk.get_attribute("href")
                if href and self.is_valid_article_url(href):
                    all_valid_links.append(href)
            
            # Store all valid page links as well
            newly_inserted_all = self.store_new_links_in_db(all_valid_links, self.news_source)
            all_links.extend(newly_inserted_all)
            
            if newly_inserted_all:
                self.save_new_links_to_file(newly_inserted_all, new_links_file)
                print(f"[{self.site_name.upper()}] Page {page_num}: Inserted {len(newly_inserted_all)} additional new valid link(s) from page.")
            
            # Check if we should stop due to no new ARTICLE content
            total_new_articles = len(newly_inserted_stories) + len(newly_inserted_all)
            if total_new_articles == 0:
                consecutive_no_new += 1
                print(f"[{self.site_name.upper()}] Page {page_num}: No new article links found. Consecutive count: {consecutive_no_new}/{self.max_consecutive_no_new}")
                if consecutive_no_new >= self.max_consecutive_no_new:
                    print(f"[{self.site_name.upper()}] Hit max consecutive pages ({self.max_consecutive_no_new}) with 0 new article inserts. Stopping.")
                    break
            else:
                consecutive_no_new = 0  # Reset consecutive counter
                print(f"[{self.site_name.upper()}] Page {page_num}: Found {total_new_articles} new articles. Reset consecutive counter.")
            
            # Try to go to next page
            if not self.click_next_button():
                print(f"[{self.site_name.upper()}] No more pages for sector: {sector_url}")
                break
                
            self.scroll_to_bottom()
        
        return all_links
    
    def scrape_links(self, max_pages=30):
        """Enhanced scraping that covers all sector pages"""
        print(f"[INFO] Starting enhanced {self.site_name} sector-based link scraping...")
        
        all_discovered_links = []
        
        try:
            # First discover all sector URLs
            sector_urls = self.discover_sector_urls()
            
            if not sector_urls:
                print(f"[WARNING] No sector URLs found, falling back to homepage scraping...")
                # Fallback to original homepage scraping
                return self._scrape_homepage_fallback(max_pages)
            
            # Scrape each sector page
            pages_per_sector = max(1, max_pages // len(sector_urls))  # Distribute pages across sectors
            
            for sector_url in sector_urls:
                try:
                    sector_links = self.scrape_sector_page(sector_url, pages_per_sector)
                    all_discovered_links.extend(sector_links)
                    
                    print(f"[{self.site_name.upper()}] Completed scraping {sector_url}: {len(sector_links)} new links")
                    
                    # Small delay between sectors to be respectful
                    time.sleep(2)
                    
                except Exception as e:
                    print(f"[ERROR] Error scraping sector {sector_url}: {e}")
                    continue
            
            print(f"[INFO] Finished enhanced {self.site_name} scraping. Total new links: {len(all_discovered_links)}")
            return all_discovered_links
            
        except Exception as e:
            print(f"[ERROR] Error in enhanced scraping: {e}")
            # Fallback to original method
            return self._scrape_homepage_fallback(max_pages)
    
    def _scrape_homepage_fallback(self, max_pages):
        """Fallback method using the original homepage scraping approach"""
        print(f"[INFO] Using fallback homepage scraping for {self.site_name}...")
        
        # Navigate to the homepage
        if not self.safe_navigate(self.start_url):
            print(f"[ERROR] Could not access {self.site_name} website. Stopping.")
            return []
        
        consecutive_no_new = 0
        loadmore_count = 0
        new_links_file = os.path.join(os.path.dirname(self.profile_dir), "new_links.txt")
        
        for i in range(1, max_pages + 1):
            print(f"[{self.site_name.upper()}] Page {i}: Fetching links...")
            
            # Get all links on the page and filter them
            link_elements = self.driver.find_elements(By.TAG_NAME, "a")
            all_valid_links = []
            total_links_found = 0
            
            for lnk in link_elements:
                href = lnk.get_attribute("href")
                if href:
                    total_links_found += 1
                    if self.is_valid_article_url(href):
                        all_valid_links.append(href)
                    else:
                        print(f"[{self.site_name.upper()}] Page {i}: ❌ Filtered out non-article URL: {href}")
            
            # Store new valid links in database
            newly_inserted = self.store_new_links_in_db(all_valid_links, self.news_source)
            insert_count = len(newly_inserted)
            self.save_new_links_to_file(newly_inserted, new_links_file)
            
            print(f"[{self.site_name.upper()}] Page {i}: Found {total_links_found} total links, filtered to {len(all_valid_links)} valid links, inserted {insert_count} new ones.")
            
            if insert_count == 0:
                consecutive_no_new += 1
                print(f"[{self.site_name.upper()}] Page {i}: No new article links found. Consecutive count: {consecutive_no_new}/{self.max_consecutive_no_new}")
                if consecutive_no_new >= self.max_consecutive_no_new:
                    print(f"[{self.site_name.upper()}] Hit max consecutive pages ({self.max_consecutive_no_new}) with 0 new article inserts. Stopping.")
                    break
            else:
                consecutive_no_new = 0
                print(f"[{self.site_name.upper()}] Page {i}: Found {insert_count} new articles. Reset consecutive counter.")
            
            # Try the 'View More Stories' button 3 times
            max_retries = 3
            clicked = False
            for retry in range(max_retries):
                try:
                    time.sleep(self.wait_time)
                    # ID-based locator for "View More Stories" button
                    more_btn = self.driver.find_element(By.ID, "article-list-button")
                    more_btn.click()
                    loadmore_count += 1
                    print(f"[{self.site_name.upper()}] Clicked 'View More Stories'. Waiting {self.wait_time}s...")
                    time.sleep(self.wait_time)
                    clicked = True
                    break
                except Exception as e:
                    if retry < max_retries - 1:
                        print(f"[{self.site_name.upper()}] 'View More Stories' button not found, retry {retry + 1}/{max_retries}...")
                        self.scroll_to_bottom()
                    else:
                        print(f"[{self.site_name.upper()}] No more 'View More Stories' button after all retries. Stopping.")
                        return []
            
            if not clicked:
                # If we never found the button, end scraping
                break
            
            self.scroll_to_bottom()
        
        print(f"[INFO] Finished {self.site_name} fallback scraping. Clicked 'View More Stories' {loadmore_count} times.")
        return [] 