import time
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from .base_scraper import BaseScraper

class BisnowScraper(BaseScraper):
    """
    Scraper for Bisnow website
    """
    
    def __init__(self, driver, conn, site_config, wait_time=0):
        super().__init__(driver, conn, site_config, wait_time)
        self.site_name = "bisnow"
        self.news_source = "bisnow"
        self.start_url = "https://www.bisnow.com/"
        
    def is_logged_in(self):
        """Check if we're already logged into Bisnow"""
        try:
            print(f"[INFO] Checking login status for {self.site_name}")
            # Make sure we're in the main content, not in an iframe
            try:
                self.driver.switch_to.default_content()
            except Exception as e:
                print(f"[WARNING] Error switching to default content: {e}")
                
            # Get the indicators that show we're logged in
            indicators = self.site_config.get("already_logged_in_indicators", [])
            if not indicators:
                print(f"[WARNING] No login indicators defined for {self.site_name}. Cannot determine login status.")
                return False
                
            # Get the indicators that show we're NOT logged in
            not_logged_in_indicators = self.site_config.get("not_logged_in_indicators", ["Sign In", "Log In", "Login", "Sign Up", "Register"])

            # Check if any of the indicators are present in the page
            page_source = self.driver.page_source.lower()
            
            # Then check for logged in indicators
            for indicator in indicators:
                if indicator.lower() in page_source.lower():
                    print(f"[INFO] Detected logged-in state for {self.site_name} (found: '{indicator}')")
                    return True

            # First check if any "not logged in" indicators are present
            for indicator in not_logged_in_indicators:
                if indicator.lower() in page_source:
                    print(f"[INFO] Detected not-logged-in indicator: '{indicator}'")
                    return False

            # If we reached here, we're likely logged in
            return True
            
        except Exception as e:
            print(f"[WARNING] Error checking login status for {self.site_name}: {e}")
            return False
    
    def login(self):
        """Handle login for Bisnow - implementing specific login flow based on the site structure"""
        try:
            # Check if already logged in
            if self.is_logged_in():
                print(f"[INFO] Already logged in to {self.site_name}")
                return True
                
            # Look for the login element with the specified class
            print("[INFO] Looking for login button on Bisnow homepage...")
            login_element = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, self.site_config.get("login_trigger_class", "logIn")))
            )
            
            # Click the login button to open the modal
            print("[INFO] Clicking login button to open login modal...")
            login_element.click()
            
            # Wait for the login modal to appear and email field to be visible
            print("[INFO] Waiting for login modal to appear...")
            email_field = WebDriverWait(self.driver, 10).until(
                EC.visibility_of_element_located((By.ID, self.site_config.get("email_field_id", "login_email_signin")))
            )
            
            # Enter email
            email = self.site_config.get("email", os.environ.get("BISNOW_EMAIL", ""))
            if not email:
                print("[ERROR] No email provided for Bisnow login")
                return False
                
            print(f"[INFO] Entering email: {email}")
            email_field.send_keys(email)
            
            # Find and fill password field
            password_field = self.driver.find_element(By.ID, self.site_config.get("password_field_id", "login_password"))
            password = self.site_config.get("password", os.environ.get("BISNOW_PASSWORD", ""))
            if not password:
                print("[ERROR] No password provided for Bisnow login")
                return False
                
            print("[INFO] Entering password")
            password_field.send_keys(password)
            
            # Click the login button in the modal
            print("[INFO] Clicking login button in modal...")
            login_button = self.driver.find_element(By.CLASS_NAME, self.site_config.get("submit_button_class", "do-login-btn"))
            login_button.click()
            
            # Wait for login to complete - look for success indicator
            print("[INFO] Waiting for login to complete...")
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.XPATH, self.site_config.get("success_indicator_xpath", "//*[contains(text(), 'My Account')]")))
            )
            
            # Verify login was successful
            if self.is_logged_in():
                print("[INFO] Successfully logged in to Bisnow")
                return True
            else:
                print("[WARNING] Login process completed but login indicators not found for Bisnow")
                return False
        except Exception as e:
            print(f"[WARNING] Failed to login to Bisnow: {e}")
            return False
    
    def scrape_links(self, max_pages=30):
        """Scrape links from Bisnow homepage"""
        print(f"[INFO] Starting {self.site_name} link scraping...")
        
        # Navigate to the homepage
        if not self.safe_navigate(self.start_url):
            print(f"[ERROR] Could not access {self.site_name} website. Stopping.")
            return []
        
        consecutive_no_new = 0
        new_links_file = os.path.join(os.path.dirname(self.profile_dir), "new_links.txt")
        
        for i in range(1, max_pages + 1):
            print(f"[{self.site_name.upper()}] Page {i}: Fetching links...")
            
            # Get all links on the page and filter them
            link_elements = self.driver.find_elements(By.TAG_NAME, "a")
            all_valid_links = []
            total_links_found = 0
            
            for lnk in link_elements:
                href = lnk.get_attribute("href")
                if href:
                    total_links_found += 1
                    if self.is_valid_article_url(href):
                        all_valid_links.append(href)
                    else:
                        print(f"[{self.site_name.upper()}] Page {i}: ❌ Filtered out non-article URL: {href}")
            
            # Store new valid links in database
            newly_inserted = self.store_new_links_in_db(all_valid_links, self.news_source)
            insert_count = len(newly_inserted)
            self.save_new_links_to_file(newly_inserted, new_links_file)
            
            print(f"[{self.site_name.upper()}] Page {i}: Found {total_links_found} total links, filtered to {len(all_valid_links)} valid links, inserted {insert_count} new ones.")
            
            if insert_count == 0:
                consecutive_no_new += 1
                print(f"[{self.site_name.upper()}] Page {i}: No new article links found. Consecutive count: {consecutive_no_new}/{self.max_consecutive_no_new}")
                if consecutive_no_new >= self.max_consecutive_no_new:
                    print(f"[{self.site_name.upper()}] Hit max consecutive pages ({self.max_consecutive_no_new}) with 0 new article inserts. Stopping.")
                    break
            else:
                consecutive_no_new = 0
                print(f"[{self.site_name.upper()}] Page {i}: Found {insert_count} new articles. Reset consecutive counter.")
            
            self.scroll_to_bottom()
        
        print(f"[INFO] Finished {self.site_name} scraping.")
        return [] 