import time
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from .base_scraper import BaseScraper

class TheRealDealScraper(BaseScraper):
    """
    Scraper for TheRealDeal website
    """
    
    def __init__(self, driver, conn, site_config, wait_time=0):
        super().__init__(driver, conn, site_config, wait_time)
        self.site_name = "therealdeal"
        self.news_source = "therealdeal"
        self.start_url = "https://therealdeal.com/sector/commercial/"
        
    def is_logged_in(self):
        """Check if we're logged in by looking for profile page indicators"""
        try:
            # Navigate to profile page to check login status
            profile_url = self.site_config.get("profile_url", "https://therealdeal.com/account/")
            current_url = self.driver.current_url
            
            # Only navigate if we're not already on the profile page
            if profile_url not in current_url:
                if not self.safe_navigate(profile_url):
                    return False
            
            # Check if the profile page has the account content class
            try:
                content_element = self.driver.find_element(By.CLASS_NAME, "MyAccountPage_accountContent__knDhB")
                if content_element:
                    print(f"[INFO] Detected profile page for {self.site_name}")
                    return True
            except:
                pass
            
            # Check for other login indicators
            page_source = self.driver.page_source.lower()
            logged_in_indicators = self.site_config.get("already_logged_in_indicators", ["my account", "log out", "sign out"])
            not_logged_in_indicators = self.site_config.get("not_logged_in_indicators", ["sign in", "log in", "login", "sign up", "register"])
            
            # Check for logged in indicators
            for indicator in logged_in_indicators:
                if indicator.lower() in page_source:
                    print(f"[INFO] Detected logged-in state for {self.site_name} (found: '{indicator}')")
                    return True
            
            # Check for not logged in indicators
            for indicator in not_logged_in_indicators:
                if indicator.lower() in page_source:
                    print(f"[INFO] Detected not-logged-in indicator: '{indicator}'")
                    return False
            
            return False
            
        except Exception as e:
            print(f"[WARNING] Error checking login status for {self.site_name}: {e}")
            return False
    
    def login(self):
        """Handle login for TheRealDeal with the Piano login form"""
        try:
            # Get login URL
            login_url = self.site_config.get("login_url", "https://therealdeal.com/login/")
            print(f"[INFO] Navigating to login page: {login_url}")
            
            if not self.safe_navigate(login_url):
                # Try alternate login URL
                alternate_login_url = self.site_config.get("alternate_login_url", "https://therealdeal.com/account/login/")
                print(f"[INFO] Primary login URL failed. Trying alternate: {alternate_login_url}")
                if not self.safe_navigate(alternate_login_url):
                    print("[ERROR] Failed to navigate to any login page")
                    return False
            
            print("[INFO] Successfully loaded login page")
            self.take_screenshot(f"{self.site_name}_login_page.png")
            
            # Check for login form iframes
            iframe_found = False
            iframe_list = self.driver.find_elements(By.TAG_NAME, "iframe")
            
            if iframe_list:
                print(f"[INFO] Found {len(iframe_list)} iframes on the page. Checking each one...")
                
                for i, iframe in enumerate(iframe_list):
                    try:
                        iframe_id = iframe.get_attribute("id") or "unknown"
                        iframe_src = iframe.get_attribute("src") or "unknown"
                        print(f"[INFO] Iframe {i+1}: id='{iframe_id}', src='{iframe_src}'")
                        
                        self.driver.switch_to.frame(iframe)
                        
                        # Look for login form in this iframe
                        email_fields = self.driver.find_elements(By.CSS_SELECTOR, 
                            "input[type='email'], input[name='email'], input.email, input#email, input[placeholder*='email' i], input[placeholder*='username' i]")
                        password_fields = self.driver.find_elements(By.CSS_SELECTOR, 
                            "input[type='password'], input[name='password'], input.password, input#password")
                        
                        if email_fields and password_fields:
                            print(f"[INFO] Found login form in iframe {i+1}")
                            iframe_found = True
                            break
                        
                        self.driver.switch_to.default_content()
                    except Exception as e:
                        print(f"[WARNING] Error checking iframe {i+1}: {e}")
                        self.driver.switch_to.default_content()
            
            if not iframe_found:
                print("[INFO] No login form found in iframes. Continuing with main page...")
                self.driver.switch_to.default_content()
            
            # Get credentials
            email = self.site_config.get("email", os.environ.get("THEREALDEAL_EMAIL", ""))
            password = self.site_config.get("password", os.environ.get("THEREALDEAL_PASSWORD", ""))
            
            if not email or not password:
                print("[ERROR] No credentials provided for TheRealDeal login")
                return False
            
            # Use JavaScript to analyze and fill the form
            form_filled = self.driver.execute_script(f"""
                function analyzeAndFillLoginForm() {{
                    let result = {{ success: false, message: "" }};
                    
                    // Find email fields
                    const emailSelectors = [
                        'input[type="email"]',
                        'input[name="email"]',
                        'input.email',
                        'input#email',
                        'input[placeholder*="email" i]',
                        'input[placeholder*="username" i]'
                    ];
                    
                    let emailField = null;
                    for (const selector of emailSelectors) {{
                        emailField = document.querySelector(selector);
                        if (emailField && emailField.offsetParent !== null) {{
                            break;
                        }}
                    }}
                    
                    // Find password fields
                    const passwordSelectors = [
                        'input[type="password"]',
                        'input[name="password"]',
                        'input.password',
                        'input#password'
                    ];
                    
                    let passwordField = null;
                    for (const selector of passwordSelectors) {{
                        passwordField = document.querySelector(selector);
                        if (passwordField && passwordField.offsetParent !== null) {{
                            break;
                        }}
                    }}
                    
                    if (emailField && passwordField) {{
                        // Fill email
                        emailField.value = '{email}';
                        emailField.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        emailField.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        
                        // Fill password
                        passwordField.value = '{password}';
                        passwordField.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        passwordField.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        
                        result.success = true;
                        result.message = "Form filled successfully";
                    }} else {{
                        result.message = `Email field: ${{emailField ? 'found' : 'not found'}}, Password field: ${{passwordField ? 'found' : 'not found'}}`;
                    }}
                    
                    return result;
                }}
                
                return analyzeAndFillLoginForm();
            """)
            
            if not form_filled['success']:
                print(f"[ERROR] Failed to fill login form: {form_filled['message']}")
                return False
            
            print("[INFO] Login form filled successfully")
            
            # Submit the form
            submit_success = self.driver.execute_script("""
                function submitLoginForm() {
                    const submitSelectors = [
                        'button[type="submit"]',
                        'input[type="submit"]',
                        'button.submit',
                        'button[class*="submit"]',
                        'button[class*="login"]',
                        'input[class*="login"]'
                    ];
                    
                    for (const selector of submitSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.offsetParent !== null) {
                            element.click();
                            return true;
                        }
                    }
                    return false;
                }
                
                return submitLoginForm();
            """)
            
            if not submit_success:
                print("[ERROR] Failed to submit login form")
                return False
            
            print("[INFO] Login form submitted")
            
            # Wait for login to complete
            time.sleep(5)
            
            # Switch back to default content
            self.driver.switch_to.default_content()
            self.take_screenshot(f"{self.site_name}_after_submit.png")
            
            # Verify login was successful
            if self.is_logged_in():
                print(f"[INFO] Login successful for {self.site_name}")
                return True
            else:
                print(f"[WARNING] Login attempt completed but login indicators not found for {self.site_name}")
                return False
                
        except Exception as e:
            print(f"[ERROR] Exception during {self.site_name} login: {e}")
            self.take_screenshot(f"{self.site_name}_error.png")
            return False
    
    def scrape_links(self, max_pages=30):
        """Scrape links from TheRealDeal commercial page with improved Load More button handling"""
        print(f"[INFO] Starting {self.site_name} link scraping...")
        
        # Navigate to the commercial page
        if not self.safe_navigate(self.start_url):
            print(f"[ERROR] Could not access {self.site_name} website. Stopping.")
            return []
        
        consecutive_no_new = 0
        loadmore_count = 0
        new_links_file = os.path.join(os.path.dirname(self.profile_dir), "new_links.txt")
        
        for i in range(1, max_pages + 1):
            print(f"[{self.site_name.upper()}] Page {i}: Fetching links...")
            
            # Get all links on the page and filter them
            link_elements = self.driver.find_elements(By.TAG_NAME, "a")
            all_valid_links = []
            total_links_found = 0
            
            for lnk in link_elements:
                href = lnk.get_attribute("href")
                if href:
                    total_links_found += 1
                    if self.is_valid_article_url(href):
                        all_valid_links.append(href)
                    else:
                        print(f"[{self.site_name.upper()}] Page {i}: ❌ Filtered out non-article URL: {href}")
            
            # Store new valid links in database
            newly_inserted = self.store_new_links_in_db(all_valid_links, self.news_source)
            insert_count = len(newly_inserted)
            self.save_new_links_to_file(newly_inserted, new_links_file)
            
            print(f"[{self.site_name.upper()}] Page {i}: Found {total_links_found} total links, filtered to {len(all_valid_links)} valid links, inserted {insert_count} new ones.")
            
            if insert_count == 0:
                consecutive_no_new += 1
                print(f"[{self.site_name.upper()}] Page {i}: No new article links found. Consecutive count: {consecutive_no_new}/{self.max_consecutive_no_new}")
                if consecutive_no_new >= self.max_consecutive_no_new:
                    print(f"[{self.site_name.upper()}] Hit max consecutive pages ({self.max_consecutive_no_new}) with 0 new article inserts. Stopping.")
                    break
            else:
                consecutive_no_new = 0
                print(f"[{self.site_name.upper()}] Page {i}: Found {insert_count} new articles. Reset consecutive counter.")
            
            # Try to click "Load more" button with improved selector
            clicked = self._click_load_more_button()
            
            if not clicked:
                print(f"[{self.site_name.upper()}] No more 'Load more' button found. Stopping.")
                break
            
            loadmore_count += 1
            self.scroll_to_bottom()
        
        print(f"[INFO] Finished {self.site_name} scraping. Clicked 'Load more' {loadmore_count} times.")
        return []
    
    def _click_load_more_button(self):
        """
        Improved method to click the "Load more" button with multiple strategies
        """
        max_retries = 3
        
        for retry in range(max_retries):
            try:
                print(f"[{self.site_name.upper()}] Attempting to find 'Load more' button (attempt {retry + 1}/{max_retries})")
                time.sleep(2)  # Wait for any dynamic content to load
                
                # Strategy 1: Use the exact class combination from the provided HTML
                button_selectors = [
                    # Most specific selector using all the classes
                    'button.BlogrollScroller_loadMore__XHN9N.Button_root__eW05B.Button_primary__q4znz.Button_medium__Y3eCE',
                    # Fallback to just the main load more class
                    'button.BlogrollScroller_loadMore__XHN9N',
                    # CSS selector targeting the specific button type and classes
                    'button[type="button"].BlogrollScroller_loadMore__XHN9N',
                    # XPath with class contains
                    '//button[contains(@class, "BlogrollScroller_loadMore")]',
                    # XPath with text content
                    '//button[contains(text(), "Load more")]',
                    # More generic button selector
                    'button.Button_primary__q4znz.Button_medium__Y3eCE'
                ]
                
                for selector_type, selector in enumerate(button_selectors):
                    try:
                        print(f"[{self.site_name.upper()}] Trying selector {selector_type + 1}: {selector}")
                        
                        # Use different methods based on selector type
                        if selector.startswith('//'):
                            # XPath selector
                            load_more_btn = self.driver.find_element(By.XPATH, selector)
                        else:
                            # CSS selector
                            load_more_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                        
                        # Check if the button is visible and clickable
                        if load_more_btn.is_displayed() and load_more_btn.is_enabled():
                            print(f"[{self.site_name.upper()}] Found button with text: '{load_more_btn.text.strip()}'")
                            
                            # Scroll to the button to ensure it's in view
                            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", load_more_btn)
                            time.sleep(1)
                            
                            # Try to click the button
                            try:
                                load_more_btn.click()
                                print(f"[{self.site_name.upper()}] Successfully clicked 'Load more' button")
                                time.sleep(3)  # Wait for content to load
                                return True
                            except Exception as click_error:
                                print(f"[{self.site_name.upper()}] Direct click failed, trying JavaScript click: {click_error}")
                                # Try JavaScript click as fallback
                                self.driver.execute_script("arguments[0].click();", load_more_btn)
                                print(f"[{self.site_name.upper()}] JavaScript click successful")
                                time.sleep(3)
                                return True
                        else:
                            print(f"[{self.site_name.upper()}] Button found but not clickable (displayed: {load_more_btn.is_displayed()}, enabled: {load_more_btn.is_enabled()})")
                            
                    except Exception as selector_error:
                        print(f"[{self.site_name.upper()}] Selector {selector_type + 1} failed: {selector_error}")
                        continue
                
                # If no selector worked, try a more general approach
                print(f"[{self.site_name.upper()}] All selectors failed, trying general button search...")
                
                # Find all buttons and look for "Load more" text
                all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                for button in all_buttons:
                    try:
                        button_text = button.text.strip().lower()
                        if "load more" in button_text and button.is_displayed() and button.is_enabled():
                            print(f"[{self.site_name.upper()}] Found 'Load more' button via text search: '{button.text.strip()}'")
                            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", button)
                            time.sleep(1)
                            button.click()
                            print(f"[{self.site_name.upper()}] Successfully clicked button via text search")
                            time.sleep(3)
                            return True
                    except Exception as button_error:
                        continue
                
                # If still no luck, scroll and try again
                if retry < max_retries - 1:
                    print(f"[{self.site_name.upper()}] No button found, scrolling and retrying...")
                    self.scroll_to_bottom()
                    time.sleep(2)
                    
                    # Take screenshot for debugging
                    self.take_screenshot(f"{self.site_name}_loadmore_retry_{retry}.png")
                
            except Exception as e:
                print(f"[{self.site_name.upper()}] Error in attempt {retry + 1}: {e}")
                if retry < max_retries - 1:
                    print(f"[{self.site_name.upper()}] Retrying...")
                    time.sleep(2)
        
        print(f"[{self.site_name.upper()}] Failed to find 'Load more' button after {max_retries} attempts")
        return False 