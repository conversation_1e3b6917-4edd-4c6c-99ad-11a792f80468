import time
import os
import requests
import brotli
from bs4 import BeautifulSoup
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from .base_scraper import BaseScraper

class <PERSON><PERSON><PERSON><PERSON><PERSON>raper(BaseScraper):
    """
    Enhanced scraper for PincusCo website with proper pagination and article extraction
    """
    
    def __init__(self, driver, conn, site_config, wait_time=0):
        super().__init__(driver, conn, site_config, wait_time)
        self.site_name = "pincus"
        self.news_source = "Pincus"
        self.start_url = "https://www.pincusco.com/"
        self.max_consecutive_no_new = 3  # Set explicit threshold of 3
        
        # Ensure site_config has the correct name
        if "name" not in self.site_config:
            self.site_config["name"] = "pincus"
    
    def is_logged_in(self):
        """Check if we're logged in by looking for WordPress login indicators"""
        try:
            page_source = self.driver.page_source.lower()
            logged_in_indicators = self.site_config.get("already_logged_in_indicators", ["log out", "my account", "subscriber profile"])
            
            # Check for logged in indicators
            for indicator in logged_in_indicators:
                if indicator.lower() in page_source:
                    print(f"[INFO] Detected logged-in state for {self.site_name} (found: '{indicator}')")
                    return True
            
            print(f"[INFO] No login indicators found for {self.site_name}")
            return False
            
        except Exception as e:
            print(f"[WARNING] Error checking login status for {self.site_name}: {e}")
            return False
    
    def login(self):
        """Handle login for PincusCo - implementing WordPress login handler"""
        try:
            # WordPress sites typically use wp-login.php for authentication
            login_url = self.site_config.get("login_url", "https://www.pincusco.com/wp-login.php")
            print(f"[INFO] Navigating to WordPress login page: {login_url}")
            
            if not self.safe_navigate(login_url):
                # Try alternate URL if the main login URL fails
                alternate_login_url = self.site_config.get("alternate_login_url", "https://www.pincusco.com/log-in/")
                print(f"[INFO] Primary login URL failed. Trying alternate login URL: {alternate_login_url}")
                if not self.safe_navigate(alternate_login_url):
                    print(f"[ERROR] Failed to navigate to any login page for {self.site_name}")
                    return False
                    
            # Take screenshot of login page
            self.take_screenshot(f"{self.site_name}_login_page.png")
            
            # WordPress login form has specific field IDs
            try:
                # Find username/email field
                print(f"[INFO] Looking for WordPress username field with ID '{self.site_config.get('email_field_id', 'user_login')}'")
                username_field = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.ID, self.site_config.get("email_field_id", "user_login")))
                )
                
                # Get credentials
                email = self.site_config.get("email", os.environ.get(f"{self.site_name.upper()}_EMAIL", ""))
                password = self.site_config.get("password", os.environ.get(f"{self.site_name.upper()}_PASSWORD", ""))
                
                if not email or not password:
                    print(f"[ERROR] No credentials provided for {self.site_name} login")
                    return False
                    
                # Enter username/email
                print(f"[INFO] Entering email: {email}")
                username_field.clear()
                username_field.send_keys(email)
                
                # Find password field
                print(f"[INFO] Looking for WordPress password field with ID '{self.site_config.get('password_field_id', 'user_pass')}'")
                password_field = self.driver.find_element(By.ID, self.site_config.get("password_field_id", "user_pass"))
                
                # Enter password
                print(f"[INFO] Entering password")
                password_field.clear()
                password_field.send_keys(password)
                
                # Check "Remember Me" box if present
                try:
                    remember_me = self.driver.find_element(By.ID, self.site_config.get("remember_me_id", "rememberme"))
                    if not remember_me.is_selected():
                        remember_me.click()
                        print(f"[INFO] Checked 'Remember Me' option")
                except Exception as e:
                    print(f"[INFO] Remember Me checkbox not found or not clickable: {e}")
                
                # Find and click the login button
                print(f"[INFO] Clicking WordPress login button with ID '{self.site_config.get('submit_button_id', 'wp-submit')}'")
                login_button = self.driver.find_element(By.ID, self.site_config.get("submit_button_id", "wp-submit"))
                login_button.click()
                
                # Wait for login to complete
                print(f"[INFO] Waiting for login to complete...")
                time.sleep(5)
                
                # Take screenshot after submission
                self.take_screenshot(f"{self.site_name}_after_submit.png")
                
                # Verify login was successful
                if self.is_logged_in():
                    print(f"[INFO] Successfully logged in to {self.site_name}")
                    return True
                else:
                    print(f"[WARNING] Login attempt completed but login indicators not found for {self.site_name}")
                    return False
                    
            except Exception as e:
                print(f"[ERROR] Exception during {self.site_name} login form interaction: {e}")
                return False
                
        except Exception as e:
            print(f"[ERROR] Exception during {self.site_name} login: {e}")
            self.take_screenshot(f"{self.site_name}_error.png")
            return False
    
    def extract_article_links(self):
        """Extract article links from the PincusCo website structure"""
        article_links = []
        
        try:
            # First, take a screenshot for debugging
            self.take_screenshot(f"{self.site_name}_article_extraction.png")
            
            # Try multiple strategies to find article links
            selectors_to_try = [
                # Exact selector based on user's HTML structure
                "h3.entry-title a",
                # Common WordPress article selectors
                "article h2.entry-title a",
                "article h3.entry-title a", 
                "article h1.entry-title a",
                ".entry-title a",
                ".post-title a",
                "h2.post-title a",
                "h2.entry-title a",
                "h1.entry-title a",
                # More generic selectors
                "article a[href*='pincusco.com']",
                ".post a[href*='pincusco.com']",
                "article .entry-content a",
                # Very generic fallback
                "a[href*='pincusco.com']"
            ]
            
            # Debug: Check if we're on Cloudflare challenge page
            page_title = self.driver.title
            page_source_snippet = self.driver.page_source[:500]  # First 500 chars
            print(f"[{self.site_name.upper()}] DEBUG: Page title: '{page_title}'")
            print(f"[{self.site_name.upper()}] DEBUG: Page source snippet: {page_source_snippet[:200]}...")
            
            if "just a moment" in page_title.lower():
                print(f"[{self.site_name.upper()}] DEBUG: Still on Cloudflare challenge page")
                # Try to extract any links anyway
                all_links = self.driver.find_elements(By.TAG_NAME, "a")
                print(f"[{self.site_name.upper()}] DEBUG: Found {len(all_links)} total links on challenge page")
            
            print(f"[{self.site_name.upper()}] Trying multiple article extraction strategies...")
            
            for i, selector in enumerate(selectors_to_try):
                try:
                    print(f"[{self.site_name.upper()}] Strategy {i+1}: {selector}")
                    links = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    print(f"[{self.site_name.upper()}] Found {len(links)} links with selector: {selector}")
                    
                    if links:
                        for link in links:
                            try:
                                href = link.get_attribute('href')
                                link_text = link.text.strip()
                                print(f"[{self.site_name.upper()}] DEBUG: Found link: '{link_text}' -> {href}")
                                
                                if href and href.startswith(('http', '/')):
                                    if href.startswith('/'):
                                        href = f"https://www.pincusco.com{href}"
                                    
                                    # Use enhanced filtering method
                                    if self.is_valid_article_url(href):
                                        article_links.append(href)
                                        print(f"[{self.site_name.upper()}] ✅ Added article link: {href}")
                                    else:
                                        print(f"[{self.site_name.upper()}] ❌ Filtered out non-article URL: {href}")
                            except Exception as e:
                                print(f"[WARNING] Error processing link: {e}")
                                continue
                        
                        # If we found links with this selector, we can break or continue to get more
                        if article_links:
                            print(f"[{self.site_name.upper()}] Successfully found {len(article_links)} links with strategy {i+1}")
                            break
                            
                except Exception as e:
                    print(f"[WARNING] Strategy {i+1} failed: {e}")
                    continue
            
            # If no links found with CSS selectors, try finding articles and then extracting links
            if not article_links:
                print(f"[{self.site_name.upper()}] No links found with CSS selectors, trying article elements...")
                try:
                    articles = self.driver.find_elements(By.TAG_NAME, "article")
                    print(f"[{self.site_name.upper()}] Found {len(articles)} article elements")
                    
                    for article in articles:
                        try:
                            all_links = article.find_elements(By.TAG_NAME, "a")
                            for link in all_links:
                                href = link.get_attribute('href')
                                if href:
                                    if href.startswith('/'):
                                        href = f"https://www.pincusco.com{href}"
                                    # Use enhanced filtering method
                                    if self.is_valid_article_url(href):
                                        article_links.append(href)
                        except Exception as e:
                            continue
                except Exception as e:
                    print(f"[WARNING] Article element extraction failed: {e}")
            
            # Remove duplicates while preserving order
            seen = set()
            unique_article_links = []
            for link in article_links:
                if link not in seen:
                    seen.add(link)
                    unique_article_links.append(link)
            
            print(f"[{self.site_name.upper()}] Extracted {len(unique_article_links)} unique article links")
            
            # Show sample links for debugging
            if unique_article_links:
                print(f"[{self.site_name.upper()}] Sample article links:")
                for i, link in enumerate(unique_article_links[:3]):
                    print(f"  {i+1}. {link}")
            else:
                # Debug: show page title and some content to understand what we're seeing
                try:
                    page_title = self.driver.title
                    print(f"[{self.site_name.upper()}] DEBUG: Page title: '{page_title}'")
                    
                    # Check if we're still on a challenge page
                    page_source = self.driver.page_source.lower()
                    if any(indicator in page_source for indicator in [
                        "checking your browser", "please wait", "just a moment", 
                        "challenge-error-text", "_cf_chl_opt"
                    ]):
                        print(f"[{self.site_name.upper()}] DEBUG: Still appears to be on Cloudflare challenge page")
                except Exception as e:
                    print(f"[{self.site_name.upper()}] DEBUG: Error getting page info: {e}")
            
            return unique_article_links
            
        except Exception as e:
            print(f"[ERROR] Error extracting article links: {e}")
            self.take_screenshot(f"{self.site_name}_extraction_error.png")
            return []
    
    def scrape_links_with_requests(self, max_pages=30):
        """Alternative scraping method using requests + BeautifulSoup (bypasses Selenium detection)"""
        print(f"[INFO] Starting {self.site_name} link scraping with requests + BeautifulSoup...")
        
        # Setup session with headers to appear more like a real browser
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"'
        })
        
        # Add some common cookies to appear more like a real browser
        session.cookies.update({
            'cf_clearance': 'placeholder',  # Cloudflare clearance (placeholder)
        })
        
        consecutive_no_new = 0
        new_links_file = os.path.join(os.path.dirname(self.profile_dir), "new_links.txt")
        all_discovered_links = []
        
        for page_num in range(1, max_pages + 1):
            # Construct the page URL directly
            if page_num == 1:
                page_url = self.start_url  # https://www.pincusco.com/
            else:
                page_url = f"https://www.pincusco.com/page/{page_num}/"
            
            print(f"[{self.site_name.upper()}] Page {page_num}: Requesting {page_url}")
            
            try:
                # Make the request with explicit headers for content handling
                response = session.get(page_url, timeout=30, stream=False)
                print(f"[{self.site_name.upper()}] Page {page_num}: Status code: {response.status_code}")
                print(f"[{self.site_name.upper()}] Page {page_num}: Response headers: {dict(response.headers)}")
                
                # Check for successful response
                if response.status_code == 404:
                    print(f"[{self.site_name.upper()}] Page {page_num}: Reached end of content (404). Stopping.")
                    break
                elif response.status_code != 200:
                    print(f"[{self.site_name.upper()}] Page {page_num}: Unexpected status code {response.status_code}, skipping.")
                    continue
                
                # Handle the response content properly, including Brotli decompression
                try:
                    content_encoding = response.headers.get('content-encoding', '').lower()
                    print(f"[{self.site_name.upper()}] Page {page_num}: Content-Encoding: {content_encoding}")
                    
                    # Handle different compression types
                    if content_encoding == 'br':
                        # Brotli compression - decode manually
                        print(f"[{self.site_name.upper()}] Page {page_num}: Manually decompressing Brotli content...")
                        try:
                            raw_content = brotli.decompress(response.content)
                            content = raw_content.decode('utf-8', errors='ignore')
                            print(f"[{self.site_name.upper()}] Page {page_num}: Successfully decompressed Brotli content")
                        except Exception as br_e:
                            print(f"[{self.site_name.upper()}] Page {page_num}: Brotli decompression failed: {br_e}")
                            content = response.content.decode('utf-8', errors='ignore')
                    else:
                        # Standard content handling
                        response.encoding = response.apparent_encoding or 'utf-8'
                        content = response.text
                        print(f"[{self.site_name.upper()}] Page {page_num}: Using standard content decoding")
                    
                    print(f"[{self.site_name.upper()}] Page {page_num}: Final content length: {len(content)}")
                except Exception as e:
                    print(f"[{self.site_name.upper()}] Page {page_num}: Error decoding content: {e}")
                    # Fallback to raw content
                    try:
                        content = response.content.decode('utf-8', errors='ignore')
                    except Exception as e2:
                        print(f"[{self.site_name.upper()}] Page {page_num}: Error with fallback decoding: {e2}")
                        continue
                
                # Parse the HTML
                soup = BeautifulSoup(content, 'html.parser')
                page_title = soup.title.string if soup.title else "No title"
                print(f"[{self.site_name.upper()}] Page {page_num}: Page title: '{page_title}'")
                
                # Debug: Show first few hundred characters of response
                content_preview = content[:500] if content else "No content"
                print(f"[{self.site_name.upper()}] Page {page_num}: Content preview: {content_preview[:200]}...")
                
                # Check for HTML structure indicators
                has_html = '<html' in content.lower()
                has_body = '<body' in content.lower()
                has_title = soup.title is not None
                content_length = len(content)
                print(f"[{self.site_name.upper()}] Page {page_num}: has_html={has_html}, has_body={has_body}, has_title={has_title}, content_length={content_length}")
                
                # Check if we're still on Cloudflare challenge page
                if "just a moment" in page_title.lower() or "checking your browser" in content.lower():
                    print(f"[{self.site_name.upper()}] Page {page_num}: Still on Cloudflare challenge page, skipping.")
                    consecutive_no_new += 1
                    if consecutive_no_new >= self.max_consecutive_no_new:
                        print(f"[{self.site_name.upper()}] Too many consecutive Cloudflare pages, stopping.")
                        break
                    continue
                
                # Extract article links using BeautifulSoup
                article_links = self.extract_links_from_soup(soup, page_num)
                
                if article_links:
                    # Store new article links in database
                    newly_inserted = self.store_new_links_in_db(article_links, self.news_source)
                    insert_count = len(newly_inserted)
                    self.save_new_links_to_file(newly_inserted, new_links_file)
                    all_discovered_links.extend(newly_inserted)
                    
                    print(f"[{self.site_name.upper()}] Page {page_num}: Extracted {len(article_links)} article links, inserted {insert_count} new ones.")
                else:
                    print(f"[{self.site_name.upper()}] Page {page_num}: No article links extracted.")
                    newly_inserted = []
                
                # Also extract all other links as fallback, but filter them properly
                all_links = [a.get('href') for a in soup.find_all('a', href=True)]
                all_valid_links = []
                for href in all_links:
                    if href:
                        if href.startswith('/'):
                            href = f"https://www.pincusco.com{href}"
                        # Only include valid article URLs (not pagination or admin URLs)
                        if self.is_valid_article_url(href):
                            all_valid_links.append(href)
                
                # Store all valid page links as well
                newly_inserted_all = self.store_new_links_in_db(all_valid_links, self.news_source)
                all_discovered_links.extend(newly_inserted_all)
                
                if newly_inserted_all:
                    self.save_new_links_to_file(newly_inserted_all, new_links_file)
                    print(f"[{self.site_name.upper()}] Page {page_num}: Inserted {len(newly_inserted_all)} additional new valid link(s) from page.")
                
                # Check if we should stop due to no new ARTICLE content
                # Focus only on actual article links, not all page links
                total_new_articles = len(newly_inserted) + len(newly_inserted_all)
                if total_new_articles == 0:
                    consecutive_no_new += 1
                    print(f"[{self.site_name.upper()}] Page {page_num}: No new article links found. Consecutive count: {consecutive_no_new}/{self.max_consecutive_no_new}")
                    if consecutive_no_new >= self.max_consecutive_no_new:
                        print(f"[{self.site_name.upper()}] Hit max consecutive pages ({self.max_consecutive_no_new}) with 0 new article inserts. Stopping.")
                        break
                else:
                    consecutive_no_new = 0  # Reset consecutive counter
                    print(f"[{self.site_name.upper()}] Page {page_num}: Found {total_new_articles} new articles. Reset consecutive counter.")
                
                # Add a small delay between pages to be respectful
                time.sleep(2)
                
            except requests.exceptions.RequestException as e:
                print(f"[{self.site_name.upper()}] Page {page_num}: Request error: {e}")
                consecutive_no_new += 1
                if consecutive_no_new >= self.max_consecutive_no_new:
                    print(f"[{self.site_name.upper()}] Too many consecutive errors, stopping.")
                    break
                continue
            except Exception as e:
                print(f"[{self.site_name.upper()}] Page {page_num}: Unexpected error: {e}")
                continue
        
        print(f"[INFO] Finished {self.site_name} scraping with requests. Total new links: {len(all_discovered_links)}")
        return all_discovered_links
    
    def extract_links_from_soup(self, soup, page_num):
        """Extract article links from BeautifulSoup object"""
        article_links = []
        
        try:
            print(f"[{self.site_name.upper()}] Page {page_num}: Extracting article links with BeautifulSoup...")
            
            # Try the exact selector we know works: h3.entry-title a
            selectors_to_try = [
                "h3.entry-title a",           # Exact match from user's HTML
                ".entry-title a",             # More general
                "article h3.entry-title a",   # With article wrapper
                "article .entry-title a",     # General article + entry-title
                ".post-title a",              # Alternative title class
                "h2.entry-title a",          # Alternative heading level
                "h1.entry-title a",          # Alternative heading level
            ]
            
            for i, selector in enumerate(selectors_to_try):
                print(f"[{self.site_name.upper()}] Page {page_num}: Trying selector {i+1}: {selector}")
                
                # Find elements using CSS selector
                links = soup.select(selector)
                print(f"[{self.site_name.upper()}] Page {page_num}: Found {len(links)} links with selector: {selector}")
                
                if links:
                    for link in links:
                        try:
                            href = link.get('href')
                            link_text = link.get_text(strip=True)
                            print(f"[{self.site_name.upper()}] Page {page_num}: DEBUG: Found link: '{link_text}' -> {href}")
                            
                            if href:
                                # Handle relative URLs
                                if href.startswith('/'):
                                    href = f"https://www.pincusco.com{href}"
                                
                                # Use enhanced filtering method
                                if self.is_valid_article_url(href):
                                    article_links.append(href)
                                    print(f"[{self.site_name.upper()}] Page {page_num}: ✅ Added article link: {href}")
                                else:
                                    print(f"[{self.site_name.upper()}] Page {page_num}: ❌ Filtered out non-article URL: {href}")
                        except Exception as e:
                            print(f"[WARNING] Error processing link: {e}")
                            continue
                    
                    # If we found links with this selector, we can break
                    if article_links:
                        print(f"[{self.site_name.upper()}] Page {page_num}: Successfully found {len(article_links)} links with selector {i+1}")
                        break
            
            # Remove duplicates while preserving order
            seen = set()
            unique_article_links = []
            for link in article_links:
                if link not in seen:
                    seen.add(link)
                    unique_article_links.append(link)
            
            print(f"[{self.site_name.upper()}] Page {page_num}: Extracted {len(unique_article_links)} unique article links")
            
            # Show sample links for debugging
            if unique_article_links:
                print(f"[{self.site_name.upper()}] Page {page_num}: ✅ SUCCESS: Found {len(unique_article_links)} article links:")
                for i, link in enumerate(unique_article_links[:3]):  # Show first 3 links
                    print(f"[{self.site_name.upper()}] Page {page_num}:   {i+1}. {link}")
                if len(unique_article_links) > 3:
                    print(f"[{self.site_name.upper()}] Page {page_num}:   ... and {len(unique_article_links) - 3} more")
            else:
                print(f"[{self.site_name.upper()}] Page {page_num}: ❌ No article links found")
            
            return unique_article_links
            
        except Exception as e:
            print(f"[ERROR] Error extracting article links from soup: {e}")
            return []

    # DEPRECATED: click_next_button method removed
    # Now using direct URL navigation (e.g., /page/2/, /page/3/) instead of clicking pagination buttons
    # This approach is more reliable and bypasses issues with Cloudflare protection
    
    def scrape_links(self, max_pages=30):
        """Primary scraping method - tries requests first, falls back to Selenium if needed"""
        print(f"[INFO] Starting {self.site_name} link scraping...")
        print(f"[INFO] Attempting requests + BeautifulSoup approach first (bypasses Cloudflare detection)...")
        
        # Try requests + BeautifulSoup approach first
        try:
            links = self.scrape_links_with_requests(max_pages)
            if links:
                print(f"[INFO] ✅ SUCCESS: Requests approach found {len(links)} links!")
                return links
            else:
                print(f"[INFO] Requests approach completed but found no links.")
        except Exception as e:
            print(f"[WARNING] Requests approach failed: {e}")
        
        # Fallback to Selenium approach if requests didn't work
        print(f"[INFO] Falling back to Selenium approach...")
        return self.scrape_links_with_selenium(max_pages)
    
    def scrape_links_with_selenium(self, max_pages=30):
        """Fallback scraping method using Selenium (original approach)"""
        print(f"[INFO] Starting enhanced {self.site_name} link scraping with Selenium...")
        
        consecutive_no_new = 0
        new_links_file = os.path.join(os.path.dirname(self.profile_dir), "new_links.txt")
        all_discovered_links = []
        
        for page_num in range(1, max_pages + 1):
            # Construct the page URL directly
            if page_num == 1:
                page_url = self.start_url  # https://www.pincusco.com/
            else:
                page_url = f"https://www.pincusco.com/page/{page_num}/"
            
            print(f"[{self.site_name.upper()}] Page {page_num}: Navigating to {page_url}")
            
            # Navigate to the specific page
            if not self.safe_navigate(page_url):
                print(f"[ERROR] Could not access page {page_num} at {page_url}. Stopping.")
                break
            
            # Check if we got a 404 or page doesn't exist
            page_title = self.driver.title.lower()
            page_source = self.driver.page_source.lower()
            
            print(f"[{self.site_name.upper()}] Page {page_num}: Page title: '{self.driver.title}'")
            
            # Check for 404 or end of content indicators
            if any(indicator in page_title for indicator in ["404", "not found", "page not found"]):
                print(f"[{self.site_name.upper()}] Page {page_num}: Reached end of content (404). Stopping.")
                break
                
            if any(indicator in page_source for indicator in ["no posts found", "nothing found", "no content"]):
                print(f"[{self.site_name.upper()}] Page {page_num}: No content found. May have reached end.")
                break
            
            # Skip Cloudflare challenge handling - proceed with scraping regardless
            
            print(f"[{self.site_name.upper()}] Page {page_num}: Extracting article links...")
            
            # Extract article links using enhanced method
            article_links = self.extract_article_links()
            
            if article_links:
                # Store new article links in database
                newly_inserted = self.store_new_links_in_db(article_links, self.news_source)
                insert_count = len(newly_inserted)
                self.save_new_links_to_file(newly_inserted, new_links_file)
                all_discovered_links.extend(newly_inserted)
                
                print(f"[{self.site_name.upper()}] Page {page_num}: Extracted {len(article_links)} article links, inserted {insert_count} new ones.")
            else:
                print(f"[{self.site_name.upper()}] Page {page_num}: No article links extracted.")
                newly_inserted = []
            
            # Also get all other links on the page as fallback, but filter them properly
            link_elements = self.driver.find_elements(By.TAG_NAME, "a")
            all_valid_links = []
            for lnk in link_elements:
                href = lnk.get_attribute("href")
                if href:
                    if href.startswith('/'):
                        href = f"https://www.pincusco.com{href}"
                    # Only include valid article URLs (not pagination or admin URLs)
                    if self.is_valid_article_url(href):
                        all_valid_links.append(href)
            
            # Store all valid page links as well
            newly_inserted_all = self.store_new_links_in_db(all_valid_links, self.news_source)
            all_discovered_links.extend(newly_inserted_all)
            
            if newly_inserted_all:
                self.save_new_links_to_file(newly_inserted_all, new_links_file)
                print(f"[{self.site_name.upper()}] Page {page_num}: Inserted {len(newly_inserted_all)} additional new valid link(s) from page.")
            
            # Check if we should stop due to no new ARTICLE content
            # Focus only on actual article links, not all page links
            total_new_articles = len(newly_inserted) + len(newly_inserted_all)
            if total_new_articles == 0:
                consecutive_no_new += 1
                print(f"[{self.site_name.upper()}] Page {page_num}: No new article links found. Consecutive count: {consecutive_no_new}/{self.max_consecutive_no_new}")
                if consecutive_no_new >= self.max_consecutive_no_new:
                    print(f"[{self.site_name.upper()}] Hit max consecutive pages ({self.max_consecutive_no_new}) with 0 new article inserts. Stopping.")
                    break
            else:
                consecutive_no_new = 0  # Reset consecutive counter
                print(f"[{self.site_name.upper()}] Page {page_num}: Found {total_new_articles} new articles. Reset consecutive counter.")
            
            # Add a small delay between pages to be respectful
            time.sleep(2)
        
        print(f"[INFO] Finished enhanced {self.site_name} scraping with Selenium. Total new links: {len(all_discovered_links)}")
        return all_discovered_links

    # Now skipping Cloudflare challenge handling entirely
    
    def safe_navigate(self, url, max_retries=3):
        """Override safe_navigate - skip Cloudflare challenge handling"""
        success = super().safe_navigate(url, max_retries)
        if success:
            # Just wait for page to load, skip Cloudflare handling
            time.sleep(3)
        return success 

    def is_valid_article_url(self, url):
        """Enhanced filtering to exclude pagination, administrative, and non-article URLs"""
        if not url or not isinstance(url, str):
            return False
        
        # Must be a PincusCo URL
        if 'pincusco.com' not in url:
            return False
        
        # Exclude pagination URLs more aggressively
        pagination_patterns = [
            '/page/', '/page=', '?page=', '&page=',
            '/p/', '/p=', '?p=', '&p=',
            '/paged/', '/paged=', '?paged=', '&paged='
        ]
        
        # Exclude administrative and non-article URLs
        excluded_patterns = [
            '/category/', '/author/', '/tag/', '/tags/',
            '/wp-admin/', '/wp-content/', '/wp-includes/',
            '/feed/', '/feeds/', '/rss/', '/atom/',
            '/comments/', '/comment/', '/trackback/',
            '/search/', '/wp-login/', '/wp-register/',
            '/archive/', '/archives/', '/sitemap/',
            '/contact/', '/about/', '/privacy/', '/terms/',
            '/login/', '/register/', '/logout/',
            'mailto:', 'tel:', 'javascript:', 'ftp:',
            '#', '?share=', '?print=', '?pdf=',
            '/print/', '/pdf/', '/embed/',
            '/?', '/#'  # URLs with query params or fragments only
        ]
        
        # Check for pagination patterns
        for pattern in pagination_patterns:
            if pattern in url.lower():
                return False
        
        # Check for excluded patterns
        for pattern in excluded_patterns:
            if pattern in url.lower():
                return False
        
        # Additional checks for pagination URLs that might not match patterns
        # Check if URL ends with /page/number/ or similar
        import re
        if re.search(r'/page/\d+/?$', url.lower()):
            return False
        if re.search(r'/p/\d+/?$', url.lower()):
            return False
        
        # URL should look like an article (contain meaningful path)
        # Exclude bare domain or very short paths
        from urllib.parse import urlparse
        parsed = urlparse(url)
        path = parsed.path.strip('/')
        
        # Must have some meaningful path content
        if not path or len(path) < 3:
            return False
        
        # Should not be just a single word (likely a category or tag)
        path_parts = path.split('/')
        if len(path_parts) == 1 and len(path_parts[0]) < 10:
            return False
        
        return True 

    def test_url_filtering(self):
        """Test method to demonstrate URL filtering functionality"""
        print(f"[{self.site_name.upper()}] Testing URL filtering...")
        
        # Test URLs that should be filtered out
        test_urls = [
            "https://www.pincusco.com/page/6/",  # Pagination URL - should be filtered
            "https://www.pincusco.com/page/2/",  # Pagination URL - should be filtered
            "https://www.pincusco.com/category/news/",  # Category URL - should be filtered
            "https://www.pincusco.com/author/admin/",  # Author URL - should be filtered
            "https://www.pincusco.com/tag/finance/",  # Tag URL - should be filtered
            "https://www.pincusco.com/wp-admin/",  # Admin URL - should be filtered
            "https://www.pincusco.com/feed/",  # Feed URL - should be filtered
            "https://www.pincusco.com/#comment-123",  # Fragment URL - should be filtered
            "https://www.pincusco.com/",  # Homepage - should be filtered (too short)
            "https://www.pincusco.com/about/",  # About page - should be filtered
            "https://www.pincusco.com/contact/",  # Contact page - should be filtered
            "https://www.pincusco.com/some-interesting-article-title/",  # Valid article - should pass
            "https://www.pincusco.com/news/market-update-2024/",  # Valid article - should pass
            "https://www.pincusco.com/insights/investment-strategy/",  # Valid article - should pass
        ]
        
        print(f"[{self.site_name.upper()}] Testing {len(test_urls)} URLs...")
        
        valid_count = 0
        filtered_count = 0
        
        for url in test_urls:
            is_valid = self.is_valid_article_url(url)
            status = "✅ VALID" if is_valid else "❌ FILTERED"
            print(f"[{self.site_name.upper()}] {status}: {url}")
            
            if is_valid:
                valid_count += 1
            else:
                filtered_count += 1
        
        print(f"[{self.site_name.upper()}] Filter test results: {valid_count} valid, {filtered_count} filtered")
        return valid_count, filtered_count 