import time
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from abc import ABC, abstractmethod

class BaseScraper(ABC):
    """
    Base class for all site scrapers providing common functionality
    """
    
    def __init__(self, driver, conn, site_config, wait_time=0):
        self.driver = driver
        self.conn = conn
        self.site_config = site_config
        self.wait_time = wait_time
        self.consecutive_no_new = 0
        self.max_consecutive_no_new = 3
        
        # Common paths
        self.profile_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "urlfetch_chrome_profile")
        self.cookie_dir = os.path.join(self.profile_dir, "cookies")
        
    def safe_navigate(self, url, max_retries=3):
        """Navigate to a URL with retry mechanism for common network errors"""
        retry_count = 0
        delay = 2
        
        while retry_count < max_retries:
            try:
                print(f"[INFO] Navigating to URL: {url}")
                self.driver.get(url)
                time.sleep(self.wait_time)
                return True
            except Exception as e:
                retry_count += 1
                error_message = str(e)
                
                # Check for common network errors
                network_error = any(err in error_message for err in 
                                   ["ERR_NAME_NOT_RESOLVED", "ERR_CONNECTION_REFUSED", 
                                    "ERR_NETWORK_CHANGED", "ERR_CONNECTION_RESET",
                                    "ERR_INTERNET_DISCONNECTED", "ERR_TIMED_OUT"])
                
                if network_error and retry_count < max_retries:
                    print(f"[WARNING] Network error accessing {url}: {e}")
                    print(f"[INFO] Retrying in {delay} seconds (attempt {retry_count}/{max_retries})...")
                    time.sleep(delay)
                    delay *= 2
                else:
                    print(f"[ERROR] Failed to navigate to {url} after {retry_count} attempts: {e}")
                    return False
        
        return False
    
    def scroll_to_bottom(self):
        """Scroll to the bottom of the page"""
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(self.wait_time)
    
    def save_cookies(self, domain):
        """Save cookies for a specific domain"""
        cookies = self.driver.get_cookies()
        cookie_file = os.path.join(self.cookie_dir, f"{domain}.txt")
        
        if not cookies:
            print(f"[WARNING] No cookies found to save for {domain}")
            return
            
        try:
            os.makedirs(self.cookie_dir, exist_ok=True)
            with open(cookie_file, 'w') as f:
                for cookie in cookies:
                    f.write(str(cookie) + '\n')
            print(f"[INFO] Saved {len(cookies)} cookies for {domain} to {cookie_file}")
        except Exception as e:
            print(f"[ERROR] Failed to save cookies for {domain}: {e}")
    
    def load_cookies(self, domain):
        """Load cookies for a specific domain"""
        cookie_file = os.path.join(self.cookie_dir, f"{domain}.txt")
        
        if not os.path.exists(cookie_file):
            print(f"[WARNING] No cookie file found for {domain} at {cookie_file}")
            return False
            
        # Navigate to domain before applying cookies
        base_url = self.site_config.get("home_url", f"https://www.{domain}")
        print(f"[INFO] Navigating to {base_url} before loading cookies...")
        if not self.safe_navigate(base_url):
            print(f"[WARNING] Failed to navigate to {base_url} before loading cookies")
            return False
            
        try:
            with open(cookie_file, 'r') as f:
                cookies = f.readlines()
                
            if not cookies:
                print(f"[WARNING] Cookie file for {domain} exists but is empty")
                return False
                
            loaded_count = 0
            for cookie_str in cookies:
                try:
                    cookie = eval(cookie_str.strip())
                    if 'name' not in cookie or 'value' not in cookie:
                        continue
                        
                    if 'domain' not in cookie:
                        cookie['domain'] = f".{domain}" if not domain.startswith('.') else domain
                        
                    if 'path' not in cookie:
                        cookie['path'] = '/'
                        
                    self.driver.add_cookie(cookie)
                    loaded_count += 1
                except Exception as e:
                    print(f"[WARNING] Failed to load cookie: {e}")
                    
            print(f"[INFO] Loaded {loaded_count}/{len(cookies)} cookies for {domain}")
            
            # Refresh the page to apply cookies
            self.driver.refresh()
            time.sleep(2)
            
            return loaded_count > 0
        except Exception as e:
            print(f"[ERROR] Failed to load cookies for {domain}: {e}")
            return False
    
    def store_new_links_in_db(self, links, news_source):
        """
        Store new links in database after normalization and deduplication
        """
        from psycopg2.extras import execute_values
        
        if not links:
            return []

        # Normalize and deduplicate
        normalized_set = set()
        for raw_link in links:
            norm_link = self.normalize_url(raw_link)
            if norm_link:
                normalized_set.add(norm_link)

        if not normalized_set:
            return []

        cursor = self.conn.cursor()

        try:
            cursor.execute("""
                SELECT url 
                FROM news
                WHERE url = ANY(%s)
            """, (list(normalized_set),))
            existing_urls = set(row[0] for row in cursor.fetchall())

            to_insert = normalized_set - existing_urls
            if not to_insert:
                cursor.close()
                return []

            insert_data = [(url, news_source, False, False, False) for url in to_insert]
            query = """
                INSERT INTO news (url, news_source, fetched, enriched)
                VALUES %s
            """
            execute_values(cursor, query, insert_data)
            self.conn.commit()

            cursor.close()
            print(f"[INFO] Inserted {len(to_insert)} new {news_source} links into news.")
            return list(to_insert)

        except Exception as e:
            print(f"[ERROR] store_new_links_in_db => {e}")
            self.conn.rollback()
            cursor.close()
            return []
    
    def normalize_url(self, url: str) -> str:
        """Normalize URL by removing query parameters and trailing slashes"""
        if not url:
            return url

        # Remove everything after '?'
        base_part = url.split('?')[0]
        # Remove trailing slash or '?'
        base_part = base_part.rstrip('/?')
        return base_part
    
    def save_new_links_to_file(self, links, file_path):
        """Save newly inserted links to a file"""
        if not links:
            return 0

        if os.path.exists(file_path):
            with open(file_path, "r", encoding="utf-8") as f:
                existing = set(line.strip() for line in f)
        else:
            existing = set()

        to_add = [lnk for lnk in links if lnk not in existing]
        if to_add:
            with open(file_path, "a", encoding="utf-8") as f:
                for link in to_add:
                    f.write(link + "\n")
            print(f"[INFO] Wrote {len(to_add)} newly inserted links to {file_path}.")
            return len(to_add)
        else:
            print("[INFO] No additional new links to save to file.")
            return 0
    
    def take_screenshot(self, filename):
        """Take a screenshot for debugging purposes"""
        try:
            screenshot_path = os.path.join(self.profile_dir, filename)
            self.driver.save_screenshot(screenshot_path)
            print(f"[INFO] Saved screenshot to {screenshot_path}")
            return screenshot_path
        except Exception as e:
            print(f"[WARNING] Could not save screenshot: {e}")
            return None
    
    @abstractmethod
    def is_logged_in(self):
        """Check if user is logged in to the site"""
        pass
    
    @abstractmethod
    def login(self):
        """Perform login to the site"""
        pass
    
    @abstractmethod
    def scrape_links(self, max_pages=30):
        """Scrape links from the site"""
        pass
    
    def run_scraper(self, try_login=True, max_pages=30):
        """
        Main method to run the scraper with optional login
        """
        site_name = self.site_config.get("name", "unknown")
        domain = self.site_config.get("domain", "unknown.com")
        
        print(f"[INFO] Starting {site_name} scraper...")
        
        # Try to load cached cookies first
        cookies_loaded = self.load_cookies(domain)
        
        if cookies_loaded:
            print(f"[INFO] Loaded cached cookies for {site_name}")
        
        # Check if we're logged in after loading cookies
        logged_in = self.is_logged_in()
        
        # If not logged in and login is enabled and requested, try fresh login
        if not logged_in and try_login:
            print(f"[INFO] Not logged in to {site_name}. Attempting fresh login...")
            if self.login():
                print(f"[INFO] Successfully logged in to {site_name}")
                logged_in = True
            else:
                print(f"[WARNING] Login failed, continuing with available access")
        
        if logged_in:
            print(f"[INFO] Scraping {site_name} with authenticated access")
        else:
            print(f"[INFO] Scraping {site_name} with guest access")
        
        # Run the scraping
        try:
            links = self.scrape_links(max_pages)
            # Save cookies after scraping (if login occurred)
            self.save_cookies(domain)
            return links
        except Exception as e:
            print(f"[ERROR] Exception during {site_name} scraping: {e}")
            self.take_screenshot(f"{site_name}_error.png")
            return []
    
    def is_valid_article_url(self, url, site_domain=None):
        """Enhanced filtering to exclude pagination, administrative, and non-article URLs"""
        if not url or not isinstance(url, str):
            return False
        
        # Determine site domain
        if not site_domain:
            site_domain = getattr(self, 'site_name', 'unknown')
        
        # Must contain the site domain (flexible matching)
        domain_indicators = []
        if site_domain == "pincus":
            domain_indicators = ['pincusco.com']
        elif site_domain == "globest":
            domain_indicators = ['globest.com']
        elif site_domain == "bisnow":
            domain_indicators = ['bisnow.com']
        elif site_domain == "therealdeal":
            domain_indicators = ['therealdeal.com']
        else:
            # Generic check - URL should be http/https
            if not url.startswith(('http://', 'https://')):
                return False
        
        # Check domain if we have specific indicators
        if domain_indicators:
            domain_found = any(domain in url.lower() for domain in domain_indicators)
            if not domain_found:
                return False
        
        # Exclude pagination URLs more aggressively
        pagination_patterns = [
            '/page/', '/page=', '?page=', '&page=',
            '/p/', '/p=', '?p=', '&p=',
            '/paged/', '/paged=', '?paged=', '&paged=',
            '/pg/', '/pg=', '?pg=', '&pg=',
            '/pagenum/', '/pagenum=', '?pagenum=', '&pagenum='
        ]
        
        # Exclude administrative and non-article URLs
        excluded_patterns = [
            '/category/', '/author/', '/tag/', '/tags/',
            '/wp-admin/', '/wp-content/', '/wp-includes/',
            '/feed/', '/feeds/', '/rss/', '/atom/',
            '/comments/', '/comment/', '/trackback/',
            '/search/', '/wp-login/', '/wp-register/',
            '/archive/', '/archives/', '/sitemap/',
            '/contact/', '/about/', '/privacy/', '/terms/',
            '/login/', '/register/', '/logout/', '/signup/',
            '/account/', '/profile/', '/settings/', '/dashboard/',
            'mailto:', 'tel:', 'javascript:', 'ftp:',
            '#', '?share=', '?print=', '?pdf=',
            '/print/', '/pdf/', '/embed/',
            '/?', '/#',  # URLs with query params or fragments only
            '/sector/?', '/sectors/?',  # Bare sector pages
            'utm_source=', 'utm_medium=', 'utm_campaign=',  # Tracking parameters
            'fbclid=', 'gclid=', 'msclkid=',  # Ad tracking
        ]
        
        # Check for pagination patterns
        for pattern in pagination_patterns:
            if pattern in url.lower():
                return False
        
        # Check for excluded patterns
        for pattern in excluded_patterns:
            if pattern in url.lower():
                return False
        
        # Additional checks for pagination URLs that might not match patterns
        # Check if URL ends with /page/number/ or similar
        import re
        if re.search(r'/page/\d+/?$', url.lower()):
            return False
        if re.search(r'/p/\d+/?$', url.lower()):
            return False
        if re.search(r'/pg/\d+/?$', url.lower()):
            return False
        
        # URL should look like an article (contain meaningful path)
        # Exclude bare domain or very short paths
        from urllib.parse import urlparse
        try:
            parsed = urlparse(url)
            path = parsed.path.strip('/')
            
            # Must have some meaningful path content
            if not path or len(path) < 3:
                return False
            
            # Should not be just a single word (likely a category or tag)
            path_parts = path.split('/')
            if len(path_parts) == 1 and len(path_parts[0]) < 10:
                return False
            
            # Exclude URLs that are clearly not articles
            non_article_patterns = [
                'login', 'register', 'signup', 'contact', 'about',
                'privacy', 'terms', 'cookie', 'disclaimer',
                'advertise', 'subscribe', 'newsletter',
                'rss', 'feed', 'sitemap', 'robots'
            ]
            
            path_lower = path.lower()
            for pattern in non_article_patterns:
                if pattern in path_lower and len(path_parts) <= 2:
                    return False
                    
        except Exception as e:
            # If URL parsing fails, it's probably not a valid URL
            return False
        
        return True 