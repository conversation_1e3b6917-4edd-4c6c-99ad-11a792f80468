// Migration script to update deals and investment_criteria tables
// Usage: pnpm migrate:deals-investment-criteria

import { pool } from "../src/lib/db";
import fs from "fs";
import path from "path";

async function runMigration(): Promise<void> {
  const sqlPath = path.resolve(
    "sql-files",
    "alter_deals_and_investment_criteria.sql"
  );
  let sql: string;
  try {
    sql = fs.readFileSync(sqlPath, "utf8");
  } catch (err) {
    console.error("Failed to read SQL file:", err);
    process.exit(1);
  }

  try {
    await pool.query("BEGIN");
    await pool.query(sql);
    await pool.query("COMMIT");
    console.log("Migration completed successfully.");
  } catch (err) {
    await pool.query("ROLLBACK");
    console.error("Migration failed:", err);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

runMigration();
