# Duplicate Deal Resolver

This script identifies and resolves duplicate deals in your ANAX database using the same fuzzy matching logic implemented in the web application.

## Current Duplicates Detected

Based on your database scan, you have **3 duplicate deals** for the same property:

- **Deal ID 46**: "1111-1177 Summer Street Office Buildings, Stamford, CT" (Created: 6/30/2025)
- **Deal ID 47**: "1111-1177 Summer Street, Stamford, CT" (Created: 6/30/2025)
- **Deal ID 48**: "1111-1177 Summer Street Office Refinance" (Created: 7/1/2025)

All three deals are for the same property at 1111-1177 Summer Street, Stamford, CT with the same sponsor "1111 Summer Street Venture LLC".

## Setup

1. Navigate to the scripts directory:

```bash
cd scripts
```

2. Install dependencies:

```bash
npm install
```

3. Set up your database environment variables (or use your existing .env file):

```bash
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5432
export POSTGRES_DATABASE=anax
export POSTGRES_USER=your_username
export POSTGRES_PASSWORD=your_password
```

## Usage

### 1. Scan for Duplicates (No Changes)

Just identify duplicates without making any changes:

```bash
npm run scan
```

### 2. Interactive Resolution (Recommended)

Choose which deals to keep and merge interactively:

```bash
npm run resolve
```

This will:

- Show you all duplicate groups
- Let you choose which deal to keep in each group
- Mark others as "Merged Duplicate" status
- Add merge notes to processing_notes

### 3. Auto-Resolution

Automatically keep the earliest created deal in each group:

```bash
npm run auto-resolve
```

## How It Works

### Detection Logic

- **Name Similarity**: Uses Jaro-Winkler algorithm (80%+ similarity threshold)
- **Location Matching**: Exact match on city/state or ZIP code
- **Sponsor Matching**: Additional verification using sponsor name similarity

### Resolution Process

1. **Target Deal**: The deal you choose to keep (or earliest if auto-resolving)
2. **Duplicate Deals**: Marked with status "Merged Duplicate"
3. **Audit Trail**: All changes logged in `processing_notes` field
4. **Data Preservation**: Original data preserved, just status updated

### What Gets Updated

- **Target Deal**: Gets merge information added to `processing_notes`
- **Duplicate Deals**:
  - Status changed to "Merged Duplicate"
  - Merge notes added to `processing_notes`
  - `updated_at` timestamp updated

## Safety Features

- **Database Transactions**: All changes are atomic (all succeed or all fail)
- **Confirmation Prompts**: Interactive mode asks for confirmation
- **Scan Mode**: Preview duplicates without making changes
- **Audit Trail**: Complete record of what was merged and when

## Example Output

```
🔍 Scanning for duplicate deals...

=== DUPLICATE DEALS FOUND ===

Group 1: 3 duplicates
----------------------------------------
1. Deal ID: 46
   Name: 1111-1177 Summer Street Office Buildings, Stamford, CT
   Sponsor: 1111 Summer Street Venture LLC
   Location: N/A, N/A 06905
   Created: 6/30/2025
   Status: Under Contract for Sale/Financing

2. Deal ID: 47
   Name: 1111-1177 Summer Street, Stamford, CT
   Sponsor: 1111 Summer Street Venture LLC
   Location: N/A, N/A 06905
   Created: 6/30/2025
   Status: Active

3. Deal ID: 48
   Name: 1111-1177 Summer Street Office Refinance
   Sponsor: 1111 Summer Street Venture LLC
   Location: N/A, N/A 06905
   Created: 7/1/2025
   Status: Active
```

## Recommendations

For your current duplicates, I recommend:

1. **Keep Deal ID 46** - It has the most complete name and "Under Contract" status
2. **Merge Deal IDs 47 & 48** - Mark as duplicates since they're the same property

You can do this by running:

```bash
npm run resolve
```

Then choosing option 1 (Deal ID 46) when prompted.

## Verification

After running the script, you can verify the results by checking:

```sql
-- See remaining active deals
SELECT deal_id, deal_name, status, created_at
FROM deals
WHERE status != 'Merged Duplicate'
ORDER BY deal_name;

-- See what was merged
SELECT deal_id, deal_name, status, processing_notes
FROM deals
WHERE status = 'Merged Duplicate'
OR processing_notes LIKE '%MERGED%';
```

## Rollback

If you need to undo the merge, you can manually update the status back:

```sql
UPDATE deals
SET status = 'Active',
    processing_notes = 'Merge reversed'
WHERE deal_id IN (47, 48);  -- Replace with actual deal IDs
```
