import { exec } from "child_process";
import { promisify } from "util";
import * as fs from "fs";
import * as path from "path";

const execAsync = promisify(exec);

async function generateMigration() {
  try {
    const migrationName = process.argv[2] || "AutoGeneratedMigration";
    
    console.log("🔄 Generating TypeORM migration...");
    console.log(`📝 Migration name: ${migrationName}`);
    
    // Ensure migration directory exists
    const migrationDir = path.join(__dirname, "../src/lib/typeorm/migrations");
    if (!fs.existsSync(migrationDir)) {
      fs.mkdirSync(migrationDir, { recursive: true });
      console.log(`📁 Created migrations directory: ${migrationDir}`);
    }
    
    // Use TypeORM CLI to generate migration
    const command = `tsx ./node_modules/typeorm/cli.js -d src/lib/typeorm/config-cli.cjs migration:generate src/lib/typeorm/migrations/${migrationName}`;
    
    console.log(`🚀 Running: ${command}`);
    const { stdout, stderr } = await execAsync(command);
    
    if (stdout) {
      console.log("✅ TypeORM CLI output:");
      console.log(stdout);
    }
    
    if (stderr && !stderr.includes("Migration")) {
      console.error("⚠️ TypeORM CLI warnings/errors:");
      console.error(stderr);
    }
    
    // List generated migration files
    const migrationFiles = fs.readdirSync(migrationDir)
      .filter(file => file.endsWith('.ts'))
      .sort();
    
    if (migrationFiles.length > 0) {
      console.log("📚 Available migrations:");
      migrationFiles.forEach(file => {
        console.log(`  - ${file}`);
      });
      
      console.log("\n🎯 Next steps:");
      console.log("  1. Review the generated migration file");
      console.log("  2. Run: npm run migrate:typeorm:run");
      console.log("  3. Or run: npm run migrate:typeorm:auto (to run automatically)");
    } else {
      console.log("ℹ️ No migrations generated. Your database schema is up to date!");
    }

  } catch (error) {
    console.error("❌ Error generating migration:", error);
    if (error instanceof Error && error.message.includes("No changes in database schema")) {
      console.log("ℹ️ No changes detected in database schema. Everything is up to date!");
    } else {
      process.exit(1);
    }
  }
}

generateMigration(); 