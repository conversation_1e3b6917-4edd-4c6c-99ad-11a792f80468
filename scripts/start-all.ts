import { spawn } from "child_process";
import path from "path";

function runScript(scriptPath: string, name: string) {
  const proc = spawn("npx", ["tsx", scriptPath], {
    stdio: ["ignore", "pipe", "pipe"],
    shell: process.platform === "win32",
  });

  proc.stdout.on("data", (data) => {
    process.stdout.write(`[${name}] ${data}`);
  });
  proc.stderr.on("data", (data) => {
    process.stderr.write(`[${name} ERROR] ${data}`);
  });
  proc.on("close", (code) => {
    console.log(`[${name}] exited with code ${code}`);
  });
  return proc;
}

const dealWorkerPath = path.join(__dirname, "deal-worker.ts");
const gmailWorkerPath = path.join(__dirname, "gmail-worker.ts");

console.log("Starting deal-processing worker and Gmail worker in parallel...");

runScript(dealWorkerPath, "DealWorker");
runScript(gmail<PERSON>orkerPath, "GmailWorker");
