import express from "express";
import { ExpressAdapter } from "@bull-board/express";
import { createBullBoard } from "@bull-board/api";
import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { BullMQManager } from "../src/lib/queue/BullMQManager";

// Get queues from BullMQManager (do not start any workers)
const bullManager = BullMQManager.getInstance();
// Note: Gmail worker should be started separately, not here
// This ensures Bull Board only monitors, doesn't run workers
const dealQueue = bullManager.getDealQueue();
const gmailQueue = bullManager.getGmailQueue();

const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath("/admin/queues");

createBullBoard({
  queues: [
    new BullMQAdapter(dealQueue),
    ...(gmailQueue ? [new BullMQAdapter(gmailQueue)] : []),
  ],
  serverAdapter,
});

const app = express();
app.use("/admin/queues", serverAdapter.getRouter());

const PORT = 3001;
app.listen(PORT, () => {
  console.log(`Bull Board running on http://localhost:${PORT}/admin/queues`);
});
