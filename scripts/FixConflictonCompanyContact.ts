#!/usr/bin/env ts-node

import { Pool } from 'pg';
import stringSimilarity from 'string-similarity';

const DB_USER = "anax_user"
const DB_PASSWORD = "aCai1EeduGhahg0ainiM"
const DB_HOST = "anax.cloud"
const DB_PORT = 5432
const DB_DATABASE = "anax"
// ---- DB CONFIGURATION ----
const pool = new Pool({ 
  user: DB_USER,
  password: DB_PASSWORD,
  host: DB_HOST,
  port: DB_PORT,
  database: DB_DATABASE,
});

function normalize(str: string | undefined): string | undefined {
  return str ? str.trim().toLowerCase() : undefined;
}

async function main() {
  const client = await pool.connect();
  try {
    // Helper to process a table (companies or contacts)
    async function processTable(table: string, idField: string) {
      let count = 0;
      const res = await client.query(
        `SELECT ${idField}, conflicts FROM ${table} WHERE conflict_status = 'pending' AND conflicts IS NOT NULL`
      );
      for (const row of res.rows) {
        const id = row[idField];
        let conflicts = row.conflicts || {};
        let changed = false;
        
        for (const field in conflicts) {
          const conflict = conflicts[field];
          let existing = conflict.existing_value;
          let newv = conflict.new_value;
          // For arrays, join to string for comparison
          if (Array.isArray(existing)) existing = existing.join(',');
          if (Array.isArray(newv)) newv = newv.join(',');
          if (
            typeof existing === 'string' && typeof newv === 'string'
          ) {
            const sim = stringSimilarity.compareTwoStrings(normalize(existing) || '', normalize(newv) || '');
            if (sim >= 0.97) {
              console.log(`[SIMILARITY ${sim}] conflicts Found for `, table, id, conflicts);
              count++;
              delete conflicts[field];
              changed = true;
              console.log(`[${table}] ${id}: Removed resolved conflict for field '${field}'`);
            }
          }
        }
        const conflictFields = Object.keys(conflicts);
        if (changed) {
          if (conflictFields.length === 0) {
            // All conflicts resolved
            await client.query(
              `UPDATE ${table} SET conflicts = NULL, conflict_status = 'resolved', conflict_resolved_at = NOW() WHERE ${idField} = $1`,
              [id]
            );
            console.log(`[${table}] ${id}: All conflicts resolved.`);
          } else {
            // Some conflicts remain
            await client.query(
              `UPDATE ${table} SET conflicts = $1 WHERE ${idField} = $2`,
              [JSON.stringify(conflicts), id]
            );
            console.log(`[${table}] ${id}: Updated conflicts, ${conflictFields.length} remain.`);
          }
        }
      }
      console.log('Total conflicts resolved: ', count);
    }
    await processTable('companies', 'company_id');
    await processTable('contacts', 'contact_id');
    console.log('Conflict check complete.');
  } finally {
    client.release();
  }
}

main().catch(err => {
  console.error(err);
  process.exit(1);
});