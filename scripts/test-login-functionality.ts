#!/usr/bin/env node

/**
 * Test script for login functionality across different news sites
 * 
 * Usage:
 * npx tsx scripts/test-login-functionality.ts [sitename]
 * npx tsx scripts/test-login-functionality.ts bisnow
 * npx tsx scripts/test-login-functionality.ts all
 */

import puppeteer from 'puppeteer';
import { LoginUtility } from '../src/lib/scrapers/LoginUtility';

interface TestResult {
  siteName: string;
  success: boolean;
  error?: string;
  message?: string;
  alreadyLoggedIn?: boolean;
}

async function testSiteLogin(siteName: string): Promise<TestResult> {
  const loginUtility = new LoginUtility((level, message) => {
    console.log(`[${level.toUpperCase()}] ${message}`);
  });

  // Check if site is supported
  if (!loginUtility.isSiteSupported(siteName)) {
    return {
      siteName,
      success: false,
      error: `Site ${siteName} is not supported`
    };
  }

  const config = loginUtility.getSiteConfig(siteName);
  if (!config) {
    return {
      siteName,
      success: false,
      error: `No configuration found for ${siteName}`
    };
  }

  // Check if credentials are available
  if (!config.email || !config.password) {
    return {
      siteName,
      success: false,
      error: `No credentials configured for ${siteName}. Set ${siteName.toUpperCase()}_EMAIL and ${siteName.toUpperCase()}_PASSWORD environment variables.`
    };
  }

  console.log(`\n🔧 Testing login for ${siteName}...`);
  console.log(`📧 Email: ${config.email}`);
  console.log(`🌐 Home URL: ${config.homeUrl}`);

  let browser;
  try {
    // Launch browser
    browser = await puppeteer.launch({
      headless: process.env.HEADLESS !== 'false',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--enable-cookies',
        '--disable-cookie-encryption'
      ]
    });

    const page = await browser.newPage();
    
    // Set user agent
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    
    // Navigate to site
    console.log(`📍 Navigating to ${config.homeUrl}...`);
    await page.goto(config.homeUrl, { waitUntil: 'networkidle2', timeout: 30000 });

    // Check initial login status
    const initialLoginStatus = await loginUtility.isLoggedIn(page, config);
    console.log(`🔍 Initial login status: ${initialLoginStatus ? '✅ Logged in' : '❌ Not logged in'}`);

    // Attempt login
    const loginResult = await loginUtility.login(page, config);
    
    // Take screenshot for debugging
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const screenshotPath = `./logs/login-test-${siteName}-${timestamp}.png`;
    try {
      await page.screenshot({ path: screenshotPath, fullPage: true });
      console.log(`📸 Screenshot saved: ${screenshotPath}`);
    } catch (screenshotError) {
      console.log(`⚠️  Could not save screenshot: ${screenshotError}`);
    }

    return {
      siteName,
      success: loginResult.success,
      error: loginResult.error,
      message: loginResult.message,
      alreadyLoggedIn: loginResult.alreadyLoggedIn
    };

  } catch (error) {
    return {
      siteName,
      success: false,
      error: `Test failed: ${error}`
    };
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

async function main(): Promise<void> {
  const args = process.argv.slice(2);
  const targetInput = args[0] || 'all';
  
  console.log('🚀 Starting login functionality test...');
  console.log(`🎯 Target: ${targetInput}`);
  console.log(`🌐 Headless mode: ${process.env.HEADLESS !== 'false'}`);
  
  const loginUtility = new LoginUtility();
  const supportedSites = loginUtility.getSupportedSites();
  
  console.log(`📋 Supported sites: ${supportedSites.join(', ')}`);

  // Determine which sites to test
  let sitesToTest: string[] = [];
  
  if (targetInput === 'all') {
    sitesToTest = supportedSites;
  } else if (supportedSites.includes(targetInput)) {
    // Direct site name
    sitesToTest = [targetInput];
  } else if (targetInput.startsWith('http')) {
    // URL provided - detect site from URL
    const detectedSite = loginUtility.detectSiteFromUrl(targetInput);
    if (detectedSite && supportedSites.includes(detectedSite)) {
      console.log(`🔍 Detected site from URL: ${detectedSite}`);
      sitesToTest = [detectedSite];
    } else {
      console.error(`❌ Error: Could not detect supported site from URL '${targetInput}'.`);
      console.log(`Supported sites: ${supportedSites.join(', ')}`);
      console.log(`💡 You can also use site names directly: ${supportedSites.join(', ')}`);
      process.exit(1);
    }
  } else {
    console.error(`❌ Error: '${targetInput}' is not a supported site or valid URL.`);
    console.log(`Supported sites: ${supportedSites.join(', ')}`);
    console.log(`💡 You can also provide a URL to test a specific site`);
    process.exit(1);
  }

  console.log(`🧪 Testing ${sitesToTest.length} site(s)...`);

  const results: TestResult[] = [];

  // Test each site
  for (const siteName of sitesToTest) {
    const result = await testSiteLogin(siteName);
    results.push(result);
    
    // Wait between tests
    if (sitesToTest.length > 1) {
      console.log('⏱️  Waiting 5 seconds before next test...');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }

  // Print summary
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  let successCount = 0;
  let alreadyLoggedInCount = 0;
  
  for (const result of results) {
    const status = result.success ? '✅' : '❌';
    const details = result.alreadyLoggedIn ? ' (already logged in)' : '';
    
    console.log(`${status} ${result.siteName}${details}`);
    
    if (result.success) {
      successCount++;
      if (result.alreadyLoggedIn) {
        alreadyLoggedInCount++;
      }
    }
    
    if (result.error) {
      console.log(`   ⚠️  ${result.error}`);
    }
    
    if (result.message) {
      console.log(`   💬 ${result.message}`);
    }
  }
  
  console.log('\n📈 Statistics:');
  console.log(`Total sites tested: ${results.length}`);
  console.log(`Successful logins: ${successCount}`);
  console.log(`Already logged in: ${alreadyLoggedInCount}`);
  console.log(`New logins: ${successCount - alreadyLoggedInCount}`);
  console.log(`Failed logins: ${results.length - successCount}`);
  
  // Exit with appropriate code
  const overallSuccess = successCount === results.length;
  console.log(`\n🎯 Overall result: ${overallSuccess ? '✅ SUCCESS' : '❌ FAILED'}`);
  
  if (!overallSuccess) {
    console.log('\n💡 Troubleshooting tips:');
    console.log('- Check that all environment variables are set correctly');
    console.log('- Verify account credentials by logging in manually');
    console.log('- Run with HEADLESS=false to see browser interactions');
    console.log('- Check the screenshots in the logs folder');
    console.log('- Ensure accounts are not locked or suspended');
  }
  
  process.exit(overallSuccess ? 0 : 1);
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n⏹️  Test interrupted by user');
  process.exit(130);
});

process.on('SIGTERM', () => {
  console.log('\n⏹️  Test terminated');
  process.exit(143);
});

// Run if this file is executed directly
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Unhandled error in test script:', error);
    process.exit(1);
  });
}

export { testSiteLogin, main }; 