#!/usr/bin/env tsx

/**
 * Upload Processing Worker Script
 * 
 * This script processes pending uploads in the background.
 * Can be run manually or scheduled with cron.
 * 
 * Usage:
 *   npx tsx scripts/process-upload-worker.ts
 *   npx tsx scripts/process-upload-worker.ts --upload-id=123
 *   npx tsx scripts/process-upload-worker.ts --continuous --interval=30
 */

import UploadProcessor from '../src/lib/workers/UploadProcessor'
import { AsyncUploadService } from '../src/lib/services/AsyncUploadService'

interface WorkerOptions {
  uploadId?: number
  continuous?: boolean
  interval?: number // seconds
  maxRuns?: number
  verbose?: boolean
}

class UploadWorkerScript {
  private options: WorkerOptions
  private runCount = 0
  private processor: UploadProcessor

  constructor(options: WorkerOptions = {}) {
    this.options = {
      interval: 60, // 60 seconds default
      maxRuns: Infinity,
      verbose: true,
      ...options
    }
    this.processor = new UploadProcessor()
  }

  async run(): Promise<void> {
    this.log('🚀 Upload Processing Worker Started')
    this.log(`Options:`, this.options)

    if (this.options.uploadId) {
      // Process specific upload
      await this.processSingleUpload(this.options.uploadId)
    } else if (this.options.continuous) {
      // Run continuously
      await this.runContinuous()
    } else {
      // Single run for all pending uploads
      await this.processPendingUploads()
    }

    this.log('✅ Upload Processing Worker Finished')
  }

  private async processSingleUpload(uploadId: number): Promise<void> {
    this.log(`Processing specific upload: ${uploadId}`)
    
    try {
      const result = await this.processor.processUpload(uploadId.toString())
      
      if (result.successfulRows > 0) {
        this.log(`✅ Upload ${uploadId} processed successfully: ${result.successfulRows} rows`)
      } else {
        this.log(`❌ Failed to process upload ${uploadId}: ${result.errors.join(', ')}`)
      }
    } catch (error) {
      this.log(`❌ Error processing upload ${uploadId}:`, error)
    }
  }

  private async processPendingUploads(): Promise<void> {
    this.log('🔍 Checking for pending uploads...')
    
    try {
      const pendingUploads = await AsyncUploadService.getPendingUploads()
      
      if (pendingUploads.length === 0) {
        this.log('ℹ️  No pending uploads found')
        return
      }

      this.log(`📋 Found ${pendingUploads.length} pending uploads`)
      
      for (const upload of pendingUploads) {
        this.log(`📝 Processing: ${upload.file_name} (ID: ${upload.upload_id})`)
        
        try {
          const result = await this.processor.processUpload(upload.upload_id.toString())
          
          if (result.successfulRows > 0) {
            this.log(`✅ Completed: ${upload.file_name} - ${result.successfulRows} rows processed`)
          } else {
            this.log(`❌ Failed: ${upload.file_name} - ${result.errors.join(', ')}`)
          }
        } catch (error) {
          this.log(`❌ Error processing ${upload.file_name}:`, error)
        }
      }
      
      this.log(`🎯 Processed ${pendingUploads.length} uploads`)
    } catch (error) {
      this.log('❌ Error getting pending uploads:', error)
    }
  }

  private async runContinuous(): Promise<void> {
    this.log(`🔄 Running continuously every ${this.options.interval} seconds`)
    this.log(`📊 Max runs: ${this.options.maxRuns === Infinity ? 'unlimited' : this.options.maxRuns}`)

    while (this.runCount < this.options.maxRuns!) {
      this.runCount++
      this.log(`\n🔄 Run #${this.runCount} - ${new Date().toISOString()}`)
      
      await this.processPendingUploads()
      
      if (this.runCount < this.options.maxRuns!) {
        this.log(`⏰ Waiting ${this.options.interval} seconds until next run...`)
        await this.sleep(this.options.interval! * 1000)
      }
    }
  }

  async getWorkerStats(): Promise<void> {
    try {
      const pendingUploads = await AsyncUploadService.getPendingUploads()
      const allUploads = await AsyncUploadService.getAllUploads(50, 0)
      
      const stats = {
        pending: pendingUploads.length,
        processing: allUploads.filter(u => u.status === 'processing').length,
        completed: allUploads.filter(u => u.status === 'completed').length,
        failed: allUploads.filter(u => u.status === 'failed').length,
        total: allUploads.length
      }

      this.log('📊 Current Stats:', stats)
      
      if (pendingUploads.length > 0) {
        this.log('📋 Pending uploads:')
        pendingUploads.forEach(upload => {
          this.log(`  - ${upload.file_name} (${upload.total_rows} rows, created: ${upload.created_at})`)
        })
      }
    } catch (error) {
      this.log('❌ Error getting stats:', error)
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  private log(message: string, ...args: any[]): void {
    if (this.options.verbose) {
      const timestamp = new Date().toISOString()
      console.log(`[${timestamp}] ${message}`, ...args)
    }
  }
}

// Command line interface
async function main() {
  const args = process.argv.slice(2)
  const options: WorkerOptions = {}

  // Parse command line arguments
  for (const arg of args) {
    if (arg.startsWith('--upload-id=')) {
      options.uploadId = parseInt(arg.split('=')[1])
    } else if (arg === '--continuous') {
      options.continuous = true
    } else if (arg.startsWith('--interval=')) {
      options.interval = parseInt(arg.split('=')[1])
    } else if (arg.startsWith('--max-runs=')) {
      options.maxRuns = parseInt(arg.split('=')[1])
    } else if (arg === '--quiet') {
      options.verbose = false
    } else if (arg === '--stats') {
      // Just show stats and exit
      const worker = new UploadWorkerScript(options)
      await worker.getWorkerStats()
      return
    } else if (arg === '--help' || arg === '-h') {
      showHelp()
      return
    }
  }

  const worker = new UploadWorkerScript(options)
  
  // Handle graceful shutdown
  const shutdown = async () => {
    console.log('\n🛑 Received shutdown signal, stopping worker...')
    process.exit(0)
  }

  process.on('SIGINT', shutdown)
  process.on('SIGTERM', shutdown)

  try {
    await worker.run()
  } catch (error) {
    console.error('❌ Worker script failed:', error)
    process.exit(1)
  }
}

function showHelp() {
  console.log(`
Upload Processing Worker

Usage:
  npx tsx scripts/process-upload-worker.ts [options]

Options:
  --upload-id=ID        Process specific upload by ID
  --continuous          Run continuously
  --interval=SECONDS    Interval between runs in continuous mode (default: 60)
  --max-runs=NUMBER     Maximum number of runs in continuous mode
  --quiet               Suppress verbose output
  --stats               Show current stats and exit
  --help, -h            Show this help message

Examples:
  npx tsx scripts/process-upload-worker.ts
  npx tsx scripts/process-upload-worker.ts --upload-id=123
  npx tsx scripts/process-upload-worker.ts --continuous --interval=30
  npx tsx scripts/process-upload-worker.ts --continuous --max-runs=10
  npx tsx scripts/process-upload-worker.ts --stats
`)
}

// Auto-invoke script when run as a script
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Script failed:', error)
    process.exit(1)
  })
}

export default UploadWorkerScript 