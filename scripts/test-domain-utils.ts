#!/usr/bin/env ts-node

/**
 * Test script for domain utility functions
 * Run with: npx ts-node scripts/test-domain-utils.ts
 */

import { 
  extractDomain, 
  fetchAllCompanyWebsites, 
  checkMatchingDomains, 
  getCompaniesWithMatchingDomains,
  getUniqueDomainsWithCompanies 
} from '../src/lib/utils/domain';

async function testDomainExtraction() {
  console.log('=== Testing Domain Extraction ===');
  
  const testUrls = [
    'sub.example.com',
    'https://www.example.co.uk',
    'https://example.com',
    'https://***************',
    'https://subdomain.example.com.au',
    'https://localhost:3000',
    'https://invalid-url',
    '',
    'http://www.mzoosleepmask.us.com',
    'https://shopkhim.myshopify.com'
  ];
  
  testUrls.forEach(url => {
    const domain = extractDomain(url);
    console.log(`URL: ${url} -> Domain: ${domain}`);
  });
}

async function testCompanyWebsites() {
  console.log('\n=== Testing Company Websites Fetch ===');
  
  try {
    const companies = await fetchAllCompanyWebsites();
    console.log(`Found ${companies.length} companies with websites`);
    
    // Show first 5 companies as examples
    companies.slice(0, 5).forEach(company => {
      console.log(`Company ID: ${company.company_id}, Name: ${company.company_name}`);
      console.log(`  Website: ${company.website}`);
      console.log(`  Extracted Domain: ${company.extracted_domain}`);
      console.log('');
    });
    
    return companies;
  } catch (error) {
    console.error('Error fetching company websites:', error);
    return [];
  }
}

async function testMatchingDomains(companies?: any[]) {
  console.log('\n=== Testing Domain Matching ===');
  
  try {
    const matchingResults = await checkMatchingDomains(companies);
    const companiesWithMatches = matchingResults.filter(result => result.has_matching_domain);
    
    console.log(`Found ${companiesWithMatches.length} companies with matching domains`);
    
    // Show first 3 matching examples
    companiesWithMatches.slice(0, 3).forEach(result => {
      console.log(`\nCompany: ${result.company_name} (ID: ${result.company_id})`);
      console.log(`Website: ${result.website}`);
      console.log(`Domain: ${result.extracted_domain}`);
      console.log('Matching companies:');
      result.matching_companies?.forEach(match => {
        console.log(`  - ${match.company_name} (ID: ${match.company_id}): ${match.website}`);
      });
    });
    
    return companiesWithMatches;
  } catch (error) {
    console.error('Error checking matching domains:', error);
    return [];
  }
}

async function testUniqueDomains() {
  console.log('\n=== Testing Unique Domains ===');
  
  try {
    const domainMap = await getUniqueDomainsWithCompanies();
    console.log(`Found ${domainMap.size} unique domains`);
    
    // Show domains with multiple companies
    let multiCompanyDomains = 0;
    domainMap.forEach((companies, domain) => {
      if (companies.length > 1) {
        multiCompanyDomains++;
        if (multiCompanyDomains <= 3) { // Show first 3 examples
          console.log(`\nDomain: ${domain} (${companies.length} companies)`);
          companies.forEach(company => {
            console.log(`  - ${company.company_name} (ID: ${company.company_id})`);
          });
        }
      }
    });
    
    console.log(`\nTotal domains with multiple companies: ${multiCompanyDomains}`);
  } catch (error) {
    console.error('Error getting unique domains:', error);
  }
}

async function main() {
  console.log('Starting Domain Utility Tests...\n');
  
  // Test domain extraction
  await testDomainExtraction();
  
  // Test fetching company websites
  const companies = await testCompanyWebsites();
  
  // Test matching domains
  await testMatchingDomains(companies);
  
  // Test unique domains
  await testUniqueDomains();
  
  console.log('\n=== Tests Complete ===');
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}
