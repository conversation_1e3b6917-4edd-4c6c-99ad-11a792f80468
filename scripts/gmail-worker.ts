import { BullMQManager } from "../src/lib/queue/BullMQManager";

(async () => {
  try {
    const bullManager = BullMQManager.getInstance();
    // Always (re-)schedule the Gmail fetch job on worker start
    await bullManager.addGmailFetchJob({
      repeat: { cron: "* * * * *" }, // every 1 minute
      removeOnComplete: true,
      removeOnFail: true,
    });
    console.log("Scheduled Gmail fetch job to run every 1 minute.");
    bullManager.startGmailWorker();
    console.log("Gmail worker started.");
    // Keep process alive
    process.stdin.resume();
  } catch (err) {
    console.error("Failed to start Gmail worker:", err);
    process.exit(1);
  }
})();
