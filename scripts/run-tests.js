#!/usr/bin/env node

/**
 * Test runner script for InvestmentCriteriaProcessor
 * Usage: node scripts/run-tests.js [options]
 * 
 * Options:
 *   --watch       Run tests in watch mode
 *   --coverage    Run tests with coverage report
 *   --verbose     Run with verbose output
 *   --specific    Run specific test file (e.g., InvestmentCriteriaProcessor)
 */

const { spawn } = require('child_process');
const path = require('path');

const args = process.argv.slice(2);
const projectRoot = path.resolve(__dirname, '..');

// Parse command line arguments
const options = {
  watch: args.includes('--watch'),
  coverage: args.includes('--coverage'),
  verbose: args.includes('--verbose'),
  specific: args.find(arg => arg.startsWith('--specific='))?.split('=')[1]
};

// Build Jest command
let jestCommand = ['jest'];

if (options.watch) {
  jestCommand.push('--watch');
}

if (options.coverage) {
  jestCommand.push('--coverage');
}

if (options.verbose) {
  jestCommand.push('--verbose');
}

if (options.specific) {
  jestCommand.push(`--testPathPattern=${options.specific}`);
}

// Add default options
jestCommand.push('--colors');

console.log('🧪 Running Investment Criteria Processor Tests...');
console.log(`📁 Project Root: ${projectRoot}`);
console.log(`🔧 Command: npx ${jestCommand.join(' ')}`);
console.log('');

// Run Jest
const jest = spawn('npx', jestCommand, {
  cwd: projectRoot,
  stdio: 'inherit',
  shell: true
});

jest.on('close', (code) => {
  if (code === 0) {
    console.log('\n✅ All tests passed!');
  } else {
    console.log(`\n❌ Tests failed with exit code ${code}`);
    process.exit(code);
  }
});

jest.on('error', (error) => {
  console.error('❌ Failed to start Jest:', error);
  process.exit(1);
}); 