import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { pool } from "../src/lib/db";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const migrationsDir = path.join(__dirname, "../sql-files");

async function runMigrations() {
  // Only run the create_investment_criteria_table.sql migration
  const files = ["create_investment_criteria_table.sql"];

  for (const file of files) {
    const filePath = path.join(migrationsDir, file);
    const sql = fs.readFileSync(filePath, "utf8");
    console.log(`Running migration: ${file}`);
    try {
      await pool.query(sql);
      console.log(`Success: ${file}`);
    } catch (err) {
      console.error(`Error running ${file}:`, err);
      process.exit(1);
    }
  }
  console.log("All migrations complete!");
  process.exit(0);
}

runMigrations();
