const { Pool } = require("pg");

// Database connection
const pool = new Pool({
  user: process.env.POSTGRES_USER || "postgres",
  host: process.env.POSTGRES_HOST || "localhost",
  database: process.env.POSTGRES_DATABASE || "anax",
  password: process.env.POSTGRES_PASSWORD,
  port: process.env.POSTGRES_PORT || 5432,
});

/**
 * Calculate string similarity using Jaro-Winkler algorithm
 */
function jaroWinklerSimilarity(s1, s2) {
  const str1 = s1.toLowerCase().trim();
  const str2 = s2.toLowerCase().trim();

  if (str1 === str2) return 1.0;
  if (str1.length === 0 || str2.length === 0) return 0.0;

  const matchDistance = Math.floor(Math.max(str1.length, str2.length) / 2) - 1;
  const s1Matches = new Array(str1.length).fill(false);
  const s2Matches = new Array(str2.length).fill(false);

  let matches = 0;
  let transpositions = 0;

  // Find matches
  for (let i = 0; i < str1.length; i++) {
    const start = Math.max(0, i - matchDistance);
    const end = Math.min(i + matchDistance + 1, str2.length);

    for (let j = start; j < end; j++) {
      if (s2Matches[j] || str1[i] !== str2[j]) continue;
      s1Matches[i] = true;
      s2Matches[j] = true;
      matches++;
      break;
    }
  }

  if (matches === 0) return 0.0;

  // Calculate transpositions
  let k = 0;
  for (let i = 0; i < str1.length; i++) {
    if (!s1Matches[i]) continue;
    while (!s2Matches[k]) k++;
    if (str1[i] !== str2[k]) transpositions++;
    k++;
  }

  const jaro =
    (matches / str1.length +
      matches / str2.length +
      (matches - transpositions / 2) / matches) /
    3;

  // Calculate Jaro-Winkler similarity with prefix scaling
  const prefixLength = Math.min(4, Math.min(str1.length, str2.length));
  let prefix = 0;
  for (let i = 0; i < prefixLength; i++) {
    if (str1[i] === str2[i]) prefix++;
    else break;
  }

  return jaro + 0.1 * prefix * (1 - jaro);
}

/**
 * Check if two locations match
 */
function locationsMatch(existing, incoming) {
  const normalize = (str) => (str || "").toLowerCase().trim();

  const existingCity = normalize(existing.city);
  const existingState = normalize(existing.state);
  const existingZip = normalize(existing.zip_code);

  const incomingCity = normalize(incoming.city);
  const incomingState = normalize(incoming.state);
  const incomingZip = normalize(incoming.zip_code);

  // If ZIP codes match, locations match
  if (existingZip && incomingZip && existingZip === incomingZip) {
    return true;
  }

  // If both have city and state, they must match
  if (existingCity && existingState && incomingCity && incomingState) {
    return existingCity === incomingCity && existingState === incomingState;
  }

  return false;
}

/**
 * Find duplicate deals in the database
 */
async function findDuplicateDeals(similarityThreshold = 0.8) {
  const query = `
    SELECT 
      deal_id,
      deal_name,
      sponsor_name,
      city,
      state,
      zip_code,
      property_type,
      created_at,
      status
    FROM deals 
    WHERE status != 'deleted'
      AND deal_name IS NOT NULL
      AND deal_name != ''
    ORDER BY created_at
  `;

  const result = await pool.query(query);
  const deals = result.rows;

  const duplicateGroups = [];
  const processed = new Set();

  for (let i = 0; i < deals.length; i++) {
    if (processed.has(deals[i].deal_id)) continue;

    const currentDeal = deals[i];
    const duplicates = [currentDeal];
    processed.add(currentDeal.deal_id);

    for (let j = i + 1; j < deals.length; j++) {
      if (processed.has(deals[j].deal_id)) continue;

      const compareDeal = deals[j];

      // Check name similarity
      const nameSimilarity = jaroWinklerSimilarity(
        currentDeal.deal_name || "",
        compareDeal.deal_name || ""
      );

      // Check location match
      const locationMatch = locationsMatch(currentDeal, compareDeal);

      // Check sponsor similarity
      const sponsorSimilarity = jaroWinklerSimilarity(
        currentDeal.sponsor_name || "",
        compareDeal.sponsor_name || ""
      );

      if (nameSimilarity >= similarityThreshold && locationMatch) {
        duplicates.push(compareDeal);
        processed.add(compareDeal.deal_id);
        console.log(
          `Found duplicate: ${currentDeal.deal_name} (${currentDeal.deal_id}) <-> ${compareDeal.deal_name} (${compareDeal.deal_id})`
        );
        console.log(`  Name similarity: ${Math.round(nameSimilarity * 100)}%`);
        console.log(`  Location match: ${locationMatch}`);
        console.log(
          `  Sponsor similarity: ${Math.round(sponsorSimilarity * 100)}%`
        );
      }
    }

    if (duplicates.length > 1) {
      duplicateGroups.push(duplicates);
    }
  }

  return duplicateGroups;
}

/**
 * Display duplicate groups for review
 */
function displayDuplicateGroups(duplicateGroups) {
  console.log("\n=== DUPLICATE DEALS FOUND ===");

  duplicateGroups.forEach((group, index) => {
    console.log(`\nGroup ${index + 1}: ${group.length} duplicates`);
    console.log("----------------------------------------");

    group.forEach((deal, dealIndex) => {
      console.log(`${dealIndex + 1}. Deal ID: ${deal.deal_id}`);
      console.log(`   Name: ${deal.deal_name}`);
      console.log(`   Sponsor: ${deal.sponsor_name}`);
      console.log(
        `   Location: ${deal.city || "N/A"}, ${deal.state || "N/A"} ${
          deal.zip_code || ""
        }`
      );
      console.log(
        `   Created: ${new Date(deal.created_at).toLocaleDateString()}`
      );
      console.log(`   Status: ${deal.status}`);
      console.log("");
    });
  });
}

/**
 * Get detailed deal data for merging
 */
async function getDetailedDeal(dealId) {
  const query = "SELECT * FROM deals WHERE deal_id = $1";
  const result = await pool.query(query, [dealId]);
  return result.rows[0];
}

/**
 * Merge deals - keep the target deal and mark others as merged
 */
async function mergeDeals(targetDealId, duplicateDealIds, mergeReason) {
  const client = await pool.connect();

  try {
    await client.query("BEGIN");

    // Get the target deal
    const targetDeal = await getDetailedDeal(targetDealId);
    if (!targetDeal) {
      throw new Error(`Target deal ${targetDealId} not found`);
    }

    // Get all duplicate deals
    const duplicateDeals = [];
    for (const dealId of duplicateDealIds) {
      const deal = await getDetailedDeal(dealId);
      if (deal) {
        duplicateDeals.push(deal);
      }
    }

    // Update target deal with merge information
    const mergeInfo = {
      merged_at: new Date().toISOString(),
      merged_deal_ids: duplicateDealIds,
      merge_reason: mergeReason,
      original_processing_notes: targetDeal.processing_notes,
    };

    const updateTargetQuery = `
      UPDATE deals 
      SET 
        processing_notes = $1,
        updated_at = CURRENT_TIMESTAMP
      WHERE deal_id = $2
    `;

    const newProcessingNotes = `${
      targetDeal.processing_notes || ""
    }\n[MERGED ${new Date().toISOString()}] Merged with deals: ${duplicateDealIds.join(
      ", "
    )}. Reason: ${mergeReason}`;

    await client.query(updateTargetQuery, [newProcessingNotes, targetDealId]);

    // Mark duplicate deals as merged (set status to indicate they're duplicates)
    for (const dealId of duplicateDealIds) {
      const updateDuplicateQuery = `
        UPDATE deals 
        SET 
          status = 'Merged Duplicate',
          processing_notes = COALESCE(processing_notes, '') || $1,
          updated_at = CURRENT_TIMESTAMP
        WHERE deal_id = $2
      `;

      const mergeNote = `\n[MERGED ${new Date().toISOString()}] Marked as duplicate of deal ${targetDealId}. Original status preserved in review_notes.`;
      await client.query(updateDuplicateQuery, [mergeNote, dealId]);
    }

    await client.query("COMMIT");

    console.log(
      `✅ Successfully merged deals ${duplicateDealIds.join(
        ", "
      )} into deal ${targetDealId}`
    );
  } catch (error) {
    await client.query("ROLLBACK");
    console.error("❌ Error merging deals:", error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Interactive CLI for resolving duplicates
 */
async function interactiveResolve() {
  const readline = require("readline");
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  const question = (query) =>
    new Promise((resolve) => rl.question(query, resolve));

  try {
    console.log("🔍 Scanning for duplicate deals...\n");

    const duplicateGroups = await findDuplicateDeals(0.8);

    if (duplicateGroups.length === 0) {
      console.log("✅ No duplicate deals found!");
      return;
    }

    displayDuplicateGroups(duplicateGroups);

    for (let i = 0; i < duplicateGroups.length; i++) {
      const group = duplicateGroups[i];

      console.log(`\n=== Resolving Group ${i + 1} ===`);
      console.log("Options:");
      console.log(
        "1. Choose which deal to keep (others will be marked as duplicates)"
      );
      console.log("2. Skip this group");
      console.log("3. Exit");

      const choice = await question("Enter your choice (1-3): ");

      if (choice === "3") {
        console.log("Exiting...");
        break;
      }

      if (choice === "2") {
        console.log("Skipping this group...");
        continue;
      }

      if (choice === "1") {
        console.log("\nWhich deal would you like to keep?");
        group.forEach((deal, index) => {
          console.log(
            `${index + 1}. Deal ID ${deal.deal_id}: ${
              deal.deal_name
            } (Created: ${new Date(deal.created_at).toLocaleDateString()})`
          );
        });

        const keepChoice = await question(
          `Enter the number of the deal to keep (1-${group.length}): `
        );
        const keepIndex = parseInt(keepChoice) - 1;

        if (keepIndex >= 0 && keepIndex < group.length) {
          const targetDeal = group[keepIndex];
          const duplicateDealIds = group
            .filter((_, index) => index !== keepIndex)
            .map((deal) => deal.deal_id);

          console.log(
            `\nKeeping: Deal ID ${targetDeal.deal_id} - ${targetDeal.deal_name}`
          );
          console.log(`Marking as duplicates: ${duplicateDealIds.join(", ")}`);

          const confirm = await question("Confirm this merge? (y/N): ");

          if (
            confirm.toLowerCase() === "y" ||
            confirm.toLowerCase() === "yes"
          ) {
            await mergeDeals(
              targetDeal.deal_id,
              duplicateDealIds,
              "Interactive duplicate resolution"
            );
          } else {
            console.log("Merge cancelled.");
          }
        } else {
          console.log("Invalid choice.");
        }
      }
    }
  } catch (error) {
    console.error("Error during interactive resolve:", error);
  } finally {
    rl.close();
  }
}

/**
 * Auto-resolve duplicates by keeping the earliest created deal
 */
async function autoResolveByDate() {
  console.log("🔍 Scanning for duplicate deals...\n");

  const duplicateGroups = await findDuplicateDeals(0.8);

  if (duplicateGroups.length === 0) {
    console.log("✅ No duplicate deals found!");
    return;
  }

  displayDuplicateGroups(duplicateGroups);

  console.log(
    "\n🤖 Auto-resolving by keeping the earliest created deal in each group...\n"
  );

  for (let i = 0; i < duplicateGroups.length; i++) {
    const group = duplicateGroups[i];

    // Sort by creation date (earliest first)
    group.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    const targetDeal = group[0]; // Keep the earliest
    const duplicateDealIds = group.slice(1).map((deal) => deal.deal_id);

    console.log(
      `Group ${i + 1}: Keeping Deal ID ${
        targetDeal.deal_id
      } (earliest), merging ${duplicateDealIds.join(", ")}`
    );

    await mergeDeals(
      targetDeal.deal_id,
      duplicateDealIds,
      "Auto-resolved: kept earliest created deal"
    );
  }

  console.log("\n✅ Auto-resolution complete!");
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);
  const mode = args[0] || "interactive";

  try {
    console.log("🚀 Duplicate Deal Resolver\n");

    if (mode === "scan") {
      const duplicateGroups = await findDuplicateDeals(0.8);
      displayDuplicateGroups(duplicateGroups);
    } else if (mode === "auto") {
      await autoResolveByDate();
    } else {
      await interactiveResolve();
    }
  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await pool.end();
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  findDuplicateDeals,
  mergeDeals,
  jaroWinklerSimilarity,
  locationsMatch,
};
