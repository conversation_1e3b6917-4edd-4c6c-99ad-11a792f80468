# News Enrichment Workflow Updates

## Summary of Changes

This document outlines the major updates made to the news enrichment workflow to remove investment criteria, switch to millions-based storage, and use Perplexity with sonar model.

## 1. Updated News Enrichment Prompt (`src/lib/prompts/news-enrichment.ts`)

### Removed Features
- ✅ **Investment Criteria Extraction** - Completely removed the entire `investmentCriteria` section and all related loan/financing extraction logic
- ✅ **Loan-related mappings** - Removed references to loan programs, capital sources, structured loan tranches, etc.

### Updated Features
- ✅ **Data Format Change** - Changed from thousands to millions format:
  - `$5M` now becomes `5` (not `5000`)  
  - `$145M` now becomes `145` (not `145000`)
  - `$2.3B` now becomes `2300` (not `2300000`)
- ✅ **Enhanced Deal Structure** - Added `capRate` and `ltv` fields to deal objects
- ✅ **Web Search Integration** - Maintained comprehensive web search requirements for all entities
- ✅ **Simplified Mapping System** - Removed loan-related mappings, focused on core real estate data

## 2. Updated News Enrichment Processor (`src/lib/processors/NewsEnrichmentProcessor.ts`)

### Technology Changes
- ✅ **Switched to Perplexity + Sonar** - Replaced OpenAI with Perplexity using the `sonar` model for enhanced web search capabilities
- ✅ **LLM Factory Integration** - Using the established LLMFactory pattern consistent with other processors

### Data Storage Changes
- ✅ **Structured Database Storage** - Data now stored across specialized tables:
  - `deal_news` - Main news articles with extraction metadata
  - `deal_news_deals` - Individual deals with amounts in millions
  - `deal_news_companies` - Company mentions and deal participants  
  - `deal_news_persons` - People mentioned in articles
- ✅ **Enhanced Metadata** - Extraction metadata includes classification, confidence scores, and processing status
- ✅ **Foreign Key Relationships** - Proper relational structure linking deals, companies, and people to news articles

### Removed Features
- ✅ **Investment Criteria Storage** - No longer storing investment criteria data
- ✅ **Loan-specific Processing** - Removed all loan-related data extraction and storage

## 3. Database Schema Updates (`sql-files/news_enrichment_table_updates.sql`)

### Performance Optimizations
- ✅ **Comprehensive Indexing** - Added indexes for all major query patterns
- ✅ **Foreign Key Constraints** - Ensures data integrity across related tables
- ✅ **JSONB Indexes** - GIN indexes on metadata fields for efficient filtering

### Data Format Documentation
- ✅ **Column Comments** - Clear documentation of the millions-based storage format
- ✅ **Formatted Views** - Helper views that display human-readable values (e.g., "50M", "2.3B", "6.5%")
- ✅ **Statistics View** - Summary view for monitoring enrichment process performance

## 4. Data Flow Changes

### Previous Flow
```
News Article → Investment Criteria Extraction → Mixed Storage (thousands format)
```

### New Flow
```
News Article → Perplexity/Sonar Web Search → Structured Extraction → Specialized Tables (millions format)
```

### Processing Steps
1. **HTML Fetching** - `NewsHTMLFetcherProcessor` retrieves article content
2. **Text Extraction** - Clean HTML content for processing
3. **Web-Enhanced Analysis** - Perplexity sonar model performs comprehensive web search
4. **Structured Storage** - Data stored across normalized tables with proper relationships

## 5. Key Benefits

### Performance
- ✅ **Faster Queries** - Proper indexing and normalized structure
- ✅ **Web-Enhanced Data** - Sonar model provides more comprehensive and current information
- ✅ **Scalable Architecture** - Clean separation of concerns across tables

### Data Quality
- ✅ **Consistent Format** - All monetary values in millions for easier comparison
- ✅ **Comprehensive Coverage** - Web search ensures missing details are found
- ✅ **Structured Relationships** - Clear links between deals, companies, and people

### Maintainability
- ✅ **Focused Scope** - Removed complex investment criteria logic
- ✅ **Clear Schema** - Well-documented database structure
- ✅ **Monitoring Views** - Easy to track processing status and data quality

## 6. Migration Notes

### Running the Updates
```sql
-- Run the database updates
\i sql-files/news_enrichment_table_updates.sql
```

### Existing Data
- Previous investment criteria data remains in existing tables
- New enrichment process uses the updated structure
- Historical data can be migrated separately if needed

### Environment Variables
Ensure `PERPLEXITY_API_KEY` is set for the new processor to function.

## 7. Usage Examples

### Query deals with formatted values
```sql
SELECT 
  news_title,
  property_address,
  deal_value_formatted,
  cap_rate_formatted,
  deal_type
FROM deal_news_deals_formatted 
WHERE deal_value > 100  -- $100M+ deals
ORDER BY deal_value DESC;
```

### Monitor enrichment progress
```sql
SELECT * FROM news_enrichment_stats;
```

### Find company involvement in deals
```sql
SELECT 
  n.news_title,
  c.company_name,
  c.role,
  d.deal_value_formatted
FROM deal_news n
JOIN deal_news_companies c ON n.id = c.news_id
JOIN deal_news_deals d ON c.deal_id = d.id
WHERE c.company_name ILIKE '%blackstone%';
``` 