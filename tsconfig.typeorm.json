{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "commonjs", "moduleResolution": "node", "allowJs": true, "skipLibCheck": true, "strict": false, "strictPropertyInitialization": false, "esModuleInterop": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "outDir": "./dist", "rootDir": "./", "baseUrl": "./", "paths": {"@/*": ["./src/*"]}}, "include": ["src/lib/typeorm/**/*", "scripts/**/*"], "exclude": ["node_modules", "dist"]}